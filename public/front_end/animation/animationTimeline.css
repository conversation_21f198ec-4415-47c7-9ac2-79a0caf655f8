/*
 * Copyright (c) 2015 The Chromium Authors. All rights reserved.
 * Use of this source code is governed by a BSD-style license that can be
 * found in the LICENSE file.
 */

:host {
    overflow: hidden;
}

.animation-node-row {
    width: 100%;
    display: flex;
    border-bottom: 1px dashed hsla(0,0%,94%,1);
}

.animation-node-description {
    width: 150px;
    padding-left: 8px;
    overflow: hidden;
    position: relative;
    transform-style: preserve-3d;
    border-bottom: 1px solid hsl(0, 0%, 90%);
    margin-bottom: -1px;
    background-color: hsl(0, 0%, 98%);
    display: flex;
    align-items: center;
    flex: 0 0 150px;
}

.animation-node-description[data-keyboard-focus="true"]:focus {
    overflow: auto;
    box-shadow: 0px 0px 0px 2px var(--accent-color) inset;
}

.animation-node-description > * {
    flex: 0 0 auto;
}

.animation-timeline-row {
    height: 32px;
    position: relative;
}

path.animation-keyframe {
    fill-opacity: 0.2;
}

svg.animation-ui g:first-child:hover path.animation-keyframe {
    fill-opacity: 0.4;
}

.animation-node-selected path.animation-keyframe {
    fill-opacity: 0.4;
}

line.animation-line {
    stroke-width: 2px;
    stroke-linecap: round;
    fill: none;
}

line.animation-delay-line {
    stroke-width: 2px;
    stroke-dasharray: 6, 4;
}

line.animation-delay-line.animation-fill {
    stroke-dasharray: none;
}

circle.animation-endpoint, circle.animation-keyframe-point {
    stroke-width: 2px;
    transition: transform 100ms cubic-bezier(0, 0, 0.2, 1);
    transform: scale(1);
    transform-box: fill-box;
    transform-origin: 50% 50%;
}

.animation-ui circle.animation-endpoint:hover, .animation-ui circle.animation-keyframe-point:hover {
    transform: scale(1.2);
}

circle.animation-endpoint:active, circle.animation-keyframe-point:active {
    transform: scale(1);
}

circle.animation-keyframe-point {
    fill: white;
}

.animation-name {
    position: absolute;
    top: 8px;
    color: #333;
    text-align: center;
    margin-left: -8px;
    white-space: nowrap;
}

.animation-timeline-toolbar-container {
    display: flex;
    background-color: var(--toolbar-bg-color);
    border-bottom: var(--divider-border);
    flex: 0 0 auto;
}

.animation-timeline-toolbar {
    display: inline-block;
}

.animation-timeline-header {
    height: 28px;
    border-bottom: 1px solid #ccc;
    flex-shrink: 0;
    display: flex;
}

.animation-timeline-header:after {
    content: "";
    height: calc(100% - 48px - 28px);
    position: absolute;
    width: 150px;
    left: 0;
    margin-top: 28px;
    background-color: hsl(0, 0%, 98%);
    z-index: 0;
    border-right: 1px solid hsl(0, 0%, 90%);
}

.animation-controls {
    flex: 0 0 150px;
    position: relative;
    display: flex;
    justify-content: flex-end;
    padding-right: 8px;
}

.animation-timeline-current-time {flex: 0 0 auto;line-height: 28px;margin-right: 5px;}
.animation-grid-header {
    flex: 1 0 auto;
    z-index: 1;
    cursor: text;
}

.animation-timeline-buffer, .animation-timeline-buffer-hint {
    height: 48px;
    flex: 0 0 auto;
    border-bottom: 1px solid #ccc;
    display: flex;
    padding: 0 2px;
}

.animation-timeline-buffer:empty, .animation-timeline-buffer-hint {
    display: none;
}

.animation-timeline-buffer:empty ~ .animation-timeline-buffer-hint {
    align-items: center;
    justify-content: center;
    font-size: 14px;
    z-index: 101;
    display: flex;
}

.animation-time-overlay {
    background-color: black;
    opacity: 0.05;
    position: absolute;
    height: 100%;
    width: 100%;
    z-index: -1;
}

.animation-timeline-end > .animation-time-overlay {
    visibility: hidden;
}

.animation-scrubber {
    opacity: 1;
    position: absolute;
    left: 150px;
    height: calc(100% - 103px);
    width: calc(100% - 150px);
    top: 103px;
    border-left: 1px solid hsla(4,90%,58%,1);
    z-index: 1;
}

.animation-scrubber-line {
    width: 11px;
    background: linear-gradient(to right, transparent 5px, hsla(4,90%,58%,1) 5px, hsla(4,90%,58%,1) 6px, transparent 6px);
    position: absolute;
    top: -28px;
    height: 28px;
    left: -6px;
    padding: 0 5px;
    z-index: 2;
}

.animation-scrubber-head {
    width: 7px;
    height: 7px;
    transform: rotate(45deg);
    background: red;
    position: absolute;
    left: 2px;
    top: 1px;
}

svg.animation-timeline-grid {
    position: absolute;
    left: 140px;
    top: 76px;
    z-index: 0;
}

rect.animation-timeline-grid-line {
    fill: hsla(0,0%,93%,1);
}

.animation-timeline-row > svg.animation-ui {
    position: absolute;
}

.animation-node-timeline {
    flex-grow: 1;
}

.animation-node-description > div {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    max-height: 100%;
}

.animation-node-removed {
    -webkit-filter: saturate(0);
    cursor: not-allowed;
}

svg.animation-ui g:first-child {
    opacity: 1;
}

svg.animation-ui circle[data-keyboard-focus="true"],
svg.animation-ui path[data-keyboard-focus="true"] {
    outline: 2px solid -webkit-focus-ring-color;
}

.animation-tail-iterations {
    opacity: 0.5;
}

.animation-keyframe-step line {
    stroke-width: 2;
    stroke-opacity: 0.3;
}

text.animation-timeline-grid-label {
    font-size: 10px;
    fill: #5a5a5a;
    text-anchor: middle;
}

.animation-timeline-rows, .animation-timeline-rows-hint {
    flex-grow: 1;
    overflow-y: auto;
    z-index: 1;
    overflow-x: hidden;
}

.animation-timeline-rows-hint {
    display: none;
}

.animation-timeline-buffer:not(:empty) ~ .animation-timeline-rows:empty {
    flex-grow: 0;
}

.animation-timeline-buffer:not(:empty) ~ .animation-timeline-rows:empty ~ .animation-timeline-rows-hint {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 150px;
    padding: 10px;
}

.toolbar.animation-controls-toolbar {
    flex: 0 0 auto;
}

.animation-node-row.animation-node-selected {
    background-color: hsla(216, 71%, 53%, 0.08);
}

.animation-node-selected > .animation-node-description {
    background-color: #EFF4FD;
}

.animation-buffer-preview {
    height: 40px;
    margin: 4px 2px;
    background-color: var(--toolbar-bg-color);
    border-radius: 2px;
    flex: 1 1;
    padding: 4px;
    max-width: 100px;
    animation: newGroupAnim 200ms;
    position: relative;
}

.animation-buffer-preview-animation {
    width: 100%;
    height: 100%;
    border-radius: 2px 0 0 2px;
    position: absolute;
    top: 0;
    left: 0;
    background: hsla(219, 100%, 66%, 0.27);
    opacity: 0;
    border-right: 1px solid #A7A7A7;
    cursor: pointer;
}

.animation-buffer-preview[data-keyboard-focus="true"]:not(.selected):focus,
.animation-buffer-preview:not(.selected):hover {
    background-color: hsla(217,90%,92%,1);
}

.animation-buffer-preview[data-keyboard-focus="true"]:focus {
    outline: -webkit-focus-ring-color auto 5px;
}

.animation-buffer-preview.selected {
    background-color: var(--selection-bg-color);
}

.animation-paused {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: hsla(0,0%,70%,0.5);
    display: none;
}

.animation-paused:before, .animation-paused:after {
    content: "";
    background: hsl(0, 100%, 100%);
    width: 7px;
    height: 20px;
    border-radius: 2px;
    margin: 2px;
    border: 1px solid #ccc;
}

.animation-buffer-preview.paused .animation-paused {
    display: flex;
}

.animation-buffer-preview.selected > svg > line {
    stroke: white !important;
}

.animation-buffer-preview > svg > line {
    stroke-width: 1px;
}

@keyframes newGroupAnim {
    from {
        -webkit-clip-path: polygon(0% 0%, 0% 100%, 0% 100%, 0% 0%);
    }
    to {
        -webkit-clip-path: polygon(0% 0%, 0% 100%, 100% 100%, 100% 0%);
    }
}

.animation-playback-rate-control {
    margin: 4px 0 4px 2px;
    display: flex;
    width: 120px;
}

.animation-playback-rate-button:first-child {
    border-radius: 4px 0 0 4px;
}

.animation-playback-rate-button:last-child {
    border-radius: 0 4px 4px 0;
}

.animation-playback-rate-button {
    border: 1px solid #ccc;
    color: #000;
    display: inline-block;
    margin-right: -1px;
    padding: 1px 4px;
    background: white;
    flex: 1 0 auto;
    text-align: center;
    cursor: pointer;
}

.animation-playback-rate-button[data-keyboard-focus="true"].selected:first-child:focus,
.animation-playback-rate-button[data-keyboard-focus="true"]:focus {
    background-color: var(--focus-bg-color);
}

.animation-playback-rate-button:not(.selected):hover {
    background: hsl(211, 100%, 95%);
}

.animation-playback-rate-button.selected {
    color: hsl(0, 100%, 100%);
    background-color: var(--selection-bg-color);
    border-color: var(--selection-bg-color);
    z-index: 1;
}

.animation-playback-rate-button.selected:first-child {
    color: var(--selection-bg-color);
    background-color: hsl(217, 89%, 100%);
}

.animation-remove-button, -theme-preserve {
    position: absolute;
    top: -3px;
    right: -3px;
    background: #7B7B7B;
    border-radius: 12px;
    height: 16px;
    width: 16px;
    align-items: center;
    font-size: 10px;
    justify-content: center;
    box-shadow: 0 1px 4px 0 rgb(185, 185, 185);
    z-index: 100;
    display: none;
    cursor: pointer;
    font-weight: 700;
    color: white;
}

.animation-remove-button:hover {
    background: #585858;
}

.animation-buffer-preview:hover .animation-remove-button {
    display: flex;
}
