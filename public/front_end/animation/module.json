{"extensions": [{"type": "view", "location": "drawer-view", "id": "animations", "title": "Animations", "persistence": "closeable", "order": 0, "className": "Animation.AnimationTimeline"}], "dependencies": ["elements"], "scripts": [], "modules": ["animation.js", "animation-legacy.js", "AnimationModel.js", "AnimationGroupPreviewUI.js", "AnimationScreenshotPopover.js", "AnimationTimeline.js", "AnimationUI.js"], "resources": ["animationScreenshotPopover.css", "animationTimeline.css"]}