{"extensions": [{"type": "view", "location": "panel", "id": "audits", "title": "Audits", "order": 90, "className": "Audits.AuditsPanel", "tags": "lighthouse, pwa"}], "dependencies": ["components", "emulation", "timeline", "inspector_main", "sdk", "services", "ui"], "scripts": [], "modules": ["audits.js", "audits-legacy.js", "lighthouse/report.js", "lighthouse/report-generator.js", "RadioSetting.js", "AuditsPanel.js", "AuditsController.js", "AuditsReportSelector.js", "AuditsReportRenderer.js", "AuditsStartView.js", "AuditsStatusView.js", "AuditsProtocolService.js"], "resources": ["auditsDialog.css", "auditsPanel.css", "auditsStartView.css", "lighthouse/template.html", "lighthouse/templates.html", "lighthouse/report.css", "lighthouse/report.js"], "skip_compilation": ["lighthouse/report.js", "lighthouse/report-generator.js"]}