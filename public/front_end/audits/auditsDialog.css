/*
 * Copyright 2017 The Chromium Authors. All rights reserved.
 * Use of this source code is governed by a BSD-style license that can be
 * found in the LICENSE file.
 */

.audits-view {
    --view-horizontal-margin: 20px;
    margin: 7px var(--view-horizontal-margin);
    flex: auto;
    align-items: center;
    width: 100%;
    max-width: 500px;
}

.audits-view h2 {
    color: #666;
    font-weight: bold;
    font-size: 14px;
    flex: none;
    width: 100%;
    text-align: left;
}

.audits-view button {
    z-index: 10;
    margin-left: auto;
    margin-right: 0px;
}

.audits-status {
    width: 100%;
    flex: auto;
    align-items: center;
    color: #666;
}

.audits-status-text {
    text-align: center;
    min-height: 50px;
    margin-bottom: 10px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    max-width: 100%;
}

.audits-status-text code {
    user-select: text;
    text-align: left;
    white-space: pre-wrap;
    overflow: auto;
}

.audits-progress-wrapper {
    width: calc(100% + 2 * var(--view-horizontal-margin));
    height: 2px;
    background-color: #E1F5FE;
    position: relative;
    margin: 10px;
}

.audits-progress-bar {
    width: 0%;
    height: 100%;
    background: #039BE5;
    position: absolute;
    transform-origin: left;
    animation-fill-mode: forwards;
    animation-timing-function: ease-out;
    --progress-bar-loading-duration: 45s;
    --progress-bar-gathering-duration: 12s;
    --progress-bar-gathering-percent: 70%;
    --progress-bar-auditing-duration: 5s;
    --progress-bar-auditing-percent: 95%;
}

.audits-progress-bar.errored {
    width: 100%;
    background: #E50303;
}

.audits-progress-bar.loading {
    animation-duration: var(--progress-bar-loading-duration);
    animation-name: progressBarLoading;
}

@keyframes progressBarLoading {
  0% { width: 0%; }
  100% { width: var(--progress-bar-gathering-percent); }
}

.audits-progress-bar.gathering {
    animation-duration: var(--progress-bar-gathering-duration);
    animation-name: progressBarGathering;
}

@keyframes progressBarGathering {
  0% { width: var(--progress-bar-gathering-percent); }
  100% { width: var(--progress-bar-auditing-percent); }
}

.audits-progress-bar.auditing {
    animation-duration: var(--progress-bar-auditing-duration);
    animation-name: progressBarAuditing;
}

@keyframes progressBarAuditing {
  0% { width: var(--progress-bar-auditing-percent); }
  100% { width: 99%; }
}

.audits-report-error {
    display: block;
    margin-top: 5px;
}
