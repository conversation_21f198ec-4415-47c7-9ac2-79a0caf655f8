/*
 * Copyright 2018 The Chromium Authors. All rights reserved.
 * Use of this source code is governed by a BSD-style license that can be
 * found in the LICENSE file.
 */

.audits-start-view {
  font-family: Roboto, sans-serif;
  font-size: var(--font-size);
  line-height: 18px;

  --color-blue: #0535C1;
  /* for buttons */
  --accent-color: var(--color-blue);
  --color-bg: white;
  --font-size: 14px;
  --report-font-family: Roboto, Helvetica, Arial, sans-serif;
}

.audits-start-view header {
  flex: 2 1;
  padding: 16px;
  display: grid;
  justify-items: center;
}

.audits-logo {
    width: 75px;
    height: 75px;
    flex-shrink: 0;
    background-repeat: no-repeat;
    background-size: contain;
    background-image: url(Images/audits_logo.svg);
}

.audits-start-view-text {
  margin: 0 40px;
  text-align: center;
}

.audits-start-view form {
  display: contents;
}

.audits-form-section {
  padding: 8px;
  flex: 1 1;
}

.audits-start-view.vbox .audits-form-categories {
  border-top: 1px solid #ebebeb;
  border-bottom: 1px solid #ebebeb;
}

.audits-form-section-label {
  margin: 7px 0 7px;
  font-weight: 500;
}

.audits-form-section input {
  margin-top: 0;
  margin-bottom: 0;
  margin-left: 0;
}

.audits-form-section-label i span {
  position: relative;
  top: -2px;
}

.audits-form-section-label span.largeicon-checkmark {
  top: -4px;
}

.audits-radio {
  display: flex;
  align-items: center;
}

.audits-start-button-container {
  align-items: center;
}

.audits-start-button-container button {
  margin: 8px auto;
  font-family: var(--report-font-family);
  font-weight: 500;
  font-size: var(--font-size);
}
.audits-start-button-container button:disabled {
  cursor: not-allowed;
}

.audits-start-view .toolbar-dropdown-arrow {
  display: none;
}

.audits-launcher-row,
.audits-radio {
  margin-bottom: 6px;
}

.audits-launcher-row:last-of-type,
.audits-radio:last-of-type {
  margin-bottom: 0;
}

.audits-launcher-row .dimmed {
  padding-left: 22px;
}

.audits-help-text {
  text-align: center;
  color: red;
  font-weight: bold;
  padding-left: 10px;
}
