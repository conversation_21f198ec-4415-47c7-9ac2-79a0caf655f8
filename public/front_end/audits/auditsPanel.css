/*
 * Copyright 2017 The Chromium Authors. All rights reserved.
 * Use of this source code is governed by a BSD-style license that can be
 * found in the LICENSE file.
 */

.toolbar {
    background-color: var(--toolbar-bg-color);
    border-bottom: var(--divider-border);
}

.lh-root {
  --report-menu-width: 0;
  user-select: text;
}

/* for View Trace button */
.lh-audit-group {
  position: relative;
}
button.view-trace {
  margin: 10px;
}

.audits-results-container {
    position: relative;
}

/** `window.opener` is null for windows opened from DevTools. This breaks
    the LH viewer app, so disable this feature. */
.lh-tools--viewer {
  display: none !important;
}

.audits-settings-pane {
  flex: none;
}

.audits-settings-pane .toolbar {
  flex: 1 1;
}

.audits-toolbar-container {
  display: flex;
  flex: none;
}

.audits-toolbar-container > :first-child {
  flex: 1 1 auto;
}
