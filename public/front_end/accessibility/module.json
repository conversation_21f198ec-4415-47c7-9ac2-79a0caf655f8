{"extensions": [{"type": "view", "location": "elements-sidebar", "id": "accessibility.view", "title": "Accessibility", "order": 10, "persistence": "permanent", "className": "Accessibility.AccessibilitySidebarView"}], "dependencies": ["elements"], "scripts": [], "modules": ["accessibility.js", "accessibility-legacy.js", "AccessibilityModel.js", "AccessibilitySidebarView.js", "AccessibilityNodeView.js", "AccessibilityStrings.js", "AccessibilitySubPane.js", "ARIAProperties.js", "ARIAAttributesView.js", "ARIAMetadata.js", "AXBreadcrumbsPane.js"], "skip_compilation": ["ARIAProperties.js"], "resources": ["accessibilityNode.css", "accessibilityProperties.css", "axBreadcrumbs.css"]}