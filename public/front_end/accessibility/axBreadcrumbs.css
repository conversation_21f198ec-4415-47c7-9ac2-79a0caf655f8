/*
 * Copyright 2017 The Chromium Authors. All rights reserved.
 * Use of this source code is governed by a BSD-style license that can be
 * found in the LICENSE file.
 */

.ax-breadcrumbs-ignored-node {
    font-style: italic;
    opacity: 0.7;
}

.ax-breadcrumbs {
    padding-top: 1px;
    margin: 0;
    position: relative;
}

.ax-breadcrumbs .ax-node {
    align-items: center;
    margin-top: 1px;
    min-height: 16px;
    overflow-x: hidden;
    padding-left: 4px;
    padding-right: 4px;
    padding-top: 1px;
    position: relative;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ax-breadcrumbs .ax-node span {
    flex-shrink: 0;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ax-breadcrumbs .ax-node .wrapper {
    padding-left: 12px;
    overflow-x: hidden;
}

.ax-breadcrumbs .ax-node::before {
    -webkit-mask-image: url(Images/chevrons.svg);
    -webkit-mask-position: 0 0;
    -webkit-mask-size: 30px 10px;
    -webkit-mask-repeat: no-repeat;
    background-color: rgb(48, 57, 66);
    content: "";
    text-shadow: none;
    margin-right: -2px;
    height: 12px;
    width: 14px;
    position: absolute;
    display: inline-block;
}

.ax-breadcrumbs .ax-node:not(.parent):not(.children-unloaded)::before {
    background-color: transparent;
}

.ax-breadcrumbs .ax-node.parent::before {
    -webkit-mask-position: -20px 1px;
}

.ax-breadcrumbs .ax-node.children-unloaded::before {
    -webkit-mask-position: 0px 1px;
    width: 13px;
}

.ax-breadcrumbs .ax-node.no-dom-node {
    opacity: 0.7;
}

.ax-breadcrumbs .ax-node.children-unloaded::before {
    opacity: 0.4;
}

.ax-breadcrumbs .ax-node.preselected:not(.inspected) .selection,
.ax-breadcrumbs .ax-node.hovered:not(.inspected) .selection {
    display: block;
    left: 2px;
    right: 2px;
    background-color: var(--item-hover-color);
    border-radius: 5px;
}

.ax-breadcrumbs .ax-node.preselected:not(.inspected):focus .selection {
    border: 1px solid rgba(56, 121, 217, 0.4);
}

.ax-breadcrumbs .ax-node .selection {
    display: none;
    z-index: -1;
}

.ax-breadcrumbs .ax-node.inspected .selection {
    display: block;
    background-color: #ddd;
}

.ax-breadcrumbs .ax-node.inspected:focus .selection {
    background-color: var(--selection-bg-color);
}

.ax-breadcrumbs .ax-node.parent.inspected:focus::before {
    background-color: white;
}

.ax-breadcrumbs .ax-node.inspected:focus {
    color: white;
}

.ax-breadcrumbs .ax-node.inspected:focus * {
    color: inherit;
}
