<?xml version="1.0" encoding="utf-8"?>
<grit-part>
  <message name="IDS_DEVTOOLS_04efed137e5da6d8b456e83d87915f16" desc="Tooltip text that appears when hovering over the 'Focusable' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    If true, this element can receive focus.
  </message>
  <message name="IDS_DEVTOOLS_05a2ce9620a54ad2ec5cbe28ee248342" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Related element
  </message>
  <message name="IDS_DEVTOOLS_068258ebffe95a5429c1ef2f382a8c07" desc="Tooltip text that appears when hovering over the 'Busy (live regions)' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Whether this element or its subtree are currently being updated (and thus may be in an inconsistent state).
  </message>
  <message name="IDS_DEVTOOLS_07e01d7a96d9fd76cf08aa5916f7c788" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Implicit
  </message>
  <message name="IDS_DEVTOOLS_07eb2f589b3206baa27bc3e976a2ba9d" desc="Tooltip text that appears when hovering over the 'Role' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Indicates the purpose of this element, such as a user interface idiom for a widget, or structural role within a document.
  </message>
  <message name="IDS_DEVTOOLS_0a61c3cc7c416744e68854704890b215" desc="Tooltip text that appears when hovering over the 'Invalid user entry' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    If true, this element&apos;s user-entered value does not conform to validation requirement.
  </message>
  <message name="IDS_DEVTOOLS_0bea30a92342d49e6268b53526807666" desc="Tooltip text that appears when hovering over the 'Live region root' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    If this element may receive live updates, the root element of the containing live region.
  </message>
  <message name="IDS_DEVTOOLS_0d2ff4b5c95c1e4a191e8f9b5215d76d" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Minimum value
  </message>
  <message name="IDS_DEVTOOLS_12f0fc80eecd7dc42385720176cf55ad" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Can set value
  </message>
  <message name="IDS_DEVTOOLS_13e64d6db61eb7bc90150fedcfb7ee9e" desc="Tooltip text that appears when hovering over the 'Flows to' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Element to which the user may choose to navigate after this one, instead of the next element in the DOM order.
  </message>
  <message name="IDS_DEVTOOLS_15fb40082ce66f64f2025f64cb49c170" desc="Tooltip text that appears when hovering over the 'Active descendant' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    The descendant of this element which is active; i.e. the element to which focus should be delegated.
  </message>
  <message name="IDS_DEVTOOLS_185551542d4a950d6ed4a90e0875dfde" desc="Text in Accessibility Node View of the Accessibility panel">
    Computed Properties
  </message>
  <message name="IDS_DEVTOOLS_191abc41f8f74fb8c9671ca8f21b7433" desc="Reason element in Accessibility Node View of the Accessibility panel">
    Element not interesting for accessibility.
  </message>
  <message name="IDS_DEVTOOLS_1a759e882af91f6d006758443983681a" desc="Tooltip text that appears when hovering over the 'Multi-selectable' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Whether a user may select more than one option from this widget.
  </message>
  <message name="IDS_DEVTOOLS_1c2be4cda7b85620e6dca5fb505feedc" desc="Reason element in Accessibility Node View of the Accessibility panel">
    Element has empty alt text.
  </message>
  <message name="IDS_DEVTOOLS_1d95e5222e3708a525194a4a9a084275" desc="Tooltip text that appears when hovering over the 'Has autocomplete' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Whether and what type of autocomplete suggestions are currently provided by this element.
  </message>
  <message name="IDS_DEVTOOLS_1e3c246d40f5f65d98e6865f989207bb" desc="Tooltip text that appears when hovering over the 'Live region' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Whether and what priority of live updates may be expected for this element.
  </message>
  <message name="IDS_DEVTOOLS_24b44c7e76bab574ef8f632e607200c6" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    From attribute
  </message>
  <message name="IDS_DEVTOOLS_24b6472fcfa59cddd3f7bbc326582b5f" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Controls
  </message>
  <message name="IDS_DEVTOOLS_2627637e263ef687fa978b576580b8b1" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Checked
  </message>
  <message name="IDS_DEVTOOLS_2708e98a9741358c94c4d311e4fe5ebd" desc="Tooltip text that appears when hovering over the 'Minimum value' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    For a range widget, the minimum allowed value.
  </message>
  <message name="IDS_DEVTOOLS_2c09e798bf8c5a82d211331c82893a0f" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Editable
  </message>
  <message name="IDS_DEVTOOLS_38448aeeec239421db05e4226971546f" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Value description
  </message>
  <message name="IDS_DEVTOOLS_38d37768a9274b6bca0dca4329513be5" desc="Tooltip text that appears when hovering over the 'From title' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Value from title attribute.
  </message>
  <message name="IDS_DEVTOOLS_3b1afa99cd297c96939424e1438e12f2" desc="Reason element in Accessibility Node View of the Accessibility panel">
    Element inherits presentational role from '''
  </message>
  <message name="IDS_DEVTOOLS_3cf113006fc26a6c1b47b15659b3f4eb" desc="Tooltip text that appears when hovering over the 'Labeled by' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Element or elements which may form the name of this element.
  </message>
  <message name="IDS_DEVTOOLS_3cfcced512b6297b6f9f736c104e5358" desc="Tooltip text that appears when hovering over the 'Expanded' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Whether this element, or another grouping element it controls, is expanded.
  </message>
  <message name="IDS_DEVTOOLS_3dbfe71b566f271669f4f19a530eb336" desc="Tooltip text that appears when hovering over the 'Focused' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    If true, this element currently has focus.
  </message>
  <message name="IDS_DEVTOOLS_3e52d529681b27fb768252be67f91c97" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    From label (for)
  </message>
  <message name="IDS_DEVTOOLS_3f419458e82d6acd9c0d7f629c827145" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Labeled by
  </message>
  <message name="IDS_DEVTOOLS_422d799fbcb8be1e5165715fa83c4583" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Described by
  </message>
  <message name="IDS_DEVTOOLS_45ee38fb0d5ae40faaaefa51e5d54966" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Multi-selectable
  </message>
  <message name="IDS_DEVTOOLS_46a64c89d66d278297b6346c60f67e2c" desc="Tooltip text that appears when hovering over the 'Value description' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    A human-readable version of the value of a range widget (where necessary).
  </message>
  <message name="IDS_DEVTOOLS_4ada42850cc2d4e41f3da5254ec2feee" desc="Reason element in Accessibility Node View of the Accessibility panel">
    Element is inert.
  </message>
  <message name="IDS_DEVTOOLS_4ecb3834878eb89dd33d5aa34ba17b3e" desc="Tooltip text that appears when hovering over the 'Orientation' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Whether this linear element&apos;s orientation is horizontal or vertical.
  </message>
  <message name="IDS_DEVTOOLS_524c2f4506c117954fd1c95ec5247495" desc="Tooltip text that appears when hovering over the 'Level' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    The hierarchical level of this element.
  </message>
  <message name="IDS_DEVTOOLS_52886045a6932b0bbb56620fe5c584bf" desc="Reason element in Accessibility Node View of the Accessibility panel">
    Ancestor&apos;s children are all presentational: '''
  </message>
  <message name="IDS_DEVTOOLS_54b1f0b8e6ab39d96897d1c05836db84" desc="Tooltip text that appears when hovering over the 'Relevant (live regions)' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    If this element may receive live updates, what type of updates should trigger a notification.
  </message>
  <message name="IDS_DEVTOOLS_56d1080a1e9e298e520e65be690efd93" desc="Tooltip text that appears when hovering over the 'Atomic (live regions)' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    If this element may receive live updates, whether the entire live region should be presented to the user on changes, or only changed nodes.
  </message>
  <message name="IDS_DEVTOOLS_570e629208d2b9a6e2a312dcd7fa855d" desc="Tooltip text that appears when hovering over the 'From label (for)' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Value from label element with for= attribute.
  </message>
  <message name="IDS_DEVTOOLS_5a51e4de1a235f3f67e816cb561d3d5a" desc="Reason element in Accessibility Node View of the Accessibility panel">
    Element is <ph name="ARIAHIDDENSPAN">$1s<ex>aria-hidden</ex></ph>.
  </message>
  <message name="IDS_DEVTOOLS_5cb7a9a4a6eb6614869a518b7323a61d" desc="Tooltip text that appears when hovering over the 'Description' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    The accessible description for this element.
  </message>
  <message name="IDS_DEVTOOLS_5cd2b4477fa05212ce32e073702b6938" desc="Text in ARIAAttributes View of the Accessibility panel">
    No ARIA attributes
  </message>
  <message name="IDS_DEVTOOLS_5d910721a6256ce42c8c6308dc60ff40" desc="Reason element in Accessibility Node View of the Accessibility panel">
    Static text node is used as name for '''
  </message>
  <message name="IDS_DEVTOOLS_5e2c648cec53676f6fa0acbf702eccfc" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    From style
  </message>
  <message name="IDS_DEVTOOLS_5f05a00053834655de812d3447545a17" desc="Text in Accessibility Node View of the Accessibility panel">
    No node with this ID.
  </message>
  <message name="IDS_DEVTOOLS_63f6baf1d88963b8c8210751c8530e94" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Expanded
  </message>
  <message name="IDS_DEVTOOLS_6444fb2d544ab7073a785759497fe5b8" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    From native HTML
  </message>
  <message name="IDS_DEVTOOLS_674d8a03d6bb22d48dff01565c2b10a3" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Owns
  </message>
  <message name="IDS_DEVTOOLS_73f3b04d99cdbbfb2045ad2285986d77" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Maximum value
  </message>
  <message name="IDS_DEVTOOLS_7506ced399bb4ab929942493dffb8be9" desc="Tooltip text that appears when hovering over the 'From label (wrapped)' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Value from label element wrapped.
  </message>
  <message name="IDS_DEVTOOLS_76d01b44b32289a65360a4ba164b329f" desc="Tooltip text that appears when hovering over the 'Required' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Whether this element is a required field in a form.
  </message>
  <message name="IDS_DEVTOOLS_7882d6b9ff794f0ed72656149e7dd037" desc="Tooltip text that appears when hovering over the 'Implicit' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Implicit value.
  </message>
  <message name="IDS_DEVTOOLS_7d615068dd4fb142c11a29553b9b83bf" desc="Reason element in Accessibility Node View of the Accessibility panel">
    Element is presentational.
  </message>
  <message name="IDS_DEVTOOLS_7eedfa765b9cfe7345e3b7e20148738d" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    From placeholder attribute
  </message>
  <message name="IDS_DEVTOOLS_806cb54fdaa10d7c21dc6c00d17e9a08" desc="Tooltip text that appears when hovering over the 'From native HTML' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Value from native HTML (unknown source).
  </message>
  <message name="IDS_DEVTOOLS_80c9f4e5b244cf617075b8ce23a3ed27" desc="Text in ARIAAttributes View of the Accessibility panel">
    ARIA Attributes
  </message>
  <message name="IDS_DEVTOOLS_874b430cd4166d886a99f05d7fc14a81" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Has autocomplete
  </message>
  <message name="IDS_DEVTOOLS_88ac37355d818b18b26ec88d0e84adec" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Focusable
  </message>
  <message name="IDS_DEVTOOLS_8d1e218236bebc19ebbcc1c0008407c6" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Invalid user entry
  </message>
  <message name="IDS_DEVTOOLS_90d7af21ecb6385b5a5e16cfffd07e1d" desc="Tooltip text that appears when hovering over the 'Contents' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Value from element contents.
  </message>
  <message name="IDS_DEVTOOLS_91b442d385b54e1418d81adc34871053" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Selected
  </message>
  <message name="IDS_DEVTOOLS_953f80ed9077d40149a8e68448f09e47" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    From label (wrapped)
  </message>
  <message name="IDS_DEVTOOLS_954d1813cbd929582346898482fa370a" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Has popup
  </message>
  <message name="IDS_DEVTOOLS_95ed994edf3b74ebaa5c07378adb1b6a" desc="Tooltip text that appears when hovering over the 'Multi-line' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Whether this text box may have more than one line.
  </message>
  <message name="IDS_DEVTOOLS_970e14ef4e4d726d36b6ed0ea92bef83" desc="Text in Accessibility Node View of the Accessibility panel">
    Accessibility node not exposed
  </message>
  <message name="IDS_DEVTOOLS_98b6f72f9b4c1883deddc61bc62d71eb" desc="Reason element in Accessibility Node View of the Accessibility panel">
    Element is not visible.
  </message>
  <message name="IDS_DEVTOOLS_98bd032b07fb2909f106262ffb0a6c4f" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Live region
  </message>
  <message name="IDS_DEVTOOLS_9a68c121520a792ecf2e08730cd7e1a9" desc="Tooltip text that appears when hovering over the 'Has popup' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Whether this element has caused some kind of pop-up (such as a menu) to appear.
  </message>
  <message name="IDS_DEVTOOLS_9e8e28c5e23d1a212c6afc9cdffb9a0e" desc="Tooltip text that appears when hovering over the 'From caption' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Value from table caption.
  </message>
  <message name="IDS_DEVTOOLS_a0db49ba470c1c9ae2128c3470339153" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Level
  </message>
  <message name="IDS_DEVTOOLS_a2e08e1fbbc3e1489d688eba112f7964" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Focused
  </message>
  <message name="IDS_DEVTOOLS_a38feabd83f0c880aee942fef26450a2" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    From label
  </message>
  <message name="IDS_DEVTOOLS_a4853b77c462337ed9fecc32e4d44d7b" desc="Text in AXBreadcrumbs Pane of the Accessibility panel">
    Accessibility Tree
  </message>
  <message name="IDS_DEVTOOLS_a52f614f078fc6dc7273418b6b35b75a" desc="Tooltip text that appears when hovering over the 'Related element' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Value from related element.
  </message>
  <message name="IDS_DEVTOOLS_a69a05e6fc6cc0de6eb9b43a720d6a2b" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    From caption
  </message>
  <message name="IDS_DEVTOOLS_a858dfbc32e2169e89668adf8e997f6f" desc="Text in Accessibility Node View of the Accessibility panel">
    No accessibility node
  </message>
  <message name="IDS_DEVTOOLS_aff2b966e54c2212e86343d2bb8d3f88" desc="Reason element in Accessibility Node View of the Accessibility panel">
    Part of label element: '''
  </message>
  <message name="IDS_DEVTOOLS_b05cf8cea038a89494e9e232e65af3f3" desc="Reason element in Accessibility Node View of the Accessibility panel">
    Element is not rendered.
  </message>
  <message name="IDS_DEVTOOLS_b0b4bb7fc3f54f770e2368651fc2b285" desc="Tooltip text that appears when hovering over the 'Can set value' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Whether the value of this element can be set.
  </message>
  <message name="IDS_DEVTOOLS_b438066f384cd34d54da4c56f1e39cf9" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Multi-line
  </message>
  <message name="IDS_DEVTOOLS_b459bb2b480be9a5043f103bbc2f1d49" desc="Tooltip text that appears when hovering over the 'Checked' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Whether this checkbox, radio button or tree item is checked, unchecked, or mixed (e.g. has both checked and un-checked children).
  </message>
  <message name="IDS_DEVTOOLS_b651efdb98a5d6bd2b3935d0c3f4a5e2" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Required
  </message>
  <message name="IDS_DEVTOOLS_b7ccbbea68b73884ce03f7a3e1acdb81" desc="Tooltip text that appears when hovering over the 'From attribute' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Value from attribute.
  </message>
  <message name="IDS_DEVTOOLS_b9db1026019a8730b64397052a61ded0" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Atomic (live regions)
  </message>
  <message name="IDS_DEVTOOLS_bb129d779ae1ffda5fda12070a4a142b" desc="Tooltip text that appears when hovering over the 'From caption' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Value from figcaption element.
  </message>
  <message name="IDS_DEVTOOLS_bbbabdbe1b262f75d99d62880b953be1" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Role
  </message>
  <message name="IDS_DEVTOOLS_bf1cf93766b632e2d22d21959cb5eb2f" desc="Tooltip text that appears when hovering over the 'Editable' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    If and how this element can be edited.
  </message>
  <message name="IDS_DEVTOOLS_c108f35980e0df1e4a753232c5b66905" desc="Tooltip text that appears when hovering over the 'Selected' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Whether the option represented by this element is currently selected.
  </message>
  <message name="IDS_DEVTOOLS_c1df1da7a1ce305a3b60af9d5733ac1d" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Contents
  </message>
  <message name="IDS_DEVTOOLS_c4aa9c650883f8b6e52f8dd962003164" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Relevant (live regions)
  </message>
  <message name="IDS_DEVTOOLS_c88f4050d75172dae8ef486f546d89fa" desc="Tooltip text that appears when hovering over the 'Help' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    The computed help text for this element.
  </message>
  <message name="IDS_DEVTOOLS_c9bbad3047af039c14d0e7ec957bb867" desc="Tooltip text that appears when hovering over the 'Disabled' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    If true, this element currently cannot be interacted with.
  </message>
  <message name="IDS_DEVTOOLS_cd9da90ce06215f24f648786501e4073" desc="Reason element in Accessibility Node View of the Accessibility panel">
    Element is hidden by active modal dialog: '''
  </message>
  <message name="IDS_DEVTOOLS_cfa5234b2737df4bc3dc737484605c39" desc="Reason element in Accessibility Node View of the Accessibility panel">
    <ph name="ARIAHIDDENSPAN">$1s<ex>aria-hidden</ex></ph> is <ph name="TRUESPAN">$2s<ex>true</ex></ph> on ancestor: '''
  </message>
  <message name="IDS_DEVTOOLS_d3fd6a090f523416f156281b8735573b" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    From title
  </message>
  <message name="IDS_DEVTOOLS_d4144214055f16d813e54cf2903a4174" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Busy (live regions)
  </message>
  <message name="IDS_DEVTOOLS_d671e7068cc85e3578dc7ed21488bdf3" desc="Tooltip text that appears when hovering over the 'Value' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    The value of this element; this may be user-provided or developer-provided, depending on the element.
  </message>
  <message name="IDS_DEVTOOLS_d78a68f6a85421ae121c2cb5b73a1040" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Pressed
  </message>
  <message name="IDS_DEVTOOLS_d7c640cc33c74791ecd8fd06e5158d34" desc="Tooltip text that appears when hovering over the 'Name' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    The computed name of this element.
  </message>
  <message name="IDS_DEVTOOLS_d7dff47e75bfd5f107407eded09ee6f0" desc="Reason element in Accessibility Node View of the Accessibility panel">
    Element has <ph name="ROLEPRESENTATIONSPAN">$1s<ex>role=link</ex></ph>.
  </message>
  <message name="IDS_DEVTOOLS_d8bd6e60adcc01ce20f509ea6800de8f" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Read-only
  </message>
  <message name="IDS_DEVTOOLS_d96143ba1b15645919cea00ec9d1be62" desc="Ignored node element text content in AXBreadcrumbs Pane of the Accessibility panel">
    Ignored
  </message>
  <message name="IDS_DEVTOOLS_d98e6d7300cfa4e03c4ac9cf658449a7" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Live region root
  </message>
  <message name="IDS_DEVTOOLS_dd3195ced9c1810388e28ca2b4eb921f" desc="Text in Accessibility Node View of the Accessibility panel">
    Not specified
  </message>
  <message name="IDS_DEVTOOLS_dfea140c0381b0207a499a890fde2e31" desc="Tooltip text that appears when hovering over the 'Described by' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Element or elements which form the description of this element.
  </message>
  <message name="IDS_DEVTOOLS_e0dcc9aef6db5e61e4102efdf5d54378" desc="Text in Accessibility Node View of the Accessibility panel">
    Invalid source.
  </message>
  <message name="IDS_DEVTOOLS_e1312fc3ec5859447389668b721d932f" desc="Tooltip text that appears when hovering over the 'Pressed' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Whether this toggle button is currently in a pressed state.
  </message>
  <message name="IDS_DEVTOOLS_e258b068a6cdb373c5cbc6a74c8bd0f2" desc="Tooltip text that appears when hovering over the 'Maximum value' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    For a range widget, the maximum allowed value.
  </message>
  <message name="IDS_DEVTOOLS_e57cf3a3ec9d309180d5af992a8141c8" desc="Tooltip text that appears when hovering over the 'Controls' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Element or elements whose content or presence is/are controlled by this widget.
  </message>
  <message name="IDS_DEVTOOLS_e633854d55dfd9bbb2d91a63f09db7c5" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Flows to
  </message>
  <message name="IDS_DEVTOOLS_e71c63b8e5a312662941ee8606e8c43a" desc="Reason element in Accessibility Node View of the Accessibility panel">
    Label for '''
  </message>
  <message name="IDS_DEVTOOLS_e7be8f80bd8db0b8ca6bb2dee175b623" desc="Reason element in Accessibility Node View of the Accessibility panel">
    Element is in an inert subtree from '''
  </message>
  <message name="IDS_DEVTOOLS_e8ce1fb5b4c870b68d6f6e5c7037c61f" desc="Tooltip text that appears when hovering over the 'From style' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Value from style.
  </message>
  <message name="IDS_DEVTOOLS_f36499431cc70b125fea5984d3d0d17c" desc="Accessibility attribute name that appears under the Computed Properties section in the Accessibility pane of the Elements pane">
    Active descendant
  </message>
  <message name="IDS_DEVTOOLS_f3ea3dc14c2b876598e7b3e8e5073403" desc="Tooltip text that appears when hovering over the 'Read-only' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    If true, this element may be interacted with, but its value cannot be changed.
  </message>
  <message name="IDS_DEVTOOLS_f94f9f1c2f3bda21d4a1d4dd222cbe9f" desc="Tooltip text that appears when hovering over the 'From placeholder attribute' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Value from placeholder attribute.
  </message>
  <message name="IDS_DEVTOOLS_fc6dc044799975f19c864e9b31e3bb76" desc="Reason element in Accessibility Node View of the Accessibility panel">
    No text content.
  </message>
  <message name="IDS_DEVTOOLS_feb0d74c82c7ade6758ced29e8ba7f13" desc="Tooltip text that appears when hovering over the 'From label' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Value from label element.
  </message>
  <message name="IDS_DEVTOOLS_fec6d4d6f166d588401fc9ce7322a8f7" desc="Tooltip text that appears when hovering over the 'Owns' attribute name under the Computed Properties section in the Accessibility pane of the Elements pane">
    Element or elements which should be considered descendants of this element, despite not being descendants in the DOM.
  </message>
</grit-part>