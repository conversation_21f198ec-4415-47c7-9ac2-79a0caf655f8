{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "As chaves de acesso permitem ao usuário focar rapidamente determinada parte da página. Para haver uma navegação adequada, cada chave de acesso precisa ser única. [Sai<PERSON> mais](https://web.dev/accesskeys/)."}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Os valores de `[accesskey]` não são únicos"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "Valores de `[accesskey]` são exclusivos"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON> `role` ARIA é compatível com um subconjunto específico de atributos `aria-*`. A falta de correspondência entre eles invalida os atributos `aria-*`. [<PERSON><PERSON> mais](https://web.dev/aria-allowed-attr/)."}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Os atributos `[aria-*]` não correspondem às próprias funções"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Os atributos `[aria-*]` correspondem às próprias funções"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "Algumas funções ARIA têm atributos obrigatórios que descrevem o estado do elemento para leitores de tela. [<PERSON><PERSON> ma<PERSON>](https://web.dev/aria-required-attr/)."}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]`s não têm todos os atributos `[aria-*]` obrigat<PERSON><PERSON>s"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]`s têm todos os atributos `[aria-*]` obri<PERSON><PERSON><PERSON>s"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "Algumas funções ARIA mães precisam ter funções filhas específicas para cumprir as tarefas de acessibilidade pretendidas. [<PERSON><PERSON> mais](https://web.dev/aria-required-children/)."}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Elementos com uma `[role]` ARIA que exigem que os filhos contenham uma `[role]` específica não têm alguns ou nenhum dos filhos obrigatórios."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "Elementos com uma `[role]` ARIA que exigem que os filhos contenham uma `[role]` específica têm todos os filhos obrigatórios."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "Algumas funções ARIA filhas precisam fazer parte das funções mães específicas para cumprir as tarefas de acessibilidade pretendidas. [<PERSON><PERSON> mais](https://web.dev/aria-required-parent/)."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]`s não fazem parte do elemento pai obrigatório"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]`s fazem parte do elemento pai obrigatório"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "As funções ARIA precisam ter valores válidos para que realizem as tarefas de acessibilidade pretendidas. [<PERSON><PERSON> ma<PERSON>](https://web.dev/aria-roles/)."}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "Os valores de `[role]` não são válidos"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "Os valores de `[role]` s<PERSON> válidos"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Tecnologias assistivas, como leitores de tela, não conseguem interpretar atributos ARIA com valores inválidos. [<PERSON><PERSON> mais](https://web.dev/aria-valid-attr-value/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Os atributos `[aria-*]` não têm valores válidos"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Os atributos `[aria-*]` têm valores válidos"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "As tecnologias assistivas, como leitores de tela, não conseguem interpretar atributos ARIA com nomes inválidos. [<PERSON><PERSON> ma<PERSON>](https://web.dev/aria-valid-attr/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Os atributos `[aria-*]` não são válidos nem contêm erros de ortografia"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "Os atributos `[aria-*]` são válidos e não contêm erros de ortografia"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "As legendas tornam os elementos de áudio úteis para pessoas surdas ou deficientes auditivas, disponibilizando informações essenciais, como quem está falando, o que a pessoa está dizendo e outras informações não relacionadas à fala. [<PERSON><PERSON> ma<PERSON>](https://web.dev/audio-caption/)."}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "Os elementos `<audio>` estão sem um elemento `<track>` com `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "Os elementos `<audio>` contêm um elemento `<track>` com `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Elementos com falha"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "Quando um botão não tem um nome acessível, os leitores de tela o enunciam como \"botão\", o que o inutiliza para usuários que dependem desses leitores. [<PERSON><PERSON> mais](https://web.dev/button-name/)."}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "Os botões não têm um nome acessível"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "Os botões têm um nome acessível"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "A adição de maneiras de ignorar conteúdo repetido permite ao usuário do teclado navegar pela página com mais eficiência. [Saiba mais](https://web.dev/bypass/)."}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "A página não contém um cabeçalho, link de salto ou região de ponto de referência"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "A página contém um título, um link de salto ou uma região de ponto de referência"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "Para muitos usuários, é difícil ou impossível ler textos com baixo contraste. [<PERSON><PERSON> mais](https://web.dev/color-contrast/)."}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "As cores de primeiro e segundo plano não têm uma taxa de contraste suficiente."}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "As cores de primeiro e segundo plano têm uma taxa de contraste suficiente"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "Quando listas de definição não são marcadas corretamente, os leitores de tela podem produzir resultados confusos ou imprecisos. [Sai<PERSON> mais](https://web.dev/definition-list/)."}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>`s não contêm apenas grupos `<dt>` e `<dd>` devidamente organizados e elementos `<script>` ou `<template>`."}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "`<dl>`s contêm apenas os grupos `<dt>` e `<dd>` devidamente organizados ou os elementos `<script>` ou `<template>`."}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "Os itens da lista de definição (`<dt>` e `<dd>`) precisam ficar unidos em um elemento `<dl>` pai para garantir que os leitores de tela consigam enunciá-los corretamente. [<PERSON><PERSON> mais](https://web.dev/dlitem/)."}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "Os itens da lista de definição não estão unidos em elementos `<dl>`"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "Os itens da lista de definição estão unidos em elementos `<dl>`"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "O título oferece ao usuário do leitor de tela uma visão geral da página, além de ser extremamente útil para que os usuários de mecanismos de pesquisa determinem se uma página é relevante à pesquisa deles. [<PERSON><PERSON> mais](https://web.dev/document-title/)."}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "O documento não tem um elemento `<title>`"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "O documento tem um elemento `<title>`"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "O valor de um atributo \"id\" precisa ser único para evitar que outras instâncias sejam ignoradas por tecnologias assistivas. [<PERSON><PERSON> ma<PERSON>](https://web.dev/duplicate-id/)."}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "Os atributos `[id]` da página não são únicos"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "Os atributos `[id]` da página são únicos"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "Os usuários de leitores de tela utilizam títulos para descrever o conteúdo de frames. [<PERSON><PERSON> mais](https://web.dev/frame-title/)."}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "Os elementos `<frame>` ou `<iframe>` não têm um título"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "Os elementos `<frame>` ou `<iframe>` têm um título"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "Se uma página não especificar um atributo lang, o leitor de tela presumirá que a página está no idioma padrão que o usuário escolheu ao configurar esse leitor. Se a página não estiver no idioma padrão, o leitor de tela poderá ler o texto dela incorretamente. [<PERSON><PERSON> mais](https://web.dev/html-has-lang/)."}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "O elemento `<html>` não tem um atributo `[lang]`"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "O elemento `<html>` tem um atributo `[lang]`"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "A especificação de um [idioma BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) válido ajuda os leitores de tela a enunciar o texto corretamente. [<PERSON><PERSON> ma<PERSON>](https://web.dev/html-lang-valid/)."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "O elemento `<html>` não tem um valor válido para o atributo `[lang]`."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "O elemento `<html>` tem um valor válido para o atributo `[lang]`"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "O texto dos elementos informativos precisa ser alternativo, breve e descritivo. Os elementos decorativos podem ser ignorados com um atributo alternativo vazio. [<PERSON><PERSON> ma<PERSON>](https://web.dev/image-alt/)."}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "Os elementos de imagem não têm atributos `[alt]`"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "Os elementos de imagem têm atributos `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "Quando uma imagem for usada como um botão `<input>`, a oferta de texto alternativo poderá ajudar o usuário do leitor de tela a entender a finalidade do botão. [Sai<PERSON> mais](https://web.dev/input-image-alt/)."}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Os elementos `<input type=\"image\">` não têm texto `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "Os elementos `<input type=\"image\">` têm texto `[alt]`"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "As etiquetas garantem que os controles de formulário sejam enunciados corretamente por tecnologias assistivas, como leitores de tela. [<PERSON><PERSON> mais](https://web.dev/label/)."}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "Os elementos de formulário não têm etiquetas associadas"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "Os elementos de formulário têm etiquetas associadas"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "Não inclua elementos de dados, como os elementos \"th\" ou \"caption\", nem o atributo \"summary\" em uma tabela utilizada para fins de layout, já que isso pode gerar uma experiência confusa para os usuários de leitores de telas. [<PERSON><PERSON> ma<PERSON>](https://web.dev/layout-table/)."}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "Os elementos `<table>` de apresentação não evitam o uso de `<th>`, `<caption>` ou do atributo `[summary]`."}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "Os elementos `<table>` de apresentação evitam o uso de `<th>`, `<caption>` ou do atributo `[summary]`."}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "Textos de link (e textos alternativos de imagens, quando utilizados como link) compreensíveis, únicos e focalizáveis melhoram a experiência de navegação para usuários de leitores de tela. [<PERSON><PERSON> ma<PERSON>](https://web.dev/link-name/)."}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "<PERSON><PERSON> links não têm um nome compreensível"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "<PERSON><PERSON> links têm um nome compreensível"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "Os leitores de tela têm uma maneira específica de enunciar listas. Uma estrutura de lista adequada melhora os resultados do leitor de tela. [<PERSON><PERSON> ma<PERSON>](https://web.dev/list/)."}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "As listas não contêm apenas elementos `<li>` e elementos compatíveis com script (`<script>` e `<template>`)."}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "As listas contêm somente elementos `<li>` e elementos compatíveis com script (`<script>` e `<template>`)."}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "Os leitores de tela exigem que os itens de lista (`<li>`) estejam contidos em um `<ul>` ou `<ol>` pai para serem enunciados corretamente. [<PERSON><PERSON> mais](https://web.dev/listitem/)."}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "Os itens de lista (`<li>`) não estão contidos nos elementos pai `<ul>` ou `<ol>`."}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "Os itens de lista (`<li>`) estão contidos nos elementos pai `<ul>` ou `<ol>`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "O usuário não espera a atualização automática da página, o que move o foco novamente para a parte superior dela. Isso pode causar uma experiência confusa ou frustrante. [<PERSON><PERSON> ma<PERSON>](https://web.dev/meta-refresh/)."}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "O documento usa `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "O documento não usa `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "A desativação do zoom gera problemas para usuários com baixa visão que utilizam a ampliação de tela para enxergar corretamente o conteúdo de uma página da Web. [Saiba mais](https://web.dev/meta-viewport/)."}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` é usado no elemento `<meta name=\"viewport\">` ou o atributo `[maximum-scale]` é menor que 5."}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` não é usado no elemento `<meta name=\"viewport\">`, e o atributo `[maximum-scale]` não é menor que 5."}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "Os leitores de tela não traduzem conteúdo não textual. A adição de texto alternativo aos elementos `<object>` ajuda os leitores de tela a transmitir o significado para os usuários. [<PERSON><PERSON> mais](https://web.dev/object-alt/)."}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "Os elementos `<object>` não têm texto `[alt]`"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "Os elementos `<object>` têm texto `[alt]`"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "Um valor maior que 0 indica uma ordem explícita de navegação. Embora tecnicamente válido, isso costuma gerar experiências frustrantes para os usuários que utilizam tecnologias assistivas. [Sai<PERSON> mais](https://web.dev/tabindex/)."}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "Alguns elementos têm um valor de `[tabindex]` maior que 0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "Nenhum elemento tem um valor de `[tabindex]` maior que 0"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "Os leitores de tela têm recursos para facilitar a navegação em tabelas. Para melhorar a experiência dos usuários de leitores de tela, as c<PERSON><PERSON><PERSON> `<td>` que usam o atributo `[headers]` precisam referenciar apenas outras células na mesma tabela. [<PERSON><PERSON> mais](https://web.dev/td-headers-attr/)."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Células em um elemento `<table>` que usam o atributo `[headers]` referem-se a um elemento `id` não encontrado na mesma tabela."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "Células em um elemento `<table>` que usam o atributo `[headers]` referem-se às células na mesma tabela."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "Os leitores de tela têm recursos para facilitar a navegação em tabelas. Garantir que os cabeçalhos das tabelas se refiram sempre a alguns conjuntos de células pode melhorar a experiência dos usuários de leitores de tela. [<PERSON><PERSON> mais](https://web.dev/th-has-data-cells/)."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Os elementos `<th>` e os elementos com `[role=\"columnheader\"/\"rowheader\"]` não têm as c<PERSON><PERSON><PERSON> de dados descritas."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "Os elementos `<th>` e os elementos com `[role=\"columnheader\"/\"rowheader\"]` têm as c<PERSON><PERSON><PERSON> de dados descritas."}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "A especificação de um [idioma BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) válido nos elementos ajuda a garantir que o texto seja pronunciado corretamente pelo leitor de tela. [<PERSON><PERSON> mais](https://web.dev/valid-lang/)."}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Os atributos `[lang]` não têm um valor válido"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "Os atributos `[lang]` têm um valor válido"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "Quando um vídeo é acompanhado de legendas, as pessoas surdas e deficientes auditivas têm mais facilidade para acessar as informações dele. [<PERSON><PERSON> mais](https://web.dev/video-caption/)."}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "Os elementos `<video>` não contêm um elemento `<track>` com `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "Os elementos `<video>` contêm um elemento `<track>` com `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "As descrições de áudio trazem informações importantes que não podem ser transmitidas por meio do diálogo nos vídeos, por exemplo, expressões faciais e cenários. [<PERSON><PERSON> ma<PERSON>](https://web.dev/video-description/)."}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "Os elementos `<video>` não contêm um elemento `<track>` com `[kind=\"description\"]`."}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "Os elementos `<video>` contêm um elemento `<track>` com `[kind=\"description\"]`"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "Para ter uma exibição ideal no iOS quando o usuário adiciona um Progressive Web App à tela inicial, defina um `apple-touch-icon`. Ele precisa apontar para um PNG quadrado não transparente de 192 px (ou 180 px). [<PERSON><PERSON> mais](https://web.dev/apple-touch-icon/)."}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "<PERSON><PERSON> oferece um `apple-touch-icon` v<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "`apple-touch-icon-precomposed` está desatualizado. Use `apple-touch-icon`."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "Disponibiliza um `apple-touch-icon` v<PERSON><PERSON>o"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "As extensões do Chrome afetaram negativamente o desempenho de carregamento desta página. Tente fazer a auditoria da página no modo de navegação anônima ou em um perfil do Chrome sem extensões."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "Avaliação de script"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "Tempo total de CPU"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "Diminua o tempo gasto na análise, compilação e execução de JS. Você perceberá que exibir payloads de JS menores ajuda a fazer isso. [<PERSON><PERSON> mais](https://web.dev/bootup-time)."}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "Reduza o tempo de execução de JavaScript"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "Tempo de execução de JavaScript"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "GIFs grandes não são eficientes para exibir conteúdo animado. Use vídeos MPEG4/WebM para animações e PNG/WebP para imagens estáticas em vez de GIF para economizar bytes de rede. [<PERSON><PERSON> ma<PERSON>](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Use formatos de vídeo para conteúdo animado"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "A fim de reduzir o tempo para interação da página, faça o carregamento lento de imagens fora da tela e ocultas quando todos os recursos críticos já estiverem carregados. [<PERSON><PERSON> ma<PERSON>](https://web.dev/offscreen-images)."}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "<PERSON><PERSON> imagens fora da tela"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Os recursos estão bloqueando a primeira exibição da sua página. Exiba JS/CSS crítico inline e adie todos os JS/estilos não críticos. [Saiba mais](https://web.dev/render-blocking-resources)."}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Elimine recursos que impedem a renderização"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Grandes payloads de rede geram custos para o usuário e estão diretamente relacionados a tempos de carregamento maiores. [Saiba mais](https://web.dev/total-byte-weight)."}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "O tamanho total foi de {totalBytes, number, bytes} KB"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Evite payloads de rede muito grandes"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Evita payloads de rede muito grandes"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "A redução de arquivos CSS pode diminuir o tamanho do payload de rede. [Sai<PERSON> mais](https://web.dev/unminified-css)."}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "Reduza o CSS"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "A redução de arquivos JavaScript pode diminuir o tamanho de payloads e o tempo de análise de scripts. [<PERSON><PERSON> mais](https://web.dev/unminified-javascript)."}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Reduza o JavaScript"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Remova as regras inativas das folhas de estilo e adie o carregamento de CSS não usado para conteúdo acima da dobra, a fim de reduzir o consumo desnecessário de bytes da atividade da rede. [<PERSON><PERSON> mais](https://web.dev/unused-css-rules)."}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Remova CSS não utilizado"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Remova o JavaScript não utilizado para reduzir o consumo de bytes da atividade de rede."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Remova o JavaScript não utilizado"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Um cache com ciclo de vida longo pode acelerar visitas repetidas à sua página. [<PERSON><PERSON> ma<PERSON>](https://web.dev/uses-long-cache-ttl)."}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 recurso encontrado}one{# recurso encontrado}other{# recursos encontrados}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Disponibilize recursos estáticos com uma política de cache eficiente"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Usa uma política de cache eficiente em recursos estáticos"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Imagens otimizadas são carregadas mais rapidamente e consomem menos dados da rede celular. [Saiba mais](https://web.dev/uses-optimized-images)."}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Codifique as imagens com eficiência"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Veicule imagens que tenham o tamanho adequado para economizar dados da rede celular e melhorar o tempo de carregamento. [Sai<PERSON> mais](https://web.dev/uses-responsive-images)."}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Defina um tamanho adequado para as imagens"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Os recursos baseados em texto precisam ser veiculados com compactação (gzip, deflate ou brotli) para minimizar o total de bytes da rede. [<PERSON><PERSON> ma<PERSON>](https://web.dev/uses-text-compression)."}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Ative a compactação de texto"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "Formatos de imagem como JPEG 2000, JPEG XR e WebP geralmente oferecem uma melhor compactação do que PNG ou JPEG, o que significa downloads mais rápidos e menor consumo de dados. [Saiba mais](https://web.dev/uses-webp-images)."}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "Disponibilize imagens em formatos de última geração"}, "lighthouse-core/audits/content-width.js | description": {"message": "Se a largura do conteúdo do seu app não corresponder à largura da janela de visualização, não será possível otimizar o app para telas de dispositivos móveis. [Saiba mais](https://web.dev/content-width)."}, "lighthouse-core/audits/content-width.js | explanation": {"message": "O tamanho da janela de visualização de {innerWidth} px não corresponde ao tamanho da janela {outerWidth} px."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "O conteúdo não está no tamanho correto para a janela de visualização"}, "lighthouse-core/audits/content-width.js | title": {"message": "O conteúdo está no tamanho correto para a janela de visualização"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "As cadeias de solicitação críticas abaixo mostram quais recursos são carregados com prioridade alta. Diminua o tamanho das cadeias, reduza o tamanho do download de recursos ou adie o download de recursos desnecessários para melhorar o carregamento de página. [<PERSON><PERSON> mais](https://web.dev/critical-request-chains)."}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 rede encontrada}one{# rede encontrada}other{# redes encontradas}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "Reduza a profundidade de solicitações críticas"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "Suspensão de uso/aviso"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/deprecations.js | description": {"message": "As APIs obsoletas acabarão sendo removidas do navegador. [<PERSON><PERSON> ma<PERSON>](https://web.dev/deprecations)."}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 aviso encontrado}one{# aviso encontrado}other{# avisos encontrados}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "Utiliza APIs obsoletas"}, "lighthouse-core/audits/deprecations.js | title": {"message": "Evita APIs obsoletas"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "O cache de aplicativo está obsoleto. [Sai<PERSON> ma<PERSON>](https://web.dev/appcache-manifest)."}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "\"{AppCacheManifest}\" encontrado"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "Usa cache de aplicativo"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "Evita o cache de aplicativo"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "A especificação de um doctype evita que o navegador alterne para o modo quirks. [Sai<PERSON> mais](https://web.dev/doctype)."}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "O nome do doctype precisa ser o `html` da string em letra minúscula"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "O documento precisa conter um doctype"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "O ID público deveria ser uma string vazia"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "O ID do sistema deveria ser uma string vazia"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "A página não tem o doctype HTML e, assim, aciona o modo quirks"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "A página tem o doctype HTML"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "Elemento"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Estatística"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Valor"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "Os engenheiros do navegador recomendam páginas com menos de ~1.500 elementos DOM. A posição ideal é uma profundidade de árvore com menos de 32 elementos e menos de 60 elementos filhos/pais. Um DOM grande pode aumentar o uso da memória, causar [cálculos de estilo](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) mais longos e produzir [reflows de layout](https://developers.google.com/speed/articles/reflow) dispendiosos. [Saiba mais](https://web.dev/dom-size)."}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 elemento}one{# elemento}other{# elementos}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Evite DOM de tamanho excessivo"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Profundidade máxima de DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Total de elementos DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Máximo de elementos filhos"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "Evita DOM de tamanho excessivo"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "Adicione `rel=\"noopener\"` ou `rel=\"noreferrer\"` a qualquer link externo para melhorar o desempenho e evitar vulnerabilidades de segurança. [Sai<PERSON> mais](https://web.dev/external-anchors-use-rel-noopener)."}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "Links para destinos de origem cruzada não são seguros"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "Links para destinos de origem cruzada são seguros"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "Não é possível determinar o destino do item fixo ({anchorHTML}). Se não for usado como um hiperlink, remova target=_blank."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Os usuários não confiam ou ficam confusos com sites que solicitam a localização sem contexto. Vincule a solicitação a uma ação do usuário. [Sai<PERSON> mais](https://web.dev/geolocation-on-start)."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Solicita a permissão de geolocalização no carregamento de página"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Evita o pedido da permissão de geolocalização no carregamento de página"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Vers<PERSON>"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "Todas as bibliotecas JavaScript de front-end detectadas na página. [<PERSON><PERSON> ma<PERSON>](https://web.dev/js-libraries)."}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "Bibliotecas JavaScript detectadas"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "Para usuários em conexões lentas, os scripts externos injetados dinamicamente via `document.write()` podem atrasar o carregamento de página em dezenas de segundos. [<PERSON><PERSON> mais](https://web.dev/no-document-write)."}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Usa `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "Evita `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "Versão da biblioteca"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "Contagem de vulnerabilidades"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "Alguns scripts de terceiros podem conter vulnerabilidades de segurança conhecidas, facilmente identificadas e exploradas por invasores. [Saiba mais](https://web.dev/no-vulnerable-libraries)."}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{1 vulnerabilidade detectada}one{# vulnerabilidade detectada}other{# vulnerabilidades detectadas}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "Inclui bibliotecas JavaScript de front-end com vulnerabilidades de segurança conhecidas"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "Alto"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "Baixo"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "Médio"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "Evita bibliotecas JavaScript de front-end com vulnerabilidades de segurança conhecidas"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "Os usuários não confiam ou ficam confusos com sites que solicitam o envio de notificações sem contexto. Vincule a solicitação a gestos do usuário. [<PERSON><PERSON> mais](https://web.dev/notification-on-start)."}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Solicita a permissão de notificação no carregamento de página"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "Evita o pedido da permissão de notificação no carregamento de página"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "Elementos com falha"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "Impedir a colagem da senha prejudica a política de boa segurança. [<PERSON><PERSON> mais](https://web.dev/password-inputs-can-be-pasted-into)."}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "Evita que o usuário cole nos campos de senha"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "Permite que o usuário cole nos campos de senha"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protocolo"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 oferece muitos benefícios com relação ao HTTP/1.1, incluindo cabeçalhos binários, multiplexação e push do servidor. [<PERSON><PERSON> mais](https://web.dev/uses-http2)."}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 solicitação não veiculada via HTTP/2}one{# solicitação não veiculada via HTTP/2}other{# solicitações não veiculadas via HTTP/2}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "Não utiliza HTTP/2 para todos os recursos"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "Usa HTTP/2 para os próprios recursos"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Marque os listeners de eventos de toque e rolagem como `passive` para melhorar o desempenho de rolagem da página. [<PERSON><PERSON> ma<PERSON>](https://web.dev/uses-passive-event-listeners)."}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Não utiliza listeners passivos para melhorar o desempenho de rolagem"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Utiliza listeners passivos para melhorar o desempenho de rolagem"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "Descrição"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "Os erros registrados no console indicam problemas não resolvidos. Eles podem ocorrer devido a falhas de solicitação de rede e outras questões relacionadas ao navegador. [Sai<PERSON> mais](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "Erros do navegador foram registrados no console"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "Nenhum erro do navegador registrado no console"}, "lighthouse-core/audits/font-display.js | description": {"message": "Use o recurso CSS de exibição de fonte para garantir que o texto possa ser visto pelo usuário enquanto as webfonts s<PERSON> carregadas. [Sai<PERSON> mais](https://web.dev/font-display)."}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "Garanta que o texto continue visível durante o carregamento da webfont"}, "lighthouse-core/audits/font-display.js | title": {"message": "Todo o texto continua visível durante o carregamento da webfont"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "O Lighthouse não conseguiu verificar automaticamente o valor de exibição da fonte para o seguinte URL: {fontURL}."}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "Proporção (real)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Proporção (exibida)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "As dimensões de exibição da imagem devem corresponder à proporção. [<PERSON><PERSON> ma<PERSON>](https://web.dev/image-aspect-ratio)."}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "Exibe imagens com a proporção incorreta"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "Exibe imagens com a proporção correta"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "Informações inválidas para tamanho de imagem {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "Os navegadores podem solicitar automaticamente aos usuários que adicionem o app à tela inicial deles, o que pode aumentar a interação. [Saiba mais](https://web.dev/installable-manifest)."}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "O manifesto do app da Web não atende os requisitos de instabilidade."}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "O manifesto do app de Web atende aos requisitos de instabilidade."}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "URL não seguro"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "Todos os sites devem ser protegidos com HTTPS, mesmo aqueles que não lidam com dados confidenciais. O HTTPS evita que invasores falsifiquem ou escutem passivamente a comunicação entre o app e o usuário, além de ser um pré-requisito para HTTP/2 e muitas novas APIs para plataforma da Web. [Saiba mais](https://web.dev/is-on-https)."}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 solicitação não segura encontrada}one{# solicitação não segura encontrada}other{# solicitações não seguras encontradas}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "Não utiliza HTTPS"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "Utiliza HTTPS"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "Um carregamento de página rápido em uma rede celular garante uma boa experiência do usuário em dispositivos móveis. [<PERSON><PERSON> mais](https://web.dev/load-fast-enough-for-pwa)."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "Interativa em {timeInMs, number, seconds} s"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "Interativa na rede móvel simulada em {timeInMs, number, seconds} s"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "Esta página está carregando muito lentamente e está há 10 segundos sem interagir. Consulte a seção \"Desempenho\" e veja oportunidades e diagnósticos para saber como melhorar o desempenho."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "A velocidade de carregamento da página não é suficiente em redes móveis"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "A velocidade de carregamento da página é suficiente em redes móveis"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Categoria"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "Considere diminuir o tempo gasto na análise, compilação e execução de JS. Você perceberá que exibir payloads de JS menores ajuda a fazer isso. [<PERSON><PERSON> mais](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimize o trabalho da thread principal"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "Minimiza o trabalho da thread principal"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "Os sites precisam funcionar em todos os principais navegadores para que sejam acessíveis ao maior número de usuários. [Saiba mais](https://web.dev/pwa-cross-browser)."}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "O site funciona em diferentes navegadores"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "É necessário que as páginas individuais permitam links diretos via URL e que os URLs sejam exclusivos para o propósito de compartilhamento em mídias sociais. [Sai<PERSON> mais](https://web.dev/pwa-each-page-has-url)."}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Cada página tem um URL."}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "As transições precisam ser dinâmicas durante a navegação por toque, mesmo em uma conexão de rede lenta. Essa experiência é essencial para a percepção de desempenho. [<PERSON><PERSON> mais](https://web.dev/pwa-page-transitions)."}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "As transições de página não devem parecer bloqueadas pelo carregamento da rede."}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "\"Latência de entrada estimada\" é uma estimativa de quanto tempo o app leva para responder à entrada do usuário, em milésimos de segundo, durante a janela de carregamento de página mais movimentada de cinco segundos. Se a latência for maior que 50 ms, o usuário poderá notar lentidão no app. [Saiba mais](https://web.dev/estimated-input-latency)."}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "Latência de entrada estimada"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "\"Primeira exibição de conteúdo\" marca o momento em que o primeiro texto ou imagem é disponibilizado. [Sai<PERSON> mais](https://web.dev/first-contentful-paint)."}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "Primeiro aparecimento com conteúdo"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "\"Primeira CPU ociosa\" marca a primeira vez que a linha de execução principal da página fica silenciosa o suficiente para lidar com a entrada.  [Saiba mais](https://web.dev/first-cpu-idle)."}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "Primeira CPU ociosa"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "\"Primeira exibição significativa\" marca o momento em que o conteúdo principal de uma página se torna visível. [<PERSON><PERSON> ma<PERSON>](https://web.dev/first-meaningful-paint)."}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "Primeira exibição importante"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "\"Tempo para interação da página\" é o período necessário para que ela fique totalmente interativa. [Saiba mais](https://web.dev/interactive)."}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "Tempo até ficar interativa"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "A possível latência máxima na primeira entrada que seus usuários notariam seria a duração, em milésimos de segundo, da tarefa mais longa. [<PERSON><PERSON> ma<PERSON>](https://developers.google.com/web/updates/2018/05/first-input-delay)."}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "Possível latência máxima na primeira entrada"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "\"Índice de velocidade\" mostra a rapidez com que o conteúdo de uma página é preenchido visivelmente. [<PERSON><PERSON> ma<PERSON>](https://web.dev/speed-index)."}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "Índice de velocidade"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "Soma de todos os períodos entre \"FCP\" e \"Tempo para interação da página\", quando a duração da tarefa ultrapassa 50 ms, expressa em milésimos de segundo."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "Tempo total de bloqueio"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "O tempo de retorno (RTT, na sigla em inglês) da rede tem um grande impacto no desempenho. Se o RTT para uma origem é alto, significa que os servidores mais próximos do usuário poderiam melhorar o desempenho. [<PERSON><PERSON> mais](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "lighthouse-core/audits/network-rtt.js | title": {"message": "Tempos de retorno da rede"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "As latências de servidor podem afetar o desempenho na Web. Se a latência do servidor de uma origem for alta, isso indica que ele está sobrecarregado ou que o back-end apresenta um desempenho ruim. [<PERSON><PERSON> mais](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "Latências do back-end do servidor"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "Um service worker permite que seu app da Web funcione de maneira confiável em condições imprevisíveis de rede. [<PERSON><PERSON> mais](https://web.dev/offline-start-url)."}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url` não responde com um código 200 quando off-line."}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url` responde com um código 200 quando off-line."}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "A propriedade `start_url` não pode ser lida pelo Lighthouse no manifesto. Como resultado, a URL do documento foi considerada como `start_url`. Mensagem de erro: \"{manifestWarning}\"."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "Acima do orçamento"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "Mantenha a quantidade e o tamanho de solicitações de rede de acordo com os destinos definidos pelo orçamento de desempenho informado. [Saiba mais](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 solicitação}one{# solicitação}other{# solicitações}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "Orçamento de desempenho"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "Se você já definiu o HTTPS, redirecione todo o tráfego HTTP para HTTPS para garantir recursos da Web para todos os seus usuários. [<PERSON><PERSON> mais](https://web.dev/redirects-http)."}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "O tráfego HTTP não é redirecionado para HTTPS"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "O tráfego HTTP é redirecionado para HTTPS"}, "lighthouse-core/audits/redirects.js | description": {"message": "Os redirecionamentos causam mais atrasos antes do carregamento da página. [Sai<PERSON> mais](https://web.dev/redirects)."}, "lighthouse-core/audits/redirects.js | title": {"message": "Evite redirecionamentos múltiplos de página"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "Para definir orçamentos para a quantidade e o tamanho dos recursos da página, adicione um arquivo budget.json. [Sai<PERSON> ma<PERSON>](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 solicitação de • {byteCount, number, bytes} KB}one{# solicitação de • {byteCount, number, bytes} KB}other{# solicitações de • {byteCount, number, bytes} KB}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "Man<PERSON>ha as contagens de solicitações baixas e os tamanhos de transferência pequenos"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "<PERSON>s links canônicos sugerem o URL a ser exibido nos resultados da pesquisa. [Sai<PERSON> mais](https://web.dev/canonical)."}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "Vários URLs com conflito ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "Aponta para um domínio diferente ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "URL inválido ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Aponta para outro local de `hreflang` ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "URL relativo ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "Aponta para o URL raiz do domínio (a página inicial), em vez de uma página de conteúdo equivalente"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "O documento não tem um `rel=canonical` válido"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "O documento tem um `rel=canonical` válido"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "Fontes menores que 12 px não são legíveis e exigem que os visitantes que utilizam dispositivos móveis façam um gesto de pinça para aumentar o zoom e conseguir ler. Faça o possível para ter mais de 60% do texto da página com pelo menos 12 px. [<PERSON><PERSON> mais](https://web.dev/font-size)."}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} do texto legível"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "O texto é ilegível porque não há nenhuma metatag de janela de visualização otimizada para telas de dispositivos móveis."}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "{decimalProportion, number, extendedPercent} do texto é muito pequeno (com base na amostra de {decimalProportionVisited, number, extendedPercent})."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "O documento não usa tamanhos de fonte legíveis"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "O documento usa tamanhos de fonte legíveis"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "<PERSON><PERSON> links hreflang informam aos mecanismos de pesquisa qual versão de uma página deve ser listada nos resultados de pesquisa para um determinado idioma ou região. [Sai<PERSON> mais](https://web.dev/hreflang)."}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "O documento não tem um `hreflang` válido"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "O documento tem um `hreflang` v<PERSON>lido"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "As páginas com falha no código de status HTTP talvez não sejam indexadas corretamente. [Saiba mais](https://web.dev/http-status-code)."}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "A página tem uma falha no código de status HTTP"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "A página tem um código de status HTTP bem-sucedido"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "Os mecanismos de pesquisa não poderão incluir suas páginas nos resultados se não tiverem permissão para rastreá-las. [<PERSON><PERSON> mais](https://web.dev/is-crawable)."}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "A página está bloqueada para indexação"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "A página não está bloqueada para indexação"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "O texto com link descritivo ajuda os mecanismos de pesquisa a entender o conteúdo. [Sai<PERSON> mais](https://web.dev/link-text)."}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 link encontrado}one{# link encontrado}other{# links encontrados}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "<PERSON><PERSON> links não têm texto descritivo"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "<PERSON>s links têm texto descritivo"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "Execute a [Ferramenta de teste de dados estruturados](https://search.google.com/structured-data/testing-tool/) e a [Structured Data Linter](http://linter.structured-data.org/) para validar os dados estruturados. [Sai<PERSON> mais](https://web.dev/structured-data)."}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "Os dados estruturados são válidos"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "Metadescrições podem ser incluídas nos resultados da pesquisa para resumir concisamente o conteúdo da página. [Sai<PERSON> mais](https://web.dev/meta-description)."}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "O campo de texto da descrição está vazio."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "O documento não tem uma metadescrição"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "O documento tem uma metadescrição"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "Mecanismos de pesquisa não podem indexar conteúdo de plug-in, e muitos dispositivos restringem plug-ins ou não são compatíveis com eles. [<PERSON><PERSON> mais](https://web.dev/plugins)."}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "O documento usa plug-ins"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "O documento evita plug-ins"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "Se o arquivo robots.txt for inválido, talvez não seja possível aos rastreadores entender como você quer que seu site seja rastreado ou indexado. [Sai<PERSON> mais](https://web.dev/robots-txt)."}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "A solicitação para robots.txt retornou o status HTTP: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 erro encontrado}one{# erro encontrado}other{# erros encontrados}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "O Lighthouse não fez o download de um arquivo robots.txt"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt não é válido"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "robots.txt é válido"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "Os elementos interativos, como botões e links, precisam ser grandes o bastante (48x48 px) e ter espaço suficiente ao redor para poderem ser tocados sem sobreposição com outros elementos. [<PERSON><PERSON> ma<PERSON>](https://web.dev/tap-targets)."}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} de áreas de toque dimensionadas corretamente"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "As áreas de toque são muito pequenas porque não há nenhuma metatag de janela de visualização otimizada para telas de dispositivos móveis"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "As áreas de toque não estão dimensionadas corretamente"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Área com sobreposição"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "As áreas de toque estão dimensionadas corretamente"}, "lighthouse-core/audits/service-worker.js | description": {"message": "O service worker é a tecnologia que permite que seu app use muitos recursos do Progressive Web App, como disponibilidade off-line, adicionar à tela inicial, e notificações de push. [<PERSON><PERSON> mais](https://web.dev/service-worker)."}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "Esta página é controlada por um service worker, no entanto, nenhum `start_url` foi encontrado porque o manifesto não foi analisado como um JSON válido."}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "Esta página é controlada por um service worker, no entanto, a `start_url` ({startUrl}) não está no escopo dele ({scopeUrl})"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "Esta página é controlada por um service worker, no entanto, nenhuma `start_url` foi encontrada porque nenhum manifesto foi recuperado."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "Esta origem tem um ou mais service workers, no entanto, a página ({pageUrl}) não está em escopo."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "Não há registro de um service worker que controle a página e `start_url`"}, "lighthouse-core/audits/service-worker.js | title": {"message": "Há registro de um service worker que controla a página e `start_url`"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "Uma tela de apresentação personalizada garante uma experiência de alta qualidade quando os usuários abrem o aplicativo na tela inicial. [<PERSON><PERSON> ma<PERSON>](https://web.dev/splash-screen)."}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "Não foi configurado para uma tela de apresentação personalizada"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "Configurado para uma tela de apresentação personalizada"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "É possível atribuir um tema relacionado ao seu site à barra de endereço do navegador. [Saiba mais](https://web.dev/themed-omnibox)."}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "Não foi definida uma cor de tema para a barra de endereços."}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "Foi definida uma cor de tema para a barra de endereços."}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "Tempo de bloqueio da linha de execução principal"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "Te<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "Código de terceiros pode afetar significativamente o desempenho de carregamento. Limite o número de provedores terceiros redundantes e carregue o código de terceiros depois que a página tiver sido carregada. [<PERSON><PERSON> mais](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "A linha de execução principal foi bloqueada por {timeInMs, number, milliseconds} ms pelo código de terceiros"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "Reduza o impacto de códigos de terceiros"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "Uso de terceiros"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "\"Tempo até o primeiro byte\" identifica o momento em que o servidor envia uma resposta. [<PERSON><PERSON> <PERSON><PERSON>](https://web.dev/time-to-first-byte)."}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "O documento raiz levou {timeInMs, number, milliseconds} ms"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "Reduza os tempos de resposta do servidor (TTFB)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "Os tempos de resposta do servidor são baixos (TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "Duração"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "<PERSON><PERSON><PERSON><PERSON> iní<PERSON>"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "Tipo"}, "lighthouse-core/audits/user-timings.js | description": {"message": "Instrumente seu app com a API User Timing para avaliar o desempenho real do app durante as principais experiências do usuário. [Saiba mais](https://web.dev/user-timings)."}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 velocidade do usuário}one{# velocidade do usuário}other{# velocidades do usuário}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "Marcações e medições de User Timing"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Um <link> de pré-conexão foi encontrado para \"{securityOrigin}\", mas não foi utilizado pelo navegador. Verifique se você está usando o atributo `crossorigin` corretamente."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "Adicione dicas de recursos de `preconnect` ou `dns-prefetch` para estabelecer conexões antecipadas a origens importantes de terceiros. [Saiba mais](https://web.dev/uses-rel-preconnect)."}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "Pré-conecte às origens necessárias"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Um <link> de pré-carregamento foi encontrado para \"{preloadURL}\", mas não foi usado pelo navegador. Verifique se você está usando o atributo `crossorigin` corretamente."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "Use `<link rel=preload>` para priorizar a busca de recursos que, no momento, são solicitados posteriormente no carregamento de página. [<PERSON><PERSON> mais](https://web.dev/uses-rel-preload)."}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as principais solicitaç<PERSON>es"}, "lighthouse-core/audits/viewport.js | description": {"message": "Adicione uma tag `<meta name=\"viewport\">` para otimizar seu app para telas de dispositivos móveis. [Sai<PERSON> ma<PERSON>](https://web.dev/viewport)."}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "Nenhuma tag `<meta name=\"viewport\">` foi encontrada."}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "Não há uma tag `<meta name=\"viewport\">` com `width` ou `initial-scale` definida"}, "lighthouse-core/audits/viewport.js | title": {"message": "Há uma tag `<meta name=\"viewport\">` com `width` ou `initial-scale`"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "Seu app precisa mostrar algum conteúdo quando o JavaScript está desativado, mesmo que seja somente um aviso de que o JavaScript é necessário para o uso do app. [<PERSON><PERSON> mais](https://web.dev/without-javascript)."}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "O corpo da página precisa renderizar algum conteúdo se os scripts não estão disponíveis."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "Nenhum conteúdo substituto é exibido quando o JavaScript não está disponível."}, "lighthouse-core/audits/without-javascript.js | title": {"message": "Algum conteúdo é exibido quando o JavaScript não está disponível."}, "lighthouse-core/audits/works-offline.js | description": {"message": "Se você estiver criando um Progressive Web App, é recomendável usar um service worker para que seu app possa funcionar off-line. [<PERSON><PERSON> mais](https://web.dev/works-offline)."}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "A página atual não responde com um código 200 quando off-line"}, "lighthouse-core/audits/works-offline.js | title": {"message": "A página atual responde com um código 200 quando off-line"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "A página pode não carregar off-line porque sua URL de teste ({requested}) foi redirecionada para \"{final}\". Tente a segunda URL diretamente."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "Veja aqui oportunidades de melhorar o uso de ARIA no seu aplicativo, o que pode aprimorar a experiência dos usuários de tecnologias assistivas, como leitores de tela."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Veja aqui oportunidades de oferecer conteúdo alternativo para áudio e vídeo. <PERSON><PERSON> pode melhorar a experiência de usuários com deficiências auditivas e visuais."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Áudio e vídeo"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Esses itens destacam as práticas recomendadas comuns para acessibilidade."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Práticas recomendadas"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "Essas verificações destacam oportunidades para [melhorar a acessibilidade do seu app da Web](https://developers.google.com/web/fundamentals/accessibility). Somente um subconjunto de problemas de acessibilidade pode ser detectado automaticamente, portanto, testes manuais também devem ser realizados."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "Esses itens se referem a áreas que uma ferramenta de teste automatizada não pode cobrir. Saiba mais no nosso guia sobre [como realizar uma avaliação de acessibilidade](https://developers.google.com/web/fundamentals/accessibility/how-to-review)."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "Acessibilidade"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Veja aqui oportunidades de melhorar a legibilidade do seu conteúdo."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Contraste"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Veja aqui oportunidades de melhorar a interpretação que usuários de diferentes localidades fazem do seu conteúdo."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internacionalização e localização"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Veja aqui oportunidades de melhorar a semântica dos controles do seu aplicativo. <PERSON><PERSON> pode melhorar a experiência de usuários de tecnologias assistivas, como leitores de tela."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Nomes e etiquetas"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Veja aqui oportunidades de melhorar a navegação por teclado no seu aplicativo."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navegação"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Veja aqui oportunidades de melhorar a experiência de leitura de dados em tabelas ou listas usando tecnologia assistiva, como um leitor de tela."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tabelas e listas"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Práticas recomendadas"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "Os orçamentos de desempenho definem padrões para o desempenho do seu site."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "Orçamentos"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "Mais informações sobre o desempenho do seu aplicativo. Esses números não [afetam diretamente](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) o índice de desempenho."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnós<PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "O aspecto de desempenho mais importante é a rapidez com que os pixels são renderizados na tela. Principais métricas: Primeiro aparecimento com conteúdo, Primeira exibição importante"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Melhorias do primeiro aparecimento"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "<PERSON>ssas sugestões podem ajudar a acelerar o carregamento de página. Elas não [afetam diretamente](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) o índice de desempenho."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Oportunidades"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "Métricas"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Aprimore a experiência geral de carregamento, para que a página seja responsiva e esteja pronta para ser usada assim que possível. Principais métricas: Tempo até fica interativa, Índice de velocidade"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "<PERSON><PERSON><PERSON> gera<PERSON>"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "Estas verificações validam os aspectos de um Progressive Web App. [<PERSON><PERSON> <PERSON>](https://developers.google.com/web/progressive-web-apps/checklist)."}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "Essas verificações são solicitadas pela [Lista de verificação de PWA](https://developers.google.com/web/progressive-web-apps/checklist) de referência, mas não são automaticamente realizadas pelo Lighthouse. Elas não afetam sua pontuação, mas é importante verificá-las manualmente."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "Progressive Web App"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "Rápido e confiável"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Pode ser instalado"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Otimizado para PWA"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "Essas verificações garantem que sua página seja otimizada para a classificação dos resultados dos mecanismos de pesquisa. Existem outros fatores que o Lighthouse não verifica e que podem afetar a classificação da pesquisa. [<PERSON><PERSON> ma<PERSON>](https://support.google.com/webmasters/answer/35769)."}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "Execute estes validadores adicionais no seu site para verificar mais práticas recomendadas de SEO."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "Formate seu HTML de maneira que permita que os rastreadores entendam melhor o conteúdo do seu app."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "Práticas recomendadas para conteúdo"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Para que seu app seja exibido nos resultados da pesquisa, é necessário que os rastreadores tenham acesso a ele."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Rastreamento e indexação"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "Suas páginas precisam ser compatíveis com dispositivos móveis, para que o usuário não tenha que usar gestos de pinça ou zoom para ler o conteúdo. [Sai<PERSON> mais](https://developers.google.com/search/mobile-sites/)."}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "Otimizada para dispositivos móveis"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "Localização"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "Nome"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "Solicitações"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "Tipo de recurso"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Tempo gasto"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Possível economia"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "Possível economia"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Possível economia de {wastedBytes, number, bytes} KB"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Possível economia de {wastedMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "Documento"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "Fonte"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "Imagem"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "Mí<PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "Outro"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Folha de estilo"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Te<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "Total"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "Ocorreu um erro com a gravação dos rastros no carregamento de página. Execute o Lighthouse novamente. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "Tempo limite atingido ao aguardar a conexão inicial do protocolo do depurador."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "O Chrome não coletou nenhuma captura de tela durante o carregamento de página. Verifique se há conteúdo visível na página e execute o Lighthouse novamente. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "Os servidores DNS não resolveram o domínio fornecido."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "O coletor {artifactName} obrigatório encontrou um erro: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "Ocorreu um erro interno do Chrome. Reinicie o Chrome e tente executar o Lighthouse novamente."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "O coletor {artifactName} obrigatório não foi executado."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "O Lighthouse não carregou de maneira confiável a página solicitada. Verifique se você está testando o URL correto e se o servidor está respondendo de forma adequada a todas as solicitações."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "O Lighthouse não carregou de maneira confiável o URL solicitado, porque a página parou de responder."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "O URL informado não tem um certificado de segurança válido. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "O Chrome impediu o carregamento da página com um intersticial. Verifique se você está testando o URL correto e se o servidor está respondendo de forma adequada a todas as solicitações."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "O Lighthouse não carregou de maneira confiável a página solicitada. Verifique se você está testando o URL correto e se o servidor está respondendo de forma adequada a todas as solicitações. (Detalhes: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "O Lighthouse não carregou de maneira confiável a página solicitada. Verifique se você está testando o URL correto e se o servidor está respondendo de forma adequada a todas as solicitações. (Código de status: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "A página demorou muito para ser carregada. Siga as oportunidades no relatório para diminuir o tempo de carregamento da página, depois execute o Lighthouse novamente. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "A espera pela resposta do protocolo DevTools excedeu o tempo limite. (Método: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "A busca de conteúdo de recursos excedeu o tempo limite"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "O URL informado parece ser inválido."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "Mostrar auditorias"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "Navegação inicial"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "Latência máxima do caminho crítico:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "Erro!"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "Informar erro: nenhuma informação de auditoria"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "Dados de laboratório"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "An<PERSON><PERSON><PERSON> do [Lighthouse](https://developers.google.com/web/tools/lighthouse/) da página atual em uma rede móvel emulada. Os valores são estimados e podem variar."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "Outros itens para verificação manual"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "Não aplicável"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "Oportunidade"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "Economia estimada"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "Auditorias aprovadas"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> snippet"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "Expandir snippet"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "Mostrar recursos de terceiros"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "Alguns problemas afetaram esta execução do Lighthouse:"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "Os valores são estimados e podem variar. O índice de desempenho [se baseia somente nessas métricas](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "Passou nas auditorias, mas como avisos"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "Avisos: "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "Faça upload do seu GIF para um serviço que o disponibilizará para incorporação como um vídeo HTML5."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "Instale um [plug-in de carregamento lento do WordPress](https://wordpress.org/plugins/search/lazy+load/), que permite adiar imagens fora da tela, ou alterne para um tema que ofereça essa funcionalidade. Também recomendamos o uso do [plug-in de AMP](https://wordpress.org/plugins/amp/)."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "Existem vários plug-ins do WordPress que podem ajudar você a [aplicar inline a recursos essenciais](https://wordpress.org/plugins/search/critical+css/) ou [adiar recursos menos importantes](https://wordpress.org/plugins/search/defer+css+javascript/). As otimizações oferecidas por esses plug-ins podem corromper os recursos do tema ou dos seus plug-ins, então é provável que você precise fazer alterações no código."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "Todas as especificações de servidor, temas e plug-ins contribuem para o tempo de resposta do servidor. Recomendamos que você use um tema mais otimizado, selecionando cuidadosamente um plug-in de otimização e/ou fazendo upgrade do seu servidor."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "Recomendamos que você mostre trechos nas suas listas de postagem (por exemplo, por meio da tag \"mais\"), reduza o número de postagens exibidas em uma determinada página, divida suas postagens longas em várias páginas ou use um plug-in para aplicar lazy-load nos comentários."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "Vários [plug-ins do WordPress](https://wordpress.org/plugins/search/minify+css/) podem acelerar seu site concatenando, reduzindo e compactando seus estilos. Você também pode usar um processo de compilação para fazer essa redução antecipadamente, se possível."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "Vários [plug-ins do WordPress](https://wordpress.org/plugins/search/minify+javascript/) podem acelerar seu site concatenando, reduzindo e compactando seus scripts. Você também pode usar um processo de compilação para fazer essa redução antecipadamente, se possível."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "Reduza ou troque o número de [plug-ins do WordPress](https://wordpress.org/plugins/) que carregam CSS não utilizado na sua página. Para identificar plug-ins que estão adicionando CSS externo, execute a [cobertura do código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) no Chrome DevTools. Você pode identificar o tema/plug-in responsável a partir do URL da folha de estilo. Procure plug-ins que tenham muitas folhas de estilo na lista, apresentando um nível alto de vermelho na cobertura do código. Um plug-in só deverá colocar uma folha de estilo na fila se ela for realmente utilizada na página."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "Reduza ou troque o número de [plug-ins do WordPress](https://wordpress.org/plugins/) que carregam JavaScript não utilizado na sua página. Para identificar plug-ins que estão adicionando JS externo, execute a [cobertura do código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) no Chrome DevTools. Você pode identificar o tema/plug-in responsável a partir do URL do script. Procure plug-ins que tenham muitos scripts na lista, apresentando um nível alto de vermelho na cobertura do código. Um plug-in só deverá colocar um script na fila se ele for realmente usado na página."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "<PERSON><PERSON> sobre o [Processo de cache do navegador no WordPress](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "Use um [plug-in do WordPress para otimização de imagens](https://wordpress.org/plugins/search/optimize+images/), que as compacta sem afetar a qualidade."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "Faça upload de imagens diretamente por meio da [biblioteca de mídia](https://codex.wordpress.org/Media_Library_Screen) para garantir que os tamanhos de imagem necessários estejam disponíveis. Depois, insira-os na biblioteca de mídia ou use o widget de imagem para garantir que os tamanhos ideais sejam usados (incluindo aqueles para os pontos de interrupção responsivos). Evite usar imagens `Full Size`, a não ser que as dimensões sejam adequadas para uso. [Sai<PERSON> mais](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)."}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "Você pode ativar a compactação de texto na configuração do servidor da Web."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "Use um [plug-in](https://wordpress.org/plugins/search/convert+webp/) ou serviço que converta automaticamente as imagens enviadas nos formatos ideais."}}