{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "Piekļuves atslēgas ļauj lietotājiem ātri pievērsties lapas daļai. Lai navigācija būtu parei<PERSON>, katrai piekļuves atslēgai ir jābūt unikālai. [Uzziniet vairāk](https://web.dev/accesskeys/)."}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `[accesskey]` vē<PERSON><PERSON><PERSON> nav unikālas"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> “`[accesskey]`” v<PERSON><PERSON><PERSON><PERSON> ir un<PERSON>"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Katrs ARIA elements “`role`” atbalsta konkrētu atribūtu “`aria-*`” apakškopu. Ja tie netiek norādīti parei<PERSON>, atrib<PERSON>ti “`aria-*`” nav derīgi. [Uzziniet vairāk](https://web.dev/aria-allowed-attr/)."}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `[aria-*]` neatbilst savām lomām"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "At<PERSON><PERSON><PERSON><PERSON> `[aria-*]` atbilst savām lomām"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "Dažām ARIA lomām ir obligāti at<PERSON>, kas ekr<PERSON>a las<PERSON>tājiem norāda elementa statusu. [Uzziniet vairāk](https://web.dev/aria-required-attr/)."}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Elementiem `[role]` nav visu pieprasīto <PERSON> `[aria-*]`"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "Visiem element<PERSON> `[role]` ir <PERSON><PERSON><PERSON><PERSON><PERSON> atribūti `[aria-*]`"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "Dažām ARIA vecāklomām ir jāietver konkrētas pakārtotā<PERSON> lo<PERSON>, lai varētu nodro<PERSON>t pieejamības funkcijas. [Uzziniet vairāk](https://web.dev/aria-required-children/)."}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Elementos ar <PERSON> lomu `[role]`, kuru pakārtotajiem elementiem ir jāsatur konkrēts vienums `[role]`, trū<PERSON>t dažu vai visu šo obligāto pakārtoto elementu."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "Elementos ar <PERSON> lomu `[role]`, kuru pakārtotajiem elementiem ir jāsatur konkrēts vienums `[role]`, ir visi obligātie pakārtotie elementi."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "Dažām ARIA pakārtotajām lomām ir jābūt ietvertām konkrētās ve<PERSON>, lai varētu nodro<PERSON>t pieejamības funkcijas. [Uzziniet vairāk](https://web.dev/aria-required-parent/)."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `[role]` ne<PERSON>ver pie<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>[role]` ir iet<PERSON>s piepras<PERSON>tais vecākel<PERSON>s"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "ARIA lomām ir nepiecie<PERSON> der<PERSON> v<PERSON>, lai varētu nod<PERSON>t pieejamības funkcijas. [Uzziniet vairāk](https://web.dev/aria-roles/)."}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `[role]` v<PERSON><PERSON><PERSON><PERSON> nav derīgas"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `[role]` v<PERSON><PERSON><PERSON><PERSON> ir der<PERSON>gas"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, e<PERSON><PERSON><PERSON><PERSON>, nevar interpretēt ARIA atribūtus ar nederīgām vērtībām. [Uzziniet vairāk](https://web.dev/aria-valid-attr-value/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Atri<PERSON><PERSON><PERSON>m `[aria-*]` nav derīgu vērtību"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "At<PERSON><PERSON><PERSON><PERSON>m `[aria-*]` ir der<PERSON><PERSON> vērt<PERSON>"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, e<PERSON><PERSON><PERSON><PERSON>, nevar interpretēt ARIA atribūtus ar nederīgiem nosaukumiem. [Uzziniet vairāk](https://web.dev/aria-valid-attr/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `[aria-*]` nav derīgi vai ir k<PERSON>daini u<PERSON>ti"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `[aria-*]` ir derīgi un pareizi u<PERSON>ti"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> subtitrus, ne<PERSON><PERSON><PERSON><PERSON><PERSON> lietotāji vai lietotāji ar dzirdes traucējumiem var piekļūt audio elementiem, k<PERSON> <PERSON><PERSON> būtisku informāciju, pie<PERSON><PERSON><PERSON>, run<PERSON><PERSON><PERSON><PERSON><PERSON>, sarunu saturu, kā arī citu informāciju, kas nav sarunas. [Uzziniet vairāk](https://web.dev/audio-caption/)."}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "Elementos `<audio>` trūkst elementu `<track>` ar parametru `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "Elementi `<audio>` ietver elementu `<track>` ar parametru `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "<PERSON><PERSON><PERSON>gi <PERSON>i"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "Ja pogai nav at<PERSON><PERSON><PERSON>, ko var nolasīt ekrāna las<PERSON>, tad ekrāna lasīt<PERSON> to atskaņo kā “<PERSON>”. Tād<PERSON><PERSON><PERSON><PERSON> lieto<PERSON>, kuri i<PERSON> ekr<PERSON> las<PERSON>, ne<PERSON><PERSON><PERSON><PERSON><PERSON> tā<PERSON>. [Uzziniet vairāk](https://web.dev/button-name/)."}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "Pogām nav piekļūstamības principiem atbilstošu nosaukumu"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "Pogām ir piekļūstamības principiem atbilstoši nosaukumi"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "<PERSON>a <PERSON><PERSON>t iespēju apiet atkārtotu saturu, tastatūras lietotāji varēs labāk pārvietoties lapā. [Uzziniet vairāk](https://web.dev/bypass/)."}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "Lapā nav v<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nas saites vai orientieru daļas"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "<PERSON><PERSON><PERSON> ir v<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> saite vai orientieru da<PERSON>a"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "<PERSON>ud<PERSON><PERSON>em ir grūti vai pat <PERSON> i<PERSON>t tekstu ar zemu kont<PERSON>. [Uzziniet vairāk](https://web.dev/color-contrast/)."}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Fona un priekšplāna krāsu kontrasta koeficients nav pietiekams."}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "Fona un priekšplāna krāsām ir pietiekams kontrasta koeficients"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "<PERSON>a defin<PERSON><PERSON>ju sa<PERSON>i nav marķēti par<PERSON>, e<PERSON><PERSON><PERSON><PERSON> atskaņota<PERSON> saturs var būt mulsino<PERSON> vai neprecīzs. [Uzziniet vairāk](https://web.dev/definition-list/)."}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "Atribūtā `<dl>` nav ietvertas tikai pareizā secībā sakārtotas elementu `<dt>` un `<dd>` grupas, elements `<script>` vai elements `<template>`"}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "Atribūtā `<dl>` ir ietvertas tikai pareizā secībā sakārtotas elementu `<dt>` un `<dd>` grupas, elements `<script>` vai elements `<template>`"}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "Defin<PERSON><PERSON><PERSON> sa<PERSON> vienumiem (`<dt>` un `<dd>`) ir jāb<PERSON>t ietvertiem vecākelementā `<dl>`, lai ekr<PERSON>a las<PERSON>tāji tos varētu pareizi atskaņot. [Uzziniet vairāk](https://web.dev/dlitem/)."}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "Definīciju saraksta vienumi netiek apvienoti elementos `<dl>`"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "Definīciju saraksta vienumi tiek apvienoti elementos `<dl>`"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "Nosaukums sniedz lapas kopsavilkumu ekrāna lasītāja lietotājiem, un meklētājprogrammas lietotāji ļoti paļaujas uz to, lai note<PERSON>, vai lapa ir at<PERSON> viņu meklē<PERSON>. [Uzziniet vairāk](https://web.dev/document-title/)."}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "Dokumentā nav elementa `<title>`"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "Dokumentā ir ietverts elements `<title>`"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "ID atribūta vērtībai ir jābūt unik<PERSON>lai, lai palīgtehnoloģijas neizlaistu citas instances. [Uzziniet vairāk](https://web.dev/duplicate-id/)."}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `[id]` lapā nav unikāli"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `[id]` lapā ir unikāli"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON> i<PERSON>varu <PERSON>, lai raksturotu ietvaru saturu. [Uzziniet vairāk](https://web.dev/frame-title/)."}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "Elementiem `<frame>` vai `<iframe>` nav nosaukuma"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "Elementiem `<frame>` vai `<iframe>` ir nosaukums"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "Ja lapā nav norādīts atribūts “lang”, ekr<PERSON>a lasītājā tiek pieņ<PERSON>, ka lapas saturs ir noklusējuma valodā, kuru lietotājs izvēlējā<PERSON>, iestatot ekrāna lasītāju. Ja lapas saturs nav noklusējuma valodā, i<PERSON><PERSON><PERSON><PERSON><PERSON>, ekrāna lasītājs tekstu neatskaņ<PERSON> pareizi. [Uzziniet vairāk](https://web.dev/html-has-lang/)."}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Tagam `<html>` nav derīga atribūta `[lang]`"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "Tagam `<html>` ir atri<PERSON> `[lang]`"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> derīgu [BCP 47 valodu](https://www.w3.org/International/questions/qa-choosing-language-tags#question) ), ek<PERSON><PERSON><PERSON> parei<PERSON> atskaņos tekstu. [Uzziniet vairāk](https://web.dev/html-lang-valid/)."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Tagam `<html>` nav derīgas vērtības tā atribūtam `[lang]`"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "Elementam `<html>` ir derīga tā atribūta `[lang]` vē<PERSON><PERSON>ba"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "Informatīvajiem elementiem ir nepieciešams īss, aprakstošs alternatīvais teksts. Dekoratīvajiem elementiem alternatīvo atribūtu var atstāt tukšu. [Uzziniet vairāk](https://web.dev/image-alt/)."}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "Attēlu elementiem nav atribūtu `[alt]`"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "Attēlu elementiem ir atribūti `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "<PERSON>a attēls tiek izmantots kā poga `<input>`, alternatīvais teksts var sniegt informāciju par pogas nozīmi lietotā<PERSON>em, kuri izmanto ekr<PERSON>a las<PERSON>tā<PERSON>. [Uzziniet vairāk](https://web.dev/input-image-alt/)."}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Elementiem `<input type=\"image\">` nav teksta `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "Elementi `<input type=\"image\">` ietver tekstu `[alt]`"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "<PERSON><PERSON> <PERSON><PERSON>, palīgtehnoloģijas, <PERSON><PERSON><PERSON><PERSON>, e<PERSON><PERSON><PERSON><PERSON>, varēs pareizi atskaņot veidlapu vadīkla<PERSON>. [Uzziniet vairāk](https://web.dev/label/)."}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "Veidlapu elementiem nav sa<PERSON>"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "Veidlapu elementiem ir sa<PERSON> i<PERSON>"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "<PERSON><PERSON><PERSON>, kas tiek izmantota izk<PERSON>, nav ieteicams iek<PERSON>aut datu elementus, pie<PERSON><PERSON><PERSON>, elementus “th” vai “caption”, kā ar<PERSON> at<PERSON> “summary”, jo tā var mulsin<PERSON>t lietot<PERSON>, kuri izmanto e<PERSON>r<PERSON>. [Uzziniet vairāk](https://web.dev/layout-table/)."}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "Attēlojumam paredzētajos elementos `<table>` tiek i<PERSON> atribūts `<th>`, `<caption>` vai `[summary]`"}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "Attēlojumam paredzētajos elementos `<table>` netiek i<PERSON>ts atribūts `<th>`, `<caption>` vai `[summary]`"}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "Atšķirams, unikāls un aktivizējams saites teksts (un alternatīvais teksts attēliem, kas tiek izmantoti kā saites) nodrošina labākas navigācijas iespējas lietotājiem, kuri izman<PERSON> e<PERSON>. [Uzziniet vairāk](https://web.dev/link-name/)."}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "Saitēm nav atšķirama nosaukuma"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> ir atšķirams nosaukums"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>a lasītāji nolasa sarakstus īpašā veidā. Ja saraksta struktūra ir pareiza, tiek atvieglota satura atskaņošana ekrāna lasītājā. [Uzziniet vairāk](https://web.dev/list/)."}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> ne<PERSON>ver tikai tos elementus `<li>` un skriptus, kas atbalsta elementus (`<script>` un `<template>`)"}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "<PERSON><PERSON><PERSON> ietver tikai elementus `<li>` un skriptus, kas atbalsta elementus (`<script>` un `<template>`)"}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "<PERSON> ekr<PERSON>a las<PERSON> varētu pareizi at<PERSON>ot sarak<PERSON> vienumus (`<li>`), tiem ir jābūt ietvertiem vecākelementos `<ul>` vai `<ol>`. [Uzziniet vairāk](https://web.dev/listitem/)."}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> vien<PERSON> (`<li>`) nav ietverti vecākelementos `<ul>` vai `<ol>`"}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "<PERSON><PERSON><PERSON> v<PERSON> (`<li>`) ir iet<PERSON>i ve<PERSON> `<ul>` vai `<ol>`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ka lapa tiks automātiski at<PERSON>, un tādējādi atkal tiks pāriets uz lapas augšdaļu. Tas var būt kaitinoši vai mulsinoši. [Uzziniet vairāk](https://web.dev/meta-refresh/)."}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Dokumentā tiek izmantots metatags `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "Dokumentā netiek izmantots metatags “`<meta http-equiv=\"refresh\">`”"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "<PERSON>a t<PERSON><PERSON><PERSON><PERSON><PERSON>a ir at<PERSON><PERSON><PERSON><PERSON>, lieto<PERSON><PERSON><PERSON><PERSON> ar redzes trauc<PERSON><PERSON><PERSON><PERSON>, kuri i<PERSON>to e<PERSON>r<PERSON>, ir grūt<PERSON><PERSON> piekļūt tīmekļa lapas saturam. [Uzziniet vairāk](https://web.dev/meta-viewport/)."}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "Parametrs `[user-scalable=\"no\"]` tiek lietots elementā `<meta name=\"viewport\">`, vai atrib<PERSON>ts `[maximum-scale]` ir mazāks par 5"}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "Atribūts `[user-scalable=\"no\"]` netiek i<PERSON>ts elementā `<meta name=\"viewport\">`, un atribūts `[maximum-scale]` nav mazāks par 5"}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>a lasītāji nevar tulkot saturu, kas nav teksts. Ja elementiem `<object>` pievienosiet alternatīvo tekstu, ekr<PERSON>a lasītāji varēs lietotājiem paziņot teksta nozīmi. [Uzziniet vairāk](https://web.dev/object-alt/)."}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "Elementiem `<object>` nav teksta `[alt]`"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "Elementi `<object>` ietver tekstu `[alt]`"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "Ja vērtība ir lielāka par “0”, navigācijas secība ir noteikta. Lai gan tehniski šis risinājums ir derīgs, bie<PERSON>i vien tas mulsina lietot<PERSON>ju<PERSON>, k<PERSON> i<PERSON>. [Uzziniet vairāk](https://web.dev/tabindex/)."}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "Dažiem elementiem atribūta “`[tabindex]`” vērt<PERSON><PERSON> ir lielā<PERSON> par 0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "Nevienam elementam nav atribūta `[tabindex]` v<PERSON><PERSON><PERSON><PERSON>, kas augst<PERSON><PERSON> par 0"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "<PERSON>k<PERSON><PERSON>a lasītā<PERSON> funkcijas atvieglo pārvietošanos tabulās. Ja elementa `<td>` <PERSON><PERSON><PERSON><PERSON><PERSON>, kas izmanto atribūtu `[headers]`, ir atsauces tikai uz citām šūnām tajā pašā tabulā, tiek nodro<PERSON>ta labāka pieredze lietot<PERSON>, kuri izmanto ekrāna lasītā<PERSON>. [Uzziniet vairāk](https://web.dev/td-headers-attr/)."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Elementa `<table>` <PERSON><PERSON><PERSON><PERSON><PERSON>, kuras i<PERSON> `[headers]`, ir atsauces uz elementu `id`, kas netika atrasts tajā pašā tabulā."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "Elementa `<table>` <PERSON><PERSON><PERSON><PERSON><PERSON>, kuras i<PERSON> `[headers]`, ir atsauces uz citām šūnām tajā pašā tabulā."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> lasītā<PERSON> funkcijas atvieglo pārvietošanos tabulās. Ja tabulu virsrak<PERSON>s vienmēr ir atsauces uz citām šūn<PERSON>m, tas var nodrošin<PERSON>t labāku piered<PERSON> lieto<PERSON>, kuri i<PERSON>to ekrāna lasītā<PERSON>. [Uzziniet vairāk](https://web.dev/th-has-data-cells/)."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Elementi `<th>` un elementi ar atribūtu `[role=\"columnheader\"/\"rowheader\"]` neietver to a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datu <PERSON>"}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "Elementi `<th>` un elementi ar atribūtu `[role=\"columnheader\"/\"rowheader\"]` ietver to a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datu <PERSON>"}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "Ja elementos norādīsiet derīgu [BCP 47 valodu](https://www.w3.org/International/questions/qa-choosing-language-tags#question), ekr<PERSON>a las<PERSON>t<PERSON>s pareizi atskaņos tekstu. [Uzziniet vairāk](https://web.dev/valid-lang/)."}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Atri<PERSON><PERSON><PERSON>m `[lang]` nav derīgas vērt<PERSON>bas"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "Atrib<PERSON>tiem `[lang]` ir derīga vērt<PERSON>ba"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "<PERSON>a videoklipam ir subtitri, ned<PERSON><PERSON><PERSON><PERSON> lietotāji un lietotāji ar dzirdes traucējumiem varēs vieglāk piekļūt informācijai. [Uzziniet vairāk](https://web.dev/video-caption/)."}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "Elementi `<video>` neietver elementu `<track>` ar parametru `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "Elementi `<video>` ietver elementu `<track>` ar parametru `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "Audio aprakstos ir <PERSON>, videoklipos neiekļauta informācija, <PERSON><PERSON><PERSON><PERSON>, sejas izteiksmes un ainas. [Uzziniet vairāk](https://web.dev/video-description/)."}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "Elementi `<video>` neietver elementu `<track>` ar parametru `[kind=\"description\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "Elementi `<video>` ietver elementu `<track>` ar parametru `[kind=\"description\"]`"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "Lai iegūtu ideālu izskatu iOS ier<PERSON><PERSON><PERSON>, lietotājiem pievienojot progresīvo tīmekļa lietotni sākuma ekr<PERSON>, definēji<PERSON> atribūtu `apple-touch-icon`. <PERSON> ir jānorāda uz necaurspīdīgu 192 pikseļu (vai 180 pikseļu) kvadrātveida PNG. [Uzziniet vairāk](https://web.dev/apple-touch-icon/)."}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "<PERSON>iek nod<PERSON>āts derīgs atribūts “`apple-touch-icon`”"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "At<PERSON><PERSON><PERSON><PERSON> “`apple-touch-icon-precomposed`” ir nove<PERSON><PERSON>; ieteicams izmantot atribūtu “`apple-touch-icon`”."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> derī<PERSON> at<PERSON> “`apple-touch-icon`”"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chrome paplašinājumi negatīvi ietekmē šīs lapas ielādes veiktspēju. Mēģiniet lapas pārbaudi veikt inkognito režīmā vai no Chrome profila bez paplašinājumiem."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "Skripta novē<PERSON>"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>r<PERSON><PERSON>ā procesora laiks"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "Ieteicams samazināt laiku, kas tiek izmantots JS parsēšanai, kompilēšanai un izpildei. Iespējams, konstat<PERSON><PERSON><PERSON>, ka ir noderīgi izmantot mazākas JS lietderīgās slodzes. [Uzziniet vairāk](https://web.dev/bootup-time)."}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "JavaScript izpildes la<PERSON>"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "JavaScript izpildes laiks"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Lieli GIF attēli nav efektīvi animēta satura rādīšanai. Animācijām ieteicams izmantot MPEG4/WebM video, bet statiskiem attēliem — PNG/WebP, nevis GIF, lai sama<PERSON>tu tīkla lieto<PERSON> (baitos). [Uzziniet vairāk](https://web.dev/efficient-animated-content)."}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Izmantojiet video failu formātus animētam saturam"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "<PERSON> laiku līdz interakti<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ekr<PERSON>a attēlus un paslēptos attēlus ar lēnu ielādi ieteicams atlikt līdz visu svarīgo resursu ielādes pabe<PERSON>. [Uzziniet vairāk](https://web.dev/offscreen-images)."}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Ā<PERSON>us ekrāna esošo attēlu atlikša<PERSON>"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Resursi bloķē jūsu lapas pirmo satura atveidojumu. Ieteicams rādīt svarīgos JS/CSS iekļautā veidā un atteikties no visiem nesvarīgajiem JS/stiliem. [Uzziniet vairāk](https://web.dev/render-blocking-resources)."}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Samaziniet resursus, kas bloķē <PERSON>"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "<PERSON>las tīkla lietderīgā<PERSON> slodzes izmaksā lietotājiem īstu naudu un ir cieši saistītas ar ilgu ielādes laiku. [Uzziniet vairāk](https://web.dev/total-byte-weight)."}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Ko<PERSON><PERSON><PERSON><PERSON> lielums bija {totalBytes, number, bytes} KB."}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> lielas tīkla lietderī<PERSON><PERSON><PERSON> s<PERSON>pieļau<PERSON>"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Nepieļauj pārāk lielu tīkla lietderīgo slodzi"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "Samazinot CSS failus, var samazināties tīkla lietderīg<PERSON> s<PERSON>. [Uzziniet vairāk](https://web.dev/unminified-css)."}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "Samaziniet CSS"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Samazinot JavaScript failus, var samazināties lietderīgā<PERSON> slodzes apjomi un skriptu parsēšanas laiks. [Uzziniet vairāk](https://web.dev/unminified-javascript)."}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "JavaScript·samazināšana"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Noņemiet stila lap<PERSON>, ka<PERSON>, un atlieciet pirmajā ekrāna saturā neizmantotā CSS ielādi, lai samazinātu tīkla aktivitātes nevajadzīgi izmantoto baitu apjomu. [Uzziniet vairāk](https://web.dev/unused-css-rules)."}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Noņemt neizmantoto CSS"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Noņemiet neizmantoto JavaScript, lai samazin<PERSON>tu tīkla aktivitātes izmantoto baitu apjomu."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Noņemiet neizmantoto JavaScript"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Iestatot ilgu keša<PERSON> m<PERSON>, lapas atkārtoti apmeklējumi varētu paātrināties. [Uzziniet vairāk](https://web.dev/uses-long-cache-ttl)."}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Atrasts 1 resurss}zero{Atrasti # resursi}one{Atrasts # resurss}other{Atrasti # resursi}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Statisko elementu note<PERSON>na, izmantojot efektīvu kešatmiņas politiku"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Efektīvas kešatmiņas politikas izmantošana statiskiem elementiem"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Optimizēti attēli tiek ielādēti ātrāk un izmanto mazāku mobilo datu apjomu. [Uzziniet vairāk](https://web.dev/uses-optimized-images)."}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Efektīva attēlu kodēšana"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> at<PERSON> i<PERSON> att<PERSON>, lai tiktu izmantots mazāks mobilo datu apjoms un tiktu uzlabots ielādes laiks. [Uzziniet vairāk](https://web.dev/uses-responsive-images)."}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> lie<PERSON> att<PERSON>"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "<PERSON> kop<PERSON> t<PERSON> (baitos), ieteica<PERSON> i<PERSON><PERSON><PERSON> (Gzip, Deflate vai Brotli). [Uzziniet vairāk](https://web.dev/uses-text-compression)."}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Iespējojiet te<PERSON>ta <PERSON>"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "<PERSON><PERSON><PERSON> attēlu formāti kā JPEG 2000, JPEG XR un WebP bieži ir veiksmīgāk saspiežami nekā PNG vai JPEG. Tas nozīmē ātrāku lejupielādi un mazāku datu patēriņu. [Uzziniet vairāk](https://web.dev/uses-webp-images)."}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> attēlus nākamās paaudzes formātos"}, "lighthouse-core/audits/content-width.js | description": {"message": "Ja lietotnes satura platums neatbilst skatvietas platumam, lietotne var nebūt optimizēta mobilo ierīču ekrāniem. [Uzziniet vairāk](https://web.dev/content-width)."}, "lighthouse-core/audits/content-width.js | explanation": {"message": "Skatvietas izmērs ({innerWidth} px) neatbilst loga izmēram ({outerWidth} px)."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "Satura izmērs nav pareiz<PERSON>, sal<PERSON><PERSON><PERSON><PERSON> ar s<PERSON>tu"}, "lighthouse-core/audits/content-width.js | title": {"message": "Satura izmērs ir <PERSON>, sal<PERSON><PERSON><PERSON><PERSON> ar s<PERSON>"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "Metrika “Kritisko pieprasījumu ķēdes” t<PERSON><PERSON><PERSON><PERSON> parāda, kuri resursi ir ielādēti ar augstāko prioritāti. Lai uzlabotu lapas ielādi, ieteicams samazināt ķēžu garumu, samazināt resursu lejupielādes apjomu vai atlikt nevajadzīgo resursu lejupielādi. [Uzziniet vairāk](https://web.dev/critical-request-chains)."}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Atrasta 1 ķēde}zero{Atrastas # ķēdes}one{Atrasta # ķēde}other{Atrastas # ķēdes}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "Kritisko pieprasījumu <PERSON>"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/brīdin<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/deprecations.js | description": {"message": "Novecojušas saskarnes API laika gaitā tiks noņemtas no pārlūkprogrammas. [Uzziniet vairāk](https://web.dev/deprecations)."}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Atrasts 1 brīdinājums}zero{Atrasti # brīdinājumi}one{Atrasts # brīdinājums}other{Atrasti # brīdinājumi}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "Tiek izmantotas novecojušas saskarnes API"}, "lighthouse-core/audits/deprecations.js | title": {"message": "Nav atļautas novecojušas saskarnes API"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "Lietojumprogrammas kešatmiņa ir novecojusi. [Uzziniet vairāk](https://web.dev/appcache-manifest)."}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "Atrasts parametrs “{AppCacheManifest}”"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "Tiek izmantota lietojumprogrammas kešatmiņa"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "Netiek izmantota lietojumprogrammas kešatmiņa"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> DOCTYPE, pā<PERSON>ūkprogramma nevar pārslēgties uz saderības režīmu. [Uzziniet vairāk](https://web.dev/doctype)."}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "DOCTYPE nosaukumam jābūt mazo burtu virknei “`html`”."}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Dokumentam ir jāietver DOCTYPE."}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "<PERSON><PERSON><PERSON><PERSON> publiskais ID būs tuk<PERSON> v<PERSON>."}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "<PERSON><PERSON><PERSON><PERSON> sist<PERSON> būs tuk<PERSON> v<PERSON>."}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Lapai trūkst HTML DOCTYPE, tāpēc tiek aktivizēts saderības režīms"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "Lapā ir HTML DOCTYPE"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "Elements"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statist<PERSON><PERSON> dati"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "Pārlūkprogrammas in<PERSON><PERSON><PERSON> iesaka, lai lapā nebūtu vairāk par 1500 DOM elementiem. Ieteicams, lai koka dziļums nepārsniegtu 32 elementus un 60 bērnelementus/vecākelementus. Liels DOM elements var palielināt atmiņas lietojumu, rad<PERSON><PERSON> ilg<PERSON> [stila aprēķinus](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) un izraisīt [izkārtojuma plūduma sakārtošanu](https://developers.google.com/speed/articles/reflow). [Uzziniet vairāk](https://web.dev/dom-size)."}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 elements}zero{# elementu}one{# elements}other{# elementi}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Pārāk lielu DOM izmēru nepieļaušana"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> DOM dziļums"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "DOM elementu kopskaits"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>ts"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "Nepieļauj pārāk lielus DOM izmērus"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "Mērķis"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "Pievienojiet parametru `rel=\"noopener\"` vai `rel=\"noreferrer\"` jeb<PERSON>r<PERSON>m ārējām saitēm, lai uzlabotu veiktspēju un novērstu drošības ievainojamību. [Uzziniet vairāk](https://web.dev/external-anchors-use-rel-noopener)."}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "Saites uz savstarpējās izcelsmes galamērķiem nav drošas"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "Saites uz savstarpējās izcelsmes galamērķiem ir drošas"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "<PERSON><PERSON><PERSON> note<PERSON>t <PERSON> ({anchorHTML}) galamērķi. Ja to ne<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>, apsveriet galamērķa noņ<PERSON> (target=_blank)."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Lietotājus mulsina un rada neuzticību vietnes, kas pieprasa viņu atrašanās vietu bez konteksta. Tā vietā ieteicams saistīt pieprasījumu ar lietotāja darbību. [Uzziniet vairāk](https://web.dev/geolocation-on-start)."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Tiek pieprasīta ģeogrāfiskās atrašanās vietas note<PERSON> atļ<PERSON>ja lapas ielādei"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Netiek pieprasīta ģeogrāfiskās atrašanās vietas note<PERSON> atļau<PERSON> lapas ielādei"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "<PERSON><PERSON><PERSON> tika noteiktas visas JavaScript priekšgalsistēmas bibliotēkas. [Uzziniet vairāk](https://web.dev/js-libraries)."}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "Noteiktās JavaScript bibliotēkas"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> ir lē<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, kas ir din<PERSON> i<PERSON>, i<PERSON><PERSON><PERSON><PERSON>u “`document.write()`”, var ievē<PERSON>ja<PERSON> a<PERSON>kav<PERSON>t lapas ielādi. [Uzziniet vairāk](https://web.dev/no-document-write)."}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Tiek izmantots elements “`document.write()`”"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "Netiek izmantots elements “`document.write()`”"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "Bibliotēkas versija"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "Ievainojamības gadīju<PERSON> s<PERSON>ts"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "Dažos trešo pušu skrip<PERSON> var būt zināma drošības ievainojamība, kuras uzbrucēji viegli identificē un izmanto. [Uzziniet vairāk](https://web.dev/no-vulnerable-libraries)."}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{Konstatēta 1 ievainojamība}zero{Konstatētas # ievainojamības}one{Konstatēta # ievainojamība}other{Konstatētas # ievainojamības}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "Ietver JavaScript priekšgalsistēmas bibliotēkas ar zinā<PERSON> dro<PERSON><PERSON> ievainojamību"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "Augsts"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "Zems"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "Nepieļauj JavaScript priekšgalsistēmas bibliotēkas ar zināmām drošības ievainojamībām"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "Lietotājus mulsina un viņiem rada neuzticību viet<PERSON>, kas pieprasa sūtīt paziņojumus bez konteksta. Tā vietā ieteicams saistīt pieprasījumu ar lietotāja žestiem. [Uzziniet vairāk](https://web.dev/notification-on-start)."}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Tiek pieprasīta p<PERSON> atļauja lapas ielādei"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "Netiek pieprasīta p<PERSON> atļauja lapas ielādei"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "<PERSON><PERSON><PERSON>gi <PERSON>i"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "Pa<PERSON><PERSON> i<PERSON>ē<PERSON> novēršana neatbilst labai drošības politikai. [Uzziniet vairāk](https://web.dev/password-inputs-can-be-pasted-into)."}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "Neļauj lietotājiem ielīmēt paroles lauk<PERSON>"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "Ļauj lietot<PERSON><PERSON>em i<PERSON>t paroles laukos"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokols"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 piedāvā daudzas priekšrocības salīdzinājumā ar HTTP/1.1, tostarp binār<PERSON><PERSON> galvenes, multipleks<PERSON><PERSON><PERSON> un servera tiešu darbību (server push). [Uzziniet vairāk](https://web.dev/uses-http2)."}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 pieprasījums nav parādīts, izmantojot HTTP/2}zero{# pieprasījumi nav parādīti, izmantojot HTTP/2}one{# pieprasījums nav parādīts, izmantojot HTTP/2}other{# pieprasījumi nav parādīti, izmantojot HTTP/2}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "Netiek izmantots protokols HTTP/2 visiem tā resursiem"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "Tiek izmantots protokols HTTP/2 tā resursiem"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "<PERSON><PERSON>u savas lapas ritinā<PERSON> veiktspēju, ieteicams atzīmēt pieskārienu un peles ritentiņa notikumu uztvērējus kā “`passive`”. [Uzziniet vairāk](https://web.dev/uses-passive-event-listeners)."}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "<PERSON><PERSON> p<PERSON>, lai uzla<PERSON>u ritin<PERSON><PERSON> veiktspēju"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "<PERSON><PERSON> i<PERSON><PERSON> p<PERSON>, lai uzla<PERSON>u ritin<PERSON><PERSON> veiktspēju"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "Konsol<PERSON> reģistrētās kļūdas norāda uz neatrisinātām problēmām. Tās var rasties no tīkla pieprasījuma kļūmēm un citām pārlūkprogrammas problēmām. [U<PERSON>n<PERSON><PERSON> vairāk](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "Pārlūkprogrammas kļūdas tika reģistrētas konsolē"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "Neviena pārlūkprogrammas kļūda nav reģistrēta konsolē"}, "lighthouse-core/audits/font-display.js | description": {"message": "Izmantojiet fonta rādīšanas CSS funkciju, lai lietotāji tīmekļa fontu ielādes laikā varētu redzēt tekstu. [Uzziniet vairāk](https://web.dev/font-display)."}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "Visa teksta redzamības nodroš<PERSON>na tīmekļa fonta ielādes laikā"}, "lighthouse-core/audits/font-display.js | title": {"message": "Tīmekļa fonta ielādes laikā viss teksts paliek redzams"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "Lighthouse nevarēja automātiski pārbaudīt šī URL fonta attēlojuma vērtību: {fontURL}."}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "<PERSON><PERSON> (faktiskā)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "<PERSON><PERSON> (attēlotā)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "Att<PERSON><PERSON> par<PERSON> izmēriem jāatbilst dabiskajai malu attiecī<PERSON>i. [Uzziniet vairāk](https://web.dev/image-aspect-ratio)."}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "Tiek rādīti attēli ar nepareizu malu attiec<PERSON>bu"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "Tiek rādīti attēli ar pareizu malu attiec<PERSON>bu"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "<PERSON><PERSON><PERSON><PERSON> attēla lie<PERSON>a informācija: {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "Pārlūkprogrammās var aktīvi rādīt lietotājiem uzvednes ar ierosinājumu pievienot jūsu lietotni sākuma ekrānam. Tādējādi var izdoties palielināt iesaisti. [Uzziniet vairāk](https://web.dev/installable-manifest)."}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "Tīmekļ<PERSON> manifests neatbilst instalējamības prasībām"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "Tīmekļ<PERSON> manifests atbilst instalējamības prasībām"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "Nedrošs URL"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "Visas vietnes ir jāaizsarg<PERSON> ar protokolu HTTPS, pat ja tajās netiek apstrādāti sensitīvi dati. HTTPS neļauj iebrucējiem manipulēt vai pasīvi uztvert sakarus starp jūsu lietotni un lietotājiem, un tas ir HTTP/2 un daudzu jaunu tīmekļa platformu saskarņu API priekšnoteikums. [Uzziniet vairāk](https://web.dev/is-on-https)."}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Noteikts 1 nedrošs pieprasījums}zero{Noteikti # nedroši pieprasījumi}one{Noteikts # nedrošs pieprasījums}other{Noteikti # nedroši pieprasījumi}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "Netiek izmantots protokols HTTPS"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "Tiek izmantots protokols HTTPS"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> mobilo datu t<PERSON>, <PERSON><PERSON><PERSON><PERSON> labu pieredzi mobilo ierīču lietot<PERSON>. [Uzziniet vairāk](https://web.dev/load-fast-enough-for-pwa)."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "Interaktīvais laiks: {timeInMs, number, seconds} s"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "Interaktīvs simulētā mobilajā tīklā {timeInMs, number, seconds} sekunžu laikā"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "Lapas ielāde ir pārāk lēna — laiks līdz interaktivitātei pārsniedz 10 sekundes. <PERSON>, k<PERSON> <PERSON><PERSON><PERSON><PERSON>, skat<PERSON> sada<PERSON> “Veiktspēja” norādītās iespējas un diagnostiku."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "Lapas ielāde nav pietiekami ātra mobilajos tīklos"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "Lapas ielāde ir pietiekami ātra mobilajos tīklos"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Kategorija"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "Ieteicams samazināt laiku, kas tiek izmantots JS parsēšanai, kompilēšanai un izpildei. Iespējams, konstat<PERSON><PERSON><PERSON>, ka ir noderīgi izmantot mazākas JS lietderīgās slodzes. [Uzziniet vairāk](https://web.dev/mainthread-work-breakdown)."}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Samaziniet galvenā pavediena darbu"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>a <PERSON>"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "<PERSON> sasniegtu pēc iespējas vairāk lietot<PERSON>, ieteicams izstrād<PERSON> viet<PERSON>, kas darboja<PERSON> visās lielākaj<PERSON> pārlūkprogrammās. [Uzziniet vairāk](https://web.dev/pwa-cross-browser)."}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "<PERSON><PERSON><PERSON>rogram<PERSON>"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lai lietotāji varētu veidot un atvērt dziļās saites uz atsevišķām lapām, izmantojot URL. Turklāt URL jābūt unikāliem, lai varētu kopīgot lapas sociālajos saziņas līdzekļos. [Uzziniet vairāk](https://web.dev/pwa-each-page-has-url)."}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Katrai lapai ir URL"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>, lai, pieskaroties lietotnes elementiem, p<PERSON><PERSON><PERSON> būtu <PERSON>tras pat tad, ja tīkla darbība ir lēna. Šis ir galvenais faktors, kas nosaka to, kā lietotājs uztver veiktspēju. [Uzziniet vairāk](https://web.dev/pwa-page-transitions)."}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> nerodas sajūta, ka lēna ielāde bloķētu pārejas starp lapām"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>s ievades latentums aptuveni norāda, pēc cik ilga laika (milisekundēs) uz lietotāja ievadi reaģēs jūsu lietotne aizņemtākajā lapas ielādes 5 s periodā. Ja latentums pārsniedz 50 ms, ies<PERSON><PERSON><PERSON><PERSON>, lietotā<PERSON>em liksies, ka jūsu lietotne strādā ar traucējumiem. [Uzziniet vairāk](https://web.dev/estimated-input-latency)."}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "Pa<PERSON><PERSON>ē<PERSON>s ievades latentums"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "<PERSON><PERSON> \"Pirmais satura marķējums\" at<PERSON><PERSON><PERSON><PERSON> laiku, kad tiek marķēts pirmais teksts vai attēls. [Uzziniet vairāk](https://web.dev/first-contentful-paint)."}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "<PERSON><PERSON><PERSON> saturīgais satura atveidojums"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "Metrika “Pirmā CPU dīkstāve” <PERSON><PERSON><PERSON> la<PERSON>, kad lapas galvenais pavediens ir kļuvis pietiekami maz<PERSON>, lai varētu apstrād<PERSON>t ievadi.  [Uzziniet vairāk](https://web.dev/first-cpu-idle)."}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "Pirmā CPU dīkstāve"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>gais satura atveidojums” nor<PERSON><PERSON>, kad k<PERSON> redzams lapas galvenais saturs. [Uzziniet vairāk](https://web.dev/first-meaningful-paint)."}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "<PERSON><PERSON><PERSON>s satura atveidojums"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "Laiks līdz interaktivitātei ir la<PERSON>, ka<PERSON>, lai lapa k<PERSON>tu pilnībā interaktīva. [Uzziniet vairāk](https://web.dev/interactive)."}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "Laiks līdz interaktivitātei"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "Iespēja<PERSON><PERSON> maksim<PERSON><PERSON><PERSON> pirmās ievades a<PERSON>, ar ko jūsu lietot<PERSON> var saskar<PERSON>, ir ilgākā uzdevuma ilgums milisekundēs. [Uzziniet vairāk](https://developers.google.com/web/updates/2018/05/first-input-delay)."}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "<PERSON><PERSON><PERSON> poten<PERSON><PERSON><PERSON> pirmā ievades aizkave"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "Ātr<PERSON> rādī<PERSON><PERSON><PERSON><PERSON>, cik ātri tiek parādīts lapas saturs. [Uzziniet vairāk](https://web.dev/speed-index)."}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "Ā<PERSON><PERSON> rādītājs"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "Visu laika periodu summa (no PSM līdz “Laiks līdz interaktivitātei”), kad uzdevuma ilgums pārsniedz 50 ms (izteikts milisekundēs)."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "Kopējais bloķēšanas laiks"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "Tīkla aprites laiks (RTT) spēcīgi ietekmē veiktspēju. Ja aprites laiks uz sākumpunktu ir augsts, tas nor<PERSON>, ka <PERSON>u veik<PERSON>pēja, kas atrodas tuvāk lietot<PERSON>, var tikt uz<PERSON>a. [Uzziniet vairāk](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "lighthouse-core/audits/network-rtt.js | title": {"message": "Tīkla aprites laiks"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "Tīmekļa veiktspēju var ietekmēt servera latentums. Ja sākumpunkta servera latentums ir augsts, tas nor<PERSON>da, ka serveris ir pārslogots vai arī tam ir vāja aizmugursistēmas veiktspēja. [Uzziniet vairāk](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "<PERSON>a aizmugursistēmas latentums"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "<PERSON>zman<PERSON><PERSON>t p<PERSON> sk<PERSON>, varat nodrošināt uzticamu tīmekļa lietotnes darbību neparedzamos tīkla apstākļos. [Uzziniet vairāk](https://web.dev/offline-start-url)."}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url` bezsaist<PERSON> nereaģē ar statusa kodu 200"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url` bezsaist<PERSON> reaģē ar statusa kodu 200"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "Rīkā Lighthouse neizdevās no manifesta nolasīt `start_url`. <PERSON><PERSON>p<PERSON><PERSON> tika <PERSON>, ka `start_url` ir dokumenta URL. K<PERSON><PERSON><PERSON> ziņojums: “{manifestWarning}”."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "Saglabājiet tīkla pieprasījumu daudzumu un lielumu zem mērķiem, kas noteikti sniegtajā izpildes budžet<PERSON>. [Uzziniet vairāk](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 pieprasījums}zero{# pieprasījumi}one{# pieprasījums}other{# pieprasījumi}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "<PERSON>z<PERSON><PERSON>s b<PERSON>"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "Ja HTTPS protokols jau ir i<PERSON>, note<PERSON><PERSON> visu HTTP datplūsmu uz HTTPS, lai visiem lietotājiem iespējotu droša tīmekļa funk<PERSON>. [Uzziniet vairāk](https://web.dev/redirects-http)."}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "HTTP datplūsma netiek novirzīta uz HTTPS lapām"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "HTTP datplūsma tiek novirzīta uz HTTPS lapām"}, "lighthouse-core/audits/redirects.js | description": {"message": "Novirzīšana rada papildu aizkaves pirms lapas ielādes. [Uzziniet vairāk](https://web.dev/redirects)."}, "lighthouse-core/audits/redirects.js | title": {"message": "Nepieļaujiet vairākas lapas novir<PERSON>"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "Lai iestatītu lapas resursu daudzuma un lieluma bud<PERSON>, pievienojiet failu budget.json. [Uzziniet vairāk](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 pieprasījums • {byteCount, number, bytes} KB}zero{# pieprasījumu • {byteCount, number, bytes} KB}one{# pieprasījums • {byteCount, number, bytes} KB}other{# pieprasījumi • {byteCount, number, bytes} KB}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "Uzturiet nelielu pieprasījumu skaitu un mazu pārsūtīšanas failu lielumu"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "Kanonisk<PERSON>s sa<PERSON> ies<PERSON>, kurus URL rādīt meklēšanas rezultātos. [Uzziniet vairāk](https://web.dev/canonical)."}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "Vairāki konfliktējoši URL ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "Norāda uz citu domēnu ({url})."}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "Nederīgs URL ({url})."}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Norāda uz citu atribūta “`hreflang`” atrašanās vietu ({url})."}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "Atbilstošs URL ({url})."}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "Ekvivalenta satura lapas vietā norāda uz domēna saknes piekļuves URL (sākumlapu)"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "Dokumentā nav derīga atribūta “`rel=canonical`”"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "Dokumentam ir derīgs atribūts “`rel=canonical`”"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "<PERSON><PERSON><PERSON>, kas ir mazāks par 12 pikse<PERSON><PERSON><PERSON>, ir p<PERSON>r<PERSON><PERSON> mazs, lai būtu <PERSON>, un mobilo ierīču lietotāji ir spiesti tuvināt tekstu, lai varētu to salas<PERSON>t. Centieties, lai vairāk nekā 60% lapas teksta būtu vismaz 12 pikseļu vai lielāka izmēra. [Uzziniet vairāk](https://web.dev/font-size)."}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} salasāms teksts"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "Teksts nav salas<PERSON>ms, jo mobilo ierīču ekrāniem nav optimizēts skatvietas metatags."}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "{decimalProportion, number, extendedPercent} ir pār<PERSON>k mazs teksta izmērs (pamatojoties uz {decimalProportionVisited, number, extendedPercent} paraugu)."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "Dokumentā netiek izmantoti sa<PERSON>āmi fonta izmēri"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "Dokumentā i<PERSON>ti sa<PERSON>mi fonta izmēri"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "At<PERSON><PERSON><PERSON><PERSON> “hreflang” saites norāda meklētājprogrammām, kuru lapas versiju iek<PERSON><PERSON> meklēšanas rezultātu sarakstā konkrētai valodai vai reģionam. [Uzziniet vairāk](https://web.dev/hreflang)."}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "Dokumentā nav derīga atribūta “`hreflang`”"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "Dokumentam ir derīgs atribūts “`hreflang`”"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "Lapas ar nesekmīgu HTTP statusa kodu var tikt indeksētas nepareizi. [Uzziniet vairāk](https://web.dev/http-status-code)."}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "Lapai ir nesekmīgs HTTP statusa kods"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "Lapai ir sekmīgs HTTP statusa kods"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "Meklētājprogrammas nevar iek<PERSON><PERSON> jūsu lapas mekl<PERSON><PERSON> rezultā<PERSON>, ja tām nav atļaujas pārmeklēt lapas. [Uzziniet vairāk](https://web.dev/is-crawable)."}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> ir bloķēta indeksē<PERSON>na"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "Lapa ir pieejama indek<PERSON>"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "<PERSON><PERSON> teksts palīdz meklētājprogrammām saprast jūsu saturu. [Uzziniet vairāk](https://web.dev/link-text)."}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Atrasta 1 saite}zero{Atrastas # saites}one{Atrasta # saite}other{Atrastas # saites}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "Saitēm nav a<PERSON>ks<PERSON><PERSON> teksta"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "Sai<PERSON><PERSON><PERSON> ir a<PERSON><PERSON><PERSON> te<PERSON>"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "Lai validētu strukturētos datus, palaidiet [strukturētu datu testēšanas rīku](https://search.google.com/structured-data/testing-tool/) un rīku [Structured Data Linter](http://linter.structured-data.org/). [Uzziniet vairāk](https://web.dev/structured-data)."}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "Strukturētie dati ir derīgi"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rezultā<PERSON> var tikt iekļauti metaapraksti, lai sniegtu īsu kopsavilkumu par lapas saturu. [Uzziniet vairāk](https://web.dev/meta-description)."}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "Apraksta teksts ir tuk<PERSON>."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "Dokumentā nav metaapraksta"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "Dokumentā ir metaaprak<PERSON>"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "Meklētājprogram<PERSON> nevar indeks<PERSON>t spraudņu saturu, un daudzās ierīcēs spraudņi ir ierobežoti vai netiek atbalstīti. [Uzziniet vairāk](https://web.dev/plugins)."}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "Dokumentā tiek i<PERSON> s<PERSON>"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "Dokumentā netiek pieļauti spraudņi"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "Ja jūsu robots.txt fails ir nepar<PERSON><PERSON> veidots, r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON><PERSON>, nevar<PERSON><PERSON>, kā vajadzētu pārmeklēt vai indeksēt tīmekļa vietni atbilstoši jūsu vēlmēm. [Uzziniet vairāk](https://web.dev/robots-txt)."}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Atbildē uz robots.txt pieprasījumu tika atgriezts HTTP statuss {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Tika atrasta 1 kļūda}zero{Tika atrastas # kļūdas}one{Tika atrasta # kļūda}other{Tika atrastas # kļūdas}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse nevarēja ielādēt robots.txt failu"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt nav derīgs"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "robots.txt ir derīgs"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "Interaktīva<PERSON><PERSON> elementiem, <PERSON><PERSON><PERSON><PERSON>, pogām un saitēm, ir jā<PERSON><PERSON>t pietiekami lieliem (48 x 48 pikseļi), un tiem apkārt ir jābūt pietiekami daudz brīvas vietas, lai tiem varētu viegli pieskarties, ne<PERSON><PERSON><PERSON>ot citus elementus. [Uzziniet vairāk](https://web.dev/tap-targets)."}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} pieskārienu mērķu izmērs ir at<PERSON>."}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Pieskārienu mērķu izmērs ir pārāk maz<PERSON>, jo mobilo ierīču ekrāniem nav optimizēts skatvietas metatags"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "Pieskārienu mērķi nav at<PERSON>stoša izmēra"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Mērķis, kas pā<PERSON>"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Piesk<PERSON>rienu mērķis"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "Pieskārienu mērķi ir pietiekami liela i<PERSON>ra"}, "lighthouse-core/audits/service-worker.js | description": {"message": "Pakalpojuma skripts ir tehnoloģija, kas palīdz nodro<PERSON>t daudzas progresīvo tīmekļa lietotņu funkci<PERSON>, pie<PERSON><PERSON><PERSON>, lietotnes izmanto<PERSON> bezsaistē, pievie<PERSON><PERSON>nu sākuma ekrānam un informatīvos paziņojumus. [Uzziniet vairāk](https://web.dev/service-worker)."}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "<PERSON><PERSON> kontrolē p<PERSON> sk<PERSON>, taču netika atrasts `start_url`, jo neizdevās analizēt manifestu kā derīgu JSON failu"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "<PERSON><PERSON> kontrolē pakalpojumu skripts, ta<PERSON>u vietrādis URL `start_url` ({startUrl}) nav ietverts pakalpojumu skripta tvērumā ({scopeUrl})"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "<PERSON><PERSON> kontrol<PERSON> p<PERSON> s<PERSON>, ta<PERSON>u netika atrasts `start_url`, jo manifests netika ienests."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "<PERSON><PERSON> ir viens vai vairāki pakal<PERSON> s<PERSON>, ta<PERSON>u attie<PERSON> lapa ({pageUrl}) nav tvērumā."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "Nav reģistrēts paka<PERSON><PERSON><PERSON><PERSON> sk<PERSON>, kas kontrol<PERSON>tu lapu un `start_url`"}, "lighthouse-core/audits/service-worker.js | title": {"message": "Ir reģistrēts paka<PERSON><PERSON><PERSON><PERSON> sk<PERSON>, kas kont<PERSON><PERSON> lapu un `start_url`"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "Uzplaiksnījuma ekrāns ar piemērotu motīvu nodroš<PERSON> labu pier<PERSON>, lietot<PERSON><PERSON><PERSON> palaižot lietotni no sākuma ekrāna. [Uzziniet vairāk](https://web.dev/splash-screen)."}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "Nav konfigurēta ar pielāgotu uzplaiksnījuma ekr<PERSON>u"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "Konfigurēta ar pie<PERSON>gotu uzplaiksnīju<PERSON> e<PERSON>u"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "Pārlūkprogrammas adreses joslu var noformēt atbilstoši jūsu vietnes motīvam. [Uzziniet vairāk](https://web.dev/themed-omnibox)."}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "<PERSON><PERSON> neiestata adreses joslas motī<PERSON> kr<PERSON>."}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "Lapa iestata adreses joslas motīva kr<PERSON>."}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "Galvenā pavediena bloķēšanas laiks"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "<PERSON><PERSON><PERSON><PERSON> puse"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "Tre<PERSON><PERSON><PERSON> puses kods var ievērojami ietekmēt ielādes veiktspēju. Ierobežojiet lieko trešo pušu pakalpojumu sniedzēju skaitu un mēģiniet ielādēt trešās puses kodu pēc tam, kad jūsu lapa būs ielādēta. [Uzziniet vairāk](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> puses kods bloķēja galveno pavedienu uz {timeInMs, number, milliseconds} ms"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "Samaziniet trešo pušu koda ietekmi"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> pušu lietoju<PERSON>"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "Metrika “Laiks līdz pirmajam baitam” nor<PERSON><PERSON> la<PERSON>, kad jūsu serveris nosūta atbildi. [Uzziniet vairāk](https://web.dev/time-to-first-byte)."}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "Saknes dokumentam nepieciešamais laiks: {timeInMs, number, milliseconds} ms"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "Servera atbildes la<PERSON> (TTFB)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "Servera atbildes laiks ir mazs (TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "<PERSON><PERSON><PERSON><PERSON> laiks"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "Veids"}, "lighthouse-core/audits/user-timings.js | description": {"message": "Ieteicams pievienot lietotnei “Lietotāja laika API”, lai noteiktu lietotnes aktuālo veiktspēju lietotāju pamata darbības laikā. [Uzziniet vairāk](https://web.dev/user-timings)."}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 lietotāja laiks}zero{# lietotāju laiks}one{# lietotāja laiks}other{# lietotāju laiks}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "Lietotāju laika atzī<PERSON> un mērījumi"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Elementam “{security<PERSON><PERSON><PERSON>}” tika atrasta iepriekšējas pieslēgšanās parametrs <link>, ta<PERSON><PERSON> p<PERSON>kprogramma to neizmantoja. Pārbaudiet, vai pareizi izmanto<PERSON>t atribūtu “`crossorigin`”."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "Ieteicams pievienot “`preconnect`” vai “`dns-prefetch`” resursa nor<PERSON>, lai savlaicīgi izveidotu savienojumus ar svarīgiem trešo pušu sākumpunktiem. [Uzziniet vairāk](https://web.dev/uses-rel-preconnect)."}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "Veiciet iepriekšēju pieslēgšanu obligātajiem sākumpunktiem"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "<PERSON><PERSON><PERSON> “{preloadURL}” tika atrasts iepriekšējas ielādes parametrs <link>, ta<PERSON>u pārl<PERSON>kprogramma to neizmantoja. Pārbaudiet, vai pareizi izmantojat atribūtu “`crossorigin`”."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "Ieteicams izmantot atribūtu `<link rel=preload>`, lai noteiktu prioritāti tādu resursu ieg<PERSON>, kas pašlaik lapas ielādē tiek pieprasīti vēlāk. [Uzziniet vairāk](https://web.dev/uses-rel-preload)."}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "Veiciet svarīgāko pie<PERSON>īju<PERSON> iepriekšē<PERSON> i<PERSON>"}, "lighthouse-core/audits/viewport.js | description": {"message": "Pievienojiet tagu `<meta name=\"viewport\">`, lai optimizētu lietotni mobilo ierīču ekrāniem. [Uzziniet vairāk](https://web.dev/viewport)."}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "Netika atrasts tags `<meta name=\"viewport\">`"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "Nav taga `<meta name=\"viewport\">` ar `width` vai `initial-scale`"}, "lighthouse-core/audits/viewport.js | title": {"message": "Ir tags `<meta name=\"viewport\">` ar `width` vai `initial-scale`"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "Ja JavaScript ir atspē<PERSON>, lietotnē jāparāda zināms saturs — kaut vai tikai brīdin<PERSON>ju<PERSON>, ka lietotnes izmantošanai nepieciešams JavaScript. [Uzziniet vairāk](https://web.dev/without-javascript)."}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "Ja lapas skripti nav pieejami, lapas pamattekstā jātiek vismaz daļēji attēlotam saturam."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "Ja JavaScript nav pieejams, netiek sniegts at<PERSON><PERSON><PERSON><PERSON><PERSON> saturs"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "Ja JavaScript nav pieejams, lapas saturs zināmā mērā tiek parādīts"}, "lighthouse-core/audits/works-offline.js | description": {"message": "Ja izstrādājat progresīvo tīmekļa lieto<PERSON>, apsveriet iespēju izmantot pakalpojumu skriptu, lai lietotne varētu darboties bezsaistē. [Uzziniet vairāk](https://web.dev/works-offline)."}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "Pa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lapa bezsaistē nereaģē ar statusa kodu 200"}, "lighthouse-core/audits/works-offline.js | title": {"message": "Pa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lapa bezsaistē reaģē ar statusa kodu 200"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lapa bezsaistē netiek i<PERSON>, jo tika veikta novir<PERSON> no jūsu testa URL ({requested}) uz \"{final}\". Mēģiniet tieši testēt otro URL."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "<PERSON><PERSON> iete<PERSON>j uzlabot ARIA lietojumu jūsu lietojumprogrammā. Tādēj<PERSON><PERSON> varat uzlabot piered<PERSON> lieto<PERSON>, kuri <PERSON>, <PERSON><PERSON><PERSON><PERSON>, e<PERSON><PERSON><PERSON><PERSON>."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "<PERSON><PERSON> ieteik<PERSON><PERSON>t papildu audio vai video saturu. Tādēj<PERSON><PERSON> var uzlabot pieredzi lietotājiem ar dzirdes vai redzes traucējumiem."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Audio un video"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Šie vienumi parāda izplatītas pieejamības paraugprakses."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "<PERSON><PERSON><PERSON> atz<PERSON> parāda iespējas [uz<PERSON><PERSON> jū<PERSON> tīmekļa lietotnes pieejamību](https://developers.google.com/web/fundamentals/accessibility). Automātiski var noteikt tikai pieejamības problēmu <PERSON>, tāpēc ir ieteicama arī manuālā testē<PERSON>."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "<PERSON><PERSON> vienumi norāda uz vietām, kurām automātiskais testēšanas rīks nevar piekļūt. Uzziniet vairāk mūsu ceļvedī par [pieejam<PERSON><PERSON> pārskata veik<PERSON>](https://developers.google.com/web/fundamentals/accessibility/how-to-review)."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "Pie<PERSON>amī<PERSON>"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "<PERSON><PERSON> i<PERSON> u<PERSON>bot satura lasāmību."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "<PERSON><PERSON> i<PERSON>, k<PERSON>, <PERSON><PERSON><PERSON> lab<PERSON> i<PERSON>st j<PERSON><PERSON> saturu."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internacionalizācija un lokalizēšana"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "<PERSON><PERSON> ieteikumi <PERSON>auj u<PERSON>bot lietojumprogrammas vadīklu semantiku. Tād<PERSON><PERSON><PERSON><PERSON> var nodrošin<PERSON>t labāku pieredzi lieto<PERSON>, kuri i<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ek<PERSON><PERSON><PERSON>."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "No<PERSON><PERSON>mi un iezīmes"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Šie ieteikumi <PERSON>j uzlabot jūsu lietotnes tastatūras navigāciju."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navigā<PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "<PERSON><PERSON> i<PERSON> tabulas vai sarakstu datu <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, e<PERSON><PERSON><PERSON><PERSON>."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tabulas un saraksti"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "Izpi<PERSON>s budžets nosaka jūsu vietnes veiktspējas standartus."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "Budžeti"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "Plašāka informācija par jūsu lietojumprogrammas veiktspēju. <PERSON>ie skaitļi [tieši neietekmē](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) veiktspējas rezultātu"}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostika"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Vissvarīgākais veiktspējas aspekts ir pikseļu render<PERSON> ātrums ekrānā. Galvenās metrikas: “<PERSON>rmais saturīgais satura atveidojums”, “<PERSON>rmais nozīmīgais satura atveidojums”"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Pirmā satura atveidojuma uzlabojumi"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "<PERSON><PERSON> ieteikumi var palīdzēt ātrāk ielādēt jūsu lapu. Tie [tieša veidā neietek<PERSON>](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) veiktspējas rezultātu."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "<PERSON>es<PERSON>ējas"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "Metrikas"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Uzlabojiet vispār<PERSON><PERSON> iel<PERSON> darb<PERSON>bu, lai lapa reaģētu un būtu gatava izmantošanai pēc iespējas ātrāk. Galvenās metrikas: “Laiks līdz interaktivitātei”, “Ātruma rādītājs”"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "<PERSON>is<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "Veiktspēja"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "<PERSON><PERSON><PERSON>, varat validēt progresīvo tīmekļa lietotņu aspektus. [Uzziniet vairāk](https://developers.google.com/web/progressive-web-apps/checklist)."}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "<PERSON><PERSON><PERSON> p<PERSON> ir vajadzīgas saskaņā ar standarta [PWA kontrolsarakstu](https://developers.google.com/web/progressive-web-apps/checklist), taču Lighthouse neveic tās automātiski. Tās neietekmē jū<PERSON> rezult<PERSON>, taču ir svarīgi pārbaudīt šos lietotnes aspektus manuāli."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "Progresīvā tīmekļa lietotne"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "Ātrums un uzticamība"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Instalēšana"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PTL optimizācija"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "<PERSON><PERSON><PERSON> at<PERSON><PERSON>, ka jūsu lapa ir optimizēta meklētājprogrammu rezultātu ranž<PERSON>. Pastāv papildu faktori, kurus Lighthouse neatzīmē, bet tie var ietekmēt jūsu meklēšanas rezultātu ranž<PERSON>. [Uzziniet vairāk](https://support.google.com/webmasters/answer/35769)."}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "Palaidiet savā vietnē šos papildu apstiprinā<PERSON><PERSON> r<PERSON>, lai aplūkotu papildu MPO paraugpraksi."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "MPO"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "Formatējiet savu HTML tā, lai rāpuļprogrammas varētu labāk saprast jūsu lietotnes saturu."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "Satura paraugprakse"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Rāpuļprogrammā<PERSON> ir ne<PERSON><PERSON> pie<PERSON>ļ<PERSON> jū<PERSON> lie<PERSON>, lai nodro<PERSON><PERSON><PERSON> parād<PERSON><PERSON><PERSON> me<PERSON> rezultā<PERSON>."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> un indek<PERSON>"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "<PERSON><PERSON><PERSON> lapām ir jābūt piemērotām mobilajām ierīcēm, lai lietotājiem nebūtu jāizmanto savilkšana vai tuvināšana lapu satura lasīšanai. [Uzziniet vairāk](https://developers.google.com/search/mobile-sites/)."}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> mobilajām ierīc<PERSON>m"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Kešatmiņas TTL vērtība"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vieta"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "Resursu veids"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "Izmērs"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> laiks"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> failu lie<PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Potenciālie <PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "Potenciālie <PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Potenciālais ietaupījums: {wastedBytes, number, bytes} KB"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Potenciālais ietaupījums: {wastedMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokuments"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "Fonts"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "Multivide"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "Cits"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "<PERSON><PERSON><PERSON> lapa"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON> puse"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "Kopā"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "Nevarēja reģistrēt lapas ielādes trasējumu. <PERSON><PERSON>, vēlreiz palaidiet Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "<PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>kļūdotāja protokola savienojumu, r<PERSON><PERSON><PERSON>."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "Lapas ielādes laikā pārlūkprogramma Chrome nav apkopojusi nevienu ekrānuzņēmumu. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ka saturs ir redzams lapā, un pēc tam mēģiniet atkārtoti palaist Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "DNS serveri nevarēja atrast nor<PERSON><PERSON><PERSON>."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Nepieciešamajam parametra “{artifactName}” vācējam radā<PERSON> k<PERSON>: {errorMessage}."}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "<PERSON><PERSON><PERSON><PERSON>ja Chrome kļūda. <PERSON><PERSON><PERSON><PERSON>, restartējiet Chrome un mēģiniet atkārtoti palaist Lighthouse."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "<PERSON>epie<PERSON><PERSON><PERSON><PERSON> parametra “{artifactName}” vā<PERSON><PERSON><PERSON><PERSON>."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse nevarēja droši ielādēt jūsu pieprasīto lapu. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka testējat pareizo URL un serveris pareizi reaģē uz visiem pieprasījumiem."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse nevarēja droši i<PERSON>ādēt jūsu pieprasīto URL, jo lapa pārstāja reaģēt."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "<PERSON><PERSON><PERSON>jam URL nav derīga drošības sertifikāta. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Pārlūkprogramma Chrome neļāva ielādēt lapu ar iespiestu reklāmu. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka pārbaudāt pareizo URL un ka serveris pareizi reaģē uz visiem pieprasījumiem."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse nevarēja uzticami ielādēt jūsu pieprasīto lapu. <PERSON>dr<PERSON><PERSON><PERSON><PERSON>, ka pārbaudāt pareizo URL un ka serveris pareizi reaģē uz visiem pieprasījumiem. (Detalizēta informācija: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse nevarēja uzticami ielādēt jūsu pieprasīto lapu. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka pārbaudāt pareizo URL un ka serveris pareizi reaģē uz visiem pieprasījumiem. (Statusa kods: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Lapas ielādei bija nepieciešams pārāk ilgs laiks. <PERSON><PERSON><PERSON><PERSON>, izmantojiet pārskatā sniegtās iespējas, lai samazin<PERSON>tu lapas ielādes laiku, un pēc tam mēģiniet atkārtoti palaist Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "Gaidot DevTools protokola atbildi, ir p<PERSON><PERSON><PERSON><PERSON><PERSON> atv<PERSON>l<PERSON><PERSON> laiks. (Veids: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "Resursu satura izg<PERSON><PERSON> ir nepieciešams ilgāks laiks, nek<PERSON>"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "Šķiet, ka jūsu norādītais URL nav derīgs."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> navigā<PERSON>ja"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kritiskais ceļa latentums:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>!"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "Pārskata kļūda: nav pārbaudes informācijas"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "Laboratorijas dati"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) pašreizēj<PERSON>s lapas analīze emulētajā mobilajā tīklā. Vērtības ir aptuvenas un var atšķirties."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> vienumi man<PERSON>"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "Nav piemērojams"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "Iespēja"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "Aptuve<PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "Sakļaut <PERSON>"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> trešās puses resursus"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "<PERSON><PERSON><PERSON><PERSON>, kas ietekmēja š<PERSON> palaišanu:"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "Vērtības ir aptuvenas un var atšķirties. Veiktspējas rezultāts tiek [tiek pamatots tikai uz šīm metrikām](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar brī<PERSON>"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "Apsveriet iespēju augšupielādēt GIF attēlu pakalpojumā, kuru varēs i<PERSON>, lai iegultu GIF attēlu kā HTML5 videoklipu."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "Instalējiet [atliktās ielādes WordPress spraudni](https://wordpress.org/plugins/search/lazy+load/), kas sniedz iespēju atlikt jebkādus ārpus ekrāna esošus attēlus vai mainīt motīvu uz tādu, kur<PERSON> šī funkcija tiek nodrošināta. Ieteicams izmantot arī [AMP spraudni](https://wordpress.org/plugins/amp/)."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "<PERSON>r v<PERSON><PERSON>ki WordPress spraudņi, kas var palīdz<PERSON>t [iek<PERSON><PERSON> būtiskus līdzekļus](https://wordpress.org/plugins/search/critical+css/) vai [atlikt mazāk svarīgus resursus](https://wordpress.org/plugins/search/defer+css+javascript/). Ņemiet vērā, ka šo spraudņu nodrošinātā optimizācija var traucēt funkciju darbībai jūsu motīvā vai spraudņ<PERSON>, t<PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON><PERSON>, jums būs jāveic koda izmaiņas."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "<PERSON><PERSON><PERSON><PERSON>, spraudņi un servera specifikācijas ietekmē servera atbildes laiku. Apsveriet iespēju atrast optimizētāku motīvu, rūp<PERSON>gi izvēlēties optimizācijas spraudni un/vai jaunināt serveri."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "Apsveriet iespēju rādīt fragmentus ziņu sarak<PERSON> (piemēram, izmantojot tagu “more”), sama<PERSON><PERSON>t attiec<PERSON>gajā lapā rādā<PERSON> ziņu skaitu, sadal<PERSON>t garas ziņas vairākās lapās vai izmanto<PERSON> spraudni, lai atliktu komentāru i<PERSON>."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "<PERSON><PERSON><PERSON><PERSON> [WordPress spraudņi](https://wordpress.org/plugins/search/minify+css/) var pa<PERSON>trin<PERSON>t jūsu vietnes darbību, savie<PERSON><PERSON><PERSON>, samazinot un saspiežot stilus. <PERSON><PERSON> iespē<PERSON><PERSON>, varat arī veikt šo samazin<PERSON>šanu iepriekš izveides procesā."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "<PERSON><PERSON><PERSON><PERSON> [WordPress spraudņi](https://wordpress.org/plugins/search/minify+javascript/) var paātrināt jūsu vietnes darbību, savie<PERSON><PERSON><PERSON>, samazinot un saspiežot skriptus. <PERSON>a iespē<PERSON>, varat veikt šo samazin<PERSON>šanu jau iepriekš izveides procesā."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "Ieteicams samazināt vai mainīt tādu [WordPress spraudņu](https://wordpress.org/plugins/) skaitu, kuri iel<PERSON><PERSON>ē nelietotu CSS kodu jūsu lapā. Lai identificētu spraudņus, kuri pievieno lieku CSS kodu, mēģiniet izpild<PERSON>t [koda pārklājumu](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage), izmantojot Chrome DevTools. Saistīto motīvu/spraudni varat identificēt stila lapas vietrādī URL. Meklējiet spraudņus, kuriem sarakstā ir daudz stila lapu ar daudz sarkanām atzīmēm koda pārklājumā. Spraudnim ir jāievieto rindā stilu lapa tikai tad, ja tā faktiski tiek izmantota lapā."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "Ieteicams samazināt vai mainīt tādu [WordPress spraudņu](https://wordpress.org/plugins/) skai<PERSON>, kuri iel<PERSON>dē nelietotu JavaScript kodu jūsu lapā. Lai identificētu spraudņus, kuri pievieno lieku JS kodu, mēģiniet izpild<PERSON>t [koda pārklājumu](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage), izmantojot Chrome DevTools. Saistīto motīvu/spraudni varat identificēt skripta vietrādī URL. Meklējiet spraudņus, kuriem sarakstā ir daudz skriptu ar daudz sarkanām atzīmēm koda pārklājumā. Spraudnim ir jāievieto rindā skripts tikai tad, ja tas faktiski tiek izmantots lapā."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "Uzziniet par [pārlūkprogrammas datu saglab<PERSON><PERSON><PERSON> kešatmiņā programmatūrā WordPress](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "Ieteicams izmantot [attēlu optimizācijas WordPress spraudni](https://wordpress.org/plugins/search/optimize+images/), kas sa<PERSON><PERSON><PERSON> attē<PERSON>, vien<PERSON>kus saglabā<PERSON>t k<PERSON>it<PERSON>."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "<PERSON><PERSON><PERSON><PERSON> aug<PERSON>dē<PERSON>et attēlus, i<PERSON><PERSON><PERSON><PERSON> [multivides bibliotēku](https://codex.wordpress.org/Media_Library_Screen), lai nodrošinātu nepieciešamo attēlu lielumu pieejamību. Pēc tam ievietojiet attēlus no multivides bibliotēkas vai izmantojiet attēlu logrīku, lai tiktu izmantoti optimāli attēlu lielumi (tostarp reaģējošām robežvērtībām paredzētie). Neizmantojiet `Full Size` attēlus, ja to izmēri neatbilst to lietojumam. [Uzziniet vairāk](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)."}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "Varat iespējot teksta saspieša<PERSON> tīmekļa servera konfigurācijā."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "Ieteicams izmantot [spraudni](https://wordpress.org/plugins/search/convert+webp/) vai pakal<PERSON>, kurā jūsu augšupielādētie attēli tiks automātiski pārveidoti optimālos formā<PERSON>."}}