{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "Přístupové klávesy uživatelům umožňují rychleji vybrat část stránky. Aby navigace fungovala správně, musí být každá přístupová klávesa jedinečná. [Další informace](https://web.dev/accesskeys/)"}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Hodnoty atributů `[accesskey]` nej<PERSON><PERSON> jed<PERSON>"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "Hodnoty `[accesskey]` jsou unik<PERSON>"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> ARIA `role` podporuje konkrétní podmnožinu atributů `aria-*`. Nesprávné přiřazení atributy `aria-*` zneplatní. [Dalš<PERSON> informace](https://web.dev/aria-allowed-attr/)"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Atributy `[aria-*]` neodpovídají svým rolím"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Atributy `[aria-*]` odpovídají příslušným rolím"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> role ARIA ma<PERSON><PERSON> p<PERSON><PERSON>, <PERSON><PERSON><PERSON> obrazovek popisují stav prvku. [Dalš<PERSON> informace](https://web.dev/aria-required-attr/)"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Prvky s atributy `[role]` nemají všechny povinné atributy `[aria-*]`"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "Prvky s atributy `[role]` mají všechny povinné atributy `[aria-*]`"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "N<PERSON>kt<PERSON><PERSON> nadřazené role ARIA musejí kvůli poskytov<PERSON>í správných funkcí přístupnosti obsahovat urč<PERSON> podřízené role. [Dalš<PERSON> informace](https://web.dev/aria-required-children/)"}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "V prvcích s atributem ARIA `[role]`, j<PERSON><PERSON><PERSON> podří<PERSON> prvky musí obsahovat konkrétní atribut `[role]`, ně<PERSON><PERSON><PERSON> z těchto povinných podřízených prvků chybí."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "Prvky s atributem ARIA `[role]`, j<PERSON><PERSON><PERSON> podřízené prvky musí obsahovat konkrétní atribut `[role]`, obsahují všechny povinné podřízené prvky."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "<PERSON><PERSON> pos<PERSON><PERSON>aly správn<PERSON> přís<PERSON>ti, m<PERSON><PERSON><PERSON> být někt<PERSON><PERSON> podřízené role ARIA umístěny v konkrétních nadřazených rolích. [Další informace](https://web.dev/aria-required-parent/)"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Prvky s atributem `[role]` nejsou umístěny v požadovaném nadřazeném prvku"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "Prvky s atributy `[role]`jsou umístěny v požadovaném nadřazeném prvku"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "Aby role ARIA pos<PERSON><PERSON><PERSON> správn<PERSON>, muse<PERSON><PERSON> mít platné hodnot<PERSON>. [Další informace](https://web.dev/aria-roles/)"}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> atri<PERSON> `[role]` ne<PERSON><PERSON><PERSON> p<PERSON>"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> atri<PERSON> `[role]` j<PERSON><PERSON> p<PERSON>"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Asistenční technologie, jak<PERSON> <PERSON><PERSON><PERSON> o<PERSON>zo<PERSON>, atributy ARIA s neplatnými hodnotami nedokážou interpretovat. [Další informace](https://web.dev/aria-valid-attr-value/)"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Atributy `[aria-*]` nema<PERSON><PERSON> platn<PERSON> hodnoty"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Atributy `[aria-*]` maj<PERSON> plat<PERSON> hodnoty"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "Asistenční technologie, jak<PERSON> <PERSON><PERSON><PERSON> o<PERSON>zo<PERSON>, atributy ARIA s neplatnými názvy nedokážou interpretovat. [Další informace](https://web.dev/aria-valid-attr/)"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Atributy `[aria-*]` nejsou platné nebo v nich jsou překlepy"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "Atributy `[aria-*]` jsou platné a nejsou v nich překlepy"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "Titulky umožňují sluchově postiženým uživatelům používat zvukové prvky tím, že jim poskytují kritické informace (například kdo mluví a co říká nebo další informace nesouvisející s řečí). [Dalš<PERSON> informace](https://web.dev/audio-caption/)"}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "V prvcích `<audio>` chy<PERSON><PERSON> prvek `<track>` s atributem `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "Prvky `<audio>` obs<PERSON><PERSON><PERSON> p<PERSON> `<track>` s atributem `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "<PERSON><PERSON>ž tlačítko nemá přístupný název, čtečky obrazovek ho oznamují jako „tlačítko“ a pro jejich uživatele je tak v podstatě nepoužitelné. [Další informace](https://web.dev/button-name/)"}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "Tlačítka nemají přístupné názvy"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "Tlačítka mají přístupné n<PERSON>zvy"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "<PERSON><PERSON>ž přidáte možnosti obejití repetitivn<PERSON><PERSON> obsahu, umožníte tím efektivnější procházení stránky pomocí klávesnice. [Další informace](https://web.dev/bypass/)"}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "Stránka neobsahuje nadpis, odkaz k přeskočení ani orientační bod regionu"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "Stránka obsahuje nadpis, odkaz k přeskočení nebo orientační bod regionu"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "Text s nízkým kontrastem je pro mnoho uživatelů obtížně čitelný nebo nečitelný. [Další informace](https://web.dev/color-contrast/)"}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "<PERSON><PERSON> poz<PERSON> a popředí nemají dostatečný kontrastní poměr."}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "<PERSON><PERSON> poz<PERSON> a popředí mají dostate<PERSON>ý kontrastní poměr"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "<PERSON><PERSON><PERSON>y definic nejsou správn<PERSON>, čtečky obrazovek mohou generovat matoucí nebo nepřesný výstup. [Další informace](https://web.dev/definition-list/)"}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "Prvky `<dl>` neo<PERSON><PERSON><PERSON><PERSON> jen správně seřazené skupiny prvků `<dt>` a `<dd>` nebo prvky `<script>` či `<template>`."}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "Prvky `<dl>` neo<PERSON><PERSON><PERSON><PERSON> jen správně seřazené skupiny prvků `<dt>` a `<dd>` nebo prvky `<script>` či `<template>`."}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "A<PERSON> moh<PERSON>čky obrazovek správně oznamovat položky seznamů definic (`<dt>` a `<dd>`), muse<PERSON><PERSON> být tyto položky umístěny v nadřazeném prvku `<dl>`. [Další informace](https://web.dev/dlitem/)"}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "Položky seznamu definic nejsou umístěny v prvcích `<dl>`"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "Položky seznamu definic jsou umístěny v prvcích `<dl>`"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "N<PERSON>zev (title) uživatelům čteček obrazovek poskytuje souhrnné informace o stránce a uživatelé vyhledávačů se podle něj rozhodují, zda je stránka pro jejich vyhledávání relevantní. [Další informace](https://web.dev/document-title/)"}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "Do<PERSON><PERSON> neobsahuje prvek `<title>`"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "Dokument obsahuje prvek `<title>`"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "Aby asistenční technologie nepřehlédly dal<PERSON> v<PERSON>, hodnota atributu id musí být jedine<PERSON>. [Další informace](https://web.dev/duplicate-id/)"}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "Atributy `[id]` na stránce nejsou jedinečné"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "Atributy `[id]` na stránce jsou jedinečné"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "Čtečky obrazovek obvykle k popisu obsahu rámců používají jejich atributy title. [Další informace](https://web.dev/frame-title/)"}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "Prvky `<frame>` nebo `<iframe>` nemají atribut title"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "Prvky `<frame>` a `<iframe>` mají atribut title"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "Pokud stránka neuvádí atribut lang, čtečky obrazovky předpokl<PERSON>daj<PERSON>, že je ve výchozím jaz<PERSON>ce, k<PERSON><PERSON> už<PERSON>l zvolil při nastavování čtečky obrazovky. Pokud stránka ve skutečnosti ve výchozím jazyce není, čtečka obrazovky text nemusí přečíst správně. [Další informace](https://web.dev/html-has-lang/)"}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Prvek `<html>` nemá atribut `[lang]`"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "Prvek `<html>` má atribut `[lang]`"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "Zadáním platného [jazyka BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) pomůžete čtečkám obrazovek správně oznamovat text. [Další informace](https://web.dev/html-lang-valid/)"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Prvek `<html>` nemá platnou hodnotu atributu `[lang]`."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "Prvek `<html>` má atribut `[lang]` s platnou hodnotou"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "Informativní prvky by m<PERSON><PERSON> <PERSON><PERSON>, popisný alternativní text. Dekorativní prvky lze ignorovat pomocí prázdného atributu alt. [Další informace](https://web.dev/image-alt/)"}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "Prvky obrázků nemají atributy `[alt]`"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "Prvky obrázků mají atributy `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "<PERSON><PERSON><PERSON> je jako <PERSON> `<input>` pou<PERSON><PERSON> o<PERSON>r<PERSON>, uvedení alternativního textu pomůže uživatelům čteček obrazovek porozumět účelu tlačítka. [Další informace](https://web.dev/input-image-alt/)"}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Prvky `<input type=\"image\">` nemají text `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "Prvky `<input type=\"image\">` mají text `[alt]`"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "Štítky z<PERSON>, aby asistenční technologie (například čtečky obrazovek) správně oznamovaly ovládací prvky formulářů. [Další informace](https://web.dev/label/)"}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "K prvkům formulářů nejsou přidružené <PERSON>"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "K prvkům formulářů jsou přidružené <PERSON>ky"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "Tabulka použitá pro účely rozvržení by <PERSON><PERSON><PERSON><PERSON> o<PERSON><PERSON> da<PERSON> prvky, jako jsou prvky th nebo caption ani atribut summary. Pro uživatele čteček obrazovek to m<PERSON><PERSON><PERSON> být matoucí. [Další informace](https://web.dev/layout-table/)"}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "Prezentační prvky `<table>` obsahují prvky `<th>` či `<caption>` nebo atribut `[summary]`."}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "Prezentační prvky `<table>` neobsahují prvky `<th>` a `<caption>`ani atribut `[summary]`."}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "Text odka<PERSON>ů (a náhradní text ob<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> jsou použité jako odkazy), kter<PERSON> je rozeznatelný a jedinečný a který lze vybrat, uživatelům čteček obrazovek usnadňuje procházení stránek. [Další informace](https://web.dev/link-name/)"}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "Od<PERSON>zy nemají rozeznatelné názvy"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> maj<PERSON> rozez<PERSON>lné názvy"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "Čtečky obrazovek oznamují seznamy speciálním způsobem. Použitím správné struktury seznamu pomůžete čtečkám obrazovek s výstupem. [Další informace](https://web.dev/list/)"}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "Seznamy neobsahují výhradně prvky `<li>` a prvky, kter<PERSON> podporují skrip<PERSON> (`<script>` a `<template>`)."}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "Seznamy obsahují výhradně prvky `<li>` a prvky, kter<PERSON> podporují skripty (`<script>` a `<template>`)."}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "Aby čtečky obrazovek prvky `<li>` a `<ul>` oz<PERSON><PERSON><PERSON> správně, muse<PERSON><PERSON> být tyto prvky umístěny v nadřazených položkách (`<ol>`). [Další informace](https://web.dev/listitem/)"}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "Polož<PERSON> seznamů (`<li>`) nejsou umístěny v nadřazených prvcích `<ul>` nebo `<ol>`."}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "Položky seznamu (`<li>`) jsou umístěny v nadřazených prvcích `<ul>` nebo `<ol>`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "Uživatelé neočekávají, že se stránka bude automaticky obnovovat. Při automatickém obnovení se prohlížeč vrátí zpět na začátek stránky. M<PERSON>že to vést k nepříjemnému nebo matoucímu chování při procházení. [Další informace](https://web.dev/meta-refresh/)"}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "V dokumentu je použita metaznačka `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "V dokumentu není použita metaznačka `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "Deaktivace změny velikosti zobrazení je problematická pro slaboz<PERSON> u<PERSON>, kte<PERSON><PERSON> jsou při prohlížení obsahu webové stránky závislí na přiblížení obrazovky. [Další informace](https://web.dev/meta-viewport/)"}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "V prvku `[user-scalable=\"no\"]` je použit atribut `<meta name=\"viewport\">` nebo je atribut `[maximum-scale]` men<PERSON><PERSON> než 5."}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "V prvku `[user-scalable=\"no\"]` není použit atribut `<meta name=\"viewport\">` a atribut `[maximum-scale]` není men<PERSON> než 5."}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "Čtečky obrazovek nedokážou přeložit obsah jiného typu, než je text. Když k prvkům `<object>` přidáte alternativní text, čtečky obrazovek budou moci předat uživatelům význam. [Další informace](https://web.dev/object-alt/)"}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "Prvky `<object>` nemají text `[alt]`"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "Prvky `<object>` mají text `[alt]`"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "Hodnota větší než 0 naznačuje explicitní řazení navigace. Ačkoli je platná, často vede k chování, které je pro uživatele závislé na asistenčních technologiích nepříjemné. [Další informace](https://web.dev/tabindex/)"}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "Některé prvky mají hodnot<PERSON> `[tabindex]` větší než 0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "Žádný prvek nemá hodnotu `[tabindex]` větší než 0"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "Čtečky obrazovek maj<PERSON>, k<PERSON><PERSON> usnadňují procházení tabulek. <PERSON><PERSON><PERSON>, aby buň<PERSON> `<td>` s atributem `[headers]` odkazovaly pouze na jiné buňky ve stejné tabulce, můžete tím uživatelům čteček obrazovek usnadnit používání. [Další informace](https://web.dev/td-headers-attr/)"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Buňky v prvku `<table>`, které mají atribut `[headers]`, odkazují na prvek `id`, který se nenachází ve stejné tabulce."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "Buňky v prvku `<table>`, které mají atribut `[headers]`, odkazují na buňky ve stejné tabulce."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "Čtečky obrazovek maj<PERSON>, k<PERSON><PERSON> usnadňují procházení tabulek. <PERSON><PERSON><PERSON>, aby záhlaví tabulek vždy odkazovala na nějakou množinu buněk, bude pro uživatele čteček obrazovek procházení stránky snazší. [Další informace](https://web.dev/th-has-data-cells/)"}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Prvky `<th>` a prvky s atributem `[role=\"columnheader\"/\"rowheader\"]` ne<PERSON><PERSON><PERSON> b<PERSON>, k<PERSON><PERSON>."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "Prvky `<th>` a prvky s atributem `[role=\"columnheader\"/\"rowheader\"]` maj<PERSON> b<PERSON>, k<PERSON><PERSON>."}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "Pokud je u prvků uveden platný [jazyk BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) pomů<PERSON><PERSON> to zajistit, aby čtečky obrazovek text četly správně. [Další informace](https://web.dev/valid-lang/)"}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Atributy `[lang]` nema<PERSON><PERSON> platnou hodnotu"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "Atributy `[lang]` mají platnou hodnotu"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "<PERSON><PERSON>ž jsou u videa k dispozici titulky, je pro sluchově postižené uživatele snazší využít informace ve videu. [Další informace](https://web.dev/video-caption/)"}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "Prvky `<video>` neo<PERSON><PERSON><PERSON><PERSON> p<PERSON> `<track>` s atributem `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "Prvky `<video>` obs<PERSON><PERSON><PERSON> p<PERSON> `<track>` s atributem `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "Zvukové popisy u videí poskytují relevantní informace, kter<PERSON> nejsou patrné z dialogů, jako jsou například výrazy v obličejích a popisy scén. [Další informace](https://web.dev/video-description/)"}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "Prvky `<video>` neo<PERSON><PERSON><PERSON><PERSON> p<PERSON> `<track>` s atributem `[kind=\"description\"]`."}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "Prvky `<video>` obs<PERSON><PERSON><PERSON> p<PERSON> `<track>` s atributem `[kind=\"description\"]`"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "Pro ideální vzhled po přidání progresivní webové aplikace na plochu v systému iOS definujte atribut `apple-touch-icon`. Musí odkazovat na neprůhledný čtvercový obrázek PNG se stranami o délce 192 px (nebo 180 px). [Další informace](https://web.dev/apple-touch-icon/)"}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> plat<PERSON>ý atribut `apple-touch-icon`"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "Atribut `apple-touch-icon-precomposed` je <PERSON>. <PERSON> <PERSON>ován atribut `apple-touch-icon`."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> plat<PERSON>ý atribut `apple-touch-icon`"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Rychlost načítání této stránky byla negativně ovlivněna rozšířeními pro Chrome. Zkuste stránku zkontrolovat v anonymním režimu nebo profilu Chromu bez rozšíření."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "Vyhodnocování sk<PERSON>tů"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "<PERSON><PERSON><PERSON><PERSON>ů"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "Celková doba využití procesoru"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "Pokuste se zkrátit dobu analyzování, kompilování a spouštění JavaScriptu. Mohlo by pomoci odesílat menší soubory JavaScript. [Další informace](https://web.dev/bootup-time)"}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "Zkraťte dobu provádění JavaScriptu"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "Doba provádění <PERSON>u"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Velké soubory GIF nejsou efektivní k zobrazování animovaného obsahu. Zvažte, zda byste namísto souborů GIF nemohli pro animace použít videa MPEG4/WebM a pro statické obrázky soubory PNG/WebP. Snížíte tak množství přenášených dat. [Další informace](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Pro animovaný obsah používejte formáty videa"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Zvažte možnost načítat obrázky mimo obrazovku a skryté obrázky „líně“ až po načtení všech kritických zdrojů, abyste zkrátili dobu k dosažení interaktivnosti. [Další informace](https://web.dev/offscreen-images)"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Odložte načítání obrázků mimo obrazovku"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "První vykreslení stránky blokují zdroje. Zvažte, zda byste kriticky důležité zdroje JavaScript a CSS nemohli poskytovat přímo v kódu a stahování veškerého nekritického JavaScriptu a stylů odložit. [Další informace](https://web.dev/render-blocking-resources)"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> blo<PERSON> v<PERSON>len<PERSON>"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Přenášení velkého množství dat po síti je pro uživatele finančně nákladné a obvykle vede k pomalému načítání. [Další informace](https://web.dev/total-byte-weight)"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Celková velikost byla {totalBytes, number, bytes} kB"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Předejděte přenášení enormního množství dat"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Nepřenáš<PERSON> enormní množství dat"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "Minifikací souborů CSS lze snížit množství přenášených dat. [Další informace](https://web.dev/unminified-css)"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "Minifikujte kód CSS"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Minifikací souborů JavaScript lze snížit množství přenášených dat a zrychlit analýzu skriptů. [Další informace](https://web.dev/unminified-javascript)"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Minifikujte JavaScript"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Odstraňte ze šablon stylů nepoužívaná pravidla a odložte načítání stylů CSS, které se nepoužívají pro obsah zobrazený bez posouvání, abyste snížili množství nepotřebných dat využívaných síťovou aktivitou. [Další informace](https://web.dev/unused-css-rules)"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Odstraňte nepoužívané styly CSS"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Odstraněním nepoužívaného JavaScriptu zmenšíte množství přenášených dat."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Odstraňte nepoužívaný JavaScript"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Dlouhá platnost mezipaměti může zrychlit opakované návštěvy stránky. [Další informace](https://web.dev/uses-long-cache-ttl)"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Byl nalezen 1 zdroj}few{<PERSON><PERSON> nalezeny # zdroje}many{<PERSON><PERSON> nalezeno # zdroje}other{Bylo nalezeno # zdrojů}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Statické podklady zobrazujte s efektivními zásadami pro mezipaměť"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Používá u statických podkladů efektivní zásady pro mezipaměť"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Optimalizované obrázky se načítají rychle a spotřebovávají méně mobilních dat. [Další informace](https://web.dev/uses-optimized-images)"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Používejte efektivní kódování obrázků"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Zobrazujte obrázky s vhodnou velikostí, abyste ušetřili mobilní data a zrychlili načítání. [Další informace](https://web.dev/uses-responsive-images)"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Používejte správnou velikost obrázků"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Textové zdroje by se mě<PERSON> o<PERSON> komp<PERSON>ova<PERSON> (gzip, deflate nebo brotli), aby se <PERSON><PERSON><PERSON>lo množství přenášených dat. [Další informace](https://web.dev/uses-text-compression)"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Zapněte kompresi textu"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "Formáty obrázků JPEG 2000, JPEG XR a WebP často poskytují lepší kompresi než formáty PNG a JPEG, což znamená rychlejší stahování a menš<PERSON> spotřebu dat. [Další informace](https://web.dev/uses-webp-images)"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "Zobrazujte obrázky ve formátech nové generace"}, "lighthouse-core/audits/content-width.js | description": {"message": "Pokud se šířka obsahu aplikace neshoduje se šířkou zobrazované oblasti, aplikace nemusí být optimalizována pro obrazovky mobilních telefonů. [Další informace](https://web.dev/content-width)"}, "lighthouse-core/audits/content-width.js | explanation": {"message": "Velikost zobrazované oblasti {innerWidth} px se neshoduje s velikostí okna {outerWidth} px."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "Obsah nemá správnou velikost pro zobrazovanou oblast"}, "lighthouse-core/audits/content-width.js | title": {"message": "Obsah má správnou velikost pro zobrazovanou oblast"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "Řetězce kritických požadavk<PERSON> níže ukazují, kter<PERSON> zdroje se načítají s vysokou prioritou. <PERSON><PERSON><PERSON><PERSON>, zda byste načítání stránky nemohli vylepšit tím, že řetězce zkr<PERSON>tí<PERSON>, zmenšíte zdroje nebo odložíte stahování zdrojů, které nejsou nezbytné. [Další informace](https://web.dev/critical-request-chains)"}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Byl nalezen 1 řetěze<PERSON>}few{Byly nalezeny # řetězce}many{Bylo nalezeno # řetězce}other{Bylo nalezeno # řetězců}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "Minimalizujte hloubku kritických požadavků"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "Ukončení podpory / upozornění"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "Řádek"}, "lighthouse-core/audits/deprecations.js | description": {"message": "Zastaralá rozhraní API budou z prohlížeče v budoucnu odstraněna. [Další informace](https://web.dev/deprecations)"}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Bylo nalezeno 1 upozornění}few{Byla nalezena # upozornění}many{Bylo nalezeno # upozornění}other{Bylo nalezeno # upozornění}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "Používá zastaralá rozhraní API"}, "lighthouse-core/audits/deprecations.js | title": {"message": "Nepoužívá zastaralá rozhraní API"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "Mezipaměť aplikace je zastaralá technologie. [Další informace](https://web.dev/appcache-manifest)"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "<PERSON><PERSON><PERSON><PERSON> „{AppCacheManifest}“"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "Používá mezipaměť aplikace"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "Nepoužívá mezipaměť aplikace"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "Zadáním typu dokumentu (DOCTYPE) předejdete přechodu prohlížeče do adaptivního režimu. [Další informace](https://web.dev/doctype)"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Název typu dokumentu (DOCTYPE) musí být <PERSON> `html` psaný malými písmeny"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Dokument musí obsahovat deklaraci typu dokumentu DOCTYPE"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "V poli publicId je očekáván prázdný řetězec"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "V poli systemId je očekáván prázdný řetězec"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Na stránce není deklarace typu dokumentu (DOCTYPE) HTML, proto se aktivuje adaptivní re<PERSON>im"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "Stránka má deklaraci typu dokumentu (DOCTYPE) HTML"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "Prvek"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistika"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Hodnota"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "Vý<PERSON>j<PERSON><PERSON><PERSON>, aby stránky obsahovaly méně než cca. 1 500 prvků modelu DOM. Ideálně by hloubka stromu měla být menš<PERSON> než 32 prvků a každý nadřazený prvek by mě<PERSON> mít méně než 60 podřízených prvků. Velký model DOM může vést k většímu využit<PERSON> paměti, del<PERSON><PERSON><PERSON> [výpočtům stylů](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) a náročným [přeformátováváním rozvržení](https://developers.google.com/speed/articles/reflow). [Další informace](https://web.dev/dom-size)"}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 prvek}few{# prvky}many{# prvku}other{# prvků}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Nepoužívejte př<PERSON><PERSON>š <PERSON>lk<PERSON> model DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Maximální hloubka modelu DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Celkový počet prvků DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Maximální počet podřízených prvků"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "Nepoužívá př<PERSON><PERSON><PERSON> velk<PERSON> model DOM"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "Cíl"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "Ke všem externím odkazům přidejte atribut `rel=\"noopener\"` nebo `rel=\"noreferrer\"`. Stránku tím zrychlíte a předejdete ohrožení zabezpečení. [Další informace](https://web.dev/external-anchors-use-rel-noopener)"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "Odkazy na cíle v jiných doménách nejsou bezpečné"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "Odkazy na cíle v jiných doménách jsou bezpečné"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "Nepodařilo se určit cíl ukotvení ({anchorHTML}). Pokud tento prvek nepoužíváte jako hypertextový odkaz, zvažte odstranění atributu target=_blank."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>, kter<PERSON> bez kontextu žádají o polohu, mohou být uživatelé nedůvěřiví nebo z nich mohou být zmateni. Zvažte možnost spojit tuto žádost s akcí uživatele. [Další informace](https://web.dev/geolocation-on-start)"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Žádá při načtení stránky o oprávnění ke geolokaci"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Nežádá při načtení stránky o oprávnění ke geolokaci"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Verze"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "Všechny frontendové javascriptové knihovny zjištěné na stránce. [Další informace](https://web.dev/js-libraries)"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "<PERSON><PERSON> javascriptové knihovny"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "U uživatelů s pomalým připojením mohou externí skripty vkládané metodou zpozdit `document.write()` načtení stránky o desítky sekund. [Další informace](https://web.dev/no-document-write)"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Používá metodu `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "Nepoužívá metodu `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "Největší závažnost"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "Verze knihovny"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "Počet chyb zabezpečení"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "N<PERSON>kt<PERSON><PERSON> skripty třetích stran mohou obsahovat zná<PERSON> chyby zabezpečení, k<PERSON><PERSON> mohou snadno odhalit a zneužít. [Další informace](https://web.dev/no-vulnerable-libraries)"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{<PERSON><PERSON> jedna chyba zabezpečení}few{By<PERSON> z<PERSON> # chyby zabezpečení}many{Bylo zjištěno # chyby zabezpečení}other{Bylo zjištěno # chyb zabezpečení}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "Zahrnuje frontendové javascriptové knihovny se známými chybami zabezpečení"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "Vysoká"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "Nízká"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "Střední"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "Nepoužívá frontendové javascriptové knihovny se známými chybami zabezpečení"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>, které bez kontextu žádají o oprávnění odesílat <PERSON>, mohou být uživatelé nedůvěřiví nebo z nich mohou být zmateni. Zvažte možnost spojit tuto žádost s gesty uživatele. [Další informace](https://web.dev/notification-on-start)"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Žádá při načtení stránky o oprávnění zobrazovat oznámení"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "Nežádá při načtení stránky o oprávnění zobrazovat oznámení"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "Blokování vkládání hesel je v rozporu s dobrými bezpečnostními zásadami. [Další informace](https://web.dev/password-inputs-can-be-pasted-into)"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "Brání uživatelům ve vkládání obsahu do polí pro hesla"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "Povoluje uživatelům vkládání obsahu do polí pro hesla"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokol"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "Protokol HTTP/2 oproti protokolu HTTP/1.1 nabízí mnoho výhod, v<PERSON><PERSON><PERSON><PERSON> bin<PERSON><PERSON><PERSON><PERSON> z<PERSON>hlaví, multiplexingu a přenášení metodou push ze serveru. [Další informace](https://web.dev/uses-http2)"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 požadavek nebyl realizován pomocí protokolu HTTP/2}few{# požadavky nebyly realizovány pomocí protokolu HTTP/2}many{# požadavku nebylo realizováno pomocí protokolu HTTP/2}other{# požadavků nebylo realizováno pomocí protokolu HTTP/2}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "Nepoužívá pro všechny zdroje protokol HTTP/2"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "Pro vlastní zdroje používá protokol HTTP/2"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Zvažte označení posluchačů událostí dotyku a kolečka jako pasivn<PERSON>ch (`passive`), aby se stránka posouvala plynuleji. [Další informace](https://web.dev/uses-passive-event-listeners)"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Nepoužívá pasivní p<PERSON>, k<PERSON><PERSON> p<PERSON>"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Používá pasivní p<PERSON>, k<PERSON><PERSON><PERSON><PERSON> poso<PERSON>"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "Chyby zaprotokolované do konzole ukazují na nevyřešené problémy. Mohou pocházet ze selhání síťových požadavků nebo jiných problémů v prohlížeči. [Další informace](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "Do konzole byly zaprotokolovány chyby prohlížeče"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "Do konzole nebyly zaprotokolovány žádné chyby prohlížeče"}, "lighthouse-core/audits/font-display.js | description": {"message": "Pomocí <PERSON> font-display stylů CSS zajistěte, aby byl text při načítání webfontů viditelný uživatelům. [Dalš<PERSON> informace](https://web.dev/font-display)"}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, aby text při načítání webfontů zůstal viditelný"}, "lighthouse-core/audits/font-display.js | title": {"message": "Při načítání webfontů zůstává veškerý text viditelný"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "Nástroj Lighthouse nemohl automaticky zkontrolovat hodnotu font-display pro následující adresu URL: {fontURL}."}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "<PERSON><PERSON><PERSON><PERSON> (skutečný)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "<PERSON><PERSON><PERSON><PERSON> (zobrazený)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "Zobrazované roz<PERSON> o<PERSON> by m<PERSON><PERSON> o<PERSON><PERSON><PERSON><PERSON> př<PERSON>zenému poměru stran. [Další informace](https://web.dev/image-aspect-ratio)"}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "Zobrazuje obrázky s nesprávným poměrem stran"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "Zobrazuje obrázky se správným poměrem stran"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "Neplatné informace o velikosti obrázku {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "Prohl<PERSON>žeče mohou uživatele aktivně vyzývat, aby si vaši aplikaci přidali na plochu, což může vést k vyšší míře interakce. [Další informace](https://web.dev/installable-manifest)"}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "Manifest webové aplikace nesplňuje instalační požadavky"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "Manifest webové aplikace splňuje instalační požadavky"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "Nezabezpečená adresa URL"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "Všechny weby (v<PERSON><PERSON><PERSON><PERSON> těch, k<PERSON><PERSON> s citlivými daty), by m<PERSON><PERSON> b<PERSON><PERSON>ny protokolem HTTPS. Protokol HTTPS útočníkům zabraňuje v manipulaci s komunikací mezi vaší aplikací a uživateli (nebo v jejím pasivním poslechu) a je nezbytný pro HTTP/2 a mnoho nových webových rozhraní API. [Další informace](https://web.dev/is-on-https)"}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Byl nalezen 1 nezabezpečený požadavek}few{Byly nalezeny # nezabezpečené požadavky}many{Bylo nalezeno # nezabezpečeného požadavku}other{Bylo nalezeno # nezabezpečených požadavků}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "Nepoužívá protokol HTTPS"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "Používá protokol HTTPS"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "Rychlé načítání stránky přes mobilní síť zajišťuje dobrý dojem pro uživatele mobilních zařízení. [Další informace](https://web.dev/load-fast-enough-for-pwa)"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "Interaktivní za {timeInMs, number, seconds} s"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "V simulované mobilní síti je stránka interaktivní za {timeInMs, number, seconds} s"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "Vaše stránka se načítá příliš pomalu a neumožňuje interakci do 10 sekund. Podívejte se na příležitosti a diagnostiku v sekci Výkon, abyste zjistili, jak stránku vylepšit."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "Stránka se v mobilních sítích nenačítá dostatečně rychle"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "Stránka se v mobilních sítích načítá dostatečně rychle"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "Pokuste se zkrátit dobu analyzování, kompilování a spouštění JavaScriptu. Mohlo by pomoci odesílat menší soubory JavaScript. [Další informace](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimalizujte práci v hlavním podprocesu"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "Minimalizuje práci v hlavním podprocesu"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "Chcete-li z<PERSON>hnout co největší počet uživatelů, měly by webové stránky fungovat ve všech nejpoužívanějš<PERSON>ch prohlížečích. [Další informace](https://web.dev/pwa-cross-browser)"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "Web funguje v různých prohlížečích"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, aby na jednotlivé stránky bylo možné přidat přímý odkaz prostřednictvím adresy URL a aby s ohledem na možnost sdílení na sociálních sítích byly adresy URL jedinečné. [Další informace](https://web.dev/pwa-each-page-has-url)"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Každá stránka má adresu URL"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "Přechody by m<PERSON><PERSON> b<PERSON><PERSON>ul<PERSON> i v pomalé síti. Je to velmi důležité pro dobrý uživatelský pocit. [Další informace](https://web.dev/pwa-page-transitions)"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "Přechody na jiné stránky nepůsobí zabržděně"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "Odhadovaná latence vstupu udává, jak <PERSON><PERSON><PERSON> (v milisekundách) bude aplikaci během nejvytíženějších pěti sekund při načítání stránky odhadem trvat reakce na uživatelský vstup. Pokud je latence vět<PERSON><PERSON> než 50 ms, mohou uživatelé chování aplikace vnímat jako přerušované. [Další informace](https://web.dev/estimated-input-latency)"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "Odhadovaná latence vstupu"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "První vykreslení obsahu je okamžik vykreslení prvního textu nebo obrázku. [Další informace](https://web.dev/first-contentful-paint)"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "První vykreslení obsahu"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "První nečinnost procesoru udává čas, kdy je hlavní podproces stránky dostatečně nečinný na to, aby bylo možné zpracovat vstup.  [Další informace](https://web.dev/first-cpu-idle)"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "První nečinnost procesoru"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "První smy<PERSON>luplné vykreslení udává, kdy za<PERSON> být viditelný primární obsah str<PERSON>. [Další informace](https://web.dev/first-meaningful-paint)"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "První smysluplné vykreslení"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "Doba do interaktivity ud<PERSON><PERSON><PERSON>, jak d<PERSON>ho trvá, než stránka začne být plně interaktivní. [Další informace](https://web.dev/interactive)"}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "Doba do interaktivity"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "Maximální poten<PERSON>ální prodleva prvn<PERSON><PERSON> vstupu, k<PERSON><PERSON> by <PERSON><PERSON><PERSON><PERSON> moh<PERSON>, je tr<PERSON><PERSON> ne<PERSON> (v milisekundách). [Další informace](https://developers.google.com/web/updates/2018/05/first-input-delay)"}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "Maximální potenciální prodleva prvního vstupu"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "Index rychlosti ukazuje, jak rychle se viditelně vyplní obsah str<PERSON>. [Další informace](https://web.dev/speed-index)"}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "Index rychlosti"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "Součet všech dob (uvedený v milisekundách) mezi prvním vykreslením obsahu a dobou do interaktivity, u nichž délka úlohy překročila 50 ms."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "<PERSON><PERSON><PERSON> doba blokování"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "Na výkon má velký vliv doba odezvy sítě. Pokud je doba odezvy připojení ke zdroji vysoká, zna<PERSON><PERSON> to, že by se výkon mohl zlepšit při použití serverů méně vzdálených od uživatele. [Další informace](https://hpbn.co/primer-on-latency-and-bandwidth/)"}, "lighthouse-core/audits/network-rtt.js | title": {"message": "<PERSON><PERSON> odez<PERSON> s<PERSON>"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "Na výkon webu může mít dopad latence serverů. Vysoká latence serveru značí, že je server přetížen nebo že backend nen<PERSON> dostatečně výkonný. [Další informace](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)"}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "Latence backendu na serveru"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "Soubor service worker va<PERSON><PERSON> webové aplikaci umožňuje, aby byla spolehlivá i za nepředvídatelného stavu sítě. [Další informace](https://web.dev/offline-start-url)"}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "V rež<PERSON><PERSON> offline `start_url` nereaguje stavovým kódem 200"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "V re<PERSON><PERSON><PERSON> offline `start_url` reaguje stavovým kódem 200"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "Nástroji Lighthouse se v manifestu nepodařilo přečíst atribut `start_url`. Proto se předpokládá, že adresou URL dokumentu je `start_url`. Chybová zpráva: {manifestWarning}"}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "Udržujte množství a velikost síťových požadavků pod cílovými hodnotami, které udává poskytnutý rozpočet výkonu. [Další informace](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 požadavek}few{# požadavky}many{# požadavku}other{# požadavků}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "Rozpočete výkonu"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "Pokud jste protokol HTTPS už <PERSON>sta<PERSON>, nezapomeňte veškerý provoz přes protokol HTTP přesměrovat na HTTPS, aby všichni vaši uživatelé měli k dispozici funkce zabezpečeného webu. [Další informace](https://web.dev/redirects-http)"}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "Nepřesměrovává provoz přes protokol HTTP na HTTPS"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "Přesměrovává provoz přes protokol HTTP na HTTPS"}, "lighthouse-core/audits/redirects.js | description": {"message": "Přesměrování způsobují dalš<PERSON> prodlevy před načtením strán<PERSON>. [Další informace](https://web.dev/redirects)"}, "lighthouse-core/audits/redirects.js | title": {"message": "Nepoužívejte několik přesměrování stránky"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "Chcete-li nastavit rozpočet pro množství a velikost zdrojů na stránce, přidejte soubor budget.json. [Další informace](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 pož<PERSON><PERSON>k • {byteCount, number, bytes} kB}few{# po<PERSON><PERSON><PERSON>ky • {byteCount, number, bytes} kB}many{# požada<PERSON>ku • {byteCount, number, bytes} kB}other{# požadavků • {byteCount, number, bytes} kB}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "Používejte málo p<PERSON>žadavků a malé velikosti přenosů"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "Odkazy na kanonické verze slouží jako návrhy, kter<PERSON> adresy URL se mají zobrazovat ve výsledcích vyhledávání. [Další informace](https://web.dev/canonical)"}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "Několik konfliktních adres URL ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "Odkazuje na jinou doménu ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "Neplatná adresa URL ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Odkazuje na jiné umístění `hreflang` ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "Relativní adresa URL ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "Neodkazuje na ekvivalentní obsahovou stránku, ale na kořenovou adresu URL domény (domovskou stránku)"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "Dokument nemá platný atribut `rel=canonical`"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "Dokument má platný odkaz `rel=canonical`"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "Písma menší než 12 px jsou příliš malá na to, aby byla čitelná. Návštěvníci na mobilních zařízeních je kvůli čtení musí zvětšit roztažením prstů. Snažte se, aby více než 60 % textu na stránce mělo velikosti alespoň 12 px. [Další informace](https://web.dev/font-size)"}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "Pod<PERSON><PERSON> textu: {decimalProportion, number, extendedPercent}"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "Text nen<PERSON>, protože není k dispozici metaznačka viewport optimalizovaná pro obrazovky mobilních zařízení."}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "<PERSON>d<PERSON><PERSON><PERSON> ma<PERSON> textu: {decimalProportion, number, extendedPercent} (posuzovaný vzorek: {decimalProportionVisited, number, extendedPercent})."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "V dokumentu nejsou použity čitelné velikosti písma"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "V dokumentu jsou použity čitelné velikosti písma"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "Odkazy hreflang sděluj<PERSON> v<PERSON>, k<PERSON><PERSON> verzi stránky mají uvádět ve výsledcích vyhledávání pro určitý jazyk či oblast. [Další informace](https://web.dev/hreflang)"}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "Dokument nemá platný atribut `hreflang`"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "Dokument má platný atribut `hreflang`"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "Stránky s neúspěšnými stavovými kódy HTTP nemusejí být správně indexovány. [Další informace](https://web.dev/http-status-code)"}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "Stránka má neúspěšný stavový kód HTTP"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "Stránka má úspěšný stavový kód HTTP"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "Pokud vyhledávače nemají oprávnění procházet va<PERSON><PERSON>, ne<PERSON><PERSON> je zahrnout do výsledků vyhledávání. [Další informace](https://web.dev/is-crawable)"}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "Indexování str<PERSON> je blokováno"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "Indexování str<PERSON> nen<PERSON> b<PERSON>"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "Popisný text od<PERSON><PERSON><PERSON> pomá<PERSON> vyhledávačům porozumět vašemu obsahu. [Další informace](https://web.dev/link-text)"}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Byl nalezen 1 odkaz}few{<PERSON><PERSON> nalezeny # odkazy}many{<PERSON>lo nalezeno # odkazu}other{Bylo nalezeno # odkazů}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "<PERSON>d<PERSON>zy nemají popisný text"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> maj<PERSON> pop<PERSON> text"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "Chcete-li ověřit strukturovaná data, spusťte [nástroj na testování strukturovaných dat](https://search.google.com/structured-data/testing-tool/) a [nástroj Structured Data Linter](http://linter.structured-data.org/). [Další informace](https://web.dev/structured-data)"}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "Strukturovaná data jsou platná"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "<PERSON><PERSON><PERSON> „description“ mů<PERSON>e být zahrnut ve výsledcích vyhledávání jako stručný souhrn obsahu stránky. [Další informace](https://web.dev/meta-description)"}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "Popisný text je pr<PERSON>ý."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "Dokument nemá metaznačku „description“"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "Dokument má metaznačku „description“"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "Vyhledávače obsah pluginů nedokážou indexovat a na mnoha zařízeních jsou pluginy zakázány nebo nejsou podporovány. [Další informace](https://web.dev/plugins)"}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "Dokument používá pluginy"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "V dokumentu nejsou použity pluginy"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "Pokud soubor robots.txt nemá správ<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>e nemusejí být schopné z<PERSON>it, jak váš web mají procházet nebo indexovat. [Další informace](https://web.dev/robots-txt)"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Na žádost o soubor robots.txt byl vrácen tento stav HTTP: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Byla nalezena 1 chyba}few{Byly nalezeny # chyby}many{Bylo nalezeno # chyby}other{Bylo nalezeno # chyb}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "Nástroji Lighthouse se nepodařilo načíst soubor robots.txt."}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "Soubor robots.txt nen<PERSON> plat<PERSON>"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "Soubor robots.txt je platný"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "Interaktivní prvky, jako jsou tlačí<PERSON> a od<PERSON><PERSON>, by m<PERSON><PERSON> b<PERSON><PERSON> dostate<PERSON><PERSON> ve<PERSON> (48 × 48 px) a mělo by ok<PERSON> nich být dost místa na to, aby se na ně dalo dostatečně snadno klepnout bez přesahování do dalších prvků. [Další informace](https://web.dev/tap-targets)"}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "<PERSON><PERSON><PERSON><PERSON> dos<PERSON><PERSON><PERSON> velkých dotykových prvků: {decimalProportion, number, percent}"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Dotykové prvky jsou p<PERSON><PERSON><PERSON>, protože není k dispozici metaznačka viewport optimalizovaná pro obrazovky mobilních zařízení."}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "Dotykové prvky nejsou dostatečně velké"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Překrývající se cíl"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Dotykový prvek"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "Dotykové prvky jsou dostateč<PERSON> velké"}, "lighthouse-core/audits/service-worker.js | description": {"message": "Service worker je technologie, k<PERSON>á aplikaci umožňuje využívat mnoho funkcí progresivní webové aplikace, jako je re<PERSON><PERSON> offline, přidání na plochu nebo oznámení push. [Další informace](https://web.dev/service-worker)"}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "<PERSON><PERSON> s<PERSON> ovlád<PERSON> soubor service worker, nebyl ale nalezen atribut `start_url`, proto<PERSON>e se manifest nepodařilo analyzovat jako platný soubor JSON"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "<PERSON><PERSON> s<PERSON> ovl<PERSON><PERSON><PERSON> soubor service worker, ale atribut `start_url` ({startUrl}) pod soubor service worker ({scopeUrl}) nespadá."}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "<PERSON><PERSON> s<PERSON> ovlá<PERSON>á soubor service worker, ale atribut `start_url` nebyl na<PERSON>, proto<PERSON>e nebyl načten žádný manifest."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "<PERSON><PERSON> zdroj má jeden či více souborů service worker, ale str<PERSON> ({pageUrl}) pod něj nespadá."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "Neregis<PERSON><PERSON><PERSON> so<PERSON>or service worker, který ovlád<PERSON> stránku a `start_url`"}, "lighthouse-core/audits/service-worker.js | title": {"message": "Regis<PERSON><PERSON>je soubor service worker, který ovládá stránku a atribut `start_url`"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "Stylová úvodní obrazovka zajišťuje kvalitní uživatelský dojem při spuštění aplikace z plochy. [Další informace](https://web.dev/splash-screen)"}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "<PERSON><PERSON>í <PERSON>gurována vlastní úvodní obrazovka"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "Je nakonfigurována vlastní úvodní obrazovka"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "Motiv adresního řádku prohlížeče lze přizpůsobit motivu vašeho webu. [Další informace](https://web.dev/themed-omnibox)"}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "Nenastavuje barvu motivu adresního <PERSON>."}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "Nastavuje barvu motivu adresního <PERSON>."}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "Doba blokování hlavního podprocesu"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "Třetí strana"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "<PERSON><PERSON><PERSON> třetích stran může mít významný dopad na rychlost načítání. Omezte počet redundantních externích poskytovatelů a snažte se kód třetích stran načítat až poté, co se dokončí načtení va<PERSON>í strán<PERSON>. [Další informace](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)"}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "<PERSON><PERSON><PERSON> tř<PERSON>í strany na {timeInMs, number, milliseconds} ms zablokoval hlavní podproces"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "Snižte vliv kódu třetích stran"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "Použití zdrojů od třetích stran"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "Doba do načtení prvn<PERSON><PERSON> baj<PERSON> u<PERSON>, jak dlouho vašemu serveru trvá, než odešle odpověď. [Další informace](https://web.dev/time-to-first-byte)"}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "Hlavní dokument trval {timeInMs, number, milliseconds} ms"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "Zkraťte doby odezvy serverů (TTFB)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "<PERSON><PERSON> o<PERSON>u j<PERSON>u k<PERSON> (TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "Trvání"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "Čas zahájení"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/user-timings.js | description": {"message": "Zkuste v aplikaci pomocí rozhraní User Timing API implementovat měření reálného výkonu při událostech zásadních pro uživatelský dojem. [Další informace](https://web.dev/user-timings)"}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 časování uživatelů}few{# časování uživatelů}many{# časování uživatelů}other{# časování uživatelů}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "Hodnoty časování uživatelů"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "<PERSON><PERSON> na<PERSON> prvek <link> k předběžnému připojení ke zdroji {securityOrigin}, ale prohlížeč jej nepo<PERSON>. Zkontrolujte, zda správně používáte atribut `crossorigin`."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "Zvažte přidání sign<PERSON>lů `preconnect` nebo `dns-prefetch`, aby bylo možné včas se připojit k důležitým zdrojům třetích stran. [Další informace](https://web.dev/uses-rel-preconnect)"}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "K potřebným zdrojům se připojujte předem"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "<PERSON>l na<PERSON>zen prvek <link> k předběžnému načtení adresy {preloadURL}, ale prohlížeč jej nepouž<PERSON>. Zkontrolujte, zda správně používáte atribut `crossorigin`."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "Zvažte p<PERSON>žití <PERSON> `<link rel=preload>` k prioritnímu načtení zdroj<PERSON>, o které se nyní žádá později během načítání stránky. [Další informace](https://web.dev/uses-rel-preload)"}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "Klíčové požadavky načítejte předběžně"}, "lighthouse-core/audits/viewport.js | description": {"message": "Chcete-li aplikaci optimalizovat pro obrazovky mobilních zařízení, přidejte značku `<meta name=\"viewport\">`. [Dal<PERSON><PERSON> informace](https://web.dev/viewport)"}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "Nebyla nalezena značka `<meta name=\"viewport\">`"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "Neobsahuje značku `<meta name=\"viewport\">` s atributem `width` nebo `initial-scale`"}, "lighthouse-core/audits/viewport.js | title": {"message": "O<PERSON><PERSON><PERSON> z<PERSON>č<PERSON> `<meta name=\"viewport\">` s atributem `width` nebo `initial-scale`"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "Vaše aplikace by m<PERSON><PERSON> zobrazovat nějaký obsah, i v případě, že bude JavaScript zakázaný, i kdyby to mělo být jen upozornění pro uživatele, že k použití aplikace je potřeba JavaScript. [Další informace](https://web.dev/without-javascript)"}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "Pokud nejsou k dispozici skripty stránky, v hlavní části stránky by se měl v<PERSON>lit nějaký obsah."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "Neposkytuje záložní o<PERSON>, k<PERSON>ž není k dispozici JavaScript"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "Obsahuje urč<PERSON> o<PERSON>, k<PERSON>ž není k dispozici JavaScript"}, "lighthouse-core/audits/works-offline.js | description": {"message": "Pokud vytváříte progresivní webovou aplikaci, do<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> použít soubor service worker, aby aplikace fungovala i offline. [Další informace](https://web.dev/works-offline)"}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "Stávající stránka nereaguje stavovým kódem 200, k<PERSON><PERSON> je offline"}, "lighthouse-core/audits/works-offline.js | title": {"message": "Stávající stránka reaguje stavovým kódem 200, k<PERSON><PERSON> je offline"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "Stránka se v režimu offline nemusí načíst, protože testovací adresa URL ({requested}) byla přesměrována na adresu „{final}“. Zkuste druhou adresu otestovat přímo."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "Toto jsou příležitosti ke zlepšení používání specifikace ARIA ve vaší aplikaci, kter<PERSON> mohou zlepšit prostředí pro uživatele asistenčních technologií, jako jsou čtečky obrazovek."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Toto jsou příležitosti k poskytnutí alternativního obsahu pro zvuky a videa. <PERSON><PERSON> zlepšit prostředí pro sluchově nebo zrakově postižené uživatele."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Zvuk a video"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Tyto položky upozorňují na běžné doporučené postupy v oblasti přístupnosti."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Rady a tipy pro odpovídání na recenze"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "Tyto kontroly odhalují příležitosti ke [zlepšení přístupnosti webové aplikace](https://developers.google.com/web/fundamentals/accessibility). Automaticky lze odhalit jen část problémů s přístupností, proto obsah doporučujeme otestovat i ručně."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "Tyto položky se týkají oblastí, které v současné době automatické testování nedokáže pokrýt. Další informace najdete v průvodci [provedením kontroly přístupnosti](https://developers.google.com/web/fundamentals/accessibility/how-to-review)."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "Přístupnost"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Toto jsou příležitosti ke zlepšení čitelnosti obsahu."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Toto jsou příležitosti ke zlepšení interpretace vašeho obsahu u uživatelů různých jazyků."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internacionalizace a lokalizace"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Toto jsou příležitosti ke zlepšení sémantiky ovládacích prvků v aplikaci. Mohou zlepšit prostředí pro uživatele asistenčních technologií, jako jsou například čtečky obrazovek."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Názvy a štítky"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Toto jsou příležitosti ke zlepšení navigace pomocí klávesnice v aplikaci."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navigace"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Toto jsou příležitosti ke zlepšení dojmu při čtení tabulek nebo seznamů pomocí asistenčních technologií, jako je čtečka obrazovky."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tabulky a seznamy"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Doporuč<PERSON><PERSON>upy"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "Výkonové rozpočty nastavují standard pro výkon vašeho webu."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "Další informace o výkonu vaší aplikace. Tyto hodnoty nemají [přímý vliv](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) na skóre výkonu."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostika"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Nejkritičtějším aspektem výkonu je rychlost vykreslení pixelů na obrazovce. Klíčové metriky: První v<PERSON> o<PERSON>, První s<PERSON> v<PERSON>reslení"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Vylepšení prvního vykreslení"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "<PERSON><PERSON> návr<PERSON> vám mohou pomoci zrychlit načí<PERSON><PERSON>í s<PERSON>. Na skóre výkonu nemají [přímý vliv](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Příležitosti"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Vylepšete celkové chování při načítání, aby byla stránka co nejdříve responzivní a připravena k používání. Klíčové metriky: Doba dosažení interaktivity, Index rychlosti"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "<PERSON><PERSON>ová vylepšení"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "<PERSON>ý<PERSON>"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "Tyto kontroly vyhodnocují aspekty progresivní webové aplikace. [Další informace](https://developers.google.com/web/progressive-web-apps/checklist)"}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "Tyto kontroly vyžaduje základní [kontrolní seznam aplikace PWA](https://developers.google.com/web/progressive-web-apps/checklist), ale nástroj Lighthouse je automaticky neprovádí. Na vaše skóre vliv nemají, ale je dů<PERSON>ž<PERSON>, abyste je ově<PERSON><PERSON> ručně."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "Progresivní webová aplikace"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "Rychlé a spolehlivé"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Instalovatelné"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Optimalizováno pro PWA"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "<PERSON>to kontroly <PERSON>, aby va<PERSON>e stránka byla optimalizovaná pro hodnocení výsledků ve vyhledávačích. Na hodnocení ve vyhledávání mohou mít vliv i dal<PERSON> faktory, kter<PERSON> nástroj Lighthouse nekontroluje. [Další informace](https://support.google.com/webmasters/answer/35769)"}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "Chcete-li zkontrolovat dodržování dalších doporučených postupů pro SEO, spusťte pro svůj web tyto další validátory."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "Naformátujte soubor HTML způsobem, k<PERSON><PERSON>ž<PERSON>í <PERSON>ávačům lépe porozumět obsahu vaší aplikace."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "Doporučené postupy pro obsah"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Aby se vaše aplikace mohla zobrazovat ve výsledcích vyhledávání, prohledávače k ní musí mít přístup."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Procházení a indexování"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, aby str<PERSON>ky byly optimal<PERSON> pro mobily a aby uživatelé nemuseli obsah stránky zvětšovat. [Další informace](https://developers.google.com/search/mobile-sites/)"}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "Optimalizováno pro mobily"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Hodnota TTL (Time to Live) mezipaměti"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "Požadavky"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "Typ zdroje"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "Velikost"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Strávený čas"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "Velikost přenosu"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Možná úspora"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "Možná úspora"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Lze uspořit {wastedBytes, number, bytes} kB"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Lze uspořit {wastedMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokument"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "Písmo"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "Obrázek"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "Média"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "S<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Šablona stylů"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Třetí strana"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "Při pořizování záznamu trasování během načítání strán<PERSON> se něco pokazilo. Spusťte nástroj Lighthouse znovu. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "Při čekání na počáteční připojení pomocí ladicího protokolu vypršel časový limit."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome při načítání stránky nezískal žádné snímky obrazovky. Zkontrolujte, zda je na stránce vidět nějak<PERSON> obsah, a poté nástroj Lighthouse zkuste spustit znovu. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "Servery DNS zadanou doménu nedokázaly přeložit."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "<PERSON><PERSON><PERSON>í povinného zdroje {artifactName} do<PERSON><PERSON> k <PERSON>b<PERSON>: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "Došlo k interní chybě Chromu. Restartujte Chrome a zkuste nástroj Lighthouse spustit znovu."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "Požadovaný shromažďovací nástroj zdroje {artifactName} se nespustil."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "Nástroji Lighthouse se požadovanou stránku nepodařilo spolehlivě načíst. Zkontrolujte, zda testujete správnou adresu URL a zda server správně odpovídá na všechny požadavky."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "Nástroj Lighthouse adresu URL nemohl spolehlivě načíst, protože stránka přestala reagovat."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ne<PERSON><PERSON> platný bezpečnostní certifikát. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome zabránil načtení stránky a zobrazil vsunutou obrazovku. Zkontrolujte, zda testujete správnou adresu URL a zda server správně odpovídá na všechny požadavky."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Nástroji Lighthouse se požadovanou stránku nepodařilo spolehlivě načíst. Zkontrolujte, zda testujete správnou adresu URL a zda server správně odpovídá na všechny požadavky. (Podrobnosti: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Nástroji Lighthouse se požadovanou stránku nepodařilo spolehlivě načíst. Zkontrolujte, zda testujete správnou adresu URL a zda server správně odpovídá na všechny požadavky. (Stavový kód: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Načtení stránky trvalo příliš dlouho. Podle návrhů v přehledu zkraťte dobu načítání stránky a poté nástroj Lighthouse zkuste spustit znovu. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "Při čekání na odpověď protokolu DevTools byla překročena přidělená doba. (Metoda: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "Při načítání obsahu zdroje byla překročena přidělená doba"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> j<PERSON>, se zd<PERSON> b<PERSON>t <PERSON>."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "Zobrazit audity"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "Počáteční navigace"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "Maximální latence kritické trasy:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "Ch<PERSON>ba!"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "Chyba přehledu: žádné informace o auditu"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "Laboratorní data"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "Analýza aktuální stránky pomocí nástroje [Lighthouse](https://developers.google.com/web/tools/lighthouse/) v emulované mobilní síti. Hodnoty jsou odhady a mohou se lišit."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "Další položky k ruční kontrole"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "Není <PERSON>"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "Př<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "Odhadovaná úspora"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "Úspěšné audity"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "Sbalit úryvek"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "Rozbalit úryvek"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "Zobrazit zdroje třetích stran"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "<PERSON><PERSON><PERSON> to<PERSON><PERSON> s<PERSON>tění nástroje Lighthouse se vyskytly problémy:"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "<PERSON><PERSON><PERSON><PERSON> jsou odhady a mohou se lišit. Skóre výkonu [vych<PERSON><PERSON><PERSON> pouze z těchto metrik](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "Úspěšné audity s upozorněními"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "Upozornění: "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "Zvažte nahrání souboru GIF do služby, pomocí které ho bude možné vložit jako video HTML5."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "Nainstalujte [plugin služby WordPress pro líné načítání](https://wordpress.org/plugins/search/lazy+load/), který umožňuje odložit načítání obrázků mimo obrazovku, nebo přejděte na motiv, který tuto funkci poskytuje. Zvažte také použití [pluginu AMP](https://wordpress.org/plugins/amp/)."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "K dispozici je celá řada pluginů služby WordPress, které vám pomohou [vložit kritické podklady přímo do kódu](https://wordpress.org/plugins/search/critical+css/) nebo [odložit načítání méně důležitých zdrojů](https://wordpress.org/plugins/search/defer+css+javascript/). Upozorňujeme, že optimalizace pomocí těchto pluginů může narušit funkčnost vašeho motivu nebo pluginů. V kódu proto pravděpodobně budete muset provést změny."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "Na reakční dobu serveru mají vliv motivy, pluginy a specifikace serverů. Zvažte vyhledání optimalizovaného motivu, pečlivý výběr optimalizačního pluginu či upgradování serveru."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "Zvažte zobrazování ukázek v seznamech příspěvků (např. prostřednictvím značky k načtení dalšího obsahu), snížení počtu příspěvků zobrazených na jedné str<PERSON>, rozdělení dlouhých příspěvků na několik stránek nebo použití pluginu k línému načítání komentářů."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "K dispozici je celá řada [pluginů služby WordPress](https://wordpress.org/plugins/search/minify+css/), které web mohou zrychlit zřetězením, minifikací a komprimací stylů. Pokud je to možn<PERSON>, můžete minifikaci provést také předem pomocí procesu sestavení."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "K dispozici je celá řada [pluginů služby WordPress](https://wordpress.org/plugins/search/minify+javascript/), kter<PERSON> v<PERSON>š web mohou zrychlit zřetězením, minifikací a zkomprimováním skriptů. Pokud je to možn<PERSON>, můžete minifikaci provést také předem pomocí procesu sestavení."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "Zvažte snížení počtu [pluginů služby WordPress](https://wordpress.org/plugins/), které na stránce načítají nevyužité styly CSS. Pluginy, které přidávají nadbytečné styly CSS, můžete vyhledat pomocí funkce [Coverage](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) v nástrojích pro vývojáře v Chromu. Odpovědný motiv či plugin můžete identifikovat podle adresy URL šablony stylů. Hledejte pluginy, které v seznamu mají mnoho šablon stylů, u nichž je při použití funkce Coverage velké množství kódu označeno červeně. Plugin by měl šablonu stylů do fronty zařadit jen v případě, že je na stránce opravdu použita."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "Zvažte snížení počtu [pluginů služby WordPress](https://wordpress.org/plugins/), které na stránce načítají nepoužívaný kód JavaScript. Pluginy, které přidávají nadbytečný javascriptový kód, můžete vyhledat pomocí funkce [Coverage](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) v nástrojích pro vývojáře v Chromu. Odpovědný motiv či plugin poznáte podle adresy URL skriptu. Hledejte pluginy, které v seznamu mají mnoho skriptů, u nichž je při použití funkce Coverage velké množství kódu označeno červeně. Plugin by měl skript do fronty zařadit jen v případě, že se na stránce opravdu používá."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "Přečtěte si o [ukládání do mezipaměti prohlížeče ve službě WordPress](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "Zvažte použití [pluginu služby WordPress na optimalizaci obrázků](https://wordpress.org/plugins/search/optimize+images/), který obrázky komprimuje, p<PERSON><PERSON><PERSON><PERSON><PERSON> zach<PERSON> jejich kvalitu."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "Nahrajte obrázky přímo prostřednictvím [medi<PERSON>ln<PERSON> knihovny](https://codex.wordpress.org/Media_Library_Screen), abyste zajistili dostupnost potřebných velikostí obrázků, a poté je vložte z mediální knihovny, případně použití optimálních velikostí obrázků zajistěte pomocí widgetu pro obrázky (včetně obrázků pro responzivní dělicí body). Obrázky v této velikosti: `Full Size` používejte pouze v případě, že jsou jejich rozměry adekvátní k použití. [Další informace](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)"}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "Můžete v konfiguraci webového serveru zapnout kompresi textu."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "Zvažte použití [pluginu](https://wordpress.org/plugins/search/convert+webp/) nebo s<PERSON>, která nahrané obrázky automaticky převede na optimální formáty."}}