{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "Âćĉéŝś k̂éŷś l̂ét̂ úŝér̂ś q̂úîćk̂ĺŷ f́ôćûś â ṕâŕt̂ óf̂ t́ĥé p̂áĝé. F̂ór̂ ṕr̂óp̂ér̂ ńâv́îǵât́îón̂, éâćĥ áĉćêśŝ ḱêý m̂úŝt́ b̂é ûńîq́ûé. [L̂éâŕn̂ ḿôŕê](https://web.dev/accesskeys/)."}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "`[accesskey]` v̂ál̂úêś âŕê ńôt́ ûńîq́ûé"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]` v̂ál̂úêś âŕê ún̂íq̂úê"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Êáĉh́ ÂŔÎÁ `role` ŝúp̂ṕôŕt̂ś â śp̂éĉíf̂íĉ śûb́ŝét̂ óf̂ `aria-*` át̂t́r̂íb̂út̂éŝ. Ḿîśm̂át̂ćĥín̂ǵ t̂h́êśê ín̂v́âĺîd́ât́êś t̂h́ê `aria-*` át̂t́r̂íb̂út̂éŝ. [Ĺêár̂ń m̂ór̂é](https://web.dev/aria-allowed-attr/)."}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "`[aria-*]` ât́t̂ŕîb́ût́êś d̂ó n̂ót̂ ḿât́ĉh́ t̂h́êír̂ ŕôĺêś"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "`[aria-*]` ât́t̂ŕîb́ût́êś m̂át̂ćĥ t́ĥéîŕ r̂ól̂éŝ"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "Ŝóm̂é ÂŔÎÁ r̂ól̂éŝ h́âv́ê ŕêq́ûír̂éd̂ át̂t́r̂íb̂út̂éŝ t́ĥát̂ d́êśĉŕîb́ê t́ĥé ŝt́ât́ê óf̂ t́ĥé êĺêḿêńt̂ t́ô śĉŕêén̂ ŕêád̂ér̂ś. [L̂éâŕn̂ ḿôŕê](https://web.dev/aria-required-attr/)."}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]`ŝ d́ô ńôt́ ĥáv̂é âĺl̂ ŕêq́ûír̂éd̂ `[aria-*]` át̂t́r̂íb̂út̂éŝ"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]`ŝ h́âv́ê ál̂ĺ r̂éq̂úîŕêd́ `[aria-*]` ât́t̂ŕîb́ût́êś"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "Ŝóm̂é ÂŔÎÁ p̂ár̂én̂t́ r̂ól̂éŝ ḿûśt̂ ćôńt̂áîń ŝṕêćîf́îć ĉh́îĺd̂ ŕôĺêś t̂ó p̂ér̂f́ôŕm̂ t́ĥéîŕ îńt̂én̂d́êd́ âćĉéŝśîb́îĺît́ŷ f́ûńĉt́îón̂ś. [L̂éâŕn̂ ḿôŕê](https://web.dev/aria-required-children/)."}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Êĺêḿêńt̂ś ŵít̂h́ âń ÂŔÎÁ `[role]` t̂h́ât́ r̂éq̂úîŕê ćĥíl̂d́r̂én̂ t́ô ćôńt̂áîń â śp̂éĉíf̂íĉ `[role]` ár̂é m̂íŝśîńĝ śôḿê ór̂ ál̂ĺ ôf́ t̂h́ôśê ŕêq́ûír̂éd̂ ćĥíl̂d́r̂én̂."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "Êĺêḿêńt̂ś ŵít̂h́ âń ÂŔÎÁ `[role]` t̂h́ât́ r̂éq̂úîŕê ćĥíl̂d́r̂én̂ t́ô ćôńt̂áîń â śp̂éĉíf̂íĉ `[role]` h́âv́ê ál̂ĺ r̂éq̂úîŕêd́ ĉh́îĺd̂ŕêń."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "Ŝóm̂é ÂŔÎÁ ĉh́îĺd̂ ŕôĺêś m̂úŝt́ b̂é ĉón̂t́âín̂éd̂ b́ŷ śp̂éĉíf̂íĉ ṕâŕêńt̂ ŕôĺêś t̂ó p̂ŕôṕêŕl̂ý p̂ér̂f́ôŕm̂ t́ĥéîŕ îńt̂én̂d́êd́ âćĉéŝśîb́îĺît́ŷ f́ûńĉt́îón̂ś. [L̂éâŕn̂ ḿôŕê](https://web.dev/aria-required-parent/)."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]`ŝ ár̂é n̂ót̂ ćôńt̂áîńêd́ b̂ý t̂h́êír̂ ŕêq́ûír̂éd̂ ṕâŕêńt̂ él̂ém̂én̂t́"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]`ŝ ár̂é ĉón̂t́âín̂éd̂ b́ŷ t́ĥéîŕ r̂éq̂úîŕêd́ p̂ár̂én̂t́ êĺêḿêńt̂"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "ÂŔÎÁ r̂ól̂éŝ ḿûśt̂ h́âv́ê v́âĺîd́ v̂ál̂úêś îń ôŕd̂ér̂ t́ô ṕêŕf̂ór̂ḿ t̂h́êír̂ ín̂t́êńd̂éd̂ áĉćêśŝíb̂íl̂ít̂ý f̂ún̂ćt̂íôńŝ. [Ĺêár̂ń m̂ór̂é](https://web.dev/aria-roles/)."}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]` v̂ál̂úêś âŕê ńôt́ v̂ál̂íd̂"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "`[role]` v̂ál̂úêś âŕê v́âĺîd́"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Âśŝíŝt́îv́ê t́êćĥńôĺôǵîéŝ, ĺîḱê śĉŕêén̂ ŕêád̂ér̂ś, ĉán̂'t́ îńt̂ér̂ṕr̂ét̂ ÁR̂ÍÂ át̂t́r̂íb̂út̂éŝ ẃît́ĥ ín̂v́âĺîd́ v̂ál̂úêś. [L̂éâŕn̂ ḿôŕê](https://web.dev/aria-valid-attr-value/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "`[aria-*]` ât́t̂ŕîb́ût́êś d̂ó n̂ót̂ h́âv́ê v́âĺîd́ v̂ál̂úêś"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "`[aria-*]` ât́t̂ŕîb́ût́êś ĥáv̂é v̂ál̂íd̂ v́âĺûéŝ"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "Âśŝíŝt́îv́ê t́êćĥńôĺôǵîéŝ, ĺîḱê śĉŕêén̂ ŕêád̂ér̂ś, ĉán̂'t́ îńt̂ér̂ṕr̂ét̂ ÁR̂ÍÂ át̂t́r̂íb̂út̂éŝ ẃît́ĥ ín̂v́âĺîd́ n̂ám̂éŝ. [Ĺêár̂ń m̂ór̂é](https://web.dev/aria-valid-attr/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "`[aria-*]` ât́t̂ŕîb́ût́êś âŕê ńôt́ v̂ál̂íd̂ ór̂ ḿîśŝṕêĺl̂éd̂"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "`[aria-*]` ât́t̂ŕîb́ût́êś âŕê v́âĺîd́ âńd̂ ńôt́ m̂íŝśp̂él̂ĺêd́"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "Ĉáp̂t́îón̂ś m̂ák̂é âúd̂íô él̂ém̂én̂t́ŝ úŝáb̂ĺê f́ôŕ d̂éâf́ ôŕ ĥéâŕîńĝ-ím̂ṕâír̂éd̂ úŝér̂ś, p̂ŕôv́îd́îńĝ ćr̂ít̂íĉál̂ ín̂f́ôŕm̂át̂íôń ŝúĉh́ âś ŵh́ô íŝ t́âĺk̂ín̂ǵ, ŵh́ât́ t̂h́êý'r̂é ŝáŷín̂ǵ, âńd̂ ót̂h́êŕ n̂ón̂-śp̂éêćĥ ín̂f́ôŕm̂át̂íôń. [L̂éâŕn̂ ḿôŕê](https://web.dev/audio-caption/)."}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "`<audio>` êĺêḿêńt̂ś âŕê ḿîśŝín̂ǵ â `<track>` él̂ém̂én̂t́ ŵít̂h́ `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "`<audio>` êĺêḿêńt̂ś ĉón̂t́âín̂ á `<track>` êĺêḿêńt̂ ẃît́ĥ `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "F̂áîĺîńĝ Él̂ém̂én̂t́ŝ"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "Ŵh́êń â b́ût́t̂ón̂ d́ôéŝń't̂ h́âv́ê án̂ áĉćêśŝíb̂ĺê ńâḿê, śĉŕêén̂ ŕêád̂ér̂ś âńn̂óûńĉé ît́ âś \"b̂út̂t́ôń\", m̂ák̂ín̂ǵ ît́ ûńûśâb́l̂é f̂ór̂ úŝér̂ś ŵh́ô ŕêĺŷ ón̂ śĉŕêén̂ ŕêád̂ér̂ś. [L̂éâŕn̂ ḿôŕê](https://web.dev/button-name/)."}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "B̂út̂t́ôńŝ d́ô ńôt́ ĥáv̂é âń âćĉéŝśîb́l̂é n̂ám̂é"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "B̂út̂t́ôńŝ h́âv́ê án̂ áĉćêśŝíb̂ĺê ńâḿê"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "Âd́d̂ín̂ǵ ŵáŷś t̂ó b̂ýp̂áŝś r̂ép̂ét̂ít̂ív̂é ĉón̂t́êńt̂ ĺêt́ŝ ḱêýb̂óâŕd̂ úŝér̂ś n̂áv̂íĝát̂é t̂h́ê ṕâǵê ḿôŕê éf̂f́îćîén̂t́l̂ý. [L̂éâŕn̂ ḿôŕê](https://web.dev/bypass/)."}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "T̂h́ê ṕâǵê d́ôéŝ ńôt́ ĉón̂t́âín̂ á ĥéâd́îńĝ, śk̂íp̂ ĺîńk̂, ór̂ ĺâńd̂ḿâŕk̂ ŕêǵîón̂"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "T̂h́ê ṕâǵê ćôńt̂áîńŝ á ĥéâd́îńĝ, śk̂íp̂ ĺîńk̂, ór̂ ĺâńd̂ḿâŕk̂ ŕêǵîón̂"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "L̂óŵ-ćôńt̂ŕâśt̂ t́êx́t̂ íŝ d́îf́f̂íĉúl̂t́ ôŕ îḿp̂óŝśîb́l̂é f̂ór̂ ḿâńŷ úŝér̂ś t̂ó r̂éâd́. [L̂éâŕn̂ ḿôŕê](https://web.dev/color-contrast/)."}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "B̂áĉḱĝŕôún̂d́ âńd̂ f́ôŕêǵr̂óûńd̂ ćôĺôŕŝ d́ô ńôt́ ĥáv̂é â śûf́f̂íĉíêńt̂ ćôńt̂ŕâśt̂ ŕât́îó."}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "B̂áĉḱĝŕôún̂d́ âńd̂ f́ôŕêǵr̂óûńd̂ ćôĺôŕŝ h́âv́ê á ŝúf̂f́îćîén̂t́ ĉón̂t́r̂áŝt́ r̂át̂íô"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "Ŵh́êń d̂éf̂ín̂ít̂íôń l̂íŝt́ŝ ár̂é n̂ót̂ ṕr̂óp̂ér̂ĺŷ ḿâŕk̂éd̂ úp̂, śĉŕêén̂ ŕêád̂ér̂ś m̂áŷ ṕr̂ód̂úĉé ĉón̂f́ûśîńĝ ór̂ ín̂áĉćûŕât́ê óût́p̂út̂. [Ĺêár̂ń m̂ór̂é](https://web.dev/definition-list/)."}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>`'ŝ d́ô ńôt́ ĉón̂t́âín̂ ón̂ĺŷ ṕr̂óp̂ér̂ĺŷ-ór̂d́êŕêd́ `<dt>` âńd̂ `<dd>` ǵr̂óûṕŝ, `<script>` ór̂ `<template>` él̂ém̂én̂t́ŝ."}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "`<dl>`'ŝ ćôńt̂áîń ôńl̂ý p̂ŕôṕêŕl̂ý-ôŕd̂ér̂éd̂ `<dt>` án̂d́ `<dd>` ĝŕôúp̂ś, `<script>` ôŕ `<template>` êĺêḿêńt̂ś."}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "D̂éf̂ín̂ít̂íôń l̂íŝt́ ît́êḿŝ (`<dt>` án̂d́ `<dd>`) m̂úŝt́ b̂é ŵŕâṕp̂éd̂ ín̂ á p̂ár̂én̂t́ `<dl>` êĺêḿêńt̂ t́ô én̂śûŕê t́ĥát̂ śĉŕêén̂ ŕêád̂ér̂ś ĉán̂ ṕr̂óp̂ér̂ĺŷ án̂ńôún̂ćê t́ĥém̂. [Ĺêár̂ń m̂ór̂é](https://web.dev/dlitem/)."}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "D̂éf̂ín̂ít̂íôń l̂íŝt́ ît́êḿŝ ár̂é n̂ót̂ ẃr̂áp̂ṕêd́ îń `<dl>` êĺêḿêńt̂ś"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "D̂éf̂ín̂ít̂íôń l̂íŝt́ ît́êḿŝ ár̂é ŵŕâṕp̂éd̂ ín̂ `<dl>` él̂ém̂én̂t́ŝ"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "T̂h́ê t́ît́l̂é ĝív̂éŝ śĉŕêén̂ ŕêád̂ér̂ úŝér̂ś âń ôv́êŕv̂íêẃ ôf́ t̂h́ê ṕâǵê, án̂d́ ŝéâŕĉh́ êńĝín̂é ûśêŕŝ ŕêĺŷ ón̂ ít̂ h́êáv̂íl̂ý t̂ó d̂ét̂ér̂ḿîńê íf̂ á p̂áĝé îś r̂él̂év̂án̂t́ t̂ó t̂h́êír̂ śêár̂ćĥ. [Ĺêár̂ń m̂ór̂é](https://web.dev/document-title/)."}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "D̂óĉúm̂én̂t́ d̂óêśn̂'t́ ĥáv̂é â `<title>` él̂ém̂én̂t́"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "D̂óĉúm̂én̂t́ ĥáŝ á `<title>` êĺêḿêńt̂"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "T̂h́ê v́âĺûé ôf́ âń îd́ ât́t̂ŕîb́ût́ê ḿûśt̂ b́ê ún̂íq̂úê t́ô ṕr̂év̂én̂t́ ôt́ĥér̂ ín̂śt̂án̂ćêś f̂ŕôḿ b̂éîńĝ óv̂ér̂ĺôók̂éd̂ b́ŷ áŝśîśt̂ív̂é t̂éĉh́n̂ól̂óĝíêś. [L̂éâŕn̂ ḿôŕê](https://web.dev/duplicate-id/)."}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "`[id]` ât́t̂ŕîb́ût́êś ôń t̂h́ê ṕâǵê ár̂é n̂ót̂ ún̂íq̂úê"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "`[id]` ât́t̂ŕîb́ût́êś ôń t̂h́ê ṕâǵê ár̂é ûńîq́ûé"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "Ŝćr̂éêń r̂éâd́êŕ ûśêŕŝ ŕêĺŷ ón̂ f́r̂ám̂é t̂ít̂ĺêś t̂ó d̂éŝćr̂íb̂é t̂h́ê ćôńt̂én̂t́ŝ óf̂ f́r̂ám̂éŝ. [Ĺêár̂ń m̂ór̂é](https://web.dev/frame-title/)."}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "`<frame>` ôŕ `<iframe>` êĺêḿêńt̂ś d̂ó n̂ót̂ h́âv́ê á t̂ít̂ĺê"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "`<frame>` ôŕ `<iframe>` êĺêḿêńt̂ś ĥáv̂é â t́ît́l̂é"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "Îf́ â ṕâǵê d́ôéŝń't̂ śp̂éĉíf̂ý â ĺâńĝ át̂t́r̂íb̂út̂é, â śĉŕêén̂ ŕêád̂ér̂ áŝśûḿêś t̂h́ât́ t̂h́ê ṕâǵê íŝ ín̂ t́ĥé d̂éf̂áûĺt̂ ĺâńĝúâǵê t́ĥát̂ t́ĥé ûśêŕ ĉh́ôśê ẃĥén̂ śêt́t̂ín̂ǵ ûṕ t̂h́ê śĉŕêén̂ ŕêád̂ér̂. Íf̂ t́ĥé p̂áĝé îśn̂'t́ âćt̂úâĺl̂ý îń t̂h́ê d́êf́âúl̂t́ l̂án̂ǵûáĝé, t̂h́êń t̂h́ê śĉŕêén̂ ŕêád̂ér̂ ḿîǵĥt́ n̂ót̂ án̂ńôún̂ćê t́ĥé p̂áĝé'ŝ t́êx́t̂ ćôŕr̂éĉt́l̂ý. [L̂éâŕn̂ ḿôŕê](https://web.dev/html-has-lang/)."}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>` êĺêḿêńt̂ d́ôéŝ ńôt́ ĥáv̂é â `[lang]` át̂t́r̂íb̂út̂é"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>` êĺêḿêńt̂ h́âś â `[lang]` át̂t́r̂íb̂út̂é"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "Ŝṕêćîf́ŷín̂ǵ â v́âĺîd́ [B̂ĆP̂ 47 ĺâńĝúâǵê](https://www.w3.org/International/questions/qa-choosing-language-tags#question) h́êĺp̂ś ŝćr̂éêń r̂éâd́êŕŝ án̂ńôún̂ćê t́êx́t̂ ṕr̂óp̂ér̂ĺŷ. [Ĺêár̂ń m̂ór̂é](https://web.dev/html-lang-valid/)."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>` êĺêḿêńt̂ d́ôéŝ ńôt́ ĥáv̂é â v́âĺîd́ v̂ál̂úê f́ôŕ ît́ŝ `[lang]` át̂t́r̂íb̂út̂é."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "`<html>` êĺêḿêńt̂ h́âś â v́âĺîd́ v̂ál̂úê f́ôŕ ît́ŝ `[lang]` át̂t́r̂íb̂út̂é"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "Îńf̂ór̂ḿât́îv́ê él̂ém̂én̂t́ŝ śĥóûĺd̂ áîḿ f̂ór̂ śĥór̂t́, d̂éŝćr̂íp̂t́îv́ê ál̂t́êŕn̂át̂é t̂éx̂t́. D̂éĉór̂át̂ív̂é êĺêḿêńt̂ś ĉán̂ b́ê íĝńôŕêd́ ŵít̂h́ âń êḿp̂t́ŷ ál̂t́ ât́t̂ŕîb́ût́ê. [Ĺêár̂ń m̂ór̂é](https://web.dev/image-alt/)."}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "Îḿâǵê él̂ém̂én̂t́ŝ d́ô ńôt́ ĥáv̂é `[alt]` ât́t̂ŕîb́ût́êś"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "Îḿâǵê él̂ém̂én̂t́ŝ h́âv́ê `[alt]` át̂t́r̂íb̂út̂éŝ"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "Ŵh́êń âń îḿâǵê íŝ b́êín̂ǵ ûśêd́ âś âń `<input>` b̂út̂t́ôń, p̂ŕôv́îd́îńĝ ál̂t́êŕn̂át̂ív̂é t̂éx̂t́ ĉán̂ h́êĺp̂ śĉŕêén̂ ŕêád̂ér̂ úŝér̂ś ûńd̂ér̂śt̂án̂d́ t̂h́ê ṕûŕp̂óŝé ôf́ t̂h́ê b́ût́t̂ón̂. [Ĺêár̂ń m̂ór̂é](https://web.dev/input-image-alt/)."}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">` êĺêḿêńt̂ś d̂ó n̂ót̂ h́âv́ê `[alt]` t́êx́t̂"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">` êĺêḿêńt̂ś ĥáv̂é `[alt]` t̂éx̂t́"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "L̂áb̂él̂ś êńŝúr̂é t̂h́ât́ f̂ór̂ḿ ĉón̂t́r̂ól̂ś âŕê án̂ńôún̂ćêd́ p̂ŕôṕêŕl̂ý b̂ý âśŝíŝt́îv́ê t́êćĥńôĺôǵîéŝ, ĺîḱê śĉŕêén̂ ŕêád̂ér̂ś. [L̂éâŕn̂ ḿôŕê](https://web.dev/label/)."}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "F̂ór̂ḿ êĺêḿêńt̂ś d̂ó n̂ót̂ h́âv́ê áŝśôćîát̂éd̂ ĺâb́êĺŝ"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "F̂ór̂ḿ êĺêḿêńt̂ś ĥáv̂é âśŝóĉíât́êd́ l̂áb̂él̂ś"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "Â t́âb́l̂é b̂éîńĝ úŝéd̂ f́ôŕ l̂áŷóût́ p̂úr̂ṕôśêś ŝh́ôúl̂d́ n̂ót̂ ín̂ćl̂úd̂é d̂át̂á êĺêḿêńt̂ś, ŝúĉh́ âś t̂h́ê t́ĥ ór̂ ćâṕt̂íôń êĺêḿêńt̂ś ôŕ t̂h́ê śûḿm̂ár̂ý ât́t̂ŕîb́ût́ê, b́êćâúŝé t̂h́îś ĉán̂ ćr̂éât́ê á ĉón̂f́ûśîńĝ éx̂ṕêŕîén̂ćê f́ôŕ ŝćr̂éêń r̂éâd́êŕ ûśêŕŝ. [Ĺêár̂ń m̂ór̂é](https://web.dev/layout-table/)."}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "P̂ŕêśêńt̂át̂íôńâĺ `<table>` êĺêḿêńt̂ś d̂ó n̂ót̂ áv̂óîd́ ûśîńĝ `<th>`, `<caption>` ór̂ t́ĥé `[summary]` ât́t̂ŕîb́ût́ê."}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "P̂ŕêśêńt̂át̂íôńâĺ `<table>` êĺêḿêńt̂ś âv́ôíd̂ úŝín̂ǵ `<th>`, `<caption>` ôŕ t̂h́ê `[summary]` át̂t́r̂íb̂út̂é."}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "L̂ín̂ḱ t̂éx̂t́ (âńd̂ ál̂t́êŕn̂át̂é t̂éx̂t́ f̂ór̂ ím̂áĝéŝ, ẃĥén̂ úŝéd̂ áŝ ĺîńk̂ś) t̂h́ât́ îś d̂íŝćêŕn̂íb̂ĺê, ún̂íq̂úê, án̂d́ f̂óĉúŝáb̂ĺê ím̂ṕr̂óv̂éŝ t́ĥé n̂áv̂íĝát̂íôń êx́p̂ér̂íêńĉé f̂ór̂ śĉŕêén̂ ŕêád̂ér̂ úŝér̂ś. [L̂éâŕn̂ ḿôŕê](https://web.dev/link-name/)."}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "L̂ín̂ḱŝ d́ô ńôt́ ĥáv̂é â d́îśĉér̂ńîb́l̂é n̂ám̂é"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "L̂ín̂ḱŝ h́âv́ê á d̂íŝćêŕn̂íb̂ĺê ńâḿê"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "Ŝćr̂éêń r̂éâd́êŕŝ h́âv́ê á ŝṕêćîf́îć ŵáŷ óf̂ án̂ńôún̂ćîńĝ ĺîśt̂ś. Êńŝúr̂ín̂ǵ p̂ŕôṕêŕ l̂íŝt́ ŝt́r̂úĉt́ûŕê áîd́ŝ śĉŕêén̂ ŕêád̂ér̂ óût́p̂út̂. [Ĺêár̂ń m̂ór̂é](https://web.dev/list/)."}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "L̂íŝt́ŝ d́ô ńôt́ ĉón̂t́âín̂ ón̂ĺŷ `<li>` él̂ém̂én̂t́ŝ án̂d́ ŝćr̂íp̂t́ ŝúp̂ṕôŕt̂ín̂ǵ êĺêḿêńt̂ś (`<script>` âńd̂ `<template>`)."}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "L̂íŝt́ŝ ćôńt̂áîń ôńl̂ý `<li>` êĺêḿêńt̂ś âńd̂ śĉŕîṕt̂ śûṕp̂ór̂t́îńĝ él̂ém̂én̂t́ŝ (`<script>` án̂d́ `<template>`)."}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "Ŝćr̂éêń r̂éâd́êŕŝ ŕêq́ûír̂é l̂íŝt́ ît́êḿŝ (`<li>`) t́ô b́ê ćôńt̂áîńêd́ ŵít̂h́îń â ṕâŕêńt̂ `<ul>` ór̂ `<ol>` t́ô b́ê án̂ńôún̂ćêd́ p̂ŕôṕêŕl̂ý. [L̂éâŕn̂ ḿôŕê](https://web.dev/listitem/)."}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "L̂íŝt́ ît́êḿŝ (`<li>`) ár̂é n̂ót̂ ćôńt̂áîńêd́ ŵít̂h́îń `<ul>` ôŕ `<ol>` p̂ár̂én̂t́ êĺêḿêńt̂ś."}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "L̂íŝt́ ît́êḿŝ (`<li>`) ár̂é ĉón̂t́âín̂éd̂ ẃît́ĥín̂ `<ul>` ór̂ `<ol>` ṕâŕêńt̂ él̂ém̂én̂t́ŝ"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "Ûśêŕŝ d́ô ńôt́ êx́p̂éĉt́ â ṕâǵê t́ô ŕêf́r̂éŝh́ âút̂óm̂át̂íĉál̂ĺŷ, án̂d́ d̂óîńĝ śô ẃîĺl̂ ḿôv́ê f́ôćûś b̂áĉḱ t̂ó t̂h́ê t́ôṕ ôf́ t̂h́ê ṕâǵê. T́ĥíŝ ḿâý ĉŕêát̂é â f́r̂úŝt́r̂át̂ín̂ǵ ôŕ ĉón̂f́ûśîńĝ éx̂ṕêŕîén̂ćê. [Ĺêár̂ń m̂ór̂é](https://web.dev/meta-refresh/)."}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "T̂h́ê d́ôćûḿêńt̂ úŝéŝ `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "T̂h́ê d́ôćûḿêńt̂ d́ôéŝ ńôt́ ûśê `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "D̂íŝáb̂ĺîńĝ źôóm̂ín̂ǵ îś p̂ŕôb́l̂ém̂át̂íĉ f́ôŕ ûśêŕŝ ẃît́ĥ ĺôẃ v̂íŝíôń ŵh́ô ŕêĺŷ ón̂ śĉŕêén̂ ḿâǵn̂íf̂íĉát̂íôń t̂ó p̂ŕôṕêŕl̂ý ŝéê t́ĥé ĉón̂t́êńt̂ś ôf́ â ẃêb́ p̂áĝé. [L̂éâŕn̂ ḿôŕê](https://web.dev/meta-viewport/)."}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` îś ûśêd́ îń t̂h́ê `<meta name=\"viewport\">` él̂ém̂én̂t́ ôŕ t̂h́ê `[maximum-scale]` át̂t́r̂íb̂út̂é îś l̂éŝś t̂h́âń 5."}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` îś n̂ót̂ úŝéd̂ ín̂ t́ĥé `<meta name=\"viewport\">` êĺêḿêńt̂ án̂d́ t̂h́ê `[maximum-scale]` át̂t́r̂íb̂út̂é îś n̂ót̂ ĺêśŝ t́ĥán̂ 5."}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "Ŝćr̂éêń r̂éâd́êŕŝ ćâńn̂ót̂ t́r̂án̂śl̂át̂é n̂ón̂-t́êx́t̂ ćôńt̂én̂t́. Âd́d̂ín̂ǵ âĺt̂ t́êx́t̂ t́ô `<object>` él̂ém̂én̂t́ŝ h́êĺp̂ś ŝćr̂éêń r̂éâd́êŕŝ ćôńv̂éŷ ḿêán̂ín̂ǵ t̂ó ûśêŕŝ. [Ĺêár̂ń m̂ór̂é](https://web.dev/object-alt/)."}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>` êĺêḿêńt̂ś d̂ó n̂ót̂ h́âv́ê `[alt]` t́êx́t̂"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "`<object>` êĺêḿêńt̂ś ĥáv̂é `[alt]` t̂éx̂t́"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "Â v́âĺûé ĝŕêát̂ér̂ t́ĥán̂ 0 ím̂ṕl̂íêś âń êx́p̂ĺîćît́ n̂áv̂íĝát̂íôń ôŕd̂ér̂ín̂ǵ. Âĺt̂h́ôúĝh́ t̂éĉh́n̂íĉál̂ĺŷ v́âĺîd́, t̂h́îś ôf́t̂én̂ ćr̂éât́êś f̂ŕûśt̂ŕât́îńĝ éx̂ṕêŕîén̂ćêś f̂ór̂ úŝér̂ś ŵh́ô ŕêĺŷ ón̂ áŝśîśt̂ív̂é t̂éĉh́n̂ól̂óĝíêś. [L̂éâŕn̂ ḿôŕê](https://web.dev/tabindex/)."}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "Ŝóm̂é êĺêḿêńt̂ś ĥáv̂é â `[tabindex]` v́âĺûé ĝŕêát̂ér̂ t́ĥán̂ 0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "N̂ó êĺêḿêńt̂ h́âś â `[tabindex]` v́âĺûé ĝŕêát̂ér̂ t́ĥán̂ 0"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "Ŝćr̂éêń r̂éâd́êŕŝ h́âv́ê f́êát̂úr̂éŝ t́ô ḿâḱê ńâv́îǵât́îńĝ t́âb́l̂éŝ éâśîér̂. Én̂śûŕîńĝ `<td>` ćêĺl̂ś ûśîńĝ t́ĥé `[headers]` ât́t̂ŕîb́ût́ê ón̂ĺŷ ŕêf́êŕ t̂ó ôt́ĥér̂ ćêĺl̂ś îń t̂h́ê śâḿê t́âb́l̂é m̂áŷ ím̂ṕr̂óv̂é t̂h́ê éx̂ṕêŕîén̂ćê f́ôŕ ŝćr̂éêń r̂éâd́êŕ ûśêŕŝ. [Ĺêár̂ń m̂ór̂é](https://web.dev/td-headers-attr/)."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Ĉél̂ĺŝ ín̂ á `<table>` êĺêḿêńt̂ t́ĥát̂ úŝé t̂h́ê `[headers]` át̂t́r̂íb̂út̂é r̂éf̂ér̂ t́ô án̂ él̂ém̂én̂t́ `id` n̂ót̂ f́ôún̂d́ ŵít̂h́îń t̂h́ê śâḿê t́âb́l̂é."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "Ĉél̂ĺŝ ín̂ á `<table>` êĺêḿêńt̂ t́ĥát̂ úŝé t̂h́ê `[headers]` át̂t́r̂íb̂út̂é r̂éf̂ér̂ t́ô t́âb́l̂é ĉél̂ĺŝ ẃît́ĥín̂ t́ĥé ŝám̂é t̂áb̂ĺê."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "Ŝćr̂éêń r̂éâd́êŕŝ h́âv́ê f́êát̂úr̂éŝ t́ô ḿâḱê ńâv́îǵât́îńĝ t́âb́l̂éŝ éâśîér̂. Én̂śûŕîńĝ t́âb́l̂é ĥéâd́êŕŝ ál̂ẃâýŝ ŕêf́êŕ t̂ó ŝóm̂é ŝét̂ óf̂ ćêĺl̂ś m̂áŷ ím̂ṕr̂óv̂é t̂h́ê éx̂ṕêŕîén̂ćê f́ôŕ ŝćr̂éêń r̂éâd́êŕ ûśêŕŝ. [Ĺêár̂ń m̂ór̂é](https://web.dev/th-has-data-cells/)."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "`<th>` êĺêḿêńt̂ś âńd̂ él̂ém̂én̂t́ŝ ẃît́ĥ `[role=\"columnheader\"/\"rowheader\"]` d́ô ńôt́ ĥáv̂é d̂át̂á ĉél̂ĺŝ t́ĥéŷ d́êśĉŕîb́ê."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "`<th>` êĺêḿêńt̂ś âńd̂ él̂ém̂én̂t́ŝ ẃît́ĥ `[role=\"columnheader\"/\"rowheader\"]` h́âv́ê d́ât́â ćêĺl̂ś t̂h́êý d̂éŝćr̂íb̂é."}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "Ŝṕêćîf́ŷín̂ǵ â v́âĺîd́ [B̂ĆP̂ 47 ĺâńĝúâǵê](https://www.w3.org/International/questions/qa-choosing-language-tags#question) ón̂ él̂ém̂én̂t́ŝ h́êĺp̂ś êńŝúr̂é t̂h́ât́ t̂éx̂t́ îś p̂ŕôńôún̂ćêd́ ĉór̂ŕêćt̂ĺŷ b́ŷ á ŝćr̂éêń r̂éâd́êŕ. [L̂éâŕn̂ ḿôŕê](https://web.dev/valid-lang/)."}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "`[lang]` ât́t̂ŕîb́ût́êś d̂ó n̂ót̂ h́âv́ê á v̂ál̂íd̂ v́âĺûé"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "`[lang]` ât́t̂ŕîb́ût́êś ĥáv̂é â v́âĺîd́ v̂ál̂úê"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "Ŵh́êń â v́îd́êó p̂ŕôv́îd́êś â ćâṕt̂íôń ît́ îś êáŝíêŕ f̂ór̂ d́êáf̂ án̂d́ ĥéâŕîńĝ ím̂ṕâír̂éd̂ úŝér̂ś t̂ó âćĉéŝś ît́ŝ ín̂f́ôŕm̂át̂íôń. [L̂éâŕn̂ ḿôŕê](https://web.dev/video-caption/)."}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "`<video>` êĺêḿêńt̂ś d̂ó n̂ót̂ ćôńt̂áîń â `<track>` él̂ém̂én̂t́ ŵít̂h́ `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "`<video>` êĺêḿêńt̂ś ĉón̂t́âín̂ á `<track>` êĺêḿêńt̂ ẃît́ĥ `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "Âúd̂íô d́êśĉŕîṕt̂íôńŝ ṕr̂óv̂íd̂é r̂él̂év̂án̂t́ îńf̂ór̂ḿât́îón̂ f́ôŕ v̂íd̂éôś t̂h́ât́ d̂íâĺôǵûé ĉán̂ńôt́, ŝúĉh́ âś f̂áĉíâĺ êx́p̂ŕêśŝíôńŝ án̂d́ ŝćêńêś. [L̂éâŕn̂ ḿôŕê](https://web.dev/video-description/)."}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "`<video>` êĺêḿêńt̂ś d̂ó n̂ót̂ ćôńt̂áîń â `<track>` él̂ém̂én̂t́ ŵít̂h́ `[kind=\"description\"]`."}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "`<video>` êĺêḿêńt̂ś ĉón̂t́âín̂ á `<track>` êĺêḿêńt̂ ẃît́ĥ `[kind=\"description\"]`"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "F̂ór̂ íd̂éâĺ âṕp̂éâŕâńĉé ôń îÓŜ ẃĥén̂ úŝér̂ś âd́d̂ á p̂ŕôǵr̂éŝśîv́ê ẃêb́ âṕp̂ t́ô t́ĥé ĥóm̂é ŝćr̂éêń, d̂éf̂ín̂é âń `apple-touch-icon`. Ît́ m̂úŝt́ p̂óîńt̂ t́ô á n̂ón̂-t́r̂án̂śp̂ár̂én̂t́ 192p̂x́ (ôŕ 180p̂x́) ŝq́ûár̂é P̂ŃĜ. [Ĺêár̂ń M̂ór̂é](https://web.dev/apple-touch-icon/)."}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "D̂óêś n̂ót̂ ṕr̂óv̂íd̂é â v́âĺîd́ `apple-touch-icon`"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "`apple-touch-icon-precomposed` îś ôút̂ óf̂ d́ât́ê; `apple-touch-icon` íŝ ṕr̂éf̂ér̂ŕêd́."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "P̂ŕôv́îd́êś â v́âĺîd́ `apple-touch-icon`"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Ĉh́r̂óm̂é êx́t̂én̂śîón̂ś n̂éĝát̂ív̂él̂ý âf́f̂éĉt́êd́ t̂h́îś p̂áĝé'ŝ ĺôád̂ ṕêŕf̂ór̂ḿâńĉé. T̂ŕŷ áûd́ît́îńĝ t́ĥé p̂áĝé îń îńĉóĝńît́ô ḿôd́ê ór̂ f́r̂óm̂ á Ĉh́r̂óm̂é p̂ŕôf́îĺê ẃît́ĥóût́ êx́t̂én̂śîón̂ś."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "Ŝćr̂íp̂t́ Êv́âĺûát̂íôń"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "Ŝćr̂íp̂t́ P̂ár̂śê"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "T̂ót̂ál̂ ĆP̂Ú T̂ím̂é"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "Ĉón̂śîd́êŕ r̂éd̂úĉín̂ǵ t̂h́ê t́îḿê śp̂én̂t́ p̂ár̂śîńĝ, ćôḿp̂íl̂ín̂ǵ, âńd̂ éx̂éĉút̂ín̂ǵ ĴŚ. Ŷóû ḿâý f̂ín̂d́ d̂él̂ív̂ér̂ín̂ǵ ŝḿâĺl̂ér̂ J́Ŝ ṕâýl̂óâd́ŝ h́êĺp̂ś ŵít̂h́ t̂h́îś. [L̂éâŕn̂ ḿôŕê](https://web.dev/bootup-time)."}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "R̂éd̂úĉé Ĵáv̂áŜćr̂íp̂t́ êx́êćût́îón̂ t́îḿê"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "Ĵáv̂áŜćr̂íp̂t́ êx́êćût́îón̂ t́îḿê"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "L̂ár̂ǵê ǴÎF́ŝ ár̂é îńêf́f̂íĉíêńt̂ f́ôŕ d̂él̂ív̂ér̂ín̂ǵ âńîḿât́êd́ ĉón̂t́êńt̂. Ćôńŝíd̂ér̂ úŝín̂ǵ M̂ṔÊǴ4/Ŵéb̂Ḿ v̂íd̂éôś f̂ór̂ án̂ím̂át̂íôńŝ án̂d́ P̂ŃĜ/Ẃêb́P̂ f́ôŕ ŝt́ât́îć îḿâǵêś îńŝt́êád̂ óf̂ ǴÎF́ t̂ó ŝáv̂é n̂ét̂ẃôŕk̂ b́ŷt́êś. [L̂éâŕn̂ ḿôŕê](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Ûśê v́îd́êó f̂ór̂ḿât́ŝ f́ôŕ âńîḿât́êd́ ĉón̂t́êńt̂"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Ĉón̂śîd́êŕ l̂áẑý-l̂óâd́îńĝ óf̂f́ŝćr̂éêń âńd̂ h́îd́d̂én̂ ím̂áĝéŝ áf̂t́êŕ âĺl̂ ćr̂ít̂íĉál̂ ŕêśôúr̂ćêś ĥáv̂é f̂ín̂íŝh́êd́ l̂óâd́îńĝ t́ô ĺôẃêŕ t̂ím̂é t̂ó îńt̂ér̂áĉt́îv́ê. [Ĺêár̂ń m̂ór̂é](https://web.dev/offscreen-images)."}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "D̂éf̂ér̂ óf̂f́ŝćr̂éêń îḿâǵêś"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "R̂éŝóûŕĉéŝ ár̂é b̂ĺôćk̂ín̂ǵ t̂h́ê f́îŕŝt́ p̂áîńt̂ óf̂ ýôúr̂ ṕâǵê. Ćôńŝíd̂ér̂ d́êĺîv́êŕîńĝ ćr̂ít̂íĉál̂ J́Ŝ/ĆŜŚ îńl̂ín̂é âńd̂ d́êf́êŕr̂ín̂ǵ âĺl̂ ńôń-ĉŕît́îćâĺ ĴŚ/ŝt́ŷĺêś. [L̂éâŕn̂ ḿôŕê](https://web.dev/render-blocking-resources)."}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Êĺîḿîńât́ê ŕêńd̂ér̂-b́l̂óĉḱîńĝ ŕêśôúr̂ćêś"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "L̂ár̂ǵê ńêt́ŵór̂ḱ p̂áŷĺôád̂ś ĉóŝt́ ûśêŕŝ ŕêál̂ ḿôńêý âńd̂ ár̂é ĥíĝh́l̂ý ĉór̂ŕêĺât́êd́ ŵít̂h́ l̂ón̂ǵ l̂óâd́ t̂ím̂éŝ. [Ĺêár̂ń m̂ór̂é](https://web.dev/total-byte-weight)."}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "T̂ót̂ál̂ śîźê ẃâś {totalBytes, number, bytes} K̂B́"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Âv́ôíd̂ én̂ór̂ḿôúŝ ńêt́ŵór̂ḱ p̂áŷĺôád̂ś"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Âv́ôíd̂ś êńôŕm̂óûś n̂ét̂ẃôŕk̂ ṕâýl̂óâd́ŝ"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "M̂ín̂íf̂ýîńĝ ĆŜŚ f̂íl̂éŝ ćâń r̂éd̂úĉé n̂ét̂ẃôŕk̂ ṕâýl̂óâd́ ŝíẑéŝ. [Ĺêár̂ń m̂ór̂é](https://web.dev/unminified-css)."}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "M̂ín̂íf̂ý ĈŚŜ"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "M̂ín̂íf̂ýîńĝ J́âv́âŚĉŕîṕt̂ f́îĺêś ĉán̂ ŕêd́ûćê ṕâýl̂óâd́ ŝíẑéŝ án̂d́ ŝćr̂íp̂t́ p̂ár̂śê t́îḿê. [Ĺêár̂ń m̂ór̂é](https://web.dev/unminified-javascript)."}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "M̂ín̂íf̂ý Ĵáv̂áŜćr̂íp̂t́"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "R̂ém̂óv̂é d̂éâd́ r̂úl̂éŝ f́r̂óm̂ śt̂ýl̂éŝh́êét̂ś âńd̂ d́êf́êŕ t̂h́ê ĺôád̂ín̂ǵ ôf́ ĈŚŜ ńôt́ ûśêd́ f̂ór̂ áb̂óv̂é-t̂h́ê-f́ôĺd̂ ćôńt̂én̂t́ t̂ó r̂éd̂úĉé ûńn̂éĉéŝśâŕŷ b́ŷt́êś ĉón̂śûḿêd́ b̂ý n̂ét̂ẃôŕk̂ áĉt́îv́ît́ŷ. [Ĺêár̂ń m̂ór̂é](https://web.dev/unused-css-rules)."}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "R̂ém̂óv̂é ûńûśêd́ ĈŚŜ"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "R̂ém̂óv̂é ûńûśêd́ Ĵáv̂áŜćr̂íp̂t́ t̂ó r̂éd̂úĉé b̂ýt̂éŝ ćôńŝúm̂éd̂ b́ŷ ńêt́ŵór̂ḱ âćt̂ív̂ít̂ý."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "R̂ém̂óv̂é ûńûśêd́ Ĵáv̂áŜćr̂íp̂t́"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Â ĺôńĝ ćâćĥé l̂íf̂ét̂ím̂é ĉán̂ śp̂éêd́ ûṕ r̂ép̂éât́ v̂íŝít̂ś t̂ó ŷóûŕ p̂áĝé. [L̂éâŕn̂ ḿôŕê](https://web.dev/uses-long-cache-ttl)."}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount, plural,\n    =1 {1 r̂éŝóûŕĉé f̂óûńd̂}\n    other {# ŕêśôúr̂ćêś f̂óûńd̂}\n    }"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Ŝér̂v́ê śt̂át̂íĉ áŝśêt́ŝ ẃît́ĥ án̂ éf̂f́îćîén̂t́ ĉáĉh́ê ṕôĺîćŷ"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Ûśêś êf́f̂íĉíêńt̂ ćâćĥé p̂ól̂íĉý ôń ŝt́ât́îć âśŝét̂ś"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Ôṕt̂ím̂íẑéd̂ ím̂áĝéŝ ĺôád̂ f́âśt̂ér̂ án̂d́ ĉón̂śûḿê ĺêśŝ ćêĺl̂úl̂ár̂ d́ât́â. [Ĺêár̂ń m̂ór̂é](https://web.dev/uses-optimized-images)."}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Êf́f̂íĉíêńt̂ĺŷ én̂ćôd́ê ím̂áĝéŝ"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Ŝér̂v́ê ím̂áĝéŝ t́ĥát̂ ár̂é âṕp̂ŕôṕr̂íât́êĺŷ-śîźêd́ t̂ó ŝáv̂é ĉél̂ĺûĺâŕ d̂át̂á âńd̂ ím̂ṕr̂óv̂é l̂óâd́ t̂ím̂é. [L̂éâŕn̂ ḿôŕê](https://web.dev/uses-responsive-images)."}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "P̂ŕôṕêŕl̂ý ŝíẑé îḿâǵêś"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "T̂éx̂t́-b̂áŝéd̂ ŕêśôúr̂ćêś ŝh́ôúl̂d́ b̂é ŝér̂v́êd́ ŵít̂h́ ĉóm̂ṕr̂éŝśîón̂ (ǵẑíp̂, d́êf́l̂át̂é ôŕ b̂ŕôt́l̂í) t̂ó m̂ín̂ím̂íẑé t̂ót̂ál̂ ńêt́ŵór̂ḱ b̂ýt̂éŝ. [Ĺêár̂ń m̂ór̂é](https://web.dev/uses-text-compression)."}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Êńâb́l̂é t̂éx̂t́ ĉóm̂ṕr̂éŝśîón̂"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "Îḿâǵê f́ôŕm̂át̂ś l̂ík̂é ĴṔÊǴ 2000, ĴṔÊǴ X̂Ŕ, âńd̂ Ẃêb́P̂ óf̂t́êń p̂ŕôv́îd́ê b́êt́t̂ér̂ ćôḿp̂ŕêśŝíôń t̂h́âń P̂ŃĜ ór̂ J́P̂ÉĜ, ẃĥíĉh́ m̂éâńŝ f́âśt̂ér̂ d́ôẃn̂ĺôád̂ś âńd̂ ĺêśŝ d́ât́â ćôńŝúm̂ṕt̂íôń. [L̂éâŕn̂ ḿôŕê](https://web.dev/uses-webp-images)."}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "Ŝér̂v́ê ím̂áĝéŝ ín̂ ńêx́t̂-ǵêń f̂ór̂ḿât́ŝ"}, "lighthouse-core/audits/content-width.js | description": {"message": "Îf́ t̂h́ê ẃîd́t̂h́ ôf́ ŷóûŕ âṕp̂'ś ĉón̂t́êńt̂ d́ôéŝń't̂ ḿât́ĉh́ t̂h́ê ẃîd́t̂h́ ôf́ t̂h́ê v́îéŵṕôŕt̂, ýôúr̂ áp̂ṕ m̂íĝh́t̂ ńôt́ b̂é ôṕt̂ím̂íẑéd̂ f́ôŕ m̂ób̂íl̂é ŝćr̂éêńŝ. [Ĺêár̂ń m̂ór̂é](https://web.dev/content-width)."}, "lighthouse-core/audits/content-width.js | explanation": {"message": "T̂h́ê v́îéŵṕôŕt̂ śîźê óf̂ {innerWidth}ṕx̂ d́ôéŝ ńôt́ m̂át̂ćĥ t́ĥé ŵín̂d́ôẃ ŝíẑé ôf́ {outerWidth}p̂x́."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "Ĉón̂t́êńt̂ íŝ ńôt́ ŝíẑéd̂ ćôŕr̂éĉt́l̂ý f̂ór̂ t́ĥé v̂íêẃp̂ór̂t́"}, "lighthouse-core/audits/content-width.js | title": {"message": "Ĉón̂t́êńt̂ íŝ śîźêd́ ĉór̂ŕêćt̂ĺŷ f́ôŕ t̂h́ê v́îéŵṕôŕt̂"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "T̂h́ê Ćr̂ít̂íĉál̂ Ŕêq́ûéŝt́ Ĉh́âín̂ś b̂él̂óŵ śĥóŵ ýôú ŵh́ât́ r̂éŝóûŕĉéŝ ár̂é l̂óâd́êd́ ŵít̂h́ â h́îǵĥ ṕr̂íôŕît́ŷ. Ćôńŝíd̂ér̂ ŕêd́ûćîńĝ t́ĥé l̂én̂ǵt̂h́ ôf́ ĉh́âín̂ś, r̂éd̂úĉín̂ǵ t̂h́ê d́ôẃn̂ĺôád̂ śîźê óf̂ ŕêśôúr̂ćêś, ôŕ d̂éf̂ér̂ŕîńĝ t́ĥé d̂óŵńl̂óâd́ ôf́ ûńn̂éĉéŝśâŕŷ ŕêśôúr̂ćêś t̂ó îḿp̂ŕôv́ê ṕâǵê ĺôád̂. [Ĺêár̂ń m̂ór̂é](https://web.dev/critical-request-chains)."}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount, plural,\n    =1 {1 ĉh́âín̂ f́ôún̂d́}\n    other {# ĉh́âín̂ś f̂óûńd̂}\n    }"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "Âv́ôíd̂ ćĥáîńîńĝ ćr̂ít̂íĉál̂ ŕêq́ûéŝt́ŝ"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "D̂ép̂ŕêćât́îón̂ / Ẃâŕn̂ín̂ǵ"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "L̂ín̂é"}, "lighthouse-core/audits/deprecations.js | description": {"message": "D̂ép̂ŕêćât́êd́ ÂṔÎś ŵíl̂ĺ êv́êńt̂úâĺl̂ý b̂é r̂ém̂óv̂éd̂ f́r̂óm̂ t́ĥé b̂ŕôẃŝér̂. [Ĺêár̂ń m̂ór̂é](https://web.dev/deprecations)."}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount, plural,\n    =1 {1 ŵár̂ńîńĝ f́ôún̂d́}\n    other {# ŵár̂ńîńĝś f̂óûńd̂}\n    }"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "Ûśêś d̂ép̂ŕêćât́êd́ ÂṔÎś"}, "lighthouse-core/audits/deprecations.js | title": {"message": "Âv́ôíd̂ś d̂ép̂ŕêćât́êd́ ÂṔÎś"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "Âṕp̂ĺîćât́îón̂ Ćâćĥé îś d̂ép̂ŕêćât́êd́. [L̂éâŕn̂ ḿôŕê](https://web.dev/appcache-manifest)."}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "F̂óûńd̂ \"{AppCacheManifest}\""}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "Ûśêś Âṕp̂ĺîćât́îón̂ Ćâćĥé"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "Âv́ôíd̂ś Âṕp̂ĺîćât́îón̂ Ćâćĥé"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "Ŝṕêćîf́ŷín̂ǵ â d́ôćt̂ýp̂é p̂ŕêv́êńt̂ś t̂h́ê b́r̂óŵśêŕ f̂ŕôḿ ŝẃît́ĉh́îńĝ t́ô q́ûír̂ḱŝ-ḿôd́ê. [Ĺêár̂ń m̂ór̂é](https://web.dev/doctype)."}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "D̂óĉt́ŷṕê ńâḿê ḿûśt̂ b́ê t́ĥé l̂óŵér̂ćâśê śt̂ŕîńĝ `html`"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "D̂óĉúm̂én̂t́ m̂úŝt́ ĉón̂t́âín̂ á d̂óĉt́ŷṕê"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Êx́p̂éĉt́êd́ p̂úb̂ĺîćÎd́ t̂ó b̂é âń êḿp̂t́ŷ śt̂ŕîńĝ"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Êx́p̂éĉt́êd́ ŝýŝt́êḿÎd́ t̂ó b̂é âń êḿp̂t́ŷ śt̂ŕîńĝ"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "P̂áĝé l̂áĉḱŝ t́ĥé ĤT́M̂Ĺ d̂óĉt́ŷṕê, t́ĥúŝ t́r̂íĝǵêŕîńĝ q́ûír̂ḱŝ-ḿôd́ê"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "P̂áĝé ĥáŝ t́ĥé ĤT́M̂Ĺ d̂óĉt́ŷṕê"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "Êĺêḿêńt̂"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Ŝt́ât́îśt̂íĉ"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "V̂ál̂úê"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "Â ĺâŕĝé D̂ÓM̂ ẃîĺl̂ ín̂ćr̂éâśê ḿêḿôŕŷ úŝáĝé, ĉáûśê ĺôńĝér̂ [śt̂ýl̂é ĉál̂ćûĺât́îón̂ś](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations), âńd̂ ṕr̂ód̂úĉé ĉóŝt́l̂ý [l̂áŷóût́ r̂éf̂ĺôẃŝ](https://developers.google.com/speed/articles/reflow). [Ĺêár̂ń m̂ór̂é](https://web.dev/dom-size)."}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount, plural,\n    =1 {1 êĺêḿêńt̂}\n    other {# él̂ém̂én̂t́ŝ}\n    }"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Âv́ôíd̂ án̂ éx̂ćêśŝív̂é D̂ÓM̂ śîźê"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "M̂áx̂ím̂úm̂ D́ÔḾ D̂ép̂t́ĥ"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "T̂ót̂ál̂ D́ÔḾ Êĺêḿêńt̂ś"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "M̂áx̂ím̂úm̂ Ćĥíl̂d́ Êĺêḿêńt̂ś"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "Âv́ôíd̂ś âń êx́ĉéŝśîv́ê D́ÔḾ ŝíẑé"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "R̂él̂"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "T̂ár̂ǵêt́"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "Âd́d̂ `rel=\"noopener\"` ór̂ `rel=\"noreferrer\"` t́ô án̂ý êx́t̂ér̂ńâĺ l̂ín̂ḱŝ t́ô ím̂ṕr̂óv̂é p̂ér̂f́ôŕm̂án̂ćê án̂d́ p̂ŕêv́êńt̂ śêćûŕît́ŷ v́ûĺn̂ér̂áb̂íl̂ít̂íêś. [L̂éâŕn̂ ḿôŕê](https://web.dev/external-anchors-use-rel-noopener)."}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "L̂ín̂ḱŝ t́ô ćr̂óŝś-ôŕîǵîń d̂éŝt́îńât́îón̂ś âŕê ún̂śâf́ê"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "L̂ín̂ḱŝ t́ô ćr̂óŝś-ôŕîǵîń d̂éŝt́îńât́îón̂ś âŕê śâf́ê"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "Ûńâb́l̂é t̂ó d̂ét̂ér̂ḿîńê t́ĥé d̂éŝt́îńât́îón̂ f́ôŕ âńĉh́ôŕ ({anchorHTML}). Îf́ n̂ót̂ úŝéd̂ áŝ á ĥýp̂ér̂ĺîńk̂, ćôńŝíd̂ér̂ ŕêḿôv́îńĝ t́âŕĝét̂=_b́l̂án̂ḱ."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Ûśêŕŝ ár̂é m̂íŝt́r̂úŝt́f̂úl̂ óf̂ ór̂ ćôńf̂úŝéd̂ b́ŷ śît́êś t̂h́ât́ r̂éq̂úêśt̂ t́ĥéîŕ l̂óĉát̂íôń ŵít̂h́ôút̂ ćôńt̂éx̂t́. Ĉón̂śîd́êŕ t̂ýîńĝ t́ĥé r̂éq̂úêśt̂ t́ô á ûśêŕ âćt̂íôń îńŝt́êád̂. [Ĺêár̂ń m̂ór̂é](https://web.dev/geolocation-on-start)."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "R̂éq̂úêśt̂ś t̂h́ê ǵêól̂óĉát̂íôń p̂ér̂ḿîśŝíôń ôń p̂áĝé l̂óâd́"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Âv́ôíd̂ś r̂éq̂úêśt̂ín̂ǵ t̂h́ê ǵêól̂óĉát̂íôń p̂ér̂ḿîśŝíôń ôń p̂áĝé l̂óâd́"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "V̂ér̂śîón̂"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "Âĺl̂ f́r̂ón̂t́-êńd̂ J́âv́âŚĉŕîṕt̂ ĺîb́r̂ár̂íêś d̂ét̂éĉt́êd́ ôń t̂h́ê ṕâǵê. [Ĺêár̂ń m̂ór̂é](https://web.dev/js-libraries)."}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "D̂ét̂éĉt́êd́ Ĵáv̂áŜćr̂íp̂t́ l̂íb̂ŕâŕîéŝ"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "F̂ór̂ úŝér̂ś ôń ŝĺôẃ ĉón̂ńêćt̂íôńŝ, éx̂t́êŕn̂ál̂ śĉŕîṕt̂ś d̂ýn̂ám̂íĉál̂ĺŷ ín̂j́êćt̂éd̂ v́îá `document.write()` ĉán̂ d́êĺâý p̂áĝé l̂óâd́ b̂ý t̂én̂ś ôf́ ŝéĉón̂d́ŝ. [Ĺêár̂ń m̂ór̂é](https://web.dev/no-document-write)."}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Ûśêś `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "Âv́ôíd̂ś `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "Ĥíĝh́êśt̂ Śêv́êŕît́ŷ"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "L̂íb̂ŕâŕŷ V́êŕŝíôń"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "V̂úl̂ńêŕâb́îĺît́ŷ Ćôún̂t́"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "Ŝóm̂é t̂h́îŕd̂-ṕâŕt̂ý ŝćr̂íp̂t́ŝ ḿâý ĉón̂t́âín̂ ḱn̂óŵń ŝéĉúr̂ít̂ý v̂úl̂ńêŕâb́îĺît́îéŝ t́ĥát̂ ár̂é êáŝíl̂ý îd́êńt̂íf̂íêd́ âńd̂ éx̂ṕl̂óît́êd́ b̂ý ât́t̂áĉḱêŕŝ. [Ĺêár̂ń m̂ór̂é](https://web.dev/no-vulnerable-libraries)."}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount, plural,\n    =1 {1 v̂úl̂ńêŕâb́îĺît́ŷ d́êt́êćt̂éd̂}\n    other {# v́ûĺn̂ér̂áb̂íl̂ít̂íêś d̂ét̂éĉt́êd́}\n    }"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "Îńĉĺûd́êś f̂ŕôńt̂-én̂d́ Ĵáv̂áŜćr̂íp̂t́ l̂íb̂ŕâŕîéŝ ẃît́ĥ ḱn̂óŵń ŝéĉúr̂ít̂ý v̂úl̂ńêŕâb́îĺît́îéŝ"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "Ĥíĝh́"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "L̂óŵ"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "M̂éd̂íûḿ"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "Âv́ôíd̂ś f̂ŕôńt̂-én̂d́ Ĵáv̂áŜćr̂íp̂t́ l̂íb̂ŕâŕîéŝ ẃît́ĥ ḱn̂óŵń ŝéĉúr̂ít̂ý v̂úl̂ńêŕâb́îĺît́îéŝ"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "Ûśêŕŝ ár̂é m̂íŝt́r̂úŝt́f̂úl̂ óf̂ ór̂ ćôńf̂úŝéd̂ b́ŷ śît́êś t̂h́ât́ r̂éq̂úêśt̂ t́ô śêńd̂ ńôt́îf́îćât́îón̂ś ŵít̂h́ôút̂ ćôńt̂éx̂t́. Ĉón̂śîd́êŕ t̂ýîńĝ t́ĥé r̂éq̂úêśt̂ t́ô úŝér̂ ǵêśt̂úr̂éŝ ín̂śt̂éâd́. [L̂éâŕn̂ ḿôŕê](https://web.dev/notification-on-start)."}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "R̂éq̂úêśt̂ś t̂h́ê ńôt́îf́îćât́îón̂ ṕêŕm̂íŝśîón̂ ón̂ ṕâǵê ĺôád̂"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "Âv́ôíd̂ś r̂éq̂úêśt̂ín̂ǵ t̂h́ê ńôt́îf́îćât́îón̂ ṕêŕm̂íŝśîón̂ ón̂ ṕâǵê ĺôád̂"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "F̂áîĺîńĝ Él̂ém̂én̂t́ŝ"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "P̂ŕêv́êńt̂ín̂ǵ p̂áŝśŵór̂d́ p̂áŝt́îńĝ ún̂d́êŕm̂ín̂éŝ ǵôód̂ śêćûŕît́ŷ ṕôĺîćŷ. [Ĺêár̂ń m̂ór̂é](https://web.dev/password-inputs-can-be-pasted-into)."}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "P̂ŕêv́êńt̂ś ûśêŕŝ t́ô ṕâśt̂é îńt̂ó p̂áŝśŵór̂d́ f̂íêĺd̂ś"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "Âĺl̂óŵś ûśêŕŝ t́ô ṕâśt̂é îńt̂ó p̂áŝśŵór̂d́ f̂íêĺd̂ś"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "P̂ŕôt́ôćôĺ"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "ĤT́T̂Ṕ/2 ôf́f̂ér̂ś m̂án̂ý b̂én̂éf̂ít̂ś ôv́êŕ ĤT́T̂Ṕ/1.1, îńĉĺûd́îńĝ b́îńâŕŷ h́êád̂ér̂ś, m̂úl̂t́îṕl̂éx̂ín̂ǵ, âńd̂ śêŕv̂ér̂ ṕûśĥ. [Ĺêár̂ń m̂ór̂é](https://web.dev/uses-http2)."}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount, plural,\n    =1 {1 r̂éq̂úêśt̂ ńôt́ ŝér̂v́êd́ v̂íâ H́T̂T́P̂/2}\n    other {# ŕêq́ûéŝt́ŝ ńôt́ ŝér̂v́êd́ v̂íâ H́T̂T́P̂/2}\n    }"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "D̂óêś n̂ót̂ úŝé ĤT́T̂Ṕ/2 f̂ór̂ ál̂ĺ ôf́ ît́ŝ ŕêśôúr̂ćêś"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "Ûśêś ĤT́T̂Ṕ/2 f̂ór̂ ít̂ś ôẃn̂ ŕêśôúr̂ćêś"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Ĉón̂śîd́êŕ m̂ár̂ḱîńĝ ýôúr̂ t́ôúĉh́ âńd̂ ẃĥéêĺ êv́êńt̂ ĺîśt̂én̂ér̂ś âś `passive` t̂ó îḿp̂ŕôv́ê ýôúr̂ ṕâǵê'ś ŝćr̂ól̂ĺ p̂ér̂f́ôŕm̂án̂ćê. [Ĺêár̂ń m̂ór̂é](https://web.dev/uses-passive-event-listeners)."}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "D̂óêś n̂ót̂ úŝé p̂áŝśîv́ê ĺîśt̂én̂ér̂ś t̂ó îḿp̂ŕôv́ê śĉŕôĺl̂ín̂ǵ p̂ér̂f́ôŕm̂án̂ćê"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Ûśêś p̂áŝśîv́ê ĺîśt̂én̂ér̂ś t̂ó îḿp̂ŕôv́ê śĉŕôĺl̂ín̂ǵ p̂ér̂f́ôŕm̂án̂ćê"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "D̂éŝćr̂íp̂t́îón̂"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "Êŕr̂ór̂ś l̂óĝǵêd́ t̂ó t̂h́ê ćôńŝól̂é îńd̂íĉát̂é ûńr̂éŝól̂v́êd́ p̂ŕôb́l̂ém̂ś. T̂h́êý ĉán̂ ćôḿê f́r̂óm̂ ńêt́ŵór̂ḱ r̂éq̂úêśt̂ f́âíl̂úr̂éŝ án̂d́ ôt́ĥér̂ b́r̂óŵśêŕ ĉón̂ćêŕn̂ś. [L̂éâŕn̂ ḿôŕê](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "B̂ŕôẃŝér̂ ér̂ŕôŕŝ ẃêŕê ĺôǵĝéd̂ t́ô t́ĥé ĉón̂śôĺê"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "N̂ó b̂ŕôẃŝér̂ ér̂ŕôŕŝ ĺôǵĝéd̂ t́ô t́ĥé ĉón̂śôĺê"}, "lighthouse-core/audits/font-display.js | description": {"message": "L̂év̂ér̂áĝé t̂h́ê f́ôńt̂-d́îśp̂ĺâý ĈŚŜ f́êát̂úr̂é t̂ó êńŝúr̂é t̂éx̂t́ îś ûśêŕ-v̂íŝíb̂ĺê ẃĥíl̂é ŵéb̂f́ôńt̂ś âŕê ĺôád̂ín̂ǵ. [L̂éâŕn̂ ḿôŕê](https://web.dev/font-display)."}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "Êńŝúr̂é t̂éx̂t́ r̂ém̂áîńŝ v́îśîb́l̂é d̂úr̂ín̂ǵ ŵéb̂f́ôńt̂ ĺôád̂"}, "lighthouse-core/audits/font-display.js | title": {"message": "Âĺl̂ t́êx́t̂ ŕêḿâín̂ś v̂íŝíb̂ĺê d́ûŕîńĝ ẃêb́f̂ón̂t́ l̂óâd́ŝ"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "L̂íĝh́t̂h́ôúŝé ŵáŝ ún̂áb̂ĺê t́ô áût́ôḿât́îćâĺl̂ý ĉh́êćk̂ t́ĥé f̂ón̂t́-d̂íŝṕl̂áŷ v́âĺûé f̂ór̂ t́ĥé f̂ól̂ĺôẃîńĝ ÚR̂Ĺ: {fontURL}."}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "Âśp̂éĉt́ R̂át̂íô (Áĉt́ûál̂)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Âśp̂éĉt́ R̂át̂íô (D́îśp̂ĺâýêd́)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "Îḿâǵê d́îśp̂ĺâý d̂ím̂én̂śîón̂ś ŝh́ôúl̂d́ m̂át̂ćĥ ńât́ûŕâĺ âśp̂éĉt́ r̂át̂íô. [Ĺêár̂ń m̂ór̂é](https://web.dev/image-aspect-ratio)."}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "D̂íŝṕl̂áŷś îḿâǵêś ŵít̂h́ îńĉór̂ŕêćt̂ áŝṕêćt̂ ŕât́îó"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "D̂íŝṕl̂áŷś îḿâǵêś ŵít̂h́ ĉór̂ŕêćt̂ áŝṕêćt̂ ŕât́îó"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "Îńv̂ál̂íd̂ ím̂áĝé ŝíẑín̂ǵ îńf̂ór̂ḿât́îón̂ {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "B̂ŕôẃŝér̂ś ĉán̂ ṕr̂óâćt̂ív̂él̂ý p̂ŕôḿp̂t́ ûśêŕŝ t́ô ád̂d́ ŷóûŕ âṕp̂ t́ô t́ĥéîŕ ĥóm̂éŝćr̂éêń, ŵh́îćĥ ćâń l̂éâd́ t̂ó ĥíĝh́êŕ êńĝáĝém̂én̂t́. [L̂éâŕn̂ ḿôŕê](https://web.dev/installable-manifest)."}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "Ŵéb̂ áp̂ṕ m̂án̂íf̂éŝt́ d̂óêś n̂ót̂ ḿêét̂ t́ĥé îńŝt́âĺl̂áb̂íl̂ít̂ý r̂éq̂úîŕêḿêńt̂ś"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "Ŵéb̂ áp̂ṕ m̂án̂íf̂éŝt́ m̂éêt́ŝ t́ĥé îńŝt́âĺl̂áb̂íl̂ít̂ý r̂éq̂úîŕêḿêńt̂ś"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "Îńŝéĉúr̂é ÛŔL̂"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "Âĺl̂ śît́êś ŝh́ôúl̂d́ b̂é p̂ŕôt́êćt̂éd̂ ẃît́ĥ H́T̂T́P̂Ś, êv́êń ôńêś t̂h́ât́ d̂ón̂'t́ ĥán̂d́l̂é ŝén̂śît́îv́ê d́ât́â. H́T̂T́P̂Ś p̂ŕêv́êńt̂ś îńt̂ŕûd́êŕŝ f́r̂óm̂ t́âḿp̂ér̂ín̂ǵ ŵít̂h́ ôŕ p̂áŝśîv́êĺŷ ĺîśt̂én̂ín̂ǵ îń ôń t̂h́ê ćôḿm̂ún̂íĉát̂íôńŝ b́êt́ŵéêń ŷóûŕ âṕp̂ án̂d́ ŷóûŕ ûśêŕŝ, án̂d́ îś â ṕr̂ér̂éq̂úîśît́ê f́ôŕ ĤT́T̂Ṕ/2 âńd̂ ḿâńŷ ńêẃ ŵéb̂ ṕl̂át̂f́ôŕm̂ ÁP̂Íŝ. [Ĺêár̂ń m̂ór̂é](https://web.dev/is-on-https)."}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount, plural,\n    =1 {1 îńŝéĉúr̂é r̂éq̂úêśt̂ f́ôún̂d́}\n    other {# îńŝéĉúr̂é r̂éq̂úêśt̂ś f̂óûńd̂}\n    }"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "D̂óêś n̂ót̂ úŝé ĤT́T̂ṔŜ"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "Ûśêś ĤT́T̂ṔŜ"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "Â f́âśt̂ ṕâǵê ĺôád̂ óv̂ér̂ á ĉél̂ĺûĺâŕ n̂ét̂ẃôŕk̂ én̂śûŕêś â ǵôód̂ ḿôb́îĺê úŝér̂ éx̂ṕêŕîén̂ćê. [Ĺêár̂ń m̂ór̂é](https://web.dev/load-fast-enough-for-pwa)."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "Îńt̂ér̂áĉt́îv́ê át̂ {timeInMs, number, seconds} ś"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "Îńt̂ér̂áĉt́îv́ê ón̂ śîḿûĺât́êd́ m̂ób̂íl̂é n̂ét̂ẃôŕk̂ át̂ {timeInMs, number, seconds} ś"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "Ŷóûŕ p̂áĝé l̂óâd́ŝ t́ôó ŝĺôẃl̂ý âńd̂ íŝ ńôt́ îńt̂ér̂áĉt́îv́ê ẃît́ĥín̂ 10 śêćôńd̂ś. L̂óôḱ ât́ t̂h́ê óp̂ṕôŕt̂ún̂ít̂íêś âńd̂ d́îáĝńôśt̂íĉś îń t̂h́ê \"Ṕêŕf̂ór̂ḿâńĉé\" ŝéĉt́îón̂ t́ô ĺêár̂ń ĥóŵ t́ô ím̂ṕr̂óv̂é."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "P̂áĝé l̂óâd́ îś n̂ót̂ f́âśt̂ én̂óûǵĥ ón̂ ḿôb́îĺê ńêt́ŵór̂ḱŝ"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "P̂áĝé l̂óâd́ îś f̂áŝt́ êńôúĝh́ ôń m̂ób̂íl̂é n̂ét̂ẃôŕk̂ś"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Ĉát̂éĝór̂ý"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "Ĉón̂śîd́êŕ r̂éd̂úĉín̂ǵ t̂h́ê t́îḿê śp̂én̂t́ p̂ár̂śîńĝ, ćôḿp̂íl̂ín̂ǵ âńd̂ éx̂éĉút̂ín̂ǵ ĴŚ. Ŷóû ḿâý f̂ín̂d́ d̂él̂ív̂ér̂ín̂ǵ ŝḿâĺl̂ér̂ J́Ŝ ṕâýl̂óâd́ŝ h́êĺp̂ś ŵít̂h́ t̂h́îś. [L̂éâŕn̂ ḿôŕê](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "M̂ín̂ím̂íẑé m̂áîń-t̂h́r̂éâd́ ŵór̂ḱ"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "M̂ín̂ím̂íẑéŝ ḿâín̂-t́ĥŕêád̂ ẃôŕk̂"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "T̂ó r̂éâćĥ t́ĥé m̂óŝt́ n̂úm̂b́êŕ ôf́ ûśêŕŝ, śît́êś ŝh́ôúl̂d́ ŵór̂ḱ âćr̂óŝś êv́êŕŷ ḿâj́ôŕ b̂ŕôẃŝér̂. [Ĺêár̂ń m̂ór̂é](https://web.dev/pwa-cross-browser)."}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "Ŝít̂é ŵór̂ḱŝ ćr̂óŝś-b̂ŕôẃŝér̂"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Êńŝúr̂é îńd̂ív̂íd̂úâĺ p̂áĝéŝ ár̂é d̂éêṕ l̂ín̂ḱâb́l̂é v̂íâ ÚR̂Ĺ âńd̂ t́ĥát̂ ÚR̂Ĺŝ ár̂é ûńîq́ûé f̂ór̂ t́ĥé p̂úr̂ṕôśê óf̂ śĥár̂éâb́îĺît́ŷ ón̂ śôćîál̂ ḿêd́îá. [L̂éâŕn̂ ḿôŕê](https://web.dev/pwa-each-page-has-url)."}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Êáĉh́ p̂áĝé ĥáŝ á ÛŔL̂"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "T̂ŕâńŝít̂íôńŝ śĥóûĺd̂ f́êél̂ śn̂áp̂ṕŷ áŝ ýôú t̂áp̂ ár̂óûńd̂, év̂én̂ ón̂ á ŝĺôẃ n̂ét̂ẃôŕk̂. T́ĥíŝ éx̂ṕêŕîén̂ćê íŝ ḱêý t̂ó â úŝér̂'ś p̂ér̂ćêṕt̂íôń ôf́ p̂ér̂f́ôŕm̂án̂ćê. [Ĺêár̂ń m̂ór̂é](https://web.dev/pwa-page-transitions)."}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "P̂áĝé t̂ŕâńŝít̂íôńŝ d́ôń't̂ f́êél̂ ĺîḱê t́ĥéŷ b́l̂óĉḱ ôń t̂h́ê ńêt́ŵór̂ḱ"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "Êśt̂ím̂át̂éd̂ Ín̂ṕût́ L̂át̂én̂ćŷ íŝ án̂ éŝt́îḿât́ê óf̂ h́ôẃ l̂ón̂ǵ ŷóûŕ âṕp̂ t́âḱêś t̂ó r̂éŝṕôńd̂ t́ô úŝér̂ ín̂ṕût́, îń m̂íl̂ĺîśêćôńd̂ś, d̂úr̂ín̂ǵ t̂h́ê b́ûśîéŝt́ 5ŝ ẃîńd̂óŵ óf̂ ṕâǵê ĺôád̂. Íf̂ ýôúr̂ ĺât́êńĉý îś ĥíĝh́êŕ t̂h́âń 50 m̂ś, ûśêŕŝ ḿâý p̂ér̂ćêív̂é ŷóûŕ âṕp̂ áŝ ĺâǵĝý. [L̂éâŕn̂ ḿôŕê](https://web.dev/estimated-input-latency)."}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "Êśt̂ím̂át̂éd̂ Ín̂ṕût́ L̂át̂én̂ćŷ"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "F̂ír̂śt̂ Ćôńt̂én̂t́f̂úl̂ Ṕâín̂t́ m̂ár̂ḱŝ t́ĥé t̂ím̂é ât́ ŵh́îćĥ t́ĥé f̂ír̂śt̂ t́êx́t̂ ór̂ ím̂áĝé îś p̂áîńt̂éd̂. [Ĺêár̂ń m̂ór̂é](https://web.dev/first-contentful-paint)."}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "F̂ír̂śt̂ Ćôńt̂én̂t́f̂úl̂ Ṕâín̂t́"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "F̂ír̂śt̂ ĆP̂Ú Îd́l̂é m̂ár̂ḱŝ t́ĥé f̂ír̂śt̂ t́îḿê át̂ ẃĥíĉh́ t̂h́ê ṕâǵê'ś m̂áîń t̂h́r̂éâd́ îś q̂úîét̂ én̂óûǵĥ t́ô h́âńd̂ĺê ín̂ṕût́.  [L̂éâŕn̂ ḿôŕê](https://web.dev/first-cpu-idle)."}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "F̂ír̂śt̂ ĆP̂Ú Îd́l̂é"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "F̂ír̂śt̂ Ḿêán̂ín̂ǵf̂úl̂ Ṕâín̂t́ m̂éâśûŕêś ŵh́êń t̂h́ê ṕr̂ím̂ár̂ý ĉón̂t́êńt̂ óf̂ á p̂áĝé îś v̂íŝíb̂ĺê. [Ĺêár̂ń m̂ór̂é](https://web.dev/first-meaningful-paint)."}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "F̂ír̂śt̂ Ḿêán̂ín̂ǵf̂úl̂ Ṕâín̂t́"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "T̂ím̂é t̂ó îńt̂ér̂áĉt́îv́ê íŝ t́ĥé âḿôún̂t́ ôf́ t̂ím̂é ît́ t̂ák̂éŝ f́ôŕ t̂h́ê ṕâǵê t́ô b́êćôḿê f́ûĺl̂ý îńt̂ér̂áĉt́îv́ê. [Ĺêár̂ń m̂ór̂é](https://web.dev/interactive)."}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "T̂ím̂é t̂ó Îńt̂ér̂áĉt́îv́ê"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "T̂h́ê ḿâx́îḿûḿ p̂ót̂én̂t́îál̂ F́îŕŝt́ Îńp̂út̂ D́êĺâý t̂h́ât́ ŷóûŕ ûśêŕŝ ćôúl̂d́ êx́p̂ér̂íêńĉé îś t̂h́ê d́ûŕât́îón̂, ín̂ ḿîĺl̂íŝéĉón̂d́ŝ, óf̂ t́ĥé l̂ón̂ǵêśt̂ t́âśk̂. [Ĺêár̂ń m̂ór̂é](https://developers.google.com/web/updates/2018/05/first-input-delay)."}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "M̂áx̂ Ṕôt́êńt̂íâĺ F̂ír̂śt̂ Ín̂ṕût́ D̂él̂áŷ"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "Ŝṕêéd̂ Ín̂d́êx́ ŝh́ôẃŝ h́ôẃ q̂úîćk̂ĺŷ t́ĥé ĉón̂t́êńt̂ś ôf́ â ṕâǵê ár̂é v̂íŝíb̂ĺŷ ṕôṕûĺât́êd́. [L̂éâŕn̂ ḿôŕê](https://web.dev/speed-index)."}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "Ŝṕêéd̂ Ín̂d́êx́"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "Ŝúm̂ óf̂ ál̂ĺ t̂ím̂é p̂ér̂íôd́ŝ b́êt́ŵéêń F̂ĆP̂ án̂d́ T̂ím̂é t̂ó Îńt̂ér̂áĉt́îv́ê, ẃĥén̂ t́âśk̂ ĺêńĝt́ĥ éx̂ćêéd̂éd̂ 50ḿŝ, éx̂ṕr̂éŝśêd́ îń m̂íl̂ĺîśêćôńd̂ś."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "T̂ót̂ál̂ B́l̂óĉḱîńĝ T́îḿê"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "N̂ét̂ẃôŕk̂ ŕôún̂d́ t̂ŕîṕ t̂ím̂éŝ (ŔT̂T́) ĥáv̂é â ĺâŕĝé îḿp̂áĉt́ ôń p̂ér̂f́ôŕm̂án̂ćê. Íf̂ t́ĥé R̂T́T̂ t́ô án̂ ór̂íĝín̂ íŝ h́îǵĥ, ít̂'ś âń îńd̂íĉát̂íôń t̂h́ât́ ŝér̂v́êŕŝ ćl̂óŝér̂ t́ô t́ĥé ûśêŕ ĉóûĺd̂ ím̂ṕr̂óv̂é p̂ér̂f́ôŕm̂án̂ćê. [Ĺêár̂ń m̂ór̂é](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "lighthouse-core/audits/network-rtt.js | title": {"message": "N̂ét̂ẃôŕk̂ Ŕôún̂d́ T̂ŕîṕ T̂ím̂éŝ"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "Ŝér̂v́êŕ l̂át̂én̂ćîéŝ ćâń îḿp̂áĉt́ ŵéb̂ ṕêŕf̂ór̂ḿâńĉé. Îf́ t̂h́ê śêŕv̂ér̂ ĺât́êńĉý ôf́ âń ôŕîǵîń îś ĥíĝh́, ît́'ŝ án̂ ín̂d́îćât́îón̂ t́ĥé ŝér̂v́êŕ îś ôv́êŕl̂óâd́êd́ ôŕ ĥáŝ ṕôór̂ b́âćk̂én̂d́ p̂ér̂f́ôŕm̂án̂ćê. [Ĺêár̂ń m̂ór̂é](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "Ŝér̂v́êŕ B̂áĉḱêńd̂ Ĺât́êńĉíêś"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "Â śêŕv̂íĉé ŵór̂ḱêŕ êńâb́l̂éŝ ýôúr̂ ẃêb́ âṕp̂ t́ô b́ê ŕêĺîáb̂ĺê ín̂ ún̂ṕr̂éd̂íĉt́âb́l̂é n̂ét̂ẃôŕk̂ ćôńd̂ít̂íôńŝ. [Ĺêár̂ń m̂ór̂é](https://web.dev/offline-start-url)."}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url` d̂óêś n̂ót̂ ŕêśp̂ón̂d́ ŵít̂h́ â 200 ẃĥén̂ óf̂f́l̂ín̂é"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url` r̂éŝṕôńd̂ś ŵít̂h́ â 200 ẃĥén̂ óf̂f́l̂ín̂é"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "L̂íĝh́t̂h́ôúŝé ĉóûĺd̂ń't̂ ŕêád̂ t́ĥé `start_url` f̂ŕôḿ t̂h́ê ḿâńîf́êśt̂. Áŝ á r̂éŝúl̂t́, t̂h́ê `start_url` ẃâś âśŝúm̂éd̂ t́ô b́ê t́ĥé d̂óĉúm̂én̂t́'ŝ ÚR̂Ĺ. Êŕr̂ór̂ ḿêśŝáĝé: '{manifestWarning}'."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "Ôv́êŕ B̂úd̂ǵêt́"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "K̂éêṕ t̂h́ê q́ûán̂t́ît́ŷ án̂d́ ŝíẑé ôf́ n̂ét̂ẃôŕk̂ ŕêq́ûéŝt́ŝ ún̂d́êŕ t̂h́ê t́âŕĝét̂ś ŝét̂ b́ŷ t́ĥé p̂ŕôv́îd́êd́ p̂ér̂f́ôŕm̂án̂ćê b́ûd́ĝét̂. [Ĺêár̂ń m̂ór̂é](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count, plural,\n    =1 {1 r̂éq̂úêśt̂}\n    other {# ŕêq́ûéŝt́ŝ}\n   }"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "P̂ér̂f́ôŕm̂án̂ćê b́ûd́ĝét̂"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "Îf́ ŷóû'v́ê ál̂ŕêád̂ý ŝét̂ úp̂ H́T̂T́P̂Ś, m̂ák̂é ŝúr̂é t̂h́ât́ ŷóû ŕêd́îŕêćt̂ ál̂ĺ ĤT́T̂Ṕ t̂ŕâf́f̂íĉ t́ô H́T̂T́P̂Ś îń ôŕd̂ér̂ t́ô én̂áb̂ĺê śêćûŕê ẃêb́ f̂éât́ûŕêś f̂ór̂ ál̂ĺ ŷóûŕ ûśêŕŝ. [Ĺêár̂ń m̂ór̂é](https://web.dev/redirects-http)."}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "D̂óêś n̂ót̂ ŕêd́îŕêćt̂ H́T̂T́P̂ t́r̂áf̂f́îć t̂ó ĤT́T̂ṔŜ"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "R̂éd̂ír̂éĉt́ŝ H́T̂T́P̂ t́r̂áf̂f́îć t̂ó ĤT́T̂ṔŜ"}, "lighthouse-core/audits/redirects.js | description": {"message": "R̂éd̂ír̂éĉt́ŝ ín̂t́r̂ód̂úĉé âd́d̂ít̂íôńâĺ d̂él̂áŷś b̂éf̂ór̂é t̂h́ê ṕâǵê ćâń b̂é l̂óâd́êd́. [L̂éâŕn̂ ḿôŕê](https://web.dev/redirects)."}, "lighthouse-core/audits/redirects.js | title": {"message": "Âv́ôíd̂ ḿûĺt̂íp̂ĺê ṕâǵê ŕêd́îŕêćt̂ś"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "T̂ó ŝét̂ b́ûd́ĝét̂ś f̂ór̂ t́ĥé q̂úâńt̂ít̂ý âńd̂ śîźê óf̂ ṕâǵê ŕêśôúr̂ćêś, âd́d̂ á b̂úd̂ǵêt́.ĵśôń f̂íl̂é. [L̂éâŕn̂ ḿôŕê](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount, plural, =1 {1 r̂éq̂úêśt̂ • {byteCount, number, bytes} ḰB̂} other {# ŕêq́ûéŝt́ŝ • {byteCount, number, bytes} ḰB̂}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "K̂éêṕ r̂éq̂úêśt̂ ćôún̂t́ŝ ĺôẃ âńd̂ t́r̂án̂śf̂ér̂ śîźêś ŝḿâĺl̂"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "Ĉán̂ón̂íĉál̂ ĺîńk̂ś ŝúĝǵêśt̂ ẃĥíĉh́ ÛŔL̂ t́ô śĥóŵ ín̂ śêár̂ćĥ ŕêśûĺt̂ś. [L̂éâŕn̂ ḿôŕê](https://web.dev/canonical)."}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "M̂úl̂t́îṕl̂é ĉón̂f́l̂íĉt́îńĝ ÚR̂Ĺŝ ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "P̂óîńt̂ś t̂ó â d́îf́f̂ér̂én̂t́ d̂óm̂áîń ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "Îńv̂ál̂íd̂ ÚR̂Ĺ ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "P̂óîńt̂ś t̂ó âńôt́ĥér̂ `hreflang` ĺôćât́îón̂ ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "R̂él̂át̂ív̂é ÛŔL̂ ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "P̂óîńt̂ś t̂ó t̂h́ê d́ôḿâín̂'ś r̂óôt́ ÛŔL̂ (t́ĥé ĥóm̂ép̂áĝé), îńŝt́êád̂ óf̂ án̂ éq̂úîv́âĺêńt̂ ṕâǵê óf̂ ćôńt̂én̂t́"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "D̂óĉúm̂én̂t́ d̂óêś n̂ót̂ h́âv́ê á v̂ál̂íd̂ `rel=canonical`"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "D̂óĉúm̂én̂t́ ĥáŝ á v̂ál̂íd̂ `rel=canonical`"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "F̂ón̂t́ ŝíẑéŝ ĺêśŝ t́ĥán̂ 12ṕx̂ ár̂é t̂óô śm̂ál̂ĺ t̂ó b̂é l̂éĝíb̂ĺê án̂d́ r̂éq̂úîŕê ḿôb́îĺê v́îśît́ôŕŝ t́ô “ṕîńĉh́ t̂ó ẑóôḿ” îń ôŕd̂ér̂ t́ô ŕêád̂. Śt̂ŕîv́ê t́ô h́âv́ê >60% óf̂ ṕâǵê t́êx́t̂ ≥12ṕx̂. [Ĺêár̂ń m̂ór̂é](https://web.dev/font-size)."}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} l̂éĝíb̂ĺê t́êx́t̂"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "T̂éx̂t́ îś îĺl̂éĝíb̂ĺê b́êćâúŝé t̂h́êŕê'ś n̂ó v̂íêẃp̂ór̂t́ m̂ét̂á t̂áĝ óp̂t́îḿîźêd́ f̂ór̂ ḿôb́îĺê śĉŕêén̂ś."}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "{decimalProportion, number, extendedPercent} ôf́ t̂éx̂t́ îś t̂óô śm̂ál̂ĺ (b̂áŝéd̂ ón̂ {decimalProportionVisited, number, extendedPercent} śâḿp̂ĺê)."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "D̂óĉúm̂én̂t́ d̂óêśn̂'t́ ûśê ĺêǵîb́l̂é f̂ón̂t́ ŝíẑéŝ"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "D̂óĉúm̂én̂t́ ûśêś l̂éĝíb̂ĺê f́ôńt̂ śîźêś"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "ĥŕêf́l̂án̂ǵ l̂ín̂ḱŝ t́êĺl̂ śêár̂ćĥ én̂ǵîńêś ŵh́ât́ v̂ér̂śîón̂ óf̂ á p̂áĝé t̂h́êý ŝh́ôúl̂d́ l̂íŝt́ îń ŝéâŕĉh́ r̂éŝúl̂t́ŝ f́ôŕ â ǵîv́êń l̂án̂ǵûáĝé ôŕ r̂éĝíôń. [L̂éâŕn̂ ḿôŕê](https://web.dev/hreflang)."}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "D̂óĉúm̂én̂t́ d̂óêśn̂'t́ ĥáv̂é â v́âĺîd́ `hreflang`"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "D̂óĉúm̂én̂t́ ĥáŝ á v̂ál̂íd̂ `hreflang`"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "P̂áĝéŝ ẃît́ĥ ún̂śûćĉéŝśf̂úl̂ H́T̂T́P̂ śt̂át̂úŝ ćôd́êś m̂áŷ ńôt́ b̂é îńd̂éx̂éd̂ ṕr̂óp̂ér̂ĺŷ. [Ĺêár̂ń m̂ór̂é](https://web.dev/http-status-code)."}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "P̂áĝé ĥáŝ ún̂śûćĉéŝśf̂úl̂ H́T̂T́P̂ śt̂át̂úŝ ćôd́ê"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "P̂áĝé ĥáŝ śûćĉéŝśf̂úl̂ H́T̂T́P̂ śt̂át̂úŝ ćôd́ê"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "Ŝéâŕĉh́ êńĝín̂éŝ ár̂é ûńâb́l̂é t̂ó îńĉĺûd́ê ýôúr̂ ṕâǵêś îń ŝéâŕĉh́ r̂éŝúl̂t́ŝ íf̂ t́ĥéŷ d́ôń't̂ h́âv́ê ṕêŕm̂íŝśîón̂ t́ô ćr̂áŵĺ t̂h́êḿ. [L̂éâŕn̂ ḿôŕê](https://web.dev/is-crawable)."}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "P̂áĝé îś b̂ĺôćk̂éd̂ f́r̂óm̂ ín̂d́êx́îńĝ"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "P̂áĝé îśn̂’t́ b̂ĺôćk̂éd̂ f́r̂óm̂ ín̂d́êx́îńĝ"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "D̂éŝćr̂íp̂t́îv́ê ĺîńk̂ t́êx́t̂ h́êĺp̂ś ŝéâŕĉh́ êńĝín̂éŝ ún̂d́êŕŝt́âńd̂ ýôúr̂ ćôńt̂én̂t́. [L̂éâŕn̂ ḿôŕê](https://web.dev/link-text)."}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount, plural,\n    =1 {1 l̂ín̂ḱ f̂óûńd̂}\n    other {# ĺîńk̂ś f̂óûńd̂}\n    }"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "L̂ín̂ḱŝ d́ô ńôt́ ĥáv̂é d̂éŝćr̂íp̂t́îv́ê t́êx́t̂"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "L̂ín̂ḱŝ h́âv́ê d́êśĉŕîṕt̂ív̂é t̂éx̂t́"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "R̂ún̂ t́ĥé [Ŝt́r̂úĉt́ûŕêd́ D̂át̂á T̂éŝt́îńĝ T́ôól̂](https://search.google.com/structured-data/testing-tool/) án̂d́ t̂h́ê [Śt̂ŕûćt̂úr̂éd̂ D́ât́â Ĺîńt̂ér̂](http://linter.structured-data.org/) t́ô v́âĺîd́ât́ê śt̂ŕûćt̂úr̂éd̂ d́ât́â. [Ĺêár̂ń m̂ór̂é](https://web.dev/structured-data)."}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "Ŝt́r̂úĉt́ûŕêd́ d̂át̂á îś v̂ál̂íd̂"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "M̂ét̂á d̂éŝćr̂íp̂t́îón̂ś m̂áŷ b́ê ín̂ćl̂úd̂éd̂ ín̂ śêár̂ćĥ ŕêśûĺt̂ś t̂ó ĉón̂ćîśêĺŷ śûḿm̂ár̂íẑé p̂áĝé ĉón̂t́êńt̂. [Ĺêár̂ń m̂ór̂é](https://web.dev/meta-description)."}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "D̂éŝćr̂íp̂t́îón̂ t́êx́t̂ íŝ ém̂ṕt̂ý."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "D̂óĉúm̂én̂t́ d̂óêś n̂ót̂ h́âv́ê á m̂ét̂á d̂éŝćr̂íp̂t́îón̂"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "D̂óĉúm̂én̂t́ ĥáŝ á m̂ét̂á d̂éŝćr̂íp̂t́îón̂"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "Ŝéâŕĉh́ êńĝín̂éŝ ćâń't̂ ín̂d́êx́ p̂ĺûǵîń ĉón̂t́êńt̂, án̂d́ m̂án̂ý d̂év̂íĉéŝ ŕêśt̂ŕîćt̂ ṕl̂úĝín̂ś ôŕ d̂ón̂'t́ ŝúp̂ṕôŕt̂ t́ĥém̂. [Ĺêár̂ń m̂ór̂é](https://web.dev/plugins)."}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "D̂óĉúm̂én̂t́ ûśêś p̂ĺûǵîńŝ"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "D̂óĉúm̂én̂t́ âv́ôíd̂ś p̂ĺûǵîńŝ"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "Îf́ ŷóûŕ r̂ób̂ót̂ś.t̂x́t̂ f́îĺê íŝ ḿâĺf̂ór̂ḿêd́, ĉŕâẃl̂ér̂ś m̂áŷ ńôt́ b̂é âb́l̂é t̂ó ûńd̂ér̂śt̂án̂d́ ĥóŵ ýôú ŵán̂t́ ŷóûŕ ŵéb̂śît́ê t́ô b́ê ćr̂áŵĺêd́ ôŕ îńd̂éx̂éd̂. [Ĺêár̂ń m̂ór̂é](https://web.dev/robots-txt)."}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "R̂éq̂úêśt̂ f́ôŕ r̂ób̂ót̂ś.t̂x́t̂ ŕêt́ûŕn̂éd̂ H́T̂T́P̂ śt̂át̂úŝ: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount, plural,\n    =1 {1 êŕr̂ór̂ f́ôún̂d́}\n    other {# êŕr̂ór̂ś f̂óûńd̂}\n    }"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "L̂íĝh́t̂h́ôúŝé ŵáŝ ún̂áb̂ĺê t́ô d́ôẃn̂ĺôád̂ á r̂ób̂ót̂ś.t̂x́t̂ f́îĺê"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "r̂ób̂ót̂ś.t̂x́t̂ íŝ ńôt́ v̂ál̂íd̂"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "r̂ób̂ót̂ś.t̂x́t̂ íŝ v́âĺîd́"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "Îńt̂ér̂áĉt́îv́ê él̂ém̂én̂t́ŝ ĺîḱê b́ût́t̂ón̂ś âńd̂ ĺîńk̂ś ŝh́ôúl̂d́ b̂é l̂ár̂ǵê én̂óûǵĥ (48x́48p̂x́), âńd̂ h́âv́ê én̂óûǵĥ śp̂áĉé âŕôún̂d́ t̂h́êḿ, t̂ó b̂é êáŝý êńôúĝh́ t̂ó t̂áp̂ ẃît́ĥóût́ ôv́êŕl̂áp̂ṕîńĝ ón̂t́ô ót̂h́êŕ êĺêḿêńt̂ś. [L̂éâŕn̂ ḿôŕê](https://web.dev/tap-targets)."}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} âṕp̂ŕôṕr̂íât́êĺŷ śîźêd́ t̂áp̂ t́âŕĝét̂ś"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "T̂áp̂ t́âŕĝét̂ś âŕê t́ôó ŝḿâĺl̂ b́êćâúŝé t̂h́êŕê'ś n̂ó v̂íêẃp̂ór̂t́ m̂ét̂á t̂áĝ óp̂t́îḿîźêd́ f̂ór̂ ḿôb́îĺê śĉŕêén̂ś"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "T̂áp̂ t́âŕĝét̂ś âŕê ńôt́ ŝíẑéd̂ áp̂ṕr̂óp̂ŕîát̂él̂ý"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Ôv́êŕl̂áp̂ṕîńĝ T́âŕĝét̂"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "T̂áp̂ T́âŕĝét̂"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "T̂áp̂ t́âŕĝét̂ś âŕê śîźêd́ âṕp̂ŕôṕr̂íât́êĺŷ"}, "lighthouse-core/audits/service-worker.js | description": {"message": "T̂h́ê śêŕv̂íĉé ŵór̂ḱêŕ îś t̂h́ê t́êćĥńôĺôǵŷ t́ĥát̂ én̂áb̂ĺêś ŷóûŕ âṕp̂ t́ô úŝé m̂án̂ý P̂ŕôǵr̂éŝśîv́ê Ẃêb́ Âṕp̂ f́êát̂úr̂éŝ, śûćĥ áŝ óf̂f́l̂ín̂é, âd́d̂ t́ô h́ôḿêśĉŕêén̂, án̂d́ p̂úŝh́ n̂ót̂íf̂íĉát̂íôńŝ. [Ĺêár̂ń m̂ór̂é](https://web.dev/service-worker)."}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "T̂h́îś p̂áĝé îś ĉón̂t́r̂ól̂ĺêd́ b̂ý â śêŕv̂íĉé ŵór̂ḱêŕ, ĥóŵév̂ér̂ ńô `start_url` ẃâś f̂óûńd̂ b́êćâúŝé m̂án̂íf̂éŝt́ f̂áîĺêd́ t̂ó p̂ár̂śê áŝ v́âĺîd́ ĴŚÔŃ"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "T̂h́îś p̂áĝé îś ĉón̂t́r̂ól̂ĺêd́ b̂ý â śêŕv̂íĉé ŵór̂ḱêŕ, ĥóŵév̂ér̂ t́ĥé `start_url` ({startUrl}) îś n̂ót̂ ín̂ t́ĥé ŝér̂v́îćê ẃôŕk̂ér̂'ś ŝćôṕê ({scopeUrl})"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "T̂h́îś p̂áĝé îś ĉón̂t́r̂ól̂ĺêd́ b̂ý â śêŕv̂íĉé ŵór̂ḱêŕ, ĥóŵév̂ér̂ ńô `start_url` ẃâś f̂óûńd̂ b́êćâúŝé n̂ó m̂án̂íf̂éŝt́ ŵáŝ f́êt́ĉh́êd́."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "T̂h́îś ôŕîǵîń ĥáŝ ón̂é ôŕ m̂ór̂é ŝér̂v́îćê ẃôŕk̂ér̂ś, ĥóŵév̂ér̂ t́ĥé p̂áĝé ({pageUrl}) îś n̂ót̂ ín̂ śĉóp̂é."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "D̂óêś n̂ót̂ ŕêǵîśt̂ér̂ á ŝér̂v́îćê ẃôŕk̂ér̂ t́ĥát̂ ćôńt̂ŕôĺŝ ṕâǵê án̂d́ `start_url`"}, "lighthouse-core/audits/service-worker.js | title": {"message": "R̂éĝíŝt́êŕŝ á ŝér̂v́îćê ẃôŕk̂ér̂ t́ĥát̂ ćôńt̂ŕôĺŝ ṕâǵê án̂d́ `start_url`"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "Â t́ĥém̂éd̂ śp̂ĺâśĥ śĉŕêén̂ én̂śûŕêś â h́îǵĥ-q́ûál̂ít̂ý êx́p̂ér̂íêńĉé ŵh́êń ûśêŕŝ ĺâún̂ćĥ ýôúr̂ áp̂ṕ f̂ŕôḿ t̂h́êír̂ h́ôḿêśĉŕêén̂ś. [L̂éâŕn̂ ḿôŕê](https://web.dev/splash-screen)."}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "Îś n̂ót̂ ćôńf̂íĝúr̂éd̂ f́ôŕ â ćûśt̂óm̂ śp̂ĺâśĥ śĉŕêén̂"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "Ĉón̂f́îǵûŕêd́ f̂ór̂ á ĉúŝt́ôḿ ŝṕl̂áŝh́ ŝćr̂éêń"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "T̂h́ê b́r̂óŵśêŕ âd́d̂ŕêśŝ b́âŕ ĉán̂ b́ê t́ĥém̂éd̂ t́ô ḿât́ĉh́ ŷóûŕ ŝít̂é. [L̂éâŕn̂ ḿôŕê](https://web.dev/themed-omnibox)."}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "D̂óêś n̂ót̂ śêt́ â t́ĥém̂é ĉól̂ór̂ f́ôŕ t̂h́ê ád̂d́r̂éŝś b̂ár̂."}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "Ŝét̂ś â t́ĥém̂é ĉól̂ór̂ f́ôŕ t̂h́ê ád̂d́r̂éŝś b̂ár̂."}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "M̂áîń-T̂h́r̂éâd́ B̂ĺôćk̂ín̂ǵ T̂ím̂é"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "T̂h́îŕd̂-Ṕâŕt̂ý"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "T̂h́îŕd̂-ṕâŕt̂ý ĉód̂é ĉán̂ śîǵn̂íf̂íĉán̂t́l̂ý îḿp̂áĉt́ l̂óâd́ p̂ér̂f́ôŕm̂án̂ćê. Ĺîḿît́ t̂h́ê ńûḿb̂ér̂ óf̂ ŕêd́ûńd̂án̂t́ t̂h́îŕd̂-ṕâŕt̂ý p̂ŕôv́îd́êŕŝ án̂d́ t̂ŕŷ t́ô ĺôád̂ t́ĥír̂d́-p̂ár̂t́ŷ ćôd́ê áf̂t́êŕ ŷóûŕ p̂áĝé ĥáŝ ṕr̂ím̂ár̂íl̂ý f̂ín̂íŝh́êd́ l̂óâd́îńĝ. [Ĺêár̂ń m̂ór̂é](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "T̂h́îŕd̂-ṕâŕt̂ý ĉód̂é b̂ĺôćk̂éd̂ t́ĥé m̂áîń t̂h́r̂éâd́ f̂ór̂ {timeInMs, number, milliseconds} ḿŝ"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "R̂éd̂úĉé t̂h́ê ím̂ṕâćt̂ óf̂ t́ĥír̂d́-p̂ár̂t́ŷ ćôd́ê"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "M̂ín̂ím̂íẑé t̂h́îŕd̂-ṕâŕt̂ý ûśâǵê"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "T̂ím̂é T̂ó F̂ír̂śt̂ B́ŷt́ê íd̂én̂t́îf́îéŝ t́ĥé t̂ím̂é ât́ ŵh́îćĥ ýôúr̂ śêŕv̂ér̂ śêńd̂ś â ŕêśp̂ón̂śê. [Ĺêár̂ń m̂ór̂é](https://web.dev/time-to-first-byte)."}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "R̂óôt́ d̂óĉúm̂én̂t́ t̂óôḱ {timeInMs, number, milliseconds} m̂ś"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "R̂éd̂úĉé ŝér̂v́êŕ r̂éŝṕôńŝé t̂ím̂éŝ (T́T̂F́B̂)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "Ŝér̂v́êŕ r̂éŝṕôńŝé t̂ím̂éŝ ár̂é l̂óŵ (T́T̂F́B̂)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "D̂úr̂át̂íôń"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "Ŝt́âŕt̂ T́îḿê"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "T̂ýp̂é"}, "lighthouse-core/audits/user-timings.js | description": {"message": "Ĉón̂śîd́êŕ îńŝt́r̂úm̂én̂t́îńĝ ýôúr̂ áp̂ṕ ŵít̂h́ t̂h́ê Úŝér̂ T́îḿîńĝ ÁP̂Í t̂ó m̂éâśûŕê ýôúr̂ áp̂ṕ'ŝ ŕêál̂-ẃôŕl̂d́ p̂ér̂f́ôŕm̂án̂ćê d́ûŕîńĝ ḱêý ûśêŕ êx́p̂ér̂íêńĉéŝ. [Ĺêár̂ń m̂ór̂é](https://web.dev/user-timings)."}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount, plural,\n    =1 {1 ûśêŕ t̂ím̂ín̂ǵ}\n    other {# ûśêŕ t̂ím̂ín̂ǵŝ}\n    }"}, "lighthouse-core/audits/user-timings.js | title": {"message": "Ûśêŕ T̂ím̂ín̂ǵ m̂ár̂ḱŝ án̂d́ m̂éâśûŕêś"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Â ṕr̂éĉón̂ńêćt̂ <ĺîńk̂> ẃâś f̂óûńd̂ f́ôŕ \"{security<PERSON><PERSON><PERSON>}\" b̂út̂ ẃâś n̂ót̂ úŝéd̂ b́ŷ t́ĥé b̂ŕôẃŝér̂. Ćĥéĉḱ t̂h́ât́ ŷóû ár̂é ûśîńĝ t́ĥé `crossorigin` ât́t̂ŕîb́ût́ê ṕr̂óp̂ér̂ĺŷ."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "Ĉón̂śîd́êŕ âd́d̂ín̂ǵ `preconnect` ôŕ `dns-prefetch` r̂éŝóûŕĉé ĥín̂t́ŝ t́ô éŝt́âb́l̂íŝh́ êár̂ĺŷ ćôńn̂éĉt́îón̂ś t̂ó îḿp̂ór̂t́âńt̂ t́ĥír̂d́-p̂ár̂t́ŷ ór̂íĝín̂ś. [L̂éâŕn̂ ḿôŕê](https://web.dev/uses-rel-preconnect)."}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "P̂ŕêćôńn̂éĉt́ t̂ó r̂éq̂úîŕêd́ ôŕîǵîńŝ"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Â ṕr̂él̂óâd́ <l̂ín̂ḱ> ŵáŝ f́ôún̂d́ f̂ór̂ \"{preloadURL}\" b́ût́ ŵáŝ ńôt́ ûśêd́ b̂ý t̂h́ê b́r̂óŵśêŕ. Ĉh́êćk̂ t́ĥát̂ ýôú âŕê úŝín̂ǵ t̂h́ê `crossorigin` át̂t́r̂íb̂út̂é p̂ŕôṕêŕl̂ý."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "Ĉón̂śîd́êŕ ûśîńĝ `<link rel=preload>` t́ô ṕr̂íôŕît́îźê f́êt́ĉh́îńĝ ŕêśôúr̂ćêś t̂h́ât́ âŕê ćûŕr̂én̂t́l̂ý r̂éq̂úêśt̂éd̂ ĺât́êŕ îń p̂áĝé l̂óâd́. [L̂éâŕn̂ ḿôŕê](https://web.dev/uses-rel-preload)."}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "P̂ŕêĺôád̂ ḱêý r̂éq̂úêśt̂ś"}, "lighthouse-core/audits/viewport.js | description": {"message": "Âd́d̂ á `<meta name=\"viewport\">` t̂áĝ t́ô óp̂t́îḿîźê ýôúr̂ áp̂ṕ f̂ór̂ ḿôb́îĺê śĉŕêén̂ś. [L̂éâŕn̂ ḿôŕê](https://web.dev/viewport)."}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "N̂ó `<meta name=\"viewport\">` t̂áĝ f́ôún̂d́"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "D̂óêś n̂ót̂ h́âv́ê á `<meta name=\"viewport\">` t̂áĝ ẃît́ĥ `width` ór̂ `initial-scale`"}, "lighthouse-core/audits/viewport.js | title": {"message": "Ĥáŝ á `<meta name=\"viewport\">` t̂áĝ ẃît́ĥ `width` ór̂ `initial-scale`"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "Ŷóûŕ âṕp̂ śĥóûĺd̂ d́îśp̂ĺâý ŝóm̂é ĉón̂t́êńt̂ ẃĥén̂ J́âv́âŚĉŕîṕt̂ íŝ d́îśâb́l̂éd̂, év̂én̂ íf̂ ít̂'ś ĵúŝt́ â ẃâŕn̂ín̂ǵ t̂ó t̂h́ê úŝér̂ t́ĥát̂ J́âv́âŚĉŕîṕt̂ íŝ ŕêq́ûír̂éd̂ t́ô úŝé t̂h́ê áp̂ṕ. [L̂éâŕn̂ ḿôŕê](https://web.dev/without-javascript)."}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "T̂h́ê ṕâǵê b́ôd́ŷ śĥóûĺd̂ ŕêńd̂ér̂ śôḿê ćôńt̂én̂t́ îf́ ît́ŝ śĉŕîṕt̂ś âŕê ńôt́ âv́âíl̂áb̂ĺê."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "D̂óêś n̂ót̂ ṕr̂óv̂íd̂é f̂ál̂ĺb̂áĉḱ ĉón̂t́êńt̂ ẃĥén̂ J́âv́âŚĉŕîṕt̂ íŝ ńôt́ âv́âíl̂áb̂ĺê"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "Ĉón̂t́âín̂ś ŝóm̂é ĉón̂t́êńt̂ ẃĥén̂ J́âv́âŚĉŕîṕt̂ íŝ ńôt́ âv́âíl̂áb̂ĺê"}, "lighthouse-core/audits/works-offline.js | description": {"message": "Îf́ ŷóû'ŕê b́ûíl̂d́îńĝ á P̂ŕôǵr̂éŝśîv́ê Ẃêb́ Âṕp̂, ćôńŝíd̂ér̂ úŝín̂ǵ â śêŕv̂íĉé ŵór̂ḱêŕ ŝó t̂h́ât́ ŷóûŕ âṕp̂ ćâń ŵór̂ḱ ôf́f̂ĺîńê. [Ĺêár̂ń m̂ór̂é](https://web.dev/works-offline)."}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "Ĉúr̂ŕêńt̂ ṕâǵê d́ôéŝ ńôt́ r̂éŝṕôńd̂ ẃît́ĥ á 200 ŵh́êń ôf́f̂ĺîńê"}, "lighthouse-core/audits/works-offline.js | title": {"message": "Ĉúr̂ŕêńt̂ ṕâǵê ŕêśp̂ón̂d́ŝ ẃît́ĥ á 200 ŵh́êń ôf́f̂ĺîńê"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "T̂h́ê ṕâǵê ḿâý n̂ót̂ b́ê ĺôád̂ín̂ǵ ôf́f̂ĺîńê b́êćâúŝé ŷóûŕ t̂éŝt́ ÛŔL̂ ({requested}) ẃâś r̂éd̂ír̂éĉt́êd́ t̂ó \"{final}\". T̂ŕŷ t́êśt̂ín̂ǵ t̂h́ê śêćôńd̂ ÚR̂Ĺ d̂ír̂éĉt́l̂ý."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "T̂h́êśê ár̂é ôṕp̂ór̂t́ûńît́îéŝ t́ô ím̂ṕr̂óv̂é t̂h́ê úŝáĝé ôf́ ÂŔÎÁ îń ŷóûŕ âṕp̂ĺîćât́îón̂ ẃĥíĉh́ m̂áŷ én̂h́âńĉé t̂h́ê éx̂ṕêŕîén̂ćê f́ôŕ ûśêŕŝ óf̂ áŝśîśt̂ív̂é t̂éĉh́n̂ól̂óĝý, l̂ík̂é â śĉŕêén̂ ŕêád̂ér̂."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ÂŔÎÁ"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "T̂h́êśê ár̂é ôṕp̂ór̂t́ûńît́îéŝ t́ô ṕr̂óv̂íd̂é âĺt̂ér̂ńât́îv́ê ćôńt̂én̂t́ f̂ór̂ áûd́îó âńd̂ v́îd́êó. T̂h́îś m̂áŷ ím̂ṕr̂óv̂é t̂h́ê éx̂ṕêŕîén̂ćê f́ôŕ ûśêŕŝ ẃît́ĥ h́êár̂ín̂ǵ ôŕ v̂íŝíôń îḿp̂áîŕm̂én̂t́ŝ."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Âúd̂íô án̂d́ v̂íd̂éô"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "T̂h́êśê ít̂ém̂ś ĥíĝh́l̂íĝh́t̂ ćôḿm̂ón̂ áĉćêśŝíb̂íl̂ít̂ý b̂éŝt́ p̂ŕâćt̂íĉéŝ."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "B̂éŝt́ p̂ŕâćt̂íĉéŝ"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "T̂h́êśê ćĥéĉḱŝ h́îǵĥĺîǵĥt́ ôṕp̂ór̂t́ûńît́îéŝ t́ô [ím̂ṕr̂óv̂é t̂h́ê áĉćêśŝíb̂íl̂ít̂ý ôf́ ŷóûŕ ŵéb̂ áp̂ṕ](https://developers.google.com/web/fundamentals/accessibility). Ôńl̂ý â śûb́ŝét̂ óf̂ áĉćêśŝíb̂íl̂ít̂ý îśŝúêś ĉán̂ b́ê áût́ôḿât́îćâĺl̂ý d̂ét̂éĉt́êd́ ŝó m̂án̂úâĺ t̂éŝt́îńĝ íŝ ál̂śô én̂ćôúr̂áĝéd̂."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "T̂h́êśê ít̂ém̂ś âd́d̂ŕêśŝ ár̂éâś ŵh́îćĥ án̂ áût́ôḿât́êd́ t̂éŝt́îńĝ t́ôól̂ ćâńn̂ót̂ ćôv́êŕ. L̂éâŕn̂ ḿôŕê ín̂ óûŕ ĝúîd́ê ón̂ [ćôńd̂úĉt́îńĝ án̂ áĉćêśŝíb̂íl̂ít̂ý r̂év̂íêẃ](https://developers.google.com/web/fundamentals/accessibility/how-to-review)."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "Âćĉéŝśîb́îĺît́ŷ"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "T̂h́êśê ár̂é ôṕp̂ór̂t́ûńît́îéŝ t́ô ím̂ṕr̂óv̂é t̂h́ê ĺêǵîb́îĺît́ŷ óf̂ ýôúr̂ ćôńt̂én̂t́."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Ĉón̂t́r̂áŝt́"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "T̂h́êśê ár̂é ôṕp̂ór̂t́ûńît́îéŝ t́ô ím̂ṕr̂óv̂é t̂h́ê ín̂t́êŕp̂ŕêt́ât́îón̂ óf̂ ýôúr̂ ćôńt̂én̂t́ b̂ý ûśêŕŝ ín̂ d́îf́f̂ér̂én̂t́ l̂óĉál̂éŝ."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Îńt̂ér̂ńât́îón̂ál̂íẑát̂íôń âńd̂ ĺôćâĺîźât́îón̂"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "T̂h́êśê ár̂é ôṕp̂ór̂t́ûńît́îéŝ t́ô ím̂ṕr̂óv̂é t̂h́ê śêḿâńt̂íĉś ôf́ t̂h́ê ćôńt̂ŕôĺŝ ín̂ ýôúr̂ áp̂ṕl̂íĉát̂íôń. T̂h́îś m̂áŷ én̂h́âńĉé t̂h́ê éx̂ṕêŕîén̂ćê f́ôŕ ûśêŕŝ óf̂ áŝśîśt̂ív̂é t̂éĉh́n̂ól̂óĝý, l̂ík̂é â śĉŕêén̂ ŕêád̂ér̂."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "N̂ám̂éŝ án̂d́ l̂áb̂él̂ś"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "T̂h́êśê ár̂é ôṕp̂ór̂t́ûńît́îéŝ t́ô ím̂ṕr̂óv̂é k̂éŷb́ôár̂d́ n̂áv̂íĝát̂íôń îń ŷóûŕ âṕp̂ĺîćât́îón̂."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "N̂áv̂íĝát̂íôń"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "T̂h́êśê ár̂é ôṕp̂ór̂t́ûńît́îéŝ t́ô t́ô ím̂ṕr̂óv̂é t̂h́ê éx̂ṕêŕîén̂ćê óf̂ ŕêád̂ín̂ǵ t̂áb̂úl̂ár̂ ór̂ ĺîśt̂ d́ât́â úŝín̂ǵ âśŝíŝt́îv́ê t́êćĥńôĺôǵŷ, ĺîḱê á ŝćr̂éêń r̂éâd́êŕ."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "T̂áb̂ĺêś âńd̂ ĺîśt̂ś"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "B̂éŝt́ P̂ŕâćt̂íĉéŝ"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "P̂ér̂f́ôŕm̂án̂ćê b́ûd́ĝét̂ś ŝét̂ śt̂án̂d́âŕd̂ś f̂ór̂ t́ĥé p̂ér̂f́ôŕm̂án̂ćê óf̂ ýôúr̂ śît́ê."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "B̂úd̂ǵêt́ŝ"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "M̂ór̂é îńf̂ór̂ḿât́îón̂ áb̂óût́ t̂h́ê ṕêŕf̂ór̂ḿâńĉé ôf́ ŷóûŕ âṕp̂ĺîćât́îón̂. T́ĥéŝé n̂úm̂b́êŕŝ d́ôń't̂ [d́îŕêćt̂ĺŷ áf̂f́êćt̂](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) t́ĥé P̂ér̂f́ôŕm̂án̂ćê śĉór̂é."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "D̂íâǵn̂óŝt́îćŝ"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "T̂h́ê ḿôśt̂ ćr̂ít̂íĉál̂ áŝṕêćt̂ óf̂ ṕêŕf̂ór̂ḿâńĉé îś ĥóŵ q́ûíĉḱl̂ý p̂íx̂él̂ś âŕê ŕêńd̂ér̂éd̂ ón̂śĉŕêén̂. Ḱêý m̂ét̂ŕîćŝ: F́îŕŝt́ Ĉón̂t́êńt̂f́ûĺ P̂áîńt̂, F́îŕŝt́ M̂éâńîńĝf́ûĺ P̂áîńt̂"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "F̂ír̂śt̂ Ṕâín̂t́ Îḿp̂ŕôv́êḿêńt̂ś"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "T̂h́êśê śûǵĝéŝt́îón̂ś ĉán̂ h́êĺp̂ ýôúr̂ ṕâǵê ĺôád̂ f́âśt̂ér̂. T́ĥéŷ d́ôń't̂ [d́îŕêćt̂ĺŷ áf̂f́êćt̂](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) t́ĥé P̂ér̂f́ôŕm̂án̂ćê śĉór̂é."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Ôṕp̂ór̂t́ûńît́îéŝ"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "M̂ét̂ŕîćŝ"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Êńĥán̂ćê t́ĥé ôv́êŕâĺl̂ ĺôád̂ín̂ǵ êx́p̂ér̂íêńĉé, ŝó t̂h́ê ṕâǵê íŝ ŕêśp̂ón̂śîv́ê án̂d́ r̂éâd́ŷ t́ô úŝé âś ŝóôń âś p̂óŝśîb́l̂é. K̂éŷ ḿêt́r̂íĉś: T̂ím̂é t̂ó Îńt̂ér̂áĉt́îv́ê, Śp̂éêd́ Îńd̂éx̂"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Ôv́êŕâĺl̂ Ím̂ṕr̂óv̂ém̂én̂t́ŝ"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "P̂ér̂f́ôŕm̂án̂ćê"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "T̂h́êśê ćĥéĉḱŝ v́âĺîd́ât́ê t́ĥé âśp̂éĉt́ŝ óf̂ á P̂ŕôǵr̂éŝśîv́ê Ẃêb́ Âṕp̂. [Ĺêár̂ń m̂ór̂é](https://developers.google.com/web/progressive-web-apps/checklist)."}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "T̂h́êśê ćĥéĉḱŝ ár̂é r̂éq̂úîŕêd́ b̂ý t̂h́ê b́âśêĺîńê [ṔŴÁ Ĉh́êćk̂ĺîśt̂](https://developers.google.com/web/progressive-web-apps/checklist) b́ût́ âŕê ńôt́ âút̂óm̂át̂íĉál̂ĺŷ ćĥéĉḱêd́ b̂ý L̂íĝh́t̂h́ôúŝé. T̂h́êý d̂ó n̂ót̂ áf̂f́êćt̂ ýôúr̂ śĉór̂é b̂út̂ ít̂'ś îḿp̂ór̂t́âńt̂ t́ĥát̂ ýôú v̂ér̂íf̂ý t̂h́êḿ m̂án̂úâĺl̂ý."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "P̂ŕôǵr̂éŝśîv́ê Ẃêb́ Âṕp̂"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "F̂áŝt́ âńd̂ ŕêĺîáb̂ĺê"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Îńŝt́âĺl̂áb̂ĺê"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "P̂ẂÂ Óp̂t́îḿîźêd́"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "T̂h́êśê ćĥéĉḱŝ én̂śûŕê t́ĥát̂ ýôúr̂ ṕâǵê íŝ óp̂t́îḿîźêd́ f̂ór̂ śêár̂ćĥ én̂ǵîńê ŕêśûĺt̂ś r̂án̂ḱîńĝ. T́ĥér̂é âŕê ád̂d́ît́îón̂ál̂ f́âćt̂ór̂ś L̂íĝh́t̂h́ôúŝé d̂óêś n̂ót̂ ćĥéĉḱ t̂h́ât́ m̂áŷ áf̂f́êćt̂ ýôúr̂ śêár̂ćĥ ŕâńk̂ín̂ǵ. [L̂éâŕn̂ ḿôŕê](https://support.google.com/webmasters/answer/35769)."}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "R̂ún̂ t́ĥéŝé âd́d̂ít̂íôńâĺ v̂ál̂íd̂át̂ór̂ś ôń ŷóûŕ ŝít̂é t̂ó ĉh́êćk̂ ád̂d́ît́îón̂ál̂ ŚÊÓ b̂éŝt́ p̂ŕâćt̂íĉéŝ."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "ŜÉÔ"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "F̂ór̂ḿât́ ŷóûŕ ĤT́M̂Ĺ îń â ẃâý t̂h́ât́ êńâb́l̂éŝ ćr̂áŵĺêŕŝ t́ô b́êt́t̂ér̂ ún̂d́êŕŝt́âńd̂ ýôúr̂ áp̂ṕ’ŝ ćôńt̂én̂t́."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "Ĉón̂t́êńt̂ B́êśt̂ Ṕr̂áĉt́îćêś"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "T̂ó âṕp̂éâŕ îń ŝéâŕĉh́ r̂éŝúl̂t́ŝ, ćr̂áŵĺêŕŝ ńêéd̂ áĉćêśŝ t́ô ýôúr̂ áp̂ṕ."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Ĉŕâẃl̂ín̂ǵ âńd̂ Ín̂d́êx́îńĝ"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "M̂ák̂é ŝúr̂é ŷóûŕ p̂áĝéŝ ár̂é m̂ób̂íl̂é f̂ŕîén̂d́l̂ý ŝó ûśêŕŝ d́ôń’t̂ h́âv́ê t́ô ṕîńĉh́ ôŕ ẑóôḿ îń ôŕd̂ér̂ t́ô ŕêád̂ t́ĥé ĉón̂t́êńt̂ ṕâǵêś. [L̂éâŕn̂ ḿôŕê](https://developers.google.com/search/mobile-sites/)."}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "M̂ób̂íl̂é F̂ŕîén̂d́l̂ý"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Ĉáĉh́ê T́T̂Ĺ"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "L̂óĉát̂íôń"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "N̂ám̂é"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "R̂éq̂úêśt̂ś"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "R̂éŝóûŕĉé T̂ýp̂é"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "Ŝíẑé"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "T̂ím̂é Ŝṕêńt̂"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "T̂ŕâńŝf́êŕ Ŝíẑé"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "ÛŔL̂"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "P̂ót̂én̂t́îál̂ Śâv́îńĝś"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "P̂ót̂én̂t́îál̂ Śâv́îńĝś"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "P̂ót̂én̂t́îál̂ śâv́îńĝś ôf́ {wastedBytes, number, bytes} K̂B́"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "P̂ót̂én̂t́îál̂ śâv́îńĝś ôf́ {wastedMs, number, milliseconds} m̂ś"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "D̂óĉúm̂én̂t́"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "F̂ón̂t́"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "Îḿâǵê"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "M̂éd̂íâ"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} m̂ś"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "Ôt́ĥér̂"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "Ŝćr̂íp̂t́"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} ŝ"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Ŝt́ŷĺêśĥéêt́"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "T̂h́îŕd̂-ṕâŕt̂ý"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "T̂ót̂ál̂"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "Ŝóm̂ét̂h́îńĝ ẃêńt̂ ẃr̂ón̂ǵ ŵít̂h́ r̂éĉór̂d́îńĝ t́ĥé t̂ŕâćê óv̂ér̂ ýôúr̂ ṕâǵê ĺôád̂. Ṕl̂éâśê ŕûń L̂íĝh́t̂h́ôúŝé âǵâín̂. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "T̂ím̂éôút̂ ẃâít̂ín̂ǵ f̂ór̂ ín̂ít̂íâĺ D̂éb̂úĝǵêŕ P̂ŕôt́ôćôĺ ĉón̂ńêćt̂íôń."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "Ĉh́r̂óm̂é d̂íd̂ń't̂ ćôĺl̂éĉt́ âńŷ śĉŕêén̂śĥót̂ś d̂úr̂ín̂ǵ t̂h́ê ṕâǵê ĺôád̂. Ṕl̂éâśê ḿâḱê śûŕê t́ĥér̂é îś ĉón̂t́êńt̂ v́îśîb́l̂é ôń t̂h́ê ṕâǵê, án̂d́ t̂h́êń t̂ŕŷ ŕê-ŕûńn̂ín̂ǵ L̂íĝh́t̂h́ôúŝé. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "D̂ŃŜ śêŕv̂ér̂ś ĉóûĺd̂ ńôt́ r̂éŝól̂v́ê t́ĥé p̂ŕôv́îd́êd́ d̂óm̂áîń."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "R̂éq̂úîŕêd́ {artifactName} ĝát̂h́êŕêŕ êńĉóûńt̂ér̂éd̂ án̂ ér̂ŕôŕ: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "Âń îńt̂ér̂ńâĺ Ĉh́r̂óm̂é êŕr̂ór̂ óĉćûŕr̂éd̂. Ṕl̂éâśê ŕêśt̂ár̂t́ Ĉh́r̂óm̂é âńd̂ t́r̂ý r̂é-r̂ún̂ńîńĝ Ĺîǵĥt́ĥóûśê."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "R̂éq̂úîŕêd́ {artifactName} ĝát̂h́êŕêŕ d̂íd̂ ńôt́ r̂ún̂."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "L̂íĝh́t̂h́ôúŝé ŵáŝ ún̂áb̂ĺê t́ô ŕêĺîáb̂ĺŷ ĺôád̂ t́ĥé p̂áĝé ŷóû ŕêq́ûéŝt́êd́. M̂ák̂é ŝúr̂é ŷóû ár̂é t̂éŝt́îńĝ t́ĥé ĉór̂ŕêćt̂ ÚR̂Ĺ âńd̂ t́ĥát̂ t́ĥé ŝér̂v́êŕ îś p̂ŕôṕêŕl̂ý r̂éŝṕôńd̂ín̂ǵ t̂ó âĺl̂ ŕêq́ûéŝt́ŝ."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "L̂íĝh́t̂h́ôúŝé ŵáŝ ún̂áb̂ĺê t́ô ŕêĺîáb̂ĺŷ ĺôád̂ t́ĥé ÛŔL̂ ýôú r̂éq̂úêśt̂éd̂ b́êćâúŝé t̂h́ê ṕâǵê śt̂óp̂ṕêd́ r̂éŝṕôńd̂ín̂ǵ."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "T̂h́ê ÚR̂Ĺ ŷóû h́âv́ê ṕr̂óv̂íd̂éd̂ d́ôéŝ ńôt́ ĥáv̂é â v́âĺîd́ ŝéĉúr̂ít̂ý ĉér̂t́îf́îćât́ê. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Ĉh́r̂óm̂é p̂ŕêv́êńt̂éd̂ ṕâǵê ĺôád̂ ẃît́ĥ án̂ ín̂t́êŕŝt́ît́îál̂. Ḿâḱê śûŕê ýôú âŕê t́êśt̂ín̂ǵ t̂h́ê ćôŕr̂éĉt́ ÛŔL̂ án̂d́ t̂h́ât́ t̂h́ê śêŕv̂ér̂ íŝ ṕr̂óp̂ér̂ĺŷ ŕêśp̂ón̂d́îńĝ t́ô ál̂ĺ r̂éq̂úêśt̂ś."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "L̂íĝh́t̂h́ôúŝé ŵáŝ ún̂áb̂ĺê t́ô ŕêĺîáb̂ĺŷ ĺôád̂ t́ĥé p̂áĝé ŷóû ŕêq́ûéŝt́êd́. M̂ák̂é ŝúr̂é ŷóû ár̂é t̂éŝt́îńĝ t́ĥé ĉór̂ŕêćt̂ ÚR̂Ĺ âńd̂ t́ĥát̂ t́ĥé ŝér̂v́êŕ îś p̂ŕôṕêŕl̂ý r̂éŝṕôńd̂ín̂ǵ t̂ó âĺl̂ ŕêq́ûéŝt́ŝ. (D́êt́âíl̂ś: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "L̂íĝh́t̂h́ôúŝé ŵáŝ ún̂áb̂ĺê t́ô ŕêĺîáb̂ĺŷ ĺôád̂ t́ĥé p̂áĝé ŷóû ŕêq́ûéŝt́êd́. M̂ák̂é ŝúr̂é ŷóû ár̂é t̂éŝt́îńĝ t́ĥé ĉór̂ŕêćt̂ ÚR̂Ĺ âńd̂ t́ĥát̂ t́ĥé ŝér̂v́êŕ îś p̂ŕôṕêŕl̂ý r̂éŝṕôńd̂ín̂ǵ t̂ó âĺl̂ ŕêq́ûéŝt́ŝ. (Śt̂át̂úŝ ćôd́ê: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Ŷóûŕ p̂áĝé t̂óôḱ t̂óô ĺôńĝ t́ô ĺôád̂. Ṕl̂éâśê f́ôĺl̂óŵ t́ĥé ôṕp̂ór̂t́ûńît́îéŝ ín̂ t́ĥé r̂ép̂ór̂t́ t̂ó r̂éd̂úĉé ŷóûŕ p̂áĝé l̂óâd́ t̂ím̂é, âńd̂ t́ĥén̂ t́r̂ý r̂é-r̂ún̂ńîńĝ Ĺîǵĥt́ĥóûśê. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "Ŵáît́îńĝ f́ôŕ D̂év̂T́ôól̂ś p̂ŕôt́ôćôĺ r̂éŝṕôńŝé ĥáŝ éx̂ćêéd̂éd̂ t́ĥé âĺl̂ót̂t́êd́ t̂ím̂é. (M̂ét̂h́ôd́: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "F̂ét̂ćĥín̂ǵ r̂éŝóûŕĉé ĉón̂t́êńt̂ h́âś êx́ĉéêd́êd́ t̂h́ê ál̂ĺôt́t̂éd̂ t́îḿê"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "T̂h́ê ÚR̂Ĺ ŷóû h́âv́ê ṕr̂óv̂íd̂éd̂ áp̂ṕêár̂ś t̂ó b̂é îńv̂ál̂íd̂."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "Ŝh́ôẃ âúd̂ít̂ś"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "Îńît́îál̂ Ńâv́îǵât́îón̂"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "M̂áx̂ím̂úm̂ ćr̂ít̂íĉál̂ ṕât́ĥ ĺât́êńĉý:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "Êŕr̂ór̂!"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "R̂ép̂ór̂t́ êŕr̂ór̂: ńô áûd́ît́ îńf̂ór̂ḿât́îón̂"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "L̂áb̂ D́ât́â"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "[L̂íĝh́t̂h́ôúŝé](https://developers.google.com/web/tools/lighthouse/) âńâĺŷśîś ôf́ t̂h́ê ćûŕr̂én̂t́ p̂áĝé ôń âń êḿûĺât́êd́ m̂ób̂íl̂é n̂ét̂ẃôŕk̂. V́âĺûéŝ ár̂é êśt̂ím̂át̂éd̂ án̂d́ m̂áŷ v́âŕŷ."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "Âd́d̂ít̂íôńâĺ ît́êḿŝ t́ô ḿâńûál̂ĺŷ ćĥéĉḱ"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "N̂ót̂ áp̂ṕl̂íĉáb̂ĺê"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "Ôṕp̂ór̂t́ûńît́ŷ"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "Êśt̂ím̂át̂éd̂ Śâv́îńĝś"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "P̂áŝśêd́ âúd̂ít̂ś"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "Ĉól̂ĺâṕŝé ŝńîṕp̂ét̂"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "Êx́p̂án̂d́ ŝńîṕp̂ét̂"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "Ŝh́ôẃ 3r̂d́-p̂ár̂t́ŷ ŕêśôúr̂ćêś"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "T̂h́êŕê ẃêŕê íŝśûéŝ áf̂f́êćt̂ín̂ǵ t̂h́îś r̂ún̂ óf̂ Ĺîǵĥt́ĥóûśê:"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "V̂ál̂úêś âŕê éŝt́îḿât́êd́ âńd̂ ḿâý v̂ár̂ý. T̂h́ê ṕêŕf̂ór̂ḿâńĉé ŝćôŕê íŝ [b́âśêd́ ôńl̂ý ôń t̂h́êśê ḿêt́r̂íĉś](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "P̂áŝśêd́ âúd̂ít̂ś b̂út̂ ẃît́ĥ ẃâŕn̂ín̂ǵŝ"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "Ŵár̂ńîńĝś: "}, "stack-packs/packs/amp.js | efficient_animated_content": {"message": "F̂ór̂ án̂ím̂át̂éd̂ ćôńt̂én̂t́, ûśê [ám̂ṕ-âńîḿ](https://amp.dev/documentation/components/amp-anim/) t̂ó m̂ín̂ím̂íẑé ĈṔÛ úŝáĝé ŵh́îĺê t́ĥé ĉón̂t́êńt̂ ŕêḿâín̂ś ôf́f̂śĉŕêén̂."}, "stack-packs/packs/amp.js | offscreen_images": {"message": "Êńŝúr̂é t̂h́ât́ ŷóû ár̂é ŷóû úŝín̂ǵ v̂ál̂íd̂ `amp-img` t́âǵŝ f́ôŕ ŷóûŕ îḿâǵêś ŵh́îćĥ áût́ôḿât́îćâĺl̂ý l̂áẑý-l̂óâd́ ôút̂śîd́ê t́ĥé f̂ír̂śt̂ v́îéŵṕôŕt̂. [Ĺêár̂ń m̂ór̂é](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "stack-packs/packs/amp.js | render_blocking_resources": {"message": "Ûśê t́ôól̂ś ŝúĉh́ âś [ÂḾP̂ Óp̂t́îḿîźêŕ](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) t̂ó [ŝér̂v́êŕ-ŝíd̂é r̂én̂d́êŕ ÂḾP̂ ĺâýôút̂ś](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "stack-packs/packs/amp.js | unminified_css": {"message": "R̂éf̂ér̂ t́ô t́ĥé [ÂḾP̂ d́ôćûḿêńt̂át̂íôń](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) t̂ó êńŝúr̂é âĺl̂ ýôúr̂ śt̂ýl̂éŝ ár̂é ŝúp̂ṕôŕt̂éd̂."}, "stack-packs/packs/amp.js | uses_responsive_images": {"message": "T̂h́ê `amp-img` él̂ém̂én̂t́ ŝúp̂ṕôŕt̂ś t̂h́ê `srcset` át̂t́r̂íb̂út̂é t̂ó ŝṕêćîf́ŷ ẃĥíĉh́ îḿâǵê áŝśêt́ŝ t́ô úŝé b̂áŝéd̂ ón̂ t́ĥé ŝćr̂éêń ŝíẑé.  [L̂éâŕn̂ ḿôŕê](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "stack-packs/packs/amp.js | uses_webp_images": {"message": "Ĉón̂śîd́êŕ d̂íŝṕl̂áŷín̂ǵ âĺl̂ ýôúr̂ `amp-img` ćôḿp̂ón̂én̂t́ŝ ín̂ Ẃêb́P̂ f́ôŕm̂át̂ś ŵh́îĺê śp̂éĉíf̂ýîńĝ án̂ áp̂ṕr̂óp̂ŕîát̂é f̂ál̂ĺb̂áĉḱ f̂ór̂ ót̂h́êŕ b̂ŕôẃŝér̂ś. [L̂éâŕn̂ ḿôŕê](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "stack-packs/packs/angular.js | dom_size": {"message": "Ĉón̂śîd́êŕ v̂ír̂t́ûál̂ śĉŕôĺl̂ín̂ǵ ŵít̂h́ t̂h́ê Ćôḿp̂ón̂én̂t́ D̂év̂ Ḱît́ (ĈD́K̂) íf̂ v́êŕŷ ĺâŕĝé l̂íŝt́ŝ ár̂é b̂éîńĝ ŕêńd̂ér̂éd̂. [Ĺêár̂ń m̂ór̂é](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "stack-packs/packs/angular.js | total_byte_weight": {"message": "Âṕp̂ĺŷ [ŕôút̂é-l̂év̂él̂ ćôd́ê-śp̂ĺît́t̂ín̂ǵ](https://web.dev/route-level-code-splitting-in-angular/) t̂ó m̂ín̂ím̂íẑé t̂h́ê śîźê óf̂ ýôúr̂ J́âv́âŚĉŕîṕt̂ b́ûńd̂ĺêś. Âĺŝó, ĉón̂śîd́êŕ p̂ŕêćâćĥín̂ǵ âśŝét̂ś ŵít̂h́ t̂h́ê [Án̂ǵûĺâŕ ŝér̂v́îćê ẃôŕk̂ér̂](https://web.dev/precaching-with-the-angular-service-worker/)."}, "stack-packs/packs/angular.js | unminified_warning": {"message": "Îf́ ŷóû ár̂é ûśîńĝ Án̂ǵûĺâŕ ĈĹÎ, én̂śûŕê t́ĥát̂ b́ûíl̂d́ŝ ár̂é ĝén̂ér̂át̂éd̂ ín̂ ṕr̂ód̂úĉt́îón̂ ḿôd́ê. [Ĺêár̂ń m̂ór̂é](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "stack-packs/packs/angular.js | unused_javascript": {"message": "Îf́ ŷóû ár̂é ûśîńĝ Án̂ǵûĺâŕ ĈĹÎ, ín̂ćl̂úd̂é ŝóûŕĉé m̂áp̂ś îńt̂ó ŷóûŕ p̂ŕôd́ûćt̂íôń b̂úîĺd̂ t́ô ín̂śp̂éĉt́ ŷóûŕ b̂ún̂d́l̂éŝ. [Ĺêár̂ń m̂ór̂é](https://angular.io/guide/deployment#inspect-the-bundles)."}, "stack-packs/packs/angular.js | uses_rel_preload": {"message": "P̂ŕêĺôád̂ ŕôút̂éŝ áĥéâd́ ôf́ t̂ím̂é t̂ó ŝṕêéd̂ úp̂ ńâv́îǵât́îón̂. [Ĺêár̂ń m̂ór̂é](https://web.dev/route-preloading-in-angular/)."}, "stack-packs/packs/angular.js | uses_responsive_images": {"message": "Ĉón̂śîd́êŕ ûśîńĝ t́ĥé `BreakpointObserver` ût́îĺît́ŷ ín̂ t́ĥé Ĉóm̂ṕôńêńt̂ D́êv́ K̂ít̂ (ĆD̂Ḱ) t̂ó m̂án̂áĝé îḿâǵê b́r̂éâḱp̂óîńt̂ś. [L̂éâŕn̂ ḿôŕê](https://material.angular.io/cdk/layout/overview)."}, "stack-packs/packs/magento.js | critical_request_chains": {"message": "Îf́ ŷóû ár̂é n̂ót̂ b́ûńd̂ĺîńĝ ýôúr̂ J́âv́âŚĉŕîṕt̂ áŝśêt́ŝ, ćôńŝíd̂ér̂ úŝín̂ǵ [b̂ál̂ér̂](https://github.com/magento/baler)."}, "stack-packs/packs/magento.js | disable_bundling": {"message": "D̂íŝáb̂ĺê Ḿâǵêńt̂ó'ŝ b́ûíl̂t́-îń [Ĵáv̂áŜćr̂íp̂t́ b̂ún̂d́l̂ín̂ǵ âńd̂ ḿîńîf́îćât́îón̂](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html), án̂d́ ĉón̂śîd́êŕ ûśîńĝ [b́âĺêŕ](https://github.com/magento/baler/) îńŝt́êád̂."}, "stack-packs/packs/magento.js | font_display": {"message": "Ŝṕêćîf́ŷ `@font-display` ẃĥén̂ [d́êf́îńîńĝ ćûśt̂óm̂ f́ôńt̂ś](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "stack-packs/packs/magento.js | offscreen_images": {"message": "Ĉón̂śîd́êŕ m̂ód̂íf̂ýîńĝ ýôúr̂ ṕr̂ód̂úĉt́ âńd̂ ćât́âĺôǵ t̂ém̂ṕl̂át̂éŝ t́ô ḿâḱê úŝé ôf́ t̂h́ê ẃêb́ p̂ĺât́f̂ór̂ḿ'ŝ [ĺâźŷ ĺôád̂ín̂ǵ](https://web.dev/native-lazy-loading) f̂éât́ûŕê."}, "stack-packs/packs/magento.js | time_to_first_byte": {"message": "Ûśê Ḿâǵêńt̂ó'ŝ [V́âŕn̂íŝh́ îńt̂éĝŕât́îón̂](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html)."}, "stack-packs/packs/magento.js | unminified_css": {"message": "Êńâb́l̂é t̂h́ê \"Ḿîńîf́ŷ ĆŜŚ F̂íl̂éŝ\" óp̂t́îón̂ ín̂ ýôúr̂ śt̂ór̂é'ŝ D́êv́êĺôṕêŕ ŝét̂t́îńĝś. [L̂éâŕn̂ ḿôŕê](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "stack-packs/packs/magento.js | unminified_javascript": {"message": "Ûśê [T́êŕŝér̂](https://www.npmjs.com/package/terser) t́ô ḿîńîf́ŷ ál̂ĺ Ĵáv̂áŜćr̂íp̂t́ âśŝét̂ś ôút̂f́r̂óm̂ f́r̂óm̂ śt̂át̂íĉ ćôńt̂én̂t́ d̂ép̂ĺôým̂én̂t́, âńd̂ d́îśâb́l̂é t̂h́ê b́ûíl̂t́-îń m̂ín̂íf̂íĉát̂íôń f̂éât́ûŕê."}, "stack-packs/packs/magento.js | unused_javascript": {"message": "D̂íŝáb̂ĺê Ḿâǵêńt̂ó'ŝ b́ûíl̂t́-îń [Ĵáv̂áŜćr̂íp̂t́ b̂ún̂d́l̂ín̂ǵ](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)."}, "stack-packs/packs/magento.js | uses_optimized_images": {"message": "Ĉón̂śîd́êŕ ŝéâŕĉh́îńĝ t́ĥé [M̂áĝén̂t́ô Ḿâŕk̂ét̂ṕl̂áĉé](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) f̂ór̂ á v̂ár̂íêt́ŷ óf̂ t́ĥír̂d́ p̂ár̂t́ŷ éx̂t́êńŝíôńŝ t́ô óp̂t́îḿîźê ím̂áĝéŝ."}, "stack-packs/packs/magento.js | uses_rel_preconnect": {"message": "P̂ŕêćôńn̂éĉt́ ôŕ d̂ńŝ-ṕr̂éf̂ét̂ćĥ ŕêśôúr̂ćê h́îńt̂ś ĉán̂ b́ê ád̂d́êd́ b̂ý [m̂ód̂íf̂ýîńĝ á t̂h́êḿêś'ŝ ĺâýôút̂](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "stack-packs/packs/magento.js | uses_rel_preload": {"message": "`<link rel=preload>` t̂áĝś ĉán̂ b́ê ád̂d́êd́ b̂ý [m̂ód̂íf̂ýîńĝ á t̂h́êḿêś'ŝ ĺâýôút̂](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "stack-packs/packs/magento.js | uses_webp_images": {"message": "Ĉón̂śîd́êŕ ŝéâŕĉh́îńĝ t́ĥé [M̂áĝén̂t́ô Ḿâŕk̂ét̂ṕl̂áĉé](https://marketplace.magento.com/catalogsearch/result/?q=webp) f̂ór̂ á v̂ár̂íêt́ŷ óf̂ t́ĥír̂d́-p̂ár̂t́ŷ éx̂t́êńŝíôńŝ t́ô ĺêv́êŕâǵê ńêẃêŕ îḿâǵê f́ôŕm̂át̂ś."}, "stack-packs/packs/react.js | dom_size": {"message": "Ĉón̂śîd́êŕ ûśîńĝ á “ŵín̂d́ôẃîńĝ” ĺîb́r̂ár̂ý l̂ík̂é `react-window` t̂ó m̂ín̂ím̂íẑé t̂h́ê ńûḿb̂ér̂ óf̂ D́ÔḾ n̂ód̂éŝ ćr̂éât́êd́ îf́ ŷóû ár̂é r̂én̂d́êŕîńĝ ḿâńŷ ŕêṕêát̂éd̂ él̂ém̂én̂t́ŝ ón̂ t́ĥé p̂áĝé. [L̂éâŕn̂ ḿôŕê](https://web.dev/virtualize-long-lists-react-window/). Ál̂śô, ḿîńîḿîźê ún̂éĉéŝśâŕŷ ŕê-ŕêńd̂ér̂ś ûśîńĝ [śĥóûĺd̂Ćôḿp̂ón̂én̂t́Ûṕd̂át̂é](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [P̂úr̂éĈóm̂ṕôńêńt̂](https://reactjs.org/docs/react-api.html#reactpurecomponent), ór̂ [Ŕêáĉt́.m̂ém̂ó](https://reactjs.org/docs/react-api.html#reactmemo) âńd̂ [śk̂íp̂ éf̂f́êćt̂ś](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) ôńl̂ý ûńt̂íl̂ ćêŕt̂áîń d̂ép̂én̂d́êńĉíêś ĥáv̂é ĉh́âńĝéd̂ íf̂ ýôú âŕê úŝín̂ǵ t̂h́ê Éf̂f́êćt̂ h́ôók̂ t́ô ím̂ṕr̂óv̂é r̂ún̂t́îḿê ṕêŕf̂ór̂ḿâńĉé."}, "stack-packs/packs/react.js | redirects": {"message": "Îf́ ŷóû ár̂é ûśîńĝ Ŕêáĉt́ R̂óût́êŕ, m̂ín̂ím̂íẑé ûśâǵê óf̂ t́ĥé `<Redirect>` ĉóm̂ṕôńêńt̂ f́ôŕ [r̂óût́ê ńâv́îǵât́îón̂ś](https://reacttraining.com/react-router/web/api/Redirect)."}, "stack-packs/packs/react.js | time_to_first_byte": {"message": "Îf́ ŷóû ár̂é ŝér̂v́êŕ-ŝíd̂é r̂én̂d́êŕîńĝ án̂ý R̂éâćt̂ ćôḿp̂ón̂én̂t́ŝ, ćôńŝíd̂ér̂ úŝín̂ǵ `renderToNodeStream()` ôŕ `renderToStaticNodeStream()` t̂ó âĺl̂óŵ t́ĥé ĉĺîén̂t́ t̂ó r̂éĉéîv́ê án̂d́ ĥýd̂ŕât́ê d́îf́f̂ér̂én̂t́ p̂ár̂t́ŝ óf̂ t́ĥé m̂ár̂ḱûṕ îńŝt́êád̂ óf̂ ál̂ĺ ât́ ôńĉé. [L̂éâŕn̂ ḿôŕê](https://reactjs.org/docs/react-dom-server.html#rendertonodestream)."}, "stack-packs/packs/react.js | unminified_css": {"message": "Îf́ ŷóûŕ b̂úîĺd̂ śŷśt̂ém̂ ḿîńîf́îéŝ ýôúr̂ ĆŜŚ f̂íl̂éŝ áût́ôḿât́îćâĺl̂ý, êńŝúr̂é t̂h́ât́ ŷóû ár̂é d̂ép̂ĺôýîńĝ t́ĥé p̂ŕôd́ûćt̂íôń b̂úîĺd̂ óf̂ ýôúr̂ áp̂ṕl̂íĉát̂íôń. Ŷóû ćâń ĉh́êćk̂ t́ĥíŝ ẃît́ĥ t́ĥé R̂éâćt̂ D́êv́êĺôṕêŕ T̂óôĺŝ éx̂t́êńŝíôń. [L̂éâŕn̂ ḿôŕê](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "stack-packs/packs/react.js | unminified_javascript": {"message": "Îf́ ŷóûŕ b̂úîĺd̂ śŷśt̂ém̂ ḿîńîf́îéŝ ýôúr̂ J́Ŝ f́îĺêś âút̂óm̂át̂íĉál̂ĺŷ, én̂śûŕê t́ĥát̂ ýôú âŕê d́êṕl̂óŷín̂ǵ t̂h́ê ṕr̂ód̂úĉt́îón̂ b́ûíl̂d́ ôf́ ŷóûŕ âṕp̂ĺîćât́îón̂. Ýôú ĉán̂ ćĥéĉḱ t̂h́îś ŵít̂h́ t̂h́ê Ŕêáĉt́ D̂év̂él̂óp̂ér̂ T́ôól̂ś êx́t̂én̂śîón̂. [Ĺêár̂ń m̂ór̂é](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "stack-packs/packs/react.js | unused_javascript": {"message": "Îf́ ŷóû ár̂é n̂ót̂ śêŕv̂ér̂-śîd́ê ŕêńd̂ér̂ín̂ǵ, [ŝṕl̂ít̂ ýôúr̂ J́âv́âŚĉŕîṕt̂ b́ûńd̂ĺêś](https://web.dev/code-splitting-suspense/) ŵít̂h́ `React.lazy()`. Ôt́ĥér̂ẃîśê, ćôd́ê-śp̂ĺît́ ûśîńĝ á t̂h́îŕd̂-ṕâŕt̂ý l̂íb̂ŕâŕŷ śûćĥ áŝ [ĺôád̂áb̂ĺê-ćôḿp̂ón̂én̂t́ŝ](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "stack-packs/packs/react.js | user_timings": {"message": "Ûśê t́ĥé R̂éâćt̂ D́êv́T̂óôĺŝ Ṕr̂óf̂íl̂ér̂, ẃĥíĉh́ m̂ák̂éŝ úŝé ôf́ t̂h́ê Ṕr̂óf̂íl̂ér̂ ÁP̂Í, t̂ó m̂éâśûŕê t́ĥé r̂én̂d́êŕîńĝ ṕêŕf̂ór̂ḿâńĉé ôf́ ŷóûŕ ĉóm̂ṕôńêńt̂ś. [L̂éâŕn̂ ḿôŕê.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "Ĉón̂śîd́êŕ ûṕl̂óâd́îńĝ ýôúr̂ ǴÎF́ t̂ó â śêŕv̂íĉé ŵh́îćĥ ẃîĺl̂ ḿâḱê ít̂ áv̂áîĺâb́l̂é t̂ó êḿb̂éd̂ áŝ án̂ H́T̂ḾL̂5 v́îd́êó."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "Îńŝt́âĺl̂ á [l̂áẑý-l̂óâd́ Ŵór̂d́P̂ŕêśŝ ṕl̂úĝín̂](https://wordpress.org/plugins/search/lazy+load/) t́ĥát̂ ṕr̂óv̂íd̂éŝ t́ĥé âb́îĺît́ŷ t́ô d́êf́êŕ âńŷ óf̂f́ŝćr̂éêń îḿâǵêś, ôŕ ŝẃît́ĉh́ t̂ó â t́ĥém̂é t̂h́ât́ p̂ŕôv́îd́êś t̂h́ât́ f̂ún̂ćt̂íôńâĺît́ŷ. Ál̂śô ćôńŝíd̂ér̂ úŝín̂ǵ [t̂h́ê ÁM̂Ṕ p̂ĺûǵîń](https://wordpress.org/plugins/amp/)."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "T̂h́êŕê ár̂é â ńûḿb̂ér̂ óf̂ Ẃôŕd̂Ṕr̂éŝś p̂ĺûǵîńŝ t́ĥát̂ ćâń ĥél̂ṕ ŷóû [ín̂ĺîńê ćr̂ít̂íĉál̂ áŝśêt́ŝ](https://wordpress.org/plugins/search/critical+css/) ór̂ [d́êf́êŕ l̂éŝś îḿp̂ór̂t́âńt̂ ŕêśôúr̂ćêś](https://wordpress.org/plugins/search/defer+css+javascript/). B̂éŵár̂é t̂h́ât́ ôṕt̂ím̂íẑát̂íôńŝ ṕr̂óv̂íd̂éd̂ b́ŷ t́ĥéŝé p̂ĺûǵîńŝ ḿâý b̂ŕêák̂ f́êát̂úr̂éŝ óf̂ ýôúr̂ t́ĥém̂é ôŕ p̂ĺûǵîńŝ, śô ýôú ŵíl̂ĺ l̂ík̂él̂ý n̂éêd́ t̂ó m̂ák̂é ĉód̂é ĉh́âńĝéŝ."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "T̂h́êḿêś, p̂ĺûǵîńŝ, án̂d́ ŝér̂v́êŕ ŝṕêćîf́îćât́îón̂ś âĺl̂ ćôńt̂ŕîb́ût́ê t́ô śêŕv̂ér̂ ŕêśp̂ón̂śê t́îḿê. Ćôńŝíd̂ér̂ f́îńd̂ín̂ǵ â ḿôŕê óp̂t́îḿîźêd́ t̂h́êḿê, ćâŕêf́ûĺl̂ý ŝél̂éĉt́îńĝ án̂ óp̂t́îḿîźât́îón̂ ṕl̂úĝín̂, án̂d́/ôŕ ûṕĝŕâd́îńĝ ýôúr̂ śêŕv̂ér̂."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "Ĉón̂śîd́êŕ ŝh́ôẃîńĝ éx̂ćêŕp̂t́ŝ ín̂ ýôúr̂ ṕôśt̂ ĺîśt̂ś (ê.ǵ. v̂íâ t́ĥé m̂ór̂é t̂áĝ), ŕêd́ûćîńĝ t́ĥé n̂úm̂b́êŕ ôf́ p̂óŝt́ŝ śĥóŵń ôń â ǵîv́êń p̂áĝé, b̂ŕêák̂ín̂ǵ ŷóûŕ l̂ón̂ǵ p̂óŝt́ŝ ín̂t́ô ḿûĺt̂íp̂ĺê ṕâǵêś, ôŕ ûśîńĝ á p̂ĺûǵîń t̂ó l̂áẑý-l̂óâd́ ĉóm̂ḿêńt̂ś."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "Â ńûḿb̂ér̂ óf̂ [Ẃôŕd̂Ṕr̂éŝś p̂ĺûǵîńŝ](https://wordpress.org/plugins/search/minify+css/) ćâń ŝṕêéd̂ úp̂ ýôúr̂ śît́ê b́ŷ ćôńĉát̂én̂át̂ín̂ǵ, m̂ín̂íf̂ýîńĝ, án̂d́ ĉóm̂ṕr̂éŝśîńĝ ýôúr̂ śt̂ýl̂éŝ. Ýôú m̂áŷ ál̂śô ẃâńt̂ t́ô úŝé â b́ûíl̂d́ p̂ŕôćêśŝ t́ô d́ô t́ĥíŝ ḿîńîf́îćât́îón̂ úp̂-f́r̂ón̂t́ îf́ p̂óŝśîb́l̂é."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "Â ńûḿb̂ér̂ óf̂ [Ẃôŕd̂Ṕr̂éŝś p̂ĺûǵîńŝ](https://wordpress.org/plugins/search/minify+javascript/) ćâń ŝṕêéd̂ úp̂ ýôúr̂ śît́ê b́ŷ ćôńĉát̂én̂át̂ín̂ǵ, m̂ín̂íf̂ýîńĝ, án̂d́ ĉóm̂ṕr̂éŝśîńĝ ýôúr̂ śĉŕîṕt̂ś. Ŷóû ḿâý âĺŝó ŵán̂t́ t̂ó ûśê á b̂úîĺd̂ ṕr̂óĉéŝś t̂ó d̂ó t̂h́îś m̂ín̂íf̂íĉát̂íôń ûṕ f̂ŕôńt̂ íf̂ ṕôśŝíb̂ĺê."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "Ĉón̂śîd́êŕ r̂éd̂úĉín̂ǵ, ôŕ ŝẃît́ĉh́îńĝ, t́ĥé n̂úm̂b́êŕ ôf́ [Ŵór̂d́P̂ŕêśŝ ṕl̂úĝín̂ś](https://wordpress.org/plugins/) l̂óâd́îńĝ ún̂úŝéd̂ ĆŜŚ îń ŷóûŕ p̂áĝé. T̂ó îd́êńt̂íf̂ý p̂ĺûǵîńŝ t́ĥát̂ ár̂é âd́d̂ín̂ǵ êx́t̂ŕâńêóûś ĈŚŜ, t́r̂ý r̂ún̂ńîńĝ [ćôd́ê ćôv́êŕâǵê](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) ín̂ Ćĥŕôḿê D́êv́T̂óôĺŝ. Ýôú ĉán̂ íd̂én̂t́îf́ŷ t́ĥé t̂h́êḿê/ṕl̂úĝín̂ ŕêśp̂ón̂śîb́l̂é f̂ŕôḿ t̂h́ê ÚR̂Ĺ ôf́ t̂h́ê śt̂ýl̂éŝh́êét̂. Ĺôók̂ óût́ f̂ór̂ ṕl̂úĝín̂ś t̂h́ât́ ĥáv̂é m̂án̂ý ŝt́ŷĺêśĥéêt́ŝ ín̂ t́ĥé l̂íŝt́ ŵh́îćĥ h́âv́ê á l̂ót̂ óf̂ ŕêd́ îń ĉód̂é ĉóv̂ér̂áĝé. Â ṕl̂úĝín̂ śĥóûĺd̂ ón̂ĺŷ én̂q́ûéûé â śt̂ýl̂éŝh́êét̂ íf̂ ít̂ íŝ áĉt́ûál̂ĺŷ úŝéd̂ ón̂ t́ĥé p̂áĝé."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "Ĉón̂śîd́êŕ r̂éd̂úĉín̂ǵ, ôŕ ŝẃît́ĉh́îńĝ, t́ĥé n̂úm̂b́êŕ ôf́ [Ŵór̂d́P̂ŕêśŝ ṕl̂úĝín̂ś](https://wordpress.org/plugins/) l̂óâd́îńĝ ún̂úŝéd̂ J́âv́âŚĉŕîṕt̂ ín̂ ýôúr̂ ṕâǵê. T́ô íd̂én̂t́îf́ŷ ṕl̂úĝín̂ś t̂h́ât́ âŕê ád̂d́îńĝ éx̂t́r̂án̂éôúŝ J́Ŝ, t́r̂ý r̂ún̂ńîńĝ [ćôd́ê ćôv́êŕâǵê](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) ín̂ Ćĥŕôḿê D́êv́T̂óôĺŝ. Ýôú ĉán̂ íd̂én̂t́îf́ŷ t́ĥé t̂h́êḿê/ṕl̂úĝín̂ ŕêśp̂ón̂śîb́l̂é f̂ŕôḿ t̂h́ê ÚR̂Ĺ ôf́ t̂h́ê śĉŕîṕt̂. Ĺôók̂ óût́ f̂ór̂ ṕl̂úĝín̂ś t̂h́ât́ ĥáv̂é m̂án̂ý ŝćr̂íp̂t́ŝ ín̂ t́ĥé l̂íŝt́ ŵh́îćĥ h́âv́ê á l̂ót̂ óf̂ ŕêd́ îń ĉód̂é ĉóv̂ér̂áĝé. Â ṕl̂úĝín̂ śĥóûĺd̂ ón̂ĺŷ én̂q́ûéûé â śĉŕîṕt̂ íf̂ ít̂ íŝ áĉt́ûál̂ĺŷ úŝéd̂ ón̂ t́ĥé p̂áĝé."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "R̂éâd́ âb́ôút̂ [B́r̂óŵśêŕ Ĉáĉh́îńĝ ín̂ Ẃôŕd̂Ṕr̂éŝś](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "Ĉón̂śîd́êŕ ûśîńĝ án̂ [ím̂áĝé ôṕt̂ím̂íẑát̂íôń Ŵór̂d́P̂ŕêśŝ ṕl̂úĝín̂](https://wordpress.org/plugins/search/optimize+images/) t́ĥát̂ ćôḿp̂ŕêśŝéŝ ýôúr̂ ím̂áĝéŝ ẃĥíl̂é r̂ét̂áîńîńĝ q́ûál̂ít̂ý."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "Ûṕl̂óâd́ îḿâǵêś d̂ír̂éĉt́l̂ý t̂h́r̂óûǵĥ t́ĥé [m̂éd̂íâ ĺîb́r̂ár̂ý](https://codex.wordpress.org/Media_Library_Screen) t̂ó êńŝúr̂é t̂h́ât́ t̂h́ê ŕêq́ûír̂éd̂ ím̂áĝé ŝíẑéŝ ár̂é âv́âíl̂áb̂ĺê, án̂d́ t̂h́êń îńŝér̂t́ t̂h́êḿ f̂ŕôḿ t̂h́ê ḿêd́îá l̂íb̂ŕâŕŷ ór̂ úŝé t̂h́ê ím̂áĝé ŵíd̂ǵêt́ t̂ó êńŝúr̂é t̂h́ê óp̂t́îḿâĺ îḿâǵê śîźêś âŕê úŝéd̂ (ín̂ćl̂úd̂ín̂ǵ t̂h́ôśê f́ôŕ t̂h́ê ŕêśp̂ón̂śîv́ê b́r̂éâḱp̂óîńt̂ś). Âv́ôíd̂ úŝín̂ǵ `Full Size` îḿâǵêś ûńl̂éŝś t̂h́ê d́îḿêńŝíôńŝ ár̂é âd́êq́ûát̂é f̂ór̂ t́ĥéîŕ ûśâǵê. [Ĺêár̂ń M̂ór̂é](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)."}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "Ŷóû ćâń êńâb́l̂é t̂éx̂t́ ĉóm̂ṕr̂éŝśîón̂ ín̂ ýôúr̂ ẃêb́ ŝér̂v́êŕ ĉón̂f́îǵûŕât́îón̂."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "Ĉón̂śîd́êŕ ûśîńĝ á [p̂ĺûǵîń](https://wordpress.org/plugins/search/convert+webp/) ôŕ ŝér̂v́îćê t́ĥát̂ ẃîĺl̂ áût́ôḿât́îćâĺl̂ý ĉón̂v́êŕt̂ ýôúr̂ úp̂ĺôád̂éd̂ ím̂áĝéŝ t́ô t́ĥé ôṕt̂ím̂ál̂ f́ôŕm̂át̂ś."}}