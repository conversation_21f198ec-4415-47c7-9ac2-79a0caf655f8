{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "액세스 키를 사용하면 사용자가 페이지의 특정 부분에 신속하게 포커스를 맞출 수 있습니다. 정상적으로 탐색하려면 모든 액세스 키가 고유해야 합니다. [자세히 알아보기](https://web.dev/accesskeys/)"}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "`[accesskey]` 값이 고유하지 않음"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]` 값이 고유합니다"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "각 ARIA `role`은(는) `aria-*` 속성으로 구성된 특정 하위 세트를 지원합니다. 이 두 가지가 일치하지 않으면 `aria-*` 속성이 유효하지 않게 됩니다. [자세히 알아보기](https://web.dev/aria-allowed-attr/)"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "`[aria-*]` 속성이 역할과 일치하지 않음"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "`[aria-*]` 속성이 역할과 일치함"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "일부 ARIA 역할에는 스크린 리더에 관한 요소의 상태를 설명하는 속성이 필요합니다. [자세히 알아보기](https://web.dev/aria-required-attr/)"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]`에 필요한 `[aria-*]` 속성이 일부 포함되지 않음"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]`에 필요한 모든 `[aria-*]` 속성이 있음"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "일부 ARIA 상위 역할에서 의도한 접근성 기능을 실행하려면 특정 하위 역할을 포함하고 있어야 합니다. [자세히 알아보기](https://web.dev/aria-required-children/)"}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "하위 요소에 특정 `[role]`을(를) 포함해야 하는 ARIA `[role]` 지원 요소에 일부 또는 전체 하위 요소가 누락되었습니다."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "하위 요소에 특정 `[role]`을(를) 포함해야 하는 ARIA `[role]` 지원 요소에 필요한 모든 하위 요소가 있습니다."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "일부 ARIA 하위 역할에서 의도한 접근성 기능을 올바르게 실행하려면 특정 상위 역할에 포함되어 있어야 합니다. [자세히 알아보기](https://web.dev/aria-required-parent/)"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]`이(가) 필수 상위 요소에 포함되지 않음"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]`이(가) 필수 상위 요소에 포함됨"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "ARIA 역할에서 의도한 접근성 기능을 실행하려면 유효한 값을 포함하고 있어야 합니다. [자세히 알아보기](https://web.dev/aria-roles/)"}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]` 값이 유효하지 않음"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "`[role]` 값이 유효함"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "스크린 리더와 같은 보조 기술은 잘못된 값이 있는 ARIA 속성을 해석하지 못합니다. [자세히 알아보기](https://web.dev/aria-valid-attr-value/)"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "`[aria-*]` 속성에 유효한 값이 없음"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "`[aria-*]` 속성에 유효한 값이 있음"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "스크린 리더와 같은 보조 기술은 잘못된 이름이 있는 ARIA 속성을 해석하지 못합니다. [자세히 알아보기](https://web.dev/aria-valid-attr/)"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "`[aria-*]` 속성이 유효하지 않거나 맞춤법 오류가 있음"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "`[aria-*]` 속성이 유효하며 맞춤법 오류가 없음"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "자막이 있으면 청각장애인이나 청각에 문제가 있는 사용자도 오디오 요소를 사용할 수 있게 되며 말하고 있는 사람, 말하는 내용, 기타 비음성적 정보와 같은 중요 정보가 제공됩니다. [자세히 알아보기](https://web.dev/audio-caption/)"}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "`<audio>` 요소에서 `[kind=\"captions\"]` 지원 `<track>` 요소가 누락됨"}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "`<audio>` 요소에 `[kind=\"captions\"]` 지원 `<track>` 요소가 포함됨"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "통과하지 못한 요소"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "버튼에 접근 가능한 이름이 없는 경우 스크린 리더에서 '버튼'으로만 읽기 때문에 스크린 리더에 의존하는 사용자에게는 해당 버튼이 유용하지 않게 됩니다. [자세히 알아보기](https://web.dev/button-name/)"}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "버튼에 접근 가능한 이름이 없습니다"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "버튼에 접근 가능한 이름이 있습니다"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "반복적인 콘텐츠를 건너뛸 방법을 추가하면 키보드 사용자가 페이지를 더 효율적으로 탐색할 수 있습니다. [자세히 알아보기](https://web.dev/bypass/)"}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "페이지에 제목, 링크 건너뛰기, 랜드마크 영역이 포함되어 있지 않습니다"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "페이지에 제목, 링크 건너뛰기, 랜드마크 영역이 포함되어 있습니다"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "많은 사용자가 저대비 텍스트를 읽는 데 어려움을 겪거나 전혀 읽지 못합니다. [자세히 알아보기](https://web.dev/color-contrast/)"}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "백그라운드 및 포그라운드 색상의 대비율이 충분하지 않습니다"}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "백그라운드 및 포그라운드 색상의 대비율이 충분합니다"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "정의 목록이 올바르게 표시되지 않으면 스크린 리더에서 혼란스럽거나 정확하지 않은 내용을 말할 수 있습니다. [자세히 알아보기](https://web.dev/definition-list/)"}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>`에 순서가 올바른 `<dt>` 및 `<dd>` 그룹, `<script>` 또는 `<template>` 요소만 포함되지 않음"}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "`<dl>`에 순서가 올바른 `<dt>` 및 `<dd>` 그룹, `<script>` 또는 `<template>` 요소만 포함됨"}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "스크린 리더에서 정의 목록 항목(`<dt>` 및 `<dd>`)을 올바르게 읽으려면 정의 목록 항목이 상위 `<dl>` 요소에 래핑되어 있어야 합니다. [자세히 알아보기](https://web.dev/dlitem/)"}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "정의 목록 항목이 `<dl>` 요소에서 래핑되어 있지 않음"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "정의 목록 항목이 `<dl>` 요소에서 래핑되어 있음"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "제목을 사용하면 스크린 리더 사용자에게는 페이지의 개요를 제공할 수 있습니다. 검색엔진 사용자는 제목으로 페이지가 자신의 검색어와 관련되어 있는지 여부를 판단합니다. [자세히 알아보기](https://web.dev/document-title/)"}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "문서에 `<title>` 요소가 없음"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "문서에 `<title>` 요소가 있음"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "보조 기술에서 다른 인스턴스를 놓치지 않도록 하려면 ID 속성값이 고유해야 합니다. [자세히 알아보기](https://web.dev/duplicate-id/)"}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "페이지에 있는 `[id]` 속성이 고유하지 않음"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "페이지에 있는 `[id]` 속성이 고유함"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "스크린 리더 사용자는 프레임 콘텐츠를 설명해 주는 프레임 제목을 사용합니다. [자세히 알아보기](https://web.dev/frame-title/)"}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "`<frame>` 또는 `<iframe>` 요소에 제목이 없음"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "`<frame>` 또는 `<iframe>` 요소에 제목이 있음"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "페이지에서 lang 속성을 지정하지 않는 경우 스크린 리더는 사용자가 스크린 리더를 설정할 때 선택한 기본 언어로 페이지가 작성되어 있다고 가정합니다. 페이지가 기본 언어로 작성되어 있지 않으면 스크린 리더에서 페이지에 있는 텍스트를 제대로 읽어줄 수 없습니다. [자세히 알아보기](https://web.dev/html-has-lang/)"}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>` 요소에 `[lang]` 속성이 없음"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>` 요소에 `[lang]` 속성이 있음"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "유효한 [BCP 47 언어](https://www.w3.org/International/questions/qa-choosing-language-tags#question)를 지정하면 스크린 리더에서 텍스트를 올바르게 읽는 데 도움이 됩니다. [자세히 알아보기](https://web.dev/html-lang-valid/)"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>` 요소의 `[lang]` 속성에 유효한 값이 없음"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "`<html>` 요소에 `[lang]` 속성의 유효한 값이 있음"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "정보 요소는 짧아야 하며 설명을 제공하는 대체 텍스트를 목표로 해야 합니다. 장식 요소는 Alt 속성이 비어 있는 경우 무시될 수 있습니다. [자세히 알아보기](https://web.dev/image-alt/)"}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "이미지 요소에 `[alt]` 속성 없음"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "이미지 요소에 `[alt]` 속성이 있음"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "이미지가 `<input>` 버튼으로 사용되는 경우 대체 텍스트를 제공하면 스크린 리더 사용자가 버튼의 목적을 쉽게 이해하는 데 도움이 됩니다. [자세히 알아보기](https://web.dev/input-image-alt/)"}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">` 요소에 `[alt]` 텍스트가 없음"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">` 요소에 `[alt]` 텍스트가 있음"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "라벨을 사용하면 스크린 리더와 같은 보조 기술에서 양식 컨트롤을 올바르게 읽을 수 있습니다. [자세히 알아보기](https://web.dev/label/)"}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "양식 요소에 관련 라벨이 포함되어 있지 않습니다"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "양식 요소에 관련 라벨이 포함되어 있습니다"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "스크린 리더 사용자에게 혼란스러운 환경이 생길 수 있으므로 레이아웃 용도로 사용되는 표에는 th 또는 caption 요소와 같은 데이터 요소나 summary 속성이 포함되지 않아야 합니다. [자세히 알아보기](https://web.dev/layout-table/)"}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "프레젠테이션 `<table>` 요소에서 `<th>`, `<caption>` 또는 `[summary]` 속성의 사용이 지양되지 않음"}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "프레젠테이션 `<table>` 요소에서 `<th>`, `<caption>` 또는 `[summary]` 속성을 가급적 사용하지 않음"}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "인식하기 쉽고, 고유하고, 초점을 맞추기 쉬운 링크 텍스트(및 이미지가 링크로 사용되는 경우 이미지의 대체 텍스트)를 사용하면 스크린 리더 사용자의 탐색 환경을 개선할 수 있습니다. [자세히 알아보기](https://web.dev/link-name/)"}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "링크에 인식 가능한 이름이 포함되어 있지 않습니다"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "링크에 인식 가능한 이름이 포함되어 있습니다"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "스크린 리더에는 목록을 읽는 특정 방식이 있습니다. 목록 구조를 적절히 작성하면 스크린 리더 출력에 도움이 됩니다. [자세히 알아보기](https://web.dev/list/)"}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "목록에 `<li>` 요소와 스크립트 지원 요소(`<script>` 및 `<template>`)만 포함되지 않음"}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "목록에 `<li>` 요소와 요소 지원 스크립트(`<script>` 및 `<template>`)만 포함됨"}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "스크린 리더에서 목록 항목(`<li>`)을 올바르게 읽으려면 목록 항목이 상위 `<ul>` 또는 `<ol>`에 포함되어 있어야 합니다. [자세히 알아보기](https://web.dev/listitem/)"}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "목록 항목(`<li>`)이 `<ul>` 또는 `<ol>` 상위 요소 내에 포함되지 않음"}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "목록 항목(`<li>`)이 `<ul>` 또는 `<ol>` 상위 요소 내에 포함되어 있음"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "사용자는 페이지가 자동으로 새로고침된다고 예상하지 못하기 때문에 페이지가 자동으로 새로고침되면 초점이 다시 페이지 상단에 맞춰집니다. 이로 인해 불쾌하거나 혼란스러운 상황이 발생할 수 있습니다. [자세히 알아보기](https://web.dev/meta-refresh/)"}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "문서에서 `<meta http-equiv=\"refresh\">` 사용됨"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "문서에서 `<meta http-equiv=\"refresh\">`이(가) 사용되지 않음"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "확대/축소를 사용 중지하면 저시력으로 인해 웹페이지의 콘텐츠를 제대로 확인하기 위해 화면 확대를 사용하는 사용자에게 문제가 될 수 있습니다. [자세히 알아보기](https://web.dev/meta-viewport/)"}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]`이(가) `<meta name=\"viewport\">` 요소에서 사용되거나 `[maximum-scale]` 속성이 5보다 작음"}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]`은(는) `<meta name=\"viewport\">` 요소에 사용되지 않으며 `[maximum-scale]` 속성이 5보다 작지 않음"}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "스크린 리더는 텍스트가 아닌 콘텐츠를 번역할 수 없습니다. `<object>` 요소에 Alt 텍스트를 추가하면 스크린 리더에서 사용자에게 텍스트의 의미를 전달하는 데 도움이 됩니다. [자세히 알아보기](https://web.dev/object-alt/)"}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>` 요소에 `[alt]` 텍스트가 없음"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "`<object>` 요소에 `[alt]` 텍스트가 있음"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "0보다 큰 값은 명시적인 탐색 순서를 나타냅니다. 기술적으로는 유효하나 보조 기술에 의존하는 사용자에게 불편한 환경이 생기는 경우가 많습니다. [자세히 알아보기](https://web.dev/tabindex/)"}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "일부 요소의 `[tabindex]` 값이 0보다 큼"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "`[tabindex]` 값이 0보다 큰 요소가 없음"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "스크린 리더에는 표를 좀 더 쉽게 탐색하는 기능이 있습니다. `[headers]` 속성을 사용하는 `<td>` 셀이 동일한 테이블에 있는 다른 셀만 참조하게 하면 스크린 리더 사용자 환경을 개선할 수 있습니다. [자세히 알아보기](https://web.dev/td-headers-attr/)"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "`[headers]` 속성을 사용하는 `<table>` 요소의 셀은 동일한 테이블 내에 없는 요소 `id`만 참조합니다."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "`[headers]` 속성을 사용하는 `<table>` 요소의 셀이 동일한 테이블 내의 테이블 셀을 참조합니다."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "스크린 리더에는 표를 좀 더 쉽게 탐색하는 기능이 있습니다. 표 헤더에서 항상 셀 세트 일부를 참조하게 하면 스크린 리더 사용자 환경이 개선될 수 있습니다. [자세히 알아보기](https://web.dev/th-has-data-cells/)"}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "`<th>` 요소와 `[role=\"columnheader\"/\"rowheader\"]` 지원 요소가 설명하는 데이터 셀이 해당 요소에 포함되지 않음"}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "`<th>` 요소와 `[role=\"columnheader\"/\"rowheader\"]` 지원 요소가 설명하는 데이터 셀이 해당 요소에 포함됨"}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "요소에 유효한 [BCP 47 언어](https://www.w3.org/International/questions/qa-choosing-language-tags#question)를 지정하면 스크린 리더에서 텍스트를 올바르게 읽는 데 도움이 됩니다. [자세히 알아보기](https://web.dev/valid-lang/)"}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "`[lang]` 속성에 유효한 값이 없음"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "`[lang]` 속성에 유효한 값이 있음"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "동영상에 자막이 제공되면 청각장애인이나 난청이 있는 사용자가 동영상의 정보에 더 쉽게 접근할 수 있습니다. [자세히 알아보기](https://web.dev/video-caption/)"}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "`<video>` 요소에 `[kind=\"captions\"]` 지원 `<track>` 요소가 포함되지 않음"}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "`<video>` 요소에 `[kind=\"captions\"]` 지원 `<track>` 요소가 포함됨"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "오디오 설명은 표정 및 장면과 같이 대화에서 제공할 수 없는 동영상에 관한 정보를 제공합니다. [자세히 알아보기](https://web.dev/video-description/)"}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "`<video>` 요소에 `[kind=\"description\"]` 지원 `<track>` 요소가 포함되지 않음"}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "`<video>` 요소에 `[kind=\"description\"]` 지원 `<track>` 요소가 포함됨"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "프로그레시브 웹 앱을 홈 화면에 추가했을 때 iOS에서 제대로 표시되려면 `apple-touch-icon`을(를) 정의해야 합니다. 투명하지 않은 192px 또는 180px 정사각형 PNG로 설정되어야 합니다. [자세히 알아보기](https://web.dev/apple-touch-icon/)"}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "유효한 `apple-touch-icon`이(가) 제공되지 않음"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "`apple-touch-icon-precomposed`이(가) 최신 버전이 아니므로 `apple-touch-icon`이(가) 적합합니다."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "유효한 `apple-touch-icon` 제공"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chrome 확장 프로그램이 이 페이지의 로드 성능에 부정적인 영향을 미쳤습니다. 시크릿 모드나 확장 프로그램이 없는 Chrome 프로필에서 페이지를 검사해 보세요."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "스크립트 평가"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "스크립트 파싱"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "총 CPU 시간"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "JS 파싱, 컴파일, 실행에 소요되는 시간을 줄여 보세요. 용량이 적은 JS 페이로드를 제공하면 도움이 될 수 있습니다. [자세히 알아보기](https://web.dev/bootup-time)"}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "자바스크립트 실행 시간 단축"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "자바스크립트 실행 시간"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "대용량의 GIF는 애니메이션 콘텐츠를 전달하는 데 비효율적입니다. 애니메이션에는 MPEG4/WebM 동영상을, 정적인 이미지에는 GIF 대신 PNG/WebP를 사용하여 네트워크 바이트를 절약하세요. [자세히 알아보기](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "애니메이션 콘텐츠에 동영상 형식 사용하기"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "중요한 리소스의 로드가 모두 완료된 후에는 오프스크린 및 숨겨진 이미지를 지연 로드함으로써 상호작용 시간을 줄이는 것이 좋습니다. [자세히 알아보기](https://web.dev/offscreen-images)"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "오프스크린 이미지 지연하기"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "리소스가 페이지의 첫 페인트를 차단하고 있습니다. 중요한 JS/CSS를 인라인으로 전달하고 중요하지 않은 모든 JS/Style을 지연하는 것이 좋습니다. [자세히 알아보기](https://web.dev/render-blocking-resources)"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "렌더링 차단 리소스 제거하기"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "네트워크 페이로드가 커지면 사용자에게 실제 비용 부담이 되며 로드 시간이 길어질 수 있습니다. [자세히 알아보기](https://web.dev/total-byte-weight)"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "총 크기: {totalBytes, number, bytes}KB"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "네트워크 페이로드가 커지지 않도록 관리하기"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "대규모 네트워크 페이로드 방지하기"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "CSS 파일을 축소하면 네트워크 페이로드 크기를 줄일 수 있습니다. [자세히 알아보기](https://web.dev/unminified-css)"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "CSS 축소하기"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "자바스크립트 파일을 축소하면 페이로드 크기와 스크립트 파싱 시간을 줄일 수 있습니다. [자세히 알아보기](https://web.dev/unminified-javascript)"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "자바스크립트 줄이기"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "스타일시트에서 사용하지 않는 규칙을 삭제하고 스크롤 없이 볼 수 있는 부분에 사용되지 않는 CSS의 로드를 지연시켜 네트워크 활동에 소비되는 불필요한 바이트를 줄이세요. [자세히 알아보기](https://web.dev/unused-css-rules)"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "사용하지 않는 CSS 제거"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "사용하지 않는 자바스크립트를 삭제하고 네트워크 활동에 소비되는 바이트를 줄이세요."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "사용하지 않는 자바스크립트 삭제하기"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "캐시 수명이 길면 페이지를 반복해서 방문하는 속도가 빨라질 수 있습니다. [자세히 알아보기](https://web.dev/uses-long-cache-ttl)"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{리소스 1개 발견됨}other{리소스 #개 발견됨}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "효율적인 캐시 정책을 사용하여 정적인 애셋 제공하기"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "정적인 애셋에 효율적인 캐시 정책 사용하기"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "최적화된 이미지는 빠르게 로드되며 모바일 데이터를 적게 소비합니다. [자세히 알아보기](https://web.dev/uses-optimized-images)"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "효율적으로 이미지 인코딩하기"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "적절한 크기의 이미지를 게재하여 모바일 데이터를 절약하고 로드 시간을 단축하세요. [자세히 알아보기](https://web.dev/uses-responsive-images)"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "이미지 크기 적절하게 설정하기"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "총 네트워크 바이트를 최소화하려면 텍스트 기반 리소스를 압축(gzip, deflate, brotli)하여 제공해야 합니다. [자세히 알아보기](https://web.dev/uses-text-compression)"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "텍스트 압축 사용"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "JPEG 2000, JPEG XR, WebP와 같은 이미지 형식을 사용하면 PNG 또는 JPEG보다 압축률이 높으므로 다운로드 속도가 빠르고 데이터 소비량도 줄어듭니다. [자세히 알아보기](https://web.dev/uses-webp-images)"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "차세대 형식을 사용해 이미지 제공하기"}, "lighthouse-core/audits/content-width.js | description": {"message": "앱 콘텐츠의 너비가 표시 영역의 너비와 일치하지 않을 경우 앱이 휴대기기 화면에 최적화되지 않을 수 있습니다. [자세히 알아보기](https://web.dev/content-width)"}, "lighthouse-core/audits/content-width.js | explanation": {"message": "{innerWidth}px의 표시 영역 크기는 {outerWidth}px인 창 크기와 일치하지 않습니다."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "콘텐츠의 크기가 표시 영역에 알맞지 않음"}, "lighthouse-core/audits/content-width.js | title": {"message": "콘텐츠의 크기가 표시 영역에 알맞음"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "다음의 크리티컬 요청 체인은 로드 시 우선순위가 높은 리소스를 보여줍니다. 체인의 길이를 줄이고, 리소스의 다운로드 크기를 줄이거나 불필요한 리소스의 다운로드를 지연하여 페이지 로드 속도를 높이는 것이 좋습니다. [자세히 알아보기](https://web.dev/critical-request-chains)"}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1개 체인 발견됨}other{#개 체인 발견됨}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "중요 요청 깊이 최소화하기"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "지원 중단/경고"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "행"}, "lighthouse-core/audits/deprecations.js | description": {"message": "이후에 지원 중단된 API는 브라우저에서 삭제됩니다. [자세히 알아보기](https://web.dev/deprecations)"}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{경고 1개가 발견됨}other{경고 #개가 발견됨}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "지원 중단된 API 사용"}, "lighthouse-core/audits/deprecations.js | title": {"message": "지원 중단 API 사용하지 않기"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "애플리케이션 캐시는 지원이 중단되었습니다. [자세히 알아보기](https://web.dev/appcache-manifest)"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "검색된 ‘{AppCacheManifest}’"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "애플리케이션 캐시 사용"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "애플리케이션 캐시 사용하지 않기"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "Doctype을 지정하면 브라우저가 쿼크 모드로 전환할 수 없습니다. [자세히 알아보기](https://web.dev/doctype)"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Doctype 이름은 소문자 문자열 `html`이어야 합니다."}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "문서에는 Doctype이 포함되어 있어야 합니다."}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "publicId가 빈 문자열일 것으로 예상됨"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "systemId가 빈 문자열일 것으로 예상됨"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "페이지에 HTML Doctype이 없으므로 쿼크 모드가 트리거됨"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "페이지에 HTML Doctype 있음"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "요소"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "통계"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "값"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "브라우저 엔지니어들은 페이지에 1,500개 미만의 DOM 요소를 포함하는 것을 권장합니다. 최적값은 트리 깊이가 요소 32개 미만이고 하위/상위 요소 60개 미만일 때입니다. DOM이 크면 메모리 사용량이 늘어나고 [스타일 계산](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) 시간이 길어질 수 있으며 큰 비용이 드는 [레이아웃 리플로우](https://developers.google.com/speed/articles/reflow)가 발생할 수 있습니다. [자세히 알아보기](https://web.dev/dom-size)"}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{요소 1개}other{요소 #개}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "과도한 DOM 크기 지양하기"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "최대 DOM 깊이"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "총 DOM 요소 개수"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "최대 하위 요소"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "과도한 DOM 크기 지양하기"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "타겟"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "성능을 개선하고 보안 취약점을 예방하려면 `rel=\"noopener\"` 또는 `rel=\"noreferrer\"`을(를) 외부 링크에 추가합니다. [자세히 알아보기](https://web.dev/external-anchors-use-rel-noopener)"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "교차 도메인 대상 링크가 안전하지 않음"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "교차 도메인 대상 링크가 안전함"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "앵커({anchorHTML})의 대상을 확인하지 못했습니다. 하이퍼링크로 사용되지 않는 경우 target=_blank를 삭제하는 것이 좋습니다."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "사용자가 컨텍스트 없이 위치 정보를 요청한 사이트를 신뢰할 수 없거나 이로 인해 혼란스러운 상태입니다. 대신 사용자 작업 요청 입력을 고려해 보세요. [자세히 알아보기](https://web.dev/geolocation-on-start)"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "페이지 로드 시 위치정보 권한 요청"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "페이지 로드 시 위치정보 권한 요청 방지하기"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "버전"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "페이지에서 감지된 모든 프런트 엔드 JavaScript 라이브러리입니다. [자세히 알아보기](https://web.dev/js-libraries)"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "감지된 JavaScript 라이브러리"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "연결이 느린 사용자의 경우 `document.write()`에서 동적으로 삽입된 외부 스크립트로 인해 페이지 로드가 몇십 초까지 지연될 수 있습니다. [자세히 알아보기](https://web.dev/no-document-write)"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "`document.write()`을(를) 사용함"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "`document.write()` 지양하기"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "높은 심각도순"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "라이브러리 버전"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "취약점 수"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "일부 타사 스크립트에는 공격자가 쉽게 식별하고 공격할 수 있는 알려진 보안 취약점이 포함되어 있을 수 있습니다. [자세히 알아보기](https://web.dev/no-vulnerable-libraries)"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{취약점 1개가 감지됨}other{취약점 #개가 감지됨}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "알려진 보안 취약점이 있는 프런트 엔드 JavaScript 라이브러리가 포함됨"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "높음"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "낮음"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "보통"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "알려진 보안 취약점이 있는 프런트 엔드 JavaScript 라이브러리를 사용하지 않음"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "사용자가 컨텍스트 없이 알림 전송을 요청한 사이트를 신뢰할 수 없거나 이로 인해 혼란스러운 상태입니다. 대신 사용자 동작에 대한 요청 입력을 고려해 보세요. [자세히 알아보기](https://web.dev/notification-on-start)"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "페이지 로드 시 알림 권한 요청"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "페이지 로드 시 알림 권한 요청 방지하기"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "통과하지 못한 요소"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "비밀번호 붙여넣기 방지로 인해 타당한 보안 정책이 저해됩니다. [자세히 알아보기](https://web.dev/password-inputs-can-be-pasted-into)"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "사용자가 비밀번호 입력란에 내용을 붙여넣지 못하도록 차단"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "사용자가 비밀번호 입력란에 붙여넣을 수 있도록 허용"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "프로토콜"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2는 HTTP/1.1에 비해 바이너리 헤더, 멀티플렉싱, 서버 푸시 등의 다양한 이점을 제공합니다. [자세히 알아보기](https://web.dev/uses-http2)"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{HTTP/2를 통해 게재되지 않는 요청 1건}other{HTTP/2를 통해 게재되지 않는 요청 #건}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "모든 리소스에 HTTP/2를 사용하지 않음"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "자체 리소스에 HTTP/2 사용"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "터치 및 휠 이벤트 리스너를 `passive`(으)로 표시하면 페이지 스크롤 성능을 개선할 수 있습니다. [자세히 알아보기](https://web.dev/uses-passive-event-listeners)"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "스크롤 성능 개선에 패시브 리스너를 사용하지 않음"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "스크롤 성능 개선에 패시브 리스너 사용"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "설명"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "콘솔에 로그된 오류는 해결되지 않은 문제를 의미합니다. 네트워크 요청 실패를 비롯한 기타 브라우저 문제로 인해 발생할 수 있습니다. [자세히 알아보기](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "브라우저 오류가 콘솔에 로그됨"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "콘솔에 로그된 브라우저 오류 없음"}, "lighthouse-core/audits/font-display.js | description": {"message": "웹폰트가 로드되는 동안 사용자에게 텍스트가 표시되도록 글꼴 표시 CSS 기능을 사용합니다. [자세히 알아보기](https://web.dev/font-display)"}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "웹폰트가 로드되는 동안 텍스트가 계속 표시되는지 확인하기"}, "lighthouse-core/audits/font-display.js | title": {"message": "웹폰트가 로드되는 동안 모든 텍스트가 계속 표시됩니다"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "Lighthouse에서 다음 URL의 글꼴 표시 값을 자동으로 확인하지 못했습니다. {fontURL}"}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "가로세로 비율(실제)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "가로세로 비율(표시됨)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "이미지 표시 측정기준은 원래 가로세로 비율과 일치해야 합니다. [자세히 알아보기](https://web.dev/image-aspect-ratio)"}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "이미지를 올바르지 않은 가로세로 비율로 표시"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "이미지를 올바른 가로세로 비율로 표시"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "잘못된 이미지 크기 정보 {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "브라우저는 사용자에게 홈 화면에 앱을 추가하라는 메시지를 사전에 표시할 수 있으며 이렇게 하면 참여도가 높아질 수 있습니다. [자세히 알아보기](https://web.dev/installable-manifest)"}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "웹 앱 매니페스트가 설치 가능 요건을 충족하지 않음"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "웹 앱 매니페스트가 설치 가능 요건을 충족함"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "안전하지 않은 URL"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "민감한 데이터를 다루지 않는 사이트를 포함한 모든 사이트는 HTTPS로 보호해야 합니다. HTTPS는 침입자가 앱과 사용자 사이의 통신을 조작하거나 통신에 대해 패시브 리스너를 사용하지 못하도록 방지하며 HTTP/2와 여러 신규 웹 플랫폼 API의 필수 요건이기도 합니다. [자세히 알아보기](https://web.dev/is-on-https)"}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{안전하지 않은 요청 1건 검색됨}other{안전하지 않은 요청 #건 검색됨}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "HTTPS 사용하지 않음"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "HTTPS 사용"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "셀룰러 네트워크에서 페이지 로드가 빠르면 우수한 모바일 사용자 환경을 제공할 수 있습니다. [자세히 알아보기](https://web.dev/load-fast-enough-for-pwa)"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "페이지와 상호작용할 수 있게 될 때까지 걸린 시간: {timeInMs, number, seconds}초"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "시뮬레이션된 모바일 네트워크에서 페이지와 상호작용할 수 있게 될 때까지 걸린 시간: {timeInMs, number, seconds}초"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "페이지가 너무 느리게 로드되므로 10초 이내에 사용할 수 없습니다. '성능' 섹션에서 추천 내용과 진단을 확인해 개선 방안을 알아보세요."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "모바일 네트워크에서 페이지 로딩이 충분히 빠르지 않음"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "모바일 네트워크에서 페이지 로드가 충분히 빠름"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "카테고리"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "JS 파싱, 컴파일, 실행에 소요되는 시간을 줄여 보세요. 용량이 적은 JS 페이로드를 제공하면 도움이 될 수 있습니다. [자세히 알아보기](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "기본 스레드 작업 최소화하기"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "기본 스레드 작업 최소화하기"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "최대한 많은 사용자가 이용할 수 있으려면 사이트가 모든 주요 브라우저에서 작동해야 합니다. [자세히 알아보기](https://web.dev/pwa-cross-browser)"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "사이트가 다양한 브라우저에서 작동함"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "페이지를 소셜 미디어에 공유하려면 개별 페이지가 URL을 통해 딥 링크로 연결될 수 있어야 하며 페이지별로 URL이 달라야 합니다. [자세히 알아보기](https://web.dev/pwa-each-page-has-url)"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "페이지마다 URL이 있음"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "탭을 했을 때 느린 네트워크에서도 전환이 빠르게 느껴져야 합니다. 이 환경은 사용자가 체감하는 성능의 핵심입니다. [자세히 알아보기](https://web.dev/pwa-page-transitions)"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "페이지 전환 시 네트워크에서 막히는 느낌이 들지 않음"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "예상 입력 대기시간은 페이지 로드가 가장 많은 5초 동안 앱이 사용자 입력에 응답하는 데 걸리는 시간(밀리초)의 추정치입니다. 지연 시간이 50밀리초보다 길면 사용자가 앱이 느리다고 인식할 수 있습니다. [자세히 알아보기](https://web.dev/estimated-input-latency)"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "예상 입력 대기시간"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "콘텐츠가 포함된 첫 페인트는 첫 번째 텍스트 또는 이미지가 표시되는 시간을 나타냅니다. [자세히 알아보기](https://web.dev/first-contentful-paint)"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "최초 만족 페인트"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "최초 CPU 유휴 상태는 페이지의 기본 스레드가 입력을 처리할 수 있을 만큼 조용한 상태가 된 첫 번째 시간을 표시합니다.  [자세히 알아보기](https://web.dev/first-cpu-idle)"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "최초 CPU 유휴 상태"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "유의미한 첫 페인트는 페이지의 기본 콘텐츠가 표시되는 경우를 측정합니다. [자세히 알아보기](https://web.dev/first-meaningful-paint)"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "최초 유의미 페인트"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "사용할 수 있을 때까지 걸리는 시간은 완전히 페이지와 상호작용할 수 있게 될 때까지 걸리는 시간입니다. [자세히 알아보기](https://web.dev/interactive)"}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "상호작용 시간"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "사용자가 경험할 수 있는 최대 첫 입력 지연 예상 시간은 가장 긴 작업의 길이(밀리초)입니다. [자세히 알아보기](https://developers.google.com/web/updates/2018/05/first-input-delay)"}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "최대 첫 입력 지연 예상 시간"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "속도 색인은 페이지 콘텐츠가 얼마나 빨리 표시되는지 보여줍니다. [자세히 알아보기](https://web.dev/speed-index)"}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "속도 색인"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "FCP와 상호작용 시간 사이의 모든 시간의 합으로 작업 지속 시간이 50ms를 넘으면 밀리초 단위로 표현됩니다."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "총 차단 시간"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "네트워크 왕복 시간(RTT)이 성능에 큰 영향을 줍니다. 출발지로의 RTT가 크면 서버가 사용자에게 가까울 때 성능이 향상될 수 있음을 나타냅니다. [자세히 알아보기](https://hpbn.co/primer-on-latency-and-bandwidth/)"}, "lighthouse-core/audits/network-rtt.js | title": {"message": "네트워크 왕복 시간"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "서버 지연 시간은 웹 성능에 영향을 줄 수 있습니다. 출발지의 서버 지연 시간이 길면 서버에 과부하가 걸렸거나 백엔드 성능이 낮음을 나타냅니다. [자세히 알아보기](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)"}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "서버 백엔드 지연 시간"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "서비스 워커를 사용하면 예측 불가능한 네트워크 상태에서도 웹 앱이 안정적으로 작동할 수 있습니다. [자세히 알아보기](https://web.dev/offline-start-url)"}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url`이(가) 오프라인 시 200으로 응답하지 않음"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url`이(가) 오프라인 시 200으로 응답함"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "Lighthouse가 매니페스트에서 `start_url`을(를) 읽지 못했습니다. 그 결과 `start_url`은(는) 문서 URL인 것으로 간주되었습니다. 오류 메시지는 '{manifestWarning}'입니다."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "예산 초과"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "네트워크 요청의 양과 크기는 제공된 성능 예산에 따라 설정된 목표치 아래로 유지하세요. [자세히 알아보기](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{요청 1건}other{요청 #건}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "성능 예산"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "HTTPS를 이미 설정한 경우 모든 사용자가 안전한 웹 기능을 사용하기 위해서는 모든 HTTP 트래픽이 HTTPS로 리디렉션되어야 합니다. [자세히 알아보기](https://web.dev/redirects-http)"}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "HTTP 트래픽을 HTTPS로 리디렉션하지 않음"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "HTTP 트래픽을 HTTPS로 리디렉션함"}, "lighthouse-core/audits/redirects.js | description": {"message": "리디렉션을 사용하면 페이지가 로드되기 전 추가적인 지연이 발생합니다. [자세히 알아보기](https://web.dev/redirects)"}, "lighthouse-core/audits/redirects.js | title": {"message": "여러 차례의 페이지 리디렉션 피하기"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "페이지 리소스의 수량과 크기와 관련해 예산을 설정하려면 budget.json 파일을 추가하세요. [자세히 알아보기](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{요청 1건 • {byteCount, number, bytes}KB}other{요청 #건 • {byteCount, number, bytes}KB}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "요청 수는 낮게, 전송 크기는 작게 유지하기"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "표준 링크는 검색결과에 어떤 URL을 표시할지 알려줍니다. [자세히 알아보기](https://web.dev/canonical)"}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "여러 개의 URL({urlList})이 충돌됨"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "다른 도메인({url})을 가리킵니다."}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "잘못된 URL({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "다른 `hreflang` 위치({url})를 가리킵니다."}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "상대 URL({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "콘텐츠 페이지가 아닌 도메인의 루트 URL(홈페이지)을 가리킴"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "문서에 유효한 `rel=canonical` 없음"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "문서에 유효한 `rel=canonical` 있음"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "12px보다 글꼴 크기가 작으면 읽기 어렵기 때문에 모바일 방문자가 '손가락으로 확대'해야만 읽을 수 있습니다. 페이지 텍스트의 60% 이상을 12px 이상으로 유지하도록 노력하세요. [자세히 알아보기](https://web.dev/font-size)"}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} 읽기 쉬운 텍스트"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "모바일 화면에 최적화된 표시 영역 메타 태그가 없어서 텍스트를 알아볼 수 없음"}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "텍스트의 {decimalProportion, number, extendedPercent} 값이 너무 작습니다({decimalProportionVisited, number, extendedPercent} 샘플 기준)."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "문서에서 읽기 쉬운 글꼴 크기를 사용하지 않음"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "문서가 읽기 쉬운 글꼴 크기를 사용함"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "hreflang 링크는 검색엔진에 특정 언어나 지역에서 페이지의 어떤 버전을 검색결과로 표시해야 할지 알려줍니다. [자세히 알아보기](https://web.dev/hreflang)"}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "문서에 유효한 `hreflang` 없음"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "문서에 유효한 `hreflang` 있음"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "실패한 HTTP 상태 코드가 있는 페이지는 제대로 색인이 생성되지 않을 수 있습니다. [자세히 알아보기](https://web.dev/http-status-code)"}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "페이지에 실패한 HTTP 상태 코드가 있음"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "페이지에 성공적인 HTTP 상태 코드가 있음"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "검색엔진이 페이지를 크롤링할 권한이 없으면 검색결과에 포함할 수 없습니다. [자세히 알아보기](https://web.dev/is-crawable)"}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "페이지의 색인 생성이 차단됨"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "페이지의 색인 생성이 차단되지 않음"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "구체적인 링크 텍스트는 검색엔진에서 콘텐츠를 이해하는 데 도움이 됩니다. [자세히 알아보기](https://web.dev/link-text)"}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{링크 1개 찾음}other{링크 #개 찾음}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "링크에 설명 텍스트가 없음"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "링크에 설명 텍스트가 있음"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "[구조화된 데이터용 테스트 도구](https://search.google.com/structured-data/testing-tool/)와 [구조화된 데이터 Linter](http://linter.structured-data.org/)를 실행하여 구조화된 데이터를 검증합니다. [자세히 알아보기](https://web.dev/structured-data)"}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "구조화된 데이터가 유효함"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "검색결과에 페이지 콘텐츠를 간략하게 요약하기 위한 메타 설명이 포함될 수 있습니다. [자세히 알아보기](https://web.dev/meta-description)"}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "설명 텍스트가 비어 있습니다."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "문서에 메타 설명이 없음"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "문서에 메타 설명이 있음"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "검색엔진은 플러그인 콘텐츠의 색인을 생성할 수 없고 플러그인을 제한하거나 지원하지 않는 기기도 많습니다. [자세히 알아보기](https://web.dev/plugins)"}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "문서가 플러그인을 사용함"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "문서에서 플러그인을 사용할 수 없음"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "robots.txt 파일 형식이 잘못된 경우 크롤러가 웹사이트를 어떻게 크롤링하고 색인을 생성해야 할지 파악하지 못할 수 있습니다. [자세히 알아보기](https://web.dev/robots-txt)"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "robots.txt 요청에 반환된 HTTP 상태: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{오류 1개 발견}other{오류 #개 발견}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse에서 robots.txt 파일을 다운로드할 수 없음"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt가 유효하지 않음"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "robots.txt가 유효함"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "버튼이나 링크 같은 상호작용 요소는 충분히 커야 하며(48 x 48px), 주변에 충분한 공간이 있고 다른 요소와 겹치지 않아 쉽게 탭할 수 있어야 합니다. [자세히 알아보기](https://web.dev/tap-targets)"}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} 탭 타겟 크기가 적절함"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "모바일 화면에 최적화된 표시 영역 메타 태그가 없어서 탭 타겟이 너무 작음"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "탭 타겟 크기가 적절하지 않음"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "타겟이 중복됨"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "탭 타겟"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "탭 타겟의 크기가 적절함"}, "lighthouse-core/audits/service-worker.js | description": {"message": "서비스 워커는 앱에서 오프라인, 홈 화면에 추가, 푸시 알림 등 다양한 프로그레시브 웹 앱 기능을 사용할 수 있도록 설정하는 기술입니다. [자세히 알아보기](https://web.dev/service-worker)"}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "이 페이지는 서비스 워커로 인해 제어되지만 매니페스트가 유효한 JSON으로 파싱하는 데 실패했으므로 `start_url`을(를) 찾지 못했습니다."}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "이 페이지는 서비스 워커로 인해 제어되지만 `start_url`({startUrl})이(가) 서비스 워커의 범위({scopeUrl})에 있지 않습니다."}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "이 페이지는 서비스 워커로 인해 제어되지만 가져온 매니페스트가 없으므로 `start_url`을(를) 찾지 못했습니다."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "이 원본에 하나 이상의 서비스 워커가 있지만 페이지({pageUrl})가 범위 내에 있지 않습니다."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "페이지와 `start_url`을(를) 제어하는 서비스 워커를 등록하지 않음"}, "lighthouse-core/audits/service-worker.js | title": {"message": "페이지와 `start_url`을(를) 제어하는 서비스 워커를 등록함"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "테마 스플래시 화면을 사용하면 사용자가 홈 화면에서 앱을 실행했을 때 고품질의 환경을 경험할 수 있습니다. [자세히 알아보기](https://web.dev/splash-screen)"}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "맞춤 스플래시 화면에 맞게 구성되지 않음"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "맞춤 스플래시 화면에 맞게 구성됨"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "브라우저 주소 표시줄에는 사이트와 맞는 테마를 설정할 수 있습니다. [자세히 알아보기](https://web.dev/themed-omnibox)"}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "주소 표시줄의 테마 색상을 설정하지 않음"}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "주소 표시줄의 테마 색상을 설정함"}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "기본 스레드 차단 시간"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "타사"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "타사 코드는 로드 성능에 크게 영향을 미칠 수 있습니다. 페이지에서 먼저 로딩을 끝낸 후 중복되는 타사 공급업체의 수를 제한하고 타사 코드를 로드해 보세요. [자세히 알아보기](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)"}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "타사 코드가 {timeInMs, number, milliseconds} ms 동안 기본 스레드를 차단했습니다."}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "타사 코드의 영향을 줄임"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "타사 사용"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "Time To First Byte는 서버가 응답을 보내는 시간을 식별합니다. [자세히 알아보기](https://web.dev/time-to-first-byte)"}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "루트 문서에 {timeInMs, number, milliseconds} ms 소요됨"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "서버 응답 시간(TTFB) 단축"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "서버 응답 시간 낮음(TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "시간"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "시작 시간"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "유형"}, "lighthouse-core/audits/user-timings.js | description": {"message": "앱에서 User Timing API를 사용하여 중요 사용자 경험이 이루어지는 동안의 실제 성능을 측정하세요. [자세히 알아보기](https://web.dev/user-timings)"}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{사용자 시간 1회}other{사용자 시간 #회}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "사용자 타이밍 표시 및 측정 값"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "‘{<PERSON><PERSON><PERSON><PERSON>}’에 사전 연결 <link>가 있으나 브라우저에서 사용되지 않습니다. `crossorigin` 속성을 올바르게 사용하고 있는지 확인하세요."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "`preconnect` 또는 `dns-prefetch` 리소스 힌트를 추가하여 중요한 타사 원본에 대한 조기 연결을 수립하는 것이 좋습니다. [자세히 알아보기](https://web.dev/uses-rel-preconnect)"}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "필수 원본 미리 연결하기"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "'{preloadURL}'에 사전 로드 <link>가 있으나 브라우저에서 사용되지 않습니다. `crossorigin` 속성을 올바르게 사용하고 있는지 확인하세요."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "`<link rel=preload>`을(를) 사용하여 현재 요청되는 리소스를 페이지 로드에 나중에 가져올 때 우선순위를 정하는 것이 좋습니다. [자세히 알아보기](https://web.dev/uses-rel-preload)"}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "중요한 요청을 미리 로드하기"}, "lighthouse-core/audits/viewport.js | description": {"message": "휴대기기 화면에 앱을 최적화하려면 `<meta name=\"viewport\">` 태그를 추가합니다. [자세히 알아보기](https://web.dev/viewport)"}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "`<meta name=\"viewport\">` 태그 없음"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "`width` 또는 `initial-scale`이(가) 포함된 `<meta name=\"viewport\">` 태그가 없음"}, "lighthouse-core/audits/viewport.js | title": {"message": "`width` 또는 `initial-scale`이(가) 포함된 `<meta name=\"viewport\">` 태그가 있음"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "JavaScript가 사용 중지되었더라도 사용자에게 앱을 사용하려면 JavaScript가 필요하다는 경고 등의 일부 콘텐츠는 앱에 표시되어야 합니다. [자세히 알아보기](https://web.dev/without-javascript)"}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "페이지 본문에서 스크립트를 사용할 수 없는 경우 본문에서 콘텐츠를 일부 렌더링해야 합니다."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "JavaScript를 사용할 수 없는 경우 대체 콘텐츠를 제공하지 않음"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "JavaScript를 사용할 수 없는 경우에도 일부 콘텐츠를 포함함"}, "lighthouse-core/audits/works-offline.js | description": {"message": "프로그레시브 웹 앱을 빌드할 경우 앱이 오프라인에서도 작동할 수 있도록 서비스 워커를 사용하는 것이 좋습니다. [자세히 알아보기](https://web.dev/works-offline)"}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "현재 페이지가 오프라인 시 200으로 응답하지 않음"}, "lighthouse-core/audits/works-offline.js | title": {"message": "현재 페이지가 오프라인 시 200으로 응답함"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "테스트 URL({requested})이 '{final}'(으)로 리디렉션되므로 이 페이지가 오프라인 상태에서는 로드되지 않을 수 있습니다. 두 번째 URL로 직접 테스트해 보세요."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "애플리케이션의 ARIA 사용을 개선하도록 추천된 사항입니다. 이를 통해 스크린 리더와 같은 지원 기술을 사용하는 사용자의 환경을 개선할 수 있습니다."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "오디오 및 비디오의 대체 콘텐츠를 제공하도록 추천된 사항입니다. 이를 통해 청각 장애나 시각 장애가 있는 사용자의 환경을 개선할 수 있습니다."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "오디오 및 동영상"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "일반적인 접근성 권장사항을 강조표시합니다."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "권장사항"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "이 검사에서는 [웹 앱의 접근성을 개선](https://developers.google.com/web/fundamentals/accessibility)하도록 추천된 사항을 강조합니다. 접근성 문제의 일부만 자동으로 감지되므로 직접 테스트하는 것도 좋습니다."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "자동화된 테스트 도구가 처리할 수 없는 영역을 다루는 항목입니다. [접근성 검토 실시](https://developers.google.com/web/fundamentals/accessibility/how-to-review)에 관한 가이드에서 자세히 알아보세요."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "접근성"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "콘텐츠의 가독성을 개선할 추천 내용입니다."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "대비"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "다른 언어를 사용하는 사용자가 콘텐츠를 더 쉽게 이해할 수 있게 개선하도록 추천된 사항입니다."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "다국어화 및 현지화"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "애플리케이션 컨트롤의 의미 해석을 개선하도록 추천된 사항입니다. 이를 통해 스크린 리더와 같은 지원 기술을 사용하는 사용자의 환경을 개선할 수 있습니다."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "이름 및 라벨"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "애플리케이션에서 키보드 탐색을 개선하도록 추천된 사항입니다."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "탐색"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "다음은 스크린 리더와 같은 보조 기술을 사용하여 표나 목록 데이터를 읽는 환경을 개선하도록 추천된 사항입니다."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "표와 목록"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "권장사항"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "성능 예산은 사이트 성능의 표준을 설정합니다."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "예산"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "애플리케이션 성능과 관련된 추가 정보입니다. 이러한 숫자는 성능 점수에 [직접적인 영향](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)을 미치지 않습니다."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "진단"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "성능에서 가장 중요한 측면은 픽셀이 화면에 렌더링되는 속도입니다. 주요 측정항목: 최초 만족 페인트, 최초 유의미 페인트"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "최초 페인트 개선"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "이러한 권장사항은 페이지를 더 빠르게 로드하는 데 도움이 될 수 있습니다. 성능 점수에는 [직접적인 영향](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)을 미치지 않습니다."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "추천"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "측정항목"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "페이지가 빠르게 반응하고 가능한 한 빨리 사용할 수 있는 준비가 되도록 전반적인 로드 환경을 강화하세요. 주요 측정항목: 상호작용 시간, 속도 색인"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "전반적인 개선사항"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "성능"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "이 검사는 프로그레시브 웹 앱의 요소를 검사합니다. [자세히 알아보기](https://developers.google.com/web/progressive-web-apps/checklist)"}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "이 검사는 기준 [PWA 체크리스트](https://developers.google.com/web/progressive-web-apps/checklist)의 필수 항목이지만 Lighthouse에서 자동으로 확인되지는 않습니다. 점수에 영향을 미치지는 않지만 직접 확인하는 것이 중요합니다."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "프로그레시브 웹 앱"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "속도와 안정성"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "설치 가능"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PWA 최적화됨"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "이와 같은 검사를 통해 페이지가 검색엔진 결과 순위에 최적화되도록 할 수 있습니다. Lighthouse에서 점검하지 않는 추가적인 요소가 검색 순위에 영향을 줄 수도 있습니다. [자세히 알아보기](https://support.google.com/webmasters/answer/35769)"}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "사이트에서 이러한 추가 검증 도구를 실행하여 추가적인 검색엔진 최적화 권장사항을 확인합니다."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "검색엔진 최적화"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "크롤러가 앱 콘텐츠를 효과적으로 파악할 수 있도록 HTML 형식을 지정하세요."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "콘텐츠 권장사항"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "검색결과에 표시하려면 크롤러가 앱에 액세스할 수 있어야 합니다."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "크롤링 및 색인 생성"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "사용자가 콘텐츠를 읽기 위해 페이지를 확대하거나 축소할 필요가 없도록 모바일 친화적인 페이지를 지원하세요. [자세히 알아보기](https://developers.google.com/search/mobile-sites/)"}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "모바일 친화적"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "캐시 TTL"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "위치"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "이름"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "요청"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "리소스 유형"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "크기"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "소요 시간"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "전송 크기"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "가능한 절감 효과"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "가능한 절감 효과"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "절감 가능치: {wastedBytes, number, bytes} KB"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "절감 가능치: {wastedMs, number, milliseconds} 밀리초"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "문서"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "글꼴"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "이미지"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "미디어"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} 밀리초"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "기타"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "스크립트"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} 초"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "스타일시트"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "타사"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "합계"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "페이지 로드 중 처리한 추적을 기록하던 중 문제가 발생했습니다. Lighthouse를 다시 실행하세요. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "초기 디버거 프로토콜 연결 대기 시간이 초과되었습니다."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome이 페이지 로드 중 스크린샷을 수집하지 않았습니다. 페이지에 표시된 콘텐츠가 있는지 확인한 다음 Lighthouse를 다시 실행해 보세요. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "DNS 서버에서 제공된 도메인을 해결하지 못했습니다."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "필수 {artifactName} 수집기에 다음 오류가 발생했습니다. {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "Chrome 내부 오류가 발생했습니다. Chrome을 다시 시작한 다음 Lighthouse를 다시 실행해 주세요."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "필수 {artifactName} 수집기가 실행되지 않았습니다."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse에서 사용자가 요청한 페이지를 안정적으로 로드하지 못했습니다. 올바른 URL을 테스트하고 있으며 서버에서 모든 요청에 적절하게 응답하고 있는지 확인하세요."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "페이지 응답이 중지되었기 때문에 Lighthouse에서 사용자가 요청한 URL을 안정적으로 로드하지 못했습니다."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "제공한 URL에 유효한 보안 인증서가 포함되어 있지 않습니다. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome에서 전면 광고가 있는 페이지를 로드하지 못하도록 했습니다. 올바른 URL로 테스트하고 있으며 서버가 모든 요청에 적절하게 응답하고 있는지 확인하세요."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse에서 사용자가 요청한 페이지를 안정적으로 로드하지 못했습니다. 올바른 URL로 테스트하고 있으며 서버가 모든 요청에 적절하게 응답하고 있는지 확인하세요. (세부정보: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse에서 사용자가 요청한 페이지를 안정적으로 로드하지 못했습니다. 올바른 URL로 테스트하고 있으며 서버가 모든 요청에 적절하게 응답하고 있는지 확인하세요. (상태 코드: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "페이지 로드에 시간이 너무 오래 걸립니다. 보고서의 추천에 따라 페이지 로드 시간을 줄인 다음 Lighthouse를 다시 실행해 보세요. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "DevTools 프로토콜 응답 대기가 할당된 시간을 초과했습니다. (메서드: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "리소스 콘텐츠 가져오기 시간이 할당된 시간을 초과했습니다"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "사용자가 제공한 URL이 잘못되었습니다."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "감사 보기"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "초기 탐색"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "최상 경로 최대 지연 시간:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "오류!"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "보고 오류: 감사 정보 없음"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "실험실 데이터"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "에뮬레이션된 모바일 네트워크에서 분석한 현재 페이지의 [Lighthouse](https://developers.google.com/web/tools/lighthouse/) 결과입니다. 값은 추정치이며 달라질 수 있습니다."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "직접 확인해야 하는 추가 항목"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "해당 사항 없음"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "추천"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "예상 절감치"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "통과한 감사"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "스니펫 접기"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "스니펫 펼치기"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "타사 리소스 표시"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "Lighthouse 실행에 영향을 미치는 문제가 발생했습니다."}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "값은 추정치이며 달라질 수 있습니다. 성능 점수는 [이 측정항목만을 기준](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)으로 합니다."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "감사를 통과했으나 경고를 받음"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "경고: "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "GIF를 HTML5 동영상으로 삽입할 수 있게 해주는 서비스에 GIF를 업로드하는 것이 좋습니다."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "[지연 로드 WordPress 플러그인](https://wordpress.org/plugins/search/lazy+load/)을 설치하면 화면에 모든 오프스크린 이미지를 지연하거나 이 기능을 제공하는 테마로 전환할 수 있습니다. [AMP 플러그인](https://wordpress.org/plugins/amp/) 사용도 고려해 보세요."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "[중요한 애셋을 인라인](https://wordpress.org/plugins/search/critical+css/)하거나 [덜 중요한 리소스를 지연](https://wordpress.org/plugins/search/defer+css+javascript/)할 수 있게 도와주는 다양한 WordPress 플러그인이 있습니다. 이러한 플러그인에서 제공하는 최적화로 인해 내가 사용하는 테마 또는 플러그인의 기능이 깨질 수 있으므로 코드를 변경해야 할 가능성이 큽니다."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "테마, 플러그인, 서버 사양은 모두 서버 응답 시간을 길어지게 만듭니다. 보다 최적화된 테마를 찾고, 최적화 플러그인을 신중하게 선택하고, 서버 업그레이드를 고려해 보세요."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "게시물 목록에 발췌문을 표시하거나(예: 더 많은 태그 사용), 특정 페이지에 표시되는 게시물의 개수를 줄이거나, 길이가 긴 게시물을 여러 페이지로 나누거나, 플러그인을 사용하여 댓글을 지연 로드하는 것이 좋습니다."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "다양한 [WordPress 플러그인](https://wordpress.org/plugins/search/minify+css/)을 사용해 스타일을 연결, 축소, 압축함으로써 사이트 속도를 높일 수 있습니다. 또한 가능한 경우 빌드 프로세스를 사용하여 축소 작업을 미리 실행할 수도 있습니다."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "다양한 [WordPress 플러그인](https://wordpress.org/plugins/search/minify+javascript/)을 사용해 스크립트를 연결, 축소, 압축함으로써 사이트 속도를 높일 수 있습니다. 또한 가능한 경우 빌드 프로세스를 사용하여 축소 작업을 미리 실행할 수도 있습니다."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "페이지에서 사용되지 않는 CSS를 로드하는 [WordPress 플러그인](https://wordpress.org/plugins/) 개수를 줄이거나 전환하는 것이 좋습니다. 과도한 CSS를 부가하는 플러그인이 무엇인지 확인하려면 Chrome DevTools의 [코드 범위](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)를 실행해 보세요. 스타일시트의 URL에서 관련된 테마 또는 플러그인을 확인할 수 있습니다. 목록에서 코드 범위에 빨간색이 길게 표시된 스타일시트가 있는 플러그인을 찾아보세요. 실제로 페이지에서 사용되는 플러그인은 대기열에 하나의 스타일시트만 포함해야 합니다."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "페이지에서 사용되지 않는 JavaScript를 로드하는 [WordPress 플러그인](https://wordpress.org/plugins/) 개수를 줄이거나 전환하는 것이 좋습니다. 과도한 JS를 부가하는 플러그인이 무엇인지 확인하려면 Chrome DevTools의 [코드 범위](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)를 실행해 보세요. 스크립트의 URL에서 관련 테마 또는 플러그인을 확인할 수 있습니다. 목록에서 코드 범위에 빨간색이 길게 표시된 스크립트가 있는 플러그인을 찾아보세요. 실제로 페이지에서 사용되는 플러그인은 대기열에 하나의 스크립트만 포함해야 합니다."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "[WordPress 브라우저 캐싱](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)에 관해 자세히 알아보세요."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "품질은 그대로 유지하면서 이미지를 압축해주는 [이미지 최적화 WordPress 플러그인](https://wordpress.org/plugins/search/optimize+images/)을 사용해 보세요."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "필수 이미지 크기를 사용할 수 있도록 [미디어 라이브러리](https://codex.wordpress.org/Media_Library_Screen)에서 직접 이미지를 업로드하세요. 그런 다음 미디어 라이브러리에서 이미지를 삽입하거나 이미지 위젯을 사용하여 최적의 이미지 크기를 사용하는지 확인하세요(반응형 중단점용 이미지 포함). 이미지 크기가 용도에 적합하지 않다면 `Full Size` 이미지는 사용하지 않는 것이 좋습니다. [자세히 알아보기](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)"}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "웹 서버 구성에서 텍스트 압축을 사용 설정할 수 있습니다."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "업로드한 이미지를 최적화된 양식으로 자동 변환해주는 [플러그인](https://wordpress.org/plugins/search/convert+webp/) 또는 서비스를 사용하는 것이 좋습니다."}}