{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "คีย์การเข้าถึงให้ผู้ใช้โฟกัสที่ส่วนหนึ่งของหน้าได้อย่างรวดเร็ว คีย์การเข้าถึงแต่ละรายการต้องไม่ซ้ำกันเพื่อให้ไปยังส่วนต่างๆ ได้อย่างถูกต้อง [ดูข้อมูลเพิ่มเติม](https://web.dev/accesskeys/)"}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "ค่า `[accesskey]` ซ้ำกัน"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "ค่า `[accesskey]` ไม่ซ้ำกัน"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "`role` ของ ARIA แต่ละรายการรองรับชุดย่อยของแอตทริบิวต์ `aria-*` ที่เจาะจง หากรายการเหล่านี้ไม่ตรงกันจะทำให้แอตทริบิวต์ `aria-*` ไม่ถูกต้อง [ดูข้อมูลเพิ่มเติม](https://web.dev/aria-allowed-attr/)"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "แอตทริบิวต์ `[aria-*]` ไม่ตรงกับบทบาทของตน"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "แอตทริบิวต์ `[aria-*]` ตรงกับบทบาทของตน"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "บทบาท ARIA บางบทบาทกำหนดให้มีแอตทริบิวต์ที่อธิบายสถานะขององค์ประกอบให้โปรแกรมอ่านหน้าจอทราบ [ดูข้อมูลเพิ่มเติม](https://web.dev/aria-required-attr/)"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]` ไม่มีแอตทริบิวต์ `[aria-*]` ทั้งหมดที่จำเป็น"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]` มีแอตทริบิวต์ `[aria-*]` ที่จำเป็นทั้งหมด"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "บทบาท ARIA ระดับบนสุดบางบทบาทต้องมีบทบาทย่อยที่เจาะจงเพื่อใช้ฟังก์ชันการช่วยเหลือพิเศษตามวัตถุประสงค์ [ดูข้อมูลเพิ่มเติม](https://web.dev/aria-required-children/)"}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "องค์ประกอบที่มี ARIA `[role]` ที่กำหนดให้องค์ประกอบย่อยต้องมี `[role]` ที่เฉพาะเจาะจงขาดองค์ประกอบย่อยที่จำเป็นดังกล่าวบางส่วนหรือทั้งหมด"}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "องค์ประกอบที่มี ARIA `[role]` ที่กำหนดให้องค์ประกอบย่อยต้องมี `[role]` ที่เฉพาะเจาะจงนั้นมีองค์ประกอบย่อยที่จำเป็นทั้งหมด"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "บทบาท ARIA ย่อยบางบทบาทต้องอยู่ในบทบาทระดับบนสุดที่เจาะจงเพื่อให้ใช้ฟังก์ชันการช่วยเหลือพิเศษตามวัตถุประสงค์ได้อย่างถูกต้อง [ดูข้อมูลเพิ่มเติม](https://web.dev/aria-required-parent/)"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]` ไม่ได้อยู่ในองค์ประกอบระดับบนสุดที่กำหนด"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]` อยู่ในองค์ประกอบระดับบนสุดที่กำหนด"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "บทบาท ARIA ต้องมีค่าที่ถูกต้องเพื่อใช้ฟังก์ชันการช่วยเหลือพิเศษตามวัตถุประสงค์ [ดูข้อมูลเพิ่มเติม](https://web.dev/aria-roles/)"}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "ค่า `[role]` ไม่ถูกต้อง"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "ค่า `[role]` ถูกต้อง"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "เทคโนโลยีอำนวยความสะดวก เช่น โปรแกรมอ่านหน้าจอ จะตีความแอตทริบิวต์ ARIA ที่มีค่าไม่ถูกต้องไม่ได้ [ดูข้อมูลเพิ่มเติม](https://web.dev/aria-valid-attr-value/)"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "แอตทริบิวต์ `[aria-*]` ไม่มีค่าที่ถูกต้อง"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "แอตทริบิวต์ `[aria-*]` มีค่าที่ถูกต้อง"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "เทคโนโลยีอำนวยความสะดวก เช่น โปรแกรมอ่านหน้าจอ จะตีความแอตทริบิวต์ ARIA ที่มีชื่อไม่ถูกต้องไม่ได้ [ดูข้อมูลเพิ่มเติม](https://web.dev/aria-valid-attr/)"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "แอตทริบิวต์ `[aria-*]` ไม่ถูกต้องหรือสะกดผิด"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "แอตทริบิวต์ `[aria-*]` ถูกต้องและสะกดถูกต้อง"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "คำบรรยายวิดีโอช่วยให้คนหูหนวกหรือผู้ใช้ที่มีความบกพร่องทางการได้ยินเข้าใจองค์ประกอบเสียง โดยให้ข้อมูลสำคัญ เช่น ผู้ที่กำลังพูด สิ่งที่กำลังพูด และข้อมูลอื่นๆ ที่ไม่ใช่เสียงพูด [ดูข้อมูลเพิ่มเติม](https://web.dev/audio-caption/)"}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "องค์ประกอบ `<audio>` ไม่มีองค์ประกอบ `<track>` ที่มี`[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "องค์ประกอบ `<audio>` มีองค์ประกอบ `<track>` ที่มี `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "องค์ประกอบที่ไม่ผ่านการตรวจสอบ"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "เมื่อปุ่มไม่มีชื่อสำหรับการช่วยเหลือพิเศษ โปรแกรมอ่านหน้าจอจะอ่านปุ่มนั้นว่า \"ปุ่ม\" ซึ่งทำให้ผู้ที่ต้องใช้โปรแกรมอ่านหน้าจอใช้ปุ่มดังกล่าวไม่ได้ [ดูข้อมูลเพิ่มเติม](https://web.dev/button-name/)"}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "ปุ่มต่างๆ ไม่มีชื่อสำหรับการช่วยเหลือพิเศษ"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "ปุ่มต่างๆ มีชื่อสำหรับการช่วยเหลือพิเศษ"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "การเพิ่มวิธีข้ามผ่านเนื้อหาที่ซ้ำกันช่วยให้ผู้ใช้แป้นพิมพ์ไปยังส่วนต่างๆ ของหน้าได้อย่างมีประสิทธิภาพมากขึ้น [ดูข้อมูลเพิ่มเติม](https://web.dev/bypass/)"}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "หน้าเว็บไม่มีส่วนหัว ลิงก์การข้าม หรือภูมิภาคของจุดสังเกต"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "หน้าเว็บมีส่วนหัว ลิงก์การข้าม หรือภูมิภาคของจุดสังเกต"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "ข้อความคอนทราสต์ต่ำมักทำให้ผู้ใช้จำนวนมากอ่านได้ยากหรืออ่านไม่ได้เลย [ดูข้อมูลเพิ่มเติม](https://web.dev/color-contrast/)"}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "สีพื้นหลังและสีพื้นหน้ามีอัตราส่วนคอนทราสต์ไม่เพียงพอ"}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "สีพื้นหลังและสีพื้นหน้ามีอัตราส่วนคอนทราสต์ที่เพียงพอ"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "เมื่อมีการทำเครื่องหมายรายการคำจำกัดความอย่างไม่ถูกต้อง โปรแกรมอ่านหน้าจออาจสร้างเอาต์พุตที่ทำให้สับสนหรือไม่แม่นยำ [ดูข้อมูลเพิ่มเติม](https://web.dev/definition-list/)"}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>` ไม่ได้มีเพียงกลุ่ม `<dt>` และ `<dd>` หรือองค์ประกอบ `<script>` หรือ `<template>` ที่เรียงลำดับอย่างถูกต้อง"}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` มีเพียงกลุ่ม `<dt>` และ `<dd>` องค์ประกอบ `<script>` หรือ `<template>` ที่เรียงลำดับอย่างถูกต้อง"}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "รายการย่อยของคำจำกัดความ (`<dt>` และ `<dd>`) ต้องรวมอยู่ในองค์ประกอบ `<dl>` ระดับบนสุดเพื่อดูแลให้โปรแกรมอ่านหน้าจออ่านได้อย่างถูกต้อง [ดูข้อมูลเพิ่มเติม](https://web.dev/dlitem/)"}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "รายการย่อยของคำจำกัดความไม่ได้รวมอยู่ในองค์ประกอบ `<dl>`"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "รายการย่อยของคำจำกัดความรวมอยู่ในองค์ประกอบ `<dl>`"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "ชื่อช่วยให้ผู้ใช้โปรแกรมอ่านหน้าจอทราบถึงภาพรวมของหน้า และผู้ใช้เครื่องมือค้นหาจะดูความเกี่ยวข้องของหน้ากับการค้นหาของตนจากชื่อเป็นหลัก [ดูข้อมูลเพิ่มเติม](https://web.dev/document-title/)"}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "เอกสารไม่มีองค์ประกอบ `<title>`"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "เอกสารมีองค์ประกอบ `<title>`"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "ค่าของแอตทริบิวต์รหัสต้องไม่ซ้ำกันเพื่อป้องกันไม่ให้เทคโนโลยีอำนวยความสะดวกมองข้ามอินสแตนซ์อื่นๆ [ดูข้อมูลเพิ่มเติม](https://web.dev/duplicate-id/)"}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "แอตทริบิวต์ `[id]` ในหน้าเว็บซ้ำกัน"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "แอตทริบิวต์ `[id]` ในหน้าเว็บไม่ซ้ำกัน"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "ผู้ใช้โปรแกรมอ่านหน้าจอต้องใช้ชื่อเฟรมเพื่ออธิบายเนื้อหาของเฟรม [ดูข้อมูลเพิ่มเติม](https://web.dev/frame-title/)"}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "องค์ประกอบ `<frame>` หรือ `<iframe>` ไม่มีชื่อ"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "องค์ประกอบ `<frame>` หรือ `<iframe>` มีชื่อ"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "หากหน้าเว็บไม่ได้ระบุแอตทริบิวต์ lang โปรแกรมอ่านหน้าจอจะถือว่าหน้าดังกล่าวใช้ภาษาเริ่มต้นที่ผู้ใช้เลือกเมื่อตั้งค่าโปรแกรมอ่านหน้าจอ หากที่จริงแล้วหน้าดังกล่าวไม่ได้ใช้ภาษาเริ่มต้น โปรแกรมอ่านหน้าจออาจอ่านข้อความในหน้าได้ไม่ถูกต้อง [ดูข้อมูลเพิ่มเติม](https://web.dev/html-has-lang/)"}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "องค์ประกอบ `<html>` ไม่มีแอตทริบิวต์ `[lang]`"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "องค์ประกอบ `<html>` มีแอตทริบิวต์ `[lang]`"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "การระบุ[ภาษา BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) ที่ถูกต้องช่วยให้โปรแกรมอ่านหน้าจออ่านข้อความได้อย่างถูกต้อง [ดูข้อมูลเพิ่มเติม](https://web.dev/html-lang-valid/)"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "องค์ประกอบ `<html>` ไม่มีค่าที่ถูกต้องสำหรับแอตทริบิวต์ `[lang]`"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "องค์ประกอบ `<html>` มีค่าที่ถูกต้องสำหรับแอตทริบิวต์ `[lang]`"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "องค์ประกอบเพื่อการให้ข้อมูลควรมีข้อความสำรองที่สั้นกระชับและสื่อความหมาย การใช้แอตทริบิวต์ Alt ที่ว่างเปล่าจะเป็นการเพิกเฉยต่อองค์ประกอบเพื่อการตกแต่ง [ดูข้อมูลเพิ่มเติม](https://web.dev/image-alt/)"}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "องค์ประกอบรูปภาพไม่มีแอตทริบิวต์ `[alt]`"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "องค์ประกอบรูปภาพมีแอตทริบิวต์ `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "เมื่อมีการใช้รูปภาพเป็นปุ่ม `<input>` การระบุข้อความสำรองจะช่วยให้ผู้ใช้โปรแกรมอ่านหน้าจอเข้าใจวัตถุประสงค์ของปุ่มได้ [ดูข้อมูลเพิ่มเติม](https://web.dev/input-image-alt/)"}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "องค์ประกอบ `<input type=\"image\">` ไม่มีข้อความ `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "องค์ประกอบ `<input type=\"image\">` มีข้อความ `[alt]`"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "ป้ายกำกับช่วยดูแลให้เทคโนโลยีอำนวยความสะดวกอย่างเช่น โปรแกรมอ่านหน้าจอ อ่านส่วนควบคุมฟอร์มได้อย่างถูกต้อง [ดูข้อมูลเพิ่มเติม](https://web.dev/label/)"}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "องค์ประกอบฟอร์มไม่มีป้ายกำกับที่เชื่อมโยง"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "องค์ประกอบฟอร์มมีป้ายกำกับที่เชื่อมโยงอยู่"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "ตารางที่ใช้สำหรับการออกแบบต้องไม่มีองค์ประกอบข้อมูล เช่น องค์ประกอบ th หรือคำอธิบายภาพ หรือแอตทริบิวต์สรุป เพราะอาจสร้างความสับสนระหว่างการใช้งานของผู้ใช้โปรแกรมอ่านหน้าจอ [ดูข้อมูลเพิ่มเติม](https://web.dev/layout-table/)"}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "องค์ประกอบ `<table>` สำหรับการนำเสนอไม่หลีกเลี่ยงการใช้ `<th>`, `<caption>` หรือแอตทริบิวต์ `[summary]`"}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "องค์ประกอบ `<table>` สำหรับการนำเสนอหลีกเลี่ยงการใช้ `<th>`, `<caption>` หรือแอตทริบิวต์ `[summary]`"}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "ข้อความลิงก์ (และข้อความสำรองสำหรับรูปภาพเมื่อใช้เป็นลิงก์) ที่แยกแยะได้ ไม่ซ้ำกัน และโฟกัสได้ ช่วยปรับปรุงประสบการณ์การไปยังส่วนต่างๆ สำหรับผู้ใช้โปรแกรมอ่านหน้าจอ [ดูข้อมูลเพิ่มเติม](https://web.dev/link-name/)"}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "ลิงก์ไม่มีชื่อที่แยกแยะได้"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "ลิงก์มีชื่อที่แยกแยะได้"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "โปรแกรมอ่านหน้าจอมีวิธีเฉพาะในการอ่านรายการ การดูแลให้รายการมีโครงสร้างที่ถูกต้องช่วยโปรแกรมอ่านหน้าจอในการอ่านเนื้อหา [ดูข้อมูลเพิ่มเติม](https://web.dev/list/)"}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "รายการไม่ได้มีแต่องค์ประกอบ `<li>` และองค์ประกอบที่รองรับสคริปต์ (`<script>` และ`<template>`)"}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "รายการมีเพียงองค์ประกอบ `<li>` และองค์ประกอบที่รองรับสคริปต์ (`<script>` และ `<template>`)"}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "โปรแกรมอ่านหน้าจอกำหนดให้รายการย่อย (`<li>`) อยู่ใน `<ul>` หรือ `<ol>` ระดับบนสุดเพื่อให้อ่านได้อย่างถูกต้อง [ดูข้อมูลเพิ่มเติม](https://web.dev/listitem/)"}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "รายการย่อย (`<li>`) ไม่ได้อยู่ภายในองค์ประกอบระดับบนสุด `<ul>` หรือ `<ol>`"}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "รายการย่อย (`<li>`) อยู่ในองค์ประกอบระดับบนสุด `<ul>` หรือ `<ol>`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "ผู้ใช้ไม่ได้คาดหวังให้หน้าเว็บรีเฟรชโดยอัตโนมัติ และการรีเฟรชหน้าเว็บจะย้ายโฟกัสกลับไปที่ด้านบนของหน้า ซึ่งอาจทำให้ผู้ใช้ได้รับประสบการณ์การใช้งานที่สับสนหรือน่าหงุดหงิด [ดูข้อมูลเพิ่มเติม](https://web.dev/meta-refresh/)"}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "เอกสารใช้ `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "เอกสารนี้ไม่ได้ใช้ `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "การปิดใช้การซูมจะเป็นปัญหาสำหรับผู้ใช้ที่มีสายตาเลือนรางซึ่งต้องใช้การขยายหน้าจอเพื่อให้ดูเนื้อหาของหน้าเว็บได้อย่างชัดเจน [ดูข้อมูลเพิ่มเติม](https://web.dev/meta-viewport/)"}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "มีการใช้ `[user-scalable=\"no\"]` ในองค์ประกอบ `<meta name=\"viewport\">`หรือแอตทริบิวต์ `[maximum-scale]` น้อยกว่า 5"}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "ไม่มีการใช้ `[user-scalable=\"no\"]` ในองค์ประกอบ `<meta name=\"viewport\">` และแอตทริบิวต์ `[maximum-scale]` ไม่น้อยกว่า 5"}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "โปรแกรมอ่านหน้าจอแปลเนื้อหาที่ไม่ใช่ข้อความไม่ได้ การเพิ่มข้อความแสดงแทนลงในองค์ประกอบ `<object>` ช่วยโปรแกรมอ่านหน้าจอถ่ายทอดความหมายให้แก่ผู้ใช้ [ดูข้อมูลเพิ่มเติม](https://web.dev/object-alt/)"}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "องค์ประกอบ `<object>` ไม่มีข้อความ `[alt]`"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "องค์ประกอบ `<object>` มีข้อความ `[alt]`"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "ค่าที่มากกว่า 0 หมายความว่ามีการจัดเรียงการนำทางที่ชัดเจน แม้ว่าการทำงานนี้จะไม่มีปัญหาในทางเทคนิค แต่มักก่อให้เกิดประสบการณ์การใช้งานที่น่าหงุดหงิดสำหรับผู้ใช้เทคโนโลยีอำนวยความสะดวก [ดูข้อมูลเพิ่มเติม](https://web.dev/tabindex/)"}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "องค์ประกอบบางอย่างมีค่า `[tabindex]` มากกว่า 0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "ไม่มีองค์ประกอบที่มีค่า `[tabindex]` มากกว่า 0"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "โปรแกรมอ่านหน้าจอมีฟีเจอร์ที่ช่วยให้ไปยังส่วนต่างๆ ของตารางได้ง่ายขึ้น การดูแลให้เซลล์ `<td>` ที่ใช้แอตทริบิวต์ `[headers]` อ้างอิงถึงเซลล์อื่นๆ ในตารางเดียวกันเท่านั้นอาจช่วยปรับปรุงประสบการณ์สำหรับผู้ใช้โปรแกรมอ่านหน้าจอ [ดูข้อมูลเพิ่มเติม](https://web.dev/td-headers-attr/)"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "เซลล์ในองค์ประกอบ `<table>` ที่ใช้แอตทริบิวต์ `[headers]` อ้างอิงถึง `id` ขององค์ประกอบที่ไม่พบในตารางเดียวกันนี้"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "เซลล์ในองค์ประกอบ `<table>` ที่ใช้แอตทริบิวต์ `[headers]` อ้างอิงถึงเซลล์ของตารางภายในตารางเดียวกัน"}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "โปรแกรมอ่านหน้าจอมีฟีเจอร์ที่ช่วยให้ไปยังส่วนต่างๆ ของตารางได้ง่ายขึ้น การดูแลให้ส่วนหัวของตารางอ้างอิงถึงชุดเซลล์บางชุดอยู่เสมออาจช่วยปรับปรุงประสบการณ์สำหรับผู้ใช้โปรแกรมอ่านหน้าจอ [ดูข้อมูลเพิ่มเติม](https://web.dev/th-has-data-cells/)"}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "องค์ประกอบ `<th>` และองค์ประกอบที่มี `[role=\"columnheader\"/\"rowheader\"]` ไม่มีเซลล์ข้อมูลที่องค์ประกอบอธิบาย"}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "องค์ประกอบ `<th>` และองค์ประกอบที่มี `[role=\"columnheader\"/\"rowheader\"]` มีเซลล์ข้อมูลที่องค์ประกอบอธิบาย"}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "การระบุ[ภาษา BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) ที่ถูกต้องในองค์ประกอบต่างๆ ช่วยดูแลให้โปรแกรมอ่านหน้าจอออกเสียงข้อความได้อย่างถูกต้อง [ดูข้อมูลเพิ่มเติม](https://web.dev/valid-lang/)"}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "แอตทริบิวต์ `[lang]` ไม่มีค่าที่ถูกต้อง"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "แอตทริบิวต์ `[lang]` มีค่าที่ถูกต้อง"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "เมื่อวิดีโอมีคำอธิบายภาพ คนหูหนวกและผู้ใช้ที่มีความบกพร่องทางการได้ยินจะเข้าถึงข้อมูลของวิดีโอได้ง่ายขึ้น [ดูข้อมูลเพิ่มเติม](https://web.dev/video-caption/)"}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "องค์ประกอบ `<video>` ไม่มีองค์ประกอบ `<track>` ที่มี `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "องค์ประกอบ `<video>` มีองค์ประกอบ `<track>` ที่มี `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "คำอธิบายเสียงให้ข้อมูลเกี่ยวข้องสำหรับวิดีโอที่สื่อสารผ่านบทพูดไม่ได้ เช่น สีหน้าและฉากต่างๆ [ดูข้อมูลเพิ่มเติม](https://web.dev/video-description/)"}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "องค์ประกอบ `<video>` ไม่มีองค์ประกอบ `<track>` ที่มี `[kind=\"description\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "องค์ประกอบ `<video>` มีองค์ประกอบ `<track>` ที่มี `[kind=\"description\"]`"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "ให้ระบุ `apple-touch-icon` เพื่อให้ปรากฏใน iOS ได้อย่างดีที่สุดเมื่อผู้ใช้เพิ่ม Progressive Web App ลงในหน้าจอหลัก โดยต้องชี้ไปที่สี่เหลี่ยมจตุรัสแบบไม่โปร่งใสขนาด 192 พิกเซล (หรือ 180 พิกเซล) รูปแบบ PNG [ดูข้อมูลเพิ่มเติม](https://web.dev/apple-touch-icon/)"}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "ไม่ได้ให้ `apple-touch-icon` ที่ถูกต้อง"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "`apple-touch-icon-precomposed` ไม่อัปเดต แนะนำให้ใช้ `apple-touch-icon` จะดีกว่า"}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "มี `apple-touch-icon` ที่ถูกต้อง"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "ส่วนขยาย Chrome ส่งผลเสียต่อประสิทธิภาพในการโหลดของหน้านี้ ลองตรวจสอบหน้าในโหมดไม่ระบุตัวตนหรือจากโปรไฟล์ Chrome ที่ไม่มีส่วนขยาย"}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "การประเมินสคริปต์"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "การแยกวิเคราะห์สคริปต์"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "เวลา CPU รวม"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "พิจารณาลดเวลาที่ใช้ในการแยกวิเคราะห์ แปลโปรแกรม และดำเนินการกับ JS การส่งเปย์โหลด JS ปริมาณน้อยอาจช่วยในเรื่องนี้ได้ [ดูข้อมูลเพิ่มเติม](https://web.dev/bootup-time)"}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "ลดเวลาในการดำเนินการกับ JavaScript"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "เวลาในการดำเนินการกับ JavaScript"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "GIF ขนาดใหญ่ไม่มีประสิทธิภาพในการแสดงเนื้อหาภาพเคลื่อนไหว พิจารณาใช้วิดีโอ MPEG4/WebM สำหรับภาพเคลื่อนไหวและใช้ PNG/WebP สำหรับภาพนิ่งแทน GIF เพื่อประหยัดไบต์ของเครือข่าย [ดูข้อมูลเพิ่มเติม](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "ใช้รูปแบบวิดีโอสำหรับเนื้อหาภาพเคลื่อนไหว"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "พิจารณาโหลดรูปภาพนอกหน้าจอและรูปภาพที่ซ่อนไว้แบบ Lazy Loading หลังจากที่ทรัพยากรที่สำคัญทั้งหมดโหลดเสร็จแล้วเพื่อลดเวลาในการโต้ตอบ [ดูข้อมูลเพิ่มเติม](https://web.dev/offscreen-images)"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "เลื่อนเวลาโหลดรูปภาพนอกจอภาพ"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "ทรัพยากรบล็อก First Paint ของหน้าเว็บอยู่ พิจารณาแสดง JS/CSS ที่สำคัญในหน้าและเลื่อนเวลาแสดง JS/สไตล์ที่ไม่สำคัญทั้งหมดออกไป [ดูข้อมูลเพิ่มเติม](https://web.dev/render-blocking-resources)"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "กำจัดทรัพยากรที่บล็อกการแสดงผล"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "เปย์โหลดปริมาณมากของเครือข่ายทำให้ผู้ใช้เสียค่าใช้จ่ายสูงและสัมพันธ์กับเวลาการโหลดนานเป็นอย่างมาก [ดูข้อมูลเพิ่มเติม](https://web.dev/total-byte-weight)"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "ขนาดรวมเดิมคือ {totalBytes, number, bytes} KB"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "หลีกเลี่ยงเปย์โหลดเครือข่ายปริมาณมาก"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "หลีกเลี่ยงเปย์โหลดเครือข่ายปริมาณมาก"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "การลดขนาดไฟล์ CSS ช่วยลดขนาดเปย์โหลดของเครือข่ายได้ [ดูข้อมูลเพิ่มเติม](https://web.dev/unminified-css)"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "ลดขนาด CSS"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "การลดขนาดไฟล์ JavaScript ช่วยลดขนาดเปย์โหลดและเวลาในการแยกวิเคราะห์สคริปต์ได้ [ดูข้อมูลเพิ่มเติม](https://web.dev/unminified-javascript)"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "ลดขนาด JavaScript"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "นำกฎที่ไม่มีผลแล้วออกจากสไตล์ชีตและเลื่อนเวลาการโหลด CSS ที่ไม่ได้ใช้สำหรับเนื้อหาครึ่งหน้าบนเพื่อลดจำนวนไบต์ที่ไม่จำเป็นที่กิจกรรมเครือข่ายใช้ [ดูข้อมูลเพิ่มเติม](https://web.dev/unused-css-rules)"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "นำ CSS ที่ไม่ได้ใช้ออก"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "นำ JavaScript ที่ไม่ได้ใช้ออกเพื่อลดจำนวนไบต์ที่กิจกรรมเครือข่ายใช้"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "นำ JavaScript ที่ไม่ได้ใช้ออก"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "อายุการใช้งานแคชที่ยาวนานช่วยเพิ่มการเข้าชมหน้าเว็บซ้ำได้ [ดูข้อมูลเพิ่มเติม](https://web.dev/uses-long-cache-ttl)"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{พบทรัพยากร 1 รายการ}other{พบทรัพยากร # รายการ}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "แสดงเนื้อหาคงที่ที่มีนโยบายแคชที่มีประสิทธิภาพ"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "ใช้นโยบายแคชที่มีประสิทธิภาพกับเนื้อหาคงที่"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "รูปภาพที่ได้รับการเพิ่มประสิทธิภาพจะโหลดได้เร็วขึ้นและใช้อินเทอร์เน็ตมือถือน้อยลง [ดูข้อมูลเพิ่มเติม](https://web.dev/uses-optimized-images)"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "เข้ารหัสรูปภาพอย่างมีประสิทธิภาพ"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "แสดงรูปภาพที่มีขนาดที่เหมาะสมเพื่อประหยัดอินเทอร์เน็ตมือถือและปรับปรุงเวลาในการโหลด [ดูข้อมูลเพิ่มเติม](https://web.dev/uses-responsive-images)"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "ปรับขนาดรูปภาพให้เหมาะสม"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "ทรัพยากรแบบข้อความควรแสดงผลโดยมีการบีบอัด (<PERSON><PERSON><PERSON>, Deflate หรือ Brotli) เพื่อลดจำนวนไบต์เครือข่ายทั้งหมด [ดูข้อมูลเพิ่มเติม](https://web.dev/uses-text-compression)"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "เปิดใช้การบีบอัดข้อความ"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "รูปแบบรูปภาพอย่างเช่น JPEG 2000, JPEG XR และ WebP มักบีบอัดได้ดีกว่า PNG หรือ JPEG ซึ่งหมายความว่าจะดาวน์โหลดได้เร็วขึ้นและใช้อินเทอร์เน็ตน้อยลง [ดูข้อมูลเพิ่มเติม](https://web.dev/uses-webp-images)"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "แสดงรูปภาพในรูปแบบสมัยใหม่"}, "lighthouse-core/audits/content-width.js | description": {"message": "หากความกว้างของเนื้อหาในแอปไม่ตรงกับความกว้างของวิวพอร์ต แอปอาจไม่ได้รับการเพิ่มประสิทธิภาพสำหรับหน้าจออุปกรณ์เคลื่อนที่ [ดูข้อมูลเพิ่มเติม](https://web.dev/content-width)"}, "lighthouse-core/audits/content-width.js | explanation": {"message": "ขนาดวิวพอร์ต {innerWidth} พิกเซลไม่ตรงกับขนาดหน้าต่าง {outerWidth} พิกเซล"}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "ไม่ได้ปรับขนาดเนื้อหาอย่างถูกต้องสำหรับวิวพอร์ต"}, "lighthouse-core/audits/content-width.js | title": {"message": "มีการปรับขนาดเนื้อหาอย่างถูกต้องสำหรับวิวพอร์ต"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "ห่วงโซ่คำขอที่สำคัญด้านล่างแสดงให้เห็นทรัพยากรที่โหลดโดยมีลำดับความสำคัญสูง พิจารณาลดความยาวของห่วงโซ่ ลดขนาดการดาวน์โหลดของทรัพยากร หรือเลื่อนเวลาการดาวน์โหลดทรัพยากรที่ไม่จำเป็นเพื่อปรับปรุงการโหลดหน้าเว็บ [ดูข้อมูลเพิ่มเติม](https://web.dev/critical-request-chains)"}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{พบห่วงโซ่ 1 รายการ}other{พบห่วงโซ่ # รายการ}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "ลดความลึกของคำขอที่สำคัญ"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "การเลิกใช้งาน / คำเตือน"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "บรรทัด"}, "lighthouse-core/audits/deprecations.js | description": {"message": "API ที่เลิกใช้งานแล้วจะถูกนำออกจากเบราว์เซอร์ในท้ายที่สุด [ดูข้อมูลเพิ่มเติม](https://web.dev/deprecations)"}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{พบคำเตือน 1 รายการ}other{พบคำเตือน # รายการ}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "ใช้ API ที่เลิกใช้งานแล้ว"}, "lighthouse-core/audits/deprecations.js | title": {"message": "หลีกเลี่ยงการใช้ API ที่เลิกใช้งานแล้ว"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "แคชของแอปพลิเคชันเลิกใช้งานแล้ว [ดูข้อมูลเพิ่มเติม](https://web.dev/appcache-manifest)"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "พบ \"{AppCacheManifest}\""}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "ใช้แคชของแอปพลิเคชัน"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "หลีกเลี่ยงการใช้แคชของแอปพลิเคชัน"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "การระบุ DOCTYPE ช่วยป้องกันไม่ให้เบราว์เซอร์เปลี่ยนไปใช้โหมดที่ไม่มาตรฐาน [ดูข้อมูลเพิ่มเติม](https://web.dev/doctype)"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "ชื่อ DOCTYPE ต้องเป็นสตริงตัวพิมพ์เล็ก `html`"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "เอกสารต้องมี DOCTYPE"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "สตริง publicId ควรจะว่าง"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "สตริง systemId ควรจะว่าง"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "หน้าไม่มี DOCTYPE HTML ดังนั้นจึงทริกเกอร์โหมดที่ไม่มาตรฐาน"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "หน้ามี DOCTYPE HTML"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "องค์ประกอบ"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "สถิติ"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "ค่า"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "วิศวกรเบราว์เซอร์แนะนำให้ใช้หน้าเว็บที่มีองค์ประกอบ DOM น้อยกว่า 1,500 รายการโดยประมาณ ความลึกที่เหมาะที่สุดคือแบบต้นไม้ซึ่งมีองค์ประกอบน้อยกว่า 32 รายการและมีองค์ประกอบย่อย/หลักน้อยกว่า 60 รายการ รายการ DOM ขนาดใหญ่อาจใช้หน่วยความจำเพิ่มขึ้น ทำให้[การคำนวณสไตล์](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations)ยาวนานขึ้น และสร้าง[การจัดเรียงการออกแบบใหม่](https://developers.google.com/speed/articles/reflow)ซึ่งมีค่าใช้จ่ายสูง [ดูข้อมูลเพิ่มเติม](https://web.dev/dom-size)"}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 องค์ประกอบ}other{# องค์ประกอบ}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "หลีกเลี่ยง DOM ที่มีขนาดใหญ่เกินไป"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "ความลึก DOM สูงสุด"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "องค์ประกอบ DOM ทั้งหมด"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "จำนวนองค์ประกอบย่อยสูงสุด"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "หลีกเลี่ยง DOM ที่มีขนาดใหญ่เกินไป"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "เป้าหมาย"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "เพิ่ม `rel=\"noopener\"` หรือ `rel=\"noreferrer\"` ไปยังลิงก์ภายนอกใดๆ เพื่อปรับปรุงประสิทธิภาพและป้องกันช่องโหว่ด้านความปลอดภัย [ดูข้อมูลเพิ่มเติม](https://web.dev/external-anchors-use-rel-noopener)"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "ลิงก์ไปปลายทางแบบ Cross-Origin ไม่ปลอดภัย"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "ลิงก์ไปปลายทางแบบ Cross-Origin ปลอดภัย"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "ระบุปลายทางสำหรับโฆษณาด้านล่างสุด ({anchorHTML}) ไม่ได้ หากไม่ได้ใช้เป็นไฮเปอร์ลิงก์ ลองเอา target=_blank ออก"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "ผู้ใช้ไม่เชื่อถือหรือเกิดความสับสนในเว็บไซต์ที่ขอข้อมูลตำแหน่งโดยไม่มีบริบทให้ พิจารณาผูกคำขอกับการกระทำของผู้ใช้แทน [ดูข้อมูลเพิ่มเติม](https://web.dev/geolocation-on-start)"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "ขอสิทธิ์เข้าถึงตำแหน่งทางภูมิศาสตร์ในการโหลดหน้าเว็บ"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "หลีกเลี่ยงการขอสิทธิ์เข้าถึงตำแหน่งทางภูมิศาสตร์ในการโหลดหน้าเว็บ"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "เวอร์ชัน"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "ตรวจพบไลบรารี JavaScript ส่วนหน้าทั้งหมดในหน้าเว็บ [ดูข้อมูลเพิ่มเติม](https://web.dev/js-libraries)"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "ตรวจพบไลบรารี JavaScript"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "สำหรับผู้ใช้ที่การเชื่อมต่อช้า สคริปต์ภายนอกที่แทรกเข้ามาแบบไดนามิกผ่านทาง `document.write()` จะช่วยหน่วงการโหลดหน้าเว็บได้หลายสิบวินาที [ดูข้อมูลเพิ่มเติม](https://web.dev/no-document-write)"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "ใช้ `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "หลีกเลี่ยงการใช้ `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "ความรุนแรงสูงสุด"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "เวอร์ชันของไลบรารี"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "จำนวนช่องโหว่"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "สคริปต์ของบุคคลที่สามบางรายการอาจมีช่องโหว่ด้านความปลอดภัยที่เป็นที่รู้จักซึ่งผู้โจมตีจะหาพบได้ง่ายและใช้ประโยชน์จากช่องโหว่นั้น [ดูข้อมูลเพิ่มเติม](https://web.dev/no-vulnerable-libraries)"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{ตรวจพบช่องโหว่ 1 รายการ}other{ตรวจพบช่องโหว่ # รายการ}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "มีไลบรารี JavaScript ส่วนหน้าที่มีช่องโหว่ด้านความปลอดภัย"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "สูง"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "ต่ำ"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "ปานกลาง"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "หลีกเลี่ยงการใช้ไลบรารี JavaScript ส่วนหน้าที่มีช่องโหว่ด้านความปลอดภัย"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "ผู้ใช้ไม่เชื่อถือหรือเกิดความสับสนในเว็บไซต์ที่ขอส่งการแจ้งเตือนโดยไม่มีบริบทให้ พิจารณาผูกคำขอกับท่าทางสัมผัสของผู้ใช้แทน [ดูข้อมูลเพิ่มเติม](https://web.dev/notification-on-start)"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "ขอสิทธิ์การแจ้งเตือนในการโหลดหน้าเว็บ"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "หลีกเลี่ยงการขอสิทธิ์การแจ้งเตือนในการโหลดหน้าเว็บ"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "องค์ประกอบที่ไม่ผ่านการตรวจสอบ"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "การป้องกันการวางรหัสผ่านทำให้นโยบายความปลอดภัยที่ดีอ่อนแอลง [ดูข้อมูลเพิ่มเติม](https://web.dev/password-inputs-can-be-pasted-into)"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "ป้องกันผู้ใช้ไม่ให้วางรหัสผ่านในช่อง"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "อนุญาตผู้ใช้ให้วางรหัสผ่านในช่องได้"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "โปรโตคอล"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 มีข้อดีที่มากกว่า HTTP/1.1 คือ ส่วนหัวแบบไบนารี การทำมัลติเพล็กซิง และการ Push ไปที่เซิร์ฟเวอร์ [ดูข้อมูลเพิ่มเติม](https://web.dev/uses-http2)"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{คำขอ 1 รายการไม่ได้แสดงผ่าน HTTP/2}other{คำขอ # รายการไม่ได้แสดงผ่าน HTTP/2}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "ไม่ได้ใช้ HTTP/2 สำหรับทรัพยากรทั้งหมด"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "ใช้ HTTP/2 กับทรัพยากรของตนเอง"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "ลองระบุ Listener เหตุการณ์แบบแตะและลูกกลิ้งเป็น `passive` เพื่อปรับปรุงประสิทธิภาพการเลื่อนของหน้าเว็บ [ดูข้อมูลเพิ่มเติม](https://web.dev/uses-passive-event-listeners)"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "ไม่ได้ใช้ Listener แบบแพสซีฟเพื่อปรับปรุงประสิทธิภาพการเลื่อน"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "ใช้ Listener แบบแพสซีฟเพื่อปรับปรุงประสิทธิภาพการเลื่อน"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "รายละเอียด"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "ข้อผิดพลาดที่บันทึกลงในคอนโซลแสดงให้เห็นถึงปัญหาที่ไม่ได้รับการแก้ไข ข้อผิดพลาดอาจมาจากคำขอเครือข่ายที่ไม่สำเร็จ และปัญหาอื่นๆ เกี่ยวกับเบราว์เซอร์ [ดูข้อมูลเพิ่มเติม](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "บันทึกข้อผิดพลาดเบราว์เซอร์ลงในคอนโซลแล้ว"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "ไม่มีข้อผิดพลาดเบราว์เซอร์บันทึกลงในคอนโซล"}, "lighthouse-core/audits/font-display.js | description": {"message": "ใช้ประโยชน์จากฟีเจอร์ CSS สำหรับแสดงแบบอักษรเพื่อให้ผู้ใช้มองเห็นข้อความได้ในขณะที่กำลังโหลดเว็บฟอนต์ [ดูข้อมูลเพิ่มเติม](https://web.dev/font-display)"}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "ตรวจสอบว่าข้อความจะยังมองเห็นได้ในระหว่างการโหลดเว็บฟอนต์"}, "lighthouse-core/audits/font-display.js | title": {"message": "ข้อความทั้งหมดจะยังมองเห็นได้ในระหว่างการโหลดเว็บฟอนต์"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "Lighthouse ตรวจสอบค่าการแสดงแบบอักษรสำหรับ URL ต่อไปนี้โดยอัตโนมัติไม่ได้: {fontURL}"}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "สัดส่วนภาพ (ขนาดจริง)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "สัดส่วนภาพ (ที่แสดง)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "ขนาดแสดงรูปภาพควรจะมีสัดส่วนที่เป็นธรรมชาติ [ดูข้อมูลเพิ่มเติม](https://web.dev/image-aspect-ratio)"}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "แสดงรูปภาพที่มีสัดส่วนไม่ถูกต้อง"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "แสดงรูปภาพที่มีสัดส่วนถูกต้อง"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "ข้อมูลขนาดรูปภาพไมู่กต้อง {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "เบราว์เซอร์แจ้งผู้ใช้อย่างชัดแจ้งให้เพิ่มแอปของคุณในหน้าจอหลัก ซึ่งจะทำให้ผู้ใช้มีส่วนร่วมเพิ่มขึ้นได้ [ดูข้อมูลเพิ่มเติม](https://web.dev/installable-manifest)"}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "ไฟล์ Manifest ของเว็บแอปไม่ตรงตามข้อกำหนดด้านความสามารถในการติดตั้ง"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "ไฟล์ Manifest ของเว็บแอปตรงตามข้อกำหนดด้านความสามารถในการติดตั้ง"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "URL ไม่ปลอดภัย"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "ทุกเว็บไซต์ควรป้องกันด้วยการใช้ HTTPS แม้ว่าจะเป็นเว็บไซต์ที่ไม่มีข้อมูลที่ละเอียดอ่อนก็ตาม HTTPS ป้องกันผู้บุกรุกไม่ให้แทรกแซงหรือแอบฟังการสื่อสารระหว่างแอปกับผู้ใช้ของคุณ และเป็นข้อกำหนดที่ต้องทำก่อนสำหรับ HTTP/2 รวมถึง API ของแพลตฟอร์มเว็บใหม่ๆ อีกมาก [ดูข้อมูลเพิ่มเติม](https://web.dev/is-on-https)"}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{พบคำขอที่ไม่ปลอดภัย 1 รายการ}other{พบคำขอที่ไม่ปลอดภัย # รายการ}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "ไม่ได้ใช้ HTTPS"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "ใช้ HTTPS"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "การโหลดหน้าเว็บที่รวดเร็วผ่านเครือข่ายมือถือช่วยให้มั่นใจว่าผู้ใช้ได้รับประสบการณ์การใช้งานที่ดีในอุปกรณ์เคลื่อนที่ [ดูข้อมูลเพิ่มเติม](https://web.dev/load-fast-enough-for-pwa)"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "โต้ตอบที่ {timeInMs, number, seconds} วินาที"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "มีการโต้ตอบในเครือข่ายมือถือจำลองที่ {timeInMs, number, seconds} วินาที"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "หน้าเว็บโหลดช้าเกินไปและไม่มีการโต้ตอบภายใน 10 วินาที ไปที่โอกาสและการวินิจฉัยในส่วน \"ประสิทธิภาพ\" เพื่อดูวิธีปรับปรุง"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "การโหลดหน้าเว็บไม่เร็วพอเมื่อใช้เครือข่ายมือถือ"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "การโหลดหน้าเว็บเร็วพอเมื่อใช้เครือข่ายมือถือ"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "หมวดหมู่"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "พิจารณาลดเวลาที่ใช้ในการแยกวิเคราะห์ แปลโปรแกรม และดำเนินการกับ JS การส่งเพย์โหลด JS ปริมาณน้อยลงอาจช่วยในเรื่องนี้ได้ [ดูข้อมูลเพิ่มเติม](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "ลดการทำงานของเธรดหลัก"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "ลดการทำงานของเธรดหลัก"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "เว็บไซต์ควรทำงานในเบราว์เซอร์หลักๆ ทั้งหมดได้เพื่อให้เข้าถึงผู้ใช้จำนวนมากที่สุด [ดูข้อมูลเพิ่มเติม](https://web.dev/pwa-cross-browser)"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "เว็บไซต์ทำงานในเบราว์เซอร์ต่างๆ ได้"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "ตรวจดูว่าแต่ละหน้าทำ Deep Link ผ่าน URL ได้และ URL ต่างๆ ไม่ซ้ำกันเพื่อให้แชร์ได้ในโซเชียลมีเดีย [ดูข้อมูลเพิ่มเติม](https://web.dev/pwa-each-page-has-url)"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "แต่ละหน้ามี URL ที่ไม่ซ้ำกัน"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "การเปลี่ยนควรจะดำเนินไปอย่างรวดเร็วขณะที่คุณแตะไปรอบๆ แม้ในเครือข่ายที่ช้า ซึ่งเป็นสิ่งสำคัญที่ทำให้ผู้ใช้รับรู้ได้ถึงประสิทธิภาพ [ดูข้อมูลเพิ่มเติม](https://web.dev/pwa-page-transitions)"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "เปลี่ยนหน้าได้รวดเร็วแม้ว่าเครือข่ายจะช้า"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "เวลาตอบสนองต่ออินพุตโดยประมาณเป็นระยะเวลาโดยประมาณที่แอปใช้เพื่อตอบสนองอินพุตของผู้ใช้ระหว่างการโหลดหน้าเว็บในกรอบเวลา 5 วินาทีที่ทำงานหนักที่สุด มีหน่วยเป็นมิลลิวินาที หากเวลาในการตอบสนองนานกว่า 50 มิลลิวินาที ผู้ใช้อาจรู้สึกว่าแอปช้า [ดูข้อมูลเพิ่มเติม](https://web.dev/estimated-input-latency)"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "เวลาในการตอบสนองต่ออินพุตโดยประมาณ"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "First Contentful Paint ระบุเวลาที่มีการแสดงผลข้อความหรือรูปภาพครั้งแรก [ดูข้อมูลเพิ่มเติม](https://web.dev/first-contentful-paint)"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "การแสดงผลที่มีเนื้อหาเต็มครั้งแรก"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "First CPU Idle ระบุครั้งแรกที่เทรดหลักของหน้าเว็บว่างพอที่จะจัดการกับอินพุต  [ดูข้อมูลเพิ่มเติม](https://web.dev/first-cpu-idle)"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "CPU ไม่ได้ใช้งานครั้งแรก"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "การแสดงผลที่มีความหมายครั้งแรกวัดเมื่อเนื้อหาหลักของหน้าเว็บปรากฏ [ดูข้อมูลเพิ่มเติม](https://web.dev/first-meaningful-paint)"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "การแสดงผลที่มีความหมายครั้งแรก"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "เวลาในการตอบสนองคือระยะเวลาที่หน้าเว็บใช้ในการตอบสนองอย่างสมบูรณ์ [ดูข้อมูลเพิ่มเติม](https://web.dev/interactive)"}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "เวลาในการโต้ตอบ"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "First Input Delay สูงสุดที่อาจเกิดขึ้นซึ่งผู้ใช้อาจเจอคือระยะเวลาของงานที่ยาวที่สุดเป็นมิลลิวินาที [ดูข้อมูลเพิ่มเติม](https://developers.google.com/web/updates/2018/05/first-input-delay)"}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "First Input Delay สูงสุดที่อาจเกิดขึ้น"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "ดัชนีความเร็วแสดงให้เห็นความเร็วที่เนื้อหาของหน้าปรากฏจนดูสมบูรณ์ [ดูข้อมูลเพิ่มเติม](https://web.dev/speed-index)"}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "ดัชนีความเร็ว"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "ผลรวมช่วงเวลาทั้งหมดระหว่าง FCP และเวลาในการตอบสนอง เมื่อความยาวของงานเกิน 50ms หน่วยเป็นมิลลิวินาที"}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "เวลาที่บล็อกทั้งหมด"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "ระยะเวลารับส่งข้อมูล (RTT) ของเครือข่ายมีผลกระทบอย่างมากต่อประสิทธิภาพ หากต้นทางมี RTT สูง แสดงว่าเซิร์ฟเวอร์ที่อยู่ใกล้กับผู้ใช้มากกว่าอาจช่วยปรับปรุงประสิทธิภาพได้ [ดูข้อมูลเพิ่มเติม](https://hpbn.co/primer-on-latency-and-bandwidth/)"}, "lighthouse-core/audits/network-rtt.js | title": {"message": "ระยะเวลารับส่งข้อมูลของเครือข่าย"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "เวลาในการตอบสนองต่อเซิร์ฟเวอร์อาจส่งผลกระทบต่อประสิทธิภาพของเว็บ หากต้นทางใช้เวลาในการตอบสนองต่อเซิร์ฟเวอร์นาน แสดงว่ามีการใช้งานเซิร์ฟเวอร์มากเกินไปหรือประสิทธิภาพแบ็กเอนด์ของเซิร์ฟเวอร์ไม่ดี [ดูข้อมูลเพิ่มเติม](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)"}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "เวลาในการตอบสนองจากแบ็กเอนด์ของเซิร์ฟเวอร์"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "Service Worker ช่วยให้เว็บแอปของคุณเชื่อถือได้ในสภาวะของเครือข่ายที่คาดการณ์ไม่ได้ [ดูข้อมูลเพิ่มเติม](https://web.dev/offline-start-url)"}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url` ไม่ตอบสนองด้วยรหัส 200 เมื่อออฟไลน์"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url` ตอบสนองด้วยรหัส 200 เมื่อออฟไลน์"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "Lighthouse อ่าน `start_url` จากไฟล์ Manifest ไม่ได้ ด้วยเหตุนี้ ระบบจึงสันนิษฐานว่า `start_url` เป็น URL ของเอกสาร ข้อความแสดงข้อผิดพลาด: \"{manifestWarning}\""}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "เกินงบประมาณ"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "ควบคุมให้จำนวนและขนาดของคำขอเครือข่ายอยู่ภายในเป้าหมายที่กำหนดตามงบประมาณประสิทธิภาพที่ให้มา [ดูข้อมูลเพิ่มเติม](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 คำขอ}other{# คำขอ}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "งบประมาณประสิทธิภาพ"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "หากคุณตั้งค่า HTTPS ไว้แล้ว ให้ตรวจสอบว่าได้เปลี่ยนเส้นทางการเข้าชมผ่าน HTTP ทั้งหมดไปยัง HTTPS เพื่อให้ผู้ใช้ทุกคนใช้ฟีเจอร์เว็บที่ปลอดภัยได้ [ดูข้อมูลเพิ่มเติม](https://web.dev/redirects-http)"}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "ไม่ได้เปลี่ยนเส้นทางการเข้าชมผ่าน HTTP ไปยัง HTTPS"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "เปลี่ยนเส้นทางการเข้าชมผ่าน HTTP ไปยัง HTTPS"}, "lighthouse-core/audits/redirects.js | description": {"message": "การเปลี่ยนเส้นทางทำให้เกิดความล่าช้ามากขึ้นก่อนที่หน้าเว็บจะโหลดได้ [ดูข้อมูลเพิ่มเติม](https://web.dev/redirects)"}, "lighthouse-core/audits/redirects.js | title": {"message": "หลีกเลี่ยงการเปลี่ยนเส้นทางหลายหน้า"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "หากต้องการตั้งงบประมาณสำหรับจำนวนและขนาดของทรัพยากรหน้า ให้เพิ่มไฟล์ budget.json [ดูข้อมูลเพิ่มเติม](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{คำขอ 1 รายการ • {byteCount, number, bytes} KB}other{คำขอ # รายการ • {byteCount, number, bytes} KB}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "ควบคุมให้จำนวนคำขอมีไม่มากและการโอนมีขนาดเล็ก"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "ลิงก์ Canonical จะบอกถึง URL ที่จะแสดงในผลการค้นหา [ดูข้อมูลเพิ่มเติม](https://web.dev/canonical)"}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "URL หลายรายการขัดแย้งกัน ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "ชี้ไปที่โดเมนอื่น ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "URL ไม่ถูกต้อง ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "ชี้ไปที่ `hreflang` ตำแหน่งอื่น ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "URL แบบสัมพัทธ์ ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "ชี้ไปที่ URL ระดับรากของโดเมน (หน้าแรก) แทนที่จะเป็นหน้าที่เทียบเท่ากันของเนื้อหา"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "เอกสารไม่มี `rel=canonical` ที่ถูกต้อง"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "เอกสารมี `rel=canonical` ที่ถูกต้อง"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "ขนาดตัวอักษรที่เล็กกว่า 12 พิกเซลจะเล็กเกินไปจนอ่านไม่ออกและทำให้ผู้เข้าชมในอุปกรณ์เคลื่อนที่ต้อง “บีบเพื่อซูมเข้า” เพื่ออ่าน พยายามให้ข้อความในหน้าเว็บมากกว่า 60% มีขนาดอย่างน้อย 12 พิกเซล [ดูข้อมูลเพิ่มเติม](https://web.dev/font-size)"}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "ข้อความที่อ่านได้ชัดเจน {decimalProportion, number, extendedPercent}"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "ข้อความอ่านได้ไม่ชัดเจนเพราะไม่มีเมตาแท็กวิวพอร์ตที่เพิ่มประสิทธิภาพให้เหมาะกับหน้าจออุปกรณ์เคลื่อนที่"}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "{decimalProportion, number, extendedPercent} ของข้อความมีขนาดเล็กเกินไป (อิงจากตัวอย่าง {decimalProportionVisited, number, extendedPercent})"}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "เอกสารไม่ได้ใช้ขนาดตัวอักษรที่อ่านได้ชัดเจน"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "เอกสารใช้ขนาดตัวอักษรที่อ่านได้ชัดเจน"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "ลิงก์ hreflang จะบอกให้เครื่องมือค้นหาทราบถึงเวอร์ชันของหน้าเว็บที่ควรแสดงในผลการค้นหาสำหรับแต่ละภาษาหรือภูมิภาค [ดูข้อมูลเพิ่มเติม](https://web.dev/hreflang)"}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "เอกสารไม่มี `hreflang` ที่ถูกต้อง"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "เอกสารมี `hreflang` ที่ถูกต้อง"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "หน้าเว็บที่มีรหัสสถานะ HTTP ไม่สำเร็จอาจไม่ได้รับการจัดทำดัชนีอย่างถูกต้อง [ดูข้อมูลเพิ่มเติม](https://web.dev/http-status-code)"}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "หน้าเว็บมีรหัสสถานะ HTTP ไม่สำเร็จ"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "หน้าเว็บมีรหัสสถานะ HTTP สำเร็จ"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "เครื่องมือค้นหาจะรวมหน้าเว็บของคุณไว้ในผลการค้นหาไม่ได้หากไม่มีสิทธิ์รวบรวมข้อมูลหน้าดังกล่าว [ดูข้อมูลเพิ่มเติม](https://web.dev/is-crawable)"}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "หน้าเว็บถูกบล็อกไม่ให้มีการจัดทำดัชนี"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "หน้าไม่ได้ถูกบล็อกจากการจัดทำดัชนี"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "ข้อความอธิบายลิงก์ช่วยให้เครื่องมือค้นหาเข้าใจเนื้อหาของคุณ [ดูข้อมูลเพิ่มเติม](https://web.dev/link-text)"}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{พบ 1 ลิงก์}other{พบ # ลิงก์}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "ลิงก์ไม่มีข้อความอธิบาย"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "ลิงก์มีข้อความอธิบาย"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "เรียกใช้[เครื่องมือทดสอบข้อมูลที่มีโครงสร้าง](https://search.google.com/structured-data/testing-tool/)และ [Linter ข้อมูลที่มีโครงสร้าง](http://linter.structured-data.org/)เพื่อตรวจสอบความถูกต้องของข้อมูลที่มีโครงสร้าง [ดูข้อมูลเพิ่มเติม](https://web.dev/structured-data)"}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "ข้อมูลที่มีโครงสร้างถูกต้อง"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "อาจมีการรวมคำอธิบายเมตาในผลการค้นหาเพื่อสรุปเนื้อหาของหน้าเว็บให้สั้นกระชับ [ดูข้อมูลเพิ่มเติม](https://web.dev/meta-description)"}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "ข้อความอธิบายว่างเปล่า"}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "เอกสารไม่มีคำอธิบายเมตา"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "เอกสารมีคำอธิบายเมตา"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "เครื่องมือค้นหาจัดทำดัชนีเนื้อหาปลั๊กอินไม่ได้ และอุปกรณ์จำนวนมากจำกัดการใช้หรือไม่รองรับปลั๊กอิน [ดูข้อมูลเพิ่มเติม](https://web.dev/plugins)"}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "เอกสารใช้ปลั๊กอิน"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "เอกสารหลีกเลี่ยงการใช้ปลั๊กอิน"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "หากไฟล์ robots.txt มีรูปแบบไม่ถูกต้อง โปรแกรมรวบรวมข้อมูลอาจไม่เข้าใจวิธีที่คุณต้องการให้รวบรวมข้อมูลหรือจัดทำดัชนีเว็บไซต์ [ดูข้อมูลเพิ่มเติม](https://web.dev/robots-txt)"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "คำขอ robots.txt แสดงสถานะ HTTP ต่อไปนี้ {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{พบข้อผิดพลาด 1 รายการ}other{พบข้อผิดพลาด # รายการ}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse ดาวน์โหลดไฟล์ robots.txt ไม่ได้"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt ไม่ถูกต้อง"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "robots.txt ถูกต้อง"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "องค์ประกอบสำหรับการโต้ตอบ เช่น ปุ่มและลิงก์ ต้องมีขนาดใหญ่พอ (48x48 พิกเซล) และมีพื้นที่ว่างโดยรอบมากพอเพื่อให้แตะได้ง่ายๆ โดยไม่ซ้อนทับกับองค์ประกอบอื่นๆ [ดูข้อมูลเพิ่มเติม](https://web.dev/tap-targets)"}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "เป้าหมายการแตะที่มีขนาดเหมาะสม {decimalProportion, number, percent}"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "เป้าหมายการแตะมีขนาดเล็กเกินไปเพราะไม่มีเมตาแท็กวิวพอร์ตที่เพิ่มประสิทธิภาพให้เหมาะกับหน้าจออุปกรณ์เคลื่อนที่"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "เป้าหมายการแตะมีขนาดที่ไม่เหมาะสม"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "เป้าหมายซ้อนทับกัน"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "เป้าหมายการแตะ"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "เป้าหมายการแตะมีขนาดที่เหมาะสม"}, "lighthouse-core/audits/service-worker.js | description": {"message": "Service Worker เป็นเทคโนโลยีที่ช่วยให้แอปของคุณใช้ฟีเจอร์ของ Progressive Web App ได้หลายฟีเจอร์ เช่น ออฟไลน์ เพิ่มไปยังหน้าจอหลัก และข้อความ Push [ดูข้อมูลเพิ่มเติม](https://web.dev/service-worker)"}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "หน้านี้ควบคุมโดย Service Worker แต่ไม่พบ `start_url` เนื่องจากไฟล์ Manifest แยกวิเคราะห์เป็น JSON ที่ถูกต้องไม่ได้"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "หน้านี้ควบคุมโดย Service Worker แต่ `start_url` ({startUrl}) ไม่ได้อยู่ในขอบเขตของ Service Worker นั้น ({scopeUrl})"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "หน้านี้ควบคุมโดย Service Worker แต่ไม่พบ `start_url` เพราะไม่มีการดึงไฟล์ Manifest"}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "ต้นทางนี้มี Service Worker อย่างน้อย 1 ไฟล์ แต่หน้าเว็บ ({pageUrl}) ไม่อยู่ในขอบเขต"}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "ไม่ได้ลงทะเบียน Service Worker ที่ควบคุมหน้าเว็บและ `start_url`"}, "lighthouse-core/audits/service-worker.js | title": {"message": "ลงทะเบียน Service Worker ที่ควบคุมหน้าเว็บและ `start_url`"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "หน้าจอแนะนำที่มีธีมช่วยให้ผู้ใช้ได้รับประสบการณ์ที่มีคุณภาพสูงเมื่อเปิดแอปของคุณจากหน้าจอหลัก [ดูข้อมูลเพิ่มเติม](https://web.dev/splash-screen)"}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "ไม่ได้กำหนดค่าให้ใช้หน้าจอแนะนำที่กำหนดเอง"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "มีการกำหนดค่าให้ใช้หน้าจอแนะนำที่กำหนดเอง"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "คุณกำหนดธีมของแถบที่อยู่เบราว์เซอร์ให้เข้ากับเว็บไซต์ได้ [ดูข้อมูลเพิ่มเติม](https://web.dev/themed-omnibox)"}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "ไม่ได้กำหนดสีธีมสำหรับแถบที่อยู่"}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "กำหนดสีธีมของแถบที่อยู่"}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "เวลาในการบล็อกเทรดหลัก"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "บุคคลที่สาม"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "โค้ดของบุคคลที่สามอาจส่งผลกระทบที่สำคัญต่อประสิทธิภาพการโหลด จำกัดจำนวนผู้ให้บริการบุคคลที่สามที่มากเกินไปและพยายามโหลดโค้ดของบุคคลที่สามหลังจากที่หน้าเว็บโหลดเบื้องต้นเสร็จเรียบร้อยแล้ว [ดูข้อมูลเพิ่มเติม](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)"}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "โค้ดของบุคคลที่สามบล็อกเทรดหลักเป็นเวลา {timeInMs, number, milliseconds} วินาที"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "ลดผลกระทบจากโค้ดของบุคคลที่สาม"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "การใช้บุคคลที่สาม"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "Time To First Byte ระบุเวลาที่เซิร์ฟเวอร์ส่งการตอบกลับ [ดูข้อมูลเพิ่มเติม](https://web.dev/time-to-first-byte)"}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "เอกสารรากใช้เวลา {timeInMs, number, milliseconds} มิลลิวินาที"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "ลดเวลาในการตอบกลับของเซิร์ฟเวอร์ (TTFB)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "เวลาตอบสนองของเซิร์ฟเวอร์ต่ำ (TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "ระยะเวลา"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "เวลาเริ่มต้น"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "ประเภท"}, "lighthouse-core/audits/user-timings.js | description": {"message": "พิจารณาติดตั้ง User Timing API ในแอปเพื่อวัดประสิทธิภาพในระหว่างประสบการณ์สำคัญของผู้ใช้ในชีวิตจริงได้ [ดูข้อมูลเพิ่มเติม](https://web.dev/user-timings)"}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{ระยะเวลาของผู้ใช้ 1 รายการ}other{ระยะเวลาของผู้ใช้ # รายการ}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "ระยะเวลาที่เจาะจงของผู้ใช้และระยะเวลาทั่วไป"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "พบ <link> ที่เชื่อมต่อล่วงหน้าสำหรับ \"{security<PERSON><PERSON><PERSON>}\" แต่เบราว์เซอร์ไม่ได้นำไปใช้งาน โปรดตรวจสอบว่าคุณใช้แอตทริบิวต์ `crossorigin` อย่างถูกต้องแล้ว"}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "พิจารณาเพิ่ม `preconnect` หรือ `dns-prefetch` ซึ่งบอกถึงทรัพยากรเพื่อสร้างการเชื่อมต่อกับต้นทางที่สำคัญของบุคคลที่สามตั้งแต่เนิ่นๆ [ดูข้อมูลเพิ่มเติม](https://web.dev/uses-rel-preconnect)"}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "เชื่อมต่อกับต้นทางที่จำเป็นล่วงหน้า"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "พบ <link> ที่โหลดล่วงหน้าสำหรับ \"{preloadURL}\" แต่เบราว์เซอร์ไม่ได้นำไปใช้งาน โปรดตรวจสอบว่าคุณใช้แอตทริบิวต์ `crossorigin` อย่างถูกต้องแล้ว"}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "พิจารณาใช้ `<link rel=preload>` เพื่อจัดลำดับความสำคัญในการเรียกทรัพยากรที่มีการขอให้โหลดหน้าเว็บภายหลัง [ดูข้อมูลเพิ่มเติม](https://web.dev/uses-rel-preload)"}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "โหลดคำขอสำคัญล่วงหน้า"}, "lighthouse-core/audits/viewport.js | description": {"message": "เพิ่มแท็ก `<meta name=\"viewport\">` เพื่อเพิ่มประสิทธิภาพแอปสำหรับหน้าจออุปกรณ์เคลื่อนที่ [ดูข้อมูลเพิ่มเติม](https://web.dev/viewport)"}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "ไม่พบแท็ก `<meta name=\"viewport\">`"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "ไม่มีแท็ก `<meta name=\"viewport\">` ที่มี `width` หรือ `initial-scale`"}, "lighthouse-core/audits/viewport.js | title": {"message": "มีแท็ก `<meta name=\"viewport\">` ที่มี `width` หรือ `initial-scale`"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "แอปควรแสดงเนื้อหาบางอย่างเมื่อมีการปิดใช้ JavaScript แม้จะเป็นเพียงการเตือนผู้ใช้ว่าการใช้แอปจำเป็นต้องใช้ JavaScript [ดูข้อมูลเพิ่มเติม](https://web.dev/without-javascript)"}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "ส่วนเนื้อหาในหน้าควรแสดงเนื้อหาบางอย่างถ้าสคริปต์ในหน้าไม่พร้อมใช้งาน"}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "ไม่แสดงเนื้อหาทางเลือกเมื่อ JavaScript ไม่พร้อมใช้งาน"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "มีเนื้อหาบางอย่างแสดงขึ้นเมื่อ JavaScript ไม่พร้อมใช้งาน"}, "lighthouse-core/audits/works-offline.js | description": {"message": "หากคุณกำลังสร้าง Progressive Web App ให้พิจารณาใช้ Service Worker เพื่อให้แอปทำงานแบบออฟไลน์ได้ [ดูข้อมูลเพิ่มเติม](https://web.dev/works-offline)"}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "หน้าปัจจุบันไม่ตอบสนองด้วยรหัส 200 เมื่อออฟไลน์"}, "lighthouse-core/audits/works-offline.js | title": {"message": "หน้าปัจจุบันตอบสนองด้วยรหัส 200 เมื่อออฟไลน์"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "หน้านี้อาจไม่โหลดขึ้นขณะออฟไลน์เนื่องจาก URL ทดสอบของคุณ ({requested}) มีการเปลี่ยนเส้นทางไปยัง \"{final}\" ลองทดสอบ URL ที่ 2 โดยตรง"}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "นี่เป็นโอกาสปรับปรุงการใช้งาน ARIA ในแอปพลิเคชันของคุณ ซึ่งอาจช่วยให้ผู้ใช้ได้รับประสบการณ์การใช้งานเทคโนโลยีอำนวยความสะดวก เช่น โปรแกรมอ่านหน้าจอ ที่ดียิ่งขึ้น"}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "นี่เป็นโอกาสระบุเนื้อหาสำรองสำหรับเสียงและวิดีโอ การดำเนินการนี้อาจช่วยปรับปรุงประสบการณ์ของผู้ใช้ที่มีความบกพร่องทางการได้ยินหรือการมองเห็น"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "เสียงและวิดีโอ"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "รายการเหล่านี้ไฮไลต์แนวทางปฏิบัติที่ดีที่สุดที่พบบ่อยของการช่วยเหลือพิเศษ"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "แนวทางปฏิบัติที่ดีที่สุด"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "การตรวจสอบเหล่านี้ไฮไลต์โอกาสในการ[ปรับปรุงการช่วยเหลือพิเศษของเว็บแอป](https://developers.google.com/web/fundamentals/accessibility) โดยจะตรวจพบอัตโนมัติได้เฉพาะปัญหากลุ่มย่อยด้านการช่วยเหลือพิเศษ เราจึงขอแนะนำให้ตรวจสอบด้วยตนเองด้วย"}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "รายการเหล่านี้จัดการพื้นที่ที่เครื่องมือทดสอบอัตโนมัติไม่ครอบคลุม ดูข้อมูลเพิ่มเติมในคำแนะนำเกี่ยวกับ[การดำเนินการตรวจสอบการช่วยเหลือพิเศษ](https://developers.google.com/web/fundamentals/accessibility/how-to-review)"}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "การช่วยเหลือพิเศษ"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "นี่เป็นโอกาสปรับปรุงความอ่านง่ายของเนื้อหา"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "คอนทราสต์"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "นี่เป็นโอกาสปรับปรุงการตีความเนื้อหาของคุณโดยผู้ใช้ภาษาต่างๆ"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "การปรับให้เป็นสากลและการแปล"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "นี่เป็นโอกาสปรับปรุงความหมายของส่วนควบคุมในแอปพลิเคชันของคุณ การดำเนินการนี้อาจช่วยให้ผู้ใช้ได้รับประสบการณ์การใช้งานเทคโนโลยีอำนวยความสะดวก เช่น โปรแกรมอ่านหน้าจอ ที่ดียิ่งขึ้น"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "ชื่อและป้ายกำกับ"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "นี่เป็นโอกาสปรับปรุงการไปยังส่วนต่างๆ ในแอปพลิเคชันของคุณด้วยแป้นพิมพ์"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "การนำทาง"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "นี่เป็นโอกาสปรับปรุงประสบการณ์การอ่านตารางหรือข้อมูลรายการโดยใช้เทคโนโลยีอำนวยความสะดวก เช่น โปรแกรมอ่านหน้าจอ"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "ตารางและรายการ"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "แนวทางปฏิบัติที่ดีที่สุด"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "งบประมาณประสิทธิภาพจะใช้เป็นมาตรฐานสำหรับประสิทธิภาพของเว็บไซต์คุณ"}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "งบประมาณ"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "ข้อมูลเพิ่มเติมเกี่ยวกับประสิทธิภาพของแอปพลิเคชัน ตัวเลขเหล่านี้ไม่[ส่งผลกระทบโดยตรง](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)ต่อคะแนนประสิทธิภาพ"}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "การวินิจฉัย"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "ประสิทธิภาพที่สำคัญที่สุดคือความเร็วที่พิกเซลแสดงผลในหน้าจอ เมตริกที่สำคัญ ได้แก่ การแสดงผลที่มีเนื้อหาเต็มครั้งแรก การแสดงผลที่มีความหมายครั้งแรก"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "การปรับปรุงการแสดงผลครั้งแรก"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "คำแนะนำเหล่านี้จะช่วยให้หน้าโหลดได้เร็วขึ้น โดยจะไม่[ส่งผลกระทบโดยตรง](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)ต่อคะแนนประสิทธิภาพ"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "โอกาส"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "เมตริก"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "ปรับปรุงประสบการณ์ในการโหลดโดยรวมเพื่อให้หน้าเว็บตอบสนองและพร้อมใช้งานโดยเร็วที่สุด เมตริกที่สำคัญ ได้แก่ เวลาในการโต้ตอบ ดัชนีความเร็ว"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "การปรับปรุงโดยรวม"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "ประสิทธิภาพ"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "การตรวจสอบเหล่านี้ตรวจสอบความถูกต้องของลักษณะต่างๆ ของ Progressive Web App [ดูข้อมูลเพิ่มเติม](https://developers.google.com/web/progressive-web-apps/checklist)"}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "การตรวจสอบเหล่านี้เป็นสิ่งที่ต้องทำใน[รายการตรวจสอบ PWA](https://developers.google.com/web/progressive-web-apps/checklist) ซึ่งเป็นเกณฑ์พื้นฐาน แต่ Lighthouse ไม่ได้ทำการตรวจสอบดังกล่าวโดยอัตโนมัติ การตรวจสอบจะไม่ส่งผลต่อคะแนนของคุณ แต่คุณควรตรวจสอบด้วยตนเอง"}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "Progressive Web App"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "ทำงานเร็วและวางใจได้"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "ติดตั้งได้"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "เพิ่มประสิทธิภาพ PWA แล้ว"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "การตรวจสอบเหล่านี้ช่วยให้มั่นใจว่าหน้าเว็บของคุณได้รับการเพิ่มประสิทธิภาพสำหรับการจัดอันดับผลลัพธ์ของเครื่องมือค้นหา มีปัจจัยอื่นๆ ที่ Lighthouse ไม่ได้ตรวจสอบซึ่งอาจส่งผลกระทบต่อการจัดอันดับของคุณในการค้นหา [ดูข้อมูลเพิ่มเติม](https://support.google.com/webmasters/answer/35769)"}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "เรียกใช้ตัวตรวจสอบความถูกต้องเพิ่มเติมเหล่านี้ในเว็บไซต์ของคุณเพื่อดูแนวทางปฏิบัติที่ดีที่สุดเพิ่มเติมเกี่ยวกับ SEO"}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "จัด HTML ให้อยู่ในรูปแบบที่ช่วยให้โปรแกรมรวบรวมข้อมูลเข้าใจเนื้อหาแอปได้ง่ายขึ้น"}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "แนวทางปฏิบัติที่ดีที่สุดเกี่ยวกับเนื้อหา"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "โปรแกรมรวบรวมข้อมูลจะต้องเข้าถึงแอปของคุณได้เพื่อให้แอปปรากฏในผลการค้นหา"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "การรวบรวมข้อมูลและจัดทำดัชนี"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "ตรวจสอบว่าหน้าเว็บเหมาะกับอุปกรณ์เคลื่อนที่ ผู้ใช้จะได้ไม่ต้องบีบนิ้วหรือซูมเข้าเพื่ออ่านหน้าเนื้อหา [ดูข้อมูลเพิ่มเติม](https://developers.google.com/search/mobile-sites/)"}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "เหมาะกับอุปกรณ์เคลื่อนที่"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "แคช TTL"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "ตำแหน่ง"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "ชื่อ"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "คำขอ"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "ประเภททรัพยากร"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "ขนาด"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "เวลาที่ใช้"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "ขนาดการโอน"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "เวลาที่อาจประหยัดได้"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "เวลาที่อาจประหยัดได้"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "อาจประหยัดได้ {wastedBytes, number, bytes} KB"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "อาจประหยัดได้ {wastedMs, number, milliseconds} มิลลิวินาที"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "เอกสาร"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "แบบอักษร"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "รูปภาพ"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "สื่อ"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} มิลลิวินาที"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "อื่นๆ"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "สคริปต์"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} วินาที"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "สไตล์ชีต"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "บุคคลที่สาม"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "รวม"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "เกิดข้อผิดพลาดในการบันทึกการติดตามระหว่างการโหลดหน้าเว็บ โปรดเรียกใช้ Lighthouse อีกครั้ง ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "หมดเวลาระหว่างที่รอการเชื่อมต่อโปรโตคอลโปรแกรมแก้ไขข้อบกพร่องเริ่มต้น"}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome ไม่ได้รวบรวมภาพหน้าจอใดๆ ระหว่างการโหลดหน้า โปรดตรวจสอบว่ามีเนื้อหาที่มองเห็นได้ในหน้าเว็บ จากนั้นลองเรียกใช้ Lighthouse อีกครั้ง ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "เซิร์ฟเวอร์ DNS แก้ปัญหาโดเมนที่ระบุไม่ได้"}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "ตัวรวบรวม {artifactName} ที่จำเป็นพบข้อผิดพลาด: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "เกิดข้อผิดพลาดภายในของ Chrome โปรดรีสตาร์ท Chrome และลองเรียกใช้ Lighthouse อีกครั้ง"}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "ตัวรวบรวม {artifactName} ที่จำเป็นไม่ทำงาน"}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse โหลดหน้าเว็บที่คุณขออย่างน่าเชื่อถือไม่ได้ ตรวจสอบว่าคุณกำลังทดสอบ URL ที่ถูกต้องและเซิร์ฟเวอร์ตอบสนองคำขอทั้งหมดอย่างถูกต้อง"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse โหลด URL ที่คุณขออย่างน่าเชื่อถือไม่ได้เพราะหน้าเว็บไม่ตอบสนอง"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "URL ที่ระบุไม่มีใบรับรองความปลอดภัยที่ถูกต้อง {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome ป้องกันการโหลดหน้าเว็บด้วยโฆษณาคั่นระหว่างหน้า ตรวจสอบว่าคุณกำลังทดสอบ URL ที่ถูกต้องและเซิร์ฟเวอร์ตอบสนองคำขอทั้งหมดอย่างถูกต้อง"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse โหลดหน้าเว็บที่คุณขออย่างน่าเชื่อถือไม่ได้ ตรวจสอบว่าคุณกำลังทดสอบ URL ที่ถูกต้องและเซิร์ฟเวอร์ตอบสนองคำขอทั้งหมดอย่างถูกต้อง (รายละเอียด: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse โหลดหน้าเว็บที่คุณขออย่างน่าเชื่อถือไม่ได้ ตรวจสอบว่าคุณกำลังทดสอบ URL ที่ถูกต้องและเซิร์ฟเวอร์ตอบสนองคำขอทั้งหมดอย่างถูกต้อง (รหัสสถานะ: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "หน้าเว็บของคุณใช้เวลาโหลดนานเกินไป โปรดทำตามโอกาสในรายงานเพื่อลดเวลาในการโหลดหน้าเว็บแล้วลองเรียกใช้ Lighthouse อีกครั้ง ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "การรอการตอบสนองของโปรโตคอล DevTools เกินเวลาที่จัดสรรไว้ (เมธอด: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "การดึงข้อมูลเนื้อหาทรัพยากรเกินเวลาที่จัดสรรไว้"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "ดูเหมือนว่า URL ที่ระบุจะไม่ถูกต้อง"}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "แสดงการตรวจสอบ"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "การนำทางเริ่มต้น"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "เวลาในการตอบสนองของเส้นทางสำคัญที่ยาวที่สุด"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "ข้อผิดพลาด!"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "ข้อผิดพลาดในรายงาน: ไม่มีข้อมูลการตรวจสอบ"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "ข้อมูลในห้องทดลอง"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "การวิเคราะห์หน้าปัจจุบันในเครือข่ายมือถือจำลองโดย [Lighthouse](https://developers.google.com/web/tools/lighthouse/) ค่ามาจากการประมาณและอาจแตกต่างกันไป"}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "รายการเพิ่มเติมที่ควรตรวจสอบด้วยตนเอง"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "ไม่เกี่ยวข้อง"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "โอกาส"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "เวลาที่ประหยัดได้โดยประมาณ"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "การตรวจสอบที่ผ่านแล้ว"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "ยุบตัวอย่างข้อมูล"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "ขยายตัวอย่างข้อมูล"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "แสดงทรัพยากรของบุคคลที่สาม"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "เกิดปัญหาที่มีผลต่อการทำงานนี้ของ Lighthouse"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "ค่ามาจากการประมาณและอาจแตกต่างกันไป คะแนนประสิทธิภาพ[อิงจากเมตริกเหล่านี้เท่านั้น](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)"}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "ผ่านการตรวจสอบแต่มีคำเตือน"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "คำเตือน "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "ลองอัปโหลด GIF ไปยังบริการซึ่งจะทำให้ใช้ GIF เพื่อฝังเป็นวิดีโอ HTML5 ได้"}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "ติดตั้ง[ปลั๊กอินการโหลดแบบ Lazy Loading ของ WordPress](https://wordpress.org/plugins/search/lazy+load/) ที่จะช่วยเลื่อนเวลาโหลดรูปภาพนอกหน้าจอ หรือเปลี่ยนไปใช้ธีมที่มีฟังก์ชันดังกล่าว และอาจลองพิจารณาใช้[ปลั๊กอิน AMP](https://wordpress.org/plugins/amp/)"}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "มีปลั๊กอิน WordPress หลายรายการที่ช่วยคุณ[แทรกเนื้อหาที่สำคัญ](https://wordpress.org/plugins/search/critical+css/) หรือ[เลื่อนเวลาโหลดทรัพยากรที่สำคัญน้อยกว่า](https://wordpress.org/plugins/search/defer+css+javascript/) โปรดระวังว่าการเพิ่มประสิทธิภาพโดยปลั๊กอินเหล่านี้อาจทำให้ฟีเจอร์ของธีมหรือปลั๊กอินของคุณเสียหาย ซึ่งน่าจะทำให้คุณต้องแก้ไขโค้ด"}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "การกำหนดธีม ปลั๊กอิน และเซิร์ฟเวอร์ล้วนส่งผลต่อเวลาการตอบสนองของเซิร์ฟเวอร์ ลองหาธีมที่เพิ่มประสิทธิภาพมากขึ้น พยายามเลือกปลั๊กอินการเพิ่มประสิทธิภาพด้วยความระมัดระวัง และ/หรืออัปเกรดเซิร์ฟเวอร์"}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "ลองแสดงข้อความที่ตัดตอนมาในรายการโพสต์ (เช่น ผ่านแท็ก \"เพิ่มเติม\") ลดจำนวนโพสต์ที่แสดงในหน้าหนึ่งๆ แบ่งโพสต์ยาวๆ เป็นหลายหน้า หรือใช้ปลั๊กอินเพื่อโหลดความคิดเห็นแบบ Lazy Loading"}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "มี[ปลั๊กอิน WordPress](https://wordpress.org/plugins/search/minify+css/) หลายอย่างที่ช่วยให้เว็บไซต์เร็วขึ้นได้ด้วยการลิงก์ ลดขนาด และบีบอัดสไตล์ นอกจากนี้คุณอาจใช้กระบวนการของเวอร์ชันเพื่อลดขนาดล่วงหน้าหากเป็นไปได้"}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "มี[ปลั๊กอิน WordPress](https://wordpress.org/plugins/search/minify+javascript/) หลายอย่างที่ช่วยให้เว็บไซต์เร็วขึ้นได้ด้วยการลิงก์ ลดขนาด และบีบอัดสคริปต์ นอกจากนี้คุณอาจใช้กระบวนการของเวอร์ชันเพื่อลดขนาดล่วงหน้าหากเป็นไปได้"}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "ลองลดหรือเปลี่ยนจำนวน[ปลั๊กอิน WordPress](https://wordpress.org/plugins/) ที่โหลด CSS ที่ไม่ได้ใช้ในหน้าเว็บของคุณ หากต้องการระบุปลั๊กอินที่เพิ่ม CSS โดยไม่จำเป็น ให้ลองเรียกใช้[การครอบคลุมโค้ด](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)ใน DevTools ของ Chrome คุณระบุธีม/ปลั๊กอินที่รับผิดชอบได้จาก URL ของสไตล์ชีต หาปลั๊กอินที่มีสไตล์ชีตจำนวนมากอยู่ในรายการซึ่งมีสีแดงอยู่จำนวนมากในการครอบคลุมโค้ด ปลั๊กอินควรเป็นเพียงตัวกำหนดลำดับของสไตล์ชีตเท่านั้นหากใช้ปลั๊กอินในหน้าจริงๆ"}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "ลองลดหรือเปลี่ยนจำนวน[ปลั๊กอิน WordPress](https://wordpress.org/plugins/) ที่โหลด JavaScript ที่ไม่ได้ใช้ในหน้าเว็บของคุณ หากต้องการระบุปลั๊กอินที่เพิ่ม JS โดยไม่จำเป็น ให้ลองเรียกใช้ [การครอบคลุมโค้ด](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)ใน DevTools ของ Chrome คุณระบุธีม/ปลั๊กอินที่รับผิดชอบได้จาก URL ของสคริปต์ หาปลั๊กอินที่มีสคริปต์จำนวนมากอยู่ในรายการซึ่งมีสีแดงอยู่จำนวนมากในการครอบคลุมโค้ด ปลั๊กอินควรเป็นเพียงตัวกำหนดลำดับของสคริปต์เท่านั้นหากใช้ปลั๊กอินในหน้าจริงๆ"}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "อ่านเกี่ยวกับ[การแคชของเบราว์เซอร์ใน WordPress](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)"}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "พิจารณาใช้[ปลั๊กอิน WordPress การเพิ่มประสิทธิภาพรูปภาพ](https://wordpress.org/plugins/search/optimize+images/)ที่บีบอัดรูปภาพแต่ยังคงคุณภาพไว้ได้"}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "อัปโหลดรูปภาพโดยตรงผ่าน[ไลบรารีสื่อ](https://codex.wordpress.org/Media_Library_Screen)เพื่อให้แน่ใจว่ามีขนาดรูปภาพที่จำเป็นพร้อมใช้งาน จากนั้นแทรกรูปภาพจากไลบรารีสื่อหรือใช้วิดเจ็ตรูปภาพเพื่อให้มีการใช้ขนาดรูปภาพที่มีประสิทธิภาพสูงสุด (รวมถึงขนาดสำหรับเบรกพอยท์ที่ปรับเปลี่ยนตามอุปกรณ์) หลีกเลี่ยงการใช้รูปภาพ`Full Size` นอกเสียจากว่าขนาดจะเพียงพอต่อการใช้งาน [ดูข้อมูลเพิ่มเติม](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)"}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "คุณเปิดใช้การบีบอัดข้อความในการกำหนดค่าเว็บเซิร์ฟเวอร์ได้"}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "พิจารณาใช้[ปลั๊กอิน](https://wordpress.org/plugins/search/convert+webp/)หรือบริการที่จะแปลงรูปภาพที่อัปโหลดเป็นรูปแบบที่เหมาะสมที่สุดโดยอัตโนมัติ"}}