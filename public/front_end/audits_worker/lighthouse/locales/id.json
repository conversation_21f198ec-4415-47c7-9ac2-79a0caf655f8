{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "Kunci akses memungkinkan pengguna memfokuskan bagian halaman dengan cepat. Untuk navigasi yang tepat, setiap kunci akses harus unik. [P<PERSON>jari lebih lanjut](https://web.dev/accesskeys/)."}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "<PERSON><PERSON> `[accesskey]` tidak unik."}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "<PERSON><PERSON> `[accesskey]` bersifat unik"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Setiap ARIA `role` mendukung subset tertentu dari atribut `aria-*`. Membatalkan pencocokan ini akan membuat atribut `aria-*` menjadi tidak valid. [Pelajari lebih lanjut](https://web.dev/aria-allowed-attr/)."}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Atribut `[aria-*]` tidak cocok dengan perannya"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Atribut `[aria-*]` cocok dengan perannya"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "Beberapa peran ARIA memiliki atribut wajib yang menjelaskan status elemen ke pembaca layar. [<PERSON><PERSON>jar<PERSON> lebih lanjut](https://web.dev/aria-required-attr/)."}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]` tidak memiliki semua atribut `[aria-*]` yang dip<PERSON>an"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]` memiliki semua atribut `[aria-*]` yang dip<PERSON>an"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "Beberapa peran induk ARIA harus memuat peran turunan tertentu agar dapat menjalankan fungsi aksesibilitas yang diinginkan. [Pelajari lebih lanjut](https://web.dev/aria-required-children/)."}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> dengan`[role]` ARIA yang mewajibkan turunannya berisi `[role]` te<PERSON>tu kehilangan beberapa atau semua turunan yang dip<PERSON>lukan."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "<PERSON><PERSON><PERSON> dengan `[role]` ARIA yang mewajibkan turunannya berisi `[role]` tertentu memiliki semua turunan yang diperlukan."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "Beberapa peran turunan ARIA harus dimuat oleh peran induk tertentu agar dapat menjalankan fungsi aksesibilitas yang diinginkan dengan tepat. [Pelajari lebih lanjut](https://web.dev/aria-required-parent/)."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]` tidak dimuat oleh elemen induk wajibnya"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]` dimuat oleh elemen induk wajibnya"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "Peran ARIA harus memiliki nilai yang valid agar dapat menjalankan fungsi aksesibilitas yang diinginkan. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://web.dev/aria-roles/)."}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "<PERSON><PERSON> `[role]` tidak valid"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "<PERSON><PERSON> `[role]` valid"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Teknologi asistif, se<PERSON>i pem<PERSON> layar, tidak dapat menafsirkan atribut ARIA dengan nilai yang tidak valid. [Pelajari lebih lanjut](https://web.dev/aria-valid-attr-value/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Atribut `[aria-*]` tidak memiliki nilai yang valid"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Atribut `[aria-*]` memiliki nilai yang valid"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "Teknologi asistif, se<PERSON>i pem<PERSON> layar, tidak dapat menafsirkan atribut ARIA dengan nama yang tidak valid. [Pelajari lebih lanjut](https://web.dev/aria-valid-attr/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Atribut `[aria-*]` tidak valid atau salah eja"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "Atribut `[aria-*]` valid dan tidak salah eja"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "Teks membuat elemen audio mudah digunakan oleh pengguna yang menyandang gangguan pendengaran atau tunarungu, sehingga memberikan informasi penting seperti siapa yang berbicara, apa yang di<PERSON>kan, dan informasi non-lisan lainnya. [Pelajari lebih lanjut](https://web.dev/audio-caption/)."}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "Elemen `<audio>` tidak memiliki elemen `<track>` dengan `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "Elemen `<audio>` memuat elemen `<track>` dengan `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "<PERSON><PERSON><PERSON> yang <PERSON>"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "Jika tombol tidak memiliki nama yang dapat diakses, pembaca layar akan mengucapkannya sebagai \"tombol\", sehingga tidak dapat digunakan oleh pengguna yang mengandalkan pembaca layar. [Pelajari lebih lanjut](https://web.dev/button-name/)."}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "Tombol tidak memiliki nama yang dapat diakses"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "Tombol memiliki nama yang dapat diakses"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "Dengan menambahkan cara untuk mengabaikan konten be<PERSON>lang, pengguna keyboard dapat membuka halaman dengan lebih efisien. [Pelajari lebih lanjut](https://web.dev/bypass/)."}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "Halaman ini tidak memuat judul, link le<PERSON>, atau wilayah landmark"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "Halaman ini memuat judul, link le<PERSON>, atau wilayah landmark"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "Teks yang memiliki kontras rendah sulit atau tidak mungkin dibaca oleh kebanyakan pengguna. [Pelajari lebih lanjut](https://web.dev/color-contrast/)."}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "<PERSON>na latar belakang dan latar depan tidak memiliki rasio kontras yang cukup."}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "<PERSON>na latar belakang dan latar depan memiliki rasio kontras yang cukup"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "Jika daftar definisi tidak di-markup dengan tepat, pembaca layar dapat menghasilkan output yang membingungkan atau tidak akurat. [P<PERSON>jari lebih lanjut](https://web.dev/definition-list/)."}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>` tidak memuat grup `<dt>` dan `<dd>`, `<script>`, atau elemen `<template>` yang diurutkan dengan tepat."}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` hanya memuat grup `<dt>` dan `<dd>`, `<script>`, atau elemen `<template>` yang diurutkan dengan tepat."}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "Item daftar definisi (`<dt>` dan `<dd>`) harus tergabung dalam elemen `<dl>` induk untuk memastikan bahwa pembaca layar dapat mengucapkannya dengan tepat. [Pelajari lebih lanjut](https://web.dev/dlitem/)."}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "Item daftar definisi tidak tergabung dalam elemen `<dl>`"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "Item daftar definisi tergabung dalam elemen `<dl>`"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "Judul ini memberi pengguna pembaca layar ringkasan halaman, dan pengguna mesin telusur sangat mengandalkannya untuk menentukan apakah halaman relevan dengan penelusurannya atau tidak. [Pelajari lebih lanjut](https://web.dev/document-title/)."}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "Dokumen tidak memiliki elemen `<title>`"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "Dokumen memiliki elemen `<title>`"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "Nilai atribut ID harus unik untuk mencegah instance lain terabaikan oleh teknologi asistif. [Pelajari lebih lanjut](https://web.dev/duplicate-id/)."}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "Atribut `[id]` di halaman ini tidak unik"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "Atribut `[id]` di halaman ini unik"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "Pengguna pembaca layar mengandalkan judul bingkai untuk menjelaskan isi bingkai. [Pelajari lebih lanjut](https://web.dev/frame-title/)."}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "Elemen `<frame>` atau `<iframe>` tidak memiliki judul"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "Elemen `<frame>` atau `<iframe>` memiliki judul"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "Jika halaman tidak menentukan atribut bahasa, pembaca layar akan mengasumsikan bahwa halaman menggunakan bahasa default yang dipilih pengguna saat menyiapkan pembaca layar. Jika halaman tidak dalam bahasa default, pembaca layar mungkin tidak dapat mengucapkan teks di halaman tersebut dengan benar. [Pelajari lebih lanjut](https://web.dev/html-has-lang/)."}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Elemen `<html>` tidak memiliki atribut `[lang]`"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "Elemen `<html>` memiliki atribut `[lang]`"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "Menentukan [bahasa BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) yang valid akan membantu pembaca layar mengucapkan teks dengan tepat. [P<PERSON>jar<PERSON> lebih lanjut](https://web.dev/html-lang-valid/)."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Elemen `<html>` tidak memiliki nilai yang valid untuk atribut `[lang]`-nya."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "Elemen `<html>` memiliki nilai yang valid untuk atribut `[lang]`-nya"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "Elemen informatif harus memberikan teks alternatif yang singkat dan deskriptif. Elemen dekoratif dapat diabaikan dengan atribut alt kosong. [<PERSON><PERSON>jar<PERSON> lebih lanjut](https://web.dev/image-alt/)."}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "Elemen gambar tidak memiliki atribut `[alt]`"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "Elemen halaman memiliki atribut `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "Saat suatu gambar digunakan sebagai tombol `<input>`, menyediakan teks alternatif dapat membantu pengguna pembaca layar memahami fungsi tombol tersebut. [Pelajari lebih lanjut](https://web.dev/input-image-alt/)."}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Elemen `<input type=\"image\">` tidak memiliki teks `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "Elemen `<input type=\"image\">` memiliki teks `[alt]`"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "Label memastikan bahwa kontrol bentuk diucapkan dengan tepat oleh teknologi asistif, seperti pembaca layar. [Pelajari lebih lanjut](https://web.dev/label/)."}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "Elemen formulir tidak memiliki label yang terkait"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "Elemen formulir memiliki label yang terkait"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "Tabel yang digunakan untuk tujuan tata letak sebaiknya tidak mencakup elemen data, seperti elemen th atau teks atau atribut ringkasan karena hal ini dapat menyebabkan pengalaman yang membingungkan bagi pengguna pembaca layar. [Pelajari lebih lanjut](https://web.dev/layout-table/)."}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "Elemen `<table>` presentasional tidak menghindari penggunaan atribut `<th>`, `<caption>`, atau `[summary]`."}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "Elemen `<table>` yang bersifat presentasional menghindari penggunaan atribut `<th>`, `<caption>`, atau `[summary]`."}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "Teks link (dan teks alternatif untuk gambar, saat digunakan sebagai link) yang mudah dilihat, unik, dan dapat difokuskan menyempurnakan pengalaman navigasi bagi pengguna pembaca layar. [Pelajari lebih lanjut](https://web.dev/link-name/)."}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "Link tidak memiliki nama yang dapat dikenali"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "<PERSON> memiliki nama yang dapat dikenali"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "Pembaca layar memiliki cara tertentu untuk membacakan daftar. Memastikan struktur daftar dengan tepat akan membantu output pembaca layar. [Pelajari lebih lanjut](https://web.dev/list/)."}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "Daftar tidak hanya berisi elemen `<li>` dan skrip yang mendukung elemen (`<script>` dan `<template>`)."}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "Daftar hanya memuat elemen `<li>` dan skrip yang mendukung elemen (`<script>` dan `<template>`)."}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "Pembaca layar mengharuskan item daftar (`<li>`) untuk dimuat dalam `<ul>` atau `<ol>` induk agar dapat diucapkan dengan tepat. [Pelajari lebih lanjut](https://web.dev/listitem/)."}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "Daftar item (`<li>`) tidak dimuat dalam elemen induk `<ul>` atau `<ol>`."}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "Item daftar (`<li>`) dimuat dalam elemen induk `<ul>` atau `<ol>`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "Pengguna tidak mengharapkan halaman dimuat ulang secara otomatis, karena tindak<PERSON> tersebut akan memindahkan fokus kembali ke bagian atas halaman. Hal ini dapat menimbulkan pengalaman yang menjeng<PERSON>kan atau membingungkan. [Pelajari lebih lanjut](https://web.dev/meta-refresh/)."}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Do<PERSON>men menggunakan `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "Dokumen tidak menggunakan `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "Menonaktifkan zoom akan menimbulkan masalah bagi pengguna dengan gangguan penglihatan yang mengandalkan pembesaran layar untuk melihat konten halaman dengan tepat. [Pelajari lebih lanjut](https://web.dev/meta-viewport/)."}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` digunakan dalam elemen `<meta name=\"viewport\">` atau atribut `[maximum-scale]` kurang dari 5."}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` tidak digunakan dalam elemen `<meta name=\"viewport\">` dan atribut `[maximum-scale]` tidak kurang dari 5."}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "Pembaca layar tidak dapat menerjemahkan konten yang bukan teks. Menambahkan teks alternatif ke elemen `<object>` membantu pembaca layar menyampaikan makna kepada pengguna. [P<PERSON>jari lebih lanjut](https://web.dev/object-alt/)."}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "Elemen `<object>` tidak memiliki teks `[alt]`"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "Elemen `<object>` memiliki teks `[alt]`"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "<PERSON><PERSON> yang lebih besar dari 0 menunjukkan pengurutan navigasi eksplisit. Walaupun secara teknis valid, hal ini sering menciptakan pengalaman yang membingungkan bagi pengguna yang mengandalkan teknologi asistif. [Pelajari lebih lanjut](https://web.dev/tabindex/)."}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "Beberapa elemen memiliki nilai `[tabindex]` yang lebih besar dari 0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "Tidak ada elemen yang memiliki nilai `[tabindex]` lebih besar dari 0"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "Pembaca layar memiliki fitur yang memudahkan navigasi tabel. Memastikan sel `<td>` yang menggunakan atribut `[headers]` hanya merujuk ke sel lain dalam tabel yang sama dapat menyempurnakan pengalaman bagi pengguna pembaca layar. [Pelajari lebih lanjut](https://web.dev/td-headers-attr/)."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Sel di elemen `<table>` yang menggunakan atribut `[headers]` yang merujuk pada elemen `id` tidak ditemukan dalam tabel yang sama."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "Sel dalam elemen `<table>` yang menggunakan atribut `[headers]` yang merujuk pada sel tabel dalam tabel yang sama."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "Pembaca layar memiliki fitur yang memudahkan navigasi tabel. Memastikan header tabel selalu merujuk ke sekumpulan sel dapat menyempurnakan pengalaman bagi pengguna pembaca layar. [Pelajari lebih lanjut](https://web.dev/th-has-data-cells/)."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Elemen `<th>` dan elemen dengan `[role=\"columnheader\"/\"rowheader\"]` tidak memiliki sel data yang dideskripsikannya."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "Elemen `<th>` dan elemen dengan `[role=\"columnheader\"/\"rowheader\"]` memiliki sel data yang didesk<PERSON>."}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "Menentukan [bahasa BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) yang valid pada elemen membantu memastikan bahwa teks diucapkan dengan benar oleh pembaca layar. [Pelajari lebih lanjut](https://web.dev/valid-lang/)."}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Atribut `[lang]` tidak memiliki nilai yang valid"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "Atribut `[lang]` memiliki nilai yang valid"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "Video yang menyediakan teks akan memudahkan pengguna yang menyandang gangguan pendengaran dan tunarungu untuk mengakses informasinya. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://web.dev/video-caption/)."}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "Elemen `<video>` tidak memuat elemen `<track>` dengan `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "Elemen `<video>` memuat elemen `<track>` dengan `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "Deskripsi audio memberikan informasi yang relevan untuk video yang tidak dapat diberikan melalui dialog, seperti ekspresi wajah dan adegan. [<PERSON><PERSON>jar<PERSON> lebih lanjut](https://web.dev/video-description/)."}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "Elemen `<video>` tidak memuat elemen `<track>` dengan `[kind=\"description\"]`."}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "Elemen `<video>` memuat elemen `<track>` dengan `[kind=\"description\"]`"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "Untuk tampilan ideal pada iOS saat pengguna menambahkan progressive web app ke layar utama, tentukan `apple-touch-icon`. Ikon harus mengarah ke PNG persegi 192 piksel (atau 180 piksel) yang tidak transparan. [<PERSON><PERSON><PERSON><PERSON>](https://web.dev/apple-touch-icon/)."}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "T<PERSON>k <PERSON> `apple-touch-icon` yang valid"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "`apple-touch-icon-precomposed` sudah usang; `apple-touch-icon` dipilih."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "Memberikan `apple-touch-icon` yang valid"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Ekstensi Chrome berpengaruh negatif terhadap performa pemuatan halaman ini. Coba audit halaman dalam mode samaran atau dari profil Chrome tanpa ekstensi."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "Waktu CPU Total"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "Sebaiknya kurangi waktu yang dihabiskan untuk mengurai, <PERSON><PERSON><PERSON><PERSON><PERSON>, dan mengeksekusi JS. Coba kirim payload JS yang lebih kecil untuk membantu mengurangi waktu. [Pelajari lebih lanjut](https://web.dev/bootup-time)."}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "Mengurangi waktu eksekusi JavaScript"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "Waktu eksekusi JavaScript"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "GIF berukuran besar tidak efisien untuk menayangkan konten animasi. Sebaiknya gunakan video MPEG4/WebM sebagai animasi dan PNG/WebP sebagai gambar statis untuk menggantikan GIF guna menghemat byte jaringan. [Pelajari lebih lanjut](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Gunakan format video untuk konten animasi"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Sebaiknya lakukan pemuatan lambat di balik layar dan gambar tersembunyi setelah semua resource kritis selesai dimuat guna mengurangi waktu untuk interaktif. [Pelajari lebih lanjut](https://web.dev/offscreen-images)."}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "<PERSON>nda gambar di balik layar"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Resource memblokir first paint halaman. Sebaiknya kirim inline JS/CSS kritis dan tunda semua JS/gaya yang tidak kritis. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://web.dev/render-blocking-resources)."}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Hilangkan resource yang memblokir render"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Payload jaringan yang besar menimbulkan biaya yang tinggi bagi pengguna dan berkorelasi erat dengan waktu pemuatan yang lama. [Pelajari lebih lanjut](https://web.dev/total-byte-weight)."}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Total ukuran adalah {totalBytes, number, bytes} KB"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Menghindari payload jaringan yang sangat besar"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Menghindari payload jaringan yang sangat besar"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "Meminifikasi file CSS dapat mengurangi ukuran payload jaringan. [Pelajari lebih lanjut](https://web.dev/unminified-css)."}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "Kecilkan CSS"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Meminifikasi file JavaScript dapat mengurangi ukuran payload dan waktu penguraian skrip. [Pelajari lebih lanjut](https://web.dev/unminified-javascript)."}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Kecilkan ukuran JavaScript"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Hapus aturan yang tidak berlaku dari stylesheet dan tunda pemuatan CSS yang tidak digunakan untuk konten paruh atas, guna mengurangi byte yang tidak perlu yang digunakan oleh aktivitas jaringan. [Pelajari lebih lanjut](https://web.dev/unused-css-rules)."}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Hapus CSS yang tidak digunakan"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Menghapus JavaScript yang tidak digunakan untuk mengurangi byte yang digunakan oleh aktivitas jaringan."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Hapus JavaScript yang tidak digunakan"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Durasi cache yang panjang dapat mempercepat kunjungan berulang ke halaman Anda. [Pelajari lebih lanjut](https://web.dev/uses-long-cache-ttl)."}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 resource ditemukan}other{# resource ditemukan}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Tayangkan aset statis dengan kebijakan cache yang efisien"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Menggunakan kebijakan cache yang efisien pada aset statis"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "G<PERSON>bar yang dioptimalkan dimuat lebih cepat dan menghabiskan lebih sedikit data seluler. [Pelajari lebih lanjut](https://web.dev/uses-optimized-images)."}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Enkode gambar secara efisien"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Menayangkan gambar yang berukuran sesuai untuk menghemat kuota dan meningkatkan waktu pemuatan. [Pelajari lebih lanjut](https://web.dev/uses-responsive-images)."}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Ubah ukuran gambar dengan tepat"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Resource berbasis teks harus ditayangkan dengan kompresi (gzip, deflate, atau brotli) untuk meminimalkan total byte jaringan. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://web.dev/uses-text-compression)."}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Aktifkan kompresi teks"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "Format gambar seperti JPEG 2000, JPEG XR, dan <PERSON>P biasanya memberikan kompresi yang lebih baik daripada PNG atau JPEG, sehingga download lebih cepat dan konsumsi data lebih kecil. [Pelajari lebih lanjut](https://web.dev/uses-webp-images)."}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "Tayangkan gambar dalam format generasi berikutnya"}, "lighthouse-core/audits/content-width.js | description": {"message": "Jika lebar konten aplikasi Anda tidak cocok dengan lebar area pandang, aplikasi mungkin tidak dioptimalkan untuk layar perangkat seluler. [Pelajari lebih lanjut](https://web.dev/content-width)."}, "lighthouse-core/audits/content-width.js | explanation": {"message": "Ukuran area pandang {innerWidth} piksel tidak cocok dengan ukuran jendela {outerWidth} piksel."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "Ukuran konten untuk area pandang tidak tepat"}, "lighthouse-core/audits/content-width.js | title": {"message": "Ukuran konten untuk area pandang sudah tepat"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "Rantai Permintaan Penting di bawah menampilkan resource apa saja yang dimuat dengan prioritas tinggi. Sebaiknya kurangi panjang rantai, kurangi ukuran download resource, atau tunda download resource yang tidak penting untuk mempercepat pemuatan halaman. [Pelajari lebih lanjut](https://web.dev/critical-request-chains)."}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 rantai ditemukan}other{# rantai ditemukan}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "Minimalkan Kedalaman Permintaan Penting"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "Penghentian/Peringatan"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/deprecations.js | description": {"message": "API yang tidak digunakan lagi pada akhirnya akan dihapus dari browser. [Pelajari lebih lanjut](https://web.dev/deprecations)."}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 peringatan ditemukan}other{# peringatan ditemukan}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "Menggunakan API yang tidak digunakan lagi"}, "lighthouse-core/audits/deprecations.js | title": {"message": "Menghindari API yang tidak digunakan lagi"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "Cache Aplikasi tidak digunakan lagi. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://web.dev/appcache-manifest)."}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "Menemukan \"{AppCacheManifest}\""}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "Menggunakan Cache Aplikasi"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "Menentukan doctype akan mencegah browser beral<PERSON> ke quirks mode. [Pelajari lebih lanjut](https://web.dev/doctype)."}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Nama doctype harus berupa string huruf kecil `html`"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Dokumen harus berisi doctype"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "PublicId diperkirakan menjadi string kosong"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "SystemId diperkirakan menjadi string kosong"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Halaman tidak memiliki doctype HTML sehingga memicu quirks mode"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "Halaman memiliki doctype HTML"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "Elemen"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistik"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "Engineer browser merekomendasikan halaman yang berisi kurang dari ~ 1.500 elemen DOM. Ukuran yang pas adalah suatu kedalaman hierarki yang terdiri dari <32 elemen dan kurang dari 60 elemen turunan/induk. DOM yang besar dapat meningkatkan penggunaan memori, men<PERSON><PERSON><PERSON><PERSON> [penghit<PERSON><PERSON> gaya](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) yang lebih lama, dan menghasilkan [penyesuaian tata letak](https://developers.google.com/speed/articles/reflow) yang mahal. [Pelajari lebih lanjut](https://web.dev/dom-size)."}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 elemen}other{# elemen}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Menghindari ukuran DOM yang be<PERSON>han"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Kedalaman DOM Maksimum"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Elemen DOM Total"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "Menghindari ukuran DOM yang be<PERSON>han"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "Target"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "Tambahkan `rel=\"noopener\"` atau `rel=\"noreferrer\"` ke link eksternal mana pun untuk menyempurnakan performa dan mencegah kerentanan keamanan. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://web.dev/external-anchors-use-rel-noopener)."}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "<PERSON> ke tujuan lintas asal tidak aman"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "<PERSON> ke tujuan lintas asal aman"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "Tidak dapat menentukan tujuan untuk anchor ({anchorHTML}). Jika tidak digunakan sebagai hyperlink, sebaiknya hapus target=_blank."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Pengguna tidak percaya atau bingung dengan situs yang meminta lokasi mereka tanpa konteks. Sebaiknya kaitkan permintaan dengan tindakan pengguna. [Pelajari lebih lanjut](https://web.dev/geolocation-on-start)."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Meminta izin geolokasi pada pemuatan halaman"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Menghindari meminta izin geolokasi pada pemuatan halaman"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "Semua library JavaScript front-end terdete<PERSON>i di halaman. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://web.dev/js-libraries)."}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "Library JavaScript yang terdeteksi"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "Untuk pengguna dengan koneksi la<PERSON>t, skrip eksternal secara dinamis dimasukkan melalui `document.write()` dapat menunda pemuatan halaman selama puluhan detik. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://web.dev/no-document-write)."}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Menggunakan `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "Versi Library"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "Beberapa skrip pihak ketiga dapat berisi kerentanan keamanan umum yang mudah diidentifikasi dan dimanfaatkan oleh penyerang. [P<PERSON>jari lebih lanjut](https://web.dev/no-vulnerable-libraries)."}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{1 kerentanan terdeteksi}other{# kerentanan terdeteksi}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "Menyertakan library JavaScript front-end yang memiliki kerentanan keamanan umum"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "Tingg<PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "Rendah"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "Sedang"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "Menghindari library JavaScript front-end yang memiliki kerentanan keamanan umum"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "Pengguna tidak percaya atau bingung dengan situs yang meminta untuk mengirim pemberitahuan tanpa konteks. Sebaiknya kaitkan permintaan dengan gestur pengguna. [Pelajari lebih lanjut](https://web.dev/notification-on-start)."}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Meminta izin notifikasi pada pemuatan halaman"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "Mengh<PERSON>ri meminta izin notifikasi pada pemuatan halaman"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "<PERSON><PERSON><PERSON> yang <PERSON>"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "<PERSON><PERSON>gah menempelkan sandi akan merusak kebijakan keamanan yang baik. [Pelajari lebih lanjut](https://web.dev/password-inputs-can-be-pasted-into)."}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "Mencegah pengguna menempelkan sesuatu ke kolom sandi."}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "Mengizinkan pengguna menempelkan sesuatu ke kolom sandi"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokol"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 menawarkan banyak manfaat dibandingkan HTTP/1.1, termasuk header biner, multiplexing, dan push server. [Pelajari lebih lanjut](https://web.dev/uses-http2)."}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 permintaan tidak dilayani melalui HTTP/2}other{# permintaan tidak dilayani melalui HTTP/2}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "Tidak menggunakan HTTP/2 untuk semua resourcenya"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "Menggunakan HTTP/2 untuk resource miliknya sendiri"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> tandai sentuhan <PERSON>a dan pemroses peristiwa gulir sebagai `passive` untuk menyempurnakan performa scroll halaman Anda. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://web.dev/uses-passive-event-listeners)."}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Tidak menggunakan pemroses pasif untuk menyempurnakan performa scroll"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Menggunakan pemroses pasif untuk menyempurnakan performa scroll"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "Error yang dicatat di konsol menunjukkan masalah yang belum terselesaikan. Error dapat berasal dari permintaan jaringan yang gagal dan masalah browser lainnya. [Pelajari lebih lanjut](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "Error browser dicatat di konsol"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "Tidak ada error browser yang dicatat ke konsol"}, "lighthouse-core/audits/font-display.js | description": {"message": "Memanfaatkan fitur CSS tampilan font untuk memastikan teks terlihat oleh pengguna saat font web sedang dimuat. [Pelajari lebih lanjut](https://web.dev/font-display)."}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "Pastikan teks tetap terlihat selama pemuatan font web"}, "lighthouse-core/audits/font-display.js | title": {"message": "Semua teks tetap terlihat selama pemuatan font web"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "Lighthouse tidak dapat secara otomatis memeriksa nilai tampilan font untuk URL berikut: {fontURL}."}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "<PERSON><PERSON> (Aktual)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "<PERSON><PERSON> (Ditampilkan)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "Dimensi tampilan gambar harus cocok dengan rasio tinggi lebar natural. [Pelajari lebih lanjut](https://web.dev/image-aspect-ratio)."}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "Menampilkan gambar dengan rasio tinggi lebar yang salah"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "Menampilkan gambar dengan rasio tinggi lebar yang benar"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "Informasi ukuran gambar {url} tidak valid"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "Browser dapat secara proaktif meminta pengguna untuk menambahkan aplikasi Anda ke layar utama mereka sehingga interaksi menjadi lebih tinggi. [Pelajari lebih lanjut](https://web.dev/installable-manifest)."}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "Manifes aplikasi web tidak memenuhi persyaratan kemampuan penginstalan"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "Manifes aplikasi web memenuhi persyaratan kemampuan penginstalan"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "URL yang tidak aman"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "Semua situs harus dilindungi dengan HTTPS, termasuk situs-situs yang tidak menangani data sensitif. HTTPS mencegah penyusup merusak atau mendengarkan komunikasi secara pasif antara aplikasi dan pengguna, serta merupakan prasyarat untuk HTTP/2 dan banyak API platform web baru. [Pelajari lebih lanjut](https://web.dev/is-on-https)."}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 permintaan tidak aman ditemukan}other{# permintaan tidak aman ditemukan}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "Tidak menggunakan HTTPS"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "Menggunakan HTTPS"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "Pemuatan halaman yang cepat melalui jaringan seluler menjamin pengalaman pengguna seluler yang baik. [<PERSON><PERSON>jar<PERSON> lebih lanjut](https://web.dev/load-fast-enough-for-pwa)."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "Interaktif di {timeInMs, number, seconds} dtk"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "Interaktif di jaringan seluler tersimulasi pada {timeInMs, number, seconds} dtk"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "Pemuatan halaman terlalu lambat dan tidak interaktif selama 10 detik. <PERSON><PERSON> peluang dan diagnostik dalam bagian \"Performa\" untuk mempelajari cara meningkatkan kecepatan pemuatan."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "Pemuatan halaman tidak cukup cepat pada jaringan seluler"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "Pemuatan halaman cukup cepat pada jaringan seluler"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "Sebaiknya kurangi waktu yang dihabiskan untuk mengurai, <PERSON><PERSON><PERSON><PERSON><PERSON>, dan mengeksekusi JS. Coba kirim payload JS yang lebih kecil untuk membantu mengurangi waktu. [P<PERSON>jari lebih lanjut](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimalkan peker<PERSON>an thread utama"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "Meminimalkan peker<PERSON>an thread utama"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "Untuk menjangkau jumlah pengguna terbanyak, situs harus berjalan di seluruh browser utama. [P<PERSON>jari lebih lanjut](https://web.dev/pwa-cross-browser)."}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "Situs dapat ber<PERSON><PERSON> lintas-browser"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Pastikan halaman individu dapat dijadikan deep link melalui URL, dan URL tersebut unik agar dapat dibagikan di media sosial. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://web.dev/pwa-each-page-has-url)."}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Setiap halaman memiliki URL"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "Transisi harus terasa cepat ketika Anda men<PERSON>-ngetuk, bahkan saat jaringan lambat. Pengalaman ini merupakan kunci persepsi pengguna terhadap performa. [Pelajari lebih lanjut](https://web.dev/pwa-page-transitions)."}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "Transisi halaman sepertinya tidak diblokir di jaringan"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "Perkiraan Latensi Ma<PERSON>kan adalah perkiraan durasi yang diperlukan aplikasi untuk merespons masukan pengguna, dalam milidetik, selama durasi 5 detik tersibuk saat pemuatan halaman. Jika latensi di atas 50 md, pengguna dapat menganggap aplikasi Anda lambat. [Pelajari lebih lanjut](https://web.dev/estimated-input-latency)."}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "First Contentful Paint menandai waktu saat teks atau gambar pertama di-paint. [P<PERSON><PERSON><PERSON> lebih lanjut](https://web.dev/first-contentful-paint)."}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "First Contentful Paint"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "CPU Pertama Tidak Ada Aktivitas menandai waktu pertama kalinya thread utama halaman menjadi agak tenang untuk menangani masukan.  [<PERSON><PERSON>jar<PERSON> lebih lanjut](https://web.dev/first-cpu-idle)."}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "CPU Pertama Tidak Ada Aktivitas"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "First Meaningful Paint mengukur waktu saat konten utama halaman terlihat. [Pelajari lebih lanjut](https://web.dev/first-meaningful-paint)."}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "First Meaningful Paint"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "Waktu untuk interaktif adalah lamanya waktu yang diperlukan halaman untuk menjadi interaktif sepenuhnya. [Pelajari lebih lanjut](https://web.dev/interactive)."}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "Waktu untuk Interaktif"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "Potensi maksimal <PERSON>an Input Pertama yang dapat dialami pengguna Anda adalah durasi tugas terpanjang, yang dinyatakan dalam milidetik. [Pelajari lebih lanjut](https://developers.google.com/web/updates/2018/05/first-input-delay)."}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "Potensi Maks<PERSON> In<PERSON> Pertama"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "Indeks <PERSON> menunjukkan seberapa cepat konten halaman terlihat terisi lengkap. [Pelajari lebih lanjut](https://web.dev/speed-index)."}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "<PERSON><PERSON><PERSON> semua jangka waktu antara FCP dan Waktu untuk Interaktif, ketika durasi tugas melebihi 50 md, dinyatakan dalam milidetik."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "Total Waktu Pemblokiran"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "Waktu round trip (RTT) jaringan berdampak besar pada performa. RTT ke asal yang tinggi merupakan indikasi bahwa server yang berjarak lebih dekat ke pengguna dapat meningkatkan performa. [Pelajari lebih lanjut](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "lighthouse-core/audits/network-rtt.js | title": {"message": "Waktu Round Trip Jaringan"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "Latensi server dapat memengaruhi performa web. Latensi server untuk suatu asal yang tinggi merupakan indikasi bahwa server kelebihan muatan atau memiliki performa backend yang buruk. [Pelajari lebih lanjut](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "<PERSON><PERSON><PERSON>end <PERSON>"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "Pekerja layanan memungkinkan aplikasi web dapat diandalkan dalam kondisi jaringan yang tidak terduga. [Pelajari lebih lanjut](https://web.dev/offline-start-url)."}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url` tidak merespons dengan kode 200 saat offline"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url` merespons dengan kode 200 saat offline"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "Lighthouse tidak dapat membaca `start_url` dari manifes tersebut. <PERSON><PERSON> ka<PERSON> itu, `start_url` dianggap sebagai URL dokumen. Pesan error: '{manifestWarning}'."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "Pertahankan jumlah dan ukuran permintaan jaringan di bawah target yang ditetapkan oleh anggaran performa yang disediakan. [Pelajari lebih lanjut](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 permintaan}other{# permintaan}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "<PERSON><PERSON><PERSON> performa"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "Jika Anda sudah menyiapkan HTTPS, pastikan mengalihkan semua traffic HTTP ke HTTPS agar dapat menyediakan fitur web yang aman bagi semua pengguna. [Pelajari lebih lanjut](https://web.dev/redirects-http)."}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "Tidak mengalihkan traffic HTTP ke HTTPS"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "Mengalihkan traffic HTTP ke HTTPS"}, "lighthouse-core/audits/redirects.js | description": {"message": "Pengalihan mencakup penundaan tambahan sebelum halaman dapat dimuat. [Pelajari lebih lanjut](https://web.dev/redirects)."}, "lighthouse-core/audits/redirects.js | title": {"message": "<PERSON><PERSON><PERSON> pengalihan lebih dari satu halaman"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "Untuk mengatur anggaran jumlah dan ukuran resource halaman, tambahkan file budget.json. [P<PERSON>jar<PERSON> lebih lanjut](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 permintaan • {byteCount, number, bytes} KB}other{# permintaan • {byteCount, number, bytes} KB}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "Pertahankan jumlah permintaan tetap rendah dan ukuran transfer tetap kecil"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "Link kanonis menyarankan URL yang akan ditampilkan dalam hasil penelusuran. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://web.dev/canonical)."}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "<PERSON> beberapa URL yang berten<PERSON>gan ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "Mengarahkan ke domain yang berbeda ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "URL tidak valid ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Mengarahkan ke lokasi `hreflang` lain ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "URL relatif ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "Mengarah ke URL root domain (halaman beranda), bukan ke halaman yang setara untuk konten itu"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "Dokumen tidak memiliki `rel=canonical` yang valid"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "Dokumen memiliki `rel=canonical` yang valid"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "Ukuran font di bawah 12 piksel terlalu kecil untuk dapat dibaca dan memaksa pengunjung seluler “mencubit untuk memperbesar\" agar dapat membacanya. Usahakan untuk menampilkan >60% teks halaman dalam ukuran ≥12 piksel. [Pelajari lebih lanjut](https://web.dev/font-size)."}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "Teks yang dapat dibaca {decimalProportion, number, extendedPercent}"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "Teks tidak terbaca karena tidak ada tag meta viewport yang dioptimalkan untuk layar seluler."}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "{decimalProportion, number, extendedPercent} teks terlalu kecil (berdasarkan sampel {decimalProportionVisited, number, extendedPercent})."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "Dokumen tidak menggunakan ukuran font yang terbaca"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "Dokumen menggunakan ukuran font yang terbaca"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "Link hreflang memberi tahu mesin telusur versi halaman yang harus dicantumkan dalam hasil penelusuran untuk wilayah atau bahasa tertentu. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://web.dev/hreflang)."}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "Dokumen tidak memiliki `hreflang` yang valid"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "Dokumen memiliki `hreflang` yang valid"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "Halaman dengan kode status HTTP yang tidak berhasil mungkin tidak akan diindeks dengan tepat. [Pelajari lebih lanjut](https://web.dev/http-status-code)."}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "Halaman memiliki kode status HTTP yang tidak berhasil"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "Halaman memiliki kode status HTTP yang berhasil"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "Mesin telusur tidak dapat menyertakan halaman Anda dalam hasil penelusuran jika tidak memiliki izin untuk meng-crawl halaman tersebut. [<PERSON><PERSON>jar<PERSON> lebih lanjut](https://web.dev/is-crawable)."}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "<PERSON><PERSON> di<PERSON> dari <PERSON>"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "Halaman tidak diblokir dari pen<PERSON>"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "Teks link deskriptif membantu mesin telusur memahami konten <PERSON>. [<PERSON><PERSON><PERSON><PERSON> lebih lan<PERSON>t](https://web.dev/link-text)."}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 link ditemukan}other{# link ditemukan}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "Link tidak memiliki teks deskriptif"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "Link memiliki teks deskriptif"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "Jalankan [<PERSON><PERSON>an Data Terstruktur](https://search.google.com/structured-data/testing-tool/) dan [Linter Data Terstruktur](http://linter.structured-data.org/) untuk memvalidasi data terstruktur. [Pelajari lebih lanjut](https://web.dev/structured-data)."}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "Data terstruktur valid"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "Deskripsi meta mungkin disertakan dalam hasil penelusuran untuk merangkum isi halaman dengan singkat. [Pelajari lebih lanjut](https://web.dev/meta-description)."}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "Teks deskripsi kosong."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "Dokumen tidak memiliki deskripsi meta"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "Dokumen memiliki deskripsi meta"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "Mesin telusur tidak dapat mengindeks konten plugin, dan banyak perangkat yang membatasi plugin atau tidak mendukungnya. [Pelajari lebih lanjut](https://web.dev/plugins)."}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "Dokumen menggunakan plugin"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "<PERSON><PERSON><PERSON> plugin"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "Jika file robots.txt Anda salah format, crawler mungkin tidak dapat memahami cara crawling atau pengindeksan situs yang Anda inginkan. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://web.dev/robots-txt)."}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Permintaan untuk robots.txt menampilkan status HTTP: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 error ditemukan}other{# error ditemukan}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse tidak dapat mendownload file robots.txt"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt tidak valid"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "robots.txt valid"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "Elemen interaktif seperti tombol dan link harus berukuran cukup besar (48x48 piksel), dan memiliki cukup ruang di sekelilingnya, agar cukup mudah diketuk tanpa tumpang-tindih dengan elemen lain. [Pelajari lebih lanjut](https://web.dev/tap-targets)."}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} target ketuk memiliki ukuran yang tepat"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Target ketuk terlalu kecil karena tidak ada tag meta viewport yang dioptimalkan untuk layar seluler"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "Target ketuk tidak memiliki ukuran yang tepat"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Target Tumpang-Tindih"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Target Ketuk"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "Target ketuk memiliki ukuran yang tepat"}, "lighthouse-core/audits/service-worker.js | description": {"message": "<PERSON>ek<PERSON><PERSON> layanan adalah teknologi yang memungkinkan aplikasi Anda menggunakan banyak fitur Progressive Web App, seperti fitur offline, tambahkan ke layar utama, dan notifikasi push. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://web.dev/service-worker)."}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "Halaman ini dikontrol oleh pek<PERSON>, tetapi `start_url` tidak ditemukan karena manifes gagal diurai sebagai JSON yang valid"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "Halaman ini dikontrol oleh pekerja layanan, tetapi `start_url` ({startUrl}) tidak termasuk dalam cakupan pekerja layanan ({scopeUrl})"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "Halaman ini dikontrol oleh pek<PERSON>, tetapi `start_url` tidak ditemukan karena tidak ada manifes yang diambil."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "Asal memiliki satu pekerja layanan atau lebih, tetapi halaman ({pageUrl}) tidak termasuk dalam cakupan."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "Tidak mendaftarkan pekerja layanan yang mengontrol halaman dan `start_url`"}, "lighthouse-core/audits/service-worker.js | title": {"message": "Mendaftarkan pekerja layanan yang mengontrol halaman dan `start_url`"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "Layar pembuka yang diberi tema akan memberikan pengalaman berkualitas tinggi saat pengguna meluncurkan aplikasi dari layar utama. [<PERSON><PERSON>jari lebih lanjut](https://web.dev/splash-screen)."}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "Tidak dikonfigurasi untuk layar pembuka khusus"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "Dikonfigurasi untuk layar pembuka khusus"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "Kolom URL browser dapat diberi tema agar cocok dengan situs Anda. [Pelajari lebih lanjut](https://web.dev/themed-omnibox)."}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "Tidak menyetel warna tema untuk kolom URL."}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "Menyetel warna tema untuk kolom URL."}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "Kode pihak ketiga dapat memberikan dampak signifikan terhadap performa muatan. Batasi jumlah penyedia pihak ketiga yang berlebihan dan coba muat kode pihak ketiga terutama setelah halaman selesai memuat. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "Kode pihak ketiga memblokir thread utama untuk {timeInMs, number, milliseconds} ms"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "Kurang<PERSON> dampak kode pihak ketiga"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "Time To First Byte mengidentifikasi waktu saat server mengirim respons. [<PERSON><PERSON><PERSON><PERSON> lebih lanjut](https://web.dev/time-to-first-byte)."}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "Dokumen root memerlukan waktu {timeInMs, number, milliseconds} md"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "Mengurangi waktu respons server (TTFB)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "Waktu respons server sedikit (TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/user-timings.js | description": {"message": "Sebaiknya lengkapi aplikasi Anda dengan User Timing API untuk mengukur performa aplikasi Anda yang sebenarnya selama pengalaman pengguna utama. [<PERSON><PERSON>jar<PERSON> lebih lanjut](https://web.dev/user-timings)."}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 waktu pengguna}other{# waktu pengguna}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "Tanda dan ukuran <PERSON>"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Se<PERSON>ah <link> prakoneksi ditemukan untuk {securityOrigin}, tetapi tidak digunakan oleh browser. Pastikan Anda menggunakan atribut `crossorigin` dengan tepat."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "Sebaiknya tambahkan petunjuk resource `preconnect` atau `dns-prefetch` guna membuat sambungan lebih awal ke asal pihak ketiga yang penting. [Pelajari lebih lanjut](https://web.dev/uses-rel-preconnect)."}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "Sambungkan terlebih dahulu ke nama domain yang diperlukan"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Se<PERSON>ah <link> pramuat ditemukan untuk {preloadURL}, tetapi tidak digunakan oleh browser. Pastikan Anda menggunakan atribut `crossorigin` dengan tepat."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "Se<PERSON><PERSON><PERSON> gunakan `<link rel=preload>` untuk memprioritaskan pengambilan resource yang saat ini diminta selama pemuatan halaman. [Pelajari lebih lanjut](https://web.dev/uses-rel-preload)."}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "<PERSON>at permintaan utama terlebih dahulu"}, "lighthouse-core/audits/viewport.js | description": {"message": "Tambahkan tag `<meta name=\"viewport\">` guna mengoptimalkan aplikasi Anda untuk layar perangkat seluler. [P<PERSON>jari lebih lanjut](https://web.dev/viewport)."}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "Tag `<meta name=\"viewport\">` tidak ditemukan"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "Tidak memiliki tag `<meta name=\"viewport\">` dengan `width` atau `initial-scale`"}, "lighthouse-core/audits/viewport.js | title": {"message": "Memiliki tag `<meta name=\"viewport\">` dengan `width` atau `initial-scale`"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "Aplikasi Anda harus menampilkan beberapa konten saat JavaScript nonaktif, meskipun hanya berupa peringatan kepada pengguna bahwa JavaScript diperlukan untuk menggunakan aplikasi. [<PERSON><PERSON>jar<PERSON> lebih lanjut](https://web.dev/without-javascript)."}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "<PERSON>i halaman harus merender beberapa konten jika skripnya tidak tersedia."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "Tidak menyediakan konten pengganti saat JavaScript tidak tersedia"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "<PERSON><PERSON><PERSON> be<PERSON> konten saat JavaScript tidak tersedia"}, "lighthouse-core/audits/works-offline.js | description": {"message": "Jika Anda membuat build Progressive Web App, pertimbangkan untuk menggunakan pekerja layanan agar aplikasi dapat berfungsi secara offline. [<PERSON><PERSON>jar<PERSON> lebih lanjut](https://web.dev/works-offline)."}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "Halaman saat ini tidak merespons dengan kode 200 saat offline"}, "lighthouse-core/audits/works-offline.js | title": {"message": "Halaman saat ini merespons dengan kode 200 saat offline"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "Halaman tersebut mungkin tidak dimuat secara offline karena URL uji Anda ({requested}) tidak dialihkan ke \"{final}\". Coba uji URL kedua secara langsung."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "<PERSON>i adalah peluang untuk meningkatkan penggunaan ARIA pada aplikasi Anda, yang dapat meningkatkan pengalaman bagi pengguna teknologi asistif, seperti pembaca layar."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Ini adalah peluang untuk memberikan konten alternatif untuk audio dan video. Hal ini dapat meningkatkan pengalaman bagi pengguna yang menyandang gangguan penglihatan atau pendengaran."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Audio dan video"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Item berikut memperjelas praktik terbaik yang umum untuk aksesibilitas."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Praktik terbaik"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "Pemeriksaan ini menandai peluang untuk [menyempurnakan aksesibilitas aplikasi web Anda](https://developers.google.com/web/fundamentals/accessibility). Hanya sejumlah kecil masalah aksesibilitas yang dapat terdeteksi secara otomatis, se<PERSON><PERSON> pengujian manual juga dianjurkan."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "Item berikut ini menangani area yang tidak dapat dicakup oleh fitur pengujian otomatis. Pelajari lebih lanjut dalam panduan kami tentang [menjalankan tinjauan aksesibilitas](https://developers.google.com/web/fundamentals/accessibility/how-to-review)."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "Aksesibilitas"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Ini adalah peluang untuk meningkatkan keterbacaan konten Anda."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Kontras"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Ini adalah peluang untuk menyempurnakan interpretasi konten Anda oleh pengguna dalam lokal yang berbeda."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internasionalisasi dan pelokalan"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Ini adalah peluang untuk meningkatkan semantik kontrol dalam aplikasi Anda. Hal ini dapat menyempurnakan pengalaman bagi pengguna teknologi asistif, seperti pembaca layar."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Nama dan label"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Ini adalah peluang untuk menyempurnakan navigasi keyboard pada aplikasi Anda."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Na<PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Ini adalah peluang untuk meningkatkan pengalaman membaca data dalam format tabel atau daftar menggunakan teknologi asistif, seperti pembaca layar."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "<PERSON><PERSON> dan daftar"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Praktik Terbaik"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "Ang<PERSON><PERSON> performa menetapkan standar performa situs Anda."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "Informasi selengkapnya tentang performa aplikasi Anda. Angka ini tidak [secara langsung memengaruhi](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) skor Performa."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostik"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Aspek terpenting dari performa adalah seberapa cepat piksel dirender di layar. <PERSON><PERSON> <PERSON>: First Contentful Paint, First Meaningful Paint"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Penyempurnaan First Paint"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Saran ini dapat membantu pemuatan halaman menjadi lebih cepat. Saran tersebut tidak [secara langsung memengaruhi](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) skor Performa."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "<PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Menyempurnakan pengalaman pemuatan halaman k<PERSON>, se<PERSON><PERSON> halaman responsif dan siap untuk digunakan secepatnya. Metrik utama: <PERSON>aktu untu<PERSON>ak<PERSON>f, <PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "Performa"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "Pemeriksaan ini memvalidasi aspek-aspek Progressive Web App. [Pelajari lebih lanjut](https://developers.google.com/web/progressive-web-apps/checklist)."}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "Pemeriksaan ini diperlukan oleh [Checklist PWA](https://developers.google.com/web/progressive-web-apps/checklist) untuk dasar pengukuran tetapi tidak otomatis diperiksa oleh Lighthouse. Pemeriksaan tersebut tidak memengaruhi skor, tetapi penting karena hal ini berarti Anda memverifikasi situs-situs secara manual."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "Progressive Web App"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "<PERSON><PERSON><PERSON> dan dapat di<PERSON>an"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Dapat <PERSON>"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PWA yang Dioptimalkan"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "Pemeriksaan ini memastikan bahwa halaman Anda dioptimalkan untuk pemeringkatan hasil mesin telusur. Ada faktor lain yang tidak diperiksa Lighthouse yang dapat memengaruhi pemeringkatan penelusuran Anda. [P<PERSON>jari lebih lanjut](https://support.google.com/webmasters/answer/35769)."}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "Jalankan validator tambahan ini di situs Anda untuk memeriksa praktik terbaik SEO lainnya."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "Format HTML Anda dengan cara yang memungkinkan crawler untuk lebih memahami konten aplikasi Anda."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "Praktik Terbaik Konten"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "<PERSON>gar muncul di hasil penel<PERSON><PERSON>n, crawler perlu menga<PERSON><PERSON> aplik<PERSON>."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Crawling dan <PERSON>"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "Pastikan halaman Anda mobile-friendly agar pengguna tidak perlu mencubit atau memperbesar untuk membaca halaman konten. [Pelajari lebih lanjut](https://developers.google.com/search/mobile-sites/)."}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "Mobile Friendly"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "TTL Cache"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "Ukuran"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "<PERSON><PERSON><PERSON> ya<PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "Ukuran Transfer"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Potensi Penghematan"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "Potensi Penghematan"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Potensi penghematan sebesar {wastedBytes, number, bytes} KB"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Potensi penghematan {wastedMs, number, milliseconds} md"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokumen"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "Font"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "Gambar"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "Media"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} md"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} dtk"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Stylesheet"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "<PERSON><PERSON> ketiga"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "Total"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "<PERSON><PERSON><PERSON><PERSON> kesalahan saat merekam jejak selama pemuatan halaman Anda. Harap jalankan Lighthouse kembali. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "W<PERSON>tu tunggu untuk sambungan Protokol Debugger awal berakhir."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome tidak men<PERSON><PERSON><PERSON>an screenshot apa pun selama pemuatan halaman. Pastikan terdapat konten yang terlihat pada halaman, kemudian coba jalankan kembali Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "Server DNS tidak dapat menetapkan domain yang disediakan."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Terjadi error pada pengumpul {artifactName} wajib: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "Terjadi error Chrome internal. Harap mulai ulang Chrome dan coba jalankan kembali Lighthouse."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "Pengumpul {artifactName} yang diperlukan tidak berjalan."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse tidak dapat memuat halaman yang Anda minta dengan lancar. Pastikan Anda menguji URL yang benar dan server merespons semua permintaan dengan baik."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse tidak dapat memuat URL yang Anda minta dengan lancar karena halaman berhenti merespons."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "URL yang Anda berikan tidak memiliki sertifikat keamanan yang valid. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome mencegah pemuatan halaman dengan interstisial. Pastikan Anda menguji URL yang benar dan server merespons semua permintaan dengan baik."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse tidak dapat memuat halaman yang Anda minta dengan lancar. Pastikan Anda menguji URL yang benar dan server merespons semua permintaan dengan baik. (Detail: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse tidak dapat memuat halaman yang Anda minta dengan lancar. Pastikan Anda menguji URL yang benar dan server merespons semua permintaan dengan baik. (Kode status: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Waktu pemuatan halaman Anda terlalu lama. <PERSON><PERSON> ikuti peluang dalam laporan untuk mengurangi waktu muat halaman Anda, kemudian coba jalankan kembali Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "<PERSON><PERSON><PERSON> tunggu respons protokol DevTools telah melampaui waktu yang dialokasikan. (Metode: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "Pengambilan konten resource telah melampaui waktu yang dialokasikan"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "URL yang Anda berikan tampaknya tidak valid."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "Tampilkan audit"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "Latensi lokasi kritis maksimal:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "Error!"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "Error laporan: tidak ada informasi audit"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "Data Lab"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "<PERSON><PERSON>is [Lighthouse](https://developers.google.com/web/tools/lighthouse/) untuk halaman saat ini di jaringan seluler teremulasi. <PERSON><PERSON> adalah hasil perkiraan dan dapat berbeda-beda."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "<PERSON>em tambahan untuk diperiksa secara manual"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "Tidak berlaku"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "Lulus audit"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "Ciutkan cuplikan"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "Luask<PERSON> cup<PERSON>an"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "Tampilkan resource pihak ketiga"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "Ada masalah yang memengaruhi jalannya Lighthouse ini:"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "<PERSON><PERSON> adalah hasil perkiraan dan dapat berbeda-beda. Skor performa [hanya berdasarkan metrik-metrik ini](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "Lulus audit tetapi dengan per<PERSON>"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "Peringatan: "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "Sebaiknya upload GIF ke server yang akan menyediakannya untuk disematkan sebagai video HTML5."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "Instal [plugin pemuatan lambat di WordPress ](https://wordpress.org/plugins/search/lazy+load/) yang menyediakan kemampuan untuk menunda pemuatan gambar di bagian halaman yang belum ditampilkan, atau beralihlah ke tema yang menyediakan fungsi tersebut. Sebaiknya juga gunakan [plugin AMP](https://wordpress.org/plugins/amp/)."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "Terdapat sejumlah plugin di WordPress yang dapat membantu Anda [menyejajarkan aset penting](https://wordpress.org/plugins/search/critical+css/) atau [menunda resource yang tidak penting](https://wordpress.org/plugins/search/defer+css+javascript/). Harap berhati-hati karena pengoptimalan yang disediakan oleh plugin ini dapat merusak fitur tema atau plugin, sehingga Anda cenderung perlu mengubah kode."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "<PERSON><PERSON><PERSON> spes<PERSON> tema, plugin, dan server berkontribusi pada waktu respons server. Sebaiknya cari tema yang lebih optimal, pilih plugin pengopt<PERSON><PERSON> dengan hati-hati, dan/atau upgrade server <PERSON><PERSON>."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "Sebaiknya tampilkan kutipan dalam daftar postingan (misal<PERSON> melalui tag lainnya), kurangi jumlah postingan yang ditampilkan pada halaman yang ada, bagi postingan panjang menjadi beberapa halaman, atau gunakan plugin untuk menunda pemuatan (lazy-load) komentar."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "<PERSON><PERSON><PERSON><PERSON> [plugin WordPress](https://wordpress.org/plugins/search/minify+css/) dapat mempercepat situs Anda dengan menggab<PERSON>kan, memini<PERSON><PERSON><PERSON>, dan mengompresi gaya Anda. Anda juga dapat menggunakan proses pembuatan build untuk melakukan minifikasi di tahap awal jika memungkinkan."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "<PERSON><PERSON><PERSON><PERSON> [plugin WordPress](https://wordpress.org/plugins/search/minify+javascript/) dapat mempercepat situs Anda dengan menggabungkan, memini<PERSON><PERSON>i, dan mengompresi skrip. Anda juga dapat menggunakan proses pembuatan build untuk melakukan minifikasi di awal jika mungkin."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "Sebaiknya kurangi atau ubah jumlah [plugin WordPress](https://wordpress.org/plugins/) yang memuat CSS yang tidak digunakan di halaman Anda. Untuk mengidentifikasi plugin yang menambahkan CSS tidak relevan, coba jalankan [cakupan kode](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) di Chrome DevTools. Anda dapat mengidentifikasi tema/plugin yang bertanggung jawab dari URL stylesheet. Cari plugin yang memiliki banyak stylesheet dalam daftar yang memiliki banyak warna merah dalam cakupan kode. Plugin sebaiknya hanya menambahkan stylesheet ke antrean jika memang benar digunakan di halaman."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "Sebaiknya kurangi atau ubah jumlah [plugin WordPress](https://wordpress.org/plugins/) yang memuat JavaScript yang tidak digunakan di halaman Anda. Untuk mengidentifikasi plugin yang menambahkan JS tidak relevan, coba jalankan [cakupan kode](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) di Chrome DevTools. Anda dapat mengidentifikasi tema/plugin yang bertanggung jawab dari URL skrip. Cari plugin dengan banyak skrip dalam daftar yang memiliki banyak warna merah dalam cakupan kode. Plugin sebaiknya hanya menambahkan skrip ke dalam antrean jika memang benar digunakan di halaman."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "Baca [<PERSON><PERSON> Browser di WordPress](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "<PERSON><PERSON><PERSON><PERSON> gunakan [plugin WordPress untuk pengoptimalan gambar](https://wordpress.org/plugins/search/optimize+images/) yang mengompresi gambar Anda dengan tetap mempertahankan kualitas."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "Upload gambar langsung melalui [koleksi media](https://codex.wordpress.org/Media_Library_Screen) untuk memastikan ukuran gambar yang diperlukan tersedia, lalu masukkan koleksi media atau gunakan widget gambar untuk memastikan ukuran gambar optimal digunakan (termasuk untuk titik henti sementara responsif). Hindari menggunakan gambar `Full Size`, kecuali dimensinya memungkinkan untuk digunakan. [Pelajari Lebih Lanjut](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)."}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "Anda dapat mengaktifkan kompresi teks di konfigurasi server web."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "<PERSON><PERSON><PERSON><PERSON> gunakan [plugin](https://wordpress.org/plugins/search/convert+webp/) atau layanan yang otomatis mengonversi gambar yang diupload ke format optimal."}}