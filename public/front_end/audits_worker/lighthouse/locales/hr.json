{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "Pristupne tipke omogućuju korisnicima da se brzo usredotoče na određeni dio stranice. Za pravilno kretanje svaka pristupna tipka mora biti jedinstvena. [Saznajte više](https://web.dev/accesskeys/)."}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> `[accesskey]` nisu jedinstvene"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]` vrijednosti su jedinstvene"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON><PERSON> ARIA `role` podr<PERSON><PERSON> određeni podskup `aria-*` atributa. Njihovo nepodudaranje poništava `aria-*` atribute. [Saznajte više](https://web.dev/aria-allowed-attr/)."}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Atributi `[aria-*]` ne podudaraju se sa svojim ulogama"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Atributi `[aria-*]` podudaraju se sa svojim ulogama"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "Neke uloge ARIA-e zahtijevaju atribute koji opisuju stanje elementa čitačima zaslona. [Saznajte više](https://web.dev/aria-required-attr/)."}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Elementi `[role]` nemaju sve potrebne atribute `[aria-*]`"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]` imaju sve obavezne atribute`[aria-*]`"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "Neke nadređene uloge ARIA moraju sadržavati posebne podređene uloge za izvršavanje svojih namijenjenih funkcija pristupačnosti. [Saznajte više](https://web.dev/aria-required-children/)."}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Elementi s ARIA-om `[role]` koji zahtijeva<PERSON> da podređeni elementi sadrže određenu ulogu `[role]` ne sadrže neke ili sve te obavezne podređene elemente."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "Elementi s ARIA-om `[role]` koji zahtije<PERSON> da podređeni elementi sadrže određenu ulogu `[role]` imaju sve obavezne podređene elemente."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "Određene nadređene uloge moraju sadržavati neke podređene uloge ARIA kako bi mogle pravilno izvršavati namijenjene funkcije pristupačnosti. [Saznajte više](https://web.dev/aria-required-parent/)."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Potrebni nadređeni element ne sadrži `[role]`"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "Potrebni nadređeni element sadr<PERSON>i `[role]`."}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "Uloge ARIA moraju sadržavati valjane vrijednosti da bi se mogle izvršavati njihove namijenjene funkcije pristupačnosti. [Saznajte više](https://web.dev/aria-roles/)."}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> `[role]` nisu val<PERSON>e"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> `[role]` su valjane"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>olo<PERSON>, poput <PERSON>, ne mogu tumačiti atribute ARIA s nevaže<PERSON><PERSON> vrijednostima. [Saznajte više](https://web.dev/aria-valid-attr-value/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Atributi `[aria-*]` ne sadrže valjane vrijednosti"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Atributi `[aria-*]` sad<PERSON><PERSON><PERSON> valjane vrijednosti"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>olo<PERSON>, poput č<PERSON>, ne mogu tumačiti atribute ARIA s nevažećim nazivima. [Saznajte više](https://web.dev/aria-valid-attr/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Atributi `[aria-*]` nisu valjani ili su pogrešno napisani"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "Atributi `[aria-*]` su valjani i nisu pogreš<PERSON> napisani"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "Titlovi omogućuju gluhim i nagluhim korisnicima uporabu audioelemenata jer pružaju ključne informacije, primjerice tko govori, što govori i druge informacije osim govora. [Saznajte više](https://web.dev/audio-caption/)."}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "Elementima `<audio>` nedostaje element `<track>` s `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "Elementi `<audio>` sadržavaju element `<track>` s `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Elementi koji nisu pro<PERSON> provjeru"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "Kada gumb nema pristupa<PERSON> naziv, <PERSON><PERSON><PERSON><PERSON> zaslona najavljuju ga kao \"gumb\" te je on neupotrebljiv za korisnike koji se oslanjaju na čitače zaslona. [Saznajte više](https://web.dev/button-name/)."}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "G<PERSON><PERSON> nemaju pristupačan naziv"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "<PERSON><PERSON><PERSON> imaju prist<PERSON> naziv"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "Dodavanje načina za zaobilaženje repetitivnog sadržaja omogućuje korisnicima tipkovnice učinkovitije kretanje po stranici. [Saznajte više](https://web.dev/bypass/)."}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "Stranica ne sadrži <PERSON>lov, vezu za preskakanje ili regiju kao orijentir"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "Stranica <PERSON>ž<PERSON>, vezu za preskakanje ili regiju kao orijentir"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "Mnogim je korisnicima teško ili nemoguće čitati tekst niskog kontrasta. [Saznajte više](https://web.dev/color-contrast/)."}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Boje u pozadini i prednjem planu nemaju zadovoljavajući omjer kontrasta."}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "Boje u pozadini i prednjem planu imaju zadovoljavajući omjer kontrasta"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "Kada popisi definicija nisu valjan<PERSON>, č<PERSON><PERSON><PERSON> z<PERSON>lona mogu proizvesti zbunjujuć ili netočan izlaz. [Saznajte više](https://web.dev/definition-list/)."}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "Elementi `<dl>` ne sadrže samo pravilno naručene grupe `<dt>` i `<dd>`, elemente `<script>` ili `<template>`."}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` sad<PERSON><PERSON>i samo pravilno naručene grupe `<dt>` i `<dd>` `<script>` ili elemente `<template>`."}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "Stavke na popisu definicija (`<dt>` i `<dd>`) moraju biti sadržane u nadređenom elementu `<dl>` da bi ih čitači zaslona mogli pravilno najaviti. [Saznajte više](https://web.dev/dlitem/)."}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "Stavke na popisu definicija nisu upakirane u elemente `<dl>`"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "Stavke na popisu definicija upakirane su u elemente `<dl>`"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "Naslov korisnicima čitača zaslona pruža pregled stranice, a korisnici tražilice značajno se na njega oslanjaju kako bi odredili je li neka stranica relevantna za njihovo pretraživanje. [Saznajte više](https://web.dev/document-title/)."}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "Dokument ne sadrži element `<title>`"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "Dokument sadrži element `<title>`"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "Vrijednost atributa ID mora biti jedinstvena kako bi se spriječilo da pomoćne tehnologije propuste druge instance. [Saznajte više](https://web.dev/duplicate-id/)."}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "Atributi `[id]` na stranici nisu jedinstveni."}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "Atributi `[id]` na stranici su jedinstveni"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "Korisnici čitača zaslona oslanjaju se na naslove okvira za opisivanje sadržaja okvira. [Saznajte više](https://web.dev/frame-title/)."}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "Elementi `<frame>` ili `<iframe>` nema<PERSON> naslov"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "Elementi `<frame>` ili `<iframe>` imaju naslov"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "Ako za stranicu nije naveden atribut jezika, čita<PERSON> zaslona pretpostavlja da je stranica na zadanom jeziku koji je korisnik odabrao prilikom postavljanja čitača zaslona. Ako stranica nije stvarno na zadanom jeziku, čitač zaslona možda neće ispravno najaviti tekst sa stranice. [Saznajte više](https://web.dev/html-has-lang/)."}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Element `<html>` nema atribut `[lang]`"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "Element `<html>` ima atribut `[lang]`"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "Na<PERSON>đ<PERSON><PERSON> valjanog [BCP 47 jezika](https://www.w3.org/International/questions/qa-choosing-language-tags#question) pomaže čitačima zaslona u pravilnom najavljivanju teksta. [Saznajte više](https://web.dev/html-lang-valid/)."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Element `<html>` ne sadrži valjanu vrijednost za atribut `[lang]`."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "Element `<html>` ima valjanu vrijednost za svoj atribut `[lang]`"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "Informativni elementi trebali bi sadržavati kratak, opisni zamjenski tekst. Ukrasni elementi mogu se zanemariti praznim atributom alt. [Saznajte više](https://web.dev/image-alt/)."}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "Elementi slike nemaju atribute `[alt]`"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "Elementi slike imaju `[alt]` atribute"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "Kada se slika upotrebljava kao gumb `<input>`, navođenje zamjenskog teksta može pomoći korisnicima čitača zaslona da shvate svrhu gumba. [Saznajte više](https://web.dev/input-image-alt/)."}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Elementi `<input type=\"image\">` nemaju tekst`[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "Elementi `<input type=\"image\">` sadr<PERSON><PERSON> `[alt]` tekst"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "Oznake osiguravaju da pomoćne tehnologije, poput č<PERSON>, pravilno najavljuju kontrole oblika. [Saznajte više](https://web.dev/label/)."}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "Elementi oblika ne sadrže povezane oznake"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "Elementi oblika imaju povezane oznake"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "Tablica koja se upotrebljava u svrhe izgleda ne smije sadržavati elemente podataka kao što su elementi th ili opis ili atribut sažetka jer to može zbuniti korisnike čitača zaslona. [Saznajte više](https://web.dev/layout-table/)."}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "Prezentacijski elementi za `<table>` ne izbjegavaju upotrebu atributa `<th>`, `<caption>` ili `[summary]`."}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "Prezentacijski elementi za `<table>` izbjegavaju upotrebu `<th>`, `<caption>` ili atributa `[summary]`."}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "Tekst veze (i zamjenski tekst za slike kada se upotrebljava kao veze) koji je prepoznatljiv, jedinstven i može se fokusirati omogućuje lakše kretanje korisnicima čitača zaslona. [Saznajte više](https://web.dev/link-name/)."}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "Veze nemaju naziv koji je moguće raspoznati"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "Veze imaju naziv koji je moguće raspoznati"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "Čitači zaslona imaju poseban način najavljivanja popisa. Osiguravanje pravilne strukture popisa pomaže izlazu čitača zaslona. [Saznajte više](https://web.dev/list/)."}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "Popisi ne sadrže samo elemente `<li>` i elemente koji podupiru skriptu (`<script>` i`<template>`)."}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "Popisi sadrže samo elemente `<li>` i elemente koji podupiru skriptu (`<script>` i`<template>`)."}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "Za čitače zaslona stavke na popisu (`<li>`) moraju biti sadržane unutar nadređenog `<ul>` ili `<ol>` kako bi ih se moglo pravilno najaviti. [Saznajte više](https://web.dev/listitem/)."}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> (`<li>`) nisu sadržane unutar nadređenih elemenata `<ul>` ili `<ol>`."}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "<PERSON><PERSON><PERSON> popisa (`<li>`) sadržane su unutar nadređenih elemenata `<ul>` ili `<ol>`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "Korisnici ne očekuju automatsko osvježavanje stranice, pa se fokus vraća na vrh stranice. To može biti frustrirajuće i zbuniti korisnike. [Saznajte više](https://web.dev/meta-refresh/)."}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Dokument upotrebljava `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "Dokument ne upotrebljava `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "Onemogućavanje zumiranja problematično je za slabovidne korisnike koji se oslanjaju na povećavanje zaslona kako bi pravilno vidjeli sadržaj web-stranice. [Saznajte više](https://web.dev/meta-viewport/)."}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` se upotrebljava u elementu `<meta name=\"viewport\">` ili je atribut `[maximum-scale]` manji od pet."}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` se ne upotrebljava u elementu `<meta name=\"viewport\">` i atribut `[maximum-scale]` nije manji od pet."}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "Čitači zaslona ne mogu prevesti sadržaj osim teksta. Dodavanje zamjenskog teksta elementima `<object>` pomaže čitačima zaslona u prenošenju značenja korisnicima. [Saznajte više](https://web.dev/object-alt/)."}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "Elementi `<object>` nemaju tekst`[alt]`"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "Elementi `<object>` sad<PERSON><PERSON><PERSON> `[alt]` tekst"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "Vrijednost viša od 0 podrazumijeva eksplicitno naređivanje kretanja. Iako je tehnički valjano, to često frustrira korisnike koji se oslanjaju na pomoćne tehnologije. [Saznajte više](https://web.dev/tabindex/)."}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "Neki elementi imaju vrijednost `[tabindex]` višu od 0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "Nijedan element nema vrijednost `[tabindex]` veću od 0"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "Čitači zaslona sadrže značajke za olakšavanje kretanja po tablicama. Potrebno je pripaziti da se ćelije `<td>` koje upotrebljavaju atribut `[headers]` odnose samo na druge ćelije u istoj tablici kako bi se korisnicima čitača zaslona omogućio bolji doživljaj. [Saznajte više](https://web.dev/td-headers-attr/)."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Ćelije u elementu `<table>` koje upotrebljavaju atribut `[headers]` odnose se na element `id` koji nije pronađen unutar iste tablice."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "Ćelije u elementu `<table>` koje upotrebljavaju atribut `[headers]` odnose se na ćelije tablice unutar iste tablice."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "Čitači zaslona sadrže značajke za olakšavanje kretanja po tablicama. Osiguravanje da se zaglavlja tablice uvijek odnose na neki skup ćelija može poboljšati doživljaj za korisnike čitača zaslona. [Saznajte više](https://web.dev/th-has-data-cells/)."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Elementi `<th>` i elementi s`[role=\"columnheader\"/\"rowheader\"]` nemaju podatkovne ćelije koje oni opisuju."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "Elementi `<th>` i elementi s `[role=\"columnheader\"/\"rowheader\"]` imaju podatkovne <PERSON>elije koje opisuju."}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "Na<PERSON><PERSON><PERSON><PERSON> valjanog [BCP 47 jezika](https://www.w3.org/International/questions/qa-choosing-language-tags#question) u elementima pomaže osigurati da čitač zaslona ispravno izgovara tekst. [Saznajte više](https://web.dev/valid-lang/)."}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Atributi `[lang]` ne sadrže valjanu vrijednost"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "Atributi `[lang]` imaju valjanu vrijednost"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "Kada videozapis sadr<PERSON>i titlove, glu<PERSON> i nagluhim osobama jednostavnije je pristupiti njegovim informacijama. [Saznajte više](https://web.dev/video-caption/)."}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "Elementi `<video>` ne sadrže element `<track>` s `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "Elementi `<video>` sadržavaju element `<track>` s `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "Audioopisi pružaju relevantne informacije za videozapise koje dijaloški okviri ne mogu pružiti, primjerice izraze lica i scene. [Saznajte više](https://web.dev/video-description/)."}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "Elementi `<video>` ne sadrže element `<track>` s `[kind=\"description\"]`."}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "Elementi `<video>` sadržavaju element `<track>` s `[kind=\"description\"]`"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "Za idealan izgled na iOS-u kad korisnici na početni zaslon dodaju progresivnu web-aplikaciju definirajte `apple-touch-icon`. Mora ukazivati na neprozirni kvadratni PNG od 192 px (ili 180 px). [Saznaj<PERSON> više](https://web.dev/apple-touch-icon/)."}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "<PERSON><PERSON> pruža valjani `apple-touch-icon`"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "`apple-touch-icon-precomposed` je z<PERSON><PERSON>; prednost ima `apple-touch-icon`."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "<PERSON><PERSON> val<PERSON>u ikonu `apple-touch-icon`"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chromeova proširenja negativno su utjecala na izvedbu učitavanja ove stranice. Pokušajte pregledati stranicu anonimno ili putem Chromeovog profila bez proširenja."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "Raščlamba skripte"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "Ukupno vrijeme CPU-a"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "Savjetujemo vam da skratite vrijeme potrebno za raščlambu, kompiliranje i izvršavanje JS-a. Isporuka manjih JS-ova mogla bi vam pomoći da to postignete. [Saznajte više](https://web.dev/bootup-time)."}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "Skratite vrijeme izvršavanja JavaScripta"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "Vrijeme izvršavanja JavaScripta"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Veliki GIF-ovi nisu učinkoviti za prikaz animiranog sadržaja. Savjetujemo vam da umjesto GIF-a upotrebljavate MPEG4/WebM videozapise za animacije i PNG/WebP za statične slike da biste smanjili količinu mrežnih bajtova. [Saznajte više](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Upotrebljavajte formate videozapisa za animirani sadržaj"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Za slike izvan zaslona i skrivene slike savjetujemo odgođeno učitavanje nakon što se učitaju svi kritični resursi da biste skratili vrijeme do interaktivnosti. [Saznajte više](https://web.dev/offscreen-images)."}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Odgodite slike izvan z<PERSON>a"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Resursi blokiraju prvo renderiranje vaše stranice. Savjetujemo vam da kljulan JS/CSS isporučite u tekstu te da da odgodite sve JS-ove/stilove koji nisu ključni. [Saznajte više](https://web.dev/render-blocking-resources)."}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Uklonite resurse koji blokiraju generiranje"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Veliki mrežni resursi korisnicima uzrokuju stvarne novčane troškove i usko koreliraju s dugim vremenom učitavanja. [Saznajte više](https://web.dev/total-byte-weight)."}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Ukupna veličina bila je {totalBytes, number, bytes} KB"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Izbjegavajte ogromne mrežne resurse"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Izbjegava ogromne mrežne resurse"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "Umanjenjem CSS datoteka mogu se smanjiti veličine mrežnih resursa. [Saznajte više](https://web.dev/unminified-css)."}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "Umanjite CSS"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Umanjenjem JavaScript datoteka mogu se smanjiti veličine resursa i skratiti vrijeme raščlambe skripte. [Saznajte više](https://web.dev/unminified-javascript)."}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Umanjite JavaScript"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Uklonite nepotrebna pravila iz stilskih tablica i odgodite učitavanje CSS-a koji se ne koristi za sadržaj na vidljivom dijelu stranice kako biste smanjili nepotrebnu potrošnju bajtova u mrežnoj aktivnosti. [Saznajte više](https://web.dev/unused-css-rules)."}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Uklonite CSS koji se ne koristi"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Uklonite JavaScript koji se ne koristi da biste smanjili potrošnju bajtova u mrežnoj aktivnosti."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Uklonite nekorišteni JavaScript"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Dugotrajno predmemoriranje može ubrzati ponovljene posjete vašoj stranici. [Saznajte više](https://web.dev/uses-long-cache-ttl)."}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Pronađen je jedan resurs}one{Pronađen je # resurs}few{Pronađena su # resursa}other{<PERSON>nađeno je # resursa}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Poslužite statične elemente s pravilima učinkovitog predmemoriranja"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Upotrebljava pravila učinkovitog predmemoriranja za statične elemente"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Optimizirane slike učitavaju se brže i troše manje mobilnih podataka. [Saznajte više](https://web.dev/uses-optimized-images)."}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Kodirajte slike učinkovito"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Poslužite slike prikladnih veličina da biste uštedjeli mobilne podatke i poboljšali vrijeme učitavanja. [Saznajte više](https://web.dev/uses-responsive-images)."}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Postavite slike u odgovarajućoj veličini"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Tekstualni resursi trebaju se posluživati s kompresijom (gzip, deflate ili brotli) radi minimiziranja ukupne količine mrežnih bajtova. [Saznajte više](https://web.dev/uses-text-compression)."}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Omogućite sažimanje teksta"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "Formati slike kao što su JPEG 2000, JPEG XR i WebP često pružaju bolje sažimanje nego PNG ili JPEG, što znači brža preuzimanja i manju potrošnju podataka. [Saznajte više](https://web.dev/uses-webp-images)."}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "Poslužite slike u modernim formatima"}, "lighthouse-core/audits/content-width.js | description": {"message": "Ako se širina sadržaja vaše aplikacije ne podudara s vidljivim dijelom, vaša aplikacija možda neće biti optimizirana za mobilne zaslone. [Saznajte više](https://web.dev/content-width)."}, "lighthouse-core/audits/content-width.js | explanation": {"message": "Veličina vidljivog dijela od {innerWidth} px ne podudara se s veličinom prozora od {outerWidth} px."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "Sad<PERSON><PERSON><PERSON> nije ispravne veličine za vidljivi dio."}, "lighthouse-core/audits/content-width.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> je ispravne veličine za vidljivi dio"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "Lanci kritičkih zahtjeva u nastavku prikazuju koji se resursi učitavaju s visokim prioritetom. Savjetujemo vam da skratite duljinu lanaca, smanjite veličinu resursa za preuzimanje ili odgodite preuzimanje resursa koji nisu nužni kako biste poboljšali učitavanje stranice. [Saznajte više](https://web.dev/critical-request-chains)."}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{<PERSON>nađen je jedan lanac}one{Pronađen je # lanac}few{<PERSON>nađena su # lanca}other{<PERSON>nađeno je # lanaca}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "Minimizirajte dubinu kritičnih zahtjeva"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "Ukidanje / upozorenje"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/deprecations.js | description": {"message": "Ukinuti API-ji na kraju će se ukloniti iz preglednika. [Saznajte više](https://web.dev/deprecations)."}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{<PERSON><PERSON><PERSON><PERSON> je jedno upozorenje}one{Broj pronađenih upozorenja: #}few{Broj pronađenih upozorenja: #}other{Broj pronađenih upozorenja: #}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "Upotrebljava ukinute API-je"}, "lighthouse-core/audits/deprecations.js | title": {"message": "Izbjegava ukinute API-je"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "Ukinuta je predmemorija aplikacije. [Saznajte više](https://web.dev/appcache-manifest)."}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "„{AppCacheManifest}” je pronađen"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "Upotrebljava predmemoriju aplikacije"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "Izbjegava predmemoriju aplikacije"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "Specificiranje vrste dokumenta (doctype) sprječava preglednik da prijeđe u način rada sa starijim značajkama. [Saznajte više](https://web.dev/doctype)."}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Naziv vrste dokumenta (doctype) mora biti niz napisan malim slovima `html`"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "URL mora sadržavati vrstu dokumenta."}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Očekivani javni ID bit će prazan niz"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Očekivani ID sustava bit će prazan niz"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Stranici nedostaje vrsta dokumenta HTML, stoga pokreće način rada sa starijim značajkama"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "Stranica ima HTML vrstu dokumenta"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "Element"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistika"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Vrijednost"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "Inženjeri za preglednike preporučuju da stranice sadrže manje od ~1500 DOM elemenata. Pravo mjesto jest drvo dubine < 32 elemenata te koje ima manje od 60 podređenih/nadređenih elemenata. Veliki DOM može povećati upotrebu memorije, uzrokovati duže [izračune stila](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) te dovesti do skupih [preoblikovanja izgleda](https://developers.google.com/speed/articles/reflow). [Saznajte više](https://web.dev/dom-size)."}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{Jedan element}one{# element}few{# elementa}other{# elemenata}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Izbjegavajte pretjeranu veličinu DOM-a"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Maksimalna dubina DOM-a"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Ukupan broj elemenata DOM-a"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Maksimalni broj podređenih elemenata"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "Izbjegava pretjeranu veličinu DOM-a"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "Dodajte `rel=\"noopener\"` ili `rel=\"noreferrer\"` bilo kojoj eksternoj vezi da biste unaprijedili uspješnost i spriječili sigurnosne propuste. [Saznajte više](https://web.dev/external-anchors-use-rel-noopener)."}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "Veze na odredišta različitih polazišta nisu sigurne"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "Veze na odredišta različitih polazišta sigurne su"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "<PERSON><PERSON> mogu<PERSON>e odrediti odredište sidrenja za ({anchorHTML}). Ako se ne upotrebljava kao hip<PERSON>, razmislite o tome da uklonite target=_blank."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Korisnici ne vjeruju web-lokacijama koje žele znati njihovu lokaciju bez konteksta ili ih takve web-lokacije zbunjuju. Razmislite o tome da umjesto toga zahtjev povežete s radnjama korisnika. [Saznajte više](https://web.dev/geolocation-on-start)."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Zahtijeva dopuštenje za geolociranje pri učitavanju stranice"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Izbjegava traženje dopuštenja za geolociranje pri učitavanju stranice"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Verzija"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "Sve pristupne JavaScript biblioteke otkrivene na stranici. [Saznajte više](https://web.dev/js-libraries)."}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "Pronađene JavaScript biblioteke"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "<PERSON><PERSON> korisnici imaju spore veze, vanjske skripte koje se dinamički ubacuju pomoću `document.write()` mogu odgoditi učitavanje stranice za desetke sekundi. [Saznajte više](https://web.dev/no-document-write)."}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Upotrebljava `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "Izbjegava `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "Najviši stupanj ozbiljnosti"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "Verzija biblioteke"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "Neke skripte treće strane mogu sadržavati poznate sigurnosne propuste te ih napadači mogu lako identificirati i iskoristiti. [Saznajte više](https://web.dev/no-vulnerable-libraries)."}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{Otkriven je jedan sigurnosni propust}one{Broj otkrivenih sigurnosnih propusta: #}few{Broj otkrivenih sigurnosnih propusta: #}other{Broj otkrivenih sigurnosnih propusta: #}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "Uključuje pristupne JavaScript biblioteke s poznatim sigurnosnim propustima"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "Visok"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "Srednje"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "Izbjegava pristupne JavaScript biblioteke s poznatim sigurnosnim propustima"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "Korisnici ne vjeruju web-lokacijama koje traže slanje obavijesti bez konteksta ili ih takve web-lokacije zbunjuju. Razmislite o tome da umjesto toga zahtjev povežete s pokretima korisnika. [Saznajte više](https://web.dev/notification-on-start)."}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Zahtijeva dopuštenje za obavještavanje pri učitavanju stranice"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "Izbjegava traženje dopuštenja za obavještavanje pri učitavanju stranice"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "Elementi koji nisu pro<PERSON> provjeru"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "Sprječavanje lijepljenja zaporke narušava kvalitetu dobrih sigurnosnih pravila. [Saznajte više](https://web.dev/password-inputs-can-be-pasted-into)."}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "Onemogućuje korisnicima lijepljenje u polja za zaporku"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "Omogućuje korisnicima lijepljenje u polja za zaporku"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokol"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 ima brojne prednosti u odnosu na HTTP/1.1, uključujući binarna zaglavlja, multipleksiranja i oglašavanja poslužitelja. [Saznajte više](https://web.dev/uses-http2)."}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{<PERSON><PERSON> zahtjev koji nije poslužen protokolom HTTP/2}one{Broj zahtjeva koji nisu posluženi protokolom HTTP/2: #}few{<PERSON><PERSON>j zahtjeva koji nisu posluženi protokolom HTTP/2: #}other{Broj zahtjeva koji nisu posluženi protokolom HTTP/2: #}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "Ne upotrebljava HTTP/2 za sve svoje resurse"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "Upotrebljava HTTP/2 za svoje resurse"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Razmislite o tome da svoje pasivne slušatelje događaja označite kao `passive` da biste poboljšali rezultate pretraživanja. [Saznajte više](https://web.dev/uses-passive-event-listeners)."}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Ne upotrebljava pasivne osluškivače za unaprjeđenje rezultata pretraživanja"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Upotrebljava pasivne osluškivače za unaprjeđenje rezultata pretraživanja"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "Opis"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "Pogreške zabilježene u konzoli ukazuju na neriješene probleme. Rezultat su neuspjelih mrežnih zahtjeva i ostalih pitanja povezanih s preglednikom. [Saznajte više](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "Pogreške preglednika zabilježene su u konzoli"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "Na konzoli nema zabilježenih pogrešaka preglednika"}, "lighthouse-core/audits/font-display.js | description": {"message": "Iskoristite CSS značajku za prikaz fontova kako bi tekst bio vidljiv korisnicima dok se web-fontovi učitavaju. [Saznajte više](https://web.dev/font-display)."}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "Neka tekst ostaje vidljiv tijekom učitavanja web-fontova"}, "lighthouse-core/audits/font-display.js | title": {"message": "Sav tekst ostaje vidljiv tijekom učitavanja web-fontova"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "Lighthouse nije mogao automatski provjeriti vrijednost prikazanog fonta za sljedeći URL: {fontURL}."}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "<PERSON><PERSON><PERSON> (stvarni)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "<PERSON><PERSON><PERSON> (prikazane)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "Dimenzije prikaza slike trebale bi odgovarati prirodnom omjeru slike. [Saznajte više](https://web.dev/image-aspect-ratio)."}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "Prikazuje slike netočnog omjera"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "Prikazuje slike točnog omjera"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "Nevažeće informacije o veličini slike {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "Preglednici mogu proaktivno zatražiti od korisnika da dodaju vašu aplikaciju na početni zaslon, što može dovesti do veće angažiranosti. [Saznajte više](https://web.dev/installable-manifest)."}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "Manifest web-aplikacije ne udovoljava zahtjevima za instalaciju"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "Manifest web-aplikacije udovoljava zahtjevima za instalaciju"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "Nesiguran URL"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "Sve web-lokacije trebale bi biti zaštićene HTTPS-om, čak i one koje ne obrađuju osjetljive podatke. HTTPS sprječava uljeze u neovlaštenom pristupu komunikacijama između vaše aplikacije i vaših korisnika te im onemogućuje pasivno slušanje tih komunikacija. Preduvjet je za HTTP/2 i API-je brojnih novih web-platformi. [Saznajte više](https://web.dev/is-on-https)."}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{<PERSON><PERSON><PERSON><PERSON> je jedan zahtjev koji nije siguran}one{Broj pronađenih zahtjeva koji nisu sigurni: #}few{Broj pronađenih zahtjeva koji nisu sigurni: #}other{Broj pronađenih zahtjeva koji nisu sigurni: #}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "Ne upotrebljava HTTPS"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "Upotrebljava HTTPS"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "Brzo učitavanje stranice putem mobilne mreže omogućuje dobar doživljaj korisnicima na mobilnim uređajima. [Saznajte više](https://web.dev/load-fast-enough-for-pwa)."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "Interaktivno u {timeInMs, number, seconds} s"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "Interaktivno na simuliranoj mobilnoj mreži za {timeInMs, number, seconds} s"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "Vaša stranica presporo se učitava i nije interaktivna unutar deset sekundi. U odjeljku \"Izvedba\" pogledajte prilike i dijagnostiku da biste saznali kako je poboljšati."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "Učitavanje stranice nije dovoljno brzo na mobilnim mrežama"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "Učitavanje stranice dovoljno je brzo na mobilnim mrežama"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Kategorija"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "Savjetujemo vam da skratite vrijeme potrebno za raščlambu, kompiliranje i izvršavanje JS-a. Isporuka manjih JS-ova mogla bi vam pomoći da to postignete. [Saznajte više](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimizirajte rad glavne niti"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "Minimizira rad glavne niti"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "Da bi dosegle najveći broj koris<PERSON>, web-lokacije bi trebale funkcionirati u svakom značajnijem pregledniku. [Saznajte više](https://web.dev/pwa-cross-browser)."}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "Web-lokacija funkcionira na različitim preglednicima"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Pobrinite se da su sve individualne stranice dubinski povezane putem URL-a i da su URL-ovi jedinstveni radi dijeljenja na društvenim medijima. [Saznajte više](https://web.dev/pwa-each-page-has-url)."}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Svaka stranica ima URL"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "Pri dodirivanju stavki prijelazi bi trebali biti brzi, čak i na sporoj mreži. To je ključno za korisnikovu percepciju izvedbe. [Saznajte više](https://web.dev/pwa-page-transitions)."}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "Ne čini se da se prijelazi stranica blokiraju na mreži"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "Procijenjena latencija unosa procjena je vremena koje je potrebno da vaša aplikacija reagira na korisnički unos, u milisekundama, tijekom najintenzivnijih pet sekundi učitavanja stranice. Ako je latencija viša od 50 ms, korisnici mogu doživjeti vašu aplikaciju kao usporenu. [Saznajte više](https://web.dev/estimated-input-latency)."}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "Procijenjena latenc<PERSON>"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "Prvo renderiranje sadržaja označava vrijeme renderiranja prvog teksta ili slike. [Saznajte više](https://web.dev/first-contentful-paint)."}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "Prvi procesor u mirovanju označava prvi trenutak u kojem je glavna nit stranice dovoljno neopterećena da bi obradila unos.  [Saznajte više](https://web.dev/first-cpu-idle)."}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "Prvi procesor u mirovanju"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "Prvo korisno renderiranje mjeri kada je vidljiv primarni sadržaj stranice. [Saznajte više](https://web.dev/first-meaningful-paint)."}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "<PERSON>r<PERSON> s<PERSON>o bo<PERSON>"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "Vrijeme do interaktivnosti količina je vremena koje je potrebno da stranica postane potpuno interaktivna. [Saznajte više](https://web.dev/interactive)."}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "Vrijeme do interaktivnosti"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "Maksimalno potencijalno prvo kašnjenje unosa koje bi korisnik mogao doživjeti trajanje je, u milisekundama, najduljeg zadatka. [Saznajte više](https://developers.google.com/web/updates/2018/05/first-input-delay)."}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "Maks. potencijalno kašnjenje odgovora na prvi unos"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "Indeks brzine prikazuje koliko se brzo sadržaj stranice vidljivo popunjava. [Saznajte više](https://web.dev/speed-index)."}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "<PERSON><PERSON><PERSON> br<PERSON>"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "Zbroj svih razdoblja između PRS-a i vremena do interaktivnosti kada trajanje zadatka prelazi 50 ms, iskazano u milisekundama."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "Ukupno vrijeme blokiranja"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "Vrijeme od slanja upita do primanja odgovora (RTT) za mrežu ima velik utjecaj na izvedbu. Ako je RTT do polazišta visok, to je znak da bi poslužitelji bliže korisniku mogli poboljšati izvedbu. [Saznajte više](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "lighthouse-core/audits/network-rtt.js | title": {"message": "Vrijeme od slanja upita do primanja odgovora za mrežu"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "Latencije poslužitelja mogu utjecati na izvedbu na webu. Ako je latencija poslužitelja polazišta visoka, to je znak da je poslužitelj preopterećen ili da ima lošu pozadinsku izvedbu. [Saznajte više](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "Pozadinske latencije poslužitelja"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "Uslužni alat omogućava web-aplikaciji da bude pouzdana u nepredvidivim mrežnim uvjetima. [Saznajte više](https://web.dev/offline-start-url)."}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "Za `start_url` ne prikazuje se kôd 200 kad je offline"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "Za `start_url` prika<PERSON><PERSON> se kôd 200 kad je offline"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "Lighthouse nije mogao pročitati `start_url` iz manifesta. Zbog toga se pretpostavilo da je `start_url` URL dokumenta. Poruka pogreš<PERSON>: \"{manifestWarning}\"."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "Više od proračuna"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "Zahtjeve za količinom i veličinom mreže održavajte ispod ciljeva utvrđenih danim proračunom za izvršavanje. [Saznajte više](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{<PERSON><PERSON>}one{<PERSON><PERSON><PERSON> zahtje<PERSON>: #}few{<PERSON><PERSON><PERSON> zahtjeva: #}other{<PERSON><PERSON><PERSON> zahtjeva: #}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "Proračun za izvedbu"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "<PERSON><PERSON> ste već postavili HTTPS, provjerite preusmjeravate li sav HTTP promet na HTTPS kako biste omogućili sigurne web-značajke za sve korisnike. [Saznajte više](https://web.dev/redirects-http)."}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "Ne preusmjerava HTTP promet na HTTPS"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "Preusmjerava HTTP promet na HTTPS"}, "lighthouse-core/audits/redirects.js | description": {"message": "Preusmjeravanja uvode dodatna kašnjenja prije nego što se stranica može učitati. [Saznajte više](https://web.dev/redirects)."}, "lighthouse-core/audits/redirects.js | title": {"message": "Izbjegavajte višestruka preusmjeravanja stranica"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "Da biste postavili proračune za količinu i veličinu resursa stranice, dodajte datoteku budget.json. [Saznajte više](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{<PERSON><PERSON> • {byteCount, number, bytes} KB}one{# zahtjev • {byteCount, number, bytes} KB}few{# zahtjeva • {byteCount, number, bytes} KB}other{# zahtjeva • {byteCount, number, bytes} KB}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "<PERSON><PERSON> z<PERSON><PERSON><PERSON><PERSON> budu <PERSON>, a veličine prijenosa male"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "Kanonske veze ukazuju na to koji URL prikazati u rezultatima pretraživanja. [Saznajte više](https://web.dev/canonical)."}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "Više URL-ova u sukobu ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "Usmjerava na drugu domenu ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "Nevažeći URL ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Usmjerava na drugu `hreflang` lokaciju ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "Relativni URL ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "Upućuje na korijenski URL domene (početnu stranicu), a ne na ekvivalentnu stranicu sadržaja"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "Dokument ne sadrži valjanu vezu `rel=canonical`"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "Dokument ima valjani `rel=canonical`"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "Fontovi manji od 12 px premali su da bi bili čitljivi, pa posjetitelji na mobilnim uređajima moraju zumirati prstima. Neka više od 60% teksta na stranici ima veličinu od najmanje 12px. [Saznajte više](https://web.dev/font-size)."}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} čitljivog teksta"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "Tekst nije čitljiv jer nijedna metaoznaka vidljivog dijela nije optimizirana za zaslone mobilnih uređaja."}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "{decimalProportion, number, extendedPercent} teksta nije dovoljno veliko (na temelju uzorka od {decimalProportionVisited, number, extendedPercent})."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "Dokument ne sadrži čitljive veličine fontova"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "Dokument upotrebljava čitljive veličine fontova"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "Veze s atributom hreflang govore tražilicama koju verziju stranice trebaju navesti u rezultatima pretraživanja za određeni jezik ili regiju. [Saznajte više](https://web.dev/hreflang)."}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "Dokument ne sadrži važe<PERSON>i `hreflang`"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "Dokument ima valjani `hreflang`"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "Stranice s neuspješnim HTTP kodom statusa možda se neće pravilno indeksirati. [Saznajte više](https://web.dev/http-status-code)."}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "Stranica ima neuspješan HTTP kôd statusa"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "Stranica ima uspješan HTTP kôd statusa"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "Tražilice ne mogu uključiti vaše stranice u rezultate pretraživanja ako nemaju dopuštenje da ih pretraže i indeksiraju. [Saznajte više](https://web.dev/is-crawable)."}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "Indeksiranje je stranice blokirano"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "Indeksiranje stranice nije blokirano"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "Opisni tekst veze pomaže tražilicama da razumiju vaš sadrž<PERSON>. [Saznajte više](https://web.dev/link-text)."}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Pronađena je jedna veza}one{Pronađena je # veza}few{Pronađene su # veze}other{<PERSON>nađeno je # veza}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "Veze nemaju opisni tekst"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "<PERSON>eze sadr<PERSON><PERSON> deskriptivan tekst"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "Pokrenite [Alat za testiranje strukturiranih podataka](https://search.google.com/structured-data/testing-tool/) i [Datoteku za provjeru koda (linter) strukturiranih podataka](http://linter.structured-data.org/) da biste potvrdili strukturirane podatke. [Saznajte više](https://web.dev/structured-data)."}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "Strukturirani su podaci važeći"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "Metaopisi mogu se uključiti u rezultate pretraživanja kako bi se jezgrovito sažeo sadržaj stranice. [Saznajte više](https://web.dev/meta-description)."}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "Tekst opisa nema sadržaj."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "Dokument ne sadrži metaopis"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "Do<PERSON><PERSON> sadr<PERSON>"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "Traž<PERSON>ce ne mogu indeksirati sadr<PERSON> dodata<PERSON>, pa mnogi uređaji ograničavaju dodatke ili ih ne podržavaju. [Saznajte više](https://web.dev/plugins)."}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "Dokument upotrebljava dodatke"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "Dokument izbjegava dodatke"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "<PERSON>ko je vaša robots.txt da<PERSON><PERSON><PERSON>, alati za indeksiranje možda neće moći razumjeti kako želite da se vaša web-lokacija pretraži ili indeksira. [Saznajte više](https://web.dev/robots-txt)."}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Zahtjevi za robots.txt vraćaju HTTP status: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Pronađena je jedna pogreška}one{Pronađena je # pogreška}few{Pronađene su # pogreške}other{Pronađeno je # pogrešaka}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse nije uspio preuzeti robots.txt datoteku"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt nije valjan"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "robots.txt je valjan"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "Interaktivni elementi kao što su gumbi i veze trebali bi biti dovoljno veliki (48 x 48 px) i oko njih bi trebalo biti dovoljno prostora da ih se može lako dodirnuti bez preklapanja s drugim elementima. [Saznajte više](https://web.dev/tap-targets)."}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} ciljeva dodira odgovarajuć<PERSON> velič<PERSON>"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Ciljevi dodira nisu dovoljno veliki jer nijedna metaoznaka vidljivog dijela nije optimizirana za zaslone mobilnih uređaja"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "Ciljevi dodira nisu odgovaraj<PERSON>će veličine"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Preklapanje cilja"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "Ciljevi dodira odgovarajuće su veličine"}, "lighthouse-core/audits/service-worker.js | description": {"message": "Uslužni alat jest tehnologija koja aplikaciji omogućava korištenje brojnih značajki progresivne web-aplikacije, kao <PERSON>to je offline rad, dodavanje na početni zaslon i push obavijesti. [Saznajte više](https://web.dev/service-worker)."}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "Ovom stranicom upravlja uslužni alat, no nije pronađen `start_url` jer manifest nije raščlanjen kao važeći JSON"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "Ovom stranicom upravlja uslužni alat, no `start_url` ({startUrl}) nije u rasponu uslužnog alata ({scopeUrl})"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "Ovom stranicom upravlja uslužni alat, no nije pronađen `start_url` jer nije dohvaćen manifest."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "<PERSON>va izvor ima jedan ili više uslužnih alata, no stranica ({pageUrl}) nije u rasponu."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "Ne registrira uslužni alat koji kontrolira stranicu i `start_url`"}, "lighthouse-core/audits/service-worker.js | title": {"message": "Registrira uslužni alat koji upravlja stranicom i `start_url`"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "Tematski pozdravni zaslon pruža bolji doživljaj korisnicima koji pokreću vašu aplikaciju na početnom zaslonu. [Saznajte više](https://web.dev/splash-screen)."}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "<PERSON>je konfigurirano za prilagođeni pozdravni zaslon"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "Konfiguriran za prilagođeni pozdravni zaslon"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "Adresna traka preglednika može se tematski podudarati s vašom web-lokacijom. [Saznajte više](https://web.dev/themed-omnibox)."}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "Ne postavlja boju teme za adresnu traku."}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "Postavlja boju teme za adresnu traku."}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "Vrijeme blokiranja glavne niti"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "T<PERSON>ća strana"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "Kôd treće strane može znatno utjecati na uspješnost učitavanja. Ograničite broj suvišnih pružatelja treće strane i pokušajte učitati kôd treće strane nakon primarnog zavšetka učitavanja vaše stranice. [Saznajte više](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "<PERSON><PERSON><PERSON> treće strane blokirao je glavnu nit na {timeInMs, number, milliseconds} ms"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "Smanjite utjecaj koda trećih strana"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "Upotreba treće strane"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "Vrijeme do prvog bajta navodi vrijeme u koje poslužitelj šalje odgovor. [Saznajte više](https://web.dev/time-to-first-byte)."}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "Za korijenski dokument bilo je potrebno {timeInMs, number, milliseconds} ms"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "Skratite vremena odgovora poslužitelja (TTFB)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "Poslužitelj odgovara sporo (TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "Vrijeme početka"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "Vrsta"}, "lighthouse-core/audits/user-timings.js | description": {"message": "Savjetujemo vam da na aplikaciju primijenite API za praćenje korisničkog vremena radi mjerenja izvedbe aplikacije u stvarnom vremenu tijekom važnih korisničkih doživljaja. [Saznajte više](https://web.dev/user-timings)."}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{Jedno praćenje korisničkog vremena}one{# praćenje korisničkog vremena}few{# praćenja korisničkog vremena}other{# praćenja korisničkog vremena}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "Oznake i izmjere Praćenja korisničkog vremena"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "<PERSON><PERSON><PERSON><PERSON> je <link> za pret<PERSON>vez<PERSON><PERSON> za „{securityOrigin}”, ali ga preglednik nije upotrijebio. Provjerite upotrebljavate li atribut `crossorigin` pravilno."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "Sav<PERSON><PERSON><PERSON><PERSON> vam da dodate `preconnect` ili `dns-prefetch` prilagodbe resursa radi uspostavljanja ranih veza s važnim izvorima trećih strana. [Saznajte više](https://web.dev/uses-rel-preconnect)."}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "Rano se povežite s potrebnim izvorima"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "<PERSON>a „{preloadURL}” prona<PERSON><PERSON> je <link> za preduč<PERSON>, no preglednik ga nije upotrijebio. Provjerite upotrebljavate li atribut `crossorigin` pravilno."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "Savjetuje<PERSON> vam da koristite `<link rel=preload>` da biste dali prednost dohvaćanju resursa koji se trenutačno traže kasnije u učitavanju stranice. [Saznajte više](https://web.dev/uses-rel-preload)."}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "Unaprijed učitajte ključne zahtjeve"}, "lighthouse-core/audits/viewport.js | description": {"message": "Do<PERSON>jte ozna<PERSON> `<meta name=\"viewport\">` da biste optimizirali svoju aplikaciju za mobilne zaslone. [Saznajte više](https://web.dev/viewport)."}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "<PERSON><PERSON><PERSON>a oz<PERSON> `<meta name=\"viewport\">` nije pronađena"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "Nema oznaku `<meta name=\"viewport\">` s `width` ili `initial-scale`"}, "lighthouse-core/audits/viewport.js | title": {"message": "<PERSON><PERSON> `<meta name=\"viewport\">` s `width` ili `initial-scale`"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "Vaša aplikacija trebala bi prikazati bilo kakav sadržaj kada je onemogućen JavaScript, čak i ako je to samo upozorenje da je JavaScript obavezan za korištenje aplikacije. [Saznajte više](https://web.dev/without-javascript)."}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "T<PERSON><PERSON>lo stranice trebalo bi generirati bilo kakav sadržaj ako skripte nisu dostupne."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "Ne pruža zamjenski sadržaj kada JavaScript nije dostupan"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "Prikazuje se bilo kakav sadržaj kada JavaScript nije dostupan"}, "lighthouse-core/audits/works-offline.js | description": {"message": "Ako razvijate progresivnu web-aplikaciju, razmislite o korištenju uslužnog alata tako da aplikacija može funkcionirati offline. [Saznajte više](https://web.dev/works-offline)."}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "Za trenutačnu stranicu ne prikazuje se kôd 200 kad je offline"}, "lighthouse-core/audits/works-offline.js | title": {"message": "Za trenutačnu stranicu prikazuje se kôd 200 kad je offline"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "Stranica se možda ne učitava offline jer je vaš testni URL ({requested}) preusmjeren na \"{final}\". Pokušajte izravno testirati drugi URL."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "To su prilike za poboljšanje uporabe sustava ARIA u vašoj aplikaciji, što može unaprijediti doživljaj za korisnike pomoćne tehnologije, primjerice čitača zaslona."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Ovo su prilike za poboljšanje zamjenskog sadržaja za zvuk i videoprikaz. To može poboljšati doživljaj za korisnike s oštećenjem sluha ili vida."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Zvuk i videoprikaz"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Ove stavke ističu uobičajene najbolje primjere iz prakse za pristupačnost."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Najbolji primjeri iz prakse"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "Ove provjere ističu prilike za [poboljšanje pristupačnosti vaše web-aplikacije](https://developers.google.com/web/fundamentals/accessibility). Budući da se automatskom provjerom može otkriti samo dio poteškoća s pristupačnošću, savjetujemo i ručno testiranje."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "Područja adresa za te stavke koja alat za automatizirano testiranje ne može pokriti. Saznajte više u našem vodiču o [provođenju pregleda pristupačnosti](https://developers.google.com/web/fundamentals/accessibility/how-to-review)."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "Pristupačnost"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "To su mogućnosti za poboljšanje čitljivosti vašeg sadržaja."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "To su prilike da unaprijedite tumačenje svojeg sadržaja za korisnike na različitim jezicima."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internacionalizacija i lokalizacija"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "To su prilike za poboljšanje semantike kontrola u vašoj aplikaciji. Na taj način možete poboljšati doživljaj za korisnike pomoćne tehnologije, primjerice čitača zaslona."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Nazivi i oznake"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Ovo su prilike za poboljšanje kretanja tipkovnicom u vašoj aplikaciji."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Kretanje"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Ovo su prilike za poboljšanje doživljaja čitanja podataka u tablicama ili na popisima koristeći se pomoćnim tehnologijama, primjerice čitačem zaslona."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tablice i popisi"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Najbolji primjeri iz prakse"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "Proračuni za izvedbu postavljaju standarde izvedbe vaše stranice."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "Više informacija o izvedbi vaše aplikacije. Ovi brojevi ne [utječu izravno](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) na rezultat izvedbe."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "Dijagnostika"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Najkritičniji je aspekt izvedbe brzina kojom se pikseli generiraju na zaslonu. Ključni mjerni podaci: <PERSON>rvo boje<PERSON><PERSON>, <PERSON><PERSON><PERSON> smisleno bojenje"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Poboljšanja prvog bojenja"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "<PERSON>vi prijedlozi mogu vam pomoći da brže učitate stranicu. Oni ne [utječu izravno](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) na rezultat izvedbe."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Poboljšajte općeniti doživljaj učitavanja tako da stranica bude responzivna i spremna za upotrebu što je prije moguće. Ključni mjerni podaci: Vrijeme do interaktivnosti, Indeks brzine"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Općenita poboljšanja"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "Izvedba"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "Te provjere potvrđuju aspekte progresivne web-aplikacije. [Saznajte više](https://developers.google.com/web/progressive-web-apps/checklist)."}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "Te su provjere potrebne za osnovni [PWA kontrolni popis](https://developers.google.com/web/progressive-web-apps/checklist), no Lighthouse ih ne izvodi automatski. One ne utječu na vaš rezultat, no važno je da ih ručno izvršite."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "Progresivna web-aplikacija"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "Brzo i pouzdano"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Može se instalirati"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PWA je optimiziran"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "Ove provjere služe za optimizaciju vaše stranice za rangiranje rezultata tražilice. Lighthouse ne provjerava neke dodatne čimbenike koji mogu utjecati na vaše rangiranje u pretraživanju. [Saznajte više](https://support.google.com/webmasters/answer/35769)."}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "Pokrenite ove dodatne validatore na svojoj web-lokaciji da biste pregledali dodatne najbolje primjere iz prakse za SEO."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "Formatirajte HTML na način koji omogućuje alatima za indeksiranje da bolje razumiju sadržaj vaše aplikacije."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "Najbolji primjeri sadržaja"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Da bi se vaša aplikacija pojavila u rezultatima pretraživanja, omogućite alatima za indeksiranje da joj pristupe."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Pretraživanje i indeksiranje"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "Prilagodite svoje stranice mobilnim uređajima kako ih korisnici ne bi trebali sami povećavati da bi čitali sadržaj. [Saznajte više](https://developers.google.com/search/mobile-sites/)."}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "Prilagođeno mobilnim uređajima"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "TTL predmemoriranja"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "Lokacija"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "Vrsta resursa"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "Veličina"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Utrošeno vrijeme"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "Veličina prijenosa"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Potencijalna ušteda"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "Potencijalna ušteda"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Potencijalna ušteda {wastedBytes, number, bytes} KB"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Potencijalna ušteda {wastedMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokument"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "Font"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "Slika"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "Drugo"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "Skripta"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "List stila"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "T<PERSON>ća strana"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "Ukupno"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "Nešto nije u redu sa snimanjem traga preko učitavanja stranice. Ponovno pokrenite Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "Isteklo je vrijeme čekanja za inicijalnu vezu za protokol za otklanjanje pogrešaka."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome nije prikupio nikakve snimke zaslona tijekom učitavanja stranice. Provjerite je li sadržaj vidljiv na stranici, a zatim pokušajte ponovno pokrenuti Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "DNS poslužitelji nisu mogli razriješiti navedenu domenu."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Potrebni prikupljač {artifactName} naišao je na pogrešku: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "Došlo je do interne pogreške u Chromeu. Ponovo pokrenite Chrome i pokušajte ponovo pokrenuti Lighthouse."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "<PERSON>je se pokrenuo potrebni prikupljač {artifactName}."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse nije mogao pouzdano učitati stranicu koju ste zatražili. Provjerite testirate li ispravan URL i odgovara li poslužitelj pravilno na sve zahtjeve."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse nije mogao pouzdano učitati URL koji ste zatražili jer je stranica prestala reagirati."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "URL koji ste naveli nema valjani sigurnosni certifikat. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome je spriječio međuprostorno učitavanje stranice. Provjerite testirate li ispravan URL i odgovara li poslužitelj pravilno na sve zahtjeve."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse nije mogao pouzdano učitati stranicu koju ste zatražili. Provjerite testirate li ispravan URL i odgovara li poslužitelj pravilno na sve zahtjeve. (Pojedinosti: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse nije mogao pouzdano učitati stranicu koju ste zatražili. Provjerite testirate li ispravan URL i odgovara li poslužitelj pravilno na sve zahtjeve. (Statusni kôd: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Učitavanje stranice trajalo je predugo. Slijedite mogućnosti u izvješću da biste smanjili vrijeme učitavanja stranice, a zatim pokušajte ponovno pokrenuti Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "Čekanje odgovora protokola DevTools premašilo je dodijeljeno vrijeme. (Način: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "Dohvaćanje sadržaja resursa premašilo je dodijeljeno vrijeme"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "Čini se da URL koji ste naveli nije važeći."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "Prikažite preglede"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "Početna navigacija"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "Maksimalna latencija kritičkog puta:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "Pogreška!"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "Pogreška izvješća: nema podataka o pregledu"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "Laboratorijski podaci"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) analiza trenutačne stranice na emuliranoj mobilnoj mreži. Vrijednosti se procjenjuju i mogu se razlikovati."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "Dodatne stavke za ručnu provjeru"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "<PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "Procijenjena <PERSON>"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "Po<PERSON><PERSON><PERSON> resurse treće strane"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "Na ovo izvođenje Lighthousea utjecale su neke poteškoće:"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "Vrijednosti se procjenjuju i mogu se razlikovati. Rezultat izvedbe jest [, a temelji se samo na ovim mjernim podacima ](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "Uspješni pregledi no s upozorenjima"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "Upozorenja: "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "Savjetujemo vam da prenesete GIF na uslugu na kojoj će se kodirati za ugrađivanje kao HTML5 videozapis."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "Instalirajte [WordPressov dodatak za lijeno učitavanje](https://wordpress.org/plugins/search/lazy+load/) koji pruža mogućnost odgode slika koje nisu na zaslonu ili prijeđite na temu koja pruža tu funkciju. Savjetujemo vam i upotrebu [dodatka za AMP](https://wordpress.org/plugins/amp/)."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "Brojni WordPressovi dodaci omogućuju vam da [ugradite kritične elemente](https://wordpress.org/plugins/search/critical+css/) ili [odgodite nevažnije resurse](https://wordpress.org/plugins/search/defer+css+javascript/). Upozoravamo da optimizacije koje pružaju ti dodaci mogu oštetiti značajke vaše teme ili dodataka, pa ćete vjerojatno trebati unijeti promjene u kôd."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "Specifikacije za teme, dodatke i poslužitelj produljuju vrijeme odgovora poslužitelja. Savjetujemo vam da pronađete optimiziraniju temu, pažljivo odaberete dodatak za optimizaciju i/ili nadogradite poslužitelj."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "Savjetujemo vam da prikažete odlomke na popisu postova (na primjer pomoću više oznaka), smanjite broj postova koji se prikazuju na određenoj stranici, raz<PERSON>mite dugačke postove na više stranica ili koristite dodatak za lijeno učitavanje komentara."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "<PERSON><PERSON><PERSON><PERSON> [WordPressov<PERSON> dodaci](https://wordpress.org/plugins/search/minify+css/) mogu ubrzati vašu web-lokaciju ulančavanjem, umanjivanjem i komprimiranjem stilova. Umanjivanje je dobro izvršiti i unaprijed, tijekom postupka razvoja."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "<PERSON><PERSON><PERSON><PERSON> [WordPressov<PERSON> dodaci](https://wordpress.org/plugins/search/minify+javascript/) mogu ubrzati vašu web-lokaciju ulančavanjem, umanjivanjem i komprimiranjem skripti. Umanjivanje je dobro izvršiti i unaprijed, tijekom postupka razvoja."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "Savjetujemo vam da smanjite broj [WordPressovih dodataka](https://wordpress.org/plugins/) koji učitavaju CSS koji se ne koristi na vašoj stranici ili da ih isključite. Da biste pronašli dodatke koji dodaju suvišan CSS, pokrenite [pokrivenost koda](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) u alatu Chrome DevTools. Odgovornu temu ili dodatak možete pronaći u URL-u stilske tablice. Obratite pažnju na dodatke koji na popisu imaju mnogo stilskih tablica s mnogo crvenog u pokrivenosti koda. Dodatak bi trebao postaviti stilsku tablicu u red samo ako se ona doista upotrebljava na stranici."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "Savjetujemo vam da smanjite broj [WordPressovih dodataka](https://wordpress.org/plugins/) koji učitavaju JavaScript koji se ne koristi na vašoj stranici ili da ih isključite. Da biste pronašli dodatke koji dodaju suvišan JS, pokrenite [pokrivenost koda](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) u alatu Chrome DevTools. Odgovornu temu ili dodatak možete pronaći u URL-u skripte. Obratite pažnju na dodatke koji na popisu imaju mnogo skripti s mnogo crvenog u pokrivenosti koda. Dodatak bi trebao postaviti skriptu u red samo ako se ona doista upotrebljava na stranici."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "Pročitajte više o [predmemoriranju koje preglednici obavljaju u WordPressu](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "Savjetujemo vam upotrebu [WordPressovog dodatka za optimizaciju slika](https://wordpress.org/plugins/search/optimize+images/) koji komprimira slike bez gubitka kvalitete."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "Prenesite slike izravno pomoću  [medijske biblioteke](https://codex.wordpress.org/Media_Library_Screen) kako bi bile dostupne potrebne veličine slika, a zatim ih umetnite iz medijske biblioteke ili upotrijebite widget za slike da bi se koristile optimalne veličine slika (uključujući one za responzivne prijelomne točke). Izbjegavajte upotrebu slika `Full Size` osim ako dimenzije odgovaraju njihovoj upotrebi. [Saznajte više](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)."}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "Možete omogućiti kompresiju teksta u konfiguraciji web-poslužitelja."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "Savjetujemo vam upotrebu [dodatka](https://wordpress.org/plugins/search/convert+webp/) ili usluge koji će automatski konvertirati prenesene slike u optimalne formate."}}