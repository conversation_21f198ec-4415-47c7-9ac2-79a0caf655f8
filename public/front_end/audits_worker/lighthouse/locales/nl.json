{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "Met toegangstoetsen kunnen gebruikers snel de focus op een gedeelte van de pagina plaatsen. Voor correcte navigatie moet elke toegangstoets uniek zijn. [Meer informatie](https://web.dev/accesskeys/)"}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "`[accesskey]`-wa<PERSON>en zijn niet uniek"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]`-wa<PERSON>en zijn uniek"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON>-`role` ondersteunt een specifieke subset van `aria-*`-kenmerken. Als deze verkeerd worden gekoppeld, worden de `aria-*`-kenmerken ongeldig. [Meer informatie](https://web.dev/aria-allowed-attr/)"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "`[aria-*]`-kenmerken komen niet overeen met hun rollen"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "`[aria-*]`-kenmerken komen overeen met hun rollen"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "Sommige ARIA-rollen hebben vereiste kenmerken die de status van het element beschrijven voor schermlezers. [Meer informatie](https://web.dev/aria-required-attr/)"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]`-elementen bevatten niet alle vereiste `[aria-*]`-kenmerken"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]`-elementen bevatten alle vereiste `[aria-*]`-kenmerken"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "Sommige bovenliggende ARIA-rollen moeten specifieke onderliggende rollen bevatten om de beoogde toegankelijkheidsfuncties uit te voeren. [Meer informatie](https://web.dev/aria-required-children/)"}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "In elementen met een <PERSON> `[role]` die vereisen dat onderliggende elementen een specifieke `[role]` be<PERSON><PERSON>, ontbreken enkele (of alle) van die vereiste onderliggende elementen."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "Elementen met een <PERSON> `[role]` die vereisen dat onderliggende elementen een specifieke `[role]` bevatten, bevatten alle vereiste onderliggende elementen."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "Sommige onderliggende ARIA-rollen moeten zijn opgenomen in specifieke bovenliggende rollen om de beoogde toegankelijkheidsfuncties op de juiste manier uit te voeren. [Meer informatie](https://web.dev/aria-required-parent/)"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]`-elementen zijn niet opgenomen in het vereiste bovenliggende element"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]`-elementen zijn opgenomen in het vereiste bovenliggende element"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "ARIA-rollen moeten geldige waarden hebben om hun beoogde toegankelijkheidsfuncties uit te voeren. [Meer informatie](https://web.dev/aria-roles/)"}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]`-wa<PERSON><PERSON> zijn niet geldig"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "`[role]`-wa<PERSON><PERSON> zijn geldig"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "ARIA-<PERSON><PERSON><PERSON><PERSON> met ongeldige waarden kunnen niet worden geïnterpreteerd door hulptechnologieën, zoals schermlezers. [Meer informatie](https://web.dev/aria-valid-attr-value/)"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "`[aria-*]`-kenmerken hebben geen geldige waarden"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "`[aria-*]`-kenmerken bevatten geldige waarden"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "ARIA-k<PERSON><PERSON><PERSON> met ongeldige namen kunnen niet worden geïnterpreteerd door hulptechnologieën, zoals scher<PERSON>lezers. [Meer informatie](https://web.dev/aria-valid-attr/)"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "`[aria-*]`-ken<PERSON><PERSON> zijn niet geldig of zijn verkeerd gespeld"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "`[aria-*]`-kenmerken zijn geldig en niet verkeerd gespeld"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "Ondertiteling maakt audio-elementen bruikbaar voor doven en slechthorenden en geeft essentiële informatie zoals wie praat, wat de persoon zegt en andere niet-gesproken informatie. [Meer informatie](https://web.dev/audio-caption/)"}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "Voor `<audio>`-elementen ontbreekt een `<track>`-element met `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "`<audio>`-elementen bevatten een `<track>`-element met `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Mislukte elementen"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "Wanneer een knop geen toegankelijke naam he<PERSON>, kondigen schermlezers deze aan als 'knop', waardoor de knop onbruikbaar wordt voor gebruikers die afhankelijk zijn van schermlezers. [Meer informatie](https://web.dev/button-name/)"}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> hebben geen toegankeli<PERSON>e naam"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> hebben een toegankelijke naam"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "Als je manieren toevoegt om herhaalde content te omzeilen, kunnen toetsenbordgebruikers efficiënter navigeren op de pagina. [Meer informatie](https://web.dev/bypass/)"}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "De pagina bevat geen kop, link voor overslaan of herkenningspuntregio"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "De pagina bevat een kop, link voor overslaan of herkenningspuntregio"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "<PERSON>oor veel gebruikers is tekst met weinig contrast moeilijk of onmogelijk te lezen. [Meer informatie](https://web.dev/color-contrast/)"}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "De contrastverhouding van achtergrond- en voorgrondkleuren is niet voldo<PERSON>e"}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "De contrastverhouding van achtergrond- en voorgrondkleuren is voldoende"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "<PERSON><PERSON> definitielij<PERSON> niet juist zijn <PERSON>, kunnen schermlezers verwarrende of onjuiste uitvoer produceren. [Meer informatie](https://web.dev/definition-list/)"}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>`-elementen bevatten niet alleen juist geordende `<dt>`- en `<dd>`-g<PERSON><PERSON>en, `<script>` of `<template>`-elementen."}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "`<dl>`-elementen bevatten alleen juist geordende`<dt>`- en `<dd>`-g<PERSON><PERSON>en,`<script>` of `<template>`-elementen."}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "Definitielijstitems (`<dt>` en `<dd>`) moeten zijn verpakt in een bovenliggend `<dl>`-element om ervoor te zorgen dat schermlezers ze juist kunnen aankondigen. [Meer informatie](https://web.dev/dlitem/)"}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "Definitielijstitems zijn niet verpakt in `<dl>`-elementen"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "Definitielijstitems zijn verpakt in `<dl>`-elementen"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "De titel geeft gebruikers van een schermlezer een overzicht van de pagina. Gebruikers van een zoekmachine vertrouwen in hoge mate hierop om te bepalen of een pagina relevant is voor hun zoekopdracht. [Meer informatie](https://web.dev/document-title/)"}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "Document bevat geen `<title>`-element"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "Document bevat een `<title>`-element"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "De waarde van een id-kenmerk moet uniek zijn om te voorkomen dat andere instanties over het hoofd worden gezien door hulptechnologieën. [Meer informatie](https://web.dev/duplicate-id/)"}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "De `[id]`-kenmerken op de pagina zijn niet uniek"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "De `[id]`-kenmerken op de pagina zijn uniek"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "Gebruikers van een schermlezer zijn afhan<PERSON><PERSON><PERSON> van frametitels die de content van de frames beschrijven. [Meer informatie](https://web.dev/frame-title/)"}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "`<frame>`- of `<iframe>`-elementen hebben geen titel"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "`<frame>`- of `<iframe>`-elementen hebben een titel"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "Als de pagina geen lang-ken<PERSON><PERSON> specific<PERSON>, neemt een schermlezer aan dat de pagina is ges<PERSON><PERSON>ven in de standaardtaal die de gebruiker heeft gekozen toen de schermlezer werd ingesteld. Als de pagina niet in de standaardtaal is ges<PERSON><PERSON><PERSON>, kan de schermlezer de tekst van de pagina mogelijk niet juist aankondigen. [Meer informatie](https://web.dev/html-has-lang/)"}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>`-element bevat geen `[lang]`-kenmerk"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>`-element bevat een `[lang]`-kenmerk"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "Als je een geldige [BCP 47-taal](https://www.w3.org/International/questions/qa-choosing-language-tags#question) opgeeft, kunnen schermlezers de tekst juist aankondigen. [Meer informatie](https://web.dev/html-lang-valid/)"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>`-element bevat geen geldige waarde voor het `[lang]`-kenmerk."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "`<html>`-element bevat een geldige waarde voor het`[lang]`-kenmerk"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "Voor informatieve elementen moet een korte, beschrijvende alternatieve tekst worden gebruikt. Decoratieve elementen kunnen worden genegeerd met een leeg alt-kenmerk. [Meer informatie](https://web.dev/image-alt/)"}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "Afbeeldingselementen hebben geen `[alt]`-kenmerken"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "Afbeeldingselementen bevatten `[alt]`-kenmerken"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "Wanneer een afbeelding wordt gebruikt als `<input>`-knop en je hiervoor alternatieve tekst opgeeft, kunnen gebruikers van een schermlezer beter begrijpen wat het doel van de knop is. [Meer informatie](https://web.dev/input-image-alt/)"}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">`-elementen bevatten geen `[alt]`-tekst"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">`-elementen bevatten `[alt]`-tekst"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "Labels zorgen ervoor dat formulieropties juist worden aangekondigd door hulptechnologieën, zoals schermlezers. [Meer informatie](https://web.dev/label/)"}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "Formulierelementen hebben geen bijbehorende labels"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "Formulierelementen hebben bijbehorende labels"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "Een tabel die wordt gebruikt voor lay-outdoeleinden, mag geen gegevenselementen (zoals th- of caption-elementen of het summary-kenmerk) bevatten omdat dit verwarrend kan zijn voor gebruikers van een schermlezer. [Meer informatie](https://web.dev/layout-table/)"}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "`<table>`-presentatie-elementen vermijden niet het gebruik van `<th>`, `<caption>` of het `[summary]`-kenmerk."}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "In `<table>`-presentatie-elementen wordt het gebruik van `<th>`, `<caption>` of het `[summary]`-kenmerk vermeden."}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "Met linktekst (en alternatieve tekst voor afbeeldingen, indien gebruikt als links) die herkenbaar, uniek en focusbaar is, verbeter je de navigatiefunctionaliteit voor gebruikers van een schermlezer. [Meer informatie](https://web.dev/link-name/)"}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "<PERSON>s hebben geen herkenbare naam"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "<PERSON>s hebben een herkenbare naam"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "Schermlezers hebben een specifieke manier om lijsten aan te kondigen. Met een juiste lijststructuur verbetert de uitvoer van schermlezers. [Meer informatie](https://web.dev/list/)"}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> bevatten niet alleen `<li>`-elementen en elementen voor scriptondersteuning (`<script>` en `<template>`)."}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "<PERSON><PERSON><PERSON> bevatten alleen `<li>`-elementen en elementen voor scriptondersteuning (`<script>` en `<template>`)."}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "Voor schermlezers moeten lijstitems (`<li>`) binnen een bovenliggende `<ul>` of `<ol>` worden geplaatst om juist te worden aangekondigd. [Meer informatie](https://web.dev/listitem/)"}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "Lijstitems (`<li>`) zijn niet opgenomen in `<ul>` of bovenliggende `<ol>`-elementen."}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "Lijstitems (`<li>`) zijn tussen bovenliggende `<ul>`- of `<ol>`-elementen geplaatst"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "Gebruikers verwachten niet dat een pagina automatisch wordt vernieuwd. Als dit wel geb<PERSON>t, wordt de focus teruggezet op de bovenkant van de pagina. Dit kan vervelend of verwarrend zijn voor gebruikers. [Meer informatie](https://web.dev/meta-refresh/)"}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Het document gebruikt `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "Het document gebruikt `<meta http-equiv=\"refresh\">` niet"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "Uitschakeling van de zoomfunctie is problematisch voor slechtzienden die afhankelijk zijn van schermvergroting om de content van een webpagina te zien. [Meer informatie](https://web.dev/meta-viewport/)"}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` wordt gebruikt in het `<meta name=\"viewport\">`-element of het `[maximum-scale]`-kenmerk is minder dan 5."}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` wordt niet gebruikt in het `<meta name=\"viewport\">`-element en het `[maximum-scale]`-kenmerk is niet minder dan 5."}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "Schermlezers kunnen geen andere content dan tekst vertalen. Als je alt-tekst aan `<object>`-elementen toevoegt, kunnen schermlezers de betekenis overbrengen aan gebruikers. [Meer informatie](https://web.dev/object-alt/)"}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>`-elementen bevatten geen `[alt]`-tekst"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "`<object>`-elementen bevatten `[alt]`-tekst"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "Een waarde groter dan 0 impliceert een expliciete navigatievolgorde. Hoewel dit technisch geldig is, is dit vaak vervelend voor gebruikers die afhankelijk zijn van hulptechnologieën. [Meer informatie](https://web.dev/tabindex/)"}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "Sommige elementen hebben een `[tabindex]`-waarde die groter is dan 0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "Geen element dat een `[tabindex]`-waarde heeft die groter is dan 0"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "Schermlezers hebben functies waarmee gemakkelijker kan worden genavigeerd in tabellen. Als je zorgt dat `<td>`-cellen die het `[headers]`-ken<PERSON><PERSON> gebruiken, alleen verwijzen naar andere cellen in dezelfde tabel, kun je de functionaliteit verbeteren voor gebruikers van een schermlezer. [Meer informatie](https://web.dev/td-headers-attr/)"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Cellen in een `<table>`-element die het `[headers]`-ken<PERSON>k gebruiken, verwijzen naar een element `id` dat niet in dezelfde tabel wordt gevonden."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "Cellen in een `<table>`-element dat het `[headers]`-ken<PERSON>k gebruik<PERSON>, verwijzen naar tabelcellen in dezelfde tabel."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "Schermlezers hebben functies waarmee gemakkelijker kan worden genavigeerd in tabellen. Als je zorgt dat tabelheaders altijd verwijzen naar een bepaalde reeks cellen, kun je de functionaliteit verbeteren voor gebruikers van een schermlezer. [Meer informatie](https://web.dev/th-has-data-cells/)"}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "`<th>`-elementen en elementen met `[role=\"columnheader\"/\"rowheader\"]` bevatten niet de gegevenscellen die ze beschrijven."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "`<th>`-elementen en elementen met `[role=\"columnheader\"/\"rowheader\"]` bevatten de gegevenscellen die ze beschrijven."}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "Als je een geldige [BCP 47-taal](https://www.w3.org/International/questions/qa-choosing-language-tags#question) voor elementen opgeeft, kan de tekst juist wordt uitgesproken door een schermlezer. [Meer informatie](https://web.dev/valid-lang/)"}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "`[lang]`-kenmerken hebben geen geldige waarde"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "`[lang]`-kenmerken bevatten een geldige waarde"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "Video's met ondertiteling bieden doven en slechthorenden betere toegang tot de bijbehorende informatie. [Meer informatie](https://web.dev/video-caption/)"}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "`<video>`-elementen bevatten geen `<track>`-element met `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "`<video>`-elementen bevatten een `<track>`-element met `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "Audiobeschrijvingen bieden relevante informatie over video's die dialoog niet kan bieden, zoals gezichtsuitdrukkingen en scènes. [Meer informatie](https://web.dev/video-description/)"}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "`<video>`-elementen bevatten geen `<track>`-element met `[kind=\"description\"]`."}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "`<video>`-elementen bevatten een `<track>`-element met `[kind=\"description\"]`"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "Definieer een `apple-touch-icon` voor een ideale weergave op iOS wanneer gebruikers een progressive web-app aan het startscherm toevoegen. Deze moet verwijzen naar een niet-transparante vierkante PNG van 192px (of 180px). [Meer informatie](https://web.dev/apple-touch-icon/)"}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "Geeft geen geldig `apple-touch-icon` op"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "`apple-touch-icon-precomposed` is verouderd; `apple-touch-icon` k<PERSON><PERSON><PERSON>."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "Verstrekt een geldige `apple-touch-icon`"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chrome-extensies hadden een negatieve invloed op de laadprestaties van deze pagina. Controleer de pagina in de incognitomodus of via een Chrome-profiel zonder extensies."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "Scriptevaluatie"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "<PERSON><PERSON><PERSON> parseren"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "Totale CPU-tijd"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "Overweeg de tijd te verminderen die aan parseren, compileren en uitvoeren van JS wordt besteed. Het leveren van kleinere JS-payloads kan hierbij helpen. [Meer informatie](https://web.dev/bootup-time)"}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "Verkort de JavaScript-uitvoeringstijd"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "JavaScript-uitvoeringstijd"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Grote gif's zijn niet efficiënt om content met animaties te leveren. Overweeg het gebruik van MPEG4-/WebM-video's voor animaties en PNG/WebP voor statische afbeeldingen in pla<PERSON> van gif's om netwerkbytes te besparen. [Meer informatie](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Gebruik video-indelingen voor content met animaties"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Overweeg om afbeeldingen die niet in beeld zijn en verborgen afbeeldingen via 'lazy loading' te laden nadat alle kritieke bronnen zijn geladen om zo de tijd tot interactief te verlagen. [Meer informatie](https://web.dev/offscreen-images)"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "<PERSON>ad afbeeldingen die niet in beeld zijn nog niet"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Bronnen blokkeren de eerste weergave (FP) voor je pagina. Overweeg kritieke JS/css inline te leveren en alle niet-kritieke JS/stijlen uit te stellen. [Meer informatie](https://web.dev/render-blocking-resources)"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Verwijder bronnen die de weergave blokkeren"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Grote netwerkpayloads kosten gebruikers effectief geld en hebben vaak lange laadtijden. [Meer informatie](https://web.dev/total-byte-weight)"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Totale grootte was {totalBytes, number, bytes} KB"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Vermijd enorme netwerkpayloads"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Vermijdt enorme netwerkpayloads"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "Als je css-best<PERSON><PERSON> verklein<PERSON>, kun je de omvang van netwerkpayloads verkleinen. [Meer informatie](https://web.dev/unminified-css)"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "Verklein de css"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Als je JavaScript-best<PERSON><PERSON> verk<PERSON>, kunnen de omvang van de payload en de parseringstijd van het script worden verkleind. [Meer informatie](https://web.dev/unminified-javascript)"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Verklein JavaScript"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Verwijder overbodig geworden regels uit stylesheets en stel het laden van ongebruikte css uit voor content boven de vouw zodat er minder onnodige bytes worden verbruikt door netwerkactiviteit. [Meer informatie](https://web.dev/unused-css-rules)"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Ongebruikte css verwijderen"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Verwijder ongebruikt JavaScript om het aantal bytes te verminderen dat wordt verbruikt door netwerkactiviteit."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Verwijder ongebruikt JavaScript"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "<PERSON>en lange levensduur voor het cachegeheugen kan herhaalde bezoeken aan je pagina versnellen. [Meer informatie](https://web.dev/uses-long-cache-ttl)"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 bron gevonden}other{# bronnen gevonden}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Lever statische items met een efficiënt cachebeleid"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Gebruikt een efficiënt cachebeleid voor statische items"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Geoptimaliseerde afbeeldingen worden sneller geladen en verbruiken minder mobiele data. [Meer informatie](https://web.dev/uses-optimized-images)"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "<PERSON><PERSON> afbeeldingen op een efficiënte manier"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> met het juiste formaat om mobiele data te besparen en de laadtijd te verbeteren. [Meer informatie](https://web.dev/uses-responsive-images)"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "<PERSON><PERSON> a<PERSON> het juiste formaat"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Tekstgebaseerde bronnen moeten worden geleverd met compressie (gzip, deflate of brotli) om het totale aantal netwerkbytes te minimaliseren. [Meer informatie](https://web.dev/uses-text-compression)"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Schakel tekstcompressie in"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "Afbeeldingsindelingen zoals JPEG 2000, JPEG XR en WebP bieden vaak betere compressie dan PNG of JPEG. Dit resulteert in snellere downloads en minder dataverbruik. [Meer informatie](https://web.dev/uses-webp-images)"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "Lever afbeeldingen in moderne indelingen"}, "lighthouse-core/audits/content-width.js | description": {"message": "Als de breedte van de content van je app niet overeenkomt met de breedte van het kijkvenster, is je app mogelijk niet geoptimaliseerd voor mobiele schermen. [Meer informatie](https://web.dev/content-width)"}, "lighthouse-core/audits/content-width.js | explanation": {"message": "Het kijkvensterformaat van {innerWidth}px komt niet overeen met het vensterformaat van {outerWidth}px."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "De <PERSON> heeft niet het juiste formaat voor het kijkvenster"}, "lighthouse-core/audits/content-width.js | title": {"message": "De <PERSON> heeft het juiste formaat voor het kijkvenster"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "De onderstaande kritieke verzoekketens laten zien welke bronnen met een hoge prioriteit worden geladen. Overweeg de lengte van ketens te verkleinen, de downloadgrootte van bronnen te beperken of het downloaden van onnodige bronnen uit te stellen om de laadtijd van de pagina te verbeteren. [Meer informatie](https://web.dev/critical-request-chains)"}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 keten gevonden}other{# ketens gevonden}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "Minimaliseer de diepte van kritieke verzoeken"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "Beëindiging / Waarschuwing"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/deprecations.js | description": {"message": "Beëindigde API's worden uiteindelijk verwijderd uit de browser. [Meer informatie](https://web.dev/deprecations)"}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 waarschuwing gevonden}other{# waarschuwingen gevonden}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "Gebruikt beëindigde API's"}, "lighthouse-core/audits/deprecations.js | title": {"message": "Vermijdt beëindigde API's"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "Applicatiecache is beëindigd. [Meer informatie](https://web.dev/appcache-manifest)"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "'{AppCacheManifest}' gevonden"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "Gebruikt applicatiecache"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "Vermijdt applicatiecache"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "Door een doctype op te geven, voorkom je dat de browser overschakelt naar de quirks-modus. [Meer informatie](https://web.dev/doctype)"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "De naam van het doctype moet de tekenreeks `html` in kleine letters zijn"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Document moet een doctype bevatten"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Verwachtte een lege tekenreeks voor publicId"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Verwachtte een lege tekenreeks voor systemId"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Pa<PERSON>a heeft geen html-doctype en activeert dus de quirks-modus"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "<PERSON><PERSON>a bevat html-doctype"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "Element"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistiek"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "Ontwikkelaars van browsers raden aan dat pagina's minder dan ~1.500 DOM-elementen bevatten. De ideale hoeveelheid bestaat uit vertakkingen met < 32 elementen en minder dan 60 onder-/bovenliggende elementen. Een grote DOM kan het geheugengebruik vergroten, en [stijlberekeningen](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) en kostbare [dynamische aanpassingen in de vormgeving](https://developers.google.com/speed/articles/reflow) veroorzaken. [Meer informatie](https://web.dev/dom-size)"}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 element}other{# elementen}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Vermijd een overmatig grote DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Maximum DOM-diepte"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Totaal aantal DOM-elementen"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Maximum aantal onderliggende elementen"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "Vermijdt een overmatige grote DOM"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "Voeg `rel=\"noopener\"` of `rel=\"noreferrer\"` toe aan eventuele externe links om de prestaties te verbeteren en kwetsbaarheden in de beveiliging te voorkomen. [Meer informatie](https://web.dev/external-anchors-use-rel-noopener)"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "<PERSON>s naar cross-origin-best<PERSON><PERSON><PERSON> zijn onveilig"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "<PERSON>s naar cross-origin-best<PERSON><PERSON><PERSON> zijn <PERSON>ig"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "Kan geen bestemming bepalen voor ankeradvertentie ({anchorHTML}). Als deze niet wordt gebruikt als hyperlink, overweeg dan om target=_blank te verwijderen."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Gebruikers wantrouwen of raken in de war van sites die vragen om hun locatie zonder context. Overweeg het verzoek in plaats daarvan te koppelen aan gebruikershandelingen. [Meer informatie](https://web.dev/geolocation-on-start)"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Verzoekt om de geolocatierechten bij laden van pagina"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Vermijdt verzoeken om de geolocatierechten bij laden van pagina"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "Alle front end JavaScript-bibliotheken op de pagina gedetecteerd. [Meer informatie](https://web.dev/js-libraries)"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "Gedetecteerde JavaScript-bibliotheken"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "Externe scripts die dynamisch worden geïnjecteerd via `document.write()` kunnen bij gebruikers met een langzame verbinding het laden van de pagina met tie<PERSON><PERSON> seconden vertragen. [Meer informatie](https://web.dev/no-document-write)"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Gebruikt `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "Vermijdt `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "<PERSON><PERSON> ernstig"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "Bibliotheekversie"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "Aantal kwetsbaarheden"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "Sommige scripts van derden kunnen bekende beveiligingskwetsbaarheden bevatten die makkelijk te identificeren en door aanvallers te gebruiken zijn. [Meer informatie](https://web.dev/no-vulnerable-libraries)"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{1 kwetsbaarheid gedetecteerd}other{# kwetsbaarheden gedetecteerd}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "Omvat front end JavaScript-bibliotheken met bekende beveiligingskwetsbaarheden"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "Laag"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "Normaal"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "Vermijdt front end JavaScript-bibliotheken met bekende beveiligingskwetsbaarheden"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "Gebruikers want<PERSON>wen of raken in de war van sites die vragen om het versturen van meldingen zonder context. Overweeg het verzoek in plaats daarvan te koppelen aan gebruikersgebaren. [Meer informatie](https://web.dev/notification-on-start)"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Verzoekt om de meldingsrechten bij laden van pagina"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "Vermijdt verzoeken om de meldingsrechten bij laden van pagina"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "Mislukte elementen"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "Verhin<PERSON> van het plakken van wachtwoorden ondermijnt een goed beveiligingsbeleid. [Meer informatie](https://web.dev/password-inputs-can-be-pasted-into)"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "Voorkomt dat gebruikers kunnen plakken in wachtwoordvelden"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "Laat gebruikers plakken in wachtwoordvelden"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protocol"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 beschikt over veel voordelen ten opzichte van HTTP/1.1, waaronder binaire headers, multiplexing en serverpush. [Meer informatie](https://web.dev/uses-http2)"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 verzoek niet weergegeven via HTTP/2}other{# verzoeken niet weergegeven via HTTP/2}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "Gebruikt HTTP/2 niet voor alle bronnen"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "Gebruikt HTTP/2 voor de eigen bronnen"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Overweeg je touch- en event-listeners te markeren als `passive` om de scrollprestaties van je pagina te verbeteren. [Meer informatie](https://web.dev/uses-passive-event-listeners)"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Gebruikt geen passieve listeners om scrollprestaties te verbeteren"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Gebruikt passieve listeners voor de verbetering van de scrollprestaties"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "Beschrijving"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "Fouten die op de console worden geregistreerd, geven aan dat er onopgeloste problemen zijn. Ze kunnen afkomstig zijn van niet-uitgevoerde netwerkverzoeken en andere problemen met de browser. [Meer informatie](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "<PERSON><PERSON> zijn <PERSON>n gelogd op de console"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "<PERSON>r zijn geen browserfouten gelogd op de console"}, "lighthouse-core/audits/font-display.js | description": {"message": "<PERSON><PERSON> gebru<PERSON> van de css-functie 'font-display' om ervoor te zorgen dat tekst zichtbaar is voor gebruikers terwijl weblettertypen worden geladen. [Meer informatie](https://web.dev/font-display)"}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "Zorg ervoor dat tekst zichtbaar blijft tijdens het laden van weblettertypen"}, "lighthouse-core/audits/font-display.js | title": {"message": "Alle tekst blijft zichtbaar tijdens het laden van weblettertypen"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "Lighthouse kan de waarde voor de lettertypeweergave niet automatisch controleren voor de volgende URL: {fontURL}."}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "Beeldverhouding (werkelijk)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Beeldverhouding (weergegeven)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "De weergaveafmetingen van afbeeldingen moeten overeenkomen met de natuurlijke beeldverhouding. [Meer informatie](https://web.dev/image-aspect-ratio)"}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "<PERSON><PERSON> af<PERSON><PERSON><PERSON> weer met een on<PERSON><PERSON>e beeld<PERSON>houding"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "<PERSON><PERSON> af<PERSON><PERSON><PERSON> weer met een juiste beeldverhouding"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "Ongeldige informatie over afbeeldingsformaat {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "Browsers kunnen gebruikers proactief vragen je app aan hun startscherm toe te voegen. Dit kan leiden tot grotere betrokkenheid. [Meer informatie](https://web.dev/installable-manifest)"}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "Het manifest van de web-app voldoet niet aan de vereisten voor installeerbaarheid"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "Het manifest van de web-app voldoet aan de vereisten voor installeerbaarheid"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "Niet-beveiligde URL"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "Alle sites moeten worden beschermd met HTTPS, zelfs sites die geen gevoelige gegevens verwerken. HTTPS voorkomt dat indringers de communicatie tussen je app en je gebruikers manipuleren of hier passief naar luisteren en is een vereiste voor HTTP/2 en veel nieuwe webplatform-API's. [Meer informatie](https://web.dev/is-on-https)"}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 niet-beveiligd verzoek gevonden}other{# niet-beveiligde verzoeken gevonden}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "Gebruikt geen HTTPS"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "Gebruikt HTTPS"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "Snel laden van een pagina via een mobiel netwerk zorgt voor een goede mobiele gebruikerservaring. [Meer informatie](https://web.dev/load-fast-enough-for-pwa)"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "Interactief na {timeInMs, number, seconds} s"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "Interactief op gesimuleerd mobiel netwerk binnen {timeInMs, number, seconds} s"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "Je pagina wordt te langzaam geladen en is niet binnen tien seconden interactief. Bekijk de mogelijkheden en diagnostische gegevens in het gedeelte Prestaties voor meer informatie over verbeteringen."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "De pagina wordt niet snel genoeg geladen via mobiele netwerken"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "De pagina wordt snel genoeg geladen via mobiele netwerken"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Categorie"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "Overweeg de tijd te verminderen die aan parseren, compileren en uitvoeren van JS wordt besteed. Het leveren van kleinere JS-payloads kan hierbij helpen. [Meer informatie](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Primaire threadbewerkingen minimaliseren"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "Primaire threadbewerkingen minimaliseren"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "Sites moeten werken in alle grote browsers om zoveel mogelijk gebruikers te bereiken. [Meer informatie](https://web.dev/pwa-cross-browser)"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "De site werkt in verschillende browsers"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Zorg ervoor dat deep links voor afzonderlijke pagina's via een URL kunnen worden opgenomen en dat URL's uniek zijn zodat ze op social media kunnen worden gedeeld. [Meer informatie](https://web.dev/pwa-each-page-has-url)"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Elke pagina heeft een URL"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "Overgangen moeten snel aanvoelen terwijl je op een pagina tikt, zelfs bij gebruik van een langzaam netwerk. Dit is essentieel voor hoe de gebruiker de prestaties waarneemt. [Meer informatie](https://web.dev/pwa-page-transitions)."}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "Paginaovergangen voelen niet alsof ze vastlopen op het netwerk"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "Ges<PERSON>tte wachttijd voor invoer is een schatting van de tijd die je app nodig heeft om te reageren op gebruikersinvoer (in milliseconden) gemeten voor de drukste periode van 5 seconden tijdens het laden van de pagina. Als de wachttijd langer dan 50 ms is, kunnen gebruikers je app als traag beschouwen. [Meer informatie](https://web.dev/estimated-input-latency)"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "Geschatte invoerwachttijd"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "'Eerste tekenbewerking met content' (FCP) geeft het tijdstip aan waarop de eerste tekst of afbeelding wordt weergegeven. [Meer informatie](https://web.dev/first-contentful-paint)"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "Eerste weergave met content (FCP)"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "'Eerste keer dat CPU inactief was' geeft de eerste keer aan dat de primaire thread van de pagina rustig genoeg was om invoer te verwerken.  [Meer informatie](https://web.dev/first-cpu-idle)."}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "Eerste keer dat CPU inactief was"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "'Eerste nuttige weergave' (FMP) meet wanneer de primaire content van een pagina zichtba<PERSON> is. [Meer informatie](https://web.dev/first-meaningful-paint)"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "Eerste nuttige weergave (FMP)"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "Tijd tot interactief is de hoeveelheid tijd die nodig is voordat een pagina volledig interactief is. [Meer informatie](https://web.dev/interactive)"}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "Tijd tot interactief"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "De maximale potentiële vertraging voor de eerste invoer die gebruikers kunnen ervaren, is de duur (in milliseconden) van de langste taak. [Meer informatie](https://developers.google.com/web/updates/2018/05/first-input-delay)"}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "Max<PERSON> potenti<PERSON> eerste invoervertraging"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "Snelheidsindex laat zien hoe snel de content van een pagina zichtba<PERSON> is. [Meer informatie](https://web.dev/speed-index)"}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "Snelheidsindex"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "Som van alle perioden tussen 'Eerste tekenbewerking met content' (FCP) en 'Tijd tot interactief', wanneer de taaklengte langer duurt dan 50 ms, aang<PERSON><PERSON> in milliseconden."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "Totale geblokkeerde tijd"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "Retourtijden (RTT) van netwerken hebben een grote invloed op de prestaties. Een hoge RTT naar een beginpunt geeft aan dat de prestaties van servers dichter bij de gebruiker kunnen worden verbeterd. [Meer informatie](https://hpbn.co/primer-on-latency-and-bandwidth/)"}, "lighthouse-core/audits/network-rtt.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> van netwerk"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "Serverwachttijden kunnen invloed hebben op webprestaties. Als de serverwachttijd van een beginpunt hoog is, is dit een indicatie dat de server overbelast is of slechte backend-prestaties levert. [Meer informatie](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)"}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> van server-backend"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "Met een service worker kan je web-app betrouwbaar functioneren bij onvoorspelbare netwerkomstandigheden. [Meer informatie](https://web.dev/offline-start-url)"}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url` retourneert geen 200-statuscode wanneer de pagina offline is"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url` retourneert een 200-statuscode wanneer de pagina offline is"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "Lighthouse kan de `start_url` in het manifest niet lezen. Daarom is aangenomen dat de `start_url` de URL van het document is. Foutmelding: '{manifestWarning}'."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "Over het budget"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "<PERSON><PERSON> de hoeveelheid en grootte van netwerkverzoeken onder de door het verstrekte prestatiebudget ingestelde doelen. [Meer informatie](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 verzoek}other{# verzoeken}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "Prestatiebudget"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "Als je HTTPS al hebt ingesteld, zorg je ervoor dat je al het HTTP-verkeer omleidt naar HTTPS om beveiligde webfuncties voor alle gebruikers te activeren. [Meer informatie](https://web.dev/redirects-http)"}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> wordt HTTP-verkeer niet omgeleid naar HTTPS"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "Hier<PERSON> wordt HTTP-verkeer omgeleid naar HTTPS"}, "lighthouse-core/audits/redirects.js | description": {"message": "Omleidingen zorgen voor extra vertraging voordat de pagina kan worden geladen. [Meer informatie](https://web.dev/redirects)"}, "lighthouse-core/audits/redirects.js | title": {"message": "Vermijd meerdere pagina-omleidingen"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "Voeg een budget.json-bestand toe om budgetten in te stellen voor de hoeveelheid en grootte van paginabronnen. [Meer informatie](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 verzoek • {byteCount, number, bytes} KB}other{# verzoeken • {byteCount, number, bytes} KB}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "Houd het aantal verzoeken laag en de overdrachtsgrootte klein"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "Canonieke links geven een suggestie voor welke URL moet worden weergegeven in de zoekresultaten. [Meer informatie](https://web.dev/canonical)"}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "Meerdere conflicterende URL's ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "Wijst naar een ander domein ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "Ongeldige URL ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Wijst naar een andere `hreflang`-locatie ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "Relatieve URL ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "Verwijst naar de root-URL van het domein (de homepage), in plaats van een equivalente pagina van de content"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "Document bevat geen geldig `rel=canonical`"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "Document bevat een geldige `rel=canonical`"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "Lettergrootten kleiner dan 12 pixels zijn te klein om leesbaar te zijn en leiden ertoe dat mobiele bezoekers hun 'vingers moeten 'samenknijpen' om te zoomen voordat ze de tekst kunnen lezen. Probeer om meer dan 60% van de paginatekst gelijk aan of groter dan 12 pixels te maken. [Meer informatie](https://web.dev/font-size)"}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} leesbare tekst"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "Tekst is onleesbaar omdat er geen kijkvenstermetatag is geoptimaliseerd voor mobiele schermen."}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "{decimalProportion, number, extendedPercent} van de tekst is te klein (op basis van een voorbeeld van {decimalProportionVisited, number, extendedPercent})."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "Document gebruikt geen leesbare lettergrootten"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "Document gebruikt leesbare lettergrootten"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "hreflang-links laten zoekmachines weten welke versie van een pagina ze moeten vermelden in zoekresultaten voor een bepaalde taal of regio. [Meer informatie](https://web.dev/hreflang)"}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "Document bevat geen geldige `hreflang`"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "Document bevat een geldige `hreflang`"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "<PERSON><PERSON><PERSON>'s met ongeldige HTTP-statuscodes worden mogelijk niet juist geïndexeerd. [Meer informatie](https://web.dev/http-status-code)"}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "Pagina bevat ongeldige HTTP-statuscode"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "<PERSON>gina bevat geldige HTTP-statuscode"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "Zoekmachines kunnen je pagina's niet opnemen in zoekresultaten als de zoekmachines geen rechten hebben om ze te crawlen. [Meer informatie](https://web.dev/is-crawable)"}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "Pagina is geblokkeerd tegen indexeren"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "Pagina is niet geblo<PERSON>erd tegen indexeren"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "<PERSON><PERSON> de <PERSON> van beschrijvende linktekst kunnen zoekmachines je content begrijpen. [Meer informatie](https://web.dev/link-text)"}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 link gevonden}other{# links gevonden}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "Links bevatten geen beschrijvende tekst"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "<PERSON>s bevatten beschrijvende tekst"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "<PERSON><PERSON><PERSON> de [Tool voor het testen van gestructureerde gegevens](https://search.google.com/structured-data/testing-tool/) en de [Linter voor gestructureerde gegevens](http://linter.structured-data.org/) uit om gestructureerde gegevens te valideren. [Meer informatie](https://web.dev/structured-data)"}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "De gestructureerde gegevens zijn geldig"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "Er kunnen metabeschrijvingen worden opgenomen in zoekresultaten voor een korte samenvatting van paginacontent. [Meer informatie](https://web.dev/meta-description)"}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "Beschrijvingstekst is leeg."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "Document bevat geen metabeschrijving"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "Document bevat een metabeschrijving"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "Zoekmachines kunnen content van plug-ins niet indexeren en veel apparaten beperken plug-ins of ondersteunen deze niet. [Meer informatie](https://web.dev/plugins)"}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "Document gebruikt plug-ins"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "Document vermijdt plug-ins"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "Als je robots.txt-bestand niet juist is op<PERSON><PERSON><PERSON><PERSON>, begrijpen crawlers mogelijk niet hoe je wilt dat je website wordt gecrawld of geïndexeerd. [Meer informatie](https://web.dev/robots-txt)"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Verzoek voor robots.txt heeft volgende HTTP-status geretourneerd: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 fout gevonden}other{# fouten gevonden}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse kan geen robots.txt-bestand downloaden"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt is niet geldig"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "robots.txt is geldig"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "Interactieve elementen, zoa<PERSON> knoppen en links, moeten groot genoeg zijn (48 x 48 pixels) en moeten voldoende ruimte eromheen hebben, zodat er makkelijk op getikt kan worden zonder dat andere elementen worden aangeraakt. [Meer informatie](https://web.dev/tap-targets)"}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} van de tik<PERSON>elen heeft een geschikt formaat"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Tikdoelen zijn te klein, omdat er geen kijkvenstermetatag is geoptimaliseerd voor mobiele schermen"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> hebben niet het juiste formaat"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Overlappend doel"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> hebben het juiste formaat"}, "lighthouse-core/audits/service-worker.js | description": {"message": "De service worker is de technologie waarmee je app veel functies van progressive web-apps kan gebruiken, zoals offline functionaliteit, toevoegen aan het startscherm en pushmeldingen. [Meer informatie](https://web.dev/service-worker)"}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "Deze pagina wordt beheerd door een service worker, maar er is geen `start_url` gevonden omdat het manifest niet kan worden geparseerd als geldig json-bestand"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "Deze pagina wordt beheerd door een service worker, maar de `start_url` ({startUrl}) valt niet binnen het bereik van de service worker ({scopeUrl})"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "Deze pagina wordt beheerd door een service worker, maar er is geen `start_url` gevonden omdat er geen manifest is opgeha<PERSON>."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "Deze herkomst heeft een of meer service workers, maar de pagina ({pageUrl}) valt niet binnen het bereik."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "Registreert geen service worker die de pagina en `start_url` beheert"}, "lighthouse-core/audits/service-worker.js | title": {"message": "Registreert een service worker die de pagina en `start_url` beheert"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "<PERSON><PERSON> met een thema zorgt voor een gebruikerservaring van hoge kwaliteit wanneer gebruikers je app starten vanaf hun startscherm. [Meer informatie](https://web.dev/splash-screen)"}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "Is niet geconfigureerd voor een aangepast startscherm"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "Is geconfigureerd voor een aangepast startscherm"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "Het thema van de adresbalk van de browser kan worden aangepast aan je site. [Meer informatie](https://web.dev/themed-omnibox)"}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "Hi<PERSON><PERSON> wordt geen themakleur voor de adresbalk ingesteld."}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "Hi<PERSON><PERSON> wordt een themakleur voor de adresbalk ingesteld."}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "Tijd dat primaire thread is geb<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "Derden"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "Code van derden kan van grote invloed zijn op de laadprestaties. Beperk het aantal overbodige externe providers en probeer code van derden te laden nadat je pagina primair is geladen. [Meer informatie](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)"}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "Code van derden heeft de primaire thread gedurende {timeInMs, number, milliseconds} ms geblokkeerd"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "De impact van code van derden beperken"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "Gebruik door derden"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "'Tijd tot eerste byte' identificeert het tijdstip waarop je server een reactie stuurt. [Meer informatie](https://web.dev/time-to-first-byte)"}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "Hoofddocument duurde {timeInMs, number, milliseconds} ms"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "Beperk serverreactietijden (TTFB)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "Serverreactietijden zijn laag (TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "Begintijd"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "Type"}, "lighthouse-core/audits/user-timings.js | description": {"message": "Overweeg je app te voorzien van de API voor gebruikerstiming om de daadwerkelijke prestaties van je app tijdens belangrijke gebruikerservaringen te meten. [Meer informatie](https://web.dev/user-timings)"}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 gebruikerstiming}other{# gebruikerstimings}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "Markeringen en metingen voor gebruikerstiming"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Er is een <link> voor vooraf verbinden gevonden voor {securityOrigin} maar deze is niet gebruikt door de browser. Controleer of je het `crossorigin`-kenmerk juist gebruikt."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "Overweeg hints voor `preconnect` of `dns-prefetch` van bron<PERSON> toe te voegen om vroege verbindingen met belangrijke externe herkomsten tot stand te brengen. [Meer informatie](https://web.dev/uses-rel-preconnect)"}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "<PERSON><PERSON> v<PERSON><PERSON> verb<PERSON>ing met ve<PERSON><PERSON> her<PERSON>"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Er is een <link> voor vooraf laden gevonden voor {preloadURL} maar deze is niet gebruikt door de browser. Controleer of je het `crossorigin`-kenmerk juist gebruikt."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "Overweeg `<link rel=preload>` te gebruiken om prioriteit te geven aan het ophalen van bronnen die momenteel later tijdens het laden van de pagina worden opgehaald. [Meer informatie](https://web.dev/uses-rel-preload)"}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "Laad belangrijke verzoeken vooraf"}, "lighthouse-core/audits/viewport.js | description": {"message": "Voeg een `<meta name=\"viewport\">`-tag toe om je app te optimaliseren voor mobiele schermen. [Meer informatie](https://web.dev/viewport)"}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "Geen `<meta name=\"viewport\">`-tag gevonden"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "Bevat geen `<meta name=\"viewport\">`-tag met `width` of `initial-scale`"}, "lighthouse-core/audits/viewport.js | title": {"message": "Bevat een `<meta name=\"viewport\">`-tag met `width` of `initial-scale`"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "Je app moet wat content weergeven wanneer JavaScript is uitgeschakeld, zelfs als het alleen maar een waarschuwing aan de gebruiker is dat JavaScript is vereist om de app te gebruiken. [Meer informatie](https://web.dev/without-javascript)"}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "Het hoof<PERSON><PERSON><PERSON> van de pagina moet wat content weergeven als de bijbehorende scripts niet beschik<PERSON> zijn."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "Biedt geen reservecontent wanneer JavaScript niet beschik<PERSON>ar is"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "Bevat wat content wanneer JavaScript niet beschikbaar is"}, "lighthouse-core/audits/works-offline.js | description": {"message": "Als je een progressive web-app bouwt, kun je overwegen een service worker te gebruiken zodat je app offline kan worden gebruikt. [Meer informatie](https://web.dev/works-offline)"}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "De huidige pagina retourneert geen 200-statuscode wanneer de pagina offline is"}, "lighthouse-core/audits/works-offline.js | title": {"message": "De huidige pagina retourneert een 200-statuscode wanneer de pagina offline is"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "De pagina wordt mogelijk niet geladen omdat je test-URL ({requested}) is omgeleid naar '{final}'. Test de tweede URL rechtstreeks."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "Dit zijn suggesties om het gebruik van ARIA in je app te verbeteren, wat kan leiden tot betere functionaliteit voor gebruikers van hulptechnologie, zoa<PERSON> een scher<PERSON>."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Dit zijn mogelijkheden om alternatieve content voor audio en video te bieden. Dit verbetert mogelijk de functionaliteit voor gebruikers met een visuele of gehoorbeperking."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Audio en video"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Deze items geven praktische tips voor algemene toegankelijkheid aan."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Praktische tips"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "Deze controles geven mogelijkheden aan om [de toegang tot je web-app te verbeteren](https://developers.google.com/web/fundamentals/accessibility). Alleen een subset van toegankelijkheidsproblemen kan automatisch worden gedetecteerd. Daarom wordt handmatig testen ook aangeraden."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "Deze items gaan over gebieden die niet kunnen worden getest met een automatische testtool. Bekijk meer informatie in onze gids over [het uitvoeren van een toegankelijkheidscontrole](https://developers.google.com/web/fundamentals/accessibility/how-to-review)."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "Toegankelijkheid"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Dit zijn suggesties om de le<PERSON>ba<PERSON>he<PERSON> van je content te verbeteren."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Contrast"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Dit zijn suggesties om de interpretatie van je content door gebruikers in verschillende landen te verbeteren."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internationalisering en lokalisatie"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Dit zijn suggesties om de semantiek van de opties in je app te verbeteren. Zo kun je de functionaliteit verbeteren voor gebruikers van hulptechnologie, zoa<PERSON> een scher<PERSON>."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Namen en labels"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Dit zijn de mogelijkheden om toetsenbordnavigatie in je app te verbeteren."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navigatie"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Dit zijn mogelijkheden om de functionaliteit voor het lezen van gegevens in tabellen of lijsten te verbeteren met hulptechnologie, zoals een scher<PERSON>."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tabellen en lijsten"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Praktische tips"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "Prestatiebudgetten stellen normen in voor de prestaties van je site."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "<PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "Meer informatie over de prestaties van je app. Deze cijfers hebben geen [directe invloed](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) op de prestatiescore."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostische gegevens"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Het meest essentiële aspect van de prestaties is hoe snel pixels worden weergegeven op het scherm. Belangrijkste statistieken: eerste weergave met content (FCP), eerste nuttige weergave (FMP)"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Verbeteringen voor eerste weergave"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Deze suggesties kunnen helpen je pagina sneller te laden. Ze hebben geen [directe invloed](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) op de prestatiescore."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Aanbevelingen"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "Statistieken"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Verbeter de algemene laadfunctionaliteit, zodat de pagina zo snel mogelijk reageert en gebruiksklaar is. Belangrijkste statistieken: Tijd tot interactief, Snelheidsindex"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Algemene verbeteringen"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "Prestaties"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "Deze controles valideren de aspecten van een progressive web-app. [Meer informatie](https://developers.google.com/web/progressive-web-apps/checklist)"}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "Deze controles worden vereist door de baseline [PWA-checklist](https://developers.google.com/web/progressive-web-apps/checklist) maar worden niet automatisch gecontroleerd door Lighthouse. Ze zijn niet van invloed op je score, maar het is wel belangrijk dat je ze handmatig verifieert."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "Progressive web-app"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "Snel en betrouwbaar"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Installeerbaar"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Geoptimaliseerd voor PWA"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "Deze controles zorgen ervoor dat je pagina is geoptimaliseerd voor positionering in zoekmachineresultaten. Er zijn aanvullende factoren waarop Lighthouse niet controleert, die van invloed kunnen zijn op je positie in de zoekresultaten. [Meer informatie](https://support.google.com/webmasters/answer/35769)"}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "<PERSON><PERSON><PERSON> deze extra validators op je site uit om aanvullende praktische tips voor SEO te controleren."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "Maak je html zo op dat crawlers de content van je app beter begrijpen."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "Praktische tips voor content"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Crawlers hebben toegang nodig tot je app om je website in zoekresultaten weer te geven."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Crawlen en indexeren"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "<PERSON><PERSON>g dat je pagina's geschikt zijn voor mobiele apparaten zodat gebruikers niet hun vingers hoeven samen te knijpen of moeten zoomen om de contentpagina's te lezen. [Meer informatie](https://developers.google.com/search/mobile-sites/)"}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "Geschikt voor mobiele apparaten"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Cache-TTL"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "Locatie"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "Verzoeken"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "Brontype"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "Form<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "<PERSON><PERSON> tijd"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "Overdrachtgrootte"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Potentiële besparingen"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "Potentiële besparingen"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Potentiële besparing van {wastedBytes, number, bytes} KB"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Potentiële besparing van {wastedMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "Document"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "Lettertype"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "Afbeelding"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "Media"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Stylesheet"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Derden"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "Totaal"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "Er is een fout opgetreden bij het opnemen van het netwerkspoor voor het laden van je pagina. Voer Lighthouse opnieuw uit. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "Time-out tij<PERSON><PERSON> wachten op eerste verbinding met Debugger-protocol."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome heeft geen screenshots verzameld tijdens het laden van de pagina. Zorg dat er content zichtba<PERSON> is op de pagina en voer Lighthouse daarna opnieuw uit. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "DNS-servers kunnen het opgegeven domein niet omzetten."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Er is een fout opgetreden in de vereiste {artifactName}-verzamelaar: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "Er is een interne Chrome-fout opgetreden. Start Chrome opnieuw op en probeer Lighthouse nogmaals uit te voeren."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "Vereiste {artifactName}-verzamelaar is niet uitgevoerd."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse kan de door jou aangevraagde pagina niet goed laden. Zorg ervoor dat je de juiste URL test en dat de server correct reageert op alle verzoeken."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse kan de door jou aangevraagde URL niet goed laden omdat de pagina niet meer reageert."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "De URL die je hebt opgegeven, bevat geen geldig beveiligingscertificaat. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome heeft laden van pagina met interstitial voorkomen. Zorg dat je de juiste URL test en de server correct reageert op alle verzoeken."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse kan de aangevraagde pagina niet goed laden. Zorg dat je de juiste URL test en de server correct reageert op alle verzoeken. (Details: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse kan de aangevraagde pagina niet goed laden. Zorg dat je de juiste URL test en de server correct reageert op alle verzoeken. (Statuscode: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Het laden van je pagina duurde te lang. Volg de suggesties in het rapport om de laadtijd van je pagina te beperken. Voer Lighthouse daarna opnieuw uit. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "Tijdens het wachten op een reactie van het DevTools-protocol is de toegewezen tijd overschreden. (Methode: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "Voor het ophalen van broncontent is de toegewezen tijd overschreden"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "Het lijkt erop dat je een ongeldige URL hebt opgegeven."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "<PERSON><PERSON> weerge<PERSON>"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "Beginnavigatie"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "Maximum wachttijd voor kritiek pad:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "Fout"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "Rapportfout: geen controlegegevens"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "Labgegevens"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "Analyse met [Lighthouse](https://developers.google.com/web/tools/lighthouse/) van de huidige pagina via een geëmuleerd mobiel netwerk. Waarden worden geschat en kunnen variëren."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "Aanvullende items om handmatig te controleren"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "N.v.t."}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "Aanbeveling"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> besparing"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "Geslaagde controles"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "Fragment samenvouwen"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "Fragment uitvouwen"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "<PERSON><PERSON><PERSON> van <PERSON> weergeven"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "<PERSON>r zijn problemen opgetreden bij deze uitvoering van Lighthouse:"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "Waarden worden geschat en kunnen variëren. De prestatiescore is [alleen gebaseerd op deze statistieken](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "Geslaagd voor controles maar met waarschuwingen"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "Waarschuwingen: "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "Overweeg je gif te uploaden naar een service waarmee het mogelijk is de gif in te sluiten als html5-video."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "Installeer een [WordPress-plug-in voor lazy loading](https://wordpress.org/plugins/search/lazy+load/) waarmee afbeeldingen die niet in beeld zijn, kunnen worden uitgesteld, of schakel over naar een thema met deze functionaliteit. Je kunt ook overwegen [de AMP-plug-in](https://wordpress.org/plugins/amp/) te gebruiken."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "Er zijn een aantal WordPress-plug-ins om [kritieke items inline te plaatsen](https://wordpress.org/plugins/search/critical+css/) of [minder belangrijke bronnen uit te stellen](https://wordpress.org/plugins/search/defer+css+javascript/). Bedenk wel dat de optimalisatie die deze plug-ins bieden, bepaalde functies van je thema of plug-ins kunnen verstoren. Het is daarom goed mogelijk dat je de code moet wijzigen."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "Thema's, plug-ins en serverspecificaties zijn allemaal van invloed op de reactietijd van de server. Overweeg een thema te gebruiken dat beter is geoptimaliseerd, kies met zorg een optimalisatieplug-in en/of upgrade je server."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "Overweeg fragmenten te tonen in je postlijsten (bijvoorbeeld via de tag 'more'), zodat er per pagina minder posts worden weergegeven. Ook kun je lange posts verdelen over meerdere pagina's of een plug-in voor lazy loading van reacties gebruiken."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "Er zijn een aantal [WordPress-plug-ins](https://wordpress.org/plugins/search/minify+css/) die stijlen inkorten, verkleinen en comprimeren en je site sneller maken. Mogelijk kun je een ontwerpproces gebruiken dat deze verkleining van tevoren toepast."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "<PERSON>r zijn een aantal [WordPress-plug-ins](https://wordpress.org/plugins/search/minify+javascript/) die je site sneller kunnen maken door je scripts in te korten, te verkleinen of te comprimeren. Indien mogelijk is het bovendien handig zulke verkleining al mee te nemen in het ontwerpproces."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "Overweeg gebruik te maken van minder of andere [WordPress-plug-ins](https://wordpress.org/plugins/) die ongebruikte css laden op je pagina. Voer [codedekking](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) uit in Chrome DevTools als je plug-ins wilt identificeren die extra css toevoegen. Via de URL van de stylesheet kun je identificeren welk thema/welke plug-in verantwoordelijk is. Ga in de lijst op zoek naar plug-ins met veel stylesheets en veel rood in de codedekking. Een plug-in zou een stylesheet alleen in de wachtrij moeten plaatsen als deze daadwerkelijk wordt gebruikt op de pagina."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "Overweeg gebruik te maken van minder of andere [WordPress-plug-ins](https://wordpress.org/plugins/) die ongebruikt JavaScript laden op je pagina. Voer [codedekking](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) uit in Chrome DevTools als je plug-ins wilt identificeren die extra JS toevoegen. Via de URL van het script kun je identificeren welk thema/welke plug-in verantwoordelijk is. Ga in de lijst op zoek naar plug-ins met veel scripts en veel rood in de codedekking. Een plug-in zou een script alleen in de wachtrij moeten plaatsen als het daadwerkelijk wordt gebruikt op de pagina."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "<PERSON><PERSON> meer over [browsercaching in WordPress](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "Overweeg een [beeldoptimalisatieplug-in voor WordPress](https://wordpress.org/plugins/search/optimize+images/) te gebruiken om je afbeeldingen te comprimeren terwijl de beeldkwaliteit behouden blijft."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "Upload afbeeldingen rechtstreeks via de [mediabibliotheek](https://codex.wordpress.org/Media_Library_Screen) om er zeker van te zijn dat de vereiste afbeeldingsformaten beschikbaar zijn. Voeg de afbeeldingen vervolgens in vanuit de mediabibliotheek. Ook kun je de afbeeldingswidget gebruiken om ervoor te zorgen dat de optimale afbeeldingsformaten worden gebruikt (inclusief voor responsieve breakpoints). Vermijd `Full Size`-afbeeldingen, tenzij de afmetingen geschikt zijn voor waar ze worden geplaatst. [Meer informatie](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)"}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "Je kunt tekstcompressie inschakelen bij je webserverinstellingen."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "Overweeg een [plug-in](https://wordpress.org/plugins/search/convert+webp/) of service te gebruiken die je geüploade afbeeldingen automatisch converteert naar de optimale formaten."}}