{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "<PERSON><PERSON> von <PERSON>nkombinationen können Nutzer schnell einen Bereich der Seite in den Fokus rücken. Damit die Navigation wie vorgesehen funktioniert, muss jede Tastenkombination eindeutig sein. [Weitere Informationen.](https://web.dev/accesskeys/)"}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "`[accesskey]`-Werte sind nicht eindeutig"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]`-Werte sind eindeutig"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON>-`role` unterstützt eine bestimmte Auswahl an `aria-*`-Attributen. Wenn sie jedoch falsch zugeordnet sind, werden die `aria-*`-Attribute ungültig. [Weitere Informationen.](https://web.dev/aria-allowed-attr/)"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "`[aria-*]`-Attribute stimmen nicht mit ihren Rollen überein"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "`[aria-*]`-Attribute entsprechen ihren Rollen"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "<PERSON><PERSON><PERSON> e<PERSON><PERSON>-Rollen sind Attribute erforderlich, die Screenreadern den Zustand des Elements beschreiben. [Weitere Informationen.](https://web.dev/aria-required-attr/)"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]`-Elemente weisen nicht alle erforderlichen `[aria-*]`-Attribute auf"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]`-Elemente verfügen über alle erforderlichen `[aria-*]`-Attribute"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "Einige übergeordnete ARIA-Rollen müssen bestimmte untergeordnete Rollen enthalten, damit sie die beabsichtigten Hilfsfunktionen erfüllen können. [Weitere Informationen.](https://web.dev/aria-required-children/)"}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Den Elementen mit einer ARIA-`[role]`, deren untergeordnete Elemente eine bestimmte `[role]` enthalten müssen, fehlen einige oder alle dieser erforderlichen untergeordneten Elemente."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "Die Elemente mit einer ARIA-`[role]`, deren untergeordnete Elemente eine bestimmte `[role]` enthalten müssen, haben alle erforderlichen untergeordneten Elemente."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "Einige untergeordnete ARIA-<PERSON><PERSON> müssen in bestimmten übergeordneten Rollen enthalten sein, damit sie die beabsichtigten Hilfsfunktionen erfüllen können. [Weitere Informationen.](https://web.dev/aria-required-parent/)"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]`-Elemente sind nicht ihren jeweils erforderlichen übergeordneten Elementen untergeordnet"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]`-Elemente sind ihren jeweils erforderlichen übergeordneten Elementen untergeordnet"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "<PERSON><PERSON><PERSON>-<PERSON><PERSON> müssen gültige Werte angegeben sein, damit sie die beabsichtigten Hilfsfunktionen erfüllen können. [Weitere Informationen.](https://web.dev/aria-roles/)"}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]`-<PERSON><PERSON> sind ungültig"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "`[role]`-<PERSON><PERSON> sind gü<PERSON>ig"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Hilfstechnologien wie Screenreader können ARIA-Attribute mit ungültigen Werten nicht interpretieren. [Weitere Informationen.](https://web.dev/aria-valid-attr-value/)"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "`[aria-*]`-Attribute weisen keine gültigen Werte auf"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "`[aria-*]`-Attribute weisen gültige Werte auf"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "Hilfstechnologien wie Screenreader können ARIA-Attribute mit ungültigen Namen nicht interpretieren. [Weitere Informationen.](https://web.dev/aria-valid-attr/)"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "`[aria-*]`-Attribute sind ungültig oder falsch geschrieben"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "`[aria-*]`-Attribute sind gültig und richtig geschrieben"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "Durch Untertitel sind Audioelemente auch für Gehörlose und Hörgeschädigte nutzbar, da sie wichtige Informationen zur sprechenden Person und zum Inhalt des Gesprächs sowie andere nonverbale Informationen enthalten. [Weitere Informationen.](https://web.dev/audio-caption/)"}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "Den `<audio>`-<PERSON><PERSON><PERSON> fehlt ein `<track>`-<PERSON>ement mit `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "`<audio>`-<PERSON><PERSON><PERSON> enthalten ein `<track>`-Element mit `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Fehlerhafte Elemente"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "Wenn eine Schaltfläche keinen zugänglichen Namen hat, wird sie von Screenreadern als \"Schaltfläche\" angesagt, was sie <PERSON>, die auf Screenreader angewiesen sind, unbrauchbar macht. [Weitere Informationen.](https://web.dev/button-name/)"}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "Schaltflächen haben keinen für Screenreader zugänglichen Namen"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "Die Namen der Schaltflächen sind für Screenreader zugänglich"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "<PERSON><PERSON>er Inhalte umgehen können, die sich wiederholen, sorgt das für eine effizientere Navigation. [Weitere Informationen.](https://web.dev/bypass/)"}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "Die Seite enthält keine Überschrift, keinen Link zum Überspringen und keinen Landmark-Bereich"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "Die Seite enthält eine Überschrift, einen Link zum Überspringen oder einen Landmark-Bereich"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "Text mit geringem Kontrast ist für viele Nutzer schlecht oder gar nicht lesbar. [Weitere Informationen.](https://web.dev/color-contrast/)"}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Das Kontrastverhältnis von Hintergrund- und Vordergrundfarben ist nicht ausreichend."}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "Das Kontrastverhältnis von Hintergrund- und Vordergrundfarben ist ausreichend"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "Wenn Definitionslisten nicht korrekt mit Markup versehen sind, geben Screenreader unter Umständen verwirrende oder falsche Inhalte aus. [Weitere Informationen.](https://web.dev/definition-list/)"}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>`-Elemente enthalten nicht nur richtig angeordnete Gruppen aus `<dt>`- und `<dd>`-El<PERSON><PERSON>, `<script>`- oder `<template>`-Elemente."}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "Alle Gruppen aus `<dt>`- und `<dd>`-Element<PERSON> sowie `<script>`- oder `<template>`-<PERSON><PERSON><PERSON>, die in `<dl>`-Elementen enthalten sind, sind richtig angeordnet."}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "Definitionslistenelemente (`<dt>` und `<dd>`) müssen in ein übergeordnetes `<dl>`-<PERSON><PERSON> eingefasst sein, damit sie von Screenreadern richtig angesagt werden können. [Weitere Informationen.](https://web.dev/dlitem/)"}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "Definitionslistenelemente sind nicht in `<dl>`-Elemente eingefasst"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "Definitionslistenelemente sind in `<dl>`-Elemente eingefasst"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "Anhand des Titels wissen Screenreader-<PERSON><PERSON><PERSON>, worum es auf der Seite geht. Außerdem entscheiden Nutzer von Suchmaschinen auf der Grundlage des Titels, ob eine Seite für ihre Suche relevant ist. [Weitere Informationen.](https://web.dev/document-title/)"}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "Dokument hat kein `<title>`-Element"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "Dokument enthält ein `<title>`-Element"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "Der Wert eines ID-Attributs muss eindeutig sein, damit andere Instanzen nicht von Hilfstechnologien übersehen werden. [Weitere Informationen.](https://web.dev/duplicate-id/)"}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "`[id]`-Attribute auf der Seite sind nicht eindeutig"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "`[id]`-Attribute auf der Seite sind eindeutig"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "<PERSON><PERSON><PERSON> von Screenreadern sind auf Frametitel angewiesen, die die Frameinhalte beschreiben. [Weitere Informationen.](https://web.dev/frame-title/)"}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "`<frame>`- oder `<iframe>`-<PERSON>emente haben keinen Titel"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "`<frame>`- oder `<iframe>`-Elemente verfügen über einen Titel"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "Wenn auf einer Seite kein lang-Attribut angegeben ist, nimmt ein Screenreader an, dass es sich um die Standardsprache handelt, die der Nutzer beim Einrichten des Screenreaders ausgewählt hat. Wenn die Sprache jedoch nicht der Standardsprache entspricht, gibt der Screenreader den Inhalt der Seite möglicherweise falsch aus. [Weitere Informationen.](https://web.dev/html-has-lang/)"}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>`-<PERSON><PERSON> enthält kein `[lang]`-Attribut"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>`-<PERSON>ement hat ein `[lang]`-Attribut"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "Durch Angabe einer gültigen [Sprache gemäß BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) kann der Text von einem Screenreader korrekt wiedergegeben werden. [Weitere Informationen.](https://web.dev/html-lang-valid/)"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>`-<PERSON><PERSON> weist keinen gültigen Wert für sein `[lang]`-Attribut auf."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "Das `<html>`-Element hat einen gültigen Wert für sein `[lang]`-Attribut"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "Informative Elemente sollten einen kurzen, beschreibenden alternativen Text haben. Dekorative Elemente können mit einem leeren ALT-Attribut ignoriert werden. [Weitere Informationen.](https://web.dev/image-alt/)"}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "B<PERSON><PERSON>e haben keine `[alt]`-Attribute"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "Bildelemente verfügen über `[alt]`-Attribute"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "Wenn ein Bild als `<input>`-Schaltfläche verwendet wird, kann die Angabe von alternativem Text Screenreader-Nutzern helfen, den Zweck der Schaltfläche besser zu verstehen. [Weitere Informationen.](https://web.dev/input-image-alt/)"}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">`-Elemente verfügen nicht über `[alt]`-Text"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">`-Elemente verfügen über `[alt]`-Text"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "Durch Labels wird gewährleistet, dass Steuerelemente für Formulare von Hilfstechnologien wie Screenreadern richtig angesagt werden. [Weitere Informationen.](https://web.dev/label/)"}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "Die Formularelemente sind nicht mit Labels verknüpft"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "Formularelemente sind mit Labels verknüpft"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "Eine für Layoutzwecke verwendete Tabelle sollte keine Datenelemente wie \"th\"- bzw. \"caption\"-Elemente oder das \"summary\"-Attribut enthalten, weil dies für Nutzer von Screenreadern verwirrend sein kann. [Weitere Informationen.](https://web.dev/layout-table/)"}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "`<table>`-Präsentationselemente verwenden `<th>`, `<caption>` oder das `[summary]`-Attribut."}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "`<table>`-Präsentationselemente enthalten keine `<th>`-, `<caption>`- oder `[summary]`-Attribute."}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "Linktext, der leicht erkennbar, eindeutig und fokussierbar ist, verbessert die Navigation für Screenreader-Nutzer. Dies gilt auch für alternativen Text für Bilder, die als Links verwendet werden. [Weitere Informationen.](https://web.dev/link-name/)"}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "Links haben keinen leicht erkennbaren Namen"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "Links haben einen leicht erkennbaren Namen"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "Screenreader sagen Listen auf bestimmte Art und Weise an. Eine korrekte Listenstruktur gewährleistet, dass der Screenreader sie richtig ausgibt. [Weitere Informationen.](https://web.dev/list/)"}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "Listen enthalten nicht nur `<li>`-Elemente und Elemente zur Skriptunterstützung (`<script>` sowie `<template>`)."}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "Listen enthalten nur `<li>`-Elemente und Elemente zur Skriptunterstützung (`<script>` sowie `<template>`)."}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "Listenelemente (`<li>`) müssen sich in einem übergeordneten `<ul>`- oder `<ol>`-<PERSON><PERSON> befinden, damit Screenreader sie richtig ansagen können. [Weitere Informationen.](https://web.dev/listitem/)"}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "Listenelemente (`<li>`) befinden sich nicht in übergeordneten `<ul>`- oder `<ol>`-Elementen."}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "Listenelemente (`<li>`) befinden sich in übergeordneten `<ul>`- oder `<ol>`-Elementen"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "<PERSON>utzer rechnen nicht damit, dass eine Seite automatisch aktualisiert wird. Außerdem wird dadurch der Fokus wieder auf den Seitenanfang verschoben. Das kann für den Nutzer frustrierend oder verwirrend sein. [Weitere Informationen.](https://web.dev/meta-refresh/)"}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Im Dokument wird `<meta http-equiv=\"refresh\">` verwendet"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "Dieses Dokument verwendet `<meta http-equiv=\"refresh\">` nicht"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "<PERSON>n Sie die Zoomfunktion deaktivieren, haben <PERSON> mit eingeschränktem Sehvermögen, die auf die Bildschirmvergrößerung angewiesen sind, Probleme, den Inhalt einer Webseite zu sehen. [Weitere Informationen.](https://web.dev/meta-viewport/)"}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` wird im `<meta name=\"viewport\">`-Element verwendet oder das `[maximum-scale]`-Attribut ist kleiner als 5."}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` wird nicht im `<meta name=\"viewport\">`-Element verwendet und das `[maximum-scale]`-Attribut ist nicht kleiner als 5."}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "Screenreader können lediglich Textinhalte interpretieren. <PERSON><PERSON> `<object>`-Elementen Alt-Text hinzufügen, können Screenreader-<PERSON><PERSON>er besser verstehen, was diese Elemente darstellen. [Weitere Informationen.](https://web.dev/object-alt/)"}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>`-Elemente verfügen nicht über `[alt]`-Text"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "`<object>`-Elemente verfügen über `[alt]`-Text"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "Ein Wert größer als 0 impliziert eine explizite Navigationsanordnung. Das ist zwar technisch möglich; aber für Nutzer, die auf Hilfstechnologien angewiesen sind, ist dies häufig frustrierend. [Weitere Informationen.](https://web.dev/tabindex/)"}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "Der `[tabindex]`-<PERSON>rt einiger Elemente ist größer als 0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "Kein Element hat einen `[tabindex]`-Wert größer als 0"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "Screenreader bieten Funktionen, die die Navigation in Tabellen vereinfachen. <PERSON><PERSON> <PERSON><PERSON>, dass `<td>`<PERSON><PERSON><PERSON><PERSON>, die das `[headers]`-Attribut verwenden, nur auf andere Zellen in derselben Tabelle verweisen, kann dies für Screenreader-Nutzer hilfreich sein. [Weitere Informationen.](https://web.dev/td-headers-attr/)"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> in einem \"`<table>`\"-<PERSON><PERSON>, die das Attribut \"`[headers]`\" enthalten, verweisen auf ein \"`id`\"-El<PERSON>, das sich nicht in derselben Tabelle befindet."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "<PERSON><PERSON><PERSON> in einem \"`<table>`\"-<PERSON><PERSON>, die das Attribut \"`[headers]`\" enthalten, verweisen auf Zellen in derselben Tabelle."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "Screenreader bieten Funktionen, die die Navigation in Tabellen vereinfachen. <PERSON><PERSON> <PERSON><PERSON><PERSON>rgen, dass Tabellenüberschriften immer auf eine Auswahl von <PERSON> verwei<PERSON>, kann dies für Screenreader-Nutzer hilfreich sein. [Weitere Informationen.](https://web.dev/th-has-data-cells/)"}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "<PERSON>ür `<th>`-Elemente und Elemente mit `[role=\"columnheader\"/\"rowheader\"]` sind keine Datenzellen vorhanden, die sie beschreiben."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "<PERSON>ür `<th>`-Elemente und Elemente mit `[role=\"columnheader\"/\"rowheader\"]` sind Datenzellen vorhanden, die sie beschreiben."}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "Durch Angabe einer gültigen [Sprache gemäß BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) in Elementen kann der Text von einem Screenreader korrekt ausgesprochen werden. [Weitere Informationen.](https://web.dev/valid-lang/)"}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "`[lang]`-Attribute weisen keinen gültigen Wert auf"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "`[lang]`-Attribute weisen einen gültigen Wert auf"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "<PERSON>n ein Video Untertitel enthält, können gehörlose und hörgeschädigte Nutzer die Informationen im Video besser verstehen. [Weitere Informationen.](https://web.dev/video-caption/)"}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "`<video>`-<PERSON><PERSON><PERSON> enthalten kein `<track>`-Element mit `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "`<video>`-<PERSON><PERSON><PERSON> enthalten ein `<track>`-<PERSON>ement mit `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "Audiobeschreibungen enthalten relevante Informationen für Videos, die aus dem Dialog nicht ersichtlich sind, z. B. Gesichtsausdrücke und Schauplätze. [Weitere Informationen.](https://web.dev/video-description/)"}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "`<video>`-<PERSON><PERSON><PERSON> enthalten kein `<track>`-Element mit `[kind=\"description\"]`."}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "`<video>`-<PERSON><PERSON><PERSON> enthalten ein `<track>`-<PERSON>ement mit `[kind=\"description\"]`"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "Definieren Sie ein `apple-touch-icon` für eine ideale Darstellung unter iOS, wenn Nutzer die progressive Web-App dem Startbildschirm hinzufügen. Es muss auf eine nicht transparente, quadratische PNG-Datei mit 192 px (oder 180 px) verweisen. [Weitere Informationen.](https://web.dev/apple-touch-icon/)"}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "Hat kein gültiges `apple-touch-icon`"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "`apple-touch-icon-precomposed` ist veraltet; `apple-touch-icon` wird bevor<PERSON><PERSON>."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "Enthält ein gültiges `apple-touch-icon`"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chrome-Erweiterungen haben die Ladegeschwindigkeit dieser Seite beeinträchtigt. Versuchen Sie, die Seite im Inkognito-Modus oder mit einem Chrome-Profil ohne Erweiterungen zu überprüfen."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "Skriptauswertung"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "CPU-Zeit insgesamt"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "Versuchen Sie, die Zeit für das Parsen, Kompilieren und Ausführen von JavaScript zu reduzieren. Die Bereitstellung kleinerer JS-Nutzlasten kann dabei helfen. [Weitere Informationen.](https://web.dev/bootup-time)"}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "Ausführungszeit von JavaScript reduzieren"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "JavaScript-Ausführungszeit"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Große GIF-Dateien sind nur bedingt für die Auslieferung animierter Inhalte geeignet. Sie können stattdessen MPEG4- oder WebM-Videos für Animationen und PNG oder WebP für statische Bilder verwenden und so die Netzwerk-Datenmenge reduzieren. [Weitere Informationen](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Videoformate für animierte Inhalte verwenden"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "<PERSON>n <PERSON> nicht sichtbare und versteckte Bilder erst laden lassen, nachdem alle wichtigen Ressourcen geladen wurden, können Sie die Zeit bis zur Interaktivität reduzieren. [Weitere Informationen.](https://web.dev/offscreen-images)"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Nicht sichtbare Bilder aufschieben"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Ressourcen blockieren den First Paint Ihrer Seite. Versuchen Sie, wichtiges JS und wichtige CSS inline anzugeben und alle nicht kritischen JS und Stile aufzuschieben. [Weitere Informationen.](https://web.dev/render-blocking-resources)"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Ressourcen beseitigen, die das Rendering blockieren"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Große Netzwerknutzlasten kosten Nutzer bares Geld und hängen eng mit langen Ladezeiten zusammen. [Weitere Informationen.](https://web.dev/total-byte-weight)"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Die Gesamtgröße war {totalBytes, number, bytes} KB"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Sehr große Netzwerknutzlasten vermeiden"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Vermeidet sehr große Netzwerknutzlasten"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "Durch die Komprimierung von CSS-Dateien können Netzwerknutzlasten verkleinert werden. [Weitere Informationen.](https://web.dev/unminified-css)"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "CSS komprimieren"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Durch die Komprimierung von JavaScript-Dateien können Nutzlastgrößen und die Zeit zum Parsen von Skripts reduziert werden. [Weitere Informationen.](https://web.dev/unminified-javascript)"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "JavaScript komprimieren"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Sie können ungültige Regeln aus Stylesheets entfernen und das Laden von CSS aufschieben, die nicht für ohne Scrollen sichtbare Inhalte verwendet werden, um unnötigen Datenverbrauch durch Netzwerkaktivität zu vermeiden. [Weitere Informationen.](https://web.dev/unused-css-rules)"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Nicht verwendete CSS entfernen"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Entfernen Si<PERSON> nicht verwendetes JavaScript, um die Datenmenge bei Netzwerkaktivitäten zu reduzieren."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Nicht genutztes JavaScript entfernen"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Eine lange Lebensdauer des Cache kann wiederholte Besuche Ihrer Seite beschleunigen. [Weitere Informationen.](https://web.dev/uses-long-cache-ttl)"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 Ressource gefunden}other{# Ressourcen gefunden}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Statische Inhalte mit einer effizienten Cache-Richtlinie bereitstellen"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Verwendet eine effiziente Cache-Richtlinie für statische Inhalte"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Optimierte Bilder werden schneller geladen und verbrauchen weniger mobile Daten. [Weitere Informationen.](https://web.dev/uses-optimized-images)"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Bilder effizient codieren"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "<PERSON><PERSON><PERSON> Bilder bereit, die eine angemessene Größe haben, um mobile Daten zu sparen und die Ladezeit zu verbessern. [Weitere Informationen.](https://web.dev/uses-responsive-images)"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Bilder rich<PERSON><PERSON>en"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Textbasierte Ressourcen sollten mit Komprimierung (gzip, Deflate oder Brotli) ausgeliefert werden, um die Datenmenge im Netzwerk insgesamt zu minimieren. [Weitere Informationen.](https://web.dev/uses-text-compression)"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Textkomprimierung aktivieren"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "Bildformate wie JPEG 2000, JPEG XR und WebP bieten oft eine bessere Komprimierung als PNG oder JPEG, was schnellere Downloads und einen geringeren Datenverbrauch ermöglicht. [Weitere Informationen.](https://web.dev/uses-webp-images)"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "Bilder in modernen Formaten bereitstellen"}, "lighthouse-core/audits/content-width.js | description": {"message": "Wenn die Breite Ihrer App-Inhalte nicht mit der des Darstellungsbereichs übereinstimmt, ist Ihre App möglicherweise nicht für Bildschirme von Mobilgeräten optimiert. [Weitere Informationen.](https://web.dev/content-width)"}, "lighthouse-core/audits/content-width.js | explanation": {"message": "Die Größe des Darstellungsbereichs von {innerWidth} Pixeln stimmt nicht mit der Fenstergröße von {outerWidth} Pixeln überein."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "Inhalt hat nicht die richtige Größe für den Darstellungsbereich"}, "lighthouse-core/audits/content-width.js | title": {"message": "Inhalt hat die richtige Größe für den Darstellungsbereich"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "In den unten aufgeführten Ketten kritischer Anfragen können Si<PERSON>, welche Ressourcen mit einer hohen Priorität geladen werden. Versuchen Sie, die Ketten zu verkürzen, die Downloadgröße von Ressourcen zu reduzieren oder das Herunterladen unnötiger Ressourcen aufzuschieben, um den Seitenaufbau zu beschleunigen. [Weitere Informationen.](https://web.dev/critical-request-chains)"}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 Kette gefunden}other{# Ketten gefunden}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "Tiefe kritischer Anforderungen minimieren"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "Veraltet/Warnung"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/deprecations.js | description": {"message": "Veraltete APIs werden aus dem Browser entfernt. [Weitere Informationen.](https://web.dev/deprecations)"}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 Warnung gefunden}other{# Warnungen gefunden}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "Verwendet veraltete APIs"}, "lighthouse-core/audits/deprecations.js | title": {"message": "Vermeidet veraltete APIs"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "Application Cache ist veraltet. [Weitere Informationen.](https://web.dev/appcache-manifest)"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "\"{AppCacheManifest}\" gefunden"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "Verwendet Application Cache"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "Vermeidet Application Cache"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "<PERSON>n <PERSON><PERSON> einen DOCTYPE festlegen, hindern <PERSON><PERSON> Browser <PERSON>, zum Quirks-<PERSON><PERSON> zu wechseln. [Weitere Informationen.](https://web.dev/doctype)"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "DOCTYPE-Name muss dieser String sein (in Kleinbuchstaben): `html`"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Dokument muss einen DOCTYPE enthalten"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "PublicId sollte ein leerer String sein"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "SystemId sollte ein leerer String sein"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Seite verfügt nicht über den HTML-DOCTYPE und startet daher den Quirks-Modus"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "Seite verfügt über den HTML-DOCTYPE"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "Element"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistik"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Wert"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "Laut der Empfehlung von Browserentwicklern sollten Seiten nicht mehr als ungefähr 1.500 DOM-Elemente enthalten. Die ideale Strukturtiefe liegt bei unter 32 Elementen und weniger als 60 unter- und übergeordneten Elementen. Ein großes DOM kann zu hohem Speicherverbrauch, langwierigen [Stilberechnungen](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) und kostspieligen [dynamischen Umbrüchen im Layout](https://developers.google.com/speed/articles/reflow) führen. [Weitere Informationen.](https://web.dev/dom-size)"}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 Element}other{# Elemente}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Übermäßige DOM-Größe vermeiden"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Maximale DOM-Tiefe"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "DOM-Elemente insgesamt"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Maximale Anzahl von untergeordneten Elementen"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "Vermeidet eine übermäßige DOM-Größe"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "Fügen Sie beliebigen externen Links `rel=\"noopener\"` oder `rel=\"noreferrer\"` hinzu, um die Leistung zu verbessern und Sicherheitslücken zu vermeiden. [Weitere Informationen.](https://web.dev/external-anchors-use-rel-noopener)"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "Links zu ursprungsübergreifenden Zielen sind unsicher"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "Links zu ursprungsübergreifenden Zielen sind sicher"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "<PERSON>iel für Anker ({anchorHTML}) kann nicht bestimmt werden. <PERSON><PERSON> dies nicht als Hyperlink genutzt wird, sollten Sie target=_blank entfernen."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "<PERSON>utzer sind misstrauisch oder verwirrt, wenn Websites den Standort ohne Begründung anfordern. Versuchen Sie stattdessen, die Anforderung mit einer Nutzeraktion zu verbinden. [Weitere Informationen.](https://web.dev/geolocation-on-start)"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Fordert die Berechtigung zur Standortbestimmung beim Seitenaufbau an"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "<PERSON><PERSON> während des Seitenaufbaus keine Berechtigung zur Standortbestimmung an"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Version"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "Alle Front-End-JavaScript-Bibliotheken auf der Seite wurden erkannt. [Weitere Informationen.](https://web.dev/js-libraries)"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "JavaScript-Bibliotheken erkannt"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "<PERSON><PERSON><PERSON> mit langsamen Verbindungen können externe Skripts, die dynamisch über `document.write()` eingefügt werden, den Seitenaufbau um einige Sekunden verzögern. [Weitere Informationen.](https://web.dev/no-document-write)"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Verwendet `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "Verwendet kein `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "Höchster Schweregrad"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "Bibliotheksversion"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "Anzahl der Sicherheitslücken"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "<PERSON><PERSON> Skripts von Drittanbietern können bekannte Sicherheitslücken enthalten, die von Angreifern leicht zu identifizieren und zu missbrauchen sind. [Weitere Informationen.](https://web.dev/no-vulnerable-libraries)"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{1 Sicherheitslücke erkannt}other{# Sicherheitslücken erkannt}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "Enthält Front-End-JavaScript-Bibliotheken mit bekannten Sicherheitslücken"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "Hoch"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "Vermeidet Front-End-JavaScript-Bibliotheken mit bekannten Sicherheitslücken"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "<PERSON>utzer sind misstrauisch oder verwirrt, wenn Websites die Berechtigung zum Senden von Benachrichtigungen ohne Begründung anfordern. Versuchen Sie stattdessen, die Anforderung mit Touch-Gesten zu verbinden. [Weitere Informationen.](https://web.dev/notification-on-start)"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Fordert die Benachrichtigungsberechtigung beim Seitenaufbau an"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "<PERSON><PERSON> während des Seitenaufbaus keine Benachrichtigungsberechtigung an"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "Fehlerhafte Elemente"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "<PERSON> Einfügen von Passwörtern sollte entsprechend guten Sicherheitsrichtlinien zulässig sein. [Weitere Informationen.](https://web.dev/password-inputs-can-be-pasted-into)"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "<PERSON><PERSON>t Nutzer daran, Inhalte in Passwortfelder einzufügen"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "Erlaubt Nutzern, Inhalte in Passwortfelder einzufügen"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokoll"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 bietet gegenüber HTTP/1.1 viele Vorteile, wie z. B. Binärkopfzeilen, Multiplexing und Server-Push. [Weitere Informationen.](https://web.dev/uses-http2)"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{Ressourcen für 1 Anfrage nicht über HTTP/2 bereitgestellt}other{Ressourcen für # Anfragen nicht über HTTP/2 bereitgestellt}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "Verwendet HTTP/2 nicht für alle Ressourcen"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "Verwendet HTTP/2 für eigene Ressourcen"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "<PERSON>n Sie Ihre Ereignis-Listener für Tipp- und Mausradbewegungen als `passive` markieren, können Sie damit die Scrollleistung Ihrer Seite verbessern. [Weitere Informationen.](https://web.dev/uses-passive-event-listeners)"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Verwendet keine passiven Listener zur Verbesserung der Scrollleistung"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Verwendet passive Listener zur Verbesserung der Scrollleistung"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "Beschreibung"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "In der Konsole protokollierte Fehler weisen auf ungelöste Probleme hin. Sie können durch fehlgeschlagene Netzwerkanfragen und andere Browser-Probleme verursacht werden. [Weitere Informationen](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "<PERSON><PERSON> wurden Browserfehler in der Konsole protokolliert"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "<PERSON><PERSON> wurden keine Brows<PERSON><PERSON>hler in der Konsole protokolliert"}, "lighthouse-core/audits/font-display.js | description": {"message": "Verwenden Sie die CSS-Funktion font-display, damit der Text für Nutzer sichtbar ist, während Webfonts geladen werden. [Weitere Informationen.](https://web.dev/font-display)"}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "<PERSON><PERSON><PERSON>, dass der Text während der Webfont-Ladevorgänge sichtbar bleibt"}, "lighthouse-core/audits/font-display.js | title": {"message": "Der gesamte Text bleibt während der Webfont-Ladevorgänge sichtbar"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "Für die folgende URL konnte Lighthouse den Schriftart-Anzeigewert nicht automatisch prüfen: {fontURL}."}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "Seitenverh<PERSON><PERSON><PERSON> (Original)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Seitenverhältnis (angezeigt)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "Die Bildgröße sollte dem natürlichen Seitenverhältnis entsprechen. [Weitere Informationen.](https://web.dev/image-aspect-ratio)"}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> mit einem falschen Seitenverhältnis an"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "<PERSON><PERSON><PERSON> mit einem korrekten Seitenverhältnis an"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "Ungültige Informationen zur Bildgröße: {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "Browser können Nutzer direkt dazu auffordern, Ihre Web-App zum Startbildschirm hinzuzufügen. Das kann zu mehr Engagement führen. [Weitere Informationen.](https://web.dev/installable-manifest)"}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "Das Manifest der Web-App erfüllt die Anforderungen an die Installierbarkeit nicht"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "Das Manifest der Web-App erfüllt die Anforderungen an die Installierbarkeit"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "Unsichere URL"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "Alle Websites sollten durch HTTPS geschützt werden – selbst wenn sie keine vertraulichen Daten enthalten. HTTPS verhindert, dass andere Personen die Website manipulieren oder die Kommunikation zwischen Ihrer App und Ihren Nutzern mitverfolgen können. Dieses Protokoll ist eine Voraussetzung für HTTP/2 sowie für viele neue Webplattform-APIs. [Weitere Informationen.](https://web.dev/is-on-https)"}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 unsichere Anfrage gefunden}other{# unsichere Anfragen gefunden}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "Verwendet nicht HTTPS"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "Verwendet HTTPS"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "Ein schneller Seitenaufbau über ein Mobilfunknetz sorgt dafür, dass die Seite für Nutzer auf Mobilgeräten angenehm zu bedienen ist. [Weitere Informationen.](https://web.dev/load-fast-enough-for-pwa)"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "Interaktiv nach {timeInMs, number, seconds} s"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "Im simulierten Mobilfunknetz interaktiv nach {timeInMs, number, seconds} s"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "Ihre Seite lädt zu langsam und es dauert länger als 10 Sekunden, bis sie interaktiv ist. Im Abschnitt \"Leistung\" finden Sie Empfehlungen und Diagnosedaten, die Ihnen helfen können, Ihre Seite zu optimieren."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "Seitenaufbau in Mobilfunknetzen ist nicht schnell genug"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "Seitenaufbau in Mobilfunknetzen ist schnell genug"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "Versuchen Sie, die Zeit für das Parsen, Kompilieren und Ausführen von JavaScript zu reduzieren. Die Bereitstellung kleinerer JS-Nutzlasten kann dabei helfen. [Weitere Informationen](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Aufwand für Hauptthread minimieren"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "Minimiert den Aufwand für den Hauptthread"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "Damit Sie möglichst viele Nutzer erreichen können, sollte Ihre Website mit allen gängigen Browsern kompatibel sein. [Weitere Informationen.](https://web.dev/pwa-cross-browser)"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "Website funktioniert auf verschiedenen Browsern"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Für die einzelnen Seiten sollten Deeplinks erstellt werden können. Achten Si<PERSON>uf, dass die entsprechenden URLs eindeutig sind, sodass sich die Seiten in sozialen Netzwerken leichter teilen lassen. [Weitere Informationen.](https://web.dev/pwa-each-page-has-url)"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Jede Seite hat eine URL"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "Übergänge sollten sich auch bei einer langsamen Netzwerkverbindung schnell anfühlen. Dies trägt beim <PERSON>utzer erheblich zur wahrgenommenen Leistung bei. [Weitere Informationen.](https://web.dev/pwa-page-transitions)"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "Seitenübergänge vermitteln nicht das Gefühl von übermäßigen Ladezeiten"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "Die geschätzte Eingabelatenz ist eine Schätzung dessen, wie viele Millisekunden Ihre App benötigt, um während des 5-s-Fensters mit der stärksten Auslastung beim Seitenaufbau auf Nutzereingaben zu reagieren. Wenn die Latenz bei Ihnen über 50 ms beträgt, empfinden Nutzer Ihre App möglicherweise als langsam. [Weitere Informationen.](https://web.dev/estimated-input-latency)"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "Geschätzte Eingabelatenz"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "First Contentful Paint gibt an, wann der erste Text oder das erste Bild gezeichnet wird. [Weitere Informationen.](https://web.dev/first-contentful-paint)"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "<PERSON>rst<PERSON> Inhalte gezeichnet"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "Der Messwert \"Erster CPU-Leerlauf\" gibt an, wann die Aktivität des Hauptthreads der Seite das erste Mal gering genug ist, um Eingaben zu verarbeiten.  [Weitere Informationen.](https://web.dev/first-cpu-idle)"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "Erster CPU-<PERSON>rlau<PERSON>"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "\"Inhalte weitgehend gezeichnet\" gibt an, wann die Hauptinhalte einer Seite sichtbar sind. [Weitere Informationen.](https://web.dev/first-meaningful-paint)"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "Inhalte weitgehend gezeichnet"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "Die Zeit bis Interaktivität entspricht der Zeit, die vergeht, bis die Seite vollständig interaktiv ist. [Weitere Informationen.](https://web.dev/interactive)"}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "Zeit bis Interaktivität"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "Der maximale potenzielle First Input Delay, der bei Ihren Nutzern auftreten kann, entspricht der Dauer der längsten Aufgabe in Millisekunden. [Weitere Informationen.](https://developers.google.com/web/updates/2018/05/first-input-delay)"}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "Maximaler potenzieller First Input Delay"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "Der Geschwindigkeitsindex gibt an, wie schnell die Inhalte einer Seite sichtbar dargestellt werden. [Weitere Informationen.](https://web.dev/speed-index)"}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "Geschwindigkeitsindex"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "Summe aller Zeiträume (in Millisekunden) zwischen FCP und Zeit bis Interaktivität, wenn die Aufgabendauer 50 ms überschreitet."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "Gesamtdauer der Blockierung"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "Die Netzwerk-Umlaufzeit (RTT, Round Trip Time) hat großen Einfluss auf die Leistung. Wenn die RTT zu einem Ursprung hoch ausfällt, weist dies darauf hin, dass die Leistung mit Servern verbessert werden kann, die sich näher beim <PERSON> befinden. [Weitere Informationen.](https://hpbn.co/primer-on-latency-and-bandwidth/)"}, "lighthouse-core/audits/network-rtt.js | title": {"message": "Netzwerk-Umlaufzeit"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "Serverlatenzen können die Leistung im Web beeinträchtigen. Wenn die Serverlatenz eines Ursprungs hoch ist, weist dies darauf hin, dass der Server überlastet ist oder eine schlechte Back-End-Leistung bietet. [Weitere Informationen.](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)"}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "Server-Back-End-<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "Mithilfe eines Service Workers kann Ihre Web-App auch bei schlechten Netzwerkbedingungen zuverlässig funktionieren. [Weitere Informationen.](https://web.dev/offline-start-url)"}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url` reagiert im Offlinemodus nicht mit dem HTTP-Statuscode 200"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url` reagiert im Offlinemodus mit dem HTTP-Status 200"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "Lighthouse konnte die `start_url` nicht im Manifest abrufen. Da<PERSON> wurde angenommen, dass es sich bei der `start_url` um die URL des Dokuments handelt. Fehlermeldung: \"{manifestWarning}\"."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "<PERSON><PERSON> dem <PERSON>"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "Die Anzahl und Größe der Netzwerkanfragen sollten unter den Zielvorgaben des Leistungsbudgets liegen. [Weitere Informationen.](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 <PERSON><PERSON><PERSON>}other{# Anfragen}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "Leistungsbudget"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "Wenn Sie HTTPS bereits eingerichtet haben, sollten Sie den gesamten HTTP-Traffic auf HTTPS weiterleiten, damit für alle Ihre Nutzer sichere Webfunktionen aktiviert sind. [Weitere Informationen.](https://web.dev/redirects-http)"}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "HTTP-Traffic wird nicht auf HTTPS weitergeleitet"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "HTTP-Traffic wird auf HTTPS weitergeleitet"}, "lighthouse-core/audits/redirects.js | description": {"message": "Weiterleitungen führen zu zusätzlichen Verzögerungen, bevor die Seite geladen werden kann. [Weitere Informationen.](https://web.dev/redirects)"}, "lighthouse-core/audits/redirects.js | title": {"message": "Mehrere Weiterleitungen auf die Seite vermeiden"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "Fügen Sie zum Einrichten von Budgets für die Anzahl und Größe von Seitenressourcen eine budget.json-Datei hinzu. [Weitere Informationen.](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 Anfrage • {byteCount, number, bytes} KB}other{# Anfragen • {byteCount, number, bytes} KB}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "Halten Sie die Anfrageanzahl niedrig und die Übertragungsgröße gering"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "Über kanonische Links wird angegeben, welche URL in den Suchergebnissen angezeigt werden soll. [Weitere Informationen.](https://web.dev/canonical)"}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "<PERSON><PERSON><PERSON> in Konflikt stehende URLs ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "Verweist auf eine andere Domain ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "Ungültige URL ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Verweist auf einen anderen `hreflang`-Speicherort ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "Relative URL ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "Verweist auf die Stamm-URL (die Startseite) der Domain statt auf eine identische Inhaltsseite"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "Dokument enthält kein gültiges `rel=canonical`-Element"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "Dokument enthält ein gültiges `rel=canonical`-Element"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "Schriftgr<PERSON><PERSON><PERSON> von weniger als 12 px sind zu klein und deshalb nicht gut lesbar, sodass <PERSON><PERSON><PERSON> von Mobilgeräten den Text per Fingerbewegung heranzoomen müssen. Mindestens 60 % des Texts auf der Seite sollten deshalb eine Schriftgröße von mindestens 12 px haben. [Weitere Informationen.](https://web.dev/font-size)"}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} gut lesbarer Text"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "Text ist nicht lesbar, weil kein Meta-Tag für den Darstellungsbereich vorhanden ist, das für Bildschirme von Mobilgeräten optimiert ist."}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "{decimalProportion, number, extendedPercent} des Texts ist zu klein (auf Grundlage einer Stichprobengröße von {decimalProportionVisited, number, extendedPercent})."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "Dokument enthält keine gut lesbaren Schriftgrößen"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "Dokument enthält gut lesbare Schriftgrößen"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "<PERSON><PERSON> von \"hreflang\"-<PERSON><PERSON> können Suchmaschinen ermitteln, welche Version einer Seite sie in den Suchergebnissen für eine bestimmte Sprache oder Region anzeigen sollen. [Weitere Informationen.](https://web.dev/hreflang)"}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "Dokument enthält kein gültiges `hreflang`-Element"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "Dokument enthält ein gültiges `hreflang`-Element"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "Seiten mit ungültigen HTTP-Statuscodes werden möglicherweise nicht richtig indexiert. [Weitere Informationen.](https://web.dev/http-status-code)"}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "Seite hat keinen gültigen HTTP-Statuscode"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "Seite hat einen gültigen HTTP-Statuscode"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "Suchmaschinen können Ihre Seiten nicht in die Suchergebnisse aufnehmen, wenn sie nicht dazu berechtigt sind, sie zu crawlen. [Weitere Informationen.](https://web.dev/is-crawable)"}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "Seite ist von Indexierung ausgeschlossen"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "Seite ist nicht von Indexierung ausgeschlossen"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "Mit beschreibendem Linktext können Suchmaschinen Ihre Inhalte besser verstehen. [Weitere Informationen.](https://web.dev/link-text)"}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 Link gefunden}other{# Links gefunden}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "Links enthalten keinen beschreibenden Text"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "Links haben beschreibenden Text"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "Sie können das [Testtool für strukturierte Daten](https://search.google.com/structured-data/testing-tool/) und den [Lint für strukturierte Daten](http://linter.structured-data.org/) ausführen, um strukturierte Daten zu validieren. [Weitere Informationen.](https://web.dev/structured-data)"}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "Strukturierte Daten sind gültig"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "Meta-Beschreibungen können in die Suchergebnisse aufgenommen werden, um die Seiteninhalte kurz zusammenzufassen. [Weitere Informationen.](https://web.dev/meta-description)"}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "Beschreibungstext ist leer."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "Dokument enthält keine Meta-Beschreibung"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "Dokument enthält eine Meta-Beschreibung"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "Suchmaschinen können keine Plug-in-Inhalte indexieren, und auf vielen Geräten werden Plug-ins eingeschränkt oder nicht unterstützt. [Weitere Informationen.](https://web.dev/plugins)"}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "Dokument verwendet Plug-ins"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "Dokument verwendet keine Plug-ins"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "<PERSON>n Ihre robots.txt-<PERSON><PERSON> feh<PERSON>haft ist, können Crawler möglicherweise nicht nachvollziehen, wie Ihre Website gecrawlt oder indexiert werden soll. [Weitere Informationen.](https://web.dev/robots-txt)"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "robots.txt-Anfrage hat diesen HTTP-Status zurückgegeben: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 Fehler gefunden}other{# Fehler gefunden}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse konnte keine robots.txt-<PERSON><PERSON> her<PERSON>n"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt ist ungültig"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "robots.txt ist gültig"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "Interaktive Elemente wie Schaltflächen und Links sollten groß genug sein (48 x 48 px) und genügend Platz um sich herum haben, um einfach angetippt werden zu können. Dabei sollten sie sich aber nicht mit anderen Elementen überschneiden. [Weitere Informationen.](https://web.dev/tap-targets)"}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} der Tippziele haben eine passende Größe"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Die Tippziele sind zu klein, weil kein Meta-Tag für den Darstellungsbereich vorhanden ist, das für Bildschirme von Mobilgeräten optimiert ist"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> von Tippzielen ist nicht richtig eingestellt"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Sich überschneidendes Ziel"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> von Tippzielen ist richtig eingestellt"}, "lighthouse-core/audits/service-worker.js | description": {"message": "Der Service Worker ermöglicht es Ihrer App, viele der Funktionen von progressiven Web-Apps zu nutzen, beispielswei<PERSON> den Offlinemodus, das Hinzufügen zum Startbildschirm und Push-Benachrichtigungen. [Weitere Informationen.](https://web.dev/service-worker)"}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "Diese Seite wird von einem Service Worker kontrolliert. Es wurde jedoch keine `start_url` gefunden, weil das Manifest nicht als gültige JSON-Datei geparst werden konnte."}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "Diese Seite wird zwar von einem Service Worker kontrolliert, die `start_url` ({startUrl}) liegt jedoch nicht in dessen Zuständigkeitsbereich ({scopeUrl})"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "Diese Seite wird zwar von einem Service Worker kontrolliert, es wurde jedoch keine `start_url` gefunden, da kein Manifest abgerufen wurde."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "Dieser Ursprung verfügt über mindestens einen Service Worker. Die Seite ({pageUrl}) liegt jedoch nicht in dessen Zuständigkeitsbereich."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "<PERSON>s wurde kein Service Worker erkannt, der die Seite und `start_url` kontrolliert"}, "lighthouse-core/audits/service-worker.js | title": {"message": "Es wurde ein Service Worker erkannt, der die Seite und `start_url` kontrolliert."}, "lighthouse-core/audits/splash-screen.js | description": {"message": "Wenn Sie Ihren Startbildschirm gemäß dem Design Ihrer App gestalten, vermitteln Sie den Nutzern schon beim Ladevorgang einen hochwertigen Eindruck. [Weitere Informationen.](https://web.dev/splash-screen)"}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "Nicht für einen benutzerdefinierten Startbildschirm konfiguriert"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "Konfiguriert für einen benutzerdefinierten Startbildschirm"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "Die Adressleiste des Browsers kann an das Design Ihrer Website angepasst werden. [Weitere Informationen.](https://web.dev/themed-omnibox)"}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "Legt keine Designfarbe für die Adressleiste fest."}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "Legt eine Designfarbe für die Adressleiste fest."}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "Dauer der Blockierung des Hauptthreads"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "Dritt<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "Code von Drittanbietern kann die Ladegeschwindigkeit erheblich beeinträchtigen. Beschränken Sie die Zahl redundanter Drittanbieter und versuchen Sie, solchen Code erst nachträglich zu laden. [Weitere Informationen.](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)"}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "<PERSON> von Drittan<PERSON>tern hat den Hauptthread {timeInMs, number, milliseconds} ms lang blockiert"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "Die Auswirkungen von Drittanbieter-Code minimieren"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "Nutzung von Drittanbieter-Code"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "TTFB (Time To First Byte) erkennt den Zeitpunkt, zu dem Ihr Server eine Antwort sendet. [Weitere Informationen.](https://web.dev/time-to-first-byte)"}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "Stammdokument brauchte {timeInMs, number, milliseconds} ms"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "Serverantwortzeiten reduzieren (TTFB)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "Serverantwortzeiten sind niedrig (TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "Begin<PERSON>"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/user-timings.js | description": {"message": "Sie können die User Timing API in Ihre App integrieren. Damit lässt sich die Leistung Ihrer App während wichtiger Nutzerinteraktionen in der Praxis messen. [Weitere Informationen.](https://web.dev/user-timings)"}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 Nutzertiming}other{# Nutzertimings}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "Markierungen und Messungen für das Nutzertiming"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "<PERSON><PERSON><PERSON> {security<PERSON><PERSON><PERSON>} wurde ein <link> zur Vorverbindung gefunden, der jedoch vom Browser nicht verwendet wurde. Überprüfen Si<PERSON>, ob das `crossorigin`-Attribut richtig verwendet wird."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "<PERSON>n <PERSON>e Hinweise auf Ressourcen als `preconnect` oder `dns-prefetch` hi<PERSON><PERSON><PERSON><PERSON>, können Sie möglichst frühzeitig eine Verbindung zu wichtigen Drittanbieterursprüngen herstellen. [Weitere Informationen.](https://web.dev/uses-rel-preconnect)"}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "Vorverbindung zu erforderlichen Ursprüngen aufbauen"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Ein <link>-Element zum Vorabladen wurde für {preloadURL} gefunden, aber nicht vom Browser verwendet. Überprüfen Sie, ob das `crossorigin`-Attribut richtig verwendet wird."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "Mit `<link rel=preload>` können Sie das Abrufen von Ressourcen priorisieren, die derzeit beim Seitenaufbau erst später angefordert werden. [Weitere Informationen.](https://web.dev/uses-rel-preload)"}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "Wichtige Anforderungen vorab laden"}, "lighthouse-core/audits/viewport.js | description": {"message": "<PERSON>ügen <PERSON> e<PERSON> `<meta name=\"viewport\">`-<PERSON> hinz<PERSON>, um Ihre App für Bildschirme von Mobilgeräten zu optimieren. [Weitere Informationen.](https://web.dev/viewport)"}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "Kein `<meta name=\"viewport\">`-Tag gefunden"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "Hat kein `<meta name=\"viewport\">`-Tag mit `width` oder `initial-scale`"}, "lighthouse-core/audits/viewport.js | title": {"message": "Hat ein `<meta name=\"viewport\">`-Tag mit `width` oder `initial-scale`"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "Wenn JavaScript deaktiviert ist, sollte Ihre App dennoch einige Inhalte da<PERSON>ellen – auch wenn es sich dabei nur um eine Warnung handelt, dass die App JavaScript benötigt. [Weitere Informationen](https://web.dev/without-javascript)."}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "Die Body der Seite sollte einige Inhalte rendern, wenn ihre Skripts nicht verfügbar sind."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> keinen Fallback-Content, wenn JavaScript nicht verfügbar ist"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "Enthält einige Inhalte, wenn JavaScript nicht verfügbar ist"}, "lighthouse-core/audits/works-offline.js | description": {"message": "<PERSON>n Sie eine progressive Web-<PERSON><PERSON> en<PERSON>, sollten Sie einen Service Worker verwenden, damit Ihre <PERSON> auch offline funktioniert. [Weitere Informationen.](https://web.dev/works-offline)"}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "Aktuelle Seite reagiert im Offlinemodus nicht mit dem HTTP-Statuscode 200"}, "lighthouse-core/audits/works-offline.js | title": {"message": "Aktuelle Seite reagiert im Offlinemodus mit dem HTTP-Statuscode 200"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "Die Seite lädt im Offlinemodus möglicherweise nicht, weil Ihre Test-URL ({requested}) auf \"{final}\" weitergeleitet wurde. Versuchen Sie, die zweite URL direkt zu testen."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "<PERSON><PERSON> dieser Möglichkeiten können Sie die Nutzung von ARIA in Ihrer Anwendung verbessern, wovon <PERSON><PERSON><PERSON> von Hilfstechnologien wie Screenreadern unter Umständen profitieren."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Hier finden Sie Möglichkeiten, um Alternativen für Audio- und Videoinhalte anzubieten. Dies kann die Nutzung für Personen mit eingeschränktem Hör- und Sehvermögen verbessern."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Audio und Video"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Hier finden Sie häufig genutzte Best Practices für Barrierefreiheit."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Best Practices"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "Mit diesen Prüfungen erfahren Sie, [wie Sie die Barrierefreiheit Ihrer Web-App verbessern](https://developers.google.com/web/fundamentals/accessibility). Nur bestimmte Probleme mit der Barrierefreiheit können durch automatisierte Tests erkannt werden. Deshalb ist es empfehlenswert, zusätzlich manuelle Tests durchzuführen."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "Diese Prüfungen sind für Bereiche vorgesehen, für die automatische Testtools nicht geeignet sind. Weitere Informationen finden Sie in unserem Leitfaden zur [Durchführung einer Prüfung auf Barrierefreiheit](https://developers.google.com/web/fundamentals/accessibility/how-to-review)."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "Barrierefreiheit"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "<PERSON><PERSON> dieser Möglichkeiten können Sie die Lesbarkeit Ihrer Inhalte verbessern."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Damit können <PERSON> da<PERSON>ü<PERSON> sorgen, dass Ihre Inhalte in verschiedenen Sprachen besser verstanden werden."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internationalisierung und Lokalisierung"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "<PERSON><PERSON> dieser Möglichkeiten können Sie die Semantik der Steuerelemente Ihrer Anwendung verbessern. Dies kommt Nutzern von Hilfstechnologien wie Screenreadern zugute."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Namen und Labels"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Hier finden Sie Möglichkeiten, die Tastaturnavigation in Ihrer App zu verbessern."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navigation"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Hier finden Sie Möglichkeiten, um das Lesen von Daten in Tabellen oder Listen mit Hilfstechnologie wie Screenreadern zu verbessern."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tabellen und Listen"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Best Practices"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> von Leistungsbudgets werden Standards für die Leistung Ihrer Website definiert."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "Budgets"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "Weitere Informationen zur Leistung Ihrer App finden Sie hier. Diese <PERSON> haben keinen [direkten Einfluss](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) auf die Leistungsbewertung."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnose"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Der wichtigste Faktor bei der Leistung ist, wie schnell Pixel auf dem Bildschirm gerendert werden. Wichtige Messwerte: \"Erste Inhalte gezeichnet\", \"Inhalte weitgehend gezeichnet\""}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Verbesserungen beim Zeichnen der ersten Inhalte"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> diese Empfehlungen lässt sich die Ladezeit Ihrer Seite möglicherweise verkürzen. Sie haben keinen [direkten Einfluss](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) auf die Leistungsbewertung."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Empfehlungen"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "Messwerte"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Hier können Sie die Ladezeiten verkürzen, damit die Seite so schnell wie möglich reagiert und Einsatzbereit ist. Wichtige Messwerte: \"Zeit bis Interaktivität\", \"Geschwindigkeitsindex\""}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Allgemeine Verbesserungen"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "Le<PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "Diese Prüfungen dienen dazu, die einzelnen Aspekte einer progressiven Web-App zu überprüfen. [Weitere Informationen](https://developers.google.com/web/progressive-web-apps/checklist)."}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "Diese Prüfungen sind laut der grundlegenden [PWA-Checkliste](https://developers.google.com/web/progressive-web-apps/checklist) <PERSON><PERSON><PERSON><PERSON>, werden von Lighthouse jedoch nicht automatisch durchgeführt. Sie haben zwar keine Auswirkung auf Ihre Leistungsbewertung, aber es ist wichtig, sie manuell durchzuführen."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "Progressive Web-App"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "Schnell und zuverlässig"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Installierbar"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PWA-optimiert"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "Mit diesen Prüfungen ist gewährleistet, dass Ihre Seite für das Ergebnis-Ranking von Suchmaschinen optimiert ist. Darüber hinaus gibt es aber auch noch andere Faktoren, die sich auf das Such-Ranking Ihrer Seite auswirken können und die Lighthouse nicht berücksichtigt. [Weitere Informationen.](https://support.google.com/webmasters/answer/35769)"}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "Sie können diese zusätzlichen Validierungen für Ihre Website ausführen, um weitere Best Practices für die SEO zu prüfen."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "Formatieren Sie Ihren HTML-Code so, dass Crawler den Inhalt Ihrer App besser verstehen."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "Best Practices für Inhalte"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Damit Ihre Website in den Suchergebnissen angezeigt werden kann, benötigen Crawler Zugriff auf Ihre App."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Crawling und Indexierung"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "<PERSON><PERSON><PERSON>, dass Ihre Seiten für Mobilgeräte optimiert sind, damit <PERSON> problemlos Inhalte lesen können, ohne mit den Fingern heranzoomen zu müssen. [Weitere Informationen.](https://developers.google.com/search/mobile-sites/)"}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "<PERSON><PERSON>r Mobilgeräte optimiert"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Cache-TTL"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "Position"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "Name"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "Anfragen"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "Ressourcentyp"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "Größe"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Z<PERSON>au<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "Übertragungsgröße"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Mögliche Einsparungen"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "Mögliche Einsparungen"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Mögliche Einsparung von {wastedBytes, number, bytes} KB"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Mögliche Einsparung von {wastedMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokument"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "Bild"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "Medien"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "Sonstige"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "S<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Stylesheet"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Dritt<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "Gesamt"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "<PERSON>im Aufzeichnen des Trace über Ihren Seitenaufbau ist ein Problem aufgetreten. Bitte führen Sie Lighthouse noch einmal aus. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "Zeitüberschreitung beim Warten auf die ursprüngliche Verbindung zum Debugger-Protokoll."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "<PERSON>im <PERSON>nauf<PERSON>u wurden von Chrome keine Screenshots erfasst. Achten <PERSON>, dass auf der Seite Inhalte sichtbar sind, und versuchen <PERSON> dann, Lighthouse noch einmal auszuführen. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "Die angegebene Domain konnte von den DNS-Servern nicht aufgelöst werden."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "<PERSON><PERSON> {artifactName}-<PERSON><PERSON>er ist ein Fehler aufgetreten: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "Ein interner Chrome-Fehler ist aufgetreten. Starten Sie Chrome neu und versuchen Sie anschließend, Lighthouse noch einmal auszuführen."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "Erforderlicher {artifact<PERSON>ame}-<PERSON><PERSON><PERSON> wurde nicht ausgeführt."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "Die von Ihnen angeforderte Seite konnte von Lighthouse nicht zuverlässig geladen werden. Überprüfen Sie, ob Sie die richtige URL testen und der Server auf alle Anfragen angemessen reagiert."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "Die angeforderte URL konnte von Lighthouse nicht zuverlässig geladen werden, weil die Seite nicht mehr reagiert hat."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Die von Ihnen angegebene URL hat kein gültiges Sicherheitszertifikat. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome hat den Seitenaufbau mit einem Interstitial verhindert. Überprüfen Sie, ob Sie die richtige URL testen und der Server auf alle Anfragen angemessen reagiert."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Die von Ihnen angeforderte Seite konnte von Lighthouse nicht zuverlässig geladen werden. Überprüfen Sie, ob Sie die richtige URL testen und der Server auf alle Anfragen angemessen reagiert. (Details: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Die von Ihnen angeforderte Seite konnte von Lighthouse nicht zuverlässig geladen werden. Überprüfen Sie, ob Sie die richtige URL testen und der Server auf alle Anfragen angemessen reagiert. (Statuscode: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Das Laden Ihrer Seite hat zu lange gedauert. Nutzen Sie die Tipps im Bericht, um die Seitenladezeit zu verringern, und versuchen Sie anschließend noch einmal, Lighthouse auszuführen. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "Die maximal zulässige Antwortzeit des DevTools-Protokolls wurde überschritten. (Methode: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "Die maximal zulässige Zeit für das Abrufen von Ressourceninhalten wurde überschritten"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "Die von Ihnen angegebene URL scheint ungültig zu sein."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "Überprüfungen ansehen"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "Anfangsnavigation"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "Maximale Latenz für kritischen Pfad:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "<PERSON><PERSON>."}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "Fehler gemeldet: keine Informationen zur Überprüfung"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "Labdaten"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/)-Analyse der aktuellen Seite in einem emulierten Mobilfunknetz. Die Werte sind Schätzungen und können variieren."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "Zusätzliche Elemente zur manuellen Überprüfung"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "<PERSON>cht zu<PERSON>nd"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "Empfehlung"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "Geschätzte Einsparung"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "Bestandene Prüfungen"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "Snippet minimieren"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "Snippet maximieren"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "Drittanbieter-Ressourcen anzeigen"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "Einige Probleme haben diese Ausführung von Lighthouse beeinträchtigt:"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "Die Werte sind Schätzungen und können variieren. Die Leistungsbewertung [basiert nur auf diesen Messwerten](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "Bestandene Prüfungen mit Warnungen"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "Warnungen: "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "Sie haben die Möglichkeit, Ihr GIF bei einem Dienst ho<PERSON>zuladen, der da<PERSON><PERSON>r sorgt, dass es als HTML5-Video eingebettet werden kann."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "Sie können ein [Lazy-Loading-Plug-in für WordPress](https://wordpress.org/plugins/search/lazy+load/) installieren, mit dem Sie nicht sichtbare Bilder aufschieben. Alternativ können Sie auch zu einem Design wechseln, das diese Funktion bietet. Sie sollten sich auch überlegen, [das AMP-Plug-in](https://wordpress.org/plugins/amp/) zu verwenden."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "Es gibt eine Reih<PERSON> von WordPress-Plug-ins, mit denen Sie [wichtige Assets einbetten](https://wordpress.org/plugins/search/critical+css/) oder [weniger wichtige Ressourcen aufschieben](https://wordpress.org/plugins/search/defer+css+javascript/) können. Beachten Sie, dass über diese Plug-ins bereitgestellte Optimierungen dazu führen können, dass Ihre Designs oder Plug-ins nicht funktionieren. Daher müssen Sie wahrscheinlich Änderungen am Code vornehmen."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "So<PERSON>hl Designs, Plug-ins als auch Serverspezifikationen tragen zur Serverantwortzeit bei. Versuchen Sie, ein noch weiter optimiertes Design zu finden, wählen Sie ein geeignetes Optimierungs-Plug-in aus und/oder upgraden Sie Ihren Server."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "Sie haben die Möglichkeit, Auszüge in Ihrer Beitragsliste einzublenden (z. B. über das Tag \"Mehr\"), die Anzahl der Beiträge auf einer Seite zu verringern, lange Beiträge auf mehrere Seiten aufzuteilen oder ein Plug-in für das Lazy Loading von Kommentaren zu verwenden."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "Ihre Website lässt sich mit einer Reihe von [WordPress-Plug-ins](https://wordpress.org/plugins/search/minify+css/) beschleunigen, durch die Ihre Stile verkettet und komprimiert werden. Sofern möglich, können Sie diese Komprimierung auch im Voraus über einen Build-Prozess vornehmen."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "Ihre Website lässt sich mit einer Reihe von [WordPress-Plug-ins](https://wordpress.org/plugins/search/minify+javascript/) beschleunigen, durch die Ihre Skripts verkettet und komprimiert werden. So<PERSON><PERSON> mö<PERSON>, können Sie diese Komprimierung auch im Voraus über einen Build-Prozess vornehmen."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "<PERSON><PERSON><PERSON><PERSON>, ob <PERSON><PERSON> [WordPress-Plug-ins](https://wordpress.org/plugins/), über die nicht verwendete CSS auf Ihre Seite geladen werden, entfernen oder durch alternative Plug-ins ersetzen können. Wenn Sie die Plug-ins ermitteln möchten, über die irrelevante CSS hinzugefügt werden, können Sie das Prüftool zur [Codeabdeckung](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) in den Chrome-Entwicklertools verwenden. Das entsprechende Design/Plug-in können Sie anhand der URL des Stylesheets erkennen. Suchen Sie in der Liste nach Plug-ins mit vielen Stylesheets, bei denen im Prüftool zur Codeabdeckung viel nicht verwendeter Code (markiert in Rot) angezeigt wird. Ein Stylesheet sollte nur dann in ein Plug-in aufgenommen werden, wenn es auch tatsächlich auf der Seite verwendet wird."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "<PERSON><PERSON><PERSON><PERSON>, ob <PERSON><PERSON> [WordPress-Plug-ins](https://wordpress.org/plugins/), über die nicht verwendete JavaScript-Dateien auf Ihre Seite geladen werden, entfernen oder durch alternative Plug-ins ersetzen können. Wenn Sie die Plug-ins ermitteln möchten, über die irrelevante JavaScript-Dateien hinzugefügt werden, können Sie das Prüftool zur [Codeabdeckung](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) in den Chrome-Entwicklertools verwenden. Das entsprechende Design/Plug-in können Sie anhand der URL des Skripts erkennen. Suchen Sie in der Liste nach Plug-ins mit vielen Skripts, bei denen im Prüftool zur Codeabdeckung viel nicht verwendeter Code (markiert in Rot) angezeigt wird. Ein Skript sollte nur dann in ein Plug-in aufgenommen werden, wenn es auch tatsächlich auf der Seite verwendet wird."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "Hier erhalten Sie Informationen zum [Browser-Caching in WordPress](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "Sie haben die Möglichkeit, ein [WordPress-Plug-in für die Bildoptimierung](https://wordpress.org/plugins/search/optimize+images/) zu verwenden, durch das Ihre Bilder komprimiert werden, die Qualität jedoch gleich bleibt."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "Sie haben die Möglichkeit, Bilder direkt über die [Medienbibliothek](https://codex.wordpress.org/Media_Library_Screen) hoch<PERSON><PERSON><PERSON>, damit die erforderlichen Bildgrößen verfügbar sind. Die Bilder können Sie dann aus der Medienbibliothek einfügen oder auch das Bild-Widget nutzen, damit die optimalen Bildgrößen verwendet werden (einschließlich derjenigen für die responsiven Haltepunkte). Bilder in `Full Size` sollten nur verwendet werden, wenn die Abmessungen für die entsprechende Nutzung geeignet sind. [Weitere Informationen.](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)"}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "Sie können die Textkomprimierung in der Konfiguration Ihres Webservers aktivieren."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "Sie haben die Möglichkeit, Ihre hochgeladenen Bilder mithilfe eines [Plug-ins](https://wordpress.org/plugins/search/convert+webp/) oder eines Dienstes automatisch in das optimale Format zu konvertieren."}}