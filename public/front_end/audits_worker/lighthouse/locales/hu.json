{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "A hozzáférési kulcsok segítségével a felhasználók gyorsan fókuszálhatnak az oldal adott részére. A megfelelő navigáció érdekében az összes hozzáférési kulcsnak egyedinek kell lennie. [További információ](https://web.dev/accesskeys/)."}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "A(z) `[accesskey]` értékek nem egyediek"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "A következő értékek egyediek: `[accesskey]`"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Minden egyes ARIA `role` szer<PERSON>k<PERSON><PERSON> `aria-*` attribútumok konkrét részhalmazát támogatja. A hibás párosításuk érvényteleníti a(z) `aria-*` attribútumokat. [Tov<PERSON><PERSON><PERSON> inform<PERSON>](https://web.dev/aria-allowed-attr/)."}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Bizonyos `[aria-*]` attribútumok nem felelnek meg szerepkörüknek"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "A(z) `[aria-*]` attribútumok megfelelnek szerepüknek"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "Bizonyos ARIA-szerepkörök kötelező attribútumokkal rendelkeznek, amelyek az elem állapotát írják le a képernyőolvasók számára. [Tov<PERSON>bbi információ](https://web.dev/aria-required-attr/)."}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "A(z) `[role]` elemek nem rendelkeznek minden szükséges `[aria-*]` attribútummal"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "A(z) `[role]` attribútumok minden szükséges `[aria-*]` attribútummal rendelkeznek"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "Bizonyos fölérendelt ARIA-szerepköröknek meghatározott alárendelt szerepköröket kell tartalmazniuk, hogy megfelelően teljesíthessék a kívánt kisegítő funkciójukat. [Tov<PERSON><PERSON>i információ](https://web.dev/aria-required-children/)."}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> `[role]` attrib<PERSON>tummal rendelkez<PERSON> elemekből, me<PERSON><PERSON> gyermekelemeiben kell lennie adott `[role]` attri<PERSON><PERSON><PERSON>nak, hiányzik ezeknek a kötelező gyermekelemeknek mindegyike vagy némelyike."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "<PERSON><PERSON><PERSON> `[role]` attribútummal rendelkező elemekben, me<PERSON><PERSON> gyermekelemeiben kell lennie adott `[role]` attri<PERSON><PERSON><PERSON>nak, megvan a kötelező gyermekelemek mindegyike."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "Bizonyos alárendelt ARIA-szerepköröket meghatározott fölérendelt szerepköröknek kell tartalma<PERSON>uk, hogy megfelelően teljesíthessék a kívánt kisegítő funkciójukat. [További információ](https://web.dev/aria-required-parent/)."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "A(z) `[role]` elemek nem a megfelelő szülőelemben vannak"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "A(z) `[role]` elemek a megfelelő szülőelemben vannak"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "Az ARIA-szerepköröknek érvényes értékekkel kell rendelkezniük, hogy megfelelően teljesíthessék a kívánt kisegítő funkciójukat. [Tov<PERSON><PERSON>i információ](https://web.dev/aria-roles/)."}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "A(z) `[role]` értékek nem érvényesek"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "A(z) `[role]` értékek érvényesek"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "A kisegítő technológiák (például a képernyőolvasók) nem tudják értelmezni az érvénytelen értékkel rendelkez<PERSON> ARIA-attribútumokat. [További információ](https://web.dev/aria-valid-attr-value/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "A(z) `[aria-*]` attribútumoknak nincs érvényes értékük"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "A(z) `[aria-*]` attribútumok érvényes értékkel rendelkeznek"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "A kisegítő technológiák (például a képernyőolvasók) nem tudják értelmezni az érvénytelen névvel rendelkező ARIA-attribútumokat. [Tov<PERSON>bbi információ](https://web.dev/aria-valid-attr/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "A(z) `[aria-*]` attribútumok nem érvényesek vagy elgépelést tartalmaznak"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "A(z) `[aria-*]` attribútumok érvényesek és nincsenek elgépelve"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "A feliratok a legfontosabb információk közlésével teszik használhatóvá az audioelemeket a siket és hallássérült felhasználók számára (péld<PERSON><PERSON> ki be<PERSON>, mit mond, vala<PERSON><PERSON> e<PERSON>, nem besz<PERSON>ez kötődő információk). [További információ](https://web.dev/audio-caption/)."}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "A(z) `<audio>` elemek nem rendelkez<PERSON>k `[kind=\"captions\"]` tartalmú `<track>` elemmel."}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "A(z) `<audio>` elemek<PERSON>z `[kind=\"captions\"]` tartalmú `<track>` elem tartozik"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "<PERSON><PERSON><PERSON> el<PERSON>"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "Ha valamelyik gomb nem rendelkezik hozzáférhető névvel, a képernyőolvasók „gomb” néven olvassák fel, ami nem igazán hasznos a képernyőolvasóra hagyatkozó felhasználók számára. [További információ](https://web.dev/button-name/)."}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "Bizonyos gombok nem rendelkeznek hozzáférhető névvel"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "A gombok rendelkeznek kisegítő névvel"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "<PERSON>z ismétlődő tartalmakat megkerülő módszerek megvalósításával a billentyűzetet használó személyek hatékonyabban navigálhatnak az oldalon. [További információ](https://web.dev/bypass/)."}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "Az oldal nem tartalma<PERSON> cí<PERSON>, átugró linket vagy igazodásipont-régiót"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "<PERSON>z oldal címsort, átugró linket vagy igazodásipont-régiót tartalmaz"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "<PERSON>z alacsony kontrasztú szöveg sokak számára nehezen vagy egyáltalán nem olvasható. [További információ](https://web.dev/color-contrast/)."}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "<PERSON><PERSON> a háttér- és előtérszínek közötti kontrasztarány."}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "Megfelelő a háttér- és előtérszínek közötti kontrasztarány"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "Ha nem megfelelő a definíciós listák jelölése, a képernyőolvasók zavaros vagy pontatlan szöveget generálhatnak. [További információ](https://web.dev/definition-list/)."}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "A(z) `<dl>` elemek nem csak megfelelően rendezett `<dt>` és `<dd>` csoportokat, illetve `<script>` vagy `<template>` elemeket tartalmaznak."}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "A(z) `<dl>` elemek csak megfelelő sorrendű `<dt>` és `<dd>` csoportokat, illetve `<script>` vagy `<template>` elemeket tartalmaznak."}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "A definíciós listák elemeit (`<dt>` és `<dd>`) `<dl>` sz<PERSON><PERSON><PERSON>elembe kell foglalni, hogy a képernyőolvasók megfelelően felolvashassák őket. [További információ](https://web.dev/dlitem/)."}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "A definíciós listák elemei ninc<PERSON>ek `<dl>` elemekben"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "A definíciós listák elemei `<dl>` elem<PERSON><PERSON> vannak"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "A cím áttekintést ad az oldalról a képernyőolvasót használó <PERSON>k, a keresőmotorok pedig nagymértékben hagyatkoznak rá annak meghatározásához, hogy az oldal releváns-e az adott kereséshez. [További információ](https://web.dev/document-title/)."}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "A dokumentum nem rendelkezik `<title>` elemmel"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "A dokumentum tartalmaz `<title>` elemet"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "Az „id” attribútum értékének egyedinek kell lennie, hogy a kisegítő technológiák ne hagyják figyelmen kívül a többi előfordulást. [További információ](https://web.dev/duplicate-id/)."}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "<PERSON>z oldal `[id]` attribútumai nem egyediek"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "Az oldalon talá<PERSON>ható `[id]` attribútumok egyediek"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "A képernyőolvasók a keretcímek alapján írják le a keretek tartalmát. [További információ](https://web.dev/frame-title/)."}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "A(z) `<frame>` és a(z) `<iframe>` elemeknek nincs cí<PERSON>ük"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "Van címe a(z) `<frame>` és `<iframe>` elemeknek"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "Ha az oldal nem határoz meg „lang” attribútumot, a képernyőolvasók azt feltételezik majd, hogy az oldal nyelve megegyezik azzal az alapértelmezett nyelvvel, amelyet a felhasználó a képernyőolvasó beállításakor választott. Ha az oldal tényleges nyelve eltér az alapértelmezett nyelvtől, ak<PERSON> előfordulhat, hogy a képernyőolvasók helytelen kiejtéssel olvassák majd fel az oldal szövegét. [További információ](https://web.dev/html-has-lang/)."}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "A(z) `<html>` elem nem rendelkezik `[lang]` attribútummal"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "A(z) `<html>` elem<PERSON>z tartozik `[lang]` attribútum"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "Ha érvényes [BCP 47 által definiált nyelvet](https://www.w3.org/International/questions/qa-choosing-language-tags#question) has<PERSON><PERSON><PERSON>, a képernyőolvasók könnyebben felolvassák a szövegeket. [További információ](https://web.dev/html-lang-valid/)."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "A(z) `<html>` elem `[lang]` attribútumának nincs érvényes értéke."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "A(z) `<html>` elem `[lang]` attribútumának értéke érvényes"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "<PERSON><PERSON><PERSON>, beszédes alternatív szöveget használjon a tájékoztató elemekhez. A díszítőelemek figyelmen kívül hagyhatók üres „alt” attribútummal. [További információ](https://web.dev/image-alt/)."}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "A képelemekhez nem tartozik `[alt]` attribútum"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "A képelemekhez tartozik `[alt]` attribútum"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "Ha a(z) `<input>` t<PERSON><PERSON><PERSON> gombként használt képekhez alternatív szöveget is megad, segíthet a képernyőolvasót használó látogatóknak a gomb rendeltetésének megértésében. [További információ](https://web.dev/input-image-alt/)."}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "A(z) `<input type=\"image\">` elemekhez nem tartozik `[alt]` szöveg"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "A(z) `<input type=\"image\">` elemek rendelkez<PERSON>k `[alt]` szöveggel"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "A címkék biztosítják, hogy a kisegítő technológiák (pl. a képernyőolvasók) megfelelően jelezzék az űrlapvezérlőket. [További információ](https://web.dev/label/)."}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "Bizonyos formátumelemek nem rendelkeznek társított címk<PERSON>kel"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "A formátumelemekhez megfelelő címkék vannak társítva"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "<PERSON>z elrendezési célokat szolgáló táblázat ne tartalmazzon adatelemeket (pl. „th” vagy „caption” elemet, illetve „summary” attribútumot), mert zavarók lehetnek a képernyőolvasót használó személyek számára. [További információ](https://web.dev/layout-table/)."}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "A prezentációs `<table>` elemek nem kerülik a(z) `<th>`, a(z) `<caption>` vagy a(z) `[summary]` attribútum használatát."}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "A prezentációs `<table>` elemek kerülik a(z) `<th>`, a(z) `<caption>` vagy a(z) `[summary]` attribútum használatát."}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "A felismerhető, egyedi és fókuszálható linkszövegek (és linkként használt képek alternatív szövegei) jobb navigációs élményt biztosítanak a képernyőolvasóra hagyatkozó felhasználók számára. [További információ](https://web.dev/link-name/)."}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "A linkekhez nem tartozik felismerhető név"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "A linkekhez felismerhető név tartozik"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "A képernyőolvasók sajátos módon olvassák fel a listákat. A megfelelő listastruktúra biztosítása segíti a képernyőolvasók működését. [További információ](https://web.dev/list/)."}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "A listák nem csak `<li>` elemeket és szkripttámogató elemeket (`<script>` és `<template>`) tartalmaznak."}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "A listák csak `<li>` elemeket és szkripttámogató elemeket (`<script>`, `<template>`) tartalmaznak."}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "A listaelemeket (`<li>`) fölérendelt `<ul>` vagy `<ol>` elemekben kell elhelyezni, hogy a képernyőolvasók megfelelően olvassák fel őket. [További információ](https://web.dev/listitem/)."}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "A listael<PERSON>ek (`<li>`) nem `<ul>` vagy `<ol>` szülőelemekben vannak."}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "A listael<PERSON>ek (`<li>`) `<ul>` vagy `<ol>` szülőelemekben vannak"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "A felhasználók nem számítanak az oldal automatikus frissítésére, ráadásul a frissítés visszahelyezi a fókuszt az oldal tetejére. Ez frusztrá<PERSON> lehet, valamint összezavarhatja a felhasználót. [További információ](https://web.dev/meta-refresh/)."}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "A dokumentum `<meta http-equiv=\"refresh\">` címkét használ"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "A dokumentum nem használja a(z) `<meta http-equiv=\"refresh\">` címkét"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "A nagyítás letiltása problémát jelent a gyengén látó felhasználók számára, akik a képernyőnagyításra hagyatkoznak ahhoz, hogy megfelelően láthassák a weboldal tartalmát. [További információ](https://web.dev/meta-viewport/)."}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "A(z) `[user-scalable=\"no\"]` szerepel a(z) `<meta name=\"viewport\">` elemben, vagy a(z) `[maximum-scale]` attribútum 5-n<PERSON>l kisebb."}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "Nem használja a(z) `[user-scalable=\"no\"]` attribútumot a(z) `<meta name=\"viewport\">` elemben, a(z) `[maximum-scale]` attribútum pedig nem kevesebb 5-nél."}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "A képernyőolvasók nem tudj<PERSON> lefordítani a nem szövegalapú tartalmakat. Ha alternatív szöveget helyez el a(z) `<object>` elem<PERSON>ben, a képernyőolvasók kommunikálhatják az elemek jelentését a felhasználóknak. [További információ](https://web.dev/object-alt/)."}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "A(z) `<object>` elemekhez nem tartozik `[alt]` szöveg"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "A(z) `<object>` elemek rendelk<PERSON> `[alt]` szöveggel"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "A 0-nál nagyobb érték explicit navigációs sorrendet jelent. Ez ugyan technikailag érvényes, azonban gyakran problémát jelent a kisegítő technológiákra hagyatkozó felhasználók számára. [További információ](https://web.dev/tabindex/)."}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "Bizonyos elemek `[tabindex]` attribútuma 0-nál nagyobb értékkel rendelkezik."}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "Egyetlen elem `[tabindex]` attribútumának sem 0-nál nagyobb az értéke"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "A képernyőolvasók olyan funkciókkal is re<PERSON><PERSON><PERSON><PERSON><PERSON>, amely<PERSON> meg<PERSON>önnyítik a táblázatokban való navigációt. Ha a(z) `[headers]` attribútumot hasz<PERSON> `<td>` cell<PERSON> csak a saját táblázatuk más celláira hivat<PERSON>znak, jobb felhasználói élményt nyújthat a képernyő felolvasása során. [További információ](https://web.dev/td-headers-attr/)."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "<PERSON><PERSON> egyik `<table>` elem<PERSON> l<PERSON>, `[headers]` attribútumot használó cellák olyan `id` elemre hi<PERSON>, amely nem tal<PERSON><PERSON>ható meg ugyanabban a táblázatban."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "<PERSON><PERSON> egyik `<table>` elem<PERSON> lév<PERSON>, `[headers]` attribútumot használó cellák a saját táblázatuk celláira hivatkoznak."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "A képernyőolvasók olyan funkciókkal is re<PERSON><PERSON><PERSON>ne<PERSON>, amelyek megkönnyítik a táblázatokban való navigációt. Ha biztosítja, hogy a táblák fejlécei mindig hivatkozzanak cellákra, megkönnyítheti az oldal használatát a képernyőolvasóra hagyatkozó felhasználók számára. [További információ](https://web.dev/th-has-data-cells/)."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "A(z) `<th>` elemekhez és a(z) `[role=\"columnheader\"/\"rowheader\"]` tartalmú elemekhez nem tartoznak általuk leírt adatcellák."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "A(z) `<th>` elemek és a(z) `[role=\"columnheader\"/\"rowheader\"]` tartalmú elemek rendelkeznek a leírt adatcellákkal."}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "Ha az elemeken érvényes [BCP 47 által definiált nyelvet](https://www.w3.org/International/questions/qa-choosing-language-tags#question) hat<PERSON><PERSON>z meg, akkor segíthet a képernyőolvasóknak a szöveg helyes kiejtésében. [További információ](https://web.dev/valid-lang/)."}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "A(z) `[lang]` attribútumokhoz nem tartozik érvényes érték"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "A(z) `[lang]` attribútumok érvényes értékkel rendelkeznek"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "Ha a videóhoz felirat tartozik, a siket és hallássérült felhasználók egyszerűbben hozzájuthatnak a videóban található információkhoz. [Tov<PERSON>bbi információ](https://web.dev/video-caption/)."}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "A(z) `<video>` elemekhez nem tartozik `[kind=\"captions\"]` tartalmú `<track>` elem."}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "A(z) `<video>` elemek<PERSON>z `[kind=\"captions\"]` tartalmú `<track>` elem tartozik"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "Az audiokommentárok olyan fontos információkat nyújtanak a videókhoz, amelyek a párbeszédekből nem derülnek ki (ilyen például az arckifejezések és a jelenetek leírása). [További információ](https://web.dev/video-description/)."}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "A(z) `<video>` elemekhez nem tartozik `[kind=\"description\"]` tartalmú `<track>` elem."}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "A(z) `<video>` elemek<PERSON>z `[kind=\"description\"]` tartalmú `<track>` elem tartozik"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "Ha azt szeretné, hogy a megjelenés ideális legyen iOS rendszeren, amikor a felhasználók progresszív webes alkalmazást adnak hozzá a kezdőképernyőhöz, hat<PERSON>rozzon meg egy `apple-touch-icon` elemet. Az ikonnak 192 képpont (vagy 180 képpont) méretű, nem <PERSON>, négyzetes PNG-re kell mutatnia. [További információ](https://web.dev/apple-touch-icon/)."}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> `apple-touch-icon`"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "A(z) `apple-touch-icon-precomposed` elavult; a(z) `apple-touch-icon` <PERSON><PERSON><PERSON><PERSON><PERSON> helyette."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `apple-touch-icon` elemet tartalmaz"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Egyes <PERSON>-bővítmények kedvezőtlenül befolyásolták az oldal betöltési teljesítményét. Próbálkozzon az oldal inkognitómódban vagy bővítmények nélküli Chrome-profilból történő ellenőrzésével."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "Szkriptértékelés"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "Szkriptelemzés"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "<PERSON><PERSON>s <PERSON>"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "Érdemes csökkenteni a JavaScript értelmezésére, összeállítására és végrehajtására fordított időt. <PERSON><PERSON><PERSON> seg<PERSON>, ha kisebb méretű JavaScript-forrásokat továbbít. [További információ](https://web.dev/bootup-time)."}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "Csökkentse a JavaScript végrehajtási idejét"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "JavaScript végrehajtási ideje"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "A túl nagy GIF-fájlokkal nem lehet hatékonyan animált tartalmakat nyújtani. Az adatforgalom csökkentésének érdekében a GIF-ek helyett érdemes az animációkhoz MPEG4-/WebM-videókat hasz<PERSON>ln<PERSON>, a statikus képekhez pedig PNG-/WebP-képeket. [További információ](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Használjon videoformátumot az animált tartalmakhoz"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Vegye fontolóra a képernyőn kívüli és rejtett képek késleltetett, kritikus források utáni betöltését, hogy az oldal hamarabb interaktívvá váljon. [További információ](https://web.dev/offscreen-images)."}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Késleltesse a képernyőn kívüli képek betöltését"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Források blokkolják az oldal első vizuális válaszát. Javasoljuk, hogy a legfontosabb JavaScript-/CSS-elemeket beágyazva továbbítsa, a nem fontos JS-t/CSS-t pedig késleltesse [További információ](https://web.dev/render-blocking-resources)."}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Távolítsa el a megjelenítést gátló erőforrásokat"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "A nagy hálózati terhelés tényleges anyagi költséget jelenthet a felhasználóknak, és jellemzően jelentősen megnöveli a betöltési időt. [További információ](https://web.dev/total-byte-weight)."}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "A teljes méret {totalBytes, number, bytes} kB volt"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Kerülje a nagyon nagy hálózati hasznosadat-forgalmat"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "A nagyon nagy hálózati terhelés elkerülése"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "A CSS-fájlok minimalizálása csökkentheti a hálózaton továbbított adatok méretét. [További információ](https://web.dev/unminified-css)."}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "Minimalizálja a CSS-fájlokat"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "A JavaScript-fájlok minimaliz<PERSON>val c<PERSON>ökkenthető a továbbított adatok mérete és a szkriptek értelmezésére fordított idő. [További információ](https://web.dev/unminified-javascript)."}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Minimalizálja a JavaScript-kódot"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "A stíluslapok érvénytelen szabályainak eltávolításával és a hajtás feletti tartalomhoz nem használt CSS-kód betöltésének késleltetésével csökkentheti a hálózati tevékenység által felhasznált bájtmennyiséget. [További információ](https://web.dev/unused-css-rules)."}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Távolítsa el a nem használt CSS-kódot"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "A nem használt JavaScript-kód eltávolítása a hálózati tevékenység adatforgalmának csökkentéséhez."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Távolítsa el a nem használt JavaScript-kódot"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Ha a gyorsítótárak élett<PERSON>ama hosszú, gyorsabb<PERSON> válnak a weboldal későbbi ismételt megnyitásai. [További információ](https://web.dev/uses-long-cache-ttl)."}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 erőforrás található}other{# erőforrás található}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Jelenítse meg a statikus eszközöket hatékony gyorsítótár-házirend <PERSON>"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Használjon hatékony gyorsítótár-házirendet a statikus eszközöknél"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Az optimalizált képek gyorsabban betöltődnek és kevesebb adatforgalmat generálnak. [További információ](https://web.dev/uses-optimized-images)."}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Kódo<PERSON>ja <PERSON> a képeket"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Használjon olyan k<PERSON>, amelyek megfelelő méretükkel elősegítik a mobiladat-forgalom csökkentését és a betöltési idő javulását. [További információ](https://web.dev/uses-responsive-images)."}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Méretezze megfelelően a képeket"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "A szövegalapú forrásokat tömör<PERSON> (gzip, Deflate vagy <PERSON>) célszerű továbbítani a hálózati adatforgalom minimalizálása érdekében. [További információ](https://web.dev/uses-text-compression)."}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Engedélyezze a szövegtömörítést"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "Az olyan képformátumok, mint a JPEG 2000, a JPEG XR és a WebP gyakran jobb tömörítést nyújtanak, mint a PNG és a JPEG, azaz kevesebb adatforgalom mellett gyorsabb letöltést biztosítanak. [További információ](https://web.dev/uses-webp-images)."}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "Jelenítse meg a képeket következő generációs formátumok<PERSON>"}, "lighthouse-core/audits/content-width.js | description": {"message": "Ha az alkalmazás tartalmának szélessége nem egyezik a megjelenítési terület szélességével, ak<PERSON> lehet, hogy alkalmazása nincs optimalizálva a mobilok képernyőjére. [További információ](https://web.dev/content-width)."}, "lighthouse-core/audits/content-width.js | explanation": {"message": "A megjelenítési terület mérete ({innerWidth} képpont) nem egyezik az ablak méretével ({outerWidth} képpont)."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "A tartalom nincs megfelelően méretezve a megjelenítési területhez"}, "lighthouse-core/audits/content-width.js | title": {"message": "A tartalom megfelelően van méretezve a megjelenítési területhez"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "Az alábbi kritikus kérésláncok megjelenítik, hogy milyen források töltődnek be magas prioritással. Az oldalbetöltés javítása érdekében fontolja meg a láncok hosszának csökkentését, a letöltött források méretének csökkentését, vagy a felesleges források letöltésének késleltetését. [További információ](https://web.dev/critical-request-chains)."}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 lánc található}other{# lánc található}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "Minimalizálja a kritikus lekérdezések mélységét"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "Elavulás / Figyelmeztetés"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "Sor"}, "lighthouse-core/audits/deprecations.js | description": {"message": "Az elavult API-k előbb-utóbb kikerülnek a böngészőből. [További információ](https://web.dev/deprecations)."}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 figyelmeztetés}other{# figyelmeztetés}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "Elavult API-kat <PERSON>"}, "lighthouse-core/audits/deprecations.js | title": {"message": "Ker<PERSON>li az elavult API-kat"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "<PERSON>z alkalmazás-gyors<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> elav<PERSON>. [További információ](https://web.dev/appcache-manifest)."}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "A(z) „{AppCacheManifest}” van haszná<PERSON>"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "Alkalmazás-gyorsítótárat <PERSON>"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "Kerüli az alkalmazás-gyorsítótárat"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "A doctype meghat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a böngésző visszafelé kompatibilis módra váltson. [Tov<PERSON>bbi információ](https://web.dev/doctype)."}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "A doctype nevének a kisbet<PERSON> `html` szövegnek kell lennie"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "A dokumentumnak doctype szakaszt kell tartalmaznia"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "A publicId nem üres karakterlánc"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "A systemId mező nem üres karakterlánc"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Az oldalon nincs HTML doctype, ez<PERSON>rt vissza<PERSON><PERSON> kompatibilis módot indít"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "Az oldalon szerepel a HTML doctype"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "Elem"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Jellemző"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "A böngészők fejlesztői azt javasolják, hogy az oldalak legfeljebb kb. 1500 DOM-elemet tartalmazzanak. Ideális esetben 32-nél kisebb mélységű fa és 60-nál kevesebb gyermek/szül<PERSON> elem van. A nagy méretű DOM megnöveli a memóriahasználatot, hosszabb ideig tartó [stílusszámítást](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) igényel, valamint költséges [elrendez<PERSON>-áttördeléssel](https://developers.google.com/speed/articles/reflow) jár. [További információ](https://web.dev/dom-size)."}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 elem}other{# elem}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Ke<PERSON><PERSON><PERSON><PERSON> a túl nagy DOM-méretet"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "<PERSON><PERSON><PERSON>-mélys<PERSON>g"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "DOM-el<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Alárendelt elemek maximális <PERSON>"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> a túlzó DOM-méretet"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "Adjon `rel=\"noopener\"` vagy `rel=\"noreferrer\"` címkét a külső linkekhez, hogy javíthassa a teljesítményt és elkerülhesse a sebezhetőségeket. [További információ](https://web.dev/external-anchors-use-rel-noopener)."}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "<PERSON>z eredetközi célokra mutató linkek nem biztonságosak"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "<PERSON>z eredetközi célokra mutató linkek biztonságosak"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "A horgony célpontja nem meghatáro<PERSON>ható ({anchorHTML}). Ha nem hiper<PERSON><PERSON><PERSON><PERSON>, érdemes eltávolítania a target=_blank részt."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "A tartózkodási helyre von<PERSON> engedély váratlan kérése bizalmatlanságra adhat okot, valamint összezavarhatja a felhasználókat. Érdemes inkább felhasználói műveletekhez kötni a kérést. [További információ](https://web.dev/geolocation-on-start)."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Oldalbetöltéskor a földrajzi helyre vonat<PERSON>ó engedélyt kéri"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> a földrajzi hely<PERSON> von<PERSON><PERSON> engedély k<PERSON>ét oldalbetöltéskor"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "<PERSON>z oldalon észlelt minden front-end JavaScript-függvénytár. [További információ](https://web.dev/js-libraries)."}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "Észlelt JavaScript-függvénytárak"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "A külső szkriptek `document.write()` segítségével történő dinamikus beillesztése több tíz másodperccel lassíthatja az oldalbetöltést a lassú kapcsolaton csatlakozó felhasználók esetében. [További információ](https://web.dev/no-document-write)."}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "A(z) `document.write()` metódust használja"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "Kerüli a(z) `document.write()` hasz<PERSON>lat<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "Függvén<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "Sebezhetőségek száma"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "A harmadik felektől származó szkriptek ismert sebezhetőségeket tartalmazhatnak, melyeket a támadók könnyedén felismerhetnek és kihasználhatnak. [További információ](https://web.dev/no-vulnerable-libraries)."}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{1 észlelt sebezhetőség}other{# észlelt sebezhetőség}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "Ismert sebezhetőségeket tartalmazó front-end JavaScript-függvénytárakat használ"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "Magas"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "Alacsony"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "Közepes"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "Kerüli az ismert sebezhetőségeket tartalmazó front-end JavaScript-függvénytárakat"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "Az értesítése<PERSON><PERSON> engedély vá<PERSON>lan kérése bizalmatlanságra adhat okot, valamint összezavarhatja a felhasználókat. Érdemes inkább felhasználói műveletekhez kötni a kérést. [További információ](https://web.dev/notification-on-start)."}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Oldalbetöltéskor az értesítések<PERSON> von<PERSON> engedély<PERSON> kéri"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> az értesíté<PERSON>k<PERSON> engedély k<PERSON><PERSON><PERSON>t oldalbetöltéskor"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "<PERSON><PERSON><PERSON> el<PERSON>"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "A jelszóbeillesztés megakadályozása rossz biztonsági gyakorlat. [További információ](https://web.dev/password-inputs-can-be-pasted-into)."}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "Megakadályozza, hogy a felhasználók szöveget illesszenek be a jelszómezőkbe"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a felhasználók beillesszenek a jelszómezőkbe"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokoll"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "A HTTP/2 számos előnyt nyújt a HTTP/1.1-he<PERSON> k<PERSON>, p<PERSON>ld<PERSON><PERSON> bin<PERSON> fej<PERSON>, multiplexelést és előzetes adatküldést (server push). [További információ](https://web.dev/uses-http2)."}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 nem HTTP/2-t hasz<PERSON><PERSON><PERSON> kéré<PERSON>}other{# nem HTTP/2-t hasz<PERSON><PERSON><PERSON> kéré<PERSON>}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "Nem minden for<PERSON><PERSON><PERSON><PERSON> használ HTTP/2-t"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "HTTP/2-t hasz<PERSON>l a saját forrásokhoz"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Fontolja meg az érintési és görgetési eseményfigyelők megjelölését mint `passive`, hogy javuljon az oldal görgetést érintő teljesítménye. [További információ](https://web.dev/uses-passive-event-listeners)."}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Nem <PERSON>l passzív figyelőket a görgetés teljesítményének javításához"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Passzív figyelőket alkalmaz a görgetés teljesítményének javításához"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "Le<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "A konzolon megjelenő hibák megoldatlan problémákat jeleznek. Okaik lehetnek sikertelen hálózati kérések, valamint más böngészős tényezők is. [További információ](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "A böngészőhibák a konzolon láthatók"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "<PERSON>em k<PERSON> böngészőhiba a konzolra"}, "lighthouse-core/audits/font-display.js | description": {"message": "Használja a CSS font-display le<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a szövegek a webes betűtípusok betöltése közben is biztosan láthatók legyenek a felhasználók számára. [További információ](https://web.dev/font-display)."}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "Biztosítsa, hogy a szöveg látható marad a webes betűtípusok betöltése során"}, "lighthouse-core/audits/font-display.js | title": {"message": "Az összes szöveg látható marad a webes betűtípusok betöltésekor"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "A Lighthouse nem tudta automatikusan ellenőrizni a következő URL font-display értékét: {fontURL}"}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "Képarány (tényleges)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Képarány (megjelenített)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "A képmegjelenítési méretek ideális esetben természetes képarányt alkalmaznak. [További információ](https://web.dev/image-aspect-ratio)."}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "Hibás képaránnyal jeleníti meg a képeket"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "<PERSON><PERSON><PERSON> jele<PERSON>íti meg a képeket"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "Érvénytelen képméretezési információ {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "A böngészők proaktív módon kérhetik a felhasználókat, hogy adják hozzá az Ön alkalmazását a kezdőképernyőjükhöz, ami erősebb elköteleződéshez vezethet. [További információ](https://web.dev/installable-manifest)."}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "Az internetes alkalmazás manifestje nem felel meg a telepíthetőségi követelményeknek"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "Az internetes alkalmazás manifestje megfelel a telepíthetőségi követelményeknek"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "Nem bi<PERSON>tonságos URL"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "Minden webhelyet HTTPS-sel kell védeni, még akkor is, ha nem kezelnek bizalmas adatokat A HTTPS megakadályozza, hogy behatolók módosítsák vagy megfigyeljék az alkalmazás és a felhasználók közötti kommunikációt. Ezt a protokollt megköveteli a HTTP/2, valamint számos új webes API is. [További információ](https://web.dev/is-on-https)."}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 nem biztons<PERSON> kérés}other{# nem biztons<PERSON>gos kérés}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "<PERSON>em használ HTTPS-t"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "HTTPS-t használ"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "A mobilhálózatokon is gyors oldalbetöltés jobb mobilos felhasználói élményt jelent. [További információ](https://web.dev/load-fast-enough-for-pwa)."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "{timeInMs, number, seconds} mp az interaktívvá válásig"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "Interaktívvá válás szimulált mobilhálózaton: {timeInMs, number, seconds} mp"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "Az oldal túl lassan töltő<PERSON>k be, és nem válik interaktívvá tíz másodpercen belül. A probléma elhárításához tekintse meg a „Teljesítmény” szakaszban lévő lehetőségeket és diagnosztikákat."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "Az oldalbetöltés nem elég gyors mobilhálózatokon"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "Az oldalbetöltés kellően gyors mobilhálózatokon"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Kategória"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "Érdemes c<PERSON>ökkenteni a JavaScript értelmezésére, összeállítására és végrehajtására fordított időt. <PERSON><PERSON><PERSON> seg<PERSON>, ha kisebb méretű JavaScript-forrásokat továbbít. [További információ](https://web.dev/mainthread-work-breakdown)."}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimalizálja a fő szál terhelését"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "Minimalizálja a fő szál terhelését"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "A lehető legtöbb felhasználó elérése érdekében a webhelyeknek minden elterjedtebb böngészőben működniük kell. [További információ](https://web.dev/pwa-cross-browser)."}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "A webhely több<PERSON><PERSON><PERSON> b<PERSON> is működik"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Lássa el az egyes oldalakat mélylinkként használható URL-ekkel, és ügyeljen arra, hogy ezek az URL-ek egyediek legyenek a közösségi médiában való megosztás céljából. [További információ](https://web.dev/pwa-each-page-has-url)."}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Minden oldal rendelkezik URL-címmel"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "A koppintgatás során az átmeneteknek még lassú h<PERSON> is gyorsnak kell lenniük. Ez az egyik legfontosabb tényező a felhasználók által észlelt teljesítmény tekintetében. [További információ](https://web.dev/pwa-page-transitions)."}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "Az oldalak közti váltásnak nem szabadna akadályozni a hálózati forgalmat"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "A becsült bemeneti késés annak a becsült értéke, hogy az alkalmazás mennyi idő alatt reagál – ezredmásodpercben (ms) megadva – a felhasználói interakciókra az oldalbetöltés legforgalmasabb 5 másodperces időkeretében. Ha a várakozási idő meghaladja az 50 ms-ot, a felhasználók lassúnak érezhetik az alkalmazást. [További információ](https://web.dev/estimated-input-latency)."}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "Becsült bemeneti k<PERSON>"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "<PERSON>z első vizu<PERSON><PERSON> tartalomválasz azt az időpontot jel<PERSON>, amikor a rendszer megkezdi az első szöveg vagy kép megjelenítését. [Tov<PERSON>bbi információ](https://web.dev/first-contentful-paint)."}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "<PERSON><PERSON>ő, tartalommal rendelkező leképezés"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "Az első processzor-üresjárat mutató az első olyan alkalmat jel<PERSON>, amikor az oldal főszálának aktivitása elég alacsonnyá vált ahhoz, hogy kezelni tudja a felhasználói interakciókat.  [További információ](https://web.dev/first-cpu-idle)."}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "Első processzor-üresjárat"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "<PERSON>z első releváns vizuális válasz azt méri, hogy mikor válik láthatóvá az oldal elsődleges tartalma. [Tov<PERSON><PERSON>i információ](https://web.dev/first-meaningful-paint)."}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "Első releváns le<PERSON>"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "Az interaktivitásig eltelt idő az az idő, amely ahhoz szükséges, hogy az oldal teljesen interaktívvá váljon. [További információ](https://web.dev/interactive)."}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "Interaktivitásig eltelt idő"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "Az első interakciótól számított maximális potenciális válaszkésés a leghosszabb feladat időtartama ezredmásodpercben. [További információ](https://developers.google.com/web/updates/2018/05/first-input-delay)."}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "Első interakciótól számított max. potenciális k<PERSON>"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "A Sebességindex mutató azt j<PERSON>, hogy az adott oldal tartalmai milyen gyorsan válnak láthatóvá. [További információ](https://web.dev/speed-index)."}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "Sebességindex"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "Az első vizuális tartalomválasz és az interaktivitásig eltelt idő közötti minden (50 ms-nál hosszabb feladatot jelentő) periódus időtartamának összege milliszekundumban kifejezve."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "Blokkolás <PERSON>"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "A hálózati oda-vissza út ideje (round trip time, RTT) nagy hatással van a teljesítményre. Ha túl magas az RTT értéke valamelyik eredet esetében, az azt jelenti, hogy a felhasználóhoz közelebbi szerverek javíthatják a teljesítményt. [További információ](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "lighthouse-core/audits/network-rtt.js | title": {"message": "Oda-visszautak ideje a hálózaton"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "A szerverek várakozási ideje hatással lehet a webes teljesítményre. Túlterhelést vagy nem megfelelő back-end teljesítmény j<PERSON>z, ha az eredetszerver várakozási ideje túl magas. [További információ](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "Várakozási idő a háttérszervereknél"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "A szolgáltató munkatársnak köszönhetően internetes alkalmazása előre nem látható hálózati körülmények mellett is megbízhatóan fog működni. [További információ](https://web.dev/offline-start-url)."}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "A(z) `start_url` nem 200-as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amikor offline állapotban van"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "A(z) `start_url` 200-as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amikor offline állapotban van"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "A Lighthouse nem tudta olvasni a manifest `start_url` elemét. Ennek következtében azt feltételezi, hogy a(z) `start_url` a dokumentum URL-címe. Hibaüzenet: „{manifestWarning}”."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "A hálózati kérések száma és mérete maradjon a megadott teljesítménybüdzsé célpontjai alatt. [Tov<PERSON>bbi információ](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 kérés}other{# kérés}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "Teljesítménybüdzsé"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "<PERSON> már be<PERSON>ll<PERSON> a HTTPS protokollt, gondoskodjon a teljes HTTP-forgalom HTTPS-re tört<PERSON><PERSON>áról, hogy a biztonságos webes funkciók az összes felhasználó számára rendelkezésre álljanak. [További információ](https://web.dev/redirects-http)."}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "Nem i<PERSON>án<PERSON>í<PERSON>ja á<PERSON> a HTTP-forgalmat HTTPS-re"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "Átirányítja a HTTP-forgalmat HTTPS-re"}, "lighthouse-core/audits/redirects.js | description": {"message": "Az átirányítások további késlekedéssel hosszabbítják meg az oldalbetöltéshez szükséges időt. [További információ](https://web.dev/redirects)."}, "lighthouse-core/audits/redirects.js | title": {"message": "Kerülje a többszörös oldalátirányítást"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "Az oldal forrásainak mennyiségére és méretére von<PERSON> büdzséket budget.json fájl hozzáadásával határozhatja meg. [További információ](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 kérelem • {byteCount, number, bytes} KB}other{# kérelem • {byteCount, number, bytes} KB}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "A kérések száma legyen kevés, az átvitelek pedig kis méretűek"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "A gyűjtőlinkek a keresési eredményként megjelenítendő URL-re tesznek javaslatot. [További információ](https://web.dev/canonical)."}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "T<PERSON>bb ütköző URL ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "Más domainre mutat ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "Érvénytelen URL ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "<PERSON><PERSON><PERSON> `hreflang` helyre mutat ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "Relatív URL ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "A domain gyökér-URL-jére (a kezdőlapra) mutat egyenértékű tartalommal rendelkez<PERSON> oldal helyett"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "A dokumentumhoz nem tartozik érvényes `rel=canonical`"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "A dokumentum érvényes `rel=canonical` attribútumot tartalmaz"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "A 12 képpontnál kisebb betűméretek nehezen láthatók, ez<PERSON><PERSON> a mobilhasználóknak a szöveget elolvasásához fel kell nagyítaniuk a képernyő tartalmát. Törekedjen arra, hogy az oldal szövegének legalább 60%-a minimum 12 képpontos betűmérettel jelenjen meg. [További információ](https://web.dev/font-size)."}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent}-nyi olvasható szöveg"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "A szöveg olvashatatlan, ugyanis a látható terület metacímkéje nincs optimalizálva a mobilképernyőkre"}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "A szöveg {decimalProportion, number, extendedPercent}-a túl kicsi (az összes szöveg {decimalProportionVisited, number, extendedPercent}-a alapján)."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "A dokumentum olvashatatlan betűméreteket használ"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "A dokumentum olvasható betűméreteket tartalmaz"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "A hreflang linkek azt mondják meg a keresőmotoroknak, hogy nyelvtől vagy régiótól függően az oldal melyik változatát kell megjeleníteniük a keresési találatokban. [További információ](https://web.dev/hreflang)."}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "A dokumentum nem rendelkezik érvényes `hreflang` attribútummal"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "A dokumentum érvényes `hreflang` attribútumot tartalmaz"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "A sikertelenséget jelző HTTP-állapotkóddal rendelkező oldalak indexelése eredménytelen lehet. [További információ](https://web.dev/http-status-code)."}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "Az oldal sikertelenséget jelző HTTP-állapotkóddal rendelkezik"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "Az oldal sikerességet jelző HTTP-állapotkóddal rendelkezik"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "A keresőmotorok azokat az oldalakat nem tudják keresési eredményként megjeleníteni, am<PERSON><PERSON>érképezésére ninc<PERSON> en<PERSON>ély<PERSON>. [Tov<PERSON><PERSON>i információ](https://web.dev/is-crawable)."}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "Az oldal indexelését <PERSON>"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "Az oldalnál nincs letiltva az indexelés"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "A leíró jellegű linkszövegek segítenek a keresőmotoroknak a tartalmak értelmezésében. [További információ](https://web.dev/link-text)."}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 link található}other{# link található}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "A linkek nem rendelkeznek leíró jellegű szöveggel"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "A linkek leíró jellegű szöveggel rendelkeznek"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "Ellenőrizze a strukturált adatok érvényességét a [Strukturált adatok tesztelőeszköz](https://search.google.com/structured-data/testing-tool/) és a [Structured Data Linter](http://linter.structured-data.org/) segítségével. [További információ](https://web.dev/structured-data)."}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "A strukturált adatok érvényesek"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "Az oldaltartalom tömör összefoglalásának érdekében metaleírások szerepelhetnek a keresési eredményekben. [További információ](https://web.dev/meta-description)."}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "A Leírás mező üres."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "A dokumentum nem rendelkezik metaleírással"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "A dokumentum rendelkezik metaleírással"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "Sok eszköz korlátozza vagy nem támogatja a beépülő modulokat, a keresőmotorok pedig nem tudják indexelni a modulok által megjelenített tartalmakat. [További információ](https://web.dev/plugins)."}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "A dokumentum beépülő modulokat használ"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "A dokumentum nem használ beépülő modulokat"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "Ha a robots.txt fájl form<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a feltérképező robotok nem tudják <PERSON>, hogy Ön hogyan szeretné feltérképeztetni vagy indexeltetni a webhelyét. [További információ](https://web.dev/robots-txt)."}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "A robots.txt fájlra irányuló kéré<PERSON> a következő HTTP-állapotkóddal tért vissza: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 hiba található}other{# hiba található}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "A Lighthouse nem tudta letölteni a robots.txt fájlt"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "A robots.txt fájl nem érvényes"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "A robots.txt fájl érvényes"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "Az interaktív elemeknek (például a gomboknak és a linkeknek) elég nagynak kell lenniük (48 × 48 képpont), és elég helynek kell lennie k<PERSON>ük, hogy könnyen rá<PERSON>k le<PERSON> kop<PERSON> a szomszédos elemek megérintése nélkül. [További információ](https://web.dev/tap-targets)."}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent}-nyi megfelelően méretezett koppintható elem"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "A koppintási célok túl k<PERSON>, ugyanis a látható terület metacímkéje nincs optimalizálva a mobilképernyőkre"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "A koppintási célok mérete nem megfelelő"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Átfedésben lévő cél"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Koppintási cél"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "A koppintási célok mérete megfelelő"}, "lighthouse-core/audits/service-worker.js | description": {"message": "A szolgáltató munkatárs elnevezésű technológia lehető teszi az alkalmazás számára a progresszív webes alkalmazások funkcióinak használatát. Ilyen funkció például az offline működés, a kezdőképernyőhöz való hozzáadás és a leküldött (push) értesítések. [További információ](https://web.dev/service-worker)."}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "<PERSON>z oldalt szolgáltató munkatárs vezérli, azonban a(z) `start_url` nem ta<PERSON>, ugyanis nem sikerült a manifest érvényes JSON-ként való elemzése"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "Az oldalt szolgáltató munkatárs vezérli, azonban a(z) `start_url` ({startUrl}) nincs a szolgáltató munkatárs hatókörében ({scopeUrl})"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "<PERSON>z oldalt szolgáltató munkatárs vezérli, azonban a(z) `start_url` nem ta<PERSON>, mert nem si<PERSON>ült lekérni a manifestfájlt."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "Ez a forrás rendelkezik legalább egy szolgáltató munk<PERSON>á<PERSON>, azonban az oldal ({pageUrl}) nincs a hatókörükben."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "<PERSON><PERSON> re<PERSON><PERSON><PERSON> m<PERSON>, amely vezérli az oldalt és a(z) `start_url` URL-t"}, "lighthouse-core/audits/service-worker.js | title": {"message": "<PERSON><PERSON><PERSON> munkatársat regisztrál, amely vezérli az oldalt és a(z) `start_url` URL-t"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "A saját témájú betöltési képernyő jó felhasználói élményt ered<PERSON>ez, amikor a kezdőképernyőről indítják el az alkalmazást. [További információ](https://web.dev/splash-screen)."}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "<PERSON><PERSON><PERSON>ítva egyéni betöltési képernyő"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "<PERSON> állítva egyéni betöltési képernyő"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "A böngésző címsávjához megadható a webhelyhez illő téma. [További információ](https://web.dev/themed-omnibox)."}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "<PERSON><PERSON> be a téma színét a címsávon."}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "A téma színét állítja be a címsávon."}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "Fő szál akadályozásának időtartama"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "<PERSON><PERSON>dik fél"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "A harmadik felektől származó kódok jelentős hatással lehetnek a betöltés teljesítményére. Minél kevesebb harmadik féltől származó kódot használjon, és lehetőleg azután töltse be őket, hogy az oldal nagyrészt már betöltődött. [További információ](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "Harmadik féltől származó kód {timeInMs, number, milliseconds} ms-ig akadályozta a fő szál végrehajtását"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "Csökkentse a harmadik felek kódjai által kiváltott hatást"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "Harmadik féltől származó kód hasz<PERSON>a"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "<PERSON><PERSON> el<PERSON>ő bá<PERSON>tig eltelt idő azt mutatja, hogy a szerver mikor küldött választ. [További információ](https://web.dev/time-to-first-byte)."}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms a gyökérdokumentumhoz"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "Csökkentse a szerver válaszidejét (TTFB)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "Alacsony a szerver válaszideje (TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "Időtar<PERSON>"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "Kezdés ideje"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/user-timings.js | description": {"message": "Érdemes lehet felhasználnia alkalmazásában a User Timing API-t, amellyel valós hasz<PERSON>lat során mért teljesítményadatokat kaphat a legfontosabb felhasználói műveletekről. [További információ](https://web.dev/user-timings)."}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 felhasználói időmérés}other{# felhasználói időmérés}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "Felhasználói időzítőjelek és intézkedések"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Előcsatlakozási <link> található a(z) {securityOrigin} c<PERSON><PERSON><PERSON>, de a böngész<PERSON> nem használta. <PERSON><PERSON><PERSON><PERSON>, hogy megfelelően használja-e a(z) `crossorigin` attribútumot."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "Vegye fontolóra `preconnect` vagy `dns-prefetch` er<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ho<PERSON>, hogy korai kapcsolatokat hozhasson létre harmadik felekhez tartozó fontos forrásokkal. [További információ](https://web.dev/uses-rel-preconnect)."}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "Csatlakozzon előre a szükséges forrásokhoz"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Előtöltési <link> található a(z) {preloadURL} c<PERSON><PERSON><PERSON>, de a böngésző nem használta. <PERSON><PERSON><PERSON><PERSON>, hogy megfelelően használja-e a(z) `crossorigin` attribútumot."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "Fontolja meg a(z) `<link rel=preload>` hasz<PERSON><PERSON><PERSON><PERSON>, hogy prioritással tölthesse be azokat a forrásokat, melyeket az aktuális oldalbetöltés egyébként későbbre sorolt. [További információ](https://web.dev/uses-rel-preload)."}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "Töltse be előre a kulcsfontosságú kéréseket"}, "lighthouse-core/audits/viewport.js | description": {"message": "<PERSON><PERSON> ho<PERSON><meta name=\"viewport\">` <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amellyel mobilképernyőkre optimalizálhatja az alkalmazást. [További információ](https://web.dev/viewport)."}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "<PERSON><PERSON> `<meta name=\"viewport\">` címke"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "Nincs `width` vagy `initial-scale` beállítással rendelkez<PERSON> `<meta name=\"viewport\">` címkéje"}, "lighthouse-core/audits/viewport.js | title": {"message": "Van `width` vagy `initial-scale` beállítással rendelkez<PERSON> `<meta name=\"viewport\">` címkéje"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "Az alkalmazásnak akkor is meg kellene jelenítenie valamilyen tartalmat, ha a JavaScript le van tiltva. Ez akár egy figyelmeztetés is lehet, mely szerint JavaScript szükséges az alkalmazás használatához. [További információ](https://web.dev/without-javascript)."}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "Az oldal törzsében meg kellene jelennie valamilyen tartalomnak, ha a szkriptek nem használhatók."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "<PERSON><PERSON> tartalék tartalmat, ha a JavaScript nem használható"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "<PERSON><PERSON><PERSON> benne tartalmak ak<PERSON> is, amikor a JavaScript nem használható"}, "lighthouse-core/audits/works-offline.js | description": {"message": "Ha progresszív webes alkalmazást k<PERSON>, fontolja meg s<PERSON>lgáltató munkatá<PERSON>, így alkalmazása offline állapotban is működni fog. [További információ](https://web.dev/works-offline)."}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "A jelenlegi oldal nem 200-as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amikor offline állapot<PERSON> van"}, "lighthouse-core/audits/works-offline.js | title": {"message": "A jelenlegi oldal 200-as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, am<PERSON><PERSON> offline állapotban van"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az oldal offline állapotban nem töltődik be, mert a teszt-URL-t ({requested}) a rendszer a következő címre irányította át: „{final}”. Próbálkozzon a második URL közvetlen ellenőrzésével."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "Ezek a lehetőségek segíthetnek az ARIA alkalmazásban való használatának javításában, ami jobb felhasználói élményt biztosíthat a kisegítő technológiákat (például képernyőolvasót) használó személyeknek."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "<PERSON><PERSON><PERSON><PERSON>, melyek révén alternatív tartalmakat biztosíthat videókhoz és hanganyagokhoz. Ezzel javíthatja a hallás- és látássérült felhasználók élményét."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Hang- és videohív<PERSON>ok"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Itt a kisegítő lehetőségekkel kapcsolatos bevált módszereket találja."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "Ezek az ellenőrzések olyan lehetőségeket emelnek ki, melyekkel javíthatók a [webalkalmazás kisegítő lehetőségei](https://developers.google.com/web/fundamentals/accessibility). A kisegítő lehetőségekkel kapcsolatos problémáknak csak egy része észlelhető automatikusan, ezért a manuális ellenőrzés is aj<PERSON><PERSON>t."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "Az alábbiak automatikus tesztelőeszközzel nem ellenőrizhető területekre vonatkoznak. További információt a [kisegítő lehetőségek felülvizsgálatáról](https://developers.google.com/web/fundamentals/accessibility/how-to-review) szóló útmutatónkban talál."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "Kisegítő lehetőségek"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Ezek a lehetőségek a tartalom olvashatóságát segítik."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Kontraszt"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Ezek a lehetőségek a tartalom különböző országokban élő felhasználók általi értelmezését segítik."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Nemzetközi megoldások és honosítás"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Ezek a lehetőségek az alkalmazás szemantikai jellemzőinek fejlesztését segítik. Javíthatják a kisegítő technológiákat (például képernyőolvasót) használó személyek felhasználói élményét."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Nevek és címkék"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "<PERSON><PERSON><PERSON> is van a billentyűzettel való navigáció fejlesztésére az alkalmazásban."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Ezek a lehetőségek a táblázatos és listaformátumú adatok olvasási élményét javítják olyan kisegítő technológiák használata esetén, mint a képernyőolvasó."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Táblázatok és listák"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "A teljesítménybüdzsék elvárásokat határoznak meg a webhely teljesítmény<PERSON><PERSON>."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "Büdzsék"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "További információ alkalmazása teljesítményéről. Ezek a számok [nem befolyásolj<PERSON> közvetlenül](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) a teljesítménypontszámot."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnosztika"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "A teljesítmény legfontosabb szempontja az, hogy milyen gyorsan jelennek meg a képpontok a képernyőn. Legfontosabb mutatók: <PERSON><PERSON>ő, tartalommal rendelkező leképezés, Első releváns leképezés"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Az első leképezést érintő fejlesztések"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Ezek a javaslatok segíthetnek az oldalbetöltés felgyorsításában. Mindazonáltal a teljesítménypontszámra [nincs közvetlen hatásuk](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Lehetőségek"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "Mutatók"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Növelje az átfogó betöltési élményt annak érdekében, hogy az oldal reszponzív legyen, és a lehető legrövidebb időn belül használhatóvá váljon. Főbb mutatók: interaktivitásig eltelt idő, sebességindex"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Átfogó j<PERSON>"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "Teljesítmény"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "Ezek az ellenőrzések különböző szempontokból érvényesítik a progresszív webes alkalmazásokat. [További információ](https://developers.google.com/web/progressive-web-apps/checklist)."}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "Ezek az ellenőrzések kötelezők a [progresszív webes alkalmazások alapvető ellenőrzőlistáján](https://developers.google.com/web/progressive-web-apps/checklist), de a Lighthouse nem végzi el őket automatikusan. Az ellenőrzések a kapott pontszámot nem befolyásolják, de fontos a manuális végrehajtásuk."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "Progresszív webes alkalmazás"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "Gyors és megbízható"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Telepíthető"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PWA-optimalizálás"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "Ezekkel az ellenőrzésekkel győződhet meg arról, hogy oldala megfelelően optimalizált-e a keresőmotorok találati rangsorolásához. Vannak olyan egy<PERSON>, a Lighthouse által nem ellenőrzött tényez<PERSON>k is, amelyek befoly<PERSON>olhatják a keresésekben elért helyez<PERSON>t. [További információ](https://support.google.com/webmasters/answer/35769)."}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "Ha ezeket a további érvényesítőket is futtat<PERSON>, egyéb bevált SEO-módszereket is ellenőrizhet."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "Keresőoptimalizálás"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "Formázza úgy a HTML-kódot, hogy a feltérképező robotok jobban megérthessék az alkalmazás tartalmait."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON> bevált mó<PERSON>"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "A feltérképező robotoknak hozzáférésre van szükségük az alkalmazáshoz annak érdekében, hogy a webhely megjelenhessen a keresési találatok között."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Feltérképezés és indexelés"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "Ha mobilbarát oldalakat k<PERSON>zí<PERSON>, a tartalmak felnagyítás nélkül is olvashatók lesznek. [További információ](https://developers.google.com/search/mobile-sites/)."}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Gyorsítótár-TTL"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "Név"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "K<PERSON><PERSON>sek"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "<PERSON><PERSON><PERSON> t<PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Eltöltött idő"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "Átvitel<PERSON> mé<PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Potenci<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "Potenci<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Potenciálisan {wastedBytes, number, bytes} KB megtakarítás"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Potenciálisan {wastedMs, number, milliseconds} ms megtakarí<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokumentum"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "Betűtípus"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "Média"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "Szkript"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} mp"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Harmadik féltől származó"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "Összes"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "Hiba történt az oldalbetöltés nyomának rögzítése során. Futtassa újra a Lighthouse szolgáltatást. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "Időtúllépés a hibaelhárító protokoll kezdeti kapcsolatára való várakozás során."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "A Chrome nem gyűjtött képernyőképeket az oldal betöltése során. <PERSON><PERSON><PERSON><PERSON>, hogy van-e látható tartalom az oldalon, majd próbálja újra futtatni a Lighthouse szolgáltatást. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "A DNS-szerverek nem tudták értelmezni a megadott domaint."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "A szükséges {artifactName} begyűjtő hibába ütközött: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "Belső Chrome-hiba történt. Indítsa újra a Chrome-ot, és próbálja újra futtatni a Lighthouse szolgáltatást."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "A kötelező {artifactName} nem futott le."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "A Lighthouse nem tudta megfelelően betölteni a kért oldalt. Győződjön meg arról, hogy a helyes URL-t teszteli, és a szerver megfelelően válaszol az összes kérelemre."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "A Lighthouse nem tudta megfelelően betölteni a kért URL-t, mert az oldal nem válaszol."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "A megadott URL-hez nem tartozik érvényes biztonsági tanúsítvány. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "A Chrome közbeiktatott képernyő megjelenítésével megakadályozta az oldal betöltését. <PERSON><PERSON><PERSON><PERSON>, hogy a helyes URL-t teszteli-e, és hogy a szerver minden kérésre megfelelően reagál-e."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "A Lighthouse nem tudta megbízhatóan betölteni a kért oldalt. <PERSON><PERSON><PERSON><PERSON>, hogy a helyes URL-t teszteli-e, és hogy a szerver minden kérésre megfelelően reagál-e. (Részletek: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "A Lighthouse nem tudta megb<PERSON>zhatóan betölteni a kért oldalt. <PERSON><PERSON><PERSON><PERSON>, hogy a helyes URL-t teszteli-e, és hogy a szerver minden kérésre megfelelően reagál-e. (Állapotkód: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Túl sok<PERSON>ig tartott az oldal betöltése. Csökkentse az oldalbetöltési időt a jelentésben leírt lehetőségeket követve, majd futtassa újra a Lighthouse-t. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "A DevTools protokoll válaszára való várakozás túllépte a megengedett időt. (Módszer: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "A tartalom lekérése túllépte a megengedett időt"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "<PERSON><PERSON>, a megadott URL érvénytelen."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "Ellenőrzések megjeleníté<PERSON>"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "Kezdeti navigáció"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "Kritikus elérési út maximális várakozási ideje:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "<PERSON><PERSON>!"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "Jelentési hiba: nincs ellenőrzési információ"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "Laboradatok"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "Az aktuá<PERSON> [Lighthouse](https://developers.google.com/web/tools/lighthouse/)-elemzése emulált mobilhálózaton. Az értékek becsültek és változhatnak."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> elemzendő elemek"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "<PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "Lehetőség"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> megtakarí<PERSON>"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "Sikeresen teljesített ellenőrzések"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "K<PERSON>dr<PERSON>zlet összecsukása"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kibont<PERSON>"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "Harmadik féltől származó források megjelenítése"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "A Lighthouse-futtatást befolyásoló problémák fordultak elő:"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "Az értékek becsültek és változhatnak. A teljesítménypontszám [csak ezeken a mutatókon alapszik](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "Átment az ellenőrzéseken – figyelmeztetésekkel"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "Figyelmeztetések: "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "Fontolja meg a GIF-fájlok olyan szolgáltatóhoz való feltöltését, amely lehetővé teszi a fájlok HTML5-videóként történő beágyazását."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "Telepítsen [lusta betöltést biztosító WordPress-bővítményt](https://wordpress.org/plugins/search/lazy+load/), amely lehetőséget ad a képernyőn kívül eső képek késleltetett betöltésére – vagy v<PERSON> olyan té<PERSON>, amely rendelkezik ezzel a funkcióval. Fontolja meg az [AMP-bővítmény](https://wordpress.org/plugins/amp/) használatát is."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "Számos WordPress-bővítmény segíthet a [kritikus elemek beágyazásában](https://wordpress.org/plugins/search/critical+css/) és a [kevésbé fontos források](https://wordpress.org/plugins/search/defer+css+javascript/) késleltetésében. Fontos, hogy az ilyen jellegű bővítmények által nyújtott optimalizációk működésképtelenné tehetik a témák vagy más bővítmények funkcióit, ezért valószínűleg módosítania kell majd a meglévő kódot."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "A témák, a bővítmények és a szerver specifikációi mind befolyásolják a szerver válaszidejét. Érdemes lehet jobban optimalizált témát kere<PERSON>nie, megfelelő optimalizáló bővítményt választania és/vagy nagyobb teljesítményű szerverre váltania."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "Fontolja meg kivonatok megjelenítését a bejegyzéslistákban (pl. a more címkével), az oldalanként megjelenített bejegyzések számának csökkentését, a hosszabb bejegyzések több oldalra tördelését, valamint a hozzászólások lusta betöltését bővítmény segítségével."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "Számos [WordPress-bővítmény](https://wordpress.org/plugins/search/minify+css/) gyorsíthat webhelyén a stíluslapok egyesítésével, minimalizálásával és tömörítésével. Ha lehetséges, a minimalizálást érdemes buildfolyamat használatával előre elvégezni."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "Számos [WordPress-bővítmény](https://wordpress.org/plugins/search/minify+javascript/) gyorsíthat webhelyén a szkriptek egyesítésével, minimalizálásával és tömörítésével. Ha lehetséges, a minimalizálást érdemes buildfolyamat használatával előre elvégezni."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "Fontolja meg a nem használt CSS-t betölt<PERSON> [WordPress-bővítmény<PERSON>](https://wordpress.org/plugins/) lecserélését, vagy számuk csökkentését. A felesleges CSS-t elhelyez<PERSON> bővítmények azonosításában a Chrome DevTools [kódlefedettség](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) eszköze is segíthet. A felelős téma vagy bővítmény a stíluslap URL-je alapján azonosítható. Keressen olyan bővítményeket, amelyeknek több stíluslapj<PERSON>ban is sok piros szín szerepel a kódlefedettségi listán. Ideális esetben a bővítmények csak az oldalon ténylegesen használt stíluslapokat állítják sorba."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "Fontolja meg a nem használt JavaScriptet betöltő [WordPress-bővítmények](https://wordpress.org/plugins/) lecserélését vagy számuk csökkentését. A felesleges JavaScriptet elhelyező bővítmények azonosításában a Chrome DevTools [kódlefedettség](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) eszköze is segíthet. A felelős téma vagy bővítmény a szkript URL-je alapján azonosítható. Keressen olyan bővítményeket, amelyeknek több szkriptjében is sok piros szín szerepel a kódlefedettségi listán. Ideális esetben a bővítmények csak az oldalon ténylegesen használt szkripteket állítják sorba."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> információ a [WordPress böngészős gyorsítótárazásáról](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "Fontolja meg olyan [képoptimalizáló WordPress-bővítmény](https://wordpress.org/plugins/search/optimize+images/) has<PERSON><PERSON><PERSON><PERSON><PERSON>, mellyel minőségromlás nélkül tömöríthetők a képek."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "A képeket közvetlenül a [Media Library](https://codex.wordpress.org/Media_Library_Screen) felü<PERSON>én töltse fel, hogy biztosan minden szükséges képméret rendelkezésre álljon, e<PERSON><PERSON><PERSON> pedig innen sz<PERSON><PERSON>ja be <PERSON>ket, vagy használja az Image Widgetet, hogy biztosan az optimális képméreteket alkalmazza (a reszponzív töréspontok esetében is) `Full Size` képeket csak akkor használjon, ha méretük megfelel a felhasználás módjának. [További információ](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)."}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "A szövegtömörítést a webszerver konfigurációjában engedélyezheti."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "Érdemes olyan [bőv<PERSON><PERSON><PERSON><PERSON><PERSON>](https://wordpress.org/plugins/search/convert+webp/) v<PERSON>y szo<PERSON>t használnia, amely automatikusan az optimális formátumba konvertálja a feltöltött képeket."}}