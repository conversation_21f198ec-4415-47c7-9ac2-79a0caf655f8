{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "Тастери за приступ омогућавају корисницима да брзо фокусирају део странице. Да би навигација радила исправно, сваки тастер за приступ мора да буде јединствен. [Сазнајте више](https://web.dev/accesskeys/)."}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Вредности за `[accesskey]` нису јединствене"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "Вредности за `[accesskey]` су јединствене"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Сваки ARIA елемент „`role`“ подржава одређени подскуп атрибута „`aria-*`“. Ако се ови елементи не подударају, атрибути „`aria-*`“ ће бити неважећи. [Сазнајте више](https://web.dev/aria-allowed-attr/)."}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Атрибути `[aria-*]` се не подударају са својим улогама"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Атрибути `[aria-*]` се подударају са својим улогама"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "Поједине ARIA улоге имају обавезне атрибуте који статус елемента описују читачима екрана. [Сазнајте више](https://web.dev/aria-required-attr/)."}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Улоге `[role]` немају све обавезне атрибуте `[aria-*]`"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "Улоге `[role]` имају све обавезне атрибуте `[aria-*]`"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "Поједине надређене ARIA улоге морају да обухватају одређене подређене улоге да би правилно обављале намењене функције приступачности. [Сазнајте више](https://web.dev/aria-required-children/)."}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Елементима са ARIA улогом `[role]` који захтевају да подређени елементи садрже конкретни елемент `[role]` недостају неки или сви ти потребни подређени елементи."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "Елементи са ARIA улогом `[role]` који захтевају да подређени елементи садрже конкретни елемент `[role]` имају све потребне подређене елементе."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "Поједине подређене ARIA улоге морају да буду обухваћене одређеним надређеним улогама да би правилно обављале намењене функције приступачности. [Сазнајте више](https://web.dev/aria-required-parent/)."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Улоге `[role]` нису обухваћене својим обавезним надређеним елементом"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "Улоге `[role]` су обухваћене својим обавезним надређеним елементом"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "Вредности ARIA улога морају да буду важеће да би правилно обављале намењене функције приступачности. [Сазнајте више](https://web.dev/aria-roles/)."}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "Вредности за `[role]` нису важеће"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "Вредности за `[role]` су важеће"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Технологије за помоћ особама са инвалидитетом, попут читача екрана, не могу да интерпретирају ARIA атрибуте са неважећим вредностима. [Сазнајте више](https://web.dev/aria-valid-attr-value/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Вредности атрибута `[aria-*]` нису важеће"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Вредности атрибута `[aria-*]` су важеће"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "Технологије за помоћ особама са инвалидитетом, попут читача екрана, не могу да интерпретирају ARIA атрибуте са неважећим називима. [Сазнајте више](https://web.dev/aria-valid-attr/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Атрибути `[aria-*]` нису важећи или су погрешно написани"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "Атрибути `[aria-*]` су важећи и нису погрешно написани"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "Титлови омогућавају да глуви корисници или корисници са оштећењем слуха користе аудио елементе, чиме се пружају важне информације, на пример, информације о томе која особа говори, шта говори, као и друге информације које нису везане за говор. [Сазнајте више](https://web.dev/audio-caption/)."}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "Елементима `<audio>` недостаје елемент `<track>` са атрибутом `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "Елементи `<audio>` садрже елемент `<track>` са атрибутом `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Елементи који нису прошли проверу"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "Када дугме нема назив прилагођен функцији приступачности, читачи екрана га најављују као „дугме“, па корисници који се ослањају на читаче екрана не могу да га користе. [Сазнајте више](https://web.dev/button-name/)."}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "Дугмад нема називе прилагођене функцијама приступачности"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "Дугмад има називе прилагођене функцијама приступачности"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "Када се додају начини за заобилажење садржаја који се понавља, корисници тастатуре могу ефикасније да се крећу по страници. [Сазнајте више](https://web.dev/bypass/)."}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "Страница не обухвата наслов, линк за прескакање нити регион оријентира"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "Страница обухвата наслов, линк за прескакање или регион оријентира"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "Многи корисници веома тешко читају текст са малим контрастом или уопште не могу да га читају. [Сазнајте више](https://web.dev/color-contrast/)."}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Боје у позадини и у првом плану немају задовољавајући однос контраста."}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "Боје у позадини и у првом плану имају задовољавајући однос контраста"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "Када листе дефиниција нису правилно означене, читачи екрана могу да пружају збуњујући или нетачан излаз. [Сазнајте више](https://web.dev/definition-list/)."}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>` не садржи само правилно наручене групе `<dt>` и `<dd>`, елементе `<script>` или `<template>`."}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` садржи само правилно наручене групе`<dt>` и `<dd>`, елементе `<script>` или `<template>`."}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "Ставке листе дефиниција (`<dt>` и `<dd>`) морају да буду упаковане у надређени елемент`<dl>` да би читачи екрана могли да их правилно читају. [Сазнајте више](https://web.dev/dlitem/)."}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "Ставке листе дефиниција су упаковане у елементе `<dl>`"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "Ставке листе дефиниција су упаковане у елементе`<dl>`"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "Наслов корисницима читача екрана пружа преглед странице, а корисници претраживача се на њега ослањају да би утврдили да ли је страница релевантна за њихову претрагу. [Сазнајте више](https://web.dev/document-title/)."}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "Документ нема елемент `<title>`"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "Документ има елемент `<title>`"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "Вредност атрибута ИД мора да буде јединствена да би се спречило да технологије за помоћ особама са инвалидитетом пропусте друге инстанце. [Сазнајте више](https://web.dev/duplicate-id/)."}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "Атрибути `[id]` на страници нису јединствени"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "Атрибути `[id]` на страници су јединствени"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "Корисници читача екрана очекују од наслова оквира да им опишу садржај оквира. [Сазнајте више](https://web.dev/frame-title/)."}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "Елементи `<frame>` или `<iframe>` немају наслов"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "Елементи `<frame>` или `<iframe>` имају наслов"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "Ако за страницу није наведен атрибут за језик, читач екрана претпоставља да је страница на подразумеваном језику који је корисник одабрао током подешавања читача екрана. Ако страница заправо није на подразумеваном језику, читач екрана можда неће правилно читати текст са странице. [Сазнајте више](https://web.dev/html-has-lang/)."}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Елемент `<html>` нема атрибут `[lang]`"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "Елемент `<html>` има атрибут `[lang]`"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "Навођењем важећег кода [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) омогућава се да читач екрана правилно чита текст. [Сазнајте више](https://web.dev/html-lang-valid/)."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Елемент `<html>` нема важећу вредност за свој атрибут `[lang]`."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "Елемент `<html>` има важећу вредност за свој атрибут `[lang]`"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "Информативни елементи треба да садрже кратки, описни алтернативни текст. Декоративни елементи могу да се занемаре празним атрибутом alt. [Сазнајте више](https://web.dev/image-alt/)."}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "Елементи слике немају атрибуте `[alt]`"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "Елементи слика имају атрибуте `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "Када се слика користи као дугме `<input>`, навођење алтернативног текста може да помогне корисницима да разумеју сврху дугмета. [Сазнајте више](https://web.dev/input-image-alt/)."}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Елементи `<input type=\"image\">` не садрже текст `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "Елементи `<input type=\"image\">` садрже текст `[alt]`"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "Ознаке омогућавају да технологије за помоћ особама са инвалидитетом, попут читача екрана, правилно најављују контроле образаца. [Сазнајте више](https://web.dev/label/)."}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "Елементи образаца немају повезане ознаке"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "Елементи образаца имају повезане ознаке"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "Табела која се користи у сврхе распореда не треба да обухвата елементе података, као што су елементи th или титл или атрибут резимеа јер то може да збуни кориснике читача екрана. [Сазнајте више](https://web.dev/layout-table/)."}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "Презентациони елементи `<table>` не избегавају употребу атрибута `<th>`, `<caption>` или `[summary]`."}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "Презентациони елементи `<table>` избегавају употребу атрибута `<th>`, `<caption>` или `[summary]`."}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "Текст линка (и алтернативни текст за слике када се користи за линкове) који је препознатљив, јединствен и може да се фокусира олакшава кретање за кориснике читача екрана. [Сазнајте више](https://web.dev/link-name/)."}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "Назив линкова не може да се препозна"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "Назив линкова може да се препозна"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "Читачи екрана читају листе на посебан начин. Правилна структура листе олакшава разумевање читача екрана. [Сазнајте више](https://web.dev/list/)."}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "Листе не садрже искључиво елементе `<li>` и елементе који подржавају скрипте (`<script>` и`<template>`)."}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "Листе садрже искључиво елементе `<li>` и елементе који подржавају скрипте (`<script>` и `<template>`)."}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "Читачи екрана захтевају да ставке листе (`<li>`) буду обухваћене надређеним елементима `<ul>` или `<ol>` да би могле да се правилно читају. [Сазнајте више](https://web.dev/listitem/)."}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "Ставке листе (`<li>`) нису обухваћене надређеним елементима`<ul>` или `<ol>`."}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "Ставке листе (`<li>`) су обухваћене надређеним елементима `<ul>` или `<ol>`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "Корисници не очекују да се страница аутоматски освежава и тиме се фокус премешта на почетак странице. То може да фрустира или збуњује кориснике. [Сазнајте више](https://web.dev/meta-refresh/)."}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Документ користи метаознаку `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "Документ не користи метаознаку `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "Онемогућавање зумирања представља проблем за слабовиде кориснике који се ослањају на увећавање приказа екрана да би могли да виде садржај веб-странице. [Сазнајте више](https://web.dev/meta-viewport/)."}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` се користи у елементу `<meta name=\"viewport\">` или је вредност атрибута `[maximum-scale]` мања од 5."}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` се не користи у елементу `<meta name=\"viewport\">`, а вредност атрибута `[maximum-scale]` није мања од 5."}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "Читачи екрана не могу да преводе садржај који није текст. Додавање alt текста елементима `<object>` омогућава да читачи екрана лакше пренесу значење корисницима. [Сазнајте више](https://web.dev/object-alt/)."}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "Елементи `<object>` не садрже текст `[alt]`"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "Елементи `<object>` садрже текст `[alt]`"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "Вредност већа од 0 означава експлицитно наручивање навигације. Иако је технички исправно, то често фрустрира кориснике који се ослањају на технологије за помоћ особама са инвалидитетом. [Сазнајте више](https://web.dev/tabindex/)."}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "Неки елементи имају вредност за `[tabindex]` која је већа од 0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "Вредност ниједног елемента `[tabindex]` није већа од 0"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "Читачи екрана имају функције које олакшавају кретање кроз табеле. Ако се побринете да се ћелије `<td>` које користе атрибут `[headers]` односе само на друге ћелије у истој табели, можете да побољшате доживљај за кориснике читача екрана. [Сазнајте више](https://web.dev/td-headers-attr/)."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Ћелије у елементу `<table>` које користе атрибут `[headers]` односе се на елемент `id` који се не налази у истој табели."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "Ћелије у елементу `<table>` које користе атрибут `[headers]` односе се на ћелије табеле у истој табели."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "Читачи екрана имају функције које олакшавају кретање кроз табеле. Ако се побринете да се наслови табела увек односе на неку групу ћелија, можете да побољшате доживљај за кориснике читача екрана. [Сазнајте више](https://web.dev/th-has-data-cells/)."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Елементи `<th>` и елементи са атрибутом`[role=\"columnheader\"/\"rowheader\"]` немају ћелије са подацима које описују."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "Елементи `<th>` и елементи са атрибутом `[role=\"columnheader\"/\"rowheader\"]` имају ћелије са подацима које описују."}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "Навођењем важећег кода [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) у елементима омогућава се да читач екрана правилно чита текст. [Сазнајте више](https://web.dev/valid-lang/)."}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Вредност атрибута `[lang]` није важећа"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "Атрибути `[lang]` имају важећу вредност"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "Када је доступан титл за видео, глуви корисници и они са оштећењем слуха лакше могу да приступају информацијама које видео обухвата. [Сазнајте више](https://web.dev/video-caption/)."}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "Елементи `<video>` не обухватају елемент `<track>` са атрибутом `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "Елементи `<video>` садрже елемент `<track>` са атрибутом `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "Аудио описи пружају релевантне информације за видео снимке које дијалог не може, попут израза лица и сцена. [Сазнајте више](https://web.dev/video-description/)."}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "Елементи `<video>` не обухватају елемент `<track>` са атрибутом `[kind=\"description\"]`."}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "Елементи `<video>` садрже елемент `<track>` са атрибутом `[kind=\"description\"]`"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "Да би изглед на iOS-у био идеалан када корисници додају прогресивну веб-апликацију на почетни екран, дефинишите икону `apple-touch-icon`. Она мора да усмерава на нетранспарентни квадратни PNG од 192 пиксела (или 180 пиксела). [Сазнајте више](https://web.dev/apple-touch-icon/)."}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "Не пружа важећи атрибут `apple-touch-icon`"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "Атрибут `apple-touch-icon-precomposed` је застарео; препоручује се `apple-touch-icon`."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "Пружа важећи атрибут `apple-touch-icon`"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Додаци за Chrome су негативно утицали на брзину учитавања ове странице. Пробајте да проверите страницу у режиму без архивирања или са Chrome профила без додатака."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "Процена скрипта"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "Рашчлањивање скрипта"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "Укупно CPU време"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "Препоручујемо вам да смањите време потребно за рашчлањивање, компајлирање и извршавање JS датотека. Приказивање мањих JS ресурса ће вам можда помоћи у томе. [Сазнајте више](https://web.dev/bootup-time)."}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "Смањите време извршавања JavaScript датотека"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "Време извршавања JavaScript-а"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Велики GIF-ови нису корисни за приказивање анимираног садржаја. Препоручујемо вам да уместо GIF-ова користите MPEG4/WebM видео снимке за анимације и PNG/WebP за статичне слике да бисте уштедели мрежне податке. [Сазнајте више](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Користите видео формате за анимирани садржај"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Препоручујемо вам да одложите учитавање слика ван екрана и скривених слика док се сви веома важни ресурси не учитају како бисте смањили време до почетка интеракције. [Сазнајте више](https://web.dev/offscreen-images)."}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Одложите слике ван екрана"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Ресурси блокирају прво приказивање странице. Препоручујемо вам да приказујете све важне JS/CSS датотеке у тексту и да одложите све JS датотеке/стилове који нису толико важни. [Сазнајте више](https://web.dev/render-blocking-resources)."}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Елиминишите ресурсе који блокирају приказивање"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Велике мрежне ресурсе корисници морају да плате стварним новцем и они су веома повезани са дугим временима учитавања. [Сазнајте више](https://web.dev/total-byte-weight)."}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Укупна величина је била {totalBytes, number, bytes} kB"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Избегавајте огромне мрежне ресурсе"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Избегава огромне мрежне ресурсе"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "Умањивањем CSS датотека можете да смањите величине мрежних ресурса. [Сазнајте више](https://web.dev/unminified-css)."}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "Умањите CSS"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Умањивање JavaScript датотека може да смањи величине ресурса и време рашчлањивања скрипта. [Сазнајте више](https://web.dev/unminified-javascript)."}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Умањите JavaScript"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Уклоните неактивна правила из описа стилова и одложите учитавање CSS-а који се не користи за садржај изнад прелома да бисте смањили непотребну потрошњу података током мрежних активности. [Сазнајте више](https://web.dev/unused-css-rules)."}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Уклоните некоришћени CSS"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Уклоните некоришћени JavaScript да бисте смањили потрошњу података током мрежних активности."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Уклоните некоришћени JavaScript"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Дуго трајање кеша може да убрза поновне посете страници. [Сазнајте више](https://web.dev/uses-long-cache-ttl)."}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Пронађен је 1 ресурс}one{Пронађен је # ресурс}few{Пронађена су # ресурса}other{Пронађено је # ресурса}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Приказујте статичне елементе са ефикасним смерницама кеша"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Користи ефикасне смернице кеша на статичним елементима"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Оптимизоване слике се учитавају брже и троше мање мобилних података. [Сазнајте више](https://web.dev/uses-optimized-images)."}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Ефикасно кодирајте слике"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Приказујте слике одговарајуће величине да бисте уштедели мобилне податке и побољшали време учитавања. [Сазнајте више](https://web.dev/uses-responsive-images)."}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Одредите одговарајућу величину слика"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Ресурсе засноване на тексту треба да приказујете у компримованом формату (gzip, deflate или brotli) да бисте смањили укупну количину потрошених мрежних података. [Сазнајте више](https://web.dev/uses-text-compression)."}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Омогућите компресију текста"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "Формати слика као што су JPEG 2000, JPEG XR и WebP често пружају бољу компресију него PNG или JPEG, што подразумева бржа преузимања и мању потрошњу података. [Сазнајте више](https://web.dev/uses-webp-images)."}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "Приказујте слике у форматима следеће генерације"}, "lighthouse-core/audits/content-width.js | description": {"message": "Ако се ширина садржаја апликације не подудара са ширином области приказа, апликација можда није оптимизована за екране на мобилним уређајима. [Сазнајте више](https://web.dev/content-width)."}, "lighthouse-core/audits/content-width.js | explanation": {"message": "Величина области приказа од {innerWidth} пиксела се не подудара са величином прозора од {outerWidth} пиксела."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "Садржај није одговарајуће величине за област приказа"}, "lighthouse-core/audits/content-width.js | title": {"message": "Садржај је одговарајуће величине за област приказа"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "Ланци веома важних захтева у наставку вам приказују који ресурси се учитавају са високим приоритетом. Препоручујемо вам да смањите дужину ланаца, да смањите величину преузимања за ресурсе или да одложите преузимање ресурса који нису неопходни ради бржег учитавања странице. [Сазнајте више](https://web.dev/critical-request-chains)."}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Пронађен је 1 ланац}one{Пронађен је # ланац}few{Пронађена су # ланца}other{Пронађено је # ланаца}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "Смањите број веома важних захтева"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "Застарело/упозорење"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "Ред"}, "lighthouse-core/audits/deprecations.js | description": {"message": "Застар<PERSON><PERSON>и API-ји ће на крају бити уклоњени из прегледача. [Сазнајте више](https://web.dev/deprecations)."}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Откривено је 1 упозорење}one{Откривено је # упозорење}few{Откривена су # упозорења}other{Откривено је # упозорења}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "Користи застареле API-је"}, "lighthouse-core/audits/deprecations.js | title": {"message": "Избегава застареле API-је"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "Кеш апликације је застарео. [Сазнајте више](https://web.dev/appcache-manifest)."}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "Пронађено је „{AppCacheManifest}“"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "Користи кеш апликације"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "Избегава кеш апликације"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "Навођењем doctype-а спречава се прелазак на архајски режим прегледача. [Сазнајте више](https://web.dev/doctype)."}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Назив за doctype мора да буде стринг написан малим словима `html`"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Документ мора да садржи doctype"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Очекивани publicId ће бити празан стринг"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Очекивани systemId ће бити празан стринг"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Страници недостаје HTML doctype, па се активира архајски режим"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "Страница има HTML doctype"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "Елемент"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Статистика"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Вредност"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "Инжењери за прегледаче препоручују да странице садрже мање од приближно 1500 DOM елемената. Најбоље би било да дубина стабла буде испод 32 елемента и да има мање од 60 подређених/надређених елемената. Велики DOM може да повећа потрошњу меморије, да изазове дужа [израчунавања стилова](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) и да доведе до скупих [преобликовања изгледа](https://developers.google.com/speed/articles/reflow). [Сазнајте више](https://web.dev/dom-size)."}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 елемент}one{# елемент}few{# елемента}other{# елемената}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Избегавајте превелику величину DOM-а"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Максимална дубина DOM-а"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Укупан број DOM елемената"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Максималан број подређених елемената"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "Избегава превелику величину DOM-а"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "Циљ"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "Додајте `rel=\"noopener\"` или`rel=\"noreferrer\"` свим спољним линковима да бисте побољшали учинак и спречили безбедносне пропусте. [Сазнајте више](https://web.dev/external-anchors-use-rel-noopener)."}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "Линкови до одредишта из других извора нису безбедни"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "Линкови до одредишта из других извора су безбедни"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "Није могуће одредити одредиште за текст линка ({anchorHTML}). Ако се не користи као хиперлинк, препоручујемо вам да уклоните target=_blank."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Корисници немају поверења у сајтове који траже њихову локацију без контекста или их такви сајтови збуњују. Препоручујемо вам да уместо тога повежете захтев са радњом коју обавља корисник. [Сазнајте више](https://web.dev/geolocation-on-start)."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Тражи дозволу за геолоцирање при учитавању странице"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Избегавајте тражење дозволе за геолоцирање при учитавању странице"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Верзија"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "Све корисничке JavaScript библиотеке откривене на овој страници. [Сазнајте више](https://web.dev/js-libraries)."}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "Откривене су JavaScript библиотеке"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "Ако корисници имају споре везе, спољне скрипте које се динамички убацују помоћу атрибута `document.write()` могу да одложе учитавање странице за десетине секунди. [Сазнајте више](https://web.dev/no-document-write)."}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Користи `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "Избегава атрибут `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "Највиши ниво озбиљности"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "Верзија библиотеке"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "Број пропуста"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "Неке независне скрипте могу да обухватају познате безбедносне пропусте које нападачи могу лако да препознају и искористе. [Сазнајте више](https://web.dev/no-vulnerable-libraries)."}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{Откривен је 1 пропуст}one{Откривен је # пропуст}few{Откривена су # пропуста}other{Откривено је # пропуста}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "Обухвата корисничке JavaScript датотеке са познатим безбедносним пропустима"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "Висока"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "Ниска"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "Средња"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "Избегава корисничке JavaScript датотеке са познатим безбедносним пропустима"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "Корисници немају поверења у сајтове који траже дозволу за слање обавештења без контекста или их такви сајтови збуњују. Препоручујемо вам да уместо тога повежете захтев са покретима корисника. [Сазнајте више](https://web.dev/notification-on-start)."}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Тражи дозволу за обавештења при учитавању странице"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "Избегавајте тражење дозволе за обавештења при учитавању странице"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "Елементи који нису прошли проверу"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "Спречавање лепљења лозинке нарушава добре смернице за безбедност. [Сазнајте више](https://web.dev/password-inputs-can-be-pasted-into)."}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "Спречава кориснике да налепе вредност у поља за лозинке"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "Дозвољава корисницима да налепе вредност у поља за лозинке"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Протокол"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 има бројне предности у односу на HTTP/1.1, укључујући бинарна заглавља, мултиплексирање и серверски пуш. [Сазнајте више](https://web.dev/uses-http2)."}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 захтев није испоручен преко протокола HTTP/2}one{# захтев није испоручен преко протокола HTTP/2}few{# захтева нису испоручена преко протокола HTTP/2}other{# захтева није испоручено преко протокола HTTP/2}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "Не користи HTTP/2 за све своје ресурсе"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "Користи HTTP/2 за своје ресурсе"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Препоручујемо вам да пасивне обрађиваче догађаја означите као `passive` да бисте побољшали резултате померања. [Сазнајте више](https://web.dev/uses-passive-event-listeners)."}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Не користите пасивне обрађиваче да бисте побољшали учинак померања"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Користите пасивне обрађиваче да бисте побољшали учинак померања"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "Грешке евидентиране у конзоли указују на нерешене проблеме. Оне су резултат неуспелих мрежних захтева и других проблема у вези са прегледачем. [Сазнајте више](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "Грешке прегледача су евидентиране у конзоли"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "Ниједна грешка прегледача није евидентирана у конзоли"}, "lighthouse-core/audits/font-display.js | description": {"message": "Искористите CSS функцију за приказ фонтова да бисте били сигурни да корисник може да види текст док се веб-фонтови учитавају. [Сазнајте више](https://web.dev/font-display)."}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "Побрините се да текст остане видљив током учитавања веб-фонтова"}, "lighthouse-core/audits/font-display.js | title": {"message": "Сав текст остаје видљив током учитавања веб-фонтова"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "Lighthouse није успео да аутоматски провери вредност за приказ фонтова за следећи URL: {fontURL}."}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "Размера (стварна)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Размера (приказана)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "Димензије приказа слике треба да се подударају са природном размером. [Сазнајте више](https://web.dev/image-aspect-ratio)."}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "Приказује слике са погрешном размером"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "Приказује слике са тачном размером"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "Неважеће информације о величини слике {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "Прегледачи могу проактивно да траже од корисника да додају апликацију на почетни екран, што може да доведе до већег ангажовања. [Сазнајте више](https://web.dev/installable-manifest)."}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "Манифест веб-апликације не задовољава услове за инсталирање"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "Манифест веб-апликације задовољава услове за инсталирање"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "Небезбедан URL"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "Сви сајтови треба да буду заштићени HTTPS-ом, чак и они који не обрађују осетљиве податке. HTTPS спречава уљезе да неовлашћено приступају комуникацији између апликације и корисника или да је пасивно слушају. Он је предуслов за HTTP/2 i API-је бројних нових веб-платформи. [Сазнајте више](https://web.dev/is-on-https)."}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Пронађен је 1 небезбедан захтев}one{Пронађен је # небезбедан захтев}few{Пронађена су # небезбедна захтева}other{Пронађено је # небезбедних захтева}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "Не користи HTTPS"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "Користи HTTPS"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "Брзо учитавање странице преко мобилне мреже обезбеђује добар доживљај корисницима мобилних уређаја. [Сазнајте више](https://web.dev/load-fast-enough-for-pwa)."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "Интерактивно за {timeInMs, number, seconds} сек"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "Интерактивна на симулираној мобилној мрежи за {timeInMs, number, seconds} сек"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "Страница се преспоро учитава и не постаје интерактивна у року од 10 секунди. Погледајте прилике и дијагностику у одељку „Учинак“ да бисте сазнали како да побољшате учинак странице."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "Учитавање странице није довољно брзо на мобилним мрежама"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "Учитавање странице је довољно брзо на мобилним мрежама"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Категорија"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "Препоручујемо вам да смањите време потребно за рашчлањивање, компајлирање и извршавање JS датотека. Приказивање мањих JS ресурса ће вам можда помоћи у томе. [Сазнајте више](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Смањите рад главне нити"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "Смањује рад главне нити"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "Да би имали што више корисника, сајтови треба да раде у свим значајнијим прегледачима. [Сазнајте више](https://web.dev/pwa-cross-browser)."}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "Сајт ради у различитим веб-прегледачима"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Уверите се да до појединачних страница воде прецизни линкови преко URL-ова и да су URL-ови јединствени у сврху дељења на друштвеним медијима. [Сазнајте више](https://web.dev/pwa-each-page-has-url)."}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Свака страница има URL"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "Корисници треба да имају утисак да су прелази брзи док додирују ставке, чак и на спорој мрежи. Овај доживљај је кључан за утисак који ће корисник имати о учинку. [Сазнајте више](https://web.dev/pwa-page-transitions)."}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "Преласци са странице на страницу не делују као да се блокирају због мреже"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "Процењено кашњење уноса је процена времена које је апликацији потребно да одговори на унос корисника, у милисекундама, током најпрометнијег рока од 5 секунди за учитавање странице. Ако је кашњење веће од 50 ms, корисници ће можда сматрати да апликација ради споро. [Сазнајте више](https://web.dev/estimated-input-latency)."}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "Процењено кашњење уноса"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "Прво приказивање садржаја означава време када се приказују први текст или слика. [Сазнајте више](https://web.dev/first-contentful-paint)."}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "Прво приказивање садржаја"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "Време првог неактивног процесора означава први тренутак у ком је главна нит странице довољно неактивна да би обрадила унос.  [Сазнајте више](https://web.dev/first-cpu-idle)."}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "Време првог неактивног процесора"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "Прво значајно приказивање означава време када примарни садржај странице постаје видљив. [Сазнајте више](https://web.dev/first-meaningful-paint)."}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "Прво значајно приказивање"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "Време до интеракције је количина времена која је потребна да би страница постала потпуно интерактивна. [Сазнајте више](https://web.dev/interactive)."}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "Време почетка интеракције"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "Максимално потенцијално кашњење првог уноса које може да се деси корисницима је трајање најдужег задатка у милисекундама. [Сазнајте више](https://developers.google.com/web/updates/2018/05/first-input-delay)."}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "Макс. потенцијално кашњење првог приказа"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "Индекс брзине приказује колико брзо садржај странице постаје видљив за кориснике. [Сазнајте више](https://web.dev/speed-index)."}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "Индекс брзине"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "Збир свих периода између FCP-а и времена до почетка интеракције, када задатак траје дуже од 50 ms, изражено у милисекундама."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "Укупно време блокирања"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "Трајања повратног пута (RTT) мреже знатно утичу на учинак. Ако је трајање повратног пута до почетне локације велико, то значи да би сервери који су ближи кориснику могли да побољшају учинак. [Сазнајте више](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "lighthouse-core/audits/network-rtt.js | title": {"message": "Трајања повратног пута мреже"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "Кашњења сервера могу да утичу на учинак веба. Ако је кашњење сервера за почетну локацију велико, то значи да је сервер преоптерећен или да има слаб позадински учинак. [Сазнајте више](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "Позадинска кашњења сервера"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "Сервисер омогућава да веб-апликација буде поуздана у непредвидивим условима мреже. [Сазнајте више](https://web.dev/offline-start-url)."}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url` не одговара кодом 200 када је офлајн"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url` одговара кодом 200 када је офлајн"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "Lighthouse није успео да прочита `start_url` из манифеста. Као последица тога, претпоставили смо да је `start_url` URL документа. Порука о грешци: „{manifestWarning}“."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "Премашује циљ"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "Захтеве за количину и величину мреже одржавајте испод граница одређених циљевима за учинак. [Сазнајте више](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 захтев}one{# захтев}few{# захтева}other{# захтева}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "Циљ за учинак"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "Ако сте већ подесили HTTPS, уверите се да преусмеравате сав HTTP саобраћај на HTTPS да бисте омогућили безбедне веб-функције за све кориснике. [Сазнајте више](https://web.dev/redirects-http)."}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "Не преусмерава HTTP саобраћај на HTTPS"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "Преусмерава HTTP саобраћај на HTTPS"}, "lighthouse-core/audits/redirects.js | description": {"message": "Преусмеравања доводе до додатних кашњења пре учитавања странице. [Сазнајте више](https://web.dev/redirects)."}, "lighthouse-core/audits/redirects.js | title": {"message": "Избегавајте вишеструка преусмеравања странице"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "Да бисте подесили циљеве за количину и величину ресурса странице, додајте датотеку budget.json file. [Сазнајте више](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 захтев • {byteCount, number, bytes} KB}one{# захтев • {byteCount, number, bytes} KB}few{# захтева • {byteCount, number, bytes} KB}other{# захтева • {byteCount, number, bytes} KB}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "Омогући да број захтева и величине преноса буду мали"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "Канонички линкови предлажу који URL треба да се прикаже у резултатима претраге. [Сазнајте више](https://web.dev/canonical)."}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "Више неусаглашених URL-ова ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "Усмерава ка другом домену ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "Неважећи URL ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Усмерава на другу `hreflang` локацију ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "Релативни URL ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "Усмерава на основни URL домена (почетну страницу), уместо еквивалентне странице садржаја"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "Документ нема важећу вредност `rel=canonical`"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "Документ има важећи атрибут `rel=canonical`"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "Величине фонтова испод 12 пиксела су премале да би биле читљиве и због њих корисници на мобилним уређајима морају да „зумирају прстима“ како би могли да читају садржај. Потрудите се да >60% текста странице буде ≥12 пиксела. [Сазнајте више](https://web.dev/font-size)."}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} читљивог текста"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "Текст није читљив јер не постоји метаознака области приказа која је оптимизована за екране на мобилним уређајима."}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "{decimalProportion, number, extendedPercent} текста је премало (на основу узорка од {decimalProportionVisited, number, extendedPercent})."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "Документ не користи читљиве величине фонтова"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "Документ користи читљиве величине фонтова"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "Линкови hreflang обавештавају претраживаче коју верзију странице треба да наведу у резултатима претраге за дати језик или регион. [Сазнајте више](https://web.dev/hreflang)."}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "Документ нема важећи атрибут `hreflang`"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "Документ има важећи атрибут `hreflang`"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "Странице са неуспешним HTTP кодовима статуса можда неће бити правилно индексиране. [Сазнајте више](https://web.dev/http-status-code)."}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "Страница има неуспешан HTTP кôд статуса"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "Страница има успешан HTTP кôд статуса"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "Претраживачи не могу да уврсте странице у резултате претраге ако немају дозволу да их пописују. [Сазнајте више](https://web.dev/is-crawable)."}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "Индексирање странице је блокирано"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "Индексирање странице није блокирано"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "Описни текст у линковима помаже претраживачима да разумеју садржај. [Сазнајте више](https://web.dev/link-text)."}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Пронађен је 1 линк}one{Пронађен је # линк}few{Пронађена су # линка}other{Пронађено је # линкова}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "Линкови немају описни текст"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "Линкови имају описни текст"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "Покрените [алатку за тестирање структурираних података](https://search.google.com/structured-data/testing-tool/) и [алатку за анализирање структурираних података](http://linter.structured-data.org/) да бисте проценили структуриране податке. [Сазнајте више](https://web.dev/structured-data)."}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "Структурирани подаци су важећи"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "Метаописи могу да буду уврштени у резултате претраге да би пружили сажети резиме садржаја странице. [Сазнајте више](https://web.dev/meta-description)."}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "Поље за текст описа је празно."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "Документ нема метаопис"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "Документ има метаопис"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "Претраживачи не могу да индексирају садржај додатних компонената, а многи уређаји ограничавају додатне компоненте или их не подржавају. [Сазнајте више](https://web.dev/plugins)."}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "Документ користи додатне компоненте"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "Документ избегава додатне компоненте"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "Ако датотека robots.txt није правилно направљена, пописивачи можда неће моћи да разумеју како желите да се веб-сајт попише или индексира. [Сазнајте више](https://web.dev/robots-txt)."}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Захтев за датотеку robots.txt вратио је HTTP статус: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Пронађена је 1 грешка}one{Пронађена је # грешка}few{Пронађене су # грешке}other{Пронађено је # грешака}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse није успео да преузме датотеку robots.txt"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "Датотека robots.txt није важећа"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "Датотека robots.txt је важећа"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "Интерактивни елементи попут дугмади и линкова треба да буду довољно велики (48×48 пиксела) и да имају довољно простора око себе да би било лако да се додирну без преклапања са другим елементима. [Сазнајте више](https://web.dev/tap-targets)."}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} циљева додиривања има одговарајућу величину"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Циљеви додиривања су премали јер не постоји метаознака области приказа која је оптимизована за екране на мобилним уређајима"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "Циљеви додиривања немају одговарајућу величину"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Циљ који се преклапа"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Циљ додиривања"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "Циљеви додиривања имају одговарајућу величину"}, "lighthouse-core/audits/service-worker.js | description": {"message": "Сервисер је технологија која омогућава апликацији да користи многе функције прогресивних веб-апликација, попут офлајн рада, додавања на почетни екран и искачућих обавештења. [Сазнајте више](https://web.dev/service-worker)."}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "Ову страницу контролише сервисер, али није пронађен ниједан `start_url` јер није успело рашчлањивање манифеста као важеће JSON датотеке"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "Ову страницу контролише сервисер, али `start_url` ({startUrl}) не спада у опсег сервисера ({scopeUrl})"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "Ову страницу контролише сервисер, али није пронађен ниједан `start_url` јер ниједан манифест није преузет."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "Овај извор има један или више сервисера, али страница ({pageUrl}) није у опсегу."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "Не региструје сервисер који контролише страницу и `start_url`"}, "lighthouse-core/audits/service-worker.js | title": {"message": "Региструје сервисер који контролише страницу и `start_url`"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "Тематски уводни екран обезбеђује квалитетан доживљај када корисници покрећу апликацију са почетних екрана. [Сазнајте више](https://web.dev/splash-screen)."}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "Није конфигурисано за прилагођени уводни екран"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "Конфигурисано за прилагођени уводни екран"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "Трака за адресу прегледача може да има тему која одговара сајту. [Сазнајте више](https://web.dev/themed-omnibox)."}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "Не подешава боју теме за траку за адресу."}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "Подешава боју теме за траку за адресу."}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "Период блокирања главне нити"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "Независни добављач"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "Кôд независног добављача може значајно да утиче на учинак учитавања. Ограничите број сувишних независних добављача услуге и пробајте да учитате кôд независног добављача када страница примарно заврши са учитавањем. [Сазнајте више](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "Кôд независног добављача је блокирао главну нит {timeInMs, number, milliseconds} ms"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "Смањите утицај кода независног добављача"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "Независна употреба"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "Време првог одговора одређује време у које сервер шаље одговор. [Сазнајте више](https://web.dev/time-to-first-byte)."}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "Основном документу је требало {timeInMs, number, milliseconds} ms"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "Смањите времена одговора сервера (TTFB)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "Времена одговора сервера су кратка (TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "Траја<PERSON>е"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "Време почетка"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "Тип"}, "lighthouse-core/audits/user-timings.js | description": {"message": "Препоручујемо вам да опремите апликацију API-јем за време корисника да бисте измерили учинак апликације у реалном свету током кључних корисничких доживљаја. [Сазнајте више](https://web.dev/user-timings)."}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 време корисника}one{# време корисника}few{# времена корисника}other{# времена корисника}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "Ознаке и мере Времена корисника"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Повезивање унапред <link> је пронађено за „{securityOrigin}“, али га прегледач није употребио. Проверите да ли правилно користите атрибут `crossorigin`."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "Размислите о томе да додате савете за ресурсе `preconnect` или `dns-prefetch` како бисте успоставили ране везе са важним изворима трећих страна. [Сазнајте више](https://web.dev/uses-rel-preconnect)."}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "Повежите се унапред са потребним изворима"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Предучитавање <link> је пронађено за „{preloadURL}“, али га прегледач није употребио. Проверите да ли правилно користите атрибут `crossorigin`."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "Препоручујемо вам да користите `<link rel=preload>` како бисте касније током учитавања странице дали приоритет преузимању ресурса који се тренутно траже. [Сазнајте више](https://web.dev/uses-rel-preload)."}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "Унапред учитајте најважније захтеве"}, "lighthouse-core/audits/viewport.js | description": {"message": "Додајте ознаку `<meta name=\"viewport\">` да бисте оптимизовали апликацију за екране на мобилним уређајима. [Сазнајте више](https://web.dev/viewport)."}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "Није пронађена ознака `<meta name=\"viewport\">`"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "Нема ознаку `<meta name=\"viewport\">` са ознакама `width` или `initial-scale`"}, "lighthouse-core/audits/viewport.js | title": {"message": "Има ознаку `<meta name=\"viewport\">` са ознаком `width` или `initial-scale`"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "Апликација треба да приказује неки садржај када је JavaScript онемогућен, чак и ако је то само упозорење кориснику да је JavaScript обавезан за коришћење апликације. [Сазнајте више](https://web.dev/without-javascript)."}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "Тело странице треба да приказује неки садржај ако јој скрипте нису доступне."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "Не пружа резервни садржај када JavaScript није доступан"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "Има неки садржај када JavaScript није доступан"}, "lighthouse-core/audits/works-offline.js | description": {"message": "Ако правите прогресивну веб-апликацију, размислите о томе да користите сервисер како би апликација могла да ради офлајн. [Сазнајте више](https://web.dev/works-offline)."}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "Актуелна страница не одговара кодом 200 када је офлајн"}, "lighthouse-core/audits/works-offline.js | title": {"message": "Актуелна страница одговара кодом 200 када је офлајн"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "Страница се можда не учитава офлајн зато што је пробни URL ({requested}) преусмерен на „{final}“. Пробајте директно да тестирате други URL."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "То су прилике да побољшате коришћење ARIA улога у апликацији, чиме може да побољша доживљај корисника технологије за помоћ особама са инвалидитетом, као што је читач екрана."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "То су прилике да пружите алтернативни садржај за аудио и видео датотеке. То може да побољша доживљај за кориснике са оштећеним слухом или видом."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Звук и видео"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Ове ставке истичу уобичајене најбоље праксе у вези са приступачношћу."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Најбоље праксе"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "Ове провере истичу прилике за [побољшање приступачности веб-апликације](https://developers.google.com/web/fundamentals/accessibility). Аутоматски може да се открије само један подскуп проблема са приступачношћу, па препоручујемо да обављате и ручно тестирање."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "Ове ставке обрађују области које алатка за аутоматизовано тестирање не може да обухвати. Сазнајте више у водичу о [спровођењу прегледа приступачности](https://developers.google.com/web/fundamentals/accessibility/how-to-review)."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "Приступачност"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "То су прилике да побољшате читљивост садржаја."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Контраст"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "То су прилике да побољшате тумачење свог садржаја за кориснике на различитим језицима."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Интернационализација и локализација"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "То су прилике да побољшате семантику контрола у апликацији. То може да побољша доживљај корисника технологије за помоћ особама са инвалидитетом, као што је читач екрана."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Називи и ознаке"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Ово су прилике да побољшате кретање по тастатури у апликацији."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Навигација"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "То су прилике за побољшање доживљаја при читању података из табела и листа помоћу технологије за помоћ особама са инвалидитетом, попут читача екрана."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Табеле и листе"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Најбоље праксе"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "Циљевима за учинак одређују се стандарди за учинак сајта."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "Циљеви"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "Више информација о учинку апликације. Ови бројеви не [утичу директно](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) на оцену учинка."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "Дијагностика"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Најважнији аспект учинка је брзина којом се пиксели приказују на екрану. Кључни показатељи: Прво приказивање садржаја, Прво значајно приказивање"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Побољшања првог приказивања"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Ови предлози могу да вам помогну да се страница учитава брже. Не [утичу директно](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) на оцену учинка."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Могућности"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "Показатељи"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Побољшајте општи доживљај учитавања да би страница почела да се одазива и да би била спремна за коришћење у најкраћем могућем року. Кључни показатељи: Време почетка интеракције, Индекс брзине"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Општа побољшања"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "Учинак"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "Овим проверама се оцењују аспекти прогресивне веб-апликације. [Сазнајте више](https://developers.google.com/web/progressive-web-apps/checklist)."}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "Ове провере захтева основна [Контролна листа за прогресивне веб-апликације](https://developers.google.com/web/progressive-web-apps/checklist), али их Lighthouse не спроводи аутоматски. Оне не утичу на ваш резултат, али је важно да их ручно потврдите."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "Прогре<PERSON>и<PERSON>на веб-апликација"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "Брзо и поуздано"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Може да се инсталира"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Оптимизовано за PWA"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "Захваљујући овим проверама страница ће сигурно бити оптимизована за рангирање резултата претраживача. Има још неких фактора које Lighthouse не проверава, а који могу да утичу на рангирање у претрази. [Сазнајте више](https://support.google.com/webmasters/answer/35769)."}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "Покрећите ове додатне валидаторе на сајту да бисте проверили додатне најбоље праксе оптимизације за претраживаче."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "Оптимизација за претраживаче"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "Форматирајте HTML садржај на начин који омогућава пописивачима да боље разумеју садржај апликације."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "Најбоље праксе за садржај"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Да би се апликација појавила у резултатима претраге, пописивачи треба да имају приступ до ње."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Пописивање и индексирање"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "Уверите се да су странице прилагођене мобилним уређајима да корисници не би морали да умањују или увећавају приказ како би читали странице са садржајем. [Сазнајте више](https://developers.google.com/search/mobile-sites/)."}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "Прилагођено мобилним уређајима"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Време преживљавања кеша"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "Локација"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "Назив"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "Захтеви"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "Тип ресурса"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "Величина"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Проведено време"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "Величина преноса"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Потенциј<PERSON><PERSON>на уштеда"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "Потенциј<PERSON><PERSON>на уштеда"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Потенциј<PERSON><PERSON>на уштеда од {wastedBytes, number, bytes} KB"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Потенција<PERSON>на уштеда од {wastedMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "Документ"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "Фонт"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "Слика"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "Мед<PERSON><PERSON>и"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "Друго"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "Скрипта"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} сек"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON> стила"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Независни ресурси"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "Укупно"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "Дошло је до грешке при евидентирању трага током учитавања странице. Поново покрените Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "Временско ограничење чекања на иницијалну везу за протокол програма за отклањање грешака."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome није прикупио ниједан снимак екрана током учитавања странице. Уверите се да је садржај видљив на страници, па пробајте да поново покренете Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "DNS сервери нису могли да разреше наведени домен."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Прикупљач за обавезни ресурс {artifactName} је наишао на грешку: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "Дошло је до интерне грешке у Chrome-у. Поново покрените Chrome и пробајте да поново покренете Lighthouse."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "Прикупљач за обавезни ресурс {artifactName} се није покренуо."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse није успео да поуздано учита страницу коју сте захтевали. Уверите се да тестирате одговарајући URL и да сервер правилно одговара на све захтеве."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse није успео да поуздано учита URL који сте захтевали јер је страница престала да реагује."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "URL који сте навели нема важећи безбедносни сертификат. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome је спречио учитавање странице са транзитивним огласом. Уверите се да тестирате одговарајући URL и да сервер правилно одговара на све захтеве."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse није успео да поуздано учита страницу коју сте захтевали. Уверите се да тестирате одговарајући URL и да сервер правилно одговара на све захтеве. (Детаљи: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse није успео да поуздано учита страницу коју сте захтевали. Уверите се да тестирате одговарајући URL и да сервер правилно одговара на све захтеве. (К<PERSON>д статуса: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Учитавање странице је трајало предуго. Пратите прилике у извештају да бисте скратили време учитавања странице, па поново покрените Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "Одговор протокола DevTools се чека дуже од додељеног периода. (Метод: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "Преузимање садржаја ресурса траје дуже од додељеног периода."}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "Изгледа да је URL који сте навели неважећи."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "Прикажи провере"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "Почетна навигација"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "Максимално кашњење критичне путање:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "Грешка!"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "Пријављивање грешке: нема информација о провери"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "Подаци о експерименталним функцијама"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) анализа актуелне странице емулиране помоћу мобилне мреже. Вредности представљају процене и могу да варирају."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "Додатне ставке за ручну проверу"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "Није примењиво"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "Могућност"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "Процењена уштеда"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "Провере са задовољавајућом оценом"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "Скупи фрагмент"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "Прошири фрагмент"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "Прикажи независне ресурсе"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "Било је извесних проблема који су утицали на ово покретање Lighthouse-а:"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "Вредности представљају процене и могу да варирају. Оцена учинка се [заснива само на овим показатељима](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "Провере са задовољавајућом оценом које садрже упозорења"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "Упозорења: "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "Предлажемо да отпремите GIF у услугу која ће га кодирати за уградњу у HTML5 видео."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "Инсталирајте [WordPress додатну компоненту за лако учитавање](https://wordpress.org/plugins/search/lazy+load/) која омогућава да одложите све слике ван екрана или да пређете на тему која пружа ту функцију. Препоручујемо и да користите [додатну компоненту за AMP странице](https://wordpress.org/plugins/amp/)."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "Неке WordPress додатне компоненте могу да вам помогну да [уметнете критичне елементе](https://wordpress.org/plugins/search/critical+css/) или [одложите мање важне ресурсе](https://wordpress.org/plugins/search/defer+css+javascript/). Имајте на уму да оптимизације које пружају ове додатне компоненте могу да оштете функције или теме додатних компоненти, па ћете вероватно морати да уносите промене у кôд."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "Теме, додатне компоненте и спецификације сервера доприносе времену одговора сервера. Препоручујемо да пронађете оптимизованију тему, пажљиво изаберете додатну компоненту за оптимизацију и/или надоградите сервер."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "Препоручујемо да прикажете одломке у листама постова (на пример, преко још ознака), смањите број постова који се приказују на одређеној страници, раздвојите дугачке постове на више странциа или користите додатну компоненту за лако учитавање коментара."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "Неке [WordPress додатне компоненте](https://wordpress.org/plugins/search/minify+css/) могу да убрзају сајт тако што повезују, умањују и компримују стилове. Ово умањивање можете да обавите и унапред помоћу процеса дизајнирања ако је могуће."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "Неке [WordPress додатне компоненте](https://wordpress.org/plugins/search/minify+javascript/) могу да убрзају сајт тако што повезују, умањују и компримују скрипте. Ово умањивање можете да обавите и унапред помоћу процеса дизајнирања ако је могуће."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "Препоручујемо да умањите или промените број [WordPress додатних компоненти](https://wordpress.org/plugins/) које на страници учитавају CSS који се не користи. Да бисте идентификовали додатне компоненте које додају сувишан CSS, пробајте да покренете [покривеност кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) у алатки Chrome DevTools. Можете да идентификујете одговорну тему/додатну компоненту у URL-у стилске странице. Потражите додатне компоненте које на листи имају много стилских страница са доста црвенила у покривености кода. Додатна компонента треба да стави стилску страницу на листу само ако се стварно користи на страници."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "Препоручујемо да умањите или промените број [WordPress додатних компоненти](https://wordpress.org/plugins/) које на страници учитавају JavaScript који се не користи. Да бисте идентификовали додатне компоненте које додају сувишан JS, пробајте да покренете [покривеност кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) у алатки Chrome DevTools. Можете да идентификујете одговорну тему/додатну компоненту у URL-у скрипте. Потражите додатне компоненте које на листи имају много скрипти са доста црвенила у покривености кода. Додатна компонента треба да стави скрипту на листу само ако се стварно користи на страници."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "Прочитајте више о [кеширању прегледача у WordPress-у](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "Препоручујемо да користите [WordPress додатну компоненту за оптимизацију слика](https://wordpress.org/plugins/search/optimize+images/) која компримује слике без губитка квалитета."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "Отпремајте слике директно помоћу [библиотеке медија](https://codex.wordpress.org/Media_Library_Screen) да бисте се уверили да су доступне обавезне величине слика, па их уметните у библиотеку медија или користите виџет странице да бисте се уверили да се користе оптималне величине слика (укључујући оне за преломне тачке које се одазивају). Избегавајте коришћење слика `Full Size` ако димензије нису адекватне за њихово коришћење. [Сазнајте више](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)."}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "Можете да омогућите компримовање текста у конфигурацији веб-сервера."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "Препоручујемо да користите [додатну компоненту](https://wordpress.org/plugins/search/convert+webp/) или услугу која аутоматски конвертује отпремљене слике у оптималне формате."}}