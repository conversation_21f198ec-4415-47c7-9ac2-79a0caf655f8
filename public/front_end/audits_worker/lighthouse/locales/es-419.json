{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "Las claves de acceso permiten a los usuarios dirigirse rápidamente a una parte concreta de la página. Para facilitar una navegación correcta, las claves de acceso deben ser únicas. [Obtén más información](https://web.dev/accesskeys/)."}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Los valores de `[accesskey]` no son únicos"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "Los valores de `[accesskey]` son únicos"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON> `role` de ARIA admite un subconjunto específico de atributos de `aria-*`. Si no coinciden estos valores, los atributos de `aria-*` no serán válidos. [Obtén más información](https://web.dev/aria-allowed-attr/)."}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Los atributos `[aria-*]` no coinciden con sus roles"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Los atributos `[aria-*]` coinciden con sus roles"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "Algunos roles de ARIA incluyen atributos obligatorios que describen el estado del elemento a los lectores de pantalla. [Obtén más información](https://web.dev/aria-required-attr/)."}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Los elementos `[role]` no tienen todos los atributos `[aria-*]` necesarios"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "Los elementos `[role]` tienen todos los atributos `[aria-*]` necesarios"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "Algunos roles principales de ARIA deben contener roles secundarios específicos para llevar a cabo las funciones de accesibilidad correspondientes. [Obtén más información](https://web.dev/aria-required-children/)."}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Los elementos con una función `[role]` ARIA deben incluir elementos secundarios con una `[role]` específica. Faltan algunos o todos los elementos secundarios necesarios."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "Los elementos con una función `[role]` ARIA deben incluir elementos secundarios con una `[role]` específica. Se detectaron todos los elementos secundarios necesarios."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "Algunos roles secundarios de ARIA deben incluirse dentro de roles principales específicos para llevar a cabo de manera adecuada las funciones de accesibilidad correspondientes. [Obtén más información](https://web.dev/aria-required-parent/)."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Los elementos `[role]` no se incluyen en los elementos principales necesarios"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "Los elementos `[role]` se incluyen en los elementos principales correspondientes"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "Los roles de ARIA deben tener valores válidos para realizar las funciones de accesibilidad correspondientes. [Obtén más información](https://web.dev/aria-roles/)."}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "Los valores de `[role]` no son v<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "Los valores de `[role]` son v<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Las tecnologías de asistencia, como los lectores de pantalla, no pueden interpretar atributos de ARIA con valores no válidos. [Obtén más información](https://web.dev/aria-valid-attr-value/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Los atributos `[aria-*]` no tienen valores válidos"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Los atributos `[aria-*]` tienen valores válidos"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "Las tecnologías de asistencia, como los lectores de pantalla, no pueden interpretar los atributos ARIA cuyos nombres no sean válidos. [Obtén más información](https://web.dev/aria-valid-attr/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Los atributos `[aria-*]` no son válidos o no están bien escritos"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "Los atributos `[aria-*]` son válidos y están bien escritos"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "Los subtítulos permiten que los usuarios sordos o con dificultades auditivas aprovechen los elementos de audio, dado que proporcionan información fundamental, como la indicación de quién habla, lo que dice y otros datos no verbales. [Obtén más información](https://web.dev/audio-caption/)."}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "Falta un elemento `<track>` con `[kind=\"captions\"]` en los elementos `<audio>`."}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "Los elementos `<audio>` contienen un elemento `<track>` con `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Elementos con errores"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "Si un botón no tiene un nombre accesible, los lectores de pantalla lo leerán en voz alta como \"botón\", por lo que resulta inservible para los usuarios que necesitan usar lectores de pantalla. [Obtén más información](https://web.dev/button-name/)."}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "Los botones no tienen nombres accesibles"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "Los botones tienen nombres accesibles"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "Incluir maneras de omitir el contenido repetitivo permite a los usuarios que usan un teclado navegar por la página con mayor eficiencia. [Obtén más información](https://web.dev/bypass/)."}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "La página no contiene ningún título, vínculo de omisión ni región de punto de referencia"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "La página contiene un título, un vínculo de omisión o una región de punto de referencia"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "Los textos con poco contraste resultan difíciles o imposibles de leer para muchos usuarios. [Obtén más información](https://web.dev/color-contrast/)."}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Los colores de fondo y de primer plano no tienen una relación de contraste adecuada."}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "Los colores de fondo y de primer plano tienen una relación de contraste adecuada"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "Si las listas de definiciones no están bien marcadas, es posible que los lectores de pantalla las lean de forma confusa o imprecisa. [Obtén más información](https://web.dev/definition-list/)."}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "Los elementos `<dl>` no contienen solo elementos `<script>` o `<template>`, o grupos de `<dt>` y `<dd>` ordenados correctamente."}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "Los elementos `<dl>` contienen solo elementos `<script>` o `<template>`, o grupos de `<dt>` y `<dd>` ordenados correctamente."}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "Los elementos de la lista de definiciones (`<dt>` y `<dd>`) deben incluirse en un elemento `<dl>` principal para garantizar que los lectores de pantalla los lean correctamente. [Obtén más información](https://web.dev/dlitem/)."}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "Los elementos de la lista de definiciones no se incluyen en los elementos `<dl>`"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "Los elementos de la lista de definiciones se incluyen en los elementos `<dl>`"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "El título les brinda a los usuarios de lectores de pantalla una descripción general de la página. Por su parte, los usuarios de motores de búsqueda lo usan mucho para determinar si una página es relevante para su búsqueda. [Obtén más información](https://web.dev/document-title/)."}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "El documento no tiene un elemento `<title>`"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "El documento tiene un elemento `<title>`"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "El valor de los atributos id debe ser único para evitar que las tecnologías de asistencia omitan otras instancias. [Obtén más información](https://web.dev/duplicate-id/)."}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "Los atributos `[id]` de la página no son únicos"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "Los atributos `[id]` de la página son únicos"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "Los usuarios de lectores de pantalla necesitan que los marcos tengan títulos para que se describa su contenido. [Obtén más información](https://web.dev/frame-title/)."}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "Los elementos `<frame>` o `<iframe>` no tienen título"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "Los elementos `<frame>` o `<iframe>` tienen un título"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "Si no se especifica ningún atributo de idioma para una página, los lectores de pantalla considerarán que la página está en el idioma predeterminado que el usuario eligió al configurar el lector de pantalla. Si el idioma de la página es diferente del predeterminado, es posible que el lector de pantalla no lea bien el texto de la página. [Obtén más información](https://web.dev/html-has-lang/)."}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "El elemento `<html>` no tiene un atributo `[lang]`"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "El elemento `<html>` tiene un atributo `[lang]`"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "Especificar un [idioma BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) válido permite a los lectores de pantalla leer el texto en voz alta correctamente. [Obtén más información](https://web.dev/html-lang-valid/)."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "El elemento `<html>` no tiene un valor válido para el atributo `[lang]`."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "El elemento `<html>` tiene un valor válido para su atributo `[lang]`"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "El texto de los elementos informativos debe ser corto y descriptivo. Los elementos decorativos se pueden omitir usando un atributo alt vacío. [Obtén más información](https://web.dev/image-alt/)."}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "Los elementos de imagen no tienen ningún atributo `[alt]`"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "Los elementos de imagen tienen atributos `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "Cuando se usa una imagen como botón `<input>`, resulta útil proporcionar un texto alternativo para permitir que los usuarios de lectores de pantalla entiendan cuál es la función del botón. [Obtén más información](https://web.dev/input-image-alt/)."}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Los elementos `<input type=\"image\">` no tienen texto `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "Los elementos `<input type=\"image\">` tienen texto `[alt]`"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "Las etiquetas garantizan que las tecnologías de asistencia, como los lectores de pantalla, lean los controles de los formularios de forma correcta. [Obtén más información](https://web.dev/label/)."}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "Los elementos de formulario no tienen ninguna etiqueta asociada"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "Los elementos de formulario tienen etiquetas asociadas"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "Las tablas que se usen con fines de diseño no deben incluir elementos de datos (como ordinales, leyendas o atributos de resumen), dado que estos pueden confundir a los usuarios de lectores de pantalla. [Obtén más información](https://web.dev/layout-table/)."}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "Los elementos `<table>` de presentación no evitan el uso de `<th>`, `<caption>` ni del atributo `[summary]`."}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "Los elementos `<table>` de presentación evitan el uso de `<th>`, `<caption>` o del atributo `[summary]`."}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "Usar textos de vínculo (y textos alternativos para las imágenes, si estas se usan como vínculos) que sean reconocibles y únicos, y que se puedan seleccionar mejora la experiencia de navegación de los usuarios de lectores de pantalla. [Obtén más información](https://web.dev/link-name/)."}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "Los vínculos no tienen nombres reconocibles"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "Los vínculos tienen nombres reconocibles"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "Los lectores de pantalla leen las listas en voz alta de una forma concreta. Se recomienda utilizar una estructura de lista adecuada para que los lectores de pantalla puedan leer las listas de forma correcta. [Obtén más información](https://web.dev/list/)."}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "Las listas no contienen solo elementos `<li>` y elementos que admiten secuencias de comandos (`<script>` y `<template>`)."}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "Las listas contienen solo elementos `<li>` y elementos que admiten secuencias de comando (`<script>` y `<template>`)."}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "Los lectores de pantalla requieren que los elementos de lista (`<li>`) se incluyan en un elemento `<ul>` o `<ol>` principal para leerlos correctamente. [Obtén más información](https://web.dev/listitem/)."}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "Los elementos de lista (`<li>`) no se encuentran dentro de elementos principales `<ul>` o `<ol>`."}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "Los elementos de lista (`<li>`) se incluyen en los elementos principales `<ul>` o `<ol>`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "Los usuarios no esperan que una página se actualice automáticamente. Cuando eso sucede, vuelve a mostrarse la parte superior de la página. Esto puede generar una experiencia frustrante o confusa. [Obtén más información](https://web.dev/meta-refresh/)."}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "El documento usa `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "El documento no usa `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "Desactivar el zoom genera problemas para los usuarios con visión reducida, quienes necesitan ampliar la pantalla para ver correctamente el contenido de las páginas web. [Obtén más información](https://web.dev/meta-viewport/)."}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` se usa en el elemento `<meta name=\"viewport\">` o el atributo `[maximum-scale]` tiene un valor inferior a 5."}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "No se usa `[user-scalable=\"no\"]` en el elemento `<meta name=\"viewport\">` y el atributo `[maximum-scale]` no tiene un valor inferior a 5."}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "Los lectores de pantalla no pueden traducir contenido que no sea texto. El agregado de texto alternativo a los elementos `<object>` ayuda a los lectores de pantalla a transmitir el significado correspondiente a los usuarios. [Obtén más información](https://web.dev/object-alt/)."}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "Los elementos `<object>` no tienen texto `[alt]`"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "Los elementos `<object>` tienen texto `[alt]`"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "Si el valor es superior a 0, el orden de navegación es explícito. Aunque técnicamente esta es una posibilidad válida, suele producir experiencias frustrantes en los usuarios que necesitan las tecnologías de asistencia. [Obtén más información](https://web.dev/tabindex/)."}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "Algunos elementos tienen un valor de `[tabindex]` superior a 0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "No hay ningún elemento con un valor de `[tabindex]` superior a 0"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "Los lectores de pantalla incluyen funciones para facilitar la navegación por las tablas. Asegurarse de que las celdas `<td>` que usan el atributo `[headers]` solo hagan referencia a otras celdas de la misma tabla puede mejorar la experiencia de los usuarios de lectores de pantalla. [Obtén más información](https://web.dev/td-headers-attr/)."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Las celdas de un elemento `<table>` que usan el atributo `[headers]` hacen referencia a un elemento `id` que no se encuentra en la misma tabla."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "Las celdas de un elemento `<table>` que usa el atributo `[headers]` hacen referencia a las celdas de esa misma tabla."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "Los lectores de pantalla incluyen funciones para facilitar la navegación por las tablas. Asegurarse de que los encabezados de las tablas siempre hagan referencia a un conjunto específico de celdas puede mejorar la experiencia de los usuarios de lectores de pantalla. [Obtén más información](https://web.dev/th-has-data-cells/)."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Los elementos `<th>` y los elementos con `[role=\"columnheader\"/\"rowheader\"]` no contienen las celdas de datos que describen."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "Los elementos `<th>` y los elementos con `[role=\"columnheader\"/\"rowheader\"]` contienen celdas de datos que describen."}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "Especificar un [idioma CP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) válido en los elementos ayuda a asegurar que los lectores de pantalla pronuncien bien el texto correspondiente. [Obtén más información](https://web.dev/valid-lang/)."}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Los atributos `[lang]` no tienen un valor válido"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "Los atributos `[lang]` tienen un valor válido"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "Si un video tiene subtítulos, los usuarios sordos o con dificultades auditivas pueden acceder a la información más fácilmente. [Obtén más información](https://web.dev/video-caption/)."}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "Los elementos `<video>` no contienen un elemento `<track>` con `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "Los elementos `<video>` contienen un elemento `<track>` con `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "Las descripciones de audio incluidas en los videos proporcionan información relevante que no surge de los diálogos, como la relativa a expresiones faciales y escenografía. [Obtén más información](https://web.dev/video-description/)."}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "Los elementos `<video>` no contienen un elemento `<track>` con `[kind=\"description\"]`."}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "Los elementos `<video>` contienen un elemento `<track>` con `[kind=\"description\"]`"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "Para que el aspecto en iOS sea perfecto cuando los usuarios agreguen una app web progresiva a la pantalla principal, define un atributo `apple-touch-icon`. El atributo debe apuntar a un archivo PNG cuadrado de 192 px (o 180 px) que no sea transparente. [Obtén más información](https://web.dev/apple-touch-icon/)."}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "No proporciona un ícono `apple-touch-icon` válido"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "El atributo `apple-touch-icon-precomposed` está desactualizado; usa en su lugar`apple-touch-icon`."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "Proporciona un `apple-touch-icon` v<PERSON>lido"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Las extensiones de Chrome afectaron de forma negativa al rendimiento de carga de esta página. Prueba a auditarla en modo incógnito o desde un perfil de Chrome sin extensiones."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "Evaluación de la secuencia de comandos"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "Análisis de la secuencia de comandos"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "Tiempo de CPU total"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "Te recomendamos que reduzcas el tiempo de análisis, compilación y ejecución de secuencias JS. Para ello, puedes entregar cargas útiles de JS más pequeñas. [Obtén más información](https://web.dev/bootup-time)."}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "Reduce el tiempo de ejecución de JavaScript"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "Tiempo de ejecución de JavaScript"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Los GIF de gran tamaño no son eficientes para mostrar contenido animado. En su lugar, puedes utilizar formatos de video MPEG4/WebM para animaciones y formatos PNG/WebP para imágenes estáticas a fin de ahorrar bytes de la red. [Más información](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Usa formatos de video para incluir contenido animado"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Te recomendamos que uses la carga diferida para las imágenes ocultas y fuera de pantalla una vez que hayan terminado de cargarse todos los recursos críticos a fin de reducir el tiempo de carga. [Obtén más información](https://web.dev/offscreen-images)."}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Posterga la carga de imágenes que no aparecen en pantalla"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Hay recursos que bloquean el primer procesamiento de imagen de la página. Te recomendamos entregar los elementos JS/CSS críticos insertados y postergar todos los JS/estilos que no sean críticos. [Obtén más información](https://web.dev/render-blocking-resources)."}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Elimina los recursos que bloqueen el renderizado"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Trabajar con cargas útiles de red de gran tamaño resulta oneroso para el usuario, además de aumentar considerablemente el tiempo de carga de las páginas. [Obtén más información](https://web.dev/total-byte-weight)."}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "El tamaño total era {totalBytes, number, bytes} KB"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Evita cargas útiles de red de gran tamaño"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Evita cargas útiles de red de gran tamaño"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "Si reduces los archivos CSS, puedes achicar el tamaño de la carga útil de la red. [Obtén más información](https://web.dev/unminified-css)."}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "Reduce el uso de CSS"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Si reduces los archivos JavaScript, puedes achicar el tamaño de la carga útil y el tiempo de análisis de secuencias de comandos. [Obtén más información](https://web.dev/unminified-javascript)."}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Reducir el uso de JavaScript"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Quita las reglas inactivas de las hojas de estilo y retrasa la carga de secuencias CSS que no se utilicen para el contenido de la mitad superior de la página. Así, se reducirán los bytes que consume innecesariamente la actividad de red. [Obtén más información](https://web.dev/unused-css-rules)."}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Quita los recursos CSS que no se usen"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Quita el contenido JavaScript sin usar para reducir la cantidad de bytes que consume la actividad de red."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Quita el código JavaScript sin usar"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "La duración en caché por un período prolongado puede acelerar la carga de la página cuando el usuario la visita de manera repetida. [Obtén más información](https://web.dev/uses-long-cache-ttl)."}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Se encontró 1 recurso}other{Se encontraron # recursos}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Publica elementos estáticos con una política de caché eficaz"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Usa una política de caché eficaz en recursos estáticos"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Las imágenes optimizadas se cargan más rápido y consumen menos datos móviles. [Obtén más información](https://web.dev/uses-optimized-images)."}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Codifica las imágenes de forma eficaz"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Muestra imágenes con un tamaño adecuado para ahorrar datos móviles y reducir el tiempo de carga. [Obtén más información](https://web.dev/uses-responsive-images)."}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Usa un tamaño adecuado para las imágenes"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Los recursos basados en texto se deberían publicar comprimidos (gzip, deflate o brotli) para minimizar el total de bytes de la red. [Obtén más información](https://web.dev/uses-text-compression)."}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Habilita la compresión de texto"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "Los formatos como JPEG 2000, JPEG XR y WebP suelen comprimir mejor las imágenes que los formatos PNG o JPEG, lo que hace que se descarguen más rápido y consuman menos datos. [Obtén más información](https://web.dev/uses-webp-images)."}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "Publica imágenes con formatos de próxima generación"}, "lighthouse-core/audits/content-width.js | description": {"message": "Si el ancho del contenido de tu app no coincide con el del viewport, es posible que la app no esté optimizada para pantallas de dispositivos móviles. [Obtén más información](https://web.dev/content-width)."}, "lighthouse-core/audits/content-width.js | explanation": {"message": "El tamaño de viewport de {innerWidth} px no coincide con el tamaño de ventana de {outerWidth} px."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "El contenido no tiene el tamaño correcto para el viewport"}, "lighthouse-core/audits/content-width.js | title": {"message": "El contenido tiene el tamaño correcto para el viewport"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "Las cadenas de solicitudes críticas que se muestran a continuación indican qué recursos son de alta prioridad en la carga. Te recomendamos que reduzcas la longitud de las cadenas, disminuyas el tamaño de los recursos para la descarga o postergues la descarga de recursos innecesarios para mejorar la carga de la página. [Obtén más información](https://web.dev/critical-request-chains)."}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Se encontró 1 cadena}other{Se encontraron # cadenas}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "Minimiza la profundidad de las solicitudes críticas"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "Baja/advertencia"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "Lín<PERSON>"}, "lighthouse-core/audits/deprecations.js | description": {"message": "Con el tiempo, se quitarán las API obsoletas del navegador. [Obtén más información](https://web.dev/deprecations)."}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Se encontró 1 advertencia}other{Se encontraron # advertencias}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "Usa API obsoletas"}, "lighthouse-core/audits/deprecations.js | title": {"message": "Evita las API obsoletas"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "La caché de aplicaciones es obsoleta. [Obtén más información](https://web.dev/appcache-manifest)."}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "Se encontró \"{AppCacheManifest}\""}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "Usa la caché de aplicaciones"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "Evita la caché de aplicaciones"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "Especificar un DOCTYPE evita que el navegador cambie al modo no estándar. [Obtén más información](https://web.dev/doctype)."}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "El nombre de DOCTYPE debe ser la cadena en minúsculas `html`"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "El documento debe contener un DOCTYPE"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Se esperaba que publicId fuera una string vacía"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Se esperaba que systemId fuera una string vacía"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "La página no tiene el DOCKTYPE de HTML; por lo tanto, activa el modo no estándar"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "La página tiene el DOCTYPE de HTML"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "Elemento"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Estadística"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Valor"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "Los desarrolladores de navegadores recomiendan que las páginas contengan menos de alrededor de 1500 elementos de DOM. Lo ideal es que la profundidad del árbol sea inferior a 32 elementos, con 60 elementos secundarios por elemento principal como máximo. Los DOM de gran tamaño pueden aumentar el uso de la memoria, hacer que los [cálculos de estilos](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) tarden más y generar costosos [reprocesamientos del diseño](https://developers.google.com/speed/articles/reflow). [Obtén más información](https://web.dev/dom-size)."}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 elemento}other{# elementos}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Evita un tamaño excesivo de DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Profundidad máxima de DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Total de elementos DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Número máximo de elementos secundarios"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "Evita un tamaño excesivo de DOM"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "Objetivo"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "Agrega `rel=\"noopener\"` o `rel=\"noreferrer\"` a cualquier vínculo externo para mejorar el rendimiento y evitar vulnerabilidades de seguridad. [Obtén más información](https://web.dev/external-anchors-use-rel-noopener)."}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "Los vínculos a destinos con orígenes cruzados no son seguros"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "Los vínculos a destinos con orígenes cruzados son seguros"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "No se puede determinar el destino de anclaje ({anchorHTML}). Si no se usa como hipervínculo, te recomendamos quitar target=_blank."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Los sitios que solicitan a los usuarios su ubicación sin contexto los confunden o los hacen desconfiar. Te recomendamos vincular la solicitud a una acción del usuario. [Obtén más información](https://web.dev/geolocation-on-start)."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Solicita el permiso de ubicación geográfica al cargar la página"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Evita solicitar el permiso de ubicación geográfica al cargar la página"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Versión"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "Se detectaron todas las bibliotecas JavaScript de frontend de la página. [Obtén más información](https://web.dev/js-libraries)."}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "Se detectaron bibliotecas JavaScript"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "En el caso de usuarios con una conexión lenta, las secuencias de comandos externas que se incorporan dinámicamente a través de`document.write()` pueden demorar la carga de la página decenas de segundos. [Obtén más información](https://web.dev/no-document-write)."}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Usa `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "No usa `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "Gravedad más alta"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "Versión de la biblioteca"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "Recuento de vulnerabilidades"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "Es posible que algunas secuencias de comandos de terceros contengan vulnerabilidades de seguridad conocidas que los atacantes pueden identificar y aprovechar fácilmente. [Obtén más información](https://web.dev/no-vulnerable-libraries)."}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{Se detectó una vulnerabilidad}other{Se detectaron # vulnerabilidades}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "Incluye bibliotecas JavaScript de frontend con vulnerabilidades de seguridad conocidas"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "Alta"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "Baja"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "Media"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "Evita las bibliotecas JavaScript de frontend con vulnerabilidades de seguridad conocidas"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "Los sitios que solicitan a los usuarios permiso para enviar notificaciones sin contexto los confunden o los hacen desconfiar. Te recomendamos vincular la solicitud a los gestos del usuario. [Obtén más información](https://web.dev/notification-on-start)."}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Solicita el permiso de notificaciones al cargar la página"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "Evita solicitar el permiso de notificaciones al cargar la página"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "Elementos con errores"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "Evitar el pegado de contraseñas debilita las buenas políticas de seguridad. [Obtén más información](https://web.dev/password-inputs-can-be-pasted-into)."}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "Evita que los usuarios peguen contenido en los campos de contraseña"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "Permite que los usuarios peguen contenido en los campos de contraseña"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protocolo"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 ofrece más beneficios que HTTP/1.1, como los encabezados binarios, el multiplexado y los mensajes push del servidor. [Obtén más información](https://web.dev/uses-http2)."}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 solicitud no se entregó mediante HTTP/2}other{# solicitudes no se entregaron mediante HTTP/2}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "No usa HTTP/2 para todos los recursos propios"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "Usa HTTP/2 para sus propios recursos"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "<PERSON><PERSON><PERSON> marcar tus objetos de escucha de eventos táctiles y de la rueda del mouse como `passive` para mejorar el rendimiento de desplazamiento de tu página. [Obtén más información](https://web.dev/uses-passive-event-listeners)."}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "No usa objetos de escucha pasivos para mejorar el rendimiento del desplazamiento"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Usa objetos de escucha pasivos para mejorar el rendimiento del desplazamiento"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "Descripción"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "Los errores registrados en la consola indican la existencia de problemas no resueltos. Es posible que se deban a problemas con solicitudes de red y otros relativos al navegador. [Obtén más información](https://web.dev/errors-in-console)."}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "Se registraron errores del navegador en la consola"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "No se registraron errores del navegador en la consola"}, "lighthouse-core/audits/font-display.js | description": {"message": "Utiliza la función font-display de CSS a fin de que los usuarios vean el texto mientras se carga la fuente para sitios web. [Obtén más información](https://web.dev/font-display)."}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "Asegúrate de que el texto permanezca visible mientras se carga la fuente web"}, "lighthouse-core/audits/font-display.js | title": {"message": "Todo el texto permanece visible mientras se carga la fuente para sitios web"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "Lighthouse no pudo comprobar automáticamente el valor de font-display para la siguiente URL: {fontURL}."}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "Relación de aspecto (real)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Relación de aspecto (visualizada)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "Las dimensiones de visualización de las imágenes deben coincidir con la relación de aspecto natural. [Obtén más información](https://web.dev/image-aspect-ratio)."}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "Muestra imágenes con una relación de aspecto incorrecta"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "Muestra imágenes con una relación de aspecto correcta"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "La información sobre el tamaño de la imagen no es válida {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "Los navegadores pueden solicitar a los usuarios proactivamente que agreguen tu app a la pantalla principal, lo que es posible que genere un mayor compromiso. [Obtén más información](https://web.dev/installable-manifest)."}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "El manifiesto de la aplicación web no cumple los requisitos de aptitud para la instalación"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "El manifiesto de la aplicación web cumple los requisitos de aptitud para la instalación"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "URL no segura"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "Todos los sitios deben estar protegidos con el protocolo HTTPS, incluso aquellos que no controlan datos sensibles. Este protocolo evita que intrusos manipulen o escuchen de forma pasiva las comunicaciones entre tu app y los usuarios. Además, HTTPS es un requisito del protocolo HTTP/2 y muchas API nuevas de Web Platform. [Obtén más información](https://web.dev/is-on-https)."}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Se encontró una solicitud no segura}other{Se encontraron # solicitudes no seguras}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "No usa HTTPS"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "Usa HTTPS"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "Si las páginas se cargan rápidamente en las redes móviles, el usuario disfrutará de una buena experiencia. [Obtén más información](https://web.dev/load-fast-enough-for-pwa)."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "Interactiva a los {timeInMs, number, seconds} s"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "Interactiva en una red móvil simulada a los {timeInMs, number, seconds} s"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "Tu página tarda mucho en cargarse y no es interactiva en menos de 10 segundos. Para obtener información sobre cómo mejorarla, accede a la sección Rendimiento, donde podrás consultar las oportunidades disponibles y el diagnóstico correspondiente."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "La página no se carga suficientemente rápido en las redes móviles"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "La carga de la página es lo suficientemente rápida en redes móviles"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Categoría"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "Te recomendamos que reduzcas el tiempo de análisis, compilación y ejecución de JS. Para ello, puedes utilizar cargas útiles de JS más pequeñas. [Obtén más información](https://web.dev/mainthread-work-breakdown)."}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimiza el trabajo del hilo principal"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "Minimiza el trabajo del hilo principal"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "Para alcanzar la mayor cantidad de usuarios, los sitios tienen que funcionar en todos los navegadores principales. [Obtén más información](https://web.dev/pwa-cross-browser)."}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "El sitio funciona en diferentes navegadores"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Garantiza que las páginas individuales puedan vincularse directamente a través de la URL y que las URL sean únicas para que se puedan compartir en redes sociales. [Obtén más información](https://web.dev/pwa-each-page-has-url)."}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Cada página tiene una URL"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "Las transiciones deben ser ágiles cuando presionas en diferentes lugares, incluso con conexiones de red lentas. Esta experiencia es la clave del rendimiento percibido del usuario. [Obtén más información](https://web.dev/pwa-page-transitions)."}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "No parece que se bloqueen las transiciones de la página en la red"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "La latencia de entrada estimada es el tiempo aproximado, en milisegundos, que tarda tu app en responder a las acciones de los usuarios durante el periodo de 5 s más activo de carga de la página. Si la latencia es superior a 50 ms, es posible que los usuarios piensen que tu app es lenta. [Obtén más información](https://web.dev/estimated-input-latency)."}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "Latencia de entrada estimada"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "El primer procesamiento de imagen con contenido indica el momento en el que se visualiza en la pantalla el primer texto o imagen. [Obtén más información](https://web.dev/first-contentful-paint)."}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "Primer procesamiento de imagen con contenido"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "El primer tiempo inactivo de la CPU indica la primera vez que el hilo principal de la página está lo suficientemente inactivo para recibir acciones del usuario.  [Obtén más información](https://web.dev/first-cpu-idle)."}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "Primer tiempo inactivo de la CPU"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "La primera pintura significativa mide el momento en que se muestra el contenido principal de la página. [Obtén más información](https://web.dev/first-meaningful-paint)."}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "Primera pintura significativa"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "El tiempo de carga indica cuánto tarda una página en ser totalmente interactiva. [Obtén más información](https://web.dev/interactive)."}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "Tiempo de carga"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "El máximo retraso de primera entrada que podrían experimentar los usuarios es la duración (en milisegundos) de la tarea más larga. [Obtén más información](https://developers.google.com/web/updates/2018/05/first-input-delay)."}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "Máximo retraso de primera entrada posible"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "El índice de velocidad indica la rapidez con la que se puede ver el contenido de una página. [Obtén más información](https://web.dev/speed-index)."}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "Índice de velocidad"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "Suma todos los períodos entre FCP y el tiempo de carga, cuando la tarea tarda más de 50 ms. El resultado se expresa en milisegundos."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "Tiempo total de bloqueo"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "Los tiempos de ida y vuelta (RTT) de la red afectan mucho el rendimiento. Los valores altos de RTT respecto de un origen son indicio de que usar servidores más cercanos al usuario podría mejorar el rendimiento. [Obtén más información](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "lighthouse-core/audits/network-rtt.js | title": {"message": "Tiempos de ida y vuelta de la red"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "Las latencias del servidor pueden afectar el rendimiento de la Web. Un nivel alto de latencia del servidor en un origen indica que el servidor está sobrecargado o que su rendimiento de backend es bajo. [Obtén más información](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "Latencias de backend del servidor"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "Usar un service worker permite que tu aplicación web sea confiable en condiciones de red impredecibles. [Obtén más información](https://web.dev/offline-start-url)."}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url` no responde con un código de estado HTTP 200 cuando no hay conexión"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url` responde con un código de estado HTTP 200 cuando no hay conexión"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "Lighthouse no pudo leer el atributo `start_url` del manifiesto. Por lo tanto, se tomó como `start_url` la URL del documento. Mensaje de error: \"{manifestWarning}\"."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "Superior a la estimación"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "Asegúrate de que la cantidad y el tamaño de las solicitudes de red sean menores que los valores objetivo establecidos en la estimación de rendimiento. [Obtén más información](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 solicitud}other{# solicitudes}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "Estimación de rendimiento"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "Si ya configuraste el protocolo HTTPS, asegúrate de redireccionar el tráfico de HTTP a HTTPS a fin de habilitar las características de protección de la Web para todos los usuarios. [Obtén más información](https://web.dev/redirects-http)."}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "No redirecciona el tráfico HTTP a HTTPS"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "Redirecciona el tráfico HTTP a HTTPS"}, "lighthouse-core/audits/redirects.js | description": {"message": "Las redirecciones provocan retrasos adicionales antes de que la página se cargue. [Obtén más información](https://web.dev/redirects)."}, "lighthouse-core/audits/redirects.js | title": {"message": "Evita que haya varias redirecciones de página"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "A fin de configurar estimaciones para la cantidad y el tamaño de los recursos de la página, agrega un archivo budget.json. [Obtén más información](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 solicitud • {byteCount, number, bytes} KB}other{# solicitudes • {byteCount, number, bytes} KB}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "Asegúrate de que la cantidad de solicitudes y los tamaños de transferencia sean reducidos"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "Los vínculos canónicos indican qué URL mostrar en los resultados de la búsqueda. [Obtén más información](https://web.dev/canonical)."}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "Varias URL en conflicto ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "Redirige al usuario a otro dominio ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "URL no válida ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Hace referencia a otra ubicación de `hreflang` ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "URL relativa ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "Apunta a la URL raíz del dominio (la página principal), en lugar de a una página de contenido equivalente"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "El documento no tiene un vínculo `rel=canonical` válido"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "El documento tiene un atributo `rel=canonical` válido"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "Las fuentes con un tamaño inferior a 12 px son demasiado pequeñas y poco legibles, lo que obliga a los visitantes que acceden con dispositivos móviles a pellizcar la pantalla para ampliarla y poder leer el texto. Intenta que más del 60% del texto de la página tenga un tamaño igual o superior a 12 px. [Obtén más información](https://web.dev/font-size)."}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} del texto es legible"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "El texto es ilegible porque no hay metaetiquetas de la vista del puerto optimizadas para pantallas de dispositivos móviles."}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "El porcentaje {decimalProportion, number, extendedPercent} del texto es demasiado bajo (según la muestra {decimalProportionVisited, number, extendedPercent})."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "El documento no usa tamaños de fuente legibles"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "El documento usa tamaños de fuente legibles"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "Los vínculos de hreflang indican a los motores de búsqueda qué versión de una página deben incluir en los resultados de la búsqueda para una región o un idioma determinados. [Obtén más información](https://web.dev/hreflang)."}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "El documento no tiene un atributo `hreflang` válido"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "El documento tiene un atributo `hreflang` válido"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "Es posible que las páginas con códigos de estado HTTP de error no se indexen correctamente. [Obtén más información](https://web.dev/http-status-code)."}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "El código de estado HTTP de la página no es válido"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "El código de estado HTTP de la página es válido"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "Los motores de búsqueda no pueden incluir tus páginas en los resultados de la búsqueda si no tienen permiso para rastrearlas. [Obtén más información](https://web.dev/is-crawable)."}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "Se bloqueó la indexación de la página"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "No se bloqueó la indexación de la página"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "El texto descriptivo de los vínculos ayuda a los motores de búsqueda a entender tu contenido. [Obtén más información](https://web.dev/link-text)."}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Se encontró 1 vínculo}other{Se encontraron # vínculos}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "Los vínculos no tienen texto descriptivo"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "Los vínculos tienen texto descriptivo"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "Ejecuta la [Herramienta de pruebas de datos estructurados](https://search.google.com/structured-data/testing-tool/) y la herramienta [Structured Data Linter](http://linter.structured-data.org/) para validar los datos estructurados. [Obtén más información](https://web.dev/structured-data)."}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "Los datos estructurados son v<PERSON><PERSON>os"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "Se pueden incluir metadescripciones en los resultados de la búsqueda para resumir el contenido de la página. [Obtén más información](https://web.dev/meta-description)."}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "El texto de la descripción está vacío."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "El documento no tiene una metadescripción"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "El documento tiene una metadescripción"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "Los motores de búsqueda no pueden indexar el contenido de los complementos y muchos dispositivos limitan el uso de complementos o no los admiten. [Obtén más información](https://web.dev/plugins)."}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "El documento usa complementos"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "Los documentos evitan el uso de complementos"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "Si el formato del archivo robots.txt no es correcto, es posible que los rastreadores no puedan interpretar cómo quieres que se rastree o indexe tu sitio web. [Obtén más información](https://web.dev/robots-txt)."}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "La solicitud de robots.txt mostró el siguiente estado de HTTP: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Se encontró 1 error}other{Se encontraron # errores}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse no pudo descargar un archivo robots.txt"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt no es válido"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "robots.txt es válido"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "Los elementos interactivos, como los botones y vínculos, deben ser suficientemente grandes (48 × 48 px) y tener alrededor el espacio necesario para que sea posible tocarlos con facilidad sin presionar otros elementos a la vez. [Obtén más información](https://web.dev/tap-targets)."}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "El {decimalProportion, number, percent} de los elementos táctiles tiene un tamaño adecuado"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Los elementos táctiles son demasiado pequeños porque no hay metaetiquetas de la vista del puerto optimizadas para pantallas de dispositivos móviles"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "El tamaño de los elementos táctiles no es el adecuado"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Elementos superpuestos"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Elemento táctil"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "El tamaño de los elementos táctiles es el adecuado"}, "lighthouse-core/audits/service-worker.js | description": {"message": "El service worker es la tecnología que permite que tu app use varias funciones de las apps web progresivas, como el modo sin conexión, el agregado a la pantalla principal y las notificaciones push. [Obtén más información](https://web.dev/service-worker)."}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "Un service worker controla esta página, pero no se encontró ningún atributo `start_url` porque no se pudo analizar el archivo de manifiesto como un JSON válido"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "Un service worker controla esta página, pero el atributo `start_url` ({startUrl}) está fuera del alcance del service worker ({scopeUrl})"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "Un service worker controla esta página, pero no se encontró el atributo `start_url` porque no se obtuvo ningún manifiesto."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "Este origen tiene al menos un service worker, pero la página ({pageUrl}) no está dentro del alcance."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "No registra un service worker que controle la página y `start_url`"}, "lighthouse-core/audits/service-worker.js | title": {"message": "Registra un service worker que controle la página y `start_url`"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "El uso de una pantalla de presentación con un tema asegura que los usuarios tengan una experiencia de calidad al ejecutar tu app desde sus pantallas principales. [Obtén más información](https://web.dev/splash-screen)."}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "No se configuró para una pantalla de presentación personalizada"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "Se configuró para una pantalla de presentación personalizada"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "Se puede aplicar un tema a la barra de direcciones del navegador para que combine con tu sitio web. [Obtén más información](https://web.dev/themed-omnibox)."}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "No establece un color de tema para la barra de direcciones."}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "Establece un color de tema para la barra de direcciones."}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "Tiempo de bloqueo del subproceso principal"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "Terceros"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "El código de terceros puede reducir en gran medida el rendimiento de carga. Limita la cantidad de proveedores externos redundantes y prueba cargar el código de terceros después de que haya finalizado la carga principal de tu página. [Obtén más información](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "El código de terceros bloqueó el subproceso principal por {timeInMs, number, milliseconds} ms"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "Reduce el impacto del código de terceros"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "Uso por parte de terceros"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "El tiempo hasta el primer byte indica el momento en el que el servidor envía una respuesta. [Obtén más información](https://web.dev/time-to-first-byte)."}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "El documento raíz tardó {timeInMs, number, milliseconds} ms"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "Reduce los tiempos de respuesta del servidor (TTFB)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "La respuesta del servidor es lenta (TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "Duración"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "Hora de inicio"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "Tipo"}, "lighthouse-core/audits/user-timings.js | description": {"message": "Te recomendamos que incorpores la API de Tiempos de usuario en tu app para calcular su rendimiento real durante las principales experiencias de usuario. [Obtén más información](https://web.dev/user-timings)."}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 tiempo de usuario}other{# tiempos de usuario}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "Medidas y marcas de Tiempos de usuario"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Se encontró un elemento <link> previo a la conexión para \"{securityOrigin}\", pero el navegador no lo usó. Comprueba que el atributo `crossorigin` se esté usando correctamente."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "Te recomendamos agregar sugerencias de recursos `preconnect` o `dns-prefetch` para establecer conexiones tempranas con orígenes externos importantes. [Obtén más información](https://web.dev/uses-rel-preconnect)."}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "Establece conexión previamente con los orígenes necesarios"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Se encontró un elemento <link> de precarga para \"{preloadURL}\", pero el navegador no lo usó. Comprueba que el atributo `crossorigin` se esté usando correctamente."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "Te recomendamos usar `<link rel=preload>` para dar prioridad a la obtención de los recursos que, en este momento, se solicitan en una instancia posterior de la carga de la página. [Obtén más información](https://web.dev/uses-rel-preload)."}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "Carga previamente las solicitudes clave"}, "lighthouse-core/audits/viewport.js | description": {"message": "Agrega una etiqueta `<meta name=\"viewport\">` para optimizar la app para pantallas de dispositivos móviles. [Obtén más información](https://web.dev/viewport)."}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "No se encontró ninguna etiqueta `<meta name=\"viewport\">`"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "No tiene una etiqueta `<meta name=\"viewport\">` con `width` o `initial-scale`"}, "lighthouse-core/audits/viewport.js | title": {"message": "Tiene una etiqueta `<meta name=\"viewport\">` con `width` o `initial-scale`"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "Tu app debe mostrar algún contenido cuando JavaScript está inhabilitado, aunque solo sea un aviso para informar al usuario que se necesita JavaScript para usar la app. [Obtén más información](https://web.dev/without-javascript)."}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "El cuerpo de la página debe renderizar parte del contenido aunque sus secuencias de comandos no estén disponibles."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "No se incluye contenido alternativo cuando no está disponible JavaScript"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "Se incluye parte del contenido cuando no está disponible JavaScript"}, "lighthouse-core/audits/works-offline.js | description": {"message": "Si estás creando una app web progresiva, puedes usar un service worker para que la app funcione sin conexión. [Obtén más información](https://web.dev/works-offline)."}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "La página actual no responde con un código de estado HTTP 200 cuando no hay conexión"}, "lighthouse-core/audits/works-offline.js | title": {"message": "La página actual responde con un código de estado HTTP 200 cuando no hay conexión"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "Es posible que la página no se esté cargando sin conexión porque se redireccionó la URL de prueba ({requested}) a \"{final}\". Prueba directamente la segunda URL."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "A continuación, se indican consejos para optimizar el uso de ARIA en tu app, lo que puede mejorar la experiencia de los usuarios de tecnologías de asistencia, como los lectores de pantalla."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Aquí tienes consejos para proporcionar contenido alternativo para audio y video. Así se puede mejorar la experiencia de los usuarios con dificultades auditivas o visuales."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Audio y video"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Estos elementos destacan las prácticas recomendadas de accesibilidad más habituales."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Prácticas recomendadas"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "Estas comprobaciones incluyen consejos para [mejorar la accesibilidad de tu app web](https://developers.google.com/web/fundamentals/accessibility). Solo algunos problemas de accesibilidad pueden detectarse de forma automática. Por eso, te recomendamos realizar también pruebas manuales."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "Estos elementos abarcan áreas que las herramientas de prueba automáticas no contemplan. Obtén más información en nuestra guía sobre [cómo revisar los aspectos de accesibilidad](https://developers.google.com/web/fundamentals/accessibility/how-to-review)."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "Accesibilidad"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "A continuación, se indican consejos para facilitar la lectura del contenido."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Contraste"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "A continuación, se indican consejos para que los usuarios con diversas configuraciones regionales puedan interpretar mejor el contenido de las páginas."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internacionalización y localización"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "A continuación, se indican consejos para mejorar la semántica de los controles de tu app. Estos consejos pueden mejorar la experiencia de los usuarios de tecnologías de asistencia, como los lectores de pantalla."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Nombres y etiquetas"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Estas son oportunidades para mejorar la navegación con el teclado en tu app."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navegación"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Aquí tienes consejos para mejorar la lectura de datos en tablas o listas con tecnologías de asistencia como los lectores de pantalla."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tablas y listas"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Recomendaciones"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "Las estimaciones de rendimiento establecen estándares para el rendimiento de tu sitio."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "Estimaciones"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "Obtén más información sobre el rendimiento de tu app. Estos números no [afectan directamente](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) la medición del rendimiento."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnós<PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "El aspecto más importante del rendimiento es la rapidez con la que se renderizan los píxeles en la pantalla. Métricas clave: primer procesamiento de imagen con contenido, primera pintura significativa"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Mejoras del primer procesamiento de imagen"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Estas sugerencias pueden hacer que tus páginas se carguen más rápido. No [afectan directamente](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) la medición del rendimiento."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Oportunidades"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "Métricas"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Mejora la experiencia de carga general para que la página responda bien y se pueda usar lo antes posible. Métricas clave: Tiempo de carga, <PERSON><PERSON><PERSON> de velocidad"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Mejoras generales"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "Rendimiento"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "Estas comprobaciones se centran en diferentes aspectos de las apps web progresivas. [Obtén más información](https://developers.google.com/web/progressive-web-apps/checklist)."}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "Estas comprobaciones son necesarias según el modelo de referencia [Lista de tareas para AWP](https://developers.google.com/web/progressive-web-apps/checklist), pero Lighthouse no las realiza automáticamente. Es importante que las verifiques a mano (aunque no afectan a la puntuación)."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "App web progresiva"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "Rá<PERSON><PERSON> y confiable"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Instalable"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Optimizado para PWA"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "Estas comprobaciones aseguran que tu página esté optimizada para posicionarse bien en los resultados de los motores de búsqueda. Hay otros factores que Lighthouse no comprueba y que pueden afectar tu posicionamiento en los buscadores. [Obtén más información](https://support.google.com/webmasters/answer/35769)."}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "Ejecuta estos validadores adicionales en tu sitio web para comprobar más prácticas recomendadas de SEO."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "Edita el código HTML de tu página web de forma que los rastreadores puedan entender mejor el contenido de tu app."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "Prácticas recomendadas para el contenido"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Para aparecer en los resultados de búsqueda, los rastreadores necesitan acceso a tu app."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Rastreo e indexación"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "Asegúrate de que tus páginas estén optimizadas para dispositivos móviles a fin de que los usuarios no tengan que pellizcar ni hacer zoom para leer las páginas de contenido. [Obtén más información](https://developers.google.com/search/mobile-sites/)."}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "Optimizada para dispositivos móviles"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "TTL en caché"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "Ubicación"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "Nombre"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "Solicitudes"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "Tipo de recurso"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Tiempo de uso"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "Tamaño de transferencia"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Ah<PERSON><PERSON> posibles"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "Ah<PERSON><PERSON> posibles"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Ahorro posible de {wastedBytes, number, bytes} KB"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Ahorro posible en {wastedMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "Documento"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "Fuente"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "Imagen"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "Contenido multimedia"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "Secuencia de comandos"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Hoja de estilo"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Terceros"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "Total"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "Se produjo un error de registro de seguimiento durante la carga de la página. Vuelve a ejecutar Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "Se agotó el tiempo de espera de la conexión inicial del protocolo del depurador."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome no recopiló ninguna captura de pantalla al cargar la página. Comprueba que haya contenido visible en la página y vuelve a ejecutar Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "Los servidores DNS no pudieron resolver el dominio proporcionado."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "El recopilador {artifactName} obligatorio encontró un error: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "Se produjo un error interno de Chrome. Reinicia Chrome y vuelve a ejecutar Lighthouse."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "No se ejecutó el recopilador necesario para {artifactName}."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse no pudo cargar correctamente la página que solicitaste. Comprueba que estás probando la URL correcta y que el servidor responde correctamente a todas las solicitudes."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse no pudo cargar correctamente la URL que solicitaste porque la página dej<PERSON> de responder."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "La URL que proporcionaste no tiene un certificado de seguridad válido. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome evitó la carga de la página y mostró una pantalla intersticial. Verifica que estés probando la URL correcta y que el servidor responda adecuadamente a todas las solicitudes."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse no pudo cargar correctamente la página que solicitaste. Verifica que estés probando la URL correcta y que el servidor responda adecuadamente a todas las solicitudes. (Detalles: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse no pudo cargar correctamente la página que solicitaste. Verifica que estés probando la URL correcta y que el servidor responda adecuadamente a todas las solicitudes. (Código de estado: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "La página tardó demasiado en cargarse. Sigue los consejos del informe para reducir el tiempo de carga de la página y vuelve a ejecutar Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "Se superó el tiempo asignado para la respuesta de protocolo de DevTools. (Método: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "Se superó el tiempo asignado para obtener el contenido de los recursos"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "<PERSON> parecer, la URL que proporcionaste no es válida."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "Mostrar auditorías"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "Navegación inicial"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "Latencia de ruta crítica máxima:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "Error"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "Error del informe: No hay información de la auditoría"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "Datos de prueba"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> de [Lighthouse](https://developers.google.com/web/tools/lighthouse/) de la página actual en una red móvil emulada. Los valores son estimados y pueden variar."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "Elementos adicionales que se deben comprobar manualmente"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "No aplicable"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "Oportunidad"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "<PERSON><PERSON><PERSON> estimado"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "Auditorías aprobadas"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "Contraer fragmento"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "Expandir fragmento"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "Mostrar recursos de terceros"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "Algunos problemas afectaron la ejecución de Lighthouse:"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "Los valores son estimados y pueden variar. La medición del rendimiento se [basa solo en estas métricas](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "Auditorías aprobadas con advertencias"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "Advertencias: "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "Puedes subir tu GIF a un servicio que permita insertarlo como un video HTML5."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "Instala un [complemento de carga diferida de WordPress](https://wordpress.org/plugins/search/lazy+load/) con la capacidad de postergar las imágenes fuera de pantalla o bien cambia a un tema que incluya esa función. También puedes usar [el complemento AMP](https://wordpress.org/plugins/amp/)."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "Existen varios complementos de WordPress que pueden ayudarte a [insertar elementos fundamentales](https://wordpress.org/plugins/search/critical+css/) o a [postergar recursos menos importantes](https://wordpress.org/plugins/search/defer+css+javascript/). Ten en cuenta que las optimizaciones que ofrecen estos complementos pueden interferir con funciones de tu tema o complementos, por lo que seguramente tengas que hacer cambios en el código."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "Los temas, los complementos y las especificaciones del servidor afectan al tiempo de respuesta. Puedes buscar un tema más optimizado, seleccionar un complemento de optimización o actualizar tu servidor."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "Puedes mostrar fragmentos en tus listas de entradas (por ejemplo, mediante la etiqueta \"<more>\"), reducir la cantidad de entradas que se muestran en cada página, dividir tus entradas más largas en múltiples páginas o usar un complemento para postergar la carga de los comentarios."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "Hay varios [complementos de WordPress](https://wordpress.org/plugins/search/minify+css/) que pueden concatenar, reducir y comprimir los estilos para acelerar tu sitio web. Te recomendamos que, si es posible, uses un proceso de compilación para reducir los estilos de forma anticipada."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "Hay varios [complementos de WordPress](https://wordpress.org/plugins/search/minify+javascript/) que pueden concatenar, reducir y comprimir las secuencias de comandos para acelerar tu sitio web. Te recomendamos que, si es posible, uses un proceso de compilación para realizar la reducción de forma anticipada."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "Puedes reducir o cambiar la cantidad de [complementos de WordPress](https://wordpress.org/plugins/) que cargan hojas de estilo CSS que tu página no usa. Para identificar los complementos que agregan hojas de estilo CSS innecesarias, prueba ejecutar la [cobertura de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) en DevTools de Chrome. Puedes identificar el tema o complemento concreto en la URL de la hoja de estilo. Presta atención a los complementos que tengan varias hojas de estilo en la lista con muchos elementos en rojo en la cobertura de código. Los complementos solo deberían poner en cola hojas de estilo que se usen en la página."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "Puedes reducir o cambiar la cantidad de [complementos de WordPress](https://wordpress.org/plugins/) que cargan secuencias JavaScript que tu página no usa. Para identificar los complementos que agregan secuencias JS innecesarias, prueba ejecutar la [cobertura de código](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) en DevTools de Chrome. Puedes identificar el tema o complemento concreto en la URL de la secuencia de comandos. Presta atención a los complementos que tengan varias secuencias de comandos en la lista con muchos elementos en rojo en la cobertura de código. Los complementos solo deberían poner en cola secuencias de comandos que se usen en la página."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "Consulta información sobre el [almacenamiento en la memoria caché del navegador en WordPress](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "Puedes usar un [complemento de optimización de imágenes de WordPress](https://wordpress.org/plugins/search/optimize+images/) que comprima tus imágenes y conserve la calidad."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "Carga imágenes directamente a través de la [biblioteca de medios](https://codex.wordpress.org/Media_Library_Screen) para garantizar que estén disponibles los tamaños de imagen requeridos y, luego, insértalas desde la biblioteca de medios, o bien usa el widget de imágenes para asegurarte de que se utilicen los tamaños de imagen óptimos (incluidos los que se emplean para interrupciones receptivas). Evita usar imágenes `Full Size`, a menos que las dimensiones sean adecuadas para su empleo. [Obtén más información](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)."}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "Puedes habilitar la compresión de texto en la configuración de tu servidor web."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "Puedes utilizar un [complemento](https://wordpress.org/plugins/search/convert+webp/) o servicio que convierta automáticamente las imágenes que subas a los formatos óptimos."}}