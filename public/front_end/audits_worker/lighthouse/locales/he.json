{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "מקשי גישה מאפשרים למשתמשים להתמקד במהירות בחלק מסוים בדף. כדי לאפשר ניווט תקין, כל מקש גישה צריך להיות ייחודי. [מידע נוסף](https://web.dev/accesskeys/)."}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "יש ערכי `[accesskey]` שאינם ייחודיים"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]` הערכים ייחודיים"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "כל ARIA `role` תומ<PERSON><PERSON> בקבוצת משנה ספציפית של `aria-*` מאפיינים. חוסר התאמה בין המאפיינים הופך אותם `aria-*` ללא חוקיים. [מידע נוסף](https://web.dev/aria-allowed-attr/)."}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "יש מאפייני `[aria-*]` שלא תואמים לתפקידים שלהם"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "מאפייני ה-`[aria-*]`‎ תואמים לתפקידים שלהם"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "חלק מתפקידי ה-ARIA כוללים מאפיינים נדרשים שמתארים לקוראי המסך את מצב הרכיב. [מידע נוסף](https://web.dev/aria-required-attr/)."}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "יש רכיבים מסוג `[role]` שאין להם את כל מאפייני ה-`[aria-*]` הנדרשים"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "לכל הרכיבים מסוג `[role]` יש את כל מאפייני ה-`[aria-*]` הדרושים"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "תפקידי הורה מסוימים של ARIA צריכים לכלול תפקידי צאצא ספציפיים כדי לבצע את פונקציות הנגישות שלהם. [מידע נוסף](https://web.dev/aria-required-children/)."}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "ברכיבים עם `[role]` של ARIA שדורשים מצאצאים לכלול `[role]` ספציפי, חסרים חלק מהצאצאים הנדרשים האלה, או שכולם חסרים בהם."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "ברכיבים עם `[role]` של ARIA שדורשים מצאצאים לכלול `[role]` ספציפי, קיימים כל הצאצאים הנדרשים."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "חלק מתפקידי הצאצא מסוג ARIA חייבים להיכלל בין תפקידי הורה ספציפיים כדי למלא באופן תקין את פונקציות הנגישות שלהם. [מידע נוסף](https://web.dev/aria-required-parent/)."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "מאפייני `[role]` לא נמצאים בתוך רכיב ההורה הנדרש שלהם"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "רכיבים מסוג `[role]` נמצאים בתוך רכיב ההורה הנדרש שלהם"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "תפקידי ARIA חייבים לכלול ערכים חוקיים כדי לבצע את פונקציות הנגישות שלהם. [מידע נוסף](https://web.dev/aria-roles/)."}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "יש ערכי `[role]` לא חוקיים"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "ערכי ה-`[role]` חוקיים"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "טכנולוגיות לנגישות, כמו קוראי מסך, לא יכולות לפענח מאפייני ARIA שהערכים שלהם לא חוקיים. [מידע נוסף](https://web.dev/aria-valid-attr-value/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "למאפייני ‎`[aria-*]`‎ אין ערכים חוקיים"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "למאפייני ה-`[aria-*]` יש ערכים חוקיים"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "טכנולוגיות לנגישות, כמו קוראי מסך, לא יכולות לפענח מאפייני ARIA עם שמות לא חוקיים. [מידע נוסף](https://web.dev/aria-valid-attr/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "יש מאפייני `[aria-*]` שאינם חוקיים או שכוללים שגיאות איות"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "מאפייני ה-`[aria-*]` חוקיים ולא כוללים שגיאות איות"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "כתוביות מאפשרות לאנשים חרשים ולקויי שמיעה להשתמש ברכיבי אודיו, ומספקות מידע קריטי, כמו זהות הדובר, המילים שנאמרות ומידע אחר שאינו מועבר בדיבור. [מידע נוסף](https://web.dev/audio-caption/)."}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "יש רכיבי `<audio>` שח<PERSON>ר בהם רכיב `<track>` עם `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "רכיבי `<audio>` מכילים רכיב `<track>` עם `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "רכיבים שנכשלו בבדיקה"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "כשאין ללחצן שם נגיש, קוראי מסך אומרים את המילה \"לחצן\", ובמצב כזה אנשים שמסתמכים על קוראי מסך יתקשו להשתמש בלחצן. [מידע נוסף](https://web.dev/button-name/)."}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "ללחצנים אין שמות ייחודיים"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "ללחצנים יש שם נגיש"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "הוספה של דרכים לעקיפת תוכן שחוזר על עצמו מאפשרת לאנשים שמשתמשים במקלדת לנווט בדף בצורה יעילה יותר. [מידע נוסף](https://web.dev/bypass/)."}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "הדף לא כולל כותרת, קישור לדילוג או קטע שמסומן כ-landmark"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "הדף כולל כותרת, קישור לדילוג או קטע שמסומן כ-landmark"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "משתמשים רבים מתקשים לקרוא טקסט עם ניגודיות נמוכה, או לא מסוגלים לקרוא אותו. [מידע נוסף](https://web.dev/color-contrast/)."}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "יחס הניגודיות של צבעי הרקע והחזית אינו מספיק."}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "יש יחס ניגודיות מספיק בין צבעי הרקע והחזית"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "כשרשימות של הגדרות לא מסומנות כראוי, קוראי מסך עשויים לספק פלט מבלבל או לא מדויק. [מידע נוסף](https://web.dev/definition-list/)."}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "יש רכיבי `<dl>` שלא מכילים רק רכיבי `<script>` או `<template>`, או קבוצות `<dt>` ו-`<dd>` עם סדר תקין."}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "רכיבי `<dl>` מכילים רק <PERSON>כיבי `<script>` או `<template>`, או קבוצות `<dt>` ו-`<dd>` עם סדר תקין."}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "פריטים ברשימות של הגדרות (`<dt>` ו-`<dd>`) צריכים להיות תחומים בתוך רכיב הורה מסוג `<dl>` כדי שקוראי מסך יוכלו להקריא אותם בצורה נכונה. [מידע נוסף](https://web.dev/dlitem/)."}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "יש פריטים ברשימות של הגדרות שלא תחומים בין רכיבי `<dl>`"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "פריטים ברשימות של הגדרות מוצבים בין רכיבי `<dl>`"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "הכותרת מספקת סקירה כללית של הדף למשתמשים הנעזרים בקוראי מסך. בנוסף, משתמשים של מנועי חיפוש מסתמכים במידה רבה על הכותרת כדי להבין אם הדף רלוונטי לחיפוש שלהם. [מידע נוסף](https://web.dev/document-title/)."}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "למסמך אין רכיב `<title>`"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "המסמך מכיל רכיב `<title>`"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "הערך של מאפיין id חייב להיות ייחודי כדי למנוע מטכנולוגיות לנגישות להתעלם ממופעים אחרים. [מידע נוסף](https://web.dev/duplicate-id/)."}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "יש בדף מאפייני `[id]` שאינם ייחודיים"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "מאפייני ה-`[id]` בדף הם ייחודיים"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "משתמשים הנעזרים בקוראי מסך מסתמכים על כותרות של מסגרות כדי להבין מה תוכן המסגרות. [מידע נוסף](https://web.dev/frame-title/)."}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "לרכיבי `<frame>` או `<iframe>` אין מאפיין title"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "לרכיבי `<frame>` או `<iframe>` יש מאפיין title"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "אם בדף לא מצוין מאפיין lang, קורא המסך יפעל כאילו שהדף כתוב בשפת ברירת המחדל שהמשתמש בחר במהלך הגדרת קורא המסך. אם שפת הדף שונה משפת ברירת המחדל, ייתכן שקורא המסך לא יקרא בצורה נכונה את הטקסט שבדף. [מידע נוסף](https://web.dev/html-has-lang/)."}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "לרכיב `<html>` אין רכיב `[lang]`"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "לרכיב `<html>` יש מאפיין `[lang]`"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "ציון של [שפת BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) חוקית עוזר לקוראי מסך להקריא טקסט בצורה נכונה. [מידע נוסף](https://web.dev/html-lang-valid/)."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "לרכיב `<html>` אין ערך חוקי עבור המאפיין `[lang]`."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "לרכיב `<html>` יש ערך חוקי עבור המאפיין `[lang]`"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "רכיבים אינפורמטיביים צריכים לכלול טקסט חלופי קצר ותיאורי. אפשר להתעלם מרכיבי עיצוב עם מאפיין alt ריק. [מידע נוסף](https://web.dev/image-alt/)."}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "יש רכיבי תמונה ללא מאפייני `[alt]`"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "לרכיבי תמונה יש מאפייני `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "כשתמונה משמשת כלחצן `<input>`, הוספה של טקסט חלופי יכולה לעזור למשתמשים הנעזרים בקוראי מסך להבין מה הלחצן עושה. [מידע נוסף](https://web.dev/input-image-alt/)."}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "יש רכיבי `<input type=\"image\">` שאין להם טקסט `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "לרכיבים מסוג `<input type=\"image\">` יש טקסט `[alt]`"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "תוויות עוזרות לוודא ששמות של פקדי טפסים מוקראים באופן תקין על ידי טכנולוגיות לנגישות, כמו קוראי מסך. [מידע נוסף](https://web.dev/label/)."}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "לא שויכו תוויות אל רכיבי טופס"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "לרכיבי טופס יש תוויות המשויכות אליהם"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "טבלה המשמשת לקביעת פריסה אינה יכולה לכלול רכיבי נתונים, כמו <PERSON><PERSON><PERSON><PERSON><PERSON> th, הרכ<PERSON><PERSON> ‏caption או המאפיין summary, כי הם עשויים ליצור חוויה מבלבלת עבור משתמשים הנעזרים בקורא מסך. [מידע נוסף](https://web.dev/layout-table/)."}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "ברכיבי `<table>` להצגה, אין הימנעות משימוש ב-`<th>`, ב-`<caption>` או במ<PERSON><PERSON><PERSON><PERSON>ן `[summary]`."}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "רכיבי `<table>` להצגה לא מכילים `<th>`, `<caption>` או את המאפיין `[summary]`."}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "כשהטקסט של הקישור מובן וייחודי ואפשר להתמקד בו, משתמשים שנעזרים בקורא מסך נהנים מחוויית ניווט משופרת. מצב זה נכון גם לגבי טקסט חלופי של תמונות כשנעשה בהן שימוש כקישורים. [מידע נוסף](https://web.dev/link-name/)."}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "לקישורים אין שמות ייחודיים"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "לקישורים יש שמות ייחודיים"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "קוראי מסך מקריאים רשימות בצורה מסוימת. שמירה על מבנה רשימות תקין מאפשרת הקראה תקינה על ידי קורא המסך. [מידע נוסף](https://web.dev/list/)."}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "הרשימות לא מכילות רק רכיבי `<li>` ורכיבים שתומכים בסקריפט (`<script>` ו- `<template>`)."}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "הרשימות מכילות רק רכיבי `<li>` ורכיבים שתומכים בסקריפט (`<script>` ו-`<template>`)."}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "כדי שקוראי מסך יוכלו לקרוא כראוי פריטים ברשימות, (`<li>`) הם צריכים להופיע בין רכיבי הורה מסוג `<ul>` או `<ol>`. [מידע נוסף](https://web.dev/listitem/)."}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "יש פריטים ברשימות (`<li>`) שלא נמצאים בין רכיבי הורה של `<ul>` או `<ol>`."}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "פריטים ברשימות (`<li>`) מופיעים בין רכיבי הורה `<ul>` או `<ol>`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "משתמשים לא מצפים לרענון אוטומטי של הדף, ושימוש ברענון כזה יחזיר את ההתמקדות אל ראש הדף. מצב כזה יכול ליצור חוויה מתסכלת או מבלבלת. [מידע נוסף](https://web.dev/meta-refresh/)."}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "המסמך מכיל `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "המסמך לא כולל שימוש ב-`<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "השבתת האפשרות להגדיל את תצוגת התוכן יוצרת בעיה עבור משתמשים עם ליקויי ראייה שנוהגים להגדיל את המסך כדי לראות היטב את תוכן דף האינטרנט. [מידע נוסף](https://web.dev/meta-viewport/)."}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "נעשה שימוש ב-`[user-scalable=\"no\"]` בר<PERSON><PERSON><PERSON> `<meta name=\"viewport\">`, או שערך המאפיין `[maximum-scale]` קטן מ-5."}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "לא נעשה שימוש ב-`[user-scalable=\"no\"]` ברכיב `<meta name=\"viewport\">` וערך המאפיין `[maximum-scale]` לא קטן מ-5."}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "קוראי מסך לא יכולים לתרגם תוכן שאינו טקסט. הוספה של טקסט חלופי לרכיבי `<object>` עוזרת להבהיר את המשמעות כשמשתמשים בקוראי מסך. [מידע נוסף](https://web.dev/object-alt/)."}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "יש רכיבי `<object>` שאין להם טקסט `[alt]`"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "לרכיבים מסוג `<object>` יש טקסט `[alt]`"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "ערך גדול מ-0 מרמז על סדר ניווט מפורש. זו אפשרות תקינה מבחינה טכנית, אבל במקרים רבים היא גורמת לחוויה מתסכלת עבור משתמשים שמסתמכים על טכנולוגיות לנגישות. [מידע נוסף](https://web.dev/tabindex/)."}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "לחלק מהרכיבים יש ערך `[tabindex]` גדול מ-0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "לאף רכיב אין ערך `[tabindex]` גדול מ-0"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "קוראי מסך כוללים תכונות שמקלות את הניווט בטבלאות. כשתאי `<td>` שמשתמשים במאפיין `[headers]` מתייחסים רק לתאים אחרים באותה טבלה, משתמשים הנעזרים בקוראי מסך יכולים ליהנות מחוויה טובה יותר. [מידע נוסף](https://web.dev/td-headers-attr/)."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "תאים ברכיב `<table>` שמשתמשים במא<PERSON>יין `[headers]`, מתייחסים לרכיב `id` שלא נמצא באותה טבלה."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "תאים ברכיב `<table>` שמשתמשים במא<PERSON>יין `[headers]`, מתייחסים לתאים אחרים באותה הטבלה."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "קוראי מסך כוללים תכונות שמקלות את הניווט בטבלאות. כשכותרות של טבלאות מתייחסות תמיד לקבוצה כלשהי של תאים, משתמשים הנעזרים בקורא מסך יכולים ליהנות מחוויה טובה יותר. [מידע נוסף](https://web.dev/th-has-data-cells/)."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "יש רכיבי `<th>` ורכיבים עם `[role=\"columnheader\"/\"rowheader\"]` שאין להם את תאי הנתונים שהם מתארים."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "לרכיבי `<th>` ולרכיבים עם `[role=\"columnheader\"/\"rowheader\"]` יש תאי נתונים שהם מתארים."}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "ציון של [שפת BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) חוקית ברכיבים עוזר להבטיח הגייה נכונה של הטקסט על ידי קורא המסך. [מידע נוסף](https://web.dev/valid-lang/)."}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "יש מאפייני `[lang]` שאין להם ערך חוקי"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "למאפייני `[lang]` יש ערך חוקי"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "כש<PERSON>רטון כולל כתוביות, המידע שהוא כולל נגיש יותר למשתמשים חרשים ולקויי שמיעה. [מידע נוסף](https://web.dev/video-caption/)."}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "יש רכיבי `<video>` שלא מכילים רכיב `<track>` עם `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "רכיבי `<video>` מ<PERSON>י<PERSON>ים רכיב `<track>` עם `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "בסרטונים, תיאורים קוליים מספקים מידע רלוונטי שאי אפשר להבין מהדיאלוג. למשל, הבעות פנים ונופים. [מידע נוסף](https://web.dev/video-description/)."}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "יש רכיבי `<video>` שלא מכילים רכיב `<track>` עם `[kind=\"description\"]`."}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "רכיבי `<video>` מ<PERSON>י<PERSON>ים רכיב `<track>` עם `[kind=\"description\"]`"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "כדי שהדף יוצג למשתמשים באופן אידיאלי ב-iOS לאחר הוספת Progressive Web App למסך הבית, מומלץ להגדיר `apple-touch-icon`. המאפיין חייב להפנות לתמונת PNG לא שקופה בפורמט ריבוע בגודל 192 פיקסלים (או 180 פיקסלים). [מידע נוסף](https://web.dev/apple-touch-icon/)."}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "הדף לא מכיל `apple-touch-icon` חוקי"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "`apple-touch-icon-precomposed` לא עדכני; עדיף להשתמש ב-`apple-touch-icon`."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "האתר מכיל `apple-touch-icon` חו<PERSON>י"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "תוספים ל-Chrome השפיעו לרעה על ביצועי הטעינה של הדף הזה. כדאי לבדוק את הדף במצב גלישה בסתר או באמצעות פרופיל Chrome שאינו כולל תוספים."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "הערכת סקריפט"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "ניתו<PERSON>קריפט"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "<PERSON><PERSON><PERSON> כולל של CPU (יחידת עיבוד מרכזית)"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "כדאי לשקול את האפשרות לקצר את הזמן הדרוש לניתוח, הידור וביצוע של JS. לשם כך כדאי להשתמש במטענים ייעודיים (payloads) קטנים יותר של JS. [מידע נוסף](https://web.dev/bootup-time)."}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "יש לקצר את זמן הביצוע של JavaScript"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "<PERSON><PERSON><PERSON> ביצוע של JavaScript"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "קובצי GIF גדולים לא מעבירים אנימציות בצורה יעילה. כדי לצמצם את מספר הבייטים שמועברים ברשת, במקום קובצי GIF כדאי לשקול את האפשרות להשתמש בסרטוני MPEG4/WebM בשביל אנימציות ובקובצי PNG/WebP בשביל תמונות סטטיות. [מידע נוסף](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "יש להשתמש בפורמטים של וידאו כדי להציג תוכן אנימציה"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "כדי לקצר את הזמן עד לפעילות מלאה, כדאי לשקול לבצע טעינה הדרגתית של תמונות מוסתרות ותמונות שלא מופיעות מיד במסך, כך שייטענו רק אחרי סיום הטעינה של כל המשאבים הקריטיים. [מידע נוסף](https://web.dev/offscreen-images)."}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "יש לעכב טעינה של תמונות שאינן מופיעות במסך"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "משאבים חוסמים את הצגת התמונה הראשונית של הדף במסך כדאי לשקול את האפשרות לספק תוכן JS/CSS קריטי באופן מוטבע ולדחות את הטעינה של כל תוכן ה-JS/הסגנונות שאינם קריטיים. [מידע נוסף](https://web.dev/render-blocking-resources)."}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "יש להימנע ממשאבים שחוסמים עיבוד"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "מטענים ייעודיים (payload) בנפח גדול המועברים ברשת עולים למשתמשים כסף ולעתים קרובות מאריכים את זמני הטעינה. [מידע נוסף](https://web.dev/total-byte-weight)."}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "הגודל הכולל היה ‎{totalBytes, number, bytes} KB"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "יש להימנע מהעברה של מטענים ייעודיים ענקיים (payload) ברשת"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "נמנע מהעברה של מטענים ייעודיים ענקיים (payload) ברשת"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "הקטנה של קובצי CSS יכולה לצמצם את הגודל של מטענים ייעודיים (payloads) שמועברים ברשת. [מידע נוסף](https://web.dev/unminified-css)."}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "יש להקטין קובצי CSS"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "הקטנה של קובצי JavaScript יכולה לצמצם את המטען הייעודי (payload) ולקצר את משך הזמן הנדרש לניתוח סקריפט. [מידע נוסף](https://web.dev/unminified-javascript)."}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "יש לקצר את קוד JavaScript למינימום ההכרחי"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "יש להסיר מגיליונות הסגנונות כללים שאינם בשימוש ולדחות את הטעינה של רכיבי CSS שאינם חלק מהתוכן בחלק העליון והקבוע. הפעולה הזו תצמצם צריכה בלתי נחוצה של בייטים כתוצאה מפעילות הרשת. [מידע נוסף](https://web.dev/unused-css-rules)."}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "יש להסיר CSS שאינו בשימוש"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "יש להסיר JavaScript שאינו בשימ<PERSON>ש כדי לצמצם צריכת בייטים על-ידי פעילות ברשת."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "יש להסיר JavaScript שאינו בשימוש"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "משך חיים ארוך של מטמון יכול לזרז את הביקורים החוזרים בדף. [מידע נוסף](https://web.dev/uses-long-cache-ttl)."}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{נמצא משאב אחד}two{נמצאו # משאבים}many{נמצאו # משאבים}other{נמצאו # משאבים}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "יש להציג נכסים סטטיים בעזרת מדיניות מטמון יעילה"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "יש להשתמש במדיניות מטמון יעילה בנכסים סטטיים"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "תמונות שעברו אופטימיזציה נטענות מהר יותר וצורכות פחות נתונים בחבילת הגלישה. [מידע נוסף](https://web.dev/uses-optimized-images)."}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "יש לקודד תמונות בצורה יעילה"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "הצגת תמונות שהגודל שלהן הוגדר בצורה נכונה עוזרת לחסוך בניצול חבילת הגלישה ולקצר את זמן הטעינה. [מידע נוסף](https://web.dev/uses-responsive-images)."}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "יש להגדיר את גודל התמונות בצורה נכונה"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "הצגת משאבים המבוססים על טקסט צריכה להתבצע עם דחיסה (gzip‏, deflate או brotli) כדי לצמצם את כמות הבייטים שמועברים ברשת. [מידע נוסף](https://web.dev/uses-text-compression)."}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "יש להפעיל דחיסת טקסט"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "לעתים קרובות, פורמטים של תמונות כמו JPEG 2000‏, JPEG XR ו-WebP מספקים דחיסה טובה יותר מאשר PNG או JPEG. הדחיסה המשופרת מקצרת את זמן ההורדות ומצמצמת את צריכת הנתונים. [מידע נוסף](https://web.dev/uses-webp-images)."}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "יש להציג תמונות בפורמטים עדכניים"}, "lighthouse-core/audits/content-width.js | description": {"message": "אם הרוחב של תוכן האפליקציה לא תואם לרוחב של אזור התצוגה, ייתכן שלא בוצעה אופטימיזציה לאפליקציה שלך עבור מסכים של ניידים. [מידע נוסף](https://web.dev/content-width)."}, "lighthouse-core/audits/content-width.js | explanation": {"message": "גודל אזור התצוגה של {innerWidth} פיקסלים לא תואם לגודל החלון של {outerWidth} פיקסלים."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "הגודל של התוכן הוגדר בצורה לא תקינה עבור אזור התצוגה"}, "lighthouse-core/audits/content-width.js | title": {"message": "הגודל של התוכן הוגדר בצורה תקינה עבור אזור התצוגה"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "בקטע 'שרשראות בקשה קריטיות' שבהמשך מוצגים המשאבים שנטענים עם עדיפות גבוהה. כדי לשפר את מהירות טעינת הדף, מומלץ לקצר את השרשראות, להקטין את גודל ההורדה של משאבים או לעכב את ההורדה של משאבים לא נחוצים. [מידע נוסף](https://web.dev/critical-request-chains)."}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{נמצאה שרשרת אחת}two{נמצאו # שרשראות}many{נמצאו # שרשראות}other{נמצאו # שרשראות}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "יש לצמצם את העומק של בקשות קריטיות"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "הוצאה משימוש/אזהרה"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "שורה"}, "lighthouse-core/audits/deprecations.js | description": {"message": "רכיבי API שהוצאו משימוש יוסרו בסופו של דבר מהדפדפן. [מידע נוסף](https://web.dev/deprecations)."}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{נמצאה אזהרה אחת}two{נמצאו # אזהרות}many{נמצאו # אזהרות}other{נמצאו # אזהרות}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "הדף מכיל רכיבי API שהוצאו משימוש"}, "lighthouse-core/audits/deprecations.js | title": {"message": "אין רכיבי API שהוצאו משימוש"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "Application Cache הוצא משימוש. [מידע נוסף](https://web.dev/appcache-manifest)."}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "בשימוש: \"{AppCacheManifest}\""}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "נעשה שימוש ב-Application Cache"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "לא נעשה שימוש ב-Application Cache"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "ציון של doctype מונע מהדפד<PERSON>ן לעבור למצב תאימות (quirks mode). [מידע נוסף](https://web.dev/doctype)."}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "שם של Doctype חייב להיות המחרוזת `html` עם אותיות קטנות"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "המסמך חייב להכיל doctype"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "publicId אמור להיות מחרוזת ריקה"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "systemId אמור להיות מחרוזת ריקה"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "ה-doctype של הדף אינו מוגדר ל-HTML, ולכן הופעל מצב תאימות (quirks mode)"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "ה-doctype של הדף מוגדר ל-HTML"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "נתון סטטיסטי"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "ערך"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "מהנדסי דפדפנים ממליצים לכלול פחות מכ-1,500 רכיבי DOM בדפים. התצורה המועדפת היא עץ בעומק של פחות מ-32 רכיבים, ופחות מ-60 רכיבי צאצא לכל רכיב הורה. DOM גדול עשוי להגדיל את צריכת משאבי הזיכרון, להאריך את הזמן הדרוש ל[חישובי סגנונות](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) ולהוביל ל[זרימה חוזרת של פריסות](https://developers.google.com/speed/articles/reflow) שגוזלת משאבים יקרים. [מידע נוסף](https://web.dev/dom-size)."}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{רכ<PERSON><PERSON> אחד}two{# רכיבים}many{# רכיבים}other{# רכיבים}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "יש להימנע מ-<PERSON><PERSON> גדול מדי"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "עומ<PERSON> מרבי"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "סך רכיבי DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "מקסימום רכיבי צאצא"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "נמנע מ-<PERSON><PERSON> גדול מדי"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "יעד"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "כדי לשפר את הביצועים ולמנוע פגיעויות המסכנות את האבטחה, יש להוסיף `rel=\"noopener\"` או `rel=\"noreferrer\"` לקישורים חיצוניים. [מידע נוסף](https://web.dev/external-anchors-use-rel-noopener)."}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "הדף מכיל קישורים לא בטוחים ליעדים ממקורות שונים"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "כל הקישורים ליעדים ממקורות שונים בטוחים לשימוש"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "לא ניתן לקבוע את יעד העוגן ({anchorHTML}). אם target=_blank לא משמש כהיפר-קישור, כדאי לשקול להסיר אותו."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "אתרים שמבקשים לקבל גישה למיקום של המשתמשים, בלי לציין הקשר, מעוררים אצל המשתמשים תחושת בלבול או חשדנות. במקום זאת, כדאי לשקול לקשר את הבקשות לפעולה של המשתמשים. [מידע נוסף](https://web.dev/geolocation-on-start)."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "הדף מבקש הרשאות למיקום גאוגרפי במהלך טעינת הדף"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "הדף לא מבקש הרשאות למיקום גאוגרפי במהלך טעינת הדף"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "גרסה"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "זו ביקורת שמטרתה לזהות את כל ספריות ה-JavaScript החזיתיות בדף. [מידע נוסף](https://web.dev/js-libraries)."}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "ספריות JavaScript שזוהו"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "אם החיבור איטי, סקריפטים חיצוניים המושתלים באופן דינמי דרך `document.write()` יכולים לעכב את טעינת הדף בעשרות שניות. [מידע נוסף](https://web.dev/no-document-write)."}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "הדף כולל שימוש ב-`document.write()`"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "לא נעשה שימוש ב-`document.write()`"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "רמת החומרה הגבוהה ביותר"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "גרסת הספרייה"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "מספר הפגיעויות"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "סקריפטים של צד שלישי עשויים להכיל פגיעויות ידועות באבטחה, שתוקפים יכולים לזהות ולנצל בקלות. [מידע נוסף](https://web.dev/no-vulnerable-libraries)."}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{זוהתה פגיעוּת אחת}two{זוהו # פגיעויות}many{זוהו # פגיעויות}other{זוהו # פגיעויות}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "הדף מכיל ספריות JavaScript חזיתיות בעלות פגיעויות ידועות באבטחה"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "גבוהה"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "נמוכה"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "בינונית"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "הדף לא מכיל ספריות JavaScript חזיתיות בעלות פגיעויות ידועות באבטחה"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "אתרים שמבקשים לשלוח התראות ללא הקשר מעוררים תחושת בלבול או חשדנות בקרב המשתמשים. במקום זאת, כדאי לשקול לקשר את הבקשות למחוות של המשתמשים. [מידע נוסף](https://web.dev/notification-on-start)."}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "הדף מבקש הרשאה להודעות במהלך טעינת הדף"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "הדף לא מבקש הרשאה להתראות במהלך טעינת הדף"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "רכיבים שנכשלו בבדיקה"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "מניעה של הדבקת סיסמאות פוגעת ביכולת לקיים מדיניות אבטחה טובה. [מידע נוסף](https://web.dev/password-inputs-can-be-pasted-into)."}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "בדף הזה משתמשים לא יכולים להדביק תוכן מועתק לתוך שדות של סיסמאות"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "בדף הזה משתמשים יכולים להדביק תוכן מועתק לתוך שדות של סיסמאות"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "פרוטוקול"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "ל-HTTP/2 יש הרבה יתרונות על-פני HTTP/1.1, ביניהם כותרות בינאריות, ריבוב והקצאות שרת מוקדמות (Server Push). [מידע נוסף](https://web.dev/uses-http2)."}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{בקשה אחת לא מולאה דרך HTTP/2}two{# בקשות לא מולאו דרך HTTP/2}many{# בקשות לא מולאו דרך HTTP/2}other{# בקשות לא מולאו דרך HTTP/2}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "לא נעשה שימוש ב-HTTP/2 עבור כל משאבי הדף"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "נעשה שימוש ב-HTTP/2 עבור משאבי הדף"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "כדי לשפר את ביצועי הגלילה של הדף, מומלץ לשקול להוסיף סימון `passive` למעבדים של אירועי נגיעה וגלילה עם גלגל העכבר. [מידע נוסף](https://web.dev/uses-passive-event-listeners)."}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "לא נעשה שימוש ברכיבי listener פסיביים לשיפור ביצועי הגלילה"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "נעשה שימוש ברכיבי listener פסיביים לשיפור ביצועי הגלילה"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "תיאור"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "שגיאות שנרשמו במסוף מצביעות על בעיות לא פתורות. הן עשויות להופיע בעקבות כשל בבקשות ברשת או בעיות אחרות בדפדפן. [מידע נוסף](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "נרשמו שגיאות דפדפן במסוף"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "לא נרשמו במסוף שגיאות דפדפן"}, "lighthouse-core/audits/font-display.js | description": {"message": "שימוש בתכונת תצוגת הגופן של CSS מבטיח שהטקסט מוצג למשתמש בזמן טעינה של גופני webfont. [מידע נוסף](https://web.dev/font-display)."}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "יש לוודא שטקסט ממשיך להופיע במהלך טעינת webfont"}, "lighthouse-core/audits/font-display.js | title": {"message": "כל הטקסט ממשיך להופיע במהלך טעינות של webfont"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "מערכת Lighthouse לא הצליחה לבדוק באופן אוטומטי את ערך תצוגת הגופן של כתובת ה-URL הבאה: {fontURL}."}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "יח<PERSON> גובה-רוחב (בפועל)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "יח<PERSON> גובה-רוחב (בתצוג<PERSON>)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "מידות התצוגה של התמונה צריכות להתאים ליחס הגובה-רוחב הטבעי. [מידע נוסף](https://web.dev/image-aspect-ratio)."}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "יש תמונות עם יחס גובה-רוחב שגוי"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "התמונות מוצגות ביחס גובה-רוח<PERSON> נכון"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "נתוני הגודל של תמונה לא חוקיים: {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "הדפדפנים יכולים לבקש ממשתמשים באופן יזום להוסיף את האפליקציה שלך אל מסך הבית שלהם, פעולה שמגדילה את הסיכוי להשגת מעורבות גבוהה יותר. [מידע נוסף](https://web.dev/installable-manifest)."}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "המניפסט של אפליקציית האינטרנט לא עומד בדרישות יכולת ההתקנה"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "המניפסט של אפליקציית האינטרנט עומד בדרישות יכולת ההתקנה"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "כתובת URL של בקשה לא מאובטחת"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "יש להגן באמצעות פרוטוקול HTTPS על כל האתרים, אפילו אם הם לא מכילים נתונים רגישים. פרוטוקול HTTPS מונע מפורצים לעשות שימוש לרעה בתקשורת המתקיימת בין האפליקציה למשתמשים או לצותת לה. השימוש בפרוטוקול הזה הוא דרישה מוקדמת של HTTP/2 ושל רכיבי ה-API של רבות מפלטפורמות האינטרנט החדשות. [מידע נוסף](https://web.dev/is-on-https)."}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{נמצאה בקשה לא מאובטחת אחת}two{נמצאו # בקשות לא מאובטחות}many{נמצאו # בקשות לא מאובטחות}other{נמצאו # בקשות לא מאובטחות}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "לא נעשה שימוש ב-HTTPS"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "שימוש ב-HTTPS"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "כשהדף נטען במהירות ברשת סלולרית, חוויית המשתמש איכותית. [מידע נוסף](https://web.dev/load-fast-enough-for-pwa)."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "פעילות מלאה תוך {timeInMs, number, seconds} שנ'"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "הזמן עד לפעילות מלאה בהדמיית רשת סלולרית הוא {timeInMs, number, seconds} שניות"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "הדף שלך נטען לאט מדי ולא מגיע לפעילות מלאה תוך 10 שניות. כדי ללמוד כיצד לבצע שיפורים, מומלץ לעיין בהזדמנויות ובניתוחים שמופיעים בקטע \"ביצועים\"."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "טעינת הדף לא מהירה מספיק ברשתות סלולריות"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "טעינת הדף ברשתות סלולריות מהירה מספיק"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "קטגוריה"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "כדאי לשקול את האפשרות לקצר את הזמן הדרוש לניתוח, הידור וביצוע של JS. לשם כך, כדאי להשתמש במטענים ייעודיים (payloads) קטנים יותר של JS. [מידע נוסף](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "צריך לצמצם את העבודה על התהליכון הראשי"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "מצמצם את העבודה על התהליכון הראשי"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "כדי להגדיל את היקף החשיפה לכמה שיותר משתמשים, האתרים צריכים לעבוד בכל הדפדפנים המובילים. [מידע נוסף](https://web.dev/pwa-cross-browser)."}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "האתר עובד בדפדפנים שונים"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "יש לוודא שניתן לבצע קישור עמוק לדפים נפרדים דרך כתובת URL ושכתובות ה-URL ייחודיות, כדי שניתן יהיה לשתף אותן ברשתות חברתיות. [מידע נוסף](https://web.dev/pwa-each-page-has-url)."}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "לכל דף יש כתובת URL"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "כשמקישים באפליקציה, התחושה של המעברים צריכה להיות מהירה, גם ברשת איטית. החוויה הזו היא אחד הגורמים החשובים לתפיסת הביצועים בעיני משתמשים. [מידע נוסף](https://web.dev/pwa-page-transitions)."}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "מעברי הדפים לא מרגישים חסומים ברשת"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "זמן אחזור מוערך של קלט הוא אומדן של משך הזמן הנדרש לאפליקציה כדי להגיב לקלט של משתמש. הערך מצוין באלפיות שנייה ומתייחס ל-5 השניות העמוסות ביותר בטעינת הדף. אם זמן האחזור ארוך מ-50 אלפיות שנייה, ייתכן שהמשתמשים יבחינו בעיכוב בפעילות האפליקציה. [מידע נוסף](https://web.dev/estimated-input-latency)."}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "אומ<PERSON><PERSON> זמן האחזור של קלט"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "המדד 'הצגת תוכן ראשוני (FCP)' מציין את הזמן שבו הטקסט או התמונה הראשונים מוצגים. [מידע נוסף](https://web.dev/first-contentful-paint)."}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "הצגת התוכ<PERSON> הראשוני"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "הערך 'מצב ראשון של חוסר פעילות ב-CPU' מציין את הפעם הראשונה שבה התהליכון הראשי של הדף פנוי מספיק בשביל להגיב לקלט.  [מידע נוסף](https://web.dev/first-cpu-idle)."}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "מצב ראשון של חוסר פעילות ב-CPU"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "הערך 'הצגת התוכן העיקרי (FMP)' מציין מתי מוצג התוכן העיקרי של הדף. [מידע נוסף](https://web.dev/first-meaningful-paint)."}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "הצגת התו<PERSON><PERSON> העיקרי"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "הזמן עד לפעילות מלאה הוא משך הזמן שחולף עד שהדף מאפשר פעילות מלאה. [מידע נוסף](https://web.dev/interactive)."}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "<PERSON><PERSON><PERSON> עד לאינטראקטיביות"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "ההשהיה הפוטנציאלית המרבית שהמשתמשים יכולים לחוות לאחר קלט ראשוני היא משך הזמן (באלפיות שנייה) של המשימה הארוכה ביותר. [מידע נוסף](https://developers.google.com/web/updates/2018/05/first-input-delay)."}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "השהיה פוטנציאלית מרבית לאחר קלט ראשוני"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "מדד המהירות (Speed Index) מראה באיזו מהירות מוצג התוכן בדף [מידע נוסף](https://web.dev/speed-index)."}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "מדד מהירות (Speed Index)"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "משך הזמן המצט<PERSON>ר של כל פרקי הזמן מרגע הצגת התוכן הראשוני (FCP) ועד לפעילות מלאה, במקרים שבהם משך המשימה חורג מ-50 אלפיות שנייה. הערך מבוטא באלפיות שנייה."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "<PERSON><PERSON><PERSON> הח<PERSON><PERSON><PERSON><PERSON> הכולל"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "לזמני הלוך ושוב (RTT) ברשת יש השפעה גדולה על הביצועים. אם נדרש RTT ארוך בתקשורת עם מקור, זה סימן לכך שאפשר לשפר את הביצועים בעזרת שרתים שממוקמים קרוב יותר אל המשתמש. [מידע נוסף](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "lighthouse-core/audits/network-rtt.js | title": {"message": "זמני הלוך ושוב ברשת"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "זמני האחזור של שרתים יכולים להשפיע על ביצועי האינטרנט. אם נדרש זמן אחזור ארוך בתקשורת עם המקור, זה סימן לכך שהשרת עמוס או שביצועי הקצה העורפי שלו נמוכים. [מידע נוסף](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "ז<PERSON><PERSON>י אחז<PERSON><PERSON> בקצה עורפי של שרת"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "קובץ שירות (service worker) מא<PERSON><PERSON>ר לאפליקציית האינטרנט שלך להיות אמינה יותר בתנאי רשת לא צפויים. [מידע נוסף](https://web.dev/offline-start-url)."}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url` לא מגיב בסטטוס 200 כשהוא במצב לא מקוון"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url` מג<PERSON>ב בסטטוס 200 כשהוא במצב לא מקוון"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "מערכת Lighthouse לא הצליחה לקרוא את `start_url` מהמניפסט. כתוצאה מכך, התבצעה הנחה ש-`start_url` היא כתובת ה-URL של המסמך. הודעת שגיאה: '{manifestWarning}'."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "גובה החריגה מהתקציב"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "הכמות והגודל של בקשות ברשת צריכים להיות מתחת ליעדים שהוגדרו בתקציב הביצועים הרלוונטי. [מידע נוסף](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{בקשה אחת}two{# בקשות}many{# בקשות}other{# בקשות}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "תקציב ביצועים"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "אם כבר הגדרת HTTPS, יש לוודא שכל תנועת ה-HTTP מופנית אוטומטית ל-HTTPS כדי להפעיל תכונות רשת מאובטחות לכל המשתמשים שלך. [מידע נוסף](https://web.dev/redirects-http)."}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "לא מפנה תנועת HTTP באופן אוטומטי אל HTTPS"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "מפנה תנועת HTTP באופן אוטומטי אל HTTPS"}, "lighthouse-core/audits/redirects.js | description": {"message": "הפניות אוטומטיות מעכבות את טעינת הדף. [מידע נוסף](https://web.dev/redirects)."}, "lighthouse-core/audits/redirects.js | title": {"message": "יש להימנע מהפניות אוטומטיות מרובות"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "כדי להגדיר תקציבים עבור הכמות והגודל של משאבי הדף, יש להוסיף קובץ budget.json. [מידע נוסף](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{בקשה אחת • ‎{byteCount, number, bytes} KB‏}two{# בקשות • ‎KB {byteCount, number, bytes}}many{# בקשות • ‎KB {byteCount, number, bytes}}other{# בקשות • ‎KB {byteCount, number, bytes}}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "יש לצמצם ככל האפשר את מספר הבקשות ואת גודל ההעברות"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "קישורים קנוניים מציעים את כתובת ה-URL שיש להציג בתוצאות החיפוש. [מידע נוסף](https://web.dev/canonical)."}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "התנגשויות בין כתובות URL מרובות ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "הכתובת מפנה לדומיין אחר ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "כתובת אתר לא חוקית ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "הכתובת מפנה למיקום `hreflang` אחר ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "כתובת URL יחסית ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "מצביע אל כתובת ה-U<PERSON> הבסיסית של הדומיין (דף הבית), במקום אל דף תוכן מתאים"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "למסמך אין `rel=canonical` חוקי"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "למסמך יש `rel=canonical` חוקי"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "גו<PERSON>ן בגודל של פחות מ-12 פיקסלים הוא קטן מדי לקריאה: מבקרים עם מכשיר נייד ייאלצו לעשות תנועת צביטה של התקרבות לתצוגה על מנת לקרוא את הטקסט. ההמלצה היא שיותר מ-60% מהטקסט יהיה בגודל של 12 פיקסלים לפחות. [מידע נוסף](https://web.dev/font-size)."}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "טקסט קריא: {decimalProportion, number, extendedPercent}"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "הטקסט לא קריא כי לא בוצעה אופטימיזציית מטא תג של אזור התצוגה בשביל מסכים של ניידים."}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "הגודל של {decimalProportion, number, extendedPercent} מהטקסט קטן מדי (מבוסס על דגימה של {decimalProportionVisited, number, extendedPercent})."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "מידות הגופן במסך מקשות על הקריאה"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "במסמך נעשה שימוש בגופן בגודל קריא"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "קישורי hreflang עוזרים למנועי חיפוש להבין איזו גרסה של הדף הם צריכים להציג בתוצאות החיפוש בשביל שפה מסוימת או אזור מסוים. [מידע נוסף](https://web.dev/hreflang)."}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "למסמך אין `hre<PERSON><PERSON>` חוקי"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "למסמך יש `hreflang` חוקי"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "כשלדפים יש קוד מצב HTTP לא תקין, עשויות להיות שגיאות בהוספה שלהם לאינדקס. [מידע נוסף](https://web.dev/http-status-code)."}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "לדף יש קוד מצב HTTP המצביע על בעיה"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "קוד מצב ה-HTTP של הדף הוא 'הצלחה'"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "מנועי חיפוש לא יכולים לכלול את הדפים בתוצאות החיפוש אם אין להם הרשאה לסרוק אותם. [מידע נוסף](https://web.dev/is-crawable)."}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "הוספת הדף לאינדקס חסומה"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "הוספת הדף לאינדקס אינה חסומה"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "טקסט שמתאר את הקישורים עוזר למנועי חיפוש להבין במה עוסק התוכן. [מידע נוסף](https://web.dev/link-text)."}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{נמצא קישור אחד}two{נמצאו # קישורים}many{נמצאו # קישורים}other{נמצאו # קישורים}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "אין לקישורים טקסט תיאורי"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "לקישורים יש טקסט תיאורי"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "כדי לאמת את תקינות הנתונים המובְנים צריך להפעיל את [הכלי לבדיקת הנתונים המובְנים](https://search.google.com/structured-data/testing-tool/) ואת [הלינטר (Linter) לנתונים מובנְים](http://linter.structured-data.org/). [מידע נוסף](https://web.dev/structured-data)."}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "הנתונים המובְנים חוקיים"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "תיאורי מטא יכולים להופיע בתוצאות החיפוש כדי לספק סיכום תמציתי של תוכן הדף. [מידע נוסף](https://web.dev/meta-description)."}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "טקסט התיאור ריק."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "אין למסמך מטא תיאור"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "יש למסמך מטא תיאור"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "מנועי חיפוש לא יכולים להוסיף לאינדקס תוכן של פלאגין, ומכשירים רבים מגבילים יישומי פלאגין או לא תומכים בהם. [מידע נוסף](https://web.dev/plugins)."}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "במסמך נעשה שימוש ביישומי פלאגין"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "אין במסמך שימוש ביישומי פלאגין"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "אם קובץ robots.txt אינו תקין, ייתכ<PERSON> שסורקים לא יוכלו להבין איך ברצונך שהאתר ייסרק או ייתווסף לאינדקס. [מידע נוסף](https://web.dev/robots-txt)."}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "הבקשה לקובץ robots.txt החזירה מצב HTTP‏: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{נמצאה שגיאה אחת}two{נמצאו # שגיאות}many{נמצאו # שגיאות}other{נמצאו # שגיאות}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "מערכת Lighthouse לא הצליחה להוריד קובץ robots.txt"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt אינו <PERSON>ו<PERSON>י"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "הקובץ robots.txt חוקי"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "כדי שיהיה קל להקיש על רכיבים אינטראקטיביים, כמו לחצנים וקישורים, בלי לגעת ברכיבים אחרים, הם צריכים להיות גדולים מספיק (48x48 פיקסלים) ועם ריווח גדול מספיק סביבם. [מידע נוסף](https://web.dev/tap-targets)."}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} מבין יעדי ההקשה הם בגודל תקין"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "רכיבי ההקשה קטנים מדי כי לא בוצעה אופטימיזציית מטא תגים של אזור התצוגה למסכים של ניידים"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "הגודל של רכיבי ההקשה הוגדר בצורה לא תקינה"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "יעד חופף"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "הרכיבים להקשה הוגדרו בגודל המתאים"}, "lighthouse-core/audits/service-worker.js | description": {"message": "קובץ השירות (service worker) הו<PERSON> הטכנולוגיה שמאפשרת לאפליקציה שלך להשתמש במספר תכונות Progressive Web App, כמו 'מצב לא מקוון', 'הוספה לדף הבית' ו'התראות'. [מידע נוסף](https://web.dev/service-worker)."}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "דף זה נשלט על ידי קובץ שירות (service worker), אך לא נמצא `start_url` כי לא ניתן היה לנתח את המניפסט בתור JSON תקף"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "דף זה נשלט על ידי קובץ שירות (service worker), אך ה-`start_url` ({startUrl}) לא נמצא בטווח ({scopeUrl}) של קובץ השירות"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "דף זה נשלט על ידי קובץ שירות (service worker), אך לא נמצא `start_url` כי לא אוחזר מניפסט."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "למקור זה יש לפחות קובץ שירות (service worker) אחד, אך הדף ({pageUrl}) לא נמצא בטווח."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "לא רושם קובץ שירות (service worker) ששולט בדף וב-`start_url`"}, "lighthouse-core/audits/service-worker.js | title": {"message": "רושם קובץ שירות (service worker) ששולט בדף וב-`start_url`"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "מסך פתיחה מעוצב מבטיח חוויה באיכות גבוהה כשמשתמשים מפעילים את האפליקציה שלך ממסכי הבית שלהם. [מידע נוסף](https://web.dev/splash-screen)."}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "לא מוגדר עבור מסך פתיחה בהתאמה אישית"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "מוגדר עבור מסך פתיחה בהתאמה אישית"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "ניתן לעצב את סרגל הכתובות של הדפדפן כך שיתאים לאתר שלך. [מידע נוסף](https://web.dev/themed-omnibox)."}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "לא מגדיר צבע עיצוב עבור סרגל הכתובות."}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "מגדיר צבע עיצוב עבור סרגל הכתובות."}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "משך החסימה של התהליכון הראשי"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "צ<PERSON> שלישי"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "קוד של צד שלישי עשוי להשפיע בצורה משמעותית על ביצועי הטעינה. מומלץ להגביל את הכמות של ספקי צד שלישי שאינם הכרחיים ולהשתדל לטעון קודים של צד שלישי רק אחרי שמסתיימת הטעינה של הדף. [מידע נוסף](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "קוד של צד שלישי חסם את התהליכון הראשי למשך {timeInMs, number, milliseconds} אלפיות השנייה"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "עליך להפחית את השפעת הקוד של צד שלישי"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "שימוש של צד שלישי"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "'ז<PERSON>ן עד בייט ראשון' (Time To First Byte) הוא פרק הזמן שחולף עד שהשרת שולח תגובה. [מידע נוסף](https://web.dev/time-to-first-byte)."}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "טעינת מסמך השורש ארכה {timeInMs, number, milliseconds} אלפיות שנייה"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "יש לקצר את זמני התגובה של השרת (TTFB)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "ז<PERSON><PERSON>י התגובה של השרת ארוכים (TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "משך זמן"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "שעת התחלה"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "סוג"}, "lighthouse-core/audits/user-timings.js | description": {"message": "כדי למדוד את ביצועי האפליקציה בפועל במהלך חוויות משתמש חשובות, כדאי לשקול את האפשרות להוסיף לאפליקציה את User Timing API. [מידע נוסף](https://web.dev/user-timings)."}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{תזמון משתמש אחד}two{# תזמוני משתמש}many{# תזמוני משתמש}other{# תזמוני משתמש}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "סימונים ומדידות של User Timing"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "נמצ<PERSON> רכיב <link> לקישור מר<PERSON>ש בשביל \"{securityOrig<PERSON>}\", אבל הדפד<PERSON>ן לא השתמש בו. יש לוודא שנעשה שימוש תקין במאפיין `crossorigin`."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "כדאי לשקול להוסיף את ההינטים של המשאבים `preconnect` או `dns-prefetch` כדי ליצור מראש קישורים אל מקורות חשובים של צד שלישי. [מידע נוסף](https://web.dev/uses-rel-preconnect)."}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "יש להתחבר מראש למקורות נדרשים"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "נמצא ערך <link> של טעינה מראש בשביל \"{preloadURL}\", אבל הדפד<PERSON>ן לא השתמש בו. יש לוודא שנעשה שימוש תקין במאפיין `crossorigin`."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "כדאי לשקול את האפשרות להשתמש ב-`<link rel=preload>` כדי לקבוע את סדר העדיפויות של אחזור משאבים שנדרשים בשלב מאוחר יותר של טעינת הדף. [מידע נוסף](https://web.dev/uses-rel-preload)."}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "יש לטעון מראש בקשות עיקריות"}, "lighthouse-core/audits/viewport.js | description": {"message": "כדי לבצע אופטימיזציה של האפליקציה למסכים של ניידים, עליך להוסיף את התג `<meta name=\"viewport\">`. [מידע נוסף](https://web.dev/viewport)."}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "לא נמצא תג `<meta name=\"viewport\">`"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "אין תג `<meta name=\"viewport\">` עם `width` או `initial-scale`"}, "lighthouse-core/audits/viewport.js | title": {"message": "יש תג `<meta name=\"viewport\">` עם `width` או `initial-scale`"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "האפליקציה שלך צריכה להציג תוכן כלשהו כשה-JavaScript מושבת, גם אם זו רק אזהרה למשתמש שנדרש JavaScript כדי להשתמש באפליקציה. [מידע נוסף](https://web.dev/without-javascript)."}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "אם הסקריפטים של גוף הדף לא זמינים, הוא צריך לעבד תוכן כלשהו."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "לא מספק תוכן חלופי כש-JavaScript לא זמין"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "מכיל תו<PERSON>ן כלשהו כש-JavaScript לא זמין"}, "lighthouse-core/audits/works-offline.js | description": {"message": "אם בונים Progressive Web App, מומלץ לשקול להשתמש בקובץ שירות (service worker) כדי שהאפליקציה תוכל לעבוד במצב לא מקוון. [מידע נוסף](https://web.dev/works-offline)."}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "דף זה לא מגיב בסטטוס 200 כשהוא במצב לא מקוון"}, "lighthouse-core/audits/works-offline.js | title": {"message": "דף זה מגיב בסטטוס 200 כשהוא במצב לא מקוון"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "ייתכן שהדף לא נטען באופן לא מקוון בגלל שכתובת ה-URL לבדיקה ({requested}) הופנתה אל \"{final}\". מומלץ לבדוק את כתובת ה-URL השנייה באופן ישיר."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "אלו הזדמנויות לשיפור השימוש ב-ARIA באפליקציה, והן יכולות לשפר את החוויה של משתמשים שנעזרים בטכנולוגיות לנגישות, כמו קוראי מסך."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "אלה הזדמנויות לספק תוכן חלופי לווידאו ואודיו. הפעולות האלה יכולות לשפר את החוויה של משתמשים עם לקויות שמיעה או ראייה."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "וידאו ואודיו"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "הפריטים האלה מדגישים שיטות מומלצות נפוצות בשביל נגישות."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "שיטות מומלצות"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "הבדיקות האלה מדגישות הזדמנויות [לשפר את הנגישות של אפליקציית האינטרנט](https://developers.google.com/web/fundamentals/accessibility). אפשר לזהות באופן אוטומטי רק חלק מבעיות הנגישות, ולכן מומלץ לבצע גם בדיקות ידניות."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "הפריטים האלה בודקים תחומים שלא ניתן לבדוק באמצעות כלי בדיקה אוטומטיים. מידע נוסף זמין במדריך שלנו שבו מוסבר [איך לערוך בדיקת נגישות](https://developers.google.com/web/fundamentals/accessibility/how-to-review)."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "נגישות"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "אלו הזדמנויות לשיפורים שיאפשרו לקרוא את התוכן בצורה קלה יותר."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "ניגודיות"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "אלו הזדמנו<PERSON><PERSON><PERSON> לשיפור של פענוח התוכן על-ידי משתמשים בלוקאלים שונים."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "התאמה לשוק המקומי והבינלאומי"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "אלו הזדמנויות לשיפור הסמנטיקה של פקדים באפליקציה. הן יכולות לשפר את החוויה של משתמשים שנעזרים בטכנולוגיות לנגישות, כמו קורא מסך."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "שמות ותוויות"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "אלו הזדמנויות לשיפור הניווט באפליקציה באמצעות מקלדת."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "ניווט"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "אלו הזדמנויות לשיפור החוויה של קריאת נתונים בטבלאות או רשימות באמצעות טכנולוגיה לנגישות, כמו קורא מסך."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "טבלאות ורשימות"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "שיטות מומלצות"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "באמצעות תקציבי ביצועים ניתן להגדיר יעדי ביצועים עבור האתר."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "תקציבים"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "מידע נוסף לגבי ביצועי האפליקציה. למספרים האלה אין [השפעה ישירה](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) על ציון הביצועים."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "ניתוחים"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "היבט הביצועים הקריטי ביותר הוא מהירות העיבוד של פיקסלים במסך. ערכי מפתח: הצגת התוכן הראשוני, הצגת התוכן העיקרי"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "שיפורים בעיבוד ראשון"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "ההצעות האלה יכולות לעזור לך להאיץ את טעינת הדף. אין להן [השפעה ישירה](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) על ציון הביצועים."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "הזדמנויות"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "ערכים"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "צריך לשפר את חוויית הטעינה הכללית, כך שהדף יגיב ויהיה מוכן לשימוש במהירות האפשרית. ערכי מפתח: זמן עד לאינטראקטיביות (Time to Interactive), מדד מהירות (Speed Index)"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "סך השיפורים"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "ביצועים"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "בדיקות אלה נותנות תוקף להיבטים של Progressive Web App. [מידע נוסף](https://developers.google.com/web/progressive-web-apps/checklist)."}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "בדיקות אלה נדרשות עבור שורת הבסיס [רשימת משימות של PWA](https://developers.google.com/web/progressive-web-apps/checklist), אך הן לא נבדקות באופן אוטומטי על ידי Lighthouse. הן לא משפיעות על הניקוד שלך, אך חשוב לאמת אותן באופן ידני."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "Progressive Web App"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> ואמין"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "ניתן להתקנה"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "מותאם ל-PWA"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "הבדיקות האלה עוזרות לוודא שהתוכן בדף עבר אופטימיזציה לשיפור הדירוג בתוצאות של מנועי חיפוש. יש גורמים נוספים שעשויים להשפיע על הדירוג בחיפוש, אבל מערכת Lighthouse לא בודקת אותם. [מידע נוסף](https://support.google.com/webmasters/answer/35769)."}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "אפשר להפעיל באתר את המאמתים הנוספים האלה כדי לבדוק עוד שיטות מומלצות של אופטימיזציה למנועי חיפוש."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "אופטימיז<PERSON><PERSON>ה למנועי חיפוש"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "יש לכתוב את קוד ה-HTML באופן שיאפשר לסורקים להבין את תוכן האפליקציה בצורה טובה יותר."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "שיטות מומלצות לגבי תוכן"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "כדי שהא<PERSON>ליקציה תופיע בתוצאות החיפוש, סורקים צריכים לקבל גישה אליה."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "סריקה והוספה לאינדקס"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "הדפים צריכים להתאים לניידים, כדי שמשתמשים לא יצטרכו לעשות תנועת צביטה או לשנות את המרחק מהתצוגה כדי לקרוא את דפי התוכן. [מידע נוסף](https://developers.google.com/search/mobile-sites/)."}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "התאמה לניידים"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "אורך חיים (TTL) של מטמון"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "מיקום"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "שם"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "בקשות"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "סוג המשאב"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "גודל"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "משך הזמן שנדרש"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "גודל ההעברה"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "כתובת אתר"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "פו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> חי<PERSON><PERSON>ון"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "פו<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> חי<PERSON><PERSON>ון"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "צמצום של ‎{wastedBytes, number, bytes} KB"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "פוטנציאל לקיצור זמן הטעינה ב-{wastedMs, number, milliseconds} אלפיות שנייה"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "מסמך"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "גו<PERSON>ן"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "תמונה"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "מדיה"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} אלפיות שנייה"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "סקריפט"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} שנ'"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "גליון סגנונות"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "צ<PERSON> שלישי"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "סה\"כ"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "משהו השת<PERSON><PERSON> בתיעוד המעקב אחרי טעינת הדף. יש להריץ שוב את Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "חלף הזמן הקצוב לתפוגה בהמתנה לחיבור הראשוני של פרוטוקול Debugger."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "לא נאספו צילומי מסך ב-Chrome במהלך טעינת הדף. כדאי לוודא תחילה שיש תוכן גלוי בדף, ורק אז להריץ מחדש את Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "שרתי DNS לא הצליחו לפענח את הדומיין שצוין."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "בפעולת האיסוף של המשאב הנדרש {artifactName} הייתה שגיאה: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "קרתה שגיאה פנימית של Chrome. יש להפעיל מחדש את Chrome ולנסות להריץ שוב את Lighthouse."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "פעולת האיסוף של המשאב הנדרש {artifactName} לא בוצעה."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "מערכת Lighthouse לא הצליחה לטעון באופן מהימן את הדף שביקשת. יש לוודא שהבדיקה מתבצעת בכתובת ה-URL הנכונה ושהשרת מגיב באופן תקין לכל הבקשות."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "מערכת Lighthouse לא הצליחה לטעון באופן מהימן את כתובת ה-URL שביקשת, כי הדף הפסיק להגיב."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "לכתובת ה-<PERSON><PERSON> שסיפקת אין אישור אבטחה חוקי. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "דפד<PERSON>ן Chrome מנע טעינה של דף והציג מסך מעברון במקומו. יש לוודא שהבדיקה מתבצעת בכתובת ה-URL הנכונה ושהשרת מגיב באופן תקין לכל הבקשות."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "מערכת Lighthouse לא הצליחה לטעון באופן מהימן את הדף שביקשת. יש לוודא שהבדיקה מתבצעת בכתובת ה-URL הנכונה ושהשרת מגיב באופן תקין לכל הבקשות. (פרטים: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "מערכת Lighthouse לא הצליחה לטעון באופן מהימן את הדף שביקשת. יש לוודא שהבדיקה מתבצעת בכתובת ה-URL הנכונה ושהשרת מגיב באופן תקין לכל הבקשות. (קוד סטטוס: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "טעינת הדף ארכה זמן רב מדי. מומלץ ליישם את ההמלצות שבדוח כדי לקצר את זמן הטעינה של הדף, ואז לנסות להריץ שוב את Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "משך ההמתנה לפרוטוקול DevTools חרג מהזמן המוקצב. (שיטה: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "אחז<PERSON>ר תוכן של משאבים חרג מהזמן המוקצב"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "נראה שכתובת ה-<PERSON><PERSON> שסיפקת אינה חוקית."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "הצגת בדיקות"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "ניווט התחלתי"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "ז<PERSON><PERSON> אחז<PERSON><PERSON> מקסימלי של נתיב קריטי:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "שגיאה!"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "שגיאה בדוח: אין מידע על הבדיקה"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "נתוני בדיקה"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "ניתוח [Lighthouse](https://developers.google.com/web/tools/lighthouse/) של הדף הנוכחי באמולציה של רשת סלולרית. הערכים מהווים אומדן והם עשויים להשתנות."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "פריטים נוספים שיש לבדוק באופן ידני"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "לא רלוונטי"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "הזדמנות"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "או<PERSON><PERSON><PERSON> חי<PERSON>ון"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "בדיקות עם ציון 'עובר'"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "כיווץ קטע הטקסט"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "הרחבת קטע הטקסט"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "הצגה של משאבי צד שלישי"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "היו בעיות שהשפיעו על ההרצה הזו של Lighthouse:"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "הערכים מהווים אומדן והם עשויים להשתנות. ציון הביצועים [מבוסס רק על המדדים האלה](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "בדיקות שהסתיימו ב<PERSON><PERSON>ון 'עובר', אבל עם אזהרות"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "אזהרות: "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "אפשר להעלות את ה-GIF לשירות שיאפשר להטמיע אותו כסרטון HTML5."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "ניתן להתקין [פלאגין של WordPress לטעינה מדורגת](https://wordpress.org/plugins/search/lazy+load/) שמאפשר לדחות את הטעינה של רכיבים שאינם מופיעים מיד במסך. ניתן גם להשתמש בעיצוב שמספק את האפשרות הזו. אפשרות נוספת היא להשתמש ב[פלאגין של AMP](https://wordpress.org/plugins/amp/)."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "יש כמה יישומי פלאגין של WordPress שיכולים לעזור לך [להטביע נכסים קריטיים](https://wordpress.org/plugins/search/critical+css/) או [לדחות טעינה של משאבים פחות חשובים](https://wordpress.org/plugins/search/defer+css+javascript/). חשוב: ייתכן שהאופטימיזציות המבוצעות על ידי יישומי הפלאגין האלה יגרמו לתקלות בתכונות של העיצוב או יישומי הפלאגין האחרים, ולכן כנראה שיהיה צורך לבצע שינויים בקוד."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "עיצובים, יישו<PERSON><PERSON> פלאגין ומפרטי שרתים משפיעים על זמן התגובה של השרת. אפשר להשתמש בעיצוב שעבר אופטימיזציה, לבחור בקפידה פלאגין לאופטימיזציה ו/או לשדרג את השרת."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "אפשר להציג קטעים ברשימות הפוסטים (למשל, עם תג 'עוד'), לצמצם את מספר הפוסטים המוצגים בדף נתון, לחלק פוסטים ארוכים למספר דפים או להשתמש בפלאגין כדי לטעון תגובות בצורה מדורגת."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "יש כמה [יישומי פלאגין של WordPress](https://wordpress.org/plugins/search/minify+css/) שיכולים להאיץ את האתר על ידי שרשור, הקטנה ודחיסה של סגנונות. ניתן גם להשתמש בתהליך build כדי לבצע את ההקטנה מראש, אם אפשר."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "יש כמה [יישומי פלאגין של WordPress](https://wordpress.org/plugins/search/minify+javascript/) שיכולים להאיץ את האתר על ידי שרשור, הקטנה ודחיסה של סקריפטים. ניתן גם להשתמש בתהליך build כדי לבצע את ההקטנה מראש, אם אפשר."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "כדאי לשקול לצמצם את מספר [יישומי הפלאגין של WordPress](https://wordpress.org/plugins/) שטוענים תוכן CSS בלתי נחוץ בדף, או להשבית אותם. כדי לזהות יישומי פלאגין שמוסיפים תוכן CSS מיותר, אפשר להפעיל [כיסוי קוד](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) ב-Chrome DevTools. לפי כתובת ה-URL של גיליון הסגנונות, ניתן לזהות את העיצוב/הפלאגין שאחראים להוספת התוכן. יישומי הפלאגין הבעייתיים הם אלה שברשימת גיליונות הסגנונות שלהם יש כמות גדולה של כיסוי קוד באדום. פלאגין צריך להכניס גיליון סגנונות לתור רק אם נעשה בו שימוש בדף."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "כדאי לשקול לצמצם את מספר [יישומי הפלאגין של WordPress](https://wordpress.org/plugins/) שטוענים תוכן JavaScript בלתי נחוץ בדף, או להשבית אותם. כדי לזהות יישומי פלאגין שמוסיפים תוכן JS מיותר, אפשר להפעיל [כיסוי קוד](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) ב-Chrome DevTools. לפי כתובת ה-URL של הסקריפט, ניתן לזהות את העיצוב/הפלאגין שאחראים להוספת התוכן. יישומי הפלאגין הבעייתיים הם אלה שברשימת הסקריפטים שלהם יש כמות גדולה של כיסוי קוד באדום. פלאגין צריך להכניס סקריפט לתור רק אם נעשה בו שימוש בדף."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "למידע על [שמירה במטמון הדפדפן ב-WordPress](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "כדאי לשקול להשתמש ב[פלאגין של WordPress לאופטימיזציית תמונות](https://wordpress.org/plugins/search/optimize+images/) שדוחס את התמונות בלי לפגוע באיכות שלהן."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "העלאה של תמונות באופן ישיר דרך [ספריית המדיה](https://codex.wordpress.org/Media_Library_Screen) תאפשר לך לוודא שהתמונות זמינות במידות הדרושות. אחר כך אפשר להוסיף אותן מספריית המדיה או להשתמש בווידג'ט התמונות כדי לוודא שנעשה שימוש בתמונות במידות האופטימליות (כולל תמונות בשביל נקודות מעבר רספונסיביות). יש להימנע משימוש בתמונות ב`Full Size`, אלא אם המימדים מתאימים לשימוש שנעשה בהן. [מידע נוסף](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)."}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "אפשר להפעיל דחיסת טקסט בהגדרות שרת האינטרנט."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "כדאי לשקול להשתמש ב[פלאגין](https://wordpress.org/plugins/search/convert+webp/) או בשירות שימירו את התמונות שהועלו לפורמטים האופטימליים באופן אוטומטי."}}