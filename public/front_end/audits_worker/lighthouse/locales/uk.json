{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "Ключі доступу дають змогу швидко виділяти частину сторінки. Для належної навігації кожний ключ доступу має бути унікальним. [Докладніше](https://web.dev/accesskeys/)."}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Значення `[accesskey]` неунікальні"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "Значення `[accesskey]` унікальні"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> `role` ARIA підтримує конкретний набір атрибутів `aria-*`. Як<PERSON>о вони не збігаються, атрибути `aria-*` стають недійсними. [Докладніше](https://web.dev/aria-allowed-attr/)."}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Атрибути `[aria-*]` не відповідають своїм ролям"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Атрибути `[aria-*]` відповідають своїм ролям"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "Деякі ролі ARIA мають обов'язкові атрибути, що описують стан елемента для програм зчитування з екрана. [Докладніше](https://web.dev/aria-required-attr/)."}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Ролі `[role]` не мають усіх обов'язкових атрибутів `[aria-*]`"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "Ролі `[role]` мають усі потрібні атрибути `[aria-*]`"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "Щоб виконувати потрібні функції спеціальних можливостей, деякі батьківські ролі ARIA повинні містити відповідні дочірні ролі. [Докладніше](https://web.dev/aria-required-children/)."}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "В елементах з ARIA `[role]`, які вимагають дочірні елементи з певним атрибутом `[role]`, немає кількох або всіх дочірніх елементів."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "Елементи з ARIA `[role]`, які вимагають дочірні елементи з певним атрибутом `[role]`, мають усі необхідні дочірні елементи."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "Щоб належно виконувати потрібні функції спеціальних можливостей, відповідні батьківські ролі повинні містити деякі дочірні ролі ARIA. [Докладніше](https://web.dev/aria-required-parent/)."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Ролі `[role]` не містяться в обов'язковому батьківському елементі"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "Ролі `[role]` містяться у відповідному батьківському елементі"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "Щоб належно виконувати функції спеціальних можливостей, ролі ARIA повинні мати дійсні значення. [Докладніше](https://web.dev/aria-roles/)."}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "Значення `[role]` недійсні"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "Значення `[role]` дійсні"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Допоміжні технології, як-от програми зчитування з екрана, не можуть тлумачити атрибути ARIA з недійсними значеннями. [Докладніше](https://web.dev/aria-valid-attr-value/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Атрибути `[aria-*]` не мають дійсних значень"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Атрибути `[aria-*]` мають дійсні значення"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "Допоміжні технології, як-от програми зчитування з екрана, не можуть тлумачити атрибути ARIA з недійсними назвами. [Докладніше](https://web.dev/aria-valid-attr/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Атрибути `[aria-*]` недійсні або написані неправильно"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "Атрибути `[aria-*]` дійсні та написані правильно"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "Субтитри роблять звукові елементи ефективнішими для користувачів із вадами слуху, зокрема надають важливу інформацію про те, хто й що говорить, а також інші непов'язані з мовленням деталі. [Докладніше](https://web.dev/audio-caption/)."}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "Елементи `<audio>` не містять елемент `<track>` з атрибутом `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "Елементи `<audio>` містять елемент `<track>` з атрибутом `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Елементи, що не пройшли перевірку"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "Коли кнопка не має зрозумілої назви, програми зчитування з екрана озвучують її словом \"кнопка\", через що вона стає непридатною для користувачів. [Докладніше](https://web.dev/button-name/)."}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "Кнопки не мають доступних для зчитування назв"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "Кнопки мають доступну для зчитування назву"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "Якщо додати способи обходу вмісту, що повторюється, користувачам буде простіше переходити між елементами на сторінці за допомогою клавіатури. [Докладніше](https://web.dev/bypass/)."}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "Сторінка не містить заголовка, посилання для пропуску вмісту чи мітки області"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "Сторінка містить заголовок, посилання для пропуску вмісту чи мітку області"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "Для багатьох користувачів складно або неможливо читати текст із низькою контрастністю. [Докладніше](https://web.dev/color-contrast/)."}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "<PERSON>іж кольорами фону та переднього плану недостатній коефіцієнт контрастності."}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "<PERSON>іж кольорами фону та переднього плану достатній коефіцієнт контрастності"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "Коли списки визначень мають неправильну розмітку, програми зчитування з екрана можуть генерувати незрозумілі або неточні дані. [Докладніше](https://web.dev/definition-list/)."}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "Списки визначення `<dl>` містять не лише належно впорядковані групи `<dt>` і `<dd>` чи елементи `<script>` або `<template>`."}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "Списки визначення `<dl>` містять лише належно впорядковані групи `<dt>` та `<dd>` чи елементи `<script>` або `<template>`."}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "Щоб програми зчитування з екрана правильно озвучували елементи списку визначень (`<dt>` і `<dd>`), елементи має бути згруповано в батьківському елементі `<dl>`. [Докладніше](https://web.dev/dlitem/)."}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "Елементи списку визначень не згруповано в елементах `<dl>`"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "Елементи списку визначень згруповано в елементах `<dl>`"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "Завдяки назві користувачі програми зчитування з екрана дізнаються загальну інформацію про сторінку, а користувачі пошукових систем визначають, чи сторінка відповідає їхньому запиту. [Докладніше](https://web.dev/document-title/)."}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "Документ не має елемента `<title>`"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "Документ містить елемент `<title>`"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "Значення атрибута ідентифікатора має бути унікальним, щоб допоміжні технології не пропускали інші копії. [Докладніше](https://web.dev/duplicate-id/)."}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "Атрибути `[id]` на сторінці неунікальні"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "Атрибути `[id]` на сторінці унікальні"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "Користувачі програм зчитування з екрана використовують назви фреймів, щоб дізнатися їх вміст. [Докладніше](https://web.dev/frame-title/)."}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "Елементи `<frame>` або `<iframe>` не мають назви"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "Елементи `<frame>` або `<iframe>` мають назву"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "Якщо для сторінки не вказано атрибут мови, програма зчитування з екрана припускає, що мовою сторінки є мова за умовчанням, яку користувач вибрав під час налаштування програми зчитування. Якщо насправді мова сторінки інша, програма зчитування з екрана може неправильно озвучувати текст на сторінці. [Докладніше](https://web.dev/html-has-lang/)."}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Елемент `<html>` не має атрибута `[lang]`"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "Елемент `<html>` містить атрибут `[lang]`"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "Якщо указати дійсний тег мови за [стандартом BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question), це допоможе програмі зчитування з екрана правильно озвучувати текст. [Докладніше](https://web.dev/html-lang-valid/)."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Елемент `<html>` не містить дійсного значення для атрибута `[lang]`."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "Елемент `<html>` має дійсне значення атрибута `[lang]`"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "Інформативні елементи повинні містити короткий, описовий альтернативний текст. Декоративні елементи можуть ігноруватись і мати порожній атрибут alt. [Докладніше](https://web.dev/image-alt/)."}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "Елементи зображення не мають атрибутів `[alt]`"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "Елементи зображення мають атрибути `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "Коли зображення використовується як кнопка `<input>`, додавши альтернативний текст, ви допоможете користувачам програм зчитування з екрана зрозуміти призначення кнопки. [Докладніше](https://web.dev/input-image-alt/)."}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Елементи `<input type=\"image\">` не містять текст `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "Елементи `<input type=\"image\">` містять текст `[alt]`"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "Завдяки міткам допоміжні технології, як-от програми зчитування з екрана, правильно озвучують елементи керування формою. [Докладніше](https://web.dev/label/)."}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "Елементи форми не мають пов’язаних міток"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "Елементи форми мають пов’язані мітки"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "Таблиця, яка використовується як макет, не повинна містити елементів даних, як-от елементів th або caption чи атрибут summary, оскільки це може спантеличити користувачів програм зчитування з екрана. [Докладніше](https://web.dev/layout-table/)."}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "Презентаційні елементи `<table>` використовують атрибути `<th>`, `<caption>` або `[summary]`."}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "Презентаційні елементи `<table>` не використовують атрибути `<th>`, `<caption>` або `[summary]`."}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "Унікальний і доступний для виділення текст посилання (а також альтернативний текст для зображень, коли вони використовуються як посилання), який можна розпізнати, покращує навігацію для користувачів програм зчитування з екрана. [Докладніше](https://web.dev/link-name/)."}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "Посилання не мають назв, які можна розпізнати"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "Посилання мають назви, які можна розпізнати"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "Програми зчитування з екрана озвучують списки в особливий спосіб. Правильна структура списків допомагає таким програмам правильно розпізнавати інформацію. [Докладніше](https://web.dev/list/)."}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "Списки не містять лише елементи `<li>` і елементи для підтримки виконання сценарію (`<script>` та `<template>`)."}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "Списки містять лише елементи `<li>` й елементи для підтримки виконання сценарію (`<script>` і `<template>`)."}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "Щоб програми зчитування з екрана правильно озвучували елементи списку (`<li>`), ці елементи повинні міститися в батьківських елементах `<ul>` або `<ol>`. [Докладніше](https://web.dev/listitem/)."}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "Елементів списку (`<li>`) немає в батьківських елементах `<ul>` або `<ol>`."}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "Елементи списку (`<li>`) містяться в батьківських елементах `<ul>` або `<ol>`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "Користувачі не очікують, що сторінка оновлюватиметься автоматично. Таке оновлення перемістить фокус назад угору сторінки. Це може роздратувати або спантеличити користувачів. [Докладніше](https://web.dev/meta-refresh/)."}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Документ використовує `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "Документ не використовує тег `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "Якщо вимкнути масштабування, користувачі з поганим зором не зможуть збільшити екран, щоб краще бачити вміст веб-сторінки. [Докладніше](https://web.dev/meta-viewport/)."}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "Параметр `[user-scalable=\"no\"]` використовується в елементі `<meta name=\"viewport\">` або атрибут `[maximum-scale]` менший за 5."}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "Параметр `[user-scalable=\"no\"]` не використовується в елементі `<meta name=\"viewport\">`, а атрибут `[maximum-scale]` має значення не менше 5."}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "Програми зчитування з екрана розпізнають лише текст. Якщо додати текст заміщення до елементів `<object>`, це допоможе таким програмам передати їх значення користувачам. [Докладніше](https://web.dev/object-alt/)."}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "Елементи `<object>` не містять текст `[alt]`"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "Елементи `<object>` містять текст `[alt]`"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "Значення, що перевищує 0, передбачає явне встановлення порядку навігації. Хоча технічно воно дійсне, це часто ускладнює взаємодію для користувачів, які застосовують допоміжні технології. [Докладніше](https://web.dev/tabindex/)."}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "Деякі елементи мають значення `[tabindex]`, що перевищує 0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "Жоден елемент не має значення `[tabindex]`, що перевищує 0"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "Програми зчитування з екрана мають функції, які полегшують навігацію в таблицях. Якщо клітинки `<td>`, які використовують атрибут `[headers]`, посилаються лише на інші клітинки в тій самій таблиці, це може покращити взаємодію для користувачів програми зчитування з екрана. [Докладніше](https://web.dev/td-headers-attr/)."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Клітинки в елементі `<table>`, які використовують атрибут `[headers]`, посилаються на елемент `id`, відсутній в тій самій таблиці."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "Клітинки в елементі `<table>`, які використовують атрибут `[headers]`, посилаються на клітинки тієї ж таблиці."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "Програми зчитування з екрана мають функції, які полегшують навігацію в таблицях. Якщо заголовки таблиці завжди посилаються на певні набори клітинок, це може покращити взаємодію для користувачів програм зчитування з екрана. [Докладніше](https://web.dev/th-has-data-cells/)."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Елементи `<th>` і елементи з роллю `[role=\"columnheader\"/\"rowheader\"]` не містять клітинок із даними, які описують."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "Елементи `<th>` і елементи з роллю `[role=\"columnheader\"/\"rowheader\"]` містять клітинки з даними, які описують."}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "Якщо для елементів указати дійсний тег мови за [стандартом BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question), це допоможе програмі зчитування з екрана правильно озвучувати текст. [Докладніше](https://web.dev/valid-lang/)."}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Атрибути `[lang]` не мають дійсного значення"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "Атрибути `[lang]` мають дійсне значення"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "Коли відео має субтитри, користувачам із вадами слуху простіше зрозуміти його. [Докладніше](https://web.dev/video-caption/)."}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "Елементи `<video>` не містять елемент `<track>` з атрибутом `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "Елементи `<video>` містять елемент `<track>` з атрибутом `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "Звукові описи надають важливу інформацію про відео (як-от міміка й оточення), яку не можуть забезпечити діалоги. [Докладніше](https://web.dev/video-description/)."}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "Елементи `<video>` не містять елемент `<track>` з атрибутом `[kind=\"description\"]`."}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "Елементи `<video>` містять елемент `<track>` з атрибутом `[kind=\"description\"]`"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "Налаштуйте параметр `apple-touch-icon` для ідеального вигляду на пристроях iOS, коли користувачі додають прогресивний веб-додаток на головний екран. Цей параметр має вказувати на непрозоре квадратне зображення PNG розміром 192 пікс. (або 180 пікс.). [Докладніше](https://web.dev/apple-touch-icon/)."}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "Не містить дійсного значка `apple-touch-icon`"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "Атрибут `apple-touch-icon-precomposed` заста<PERSON><PERSON><PERSON>. Радимо використовувати `apple-touch-icon`."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "Містить дійсний значок `apple-touch-icon`"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Розширення Chrome негативно впливають на завантаження цієї сторінки. Спробуйте перевірити сторінку в режимі анонімного перегляду або в профілі Chrome без розширень."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "Оцінка сценарію"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "Синтаксичний аналіз сценарію"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "Загальний процесорний час"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "Зменште час виконання синтаксичного аналізу, компілювання й запуску сценаріїв JavaScript. Для цього корисно завантажувати менші обсяги даних JavaScript. [Докладніше](https://web.dev/bootup-time)."}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "Зменште час виконання JavaScript"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "Час виконання JavaScript"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Великі файли GIF неефективні для передавання анімованого вмісту. Щоб заощадити мережевий трафік даних, радимо замість формату GIF використовувати MPEG4 або WebM для анімацій і PNG чи WebP для статичних зображень. [Докладніше](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Використовуйте формати відео для анімованого вмісту"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Щоб зменшити час до повного завантаження, використовуйте закадрові й приховані зображення, коли завантажаться всі важливі ресурси. [Докладніше](https://web.dev/offscreen-images)."}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Відкладіть закадрові зображення"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Ресурси блокують першу візуалізацію сторінки. Надсилайте спершу важливі фрагменти JavaScript або таблиці CSS і відкладайте всі некритичні елементи. [Докладніше](https://web.dev/render-blocking-resources)."}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Вилучіть ресурси, які блокують відображення"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Великі обсяги мережевих даних використовують багато коштовного трафіку відвідувачів і довго завантажуються. [Докладніше](https://web.dev/total-byte-weight)."}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Загальний розмір – {totalBytes, number, bytes} КБ"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Уникайте великих обсягів даних у мережі"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Уникається великий обсяг даних мережі"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "Стиснення файлів CSS може зменшити обсяг даних у мережі. [Докладніше](https://web.dev/unminified-css)."}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "Зменште СSS"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Стиснення файлів JavaScript може зменшити обсяг даних і час синтаксичного аналізу сценарію. [Докладніше](https://web.dev/unminified-javascript)."}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Зменште файл JavaScript"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Щоб зменшити трафік даних у мережі, видаліть непотрібні правила з таблиць стилів і відкладіть завантаження таблиць CSS, що не використовуються для вмісту у верхній частині сторінки. [Докладніше](https://web.dev/unused-css-rules)."}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Видаліть вміст CSS, який не використовується"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Видаліть файли <PERSON>Script, які ви не використовуєте, щоб зменшити кількість байтів під час активності в мережі."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Вилучіть файли JavaScript, які ви не використовуєте"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Якщо зберігати кеш за тривалий період часу, сторінки можуть завантажуватися швидше під час повторних відвідувань. [Докладніше](https://web.dev/uses-long-cache-ttl)."}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Знайдено 1 ресурс}one{Знайдено # ресурс}few{Знайдено # ресурси}many{Знайдено # ресурсів}other{Знайдено # ресурсу}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Показуйте статичні об’єкти за допомогою ефективних правил кешування"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Використовуються ефективні правила кешування статичних об’єктів"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Оптимізовані зображення завантажуються швидше й використовують менше мобільного трафіку. [Докладніше](https://web.dev/uses-optimized-images)."}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Ефективно кодуйте зображення"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Показуйте зображення правильного розміру, щоб заощадити мобільний трафік і покращити час завантаження. [Докладніше](https://web.dev/uses-responsive-images)."}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Правильно виберіть розмір зображень"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Текстові ресурси потрібно відображати зі стисненням (Gzip, Deflate ч<PERSON> Brotli), щоб мінімізувати загальний трафік мережі. [Докладніше](https://web.dev/uses-text-compression)."}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Увімкніть стиснення тексту"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "Формати зображень JPEG 2000, JPEG XR і WebP часто стискаються краще, ніж PNG чи JPEG. Тому вони швидше завантажуються й використовують менше даних. [Докладніше](https://web.dev/uses-webp-images)."}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "Показуйте зображення в нових форматах"}, "lighthouse-core/audits/content-width.js | description": {"message": "Якщо ширина контенту додатка не збігається з шириною області перегляду, можливо, додаток не вдасться оптимізувати для екранів мобільних пристроїв. [Докладніше](https://web.dev/content-width)."}, "lighthouse-core/audits/content-width.js | explanation": {"message": "Розмір області перегляду ({innerWidth} пікс.) не збігається з розміром вікна ({outerWidth} пікс.)."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "Розмір контенту не відповідає області перегляду"}, "lighthouse-core/audits/content-width.js | title": {"message": "Розмір контенту відповідає області перегляду"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "Ланцюжки важливих запитів нижче показують, які ресурси мають високий пріоритет. Щоб пришвидшити завантаження сторінки, зменште довжину ланцюжків і розмір завантажень або відкладіть завантаження непотрібних ресурсів. [Докладніше](https://web.dev/critical-request-chains)."}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Знайдено 1 ланцюжок}one{Знайдено # ланцюжок}few{Знайдено # ланцюжки}many{Знайдено # ланцюжків}other{Знайдено # ланцюжка}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "Мінімізуйте глибину важливих запитів"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "Припинення підтримки/застереження"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "Рядок"}, "lighthouse-core/audits/deprecations.js | description": {"message": "API, які більше не підтримуються, будуть видалені з веб-переглядача. [Докладніше](https://web.dev/deprecations)."}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Знайдено 1 застереження}one{Знайдено # застереження}few{Знайдено # застереження}many{Знайдено # застережень}other{Знайдено # застереження}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "Використовує непідтримувані API"}, "lighthouse-core/audits/deprecations.js | title": {"message": "Уникає інтерфейсів API, які більше не підтримуються"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "Application Cache API більше не підтримується. [Докладніше](https://web.dev/appcache-manifest)."}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "Знайдено кеш \"{AppCacheManifest}\""}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "Використовує кеш додатка"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "Уникає кешу додатка"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "Якщо вказати елемент doctype, веб-переглядач не перейде в режим сумісності. [Докладніше](https://web.dev/doctype)."}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Назва елемента doctype має починатися з малої літери й мати значення `html`"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Документ має містити тег doctype"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Очікується, що поле publicId буде порожнім"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Очікується, що поле systemId буде порожнім"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Для сторінки не вказано елемента HTML doctype, що активує режим сумісності"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "Сторінка має елемент HTML doctype"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "Елемент"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Статистика"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Значення"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "Розробники веб-переглядачів радять розміщувати на сторінці до ~1500 елементів DOM. Зона найкращого сприйняття – глибина дерева, що має менше 32 елементів і менше ніж 60 дочірніх чи батьківських елементів. Через великий файл DOM використовується більше пам'яті, [стилі обчислюються](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) довше, а [перекомпонування макетів](https://developers.google.com/speed/articles/reflow) коштує дорого. [Докладніше](https://web.dev/dom-size)."}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 елемент}one{# елемент}few{# елементи}many{# елементів}other{# елемента}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Уникайте надмірного розміру DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Максимальна глибина DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Усього елементів DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Макси<PERSON>а<PERSON>ьна кількість дочірніх елементів"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "Уникається надмірний розмір DOM"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "Ціль"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "Додайте `rel=\"noopener\"` або `rel=\"noreferrer\"` до зовнішніх посилань, щоб покращити ефективність і підвищити їх захищеність. [Докладніше](https://web.dev/external-anchors-use-rel-noopener)."}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "Міждоменні посилання на веб-сторінці ненадійні"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "Міждоменні посилання на веб-сторінці надійні"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "Не вдалося визначити сервіс для прив'язки ({anchorHTML}). Якщо гіперпосилання не використовується, видаліть цю частину: \"target=_blank\"."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Сайти, які надсилають запит на доступ до місцезнаходження без пояснення, викликають у користувачів недовіру або спантеличеність. Радимо пов'язувати запит із діями користувача. [Докладніше](https://web.dev/geolocation-on-start)."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Надсилає запит на доступ до геолокації під час завантаження сторінки"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Уникає надсилання запитів на доступ до геолокації під час завантаження сторінки"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Версія"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "Усі бібліотеки JavaScript зовнішнього інтерфейсу виявлено на сторінці. [Докладніше](https://web.dev/js-libraries)."}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "Виявлені бібліотеки JavaScript"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "Зовнішні сценарії, динамічно вставлені методом `document.write()`, можуть затримувати завантаження сторінки на десятки секунд для користувачів із повільним з'єднанням. [Докладніше](https://web.dev/no-document-write)."}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Використовує `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "Уникає `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "Найвищий рівень серйозності"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "Версія бібліотеки"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "Кількість прогалин системи безпеки"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "Деякі сторонні сценарії можуть містити відомі прогалини системи безпеки, які зловмисники можуть легко виявити та використати. [Докладніше](https://web.dev/no-vulnerable-libraries)."}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{Виявлено 1 прогалину системи безпеки}one{Виявлено # прогалину системи безпеки}few{Виявлено # прогалини системи безпеки}many{Виявлено # прогалин системи безпеки}other{Виявлено # прогалини системи безпеки}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "Включає бібліотеки JavaScript зовнішнього інтерфейсу з відомими прогалинами системи безпеки"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "Високий"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "Низький"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "Середній"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "Уникає бібліотек JavaScript зовнішнього інтерфейсу з відомими прогалинами в системі безпеки"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "Сайти, які надсилають запит на показ сповіщень без пояснення, викликають у користувачів недовіру або спантеличеність. Радимо пов'язувати запит із жестами користувача. [Докладніше](https://web.dev/notification-on-start)."}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Надсилає запит на показ сповіщень під час завантаження сторінки"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "Уникає надсилання запитів на показ сповіщень під час завантаження сторінки"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "Відхилені елементи"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "Якщо заборонити вставляти пароль, це порушить правила щодо високого рівня безпеки. [Докладніше](https://web.dev/password-inputs-can-be-pasted-into)."}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "Не дозволяє користувачам вставляти вміст у поля паролів"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "Дає змогу користувачам вставляти вміст у поля паролів"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Протокол"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "Протокол HTTP/2 має низку переваг над HTTP/1.1, як-от двійкові заголовки, мультиплексування та технологія Server Push. [Докладніше](https://web.dev/uses-http2)."}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 запит не розміщено через протокол HTTP/2}one{# запит не розміщено через протокол HTTP/2}few{# запити не розміщено через протокол HTTP/2}many{# запитів не розміщено через протокол HTTP/2}other{# запиту не розміщено через протокол HTTP/2}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "Не використовує протокол HTTP/2 для всіх ресурсів"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "Використовує протокол HTTP/2 для власних ресурсів"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Щоб сторінка краще прокручувалася, позначте блоки прослуховування подій сенсорного екрана та коліщатка як `passive`. [Докладніше](https://web.dev/uses-passive-event-listeners)."}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Не використовує пасивні прослуховувачі, щоб покращити функцію прокручування"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Використовує пасивні прослуховувачі, щоб покращити прокручування сторінки"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "Помилки, записані в журнал консолі, указують на невирішені проблеми. Вони можуть бути викликані збоями запитів мережі або іншими проблемами веб-переглядача. [Докладніше](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "Помилки веб-переглядача записано в журнал консолі"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "Помилки веб-переглядача не записано в журнал консолі"}, "lighthouse-core/audits/font-display.js | description": {"message": "Використовуйте функцію відображення шрифтів CSS, щоб текст було видно під час завантаження веб-шрифтів. [Докладніше](https://web.dev/font-display)."}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "Переконайтеся, що текст залишається видимим під час завантаження веб-шрифту"}, "lighthouse-core/audits/font-display.js | title": {"message": "Увесь текст залишається видимим під час завантаження веб-шрифтів"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "Інструменту Lighthouse не вдалось автоматично перевірити значення відображення шрифтів для цієї URL-адреси: {fontURL}."}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "Формат (фактичний)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Формат (відображуваний)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "Розміри показаного зображення мають відповідати реальному формату. [Докладніше](https://web.dev/image-aspect-ratio)."}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "Показує зображення неправильного формату"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "Показує зображення правильного формату"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "Недійсна інформація про розмір зображення {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "Веб-переглядачі можуть активно заохочувати користувачів установити ваш додаток на головний екран, що сприятиме кращій взаємодії. [Докладніше](https://web.dev/installable-manifest)."}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "Маніфест веб-додатка не відповідає умовам для встановлення"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "Маніфест веб-додатка відповідає умовам щодо встановлення"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "Ненадійна URL-адреса"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "Усі сайти мають бути захищені протоколом HTTPS, навіть якщо вони не обробляють конфіденційні дані. HTTPS не дозволяє зловмисникам втручатись в обмін даними між додатком і користувачами або пасивно прослуховувати супутні події. Це обов'язкова умова для протоколу HTTP/2 та багатьох нових API веб-платформ. [Докладніше](https://web.dev/is-on-https)."}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Знайдено 1 ненадійний запит}one{Знайдено # ненадійний запит}few{Знайдено # ненадійні запити}many{Знайдено # ненадійних запитів}other{Знайдено # ненадійного запиту}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "Не використовує протокол HTTPS"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "Використовує протокол HTTPS"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "Якщо сторінка швидко завантажується в мобільній мережі, це покращує враження користувачів мобільних пристроїв. [Докладніше](https://web.dev/load-fast-enough-for-pwa)."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "Стає інтерактивною через {timeInMs, number, seconds} с"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "Стає інтерактивною в емульованій мобільній мережі через {timeInMs, number, seconds} с"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "Сторінка завантажується надто повільно й неактивна впродовж 10 секунд. Щоб дізнатися, як виправити цю ситуацію, перегляньте можливості та дані діагностики в розділі \"Ефективність\"."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "Швидкість завантаження сторінки через мобільну мережу недостатньо висока"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "Швидкість завантаження сторінки через мобільну мережу достатньо висока"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Категорія"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "Зменште час виконання синтаксичного аналізу, компілювання й запуску сценаріїв JavaScript. Для цього корисно завантажувати менші обсяги даних JavaScript. [Докладніше](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Мінімізуйте роботу основного потоку"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "Мінімізується робота основного потоку"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "Щоб охопити якомога більше користувачів, сайти мають працювати в усіх відомих веб-переглядачах. [Докладніше](https://web.dev/pwa-cross-browser)."}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "Сайт працює у різних веб-переглядачах"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Переконайтеся, що окремі сторінки можна відкрити за допомогою прямого посилання, а URL-адреси унікальні, щоб ними можна було ділитися в соціальних мережах. [Докладніше](https://web.dev/pwa-each-page-has-url)."}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Кожна сторінка має URL-адресу"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "Додаток має швидко реагувати на дії користувача, навіть якщо мережа повільна. Це суттєво впливає на оцінку роботи вашого додатка користувачем. [Докладніше](https://web.dev/pwa-page-transitions)."}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "Схоже, перехід між сторінками не блокує мережу"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "Приблизна затримка введення показує, скільки часу в мілісекундах додаток відповідає на ввід користувача під час п'ятисекундного періоду завантаження сторінки. Якщо затримка перевищує 50 мс, користувачі можуть вважати ваш додаток повільним. [Докладніше](https://web.dev/estimated-input-latency)."}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "Приблизна затримка введення"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "Перша візуалізація вмісту показує, коли з'являється текст чи зображення. [Докладніше](https://web.dev/first-contentful-paint)."}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "Перше відображення всього вмісту"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "Перший простій ЦП вказує, коли основний ланцюжок сторінки вперше може обробити введення.  [Докладніше](https://web.dev/first-cpu-idle)."}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "Перший простій ЦП"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "Час початку візуалізації вказує, коли видно основний вміст сторінки. [Докладніше](https://web.dev/first-meaningful-paint)."}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "Перше значне відображення"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "Час до повного завантаження – це період часу, через який сторінка стане повністю інтерактивною. [Докладніше](https://web.dev/interactive)."}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "Час до повної взаємодії"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "Максимальна потенційна затримка відповіді на першу дію – це тривалість найдовшого завдання в мілісекундах. [Докладніше](https://developers.google.com/web/updates/2018/05/first-input-delay)."}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "Максима<PERSON>ьна потенційна затримка відповіді на першу дію"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "Індекс швидкості показує, через скільки часу відображається вміст сторінки. [Докладніше](https://web.dev/speed-index)."}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "Індекс швидкості"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "Загальна тривалість усіх періодів часу в мілісекундах між першою візуалізацією вмісту та часом до повного завантаження, коли час виконання завдання перевищує 50 мс."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "Загальний час блокування"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "Час затримки передачі сигналу мережі (RTT) суттєво впливає на ефективність. Якщо показник RTT високий, це означає, що сервери, розташовані ближче до користувача, можуть покращити ефективність. [Докладніше](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "lighthouse-core/audits/network-rtt.js | title": {"message": "<PERSON>ас затримки передачі сигналу мережі"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "Затримка сервера може впливати на ефективність веб-сайту. Якщо показник затримки сервера високий, це означає, що сервер перевантажено або в нього низька ефективність. [Докладніше](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "Затримка сервера"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "Синтаксис Service Worker забезпечує надійність вашого веб-додатка в разі непередбачуваних умов у мережі. [Докладніше](https://web.dev/offline-start-url)."}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url` не надсилає код 200 у режимі офлайн"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url` надсилає код 200 у режимі офлайн"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "Lighthouse не може прочитати `start_url` з маніфесту. Через це `start_url` вважається URL-адресою документа. Повідомлення про помилку: \"{manifestWarning}\"."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "Перевищення бюджету"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "Стежте, щоб кількість і розмір мережевих запитів не перевищували цільових значень, указаних у бюджеті ефективності. [Докладніше](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 запит}one{# запит}few{# запити}many{# запитів}other{# запиту}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "Бюджет ефективності"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "Якщо ви вже налаштували HTTPS, переконайтеся, що весь трафік HTTP переспрямовується на HTTPS, щоб увімкнути безпечні веб-функції для всіх користувачів. [Докладніше](https://web.dev/redirects-http)."}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "Тра<PERSON>ік HTTP не перепрямовується на HTTPS"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "Трафік HTTP переспрямовується на HTTPS"}, "lighthouse-core/audits/redirects.js | description": {"message": "Переспрямування викликають додаткові затримки під час завантаження сторінки. [Докладніше](https://web.dev/redirects)."}, "lighthouse-core/audits/redirects.js | title": {"message": "Уникайте переспрямувань кількох сторінок"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "Щоб установити бюджет відповідно до кількості та розміру ресурсів на сторінці, додайте файл budget.json. [Докладніше](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 запит • {byteCount, number, bytes} КБ}one{# запит • {byteCount, number, bytes} КБ}few{# запити • {byteCount, number, bytes} КБ}many{# запитів • {byteCount, number, bytes} КБ}other{# запиту • {byteCount, number, bytes} КБ}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "Не надсилайте багато запитів і передавайте вміст малого розміру"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "Канонічні посилання вказують, які URL-адреси показувати в результатах пошуку. [Докладніше](https://web.dev/canonical)."}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "Кілька URL-адрес конфліктують ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "Указує на інший домен ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "Нед<PERSON><PERSON>сна URL-адреса ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Указує на інше місце `hreflang` ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "Відносна URL-адреса ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "Указує на основну URL-адресу домену (домашню сторінку), а не на еквівалентну сторінку вмісту"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "Документ не має дійсного посилання `rel=canonical`"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "Документ має дійсний атрибут `rel=canonical`"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "Розміри шрифту до 12px замалі й нечитабельні. Щоб користувачі мобільних пристроїв змогли прочитати текст, їм потрібно буде збільшувати масштаб пальцями. Бажано, щоб понад 60% тексту на сторінці було написано щонайменше шрифтом 12px. [Докладніше](https://web.dev/font-size)."}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} читабельного тексту"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "Текст нечитабельний, оскільки метатег області перегляду не оптимізовано для мобільних пристроїв."}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "{decimalProportion, number, extendedPercent} тексту має замалий шрифт (на основі {decimalProportionVisited, number, extendedPercent} вибірки)."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "У документі використано нечитабельні розміри шрифтів"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "У документі використано читабельні розміри шрифтів"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "Посилання hreflang указують пошуковим системам версію сторінки, яку потрібно додати в результати пошуку для певної мови чи регіону. [Докладніше](https://web.dev/hreflang)."}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "Документ не має дійсного атрибута `hreflang`"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "Документ має дійсний атрибут `hreflang`"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "Сторінки з недійсними кодами статусу HTTP можуть неправильно індексуватися. [Докладніше](https://web.dev/http-status-code)."}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "Сторінка має недійсний код статусу HTTP"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "Сторінка має дійсний код статусу HTTP"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "Пошукові системи не зможуть включити ваші сторінки в результати пошуку, якщо в них немає дозволу сканувати їх. [Докладніше](https://web.dev/is-crawable)."}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "Сторінку не можна індексувати"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "Сторінку можна індексувати"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "Описовий текст посилання допомагає пошуковим системам розуміти ваш вміст. [Докладніше](https://web.dev/link-text)."}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Знайдено 1 посилання}one{Знайдено # посилання}few{Знайдено # посилання}many{Знайдено # посилань}other{Знайдено # посилання}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "Посилання не містять опису"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "Посилання містять опис"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "Щоб перевірити структуровані дані, скористайтесь [інструментом тестування](https://search.google.com/structured-data/testing-tool/) й [інструментом статичного аналізу структурованих даних](http://linter.structured-data.org/). [Докладніше](https://web.dev/structured-data)."}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "Структуровані дані дійсні"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "Результати пошуку можуть містити метаописи для короткого підсумку вмісту сторінки. [Докладніше](https://web.dev/meta-description)."}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "Немає опису."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "Документ не містить метаопису"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "Документ містить метаопис"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "Пошукові системи не можуть індексувати вміст плагінів. Багато пристроїв обмежують або не підтримують плагіни. [Докладніше](https://web.dev/plugins)."}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "Документ використовує плагіни"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "Документ уникає плагінів"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "Якщо файл robots.txt недійсний, веб-сканери можуть не зрозуміти, як потрібно індексувати або сканувати ваш веб-сайт. [Докладніше](https://web.dev/robots-txt)."}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "У відповідь на запит robots.txt отримано такий статус HTTP: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Знайдено 1 помилку}one{Знайдено # помилку}few{Знайдено # помилки}many{Знайдено # помилок}other{Знайдено # помилки}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "Інструменту Lighthouse не вдалося завантажити файл robots.txt"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "Файл robots.txt недійсний"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "Файл robots.txt дійсний"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "Інтерактивні елементи, як-от кнопки та посилання, мають бути достатньо великі (48x48 пікселів), а навколо них має бути достатньо місця, щоб їх можна було легко натиснути, не зачепивши інші елементи. [Докладніше](https://web.dev/tap-targets)."}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} елементів для натискання мають правильний розмір"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Елементи для натискання замалі, оскільки метатег області перегляду не оптимізовано для мобільних пристроїв"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "Елементи для натискання мають неправильний розмір"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Елементи для натискання накладаються"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Елемент для натискання"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "Елементи для натискання мають правильний розмір"}, "lighthouse-core/audits/service-worker.js | description": {"message": "Синтаксис Service Worker – це технологія, яка дає змогу додатку використовувати багато функцій прогресивного веб-додатка, як-от режим офлайн, додавання на головний екран і push-сповіщення. [Докладніше](https://web.dev/service-worker)."}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "Цією сторінкою керує синтаксис Service Worker, однак `start_url` не знайдено, оскільки не вдалося виконати синтаксичний аналіз маніфесту як дійсного файлу JSON"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "Цією сторінкою керує синтаксис Service Worker, однак параметр `start_url` ({startUrl}) перебуває за межами дії служби ({scopeUrl})"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "Цією сторінкою керує синтаксис Service Worker, однак `start_url` не знайдено, оскільки маніфест не завантажено."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "Це джерело містить один або кілька синтаксисів Service Worker, але сторінка ({pageUrl}) перебуває за межами дії служби."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "Немає синтак<PERSON>и<PERSON>у Service Worker, який керує сторінкою та `start_url`"}, "lighthouse-core/audits/service-worker.js | title": {"message": "Наявний синтаксис Service Worker, який керує сторінкою та `start_url`"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "Тематична заставка покращує взаємодію з користувачами під час запуску додатка з головного екрана. [Докладніше](https://web.dev/splash-screen)."}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "Власну заставку не налаштовано"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "Налаштовано власну заставку"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "Для адресного рядка веб-переглядача можна створити тему, яка відповідатиме вашому сайту [Докладніше](https://web.dev/themed-omnibox)."}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "Не змінює колір адресного рядка відповідно до теми."}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "Змінює колір адресного рядка відповідно до теми."}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "<PERSON>ас блокування основного ланцюжка"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "Сторонні розробники"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "Сторонній код може значно погіршити швидкість завантаження. Не використовуйте зайвий раз елементи коду сторонніх розробників та налаштуйте сторінку так, щоб сторонній код завантажувався після її основної частини. [Докладніше](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "Сторонній код заблокував основний ланцюжок на {timeInMs, number, milliseconds} мс"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "Зменште вплив стороннього коду"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "Використання стороннього коду"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "Час до першого байта визначає швидкість реакції сервера. [Докладніше](https://web.dev/time-to-first-byte)."}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "Кореневий документ відповів через {timeInMs, number, milliseconds} мс"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "Зменште час відповіді сервера (TTFB)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "Сервер довго відповідає (TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "Тривалість"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "Час початку"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "Тип"}, "lighthouse-core/audits/user-timings.js | description": {"message": "Використовуйте в додатку User Timing API, щоб отримувати показники ефективності додатка під час взаємодії з користувачами. [Докладніше](https://web.dev/user-timings)."}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 позначка часу користувача}one{# позначка часу користувача}few{# позначки часу користувача}many{# позначок часу користувача}other{# позначки часу користувача}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "Показники й мітки часу користувача"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Для {security<PERSON><PERSON><PERSON>} знайдено посилання для попереднього з'єднання <link>, але веб-переглядач його не використав. Переконайтеся, що ви правильно використовуєте атрибут `crossorigin`."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "Додайте в ресурси корективи `preconnect` чи `dns-prefetch`, щоб заздалегідь встановлювати з'єднання з важливими сторонніми джерелами. [Докладніше](https://web.dev/uses-rel-preconnect)."}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "Попередньо під’єднуйтеся до потрібних джерел"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Для {preloadURL} знайдено посилання для попереднього завантаження <link>, але веб-переглядач його не використав. Переконайтеся, що ви правильно використовуєте атрибут `crossorigin`."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "Використовуйте `<link rel=preload>`, щоб указати пріоритетність завантаження ресурсів, які наразі отримуються пізніше під час завантаження сторінки. [Докладніше](https://web.dev/uses-rel-preload)."}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "Попередньо завантажуйте основні запити"}, "lighthouse-core/audits/viewport.js | description": {"message": "Додайте тег `<meta name=\"viewport\">`, щоб оптимізувати додаток для екранів мобільних пристроїв. [Докладніше](https://web.dev/viewport)."}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "Тег `<meta name=\"viewport\">` не знайдено"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "Немає тегу `<meta name=\"viewport\">` з атрибутами `width` або `initial-scale`"}, "lighthouse-core/audits/viewport.js | title": {"message": "Має тег `<meta name=\"viewport\">` з атрибутами `width` або `initial-scale`"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "Ваш додаток має відображати певний контент, коли JavaScript вимкнено, навіть якщо це просто сповіщення для користувачів про те, що для використання додатка потрібно ввімкнути JavaScript. [Докладніше](https://web.dev/without-javascript)."}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "Сторінка має відображати контент, навіть якщо її сценарії недоступні."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "Резервний контент не відображається, коли JavaScript вимкнено"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "Певний контент відображається, коли JavaScript вимкнено"}, "lighthouse-core/audits/works-offline.js | description": {"message": "Якщо ви створюєте прогресивний веб-додаток, можете скористатися синтаксисом Service Worker, щоб додаток працював офлайн. [Докладніше](https://web.dev/works-offline)."}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "Поточна сторінка не надсилає код 200 у режимі офлайн"}, "lighthouse-core/audits/works-offline.js | title": {"message": "Поточна сторінка надсилає код 200 у режимі офлайн"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "Ця сторінка може не завантажуватись офлайн, оскільки тестову URL-адресу ({requested}) було переспрямовано на {final}. Спробуйте перевірити другу URL-адресу напряму."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "Ці рекомендації допоможуть покращити використання ролей ARIA у вашому додатку, що може позитивно вплинути на взаємодію для користувачів допоміжних технологій, як-от програм зчитування з екрана."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Ці рекомендації допоможуть надати альтернативний вміст для аудіо та відео. Це може покращити взаємодію для користувачів із вадами слуху або зору."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Аудіо та відео"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Ці елементи надають поширені практичні поради щодо спеціальних можливостей."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Практичні поради"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "Ці перевірки визначають можливості для [покращення доступності веб-додатка](https://developers.google.com/web/fundamentals/accessibility). Радимо також проводити перевірки вручну, оскільки не всі проблеми з доступністю визначаються автоматично."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "Ці елементи опрацьовують області, які не може охопити автоматизований інструмент перевірки. Докладніше читайте в нашому посібнику з [перевірки доступності](https://developers.google.com/web/fundamentals/accessibility/how-to-review)."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "Спеціальні можливості"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Ці рекомендації допоможуть покращити читабельність вмісту."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Контраст"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Ці рекомендації допоможуть покращити те, як користувачі тлумачать ваш вміст різними мовами."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Інтернаціоналізація та локалізація"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Ці рекомендації допоможуть удосконалити семантику елементів керування в додатку. Це може покращити взаємодію для користувачів допоміжних технологій, як-от програм зчитування з екрана."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Назви та мітки"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Ці рекомендації допоможуть покращити навігацію клавіатурою в додатку."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Навігація"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Ці рекомендації допоможуть покращити перегляд даних у таблиці чи списку за допомогою допоміжної технології, наприклад програми зчитування з екрана."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Таблиці та списки"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Практичні поради"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "Бюджети визначають стандарти ефективності вашого сайту."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "Бюджети"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "Докладніше про ефективність додатка. Ці числа не [впливають безпосередньо](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) на значення ефективності."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "Діагностика"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Найважливішим аспектом ефективності є швидкість відображення пікселів на екрані. Основні показники: перше відображення вмісту, перше значне відображення"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Покращення першого відображення"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Ці пропозиції допоможуть завантажувати сторінку швидше. Вони не [впливають безпосередньо](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) на значення ефективності."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Можливості"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "Показники"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Покращте загальну ефективність завантаження, щоб сторінка швидко реагувала й завантажувалася. Основні показники: час до повної взаємодії, індекс швидкості"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Загальні покращення"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "Ефективність"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "Ці категорії підтверджують аспекти прогресивного веб-додатка. [Докладніше](https://developers.google.com/web/progressive-web-apps/checklist)."}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "Ці категорії обов'язкові відповідно до основного [контрольного списку для прогресивних веб-додатків](https://developers.google.com/web/progressive-web-apps/checklist), але Lighthouse не перевіряє їх автоматично. Вони не впливають на ваш показник, проте їх потрібно підтвердити вручну."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "Прогресивний веб-додаток"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "Швидкість і надійність"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Можна встановити"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Оптимізовано для прогресивного веб-додатка"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "Ці перевірки визначають, чи сторінка оптимізована для позиціонування результатів пошукової системи. Lighthouse не перевіряє певні додаткові чинники, які можуть впливати на позицію веб-сайту в результатах пошуку. [Докладніше](https://support.google.com/webmasters/answer/35769)."}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "Щоб отримати додаткові практичні поради щодо оптимізації пошукових систем, скористайтеся додатковими засобами перевірки на своєму сайті."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "Оптим. пошук. систем"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "Відформатуйте HTML так, щоб веб-сканери краще розуміли вміст вашого додатка."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "Практичні поради щодо вмісту"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Щоб ваш вміст з’являвся в результатах пошуку, веб-сканерам потрібно надати доступ до додатка."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Сканування й індексування"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "Переконайтеся, що ваші сторінки адаптовані для мобільних пристроїв і користувачам не потрібно зменшувати чи збільшувати масштаб, щоб читати їх вміст. [Докладніше](https://developers.google.com/search/mobile-sites/)."}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "Для мобільних пристроїв"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "TTL кешу"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "Місцезнаходження"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "Назва"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "Запити"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "Тип ресурсу"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "Розмір"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Витрачений час"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "Розмір передавання"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "URL-адреса"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Потенційне заощадження"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "Потенційне заощадження"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Потенційне заощадження – {wastedBytes, number, bytes} КБ"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Потенційне заощадження – {wastedMs, number, milliseconds} мс"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "Документ"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "<PERSON>ри<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "Зображення"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "Медіа"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} мс"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "Інше"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "Сценарій"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} с"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Таблиця стилів"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Сторонні"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "Усього"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "Не вдалося записати результати трасування для завантаження вашої сторінки. Запустіть інструмент Lighthouse ще раз. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "Час очікування початкового з’єднання з протоколом Debugger минув."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "Веб-переглядач Chrome не отримав знімки екрана під час завантаження сторінки. Переконайтеся, що на сторінці є видимий вміст, а потім спробуйте перезапустити інструмент Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "DNS-серверам не вдалось обробити вказаний домен."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "У збирачі обов'язкових ресурсів {artifactName} сталася помилка: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "Сталася внутрішня помилка Chrome. Перезапустіть Chrome і спробуйте знову запустити інструмент Lighthouse."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "Зб<PERSON><PERSON><PERSON>ч обов'язкових ресурсів {artifactName} не запущено."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "Інструменту Lighthouse не вдалося безпечно завантажити сторінку, яку ви вказали. Переконайтеся, що ви тестуєте правильну URL-адресу, а сервер належним чином відповідає на всі запити."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "Інструменту Lighthouse не вдалося безпечно завантажити URL-адресу, яку ви вказали, оскільки сторінка перестала відповідати."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Указана вами URL-адреса не має дійсного сертифіката безпеки. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Веб-переглядач Chrome заблокував завантаження сторінки та відобразив проміжний екран. Переконайтеся, що ви тестуєте правильну URL-адресу, а сервер належним чином відповідає на всі запити."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Інструменту Lighthouse не вдалося безпечно завантажити сторінку, яку ви вказали. Переконайтеся, що ви тестуєте правильну URL-адресу, а сервер належним чином відповідає на всі запити. (Деталі: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Інструменту Lighthouse не вдалося безпечно завантажити сторінку, яку ви вказали. Переконайтеся, що ви тестуєте правильну URL-адресу, а сервер належним чином відповідає на всі запити. (Код статусу: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Сторінка завантажувалася задовго. Дотримуйтеся рекомендацій у звіті, щоб зменшити час завантаження сторінки, а потім спробуйте перезапустити інструмент Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "Час очікування відповіді протоколу DevTools перевищив установлений період. (Метод: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "Час отримання вмісту ресурсу перевищив установлений час"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "Схоже, указана вами URL-адреса недійсна."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "Показати перевірки"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "Початкова навігація"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "Максимальна критична затримка шляху:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "Помилка."}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "Повідомлення про помилку: немає інформації про перевірку"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "Дані тестів"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> [Lighthouse](https://developers.google.com/web/tools/lighthouse/) поточної сторінки в емульованій мобільній мережі. Значення приблизні й можуть відрізнятися."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "Додаткові елементи, які потрібно перевірити вручну"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "Не застосовуються"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "Можливість"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "Приблизне заощадження"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "Виконані перевірки"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "Згорнути фрагмент"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "Розгорнути фрагмент"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "Показати сторонні ресурси"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "Під час запуску Lighthouse виникли перелічені нижче проблеми."}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "Значення приблизні й можуть відрізнятися. Значення ефективності визначається [на основі цих показників](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "Перевірки зі статусом \"Пройдено\", що містять застереження"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "Застереження. "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "Спробуйте завантажити файл GIF у сервіс, де його можна вставити як відео HTML5."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "Установіть [плагін lazy-load від WordPress](https://wordpress.org/plugins/search/lazy+load/), який дає змогу відкласти завантаження закадрових зображень, або виберіть тему, що дозволяє це зробити. Також можете скористатися [плагіном AMP](https://wordpress.org/plugins/amp/)."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "Існує багато плагінів WordPress, які можуть допомогти [вбудувати важливі об'єкти](https://wordpress.org/plugins/search/critical+css/) або [відкласти менш важливі ресурси](https://wordpress.org/plugins/search/defer+css+javascript/). Зауважте, що така оптимізація може порушити функції теми або плагінів, тож доведеться змінити код."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "Теми, плагіни й характеристики сервера впливають на час відповіді. Спробуйте знайти більш оптимізовану тему, підібрати плагін для оптимізації та/або оновити сервер."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "Спробуйте показувати витяги в списках дописів (через тег \"більше\"), зменшити кількість показаних публікацій на сторінці, розділити довгі дописи на кілька сторінок або скористатися плагіном, щоб відкласти завантаження коментарів."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "Багато [плагінів WordPress](https://wordpress.org/plugins/search/minify+css/) можуть пришвидшити ваш сайт: вони об'єднують, зменшують і стискають стилі. Також можна скористатися процесом складання, щоб завчасно зменшити розмір, якщо це можливо."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "Багато [плагінів WordPress](https://wordpress.org/plugins/search/minify+javascript/) можуть пришвидшити ваш сайт: вони об'єднують, зменшують і стискають сценарії. Також можна скористатися процесом складання, щоб завчасно зменшити розмір, якщо це можливо."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "Спробуйте зменшити кількість [плагінів WordPress](https://wordpress.org/plugins/), що завантажують на сторінці непотрібні таблиці стилів CSS. Щоб визначити плагіни, які додають зайві таблиці CSS, перевірте [покриття коду](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в Chrome DevTools. Ви можете визначити тему чи плагін через URL-адресу таблиці стилів. У покритті коду знайдіть плагіни з багатьма таблицями стилів за великим обсягом червоного тексту. Плагін має ставити таблицю стилів у чергу, лише коли вона дійсно використовується на сторінці."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "Спробуйте зменшити кількість [плагінів WordPress](https://wordpress.org/plugins/), що завантажують на сторінці непотрібні фрагменти JavaScript. Щоб визначити плагіни, які додають зайвий код JavaScript, перевірте [покриття коду](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в Chrome DevTools. Ви можете визначити тему чи плагін через URL-адресу сценарію. У покритті коду знайдіть плагіни з багатьма сценаріями за великим обсягом червоного тексту. Плагін має ставити сценарій у чергу, лише коли він дійсно використовується на сторінці."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "Дізнайтеся про [кешування веб-переглядача у WordPress](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "Спробуйте [плагін WordPress для оптимізації зображень](https://wordpress.org/plugins/search/optimize+images/), який стискає зображення, але зберігає їх якість."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "Завантажуйте зображення через [бібліотеку медіафайлів](https://codex.wordpress.org/Media_Library_Screen), щоб переконатися, що вони доступні в потрібному розмірі. Потім вставляйте їх в оптимальному розмірі з бібліотеки або через віджет для зображень (зокрема для адаптивних точок переходу). Використовуйте зображення, що мають `Full Size`, лише якщо вони повністю поміщаються. [Докладніше](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)."}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "Ви можете ввімкнути стиснення тексту в конфігурації веб-сервера."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "Скористайтеся [плагіном](https://wordpress.org/plugins/search/convert+webp/) або сервісом, який автоматично конвертує завантажені зображення в оптимальні формати."}}