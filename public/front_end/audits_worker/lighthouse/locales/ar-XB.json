{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "‏‮Access‬‏ ‏‮keys‬‏ ‏‮let‬‏ ‏‮users‬‏ ‏‮quickly‬‏ ‏‮focus‬‏ ‏‮a‬‏ ‏‮part‬‏ ‏‮of‬‏ ‏‮the‬‏ ‏‮page‬‏. ‏‮For‬‏ ‏‮proper‬‏ ‏‮navigation‬‏, ‏‮each‬‏ ‏‮access‬‏ ‏‮key‬‏ ‏‮must‬‏ ‏‮be‬‏ ‏‮unique‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/accesskeys/)."}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "`[accesskey]` ‏‮values‬‏ ‏‮are‬‏ ‏‮not‬‏ ‏‮unique‬‏"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]` ‏‮values‬‏ ‏‮are‬‏ ‏‮unique‬‏"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "‏‮Each‬‏ ‏‮ARIA‬‏ `role` ‏‮supports‬‏ ‏‮a‬‏ ‏‮specific‬‏ ‏‮subset‬‏ ‏‮of‬‏ `aria-*` ‏‮attributes‬‏. ‏‮Mismatching‬‏ ‏‮these‬‏ ‏‮invalidates‬‏ ‏‮the‬‏ `aria-*` ‏‮attributes‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/aria-allowed-attr/)."}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "`[aria-*]` ‏‮attributes‬‏ ‏‮do‬‏ ‏‮not‬‏ ‏‮match‬‏ ‏‮their‬‏ ‏‮roles‬‏"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "`[aria-*]` ‏‮attributes‬‏ ‏‮match‬‏ ‏‮their‬‏ ‏‮roles‬‏"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "‏‮Some‬‏ ‏‮ARIA‬‏ ‏‮roles‬‏ ‏‮have‬‏ ‏‮required‬‏ ‏‮attributes‬‏ ‏‮that‬‏ ‏‮describe‬‏ ‏‮the‬‏ ‏‮state‬‏ ‏‮of‬‏ ‏‮the‬‏ ‏‮element‬‏ ‏‮to‬‏ ‏‮screen‬‏ ‏‮readers‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/aria-required-attr/)."}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]`‏‮s‬‏ ‏‮do‬‏ ‏‮not‬‏ ‏‮have‬‏ ‏‮all‬‏ ‏‮required‬‏ `[aria-*]` ‏‮attributes‬‏"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]`‏‮s‬‏ ‏‮have‬‏ ‏‮all‬‏ ‏‮required‬‏ `[aria-*]` ‏‮attributes‬‏"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "‏‮Some‬‏ ‏‮ARIA‬‏ ‏‮parent‬‏ ‏‮roles‬‏ ‏‮must‬‏ ‏‮contain‬‏ ‏‮specific‬‏ ‏‮child‬‏ ‏‮roles‬‏ ‏‮to‬‏ ‏‮perform‬‏ ‏‮their‬‏ ‏‮intended‬‏ ‏‮accessibility‬‏ ‏‮functions‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/aria-required-children/)."}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "‏‮Elements‬‏ ‏‮with‬‏ ‏‮an‬‏ ‏‮ARIA‬‏ `[role]` ‏‮that‬‏ ‏‮require‬‏ ‏‮children‬‏ ‏‮to‬‏ ‏‮contain‬‏ ‏‮a‬‏ ‏‮specific‬‏ `[role]` ‏‮are‬‏ ‏‮missing‬‏ ‏‮some‬‏ ‏‮or‬‏ ‏‮all‬‏ ‏‮of‬‏ ‏‮those‬‏ ‏‮required‬‏ ‏‮children‬‏."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "‏‮Elements‬‏ ‏‮with‬‏ ‏‮an‬‏ ‏‮ARIA‬‏ `[role]` ‏‮that‬‏ ‏‮require‬‏ ‏‮children‬‏ ‏‮to‬‏ ‏‮contain‬‏ ‏‮a‬‏ ‏‮specific‬‏ `[role]` ‏‮have‬‏ ‏‮all‬‏ ‏‮required‬‏ ‏‮children‬‏."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "‏‮Some‬‏ ‏‮ARIA‬‏ ‏‮child‬‏ ‏‮roles‬‏ ‏‮must‬‏ ‏‮be‬‏ ‏‮contained‬‏ ‏‮by‬‏ ‏‮specific‬‏ ‏‮parent‬‏ ‏‮roles‬‏ ‏‮to‬‏ ‏‮properly‬‏ ‏‮perform‬‏ ‏‮their‬‏ ‏‮intended‬‏ ‏‮accessibility‬‏ ‏‮functions‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/aria-required-parent/)."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]`‏‮s‬‏ ‏‮are‬‏ ‏‮not‬‏ ‏‮contained‬‏ ‏‮by‬‏ ‏‮their‬‏ ‏‮required‬‏ ‏‮parent‬‏ ‏‮element‬‏"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]`‏‮s‬‏ ‏‮are‬‏ ‏‮contained‬‏ ‏‮by‬‏ ‏‮their‬‏ ‏‮required‬‏ ‏‮parent‬‏ ‏‮element‬‏"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "‏‮ARIA‬‏ ‏‮roles‬‏ ‏‮must‬‏ ‏‮have‬‏ ‏‮valid‬‏ ‏‮values‬‏ ‏‮in‬‏ ‏‮order‬‏ ‏‮to‬‏ ‏‮perform‬‏ ‏‮their‬‏ ‏‮intended‬‏ ‏‮accessibility‬‏ ‏‮functions‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/aria-roles/)."}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]` ‏‮values‬‏ ‏‮are‬‏ ‏‮not‬‏ ‏‮valid‬‏"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "`[role]` ‏‮values‬‏ ‏‮are‬‏ ‏‮valid‬‏"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "‏‮Assistive‬‏ ‏‮technologies‬‏, ‏‮like‬‏ ‏‮screen‬‏ ‏‮readers‬‏, ‏‮can‬‏'‏‮t‬‏ ‏‮interpret‬‏ ‏‮ARIA‬‏ ‏‮attributes‬‏ ‏‮with‬‏ ‏‮invalid‬‏ ‏‮values‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/aria-valid-attr-value/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "`[aria-*]` ‏‮attributes‬‏ ‏‮do‬‏ ‏‮not‬‏ ‏‮have‬‏ ‏‮valid‬‏ ‏‮values‬‏"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "`[aria-*]` ‏‮attributes‬‏ ‏‮have‬‏ ‏‮valid‬‏ ‏‮values‬‏"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "‏‮Assistive‬‏ ‏‮technologies‬‏, ‏‮like‬‏ ‏‮screen‬‏ ‏‮readers‬‏, ‏‮can‬‏'‏‮t‬‏ ‏‮interpret‬‏ ‏‮ARIA‬‏ ‏‮attributes‬‏ ‏‮with‬‏ ‏‮invalid‬‏ ‏‮names‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/aria-valid-attr/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "`[aria-*]` ‏‮attributes‬‏ ‏‮are‬‏ ‏‮not‬‏ ‏‮valid‬‏ ‏‮or‬‏ ‏‮misspelled‬‏"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "`[aria-*]` ‏‮attributes‬‏ ‏‮are‬‏ ‏‮valid‬‏ ‏‮and‬‏ ‏‮not‬‏ ‏‮misspelled‬‏"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "‏‮Captions‬‏ ‏‮make‬‏ ‏‮audio‬‏ ‏‮elements‬‏ ‏‮usable‬‏ ‏‮for‬‏ ‏‮deaf‬‏ ‏‮or‬‏ ‏‮hearing‬‏-‏‮impaired‬‏ ‏‮users‬‏, ‏‮providing‬‏ ‏‮critical‬‏ ‏‮information‬‏ ‏‮such‬‏ ‏‮as‬‏ ‏‮who‬‏ ‏‮is‬‏ ‏‮talking‬‏, ‏‮what‬‏ ‏‮they‬‏'‏‮re‬‏ ‏‮saying‬‏, ‏‮and‬‏ ‏‮other‬‏ ‏‮non‬‏-‏‮speech‬‏ ‏‮information‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/audio-caption/)."}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "`<audio>` ‏‮elements‬‏ ‏‮are‬‏ ‏‮missing‬‏ ‏‮a‬‏ `<track>` ‏‮element‬‏ ‏‮with‬‏ `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "`<audio>` ‏‮elements‬‏ ‏‮contain‬‏ ‏‮a‬‏ `<track>` ‏‮element‬‏ ‏‮with‬‏ `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "‏‮Failing‬‏ ‏‮Elements‬‏"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "‏‮When‬‏ ‏‮a‬‏ ‏‮button‬‏ ‏‮doesn‬‏'‏‮t‬‏ ‏‮have‬‏ ‏‮an‬‏ ‏‮accessible‬‏ ‏‮name‬‏, ‏‮screen‬‏ ‏‮readers‬‏ ‏‮announce‬‏ ‏‮it‬‏ ‏‮as‬‏ \"‏‮button‬‏\", ‏‮making‬‏ ‏‮it‬‏ ‏‮unusable‬‏ ‏‮for‬‏ ‏‮users‬‏ ‏‮who‬‏ ‏‮rely‬‏ ‏‮on‬‏ ‏‮screen‬‏ ‏‮readers‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/button-name/)."}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "‏‮Buttons‬‏ ‏‮do‬‏ ‏‮not‬‏ ‏‮have‬‏ ‏‮an‬‏ ‏‮accessible‬‏ ‏‮name‬‏"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "‏‮Buttons‬‏ ‏‮have‬‏ ‏‮an‬‏ ‏‮accessible‬‏ ‏‮name‬‏"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "‏‮Adding‬‏ ‏‮ways‬‏ ‏‮to‬‏ ‏‮bypass‬‏ ‏‮repetitive‬‏ ‏‮content‬‏ ‏‮lets‬‏ ‏‮keyboard‬‏ ‏‮users‬‏ ‏‮navigate‬‏ ‏‮the‬‏ ‏‮page‬‏ ‏‮more‬‏ ‏‮efficiently‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/bypass/)."}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "‏‮The‬‏ ‏‮page‬‏ ‏‮does‬‏ ‏‮not‬‏ ‏‮contain‬‏ ‏‮a‬‏ ‏‮heading‬‏, ‏‮skip‬‏ ‏‮link‬‏, ‏‮or‬‏ ‏‮landmark‬‏ ‏‮region‬‏"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "‏‮The‬‏ ‏‮page‬‏ ‏‮contains‬‏ ‏‮a‬‏ ‏‮heading‬‏, ‏‮skip‬‏ ‏‮link‬‏, ‏‮or‬‏ ‏‮landmark‬‏ ‏‮region‬‏"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "‏‮Low‬‏-‏‮contrast‬‏ ‏‮text‬‏ ‏‮is‬‏ ‏‮difficult‬‏ ‏‮or‬‏ ‏‮impossible‬‏ ‏‮for‬‏ ‏‮many‬‏ ‏‮users‬‏ ‏‮to‬‏ ‏‮read‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/color-contrast/)."}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "‏‮Background‬‏ ‏‮and‬‏ ‏‮foreground‬‏ ‏‮colors‬‏ ‏‮do‬‏ ‏‮not‬‏ ‏‮have‬‏ ‏‮a‬‏ ‏‮sufficient‬‏ ‏‮contrast‬‏ ‏‮ratio‬‏."}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "‏‮Background‬‏ ‏‮and‬‏ ‏‮foreground‬‏ ‏‮colors‬‏ ‏‮have‬‏ ‏‮a‬‏ ‏‮sufficient‬‏ ‏‮contrast‬‏ ‏‮ratio‬‏"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "‏‮When‬‏ ‏‮definition‬‏ ‏‮lists‬‏ ‏‮are‬‏ ‏‮not‬‏ ‏‮properly‬‏ ‏‮marked‬‏ ‏‮up‬‏, ‏‮screen‬‏ ‏‮readers‬‏ ‏‮may‬‏ ‏‮produce‬‏ ‏‮confusing‬‏ ‏‮or‬‏ ‏‮inaccurate‬‏ ‏‮output‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/definition-list/)."}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>`'‏‮s‬‏ ‏‮do‬‏ ‏‮not‬‏ ‏‮contain‬‏ ‏‮only‬‏ ‏‮properly‬‏-‏‮ordered‬‏ `<dt>` ‏‮and‬‏ `<dd>` ‏‮groups‬‏, `<script>` ‏‮or‬‏ `<template>` ‏‮elements‬‏."}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "`<dl>`'‏‮s‬‏ ‏‮contain‬‏ ‏‮only‬‏ ‏‮properly‬‏-‏‮ordered‬‏ `<dt>` ‏‮and‬‏ `<dd>` ‏‮groups‬‏, `<script>` ‏‮or‬‏ `<template>` ‏‮elements‬‏."}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "‏‮Definition‬‏ ‏‮list‬‏ ‏‮items‬‏ (`<dt>` ‏‮and‬‏ `<dd>`) ‏‮must‬‏ ‏‮be‬‏ ‏‮wrapped‬‏ ‏‮in‬‏ ‏‮a‬‏ ‏‮parent‬‏ `<dl>` ‏‮element‬‏ ‏‮to‬‏ ‏‮ensure‬‏ ‏‮that‬‏ ‏‮screen‬‏ ‏‮readers‬‏ ‏‮can‬‏ ‏‮properly‬‏ ‏‮announce‬‏ ‏‮them‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/dlitem/)."}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "‏‮Definition‬‏ ‏‮list‬‏ ‏‮items‬‏ ‏‮are‬‏ ‏‮not‬‏ ‏‮wrapped‬‏ ‏‮in‬‏ `<dl>` ‏‮elements‬‏"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "‏‮Definition‬‏ ‏‮list‬‏ ‏‮items‬‏ ‏‮are‬‏ ‏‮wrapped‬‏ ‏‮in‬‏ `<dl>` ‏‮elements‬‏"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "‏‮The‬‏ ‏‮title‬‏ ‏‮gives‬‏ ‏‮screen‬‏ ‏‮reader‬‏ ‏‮users‬‏ ‏‮an‬‏ ‏‮overview‬‏ ‏‮of‬‏ ‏‮the‬‏ ‏‮page‬‏, ‏‮and‬‏ ‏‮search‬‏ ‏‮engine‬‏ ‏‮users‬‏ ‏‮rely‬‏ ‏‮on‬‏ ‏‮it‬‏ ‏‮heavily‬‏ ‏‮to‬‏ ‏‮determine‬‏ ‏‮if‬‏ ‏‮a‬‏ ‏‮page‬‏ ‏‮is‬‏ ‏‮relevant‬‏ ‏‮to‬‏ ‏‮their‬‏ ‏‮search‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/document-title/)."}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "‏‮Document‬‏ ‏‮doesn‬‏'‏‮t‬‏ ‏‮have‬‏ ‏‮a‬‏ `<title>` ‏‮element‬‏"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "‏‮Document‬‏ ‏‮has‬‏ ‏‮a‬‏ `<title>` ‏‮element‬‏"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "‏‮The‬‏ ‏‮value‬‏ ‏‮of‬‏ ‏‮an‬‏ ‏‮id‬‏ ‏‮attribute‬‏ ‏‮must‬‏ ‏‮be‬‏ ‏‮unique‬‏ ‏‮to‬‏ ‏‮prevent‬‏ ‏‮other‬‏ ‏‮instances‬‏ ‏‮from‬‏ ‏‮being‬‏ ‏‮overlooked‬‏ ‏‮by‬‏ ‏‮assistive‬‏ ‏‮technologies‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/duplicate-id/)."}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "`[id]` ‏‮attributes‬‏ ‏‮on‬‏ ‏‮the‬‏ ‏‮page‬‏ ‏‮are‬‏ ‏‮not‬‏ ‏‮unique‬‏"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "`[id]` ‏‮attributes‬‏ ‏‮on‬‏ ‏‮the‬‏ ‏‮page‬‏ ‏‮are‬‏ ‏‮unique‬‏"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "‏‮Screen‬‏ ‏‮reader‬‏ ‏‮users‬‏ ‏‮rely‬‏ ‏‮on‬‏ ‏‮frame‬‏ ‏‮titles‬‏ ‏‮to‬‏ ‏‮describe‬‏ ‏‮the‬‏ ‏‮contents‬‏ ‏‮of‬‏ ‏‮frames‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/frame-title/)."}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "`<frame>` ‏‮or‬‏ `<iframe>` ‏‮elements‬‏ ‏‮do‬‏ ‏‮not‬‏ ‏‮have‬‏ ‏‮a‬‏ ‏‮title‬‏"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "`<frame>` ‏‮or‬‏ `<iframe>` ‏‮elements‬‏ ‏‮have‬‏ ‏‮a‬‏ ‏‮title‬‏"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "‏‮If‬‏ ‏‮a‬‏ ‏‮page‬‏ ‏‮doesn‬‏'‏‮t‬‏ ‏‮specify‬‏ ‏‮a‬‏ ‏‮lang‬‏ ‏‮attribute‬‏, ‏‮a‬‏ ‏‮screen‬‏ ‏‮reader‬‏ ‏‮assumes‬‏ ‏‮that‬‏ ‏‮the‬‏ ‏‮page‬‏ ‏‮is‬‏ ‏‮in‬‏ ‏‮the‬‏ ‏‮default‬‏ ‏‮language‬‏ ‏‮that‬‏ ‏‮the‬‏ ‏‮user‬‏ ‏‮chose‬‏ ‏‮when‬‏ ‏‮setting‬‏ ‏‮up‬‏ ‏‮the‬‏ ‏‮screen‬‏ ‏‮reader‬‏. ‏‮If‬‏ ‏‮the‬‏ ‏‮page‬‏ ‏‮isn‬‏'‏‮t‬‏ ‏‮actually‬‏ ‏‮in‬‏ ‏‮the‬‏ ‏‮default‬‏ ‏‮language‬‏, ‏‮then‬‏ ‏‮the‬‏ ‏‮screen‬‏ ‏‮reader‬‏ ‏‮might‬‏ ‏‮not‬‏ ‏‮announce‬‏ ‏‮the‬‏ ‏‮page‬‏'‏‮s‬‏ ‏‮text‬‏ ‏‮correctly‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/html-has-lang/)."}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>` ‏‮element‬‏ ‏‮does‬‏ ‏‮not‬‏ ‏‮have‬‏ ‏‮a‬‏ `[lang]` ‏‮attribute‬‏"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>` ‏‮element‬‏ ‏‮has‬‏ ‏‮a‬‏ `[lang]` ‏‮attribute‬‏"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "‏‮Specifying‬‏ ‏‮a‬‏ ‏‮valid‬‏ [‏‮BCP‬‏ 47 ‏‮language‬‏](https://www.w3.org/International/questions/qa-choosing-language-tags#question) ‏‮helps‬‏ ‏‮screen‬‏ ‏‮readers‬‏ ‏‮announce‬‏ ‏‮text‬‏ ‏‮properly‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/html-lang-valid/)."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>` ‏‮element‬‏ ‏‮does‬‏ ‏‮not‬‏ ‏‮have‬‏ ‏‮a‬‏ ‏‮valid‬‏ ‏‮value‬‏ ‏‮for‬‏ ‏‮its‬‏ `[lang]` ‏‮attribute‬‏."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "`<html>` ‏‮element‬‏ ‏‮has‬‏ ‏‮a‬‏ ‏‮valid‬‏ ‏‮value‬‏ ‏‮for‬‏ ‏‮its‬‏ `[lang]` ‏‮attribute‬‏"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "‏‮Informative‬‏ ‏‮elements‬‏ ‏‮should‬‏ ‏‮aim‬‏ ‏‮for‬‏ ‏‮short‬‏, ‏‮descriptive‬‏ ‏‮alternate‬‏ ‏‮text‬‏. ‏‮Decorative‬‏ ‏‮elements‬‏ ‏‮can‬‏ ‏‮be‬‏ ‏‮ignored‬‏ ‏‮with‬‏ ‏‮an‬‏ ‏‮empty‬‏ ‏‮alt‬‏ ‏‮attribute‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/image-alt/)."}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "‏‮Image‬‏ ‏‮elements‬‏ ‏‮do‬‏ ‏‮not‬‏ ‏‮have‬‏ `[alt]` ‏‮attributes‬‏"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "‏‮Image‬‏ ‏‮elements‬‏ ‏‮have‬‏ `[alt]` ‏‮attributes‬‏"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "‏‮When‬‏ ‏‮an‬‏ ‏‮image‬‏ ‏‮is‬‏ ‏‮being‬‏ ‏‮used‬‏ ‏‮as‬‏ ‏‮an‬‏ `<input>` ‏‮button‬‏, ‏‮providing‬‏ ‏‮alternative‬‏ ‏‮text‬‏ ‏‮can‬‏ ‏‮help‬‏ ‏‮screen‬‏ ‏‮reader‬‏ ‏‮users‬‏ ‏‮understand‬‏ ‏‮the‬‏ ‏‮purpose‬‏ ‏‮of‬‏ ‏‮the‬‏ ‏‮button‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/input-image-alt/)."}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">` ‏‮elements‬‏ ‏‮do‬‏ ‏‮not‬‏ ‏‮have‬‏ `[alt]` ‏‮text‬‏"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">` ‏‮elements‬‏ ‏‮have‬‏ `[alt]` ‏‮text‬‏"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "‏‮Labels‬‏ ‏‮ensure‬‏ ‏‮that‬‏ ‏‮form‬‏ ‏‮controls‬‏ ‏‮are‬‏ ‏‮announced‬‏ ‏‮properly‬‏ ‏‮by‬‏ ‏‮assistive‬‏ ‏‮technologies‬‏, ‏‮like‬‏ ‏‮screen‬‏ ‏‮readers‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/label/)."}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "‏‮Form‬‏ ‏‮elements‬‏ ‏‮do‬‏ ‏‮not‬‏ ‏‮have‬‏ ‏‮associated‬‏ ‏‮labels‬‏"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "‏‮Form‬‏ ‏‮elements‬‏ ‏‮have‬‏ ‏‮associated‬‏ ‏‮labels‬‏"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "‏‮A‬‏ ‏‮table‬‏ ‏‮being‬‏ ‏‮used‬‏ ‏‮for‬‏ ‏‮layout‬‏ ‏‮purposes‬‏ ‏‮should‬‏ ‏‮not‬‏ ‏‮include‬‏ ‏‮data‬‏ ‏‮elements‬‏, ‏‮such‬‏ ‏‮as‬‏ ‏‮the‬‏ ‏‮th‬‏ ‏‮or‬‏ ‏‮caption‬‏ ‏‮elements‬‏ ‏‮or‬‏ ‏‮the‬‏ ‏‮summary‬‏ ‏‮attribute‬‏, ‏‮because‬‏ ‏‮this‬‏ ‏‮can‬‏ ‏‮create‬‏ ‏‮a‬‏ ‏‮confusing‬‏ ‏‮experience‬‏ ‏‮for‬‏ ‏‮screen‬‏ ‏‮reader‬‏ ‏‮users‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/layout-table/)."}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "‏‮Presentational‬‏ `<table>` ‏‮elements‬‏ ‏‮do‬‏ ‏‮not‬‏ ‏‮avoid‬‏ ‏‮using‬‏ `<th>`, `<caption>` ‏‮or‬‏ ‏‮the‬‏ `[summary]` ‏‮attribute‬‏."}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "‏‮Presentational‬‏ `<table>` ‏‮elements‬‏ ‏‮avoid‬‏ ‏‮using‬‏ `<th>`, `<caption>` ‏‮or‬‏ ‏‮the‬‏ `[summary]` ‏‮attribute‬‏."}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "‏‮Link‬‏ ‏‮text‬‏ (‏‮and‬‏ ‏‮alternate‬‏ ‏‮text‬‏ ‏‮for‬‏ ‏‮images‬‏, ‏‮when‬‏ ‏‮used‬‏ ‏‮as‬‏ ‏‮links‬‏) ‏‮that‬‏ ‏‮is‬‏ ‏‮discernible‬‏, ‏‮unique‬‏, ‏‮and‬‏ ‏‮focusable‬‏ ‏‮improves‬‏ ‏‮the‬‏ ‏‮navigation‬‏ ‏‮experience‬‏ ‏‮for‬‏ ‏‮screen‬‏ ‏‮reader‬‏ ‏‮users‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/link-name/)."}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "‏‮Links‬‏ ‏‮do‬‏ ‏‮not‬‏ ‏‮have‬‏ ‏‮a‬‏ ‏‮discernible‬‏ ‏‮name‬‏"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "‏‮Links‬‏ ‏‮have‬‏ ‏‮a‬‏ ‏‮discernible‬‏ ‏‮name‬‏"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "‏‮Screen‬‏ ‏‮readers‬‏ ‏‮have‬‏ ‏‮a‬‏ ‏‮specific‬‏ ‏‮way‬‏ ‏‮of‬‏ ‏‮announcing‬‏ ‏‮lists‬‏. ‏‮Ensuring‬‏ ‏‮proper‬‏ ‏‮list‬‏ ‏‮structure‬‏ ‏‮aids‬‏ ‏‮screen‬‏ ‏‮reader‬‏ ‏‮output‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/list/)."}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "‏‮Lists‬‏ ‏‮do‬‏ ‏‮not‬‏ ‏‮contain‬‏ ‏‮only‬‏ `<li>` ‏‮elements‬‏ ‏‮and‬‏ ‏‮script‬‏ ‏‮supporting‬‏ ‏‮elements‬‏ (`<script>` ‏‮and‬‏ `<template>`)."}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "‏‮Lists‬‏ ‏‮contain‬‏ ‏‮only‬‏ `<li>` ‏‮elements‬‏ ‏‮and‬‏ ‏‮script‬‏ ‏‮supporting‬‏ ‏‮elements‬‏ (`<script>` ‏‮and‬‏ `<template>`)."}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "‏‮Screen‬‏ ‏‮readers‬‏ ‏‮require‬‏ ‏‮list‬‏ ‏‮items‬‏ (`<li>`) ‏‮to‬‏ ‏‮be‬‏ ‏‮contained‬‏ ‏‮within‬‏ ‏‮a‬‏ ‏‮parent‬‏ `<ul>` ‏‮or‬‏ `<ol>` ‏‮to‬‏ ‏‮be‬‏ ‏‮announced‬‏ ‏‮properly‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/listitem/)."}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "‏‮List‬‏ ‏‮items‬‏ (`<li>`) ‏‮are‬‏ ‏‮not‬‏ ‏‮contained‬‏ ‏‮within‬‏ `<ul>` ‏‮or‬‏ `<ol>` ‏‮parent‬‏ ‏‮elements‬‏."}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "‏‮List‬‏ ‏‮items‬‏ (`<li>`) ‏‮are‬‏ ‏‮contained‬‏ ‏‮within‬‏ `<ul>` ‏‮or‬‏ `<ol>` ‏‮parent‬‏ ‏‮elements‬‏"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "‏‮Users‬‏ ‏‮do‬‏ ‏‮not‬‏ ‏‮expect‬‏ ‏‮a‬‏ ‏‮page‬‏ ‏‮to‬‏ ‏‮refresh‬‏ ‏‮automatically‬‏, ‏‮and‬‏ ‏‮doing‬‏ ‏‮so‬‏ ‏‮will‬‏ ‏‮move‬‏ ‏‮focus‬‏ ‏‮back‬‏ ‏‮to‬‏ ‏‮the‬‏ ‏‮top‬‏ ‏‮of‬‏ ‏‮the‬‏ ‏‮page‬‏. ‏‮This‬‏ ‏‮may‬‏ ‏‮create‬‏ ‏‮a‬‏ ‏‮frustrating‬‏ ‏‮or‬‏ ‏‮confusing‬‏ ‏‮experience‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/meta-refresh/)."}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "‏‮The‬‏ ‏‮document‬‏ ‏‮uses‬‏ `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "‏‮The‬‏ ‏‮document‬‏ ‏‮does‬‏ ‏‮not‬‏ ‏‮use‬‏ `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "‏‮Disabling‬‏ ‏‮zooming‬‏ ‏‮is‬‏ ‏‮problematic‬‏ ‏‮for‬‏ ‏‮users‬‏ ‏‮with‬‏ ‏‮low‬‏ ‏‮vision‬‏ ‏‮who‬‏ ‏‮rely‬‏ ‏‮on‬‏ ‏‮screen‬‏ ‏‮magnification‬‏ ‏‮to‬‏ ‏‮properly‬‏ ‏‮see‬‏ ‏‮the‬‏ ‏‮contents‬‏ ‏‮of‬‏ ‏‮a‬‏ ‏‮web‬‏ ‏‮page‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/meta-viewport/)."}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` ‏‮is‬‏ ‏‮used‬‏ ‏‮in‬‏ ‏‮the‬‏ `<meta name=\"viewport\">` ‏‮element‬‏ ‏‮or‬‏ ‏‮the‬‏ `[maximum-scale]` ‏‮attribute‬‏ ‏‮is‬‏ ‏‮less‬‏ ‏‮than‬‏ 5."}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` ‏‮is‬‏ ‏‮not‬‏ ‏‮used‬‏ ‏‮in‬‏ ‏‮the‬‏ `<meta name=\"viewport\">` ‏‮element‬‏ ‏‮and‬‏ ‏‮the‬‏ `[maximum-scale]` ‏‮attribute‬‏ ‏‮is‬‏ ‏‮not‬‏ ‏‮less‬‏ ‏‮than‬‏ 5."}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "‏‮Screen‬‏ ‏‮readers‬‏ ‏‮cannot‬‏ ‏‮translate‬‏ ‏‮non‬‏-‏‮text‬‏ ‏‮content‬‏. ‏‮Adding‬‏ ‏‮alt‬‏ ‏‮text‬‏ ‏‮to‬‏ `<object>` ‏‮elements‬‏ ‏‮helps‬‏ ‏‮screen‬‏ ‏‮readers‬‏ ‏‮convey‬‏ ‏‮meaning‬‏ ‏‮to‬‏ ‏‮users‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/object-alt/)."}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>` ‏‮elements‬‏ ‏‮do‬‏ ‏‮not‬‏ ‏‮have‬‏ `[alt]` ‏‮text‬‏"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "`<object>` ‏‮elements‬‏ ‏‮have‬‏ `[alt]` ‏‮text‬‏"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "‏‮A‬‏ ‏‮value‬‏ ‏‮greater‬‏ ‏‮than‬‏ 0 ‏‮implies‬‏ ‏‮an‬‏ ‏‮explicit‬‏ ‏‮navigation‬‏ ‏‮ordering‬‏. ‏‮Although‬‏ ‏‮technically‬‏ ‏‮valid‬‏, ‏‮this‬‏ ‏‮often‬‏ ‏‮creates‬‏ ‏‮frustrating‬‏ ‏‮experiences‬‏ ‏‮for‬‏ ‏‮users‬‏ ‏‮who‬‏ ‏‮rely‬‏ ‏‮on‬‏ ‏‮assistive‬‏ ‏‮technologies‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/tabindex/)."}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "‏‮Some‬‏ ‏‮elements‬‏ ‏‮have‬‏ ‏‮a‬‏ `[tabindex]` ‏‮value‬‏ ‏‮greater‬‏ ‏‮than‬‏ 0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "‏‮No‬‏ ‏‮element‬‏ ‏‮has‬‏ ‏‮a‬‏ `[tabindex]` ‏‮value‬‏ ‏‮greater‬‏ ‏‮than‬‏ 0"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "‏‮Screen‬‏ ‏‮readers‬‏ ‏‮have‬‏ ‏‮features‬‏ ‏‮to‬‏ ‏‮make‬‏ ‏‮navigating‬‏ ‏‮tables‬‏ ‏‮easier‬‏. ‏‮Ensuring‬‏ `<td>` ‏‮cells‬‏ ‏‮using‬‏ ‏‮the‬‏ `[headers]` ‏‮attribute‬‏ ‏‮only‬‏ ‏‮refer‬‏ ‏‮to‬‏ ‏‮other‬‏ ‏‮cells‬‏ ‏‮in‬‏ ‏‮the‬‏ ‏‮same‬‏ ‏‮table‬‏ ‏‮may‬‏ ‏‮improve‬‏ ‏‮the‬‏ ‏‮experience‬‏ ‏‮for‬‏ ‏‮screen‬‏ ‏‮reader‬‏ ‏‮users‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/td-headers-attr/)."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "‏‮Cells‬‏ ‏‮in‬‏ ‏‮a‬‏ `<table>` ‏‮element‬‏ ‏‮that‬‏ ‏‮use‬‏ ‏‮the‬‏ `[headers]` ‏‮attribute‬‏ ‏‮refer‬‏ ‏‮to‬‏ ‏‮an‬‏ ‏‮element‬‏ `id` ‏‮not‬‏ ‏‮found‬‏ ‏‮within‬‏ ‏‮the‬‏ ‏‮same‬‏ ‏‮table‬‏."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "‏‮Cells‬‏ ‏‮in‬‏ ‏‮a‬‏ `<table>` ‏‮element‬‏ ‏‮that‬‏ ‏‮use‬‏ ‏‮the‬‏ `[headers]` ‏‮attribute‬‏ ‏‮refer‬‏ ‏‮to‬‏ ‏‮table‬‏ ‏‮cells‬‏ ‏‮within‬‏ ‏‮the‬‏ ‏‮same‬‏ ‏‮table‬‏."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "‏‮Screen‬‏ ‏‮readers‬‏ ‏‮have‬‏ ‏‮features‬‏ ‏‮to‬‏ ‏‮make‬‏ ‏‮navigating‬‏ ‏‮tables‬‏ ‏‮easier‬‏. ‏‮Ensuring‬‏ ‏‮table‬‏ ‏‮headers‬‏ ‏‮always‬‏ ‏‮refer‬‏ ‏‮to‬‏ ‏‮some‬‏ ‏‮set‬‏ ‏‮of‬‏ ‏‮cells‬‏ ‏‮may‬‏ ‏‮improve‬‏ ‏‮the‬‏ ‏‮experience‬‏ ‏‮for‬‏ ‏‮screen‬‏ ‏‮reader‬‏ ‏‮users‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/th-has-data-cells/)."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "`<th>` ‏‮elements‬‏ ‏‮and‬‏ ‏‮elements‬‏ ‏‮with‬‏ `[role=\"columnheader\"/\"rowheader\"]` ‏‮do‬‏ ‏‮not‬‏ ‏‮have‬‏ ‏‮data‬‏ ‏‮cells‬‏ ‏‮they‬‏ ‏‮describe‬‏."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "`<th>` ‏‮elements‬‏ ‏‮and‬‏ ‏‮elements‬‏ ‏‮with‬‏ `[role=\"columnheader\"/\"rowheader\"]` ‏‮have‬‏ ‏‮data‬‏ ‏‮cells‬‏ ‏‮they‬‏ ‏‮describe‬‏."}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "‏‮Specifying‬‏ ‏‮a‬‏ ‏‮valid‬‏ [‏‮BCP‬‏ 47 ‏‮language‬‏](https://www.w3.org/International/questions/qa-choosing-language-tags#question) ‏‮on‬‏ ‏‮elements‬‏ ‏‮helps‬‏ ‏‮ensure‬‏ ‏‮that‬‏ ‏‮text‬‏ ‏‮is‬‏ ‏‮pronounced‬‏ ‏‮correctly‬‏ ‏‮by‬‏ ‏‮a‬‏ ‏‮screen‬‏ ‏‮reader‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/valid-lang/)."}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "`[lang]` ‏‮attributes‬‏ ‏‮do‬‏ ‏‮not‬‏ ‏‮have‬‏ ‏‮a‬‏ ‏‮valid‬‏ ‏‮value‬‏"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "`[lang]` ‏‮attributes‬‏ ‏‮have‬‏ ‏‮a‬‏ ‏‮valid‬‏ ‏‮value‬‏"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "‏‮When‬‏ ‏‮a‬‏ ‏‮video‬‏ ‏‮provides‬‏ ‏‮a‬‏ ‏‮caption‬‏ ‏‮it‬‏ ‏‮is‬‏ ‏‮easier‬‏ ‏‮for‬‏ ‏‮deaf‬‏ ‏‮and‬‏ ‏‮hearing‬‏ ‏‮impaired‬‏ ‏‮users‬‏ ‏‮to‬‏ ‏‮access‬‏ ‏‮its‬‏ ‏‮information‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/video-caption/)."}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "`<video>` ‏‮elements‬‏ ‏‮do‬‏ ‏‮not‬‏ ‏‮contain‬‏ ‏‮a‬‏ `<track>` ‏‮element‬‏ ‏‮with‬‏ `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "`<video>` ‏‮elements‬‏ ‏‮contain‬‏ ‏‮a‬‏ `<track>` ‏‮element‬‏ ‏‮with‬‏ `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "‏‮Audio‬‏ ‏‮descriptions‬‏ ‏‮provide‬‏ ‏‮relevant‬‏ ‏‮information‬‏ ‏‮for‬‏ ‏‮videos‬‏ ‏‮that‬‏ ‏‮dialogue‬‏ ‏‮cannot‬‏, ‏‮such‬‏ ‏‮as‬‏ ‏‮facial‬‏ ‏‮expressions‬‏ ‏‮and‬‏ ‏‮scenes‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/video-description/)."}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "`<video>` ‏‮elements‬‏ ‏‮do‬‏ ‏‮not‬‏ ‏‮contain‬‏ ‏‮a‬‏ `<track>` ‏‮element‬‏ ‏‮with‬‏ `[kind=\"description\"]`."}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "`<video>` ‏‮elements‬‏ ‏‮contain‬‏ ‏‮a‬‏ `<track>` ‏‮element‬‏ ‏‮with‬‏ `[kind=\"description\"]`"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "‏‮For‬‏ ‏‮ideal‬‏ ‏‮appearance‬‏ ‏‮on‬‏ ‏‮iOS‬‏ ‏‮when‬‏ ‏‮users‬‏ ‏‮add‬‏ ‏‮a‬‏ ‏‮progressive‬‏ ‏‮web‬‏ ‏‮app‬‏ ‏‮to‬‏ ‏‮the‬‏ ‏‮home‬‏ ‏‮screen‬‏, ‏‮define‬‏ ‏‮an‬‏ `apple-touch-icon`. ‏‮It‬‏ ‏‮must‬‏ ‏‮point‬‏ ‏‮to‬‏ ‏‮a‬‏ ‏‮non‬‏-‏‮transparent‬‏ 192‏‮px‬‏ (‏‮or‬‏ 180‏‮px‬‏) ‏‮square‬‏ ‏‮PNG‬‏. [‏‮Learn‬‏ ‏‮More‬‏](https://web.dev/apple-touch-icon/)."}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "‏‮Does‬‏ ‏‮not‬‏ ‏‮provide‬‏ ‏‮a‬‏ ‏‮valid‬‏ `apple-touch-icon`"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "`apple-touch-icon-precomposed` ‏‮is‬‏ ‏‮out‬‏ ‏‮of‬‏ ‏‮date‬‏; `apple-touch-icon` ‏‮is‬‏ ‏‮preferred‬‏."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "‏‮Provides‬‏ ‏‮a‬‏ ‏‮valid‬‏ `apple-touch-icon`"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "‏‮Chrome‬‏ ‏‮extensions‬‏ ‏‮negatively‬‏ ‏‮affected‬‏ ‏‮this‬‏ ‏‮page‬‏'‏‮s‬‏ ‏‮load‬‏ ‏‮performance‬‏. ‏‮Try‬‏ ‏‮auditing‬‏ ‏‮the‬‏ ‏‮page‬‏ ‏‮in‬‏ ‏‮incognito‬‏ ‏‮mode‬‏ ‏‮or‬‏ ‏‮from‬‏ ‏‮a‬‏ ‏‮Chrome‬‏ ‏‮profile‬‏ ‏‮without‬‏ ‏‮extensions‬‏."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "‏‮Script‬‏ ‏‮Evaluation‬‏"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "‏‮<PERSON><PERSON><PERSON>‬‏ ‏‮Parse‬‏"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "‏‮Total‬‏ ‏‮CPU‬‏ ‏‮Time‬‏"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "‏‮Consider‬‏ ‏‮reducing‬‏ ‏‮the‬‏ ‏‮time‬‏ ‏‮spent‬‏ ‏‮parsing‬‏, ‏‮compiling‬‏, ‏‮and‬‏ ‏‮executing‬‏ ‏‮JS‬‏. ‏‮You‬‏ ‏‮may‬‏ ‏‮find‬‏ ‏‮delivering‬‏ ‏‮smaller‬‏ ‏‮JS‬‏ ‏‮payloads‬‏ ‏‮helps‬‏ ‏‮with‬‏ ‏‮this‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/bootup-time)."}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "‏‮Reduce‬‏ ‏‮JavaScript‬‏ ‏‮execution‬‏ ‏‮time‬‏"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "‏‮JavaScript‬‏ ‏‮execution‬‏ ‏‮time‬‏"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "‏‮Large‬‏ ‏‮GIFs‬‏ ‏‮are‬‏ ‏‮inefficient‬‏ ‏‮for‬‏ ‏‮delivering‬‏ ‏‮animated‬‏ ‏‮content‬‏. ‏‮Consider‬‏ ‏‮using‬‏ ‏‮MPEG‬‏4/‏‮WebM‬‏ ‏‮videos‬‏ ‏‮for‬‏ ‏‮animations‬‏ ‏‮and‬‏ ‏‮PNG‬‏/‏‮WebP‬‏ ‏‮for‬‏ ‏‮static‬‏ ‏‮images‬‏ ‏‮instead‬‏ ‏‮of‬‏ ‏‮GIF‬‏ ‏‮to‬‏ ‏‮save‬‏ ‏‮network‬‏ ‏‮bytes‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "‏‮Use‬‏ ‏‮video‬‏ ‏‮formats‬‏ ‏‮for‬‏ ‏‮animated‬‏ ‏‮content‬‏"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "‏‮Consider‬‏ ‏‮lazy‬‏-‏‮loading‬‏ ‏‮offscreen‬‏ ‏‮and‬‏ ‏‮hidden‬‏ ‏‮images‬‏ ‏‮after‬‏ ‏‮all‬‏ ‏‮critical‬‏ ‏‮resources‬‏ ‏‮have‬‏ ‏‮finished‬‏ ‏‮loading‬‏ ‏‮to‬‏ ‏‮lower‬‏ ‏‮time‬‏ ‏‮to‬‏ ‏‮interactive‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/offscreen-images)."}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "‏‮Defer‬‏ ‏‮offscreen‬‏ ‏‮images‬‏"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "‏‮Resources‬‏ ‏‮are‬‏ ‏‮blocking‬‏ ‏‮the‬‏ ‏‮first‬‏ ‏‮paint‬‏ ‏‮of‬‏ ‏‮your‬‏ ‏‮page‬‏. ‏‮Consider‬‏ ‏‮delivering‬‏ ‏‮critical‬‏ ‏‮JS‬‏/‏‮CSS‬‏ ‏‮inline‬‏ ‏‮and‬‏ ‏‮deferring‬‏ ‏‮all‬‏ ‏‮non‬‏-‏‮critical‬‏ ‏‮JS‬‏/‏‮styles‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/render-blocking-resources)."}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "‏‮Eliminate‬‏ ‏‮render‬‏-‏‮blocking‬‏ ‏‮resources‬‏"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "‏‮Large‬‏ ‏‮network‬‏ ‏‮payloads‬‏ ‏‮cost‬‏ ‏‮users‬‏ ‏‮real‬‏ ‏‮money‬‏ ‏‮and‬‏ ‏‮are‬‏ ‏‮highly‬‏ ‏‮correlated‬‏ ‏‮with‬‏ ‏‮long‬‏ ‏‮load‬‏ ‏‮times‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/total-byte-weight)."}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "‏‮Total‬‏ ‏‮size‬‏ ‏‮was‬‏ {totalBytes, number, bytes} ‏‮KB‬‏"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "‏‮Avoid‬‏ ‏‮enormous‬‏ ‏‮network‬‏ ‏‮payloads‬‏"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "‏‮Avoids‬‏ ‏‮enormous‬‏ ‏‮network‬‏ ‏‮payloads‬‏"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "‏‮Minifying‬‏ ‏‮CSS‬‏ ‏‮files‬‏ ‏‮can‬‏ ‏‮reduce‬‏ ‏‮network‬‏ ‏‮payload‬‏ ‏‮sizes‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/unminified-css)."}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "‏‮Minify‬‏ ‏‮CSS‬‏"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "‏‮Minifying‬‏ ‏‮JavaScript‬‏ ‏‮files‬‏ ‏‮can‬‏ ‏‮reduce‬‏ ‏‮payload‬‏ ‏‮sizes‬‏ ‏‮and‬‏ ‏‮script‬‏ ‏‮parse‬‏ ‏‮time‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/unminified-javascript)."}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "‏‮Minify‬‏ ‏‮JavaScript‬‏"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "‏‮Remove‬‏ ‏‮dead‬‏ ‏‮rules‬‏ ‏‮from‬‏ ‏‮stylesheets‬‏ ‏‮and‬‏ ‏‮defer‬‏ ‏‮the‬‏ ‏‮loading‬‏ ‏‮of‬‏ ‏‮CSS‬‏ ‏‮not‬‏ ‏‮used‬‏ ‏‮for‬‏ ‏‮above‬‏-‏‮the‬‏-‏‮fold‬‏ ‏‮content‬‏ ‏‮to‬‏ ‏‮reduce‬‏ ‏‮unnecessary‬‏ ‏‮bytes‬‏ ‏‮consumed‬‏ ‏‮by‬‏ ‏‮network‬‏ ‏‮activity‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/unused-css-rules)."}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "‏‮Remove‬‏ ‏‮unused‬‏ ‏‮CSS‬‏"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "‏‮Remove‬‏ ‏‮unused‬‏ ‏‮JavaScript‬‏ ‏‮to‬‏ ‏‮reduce‬‏ ‏‮bytes‬‏ ‏‮consumed‬‏ ‏‮by‬‏ ‏‮network‬‏ ‏‮activity‬‏."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "‏‮Remove‬‏ ‏‮unused‬‏ ‏‮JavaScript‬‏"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "‏‮A‬‏ ‏‮long‬‏ ‏‮cache‬‏ ‏‮lifetime‬‏ ‏‮can‬‏ ‏‮speed‬‏ ‏‮up‬‏ ‏‮repeat‬‏ ‏‮visits‬‏ ‏‮to‬‏ ‏‮your‬‏ ‏‮page‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/uses-long-cache-ttl)."}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 ‏‮resource‬‏ ‏‮found‬‏}zero{# ‏‮resources‬‏ ‏‮found‬‏}two{# ‏‮resources‬‏ ‏‮found‬‏}few{# ‏‮resources‬‏ ‏‮found‬‏}many{# ‏‮resources‬‏ ‏‮found‬‏}other{# ‏‮resources‬‏ ‏‮found‬‏}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "‏‮Serve‬‏ ‏‮static‬‏ ‏‮assets‬‏ ‏‮with‬‏ ‏‮an‬‏ ‏‮efficient‬‏ ‏‮cache‬‏ ‏‮policy‬‏"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "‏‮Uses‬‏ ‏‮efficient‬‏ ‏‮cache‬‏ ‏‮policy‬‏ ‏‮on‬‏ ‏‮static‬‏ ‏‮assets‬‏"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "‏‮Optimized‬‏ ‏‮images‬‏ ‏‮load‬‏ ‏‮faster‬‏ ‏‮and‬‏ ‏‮consume‬‏ ‏‮less‬‏ ‏‮cellular‬‏ ‏‮data‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/uses-optimized-images)."}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "‏‮Efficiently‬‏ ‏‮encode‬‏ ‏‮images‬‏"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "‏‮Serve‬‏ ‏‮images‬‏ ‏‮that‬‏ ‏‮are‬‏ ‏‮appropriately‬‏-‏‮sized‬‏ ‏‮to‬‏ ‏‮save‬‏ ‏‮cellular‬‏ ‏‮data‬‏ ‏‮and‬‏ ‏‮improve‬‏ ‏‮load‬‏ ‏‮time‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/uses-responsive-images)."}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "‏‮Properly‬‏ ‏‮size‬‏ ‏‮images‬‏"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "‏‮Text‬‏-‏‮based‬‏ ‏‮resources‬‏ ‏‮should‬‏ ‏‮be‬‏ ‏‮served‬‏ ‏‮with‬‏ ‏‮compression‬‏ (‏‮gzip‬‏, ‏‮deflate‬‏ ‏‮or‬‏ ‏‮brotli‬‏) ‏‮to‬‏ ‏‮minimize‬‏ ‏‮total‬‏ ‏‮network‬‏ ‏‮bytes‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/uses-text-compression)."}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "‏‮Enable‬‏ ‏‮text‬‏ ‏‮compression‬‏"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "‏‮Image‬‏ ‏‮formats‬‏ ‏‮like‬‏ ‏‮JPEG‬‏ 2000, ‏‮JPEG‬‏ ‏‮XR‬‏, ‏‮and‬‏ ‏‮WebP‬‏ ‏‮often‬‏ ‏‮provide‬‏ ‏‮better‬‏ ‏‮compression‬‏ ‏‮than‬‏ ‏‮PNG‬‏ ‏‮or‬‏ ‏‮JPEG‬‏, ‏‮which‬‏ ‏‮means‬‏ ‏‮faster‬‏ ‏‮downloads‬‏ ‏‮and‬‏ ‏‮less‬‏ ‏‮data‬‏ ‏‮consumption‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/uses-webp-images)."}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "‏‮Serve‬‏ ‏‮images‬‏ ‏‮in‬‏ ‏‮next‬‏-‏‮gen‬‏ ‏‮formats‬‏"}, "lighthouse-core/audits/content-width.js | description": {"message": "‏‮If‬‏ ‏‮the‬‏ ‏‮width‬‏ ‏‮of‬‏ ‏‮your‬‏ ‏‮app‬‏'‏‮s‬‏ ‏‮content‬‏ ‏‮doesn‬‏'‏‮t‬‏ ‏‮match‬‏ ‏‮the‬‏ ‏‮width‬‏ ‏‮of‬‏ ‏‮the‬‏ ‏‮viewport‬‏, ‏‮your‬‏ ‏‮app‬‏ ‏‮might‬‏ ‏‮not‬‏ ‏‮be‬‏ ‏‮optimized‬‏ ‏‮for‬‏ ‏‮mobile‬‏ ‏‮screens‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/content-width)."}, "lighthouse-core/audits/content-width.js | explanation": {"message": "‏‮The‬‏ ‏‮viewport‬‏ ‏‮size‬‏ ‏‮of‬‏ {innerWidth}‏‮px‬‏ ‏‮does‬‏ ‏‮not‬‏ ‏‮match‬‏ ‏‮the‬‏ ‏‮window‬‏ ‏‮size‬‏ ‏‮of‬‏ {outerWidth}‏‮px‬‏."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "‏‮Content‬‏ ‏‮is‬‏ ‏‮not‬‏ ‏‮sized‬‏ ‏‮correctly‬‏ ‏‮for‬‏ ‏‮the‬‏ ‏‮viewport‬‏"}, "lighthouse-core/audits/content-width.js | title": {"message": "‏‮Content‬‏ ‏‮is‬‏ ‏‮sized‬‏ ‏‮correctly‬‏ ‏‮for‬‏ ‏‮the‬‏ ‏‮viewport‬‏"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "‏‮The‬‏ ‏‮Critical‬‏ ‏‮Request‬‏ ‏‮Chains‬‏ ‏‮below‬‏ ‏‮show‬‏ ‏‮you‬‏ ‏‮what‬‏ ‏‮resources‬‏ ‏‮are‬‏ ‏‮loaded‬‏ ‏‮with‬‏ ‏‮a‬‏ ‏‮high‬‏ ‏‮priority‬‏. ‏‮Consider‬‏ ‏‮reducing‬‏ ‏‮the‬‏ ‏‮length‬‏ ‏‮of‬‏ ‏‮chains‬‏, ‏‮reducing‬‏ ‏‮the‬‏ ‏‮download‬‏ ‏‮size‬‏ ‏‮of‬‏ ‏‮resources‬‏, ‏‮or‬‏ ‏‮deferring‬‏ ‏‮the‬‏ ‏‮download‬‏ ‏‮of‬‏ ‏‮unnecessary‬‏ ‏‮resources‬‏ ‏‮to‬‏ ‏‮improve‬‏ ‏‮page‬‏ ‏‮load‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/critical-request-chains)."}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 ‏‮chain‬‏ ‏‮found‬‏}zero{# ‏‮chains‬‏ ‏‮found‬‏}two{# ‏‮chains‬‏ ‏‮found‬‏}few{# ‏‮chains‬‏ ‏‮found‬‏}many{# ‏‮chains‬‏ ‏‮found‬‏}other{# ‏‮chains‬‏ ‏‮found‬‏}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "‏‮Minimize‬‏ ‏‮Critical‬‏ ‏‮Requests‬‏ ‏‮Depth‬‏"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "‏‮Deprecation‬‏ / ‏‮Warning‬‏"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "‏‮Line‬‏"}, "lighthouse-core/audits/deprecations.js | description": {"message": "‏‮Deprecated‬‏ ‏‮APIs‬‏ ‏‮will‬‏ ‏‮eventually‬‏ ‏‮be‬‏ ‏‮removed‬‏ ‏‮from‬‏ ‏‮the‬‏ ‏‮browser‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/deprecations)."}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 ‏‮warning‬‏ ‏‮found‬‏}zero{# ‏‮warnings‬‏ ‏‮found‬‏}two{# ‏‮warnings‬‏ ‏‮found‬‏}few{# ‏‮warnings‬‏ ‏‮found‬‏}many{# ‏‮warnings‬‏ ‏‮found‬‏}other{# ‏‮warnings‬‏ ‏‮found‬‏}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "‏‮Uses‬‏ ‏‮deprecated‬‏ ‏‮APIs‬‏"}, "lighthouse-core/audits/deprecations.js | title": {"message": "‏‮Avoids‬‏ ‏‮deprecated‬‏ ‏‮APIs‬‏"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "‏‮Application‬‏ ‏‮Cache‬‏ ‏‮is‬‏ ‏‮deprecated‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/appcache-manifest)."}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "‏‮Found‬‏ \"{AppCacheManifest}\""}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "‏‮Uses‬‏ ‏‮Application‬‏ ‏‮Cache‬‏"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "‏‮Avoids‬‏ ‏‮Application‬‏ ‏‮Cache‬‏"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "‏‮Specifying‬‏ ‏‮a‬‏ ‏‮doctype‬‏ ‏‮prevents‬‏ ‏‮the‬‏ ‏‮browser‬‏ ‏‮from‬‏ ‏‮switching‬‏ ‏‮to‬‏ ‏‮quirks‬‏-‏‮mode‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/doctype)."}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "‏‮Doctype‬‏ ‏‮name‬‏ ‏‮must‬‏ ‏‮be‬‏ ‏‮the‬‏ ‏‮lowercase‬‏ ‏‮string‬‏ `html`"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "‏‮Document‬‏ ‏‮must‬‏ ‏‮contain‬‏ ‏‮a‬‏ ‏‮doctype‬‏"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "‏‮Expected‬‏ ‏‮publicId‬‏ ‏‮to‬‏ ‏‮be‬‏ ‏‮an‬‏ ‏‮empty‬‏ ‏‮string‬‏"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "‏‮Expected‬‏ ‏‮systemId‬‏ ‏‮to‬‏ ‏‮be‬‏ ‏‮an‬‏ ‏‮empty‬‏ ‏‮string‬‏"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "‏‮<PERSON>‬‏ ‏‮lacks‬‏ ‏‮the‬‏ ‏‮HTML‬‏ ‏‮doctype‬‏, ‏‮thus‬‏ ‏‮triggering‬‏ ‏‮quirks‬‏-‏‮mode‬‏"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "‏‮<PERSON>‬‏ ‏‮has‬‏ ‏‮the‬‏ ‏‮HTML‬‏ ‏‮doctype‬‏"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "‏‮Element‬‏"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "‏‮Statistic‬‏"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "‏‮Value‬‏"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "‏‮Browser‬‏ ‏‮engineers‬‏ ‏‮recommend‬‏ ‏‮pages‬‏ ‏‮contain‬‏ ‏‮fewer‬‏ ‏‮than‬‏ ~1,500 ‏‮DOM‬‏ ‏‮elements‬‏. ‏‮The‬‏ ‏‮sweet‬‏ ‏‮spot‬‏ ‏‮is‬‏ ‏‮a‬‏ ‏‮tree‬‏ ‏‮depth‬‏ < 32 ‏‮elements‬‏ ‏‮and‬‏ ‏‮fewer‬‏ ‏‮than‬‏ 60 ‏‮children‬‏/‏‮parent‬‏ ‏‮element‬‏. ‏‮A‬‏ ‏‮large‬‏ ‏‮DOM‬‏ ‏‮can‬‏ ‏‮increase‬‏ ‏‮memory‬‏ ‏‮usage‬‏, ‏‮cause‬‏ ‏‮longer‬‏ [‏‮style‬‏ ‏‮calculations‬‏](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations), ‏‮and‬‏ ‏‮produce‬‏ ‏‮costly‬‏ [‏‮layout‬‏ ‏‮reflows‬‏](https://developers.google.com/speed/articles/reflow). [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/dom-size)."}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 ‏‮element‬‏}zero{# ‏‮elements‬‏}two{# ‏‮elements‬‏}few{# ‏‮elements‬‏}many{# ‏‮elements‬‏}other{# ‏‮elements‬‏}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "‏‮Avoid‬‏ ‏‮an‬‏ ‏‮excessive‬‏ ‏‮DOM‬‏ ‏‮size‬‏"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "‏‮Maximum‬‏ ‏‮DOM‬‏ ‏‮Depth‬‏"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "‏‮Total‬‏ ‏‮DOM‬‏ ‏‮Elements‬‏"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "‏‮Maximum‬‏ ‏‮Child‬‏ ‏‮Elements‬‏"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "‏‮Avoids‬‏ ‏‮an‬‏ ‏‮excessive‬‏ ‏‮DOM‬‏ ‏‮size‬‏"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "‏‮<PERSON><PERSON>‬‏"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "‏‮Target‬‏"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "‏‮Add‬‏ `rel=\"noopener\"` ‏‮or‬‏ `rel=\"noreferrer\"` ‏‮to‬‏ ‏‮any‬‏ ‏‮external‬‏ ‏‮links‬‏ ‏‮to‬‏ ‏‮improve‬‏ ‏‮performance‬‏ ‏‮and‬‏ ‏‮prevent‬‏ ‏‮security‬‏ ‏‮vulnerabilities‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/external-anchors-use-rel-noopener)."}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "‏‮Links‬‏ ‏‮to‬‏ ‏‮cross‬‏-‏‮origin‬‏ ‏‮destinations‬‏ ‏‮are‬‏ ‏‮unsafe‬‏"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "‏‮Links‬‏ ‏‮to‬‏ ‏‮cross‬‏-‏‮origin‬‏ ‏‮destinations‬‏ ‏‮are‬‏ ‏‮safe‬‏"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "‏‮Unable‬‏ ‏‮to‬‏ ‏‮determine‬‏ ‏‮the‬‏ ‏‮destination‬‏ ‏‮for‬‏ ‏‮anchor‬‏ ({anchorHTML}). ‏‮If‬‏ ‏‮not‬‏ ‏‮used‬‏ ‏‮as‬‏ ‏‮a‬‏ ‏‮hyperlink‬‏, ‏‮consider‬‏ ‏‮removing‬‏ ‏‮target‬‏=_‏‮blank‬‏."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "‏‮Users‬‏ ‏‮are‬‏ ‏‮mistrustful‬‏ ‏‮of‬‏ ‏‮or‬‏ ‏‮confused‬‏ ‏‮by‬‏ ‏‮sites‬‏ ‏‮that‬‏ ‏‮request‬‏ ‏‮their‬‏ ‏‮location‬‏ ‏‮without‬‏ ‏‮context‬‏. ‏‮Consider‬‏ ‏‮tying‬‏ ‏‮the‬‏ ‏‮request‬‏ ‏‮to‬‏ ‏‮a‬‏ ‏‮user‬‏ ‏‮action‬‏ ‏‮instead‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/geolocation-on-start)."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "‏‮Requests‬‏ ‏‮the‬‏ ‏‮geolocation‬‏ ‏‮permission‬‏ ‏‮on‬‏ ‏‮page‬‏ ‏‮load‬‏"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "‏‮Avoids‬‏ ‏‮requesting‬‏ ‏‮the‬‏ ‏‮geolocation‬‏ ‏‮permission‬‏ ‏‮on‬‏ ‏‮page‬‏ ‏‮load‬‏"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "‏‮Version‬‏"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "‏‮All‬‏ ‏‮front‬‏-‏‮end‬‏ ‏‮JavaScript‬‏ ‏‮libraries‬‏ ‏‮detected‬‏ ‏‮on‬‏ ‏‮the‬‏ ‏‮page‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/js-libraries)."}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "‏‮Detected‬‏ ‏‮JavaScript‬‏ ‏‮libraries‬‏"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "‏‮For‬‏ ‏‮users‬‏ ‏‮on‬‏ ‏‮slow‬‏ ‏‮connections‬‏, ‏‮external‬‏ ‏‮scripts‬‏ ‏‮dynamically‬‏ ‏‮injected‬‏ ‏‮via‬‏ `document.write()` ‏‮can‬‏ ‏‮delay‬‏ ‏‮page‬‏ ‏‮load‬‏ ‏‮by‬‏ ‏‮tens‬‏ ‏‮of‬‏ ‏‮seconds‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/no-document-write)."}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "‏‮Uses‬‏ `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "‏‮Avoids‬‏ `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "‏‮Highest‬‏ ‏‮Severity‬‏"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "‏‮Library‬‏ ‏‮Version‬‏"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "‏‮Vulnerability‬‏ ‏‮Count‬‏"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "‏‮Some‬‏ ‏‮third‬‏-‏‮party‬‏ ‏‮scripts‬‏ ‏‮may‬‏ ‏‮contain‬‏ ‏‮known‬‏ ‏‮security‬‏ ‏‮vulnerabilities‬‏ ‏‮that‬‏ ‏‮are‬‏ ‏‮easily‬‏ ‏‮identified‬‏ ‏‮and‬‏ ‏‮exploited‬‏ ‏‮by‬‏ ‏‮attackers‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/no-vulnerable-libraries)."}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{1 ‏‮vulnerability‬‏ ‏‮detected‬‏}zero{# ‏‮vulnerabilities‬‏ ‏‮detected‬‏}two{# ‏‮vulnerabilities‬‏ ‏‮detected‬‏}few{# ‏‮vulnerabilities‬‏ ‏‮detected‬‏}many{# ‏‮vulnerabilities‬‏ ‏‮detected‬‏}other{# ‏‮vulnerabilities‬‏ ‏‮detected‬‏}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "‏‮Includes‬‏ ‏‮front‬‏-‏‮end‬‏ ‏‮JavaScript‬‏ ‏‮libraries‬‏ ‏‮with‬‏ ‏‮known‬‏ ‏‮security‬‏ ‏‮vulnerabilities‬‏"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "‏‮High‬‏"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "‏‮Low‬‏"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "‏‮Medium‬‏"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "‏‮Avoids‬‏ ‏‮front‬‏-‏‮end‬‏ ‏‮JavaScript‬‏ ‏‮libraries‬‏ ‏‮with‬‏ ‏‮known‬‏ ‏‮security‬‏ ‏‮vulnerabilities‬‏"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "‏‮Users‬‏ ‏‮are‬‏ ‏‮mistrustful‬‏ ‏‮of‬‏ ‏‮or‬‏ ‏‮confused‬‏ ‏‮by‬‏ ‏‮sites‬‏ ‏‮that‬‏ ‏‮request‬‏ ‏‮to‬‏ ‏‮send‬‏ ‏‮notifications‬‏ ‏‮without‬‏ ‏‮context‬‏. ‏‮Consider‬‏ ‏‮tying‬‏ ‏‮the‬‏ ‏‮request‬‏ ‏‮to‬‏ ‏‮user‬‏ ‏‮gestures‬‏ ‏‮instead‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/notification-on-start)."}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "‏‮Requests‬‏ ‏‮the‬‏ ‏‮notification‬‏ ‏‮permission‬‏ ‏‮on‬‏ ‏‮page‬‏ ‏‮load‬‏"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "‏‮Avoids‬‏ ‏‮requesting‬‏ ‏‮the‬‏ ‏‮notification‬‏ ‏‮permission‬‏ ‏‮on‬‏ ‏‮page‬‏ ‏‮load‬‏"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "‏‮Failing‬‏ ‏‮Elements‬‏"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "‏‮Preventing‬‏ ‏‮password‬‏ ‏‮pasting‬‏ ‏‮undermines‬‏ ‏‮good‬‏ ‏‮security‬‏ ‏‮policy‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/password-inputs-can-be-pasted-into)."}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "‏‮Prevents‬‏ ‏‮users‬‏ ‏‮to‬‏ ‏‮paste‬‏ ‏‮into‬‏ ‏‮password‬‏ ‏‮fields‬‏"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "‏‮Allows‬‏ ‏‮users‬‏ ‏‮to‬‏ ‏‮paste‬‏ ‏‮into‬‏ ‏‮password‬‏ ‏‮fields‬‏"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "‏‮Protocol‬‏"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "‏‮HTTP‬‏/2 ‏‮offers‬‏ ‏‮many‬‏ ‏‮benefits‬‏ ‏‮over‬‏ ‏‮HTTP‬‏/1.1, ‏‮including‬‏ ‏‮binary‬‏ ‏‮headers‬‏, ‏‮multiplexing‬‏, ‏‮and‬‏ ‏‮server‬‏ ‏‮push‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/uses-http2)."}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 ‏‮request‬‏ ‏‮not‬‏ ‏‮served‬‏ ‏‮via‬‏ ‏‮HTTP‬‏/2}zero{# ‏‮requests‬‏ ‏‮not‬‏ ‏‮served‬‏ ‏‮via‬‏ ‏‮HTTP‬‏/2}two{# ‏‮requests‬‏ ‏‮not‬‏ ‏‮served‬‏ ‏‮via‬‏ ‏‮HTTP‬‏/2}few{# ‏‮requests‬‏ ‏‮not‬‏ ‏‮served‬‏ ‏‮via‬‏ ‏‮HTTP‬‏/2}many{# ‏‮requests‬‏ ‏‮not‬‏ ‏‮served‬‏ ‏‮via‬‏ ‏‮HTTP‬‏/2}other{# ‏‮requests‬‏ ‏‮not‬‏ ‏‮served‬‏ ‏‮via‬‏ ‏‮HTTP‬‏/2}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "‏‮Does‬‏ ‏‮not‬‏ ‏‮use‬‏ ‏‮HTTP‬‏/2 ‏‮for‬‏ ‏‮all‬‏ ‏‮of‬‏ ‏‮its‬‏ ‏‮resources‬‏"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "‏‮Uses‬‏ ‏‮HTTP‬‏/2 ‏‮for‬‏ ‏‮its‬‏ ‏‮own‬‏ ‏‮resources‬‏"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "‏‮Consider‬‏ ‏‮marking‬‏ ‏‮your‬‏ ‏‮touch‬‏ ‏‮and‬‏ ‏‮wheel‬‏ ‏‮event‬‏ ‏‮listeners‬‏ ‏‮as‬‏ `passive` ‏‮to‬‏ ‏‮improve‬‏ ‏‮your‬‏ ‏‮page‬‏'‏‮s‬‏ ‏‮scroll‬‏ ‏‮performance‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/uses-passive-event-listeners)."}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "‏‮Does‬‏ ‏‮not‬‏ ‏‮use‬‏ ‏‮passive‬‏ ‏‮listeners‬‏ ‏‮to‬‏ ‏‮improve‬‏ ‏‮scrolling‬‏ ‏‮performance‬‏"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "‏‮Uses‬‏ ‏‮passive‬‏ ‏‮listeners‬‏ ‏‮to‬‏ ‏‮improve‬‏ ‏‮scrolling‬‏ ‏‮performance‬‏"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "‏‮Description‬‏"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "‏‮Errors‬‏ ‏‮logged‬‏ ‏‮to‬‏ ‏‮the‬‏ ‏‮console‬‏ ‏‮indicate‬‏ ‏‮unresolved‬‏ ‏‮problems‬‏. ‏‮They‬‏ ‏‮can‬‏ ‏‮come‬‏ ‏‮from‬‏ ‏‮network‬‏ ‏‮request‬‏ ‏‮failures‬‏ ‏‮and‬‏ ‏‮other‬‏ ‏‮browser‬‏ ‏‮concerns‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "‏‮Browser‬‏ ‏‮errors‬‏ ‏‮were‬‏ ‏‮logged‬‏ ‏‮to‬‏ ‏‮the‬‏ ‏‮console‬‏"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "‏‮No‬‏ ‏‮browser‬‏ ‏‮errors‬‏ ‏‮logged‬‏ ‏‮to‬‏ ‏‮the‬‏ ‏‮console‬‏"}, "lighthouse-core/audits/font-display.js | description": {"message": "‏‮Leverage‬‏ ‏‮the‬‏ ‏‮font‬‏-‏‮display‬‏ ‏‮CSS‬‏ ‏‮feature‬‏ ‏‮to‬‏ ‏‮ensure‬‏ ‏‮text‬‏ ‏‮is‬‏ ‏‮user‬‏-‏‮visible‬‏ ‏‮while‬‏ ‏‮webfonts‬‏ ‏‮are‬‏ ‏‮loading‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/font-display)."}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "‏‮Ensure‬‏ ‏‮text‬‏ ‏‮remains‬‏ ‏‮visible‬‏ ‏‮during‬‏ ‏‮webfont‬‏ ‏‮load‬‏"}, "lighthouse-core/audits/font-display.js | title": {"message": "‏‮All‬‏ ‏‮text‬‏ ‏‮remains‬‏ ‏‮visible‬‏ ‏‮during‬‏ ‏‮webfont‬‏ ‏‮loads‬‏"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "‏‮Lighthouse‬‏ ‏‮was‬‏ ‏‮unable‬‏ ‏‮to‬‏ ‏‮automatically‬‏ ‏‮check‬‏ ‏‮the‬‏ ‏‮font‬‏-‏‮display‬‏ ‏‮value‬‏ ‏‮for‬‏ ‏‮the‬‏ ‏‮following‬‏ ‏‮URL‬‏: {fontURL}."}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "‏‮Aspect‬‏ ‏‮Ratio‬‏ (‏‮Actual‬‏)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "‏‮Aspect‬‏ ‏‮Ratio‬‏ (‏‮Displayed‬‏)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "‏‮Image‬‏ ‏‮display‬‏ ‏‮dimensions‬‏ ‏‮should‬‏ ‏‮match‬‏ ‏‮natural‬‏ ‏‮aspect‬‏ ‏‮ratio‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/image-aspect-ratio)."}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "‏‮Displays‬‏ ‏‮images‬‏ ‏‮with‬‏ ‏‮incorrect‬‏ ‏‮aspect‬‏ ‏‮ratio‬‏"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "‏‮Displays‬‏ ‏‮images‬‏ ‏‮with‬‏ ‏‮correct‬‏ ‏‮aspect‬‏ ‏‮ratio‬‏"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "‏‮Invalid‬‏ ‏‮image‬‏ ‏‮sizing‬‏ ‏‮information‬‏ {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "‏‮Browsers‬‏ ‏‮can‬‏ ‏‮proactively‬‏ ‏‮prompt‬‏ ‏‮users‬‏ ‏‮to‬‏ ‏‮add‬‏ ‏‮your‬‏ ‏‮app‬‏ ‏‮to‬‏ ‏‮their‬‏ ‏‮homescreen‬‏, ‏‮which‬‏ ‏‮can‬‏ ‏‮lead‬‏ ‏‮to‬‏ ‏‮higher‬‏ ‏‮engagement‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/installable-manifest)."}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "‏‮Web‬‏ ‏‮app‬‏ ‏‮manifest‬‏ ‏‮does‬‏ ‏‮not‬‏ ‏‮meet‬‏ ‏‮the‬‏ ‏‮installability‬‏ ‏‮requirements‬‏"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "‏‮Web‬‏ ‏‮app‬‏ ‏‮manifest‬‏ ‏‮meets‬‏ ‏‮the‬‏ ‏‮installability‬‏ ‏‮requirements‬‏"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "‏‮Insecure‬‏ ‏‮URL‬‏"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "‏‮All‬‏ ‏‮sites‬‏ ‏‮should‬‏ ‏‮be‬‏ ‏‮protected‬‏ ‏‮with‬‏ ‏‮HTTPS‬‏, ‏‮even‬‏ ‏‮ones‬‏ ‏‮that‬‏ ‏‮don‬‏'‏‮t‬‏ ‏‮handle‬‏ ‏‮sensitive‬‏ ‏‮data‬‏. ‏‮HTTPS‬‏ ‏‮prevents‬‏ ‏‮intruders‬‏ ‏‮from‬‏ ‏‮tampering‬‏ ‏‮with‬‏ ‏‮or‬‏ ‏‮passively‬‏ ‏‮listening‬‏ ‏‮in‬‏ ‏‮on‬‏ ‏‮the‬‏ ‏‮communications‬‏ ‏‮between‬‏ ‏‮your‬‏ ‏‮app‬‏ ‏‮and‬‏ ‏‮your‬‏ ‏‮users‬‏, ‏‮and‬‏ ‏‮is‬‏ ‏‮a‬‏ ‏‮prerequisite‬‏ ‏‮for‬‏ ‏‮HTTP‬‏/2 ‏‮and‬‏ ‏‮many‬‏ ‏‮new‬‏ ‏‮web‬‏ ‏‮platform‬‏ ‏‮APIs‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/is-on-https)."}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 ‏‮insecure‬‏ ‏‮request‬‏ ‏‮found‬‏}zero{# ‏‮insecure‬‏ ‏‮requests‬‏ ‏‮found‬‏}two{# ‏‮insecure‬‏ ‏‮requests‬‏ ‏‮found‬‏}few{# ‏‮insecure‬‏ ‏‮requests‬‏ ‏‮found‬‏}many{# ‏‮insecure‬‏ ‏‮requests‬‏ ‏‮found‬‏}other{# ‏‮insecure‬‏ ‏‮requests‬‏ ‏‮found‬‏}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "‏‮Does‬‏ ‏‮not‬‏ ‏‮use‬‏ ‏‮HTTPS‬‏"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "‏‮Uses‬‏ ‏‮HTTPS‬‏"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "‏‮A‬‏ ‏‮fast‬‏ ‏‮page‬‏ ‏‮load‬‏ ‏‮over‬‏ ‏‮a‬‏ ‏‮cellular‬‏ ‏‮network‬‏ ‏‮ensures‬‏ ‏‮a‬‏ ‏‮good‬‏ ‏‮mobile‬‏ ‏‮user‬‏ ‏‮experience‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/load-fast-enough-for-pwa)."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "‏‮Interactive‬‏ ‏‮at‬‏ {timeInMs, number, seconds} ‏‮s‬‏"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "‏‮Interactive‬‏ ‏‮on‬‏ ‏‮simulated‬‏ ‏‮mobile‬‏ ‏‮network‬‏ ‏‮at‬‏ {timeInMs, number, seconds} ‏‮s‬‏"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "‏‮Your‬‏ ‏‮page‬‏ ‏‮loads‬‏ ‏‮too‬‏ ‏‮slowly‬‏ ‏‮and‬‏ ‏‮is‬‏ ‏‮not‬‏ ‏‮interactive‬‏ ‏‮within‬‏ 10 ‏‮seconds‬‏. ‏‮Look‬‏ ‏‮at‬‏ ‏‮the‬‏ ‏‮opportunities‬‏ ‏‮and‬‏ ‏‮diagnostics‬‏ ‏‮in‬‏ ‏‮the‬‏ \"‏‮Performance‬‏\" ‏‮section‬‏ ‏‮to‬‏ ‏‮learn‬‏ ‏‮how‬‏ ‏‮to‬‏ ‏‮improve‬‏."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "‏‮Page‬‏ ‏‮load‬‏ ‏‮is‬‏ ‏‮not‬‏ ‏‮fast‬‏ ‏‮enough‬‏ ‏‮on‬‏ ‏‮mobile‬‏ ‏‮networks‬‏"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "‏‮Page‬‏ ‏‮load‬‏ ‏‮is‬‏ ‏‮fast‬‏ ‏‮enough‬‏ ‏‮on‬‏ ‏‮mobile‬‏ ‏‮networks‬‏"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "‏‮Category‬‏"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "‏‮Consider‬‏ ‏‮reducing‬‏ ‏‮the‬‏ ‏‮time‬‏ ‏‮spent‬‏ ‏‮parsing‬‏, ‏‮compiling‬‏ ‏‮and‬‏ ‏‮executing‬‏ ‏‮JS‬‏. ‏‮You‬‏ ‏‮may‬‏ ‏‮find‬‏ ‏‮delivering‬‏ ‏‮smaller‬‏ ‏‮JS‬‏ ‏‮payloads‬‏ ‏‮helps‬‏ ‏‮with‬‏ ‏‮this‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "‏‮Minimize‬‏ ‏‮main‬‏-‏‮thread‬‏ ‏‮work‬‏"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "‏‮Minimizes‬‏ ‏‮main‬‏-‏‮thread‬‏ ‏‮work‬‏"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "‏‮To‬‏ ‏‮reach‬‏ ‏‮the‬‏ ‏‮most‬‏ ‏‮number‬‏ ‏‮of‬‏ ‏‮users‬‏, ‏‮sites‬‏ ‏‮should‬‏ ‏‮work‬‏ ‏‮across‬‏ ‏‮every‬‏ ‏‮major‬‏ ‏‮browser‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/pwa-cross-browser)."}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "‏‮Site‬‏ ‏‮works‬‏ ‏‮cross‬‏-‏‮browser‬‏"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "‏‮Ensure‬‏ ‏‮individual‬‏ ‏‮pages‬‏ ‏‮are‬‏ ‏‮deep‬‏ ‏‮linkable‬‏ ‏‮via‬‏ ‏‮URL‬‏ ‏‮and‬‏ ‏‮that‬‏ ‏‮URLs‬‏ ‏‮are‬‏ ‏‮unique‬‏ ‏‮for‬‏ ‏‮the‬‏ ‏‮purpose‬‏ ‏‮of‬‏ ‏‮shareability‬‏ ‏‮on‬‏ ‏‮social‬‏ ‏‮media‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/pwa-each-page-has-url)."}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "‏‮Each‬‏ ‏‮page‬‏ ‏‮has‬‏ ‏‮a‬‏ ‏‮URL‬‏"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "‏‮Transitions‬‏ ‏‮should‬‏ ‏‮feel‬‏ ‏‮snappy‬‏ ‏‮as‬‏ ‏‮you‬‏ ‏‮tap‬‏ ‏‮around‬‏, ‏‮even‬‏ ‏‮on‬‏ ‏‮a‬‏ ‏‮slow‬‏ ‏‮network‬‏. ‏‮This‬‏ ‏‮experience‬‏ ‏‮is‬‏ ‏‮key‬‏ ‏‮to‬‏ ‏‮a‬‏ ‏‮user‬‏'‏‮s‬‏ ‏‮perception‬‏ ‏‮of‬‏ ‏‮performance‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/pwa-page-transitions)."}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "‏‮Page‬‏ ‏‮transitions‬‏ ‏‮don‬‏'‏‮t‬‏ ‏‮feel‬‏ ‏‮like‬‏ ‏‮they‬‏ ‏‮block‬‏ ‏‮on‬‏ ‏‮the‬‏ ‏‮network‬‏"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "‏‮Estimated‬‏ ‏‮Input‬‏ ‏‮Latency‬‏ ‏‮is‬‏ ‏‮an‬‏ ‏‮estimate‬‏ ‏‮of‬‏ ‏‮how‬‏ ‏‮long‬‏ ‏‮your‬‏ ‏‮app‬‏ ‏‮takes‬‏ ‏‮to‬‏ ‏‮respond‬‏ ‏‮to‬‏ ‏‮user‬‏ ‏‮input‬‏, ‏‮in‬‏ ‏‮milliseconds‬‏, ‏‮during‬‏ ‏‮the‬‏ ‏‮busiest‬‏ 5‏‮s‬‏ ‏‮window‬‏ ‏‮of‬‏ ‏‮page‬‏ ‏‮load‬‏. ‏‮If‬‏ ‏‮your‬‏ ‏‮latency‬‏ ‏‮is‬‏ ‏‮higher‬‏ ‏‮than‬‏ 50 ‏‮ms‬‏, ‏‮users‬‏ ‏‮may‬‏ ‏‮perceive‬‏ ‏‮your‬‏ ‏‮app‬‏ ‏‮as‬‏ ‏‮laggy‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/estimated-input-latency)."}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "‏‮Estimated‬‏ ‏‮Input‬‏ ‏‮Latency‬‏"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "‏‮First‬‏ ‏‮Contentful‬‏ ‏‮Paint‬‏ ‏‮marks‬‏ ‏‮the‬‏ ‏‮time‬‏ ‏‮at‬‏ ‏‮which‬‏ ‏‮the‬‏ ‏‮first‬‏ ‏‮text‬‏ ‏‮or‬‏ ‏‮image‬‏ ‏‮is‬‏ ‏‮painted‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/first-contentful-paint)."}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "‏‮First‬‏ ‏‮Contentful‬‏ ‏‮Paint‬‏"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "‏‮First‬‏ ‏‮CPU‬‏ ‏‮Idle‬‏ ‏‮marks‬‏ ‏‮the‬‏ ‏‮first‬‏ ‏‮time‬‏ ‏‮at‬‏ ‏‮which‬‏ ‏‮the‬‏ ‏‮page‬‏'‏‮s‬‏ ‏‮main‬‏ ‏‮thread‬‏ ‏‮is‬‏ ‏‮quiet‬‏ ‏‮enough‬‏ ‏‮to‬‏ ‏‮handle‬‏ ‏‮input‬‏.  [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/first-cpu-idle)."}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "‏‮First‬‏ ‏‮CPU‬‏ ‏‮Idle‬‏"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "‏‮First‬‏ ‏‮Meaningful‬‏ ‏‮Paint‬‏ ‏‮measures‬‏ ‏‮when‬‏ ‏‮the‬‏ ‏‮primary‬‏ ‏‮content‬‏ ‏‮of‬‏ ‏‮a‬‏ ‏‮page‬‏ ‏‮is‬‏ ‏‮visible‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/first-meaningful-paint)."}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "‏‮First‬‏ ‏‮Meaningful‬‏ ‏‮Paint‬‏"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "‏‮Time‬‏ ‏‮to‬‏ ‏‮interactive‬‏ ‏‮is‬‏ ‏‮the‬‏ ‏‮amount‬‏ ‏‮of‬‏ ‏‮time‬‏ ‏‮it‬‏ ‏‮takes‬‏ ‏‮for‬‏ ‏‮the‬‏ ‏‮page‬‏ ‏‮to‬‏ ‏‮become‬‏ ‏‮fully‬‏ ‏‮interactive‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/interactive)."}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "‏‮Time‬‏ ‏‮to‬‏ ‏‮Interactive‬‏"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "‏‮The‬‏ ‏‮maximum‬‏ ‏‮potential‬‏ ‏‮First‬‏ ‏‮Input‬‏ ‏‮Delay‬‏ ‏‮that‬‏ ‏‮your‬‏ ‏‮users‬‏ ‏‮could‬‏ ‏‮experience‬‏ ‏‮is‬‏ ‏‮the‬‏ ‏‮duration‬‏, ‏‮in‬‏ ‏‮milliseconds‬‏, ‏‮of‬‏ ‏‮the‬‏ ‏‮longest‬‏ ‏‮task‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://developers.google.com/web/updates/2018/05/first-input-delay)."}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "‏‮Max‬‏ ‏‮Potential‬‏ ‏‮First‬‏ ‏‮Input‬‏ ‏‮Delay‬‏"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "‏‮Speed‬‏ ‏‮Index‬‏ ‏‮shows‬‏ ‏‮how‬‏ ‏‮quickly‬‏ ‏‮the‬‏ ‏‮contents‬‏ ‏‮of‬‏ ‏‮a‬‏ ‏‮page‬‏ ‏‮are‬‏ ‏‮visibly‬‏ ‏‮populated‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/speed-index)."}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "‏‮Speed‬‏ ‏‮Index‬‏"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "‏‮Sum‬‏ ‏‮of‬‏ ‏‮all‬‏ ‏‮time‬‏ ‏‮periods‬‏ ‏‮between‬‏ ‏‮FCP‬‏ ‏‮and‬‏ ‏‮Time‬‏ ‏‮to‬‏ ‏‮Interactive‬‏, ‏‮when‬‏ ‏‮task‬‏ ‏‮length‬‏ ‏‮exceeded‬‏ 50‏‮ms‬‏, ‏‮expressed‬‏ ‏‮in‬‏ ‏‮milliseconds‬‏."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "‏‮Total‬‏ ‏‮Blocking‬‏ ‏‮Time‬‏"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "‏‮Network‬‏ ‏‮round‬‏ ‏‮trip‬‏ ‏‮times‬‏ (‏‮RTT‬‏) ‏‮have‬‏ ‏‮a‬‏ ‏‮large‬‏ ‏‮impact‬‏ ‏‮on‬‏ ‏‮performance‬‏. ‏‮If‬‏ ‏‮the‬‏ ‏‮RTT‬‏ ‏‮to‬‏ ‏‮an‬‏ ‏‮origin‬‏ ‏‮is‬‏ ‏‮high‬‏, ‏‮it‬‏'‏‮s‬‏ ‏‮an‬‏ ‏‮indication‬‏ ‏‮that‬‏ ‏‮servers‬‏ ‏‮closer‬‏ ‏‮to‬‏ ‏‮the‬‏ ‏‮user‬‏ ‏‮could‬‏ ‏‮improve‬‏ ‏‮performance‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "lighthouse-core/audits/network-rtt.js | title": {"message": "‏‮Network‬‏ ‏‮Round‬‏ ‏‮Trip‬‏ ‏‮Times‬‏"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "‏‮Server‬‏ ‏‮latencies‬‏ ‏‮can‬‏ ‏‮impact‬‏ ‏‮web‬‏ ‏‮performance‬‏. ‏‮If‬‏ ‏‮the‬‏ ‏‮server‬‏ ‏‮latency‬‏ ‏‮of‬‏ ‏‮an‬‏ ‏‮origin‬‏ ‏‮is‬‏ ‏‮high‬‏, ‏‮it‬‏'‏‮s‬‏ ‏‮an‬‏ ‏‮indication‬‏ ‏‮the‬‏ ‏‮server‬‏ ‏‮is‬‏ ‏‮overloaded‬‏ ‏‮or‬‏ ‏‮has‬‏ ‏‮poor‬‏ ‏‮backend‬‏ ‏‮performance‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "‏‮Server‬‏ ‏‮Backend‬‏ ‏‮Latencies‬‏"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "‏‮A‬‏ ‏‮service‬‏ ‏‮worker‬‏ ‏‮enables‬‏ ‏‮your‬‏ ‏‮web‬‏ ‏‮app‬‏ ‏‮to‬‏ ‏‮be‬‏ ‏‮reliable‬‏ ‏‮in‬‏ ‏‮unpredictable‬‏ ‏‮network‬‏ ‏‮conditions‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/offline-start-url)."}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url` ‏‮does‬‏ ‏‮not‬‏ ‏‮respond‬‏ ‏‮with‬‏ ‏‮a‬‏ 200 ‏‮when‬‏ ‏‮offline‬‏"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url` ‏‮responds‬‏ ‏‮with‬‏ ‏‮a‬‏ 200 ‏‮when‬‏ ‏‮offline‬‏"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "‏‮Lighthouse‬‏ ‏‮couldn‬‏'‏‮t‬‏ ‏‮read‬‏ ‏‮the‬‏ `start_url` ‏‮from‬‏ ‏‮the‬‏ ‏‮manifest‬‏. ‏‮As‬‏ ‏‮a‬‏ ‏‮result‬‏, ‏‮the‬‏ `start_url` ‏‮was‬‏ ‏‮assumed‬‏ ‏‮to‬‏ ‏‮be‬‏ ‏‮the‬‏ ‏‮document‬‏'‏‮s‬‏ ‏‮URL‬‏. ‏‮Error‬‏ ‏‮message‬‏: '{manifestWarning}'."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "‏‮Over‬‏ ‏‮Budget‬‏"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "‏‮Keep‬‏ ‏‮the‬‏ ‏‮quantity‬‏ ‏‮and‬‏ ‏‮size‬‏ ‏‮of‬‏ ‏‮network‬‏ ‏‮requests‬‏ ‏‮under‬‏ ‏‮the‬‏ ‏‮targets‬‏ ‏‮set‬‏ ‏‮by‬‏ ‏‮the‬‏ ‏‮provided‬‏ ‏‮performance‬‏ ‏‮budget‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 ‏‮request‬‏}zero{# ‏‮requests‬‏}two{# ‏‮requests‬‏}few{# ‏‮requests‬‏}many{# ‏‮requests‬‏}other{# ‏‮requests‬‏}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "‏‮Performance‬‏ ‏‮budget‬‏"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "‏‮If‬‏ ‏‮you‬‏'‏‮ve‬‏ ‏‮already‬‏ ‏‮set‬‏ ‏‮up‬‏ ‏‮HTTPS‬‏, ‏‮make‬‏ ‏‮sure‬‏ ‏‮that‬‏ ‏‮you‬‏ ‏‮redirect‬‏ ‏‮all‬‏ ‏‮HTTP‬‏ ‏‮traffic‬‏ ‏‮to‬‏ ‏‮HTTPS‬‏ ‏‮in‬‏ ‏‮order‬‏ ‏‮to‬‏ ‏‮enable‬‏ ‏‮secure‬‏ ‏‮web‬‏ ‏‮features‬‏ ‏‮for‬‏ ‏‮all‬‏ ‏‮your‬‏ ‏‮users‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/redirects-http)."}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "‏‮Does‬‏ ‏‮not‬‏ ‏‮redirect‬‏ ‏‮HTTP‬‏ ‏‮traffic‬‏ ‏‮to‬‏ ‏‮HTTPS‬‏"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "‏‮Redirects‬‏ ‏‮HTTP‬‏ ‏‮traffic‬‏ ‏‮to‬‏ ‏‮HTTPS‬‏"}, "lighthouse-core/audits/redirects.js | description": {"message": "‏‮Redirects‬‏ ‏‮introduce‬‏ ‏‮additional‬‏ ‏‮delays‬‏ ‏‮before‬‏ ‏‮the‬‏ ‏‮page‬‏ ‏‮can‬‏ ‏‮be‬‏ ‏‮loaded‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/redirects)."}, "lighthouse-core/audits/redirects.js | title": {"message": "‏‮Avoid‬‏ ‏‮multiple‬‏ ‏‮page‬‏ ‏‮redirects‬‏"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "‏‮To‬‏ ‏‮set‬‏ ‏‮budgets‬‏ ‏‮for‬‏ ‏‮the‬‏ ‏‮quantity‬‏ ‏‮and‬‏ ‏‮size‬‏ ‏‮of‬‏ ‏‮page‬‏ ‏‮resources‬‏, ‏‮add‬‏ ‏‮a‬‏ ‏‮budget‬‏.‏‮json‬‏ ‏‮file‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 ‏‮request‬‏ • {byteCount, number, bytes} ‏‮KB‬‏}zero{# ‏‮requests‬‏ • {byteCount, number, bytes} ‏‮KB‬‏}two{# ‏‮requests‬‏ • {byteCount, number, bytes} ‏‮KB‬‏}few{# ‏‮requests‬‏ • {byteCount, number, bytes} ‏‮KB‬‏}many{# ‏‮requests‬‏ • {byteCount, number, bytes} ‏‮KB‬‏}other{# ‏‮requests‬‏ • {byteCount, number, bytes} ‏‮KB‬‏}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "‏‮Keep‬‏ ‏‮request‬‏ ‏‮counts‬‏ ‏‮low‬‏ ‏‮and‬‏ ‏‮transfer‬‏ ‏‮sizes‬‏ ‏‮small‬‏"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "‏‮Canonical‬‏ ‏‮links‬‏ ‏‮suggest‬‏ ‏‮which‬‏ ‏‮URL‬‏ ‏‮to‬‏ ‏‮show‬‏ ‏‮in‬‏ ‏‮search‬‏ ‏‮results‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/canonical)."}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "‏‮Multiple‬‏ ‏‮conflicting‬‏ ‏‮URLs‬‏ ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "‏‮Points‬‏ ‏‮to‬‏ ‏‮a‬‏ ‏‮different‬‏ ‏‮domain‬‏ ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "‏‮Invalid‬‏ ‏‮URL‬‏ ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "‏‮Points‬‏ ‏‮to‬‏ ‏‮another‬‏ `hreflang` ‏‮location‬‏ ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "‏‮Relative‬‏ ‏‮URL‬‏ ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "‏‮Points‬‏ ‏‮to‬‏ ‏‮the‬‏ ‏‮domain‬‏'‏‮s‬‏ ‏‮root‬‏ ‏‮URL‬‏ (‏‮the‬‏ ‏‮homepage‬‏), ‏‮instead‬‏ ‏‮of‬‏ ‏‮an‬‏ ‏‮equivalent‬‏ ‏‮page‬‏ ‏‮of‬‏ ‏‮content‬‏"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "‏‮Document‬‏ ‏‮does‬‏ ‏‮not‬‏ ‏‮have‬‏ ‏‮a‬‏ ‏‮valid‬‏ `rel=canonical`"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "‏‮Document‬‏ ‏‮has‬‏ ‏‮a‬‏ ‏‮valid‬‏ `rel=canonical`"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "‏‮Font‬‏ ‏‮sizes‬‏ ‏‮less‬‏ ‏‮than‬‏ 12‏‮px‬‏ ‏‮are‬‏ ‏‮too‬‏ ‏‮small‬‏ ‏‮to‬‏ ‏‮be‬‏ ‏‮legible‬‏ ‏‮and‬‏ ‏‮require‬‏ ‏‮mobile‬‏ ‏‮visitors‬‏ ‏‮to‬‏ “‏‮pinch‬‏ ‏‮to‬‏ ‏‮zoom‬‏” ‏‮in‬‏ ‏‮order‬‏ ‏‮to‬‏ ‏‮read‬‏. ‏‮Strive‬‏ ‏‮to‬‏ ‏‮have‬‏ >60% ‏‮of‬‏ ‏‮page‬‏ ‏‮text‬‏ ≥12‏‮px‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/font-size)."}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} ‏‮legible‬‏ ‏‮text‬‏"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "‏‮Text‬‏ ‏‮is‬‏ ‏‮illegible‬‏ ‏‮because‬‏ ‏‮there‬‏'‏‮s‬‏ ‏‮no‬‏ ‏‮viewport‬‏ ‏‮meta‬‏ ‏‮tag‬‏ ‏‮optimized‬‏ ‏‮for‬‏ ‏‮mobile‬‏ ‏‮screens‬‏."}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "{decimalProportion, number, extendedPercent} ‏‮of‬‏ ‏‮text‬‏ ‏‮is‬‏ ‏‮too‬‏ ‏‮small‬‏ (‏‮based‬‏ ‏‮on‬‏ {decimalProportionVisited, number, extendedPercent} ‏‮sample‬‏)."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "‏‮Document‬‏ ‏‮doesn‬‏'‏‮t‬‏ ‏‮use‬‏ ‏‮legible‬‏ ‏‮font‬‏ ‏‮sizes‬‏"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "‏‮Document‬‏ ‏‮uses‬‏ ‏‮legible‬‏ ‏‮font‬‏ ‏‮sizes‬‏"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "‏‮hreflang‬‏ ‏‮links‬‏ ‏‮tell‬‏ ‏‮search‬‏ ‏‮engines‬‏ ‏‮what‬‏ ‏‮version‬‏ ‏‮of‬‏ ‏‮a‬‏ ‏‮page‬‏ ‏‮they‬‏ ‏‮should‬‏ ‏‮list‬‏ ‏‮in‬‏ ‏‮search‬‏ ‏‮results‬‏ ‏‮for‬‏ ‏‮a‬‏ ‏‮given‬‏ ‏‮language‬‏ ‏‮or‬‏ ‏‮region‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/hreflang)."}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "‏‮Document‬‏ ‏‮doesn‬‏'‏‮t‬‏ ‏‮have‬‏ ‏‮a‬‏ ‏‮valid‬‏ `hreflang`"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "‏‮Document‬‏ ‏‮has‬‏ ‏‮a‬‏ ‏‮valid‬‏ `hreflang`"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "‏‮Pages‬‏ ‏‮with‬‏ ‏‮unsuccessful‬‏ ‏‮HTTP‬‏ ‏‮status‬‏ ‏‮codes‬‏ ‏‮may‬‏ ‏‮not‬‏ ‏‮be‬‏ ‏‮indexed‬‏ ‏‮properly‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/http-status-code)."}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "‏‮Page‬‏ ‏‮has‬‏ ‏‮unsuccessful‬‏ ‏‮HTTP‬‏ ‏‮status‬‏ ‏‮code‬‏"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "‏‮Page‬‏ ‏‮has‬‏ ‏‮successful‬‏ ‏‮HTTP‬‏ ‏‮status‬‏ ‏‮code‬‏"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "‏‮Search‬‏ ‏‮engines‬‏ ‏‮are‬‏ ‏‮unable‬‏ ‏‮to‬‏ ‏‮include‬‏ ‏‮your‬‏ ‏‮pages‬‏ ‏‮in‬‏ ‏‮search‬‏ ‏‮results‬‏ ‏‮if‬‏ ‏‮they‬‏ ‏‮don‬‏'‏‮t‬‏ ‏‮have‬‏ ‏‮permission‬‏ ‏‮to‬‏ ‏‮crawl‬‏ ‏‮them‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/is-crawable)."}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "‏‮Page‬‏ ‏‮is‬‏ ‏‮blocked‬‏ ‏‮from‬‏ ‏‮indexing‬‏"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "‏‮Page‬‏ ‏‮isn‬‏’‏‮t‬‏ ‏‮blocked‬‏ ‏‮from‬‏ ‏‮indexing‬‏"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "‏‮Descriptive‬‏ ‏‮link‬‏ ‏‮text‬‏ ‏‮helps‬‏ ‏‮search‬‏ ‏‮engines‬‏ ‏‮understand‬‏ ‏‮your‬‏ ‏‮content‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/link-text)."}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 ‏‮link‬‏ ‏‮found‬‏}zero{# ‏‮links‬‏ ‏‮found‬‏}two{# ‏‮links‬‏ ‏‮found‬‏}few{# ‏‮links‬‏ ‏‮found‬‏}many{# ‏‮links‬‏ ‏‮found‬‏}other{# ‏‮links‬‏ ‏‮found‬‏}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "‏‮Links‬‏ ‏‮do‬‏ ‏‮not‬‏ ‏‮have‬‏ ‏‮descriptive‬‏ ‏‮text‬‏"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "‏‮Links‬‏ ‏‮have‬‏ ‏‮descriptive‬‏ ‏‮text‬‏"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "‏‮Run‬‏ ‏‮the‬‏ [‏‮Structured‬‏ ‏‮Data‬‏ ‏‮Testing‬‏ ‏‮Tool‬‏](https://search.google.com/structured-data/testing-tool/) ‏‮and‬‏ ‏‮the‬‏ [‏‮Structured‬‏ ‏‮Data‬‏ ‏‮Linter‬‏](http://linter.structured-data.org/) ‏‮to‬‏ ‏‮validate‬‏ ‏‮structured‬‏ ‏‮data‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/structured-data)."}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "‏‮Structured‬‏ ‏‮data‬‏ ‏‮is‬‏ ‏‮valid‬‏"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "‏‮Meta‬‏ ‏‮descriptions‬‏ ‏‮may‬‏ ‏‮be‬‏ ‏‮included‬‏ ‏‮in‬‏ ‏‮search‬‏ ‏‮results‬‏ ‏‮to‬‏ ‏‮concisely‬‏ ‏‮summarize‬‏ ‏‮page‬‏ ‏‮content‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/meta-description)."}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "‏‮Description‬‏ ‏‮text‬‏ ‏‮is‬‏ ‏‮empty‬‏."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "‏‮Document‬‏ ‏‮does‬‏ ‏‮not‬‏ ‏‮have‬‏ ‏‮a‬‏ ‏‮meta‬‏ ‏‮description‬‏"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "‏‮Document‬‏ ‏‮has‬‏ ‏‮a‬‏ ‏‮meta‬‏ ‏‮description‬‏"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "‏‮Search‬‏ ‏‮engines‬‏ ‏‮can‬‏'‏‮t‬‏ ‏‮index‬‏ ‏‮plugin‬‏ ‏‮content‬‏, ‏‮and‬‏ ‏‮many‬‏ ‏‮devices‬‏ ‏‮restrict‬‏ ‏‮plugins‬‏ ‏‮or‬‏ ‏‮don‬‏'‏‮t‬‏ ‏‮support‬‏ ‏‮them‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/plugins)."}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "‏‮Document‬‏ ‏‮uses‬‏ ‏‮plugins‬‏"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "‏‮Document‬‏ ‏‮avoids‬‏ ‏‮plugins‬‏"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "‏‮If‬‏ ‏‮your‬‏ ‏‮robots‬‏.‏‮txt‬‏ ‏‮file‬‏ ‏‮is‬‏ ‏‮malformed‬‏, ‏‮crawlers‬‏ ‏‮may‬‏ ‏‮not‬‏ ‏‮be‬‏ ‏‮able‬‏ ‏‮to‬‏ ‏‮understand‬‏ ‏‮how‬‏ ‏‮you‬‏ ‏‮want‬‏ ‏‮your‬‏ ‏‮website‬‏ ‏‮to‬‏ ‏‮be‬‏ ‏‮crawled‬‏ ‏‮or‬‏ ‏‮indexed‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/robots-txt)."}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "‏‮Request‬‏ ‏‮for‬‏ ‏‮robots‬‏.‏‮txt‬‏ ‏‮returned‬‏ ‏‮HTTP‬‏ ‏‮status‬‏: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 ‏‮error‬‏ ‏‮found‬‏}zero{# ‏‮errors‬‏ ‏‮found‬‏}two{# ‏‮errors‬‏ ‏‮found‬‏}few{# ‏‮errors‬‏ ‏‮found‬‏}many{# ‏‮errors‬‏ ‏‮found‬‏}other{# ‏‮errors‬‏ ‏‮found‬‏}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "‏‮Lighthouse‬‏ ‏‮was‬‏ ‏‮unable‬‏ ‏‮to‬‏ ‏‮download‬‏ ‏‮a‬‏ ‏‮robots‬‏.‏‮txt‬‏ ‏‮file‬‏"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "‏‮robots‬‏.‏‮txt‬‏ ‏‮is‬‏ ‏‮not‬‏ ‏‮valid‬‏"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "‏‮robots‬‏.‏‮txt‬‏ ‏‮is‬‏ ‏‮valid‬‏"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "‏‮Interactive‬‏ ‏‮elements‬‏ ‏‮like‬‏ ‏‮buttons‬‏ ‏‮and‬‏ ‏‮links‬‏ ‏‮should‬‏ ‏‮be‬‏ ‏‮large‬‏ ‏‮enough‬‏ (48‏‮x‬‏48‏‮px‬‏), ‏‮and‬‏ ‏‮have‬‏ ‏‮enough‬‏ ‏‮space‬‏ ‏‮around‬‏ ‏‮them‬‏, ‏‮to‬‏ ‏‮be‬‏ ‏‮easy‬‏ ‏‮enough‬‏ ‏‮to‬‏ ‏‮tap‬‏ ‏‮without‬‏ ‏‮overlapping‬‏ ‏‮onto‬‏ ‏‮other‬‏ ‏‮elements‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/tap-targets)."}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} ‏‮appropriately‬‏ ‏‮sized‬‏ ‏‮tap‬‏ ‏‮targets‬‏"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "‏‮Tap‬‏ ‏‮targets‬‏ ‏‮are‬‏ ‏‮too‬‏ ‏‮small‬‏ ‏‮because‬‏ ‏‮there‬‏'‏‮s‬‏ ‏‮no‬‏ ‏‮viewport‬‏ ‏‮meta‬‏ ‏‮tag‬‏ ‏‮optimized‬‏ ‏‮for‬‏ ‏‮mobile‬‏ ‏‮screens‬‏"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "‏‮Tap‬‏ ‏‮targets‬‏ ‏‮are‬‏ ‏‮not‬‏ ‏‮sized‬‏ ‏‮appropriately‬‏"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "‏‮Overlapping‬‏ ‏‮Target‬‏"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "‏‮Tap‬‏ ‏‮Target‬‏"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "‏‮Tap‬‏ ‏‮targets‬‏ ‏‮are‬‏ ‏‮sized‬‏ ‏‮appropriately‬‏"}, "lighthouse-core/audits/service-worker.js | description": {"message": "‏‮The‬‏ ‏‮service‬‏ ‏‮worker‬‏ ‏‮is‬‏ ‏‮the‬‏ ‏‮technology‬‏ ‏‮that‬‏ ‏‮enables‬‏ ‏‮your‬‏ ‏‮app‬‏ ‏‮to‬‏ ‏‮use‬‏ ‏‮many‬‏ ‏‮Progressive‬‏ ‏‮Web‬‏ ‏‮App‬‏ ‏‮features‬‏, ‏‮such‬‏ ‏‮as‬‏ ‏‮offline‬‏, ‏‮add‬‏ ‏‮to‬‏ ‏‮homescreen‬‏, ‏‮and‬‏ ‏‮push‬‏ ‏‮notifications‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/service-worker)."}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "‏‮This‬‏ ‏‮page‬‏ ‏‮is‬‏ ‏‮controlled‬‏ ‏‮by‬‏ ‏‮a‬‏ ‏‮service‬‏ ‏‮worker‬‏, ‏‮however‬‏ ‏‮no‬‏ `start_url` ‏‮was‬‏ ‏‮found‬‏ ‏‮because‬‏ ‏‮manifest‬‏ ‏‮failed‬‏ ‏‮to‬‏ ‏‮parse‬‏ ‏‮as‬‏ ‏‮valid‬‏ ‏‮JSON‬‏"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "‏‮This‬‏ ‏‮page‬‏ ‏‮is‬‏ ‏‮controlled‬‏ ‏‮by‬‏ ‏‮a‬‏ ‏‮service‬‏ ‏‮worker‬‏, ‏‮however‬‏ ‏‮the‬‏ `start_url` ({startUrl}) ‏‮is‬‏ ‏‮not‬‏ ‏‮in‬‏ ‏‮the‬‏ ‏‮service‬‏ ‏‮worker‬‏'‏‮s‬‏ ‏‮scope‬‏ ({scopeUrl})"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "‏‮This‬‏ ‏‮page‬‏ ‏‮is‬‏ ‏‮controlled‬‏ ‏‮by‬‏ ‏‮a‬‏ ‏‮service‬‏ ‏‮worker‬‏, ‏‮however‬‏ ‏‮no‬‏ `start_url` ‏‮was‬‏ ‏‮found‬‏ ‏‮because‬‏ ‏‮no‬‏ ‏‮manifest‬‏ ‏‮was‬‏ ‏‮fetched‬‏."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "‏‮This‬‏ ‏‮origin‬‏ ‏‮has‬‏ ‏‮one‬‏ ‏‮or‬‏ ‏‮more‬‏ ‏‮service‬‏ ‏‮workers‬‏, ‏‮however‬‏ ‏‮the‬‏ ‏‮page‬‏ ({pageUrl}) ‏‮is‬‏ ‏‮not‬‏ ‏‮in‬‏ ‏‮scope‬‏."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "‏‮Does‬‏ ‏‮not‬‏ ‏‮register‬‏ ‏‮a‬‏ ‏‮service‬‏ ‏‮worker‬‏ ‏‮that‬‏ ‏‮controls‬‏ ‏‮page‬‏ ‏‮and‬‏ `start_url`"}, "lighthouse-core/audits/service-worker.js | title": {"message": "‏‮Registers‬‏ ‏‮a‬‏ ‏‮service‬‏ ‏‮worker‬‏ ‏‮that‬‏ ‏‮controls‬‏ ‏‮page‬‏ ‏‮and‬‏ `start_url`"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "‏‮A‬‏ ‏‮themed‬‏ ‏‮splash‬‏ ‏‮screen‬‏ ‏‮ensures‬‏ ‏‮a‬‏ ‏‮high‬‏-‏‮quality‬‏ ‏‮experience‬‏ ‏‮when‬‏ ‏‮users‬‏ ‏‮launch‬‏ ‏‮your‬‏ ‏‮app‬‏ ‏‮from‬‏ ‏‮their‬‏ ‏‮homescreens‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/splash-screen)."}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "‏‮Is‬‏ ‏‮not‬‏ ‏‮configured‬‏ ‏‮for‬‏ ‏‮a‬‏ ‏‮custom‬‏ ‏‮splash‬‏ ‏‮screen‬‏"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "‏‮Configured‬‏ ‏‮for‬‏ ‏‮a‬‏ ‏‮custom‬‏ ‏‮splash‬‏ ‏‮screen‬‏"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "‏‮The‬‏ ‏‮browser‬‏ ‏‮address‬‏ ‏‮bar‬‏ ‏‮can‬‏ ‏‮be‬‏ ‏‮themed‬‏ ‏‮to‬‏ ‏‮match‬‏ ‏‮your‬‏ ‏‮site‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/themed-omnibox)."}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "‏‮Does‬‏ ‏‮not‬‏ ‏‮set‬‏ ‏‮a‬‏ ‏‮theme‬‏ ‏‮color‬‏ ‏‮for‬‏ ‏‮the‬‏ ‏‮address‬‏ ‏‮bar‬‏."}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "‏‮Sets‬‏ ‏‮a‬‏ ‏‮theme‬‏ ‏‮color‬‏ ‏‮for‬‏ ‏‮the‬‏ ‏‮address‬‏ ‏‮bar‬‏."}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "‏‮Main‬‏-‏‮Thread‬‏ ‏‮Blocking‬‏ ‏‮Time‬‏"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "‏‮Third‬‏-‏‮Party‬‏"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "‏‮Third‬‏-‏‮party‬‏ ‏‮code‬‏ ‏‮can‬‏ ‏‮significantly‬‏ ‏‮impact‬‏ ‏‮load‬‏ ‏‮performance‬‏. ‏‮Limit‬‏ ‏‮the‬‏ ‏‮number‬‏ ‏‮of‬‏ ‏‮redundant‬‏ ‏‮third‬‏-‏‮party‬‏ ‏‮providers‬‏ ‏‮and‬‏ ‏‮try‬‏ ‏‮to‬‏ ‏‮load‬‏ ‏‮third‬‏-‏‮party‬‏ ‏‮code‬‏ ‏‮after‬‏ ‏‮your‬‏ ‏‮page‬‏ ‏‮has‬‏ ‏‮primarily‬‏ ‏‮finished‬‏ ‏‮loading‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "‏‮Third‬‏-‏‮party‬‏ ‏‮code‬‏ ‏‮blocked‬‏ ‏‮the‬‏ ‏‮main‬‏ ‏‮thread‬‏ ‏‮for‬‏ {timeInMs, number, milliseconds} ‏‮ms‬‏"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "‏‮Reduce‬‏ ‏‮the‬‏ ‏‮impact‬‏ ‏‮of‬‏ ‏‮third‬‏-‏‮party‬‏ ‏‮code‬‏"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "‏‮Third‬‏-‏‮Party‬‏ ‏‮usage‬‏"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "‏‮Time‬‏ ‏‮To‬‏ ‏‮First‬‏ ‏‮Byte‬‏ ‏‮identifies‬‏ ‏‮the‬‏ ‏‮time‬‏ ‏‮at‬‏ ‏‮which‬‏ ‏‮your‬‏ ‏‮server‬‏ ‏‮sends‬‏ ‏‮a‬‏ ‏‮response‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/time-to-first-byte)."}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "‏‮Root‬‏ ‏‮document‬‏ ‏‮took‬‏ {timeInMs, number, milliseconds} ‏‮ms‬‏"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "‏‮Reduce‬‏ ‏‮server‬‏ ‏‮response‬‏ ‏‮times‬‏ (‏‮TTFB‬‏)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "‏‮Server‬‏ ‏‮response‬‏ ‏‮times‬‏ ‏‮are‬‏ ‏‮low‬‏ (‏‮TTFB‬‏)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "‏‮Duration‬‏"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "‏‮Start‬‏ ‏‮Time‬‏"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "‏‮Type‬‏"}, "lighthouse-core/audits/user-timings.js | description": {"message": "‏‮Consider‬‏ ‏‮instrumenting‬‏ ‏‮your‬‏ ‏‮app‬‏ ‏‮with‬‏ ‏‮the‬‏ ‏‮User‬‏ ‏‮Timing‬‏ ‏‮API‬‏ ‏‮to‬‏ ‏‮measure‬‏ ‏‮your‬‏ ‏‮app‬‏'‏‮s‬‏ ‏‮real‬‏-‏‮world‬‏ ‏‮performance‬‏ ‏‮during‬‏ ‏‮key‬‏ ‏‮user‬‏ ‏‮experiences‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/user-timings)."}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 ‏‮user‬‏ ‏‮timing‬‏}zero{# ‏‮user‬‏ ‏‮timings‬‏}two{# ‏‮user‬‏ ‏‮timings‬‏}few{# ‏‮user‬‏ ‏‮timings‬‏}many{# ‏‮user‬‏ ‏‮timings‬‏}other{# ‏‮user‬‏ ‏‮timings‬‏}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "‏‮User‬‏ ‏‮Timing‬‏ ‏‮marks‬‏ ‏‮and‬‏ ‏‮measures‬‏"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "‏‮A‬‏ ‏‮preconnect‬‏ <link> ‏‮was‬‏ ‏‮found‬‏ ‏‮for‬‏ \"{securityOrigin}\" ‏‮but‬‏ ‏‮was‬‏ ‏‮not‬‏ ‏‮used‬‏ ‏‮by‬‏ ‏‮the‬‏ ‏‮browser‬‏. ‏‮Check‬‏ ‏‮that‬‏ ‏‮you‬‏ ‏‮are‬‏ ‏‮using‬‏ ‏‮the‬‏ `crossorigin` ‏‮attribute‬‏ ‏‮properly‬‏."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "‏‮Consider‬‏ ‏‮adding‬‏ `preconnect` ‏‮or‬‏ `dns-prefetch` ‏‮resource‬‏ ‏‮hints‬‏ ‏‮to‬‏ ‏‮establish‬‏ ‏‮early‬‏ ‏‮connections‬‏ ‏‮to‬‏ ‏‮important‬‏ ‏‮third‬‏-‏‮party‬‏ ‏‮origins‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/uses-rel-preconnect)."}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "‏‮Preconnect‬‏ ‏‮to‬‏ ‏‮required‬‏ ‏‮origins‬‏"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "‏‮A‬‏ ‏‮preload‬‏ <link> ‏‮was‬‏ ‏‮found‬‏ ‏‮for‬‏ \"{preloadURL}\" ‏‮but‬‏ ‏‮was‬‏ ‏‮not‬‏ ‏‮used‬‏ ‏‮by‬‏ ‏‮the‬‏ ‏‮browser‬‏. ‏‮Check‬‏ ‏‮that‬‏ ‏‮you‬‏ ‏‮are‬‏ ‏‮using‬‏ ‏‮the‬‏ `crossorigin` ‏‮attribute‬‏ ‏‮properly‬‏."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "‏‮Consider‬‏ ‏‮using‬‏ `<link rel=preload>` ‏‮to‬‏ ‏‮prioritize‬‏ ‏‮fetching‬‏ ‏‮resources‬‏ ‏‮that‬‏ ‏‮are‬‏ ‏‮currently‬‏ ‏‮requested‬‏ ‏‮later‬‏ ‏‮in‬‏ ‏‮page‬‏ ‏‮load‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/uses-rel-preload)."}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "‏‮Preload‬‏ ‏‮key‬‏ ‏‮requests‬‏"}, "lighthouse-core/audits/viewport.js | description": {"message": "‏‮Add‬‏ ‏‮a‬‏ `<meta name=\"viewport\">` ‏‮tag‬‏ ‏‮to‬‏ ‏‮optimize‬‏ ‏‮your‬‏ ‏‮app‬‏ ‏‮for‬‏ ‏‮mobile‬‏ ‏‮screens‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/viewport)."}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "‏‮No‬‏ `<meta name=\"viewport\">` ‏‮tag‬‏ ‏‮found‬‏"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "‏‮Does‬‏ ‏‮not‬‏ ‏‮have‬‏ ‏‮a‬‏ `<meta name=\"viewport\">` ‏‮tag‬‏ ‏‮with‬‏ `width` ‏‮or‬‏ `initial-scale`"}, "lighthouse-core/audits/viewport.js | title": {"message": "‏‮Has‬‏ ‏‮a‬‏ `<meta name=\"viewport\">` ‏‮tag‬‏ ‏‮with‬‏ `width` ‏‮or‬‏ `initial-scale`"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "‏‮Your‬‏ ‏‮app‬‏ ‏‮should‬‏ ‏‮display‬‏ ‏‮some‬‏ ‏‮content‬‏ ‏‮when‬‏ ‏‮JavaScript‬‏ ‏‮is‬‏ ‏‮disabled‬‏, ‏‮even‬‏ ‏‮if‬‏ ‏‮it‬‏'‏‮s‬‏ ‏‮just‬‏ ‏‮a‬‏ ‏‮warning‬‏ ‏‮to‬‏ ‏‮the‬‏ ‏‮user‬‏ ‏‮that‬‏ ‏‮JavaScript‬‏ ‏‮is‬‏ ‏‮required‬‏ ‏‮to‬‏ ‏‮use‬‏ ‏‮the‬‏ ‏‮app‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/without-javascript)."}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "‏‮The‬‏ ‏‮page‬‏ ‏‮body‬‏ ‏‮should‬‏ ‏‮render‬‏ ‏‮some‬‏ ‏‮content‬‏ ‏‮if‬‏ ‏‮its‬‏ ‏‮scripts‬‏ ‏‮are‬‏ ‏‮not‬‏ ‏‮available‬‏."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "‏‮Does‬‏ ‏‮not‬‏ ‏‮provide‬‏ ‏‮fallback‬‏ ‏‮content‬‏ ‏‮when‬‏ ‏‮JavaScript‬‏ ‏‮is‬‏ ‏‮not‬‏ ‏‮available‬‏"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "‏‮Contains‬‏ ‏‮some‬‏ ‏‮content‬‏ ‏‮when‬‏ ‏‮JavaScript‬‏ ‏‮is‬‏ ‏‮not‬‏ ‏‮available‬‏"}, "lighthouse-core/audits/works-offline.js | description": {"message": "‏‮If‬‏ ‏‮you‬‏'‏‮re‬‏ ‏‮building‬‏ ‏‮a‬‏ ‏‮Progressive‬‏ ‏‮Web‬‏ ‏‮App‬‏, ‏‮consider‬‏ ‏‮using‬‏ ‏‮a‬‏ ‏‮service‬‏ ‏‮worker‬‏ ‏‮so‬‏ ‏‮that‬‏ ‏‮your‬‏ ‏‮app‬‏ ‏‮can‬‏ ‏‮work‬‏ ‏‮offline‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://web.dev/works-offline)."}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "‏‮Current‬‏ ‏‮page‬‏ ‏‮does‬‏ ‏‮not‬‏ ‏‮respond‬‏ ‏‮with‬‏ ‏‮a‬‏ 200 ‏‮when‬‏ ‏‮offline‬‏"}, "lighthouse-core/audits/works-offline.js | title": {"message": "‏‮Current‬‏ ‏‮page‬‏ ‏‮responds‬‏ ‏‮with‬‏ ‏‮a‬‏ 200 ‏‮when‬‏ ‏‮offline‬‏"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "‏‮The‬‏ ‏‮page‬‏ ‏‮may‬‏ ‏‮not‬‏ ‏‮be‬‏ ‏‮loading‬‏ ‏‮offline‬‏ ‏‮because‬‏ ‏‮your‬‏ ‏‮test‬‏ ‏‮URL‬‏ ({requested}) ‏‮was‬‏ ‏‮redirected‬‏ ‏‮to‬‏ \"{final}\". ‏‮Try‬‏ ‏‮testing‬‏ ‏‮the‬‏ ‏‮second‬‏ ‏‮URL‬‏ ‏‮directly‬‏."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "‏‮These‬‏ ‏‮are‬‏ ‏‮opportunities‬‏ ‏‮to‬‏ ‏‮improve‬‏ ‏‮the‬‏ ‏‮usage‬‏ ‏‮of‬‏ ‏‮ARIA‬‏ ‏‮in‬‏ ‏‮your‬‏ ‏‮application‬‏ ‏‮which‬‏ ‏‮may‬‏ ‏‮enhance‬‏ ‏‮the‬‏ ‏‮experience‬‏ ‏‮for‬‏ ‏‮users‬‏ ‏‮of‬‏ ‏‮assistive‬‏ ‏‮technology‬‏, ‏‮like‬‏ ‏‮a‬‏ ‏‮screen‬‏ ‏‮reader‬‏."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "‏‮ARIA‬‏"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "‏‮These‬‏ ‏‮are‬‏ ‏‮opportunities‬‏ ‏‮to‬‏ ‏‮provide‬‏ ‏‮alternative‬‏ ‏‮content‬‏ ‏‮for‬‏ ‏‮audio‬‏ ‏‮and‬‏ ‏‮video‬‏. ‏‮This‬‏ ‏‮may‬‏ ‏‮improve‬‏ ‏‮the‬‏ ‏‮experience‬‏ ‏‮for‬‏ ‏‮users‬‏ ‏‮with‬‏ ‏‮hearing‬‏ ‏‮or‬‏ ‏‮vision‬‏ ‏‮impairments‬‏."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "‏‮Audio‬‏ ‏‮and‬‏ ‏‮video‬‏"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "‏‮These‬‏ ‏‮items‬‏ ‏‮highlight‬‏ ‏‮common‬‏ ‏‮accessibility‬‏ ‏‮best‬‏ ‏‮practices‬‏."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "‏‮Best‬‏ ‏‮practices‬‏"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "‏‮These‬‏ ‏‮checks‬‏ ‏‮highlight‬‏ ‏‮opportunities‬‏ ‏‮to‬‏ [‏‮improve‬‏ ‏‮the‬‏ ‏‮accessibility‬‏ ‏‮of‬‏ ‏‮your‬‏ ‏‮web‬‏ ‏‮app‬‏](https://developers.google.com/web/fundamentals/accessibility). ‏‮Only‬‏ ‏‮a‬‏ ‏‮subset‬‏ ‏‮of‬‏ ‏‮accessibility‬‏ ‏‮issues‬‏ ‏‮can‬‏ ‏‮be‬‏ ‏‮automatically‬‏ ‏‮detected‬‏ ‏‮so‬‏ ‏‮manual‬‏ ‏‮testing‬‏ ‏‮is‬‏ ‏‮also‬‏ ‏‮encouraged‬‏."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "‏‮These‬‏ ‏‮items‬‏ ‏‮address‬‏ ‏‮areas‬‏ ‏‮which‬‏ ‏‮an‬‏ ‏‮automated‬‏ ‏‮testing‬‏ ‏‮tool‬‏ ‏‮cannot‬‏ ‏‮cover‬‏. ‏‮Learn‬‏ ‏‮more‬‏ ‏‮in‬‏ ‏‮our‬‏ ‏‮guide‬‏ ‏‮on‬‏ [‏‮conducting‬‏ ‏‮an‬‏ ‏‮accessibility‬‏ ‏‮review‬‏](https://developers.google.com/web/fundamentals/accessibility/how-to-review)."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "‏‮Accessibility‬‏"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "‏‮These‬‏ ‏‮are‬‏ ‏‮opportunities‬‏ ‏‮to‬‏ ‏‮improve‬‏ ‏‮the‬‏ ‏‮legibility‬‏ ‏‮of‬‏ ‏‮your‬‏ ‏‮content‬‏."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "‏‮Contrast‬‏"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "‏‮These‬‏ ‏‮are‬‏ ‏‮opportunities‬‏ ‏‮to‬‏ ‏‮improve‬‏ ‏‮the‬‏ ‏‮interpretation‬‏ ‏‮of‬‏ ‏‮your‬‏ ‏‮content‬‏ ‏‮by‬‏ ‏‮users‬‏ ‏‮in‬‏ ‏‮different‬‏ ‏‮locales‬‏."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "‏‮Internationalization‬‏ ‏‮and‬‏ ‏‮localization‬‏"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "‏‮These‬‏ ‏‮are‬‏ ‏‮opportunities‬‏ ‏‮to‬‏ ‏‮improve‬‏ ‏‮the‬‏ ‏‮semantics‬‏ ‏‮of‬‏ ‏‮the‬‏ ‏‮controls‬‏ ‏‮in‬‏ ‏‮your‬‏ ‏‮application‬‏. ‏‮This‬‏ ‏‮may‬‏ ‏‮enhance‬‏ ‏‮the‬‏ ‏‮experience‬‏ ‏‮for‬‏ ‏‮users‬‏ ‏‮of‬‏ ‏‮assistive‬‏ ‏‮technology‬‏, ‏‮like‬‏ ‏‮a‬‏ ‏‮screen‬‏ ‏‮reader‬‏."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "‏‮Names‬‏ ‏‮and‬‏ ‏‮labels‬‏"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "‏‮These‬‏ ‏‮are‬‏ ‏‮opportunities‬‏ ‏‮to‬‏ ‏‮improve‬‏ ‏‮keyboard‬‏ ‏‮navigation‬‏ ‏‮in‬‏ ‏‮your‬‏ ‏‮application‬‏."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "‏‮Navigation‬‏"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "‏‮These‬‏ ‏‮are‬‏ ‏‮opportunities‬‏ ‏‮to‬‏ ‏‮to‬‏ ‏‮improve‬‏ ‏‮the‬‏ ‏‮experience‬‏ ‏‮of‬‏ ‏‮reading‬‏ ‏‮tabular‬‏ ‏‮or‬‏ ‏‮list‬‏ ‏‮data‬‏ ‏‮using‬‏ ‏‮assistive‬‏ ‏‮technology‬‏, ‏‮like‬‏ ‏‮a‬‏ ‏‮screen‬‏ ‏‮reader‬‏."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "‏‮Tables‬‏ ‏‮and‬‏ ‏‮lists‬‏"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "‏‮Best‬‏ ‏‮Practices‬‏"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "‏‮Performance‬‏ ‏‮budgets‬‏ ‏‮set‬‏ ‏‮standards‬‏ ‏‮for‬‏ ‏‮the‬‏ ‏‮performance‬‏ ‏‮of‬‏ ‏‮your‬‏ ‏‮site‬‏."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "‏‮Budgets‬‏"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "‏‮More‬‏ ‏‮information‬‏ ‏‮about‬‏ ‏‮the‬‏ ‏‮performance‬‏ ‏‮of‬‏ ‏‮your‬‏ ‏‮application‬‏. ‏‮These‬‏ ‏‮numbers‬‏ ‏‮don‬‏'‏‮t‬‏ [‏‮directly‬‏ ‏‮affect‬‏](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) ‏‮the‬‏ ‏‮Performance‬‏ ‏‮score‬‏."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "‏‮Diagnostics‬‏"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "‏‮The‬‏ ‏‮most‬‏ ‏‮critical‬‏ ‏‮aspect‬‏ ‏‮of‬‏ ‏‮performance‬‏ ‏‮is‬‏ ‏‮how‬‏ ‏‮quickly‬‏ ‏‮pixels‬‏ ‏‮are‬‏ ‏‮rendered‬‏ ‏‮onscreen‬‏. ‏‮Key‬‏ ‏‮metrics‬‏: ‏‮First‬‏ ‏‮Contentful‬‏ ‏‮Paint‬‏, ‏‮First‬‏ ‏‮Meaningful‬‏ ‏‮Paint‬‏"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "‏‮First‬‏ ‏‮Paint‬‏ ‏‮Improvements‬‏"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "‏‮These‬‏ ‏‮suggestions‬‏ ‏‮can‬‏ ‏‮help‬‏ ‏‮your‬‏ ‏‮page‬‏ ‏‮load‬‏ ‏‮faster‬‏. ‏‮They‬‏ ‏‮don‬‏'‏‮t‬‏ [‏‮directly‬‏ ‏‮affect‬‏](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) ‏‮the‬‏ ‏‮Performance‬‏ ‏‮score‬‏."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "‏‮Opportunities‬‏"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "‏‮Metrics‬‏"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "‏‮Enhance‬‏ ‏‮the‬‏ ‏‮overall‬‏ ‏‮loading‬‏ ‏‮experience‬‏, ‏‮so‬‏ ‏‮the‬‏ ‏‮page‬‏ ‏‮is‬‏ ‏‮responsive‬‏ ‏‮and‬‏ ‏‮ready‬‏ ‏‮to‬‏ ‏‮use‬‏ ‏‮as‬‏ ‏‮soon‬‏ ‏‮as‬‏ ‏‮possible‬‏. ‏‮Key‬‏ ‏‮metrics‬‏: ‏‮Time‬‏ ‏‮to‬‏ ‏‮Interactive‬‏, ‏‮Speed‬‏ ‏‮Index‬‏"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "‏‮Overall‬‏ ‏‮Improvements‬‏"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "‏‮Performance‬‏"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "‏‮These‬‏ ‏‮checks‬‏ ‏‮validate‬‏ ‏‮the‬‏ ‏‮aspects‬‏ ‏‮of‬‏ ‏‮a‬‏ ‏‮Progressive‬‏ ‏‮Web‬‏ ‏‮App‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://developers.google.com/web/progressive-web-apps/checklist)."}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "‏‮These‬‏ ‏‮checks‬‏ ‏‮are‬‏ ‏‮required‬‏ ‏‮by‬‏ ‏‮the‬‏ ‏‮baseline‬‏ [‏‮PWA‬‏ ‏‮Checklist‬‏](https://developers.google.com/web/progressive-web-apps/checklist) ‏‮but‬‏ ‏‮are‬‏ ‏‮not‬‏ ‏‮automatically‬‏ ‏‮checked‬‏ ‏‮by‬‏ ‏‮Lighthouse‬‏. ‏‮They‬‏ ‏‮do‬‏ ‏‮not‬‏ ‏‮affect‬‏ ‏‮your‬‏ ‏‮score‬‏ ‏‮but‬‏ ‏‮it‬‏'‏‮s‬‏ ‏‮important‬‏ ‏‮that‬‏ ‏‮you‬‏ ‏‮verify‬‏ ‏‮them‬‏ ‏‮manually‬‏."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "‏‮Progressive‬‏ ‏‮Web‬‏ ‏‮App‬‏"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "‏‮Fast‬‏ ‏‮and‬‏ ‏‮reliable‬‏"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "‏‮Installable‬‏"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "‏‮PWA‬‏ ‏‮Optimized‬‏"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "‏‮These‬‏ ‏‮checks‬‏ ‏‮ensure‬‏ ‏‮that‬‏ ‏‮your‬‏ ‏‮page‬‏ ‏‮is‬‏ ‏‮optimized‬‏ ‏‮for‬‏ ‏‮search‬‏ ‏‮engine‬‏ ‏‮results‬‏ ‏‮ranking‬‏. ‏‮There‬‏ ‏‮are‬‏ ‏‮additional‬‏ ‏‮factors‬‏ ‏‮Lighthouse‬‏ ‏‮does‬‏ ‏‮not‬‏ ‏‮check‬‏ ‏‮that‬‏ ‏‮may‬‏ ‏‮affect‬‏ ‏‮your‬‏ ‏‮search‬‏ ‏‮ranking‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://support.google.com/webmasters/answer/35769)."}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "‏‮Run‬‏ ‏‮these‬‏ ‏‮additional‬‏ ‏‮validators‬‏ ‏‮on‬‏ ‏‮your‬‏ ‏‮site‬‏ ‏‮to‬‏ ‏‮check‬‏ ‏‮additional‬‏ ‏‮SEO‬‏ ‏‮best‬‏ ‏‮practices‬‏."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "‏‮SEO‬‏"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "‏‮Format‬‏ ‏‮your‬‏ ‏‮HTML‬‏ ‏‮in‬‏ ‏‮a‬‏ ‏‮way‬‏ ‏‮that‬‏ ‏‮enables‬‏ ‏‮crawlers‬‏ ‏‮to‬‏ ‏‮better‬‏ ‏‮understand‬‏ ‏‮your‬‏ ‏‮app‬‏’‏‮s‬‏ ‏‮content‬‏."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "‏‮Content‬‏ ‏‮Best‬‏ ‏‮Practices‬‏"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "‏‮To‬‏ ‏‮appear‬‏ ‏‮in‬‏ ‏‮search‬‏ ‏‮results‬‏, ‏‮crawlers‬‏ ‏‮need‬‏ ‏‮access‬‏ ‏‮to‬‏ ‏‮your‬‏ ‏‮app‬‏."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "‏‮Crawling‬‏ ‏‮and‬‏ ‏‮Indexing‬‏"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "‏‮Make‬‏ ‏‮sure‬‏ ‏‮your‬‏ ‏‮pages‬‏ ‏‮are‬‏ ‏‮mobile‬‏ ‏‮friendly‬‏ ‏‮so‬‏ ‏‮users‬‏ ‏‮don‬‏’‏‮t‬‏ ‏‮have‬‏ ‏‮to‬‏ ‏‮pinch‬‏ ‏‮or‬‏ ‏‮zoom‬‏ ‏‮in‬‏ ‏‮order‬‏ ‏‮to‬‏ ‏‮read‬‏ ‏‮the‬‏ ‏‮content‬‏ ‏‮pages‬‏. [‏‮Learn‬‏ ‏‮more‬‏](https://developers.google.com/search/mobile-sites/)."}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "‏‮Mobile‬‏ ‏‮Friendly‬‏"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "‏‮<PERSON><PERSON>‬‏"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "‏‮Location‬‏"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "‏‮Name‬‏"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "‏‮Requests‬‏"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "‏‮Resource‬‏ ‏‮Type‬‏"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "‏‮Size‬‏"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "‏‮Time‬‏ ‏‮Spent‬‏"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "‏‮Transfer‬‏ ‏‮Size‬‏"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "‏‮URL‬‏"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "‏‮Potential‬‏ ‏‮Savings‬‏"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "‏‮Potential‬‏ ‏‮Savings‬‏"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "‏‮Potential‬‏ ‏‮savings‬‏ ‏‮of‬‏ {wastedBytes, number, bytes} ‏‮KB‬‏"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "‏‮Potential‬‏ ‏‮savings‬‏ ‏‮of‬‏ {wastedMs, number, milliseconds} ‏‮ms‬‏"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "‏‮Document‬‏"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "‏‮Font‬‏"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "‏‮Image‬‏"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "‏‮Media‬‏"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ‏‮ms‬‏"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "‏‮Other‬‏"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "‏‮<PERSON><PERSON><PERSON>‬‏"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} ‏‮s‬‏"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "‏‮Stylesheet‬‏"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "‏‮Third‬‏-‏‮party‬‏"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "‏‮Total‬‏"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "‏‮Something‬‏ ‏‮went‬‏ ‏‮wrong‬‏ ‏‮with‬‏ ‏‮recording‬‏ ‏‮the‬‏ ‏‮trace‬‏ ‏‮over‬‏ ‏‮your‬‏ ‏‮page‬‏ ‏‮load‬‏. ‏‮Please‬‏ ‏‮run‬‏ ‏‮Lighthouse‬‏ ‏‮again‬‏. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "‏‮Timeout‬‏ ‏‮waiting‬‏ ‏‮for‬‏ ‏‮initial‬‏ ‏‮Debugger‬‏ ‏‮Protocol‬‏ ‏‮connection‬‏."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "‏‮Chrome‬‏ ‏‮didn‬‏'‏‮t‬‏ ‏‮collect‬‏ ‏‮any‬‏ ‏‮screenshots‬‏ ‏‮during‬‏ ‏‮the‬‏ ‏‮page‬‏ ‏‮load‬‏. ‏‮Please‬‏ ‏‮make‬‏ ‏‮sure‬‏ ‏‮there‬‏ ‏‮is‬‏ ‏‮content‬‏ ‏‮visible‬‏ ‏‮on‬‏ ‏‮the‬‏ ‏‮page‬‏, ‏‮and‬‏ ‏‮then‬‏ ‏‮try‬‏ ‏‮re‬‏-‏‮running‬‏ ‏‮Lighthouse‬‏. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "‏‮DNS‬‏ ‏‮servers‬‏ ‏‮could‬‏ ‏‮not‬‏ ‏‮resolve‬‏ ‏‮the‬‏ ‏‮provided‬‏ ‏‮domain‬‏."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "‏‮Required‬‏ {artifactName} ‏‮gatherer‬‏ ‏‮encountered‬‏ ‏‮an‬‏ ‏‮error‬‏: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "‏‮An‬‏ ‏‮internal‬‏ ‏‮Chrome‬‏ ‏‮error‬‏ ‏‮occurred‬‏. ‏‮Please‬‏ ‏‮restart‬‏ ‏‮Chrome‬‏ ‏‮and‬‏ ‏‮try‬‏ ‏‮re‬‏-‏‮running‬‏ ‏‮Lighthouse‬‏."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "‏‮Required‬‏ {artifact<PERSON><PERSON>} ‏‮gatherer‬‏ ‏‮did‬‏ ‏‮not‬‏ ‏‮run‬‏."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "‏‮Lighthouse‬‏ ‏‮was‬‏ ‏‮unable‬‏ ‏‮to‬‏ ‏‮reliably‬‏ ‏‮load‬‏ ‏‮the‬‏ ‏‮page‬‏ ‏‮you‬‏ ‏‮requested‬‏. ‏‮Make‬‏ ‏‮sure‬‏ ‏‮you‬‏ ‏‮are‬‏ ‏‮testing‬‏ ‏‮the‬‏ ‏‮correct‬‏ ‏‮URL‬‏ ‏‮and‬‏ ‏‮that‬‏ ‏‮the‬‏ ‏‮server‬‏ ‏‮is‬‏ ‏‮properly‬‏ ‏‮responding‬‏ ‏‮to‬‏ ‏‮all‬‏ ‏‮requests‬‏."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "‏‮Lighthouse‬‏ ‏‮was‬‏ ‏‮unable‬‏ ‏‮to‬‏ ‏‮reliably‬‏ ‏‮load‬‏ ‏‮the‬‏ ‏‮URL‬‏ ‏‮you‬‏ ‏‮requested‬‏ ‏‮because‬‏ ‏‮the‬‏ ‏‮page‬‏ ‏‮stopped‬‏ ‏‮responding‬‏."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "‏‮The‬‏ ‏‮URL‬‏ ‏‮you‬‏ ‏‮have‬‏ ‏‮provided‬‏ ‏‮does‬‏ ‏‮not‬‏ ‏‮have‬‏ ‏‮a‬‏ ‏‮valid‬‏ ‏‮security‬‏ ‏‮certificate‬‏. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "‏‮Chrome‬‏ ‏‮prevented‬‏ ‏‮page‬‏ ‏‮load‬‏ ‏‮with‬‏ ‏‮an‬‏ ‏‮interstitial‬‏. ‏‮Make‬‏ ‏‮sure‬‏ ‏‮you‬‏ ‏‮are‬‏ ‏‮testing‬‏ ‏‮the‬‏ ‏‮correct‬‏ ‏‮URL‬‏ ‏‮and‬‏ ‏‮that‬‏ ‏‮the‬‏ ‏‮server‬‏ ‏‮is‬‏ ‏‮properly‬‏ ‏‮responding‬‏ ‏‮to‬‏ ‏‮all‬‏ ‏‮requests‬‏."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "‏‮Lighthouse‬‏ ‏‮was‬‏ ‏‮unable‬‏ ‏‮to‬‏ ‏‮reliably‬‏ ‏‮load‬‏ ‏‮the‬‏ ‏‮page‬‏ ‏‮you‬‏ ‏‮requested‬‏. ‏‮Make‬‏ ‏‮sure‬‏ ‏‮you‬‏ ‏‮are‬‏ ‏‮testing‬‏ ‏‮the‬‏ ‏‮correct‬‏ ‏‮URL‬‏ ‏‮and‬‏ ‏‮that‬‏ ‏‮the‬‏ ‏‮server‬‏ ‏‮is‬‏ ‏‮properly‬‏ ‏‮responding‬‏ ‏‮to‬‏ ‏‮all‬‏ ‏‮requests‬‏. (‏‮Details‬‏: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "‏‮Lighthouse‬‏ ‏‮was‬‏ ‏‮unable‬‏ ‏‮to‬‏ ‏‮reliably‬‏ ‏‮load‬‏ ‏‮the‬‏ ‏‮page‬‏ ‏‮you‬‏ ‏‮requested‬‏. ‏‮Make‬‏ ‏‮sure‬‏ ‏‮you‬‏ ‏‮are‬‏ ‏‮testing‬‏ ‏‮the‬‏ ‏‮correct‬‏ ‏‮URL‬‏ ‏‮and‬‏ ‏‮that‬‏ ‏‮the‬‏ ‏‮server‬‏ ‏‮is‬‏ ‏‮properly‬‏ ‏‮responding‬‏ ‏‮to‬‏ ‏‮all‬‏ ‏‮requests‬‏. (‏‮Status‬‏ ‏‮code‬‏: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "‏‮Your‬‏ ‏‮page‬‏ ‏‮took‬‏ ‏‮too‬‏ ‏‮long‬‏ ‏‮to‬‏ ‏‮load‬‏. ‏‮Please‬‏ ‏‮follow‬‏ ‏‮the‬‏ ‏‮opportunities‬‏ ‏‮in‬‏ ‏‮the‬‏ ‏‮report‬‏ ‏‮to‬‏ ‏‮reduce‬‏ ‏‮your‬‏ ‏‮page‬‏ ‏‮load‬‏ ‏‮time‬‏, ‏‮and‬‏ ‏‮then‬‏ ‏‮try‬‏ ‏‮re‬‏-‏‮running‬‏ ‏‮Lighthouse‬‏. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "‏‮Waiting‬‏ ‏‮for‬‏ ‏‮DevTools‬‏ ‏‮protocol‬‏ ‏‮response‬‏ ‏‮has‬‏ ‏‮exceeded‬‏ ‏‮the‬‏ ‏‮allotted‬‏ ‏‮time‬‏. (‏‮Method‬‏: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "‏‮Fetching‬‏ ‏‮resource‬‏ ‏‮content‬‏ ‏‮has‬‏ ‏‮exceeded‬‏ ‏‮the‬‏ ‏‮allotted‬‏ ‏‮time‬‏"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "‏‮The‬‏ ‏‮URL‬‏ ‏‮you‬‏ ‏‮have‬‏ ‏‮provided‬‏ ‏‮appears‬‏ ‏‮to‬‏ ‏‮be‬‏ ‏‮invalid‬‏."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "‏‮Show‬‏ ‏‮audits‬‏"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "‏‮Initial‬‏ ‏‮Navigation‬‏"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "‏‮Maximum‬‏ ‏‮critical‬‏ ‏‮path‬‏ ‏‮latency‬‏:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "‏‮Error‬‏!"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "‏‮Report‬‏ ‏‮error‬‏: ‏‮no‬‏ ‏‮audit‬‏ ‏‮information‬‏"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "‏‮Lab‬‏ ‏‮Data‬‏"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "[‏‮Lighthouse‬‏](https://developers.google.com/web/tools/lighthouse/) ‏‮analysis‬‏ ‏‮of‬‏ ‏‮the‬‏ ‏‮current‬‏ ‏‮page‬‏ ‏‮on‬‏ ‏‮an‬‏ ‏‮emulated‬‏ ‏‮mobile‬‏ ‏‮network‬‏. ‏‮Values‬‏ ‏‮are‬‏ ‏‮estimated‬‏ ‏‮and‬‏ ‏‮may‬‏ ‏‮vary‬‏."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "‏‮Additional‬‏ ‏‮items‬‏ ‏‮to‬‏ ‏‮manually‬‏ ‏‮check‬‏"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "‏‮Not‬‏ ‏‮applicable‬‏"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "‏‮Opportunity‬‏"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "‏‮Estimated‬‏ ‏‮Savings‬‏"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "‏‮Passed‬‏ ‏‮audits‬‏"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "‏‮Collapse‬‏ ‏‮snippet‬‏"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "‏‮Expand‬‏ ‏‮snippet‬‏"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "‏‮Show‬‏ 3‏‮rd‬‏-‏‮party‬‏ ‏‮resources‬‏"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "‏‮There‬‏ ‏‮were‬‏ ‏‮issues‬‏ ‏‮affecting‬‏ ‏‮this‬‏ ‏‮run‬‏ ‏‮of‬‏ ‏‮Lighthouse‬‏:"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "‏‮Values‬‏ ‏‮are‬‏ ‏‮estimated‬‏ ‏‮and‬‏ ‏‮may‬‏ ‏‮vary‬‏. ‏‮The‬‏ ‏‮performance‬‏ ‏‮score‬‏ ‏‮is‬‏ [‏‮based‬‏ ‏‮only‬‏ ‏‮on‬‏ ‏‮these‬‏ ‏‮metrics‬‏](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "‏‮Passed‬‏ ‏‮audits‬‏ ‏‮but‬‏ ‏‮with‬‏ ‏‮warnings‬‏"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "‏‮Warnings‬‏: "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "‏‮Consider‬‏ ‏‮uploading‬‏ ‏‮your‬‏ ‏‮GIF‬‏ ‏‮to‬‏ ‏‮a‬‏ ‏‮service‬‏ ‏‮which‬‏ ‏‮will‬‏ ‏‮make‬‏ ‏‮it‬‏ ‏‮available‬‏ ‏‮to‬‏ ‏‮embed‬‏ ‏‮as‬‏ ‏‮an‬‏ ‏‮HTML‬‏5 ‏‮video‬‏."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "‏‮Install‬‏ ‏‮a‬‏ [‏‮lazy‬‏-‏‮load‬‏ ‏‮WordPress‬‏ ‏‮plugin‬‏](https://wordpress.org/plugins/search/lazy+load/) ‏‮that‬‏ ‏‮provides‬‏ ‏‮the‬‏ ‏‮ability‬‏ ‏‮to‬‏ ‏‮defer‬‏ ‏‮any‬‏ ‏‮offscreen‬‏ ‏‮images‬‏, ‏‮or‬‏ ‏‮switch‬‏ ‏‮to‬‏ ‏‮a‬‏ ‏‮theme‬‏ ‏‮that‬‏ ‏‮provides‬‏ ‏‮that‬‏ ‏‮functionality‬‏. ‏‮Also‬‏ ‏‮consider‬‏ ‏‮using‬‏ [‏‮the‬‏ ‏‮AMP‬‏ ‏‮plugin‬‏](https://wordpress.org/plugins/amp/)."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "‏‮There‬‏ ‏‮are‬‏ ‏‮a‬‏ ‏‮number‬‏ ‏‮of‬‏ ‏‮WordPress‬‏ ‏‮plugins‬‏ ‏‮that‬‏ ‏‮can‬‏ ‏‮help‬‏ ‏‮you‬‏ [‏‮inline‬‏ ‏‮critical‬‏ ‏‮assets‬‏](https://wordpress.org/plugins/search/critical+css/) ‏‮or‬‏ [‏‮defer‬‏ ‏‮less‬‏ ‏‮important‬‏ ‏‮resources‬‏](https://wordpress.org/plugins/search/defer+css+javascript/). ‏‮Beware‬‏ ‏‮that‬‏ ‏‮optimizations‬‏ ‏‮provided‬‏ ‏‮by‬‏ ‏‮these‬‏ ‏‮plugins‬‏ ‏‮may‬‏ ‏‮break‬‏ ‏‮features‬‏ ‏‮of‬‏ ‏‮your‬‏ ‏‮theme‬‏ ‏‮or‬‏ ‏‮plugins‬‏, ‏‮so‬‏ ‏‮you‬‏ ‏‮will‬‏ ‏‮likely‬‏ ‏‮need‬‏ ‏‮to‬‏ ‏‮make‬‏ ‏‮code‬‏ ‏‮changes‬‏."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "‏‮Themes‬‏, ‏‮plugins‬‏, ‏‮and‬‏ ‏‮server‬‏ ‏‮specifications‬‏ ‏‮all‬‏ ‏‮contribute‬‏ ‏‮to‬‏ ‏‮server‬‏ ‏‮response‬‏ ‏‮time‬‏. ‏‮Consider‬‏ ‏‮finding‬‏ ‏‮a‬‏ ‏‮more‬‏ ‏‮optimized‬‏ ‏‮theme‬‏, ‏‮carefully‬‏ ‏‮selecting‬‏ ‏‮an‬‏ ‏‮optimization‬‏ ‏‮plugin‬‏, ‏‮and‬‏/‏‮or‬‏ ‏‮upgrading‬‏ ‏‮your‬‏ ‏‮server‬‏."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "‏‮Consider‬‏ ‏‮showing‬‏ ‏‮excerpts‬‏ ‏‮in‬‏ ‏‮your‬‏ ‏‮post‬‏ ‏‮lists‬‏ (‏‮e‬‏.‏‮g‬‏. ‏‮via‬‏ ‏‮the‬‏ ‏‮more‬‏ ‏‮tag‬‏), ‏‮reducing‬‏ ‏‮the‬‏ ‏‮number‬‏ ‏‮of‬‏ ‏‮posts‬‏ ‏‮shown‬‏ ‏‮on‬‏ ‏‮a‬‏ ‏‮given‬‏ ‏‮page‬‏, ‏‮breaking‬‏ ‏‮your‬‏ ‏‮long‬‏ ‏‮posts‬‏ ‏‮into‬‏ ‏‮multiple‬‏ ‏‮pages‬‏, ‏‮or‬‏ ‏‮using‬‏ ‏‮a‬‏ ‏‮plugin‬‏ ‏‮to‬‏ ‏‮lazy‬‏-‏‮load‬‏ ‏‮comments‬‏."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "‏‮A‬‏ ‏‮number‬‏ ‏‮of‬‏ [‏‮WordPress‬‏ ‏‮plugins‬‏](https://wordpress.org/plugins/search/minify+css/) ‏‮can‬‏ ‏‮speed‬‏ ‏‮up‬‏ ‏‮your‬‏ ‏‮site‬‏ ‏‮by‬‏ ‏‮concatenating‬‏, ‏‮minifying‬‏, ‏‮and‬‏ ‏‮compressing‬‏ ‏‮your‬‏ ‏‮styles‬‏. ‏‮You‬‏ ‏‮may‬‏ ‏‮also‬‏ ‏‮want‬‏ ‏‮to‬‏ ‏‮use‬‏ ‏‮a‬‏ ‏‮build‬‏ ‏‮process‬‏ ‏‮to‬‏ ‏‮do‬‏ ‏‮this‬‏ ‏‮minification‬‏ ‏‮up‬‏-‏‮front‬‏ ‏‮if‬‏ ‏‮possible‬‏."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "‏‮A‬‏ ‏‮number‬‏ ‏‮of‬‏ [‏‮WordPress‬‏ ‏‮plugins‬‏](https://wordpress.org/plugins/search/minify+javascript/) ‏‮can‬‏ ‏‮speed‬‏ ‏‮up‬‏ ‏‮your‬‏ ‏‮site‬‏ ‏‮by‬‏ ‏‮concatenating‬‏, ‏‮minifying‬‏, ‏‮and‬‏ ‏‮compressing‬‏ ‏‮your‬‏ ‏‮scripts‬‏. ‏‮You‬‏ ‏‮may‬‏ ‏‮also‬‏ ‏‮want‬‏ ‏‮to‬‏ ‏‮use‬‏ ‏‮a‬‏ ‏‮build‬‏ ‏‮process‬‏ ‏‮to‬‏ ‏‮do‬‏ ‏‮this‬‏ ‏‮minification‬‏ ‏‮up‬‏ ‏‮front‬‏ ‏‮if‬‏ ‏‮possible‬‏."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "‏‮Consider‬‏ ‏‮reducing‬‏, ‏‮or‬‏ ‏‮switching‬‏, ‏‮the‬‏ ‏‮number‬‏ ‏‮of‬‏ [‏‮WordPress‬‏ ‏‮plugins‬‏](https://wordpress.org/plugins/) ‏‮loading‬‏ ‏‮unused‬‏ ‏‮CSS‬‏ ‏‮in‬‏ ‏‮your‬‏ ‏‮page‬‏. ‏‮To‬‏ ‏‮identify‬‏ ‏‮plugins‬‏ ‏‮that‬‏ ‏‮are‬‏ ‏‮adding‬‏ ‏‮extraneous‬‏ ‏‮CSS‬‏, ‏‮try‬‏ ‏‮running‬‏ [‏‮code‬‏ ‏‮coverage‬‏](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) ‏‮in‬‏ ‏‮Chrome‬‏ ‏‮DevTools‬‏. ‏‮You‬‏ ‏‮can‬‏ ‏‮identify‬‏ ‏‮the‬‏ ‏‮theme‬‏/‏‮plugin‬‏ ‏‮responsible‬‏ ‏‮from‬‏ ‏‮the‬‏ ‏‮URL‬‏ ‏‮of‬‏ ‏‮the‬‏ ‏‮stylesheet‬‏. ‏‮Look‬‏ ‏‮out‬‏ ‏‮for‬‏ ‏‮plugins‬‏ ‏‮that‬‏ ‏‮have‬‏ ‏‮many‬‏ ‏‮stylesheets‬‏ ‏‮in‬‏ ‏‮the‬‏ ‏‮list‬‏ ‏‮which‬‏ ‏‮have‬‏ ‏‮a‬‏ ‏‮lot‬‏ ‏‮of‬‏ ‏‮red‬‏ ‏‮in‬‏ ‏‮code‬‏ ‏‮coverage‬‏. ‏‮A‬‏ ‏‮plugin‬‏ ‏‮should‬‏ ‏‮only‬‏ ‏‮enqueue‬‏ ‏‮a‬‏ ‏‮stylesheet‬‏ ‏‮if‬‏ ‏‮it‬‏ ‏‮is‬‏ ‏‮actually‬‏ ‏‮used‬‏ ‏‮on‬‏ ‏‮the‬‏ ‏‮page‬‏."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "‏‮Consider‬‏ ‏‮reducing‬‏, ‏‮or‬‏ ‏‮switching‬‏, ‏‮the‬‏ ‏‮number‬‏ ‏‮of‬‏ [‏‮WordPress‬‏ ‏‮plugins‬‏](https://wordpress.org/plugins/) ‏‮loading‬‏ ‏‮unused‬‏ ‏‮JavaScript‬‏ ‏‮in‬‏ ‏‮your‬‏ ‏‮page‬‏. ‏‮To‬‏ ‏‮identify‬‏ ‏‮plugins‬‏ ‏‮that‬‏ ‏‮are‬‏ ‏‮adding‬‏ ‏‮extraneous‬‏ ‏‮JS‬‏, ‏‮try‬‏ ‏‮running‬‏ [‏‮code‬‏ ‏‮coverage‬‏](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) ‏‮in‬‏ ‏‮Chrome‬‏ ‏‮DevTools‬‏. ‏‮You‬‏ ‏‮can‬‏ ‏‮identify‬‏ ‏‮the‬‏ ‏‮theme‬‏/‏‮plugin‬‏ ‏‮responsible‬‏ ‏‮from‬‏ ‏‮the‬‏ ‏‮URL‬‏ ‏‮of‬‏ ‏‮the‬‏ ‏‮script‬‏. ‏‮Look‬‏ ‏‮out‬‏ ‏‮for‬‏ ‏‮plugins‬‏ ‏‮that‬‏ ‏‮have‬‏ ‏‮many‬‏ ‏‮scripts‬‏ ‏‮in‬‏ ‏‮the‬‏ ‏‮list‬‏ ‏‮which‬‏ ‏‮have‬‏ ‏‮a‬‏ ‏‮lot‬‏ ‏‮of‬‏ ‏‮red‬‏ ‏‮in‬‏ ‏‮code‬‏ ‏‮coverage‬‏. ‏‮A‬‏ ‏‮plugin‬‏ ‏‮should‬‏ ‏‮only‬‏ ‏‮enqueue‬‏ ‏‮a‬‏ ‏‮script‬‏ ‏‮if‬‏ ‏‮it‬‏ ‏‮is‬‏ ‏‮actually‬‏ ‏‮used‬‏ ‏‮on‬‏ ‏‮the‬‏ ‏‮page‬‏."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "‏‮Read‬‏ ‏‮about‬‏ [‏‮Browser‬‏ ‏‮Caching‬‏ ‏‮in‬‏ ‏‮WordPress‬‏](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "‏‮Consider‬‏ ‏‮using‬‏ ‏‮an‬‏ [‏‮image‬‏ ‏‮optimization‬‏ ‏‮WordPress‬‏ ‏‮plugin‬‏](https://wordpress.org/plugins/search/optimize+images/) ‏‮that‬‏ ‏‮compresses‬‏ ‏‮your‬‏ ‏‮images‬‏ ‏‮while‬‏ ‏‮retaining‬‏ ‏‮quality‬‏."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "‏‮Upload‬‏ ‏‮images‬‏ ‏‮directly‬‏ ‏‮through‬‏ ‏‮the‬‏ [‏‮media‬‏ ‏‮library‬‏](https://codex.wordpress.org/Media_Library_Screen) ‏‮to‬‏ ‏‮ensure‬‏ ‏‮that‬‏ ‏‮the‬‏ ‏‮required‬‏ ‏‮image‬‏ ‏‮sizes‬‏ ‏‮are‬‏ ‏‮available‬‏, ‏‮and‬‏ ‏‮then‬‏ ‏‮insert‬‏ ‏‮them‬‏ ‏‮from‬‏ ‏‮the‬‏ ‏‮media‬‏ ‏‮library‬‏ ‏‮or‬‏ ‏‮use‬‏ ‏‮the‬‏ ‏‮image‬‏ ‏‮widget‬‏ ‏‮to‬‏ ‏‮ensure‬‏ ‏‮the‬‏ ‏‮optimal‬‏ ‏‮image‬‏ ‏‮sizes‬‏ ‏‮are‬‏ ‏‮used‬‏ (‏‮including‬‏ ‏‮those‬‏ ‏‮for‬‏ ‏‮the‬‏ ‏‮responsive‬‏ ‏‮breakpoints‬‏). ‏‮Avoid‬‏ ‏‮using‬‏ `Full Size` ‏‮images‬‏ ‏‮unless‬‏ ‏‮the‬‏ ‏‮dimensions‬‏ ‏‮are‬‏ ‏‮adequate‬‏ ‏‮for‬‏ ‏‮their‬‏ ‏‮usage‬‏. [‏‮Learn‬‏ ‏‮More‬‏](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)."}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "‏‮You‬‏ ‏‮can‬‏ ‏‮enable‬‏ ‏‮text‬‏ ‏‮compression‬‏ ‏‮in‬‏ ‏‮your‬‏ ‏‮web‬‏ ‏‮server‬‏ ‏‮configuration‬‏."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "‏‮Consider‬‏ ‏‮using‬‏ ‏‮a‬‏ [‏‮plugin‬‏](https://wordpress.org/plugins/search/convert+webp/) ‏‮or‬‏ ‏‮service‬‏ ‏‮that‬‏ ‏‮will‬‏ ‏‮automatically‬‏ ‏‮convert‬‏ ‏‮your‬‏ ‏‮uploaded‬‏ ‏‮images‬‏ ‏‮to‬‏ ‏‮the‬‏ ‏‮optimal‬‏ ‏‮formats‬‏."}}