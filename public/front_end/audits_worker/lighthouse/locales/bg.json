{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "Клавишите за достъп дават възможност на потребителите бързо да преместят фокуса към определена част от страницата. За правилно навигиране всеки клавиш за достъп трябва да е уникален. [Научете повече](https://web.dev/accesskeys/)"}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Някои стойности на `[accesskey]` не са уникални"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "Стойностите за `[accesskey]` са уникални"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Всеки елемент ARIA `role` поддържа конкретен поднабор от атрибути `aria-*`. При несъответствие атрибутите `aria-*` ще станат невалидни. [Научете повече](https://web.dev/aria-allowed-attr/)"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Някои атрибути `[aria-*]` не съответстват на ролите си"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Атрибутите `[aria-*]` съответстват на ролите си"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "Някои роли на ARIA имат задължителни атрибути, от които екранните четци получават описание на състоянието на съответния елемент. [Научете повече](https://web.dev/aria-required-attr/)."}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Някои елементи `[role]` нямат всички задължителни атрибути `[aria-*]`"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "Елементите `[role]` имат всички задължителни атрибути `[aria-*]`"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "Някои родителски роли на ARIA трябва да съдържат конкретни дъщерни роли, за да изпълняват функциите за достъпност, за които са предназначени. [Научете повече](https://web.dev/aria-required-children/)."}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Някои елементи с ARIA роля `[role]`, за които се изисква дъщерните им елементи да включват конкретна роля `[role]`, не съдържат някои или всички такива задължителни дъщерни елементи."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "Елементите с ARIA роля `[role]`, за които се изисква дъщерните им елементи да включват конкретна роля `[role]`, съдържат всички задължителни дъщерни елементи."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "Някои дъщерни роли на ARIA трябва да се съдържат в конкретни родителски роли, за да изпълняват правилно функциите за достъпност, за които са предназначени. [Научете повече](https://web.dev/aria-required-parent/)."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Някои елементи `[role]` не се съдържат в задължителния за тях родителски елемент"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "Елементите `[role]` се съдържат в задължителния за тях родителски елемент"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "Ролите на ARIA трябва да имат валидни стойности, за да изпълняват функциите за достъпност, за които са предназначени. [Научете повече](https://web.dev/aria-roles/)."}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "Някои стойности на `[role]` не са валидни"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "Стойностите на `[role]` са валидни"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Помощните технологии, като например екранни четци, не могат да интерпретират атрибути на ARIA с невалидни стойности. [Научете повече](https://web.dev/aria-valid-attr-value/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Някои атрибути `[aria-*]` нямат валидни стойности"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Атрибутите `[aria-*]` имат валидни стойности"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "Помощните технологии, като например екранни четци, не могат да интерпретират атрибути на ARIA с невалидни имена. [Научете повече](https://web.dev/aria-valid-attr/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Някои атрибути `[aria-*]` не са валидни или са изписани неправилно"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "Атрибутите `[aria-*]` са валидни и са изписани правилно"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "Надписите правят аудиоелементите използваеми за потребителите със слухови увреждания, като предоставят важна информация, например кой говори и какво казва, както и друга информация, несвързана с речта. [Научете повече](https://web.dev/audio-caption/)."}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "За някои елементи `<audio>` липсва елемент `<track>` с `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "Елементите `<audio>` съдържат елемент `<track>` с `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Елементи с грешки"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "Когато даден бутон няма достъпно име, той ще бъде прочитан като „бутон“ от екранните четци и съответно ще бъде неизползваем за потребителите им. [Научете повече](https://web.dev/button-name/)."}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "Бутоните нямат достъпни имена"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "Бутоните имат достъпни имена"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "Добавянето на начини за заобикаляне на повтарящото се съдържание дава възможност на потребителите, използващи клавиатура, да навигират по-ефективно в страницата. [Научете повече](https://web.dev/bypass/)."}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "Страницата не съдържа заглавие, връзка за пропускане или участък с ориентир"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "Страницата съдържа заглавие, връзка за пропускане или участък с ориентир"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "Четенето на текст с нисък контраст е трудно или невъзможно за много потребители. [Научете повече](https://web.dev/color-contrast/)."}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Коефициентът на контрастност между цветовете на заден и преден план не е достатъчно голям."}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "Коефициентът на контрастност между цветовете на заден и преден план е достатъчно голям"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "Когато списъците с определения не са маркирани правилно, екранните четци може да предоставят объркваща или неточна информация. [Научете повече](https://web.dev/definition-list/)."}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "Елементите `<dl>` не съдържат само правилно подредени групи `<dt>` и `<dd>` и елементи `<script>` или `<template>`."}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "Елементите `<dl>` съдържат само правилно подредени групи `<dt>` и `<dd>` и елементи `<script>` или `<template>`."}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "Списъчните елементи за определение (`<dt>` и `<dd>`) трябва да бъдат обвити в родителски елемент `<dl>`, за да бъдат прочетени правилно от екранните четци. [Научете повече](https://web.dev/dlitem/)."}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "Някои списъчни елементи за определение не са обвити в елементи `<dl>`"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "Списъчните елементи за определение са обвити в елементи `<dl>`"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "Заглавието дава възможност на потребителите на екранни четци да добият обща представа за страницата, а потребителите на търсещи машини разчитат на него в голяма степен, за да определят дали страницата е подходяща за търсенето им. [Научете повече](https://web.dev/document-title/)."}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "Документът няма елемент `<title>`"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "Документът има елемент `<title>`"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "Стойността на всеки атрибут id трябва да е уникална, за да се предотврати пропускането на други екземпляри от страна на помощните технологии. [Научете повече](https://web.dev/duplicate-id/)."}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "Някои атрибути `[id]` на страницата не са уникални"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "Атрибутите `[id]` на страницата са уникални"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "Потребителите на екранни четци очакват заглавието на рамката да описва съдържанието й. [Научете повече](https://web.dev/frame-title/)."}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "Някои елементи `<frame>` или `<iframe>` нямат заглавие"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "Елементите `<frame>` или `<iframe>` имат заглавие"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "Ако за дадена страница не е посочен атрибут lang, екранният четец приема, че тя е написана на стандартния език, който потребителят е избрал при настройването му. Ако страницата всъщност не е на стандартния език, екранният четец може да не прочете текста й правилно. [Научете повече](https://web.dev/html-has-lang/)."}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Елементът `<html>` няма атрибут `[lang]`"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "Елементът `<html>` има атрибут `[lang]`"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "Посочването на валиден [език по BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) помага на екранните четци да четат текста правилно. [Научете повече](https://web.dev/html-lang-valid/)."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Елементът `<html>` няма валидна стойност за атрибута `[lang]`."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "Елементът `<html>` има валидна стойност за атрибута `[lang]`"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "Информативните елементи трябва да имат кратък, описателен алтернативен текст. При декоративните елементи атрибутът alt може да бъде оставен без стойност. [Научете повече](https://web.dev/image-alt/)."}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "Някои графични елементи нямат атрибути `[alt]`"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "Графичните елементи имат атрибути `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "Когато за бутон от тип `<input>` се използва изображение, предоставянето на алтернативен текст помага на потребителите на екранни четци да разберат за какво служи бутонът. [Научете повече](https://web.dev/input-image-alt/)."}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Някои елементи `<input type=\"image\">` нямат алтернативен текст `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "Елементите `<input type=\"image\">` имат алтернативен текст (`[alt]`)"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "Етикетите дават възможност на помощните технологии, като например екранни четци, да четат правилно контролите във формуляри. [Научете повече](https://web.dev/label/)."}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "Някои елементи на формуляра нямат свързани етикети"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "Елементите на формуляра имат свързани етикети"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "Ако дадена таблица се използва само за оформление, тя не трябва да съдържа елементи с данни, като например елементите th или caption или атрибута summary, тъй като това може да създаде объркване за потребителите на екранни четци. [Научете повече](https://web.dev/layout-table/)."}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "Презентационните елементи `<table>` използват `<th>`, `<caption>` или атрибута `[summary]`."}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "Презентационните елементи `<table>` не използват `<th>`, `<caption>` или атрибута `[summary]`."}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "Текстът на връзките (и алтернативният текст за изображения, когато се използват за връзки), който е различим, уникален и дава възможност фокусът да бъде поставен върху него, подобрява навигирането за потребителите на екранни четци. [Научете повече](https://web.dev/link-name/)."}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "Някои връзки нямат отличително име"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "Връзките имат отличителни имена"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "Екранните четци съобщават съдържанието на списъците по специфичен начин. Правилното структуриране на списъците улеснява четенето им от екранните четци. [Научете повече](https://web.dev/list/)."}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "Някои списъци не съдържат само елементи `<li>` и елементи за поддръжка на скриптове (`<script>` и `<template>`)."}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "Списъците съдържат само елементи `<li>` и елементи за поддръжка на скриптове (`<script>` и `<template>`)."}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "Екранните четци изискват списъчните елементи (`<li>`) да се съдържат в родителски елемент `<ul>` или `<ol>`, за да бъдат прочетени правилно. [Научете повече](https://web.dev/listitem/)."}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "Някои списъчни елементи (`<li>`) не се съдържат в родителски елементи `<ul>` или `<ol>`."}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "Списъчните елементи (`<li>`) се съдържат в родителски елементи `<ul>` или `<ol>`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "Потребителите не очакват страницата да се опресни автоматично и ако това се случи, фокусът ще бъде върнат в горната й част. Това може да бъде дразнещо или объркващо за потребителите. [Научете повече](https://web.dev/meta-refresh/)."}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Документът използва `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "Документът не използва `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "Невъзможността за промяна на мащаба създава проблем за потребителите със слабо зрение, които разчитат на увеличението на екрана, за да виждат добре съдържанието на уеб страниците. [Научете повече](https://web.dev/meta-viewport/)."}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` се използва в елемента `<meta name=\"viewport\">` или стойността на атрибута `[maximum-scale]` е по-малка от 5."}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` не се използва в елемента `<meta name=\"viewport\">` и стойността на атрибута `[maximum-scale]` не е по-малка от 5."}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "Екранните четци не могат да интерпретират нетекстово съдържание. Добавянето на алтернативен текст към елементите `<object>` помага на екранните четци да предават смисъла им на потребителите. [Научете повече](https://web.dev/object-alt/)."}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "Някои елементи `<object>` нямат алтернативен текст `[alt]`"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "Елементите `<object>` имат алтернативен текст (`[alt]`)"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "Ако стойността е по-голяма от 0, значи се използва изричен ред на навигиране. Въпреки че е технически валидно, това често създава неудобства за потребителите, които разчитат на помощни технологии. [Научете повече](https://web.dev/tabindex/)."}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "Някои елементи имат атрибут `[tabindex]` със стойност, по-голяма от 0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "Никой от елементите няма атрибут `[tabindex]` със стойност, по-голяма от 0"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "Екранните четци имат функции за улесняване на навигирането в таблици. Когато клетките от типа `<td>`, използващи атрибута `[headers]`, сочат само към други клетки от същата таблица, това може да подобри практическата работа за потребителите на екранни четци. [Научете повече](https://web.dev/td-headers-attr/)."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Някои клетки в елемент `<table>`, които използват атрибута `[headers]`, сочат към елемент `id`, който не бе намерен в същата таблица."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "Клетките в елемент `<table>`, които използват атрибута `[headers]`, сочат към клетки от същата таблица."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "Екранните четци имат функции за улесняване на навигирането в таблици. Когато всички заглавки в таблицата сочат към някакъв набор от клетки, това може да подобри практическата работа за потребителите на екранни четци. [Научете повече](https://web.dev/th-has-data-cells/)."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Някои елементи `<th>` и елементи с `[role=\"columnheader\"/\"rowheader\"]` нямат клетки с данни, за които служат като описание."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "Елементите `<th>` и тези с `[role=\"columnheader\"/\"rowheader\"]` имат клетки с данни, за които служат като описание."}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "Посочването на валиден [език по BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) за елементите дава възможност на екранните четци да произнасят текста правилно. [Научете повече](https://web.dev/valid-lang/)."}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Някои атрибути `[lang]` нямат валидна стойност"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "Атрибутите `[lang]` имат валидна стойност"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "Наличието на надпис за даден видеоклип улеснява достъпа до съответната информация за потребителите със слухови увреждания. [Научете повече](https://web.dev/video-caption/)."}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "Някои елементи `<video>` не съдържат елемент `<track>` с `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "Елементите `<video>` съдържат елемент `<track>` с `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "Аудиоописанията предоставят полезна информация за видеоклиповете (например за обстановката и изражението на лицата на героите), която потребителите не биха могли да разберат от диалога. [Научете повече](https://web.dev/video-description/)."}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "Някои елементи `<video>` не съдържат елемент `<track>` с `[kind=\"description\"]`."}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "Елементите `<video>` съдържат елемент `<track>` с `[kind=\"description\"]`"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "За най-добро изобразяване под iOS, когато потребителите добавят прогресивно уеб приложение (PWA) към началния екран, дефинирайте атрибут `apple-touch-icon`. Той трябва да сочи към непрозрачен квадратен PNG файл със страна от 192 (или 180) пиксела. [Научете повече](https://web.dev/apple-touch-icon/)."}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "Не осигурява валиден атрибут `apple-touch-icon`"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "Атрибутът `apple-touch-icon-precomposed` не е актуален, препоръчва се `apple-touch-icon`."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "Съдържа валиден атрибут `apple-touch-icon`"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Зареждането на тази страница се забавя от разширения за Chrome. Опитайте да я проверите в режим „инкогнито“ или от потребителски профил в Chrome без инсталирани разширения."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "Проверка на скрипта"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "Синтактичен анализ на скрипта"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "Общо процесорно време"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "Препоръчваме да намалите времето, прекарвано в синтактичен анализ, компилиране и изпълнение на JS. Използването на JS ресурси с по-малък размер може да помогне за това. [Научете повече](https://web.dev/bootup-time)."}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "Намалете времето за изпълнение на JavaScript"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "Време за изпълнение на JavaScript"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Големите GIF файлове не са ефективни за показване на анимирано съдържание. Вместо това препоръчваме да използвате видеоклипове във формат MPEG4/WebM за анимации и PNG/WebP за статични изображения, за да намалите преноса на данни. [Научете повече](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Използвайте видеоформати за анимираното съдържание"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "За да намалите времето до интерактивност, препоръчваме скритите изображения и тези извън видимата част на екрана да се зареждат след всички критични ресурси. [Научете повече](https://web.dev/offscreen-images)."}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Отложете зареждането на изображенията извън видимата част на екрана"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Ресурси блокират първото изобразяване на страницата ви. Препоръчваме да вградите критичните JS/CSS елементи и да отложите зареждането на всички некритични стилове или JS код. [Научете повече](https://web.dev/render-blocking-resources)."}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Елиминирайте ресурсите, които блокират изобразяването"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Мрежовите ресурси с голям размер струват пари на потребителите и са тясно свързани с бавното зареждане. [Научете повече](https://web.dev/total-byte-weight)."}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Общият размер бе {totalBytes, number, bytes} КБ"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Не използвайте мрежови ресурси с голям размер"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Не се използват мрежови ресурси с голям размер"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "Минимизирането на файловете със CSS може да намали размера на мрежовите ресурси. [Научете повече](https://web.dev/unminified-css)."}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "Минимизирайте CSS"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Минимизирането на файловете с JavaScript може да намали размера на ресурсите и времето за синтактичен анализ на скрипта. [Научете повече](https://web.dev/unminified-javascript)."}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Минимизирайте JavaScript"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Премахнете ненужните правила от стиловите листове и отложете зареждането на CSS кода, който не се използва за съдържанието на видимата на екрана част от страницата, за да намалите ненужния пренос на данни в мрежата. [Научете повече](https://web.dev/unused-css-rules)."}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Премахнете неизползвания CSS код"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Премахнете неизползвания JavaScript, за да намалите преноса на данни при мрежовата активност."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Премахнете неизползвания JavaScript"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Продължителното съхраняване в кеша може да ускори повторните посещения на страницата ви. [Научете повече](https://web.dev/uses-long-cache-ttl)."}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Намерен е 1 ресурс}other{Намерени са # ресурса}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Използвайте ефективни правила за кеша, за да улесните показването на статичните активи"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Използват се ефективни правила за кеширане на статичните активи"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Оптимизираните изображения се зареждат по-бързо и използват по-малко мобилни данни. [Научете повече](https://web.dev/uses-optimized-images)."}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Кодирайте изображенията ефективно"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Показвайте правилно оразмерени изображения, за да пестите мобилни данни и да ускорите зареждането. [Научете повече](https://web.dev/uses-responsive-images)."}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Оразмерете изображенията правилно"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "При показването на текстови ресурси трябва да се използва компресиране (gzip, deflate или brotli), за да се намали общият пренос на данни. [Научете повече](https://web.dev/uses-text-compression)."}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Активирайте компресирането на текста"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "Графични формати, като JPEG 2000, JPEG XR и WebP, често осигуряват по-ефективно компресиране от PNG или JPEG. Това означава по-бързо изтегляне и използване на по-малко данни. [Научете повече](https://web.dev/uses-webp-images)."}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "Използвайте съвременни формати за показване на изображения"}, "lighthouse-core/audits/content-width.js | description": {"message": "Ако ширината на съдържанието на приложението ви не съответства на тази на прозоречния изглед, приложението ви може да не е оптимизирано за мобилни екрани. [Научете повече](https://web.dev/content-width)."}, "lighthouse-core/audits/content-width.js | explanation": {"message": "Размерът на прозоречния изглед ({innerWidth} пкс) не съответства на размера на прозореца ({outerWidth} пкс)."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "Съдържанието не е оразмерено правилно за прозоречния изглед"}, "lighthouse-core/audits/content-width.js | title": {"message": "Съдържанието е оразмерено правилно за прозоречния изглед"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "Веригите от критични заявки по-долу ви показват кои ресурси се зареждат с висок приоритет. За да ускорите зареждането на страницата, препоръчваме да скъсите веригите, да намалите размера за изтегляне на ресурсите или да отложите изтеглянето на ненужните от тях. [Научете повече](https://web.dev/critical-request-chains)."}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Намерена е 1 верига}other{Намерени са # вериги}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "Намалете дълбочината на критичните заявки"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "Оттегляне/предупреждение"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "Ред"}, "lighthouse-core/audits/deprecations.js | description": {"message": "Оттеглените приложни програмни интерфейси (API) след време ще бъдат премахнати от браузъра. [Научете повече](https://web.dev/deprecations)."}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Открито бе 1 предупреждение}other{Открити бяха # предупреждения}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "Използва оттеглени приложни програмни интерфейси (API)"}, "lighthouse-core/audits/deprecations.js | title": {"message": "Избягва оттеглени API"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "Кешът на приложенията е оттеглен. [Научете повече](https://web.dev/appcache-manifest)."}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "Намерихме {AppCacheManifest}"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "Използва кеша на приложенията"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "Избягва кеша на приложенията"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "Посочването на doctype не позволява на браузъра да премине в режим на обратна съвместимост. [Научете повече](https://web.dev/doctype)."}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Името за doctype трябва да е низът `html` с малки букви"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Документът трябва да съдържа doctype"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "За полето publicId се очакваше да бъде празен низ"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "За полето systemId се очакваше да бъде празен низ"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "На страницата липсва doctype на HTML, което задейства режим на обратна съвместимост"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "Страницата съдържа doctype на HTML"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "Елемент"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Статистически данни"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Стойност"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "Браузърните инженери препоръчват страниците да съдържат под 1500 елемента в DOM. Най-добре е йерархичната структура да не е по-дълбока от 32 нива и всеки родителски елемент да има по-малко от 60 дъщерни. Големият размер на DOM може да доведе до използване на повече памет, удължаване на [стиловите изчисления](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) и забавяне поради [преоформяне](https://developers.google.com/speed/articles/reflow). [Научете повече](https://web.dev/dom-size)."}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 елемент}other{# елемента}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Не използвайте DOM с твърде голям размер"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Максимална дълбочина на DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Общ брой елементи в DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Максимален брой дъщерни елементи"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "Не се използва DOM с твърде голям размер"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "Target"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "Добавете `rel=\"noopener\"` или `rel=\"noreferrer\"` към връзките към външни сайтове, за да подобрите ефективността и да избегнете уязвимости в сигурността. [Научете повече](https://web.dev/external-anchors-use-rel-noopener)."}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "Връзките към външни дестинации не са безопасни"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "Връзките към външни дестинации са безопасни"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "Не можахме да определим дестинацията за котвата ({anchorHTML}). Ако не се използва като хипервръзка, бихте могли да премахнете target=_blank."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Потребителите се объркват или нямат доверие на сайтове, които искат да узнаят местоположението им без контекст. Вместо това бихте могли да обвържете заявката към действие на потребителя. [Научете повече](https://web.dev/geolocation-on-start)."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Иска разрешение за геолокация при зареждането на страницата"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Избягва да иска разрешение за геолокация при зареждането на страницата"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Версия"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "Всички библиотеки на JavaScript за предния слой, открити на страницата. [Научете повече](https://web.dev/js-libraries)."}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "Открити библиотеки на JavaScript"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "За потребителите с бавни връзки външните скриптове, вмъквани динамично чрез `document.write()`, могат да забавят зареждането на страницата с десетки секунди. [Научете повече](https://web.dev/no-document-write)."}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Използва `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "Избягва `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "Най-високо ниво на сериозност"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "Версия на библиотеката"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "Брой уязвимости"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "Някои скриптове на трети страни може да съдържат известни уязвимости в сигурността, които лесно се откриват и използват от атакуващите. [Научете повече](https://web.dev/no-vulnerable-libraries)."}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{Открита е 1 уязвимост}other{Открити са # уязвимости}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "Включва библиотеки на JavaScript за предния слой, съдържащи известни уязвимости в сигурността"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "Високо"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "Ниско"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "Средно"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "Избягва библиотеки на JavaScript за предния слой, съдържащи известни уязвимости в сигурността"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "Потребителите се объркват или нямат доверие на сайтове, които искат да изпращат известия без контекст. Вместо това бихте могли да обвържете заявката към жестове на потребителя. [Научете повече](https://web.dev/notification-on-start)."}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Иска разрешение за известяване при зареждането на страницата"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "Избягва да иска разрешение за известяване при зареждането на страницата"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "Елементи с грешки"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "Забраната на поставянето на пароли неутрализира добра практика за сигурност. [Научете повече](https://web.dev/password-inputs-can-be-pasted-into)."}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "Не позволява на потребителите да поставят в полетата за парола"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "Разрешава на потребителите да поставят в полетата за парола"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Протокол"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 предлага много предимства спрямо HTTP/1.1, включително заглавки в двоичен формат, мултиплексиране и самостоятелно изпращане на информация от сървъра. [Научете повече](https://web.dev/uses-http2)."}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 заявка не е обслужена през HTTP/2}other{# заявки не са обслужени през HTTP/2}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "Не използва HTTP/2 за всичките си ресурси"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "Използва HTTP/2 за собствените си ресурси"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "За да подобрите ефективността на страницата си при превъртане, бихте могли да означите като `passive` приемателите си на събития, свързани с докосване и с колелцето на мишката. [Научете повече](https://web.dev/uses-passive-event-listeners)."}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Не използва пасивни приематели на събития за подобряване на ефективността при превъртане"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Използва пасивни приематели на събития за подобряване на ефективността при превъртане"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "Описание"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "Грешките, записани в конзолата, показват нерешени проблеми. Те може да се дължат на неуспешни заявки за мрежата и други проблеми в браузъра. [Научете повече](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "В конзолата бяха записани грешки в браузъра"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "В конзолата не бяха записани грешки в браузъра"}, "lighthouse-core/audits/font-display.js | description": {"message": "Използвайте функцията font-display на CSS, така че текстът да е видим за потребителите, докато уеб шрифтовете се зареждат. [Научете повече](https://web.dev/font-display)."}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "Уверете се, че текстът остава видим при зареждането на уеб шрифтовете"}, "lighthouse-core/audits/font-display.js | title": {"message": "Целият текст остава видим при зареждането на уеб шрифтовете"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "Lighthouse не успя да провери автоматично стойността на font-display за следния URL адрес: {fontURL}."}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "Съотношение (действително)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Съотношение (показвано)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "Размерите за показване на изображението трябва да съответстват на естественото съотношение. [Научете повече](https://web.dev/image-aspect-ratio)."}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "Показва изображения с неправилно съотношение"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "Показва изображенията с правилно съотношение"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "Информацията за размера на изображението е невалидна: {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "Браузърите могат проактивно да подканват потребителите да добавят приложението ви към началния екран, което може да повиши ангажираността. [Научете повече](https://web.dev/installable-manifest)."}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "Манифестът на уеб приложението не отговаря на изискванията за възможност за инсталиране"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "Манифестът на уеб приложението отговаря на изискванията за възможност за инсталиране"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "Несигурен URL адрес"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "Всички сайтове трябва да бъдат защитени с HTTPS, дори онези, които не работят с поверителни данни. HTTPS не позволява на външни лица да променят или подслушват комуникацията между приложението ви и потребителите и е задължително условие за HTTP/2 и множество нови приложни програмни интерфейси (API) за платформи в мрежата. [Научете повече](https://web.dev/is-on-https)."}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Установена е 1 незащитена заявка}other{Установени са # незащитени заявки}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "Не използва HTTPS"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "Използва HTTPS"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "Бързото зареждане на страниците през мобилни мрежи осигурява добра практическа работа за потребителите на мобилни устройства. [Научете повече](https://web.dev/load-fast-enough-for-pwa)."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "Интерактивна след {timeInMs, number, seconds} сек"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "Интерактивност при симулирана мобилна мрежа след {timeInMs, number, seconds} сек"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "Страницата се зарежда прекалено бавно и не е интерактивна в рамките на 10 секунди. За да научите как да я подобрите, разгледайте възможностите и диагностичните данни в секцията „Ефективност“."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "Страницата не се зарежда достатъчно бързо през мобилни мрежи"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "Страницата се зарежда достатъчно бързо през мобилни мрежи"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Категория"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "Препоръчваме да намалите времето, прекарвано в синтактичен анализ, компилиране и изпълнение на JS. Използването на JS ресурси с по-малък размер може да помогне за това. [Научете повече](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Сведете до минимум работата по основната нишка"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "Работата по основната нишка е сведена до минимум"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "За да достигнат до възможно най-много потребители, сайтовете трябва да работят във всички основни браузъри. [Научете повече](https://web.dev/pwa-cross-browser)."}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "Сайтът работи в различни браузъри"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Oтделните страници трябва да могат да се свързват пряко чрез URL адрес, а URL адресите трябва да са уникални, за да се даде възможност за споделянето им в социалните медии. [Научете повече](https://web.dev/pwa-each-page-has-url)."}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Всяка страница има URL адрес"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "Дори когато мрежата е бавна, преходите при докосване на различни елементи трябва да са бързи – така у потребителите се създава усещане за добра ефективност. [Научете повече](https://web.dev/pwa-page-transitions)"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "Преходите между страниците не създават усещане за забавяне на мрежата"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "Приблизителното забавяне при входящо действие показва приблизително колко време (в милисекунди) е необходимо на приложението ви, за да реагира на входящо потребителско действие по време на най-натоварения 5-секунден период от зареждането на страницата. Ако забавянето е над 50 милисекунди, приложението ви може да се стори бавно на потребителите. [Научете повече](https://web.dev/estimated-input-latency)."}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "Прогнозно забавяне при входящо действие"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "Показателят „Първо изобразяване на съдържание (FCP)“ указва след колко време се изобразява първият текстов или графичен елемент. [Научете повече](https://web.dev/first-contentful-paint)."}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "Първо изобразяване на съдържание"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "Показателят „Първи момент на неактивност на процесора“ указва първия момент, в който основната нишка на страницата е достатъчно свободна, за да обработва входящи действия.  [Научете повече](https://web.dev/first-cpu-idle)"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "Първи момент на неактивност на процесора"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "Показателят „Първо значимо изобразяване“ измерва времето, за което основното съдържание на страницата става видимо. [Научете повече](https://web.dev/first-meaningful-paint)."}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "Първо значимо изобразяване"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "Времето до интерактивност показва след колко време страницата става напълно интерактивна. [Научете повече](https://web.dev/interactive)."}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "Време до интерактивност"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "Максималното потенциално забавяне при първото взаимодействие на потребителите е продължителността в милисекунди на най-времеемката задача. [Научете повече](https://developers.google.com/web/updates/2018/05/first-input-delay)."}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "Макс. потенц. забавяне при 1. взаимодействие"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "Индексът на скоростта показва колко бързо се постига визуална завършеност на страницата. [Научете повече](https://web.dev/speed-index)."}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "Индекс на скоростта"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "Сумата от всички интервали от време между FCP и „Време до интерактивност“, когато задачата е траела над 50 мсек, изразена в милисекунди."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "Общо време на блокиране"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "Времето за осъществяване на двупосочна комуникация в мрежата оказва голямо влияние върху ефективността. Ако двупосочната комуникация с източника отнема дълго време, това означава, че разположени по-близо до потребителя сървъри биха подобрили ефективността. [Научете повече](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "lighthouse-core/audits/network-rtt.js | title": {"message": "Време за двупосочна комуникация в мрежата"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "Забавянията на сървъра могат да повлияят на ефективността на уебсайта. Голямото забавяне при източника указва, че сървърът е претоварен или задният слой не работи достатъчно ефективно. [Научете повече](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "Забавяния в задния слой на сървъра"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "Файлът service worker дава възможност на уеб приложението ви да работи надеждно при непредсказуеми условия в мрежата. [Научете повече](https://web.dev/offline-start-url)."}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url` не отговаря с код 200, когато е офлайн"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url` отговаря с код 200, когато е офлайн"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "Lighthouse не можа да прочете `start_url` от манифеста. В резултат на това бе предположено, че URL адресът на документа изпълнява функцията на `start_url`. Съобщение за грешка: „{manifestWarning}“."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "Надхвърля бюджета"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "Поддържайте количеството и обема на мрежовите заявки под целевите стойности в посочения бюджет за ефективността. [Научете повече](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 заявка}other{# заявки}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "Бюджет за ефективността"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "Ако вече сте настроили HTTPS, целият HTTP трафик трябва да се пренасочва към HTTPS, така че функциите за сигурност в мрежата да са активирани за всичките ви потребители. [Научете повече](https://web.dev/redirects-http)."}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "HTTP трафикът не се пренасочва към HTTPS"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "HTTP трафикът се пренасочва към HTTPS"}, "lighthouse-core/audits/redirects.js | description": {"message": "Пренасочванията водят до допълнително забавяне на зареждането на страницата. [Научете повече](https://web.dev/redirects)."}, "lighthouse-core/audits/redirects.js | title": {"message": "Не използвайте пренасочвания през няколко страници"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "За да определите бюджети за количеството и размера на ресурсите на страницата, добавете файл budget.json. [Научете повече](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 заявка • {byteCount, number, bytes} КБ}other{# заявки • {byteCount, number, bytes} КБ}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "Поддържайте малък брой заявки и неголям обем на прехвърляните данни"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "Каноничните връзки указват кой URL адрес да се показва в резултатите от търсенето. [Научете повече](https://web.dev/canonical)."}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "Множество несъвместими URL адреси ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "Сочи към друг домейн ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "Невалиден URL адрес ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Сочи към местоположение с друг атрибут `hreflang` ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "Относителен URL адрес ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "Води до основния URL адрес (началната страница) на домейна вместо до еквивалентна страница със съдържание"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "Документът няма валидна връзка от тип `rel=canonical`"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "Документът има валиден атрибут `rel=canonical`"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "Шрифтовете с размер под 12 пиксела са твърде малки и се налага посетителите от мобилни устройства да увеличат мащаба с разтваряне на пръсти, за да прочетат текста. Старайте се над 60% от текста на страницата да е с размер поне 12 пиксела. [Научете повече](https://web.dev/font-size)."}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} от текста е четлив"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "Текстът не е четлив, тъй като няма мета маркер viewport, оптимизиран за мобилни екрани."}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "{decimalProportion, number, extendedPercent} от текста е твърде малък (въз основа на извадка от {decimalProportionVisited, number, extendedPercent})."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "В документа не се използва шрифт с четлив размер"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "В документа се използва шрифт с четлив размер"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "Връзките от типа hreflang указват на търсещите машини коя версия на страницата да бъде включена в резултатите от търсенето за даден език или регион. [Научете повече](https://web.dev/hreflang)."}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "Документът няма валиден атрибут `hreflang`"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "Документът има валиден атрибут `hreflang`"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "Страниците с невалиден HTTP код на състоянието може да не бъдат индексирани правилно. [Научете повече](https://web.dev/http-status-code)."}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "Страницата има невалиден HTTP код на състоянието"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "Страницата има валиден HTTP код на състоянието"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "Търсещите машини не могат да включат страниците ви в резултатите от търсенето, ако нямат разрешение за обхождането им. [Научете повече](https://web.dev/is-crawable)."}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "Индексирането на страницата е блокирано"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "Индексирането на страницата не е блокирано"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "Описателният текст на връзките помага на търсещите машини да разберат съдържанието ви. [Научете повече](https://web.dev/link-text)."}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Открита е 1 връзка}other{Открити са # връзки}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "Текстът на връзките не е описателен"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "Текстът на връзките е описателен"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "Стартирайте [инструмента за тестване на структурирани данни](https://search.google.com/structured-data/testing-tool/) и [анализатора на структурирани данни](http://linter.structured-data.org/), за да проверите структурираните данни. [Научете повече](https://web.dev/structured-data)."}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "Структурираните данни са валидни"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "Мета описанията може да бъдат включени в резултатите от търсенето, за да се предостави сбито обобщение на съдържанието на страницата. [Научете повече](https://web.dev/meta-description)."}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "Липсва текст на описанието."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "Документът няма мета описание"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "Документът има мета описание"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "Търсещите машини не могат да индексират съдържание с приставки. Много устройства ограничават приставките или не ги поддържат. [Научете повече](https://web.dev/plugins)."}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "В документа се използват приставки"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "Използването на приставки се избягва в документа"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "Ако файлът ви robots.txt не е форматиран правилно, роботите може да не могат да разберат как искате да бъде обходен или индексиран уебсайтът ви. [Научете повече](https://web.dev/robots-txt)."}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "При заявката за robots.txt бе върнат следният HTTP код на състоянието: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Открита е 1 грешка}other{Открити са # грешки}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse не успя да изтегли файла robots.txt"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "Файлът robots.txt не е валиден"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "Файлът robots.txt е валиден"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "Интерактивните елементи, като бутони и връзки, трябва да са достатъчно големи (48 x 48 пиксела) и с достатъчно пространство около тях, за да се докосват лесно, без да се застъпват с други елементи. [Научете повече](https://web.dev/tap-targets)."}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} от целевите зони за докосване са оразмерени правилно"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Целевите зони за докосване са твърде малки, тъй като няма мета маркер viewport, оптимизиран за мобилни екрани"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "Целевите зони за докосване не са оразмерени правилно"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Припокриваща се целева зона"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Целева зона за докосване"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "Целевите зони за докосване са оразмерени правилно"}, "lighthouse-core/audits/service-worker.js | description": {"message": "Service worker е технологията, която дава възможност на приложението ви да използва много от функциите на прогресивните уеб приложения (PWA), като например работа офлайн, добавяне към началния екран и насочени известия. [Научете повече](https://web.dev/service-worker)."}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "Тази страница е контролир<PERSON><PERSON> от service worker, но не бе намерен параметър `start_url`, тъй като при синтактичния анализ бе установено, че манифестът не е във валиден формат JSON"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "Тази страница се контролира от файл service worker, но `start_url` ({startUrl}) не е в обхвата му ({scopeUrl})"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "Тази страница се контролира от service worker, но не бе намерен параметър `start_url`, тъй като не бе извлечен манифест."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "Този източник има един или повече файлове service worker, но страницата ({pageUrl}) не е в обхвата им."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "Няма регистриран service worker, който контролира страницата и `start_url`"}, "lighthouse-core/audits/service-worker.js | title": {"message": "Регистриран е service worker, който контролира страницата и `start_url`"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "Тематичният първоначален екран гарантира висококачествена практическа работа, когато потребителите стартират приложението ви от началния екран. [Научете повече](https://web.dev/splash-screen)."}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "Няма персонализиран първоначален екран"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "Има персонализиран първоначален екран"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "Адресната лента на браузъра може да бъде тематична, за да съответства на сайта ви. [Научете повече](https://web.dev/themed-omnibox)."}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "Не е зададен тематичен цвят за адресната лента."}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "Зададен е тематичен цвят за адресната лента."}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "Време на блокиране на основната нишка"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "Трета страна"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "Кодът от трети страни може сериозно да повлияе върху скоростта на зареждане. Ограничете броя на излишните доставчици трети страни и опитайте да зареждате кода от трети страни, след като основното зареждане на страницата ви е приключило. [Научете повече](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "Код от трети страни блокира основната нишка за {timeInMs, number, milliseconds} мсек"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "Намалете влиянието на кода от трети страни"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "Използване на код от трети страни"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "Показателят „Време до първия байт“ указва след колко време сървърът изпраща отговор. [Научете повече](https://web.dev/time-to-first-byte)."}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "За основния документ бяха необходими {timeInMs, number, milliseconds} мсек"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "Намалете времето за отговор от сървъра (време до първия байт)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "Сървърът отговаря бързо (време до първия байт)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "Продължителност"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "Начален час"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "Тип"}, "lighthouse-core/audits/user-timings.js | description": {"message": "Препоръчваме да използвате API за разбивка на потребителските времена за приложението си, за да измервате действителната му ефективност по време на ключови аспекти от практическата работа на потребителите. [Научете повече](https://web.dev/user-timings)."}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 потребителско време}other{# потребителски времена}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "Точки и измервания в разбивката на потребителските времена"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Намерен бе елемент <link> за предварително свързване за {securityOrigin}, който обаче не бе използван от браузъра. Проверете дали използвате правилно атрибута `crossorigin`."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "Препоръчваме да добавите подсказки `preconnect` или `dns-prefetch` за ресурсите с цел ранно установяване на връзка с важни източници от трети страни. [Научете повече](https://web.dev/uses-rel-preconnect)."}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "Осигурете предварително свързване с необходимите източници"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Намерен бе елемент <link> за предварително зареждане за {preloadURL}, който обаче не бе използван от браузъра. Проверете дали използвате правилно атрибута `crossorigin`."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "Препоръчваме да използвате `<link rel=preload>`, за да укажете по-ранно извличане на ресурсите, които понастоящем се заявяват на по-късен етап от зареждането на страницата. [Научете повече](https://web.dev/uses-rel-preload)."}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "Задайте ключовите заявки да се зареждат предварително"}, "lighthouse-core/audits/viewport.js | description": {"message": "Добавете маркер `<meta name=\"viewport\">`, за да оптимизирате приложението си за мобилни екрани. [Научете повече](https://web.dev/viewport)."}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "Не бе намерен маркер `<meta name=\"viewport\">`"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "Няма маркер `<meta name=\"viewport\">` с атрибут `width` или `initial-scale`"}, "lighthouse-core/audits/viewport.js | title": {"message": "Има маркер `<meta name=\"viewport\">` с атрибут `width` или `initial-scale`"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "Когато JavaScript е деактивиран, в приложението ви трябва да се показва някакво съдържание, дори да е само предупреждение към потребителя, че за използване на приложението се изисква JavaScript. [Научете повече](https://web.dev/without-javascript)."}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "В основната част на страницата трябва да се изобразява съдържание, ако скриптовете ѝ не могат да бъдат заредени."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "Не предоставя резервно съдържание, когато JavaScript не е налице"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "Показва се част от съдържанието, когато JavaScript не е налице"}, "lighthouse-core/audits/works-offline.js | description": {"message": "Ако създавате прогресивно уеб приложение (PWA), добре е да използвате service worker, за да може то да работи офлайн. [Научете повече](https://web.dev/works-offline)."}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "Текущата страница не отговаря с код 200, когато е офлайн"}, "lighthouse-core/audits/works-offline.js | title": {"message": "Текущата страница отговаря с код 200, когато е офлайн"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "Страницата може да не се зарежда офлайн, тъй като тестовият ви URL адрес ({requested}) бе пренасочен към {final}. Опитайте се да тествате втория URL адрес директно."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "Това са възможности за подобряване на използването на ARIA в приложението ви. Така може да подобрите практическата работа за потребителите на помощни технологии, като например екранни четци."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Това са възможности да предоставите алтернативно съдържание за аудио- и видеоелементите. Така може да подобрите практическата работа за потребители със слухови или зрителни нарушения."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Аудио и видео"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Тези елементи открояват често използвани най-добри практики за достъпност."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Най-добри практики"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "Тези проверки открояват възможности за [подобряване на достъпността на уеб приложението ви](https://developers.google.com/web/fundamentals/accessibility). Само определени проблеми с достъпността могат да бъдат открити автоматично. Затова ръчното тестване също е препоръчително."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "Тези проверки покриват области, които са извън обхвата на автоматичните инструменти за тестване. Научете повече в ръководството ни за [извършване на преглед на достъпността](https://developers.google.com/web/fundamentals/accessibility/how-to-review)."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "Достъпност"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Това са възможности за подобряване на четливостта на съдържанието ви."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Контраст"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Това са възможности да направите съдържанието си по-разбираемо за потребителите, използващи други езици."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Интернационализация и локализация"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Това са възможности за подобряване на семантиката на контролите в приложението ви. Така може да подобрите практическата работа за потребителите на помощни технологии, като например екранни четци."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Имена и етикети"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Това са възможности за подобряване на навигирането с клавиатура в приложението ви."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Навигация"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Това са възможности да улесните четенето на данни в таблици или списъци посредством помощни технологии, като например екранни четци."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Таблици и списъци"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Най-добри практики"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "Бюджетите за ефективността задават стандарти за ефективността на сайта ви."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "Бюджети"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "Повече информация за ефективността на приложението ви. Тези стойности не се [отразяват директно](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) върху рейтинга за ефективността."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "Диагностика"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Най-критичният аспект на ефективността е времето, за което пикселите се изобразяват на екрана. Ключови показатели: първо изобразяване на съдържание, първо значимо изобразяване"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Подобрения, свързани с първото изобразяване"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Тези предложения може да ускорят зареждането на страницата ви. Те не се [отразяват директно](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) върху рейтинга за ефективността."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Възможности"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "Показатели"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Подобрете зареждането като цяло, така че страницата да реагира бързо и да е готова за използване възможно най-скоро. Ключови показатели: време до интерактивност, индекс на скоростта"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Цялостни подобрения"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "Ефективност"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "Тези проверки са свързани с аспектите на прогресивните уеб приложения (PWA). [Научете повече](https://developers.google.com/web/progressive-web-apps/checklist)."}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "Тези проверки са задължителни според отправния [контролен списък за PWA](https://developers.google.com/web/progressive-web-apps/checklist), но не се извършват автоматично от Lighthouse. Те не се отразяват на резултата ви, но е важно да ги потвърдите ръчно."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "Прогресивно уеб приложение (PWA)"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "Бързина и надеждност"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Възможност за инсталиране"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Оптимизиране за PWA"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "Тези проверки показват дали страницата ви е оптимизирана така, че да се класира в резултатите от търсещите машини. Lighthouse не проверява някои допълнителни фактори, които може да повлияят на класирането в резултатите от търсенето. [Научете повече](https://support.google.com/webmasters/answer/35769)."}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "Стартирайте тези допълнителни инструменти на сайта си, за да проверите дали е съобразен с други най-добри практики за SEO."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "Форматирайте HTML кода си по начин, който дава възможност на роботите да разберат по-добре съдържанието на приложението ви."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "Най-добри практики за съдържанието"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "За да се показва приложението ви в резултатите от търсенето, роботите се нуждаят от достъп до него."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Обхождане и индексиране"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "Уверете се, че страниците ви са удобни за мобилни устройства, така че да не е необходимо потребителите да събират пръсти или да увеличават мащаба, за да прочетат съдържанието. [Научете повече](https://developers.google.com/search/mobile-sites/)."}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "Удобство за мобилни устройства"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Време на валидност на кеша"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "Местоположение"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "Име"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "Заявки"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "Тип ресурс"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "Размер"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Прекарано време"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "Размер на прехвърлянето"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "URL адрес"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Потенциална икономия"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "Потенциална икономия"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Потенциално спестяване на {wastedBytes, number, bytes} КБ"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Потенциално спестяване на {wastedMs, number, milliseconds} мсек"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "Документ"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "<PERSON>ри<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "Изображение"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "Мултимедия"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} мсек"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "Друго"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "Скрипт"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} сек"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Стилов лист"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Трети страни"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "Общо"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "Нещо се обърка при записването на трасирането за зареждането на страницата ви. Моля, стартирайте отново Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "Времето за изчакване изтече при първоначалното свързване с протокола за инструмента за откриване и отстраняване на грешки."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome не събра екранни снимки при зареждането на страницата. Моля, уверете се, че на нея има видимо съдържание, и опитайте отново да стартирате Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "DNS сървърите не можаха да преобразуват предоставения домейн."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "В задължителния механизъм за събиране на {artifactName} възникна грешка: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "Възникна вътрешна грешка в Chrome. Моля, рестартирайте браузъра и опитайте отново да стартирате Lighthouse."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "Задължителният механизъм за събиране на {artifactName} не се изпълни."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse не успя надеждно да зареди заявената от вас страница. Уверете се, че тествате точния URL адрес и че сървърът отговаря правилно на всички заявки."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse не успя надеждно да зареди заявения от вас URL адрес, тъй като страницата спря да реагира."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Посоченият от вас URL адрес няма валиден сертификат за сигурност. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome не допусна зареждане на страница със заставка. Уверете се, че тествате точния URL адрес и че сървърът отговаря правилно на всички заявки."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse не успя надеждно да зареди заявената от вас страница. Уверете се, че тествате точния URL адрес и че сървърът отговаря правилно на всички заявки. (Подробности: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse не успя надеждно да зареди заявената от вас страница. Уверете се, че тествате точния URL адрес и че сървърът отговаря правилно на всички заявки. (Код на състоянието: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Зареждането на страницата бе твърде бавно. Моля, използвайте посочените в отчета възможности за ускоряване на зареждането ѝ, след което опитайте отново да стартирате Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "Предвиденото време за изчакване на отговор от протокола DevTools бе превишено. (Метод: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "Предвиденото време за извличане на съдържанието на ресурсите бе превишено"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "Предоставеният от вас URL адрес изглежда невалиден."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "Показване на проверките"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "Първоначална навигация"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "Максимално забавяне в критичния път:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "Грешка!"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "Грешка в отчета: няма информация за проверката"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "Данни от контролиран тест"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "Анализът с [Lighthouse](https://developers.google.com/web/tools/lighthouse/) на текущата страница бе извършен през емулирана мобилна мрежа. Стойностите са приблизителни и може да варират."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "Допълнителни елементи, които да проверите ръчно"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "Не е приложимо"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "Възможност"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "Прогнозна икономия"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "Успешно преминати проверки"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "Свиване на фрагмента"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "Разгъване на фрагмента"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "Показване на ресурсите от трети страни"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "Възникнаха проблеми при изготвянето на този отчет от Lighthouse:"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "Стойностите са приблизителни и може да варират. Рейтингът за ефективността [се базира само на тези показатели](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "Проверките бяха преминати успешно, но има предупреждения"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "Предупреждения: "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "Добре е да качите GIF файла си в услуга, която ще даде възможност да бъде вграден като видеоклип с HTML5."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "Инсталирайте [приставка за WordPress за забавено зареждане](https://wordpress.org/plugins/search/lazy+load/), която дава възможност за отлагане на зареждането на изображенията извън видимата част на екрана, или преминете към тема с такава функционалност. Можете също да използвате [приставката за AMP](https://wordpress.org/plugins/amp/)."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "Има различни приставки за WordPress, с чиято помощ можете [да вградите важни активи](https://wordpress.org/plugins/search/critical+css/) или [да отложите зареждането на не толкова важни ресурси](https://wordpress.org/plugins/search/defer+css+javascript/). Имайте предвид, че оптимизациите, извършвани чрез тези приставки, може да възпрепятстват работата на функциите на темата ви или други приставки, така че вероятно ще се наложи да промените кода."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "Времето за реакция на сървъра зависи от спецификациите му, темите и приставките. Добре е да намерите по-оптимизирана тема, внимателно да изберете приставка за оптимизиране и/или да надстроите сървъра си."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "Обмислете възможността да показвате извадки в списъците си с публикации (напр. чрез маркера more), да намалите броя на публикациите, извеждани на дадена страница, да разделите дългите публикации на няколко страници или да използвате приставка, която да забави зареждането на коментарите."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "Има различни [приставки за WordPress](https://wordpress.org/plugins/search/minify+css/), които могат да подобрят скоростта на сайта ви чрез обединяване, минимизиране и компресиране на стиловете. Добре е също при възможност да използвате компилиране, за да извършите това минимизиране предварително."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "Има различни [приставки за WordPress](https://wordpress.org/plugins/search/minify+javascript/), които могат да подобрят скоростта на сайта ви чрез обединяване, минимизиране и компресиране на скриптовете. Добре е също при възможност да използвате компилиране, за да извършите това минимизиране предварително."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "Добре е да намалите броя на [приставките за WordPress](https://wordpress.org/plugins/), които зареждат неизползван CSS код в страницата ви, или да ги замените с други. За да откриете приставките, които добавят ненужен CSS код, стартирайте инструмента за [покритие на кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в Chrome DevTools. Можете да намерите проблемната тема/приставка в URL адреса на листа със стилове. Търсете приставки с много листове със стилове в списъка, за които преобладава червеният цвят в диаграмата на инструмента. Даден лист със стилове трябва да бъде поставен в опашката на приставка само ако действително се използва в страницата."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "Добре е да намалите броя на [приставките за WordPress](https://wordpress.org/plugins/), които зареждат неизползван JavaScript код в страницата ви, или да ги замените с други. За да откриете приставките, които добавят ненужен JS код, стартирайте инструмента за [покритие на кода](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) в Chrome DevTools. Можете да намерите проблемната тема/приставка в URL адреса на скрипта. Търсете приставки с много скриптове в списъка, за които преобладава червеният цвят в диаграмата на инструмента. Даден скрипт трябва да бъде поставен в опашката на приставка само ако действително се използва в страницата."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "Прочетете за [кеширането в браузъра при WordPress](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "Добре е да използвате [приставка за WordPress за оптимизиране на изображенията](https://wordpress.org/plugins/search/optimize+images/), която компресира графичните ви файлове, като същевременно запазва качеството им."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "Качвайте изображенията директно чрез [мултимедийната библиотека](https://codex.wordpress.org/Media_Library_Screen), за да разполагате с графични файлове с необходимите размери, и след това ги вмъквайте от библиотеката или използвайте приспособлението за изображения, така че да се използват файловете с оптимални размери (включително за адаптивните гранични точки). Избягвайте използването на пълноразмерни изображения (`Full Size`), освен ако размерите са подходящи за съответното предназначение. [Научете повече](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)."}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "Можете да активирате компресирането на текста в конфигурацията на уеб сървъра си."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "Добре е да използвате [приставка](https://wordpress.org/plugins/search/convert+webp/) или услуга за автоматично преобразуване на качените изображения в оптималния формат."}}