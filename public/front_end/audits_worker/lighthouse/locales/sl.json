{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "Tipke za dostop uporabnikom omogočajo hiter izbor dela strani. Za ustrezno pomikanje mora biti vsaka tipka za dostop drugačna. [Več o tem](https://web.dev/accesskeys/)."}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> <PERSON>[accesskey]` niso en<PERSON>"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> `[accesskey]` so enoli<PERSON>ne"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Vsak element ARIA `role` podpira določen podniz atributov `aria-*`. Če se ne ujemajo, so atributi `aria-*` razveljavljeni. [Več o tem](https://web.dev/aria-allowed-attr/)."}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Atributi `[aria-*]` se ne ujemajo z vlogami"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Atributi `[aria-*]` se ujemajo s svojimi vlogami"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "Nekatere vloge ARIA imajo zahtevane atribute, ki opišejo stanje elementa bralnikom zaslona. [Več o tem](https://web.dev/aria-required-attr/)."}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> `[role]` ni<PERSON><PERSON> vs<PERSON> z<PERSON><PERSON> atri<PERSON>ov `[aria-*]`"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "<PERSON><PERSON><PERSON> `[role]` imajo vse zahtevane atribute `[aria-*]`"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "Nekatere nadrejene vloge ARIA morajo zaradi ustreznega izvajanja funkcij za ljudi s posebnimi potrebami vsebovati določene podrejene vloge. [Več o tem](https://web.dev/aria-required-children/)."}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Elementi z vlogo ARIA `[role]`, ki <PERSON><PERSON><PERSON><PERSON><PERSON>, da podrejeni elementi vsebujejo določeno vlogo `[role]`, ne vsebujejo nekaterih ali vseh teh zahtevanih podrejenih elementov."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "Elementi z vlogo ARIA `[role]`, ki <PERSON><PERSON><PERSON><PERSON><PERSON>, da podrejeni elementi vsebujejo določeno vlogo `[role]`, vsebujejo vse zahtevane podrejene elemente."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "Nekatere podrejene vloge ARIA morajo biti zaradi ustreznega izvajanja funkcij za ljudi s posebnimi potrebami vsebovane v določenih nadrejenih vlogah. [Več o tem](https://web.dev/aria-required-parent/)."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> `[role]` <PERSON><PERSON> vsebovane v zahtevanem nadrejenem elementu"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "<PERSON><PERSON><PERSON> `[role]` so vsebovane v zahtevanem nadrejenem elementu"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "Vloge ARIA morajo imeti veljav<PERSON> vred<PERSON>ti, <PERSON><PERSON><PERSON>, da bodo izvajale želene funkcije za ljudi s posebnimi potrebami. [Ve<PERSON> o tem](https://web.dev/aria-roles/)."}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> <PERSON>[role]` niso ve<PERSON><PERSON>"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> `[role]` so ve<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> tehnologije, kot so bralniki z<PERSON>a, ne morejo tolmačiti atributov ARIA z neveljavnimi vrednostmi. [Več o tem](https://web.dev/aria-valid-attr-value/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Atributi `[aria-*]` ni<PERSON><PERSON> ve<PERSON> vrednosti"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Atributi `[aria-*]` imajo veljav<PERSON> vrednosti"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> tehnologije, kot so bralniki z<PERSON>a, ne morejo tolmačiti atributov ARIA z neveljavnimi imeni. [Več o tem](https://web.dev/aria-valid-attr/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Atributi `[aria-*]` so neveljavni ali napačno črkovani"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "Atributi `[aria-*]` so veljavni in niso napačno črkovani"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "Podnapisi poskrbijo, da so zvočni elementi uporabni za gluhe ali slušno prizadete tako, da zagotavljajo ključne informacije, na primer, kdo govori, kaj govori in druge neverbalne informacije. [Več o tem](https://web.dev/audio-caption/)."}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "Elementi `<audio>` nimajo elementa `<track>` s podnapisi `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "Elementi `<audio>` vsebujejo element `<track>` s podnapisi `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Neuspešni elementi"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "<PERSON>e gumb nima dostop<PERSON>ga imena, ga bralniki zaslona predstavijo kot »gumb«, s <PERSON><PERSON>r je neuporaben za uporabnike, ki se zanašajo na bralnike zaslona. [Več o tem](https://web.dev/button-name/)."}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> ni<PERSON>jo dos<PERSON>ga imena"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "<PERSON><PERSON><PERSON> imajo dos<PERSON> imena"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "<PERSON>e do<PERSON> nač<PERSON>, s katerimi je mogoče zaobiti ponavljajočo se vsebino, uporabnikom s tipkovnico omogočite učinkovitejše pomikanje po strani. [Več o tem](https://web.dev/bypass/)."}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "Stran ne vsebuje naslova, povezave za preskok ali območja z mejnikom"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "Stran vsebuje <PERSON>lov, povezavo za preskok ali območje z mejnikom"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "Besedilo z nizkim kontrastom številni uporabniki težko preberejo ali ga sploh ne morejo prebrati. [Več o tem](https://web.dev/color-contrast/)."}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "<PERSON><PERSON> in ospredja nimajo zadostnega kontrastnega razmerja."}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "<PERSON>ve oz<PERSON> in ospredja imajo zadostno kontrastno razmerje"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "Če seznami opredelitev niso ustrezno oz<PERSON>, lahko bralniki zaslona izgovorijo nejasno ali netočno vsebino. [Več o tem](https://web.dev/definition-list/)."}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "Elementi `<dl>` ne vsebu<PERSON>jo samo ustrezno razvrš<PERSON><PERSON> skupin `<dt>` in `<dd>`, skripta `<script>` ali elementov `<template>`."}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "Elementi `<dl>` vs<PERSON><PERSON><PERSON>jo samo ustrezno razvrš<PERSON><PERSON> skupine `<dt>` in `<dd>`, skript `<script>` ali elemente `<template>`."}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "Elementi seznamov opredelitev (`<dt>` in `<dd>`) morajo biti zaradi zagotavljanja, da jih lahko bralniki zaslona ustrezno predstavijo, oviti z nadrejenim elementom `<dl>`. [Več o tem](https://web.dev/dlitem/)."}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "Elementi seznamov opredelitev niso oviti z elementi `<dl>`"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "Elementi seznamov opredelitev so oviti z elementi `<dl>`"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "Naslov uporabnikom bralnikov zaslona zagotavlja pregled strani, uporabniki iskalnikov pa se nanašajo nanj pri določanju, ali je stran pomembna za njihovo iskanje. [Več o tem](https://web.dev/document-title/)."}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "Dokument nima elementa `<title>`"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "Dokument ima element `<title>`"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "Vrednost atributa ID-ja mora biti en<PERSON>, če ž<PERSON>te preprečiti, da bi pomožne tehnologije spregledale druge primerke. [Več o tem](https://web.dev/duplicate-id/)."}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "Atributi `[id]` na strani niso enolični"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "Atributi `[id]` na strani so enolični"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "Uporabniki bralnikov zas<PERSON>a se z<PERSON>, da jim vs<PERSON><PERSON> okvirjev opišejo naslovi okvirjev. [Več o tem](https://web.dev/frame-title/)."}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "Elementi `<frame>` ali `<iframe>` ni<PERSON><PERSON>a"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "Elementi `<frame>` ali `<iframe>` imajo naslov"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "Če stran ne določi atributa »lang«, bralnik zaslona predvideva, da je stran v privzetem jeziku, ki ga je uporabnik izbral pri nastavljanju bralnika zaslona. Če stran ni v privzetem jeziku, bralnik zaslona morda ne bo pravilno predstavil besedila na strani. [Več o tem](https://web.dev/html-has-lang/)."}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Element `<html>` nima atributa `[lang]`"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "Element `<html>` ima atribut `[lang]`"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "Če določite veljaven [jezik BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question), pomagate bralnikom zaslona ustrezno predstaviti besedilo [Več o tem](https://web.dev/html-lang-valid/)"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Element `<html>` nima veljavne vrednosti za atribut `[lang]`."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "Element `<html>` ima veljavno vrednost za svoj atribut `[lang]`"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "Informativni elementi naj imajo krat<PERSON>, opisno nadomestno besedilo. Okrasne elemente je mogoče prezreti s praznim nadomestnim atributom. [Več o tem](https://web.dev/image-alt/)."}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "Elementi slik nimajo atributov `[alt]`"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "Elementi slik imajo atribute `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "Če se kot gumb `<input>` upora<PERSON><PERSON>ja slika, lahko z navajanjem nadomestnega besedila uporabnikom bralnikov zaslona pomagate razumeti namen gumba. [Več o tem](https://web.dev/input-image-alt/)."}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Elementi `<input type=\"image\">` nimajo besedila `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "Elementi `<input type=\"image\">` imajo besedilo `[alt]`"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "<PERSON><PERSON><PERSON>vl<PERSON>, da pomožne tehnologije, na primer bralniki zaslona, ustrezno predstavijo kontrolnike za obrazce. [Več o tem](https://web.dev/label/)."}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "Elementi obrazcev nimajo povezanih oznak"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "Elementi obrazcev imajo povezane oznake"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "<PERSON><PERSON><PERSON>, ki se uporablja za namene postavitve, ne sme vsebovati podatkovnih elementov, na primer elementov »th« oziroma podnapisov ali atributa povzetka, ker lahko to privede do zavajajoče izkušnje za uporabnike bralnikov zaslona. [Več o tem](https://web.dev/layout-table/)."}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "Elementi predstavitvene razpredelnice `<table>` se ne izogibajo uporabi atributa `<th>`, `<caption>` ali `[summary]`."}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "Elementi predstavitvene razpredelnice `<table>` se izogibajo uporabi atributa `<th>`, `<caption>` ali `[summary]`."}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "Besedilo za povezavo (in nadomestno besedilo za slike, kadar so uporabljene kot povezave), ki je prepoznavno in edinstveno ter ga je mogoče izbrati, uporabnikom bralnikov zaslona zagotavlja boljšo izkušnjo pomikanja. [Več o tem](https://web.dev/link-name/)."}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "Povezave nimajo prepoznavnega imena"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "Povezave imajo prepoznavno ime"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "Bralniki zaslona predstavljajo sezname na poseben način. Če želite doprinesti h kakovostnejši izgovorjavi bralnikov zaslona, poskrbite za ustrezno strukturo seznamov. [Več o tem](https://web.dev/list/)."}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "<PERSON>znami ne vsebu<PERSON>jo samo elementov `<li>` in elementov, ki podpirajo skripte (`<script>` in `<template>`)."}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "Seznami vsebujejo samo elemente `<li>` in elemente, ki podpirajo skripte (`<script>` in `<template>`)."}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "Bralniki zaslona zahtevajo, da so elementi seznamov (`<li>`) vsebovani v nadrejenih elementih `<ul>` ali `<ol>`, da jih lahko ustrezno predstavijo. [Več o tem](https://web.dev/listitem/)."}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "Elementi seznamov (`<li>`) niso vsebovani v nadrejenih elementih `<ul>` ali `<ol>`."}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "Elementi seznamov (`<li>`) so vsebovani v nadrejenih elementih `<ul>` ali `<ol>`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "Uporabniki ne pričakujejo samodejne osvežitve strani. Če se to zgodi, se izbira pomakne nazaj na vrh strani. To lahko privede do zoprne ali zavajajoče izkušnje. [Več o tem](https://web.dev/meta-refresh/)."}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Dokument uporablja `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "Dokument ne uporablja `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "Onemogočanje povečave/pomanj<PERSON>ve je težavno za slabovidne uporabnike, ki se zanašajo na povečavo z<PERSON>lona, da lahko ustrezno vidijo vsebino spletne strani. [Več o tem](https://web.dev/meta-viewport/)."}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "Lastnosti prilagajanja velikosti `[user-scalable=\"no\"]` se uporabljajo v elementu `<meta name=\"viewport\">` ali pa je atribut `[maximum-scale]` manjši od 5."}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "Lastnosti prilagajanja velikosti `[user-scalable=\"no\"]` se ne uporabljajo v elementu `<meta name=\"viewport\">` in atribut `[maximum-scale]` ni manjši od 5."}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "Bralniki zaslona ne morejo prevesti vsebine, ki ni besedilna. Če elementom `<object>` dodate nadom<PERSON> besed<PERSON>, bralnikom zaslona pomagate prenesti pomen uporabnikom. [Več o tem](https://web.dev/object-alt/)."}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "Elementi `<object>` ni<PERSON>jo besedila `[alt]`"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "Elementi `<object>` imajo besedilo `[alt]`"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "Vrednost, večja od 0, kaže na izrecno razporeditev pomikanja. Čeprav je načeloma veljavna, pogosto vodi v zoprne izkušnje za uporabnike, ki se zanašajo na pomožne tehnologije. [Več o tem](https://web.dev/tabindex/)."}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "Nekateri elementi imajo vrednost za `[tabindex]` večjo od 0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "Noben element nima večje vrednosti za `[tabindex]` od 0"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "Bralniki zaslona imajo funkcije za preprostejše pomikanje po razpredelnicah. Če zagotovite, da se celice `<td>`, ki uporabljajo atribut `[headers]`, nanašajo samo na druge celice v isti razpredelnici, lahko izboljšate izkušnjo za uporabnike bralnikov zaslona. [Več o tem](https://web.dev/td-headers-attr/)"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Celice v elementu `<table>`, ki uporabljajo atribut `[headers]`, se nanašajo na element `id`, ki ga ni mogoče najti v isti razpredelnici."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "Celice v elementu `<table>`, ki uporabljajo atribut `[headers]`, se nanašajo na celice razpredelnice v isti razpredelnici."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "Bralniki zaslona imajo funkcije za preprostejše pomikanje po razpredelnicah. <PERSON><PERSON> zagotov<PERSON>, da se glave razpredelnic vedno nanašajo na določen nabor celic, lahko izboljšate izkušnjo za uporabnike bralnikov zaslona. [Več o tem](https://web.dev/th-has-data-cells/)."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Elementi `<th>` in elementi z <PERSON>lavami `[role=\"columnheader\"/\"rowheader\"]` ni<PERSON><PERSON> pod<PERSON>h celic, ki jih opisujejo."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "Elementi `<th>` in elementi z <PERSON>lavami `[role=\"columnheader\"/\"rowheader\"]` imajo pod<PERSON> celi<PERSON>, ki jih opisujejo."}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "Če za elemente določite veljaven [jezik BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question), poma<PERSON> zago<PERSON>, da bralnik zaslona pravilno izgovori besedilo. [Več o tem](https://web.dev/valid-lang/)."}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Atributi `[lang]` ni<PERSON><PERSON> veljav<PERSON> vrednosti"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "Atributi `[lang]` imajo veljavno vrednost"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "Če ima videoposnetek dodan pod<PERSON>, <PERSON><PERSON><PERSON> in slušno prizadeti uporabniki preprosteje dostopajo do informacij, ki jih podaja. [Več o tem](https://web.dev/video-caption/)."}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "Elementi `<video>` ne vsebu<PERSON> elementa `<track>` z opisom `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "Elementi `<video>` vsebujejo element `<track>` s podnapisi `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "Zvočni opisi zagotavljajo pomembne informacije glede videoposnetkov, ki jih sam dialog ne more, na primer opisujejo obrazne izraze in prizore. [Več o tem](https://web.dev/video-description/)."}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "Elementi `<video>` ne vsebu<PERSON> elementa `<track>` z opisom `[kind=\"description\"]`."}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "Elementi `<video>` vsebujejo element `<track>` s podnapisi `[kind=\"description\"]`"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "Za idealen prikaz v iOSu, ko uporabniki dodajo moderno spletno aplikacijo na začetni zaslon, določite to: `apple-touch-icon`. Kazati mora na neprosojno kvadratno datoteko PNG velikosti 192 (ali 180) slikovnih pik. [Več o tem](https://web.dev/apple-touch-icon/)."}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "Ne vsebuje veljavne vrednosti `apple-touch-icon`"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "`apple-touch-icon-precomposed` je zast<PERSON>; predn<PERSON>ni je `apple-touch-icon`."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "Vsebuje veljavno vrednost `apple-touch-icon`"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Razširitve za Chrome so negativno vplivale na nalaganje te strani. Poskusite pregledati to stran v načinu brez beleženja zgodovine ali v profilu za Chrome brez razširitev."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "Ocenjevanje sk<PERSON>"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "Razčlenitev skripta"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "Skupni čas CPE"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "Razmislite o skrajšanju časa, ki ga porabite za razčlenjevanje, prevajan<PERSON> in izvajanje JavaScripta. <PERSON><PERSON><PERSON><PERSON> boste, da vam lahko pri tem morda pomaga dostavljanje manjših paketov koristne vsebine JavaScript. [Več o tem](https://web.dev/bootup-time)."}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "Skrajšajte čas izvajanja JavaScripta"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "Čas izvajanja JavaScripta"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Veliki GIF-i so neučinkoviti za dostavljanje animirane vsebine. Razmislite o uporabi videoposnetkov MPEG4/WebM za animacije in slik PNG/WebP za statične slike namesto GIF-ov, s čimer prihranite omrežne bajte. [Več o tem](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Uporabite oblike zapisa videoposnetkov za animirano vsebino"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Razmislite o odloženem nalaganju slik zunaj zas<PERSON>a in skritih slik po dokončanem nalaganju kritičnih sredstev zaradi skrajšanja časa do interaktivnosti strani. [Več o tem](https://web.dev/offscreen-images)."}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Odložite nalaganje slik, ki so zunaj zaslona"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Sredstva blokirajo prvo barvanje strani. Razmislite o sprotnem dostavljanju kritičnega JavaScripta/CSS-ja in odlaganju JavaScripta/slogov, ki ni oziroma niso kritični. [Več o tem](https://web.dev/render-blocking-resources)."}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Izločite sredstva, ki blokirajo upodabljanje"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Veliki omrežni paketi koristne vsebine uporabnikom povzročajo dejanske stroške in so tesno povezani z dolgimi časi nalaganja. [Več o tem](https://web.dev/total-byte-weight)."}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Skupna velikost je bila {totalBytes, number, bytes} KB"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Izogibajte se velikanskim omrežnim paketom koristne vsebine"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Izogiba se velikanskim omrežnim paketom koristne vsebine"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "Z zmanjšanjem datotek CSS-ja lahko zmanjšate velikosti paketov koristne vsebine. [Več o tem](https://web.dev/unminified-css)."}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "Zmanjšajte CSS"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Z zmanjšanjem datotek JavaScript lahko zmanjšate velikosti paketov koristne vsebine in skrajšate čas razčlenjevanja skriptov. [Več o tem](https://web.dev/unminified-javascript)."}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Pomanjšajte JavaScript"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Iz slogovnih datotek odstranite nedejavna pravila in odložite nalaganje CSS-ja, ki se ne uporablja za vsebino na vrhu strani, ter tako zmanjšajte nepotrebno porabo bajtov v omrežni dejavnosti. [Več o tem](https://web.dev/unused-css-rules)."}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Odstranitev neuporabljenega CSS-ja"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Odstranite neuporabljeni JavaScript, če želite zmanjšati število bajtov, uporabljenih v omrežni dejavnosti."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Odstranite neuporabljeni JavaScript"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Dolgotrajno predpomnjenje lahko pospeši vnovične obiske strani. [Več o tem](https://web.dev/uses-long-cache-ttl)."}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Najdeno je bilo 1 sredstvo}one{Najdeno je bilo # sredstvo}two{Najdeni sta bili # sredstvi}few{Najdena so bila # sredstva}other{Najdenih je bilo # sredstev}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Dostavljajte statična sredstva z učinkovitim pravilnikom o predpomnjenju"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Uporaba pravilnika o učinkovitem predpomnjenju za statična sredstva"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Optimizirane slike se nalagajo hitreje in terjajo manj prenesenih podatkov v mobilnih omrežjih. [Več o tem](https://web.dev/uses-optimized-images)."}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Učinkovito kodirajte slike"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Prikazujte slike primerne velik<PERSON>i, s čimer poskrbite za prihranek prenesenih podatkov v mobilnih omrežjih in izboljšate čas nalaganja. [Več o tem](https://web.dev/uses-responsive-images)."}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Uporabite slike ustrezne velikosti"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "<PERSON><PERSON> dostavi besedilnih sredstev uporabite stiskanje (gzip, deflate ali brotli) zaradi zmanjšanja skupnega števila omrežnih bajtov. [Več o tem](https://web.dev/uses-text-compression)."}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Omogočite stiskanje besedila"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "Oblike zapisa slik, kot so JPEG 2000, JPEG XR in WebP, pogosto omogočajo učinkovitejše stiskanje kot oblika zapisa PNG ali JPEG, kar pomeni hitrejše prenose in manjšo porabo podatkov. [Več o tem](https://web.dev/uses-webp-images)."}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "Dostavljajte slike v sodobnih oblikah zapisa"}, "lighthouse-core/audits/content-width.js | description": {"message": "Če se širina vsebine aplikacije ne ujema s širino vidnega polja, aplikacija morda ni optimizirana za zaslone mobilnih naprav. [Več o tem](https://web.dev/content-width)."}, "lighthouse-core/audits/content-width.js | explanation": {"message": "Velikost vidnega ob<PERSON>č<PERSON>, ki zna<PERSON> {innerWidth} slikovnih pik, se ne ujema z velikostjo okna, ki znaša {outerWidth} slikovnih pik."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "Vsebina ni ustrezne velikosti za vidno območje"}, "lighthouse-core/audits/content-width.js | title": {"message": "Vsebina je ustrezne velikosti za vidno območje"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "Verige kriti<PERSON>h zahtev spodaj vam prikazujejo, katera sredstva so naložena z visoko prednostjo. Razmislite o skrajšanju verig, zmanjšanju velikosti sredstev ali odlaganju prenosa nepotrebnih sredstev zaradi izboljšanja nalaganja strani. [Več o tem](https://web.dev/critical-request-chains)."}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Najdena je bila 1 veriga}one{Najdena je bila # veriga}two{Najdeni sta bili # verigi}few{Najdene so bile # verige}other{Najdenih je bilo # verig}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "Zmanjšajte globino kritičnih zahtev"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "Zastaranje/opozorilo"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "Vrstica"}, "lighthouse-core/audits/deprecations.js | description": {"message": "Zastareli API-ji bodo sčasoma odstranjeni iz brskalnika. [Več o tem](https://web.dev/deprecations)."}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Najdeno je bilo 1 opozorilo}one{Najdeno je bilo # opozorilo}two{Najdeni sta bili # opozorili}few{Najdena so bila # opozorila}other{Najdenih je bilo # opozoril}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "Uporablja zastarele API-je"}, "lighthouse-core/audits/deprecations.js | title": {"message": "Izogiba se zastarelim API-jem"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "Predpomnilnik aplikacij je zastarel. [Več o tem](https://web.dev/appcache-manifest)."}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "Najdeno – »{AppCacheManifest}«"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "Uporablja predpomnilnik aplikacij"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "Izogiba se predpomnilniku aplikacij"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "Če določite doctype, brskalnik ne more preklopiti v način »quirks«. [Več o tem](https://web.dev/doctype)."}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Ime doctype mora biti niz z majhnimi črkami `html`"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Do<PERSON>ment mora vsebovati doctype"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> je bilo, da je publicid prazen niz"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> je bilo, da je systemid prazen niz"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Stran nima HTML-doctype, zato se sproži na<PERSON> »quirks«"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "Stran ima HTML-doctype"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "Element"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistični podatek"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Vrednost"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "Inženirji za brskalnike <PERSON>, da strani vsebujejo manj kot približno 1500 elementov DOM. Idealna vrednost je globina drevesa z manj kot 32 elementi in manj kot 60 podrejenimi/nadrejenimi elementi. Velik DOM lahko povzroči povečano uporabo pomnilnika, dalj<PERSON><PERSON> [slogovne izračune](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) in drage [prilagoditve postavitve](https://developers.google.com/speed/articles/reflow). [Več o tem](https://web.dev/dom-size)."}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 element}one{# element}two{# elementa}few{# elementi}other{# elementov}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Izogibajte se prekomerni velikosti DOM-a"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Največja globina DOM-a"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Skupno število elementov DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Največje število podrejenih elementov"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "Izogiba se prekomerni velikosti DOM-a"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "Zunanjim povezavam dodajte `rel=\"noopener\"` ali `rel=\"noreferrer\"` zaradi izboljšanja delovanja in preprečevanja varnostnih ranljivosti. [Več o tem](https://web.dev/external-anchors-use-rel-noopener)"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "Povezave do ciljev iz več izvorov niso varne"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "Povezave do ciljev iz več izvorov so varne"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "Ni mogoče določiti cilja za sidro ({anchorHTML}). Če se ne uporablja kot hiperpovezava, lahko odstranite target=_blank."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Uporabniki so nezaupljivi do spletnih mest oziroma jih begajo spletna mesta, ki zahtevajo njihovo lokacijo brez konteksta. Razmislite o tem, da bi zahtevo povezali z uporabniškim dejanjem. [Več o tem](https://web.dev/geolocation-on-start)."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Zahteva dovoljenje za geolokacijo pri nalaganju strani"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Ne zahteva dovoljenja za geolokacijo pri nalaganju strani"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Različica"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "Vse knjižnice JavaScript vmesnikov, zaznane na strani. [Več o tem](https://web.dev/js-libraries)."}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "Zaznane knjižnice JavaScript"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "Pri uporabnikih s počasnimi povezavami lahko zunanji skripti, dinamično vstavljeni prek `document.write()`, zakasnijo nalaganje strani za več deset sekund. [Več o tem](https://web.dev/no-document-write)."}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Uporablja `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "Se izogiba `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "Največja resnost"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "Različica knjižnice"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "Nekateri skripti drugih ponudnikov morda vsebujejo znane varnostne ranljivosti, ki jih napadalci lahko preprosto prepoznajo in izkoristijo. [Več o tem](https://web.dev/no-vulnerable-libraries)."}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{Zaznana je bila 1 ranljivost}one{Zaznana je bila # ranljivost}two{Zaznani sta bili # ranljivosti}few{Zaznane so bile # ranljivosti}other{Zaznanih je bilo # ranljivosti}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "Vključuje knjižniceJavaScript vmesnikov z znanimi varnostnimi ranljivostmi"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "Visoka"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "Nizka"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "Srednja"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "Izogiba se knjižnicam JavaScript vmesnikov z znanimi varnostnimi ranljivostmi"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "Uporabniki so nezaupljivi do spletnih mest oziroma jih begajo spletna mesta, ki zahtevajo pošiljanje obvestil brez konteksta. Razmislite o tem, da bi zahtevo povezali z uporabniškimi potezami. [Več o tem](https://web.dev/notification-on-start)."}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Zahteva dovoljenje za obvestila pri nalaganju strani"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "Ne zahteva dovoljenja za obvestila pri nalaganju strani"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "Neuspešni elementi"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "Preprečevanje lepljenja gesel omeji dober pravilnik o varnosti. [Več o tem](https://web.dev/password-inputs-can-be-pasted-into)."}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "Uporabnikom preprečuje lepljenje v polja za gesla"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "Uporabnikom omogoča lepljenje v polja za gesla"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokol"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 ima več prednosti pred HTTP/1.1, vključno z binarnimi glavami, multipleksiranjem in potisnimi sredstvi iz strežnika. [Več o tem](https://web.dev/uses-http2)."}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 zahteva ni bila poslana prek HTTP/2}one{# zahteva ni bila poslana prek HTTP/2}two{# zahtevi nista bili poslani prek HTTP/2}few{# zahteve niso bile poslane prek HTTP/2}other{# zahtev ni bilo poslanih prek HTTP/2}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "Ne uporablja HTTP/2 za vsa svoja sredstva"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "Uporablja HTTP/2 za svoja sredstva"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Posluševalnike dogodkov dotika in kolesca lahko označite kot `passive` zaradi izboljšanja delovanja pomikanja na strani. [Več o tem](https://web.dev/uses-passive-event-listeners)."}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Ne uporablja pasivnih poslušalcev za izboljšanje delovanja pomikanja"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Uporablja pasivne poslušalce za izboljšanje delovanja pomikanja"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "Opis"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "Napake, zabeležene v konzoli, označujejo nerazrešene težave. Te so lahko posledica neuspešnih omrežnih zahtev in drugih težav z brskalnikom. [Več o tem](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "Napake brskalnika so bile zabeležene v konzoli"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "V konzoli ni bila zabeležena nobena napaka brskalnika"}, "lighthouse-core/audits/font-display.js | description": {"message": "Izkoristite funkcijo CSS-ja za prikaz pisave, s <PERSON><PERSON><PERSON> p<PERSON>, da je med nalaganjem spletne pisave besedilo vidno uporabnikom. [Več o tem](https://web.dev/font-display)."}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>, da bo med nalaganjem spletne pisave besedilo ostalo vidno"}, "lighthouse-core/audits/font-display.js | title": {"message": "<PERSON><PERSON> besedilo ostaja vidno med nalaganjem spletne pisave"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "Orodje Lighthouse ni utegnilo samodejno preveriti vrednosti font-display za naslednji URL: {fontURL}."}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "Razmerje stranic (dejansko)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "<PERSON><PERSON><PERSON><PERSON> stra<PERSON> (prikazano)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "Mere prikaza slike se morajo ujemati z naravnim razmerjem stranic. [Več o tem](https://web.dev/image-aspect-ratio)."}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "Prikazuje slike z nepravilnim razmerjem stranic"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "Prikazuje slike s pravilnim razmerjem stranic"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "Neveljavni podatki o velikosti slike {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "Brskalniki lahko proaktivno pozovejo uporabnike, da dodajo vašo aplikacijo na začetni zaslon, kar lahko privede do več dejavnosti. [Več o tem](https://web.dev/installable-manifest)."}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "Manifest spletne aplikacije ne izpolnjuje zahtev za izvedljivost namestitve"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "Manifest spletne aplikacije izpolnjuje zahteve za izvedljivost namestitve"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "URL zahtev, ki niso varne"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "Vsa spletna mesta morajo biti zaščitena s HTTPS, tudi tista, ki nimajo opravka z občutljivimi podatki. HTTPS vsiljivcem preprečuje manipuliranje s komunikacijami ali pasivno poslušanje komunikacij med aplikacijo in uporabniki ter je pogoj za HTTP/2 in veliko novih API-jev za spletna okolja. [Več o tem](https://web.dev/is-on-https)."}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Najdena je bila 1 zahteva, ki ni varna}one{Najdena je bila # zahteva, ki ni varna}two{Najdeni sta bili # zahtevi, ki nista varni}few{Najdene so bile # zahteve, ki niso varne}other{Najdenih je bilo # zahtev, ki niso varne}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "Ne uporablja HTTPS"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "Uporablja HTTPS"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "Hitro nalaganje strani prek mobilnega omrežja zagotavlja dobro uporabniško izkušnjo pri uporabi mobilnih naprav. [Več o tem](https://web.dev/load-fast-enough-for-pwa)."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "Interaktivno ob {timeInMs, number, seconds} s"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "Interaktivno v simuliranem mobilnem omrežju ob {timeInMs, number, seconds} s"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "Stran se nalaga prepočasi in ni interaktivna v 10 sekundah. Oglejte si priložnosti in diagnostiko v razdelku »Delovanje«, če želite izvedeti, kako do izboljšanja."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "Nalaganje strani ni dovolj hitro v mobilnih omrežjih"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "Nalaganje strani je dovolj hitro v mobilnih omrežjih"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Kategorija"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "Razmislite o skrajšanju časa, ki ga porabite za razčlenjevanje, prevajan<PERSON> in izvajanje JavaScripta. <PERSON><PERSON><PERSON><PERSON> boste, da vam lahko pri tem morda pomaga dostavljanje manjših paketov koristne vsebine JavaScript. [Več o tem](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimizirajte delo glavne niti"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "Minimizira delo glavne niti"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "Če želite doseči naj<PERSON>č <PERSON>, morajo spletna mesta delovati v vsakem pomembnejšem brskalniku. [Več o tem](https://web.dev/pwa-cross-browser)."}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "Spletno mesto deluje v različnih brskalnikih"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Zagotovite povezave v globino do posameznih strani prek URL-jev in poskrbite, da so zaradi objavljanja v družbenih omrežjih ti URL-ji enolični. [Več o tem](https://web.dev/pwa-each-page-has-url)."}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Vsaka stran ima URL"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "Prehodi morajo ob dotikanju delovati hitro in tekoče tudi v počasnem omrežju. To je ključno za uporabnikov vtis zmogljivega delovanja. [Več o tem](https://web.dev/pwa-page-transitions)."}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "Prehodi med stranmi ne dajejo občutka, kot da so blokirani v omrežju"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "Predvidena vhodna zakasnitev je ocena, koliko milisekund potrebuje vaša aplikacija za odziv na uporabnikovo dejavnost med najaktivnejšimi 5 sekundami pri nalaganju strani. Če je zakasnitev večja od 50 ms, se lahko uporabnikom zdi, da se aplikacija zatika. [Več o tem](https://web.dev/estimated-input-latency)."}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "Ocenjena zakasnitev vnosa"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "Prvi vsebinski izris <PERSON>, ko je izrisano prvo besedilo oz<PERSON>ma je izrisana prva slika. [Več o tem](https://web.dev/first-contentful-paint)."}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "<PERSON><PERSON><PERSON> vs<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "Prva nedejavnost CPE-ja oz<PERSON>, po katerem je glavna nit strani dovolj neobremen<PERSON>na, da lahko obravnava vnos.  [Več o tem](https://web.dev/first-cpu-idle)."}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "Prva nedejavnost CPE-ja"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "<PERSON><PERSON><PERSON> s<PERSON>o <PERSON>, kdaj je vidna glavna vs<PERSON>ina strani. [Več o tem](https://web.dev/first-meaningful-paint)."}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "<PERSON><PERSON><PERSON> smiselno <PERSON>"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "Čas do interaktivnosti je čas, potreben, da stran postane povsem interaktivna. [Več o tem](https://web.dev/interactive)."}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "Čas do interaktivnosti"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "Najve<PERSON><PERSON> potencialna zakasnitev od prvega vnosa, na katero lahko uporabniki naletijo, je trajanje (v ms) najdaljšega opravila. [Več o tem](https://developers.google.com/web/updates/2018/05/first-input-delay)."}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "Najv. potencial. zakasn. od prvega vnosa"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "In<PERSON><PERSON> hit<PERSON>, kako hitro je vsebina strani vidno izpolnjena. [Več o tem](https://web.dev/speed-index)."}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "Skupek vseh časovnih obdobij med FCP-jem in časom do interaktivnosti, ko je dolžina opravila presegla 50 ms, izražena v milisekundah."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "Skupni čas blokiranja"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "Omrežni časi povratnega potovanja (RTT) imajo velik vpliv na učinkovitost delovanja. Če je RTT do izvora velik, to kaže na to, da bi lahko strežniki bližje uporabniku izboljšali učinkovitost delovanja. [Več o tem](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "lighthouse-core/audits/network-rtt.js | title": {"message": "Časi omrežnega povratnega potovanja"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "Na spletno učinkovitost delovanja lahko vplivajo zakasnitve strežnikov. Če je zakasnitev strežnika za izvor velika, to kaže na to, da je strežnik preobremenjen ali ima slabo zaledno učinkovitost delovanja. [Več o tem](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "Zakasnitve strežnikovega zaledja"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "Proces storitve spletni aplikaciji omogoča zanesljivost v nepredvidljivih omrežnih okoliščinah. [Več o tem](https://web.dev/offline-start-url)."}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url` se ne odzove s kodo stanja HTTP 200, ko nima povezave"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url` se odzove s kodo stanja HTTP 200, ko nima povezave"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "Lighthouse ni mogel prebrati URL-ja `start_url` v manifestu. Za URL dokumenta se je posledično imelo ta URL: `start_url`. Sporočilo o napaki: »{manifestWarning}«."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "Prek proračuna"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> in velikosti omrežnih zahtev imejte pod cilji, ki so nastavljeni z navedenim proračunom za uspešnost. [Več o tem](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 zahteva}one{# zahte<PERSON>}two{# zahte<PERSON>}few{# zahte<PERSON>}other{# zahtev}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "Proračun za uspešnost"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "Če ste že nastavili protokol HTTPS, poskrbite za preusmeritev vsega prometa prek protokola HTTP na protokol HTTPS, da zagotovite varne spletne funkcije vsem uporabnikom. [Več o tem](https://web.dev/redirects-http)."}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "Ne preusmeri prometa prek protokola HTTP na protokol HTTPS"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "Preusmeri promet prek protokola HTTP na protokol HTTPS"}, "lighthouse-core/audits/redirects.js | description": {"message": "Preusmeritve vnašajo dodatne zakasnitve nalaganja strani. [Več o tem](https://web.dev/redirects)."}, "lighthouse-core/audits/redirects.js | title": {"message": "Izogibajte se preusmeritvam na več strani"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "Če želite nastaviti proračune za količino in velikost sredstev strani, dodajte datoteko budget.json. [Več o tem](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 zahteva • {byteCount, number, bytes} KB}one{# zahteva • {byteCount, number, bytes} KB}two{# zahtevi • {byteCount, number, bytes} KB}few{# zahteve • {byteCount, number, bytes} KB}other{# zahtev • {byteCount, number, bytes} KB}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "Število zahtev naj bo majhno in prenosi naj ne bodo preveliki"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "Kanonične povezave predlagajo, kateri URL naj bo prikazan v rezultatih iskanja. [Več o tem](https://web.dev/canonical)."}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "Več URL-jev v sporu ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "Kaže na drugo domeno ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "Neveljaven URL ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Kaže na drugo lokacijo `hreflang` ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "Relativni URL ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "Kaže na korenski URL domene (domačo stran), namesto na enakovredno stran z vsebino"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "Dokument nima veljavne povezave `rel=canonical`"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "Dokument ima veljaven atribut `rel=canonical`"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> pisave, man<PERSON><PERSON><PERSON> od 12 slikovnih pik, so <PERSON><PERSON><PERSON><PERSON><PERSON>, da bi bile berljive, zato morajo obiskovalci z mobilnimi napravami s potezo razširjanja prstov na zaslonu povečati strani, da bi jih lahko prebrali. Poskušajte zagotoviti, da bo več kot 60 % besedila na strani velikosti, ki ni manjša od 12 slikovnih pik. [Več o tem](https://web.dev/font-size)."}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} berljivega besedila"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "Besedilo je neberljivo, ker ni metaoznake »viewport«, optimizirane za zaslone mobilnih naprav."}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "{decimalProportion, number, extendedPercent} besedila je premaj<PERSON>a (na podlagi {decimalProportionVisited, number, extendedPercent} vzorca)."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "Dokument ne uporablja berljivih velikosti pisav"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "Dokument uporablja berljive velikosti pisav"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "Povezave hreflang iskalnikom povedo, katero različico strani naj prikažejo v rezultatih iskanja za določen jezik ali regijo. [Več o tem](https://web.dev/hreflang)."}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "Dokument nima veljavnega atributa `hreflang`"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "Dokument ima veljaven atribut `hreflang`"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "Strani z neuspešnimi kodami stanja HTTP morda ne bodo pravilno indeksirane. [Več o tem](https://web.dev/http-status-code)."}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "Stran ima neuspešno kodo stanja HTTP"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "Stran ima uspešno kodo stanja HTTP"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "Če iskalniki nimajo dovoljenja za iskanje po vsebini vaših strani, jih ne morejo vključiti v rezultate iskanja. [Več o tem](https://web.dev/is-crawable)."}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "Indeksiranje strani je blokirano"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "Indeksiranje strani ni blokirano"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "Opisno besedilo povezave iskalnikom pomaga razumeti vašo vsebino. [Več o tem](https://web.dev/link-text)."}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Najdena je 1 povezava}one{Najdena je # povezava}two{Najdeni sta # povezavi}few{Najdene so # povezave}other{Najdenih je # povezav}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "Povezave nimajo opisnega besedila"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "Povezave imajo opisno besedilo"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "Za preverjanje strukturiranih podatkov zaženite [orodje za preizkušanje strukturiranih podatkov](https://search.google.com/structured-data/testing-tool/) in [orodje Linter za strukturirane podatke](http://linter.structured-data.org/). [Več o tem](https://web.dev/structured-data)."}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "Strukturirani podatki so veljavni"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "Za podroben povzetek vsebine strani so lahko v rezultatih iskanja vključeni metaopisi. [Več o tem](https://web.dev/meta-description)."}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "<PERSON><PERSON><PERSON> besedilo je prazno."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "Dokument nima metaopisa"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "Dokument ima metaopis"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "Iskalniki ne morejo indeksirati vseb<PERSON> vtičnikov in številne naprave vtičnike omejujejo ali jih ne podpirajo. [Več o tem](https://web.dev/plugins)."}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "Dokument uporablja vtičnike"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "Dokument ne vsebuje vtičnikov"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "Če datoteka robots.txt ni pravil<PERSON> ob<PERSON>, iskal<PERSON>i po spletni vsebini morda ne bodo razumeli, ka<PERSON>, da se išče po spletni vsebini vašega spletnega mesta in se jo indeksira. [Več o tem](https://web.dev/robots-txt)."}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Zahteva za datoteko robots.txt je vrnila to stanje HTTP: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Najdena je bila 1 napaka}one{Najdena je bila # napaka}two{Najdeni sta bili # napaki}few{Najdene so bile # napake}other{Najdenih je bilo # napak}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse ni mogel prenesti datoteke robots.txt"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "Datoteka robots.txt ni veljavna"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "Datoteka robots.txt je veljavna"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "Interaktivni elementi, kot so gumbi in povezave, morajo biti dovolj veliki (48 x 48 slikovnih pik) in okoli njih mora biti dovolj prostora, da se jih je mogoče dotakniti, ne da bi se pri tem dotaknili drugih elementov. [Več o tem](https://web.dev/tap-targets)."}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} ciljev dotika primerne velikosti"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Cilji za dotik so prema<PERSON><PERSON>, ker ni metaoznake »viewport«, optimizirane za zaslone mobilnih naprav."}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "Cilji za dotik niso primerne velikosti"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Prekrivajoči se cilj"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON><PERSON>j za dotik"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "Cilji za dotikanje so ustrezne velikosti"}, "lighthouse-core/audits/service-worker.js | description": {"message": "Proces storitve je tehnologija, ki aplikaciji omogoča uporabo številnih funkcij moderne spletne aplikacije, na primer delovanje brez povezave, dodajanje na začetni zaslon in potisna obvestila. [Več o tem](https://web.dev/service-worker)."}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "To stran nad<PERSON>ra proces storitve, vendar ni bilo mogoče najti ničesar od tega: `start_url`, ker ni bilo mogoče razčleniti manifesta kot veljavne datoteke JSON"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "To stran nadzira proces storitve, vendar `start_url` ({startUrl}) ni v obsegu procesa storitve ({scopeUrl})"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "To stran nad<PERSON>ra proces storitve, vendar ni bilo mogoče najti nič<PERSON>ar od tega: `start_url`, ker niso bili preneseni manifesti."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "Ta izvor ima enega ali več procesov storitve, vendar stran ({pageUrl}) ni v obsegu."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "Ne registrira procesa storitve, ki nadzira stran in to: `start_url`"}, "lighthouse-core/audits/service-worker.js | title": {"message": "Registrira proces storitve, ki nadzira stran in to: `start_url`"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "S tematskim pozdravnim zaslonom zagotovite visokokakovostno izkušnjo, ko uporabniki zaženejo aplikacijo z začetnih zaslonov. [Več o tem](https://web.dev/splash-screen)."}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "Ni konfigurirano za pozdravni z<PERSON>lon po meri"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "Konfigurirano za pozdravni z<PERSON>lon po meri"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "Naslovno vrstico brskalnika je mogoče prilagoditi s temo, ki se ujema s spletnim mestom. [Več o tem](https://web.dev/themed-omnibox)."}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "Ne nastavi barve teme za naslovno vrstico."}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "Nastavi barvo teme za naslovno vrstico."}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "Čas blokiranja glavne niti"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "Drugi ponudniki"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "Koda drugega ponudnika lahko znatno vpliva na učinkovitost nalaganja. Omejite število odvečnih drugih ponudnikov in poskusite naložiti kodo drugega ponudnika, ko je stran prvenstveno končala nalaganje. [Več o tem](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "Koda drugega ponudnika je blokirala glavno nit {timeInMs, number, milliseconds} ms"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "Zmanjšanje vpliva kode drugega ponudnika"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "Uporaba drugih pon<PERSON>nikov"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "»Čas do prvega bajta« navaja čas, v katerem vaš strežnik pošlje odziv. [Več o tem](https://web.dev/time-to-first-byte)."}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "Korenski dokument je terjal {timeInMs, number, milliseconds} ms"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "Skrajšajte odzivne čase strežnika (TTFB)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "Odzivni časi strežnika so nizki (TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "Začetni čas"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "Vrsta"}, "lighthouse-core/audits/user-timings.js | description": {"message": "Razmislite o uporabi API-ja za merjenje dejanskih izvedb uporabniš<PERSON> (User Timing API), če želite izmeriti dejansko delovanje aplikacije med ključnimi uporabniškimi izkušnjami. [Več o tem](https://web.dev/user-timings)."}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 trajanje izvedbe uporabniških dogodkov}one{# trajanje izvedbe uporabniških dogodkov}two{# trajanji izvedbe uporabniških dogodkov}few{# trajanja izvedbe uporabniških dogodkov}other{# trajanj izvedbe uporabniških dogodkov}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "Oznake in merjenja trajanj izvedbe uporabniških dogodkov"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Povezava <link> za vnaprejšnje povezovanje je bila najdena za »{securityOrigin}«, vendar je brskalnik ni uporabil. Preverite, ali pravilno uporabljate atribut `crossorigin`."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "Razmislite o dodajanju namigov za sredstva (`preconnect` ali `dns-prefetch`) zaradi vzpostavljanja zgodnjih povezav s pomembnimi izvori drugih ponudnikov. [Več o tem](https://web.dev/uses-rel-preconnect)."}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "Vnaprej se povežite z zahtevanimi izvori"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Najden je bil element <link> za »{preloadURL}«, vendar ga brskalnik ni uporabil. Preverite, ali pravilno uporabljate atribut `crossorigin`."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "Razmislite o uporabi oznake `<link rel=preload>` za dodeljevanje višje stopnje prednosti pri pridobivanju sredstev, ki so trenutno zahtevana pri nadaljnjem nalaganju strani. [Več o tem](https://web.dev/uses-rel-preload)."}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "Vnaprej nalagajte ključne zahteve"}, "lighthouse-core/audits/viewport.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> `<meta name=\"viewport\">`, da optimizirate aplikacijo za z<PERSON>lone mobilnih naprav. [Več o tem](https://web.dev/viewport)."}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "Ni <PERSON>nak `<meta name=\"viewport\">`"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "<PERSON><PERSON> `<meta name=\"viewport\">` s tem: `width` ali `initial-scale`"}, "lighthouse-core/audits/viewport.js | title": {"message": "<PERSON><PERSON> `<meta name=\"viewport\">` s tem: `width` ali `initial-scale`"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "Aplikacija bi morala prikazati ne<PERSON> vs<PERSON>, če je JavaScript onemogočen, četudi zgolj opozorilo uporabniku, da uporaba aplikacije terja JavaScript. [Več o tem](https://web.dev/without-javascript)."}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "Telo strani bi moralo upodobiti nekaj vsebine, če njeni skripti niso na voljo."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "Ne zagotavlja nadomestne vsebine, ko JavaScript ni na voljo"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "Vsebuje ne<PERSON>j vs<PERSON>, ko JavaScript ni na voljo"}, "lighthouse-core/audits/works-offline.js | description": {"message": "Če ustvarjate moderno spletno aplikacijo, razmislite o uporabi procesa storitve, da lahko aplikacija deluje brez povezave. [Več o tem](https://web.dev/works-offline)."}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "Trenutna stran se ne odzove s kodo stanja HTTP 200, ko nima povezave"}, "lighthouse-core/audits/works-offline.js | title": {"message": "Trenutna stran se odzove s kodo stanja HTTP 200, ko nima povezave"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "Stran se morda ne naloži brez povezave, ker je bil preizkusni URL ({requested}) preusmerjen na »{final}«. Poskusite neposredno preizkusiti drugi URL."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "Tu imate na voljo priložnosti, da izboljšate uporabo atributov ARIA v aplikaciji, s čimer lahko izboljšate izkušnjo za uporabnike pomožnih tehnologij, kot je bralnik zaslona."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "To so priložnosti za izboljšanje nadomestne vsebine za zvok in videoposnetke. S tem lahko izboljšate izkušnjo za uporabnike z motnjami sluha ali vida."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "<PERSON><PERSON><PERSON> in video"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Ti elementi izpostavijo pogoste najboljše postopke za zagotavljanje dostopnosti."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Najboljši postopki"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "Ta preverjanja izpostavijo priložnosti za [izboljšanje dostopnosti vaše spletne aplikacije](https://developers.google.com/web/fundamentals/accessibility). Samodejno je mogoče ugotoviti samo podnabor morebitnih težav z dostopnostjo, zato spodbujamo ročno preverjanje."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "Ti elementi se nanašajo na področja, ki jih ne more obdelati samodejno orodje za preizkušanje. Več o tem lahko preberete v našem vodniku o [izvedbi pregleda dostopnosti](https://developers.google.com/web/fundamentals/accessibility/how-to-review)."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "Dostopnost"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Tu imate na voljo priložnosti, da izboljšate berljivost vsebine."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Tu imate na voljo priložnosti, da izboljšate, kako si uporabniki v različnih jezikih tolmačijo vašo vsebino."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internacionalizacija in lokalizacija"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Tu imate na voljo priložnosti, da izboljšate pomen kontrolnikov v aplikaciji. S tem lahko izboljšate izkušnjo uporabnikov pomožne tehnologije, kot je bralnik zaslona."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Imena in oznake"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "To so <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da izboljšate premikanje po aplikaciji s tipkovnico."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Pomikanje"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "To so <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da z uporabo pomožnih tehnologij, kot je bralnik zaslona, izboljšate izkušnjo pri branju tabelarnih podatkov ali podatkov na seznamih."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tabele in seznami"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Najboljši postopki"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "Proračuni za uspešnost postavljajo standarde za uspešnost spletnega mesta."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "Več informacij o delovanju aplikacije. Te številke [neposredno ne vplivajo](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) na rezultat uspešnosti."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostika"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Bistveni vidik delovanja je, kako hitro se upodabljajo slikovne pike na zaslonu. Ključni meritvi: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> s<PERSON> bar<PERSON>"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Izboljšave prvega barvanja"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Ti predlogi lahko pomagajo pri hitrejšem nalaganju strani. [Neposredno ne vplivajo](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) na rezultat uspešnosti."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Pril<PERSON>ž<PERSON>ti"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "Meritve"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Izboljšajte splošno izkušnjo nalaganja, da bo stran odzivna in čim prej pripravljena na uporabo. Ključni meritvi: Čas do interaktivnosti, <PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Splošne izboljšave"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "Delovanje"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "Ta preverjanja potrjujejo vidike moderne spletne aplikacije. [Več o tem](https://developers.google.com/web/progressive-web-apps/checklist)."}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "Ta preverjanja zahteva osnovni [kontrolni seznam za MSA](https://developers.google.com/web/progressive-web-apps/checklist), vendar jih Lighthouse ne opravi samodejno. Preverjanja vplivajo na vaš <PERSON>ltat, vendar je pome<PERSON>, da jih opravite ročno."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "Moderna spletna aplikacija"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "Hitro in zanesljivo"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Namestljivo"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Optimizirano za PWA"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "S temi preverjanji se <PERSON>i, da je stran optimizirana za uvrstitev v rezultatih iskanja. Na uvrstitev v rezultatih iskanja lahko vplivajo dodatni dejavniki, ki jih Lighthouse ne preverja. [Več o tem](https://support.google.com/webmasters/answer/35769)."}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "Na spletnem mestu izvedite ta dodatna preverjanja, da preverite dodatne najboljše postopke za SEO."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "Oblikujte HTML na način, ki iskalnikom po vsebini omogoča boljše razumevanje vsebine vaše aplikacije."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "Najboljši postopki glede vsebine"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Če želite, da bo prikazana v rezultatih iskanja, morajo imeti iskalniki po vsebini dostop do vaše aplikacije."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Iskanje po vsebini in indeksiranje"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "<PERSON><PERSON><PERSON><PERSON>, da so strani prilagojene za mobilne naprave, da uporabnikom ne bo treba vleči s prsti skupaj ali povečevati slike, če bodo želeli brati strani z vsebino. [Več o tem](https://developers.google.com/search/mobile-sites/)."}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "Prilagojeno za mobilne naprave"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "TTL predpomnjenja"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "Lokacija"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "Ime"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "Vrsta s<PERSON>va"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "Velikost"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Porabljeni č<PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "Velikost prenosa"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Morebitni prihranki"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "Morebitni prihranki"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "{wastedBytes, number, bytes} KB morebitnega prihranka"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "{wastedMs, number, milliseconds} ms morebitnega prihranka"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokument"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "Slika"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "Predstavnost"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "Drugo"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "S<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Slogovna datoteka"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Drugi ponudniki"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "Skupno"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "Pri snemanju sledi ob nalaganju strani je prišlo do napake. Znova zaženite Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "Potek časovne omejitve pri čakanju na začetno povezavo protokola za odpravljanje napak"}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome med nalaganjem strani ni zbral posnetkov zaslona. Poskrbite, da je vsebina vidna na strani, nato poskusite znova zagnati Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "Strežnikom DNS ni uspelo razrešiti navedene domene."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "V zahtevanem zbiralniku {artifactName} je prišlo do napake: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "Prišlo je do notranje napake Chroma. Znova zaženite Chrome in poskusite znova zagnati Lighthouse."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "Zahtevani z<PERSON>al<PERSON> {artifactName} se ni izvedel."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "Orodje Lighthouse ni utegnilo zanesljivo naložiti zahtevane strani. Poskrbite, da preizkušate pravilen URL in da se strežnik ustrezno odziva na vse zahteve."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "Orodje Lighthouse ni utegnilo zanesljivo naložiti zahtevanega URL-ja, ker se je stran nehala odzi<PERSON>i."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Navedeni URL nima veljavnega varnostnega potrdila. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome je preprečil nalaganje strani z vrinjenim zaslonom. Poskrbite, da preizkušate pravilen URL in da se strežnik ustrezno odziva na vse zahteve."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Orodje Lighthouse ni utegnilo zanesljivo naložiti zahtevane strani. Poskrbite, da preizkušate pravilen URL in da se strežnik ustrezno odziva na vse zahteve. (Podrobnosti: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Orodje Lighthouse ni utegnilo zanesljivo naložiti zahtevane strani. Poskrbite, da preizkušate pravilen URL in da se strežnik ustrezno odziva na vse zahteve. (Koda stanja: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Nalaganje strani je trajalo predolgo. Upoštevajte priložnosti v poročilu, da zmanjšate čas nalaganja strani, nato poskusite znova zagnati Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "Čakanje na odziv protokola za DevTools je preseglo dodeljeni čas. (Metoda: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "Trajanje pridobivanja vsebine sredstva je preseglo dodeljeni čas"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "<PERSON><PERSON><PERSON> je, da ste navedli neveljaven URL."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "Začetno krmarjenje"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "Največja zakasnitev kritične poti:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "Napaka"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "Napaka sporočila: ni podatkov o pregledu"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "Laboratorijski podatki"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "<PERSON><PERSON><PERSON> [Lighthouse](https://developers.google.com/web/tools/lighthouse/) trenutne strani v emuliranem mobilnem omrežju. Vrednosti so ocenjene in lahko odstopajo."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "Dodatni elementi za ročno preverjanje"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "Se ne uporablja"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "Priložnost"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "Ocenjeni p<PERSON>"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "Prikaži sredstva drugih ponudnikov"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "Na to izvedbo storitve Lighthouse so vp<PERSON>le težave:"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "Vrednosti so ocenjene in lahko odstopajo. Rezultat uspešnosti [temelji samo na teh meritvah](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "Pregledi so bili uspešno opravljeni, vendar z opozorili"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "Opozorila: "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "Razmislite o tem, da bi GIF naložili v storitev, prek katere bo na voljo za vdelavo kot videposnetek v obliki HTML5."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "Namestite [vtičnik za postopno nalaganje za WordPress](https://wordpress.org/plugins/search/lazy+load/) ki omogoča odlog nalaganja slik, ki niso na zaslonu, ali preidite na temo, ki ponuja to funkcijo. Razmislite tudi o uporabi [vtičnika AMP](https://wordpress.org/plugins/amp/)."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "Na voljo je več vtičnikov za WordPress, ki vam lahko pomagajo [uvrstiti nujna sredstva](https://wordpress.org/plugins/search/critical+css/) ali [odložiti manj pomembna sredstva](https://wordpress.org/plugins/search/defer+css+javascript/). <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da lah<PERSON> optimizacije, ki jih ponujajo ti vtičniki, pokvarijo delovanje funkcij vaših tem ali vtič<PERSON>, zato boste verjetno morali spremeniti kodo."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "Teme, vti<PERSON><PERSON><PERSON> in strežniške specifikacije prispevajo k odzivnemu času strežnika. Razmislite o tem, da bi poiskali bolj optimizirano temo, skrbno izbrali optimizacijski vtičnik in/ali nadgradili strežnik."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "Razmislite o tem, da bi na seznamih objav prikazali izvlečke (npr. z oznako »more«), zman<PERSON><PERSON><PERSON> število objav, prikazanih na posamezni strani, dal<PERSON>še objave razdelili na več strani ali uporabili vtičnik za odloženo nalaganje komentarjev."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "Na voljo je več [v<PERSON><PERSON><PERSON> za WordPress](https://wordpress.org/plugins/search/minify+css/) ki lahko s sestavljanjem, pomanjševanjem in stiskanjem slogov pospešijo vaše spletno mesto. Po možnosti uporabite tudi postopek gradnje, ki to pomanjševanje izvede vnaprej."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "Na voljo je več [v<PERSON><PERSON><PERSON> za WordPress](https://wordpress.org/plugins/search/minify+javascript/), ki lahko s sestavl<PERSON>, poman<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in stiskanjem slogov pospešijo vaše spletno mesto. Po možnosti uporabite tudi postopek gradnje, ki to pomanjševanje izvede vnaprej."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "Razmislite o tem, da bi zmanjšali ali spremenili število [vtičnikov za WordPress](https://wordpress.org/plugins/), ki na vaši strani nalagajo neuporabljen CSS. Če želite ugotoviti, kateri vtičniki dodajo zunanji CSS, poskušajte z orodji Chrome DevTools izvesti [pokritost kode](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage). Ustrezen vtičnik/temo lahko ugotovite na podlagi URL-ja datoteke s slogi. Bodite pozorni na vtičnike, ki imajo na seznamu mnogo datotek s slogi, ki imajo v pokritosti kode veliko rdeče obarvanega območja. Datoteka s slogi naj bo v čakalni vrsti vtičnika samo, če je dejansko uporabljena na strani."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "Razmislite o tem, da bi zmanjšali ali spremenili število [vtičnikov za WordPress](https://wordpress.org/plugins/), ki na vaši strani nalagajo neuporabljen JavaScript. Če želite ugotoviti, kateri vtičniki dodajo zunanji JS, pos<PERSON>šajte z orodji Chrome DevTools izvesti [pokritost kode](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage). Ustrezen vtičnik/temo lahko ugotovite na podlagi URL-ja skripta. Bodite pozorni na vtičnike, ki imajo na seznamu mnogo skriptov, ki imajo v pokritosti kode veliko rdeče obarvanega območja. Skript naj bo v čakalni vrsti vtičnika samo, če je dejansko uporabljen na strani."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "Preberite o [predpomnjenju brskalnika v WordPressu](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "Razmislite o tem, da bi uporabili [vtičnik za optimizacijo slik za WordPress](https://wordpress.org/plugins/search/optimize+images/), ki slike s<PERSON>, a ohrani kakovost."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "Slike naložite neposredno prek [predstavnostne knjižnice](https://codex.wordpress.org/Media_Library_Screen) in tako zagotovite, da so na voljo potrebne velikosti slik. Nato jih vstavite iz predstavnostne knjižnice ali uporabite slikovni pripomoček, da zagotovite uporabo optimalnih velikosti slik (vključno s tistimi za odzivne prekinitvene točke). Izogibajte se slikam `Full Size`, razen če so mere primerne za njihovo uporabo. [Več o tem](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)."}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "Stiskanje besedila lahko omogočite v konfiguraciji spletnega strežnika."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "Razmislite o tem, da bi uporabili [vtičnik](https://wordpress.org/plugins/search/convert+webp/) ali storite<PERSON>, ki naložene slike samodejno pretvori v optimalne oblike."}}