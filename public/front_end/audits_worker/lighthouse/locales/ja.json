{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "アクセスキーは、ユーザーがページの特定の部分にすばやくフォーカスを移動するときに使います。正しく操作できるよう、各アクセスキーは一意にする必要があります。[詳細](https://web.dev/accesskeys/)"}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "`[accesskey]` の値が一意ではありません"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]` の値は一意です"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "各 ARIA `role` は、`aria-*` 属性の特定のサブセットに対応しています。これらが一致しない場合、`aria-*` 属性は無効になります。[詳細](https://web.dev/aria-allowed-attr/)"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "`[aria-*]` 属性は役割と一致していません"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "`[aria-*]` 属性は役割と一致しています"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "一部の ARIA 役割には、スクリーン リーダーに要素の状態を伝える必須の属性があります。[詳細](https://web.dev/aria-required-attr/)"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]` に必須の `[aria-*]` 属性が一部指定されていません"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]` に必須の `[aria-*]` 属性がすべて指定されています"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "目的のユーザー補助機能を実行するには、一部の ARIA 親役割に特定の子役割を含める必要があります。[詳細](https://web.dev/aria-required-children/)"}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Elements with an ARIA `[role]` that require children to contain a specific `[role]` are missing some or all of those required children."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "Elements with an ARIA `[role]` that require children to contain a specific `[role]` have all required children."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "目的のユーザー補助機能を正しく実行するには、一部の ARIA 子役割を特定の親役割に含める必要があります。[詳細](https://web.dev/aria-required-parent/)"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]` が必須の親要素に含まれていません"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]` は必須の親要素に含まれています"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "目的のユーザー補助機能を実行するには、ARIA 役割に有効な値を指定してください。[詳細](https://web.dev/aria-roles/)"}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]` の値は無効です"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "`[role]` の値は有効です"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "支援技術（スクリーン リーダーなど）で、無効な値が指定された ARIA 属性を解釈できません。[詳細](https://web.dev/aria-valid-attr-value/)"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "`[aria-*]` 属性に有効な値が指定されていません"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "`[aria-*]` 属性に有効な値が指定されています"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "支援技術（スクリーン リーダーなど）で、無効な名前が指定された ARIA 属性を解釈できません。[詳細](https://web.dev/aria-valid-attr/)"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "`[aria-*]` 属性は無効か、スペルミスがあります"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "`[aria-*]` 属性は有効で、スペルミスもありません"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "字幕を使用すると、聴覚障がいのあるユーザーが音声要素を利用できるようになります。話している人物やその内容、その他の会話以外の情報など、重要な情報を字幕で伝えることができます。[詳細](https://web.dev/audio-caption/)"}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "`<audio>` 要素に `[kind=\"captions\"]` が指定された `<track>` 要素がありません。"}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "`<audio>` 要素に `[kind=\"captions\"]` が指定された `<track>` 要素が含まれています"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "問題のある要素"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "ボタンにユーザー補助機能名が指定されていない場合、スクリーン リーダーでは「ボタン」と読み上げられるため、スクリーン リーダーを使用しているユーザーはボタンの用途がわからず、使用することができなくなります。[詳細](https://web.dev/button-name/)"}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "ボタンにユーザー補助機能名が指定されていません"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "ボタンにユーザー補助機能名が指定されています"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "重複するコンテンツをスキップする手段を追加すると、キーボードを使ったページの移動を効率化できます。[詳細](https://web.dev/bypass/)"}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "ページに見出し、スキップリンク、またはランドマーク領域が設定されていません"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "ページに見出し、スキップリンク、またはランドマーク領域が設定されています"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "低コントラストのテキストを使用すると、多くのユーザーは読むことが困難または不可能になります。[詳細](https://web.dev/color-contrast/)"}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "背景色と前景色には十分なコントラスト比がありません"}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "背景色と前景色には十分なコントラスト比があります"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "定義リストが適切にマークアップされていないと、スクリーン リーダーで、誤解を招く内容や不正確な内容が読み上げられる可能性があります。[詳細](https://web.dev/definition-list/)"}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>` には、正しく順序付けられた `<dt>` および `<dd>` グループ、`<script>` または `<template>` 要素以外も含まれています。"}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` には、正しく順序付けられた `<dt>` および `<dd>` グループ、`<script>` または `<template>` 要素のみが含まれています。"}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "スクリーン リーダーで正しく読み上げられるようにするには、定義リストの項目（`<dt>` と `<dd>`）を親の `<dl>` 要素でラップする必要があります。[詳細](https://web.dev/dlitem/)"}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "定義リストの項目が `<dl>` 要素でラップされていません"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "定義リストの項目は `<dl>` 要素でラップされています"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "タイトルを指定すると、スクリーン リーダーのユーザーがページの概要を把握できるようになります。検索エンジンの使用時には、検索語句に関連するページかどうかを判断するための重要な要素となります。[詳細](https://web.dev/document-title/)"}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "ドキュメントに `<title>` 要素が指定されていません"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "ドキュメントに `<title>` 要素が指定されています"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "他のインスタンスが支援技術によって見落とされることのないように、id 属性の値は一意にする必要があります。[詳細](https://web.dev/duplicate-id/)"}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "ページの `[id]` 属性が一意ではありません"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "ページの `[id]` 属性は一意です"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "スクリーン リーダーでは、フレームのコンテンツを説明するためにフレームのタイトルが使用されます。[詳細](https://web.dev/frame-title/)"}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "`<frame>` または `<iframe>` の要素にタイトルが指定されていません"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "`<frame>` または `<iframe>` の要素にタイトルが指定されています"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "ページで lang 属性が指定されていない場合、スクリーン リーダーは、スクリーン リーダーの設定時にユーザーが選択したデフォルト言語がページで使用されているものと見なします。そのページでデフォルト言語が実際には使用されていない場合、スクリーン リーダーはページのテキストを正しく読み上げられない可能性があります。[詳細](https://web.dev/html-has-lang/)"}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>` 要素に `[lang]` 属性が指定されていません"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>` 要素に `[lang]` 属性が指定されています"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "有効な [BCP 47 言語](https://www.w3.org/International/questions/qa-choosing-language-tags#question)を指定すると、スクリーン リーダーでテキストが正しく読み上げられるようになります。[詳細](https://web.dev/html-lang-valid/)"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>` 要素の `[lang]` 属性に有効な値が指定されていません。"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "`<html>` 要素の `[lang]` 属性に有効な値が指定されています"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "説明的要素は、簡潔でわかりやすい代替テキストにする必要があります。装飾的要素は、alt 属性が空の場合は無視される可能性があります。[詳細](https://web.dev/image-alt/)"}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "画像要素に `[alt]` 属性が指定されていません"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "画像要素に `[alt]` 属性が指定されています"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "画像を `<input>` ボタンとして使用している場合は、代替テキストを指定すると、スクリーン リーダーのユーザーがボタンの用途を理解しやすくなります。[詳細](https://web.dev/input-image-alt/)"}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">` 要素に `[alt]` テキストが指定されていません"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">` 要素に `[alt]` テキストが指定されています"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "ラベルを使用すると、フォームの各コントロールが支援技術（スクリーン リーダーなど）によって正しく読み上げられるようになります。[詳細](https://web.dev/label/)"}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "フォームの要素にラベルが関連付けられていません"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "フォームの要素にラベルが関連付けられています"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "レイアウト目的で表を使用している場合は、th 要素、caption 要素、summary 属性などのデータ要素を含めないでください。スクリーン リーダーを操作しにくくなる可能性があります。[詳細](https://web.dev/layout-table/)"}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "レイアウト用の `<table>` 要素で、`<th>`、`<caption>`、または `[summary]` 属性が使用されています。"}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "レイアウト用の `<table>` 要素で、`<th>`、`<caption>`、または `[summary]` 属性は使用されていません。"}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "識別可能、フォーカス可能な一意のリンクテキスト（および画像をリンクとして使用している場合はその代替テキスト）を使用すると、スクリーン リーダーでのナビゲーションの操作性が向上します。[詳細](https://web.dev/link-name/)"}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "リンクに識別可能な名前が指定されていません"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "リンクに識別可能な名前が指定されています"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "スクリーン リーダーでは、特殊な方法でリストが読み上げられます。適切に読み上げられるようにするには、正しいリスト構造を指定する必要があります。[詳細](https://web.dev/list/)"}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "リストには、`<li>` 要素と、スクリプト対応要素（`<script>` と `<template>`）以外も含まれています。"}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "リストには、`<li>` 要素と、スクリプト対応要素（`<script>` と `<template>`）のみが含まれています。"}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "スクリーン リーダーで正しく読み上げられるようにするには、リスト項目（`<li>`）を親の `<ul>` または `<ol>` に含める必要があります。[詳細](https://web.dev/listitem/)"}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "リスト項目（`<li>`）が `<ul>` または `<ol>` の親要素に含まれていません。"}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "リスト項目（`<li>`）は `<ul>` または `<ol>` の親要素に含まれています"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "ユーザーはページが自動的に更新されると思っていないため、自動更新によってフォーカスがページ上部に戻ると、ユーザーの利便性が低下する可能性があります。[詳細](https://web.dev/meta-refresh/)"}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "ドキュメントで `<meta http-equiv=\"refresh\">` が使用されています"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "ドキュメントで `<meta http-equiv=\"refresh\">` が使用されていません"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "ズーム機能を無効にすると、画面の拡大操作を利用する視力の弱いユーザーがウェブページのコンテンツを確認できなくなります。[詳細](https://web.dev/meta-viewport/)"}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` が `<meta name=\"viewport\">` 要素で使用されているか、`[maximum-scale]` 属性が 5 未満に指定されています。"}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` は `<meta name=\"viewport\">` 要素で使用されておらず、`[maximum-scale]` 属性も 5 未満ではありません。"}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "スクリーン リーダーは、テキスト以外のコンテンツを解釈できません。`<object>` 要素に代替テキストを追加すると、スクリーン リーダーを使用するユーザーが意味を把握するのに役立ちます。[詳細](https://web.dev/object-alt/)"}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>` 要素に `[alt]` テキストが指定されていません"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "`<object>` 要素に `[alt]` テキストが指定されています"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "値が 0 より大きい場合は、明示的なナビゲーション順序を示します。技術的には有効ですが、多くの場合、支援技術を使用しているユーザーにとって利便性が低下します。[詳細](https://web.dev/tabindex/)"}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "一部の要素で `[tabindex]` に 0 より大きい値が指定されています"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "`[tabindex]` に 0 より大きい値を指定している要素はありません"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "スクリーン リーダーには、表内の移動を補助する機能があります。`[headers]` 属性を使用している `<td>` セルが同じ表の他のセルのみを参照するように設定すると、スクリーン リーダーの利便性が向上する可能性があります。[詳細](https://web.dev/td-headers-attr/)"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Cells in a `<table>` element that use the `[headers]` attribute refer to an element `id` not found within the same table."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "Cells in a `<table>` element that use the `[headers]` attribute refer to table cells within the same table."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "スクリーン リーダーには、表内の移動を補助する機能があります。表のヘッダーが常に一部のセルを参照するように設定すると、スクリーン リーダーの利便性が向上する可能性があります。[詳細](https://web.dev/th-has-data-cells/)"}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "`<th>` 要素および `[role=\"columnheader\"/\"rowheader\"]` が指定された要素に、記述されたデータセルがありません。"}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "`<th>` 要素および `[role=\"columnheader\"/\"rowheader\"]` が指定された要素に、記述されたデータセルがあります。"}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "要素に有効な [BCP 47 言語](https://www.w3.org/International/questions/qa-choosing-language-tags#question)を指定すると、スクリーン リーダーでテキストが正しく読み上げられるようになります。[詳細](https://web.dev/valid-lang/)"}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "`[lang]` 属性に有効な値が指定されていません"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "`[lang]` 属性に有効な値が指定されています"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "動画に字幕を設定すると、聴覚障がいのあるユーザーが情報にアクセスしやすくなります。[詳細](https://web.dev/video-caption/)"}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "`<video>` 要素に、`[kind=\"captions\"]` が指定された `<track>` 要素が含まれていません。"}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "`<video>` 要素に `[kind=\"captions\"]` が指定された `<track>` 要素が含まれています"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "音声による説明を使用すると、顔の表情や場面といった、会話で伝えることのできない動画の関連情報を提供できます。[詳細](https://web.dev/video-description/)"}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "`<video>` 要素に、`[kind=\"description\"]` が指定された `<track>` 要素が含まれていません。"}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "`<video>` 要素に `[kind=\"description\"]` が指定された `<track>` 要素が含まれています"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "ユーザーがプログレッシブ ウェブアプリを iOS のホーム画面に追加したとき、アプリが希望どおりのデザインで表示されるようにするには、`apple-touch-icon` を定義します。参照先は不透明な 192 ピクセル（または 180 ピクセル）の正方形の PNG 画像とする必要があります。[詳細](https://web.dev/apple-touch-icon/)"}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "有効な `apple-touch-icon` が提供されていません"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "`apple-touch-icon-precomposed` は古いバージョンです。`apple-touch-icon` を使用することをおすすめします。"}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "有効な `apple-touch-icon` が提供されています"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chrome 拡張機能がこのページの読み込みに悪影響を及ぼしています。シークレット モードで、または拡張機能なしの Chrome プロファイルからページを監査してみてください。"}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "スクリプトの評価"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "スクリプトの解析"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "合計 CPU 時間"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "JavaScript の解析、コンパイル、実行にかかる時間の短縮をご検討ください。配信する JavaScript ペイロードのサイズを抑えると効果が見込めます。[詳細](https://web.dev/bootup-time)"}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "JavaScript の実行にかかる時間の低減"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "JavaScript の実行にかかる時間"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "サイズの大きい GIF は、アニメーション コンテンツの配信方法として効率的ではありません。ネットワークの通信量を抑えるため、GIF を使用する代わりに、アニメーションには MPEG4/WebM 動画、静止画像には PNG/WebP を使用することをご検討ください。[詳細](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "アニメーション コンテンツでの動画フォーマットの使用"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "オフスクリーンの非表示の画像は、重要なリソースをすべて読み込んだ後に遅れて読み込むようにして、操作可能になるまでの時間を短縮することをご検討ください。[詳細](https://web.dev/offscreen-images)"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "オフスクリーン画像の遅延読み込み"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "ページの First Paint をリソースがブロックしています。重要な JavaScript や CSS はインラインで配信し、それ以外の JavaScript やスタイルはすべて遅らせることをご検討ください。[詳細](https://web.dev/render-blocking-resources)"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "レンダリングを妨げるリソースの除外"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "ネットワーク ペイロードのサイズが大きいと、ユーザーの金銭的負担が大きくなり、多くの場合、読み込み時間が長くなります。[詳細](https://web.dev/total-byte-weight)"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "合計サイズは {totalBytes, number, bytes} KB でした"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "過大なネットワーク ペイロードの回避"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "過大なネットワーク ペイロードの回避"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "CSS ファイルを最小化すると、ネットワーク ペイロードのサイズを抑えることができます。[詳細](https://web.dev/unminified-css)"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "CSS の最小化"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "JavaScript ファイルを最小化すると、ペイロード サイズとスクリプトの解析時間を抑えることができます。[詳細](https://web.dev/unminified-javascript)"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "JavaScript の最小化"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "スタイルシートから古いルールを削除し、スクロールせずに見える範囲のコンテンツに使用されていない CSS の読み込みを遅延させると、データ通信量を減らすことができます。[詳細](https://web.dev/unused-css-rules)"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "使用していない CSS を削除してください"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "使用していない JavaScript を削除して、データ通信量を減らしてください。"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "使用していない JavaScript の削除"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "キャッシュの有効期間を長くすると、再訪問したユーザーへのページの読み込み速度を向上できます。[詳細](https://web.dev/uses-long-cache-ttl)"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 件のリソースが見つかりました}other{# 件のリソースが見つかりました}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "静的なアセットと効率的なキャッシュ ポリシーの配信"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "静的なアセットでの効率的なキャッシュ ポリシーの使用"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "画像を最適化すると、読み込み時間を短縮しモバイルデータ量を抑えることができます。[詳細](https://web.dev/uses-optimized-images)"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "効率的な画像フォーマット"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "適切なサイズの画像を配信して、モバイルデータ量を節約し読み込み時間を短縮してください。[詳細](https://web.dev/uses-responsive-images)"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "適切なサイズの画像"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "テキストベースのリソースは圧縮（gzip、deflate、または brotli）して配信し、ネットワークの全体的な通信量を最小限に抑えてください。[詳細](https://web.dev/uses-text-compression)"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "テキスト圧縮の有効化"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "JPEG 2000、JPEG XR、WebP などの画像フォーマットは、PNG や JPEG より圧縮性能が高く、ダウンロード時間やデータ使用量を抑えることができます。[詳細](https://web.dev/uses-webp-images)"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "次世代フォーマットでの画像の配信"}, "lighthouse-core/audits/content-width.js | description": {"message": "アプリのコンテンツの幅がビューポートの幅と一致しない場合、アプリがモバイル画面に合わせて最適化されない可能性があります。[詳細](https://web.dev/content-width)"}, "lighthouse-core/audits/content-width.js | explanation": {"message": "ビューポートのサイズ（{innerWidth} ピクセル）がウィンドウのサイズ（{outerWidth} ピクセル）と一致していません。"}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "コンテンツのサイズとビューポートのサイズが一致していません"}, "lighthouse-core/audits/content-width.js | title": {"message": "コンテンツのサイズとビューポートのサイズが一致しています"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "下のクリティカル リクエスト チェーンでは、高い優先度で読み込まれたリソースを確認できます。チェーンの長さを縮小する、リソースのダウンロード サイズを抑える、不要なリソースのダウンロードを遅らせるなどの手段を行って、ページの読み込み速度を改善することをご検討ください。[詳細](https://web.dev/critical-request-chains)"}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 件のチェーンが見つかりました}other{# 件のチェーンが見つかりました}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "クリティカルなリクエストの深さの最小化"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "非推奨 / 警告"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "行"}, "lighthouse-core/audits/deprecations.js | description": {"message": "サポートの終了した API は最終的にブラウザから削除されます。[詳細](https://web.dev/deprecations)"}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 件の警告が見つかりました}other{# 件の警告が見つかりました}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "サポートを終了した API が使用されています"}, "lighthouse-core/audits/deprecations.js | title": {"message": "サポートを終了した API は使用されていません"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "アプリケーション キャッシュはサポートを終了しました。[詳細](https://web.dev/appcache-manifest)"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "「{AppCacheManifest}」が見つかりました"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "アプリケーション キャッシュを使用しています"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "アプリケーション キャッシュは使用していません"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "doctype を指定すると、ブラウザは後方互換モードに切り替えることができなくなります。[詳細](https://web.dev/doctype)"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "doctype 名には `html` を小文字の文字列で指定する必要があります"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "ドキュメントには doctype を指定する必要があります"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "publicId は空の文字列であることが想定されています"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "systemId は空の文字列であることが想定されています"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "ページに HTML doctype が指定されていないため、後方互換モードに切り替わります"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "ページに HTML doctype が指定されています"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "要素"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "統計情報"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "値"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "ブラウザ エンジニアは、ページに含まれる DOM の要素数が 1,500 個を超えないようにすることを推奨しています。ツリーの深さは 32 要素まで、子や親の要素数は 60 個までにするのが最適です。DOM サイズが大きいと、メモリの使用量が増え、[スタイルの計算](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations)に時間がかかり、[レイアウトのリフロー](https://developers.google.com/speed/articles/reflow)というコストが発生する可能性があります。[詳細](https://web.dev/dom-size)"}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 件の要素}other{# 件の要素}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "過大な DOM サイズの回避"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "DOM の最大深さ"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "合計 DOM 要素数"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "子要素の上限数"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "過大な DOM サイズの回避"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "rel"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "リンク先"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "パフォーマンスを向上し、セキュリティの脆弱性が生じないようにするには、`rel=\"noopener\"` または `rel=\"noreferrer\"` を外部リンクに追加します。[詳細](https://web.dev/external-anchors-use-rel-noopener)"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "クロスオリジンへのリンクは安全ではありません"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "クロスオリジンへのリンクは安全です"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "アンカー（{anchorHTML}）のリンク先を特定できません。ハイパーリンクとして使用していない場合は、target=_blank を削除することをご検討ください。"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "サイトから脈絡なしに位置情報の許可を求められると、ユーザーは不信感を抱き、困惑します。リクエストはユーザーの操作と関連付けて行うようにしてください。[詳細](https://web.dev/geolocation-on-start)"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "ページの読み込み時に位置情報の許可がリクエストされます"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "ページの読み込み時に位置情報の許可はリクエストされません"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "バージョン"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "ページで検出されたすべてのフロントエンドの JavaScript ライブラリです。[詳細](https://web.dev/js-libraries)"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "JavaScript ライブラリが検出されました"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "接続速度が遅い環境のユーザーの場合、`document.write()` で動的に挿入される外部スクリプトによってページの読み込みが数十秒遅れる可能性があります。[詳細](https://web.dev/no-document-write)"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "`document.write()` が使用されています"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "`document.write()` は使用されていません"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "最も高い重大度"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "ライブラリのバージョン"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "脆弱性の件数"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "一部の第三者スクリプトには、悪意のあるユーザーによって簡単に特定され利用されるような、既知のセキュリティの脆弱性が含まれていることがあります。[詳細](https://web.dev/no-vulnerable-libraries)"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{1 件の脆弱性が検出されました}other{# 件の脆弱性が検出されました}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "既知のセキュリティの脆弱性を含んだフロントエンドの JavaScript ライブラリが含まれています"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "高"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "低"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "中"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "既知のセキュリティの脆弱性を含んだフロントエンドの JavaScript ライブラリは除外されています"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "サイトから脈絡なしに通知の送信許可を求められると、ユーザーは不信感を抱き、困惑します。リクエストはユーザーの操作と関連付けて行うようにしてください。[詳細](https://web.dev/notification-on-start)"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "ページの読み込み時に通知の許可がリクエストされます"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "ページの読み込み時に通知の許可はリクエストされません"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "問題のある要素"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "パスワードの貼り付けを禁止すると、良好なセキュリティ ポリシーが損なわれます。[詳細](https://web.dev/password-inputs-can-be-pasted-into)"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "ユーザーはパスワード欄に貼り付けできません"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "ユーザーはパスワード欄に貼り付けできます"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "プロトコル"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 には、バイナリ ヘッダー、多重化、サーバー プッシュなど、HTTP/1.1 と比べて多くのメリットがあります。[詳細](https://web.dev/uses-http2)"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{HTTP/2 経由で配信されなかったリクエストが 1 件あります}other{HTTP/2 経由で配信されなかったリクエストが # 件あります}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "ページリソースの一部で HTTP/2 が使用されていません"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "ページ独自のリソースで HTTP/2 が使用されています"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "ページのスクロール パフォーマンスを高めるには、touch および wheel イベント リスナーを `passive` として指定することをご検討ください。[詳細](https://web.dev/uses-passive-event-listeners)"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "スクロール パフォーマンスを高める受動的なリスナーが使用されていません"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "スクロール パフォーマンスを高める受動的なリスナーが使用されています"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "説明"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "コンソールに記録されたエラーは未解決の問題を表します。これらはネットワーク リクエストの失敗や他のブラウザの問題が原因で表示される可能性があります。[詳細](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "ブラウザのエラーがコンソールに記録されました"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "コンソールに記録されたブラウザのエラーはありません"}, "lighthouse-core/audits/font-display.js | description": {"message": "フォント表示の CSS 機能を使用して、Web フォントの読み込み中にユーザーがテキストを読めるようにしてください。[詳細](https://web.dev/font-display)"}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "ウェブフォント読み込み中のテキストの表示"}, "lighthouse-core/audits/font-display.js | title": {"message": "ウェブフォント読み込み中の全テキストの表示"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "次の URL の font-display の値を Lighthouse で確認できませんでした: {fontURL}"}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "アスペクト比（実際）"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "アスペクト比（表示）"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "画像は本来のアスペクト比で表示する必要があります。[詳細](https://web.dev/image-aspect-ratio)"}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "不適切なアスペクト比の画像が表示されています"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "正しいアスペクト比の画像が表示されています"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "画像サイズに関する情報（{url}）が正しくありません"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "ブラウザではホーム画面へのアプリの追加を促すメッセージを事前にユーザーに表示できます。これによりエンゲージメントが向上します。[詳細](https://web.dev/installable-manifest)"}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "ウェブアプリのマニフェストはインストール可能となる要件を満たしていません"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "ウェブアプリのマニフェストはインストール可能となる要件を満たしています"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "安全でない URL"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "すべてのサイトは、機密性の高い情報を扱っていない場合でも、HTTPS で保護する必要があります。HTTPS は、侵入者があなたのアプリとユーザー間の通信を改ざんしたり、傍受したりするのを防ぎます。HTTPS は、HTTP/2 や多くの新しいウェブ プラットフォーム API を使用するための前提条件となります。[詳細](https://web.dev/is-on-https)"}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{安全でないリクエストが 1 件見つかりました}other{安全でないリクエストが # 件見つかりました}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "HTTPS が使用されていません"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "HTTPS を使用しています"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "モバイル ネットワークでのページ読み込みを速くすることで、優れたモバイル ユーザー エクスペリエンスが実現されます。[詳細](https://web.dev/load-fast-enough-for-pwa)"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "操作可能になるまでの時間: {timeInMs, number, seconds} 秒"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "モバイル ネットワークのシミュレーションで操作可能になるまでの時間: {timeInMs, number, seconds} 秒"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "ページの読み込みが遅すぎます。応答に 10 秒以上かかっています。改善方法については、[パフォーマンス] に表示される最適化案と診断結果をご確認ください。"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "ページ読み込みはモバイル ネットワークで十分な速度で実行されません"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "ページ読み込みはモバイル ネットワークで十分な速度で実行されます"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "カテゴリ"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "Consider reducing the time spent parsing, compiling and executing JS. You may find delivering smaller JS payloads helps with this. [Learn more](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "メインスレッド処理の最小化"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "メインスレッド処理の最小化"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "ユーザー数を最大限に増やすには、サイトがすべての主要ブラウザで機能するようにします。[詳細](https://web.dev/pwa-cross-browser)"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "サイトがクロスブラウザに対応している"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "ソーシャル メディアで共有できるように、個々のページが URL によるディープリンクに対応していることと、それらの URL が固有であることを確認してください。[詳細](https://web.dev/pwa-each-page-has-url)"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "ページごとに 1 つの URL を使用している"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "Transitions should feel snappy as you tap around, even on a slow network. This experience is key to a user's perception of performance. [Learn more](https://web.dev/pwa-page-transitions)."}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "ページの切り替え時、ネットワークが遅いという印象を与えない"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "入力の推定待ち時間とは、ページ読み込みの最もビジーな 5 秒間における、ユーザーの入力に対するアプリの推定応答時間（ミリ秒単位）です。待ち時間が 50 ミリ秒より長い場合、ユーザーはアプリの反応が悪いと感じる可能性があります。[詳細](https://web.dev/estimated-input-latency)"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "入力の推定待ち時間"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "First Contentful Paint は、テキストまたは画像が初めてペイントされるまでにかかった時間です。[詳細](https://web.dev/first-contentful-paint)"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "First Contentful Paint"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "First CPU Idle marks the first time at which the page's main thread is quiet enough to handle input.  [Learn more](https://web.dev/first-cpu-idle)."}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "CPU の初回アイドル"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "First Meaningful Paint は、ページの主要なコンテンツが可視化されるまでにかかった時間です。[詳細](https://web.dev/first-meaningful-paint)"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "First Meaningful Paint"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "操作可能になるまでの時間とは、ページが完全に操作可能になるのに要する時間です。[詳細](https://web.dev/interactive)"}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "インタラクティブになるまでの時間"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "ユーザーに発生する可能性がある初回入力遅延の最大推定時間とは、最も長いタスクの時間（ミリ秒単位）です。[詳細](https://developers.google.com/web/updates/2018/05/first-input-delay)"}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "初回入力遅延の最大推定時間"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "速度インデックスは、ページのコンテンツが取り込まれて表示される速さを表します。[詳細](https://web.dev/speed-index)"}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "速度インデックス"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "タスクの時間が 50 ミリ秒を上回った場合の、コンテンツの初回ペイントから操作可能になるまでのすべての時間の合計を、ミリ秒単位で表します。"}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "合計ブロック時間"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "ネットワークのラウンドトリップ時間（RTT）はパフォーマンスに大きく影響します。発信元への RTT が高い場合は、サーバーの場所をユーザーの近くにするとパフォーマンスを改善できることを示しています。[詳細](https://hpbn.co/primer-on-latency-and-bandwidth/)"}, "lighthouse-core/audits/network-rtt.js | title": {"message": "ネットワークのラウンドトリップ時間"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "サーバーの待ち時間はウェブ パフォーマンスに影響することがあります。発信元のサーバーのレイテンシが高い場合は、サーバーが過負荷であるか、サーバーのバックエンド パフォーマンスが低いことを示しています。[詳細](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)"}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "サーバーのバックエンド待ち時間"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "Service Worker によって、ネットワークが予期しない状態になったときでもウェブアプリの安定した動作が可能になります。[詳細](https://web.dev/offline-start-url)"}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url` は、オフライン時にステータス 200 の応答を返しません"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url` は、オフライン時にステータス 200 の応答を返します"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "Lighthouse でマニフェストから `start_url` を読み取れませんでした。その結果、`start_url` はドキュメントの URL と認識されています。エラー メッセージ: 「{manifestWarning}」。"}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "予算超過"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "ネットワーク リクエストの数とサイズが、指定したパフォーマンス予算の設定目標内に収まるよう維持します。[詳細](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 件のリクエスト}other{# 件のリクエスト}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "パフォーマンス予算"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "HTTPS を設定済みの場合は、すべてのユーザーに安全なウェブ機能を提供できるように、すべての HTTP トラフィックが HTTPS にリダイレクトされることを確認してください。[詳細](https://web.dev/redirects-http)"}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "HTTP トラフィックは HTTPS にリダイレクトされません"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "HTTP トラフィックは HTTPS にリダイレクトされます"}, "lighthouse-core/audits/redirects.js | description": {"message": "リダイレクトを行うと、ページの読み込みにさらに時間がかかる可能性があります。[詳細](https://web.dev/redirects)"}, "lighthouse-core/audits/redirects.js | title": {"message": "複数のページ リダイレクトの回避"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "ページリソースの数とサイズの予算を設定するには、budget.json ファイルを追加します。[詳細](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 件のリクエスト • {byteCount, number, bytes} KB}other{# 件のリクエスト • {byteCount, number, bytes} KB}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "リクエスト数を少なく、転送サイズを小さく維持してください"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "正規リンクで、検索結果に表示する URL を指定します。[詳細](https://web.dev/canonical)"}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "複数の URL が競合しています（{urlList}）"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "別のドメイン（{url}）を指しています"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "URL（{url}）が無効です"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "別の `hreflang` 位置（{url}）を指しています"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "相対 URL（{url}）"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "コンテンツの同等のページではなく、ドメインのルート URL（ホームページ）を参照しています。"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "ドキュメントに有効な `rel=canonical` が指定されていません"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "ドキュメントに有効な `rel=canonical` が指定されています"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "12 px より小さいフォントサイズは小さすぎて判読できず、モバイル ユーザーには「ピンチしてズーム」の操作が必要になります。60% を超えるページ テキストでフォント サイズが 12 px 以上になるようにしてください。[詳細](https://web.dev/font-size)"}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "判読可能なテキスト: {decimalProportion, number, extendedPercent}"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "テキストが判読できません。モバイル スクリーン向けに最適化されたビューポート メタタグがありません。"}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "{decimalProportion, number, extendedPercent} のテキストが小さすぎます（{decimalProportionVisited, number, extendedPercent} のサンプルを基準とします）。"}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "ドキュメントで判読可能なフォントサイズが使用されていません"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "ドキュメントで判読可能なフォントサイズが使用されています"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "hreflang リンクを利用して、所定の言語や地域の検索結果に掲載する必要があるページのバージョンを検索エンジンに伝えます。[詳細](https://web.dev/hreflang)"}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "ドキュメントに有効な `hreflang` が指定されていません"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "ドキュメントに有効な `hreflang` が指定されています"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "HTTP ステータス コードが正しくないページはインデックスに適切に登録されていない可能性があります。[詳細](https://web.dev/http-status-code)"}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "ページに設定されている HTTP ステータス コードが正しくありません"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "ページに適切な HTTP ステータス コードが指定されています"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "ページのクロールを許可しない場合、検索エンジンはそのページを検索結果に追加できません。[詳細](https://web.dev/is-crawable)"}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "ページのインデックス登録を行えません"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "ページのインデックス登録はブロックされていません"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "リンクテキストをわかりやすくすると、検索エンジンがコンテンツを認識しやすくなります。[詳細](https://web.dev/link-text)"}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 件のリンクが見つかりました}other{# 件のリンクが見つかりました}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "リンクにわかりやすいテキストが設定されていません"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "リンクにわかりやすいテキストが設定されています"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "[構造化データ テストツール](https://search.google.com/structured-data/testing-tool/)と[構造化データ用 Linter](http://linter.structured-data.org/) を実行して構造化データを検証してください。[詳細](https://web.dev/structured-data)"}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "構造化データが無効です"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "メタ ディスクリプションを検索結果に追加すると、ページ コンテンツの内容を簡潔にまとめることができます。[詳細](https://web.dev/meta-description)"}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "説明のテキストが空です。"}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "ドキュメントにメタ ディスクリプションが指定されていません"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "ドキュメントにメタ ディスクリプションが指定されています"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "検索エンジンはプラグイン コンテンツをインデックスに登録できません。多くのデバイスで、プラグインが制限され、プラグインがサポートされていないこともあります。[詳細](https://web.dev/plugins)"}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "ドキュメントでプラグインを使用しています"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "ドキュメントではプラグインを使用できません"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "robots.txt ファイルの形式が間違っていると、ウェブサイトのクロールやインデックス登録について指定した設定をクローラが認識できない可能性があります。[詳細](https://web.dev/robots-txt)"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "robots.txt のリクエストで返された HTTP ステータス: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 件のエラーが見つかりました}other{# 件のエラーが見つかりました}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse は robots.txt ファイルをダウンロードできませんでした"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt が無効です"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "robots.txt は有効です"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "ボタンやリンクなどの操作可能な要素は十分な大きさ（48x48 px）に設定し、他の要素と重ならずに簡単にタップできるよう、要素の周囲にスペースを取る必要があります。[詳細](https://web.dev/tap-targets)"}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} でタップ ターゲットが適切なサイズに設定されています"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "タップ ターゲットが小さすぎます。モバイル スクリーン向けに最適化されたビューポート メタタグがありません。"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "タップ ターゲットのサイズが適切に設定されていません"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "重複するターゲット"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "タップ ターゲット"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "タップ ターゲットのサイズは適切に設定されています"}, "lighthouse-core/audits/service-worker.js | description": {"message": "Service Worker は、多くのプログレッシブ ウェブアプリ機能（オフライン、ホーム画面への追加、プッシュ通知など）をアプリで使用できるようにするための技術です。[詳細](https://web.dev/service-worker)"}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "このページは Service Worker によって制御されていますが、マニフェストが有効な JSON としてパースされなかったため、`start_url` は見つかりませんでした"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "このページは Service Worker によって制御されていますが、`start_url`（{startUrl}）が Service Worker のスコープ（{scopeUrl}）内にありません"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "このページは Service Worker によって制御されていますが、マニフェストが取得されなかったため、`start_url` は見つかりませんでした。"}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "この発信元には Service Worker が存在しますが、ページ（{pageUrl}）がスコープ内にありません。"}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "ページと `start_url` を制御する Service Worker が登録されていません"}, "lighthouse-core/audits/service-worker.js | title": {"message": "ページと `start_url` を制御する Service Worker が登録されています"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "テーマのあるスプラッシュ画面を設定すると、ホーム画面からのアプリの起動時に、質の良いアプリであることをユーザーにアピールできます。[詳細](https://web.dev/splash-screen)"}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "カスタムのスプラッシュ画面が設定されていません"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "カスタムのスプラッシュ画面が設定されています"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "ブラウザのアドレスバーにサイトに合わせたテーマを設定できます。[詳細](https://web.dev/themed-omnibox)"}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "アドレスバーにテーマの色が設定されていません。"}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "アドレスバーにテーマの色が設定されています。"}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "メインスレッドのブロック時間"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "第三者"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "第三者コードによって、読み込み速度が著しく低下する可能性があります。重複する第三者プロバイダの数を制限したうえで、ページのメインの部分を読み込み終えた後に第三者コードを読み込んでみてください。[詳細](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)"}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "第三者コードによってメインスレッドが {timeInMs, number, milliseconds} ミリ秒間ブロックされました"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "第三者コードの影響を抑えてください"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "第三者コードの利用"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "最初の 1 バイトまでの時間は、サーバーが応答を返すまでにかかった時間を表しています。[詳細](https://web.dev/time-to-first-byte)"}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "ルート ドキュメントの読み込みに {timeInMs, number, milliseconds} ミリ秒かかりました"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "サーバー応答時間の短縮（TTFB）"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "サーバーの応答時間が遅い（TTFB）"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "継続時間"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "開始時間"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "タイプ"}, "lighthouse-core/audits/user-timings.js | description": {"message": "User Timing API を使用してアプリをインストルメント化し、主要なユーザー エクスペリエンスでのアプリの実際のパフォーマンスを測定できるようにしてください。[詳細](https://web.dev/user-timings)"}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 件のカスタム速度}other{# 件のカスタム速度}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "カスタム速度の記録と計測"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "「{security<PERSON><PERSON><PERSON>}」で事前接続の <link> が見つかりましたが、ブラウザで使用されませんでした。`crossorigin` 属性を適切に使用していることをご確認ください。"}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "重要な第三者ドメインへの接続を早期に確立できるように、`preconnect` または `dns-prefetch` のリソースヒントを追加することを検討してください。[詳細](https://web.dev/uses-rel-preconnect)"}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "必須のドメインへの事前接続"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "「{preloadURL}」のプリロード <link> が見つかりましたが、ブラウザで使用されませんでした。`crossorigin` 属性を適切に使用していることをご確認ください。"}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "`<link rel=preload>` を使用して、現在ページ読み込みの後のほうでリクエストしているリソースを優先的に取得することをご検討ください。[詳細](https://web.dev/uses-rel-preload)"}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "キー リクエストのプリロード"}, "lighthouse-core/audits/viewport.js | description": {"message": "モバイル画面用にアプリを最適化するには、`<meta name=\"viewport\">` タグを追加してください。[詳細](https://web.dev/viewport)"}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "`<meta name=\"viewport\">` タグが見つかりません"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "`width` または `initial-scale` を指定した `<meta name=\"viewport\">` タグがありません"}, "lighthouse-core/audits/viewport.js | title": {"message": "`width` または `initial-scale` を指定した `<meta name=\"viewport\">` タグがあります"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "JavaScript が無効になっている場合でも、アプリではなんらかのコンテンツを表示する必要があります。「アプリの使用には JavaScript が必要」という旨の警告を表示するだけでもかまいません。[詳細](https://web.dev/without-javascript)"}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "スクリプトを利用できない場合でも、ページの本文にはなんらかのコンテンツを表示する必要があります。"}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "JavaScript を利用できない場合の代替コンテンツが提供されていません"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "JavaScript を利用できない場合のコンテンツが含まれています"}, "lighthouse-core/audits/works-offline.js | description": {"message": "プログレッシブ ウェブアプリを作成している場合は、アプリがオフラインでも動作するように、Service Worker の使用を検討してください。[詳細](https://web.dev/works-offline)"}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "現在のページは、オフライン時にステータス 200 の応答を返しません"}, "lighthouse-core/audits/works-offline.js | title": {"message": "現在のページは、オフライン時にステータス 200 の応答を返します"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "テスト URL（{requested}）が「{final}」にリダイレクトされているため、このページはオフラインで読み込まれない可能性があります。2 番目の URL を直接テストしてみてください。"}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "ここには、アプリでの ARIA の使用方法に関する改善点が表示されます。修正すると、支援技術（スクリーン リーダーなど）の利便性が向上する可能性があります。"}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "ここで、音声と動画の代替コンテンツを提供できます。代替コンテンツを提供すると、聴覚や視覚に障がいがあるユーザーの利便性が向上する可能性があります。"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "音声と動画"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "ここで、一般的なユーザー補助機能のおすすめの方法を確認できます。"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "おすすめの方法"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "これらのチェックにより、[ウェブアプリのユーザー補助機能の改善点](https://developers.google.com/web/fundamentals/accessibility)が明確になります。自動的に検出できるユーザー補助の問題は一部に過ぎないため、手動テストも実施することをおすすめします。"}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "ここに、自動テストツールではカバーできない範囲に対処する項目が表示されます。詳しくは、[ユーザー補助機能の審査を実施する](https://developers.google.com/web/fundamentals/accessibility/how-to-review)方法についてのガイドをご覧ください。"}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "ユーザー補助"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "ここには、コンテンツの読みやすさに関する改善点が表示されます。"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "コントラスト"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "ここには、地域ユーザー別のコンテンツの解釈に関する改善点が表示されます。"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "多言語対応とローカライズ"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "ここには、アプリ内のコントロールのセマンティクスに関する改善点が表示されます。修正すると、支援技術（スクリーン リーダーなど）の利便性が向上する可能性があります。"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "名前とラベル"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "ここには、アプリのキーボード操作性に関する改善点が表示されます。"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "操作性"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "ここには、支援技術（スクリーン リーダーなど）を使用した表やリストのデータの読み取りの利便性に関する改善点が表示されます。"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "表とリスト"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "おすすめの方法"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "パフォーマンス予算は、サイトのパフォーマンスに関する基準を設定します。"}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "予算（リソースの上限）"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "アプリケーションのパフォーマンスに関する詳細。これらの数値は、パフォーマンス スコアには[直接影響](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)しません。"}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "診断"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "パフォーマンスの最も重要な点は、ピクセルをどのくらい速く画面にレンダリングできるかです。主要な指標: First Contentful Paint、First Meaningful Paint"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "First Paint の改善点"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "これらの提案を実施すると、ページの読み込み時間を短縮できる可能性があります。なお、パフォーマンス スコアには[直接影響](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)しません。"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "改善できる項目"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "指標"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "読み込みの全体的なパフォーマンスを改善して、ページの反応性や操作性を高めましょう。主要な指標: インタラクティブになるまでの時間、速度インデックス"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "全体的な改善点"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "パフォーマンス"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "これらのチェックではプログレッシブ ウェブアプリのさまざまな側面が検証されます。[詳細](https://developers.google.com/web/progressive-web-apps/checklist)"}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "これらのチェック項目は基本の [PWA チェックリスト](https://developers.google.com/web/progressive-web-apps/checklist)で必須とされていますが、Lighthouse では自動的にチェックされません。スコアには影響しませんが、手動で確認することが重要です。"}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "プログレッシブ ウェブアプリ"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "スピードと高い信頼性"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "インストール対応"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PWA の最適化"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "これらのチェックを実行することで、ページが検索エンジンの検索結果ランキングに対し最適化されていることを確認できます。Lighthouse ではチェックされない、検索結果ランキングに影響する可能性がある他の要因もあります。[詳細](https://support.google.com/webmasters/answer/35769)"}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "サイトでこれらの他の検証ツールを実行し、SEO のその他のおすすめの方法をご確認ください。"}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "クローラがアプリのコンテンツを正確に読み取れるように HTML を適切な形式で記述します。"}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "コンテンツ制作のおすすめの方法"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "検索結果に表示するには、クローラがアプリにアクセスできるようにする必要があります。"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "クロールとインデックス登録"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "ページをスマホ対応にして、ピンチ操作や拡大操作なしでコンテンツを読めるようにします。[詳細](https://developers.google.com/search/mobile-sites/)"}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "スマホ対応"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "キャッシュの TTL"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "場所"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "名前"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "リクエスト"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "リソースの種類"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "サイズ"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "かかった時間"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "転送サイズ"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "減らせるデータ量"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "短縮できる時間"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "{wastedBytes, number, bytes} KB 減らせます"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "{wastedMs, number, milliseconds} ミリ秒短縮できます"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "ドキュメント"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "フォント"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "画像"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "メディア"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ミリ秒"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "その他"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "スクリプト"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} 秒"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "スタイルシート"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "第三者"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "合計"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "ページを読み込む際のトレースの記録中にエラーが発生しました。もう一度 Lighthouse を実行してください。（{errorCode}）"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "Debugger プロトコル接続の開始中にタイムアウトが発生しました。"}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome でページの読み込み中にスクリーンショットが収集されませんでした。ページにコンテンツが表示されていることを確認してから、Lighthouse を再実行してください。（{errorCode}）"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "DNS サーバーは指定したドメインを解決できませんでした。"}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "必須の {artifactName} の収集でエラー（{errorMessage}）が発生しました"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "Chrome 内部エラーが発生しました。Chrome を再起動して Lighthouse を再実行してください。"}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "必須の {artifactName} の収集は行われませんでした。"}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "リクエストしたページを Lighthouse で正確に読み込めませんでした。正しい URL でテストを行い、すべてのリクエストに対してサーバーからの応答が適切であることを確認してください。"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "ページからの応答が停止されたため、リクエストした URL を Lighthouse で正確に読み込めませんでした。"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "指定した URL には有効なセキュリティ証明書がありません。{securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome によりページ読み込みが停止され、中間ページが表示されました。テスト対象の URL が正しいこと、サーバーがすべてのリクエストに適切に応答していることを確認してください。"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "リクエストしたページを Lighthouse で正確に読み込めませんでした。テスト対象の URL が正しいこと、サーバーがすべてのリクエストに適切に応答していることを確認してください。（詳細: {errorDetails}）"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "リクエストしたページを Lighthouse で正確に読み込めませんでした。テスト対象の URL が正しいこと、サーバーがすべてのリクエストに適切に応答していることを確認してください。（ステータス コード: {statusCode}）"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "ページの読み込み時間が長すぎます。ページの読み込み時間を短縮するには、レポートに示される提案を実施してください。その後で、Lighthouse を再実行してください。（{errorCode}）"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "DevTools プロトコルからの応答の待ち時間が、割り当てられた時間を超えました。（メソッド: {protocolMethod}）"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "リソース コンテンツの取得時間が、割り当てられた時間を超えました"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "指定した URL は無効の可能性があります。"}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "監査を表示"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "最初の移動先"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "クリティカル パスの最大待ち時間:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "エラー"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "レポートエラー: 監査情報はありません"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "ラボデータ"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "エミュレート済みモバイル ネットワークでの現在のページに関する [Lighthouse](https://developers.google.com/web/tools/lighthouse/) 分析です。推定値のため変動する可能性があります。"}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "個別の検証が必要な他の項目"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "該当なし"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "改善できる項目"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "短縮できる時間（推定）"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "合格した監査"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "スニペットを折りたたむ"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "スニペットを展開"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "第三者リソースを表示"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "Lighthouse の実行に影響する問題が発生しました。"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "推定値のため変動する可能性があります。パフォーマンス スコアは、[こちらの指標のみを基準に算出](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)されます。"}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "監査には合格しましたが警告があります"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "警告: "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "GIF を HTML5 動画として埋め込み可能なサービスにアップロードすることをご検討ください。"}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "[WordPress の遅延読み込みプラグイン](https://wordpress.org/plugins/search/lazy+load/)をインストールすると、画面外の画像の読み込みを遅らせたり、遅延読み込み機能のあるテーマに切り替えたりできます。[AMP プラグイン](https://wordpress.org/plugins/amp/)の使用もご検討ください。"}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "[重要なアセットをインラインで読み込む](https://wordpress.org/plugins/search/critical+css/)または[重要度が低いリソースの読み込みを遅らせる](https://wordpress.org/plugins/search/defer+css+javascript/)ために役立つ、さまざまな WordPress プラグインがあります。ただし、これらのプラグインの最適化処理によって、テーマやプラグインの機能が阻害されることがあります。その場合は、コードを変更する必要があります。"}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "テーマ、プラグイン、サーバー仕様はすべてサーバーの応答時間に影響します。より最適化されたテーマを探す、最適化プラグインを慎重に選ぶ、サーバーをアップグレードすることをおすすめします。"}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "「詳しく読む」タグなどを使用して投稿リストに抜粋を表示する、ページに表示する投稿の数を減らす、長い投稿を複数のページに分けて表示する、またはコメントを遅延読み込みするプラグインを使用することをご検討ください。"}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "スタイルを結合、軽量化、圧縮してサイトの動作を速くする、さまざまな [WordPress プラグイン](https://wordpress.org/plugins/search/minify+css/)があります。可能な場合は、ビルド処理で事前に軽量化しておくこともおすすめします。"}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "スクリプトを結合、軽量化、圧縮してサイトの動作を速くする、さまざまな [WordPress プラグイン](https://wordpress.org/plugins/search/minify+javascript/)があります。可能な場合は、ビルド処理で事前に軽量化しておくこともおすすめします。"}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "ページで使用されていない CSS を読み込む [WordPress プラグイン](https://wordpress.org/plugins/)の数を減らすか、他のプラグインに切り替えることをご検討ください。不要な CSS を読み込んでいるプラグインを特定するには、Chrome DevTools で[コードの Coverage](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) を確認します。スタイルシートの URL から、それを使用しているテーマやプラグインを特定できます。多くのスタイルシートを使用しているプラグイン（コードの Coverage で赤色の部分が多いもの）をリストで探します。プラグインによってキューに追加されるスタイルシートは、実際にページで使用されるもののみにする必要があります。"}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "ページで使用されていない JavaScript を読み込む [WordPress プラグイン](https://wordpress.org/plugins/)の数を減らすか、他のプラグインに切り替えることをご検討ください。不要な JavaScript を読み込んでいるプラグインを特定するには、Chrome DevTools で[コードの Coverage](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) を確認します。スクリプトの URL から、該当のテーマやプラグインを特定できます。多くのスクリプトを使用しているプラグイン（コードの Coverage で赤色の部分が多いもの）をリストで探します。プラグインによってキューに追加されるスクリプトは、実際にページで使用されるもののみにする必要があります。"}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "[WordPress のブラウザ キャッシング](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)についてご確認ください。"}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "画質を落とさずに画像を圧縮できる [WordPress の画像最適化プラグイン](https://wordpress.org/plugins/search/optimize+images/)の使用をご検討ください。"}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "[メディア ライブラリ](https://codex.wordpress.org/Media_Library_Screen)から直接画像をアップロードして必要な画像サイズを利用できるようにしたうえで、メディア ライブラリから挿入するか、画像ウィジェットを使用して、最適な画像サイズ（レスポンシブ ブレークポイントのサイズを含む）が使用されるようにします。「`Full Size`」の画像は、十分なスペースがある場合を除いて使用しないようにします。[詳細](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)"}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "ウェブサーバーの設定でテキスト圧縮を有効にできます。"}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "アップロードした画像を最適なフォーマットに自動変換する[プラグイン](https://wordpress.org/plugins/search/convert+webp/)またはサービスの使用をご検討ください。"}}