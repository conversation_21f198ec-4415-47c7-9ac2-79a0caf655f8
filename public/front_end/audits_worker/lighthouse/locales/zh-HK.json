{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "快速鍵可讓使用者快速聚焦網頁的特定部分。如要讓使用者正確瀏覽，每個快速鍵一律不可重複。[瞭解詳情](https://web.dev/accesskeys/)。"}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "`[accesskey]` 的值重複"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]` 值獨一無二"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "每個 ARIA「`role`」都支援一部分特定的「`aria-*`」屬性。配對錯誤會導致「`aria-*`」屬性無效。[瞭解詳情](https://web.dev/aria-allowed-attr/)。"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "`[aria-*]` 屬性與其角色不符"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "`[aria-*]` 屬性與其角色相符"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "部分 ARIA 角色的必要屬性會向螢幕閱讀器使用者說明元素的狀態。[瞭解詳情](https://web.dev/aria-required-attr/)。"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]` 未具備所有必要的 `[aria-*]` 屬性"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]` 具備所有必要的 `[aria-*]` 屬性"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "部分 ARIA 父角色必須包含特定的子角色，才能正確執行無障礙功能。[瞭解詳情](https://web.dev/aria-required-children/)。"}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "包含 ARIA `[role]` 且要求子元素包含特定 `[role]` 的元素缺少部分或全部的必要子元素。"}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "包含 ARIA `[role]` 且要求子元素包含特定 `[role]` 的元素具有全部必要的子元素。"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "部分 ARIA 子角色必須包括在特定的父角色中，才能正確執行無障礙功能。[瞭解詳情](https://web.dev/aria-required-parent/)。"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]` 未包含在必要的父元素中"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]` 已包含在必要的父元素中"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "ARIA 角色必須具備有效的值，才能執行無障礙功能。[瞭解詳情](https://web.dev/aria-roles/)。"}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]` 值無效"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "`[role]` 值有效"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "輔助技術 (如螢幕閱讀器) 無法解讀具有無效值的 ARIA 屬性。[瞭解詳情](https://web.dev/aria-valid-attr-value/)。"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "`[aria-*]` 屬性並無有效的值"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "`[aria-*]` 屬性具備有效的值"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "輔助技術 (例如螢幕閱讀器) 無法解讀名稱無效的 ARIA 屬性。[瞭解詳情](https://web.dev/aria-valid-attr/)。"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "`[aria-*]` 屬性無效或拼字錯誤"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "`[aria-*]` 屬性有效且無拼字錯誤"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "字幕可提供說話者身分、說話內容等關鍵資訊以及其他非口語資訊，方便失聰或聽障使用者使用音訊元素。[瞭解詳情](https://web.dev/audio-caption/)。"}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "`<audio>` 元素缺少帶有 `[kind=\"captions\"]` 的 `<track>` 元素。"}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "`<audio>` 元素包含帶有 `[kind=\"captions\"]` 的`<track>` 元素"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "審核失敗的元素"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "如果按鈕沒有可存取的名稱，螢幕閱讀器只會讀出「按鈕」，導致依賴螢幕閱讀器的使用者並法使用該按鈕。[瞭解詳情](https://web.dev/button-name/)。"}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "按鈕沒有可存取的名稱"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "按鈕有可存取的名稱"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "為重複的內容新增略過選項，可提高鍵盤使用者的網頁瀏覽效率。[瞭解詳情](https://web.dev/bypass/)。"}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "網頁中沒有標題、略過連結或地標區域"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "網頁包含標題、略過連結或地標區域"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "大部分使用者難以閱讀或無法閱讀低對比度的文字。[瞭解詳情](https://web.dev/color-contrast/)。"}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "背景和前景顏色沒有足夠的對比度。"}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "背景和前景顏色有足夠的對比度"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "如果定義清單的標記不正確，螢幕閱讀器可能會輸出令人混淆或不正確的內容。[瞭解詳情](https://web.dev/definition-list/)。"}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>` 並非只包含排序正確的 `<dt>` 和 `<dd>` 群組、`<script>` 或 `<template>` 元素。"}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` 只包含排序正確的 `<dt>` 和 `<dd>` 群組、`<script>` 或 `<template>` 元素。"}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "定義清單項目 (`<dt>` 和 `<dd>`) 必須納入在父 `<dl>` 元素中，才能確保螢幕閱讀器正確朗讀這些項目。[瞭解詳情](https://web.dev/dlitem/)。"}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "定義清單項目未納入在 `<dl>` 元素中"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "定義清單項目已納入在 `<dl>` 元素中"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "標題可讓螢幕閱讀器使用者概略瞭解網頁內容；搜尋引擎使用者經常需要使用此資料，判斷網頁內容是否與他們的搜尋查詢有關。[瞭解詳情](https://web.dev/document-title/)。"}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "文件並無有效的 `<title>` 元素"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "文件具備 `<title>` 元素"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "ID 屬性的值不可重複，以免輔助技術忽略其他例項。[瞭解詳情](https://web.dev/duplicate-id/)。"}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "網頁中的 `[id]` 屬性重複"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "網頁中的 `[id]` 屬性沒有重複"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "螢幕閱讀器使用者依賴頁框標題來瞭解頁框內容。[瞭解詳情](https://web.dev/frame-title/)。"}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "`<frame>` 或 `<iframe>` 元素沒有標題"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "`<frame>` 或 `<iframe>` 元素包含名稱"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "如果網頁未指定 [lang] 屬性，螢幕閱讀器會假設網頁採用使用者設定螢幕閱讀器時選擇的預設語言。如果網頁實際並非採用預設語言，螢幕閱讀器可能無法正確朗讀文字。[瞭解詳情](https://web.dev/html-has-lang/)。"}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>` 元素並無 `[lang]` 屬性"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>` 元素具備 `[lang]` 屬性"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "指定有效的 [BCP 47 語言](https://www.w3.org/International/questions/qa-choosing-language-tags#question)可協助螢幕閱讀器正確朗讀文字。[瞭解詳情](https://web.dev/html-lang-valid/)。"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>` 元素的 `[lang]` 屬性並無有效的值。"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "`<html>` 元素的 `[lang]` 屬性具備有效的值"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "資訊型元素應提供簡短貼切的替代文字。只要將 alt 屬性留空，系統便會忽略該裝飾元素。[瞭解詳情](https://web.dev/image-alt/)。"}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "圖片元素並無 `[alt]` 屬性"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "圖片元素具有 `[alt]` 屬性"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "如果 `<input>` 按鈕是以圖片呈現，提供替代文字可協助螢幕閱讀器使用者瞭解該按鈕的用途。[瞭解詳情](https://web.dev/input-image-alt/)。"}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">` 元素未設定 `[alt]` 文字"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">` 元素具有 `[alt]` 文字"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "標籤可以確保輔助技術 (例如螢幕閱讀器) 正確朗讀表格控制項。[瞭解詳情](https://web.dev/label/)。"}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "表格元素沒有相關聯的標籤"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "表格元素具有相關聯的標籤"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "用於版面配置的表格不應包含資料元素 (例如 th、字幕元素或摘要屬性)，因為這些元素可能會對螢幕閱讀器使用者造成混淆。[瞭解詳情](https://web.dev/layout-table/)。"}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "展示性元素 `<table>` 使用了 `<th>`、`<caption>` 元素或 `[summary]` 屬性。"}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "展示性元素 `<table>` 並未使用`<th>`、`<caption>` `[summary]` 屬性。"}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "使用可辨別、不重複且可聚焦的連結文字 (以及連結圖片的替代文字)，有助改善螢幕閱讀器使用者的瀏覽體驗。[瞭解詳情](https://web.dev/link-name/)。"}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "連結並無可辨別的名稱"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "連結具有可辨別的名稱"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "螢幕閱讀器會以特定方式朗讀清單。確認清單採用正確的結構有助螢幕閱讀器順利輸出內容。[瞭解詳情](https://web.dev/list/)。"}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "清單中並非只包含 `<li>` 元素和指令碼支援元素 (`<script>` 和 `<template>`)。"}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "清單只包含 `<li>` 元素和支援指令碼的元素 (`<script>` 和 `<template>`)。"}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "清單項目 `<li>` 必須包含在父元素 `<ul>` 或 `<ol>` 中，螢幕閱讀器才能正確朗讀這些項目。[瞭解詳情](https://web.dev/listitem/)。"}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "清單項目 (`<li>`) 未包含在 `<ul>` 或 `<ol>` 父元素中。"}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "清單項目 (`<li>`) 已包含在 `<ul>` 或 `<ol>` 父元素中。"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "使用者不會預期系統自動重新整理網頁，且執行此操作會將焦點移回網頁頂端。這可能會對使用者造成困擾或混淆。[瞭解詳情](https://web.dev/meta-refresh/)。"}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "文件使用 `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "文件未使用 `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "停用縮放功能會對低視力人士造成困擾，他們需要透過螢幕放大功能才能清楚看見網頁內容。[瞭解詳情](https://web.dev/meta-viewport/)。"}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`<meta name=\"viewport\">` 元素中使用了 `[user-scalable=\"no\"]`，或 `[maximum-scale]` 屬性少於 5。"}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "`<meta name=\"viewport\">` 元素中未有使用 `[user-scalable=\"no\"]` 元素，而且 `[maximum-scale]` 屬性少於 5。"}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "螢幕閱讀器無法翻譯非文字內容。為 `<object>` 元素新增替代文字，可協助螢幕閱讀器向使用者傳達其意義。[瞭解詳情](https://web.dev/object-alt/)。"}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>` 元素未設定 `[alt]` 文字"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "`<object>` 元素具有 `[alt]` 文字"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "如果值大於 0，表示採用的是明確的瀏覽排序。雖然此做法在技術上可行，但通常會對依賴輔助技術的使用者造成困擾。[瞭解詳情](https://web.dev/tabindex/)。"}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "部分元素的 `[tabindex]` 值大於 0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "所有元素的 `[tabindex]` 值皆未超過 0"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "螢幕閱讀器的功能可讓使用者更輕鬆瀏覽表格。如能確保採用 `[headers]` 屬性的 `<td>` 儲存格只參照同一表格中的其他儲存格，或許能改善螢幕閱讀器的使用體驗。[瞭解詳情](https://web.dev/td-headers-attr/)。"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "`<table>` 元素中採用 `[headers]` 屬性的儲存格參照了 `id`，無法在同一表格中找到此元素。"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "`<table>` 元素中採用 `[headers]` 屬性的儲存格，參照了同一表格中的其他儲存格。"}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "螢幕閱讀器的功能可讓使用者更輕鬆瀏覽表格。如能確保表格標題一律參照特定一組儲存格，或許能有助改善螢幕閱讀器使用者的體驗。[瞭解詳情](https://web.dev/th-has-data-cells/)。"}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "`<th>` 元素及帶有 `[role=\"columnheader\"/\"rowheader\"]` 的元素沒有所描述的資料儲存格。"}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "`<th>` 元素和帶有 `[role=\"columnheader\"/\"rowheader\"]` 的元素有其描述的資料儲存格。"}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "指定元素有效的[[BCP 47 語言](https://www.w3.org/International/questions/qa-choosing-language-tags#question)] 可協助螢幕閱讀器正確朗讀文字。[瞭解詳情](https://web.dev/valid-lang/)。"}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "`[lang]` 屬性並無有效的值"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "`[lang]` 屬性具備有效的值"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "如果在影片中提供字幕，將有助於失聰或聽障使用者取得影片資料。[瞭解詳情](https://web.dev/video-caption/)。"}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "`<video>` 元素不含任何帶有 `[kind=\"captions\"]``<track>` 的元素"}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "`<video>` 元素包含帶有 `[kind=\"captions\"]` 的`<track>` 元素"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "音訊說明可為影片提供對話功能無法提供的相關資料，例如面部表情和場景。[瞭解詳情](https://web.dev/video-description/)。"}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "`<video>` 元素不含任何帶有 `[kind=\"description\"]``<track>` 的元素"}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "`<video>` 元素包含帶有 `[kind=\"description\"]` 的`<track>` 元素"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "請定義 `apple-touch-icon`。這樣，當 iOS 使用者將漸進式網絡應用程式新增到主畫面時，系統才會顯示正確圖示。apple-touch-icon 必須指向大小為 192 像素 (或 180 像素) 的不透明正方形 PNG。[瞭解詳情](https://web.dev/apple-touch-icon/)。"}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "未提供有效的 `apple-touch-icon`"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "`apple-touch-icon-precomposed` 版本過舊，建議使用 `apple-touch-icon`。"}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "提供有效的 `apple-touch-icon`"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chrome 擴充程式會對此頁面的載入效能產生負面影響。建議透過無痕模式或使用未安裝擴充程式的 Chrome 設定檔來審核頁面。"}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "指令碼評估"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "指令碼剖析"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "CPU 總執行時間"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "建議減少剖析、編譯和執行 JS 所用的時間。傳送較小的 JS 負載可能有所幫助。[瞭解詳情](https://web.dev/bootup-time)。"}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "縮短 JavaScript 執行時間"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "JavaScript 執行時間"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "大型 GIF 放送動畫內容效率往往不佳。建議改用 MPEG4/WebM 格式的動畫影片和 PNG/WebP 格式的靜態圖片，以節省網絡位元組。[瞭解詳情](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "使用影片格式的動畫內容"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "建議延遲載入螢幕外的項目並隱藏圖片，直到重要資源全部載入後再開始操作，以縮短可互動時間。[瞭解詳情](https://web.dev/offscreen-images)。"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "延遲載入螢幕外圖片"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "過多資源往往會阻止系統首次繪製頁面。建議內嵌重要的 JS/CSS，延遲所有不重要的 JS/樣式。[瞭解詳情](https://web.dev/render-blocking-resources)。"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "排除阻止呈現的資源"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "網絡負載過大會造成使用者的費用負擔，且往往與過長載入時間息息相關。[瞭解詳情](https://web.dev/total-byte-weight)。"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "總大小為 {totalBytes, number, bytes} KB"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "避免網絡負載過大"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "避免網絡負載過大"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "壓縮 CSS 檔案可減少網絡負載大小。[瞭解詳情](https://web.dev/unminified-css)。"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "壓縮 CSS"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "壓縮 JavaScript 檔案可減少負載大小和指令碼剖析時間。[瞭解詳情](https://web.dev/unminified-javascript)。"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "壓縮 Javascript"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "從樣式表中移除無用的規則，並延遲載在毋需捲動的當眼位置內容中未使用的 CSS，減少網絡活動耗用不必要的位元組。[瞭解詳情](https://web.dev/unused-css-rules)。"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "移除未使用的 CSS"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "移除未使用的 JavaScript，以減少網絡活動耗用的位元組。"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "移除未使用的 JavaScript"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "延長快取期限可加快重覆瀏覽頁面的速度。[瞭解詳情](https://web.dev/uses-long-cache-ttl)。"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{已找到 1 項資源}other{已找到 # 項資源}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "採用有效的快取政策提供靜態資產"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "使用有效的快取政策處理靜態資產"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "優化圖片以加快載入速度，減少流動數據用量。[瞭解詳情](https://web.dev/uses-optimized-images)。"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "有效地進行圖片編碼"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "提供適當大小的圖片有助節省流動數據用量，並縮短載入時間。[瞭解詳情](https://web.dev/uses-responsive-images)。"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "適當調整圖片大小"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "文字資源應經過 (gzip、deflate 或 brotli) 壓縮，以將網絡位元總數減至最少。[瞭解詳情](https://web.dev/uses-text-compression)。"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "啟用文字壓縮"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "JPEG 2000、JPEG XR 和 WebP 等圖片格式通常比 PNG 或 JPEG 有更好的壓縮效果，能夠更快完成下載及減少數據用量。[瞭解詳情](https://web.dev/uses-webp-images)。"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "提供 next-gen 格式的圖片"}, "lighthouse-core/audits/content-width.js | description": {"message": "如果應用程式內容寬度與檢視區的寬度不相符，應用程式可能無法在流動裝置螢幕上呈現優化效果。[瞭解詳情](https://web.dev/content-width)。"}, "lighthouse-core/audits/content-width.js | explanation": {"message": "檢視區大小 ({innerWidth} 像素) 與視窗大小 ({outerWidth} 像素) 不相符。"}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "尚未為檢視區正確調整內容大小"}, "lighthouse-core/audits/content-width.js | title": {"message": "已將檢視區正確調整內容大小"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "下方的「關鍵要求鏈結」顯示以高優先次序發佈的資源。為了提高頁面載入速度，建議您縮短鏈結長度，縮減下載資源的大小，或延遲下載不必要資源。[瞭解詳情](https://web.dev/critical-request-chains)。"}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{已找到 1 個鏈結}other{已找到 # 個鏈結}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "將重要要求深度降至最低"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "淘汰/警告"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "行數"}, "lighthouse-core/audits/deprecations.js | description": {"message": "系統最終會從瀏覽器中移除已淘汰的 API。[瞭解詳情](https://web.dev/deprecations)。"}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{發現 1 個警告}other{發現 # 個警告}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "使用已淘汰的 API"}, "lighthouse-core/audits/deprecations.js | title": {"message": "避免使用已淘汰的 API"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "應用程式快取已被淘汰。[瞭解詳情](https://web.dev/appcache-manifest)。"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "已找到「{AppCacheManifest}」"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "使用應用程式快取"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "避免使用應用程式快取"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "指定 DOCTYPE 能防止瀏覽器切換至怪異模式。[瞭解詳情](https://web.dev/doctype)。"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "DOCTYPE 名稱必須是小寫字串 `html`"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "文件必須包含 doctype"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "publicId 必須為空白字串"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "systemId 必須為空白字串"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "網頁缺少 HTML DOCTYPE，因此觸發了怪異模式"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "網頁含有 HTML DOCTYPE"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "元素"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "統計資料"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "值"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "瀏覽器工程師建議網頁包含的 DOM 元素數量應少於 1,500 個左右。理想的樹狀結構深度包含少於 32 個元素，且子/父元素少於 60 個。大型 DOM 會增加記憶體用量、延長[樣式運算](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations)的時間，並產生高昂的[版面配置重排](https://developers.google.com/speed/articles/reflow)。[瞭解詳情](https://web.dev/dom-size)。"}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 個元素}other{# 個元素}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "避免 DOM 過大"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "DOM 深度上限"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "DOM 元素總數"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "子元素數量上限"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "避免 DOM 過大"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "目標"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "將 `rel=\"noopener\"` 或 `rel=\"noreferrer\"` 新增至所有外部連結，可提升效能並防範安全漏洞。[瞭解詳情](https://web.dev/external-anchors-use-rel-noopener)。"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "跨原始來源目的地的連結不安全"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "跨來源目的地的連結安全"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "無法判斷錨點的目的地 ({anchorHTML})。如果 target=_blank 不是用作連結，請考慮移除。"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "如果未提供其他資訊就要求存取使用者的位置資訊，會讓使用者感到困惑而不信任網站。建議您在使用者執行特定動作時，再提出這項要求。[瞭解詳情](https://web.dev/geolocation-on-start)。"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "在載入網頁時要求存取使用者的地理位置"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "避免在載入網頁時要求存取使用者的地理位置"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "版本"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "在此網頁上偵測到的所有前端 JavaScript 程式庫。[瞭解詳情](https://web.dev/js-libraries)。"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "偵測到的 JavaScript 媒體庫"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "對於連線速度較慢的使用者，透過 `document.write()` 動態插入的外部指令碼可能會導致網頁延遲載入數十秒。[瞭解詳情](https://web.dev/no-document-write)。"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "使用 `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "避免使用 `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "嚴重程度"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "程式庫版本"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "安全漏洞數量"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "部分第三方指令碼可能包含已知的安全漏洞，攻擊者將能輕易識別及利用這些漏洞。[瞭解詳情](https://web.dev/no-vulnerable-libraries)。"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{偵測到 1 個安全漏洞}other{偵測到 # 個安全漏洞}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "包括前端 JavaScript 程式庫具有已知安全漏洞"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "高"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "低"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "中"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "避免使用包含已知安全漏洞的前端 JavaScript 程式庫"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "如果未提供其他資訊就要求使用者允許網站顯示通知，會讓使用者感到困惑而不信任網站。建議您在使用者操作特定手勢時，再提出這項要求。[瞭解詳情](https://web.dev/notification-on-start)。"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "在載入網頁時要求使用者允許網站顯示通知"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "避免在載入網頁時要求使用者允許網站顯示通知"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "審核失敗的元素"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "禁止貼上密碼會對安全政策造成不良影響。[瞭解詳情](https://web.dev/password-inputs-can-be-pasted-into)。"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "禁止使用者貼到密碼欄位"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "允許使用者貼到密碼欄位"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "通訊協定"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 具備很多 HTTP/1.1 沒有的優點，包括二進制標題、多工處理和伺服器推送。[瞭解詳情](https://web.dev/uses-http2)。"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{有 1 個要求未透過 HTTP/2 傳送}other{有 # 個要求未透過 HTTP/2 傳送}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "網頁要求的部分資源未使用 HTTP/2"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "為本身的資源使用 HTTP/2"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "建議將輕觸動作和滑鼠滾輪活動監聽器標示為 `passive`，以提升網頁的捲動效能。[瞭解詳情](https://web.dev/uses-passive-event-listeners)。"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "未使用被動事件監聽器來提升捲動效能"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "使用被動活動監聽器來提升捲動效能"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "說明"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "如果管理中心有錯誤記錄，表示系統仍有問題尚待解決，例如網絡要求錯誤和其他瀏覽器問題。[瞭解詳情](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "瀏覽器錯誤已記錄在控制台"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "系統未在管理中心記錄瀏覽器發生的錯誤"}, "lighthouse-core/audits/font-display.js | description": {"message": "運用顯示字型的 CSS 功能，確保使用者可在網頁字型載入時看到文字。[瞭解詳情](https://web.dev/font-display)。"}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "確保文字在網頁字型載入時仍然顯示"}, "lighthouse-core/audits/font-display.js | title": {"message": "在網頁字型載入時，所有文字仍然顯示"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "Lighthouse 無法自動檢查以下網址顯示字型的值：{fontURL}。"}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "實際的圖片長寬比"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "網頁上顯示的圖片長寬比"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "圖片顯示尺寸應符合正常顯示長寬比。[瞭解詳情](https://web.dev/image-aspect-ratio)。"}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "顯示的圖片長寬比不正確"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "顯示的圖片長寬比正確"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "圖片大小資料無效 {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "瀏覽器可主動提示使用者，建議他們將您的應用程式新增至主畫面，藉此提高參與度[瞭解詳情](https://web.dev/installable-manifest)。"}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "網絡應用程式資訊清單不符合可安裝性要求"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "網絡應用程式資訊清單符合可安裝性要求"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "不安全的網址"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "所有網站都應該使用 HTTPS 確保安全，即使網站不處理敏感資料亦然。HTTPS 能防範入侵者竄改或被動監聽應用程式與使用者之間的通訊，且 HTTP/2 和很多新的網絡平台 API 都要求使用 HTTPS。[瞭解詳情](https://web.dev/is-on-https)。"}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{發現 1 個不安全的要求}other{發現 # 個不安全的要求}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "未使用 HTTPS"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "使用 HTTPS"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "如果網頁在流動網絡中能快速載入，可為流動裝置使用者提供良好的使用體驗。[瞭解詳情](https://web.dev/load-fast-enough-for-pwa)。"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "在 {timeInMs, number, seconds} 秒開始互動"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "在 {timeInMs, number, seconds} 秒時於模擬流動網絡上開始互動"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "您的網頁載入速度太慢，無法在 10 秒內與使用者互動。請參閱「效能」部分的優化建議和診斷，瞭解如何改善載入速度。"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "網頁在流動網絡中的載入速度不夠快"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "網頁在流動網絡中的載入速度夠快"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "類別"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "建議減少剖析、編譯和執行 JS 所用的時間。傳送較小的 JS 負載可能有所幫助。[瞭解詳情](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "將主要執行緒的工作減至最少"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "將主要執行緒的工作減至最少"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "為盡量觸及最多使用者，網站應能在每個主要瀏覽器上暢順運作。[瞭解詳情](https://web.dev/pwa-cross-browser)。"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "網站可以在不同瀏覽器上運作"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "確保每個網頁都可透過網址進行深層連結，且具有專屬網址，方便您在社交媒體上分享。[瞭解詳情](https://web.dev/pwa-each-page-has-url)。"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "每個網頁都有一個網址"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "即使網絡速度緩慢，只要使用者瀏覽不同網頁也能營造流暢切換的感覺。這就是為使用者帶來高效感觀的關鍵。[瞭解詳情](https://web.dev/pwa-page-transitions)。"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "使用者在切換頁面時不會覺得網絡速度緩慢"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "預計輸入延遲時間是在頁面載入視窗最忙碌的 5 秒期間，您的應用程式對使用者輸入動作的預計回應時間 (以毫秒為單位)。如果延遲超過 50 毫秒，使用者可能會認為應用程式的速度緩慢。[瞭解詳情](https://web.dev/estimated-input-latency)。"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "預計輸入延遲時間"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "「首次內容繪製時間」標示繪製首個文字/首張圖片的時間。[瞭解詳情](https://web.dev/first-contentful-paint)。"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "首次內容繪製時間"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "「首次 CPU 閒置時間」標示頁面的主要執行緒首次有空處理輸入的時間。[瞭解詳情](https://web.dev/first-cpu-idle)。"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "首次 CPU 閒置時間"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "「首次有效繪製時間」評估頁面主要內容顯示的時間。[瞭解詳情](https://web.dev/first-meaningful-paint)。"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "首次有效繪製時間"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "互動準備時間是網頁進入完整互動狀態前所花的時間。[瞭解詳情](https://web.dev/interactive)。"}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "可互動所需時間"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "使用者可以體驗到最長的「首次輸入延遲時間」就是最長的工作持續時間 (以毫秒為單位)。[瞭解詳情](https://developers.google.com/web/updates/2018/05/first-input-delay)。"}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "首次輸入延遲時間最長預計值"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "「速度指數」會顯示頁面內容的展現速度。[瞭解詳情](https://web.dev/speed-index)。"}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "速度指數"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "當工作長度超過 50 毫秒時，所有 FCP 和「可互動所需時間」之間的時長總和 (以毫秒為單位)。"}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "總封鎖時間"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "網絡來回通訊時間 (RTT) 對效能有很大影響。如果系統傳送到某個來源的來回通訊時間很高，表示靠近使用者端的伺服器可改善效能。[瞭解詳情](https://hpbn.co/primer-on-latency-and-bandwidth/)。"}, "lighthouse-core/audits/network-rtt.js | title": {"message": "網絡來回通訊時間"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "伺服器延遲時間可能會影響網頁效能。如果來源端的伺服器延遲時間高，代表伺服器已超載或後端效能欠佳。[瞭解詳情](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)。"}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "伺服器後端延遲時間"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "Service Worker 可確保您的網絡應用程式運作可靠，即使網絡連線不穩定亦然。[瞭解詳情](https://web.dev/offline-start-url)。"}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url` 在離線時不會傳回狀態碼 200 的回應"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url` 在離線時會傳回狀態碼 200 的回應"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "Lighthouse 無法從資訊清單中讀取 `start_url`，因此將 `start_url` 假設為文件的網址。錯誤訊息：「{manifestWarning}」。"}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "超出預算"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "讓網絡要求的數量和大小低於使用者根據效能預算所設定的目標。[瞭解詳情](https://developers.google.com/web/tools/lighthouse/audits/budgets)。"}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 個要求}other{# 個要求}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "效能預算"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "如果您已設定 HTTPS，請確認是否已將所有 HTTP 流量重新導向至 HTTPS，以便為所有使用者提供安全的網絡功能。[瞭解詳情](https://web.dev/redirects-http)。"}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "尚未將 HTTP 流量重新導向至 HTTPS"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "將 HTTP 流量重新導向至 HTTPS"}, "lighthouse-core/audits/redirects.js | description": {"message": "重新導向會導致頁面延遲載入。[瞭解詳情](https://web.dev/redirects)。"}, "lighthouse-core/audits/redirects.js | title": {"message": "避免多次頁面重新導向"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "如要設定網頁資源的數量和大小的預算，請新增 budget.json 檔案。[瞭解詳情](https://developers.google.com/web/tools/lighthouse/audits/budgets)。"}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 個要求 • {byteCount, number, bytes} KB}other{# 個要求 • {byteCount, number, bytes} KB}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "降低要求數量並減少傳輸大小"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "標準連結會建議要在搜尋結果中顯示哪個網址。[瞭解詳情](https://web.dev/canonical)。"}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "多個互相衝突的網址 ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "指向其他網域 ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "網址無效 ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "指向其他 `hreflang` 位置 ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "相對網址 ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "指向目標為網域的根網址 (首頁)，而非相應的內容網頁"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "文件並無有效的 `rel=canonical`"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "文件具備有效的 `rel=canonical`"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "如果字型小於 12 像素，文字會太小而難以辨識，流動裝置訪客需要「兩指縮放」才能閱讀內容。網頁中應有超過 60% 採用 12 像素或以上大小的文字。[瞭解詳情](https://web.dev/font-size)。"}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} 的文字清晰可讀"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "由於網頁沒有為流動裝置螢幕設定合適的檢視區中繼標記，因文字難以辨識。"}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "{decimalProportion, number, extendedPercent} 的文字過小 (以 {decimalProportionVisited, number, extendedPercent} 範例為準)。"}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "文件使用的字型大小難以辨識"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "文件使用的字型大小清晰可讀"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "hreflang 連結會通知搜尋引擎，應在特定語言或區域的搜尋結果中該顯示哪個版本的網頁。[瞭解詳情](https://web.dev/hreflang)。"}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "文件並無有效的 `hreflang`"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "文件具備有效的 `hreflang`"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "如果網頁傳回失敗的 HTTP 狀態碼，可能無法正確加入索引。[瞭解詳情](https://web.dev/http-status-code)。"}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "網頁傳回失敗的 HTTP 狀態碼"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "網頁傳回成功的 HTTP 狀態碼"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "如果搜尋引擎沒有檢索網頁的權限，將無法在搜尋結果中顯示您的網頁。[瞭解詳情](https://web.dev/is-crawable)。"}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "網頁的索引功能被封鎖"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "網頁的索引功能未被封鎖"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "連結說明文字可協助搜尋引擎瞭解您的內容。[瞭解詳情](https://web.dev/link-text)。"}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{找到 1 個連結}other{找到 # 個連結}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "連結並無說明文字"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "連結具有說明文字"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "執行[結構化資料測試工具](https://search.google.com/structured-data/testing-tool/)和[結構化資料 Linter](http://linter.structured-data.org/) 來驗證結構化資料。[瞭解詳情](https://web.dev/structured-data)。"}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "結構化資料有效"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "您可在搜尋結果中加入中繼說明，簡要描述網頁內容。[瞭解詳情](https://web.dev/meta-description)。"}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "沒有說明文字。"}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "文件並無中繼說明"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "文件具有中繼說明"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "搜尋引擎無法為外掛程式內容加入索引，而且很多裝置對外掛程式都設有限制甚至不提供支援。[瞭解詳情](https://web.dev/plugins)。"}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "文件使用外掛程式"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "文件避免使用外掛程式"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "如果您的 robots.txt 檔案格式錯誤，檢索器可能無法瞭解您偏好的網站檢索或加入索引方式。[瞭解詳情](https://web.dev/robots-txt)。"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "robots.txt 要求傳回以下 HTTP 狀態：{statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{找到 1 個錯誤}other{找到 # 個錯誤}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse 無法下載 robots.txt 檔"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt 無效"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "robots.txt 有效"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "按鈕和連結等互動元素的大小應至少有 48x48 像素，且周圍應保留足夠空間以便使用者輕按，同時避免與其他元素重疊的情況。[瞭解詳情](https://web.dev/tap-targets)。"}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} 的輕按目標大小適中"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "由於網頁沒有為流動裝置螢幕設定合適的檢視區中繼標記，因此輕按目標太小"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "輕按目標未設定成適當大小"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "重疊的目標"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "輕按目標"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "輕按目標已設定成適當大小"}, "lighthouse-core/audits/service-worker.js | description": {"message": "Service Worker 技術可讓您的應用程式使用多項漸進式網絡應用程式的功能，例如離線存取、新增到主畫面和推送通知。[瞭解詳情](https://web.dev/service-worker)。"}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "此網頁由 Service Worker 控制，但系統無法將資訊清單剖析為有效的 JSON，因此找不到任何 `start_url`"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "此網頁由 Service Worker 控制，但 `start_url` ({startUrl}) 不在 Service Worker 的範圍內 ({scopeUrl})"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "此網頁由 Service Worker 控制，但系統未能擷取任何資訊清單，因此找不到任何 `start_url`。"}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "此來源包含一個或多個 Service Worker，但該頁面 ({pageUrl}) 不在 Service Worker 的範圍內。"}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "未註冊可控制網頁和 `start_url` 的 Service Worker"}, "lighthouse-core/audits/service-worker.js | title": {"message": "已註冊可控制網頁和 `start_url` 的 Service Worker"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "透過設定啟動畫面的主題，可確保使用者從主畫面啟動您的應用程式時享有優質體驗。[瞭解詳情](https://web.dev/splash-screen)。"}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "未設定自訂啟動畫面"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "設有自訂啟動畫面"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "您可以將瀏覽器網址列的主題設定為與網站相符。[瞭解詳情](https://web.dev/themed-omnibox)。"}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "未設定網址列的主題顏色。"}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "設定網址列的主題顏色。"}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "主要執行緒封鎖時間"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "第三方"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "第三方程式碼可能會嚴重影響載入效能。請盡量減少不必要的第三方供應商，並在網頁的主要內容載入完成後，再載入第三方程式碼。[瞭解詳情](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)。"}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "第三方程式碼將主要執行緒封鎖了 {timeInMs, number, milliseconds} 毫秒"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "減低第三方程式碼的影響"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "第三方程式碼使用情況"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "「首個字節時間」會指出您的伺服器傳送回應的時間。[瞭解詳情](https://web.dev/time-to-first-byte)。"}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "根文件回應時間為 {timeInMs, number, milliseconds} 毫秒"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "縮短伺服器回應時間 (TTFB)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "伺服器回應時間短 (TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "時間長度"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "開始時間"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "類別"}, "lighthouse-core/audits/user-timings.js | description": {"message": "建議使用「用戶使用時間」檢測您的應用程式，評估其在關鍵使用者體驗期間的實際成效。[瞭解詳情](https://web.dev/user-timings)。"}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 個用戶使用時間標記}other{# 個用戶使用時間標記}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "用戶使用時間標記和測量結果"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "「{security<PERSON><PERSON><PERSON>}」有預先連線的 <link>，但瀏覽器沒有使用。請檢查您使用 `crossorigin` 屬性的方式是否正確。"}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "建議您新增 `preconnect` 或 `dns-prefetch` 資源提示，及早連線至重要的第三方來源。[瞭解詳情](https://web.dev/uses-rel-preconnect)。"}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "預先連接至必要來源"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "{preloadURL} 有預先載入的 <link>，但瀏覽器沒有使用。請檢查您使用 `crossorigin` 屬性的方式是否正確。"}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "建議使用 `<link rel=preload>` 來指定優先需要的網絡要求，並預先擷取資源。[瞭解詳情](https://web.dev/uses-rel-preload)。"}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "預先載入關鍵要求"}, "lighthouse-core/audits/viewport.js | description": {"message": "新增 `<meta name=\"viewport\">` 標籤以對流動裝置螢幕優化您的應用程式。[瞭解詳情](https://web.dev/viewport)。"}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "找不到任何 `<meta name=\"viewport\">` 標籤"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "缺少包括 `width` 或 `initial-scale` 的 `<meta name=\"viewport\">` 標籤"}, "lighthouse-core/audits/viewport.js | title": {"message": "具備包括 `<meta name=\"viewport\">` 或 `width` 的 `initial-scale` 標籤"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "當 JavaScript 停用時，您的應用程式應該要顯示一些內容，即使只是向使用者顯示「必須啟用 JavaScript 才能使用此應用程式」這類警告訊息亦可。[瞭解詳情](https://web.dev/without-javascript)。"}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "即使網頁的指令碼無法使用，網頁內文仍應顯示一些內容。"}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "當 JavaScript 無法使用時不提供備用內容"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "當 JavaScript 無法使用時包含一些內容"}, "lighthouse-core/audits/works-offline.js | description": {"message": "如果您正在建立漸進式網絡應用程式，請考慮使用 Service Worker，以便您的應用程式離線運作。[瞭解詳情](https://web.dev/works-offline)。"}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "目前的網頁在離線時不會傳回狀態碼 200 的回應"}, "lighthouse-core/audits/works-offline.js | title": {"message": "目前的網頁在離線時會傳回狀態碼 200 的回應"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "由於您的測試網址 ({requested}) 已重新導向至「{final}」，因此此頁面可能無法在離線時載入。請試著直接測試第二個網址。"}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "這些提示可協助改善 ARIA 在應用程式中的使用情況，進而提升輔助技術 (例如螢幕閱讀器) 使用者的體驗。"}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "這些審核結果建議您為音訊和影片提供替代內容。這或許能改善聽障或視障人士的使用體驗。"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "音訊和影片"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "這些審核項目會提供常見的無障礙功能最佳做法。"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "最佳做法"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "這些檢查會提供[網頁應用程式無障礙功能的改善建議](https://developers.google.com/web/fundamentals/accessibility)。系統只能自動偵測一部分的無障礙功能問題，因此建議您另行手動測試。"}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "這些審核項目會檢查自動化測試工具未涵蓋的區域。詳情請參閱[無障礙功能審查的執行指南](https://developers.google.com/web/fundamentals/accessibility/how-to-review)。"}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "無障礙功能"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "這些提示有助提高內容的易讀性。"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "對比"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "您可以根據這些提示作出改善，讓其他地區的使用者更容易理解您的內容。"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "國際化和本地化"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "這些提示可協助提高應用程式中的控制項語義品質。這或許能改善輔助技術 (例如螢幕閱讀器) 的使用體驗。"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "名稱和標籤"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "這些審核結果可協助提高應用程式中的鍵盤瀏覽體驗。"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "導覽"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "這些審核結果建議您運用輔助技術 (例如螢幕閱讀器)，改善表格或清單資料的閱讀體驗。"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "表格和清單"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "最佳做法"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "您可根據效能預算設定網站效能的標準。"}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "預算"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "進一步瞭解應用程式效能。這些數字不會[直接影響](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)「效能」分數。"}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "診斷"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "評估效能的最重要指標在於像素在畫面上的呈現速度。關鍵數據：「首次內容繪製時間」、「首次有效繪製時間」"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "首次繪製改進"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "這些建議可加快網頁載入速度，但不會[直接影響](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)「效能」分數。"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "優化建議"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "數據"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "改善整體載入體驗，令網頁反應更靈敏快捷，盡快可供用戶使用。關鍵數據：可互動時間、速度指數"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "整體改進"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "效能"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "這些檢查項目可驗證漸進式網絡應用程式的不同層面。[瞭解詳情](https://developers.google.com/web/progressive-web-apps/checklist)。"}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "這些是基本 [PWA 檢查清單](https://developers.google.com/web/progressive-web-apps/checklist)規定的檢查項目，但 Lighthouse 不會自動檢查這些項目。它們不會影響您的分數，但請務必手動驗證這些項目。"}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "漸進式網絡應用程式"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "運作快速，效能可靠"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "可安裝"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "已優化 PWA"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "這些檢查可確保網頁採用已優化的搜尋引擎結果排名設定。部分其他因素不在 Lighthouse 檢查範圍內，仍可能會影響您的搜尋排名。[瞭解詳情](https://support.google.com/webmasters/answer/35769)。"}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "在您的網站上執行這些額外的驗證工具，以檢查其他 SEO 最佳做法。"}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "搜尋引擎優化 (SEO)"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "請設定您的 HTML 格式，讓檢索器更能瞭解應用程式內容。"}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "關於內容的最佳做法"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "檢索器需要存取您的應用程式，網站才會在搜尋結果中顯示。"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "檢索和加入索引"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "請確保您的頁面適合透過流動裝置瀏覽，讓使用者無需兩指縮放或放大螢幕即可閱讀網頁內容。[瞭解詳情](https://developers.google.com/search/mobile-sites/)。"}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "適合透過流動裝置瀏覽"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "快取 TTL"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "位置"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "名稱"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "要求"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "資源類型"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "大小"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "所用的時間"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "傳輸大小"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "網址"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "可節省的數據用量"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "可節省的數據用量"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "可節省 {wastedBytes, number, bytes} KB"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "可減少 {wastedMs, number, milliseconds} 毫秒"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "文件"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "字型"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "圖片"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "媒體"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} 毫秒"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "其他"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "指令碼"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} 秒"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "樣式表"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "第三方"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "總計"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "追蹤記錄網頁載入情況時發生錯誤。請重新執行 Lighthouse。({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "等待「Debugger 通訊協定」初始連線時逾時。"}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome 在網頁載入期間未能收集任何螢幕擷取畫面。請確認網頁上有可見內容，然後嘗試重新執行 Lighthouse。({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "DNS 伺服器無法解析您提供的網域。"}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "必要的 {artifactName} 收集程式發生錯誤：{errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "Chrome 發生內部錯誤。請重新啟動 Chrome，並嘗試重新執行 Lighthouse。"}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "未執行必要的 {artifactName} 收集程式。"}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse 無法穩定載入您要求的網頁。請確認您測試的網址是否正確，以及伺服器是否正確回應所有要求。"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "您要求的網頁已停止回應，因此 Lighthouse 無法穩定載入該網址。"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "您提供的網址並無有效的安全憑證。{securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome 使用插頁式畫面阻止系統載入網頁。請確認您測試的網址是否正確，以及伺服器是否正確回應所有要求。"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse 無法穩定載入您要求的網頁。請確認您測試的網址是否正確，以及伺服器是否正確回應所有要求。(詳情：{errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse 無法穩定載入您要求的網頁。請確認您測試的網址是否正確，以及伺服器是否正確回應所有要求。(狀態代碼：{statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "網頁的載入時間過長。請按照報告中的建議縮短網頁的載入時間，並嘗試重新啟動 Lighthouse。({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "等待 DevTools 通訊協定回應的時間超出系統分配上限。(方法：{protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "擷取資源內容的時間超出系統分配上限。"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "您提供的網址無效。"}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "顯示審核結果"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "初始導覽"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "關鍵路徑延遲時間上限："}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "發生錯誤！"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "報告錯誤：無審核資料"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "實驗室數據"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) 在模擬流動網絡上對目前網頁進行的分析。此為預計值，可能與實際值有所不同。"}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "其他手動檢查項目"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "不適用"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "優化建議"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "預計節省的時間"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "已通過的審核"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "收合片段"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "展開片段"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "顯示第三方資源"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "導致這次 Lighthouse 無法順利執行的問題："}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "此為預計值，可能與實際值有所不同。系統[只會根據這些數據](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)計算效能分數。"}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "經過審核，但附有警告訊息"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "警告： "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "建議將 GIF 上載至可將 GIF 用作 HTML5 影片嵌入的伺服器。"}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "安裝可延遲載入所有畫面外圖片的[延遲載入 WordPress 外掛程式](https://wordpress.org/plugins/search/lazy+load/)，或改用提供此功能的主題。您亦可考慮使用 [AMP 外掛程式](https://wordpress.org/plugins/amp/)。"}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "部分 WordPress 外掛程式可助您[內嵌重要資產](https://wordpress.org/plugins/search/critical+css/)或[延後載入較不重要的資源](https://wordpress.org/plugins/search/defer+css+javascript/)。請注意，這些外掛程式的優化設定可能影響主題或外掛程式的功能，因此您可能需要變更程式碼。"}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "主題、外掛程式和伺服器規格均會影響伺服器回應時間。建議尋找更優化的主題、謹慎選擇優化外掛程式和/或升級伺服器。"}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "建議在文章清單中顯示摘錄 (例如加入更多標籤)、減少特定頁面顯示的文章數量、將較長的文章分為多個頁面，或使用可延遲載入留言的外掛程式。"}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "部分 [WordPress 外掛程式](https://wordpress.org/plugins/search/minify+css/) 可以透過串連、縮小及壓縮樣式來提升網站速度。可以的話，您亦可建立流程來直接進行此壓縮操作。"}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "部分 [WordPress 外掛程式](https://wordpress.org/plugins/search/minify+javascript/) 可透過串連、縮小及壓縮指令碼來提升網站速度。可以的話，您亦可建立流程來直接進行此壓縮操作。"}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "部分 [WordPress 外掛程式](https://wordpress.org/plugins/)會在網頁中載入未使用的 CSS，建議你減少這類外掛程式的數量，或改用其他外掛程式。如要找出會新增多餘 CSS 的外掛程式，請嘗試在 Chrome DevTools 中執[程式碼覆蓋率](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)。您可透過樣式表網址找出有問題的主題/外掛程式。請留意清單中包含很多樣式表，且程式碼覆蓋率中有大量紅色標示的外掛程式。外掛程式應只將網頁上實際使用的樣式表加入清單。"}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "部分 [WordPress 外掛程式](https://wordpress.org/plugins/)會在網頁中載入未使用的 JavaScript，建議你減少這類外掛程式的數量，或改用其他外掛程式。如要找出會新增多餘 JS 的外掛程式，請嘗試在 Chrome DevTools 中執[程式碼覆蓋率](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)。您可透過指令碼網址找出有問題的主題/外掛程式。請留意清單中包含很多指令碼，且程式碼覆蓋率中有大量紅色標示的外掛程式。外掛程式應只將網頁上實際使用的指令碼加入清單。"}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "瞭解 [WordPress 的瀏覽器快取功能](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)。"}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "建議使用[圖片優化 WordPress 外掛程式](https://wordpress.org/plugins/search/optimize+images/)，在壓縮圖片時保持畫質。"}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "您可直接透過[媒體庫](https://codex.wordpress.org/Media_Library_Screen)上載圖片，確保可使用所需的圖片大小，然後從媒體庫插入圖片，或使用圖片小工具來確保您使用優化的圖片大小 (包括回應式中斷點適用的圖片大小)。除非可用空間足夠，否則請避免使用「`Full Size`」圖片。[瞭解詳情](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)。"}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "您可在網絡伺服器設定中啟用文字壓縮功能。"}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "建議使用會自動將已上載圖片轉換成最佳格式的[外掛程式](https://wordpress.org/plugins/search/convert+webp/)或服務。"}}