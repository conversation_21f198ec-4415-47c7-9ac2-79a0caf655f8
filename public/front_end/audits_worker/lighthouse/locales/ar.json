{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "تتيح مفاتيح الوصول للمستخدمين التركيز بسرعة على جزء من الصفحة. للانتقال إلى الموضع الصحيح من الصفحة، يجب أن يكون كل مفتاح وصول فريدًا. [مزيد من المعلومات](https://web.dev/accesskeys/)"}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "قيم `[accesskey]` هي غير فريدة"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "قيم `[accesskey]` فريدة"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "يوفّر كل دور ARIA `role` مجموعة فرعية محددة من سمات `aria-*`. يؤدي عدم تطابق هذه الأدوار إلى إبطال السمات `aria-*`. [مزيد من المعلومات](https://web.dev/aria-allowed-attr/)"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "سمات `[aria-*]` لا تتطابق مع أدوارها"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "سمات `[aria-*]` هي مطابقة لأدوارها"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "تتطلّب بعض أدوار ARIA تزويد برامج قراءة الشاشة بسمات تصف حالة العنصر. [مزيد من المعلومات](https://web.dev/aria-required-attr/)"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]` لا تحتوي على جميع سمات`[aria-*]` المطلوبة"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]` تحتوي على جميع سمات `[aria-*]` المطلوبة"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "يجب أن تحتوي بعض أدوار ARIA الرئيسية على أدوار ثانوية محدّدة لأداء وظائف إمكانية الوصول المقصودة. [مزيد من المعلومات](https://web.dev/aria-required-children/)"}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "العناصر التي تتضمن ARIA `[role]` والتي تتطلب عناصر ثانوية للاحتواء على `[role]` محدد تفقد بعض هذه العناصر الثانوية المطلوبة أو جميعها."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "العناصر التي تتضمن ARIA `[role]` والتي تتطلب عناصر ثانوية للاحتواء على `[role]` محدد تشتمل على جميع العناصر الثانوية المطلوبة."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "يجب أن يتم تضمين بعض أدوار ثانوية ARIA ضمن أدوار رئيسية محدّدة لتنفيذ وظائف إمكانية الوصول المقصودة بشكل صحيح. [مزيد من المعلومات](https://web.dev/aria-required-parent/)"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]` غير مضمّنة في العنصر الرئيسي المطلوب"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]` مضمّنة في العنصر الرئيسي المطلوب"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "يجب أن تحتوي أدوار ARIA على قيم صالحة لتنفيذ وظائف إمكانية الوصول المقصودة. [مزيد من المعلومات](https://web.dev/aria-roles/)"}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "قيم `[role]` هي غير صالحة"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "قيم `[role]` هي صالحة"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "لا يمكن لتقنيات المساعدة، مثل برامج قراءة الشاشة، تفسير سمات ARIA باستخدام قيم غير صحيحة. [مزيد من المعلومات](https://web.dev/aria-valid-attr-value/)"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "سمات `[aria-*]` لا تحتوي على قيم صحيحة"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "سمات `[aria-*]` تحتوي على قيم صالحة"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "لا يمكن لتقنيات المساعدة، مثل برامج قراءة الشاشة، تفسير سمات ARIA بأسماء غير صحيحة. [مزيد من المعلومات](https://web.dev/aria-valid-attr/)"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "سمات `[aria-*]` هي غير صالحة أو بها أخطاء إملائية"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "سمات `[aria-*]` هي صالحة وليس بها أخطاء إملائية"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "تجعل مقاطع الشرح العناصر الصوتية قابلة للاستخدام للمستخدمين الصُم أو الذين يعانون من إعاقة سمعية، ما يوفّر معلومات مهمة، مثل الشخص المتحدث والحوار الذي يدور ومعلومات أخرى بخلاف الكلام. [مزيد من المعلومات](https://web.dev/audio-caption/)"}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "عناصر `<audio>` لا تتضمّن عنصر `<track>` مع `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "العناصر `<audio>` تحتوي على عنصر `<track>` مع `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "العناصر التي رسبت في عملية التدقيق"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "عند عدم احتواء زر على اسم يمكن الوصول إليه، تعلن برامج قراءة الشاشة بأنه \"زر\"، ما يجعله غير قابل للاستخدام بالنسبة إلى المستخدمين الذين يعتمدون على برامج قراءة الشاشة. [مزيد من المعلومات](https://web.dev/button-name/)"}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "عدم احتواء الأزرار على اسم يمكن الوصول إليه"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "احتواء الأزرار على اسم الوصول"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "تؤدي إضافة طرق لتخطي المحتوى المكرّر إلى السماح لمستخدمي لوحة المفاتيح بالتنقل في الصفحة بكفاءة أكبر. [مزيد من المعلومات](https://web.dev/bypass/)"}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "عدم احتواء الصفحة على عنوان أو رابط تخطٍ أو منطقة معالم"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "احتواء الصفحة على عنوان أو رابط تخطٍ أو منطقة معالم"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "إنّ عملية قراءة النص المنخفض التباين تُعتبر صعبة أو مستحيلة بالنسبة إلى العديد من المستخدمين. [مزيد من المعلومات](https://web.dev/color-contrast/)"}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "عد<PERSON> احتواء الخلفية وألوان الخلفية على نسبة تباين كافية"}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "تمييز الخلفية والألوان الخلفية بنسبة تباين كافية"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "عندما لا يتم ترميز قوائم التعريفات بشكل صحيح، قد تقدم برامج قراءة الشاشة نتائج غير واضحة أو غير دقيقة. [مزيد من المعلومات](https://web.dev/definition-list/)"}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>` لا تحتوي على مجموعات `<dt>` و`<dd>` المرتبة بشكلٍ صحيح فقط، أو العناصر `<script>` أو `<template>`"}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` تحتوي على مجموعات `<dt>` و`<dd>` المرتبة بشكلٍ صحيح فقط، أو العناصر `<script>` أو `<template>`."}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "يجب إدراج عناصر قائمة التعريفات (`<dt>` و`<dd>`) في عنصر رئيسي `<dl>` وذلك لضمان إعلان برامج قراءة الشاشة عنها بشكل صحيح. [مزيد من المعلومات](https://web.dev/dlitem/)"}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "عناصر قائمة التعريفات غير مضّمنة في عناصر `<dl>`"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "عناصر قائمة التعريفات مضمّنة في عناصر `<dl>`"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "يمنح العنوان مستخدمي برامج قراءة الشاشة نظرة عامة حول الصفحة، ويعتمد مستخدمو محرك البحث على هذا بشكل كبير لتحديد ما إذا كانت الصفحة ذات صلة ببحثهم أو لا. [مزيد من المعلومات](https://web.dev/document-title/)"}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "المستند لا يحتوي على عنصر `<title>`"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "المستند يحتوي على عنصر `<title>`"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "يجب أن تكون قيمة سمة رقم التعريف فريدة لكي لا تتغاضى تقنيات المساعدة عن الأمثلة الأخرى. [مزيد من المعلومات](https://web.dev/duplicate-id/)"}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "سمات `[id]` في الصفحة هي غير فريدة"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "سمات `[id]` في الصفحة هي فريدة"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "يعتمد مستخدمو برامج قراءة الشاشة على عناوين الإطارات لوصف محتوى الإطارات. [مزيد من المعلومات](https://web.dev/frame-title/)"}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "عناصر `<frame>` أو `<iframe>` لا تحتوي على عنوان"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "عناصر `<frame>` أو `<iframe>` تحتوي على عنوان"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "في حال لم تحدّد الصفحة سمة اللغة، يفترض قارئ الشاشة أن تكون الصفحة باللغة التلقائية التي اختارها المستخدم عند إعداد قارئ الشاشة. في حال لم تكن الصفحة باللغة التلقائية، قد لا يُعلِن قارئ الشاشة عن نص الصفحة بشكل صحيح. [مزيد من المعلومات](https://web.dev/html-has-lang/)"}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "العنصر `<html>` لا يحتوي على سمة `[lang]`"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "عنصر `<html>` يحتوي على سمة `[lang]`"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "يؤدي تحديد [لغة BCP 47 ](https://www.w3.org/International/questions/qa-choosing-language-tags#question) صحيحة إلى مساعدة برامج قراءة الشاشة على الإعلان عن النص بشكلٍ صحيح. [مزيد من المعلومات](https://web.dev/html-lang-valid/)"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "العنصر `<html>` لا يحتوي على قيمة صالحة للسمة `[lang]`"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "عنصر `<html>` يحتوي على قيمة صحيحة لسمة `[lang]`"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "يجب أن تهدف العناصر الإعلامية إلى نص وصفي بديل وقصير. يمكن تجاهل العناصر الزخرفية بسمة النص البديل الفارغة. [مزيد من المعلومات](https://web.dev/image-alt/)"}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "عناصر الصور لا تحتوي على سمات `[alt]`"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "عناصر الصور تحتوي على سمات `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "عند استخدام صورة كزر `<input>`، يمكن أن يساعد توفير نص بديل مستخدمي قارئ الشاشة على فهم الغرض من الزر. [مزيد من المعلومات](https://web.dev/input-image-alt/)"}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "عناصر `<input type=\"image\">` لا تحتوي على نص `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "عناصر `<input type=\"image\">` تحتوي على نص `[alt]`"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "تضمن التصنيفات الإعلان عن عناصر التحكّم بالنموذج بشكل صحيح من خلال التقنيات المساعدة، مثل برامج قراءة الشاشة. [مزيد من المعلومات](https://web.dev/label/)"}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "عدم احتواء عناصر النموذج على تصنيفات مرتبطة"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "احتواء عناصر النموذج على التصنيفات المرتبطة"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "يجب ألا يشتمل الجدول المُستخدم لأغراض التنسيق على عناصر البيانات، مثل عناصر الشرح أو سمة الملخّص، لأن ذلك يمكن أن ينشئ تجربة محيرة لمستخدمي برامج قراءة الشاشة. [مزيد من المعلومات](https://web.dev/layout-table/)."}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "لا تتجنّب عناصر `<table>` للعرض التقديمي استخدام `<th>` أو `<caption>` أو السمة `[summary]`."}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "تتجنّب عناصر `<table>` للعرض التقديمي استخدام `<th>` أو `<caption>` أو السمة `[summary]`."}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "إنّ نص الرابط (والنص البديل للصور، عند استخدامه كروابط) الذي يُعد مميّزًا وفريدًا وقابلاً للتركيز يحسّن تجربة التنقل لمستخدمي برامج قراءة الشاشة. [مزيد من المعلومات](https://web.dev/link-name/)"}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "عدم احتواء الروابط على اسم مميّز"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "احتواء الروابط على اسم مميز"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "تعتمد برامج قراءة الشاشة على طريقة محدّدة للإعلان عن القوائم. يؤدي ضمان بنية القائمة المناسبة إلى المساعدة على الاستماع إلى قارئ الشاشة. [مزيد من المعلومات](https://web.dev/list/)"}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "القوائم لا تحتوي على عناصر `<li>` وعناصر دعم النص البرمجي (`<script>` و`<template>`) فقط."}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "القوائم تحتوي على عناصر `<li>` وعناصر دعم النص البرمجي (`<script>` و`<template>`) فقط."}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "تتطلّب برامج قراءة الشاشة عناصر قائمة (`<li>`) يجب تضمينها في عنصر رئيسي `<ul>` أو `<ol>` حتى يتم الإعلان عنها بشكلٍ صحيح. [مزيد من المعلومات](https://web.dev/listitem/)"}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "عناصر القائمة (`<li>`) غير مضمّنة في العناصر الرئيسية `<ul>` أو `<ol>`"}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "عناصر القائمة (`<li>`) مُضمَّنة في العناصر الرئيسية `<ul>` أو `<ol>`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "لا يتوقع المستخدمون إعادة تحميل الصفحة تلقائيًا. وإذا تمت إعادة التحميل التلقائية، سيتحوّل تركيز المستخدمين إلى أعلى الصفحة. وقد ينشأ عن ذلك تجربة محبطة ومربكة. [مزيد من المعلومات](https://web.dev/meta-refresh/)."}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "المستند يستخدم `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "المستند لا يستخدم `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "يسبب إيقاف ميزة التكبير/التصغير مشكلة بالنسبة إلى المستخدمين ضعاف البصر الذين يعتمدون على ميزة تكبير الشاشة لرؤية محتوى صفحة الويب بشكل صحيح. [مزيد من المعلومات](https://web.dev/meta-viewport/)"}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "يتم استخدام `[user-scalable=\"no\"]` في العنصر `<meta name=\"viewport\">` أو السمة `[maximum-scale]` هي أقل من 5."}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` غير مستخدم في العنصر `<meta name=\"viewport\">` والسمة `[maximum-scale]` لا تقلّ عن 5."}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "لا يمكن لبرامج قراءة الشاشة ترجمة المحتوى غير النصي. تؤدي إضافة نص بديل إلى عناصر `<object>` إلى مساعدة برامج قراءة الشاشة على نقل المعنى إلى المستخدمين. [مزيد من المعلومات](https://web.dev/object-alt/)"}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "عناصر `<object>` لا تحتوي على نص `[alt]`"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "عناصر `<object>` تحتوي على نص `[alt]`"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "تشير القيمة الأكبر من 0 إلى وجود طلب تنقل صريح. على الرغم من أن ذلك صحيح تقنيًّا، غالبًا ما يؤدي ذلك إلى إنشاء تجارب محبطة للمستخدمين الذين يعتمدون على التقنيات المساعدة. [مزيد من المعلومات](https://web.dev/tabindex/)"}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "بعض العناصر تحتوي على قيمة `[tabindex]` أكبر من 0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "لا يتوفّر عنصر له قيمة `[tabindex]` أكبر من 0"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "تحتوي برامج قراءة الشاشة على ميزات لتسهيل التنقل بين الجداول. يمكن تحسين تجربة استخدام برامج قراءة الشاشة من خلال ضمان إشارة الخلايا `<td>` التي تستخدم السمة `[headers]` إلى خلايا أخرى في الجدول نفسه فقط. [مزيد من المعلومات](https://web.dev/td-headers-attr/)"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "الخلايا الواردة في عنصر `<table>` التي تستخدم السمة `[headers]` تشير إلى عنصر `id` لم يتم العثور عليه في الجدول نفسه."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "الخلايا الواردة في عنصر `<table>` التي تستخدم السمة `[headers]` تشير إلى خلايا الجدول في الجدول نفسه."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "تحتوي برامج قراءة الشاشة على ميزات لتسهيل التنقل بين الجداول. قد يؤدي ضمان أن عناوين الجداول تشير دائمًا إلى بعض مجموعات الخلايا إلى تحسين تجربة مستخدمي برامج قراءة الشاشة. [مزيد من المعلومات](https://web.dev/th-has-data-cells/)"}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "عناصر `<th>` وعناصر `[role=\"columnheader\"/\"rowheader\"]` لا تحتوي على خلايا البيانات التي يتم وصفها."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "عناصر `<th>` وعناصر `[role=\"columnheader\"/\"rowheader\"]` تحتوي على خلايا البيانات التي يتم وصفها"}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "يؤدي تحديد [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) صحيحة على العناصر إلى مساعدة قارئ الشاشة على نطق النص بشكلٍ صحيح. [مزيد من المعلومات](https://web.dev/valid-lang/)"}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "سمات `[lang]` لا تحتوي على قيمة صالحة"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "سمات `[lang]` تحتوي على قيمة صالحة"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "عندما يقدم الفيديو شرحًا، يَسهُل على المستخدمين الصُم والذين يعانون من إعاقة سمعية فهم مضمونه. [مزيد من المعلومات](https://web.dev/video-caption/)"}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "عناصر `<video>` لا تحتوي على عنصر `<track>` مع `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "العناصر `<video>` تحتوي على عنصر `<track>` مع `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "توفّر الأوصاف الصوتية معلومات ذات صلة للفيديوهات التي لا يمكن إجراء الحوار بها، مثل تعبيرات الوجه وأجواء الإضاءة. [مزيد من المعلومات](https://web.dev/video-description/)"}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "عناصر `<video>` لا تحتوي على عنصر `<track>` مع `[kind=\"description\"]`."}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "العناصر `<video>` تحتوي على عنصر `<track>` مع `[kind=\"description\"]`"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "للحصول على المظهر المثالي على نظام التشغيل iOS عند إضافة المستخدمين تطبيق ويب تقدّمي إلى الشاشة الرئيسية، يمكنك تحديد `apple-touch-icon`. يجب أن تشير هذه السمة إلى مربع غير شفاف بتنسيق PNG مقاسه 192 بكسل (أو 180 بكسل). [مزيد من المعلومات](https://web.dev/apple-touch-icon/)"}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "لا يتم تقديم رمز `apple-touch-icon` صالح"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "سمة `apple-touch-icon-precomposed` هي قديمة، ويُفضّل استخدام سمة `apple-touch-icon`"}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "يتم تقديم رمز `apple-touch-icon` صالح"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "أثّرت \"إضافات Chrome\" بشكلٍ سلبي في أداء التحميل لهذه الصفحة. ويمكنك تجربة تدقيق الصفحة في وضع التصفُّح المُتخفّي أو من ملف شخصي على Chrome بدون الإضافات."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "تقييم النص البرمجي"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "تحليل النص البرمجي"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "الوقت الإجمالي لوحدة المعالجة المركزية"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "يمكنك تقليل الوقت المستغرق في تحليل جافا سكريبت وإنشائها وتنفيذها. قد يتبين لك أن تسليم أحمال جافا سكريبت بحجم أصغر يساعد على ذلك. [مزيد من المعلومات](https://web.dev/bootup-time)"}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "تقليل وقت تنفيذ جافا سكريبت"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "وقت تنفيذ جافا سكريبت"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "ملفات GIF الكبيرة غير كافية لعرض محتوى صور متحركة. يمكنك استخدام فيديوهات MPEG4/WebM للصور المتحركة وملفات PNG/WebP للصور الثابتة بدلاً من ملف GIF لحفظ وحدات البايت للشبكة. [مزيد من المعلومات](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "استخدام تنسيقات الفيديو لمحتوى الصور المتحركة"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "يمكنك إجراء تحميل بطيء للصور الموجودة خارج الشاشة والصور المخفية بعد الانتهاء من تحميل جميع الموارد المهمة من أجل تقليص وقت التفاعل. [مزيد من المعلومات](https://web.dev/offscreen-images)"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "تأجيل الصور خارج الشاشة"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "تحظر الموارد سرعة عرض الصفحة لصفحتك. ويمكنك تضمين عناصر جافا سكريبت/CSS المهمة وتأجيل جميع الأنماط/عناصر جافا سكريبت غير المهمة. [مزيد من المعلومات](https://web.dev/render-blocking-resources)"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "استبعاد موارد حظر العرض"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "تُكلِّف أحمال الشبكة الكبيرة المستخدمين الكثير من الأموال وترتبط مباشرةً بأوقات التحميل الطويلة. [مزيد من المعلومات](https://web.dev/total-byte-weight)"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "كان إجمالي الحجم {totalBytes, number, bytes} كيلوبايت."}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "تجنُّب الأحمال الضخمة للشبكة"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "تجنُّب الأحمال الضخمة للشبكة"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "يمكن أن يؤدي تصغير ملفات CSS إلى تقليل أحجام حمولة الشبكة. [مزيد من المعلومات](https://web.dev/unminified-css)"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "تصغير CSS"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "يمكن أن يؤدي تصغير ملفات جافا سكريبت إلى تقليل أحجام الأحمال ووقت تحليل النص البرمجي. [مزيد من المعلومات](https://web.dev/unminified-javascript)"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "تصغير جافا سكريبت"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "يمكنك إزالة القواعد الضارة من أوراق الأنماط وتأجيل تحميل خدمة CSS غير المستخدمة في محتوى الجزء المرئي من الصفحة للحد من وحدات البايت غير الضرورية المستهلكة من خلال نشاط الشبكة. [مزيد من المعلومات](https://web.dev/unused-css-rules)"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "إزالة خدمة CSS غير المُستخدَمة"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "يمكنك إزالة جافا سكريبت غير المُستخدَم لتقليل وحدات البايت التي يستهلكها نشاط الشبكة."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "إزالة جافا سكريبت غير المستخدم"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "يمكن لفترة التخزين المؤقت الطويلة تسريع عملية تكرار الزيارات إلى صفحتك. [مزيد من المعلومات](https://web.dev/uses-long-cache-ttl)"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{تم العثور على مورد واحد}zero{تم العثور على # مورد}two{تم العثور على مورديْنِ (#)}few{تم العثور على # موارد}many{تم العثور على # موردًا}other{تم العثور على # مورد}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "عرض الأصول الثابتة من خلال سياسة ذاكرة التخزين المؤقت الفعالة"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "استخدام سياسة ذاكرة التخزين المؤقت الفعالة على الأصول الثابتة"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "يتم تحميل الصور المحسَّنة بشكلٍ أسرع وتستهلك بيانات أقل لشبكة الجوّال. [مزيد من المعلومات](https://web.dev/uses-optimized-images)"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "تشفير الصور بكفاءة"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "يمكنك عرض صور بحجم مناسب لحفظ بيانات شبكة الجوّال وتحسين وقت التحميل. [مزيد من المعلومات](https://web.dev/uses-responsive-images)"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "الصور ذات الحجم المناسب"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "يجب عرض الموارد المستندة إلى النص باستخدام الضغط (gzip أو الانكماش أو brotli) لتقليل إجمالي وحدات البايت للشبكة. [مزيد من المعلومات](https://web.dev/uses-text-compression)"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "تفعيل ضغط النص"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "غالبًا ما توفِّر تنسيقات الصور، مثل JPEG 2000 وJPEG XR وWebP، ضغطًا أفضل من تنسيق PNG أو JPEG، وهذا يعني تنزيلاً أسرع واستهلاكًا أقل للبيانات. [مزيد من المعلومات](https://web.dev/uses-webp-images)"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "عرض الصور بتنسيقات الجيل القادم"}, "lighthouse-core/audits/content-width.js | description": {"message": "في حال كان عرض محتوى التطبيق لا يتطابق مع عرض إطار العرض، قد لا يتم تحسين تطبيقك لشاشات الجوّال. [مزيد من المعلومات](https://web.dev/content-width)"}, "lighthouse-core/audits/content-width.js | explanation": {"message": "لا يتطابق حجم إطار العرض {innerWidth} بكسل مع حجم النافذة {outerWidth} بكسل."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "عدم تحديد حجم المحتوى بشكلٍ صحيح لإطار العرض"}, "lighthouse-core/audits/content-width.js | title": {"message": "تحديد حجم المحتوى بشكلٍ صحيح لإطار العرض"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "توضح لك \"سلاسل الطلبات المهمة\" أدناه الموارد التي تم تحميلها بأولوية عالية. ويمكنك تقليل طول السلاسل أو تقليل حجم تنزيل الموارد أو تأجيل تنزيل الموارد غير الضرورية لتحسين تحميل الصفحة. [مزيد من المعلومات](https://web.dev/critical-request-chains)"}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{تم العثور على سلسلة واحدة}zero{تم العثور على # سلسلة}two{تم العثور على سلسلتيْنِ (#)}few{تم العثور على # سلاسل}many{تم العثور على # سلسلةً}other{تم العثور على # سلسلة}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "تقليل عمق الطلبات المهمة"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "إيقاف / تحذير"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "السطر"}, "lighthouse-core/audits/deprecations.js | description": {"message": "ستتم في النهاية إزالة واجهات برمجة التطبيقات المتوقفة من المتصفح. [مزيد من المعلومات](https://web.dev/deprecations)"}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{تم العثور على تحذير واحد}zero{تم العثور على # تحذير}two{تم العثور على تحذيرين (#)}few{تم العثور على # تحذيرات}many{تم العثور على # تحذيرًا}other{تم العثور على # تحذير}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "يتم استخدام واجهات برمجة التطبيقات المتوقفة"}, "lighthouse-core/audits/deprecations.js | title": {"message": "يتم تجنّب واجهات برمجة التطبيقات المتوقفة"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "تم إيقاف ذاكرة التخزين المؤقت للتطبيق. [مزيد من المعلومات](https://web.dev/appcache-manifest)"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "تم العثور على \"{AppCacheManifest}\""}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "يتم استخدام ذاكرة التخزين المؤقت للتطبيق"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "يتم تجنُب ذاكرة التخزين المؤقت للتطبيق"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "يؤدي تحديد doctype إلى منع المتصفح من التبديل إلى وضع Quirks. [مزيد من المعلومات](https://web.dev/doctype)"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "يجب أن يكون اسم DOCTYPE هو سلسلة الأحرف الصغيرة `html`"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "يجب أن يحتوي المستند على DOCTYPE"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "من الممكن أن تكون publicId المتوقعة سلسلة فارغة"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "من الممكن أن تكون systemId المتوقعة سلسلة فارغة"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "تفتقر الصفحة إلى HTML DOCTYPE، مما يؤدي إلى تشغيل وضع Quirks"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "الصفحة تحتوي على HTML DOCTYPE"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "العنصر"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "الإحصائية"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "القيمة"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "ينصح مهندسو المتصفّح بأن تحتوي الصفحات على أقل من 1500 عنصر DOM تقريبًا. من المفضّل أن يقلّ عمق الشجرة عن 32 عنصرًا وألّا يزيد عن 60 عنصرًا فرعيًا/رئيسيًا. يمكن أن يزيد حجم DOM الكبير من استخدام الذاكرة، ويتسبب في إجراء [حسابات نمطية](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) أطول، وينتج عنه [عمليات مُكلفة لإعادة عرض التنسيق](https://developers.google.com/speed/articles/reflow). [مزيد من المعلومات](https://web.dev/dom-size)"}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{عنصر واحد}zero{# عنصر}two{عنصرين (#)}few{# عناصر}many{# عنصرًا}other{# عنصر}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "تجنُب حجم DOM الزائد"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "الح<PERSON> الأقصى لعمق DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "إجمالي عدد عناصر DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "ال<PERSON><PERSON> الأقصى من عناصر الأطفال"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "تجنُب حجم DOM الزائد"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "السمة المُستهدَفة"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "يمكنك إضافة `rel=\"noopener\"` أو `rel=\"noreferrer\"` إلى أي روابط خارجية لتحسين الأداء ومنع الثغرات الأمنية. [مزيد من المعلومات](https://web.dev/external-anchors-use-rel-noopener)"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "الروابط إلى وجهات مشتركة المصدر هي غير آمنة"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "الروابط إلى وجهات مشتركة المصدر هي آمنة"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "يتعذّر تحديد الوجهة لإعلان ثابت {anchorHTML}). في حال عدم استخدامه كرابط تشعبي، يمكنك إزالة target=_blank."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "لا يثق المستخدمون في المواقع الإلكترونية التي تطلب مواقعهم الجغرافية بدون سياق أو قد يؤدي ذلك إلى إرباكهم. يمكنك ربط الطلب بإجراء المستخدم بدلاً من ذلك. [مزيد من المعلومات](https://web.dev/geolocation-on-start)"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "يتم طلب إذن رصد الموقع الجغرافي عند تحميل الصفحة"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "يتم تجنُب طلب إذن رصد الموقع الجغرافي عند تحميل الصفحة"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "الإصدار"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "تم رصد جميع مكتبات الواجهة الأمامية جافا سكريبت في الصفحة. [مزيد من المعلومات](https://web.dev/js-libraries)"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "مكتبات جافا سكريبت التي تم رصدها"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "بالنسبة إلى المستخدمين الذين لديهم اتصالات بطيئة، يمكن للبرامج النصية الخارجية التي يتم إدخالها ديناميكيًا عبر `document.write()` تأخير تحميل الصفحة لمدة ثوانٍ متعددة. [مزيد من المعلومات](https://web.dev/no-document-write)"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "يتم استخدام `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "يتم تجنُب `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "أعلى نسبة خطورة"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "إصدار المكتبة"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "ع<PERSON><PERSON> الثغرات"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "قد تحتوي بعض البرامج النصية للجهات الخارجية على ثغرات أمنية معروفة يمكن للمهاجمين تحديدها واستغلالها بسهولة. [مزيد من المعلومات](https://web.dev/no-vulnerable-libraries)"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{تم رصد ثغرة واحدة}zero{تم رصد # ثغرة}two{تم رصد ثغرتين (#)}few{تم رصد # ثغرات}many{تم رصد # ثغرةً}other{تم رصد # ثغرة}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "يتم تضمين مكتبات الواجهة الأمامية جافا سكريبت ذات الثغرات الأمنية المعروفة"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "مرتفع"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "من<PERSON><PERSON>ض"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "متوسط"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "يتم تجنُب مكتبات الواجهة الأمامية جافا سكريبت ذات الثغرات الأمنية المعروفة"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "لا يثق المستخدمون في المواقع الإلكترونية التي تطلب إرسال الإشعارات بدون سياق أو قد يؤدي ذلك إلى إرباكهم. يمكنك ربط الطلب بإيماءات المستخدم بدلاً من ذلك. [مزيد من المعلومات](https://web.dev/notification-on-start)"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "يتم طلب إذن الإشعار عند تحميل الصفحة"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "يتم تجنُّب طلب إذن الإشعار عند تحميل الصفحة"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "العناصر التي لا تسمح بلصق المحتوى"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "يؤدي منع لصق كلمة المرور إلى تقويض سياسة الأمان الجيدة. [مزيد من المعلومات](https://web.dev/password-inputs-can-be-pasted-into)"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "يتم منع المستخدمين من اللصق في حقول كلمات المرور"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "يتم السماح للمستخدمين باللصق في حقول كلمات المرور"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "البروتوكول"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "يوفّر HTTP/2 العديد من المزايا على HTTP/1.1، بما في ذلك عناوين البرامج الثنائية وعملية مضاعفة توجيه الإشارات ودفع الخادم. [مزيد من المعلومات](https://web.dev/uses-http2)"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{لم يتم عرض طلب واحد عبر HTTP/2}zero{لم يتم عرض # طلب عبر HTTP/2}two{لم يتم عرض طلبين (#) عبر HTTP/2}few{لم يتم عرض # طلبات عبر HTTP/2}many{لم يتم عرض # طلبًا عبر HTTP/2}other{لم يتم عرض # طلب عبر HTTP/2}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "لا تستخدم صفحة الويب HTTP/2 لجميع مواردها"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "تستخدم صفحة الويب HTTP/2 لمواردها الخاصة"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "يمكنك وضع علامة على \"أدوات معالجة أحداث لمس الشاشة وتحريك الماوس\" بصفتها `passive` لتحسين عملية التنقل في صفحتك. [مزيد من المعلومات](https://web.dev/uses-passive-event-listeners)"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "لا يتم استخدام أدوات معالجة الحدث السلبية لتحسين عملية التنقل في الصفحة"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "يتم استخدام أدوات معالجة الحدث السلبية لتحسين أداء التمرير"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "الوصف"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "تشير الأخطاء التي تم تسجيلها في وحدة التحكّم إلى مشاكل لم يتم حلها. قد تنتج هذه المشاكل من إخفاقات طلب الشبكة ومشاكل أخرى تتعلق بالمتصفّح. [مزيد من المعلومات](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "تم تسجيل أخطاء المتصفح في وحدة التحكّم"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "لم يتم تسجيل أخطاء المتصفح في وحدة التحكّم"}, "lighthouse-core/audits/font-display.js | description": {"message": "يمكنك الاستفادة من ميزة CSS لعرض الخطوط لضمان أن يكون النص مرئيًا للمستخدم أثناء تحميل خطوط موقع ويب. [مزيد من المعلومات](https://web.dev/font-display)"}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "التأكد من بقاء النص مرئيًا أثناء تحميل خط موقع ويب"}, "lighthouse-core/audits/font-display.js | title": {"message": "تظل جميع النصوص مرئية أثناء تحميل خط موقع ويب"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "لم يتمكّن Lighthouse من التحقّق تلقائيًا من قيمة عرض الخط لعنوان URL التالي: {fontURL}"}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "نسبة العرض إلى الارتفاع (الفعلية)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "نسبة العرض إلى الارتفاع (معروضة)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "يجب أن تتوافق أبعاد عرض الصورة مع نسبة العرض إلى الارتفاع الطبيعية. [مزيد من المعلومات](https://web.dev/image-aspect-ratio)"}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "يتم عرض الصور مع نسبة عرض إلى ارتفاع غير صحيحة"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "يتم عرض الصور مع نسبة العرض إلى الارتفاع الصحيحة"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "معلومات تغيير حجم الصورة غير صالحة {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "يمكن للمتصفحات أن تطلب من المستخدمين بشكل مسبق إضافة تطبيقك إلى الشاشة الرئيسية، ويمكن بذلك زيادة التفاعل. [مزيد من المعلومات](https://web.dev/installable-manifest)"}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "عدم استيفاء بيان تطبيق الويب متطلبات التثبيت"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "استيفاء بيان تطبيق الويب متطلبات التثبيت"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "عنوان URL غير آمن"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "يجب حماية جميع المواقع الإلكترونية باستخدام HTTPS، حتى تلك المواقع التي لا تتعامل مع البيانات الحساسة. يمنع HTTPS الدخلاء من العبث بالاتصالات بين تطبيقك والمستخدمين أو الاستماع إليها بشكل سلبي، وهو شرط مسبق لـ HTTP/2 والعديد من واجهات برمجة تطبيقات الأنظمة الأساسية للويب الجديدة. [مزيد من المعلومات](https://web.dev/is-on-https)"}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{تم العثور على طلب غير آمن واحد}zero{تم العثور على # طلب غير آمن}two{تم العثور على طلبين غير آمنين (#)}few{تم العثور على # طلبات غير آمنة}many{تم العثور على # طلبًا غير آمن}other{تم العثور على # طلب غير آمن}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "لا يتم استخدام HTTPS"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "يتم استخدام HTTPS"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "إنّ تحميل الصفحات بشكل سريع عبر شبكة الجوّال يضمن توفير تجربة استخدام عالية الجودة. [مزيد من المعلومات](https://web.dev/load-fast-enough-for-pwa)"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "الوقت التفاعلي {timeInMs, number, seconds} ثانية"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "الوقت التفاعلي على شبكة الجوّال التي تمت محاكاتها لمدة {timeInMs, number, seconds}  ثانية"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "تحميل الصفحة بطيء جدًا وليس تفاعليًا خلال 10 ثوانٍ. يُرجى الاطّلاع على الفرص وبيانات التشخيص في قسم \"الأداء\" للتعرُّف على طريقة التحسين."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "تحميل الصفحة على شبكات الجوّال ليس سريعًا بشكلٍ كافٍ"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "تحميل الصفحة سريع بشكلٍ كافٍ في شبكات الجوّال"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "الفئة"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "يمكنك تقليل الوقت المستغرق في تحليل جافا سكريبت وإنشائه وتنفيذه. قد يتبين لك أن تسليم أحمال جافا سكريبت بحجم أصغر يساعد على ذلك. [مزيد من المعلومات](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "تقليل سلسلة العمل الرئيسية"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "تقليل سلسلة العمل الرئيسية"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "للوصول إلى أكبر عدد من المستخدمين، يجب استخدام المواقع الإلكترونية على كل متصفح رئيسي. [مزيد من المعلومات](https://web.dev/pwa-cross-browser)"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "الموقع الإلكتروني يعمل عبر المتصفح"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "يُرجى التأكّد من أن الصفحات الفردية قابلة للربط بشكلٍ كبير عبر عنوان URL وأن عناوين URL فريدة لغرض إمكانية المشاركة على وسائل التواصل الاجتماعي. [مزيد من المعلومات](https://web.dev/pwa-each-page-has-url)"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "كل صفحة تحتوي على عنوان URL"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "من المفترض إجراء عمليات النقل بسرعة عند النقر حولها، حتى على شبكة بطيئة. هذه التجربة هي عنصر أساسي لإدراك مستخدم لمستوى الأداء. [مزيد من المعلومات](https://web.dev/pwa-page-transitions)"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "عمليات نقل الصفحة لا تبدو أنها محظورة على الشبكة"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "إن \"وقت الاستجابة المقدر للإدخال\" هو تقدير لطول المدة التي يستغرقها تطبيقك للاستجابة لإدخال المستخدم بالمللي ثانية، وذلك أثناء الثواني الخمس الأكثر انشغالاً في فترة تحميل الصفحة. وفي حال كان وقت الاستجابة أكثر من 50 مللي ثانية، يمكن للمستخدمين اعتبار تطبيقك بأنه بطيء في التفاعل. [مزيد من المعلومات](https://web.dev/estimated-input-latency)"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "وقت الاستجابة المُقدّر للإدخال"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "تحدد \"سرعة عرض المحتوى على الصفحة\" الوقت الذي يُعرَض فيه أول صورة أو نص. [مزيد من المعلومات](https://web.dev/first-contentful-paint)"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "First Contentful Paint"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "تشير \"وحدة المعالجة المركزية الأولى الخاملة\" إلى المرة الأولى التي يتوفر فيها وقت كافٍ للعملية الرئيسية للصفحة حتى تعالج إدخالات المستخدم.  [مزيد من المعلومات](https://web.dev/first-cpu-idle)"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "وحدة المعالجة المركزية الأولى الخاملة"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "تقيس \"سرعة عرض أوّل محتوى مفيد على الصفحة\" الوقت الذي يكون فيه المحتوى الأساسي لصفحة مرئيًا. [مزيد من المعلومات](https://web.dev/first-meaningful-paint)"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "First Meaningful Paint"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "وقت التفاعل هو مقدار الوقت المستغرق حتى تصبح الصفحة تفاعلية بالكامل. [مزيد من المعلومات](https://web.dev/interactive)"}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "وقت التفاعل"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "الحد الأقصى المحتمل من \"مهلة الاستجابة لأوّل إدخال\" الذي قد يواجهه المستخدمون هو المدة بالمللي ثانية لأطول مهمة. [مزيد من المعلومات](https://developers.google.com/web/updates/2018/05/first-input-delay)"}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "الح<PERSON> الأق<PERSON>ى المحتمل من مهلة الاستجابة لأوّل إدخال"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "يوضح مؤشر السرعة وتيرة تعبئة محتوى الصفحة على شاشة المستخدم. [مزيد من المعلومات](https://web.dev/speed-index)"}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "مؤشر السرعة"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "مجموع جميع الفترات الزمنية بين \"سرعة عرض المحتوى على الصفحة\" و\"وقت التفاعل\"، عندما تتجاوز مدة المهمة 50 مللي ثانية، معبرًا عنها بالمللي ثانية."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "إجمالي حظر الوقت"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "تؤثر مُدد الإرسال والاستقبال للشبكة تأثيرًا كبيرًا في مستوى الأداء. في حال كانت مدة الإرسال والاستقبال كبيرة، يشير ذلك إلى احتمال تحسّن أداء الخوادم الأقرب إلى المستخدم. [مزيد من المعلومات](https://hpbn.co/primer-on-latency-and-bandwidth/)"}, "lighthouse-core/audits/network-rtt.js | title": {"message": "أوقات إرسال واستقبال الشبكة"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "قد تؤثر أوقات استجابة الخادم في أداء الويب. في حال كان وقت استجابة الخادم للأصل طويلاً، هذه إشارة إلى أنه تم تحميل الخادم بشكلٍ زائد أو مستوى أدائه ضعيف في الخلفية. [مزيد من المعلومات](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)"}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "أوقات الاستجابة لواجهة الخادم الخلفية"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "يمكّن مشغّل الخدمات تطبيق الويب من أن يصبح موثوقًا به في ظروف الشبكة التي لا يمكن التنبؤ بها. [مزيد من المعلومات](https://web.dev/offline-start-url)"}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url` لا تستجيب باستخدام رمز 200 عند عدم الاتصال بالإنترنت"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url` يستجيب باستخدام رمز 200 عند عدم الاتصال بالإنترنت"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "تعذّر على Lighthouse قراءة `start_url` من البيان. ونتيجة لذلك، كان من المفترض أن يكون `start_url` هو عنوان URL للمستند. رسالة خطأ: '{manifestWarning}'."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "تجاوز الميزانية"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "يمكنك الحفاظ على كمية طلبات الشبكة وحجمها ضمن الاستهدافات المحدّدة في ميزانية الأداء المقدّمة. [مزيد من المعلومات](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{طلب واحد}zero{# طلب}two{طلبان (#)}few{# طلبات}many{# طلبًا}other{# طلب}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "ميزانية الأداء"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "في حال أعددت HTTPS مسبقًا، تأكّد من إعادة توجيه جميع زيارات HTTP إلى HTTPS من أجل تفعيل ميزات الويب الآمنة لجميع المستخدمين. [مزيد من المعلومات](https://web.dev/redirects-http)"}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "عدم إعادة توجيه زيارات HTTP إلى HTTPS"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "إعادة توجيه زيارات HTTP إلى HTTPS"}, "lighthouse-core/audits/redirects.js | description": {"message": "تؤدي عمليات إعادة التوجيه إلى حدوث تأخيرات إضافية قبل أن يتم تحميل الصفحة. [مزيد من المعلومات](https://web.dev/redirects)"}, "lighthouse-core/audits/redirects.js | title": {"message": "تجنُب عمليات إعادة توجيه الصفحات المتعددة"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "لضبط ميزانيات لكمية موارد الصفحة وحجمها، يمكنك إضافة ملف budget.json. [مزيد من المعلومات](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{طلب واحد • {byteCount, number, bytes} كيلوبايت}zero{# طلب • {byteCount, number, bytes} كيلوبايت}two{طلبان (#) • {byteCount, number, bytes} كيلوبايت}few{# طلبات • {byteCount, number, bytes} كيلوبايت}many{# طلبًا • {byteCount, number, bytes} كيلوبايت}other{# طلب • {byteCount, number, bytes} كيلوبايت}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "الحفاظ على انخفاض عدد الطلبات ونقل الأحجام الصغيرة"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "تقترح الروابط الأساسية عنوان URL للعرض في نتائج البحث. [مزيد من المعلومات](https://web.dev/canonical)"}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "تتعدّد عناوين URL المتضاربة ({urlList})."}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "يشير عنوان URL إلى نطاق مختلف ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "عنوان URL غير صالح ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "يشير عنوان URL إلى موقع جغرافي `hreflang` آخر ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "عنوان URL هو نسبي ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "يشير إلى عنوان URL الجذر للنطاق (الصفحة الرئيسية)، بدلاً من صفحة مكافئة للمحتوى"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "المستند لا يحتوي على سمة `rel=canonical` صالحة"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "المستند يحتوي على سمة `rel=canonical` صالحة"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "أحجا<PERSON> الخطوط الأقل من 12 بكسل صغيرة جدًا بحيث لا يمكن قراءتها بسهولة وتتطلب من مستخدمي الجوّال \"تحريك الإصبعين للتكبير أو التصغير\" من أجل قراءتها. يمكنك بذل قصارى جهدك للحصول على نسبة أكبر من 60% من نص الصفحة بمقدار أكبر من أو يساوي 12 بكسل. [مزيد من المعلومات](https://web.dev/font-size)"}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "نص {decimalProportion, number, extendedPercent} قابل للقراءة"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "النص غير مقروء لأنه لا تتوفّر علامة وصفية لإطار العرض محسنة لشاشات الجوّال."}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "نسبة {decimalProportion, number, extendedPercent} من النص هي صغيرة جدًا (استنادًا إلى نموذج {decimalProportionVisited, number, extendedPercent})."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "لا يستخدم المستند أحجام الخطوط القابلة للقراءة"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "يستخدم المستند أحجام الخط القابلة للقراءة"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "تخبر روابط hreflang محركات البحث عن إصدار الصفحة الذي يجب إدراجه في نتائج البحث للغة أو منطقة معيّنة. [مزيد من المعلومات](https://web.dev/hreflang)"}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "المستند لا يحتوي على سمة `hreflang` صالحة"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "المستند يحتوي على سمة `hreflang` صالحة"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "قد لا تتم فهرسة الصفحات التي تتضمّن رموز حالة HTTP غير صالحة بشكلٍ صحيح. [مزيد من المعلومات](https://web.dev/http-status-code)"}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "تحتوي الصفحة على رمز حالة HTTP غير صالح"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "تحتوي الصفحة على رمز حالة HTTP صالح"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "يتعذّر على محركات البحث تضمين صفحاتك في نتائج البحث في حال عدم حصولها على إذن للزحف إلى هذه الصفحات. [مزيد من المعلومات](https://web.dev/is-crawable)"}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "يتم حظر الصفحة من الفهرسة"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "الصفحة ليست محظورة من الفهرسة"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "يساعد نص الرابط الوصفي محركات البحث على فهم المحتوى. [مزيد من المعلومات](https://web.dev/link-text)"}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{تم العثور على رابط واحد}zero{تم العثور على # رابط}two{تم العثور على رابطين (#)}few{تم العثور على # روابط}many{تم العثور على # رابطًا}other{تم العثور على # رابط}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "عدم احتواء الروابط على نص وصفي"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "تحتوي الروابط على نص وصفي"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "يمكنك تشغيل [أداة اختبار البيانات المنظَّمة](https://search.google.com/structured-data/testing-tool/) و[Linter للبيانات المنظَّمة](http://linter.structured-data.org/) للتحقّق من البيانات المنظَّمة. [مزيد من المعلومات](https://web.dev/structured-data)"}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "البيانات المنظَّمة صالحة"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "قد يتم تضمين الأوصاف التعريفية في نتائج البحث لتلخيص محتوى الصفحة بإيجاز. [مزيد من المعلومات](https://web.dev/meta-description)"}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "نص الوصف فارغ."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "لا يحتوي المستند على وصف تعريفي"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "يحتوي المستند على وصف تعريفي"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "لا يمكن لمحركات البحث فهرسة محتوى مكون إضافي وتحظر العديد من الأجهزة استخدام المكونات الإضافية أو لا توفّرها. [مزيد من المعلومات](https://web.dev/plugins)"}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "يستخدم المستند مكونات إضافية"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "يتجنّب المستند المكونات الإضافية"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "في حال كان ملف robots.txt مكتوبًا بصيغة غير صحيحة، يمكن ألا تفهم برامج الزحف كيف تريد أن يتم الزحف إلى موقعك الإلكتروني أو أن تتم فهرسته. [مزيد من المعلومات](https://web.dev/robots-txt)"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "الطلب لملف robots.txt عرض حالة HTTP: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{تم العثور على خطأ واحد}zero{تم العثور على # خطأ}two{تم العثور على خطأين (#)}few{تم العثور على # أخطاء}many{تم العثور على # خطأً}other{تم العثور على # خطأ}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "تعذّر على Lighthouse تنزيل ملف robots.txt"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "ملف \"robots.txt\" غير صالح"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "ملف \"robots.txt\" صالح"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "يجب أن تكون العناصر التفاعلية، مثل الأزرار والروابط، كبيرة بشكلٍ كافٍ (48 × 48 بكسل) وأن تحيط بها مساحة كافية ليكون من السهل النقر عليها بدون النقر على أي عناصر أخرى. [مزيد من المعلومات](https://web.dev/tap-targets)"}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "تم تحديد حجم {decimalProportion, number, percent} لأهداف النقر بشكلٍ مناسب"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "أهدا<PERSON> النقر صغيرة جدًا لأنه لا تتوفّر علامة وصفية لإطار العرض محسنة لشاشات الجوّال."}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "لم يتم تحديد حجم أهداف النقر بشكل مناسب"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "استهداف متداخِل"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON><PERSON><PERSON> النقر"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "يتم تحديد حجم أهداف النقر بشكل مناسب"}, "lighthouse-core/audits/service-worker.js | description": {"message": "مشغّل الخدمات هو التكنولوجيا التي تمكّن تطبيقك من استخدام ميزات عديدة من \"تطبيق الويب التقدّمي\"، مثل الاستجابة عند عدم الاتصال بالإنترنت والإضافة إلى الشاشة الرئيسية والإشعارات الفورية. [مزيد من المعلومات](https://web.dev/service-worker)"}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "يتحكم مشغّل الخدمات في هذه الصفحة، ومع ذلك لم يتم العثور على `start_url` بسبب تعذّر تحليل البيان كملف JSON صالح"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "يتحكم مشغّل الخدمات في هذه الصفحة، ومع ذلك لا يتوفر `start_url` ({startUrl}) في نطاق مشغّل الخدمات ({scopeUrl})"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "يتحكم مشغّل الخدمات في هذه الصفحة، ومع ذلك لم يتم العثور على `start_url` لأنه لم يتم جلب أي بيان."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "تحتوي نقطة الانطلاق هذه على مشغّل خدمات واحد أو أكثر، ولكن لا يوجد مشغّل يتحكم في الصفحة ({pageUrl})."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "عدم تسجيل مشغّل الخدمات الذي يتحكّم في صفحة و`start_url`"}, "lighthouse-core/audits/service-worker.js | title": {"message": "تسجيل مشغّل الخدمات الذي يتحكّم في صفحة و`start_url`"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "تضمن شاشة البداية المميزة توفير تجربة عالية الجودة عند إطلاق المستخدمين تطبيقك من الشاشات الرئيسية. [مزيد من المعلومات](https://web.dev/splash-screen)"}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "عدم الضبط لشاشة بداية مخصّصة"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "تم الضبط لشاشة البداية المخصّصة"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "يمكن تصميم شريط العناوين للمتصفح لمطابقة موقعك الإلكتروني. [مزيد من المعلومات](https://web.dev/themed-omnibox)"}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "عدم ضبط لون تصميم لشريط العناوين"}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "ضبط لون تصميم لشريط العناوين"}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "وقت حظر سلسلة المحادثات الأساسية"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "الجهة الخارجية"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "يمكن أن يؤثر الرمز البرمجي الخاصّ بالجهة الخارجية بشكل كبير في أداء التحميل. يمكنك تحديد عدد مقدِّمي الخدمة للجهات الخارجية المتكرّرين ومحاولة تحميل الرمز البرمجي الخاص بالجهة الخارجية بعد انتهاء تحميل صفحتك بشكل أساسي. [مزيد من المعلومات](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)"}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "لقد حظر رمز الجهة الخارجية سلسلة المحادثات الرئيسية لمدة {timeInMs, number, milliseconds} مللي ثانية"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "تقليل تأثير رمز الجهة الخارجية"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "استخدام جهة خارجية"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "يحدّد \"وقت وصول أول بايت\" الوقت الذي يُرسل فيه الخادم استجابة. [مزيد من المعلومات](https://web.dev/time-to-first-byte)"}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "استغرق مستند الجذر {timeInMs, number, milliseconds} مللي ثانية"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "تقليل أوقات استجابة الخادم (TTFB)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "أوقات استجابة الخادم منخفضة (TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "المدة"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "وقت البدء"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "النوع"}, "lighthouse-core/audits/user-timings.js | description": {"message": "يمكنك توجيه تطبيقك باستخدام \"واجهة برمجة التطبيقات لأوقات المستخدم\" لقياس الأداء الفعلي العالمي لتطبيقك أثناء التجارب الأساسية للمستخدمين. [مزيد من المعلومات](https://web.dev/user-timings)"}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{وقت واحد للمستخدم}zero{# وقت للمستخدم}two{وقتا (#) المستخدم}few{# أوقات للمستخدم}many{# وقتًا للمستخدم}other{# وقت للمستخدم}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "علامات أوقات المستخدم وقياساتها"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "تم العثور على <link> للربط المسبق لـ \"{securityOrigin}\"، ولكن لم يتم استخدامه من خلال المتصفح. يُرجى التحقّق من استخدام السمة `crossorigin` بشكل صحيح."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "يمكنك إضافة تعديلات المورد `preconnect` أو `dns-prefetch` لإنشاء اتصالات مبكرة بأصول مهمة تابعة لجهة خارجية. [مزيد من المعلومات](https://web.dev/uses-rel-preconnect)"}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "الاتصال المسبق للأصول المطلوبة"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "تم العثور على <link> للتحميل المسبق لـ \"{preloadURL}\"، ولكن لم يتم استخدامه من خلال المتصفح. يُرجى التحقّق من استخدام السمة `crossorigin` بشكل صحيح."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "يمكنك استخدام `<link rel=preload>` لتحديد أولويات جلب الموارد المطلوبة حاليًا في وقتٍ لاحق في تحميل الصفحة. [مزيد من المعلومات](https://web.dev/uses-rel-preload)"}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "التحميل المسبق للطلبات الأساسية"}, "lighthouse-core/audits/viewport.js | description": {"message": "يُرجى إضافة علامة `<meta name=\"viewport\">` لتحسين تطبيقك لشاشات الجوّال. [مزيد من المعلومات](https://web.dev/viewport)"}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "لم يتم العثور على علامة `<meta name=\"viewport\">`"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "عدم الاحتواء على علامة `<meta name=\"viewport\">` مع `width` أو `initial-scale`"}, "lighthouse-core/audits/viewport.js | title": {"message": "تضمين علامة `<meta name=\"viewport\">` مع `width` أو `initial-scale`"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "يجب أن يعرض تطبيقك بعض المحتوى عند إيقاف جافا سكريبت، حتى في حال كان مجرد تحذير للمستخدم يشير إلى ضرورة وجود جافا سكريبت لاستخدام التطبيق. [مزيد من المعلومات](https://web.dev/without-javascript)"}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "يجب أن يعرض نص الصفحة بعض المحتوى في حالة عدم توفر النصوص البرمجية."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "عدم تقديم المحتوى الاحتياطي عند عدم توفر جافا سكريبت"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "تضمين بعض المحتوى عند عدم توفّر جافا سكريبت"}, "lighthouse-core/audits/works-offline.js | description": {"message": "في حال كنت تنشئ \"تطبيق ويب تقدّمي\"، يمكنك استخدام مشغّل الخدمات حتى يتمكّن التطبيق من العمل بلا اتصال بالإنترنت. [مزيد من المعلومات](https://web.dev/works-offline)"}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "الصفحة الحالية لا تستجيب باستخدام رمز 200 عند عدم الاتصال بالإنترنت"}, "lighthouse-core/audits/works-offline.js | title": {"message": "الصفحة الحالية تستجيب باستخدام رمز 200 عند عدم الاتصال بالإنترنت"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "قد لا يتم تحميل الصفحة بلا اتصال بالإنترنت لأنه تمت إعادة توجيه عنوان URL للاختبار ({requested}) إلى \"{final}\". ويمكنك محاولة اختبار عنوان URL الثاني مباشرة."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "هذه هي فرص لتحسين استخدام ARIA في تطبيقك، ما قد يحسّن تجربة مستخدمي التكنولوجيا المساعدة، مثل قارئ الشاشة."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "هذه الفرص تتيح توفير محتوى بديل للصوت والفيديو. قد يحسّن ذلك التجربة للمستخدمين الذين يعانون من إعاقات سمعية أو بصرية."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "الصوت والفيديو"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "تحدّد هذه العناصر أفضل الممارسات الشائعة المتعلقة بإمكانية الوصول."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "أفضل الممارسات"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "تحدّد عمليات التحقق هذه الفرص التي تتيح [تحسين إمكانية الوصول إلى تطبيق الويب](https://developers.google.com/web/fundamentals/accessibility). ولا يمكن إجراء رصد تلقائي إلّا لمجموعة فرعية من مشاكل إمكانية الوصول، لذلك يُنصح أيضًا باستخدام الاختبار اليدوي."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "تعالج هذه العناصر المناطق التي يتعذر على أداة الاختبار التلقائية تغطيتها. تعرّف على مزيد من المعلومات في دليلنا حول [مراجعة إمكانية الوصول](https://developers.google.com/web/fundamentals/accessibility/how-to-review)."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "إمكانية الوصول"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "هذه هي فرص لتحسين سهولة قراءة المحتوى."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "التباين"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "هذه هي فرص لتحسين تفسير المحتوى من خلال المستخدمين بلغات مختلفة."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "التدويل والأقلمة"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "هذه هي فرص لتحسين دلالات عناصر التحكُّم في التطبيق. قد يحسّن ذلك من تجربة مستخدمي التكنولوجيا المساعدة، مثل قارئ الشاشة."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "الأسماء والتصنيفات"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "هذه الفرص تتيح لك تحسين التنقل باستخدام لوحة المفاتيح في تطبيقك."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "التنقل"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "هذه الفرص تتسح تحسين تجربة قراءة بيانات القائمة أو الجدول باستخدام التكنولوجيا المساعدة، مثل قارئ الشاشة."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "الجداول والقوائم"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "أفضل الممارسات"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "تضبط ميزانيات الأداء معايير لأداء موقعك الإلكتروني."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "الميزانيات"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "مزيد من المعلومات حول أداء تطبيقك لا تؤثر هذه الأرقام [ بشكل مباشر في ](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) نتيجة الأداء."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "بيانات التشخيص"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "يمثل الجانب الأكثر أهمية للأداء مدى السرعة التي يتم بها عرض وحدات البكسل على الشاشة. المقاييس الرئيسية: First Contentful Paint وFirst Meaningful Paint"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "تحسينات العرض الأول"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "يمكن أن تساعد هذه الاقتراحات على تحميل صفحتك بشكل أسرع. لا تؤثر هذه الاقتراحات [بشكل مباشر](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) في نتيجة الأداء."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "فرص تحسين الأداء"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "المقاييس"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "يمكنك تحسين تجربة التحميل العامة لتصبح هذه الصفحة سريعة الاستجابة وجاهزة للاستخدام في أقرب وقت ممكن. المقاييس الأساسية: وقت التفاعل ومؤشر السرعة."}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "التحسينات العامة"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "الأداء"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "تعمل هذه العمليات على التحقق من جوانب \"تطبيق الويب التقدّمي\". [مزيد من المعلومات](https://developers.google.com/web/progressive-web-apps/checklist)"}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "يُطلب إجراء عمليات التحقّق هذه من خلال المرجع [قائمة التحقق PWA](https://developers.google.com/web/progressive-web-apps/checklist)، ولكن لم يتم التحقُّق منها تلقائيًا من خلال Lighthouse. لا تؤثر عمليات التحقق هذه في نتيجتك، ولكن من المهم أنك تتحقق منها يدويًا."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "تطبيق الويب التقدّمي"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "سريع وموثوق"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "القسم القابل للتثبيت"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "تحسين PWA"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "تضمن عمليات التحقّق هذه تحسين أداء صفحتك لتظهر في ترتيب نتائج محرّك البحث. هناك عوامل إضافية لا يتحقّق منها Lighthouse قد تؤثر في ترتيب نتائج البحث. [مزيد من المعلومات](https://support.google.com/webmasters/answer/35769)"}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "تشغيل أدوات التحقُّق الإضافية هذه على موقعك الإلكتروني للتحقُّق من أفضل ممارسات تحسين محركات البحث الإضافية."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "تحسين محركات البحث"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "يمكنك تنسيق HTML بطريقة تتيح لبرامج الزحف فهم محتوى تطبيقك بشكلٍ أفضل."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "أفضل ممارسات المحتوى"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "للظهور في نتائج البحث، تحتاج برامج الزحف إلى الوصول إلى تطبيقك."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "الزحف والفهرسة"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "يُرجى التأكّد من أن صفحاتك متوافقة مع الجوّال، حتى لا يحتاج المستخدمون إلى التصغير أو التكبير من أجل الاطّلاع على صفحات المحتوى. [مزيد من المعلومات](https://developers.google.com/search/mobile-sites/)"}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "متوافق مع الجوّال"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "ذاكرة التخزين المؤقت TTL"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "الموقع الجغرافي"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "الاسم"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "الطلبات"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "نوع المورد"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "الحجم"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "الوقت المستغرَق"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "حجم النقل"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "عنوان URL"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "التوفيرات المحتملة"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "التوفيرات المحتملة"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "التوفيرات المحتملة من {wastedBytes, number, bytes}  كيلوبايت"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "التوفيرات المحتملة من {wastedMs, number, milliseconds} مللي ثانية"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "المستند"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "الخط"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "الصورة"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "الوسائط"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} مللي ثانية"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON>ير ذلك"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "النص البرمجي"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} ثانية"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "ورقة الأنماط"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "الجهة الخارجية"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "الإجمالي"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "حدث خطأ أثناء تسجيل التتبع عند تحميل صفحتك. يُرجى تشغيل Lighthouse مرة أخرى. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "مهلة انتظار ربط بروتوكول برنامج تصحيح الخلل الأول."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "لم يجمع Chrome أي لقطات شاشة خلال عملية تحميل الصفحة. يُرجى التأكّد من توفّر محتوى مرئي على الصفحة، ثم محاولة إعادة تشغيل Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "تعذّر على خوادم نظام أسماء النطاقات حل مشكلة النطاق المُقدّم."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "واجهت عملية التجميع {artifactName} المطلوبة خطأً: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "حدث خطأ في متصفّح Chrome الداخلي. يُرجى إعادة تشغيل Chrome ومحاولة إعادة تشغيل Lighthouse."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "كان من المطلوب تجميع {artifactName} ولكن لم يتم تنفيذ ذلك."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "لم يتمكّن Lighthouse من تحميل الصفحة المطلوبة بشكل موثوق. يمكنك التأكُّد من اختبار عنوان URL الصحيح وأن الخادم يستجيب بشكل صحيح لجميع الطلبات."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "لم يتمكّن Lighthouse من تحميل عنوان URL الذي طلبته بشكل موثوق لأن الصفحة توقفت عن الاستجابة."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "لا يحتوي عنوان URL الذي قدمته على شهادة أمان صالحة. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "منَع Chrome تحميل صفحة مع محتوى بيني. عليك التأكّد من اختبار عنوان URL الصحيح وأن الخادم يستجيب بشكل صحيح لجميع الطلبات."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "لم يتمكّن Lighthouse من تحميل الصفحة المطلوبة بشكل موثوق. عليك التأكّد من اختبار عنوان URL الصحيح وأن الخادم يستجيب بشكل صحيح لجميع الطلبات. (التفاصيل: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "لم يتمكّن Lighthouse من تحميل الصفحة المطلوبة بشكل موثوق. عليك التأكّد من اختبار عنوان URL الصحيح وأن الخادم يستجيب بشكل صحيح لجميع الطلبات. (رمز الحالة: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "استغرق تحميل الصفحة وقتًا طويلاً. يُرجى اتّباع الفرص الواردة في التقرير لتقليل وقت تحميل الصفحة، ثم محاولة إعادة تشغيل Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "لقد تجاوز وقت انتظار استجابة بروتوكول DevTools الوقت المخصص. (الطريقة: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "تجاوز جلب محتوى المورد الوقت المخصّص"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "يبدو أن عنوان URL الذي قدمته غير صحيح."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "عرض عمليات التدقيق"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "التنقل الأوّلي"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "الح<PERSON> الأقصى لوقت استجابة المسار المهم:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "خطأ!"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "الإبلاغ عن خطأ: لا تتوفَّر معلومات تدقيق"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "بيانات المختبَر"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "أجرت أداة [Lighthouse](https://developers.google.com/web/tools/lighthouse/) تحليلًا للصفحة الحالية على شبكة الجوّال في وضع المحاكاة. القيم تقديرية وقابلة للتغيير."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "عناصر إضافية للتحقُّق يدويًا"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "غير سارٍ"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "فرصة تحسين الأداء"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "التوفيرات المُقدرة"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "اجتياز عمليات التدقيق بنجاح"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "تصغير المقتطف"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "توسيع المقتطف"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "عرض موارد الجهات الخارجية"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "حدثت مشاكل تؤثر في تشغيل Lighthouse:"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "القيم تقديرية وقابلة للتغيير. نتيجة الأداء [مستندة إلى هذه المقاييس فقط](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "عمليات التدقيق التي تم اجتيازها، ولكن تتضمّن التحذيرات"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "التحذيرات: "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "يمكنك تحميل ملف GIF إلى خدمة ستتيح تضمينه باعتباره فيديو HTML5."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "يمكنك تثبيت [مكون WordPress الإضافي للتحميل الكسول](https://wordpress.org/plugins/search/lazy+load/) الذي يوفر القدرة على تأجيل أي صور خارج الشاشة، أو التبديل إلى تصميم يوفِّر هذه القدرة الوظيفية. يمكنك أيضًا استخدام [مكون AMP الإضافي](https://wordpress.org/plugins/amp/)."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "هناك عدد من مكونات WordPress الإضافية التي يمكنها مساعدتك على [تضمين مواد العرض المهمة](https://wordpress.org/plugins/search/critical+css/) أو [تأجيل موارد أقل أهمية](https://wordpress.org/plugins/search/defer+css+javascript/). عليك توخي الحذر من أن التحسينات التي توفرها هذه الإضافات قد توقف ميزات التصميم أو المكونات الإضافية، لذلك ستحتاج على الأرجح إلى إجراء تغييرات في الرمز البرمجي."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "تساهم التصاميم والمكونات الإضافية ومواصفات الخادم في تحسين وقت استجابة الخادم. يمكنك البحث عن تصميم مُحسّن أكثر و/أو اختيار مكون إضافي للتحسين و/أو ترقية الخادم."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "يمكنك عرض مقتطفات في قوائم مشاركاتك (مثلاً عبر العلامة \"المزيد\")، أو تقليل عدد المشاركات المعروضة في صفحة معينة، أو تقسيم مشاركاتك الطويلة إلى صفحات متعددة، أو استخدام مكون إضافي لتحميل التعليقات ذات التحميل الكسول."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "يمكن لعدد من [مكونات WordPress الإضافية](https://wordpress.org/plugins/search/minify+css/) زيادة سرعة موقعك الإلكتروني من خلال ربط الأنماط وتصغيرها وضغطها. يمكنك أيضًا استخدام عملية إنشاء الموقع الإلكتروني لإزالة البيانات غير الضرورية بشكل مسبق إذا أمكن ذلك."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "يمكن لعدد من [مكونات WordPress الإضافية ](https://wordpress.org/plugins/search/minify+javascript/) زيادة سرعة موقعك الإلكتروني من خلال ربط النصوص البرمجية وتصغيرها وضغطها. يمكنك أيضًا استخدام عملية إنشاء الموقع الإلكتروني لإزالة البيانات غير الضرورية بشكل مسبق إذا أمكن ذلك."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "يمكنك تقليل عدد [مكونات WordPress الإضافية](https://wordpress.org/plugins/) التي تُحمِّل خدمة CSS غير المُستخدَمة في صفحتك أو تبديلها. لتحديد المكونات الإضافية التي تضيف CSS دخيلة، يمكنك محاولة تشغيل [تغطية الرمز البرمجي](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) في Chrome DevTools. يمكنك تحديد التصميم/المكون الإضافي المسؤول عن عنوان URL لورقة الأنماط. يمكنك البحث عن المكونات الإضافية التي تحتوي على العديد من أوراق الأنماط في القائمة والتي تحتوي على الكثير من اللون الأحمر في تغطية الرمز البرمجي. يجب أن يدرِج المكون الإضافي ورقة أنماط فقط في حال تم استخدامه في الصفحة فعليًا."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "يمكنك تقليل عدد [مكونات WordPress الإضافية](https://wordpress.org/plugins/) التي تُحمِّل لغة جافا سكريبت غير المُستخدَمة في صفحتك أو تبديلها. لتحديد المكونات الإضافية التي تضيف لغة جافا سكريبت دخيلة، يمكنك محاولة تشغيل [تغطية الرمز البرمجي](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) في Chrome DevTools. يمكنك تحديد التصميم/المكون الإضافي المسؤول عن عنوان URL للنص البرمجي. يمكنك البحث عن المكونات الإضافية التي تحتوي على العديد من النصوص البرمجية في القائمة والتي تحتوي على الكثير من اللون الأحمر في تغطية الرمز البرمجي. يجب أن يدرِج المكون الإضافي نصًا برمجيًا فقط في حال تم استخدامه في الصفحة فعليًا."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "يمكنك الاطّلاع على [ذاكرة التخزين المؤقت للمتصفّح في WordPress](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "يمكنك استخدام [مكون WordPress الإضافي لتحسين الصورة](https://wordpress.org/plugins/search/optimize+images/) الذي يضغط صورك مع المحافظة على الجودة."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "يمكنك تحميل الصور مباشرةً من خلال [مكتبة الوسائط](https://codex.wordpress.org/Media_Library_Screen) للتأكّد من توفّر أحجام الصور المطلوبة، ثم إدراجها من مكتبة الوسائط أو استخدام أداة الصورة لضمان استخدام أفضل حجم للصورة (بما في ذلك تلك الخاصة بنقاط فاصلة متجاوبة). يمكنك تجنب استخدام صور `Full Size` إلا إذا كانت الأبعاد كافية لاستخدامها. [مزيد من المعلومات](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)"}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "يمكنك تفعيل ضغط النص في إعداد خادم الويب."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "يمكنك استخدام [مكون إضافي](https://wordpress.org/plugins/search/convert+webp/) أو خدمة تتيح لك تحويل صورك المحمَّلة إلى أفضل التنسيقات تلقائيًا."}}