{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sayfanın bir bölümüne hızlıca odaklanmalarını sağlar. Gezinmenin düzgün bir şekilde gerçekleştirilebilmesi için her bir erişim anahtarının benzersiz olması gerekir [<PERSON>ha fazla bilgi](https://web.dev/accesskeys/)."}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "`[accesskey]` <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]` <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Her ARIA `role`, be<PERSON><PERSON><PERSON> bir `aria-*` özellik alt kümesini destekler. Bunların eşleşmemesi `aria-*` özelliklerini geçersiz kılar. [<PERSON>ha fazla bilgi](https://web.dev/aria-allowed-attr/)."}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "`[aria-*]` <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>r"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "`[aria-*]` özellikleri rolleriyle eşleşiyor"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "<PERSON>z<PERSON> <PERSON>, <PERSON>ğenin durumunu ekran okuyuculara açıklayan gerekli özelliklere sahiptir. [Daha fazla bilgi](https://web.dev/aria-required-attr/)."}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]` <PERSON>i gereken tüm `[aria-*]` özelliklerine <PERSON>"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]` <PERSON><PERSON><PERSON><PERSON><PERSON>, gereken tüm`[aria-*]` özelliklerine sahip"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "Bazı ARIA üst rollerinin amaçlanan erişilebilirlik işlevlerini gerçekleştirebilmek için belirli alt rolleri içermesi gerekir. [Daha fazla bilgi](https://web.dev/aria-required-children/)."}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "ARIA `[role]` sa<PERSON><PERSON> olup alt öğelerin belirli bir `[role]` içermesini gerektiren öğelerde bu gerekli alt öğelerin bazıları veya hiçbiri bulunmuyor."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "ARIA `[role]` sa<PERSON><PERSON> olup alt öğelerin belirli bir `[role]` içermesini gerektiren öğelerde gerekli alt öğelerin hepsi bulunuyor."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "Bazı ARIA alt rollerinin amaçlanan erişilebilirlik işlevlerini gerektiği gibi gerçekleştirebilmesi için belirli üst rollerin içinde bulunması gerekir. [Daha fazla bilgi](https://web.dev/aria-required-parent/)."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]` <PERSON>i gerekli üst öğelerinin içinde bulunmuyor"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]` <PERSON><PERSON><PERSON><PERSON> gerekli üst öğelerinin içinde bulunuyor"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "ARIA rollerinin amaçlanan erişilebilirlik işlevlerini gerçekleştirebilmesi için geçerli değerlere sahip olması gerekir. [Daha fazla bilgi](https://web.dev/aria-roles/)."}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]` <PERSON><PERSON><PERSON><PERSON><PERSON> ge<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "`[role]` <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Ekran okuyucular gibi yardımcı teknolojiler geçersiz değerlere sahip ARIA özelliklerini yorumlayamaz. [Daha fazla bilgi](https://web.dev/aria-valid-attr-value/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "`[aria-*]` özellikleri geçerli değerlere sahip <PERSON>ğil"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "`[aria-*]` özelliklerinin geçerli değerleri var"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "Ekran okuyucular gibi yardımcı teknolojiler geçersiz adlara sahip ARIA özelliklerini yorumlayamaz. [Daha fazla bilgi](https://web.dev/aria-valid-attr/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "`[aria-*]` özellikleri geçerli değil veya yanlış yazılmış"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "`[aria-*]` özellikleri geçerli ve yanlış yazılmamış"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "Altyazılar kimin k<PERSON>, neler dediği gibi önemli bilgiler ve konuşma dışında başka bilgiler sağlayarak ses öğelerinin işitme engelli veya işitme zorluğuna sahip kullanıcılar tarafından kullanılabilmesini sağlar. [Daha fazla bilgi](https://web.dev/audio-caption/)."}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "`<audio>` ö<PERSON><PERSON><PERSON><PERSON> `[kind=\"captions\"]` i<PERSON>eren bir `<track>` öğ<PERSON> bulunmuyor."}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "`<audio>` ö<PERSON><PERSON><PERSON><PERSON> `[kind=\"captions\"]`i<PERSON>eren bir `<track>` öğ<PERSON> bulunuyor"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Başarısız <PERSON>"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "Bir düğmenin erişilebilir özellikli bir adı yoksa ekran okuyucular bu düğmeyi yalnızca \"dü<PERSON><PERSON>\" olarak okuyacağı için bu, ekran okuyuculardan yararlanan kullanıcılar için yararlı olmaz. [Daha fazla bilgi](https://web.dev/button-name/)."}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "Düğmelerin erişilebilir adları yok"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "Düğmeler erişilebilir bir ada sahip"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "Tekrar eden içeriği atlamanın yollarını eklemek, klavye kullanıcılarının sayfada daha verimli bir şekilde gezinmesini sağlar. [Daha fazla bilgi](https://web.dev/bypass/)."}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "Bu sayfa bir ba<PERSON><PERSON><PERSON><PERSON>, atlama bağlantısı veya önemli nokta bölgesi içermiyor"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "Sayfa bir ba<PERSON><PERSON><PERSON><PERSON>, atlama bağlantısı veya önemli nokta bölgesi içeriyor"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "Birçok kullanıcı, d<PERSON>ş<PERSON>k kontrastlı metni okumakta zorlanır veya okuyamaz. [Daha fazla bilgi](https://web.dev/color-contrast/)."}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Arka plan ve ön plan renkleri yeterli kontrast oranına sa<PERSON> değil."}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "Arka plan ve ön plan renkleri yeterli kontrast oranına sahip"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "Tanım listeleri gerektiği gibi işaretlenmediğinde ekran okuyucular kafa karışıklığına yol açan veya yanlış çıkışlar sunabilir. [Daha fazla bilgi](https://web.dev/definition-list/)."}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>` ö<PERSON><PERSON>ri yalnızca gerektiği gibi sıralanmış `<dt>` ve `<dd>` gruplarını,`<script>` veya `<template>` öğelerini içermiyor."}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` ö<PERSON><PERSON>ri yalnızca gerektiği gibi sıralanmış `<dt>` ve `<dd>` gruplarını, `<script>` veya `<template>` öğelerini içeriyor."}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "<PERSON><PERSON><PERSON> listesi <PERSON> (`<dt>` ve `<dd>`) ekran okuyucular tarafından düzgün bir şekilde duyurulabilmesi için üst `<dl>` öğesinin içine yerleştirilmesi gerekir. [Daha fazla bilgi](https://web.dev/dlitem/)."}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> listesi <PERSON> `<dl>` öğ<PERSON>ri a<PERSON>ına <PERSON>"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "<PERSON><PERSON><PERSON> listesi <PERSON> `<dl>` öğ<PERSON>ri a<PERSON>ına <PERSON>"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ekran okuyucu kullanıcılarına sayfayla ilgili genel bir fikir verir ve arama motoru kullanıcılarının bir sayfanın aramalarıyla ilgili olup olmadığını belirlemeleri açısından son derece önemlidir. [<PERSON>ha fazla bilgi](https://web.dev/document-title/)."}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> `<title>` ö<PERSON><PERSON> yok"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "Doküman geçerli bir `<title>` ö<PERSON><PERSON> içeriyor"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "Kimlik özelliğinin değeri benzersiz olmalıdır. <PERSON><PERSON><PERSON>, yardımcı teknolojilerin farkına varmadan diğer örnekleri atlamasını önlemektir. [Daha fazla bilgi](https://web.dev/duplicate-id/)."}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> `[id]` özellikleri benzersiz <PERSON>"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> `[id]` özellikleri benzersiz"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "Ekran okuyucu k<PERSON>, çerçevelerin içeriklerinin açıklanması için çerçeve başlıklarından yararlanır. [Daha fazla bilgi](https://web.dev/frame-title/)."}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "`<frame>` veya `<iframe>` <PERSON><PERSON><PERSON><PERSON>n başlığı yok"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "`<frame>` veya `<iframe>` ö<PERSON><PERSON> bir başlığa sahip"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "Sayfa bir lang özelliği belirtmezse ekran okuyucu, say<PERSON><PERSON>n kullanıcı ekran okuyucuyu kurarken seçtiği varsayılan dilde olduğunu varsayar. Say<PERSON> aslında varsayılan dilde değilse ekran okuyucu, sayfan<PERSON>n metnini doğru bir şekilde duyurmayabilir. [Daha fazla bilgi](https://web.dev/html-has-lang/)."}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>` <PERSON><PERSON><PERSON><PERSON> `[lang]` ö<PERSON><PERSON><PERSON><PERSON> yok"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>` <PERSON><PERSON><PERSON> `[lang]` özelliği içeriyor"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "Geç<PERSON>li bir [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) be<PERSON><PERSON><PERSON><PERSON><PERSON>, ekran okuyucuların metni düzgün bir şekilde duyurmasına yardımcı olur. [<PERSON>ha fazla bilgi](https://web.dev/html-lang-valid/)."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>` <PERSON><PERSON><PERSON>, `[lang]` ö<PERSON><PERSON>ği için geçerli bir değeri sahip değil."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "`<html>` <PERSON><PERSON><PERSON>, `[lang]` ö<PERSON><PERSON>ği için geçerli bir değere sahip"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "Bilgilendirme amaçlı öğelerin hede<PERSON>, kısa ve açıklayıcı alternatif metinler olmalıdır. Dekoratif öğeler boş bir alt özelliğiyle yok sayılabilir. [Daha fazla bilgi](https://web.dev/image-alt/)."}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> `[alt]` özellikleri yok"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "Resim <PERSON> `[alt]` özellikleri var"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "Bir resim `<input>` dü<PERSON><PERSON>i olarak kullanılırken alternatif metin sa<PERSON>, ekran okuyucu kullanıcılarının düğmenin amacını anlamalarına yardımcı olabilir. [Daha fazla bilgi](https://web.dev/input-image-alt/)."}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">` <PERSON><PERSON><PERSON><PERSON>n `[alt]` metni yok"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">` <PERSON><PERSON><PERSON><PERSON>n `[alt]` metni var"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "Etiketler, form kontrollerinin ekran okuyucular gibi yardımcı teknolojiler tarafından düzgün bir şekilde duyurulmasını sağlar. [Daha fazla bilgi](https://web.dev/label/)."}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "Form öğelerinin ilişkili etiketleri yok"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "Form öğelerinin ilişkili etiketleri var"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "Düzen amaçlı kullanılan bir tab<PERSON>, veri <PERSON> (ör. th veya başlık öğeleri ya da özet özelliği) içermemesi gerekir. Zira bu durum, ekran okuyucu kullanıcılarının kafa karıştırıcı bir deneyim yaşamalarına neden olabilir. [Daha fazla bilgi](https://web.dev/layout-table/)."}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "<PERSON><PERSON> ama<PERSON> `<table>` <PERSON><PERSON><PERSON><PERSON> `<th>`, `<caption>` veya `[summary]` özelliğini kullanmaktan kaçınmıyor."}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "<PERSON>um amaçlı `<table>` <PERSON><PERSON><PERSON><PERSON> `<th>`, `<caption>` veya `[summary]` özelliğini kullanmaktan kaçınıyor."}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> edilebilir, ben<PERSON>iz ve odaklanılabilir bağlant<PERSON> metni (ve bağlantı olarak kullanıldığında resimler için alternatif metin), ekran okuyucu kullanıcılarına daha iyi bir gezinme deneyimi sunar. [Daha fazla bilgi](https://web.dev/link-name/)."}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "Bağlantıların ayırt edilebilir adları yok"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "Bağlantıların ayırt edilebilir adları var"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "Ekran okuyucular listeleri duyurmak için belirli bir yöntem kullanır. Liste yapısının gerektiği gibi olmasını sağlamak, ekran okuyucu çıkışının düzgün olmasına yardımcı olur. [Daha fazla bilgi](https://web.dev/list/)."}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "Listeler yalnızca `<li>` öğelerini ve komut dosyası destekleyen öğeleri (`<script>` ve `<template>`) içermiyor."}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "Listeler yalnızca `<li>` öğelerini ve komut dosyası destekleyen öğeleri (`<script>` ve `<template>`) içeriyor."}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "Ekran okuyucuların liste öğelerini (`<li>`) düzgün bir şekilde okuyabilmesi için liste öğelerinin, üst `<ul>` veya `<ol>` öğesinde yer alması gerekir. [Daha fazla bilgi](https://web.dev/listitem/)."}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "Liste öğeleri (`<li>`), `<ul>` veya `<ol>` üst öğelerinde yer almıyor."}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "Liste öğeleri (`<li>`), `<ul>` veya `<ol>` üst öğelerinde yer alıyor"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "Kullanıcılar bir sayfanın otomatik olarak yenileneceğini düşünmez ve bu işlem yeniden sayfanın üst tarafına odaklanılmasına neden olur. Bu durum, can sıkıcı veya kafa karışıklığına yol açan bir deneyime yol açabilir. [Daha fazla bilgi](https://web.dev/meta-refresh/)."}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Doküman `<meta http-equiv=\"refresh\">` kullanıyor"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "Doküman `<meta http-equiv=\"refresh\">` öğesini kullanmıyor"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "Yakınlaştırmanın devre dışı bırakılması, az gören ve bir web sayfasının içeriğini düzgün bir şekilde görebilmek için ekran büyütme özelliğinden yararlanan kullanıcılar için sorun teşkil eder. [Daha fazla bilgi](https://web.dev/meta-viewport/)."}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]`, `<meta name=\"viewport\">` öğesinde kullanılmış veya `[maximum-scale]` özelliği 5'ten küçük."}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]`, `<meta name=\"viewport\">` öğesinde kullanılmamış ve `[maximum-scale]` özelliği 5'ten küçük değil."}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "Ekran okuyucular metin dışı içeriği çeviremez. `<object>` öğelerine alternatif metin ekle<PERSON>, ekran okuyucuların ilgili öğenin ne anlama geldiğini kullanıcılara söylemesine yardımcı olur. [Daha fazla bilgi](https://web.dev/object-alt/)."}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>` <PERSON><PERSON><PERSON><PERSON><PERSON> `[alt]` metni yok"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "`<object>` <PERSON><PERSON><PERSON><PERSON><PERSON> `[alt]` metni var"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "0'dan b<PERSON><PERSON><PERSON><PERSON> bir de<PERSON>, açık bir gezinme sıralamasını belirtir. Bu durum teknik olarak geçerli olsa da yardımcı teknolojilerden yararlanan kullanıcıların genellikle sinir bozucu deneyimler yaşamalarına neden olur. [Daha fazla bilgi](https://web.dev/tabindex/)."}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "Bazı öğeler 0'dan büyük bir `[tabindex]` değeri içeriyor"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "Hiçbir öğe 0'dan büyük `[tabindex]` değeri içermiyor"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "Ekran okuyucuların tablolarda daha kolay gezinmeyi sağlayan özellikleri vardır. `[headers]` özelliğini kullanan`<td>` hücrelerinin yalnızca aynı tablodaki diğer hücrelere atıfta bulunmasını sağlamak, ekran okuyucu kullanıcılarına daha iyi bir deneyim sunabilir. [Daha fazla bilgi](https://web.dev/td-headers-attr/)."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "`<table>` öğesinde olu<PERSON> `[headers]` özelliğini kullanan hücrelerin atıfta bulunduğu öğe `id` aynı tabloda yer almıyor."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "`<table>` öğ<PERSON><PERSON> olu<PERSON> `[headers]` özelliğini kullanan hücreler, aynı tablodaki tablo hücrelerine atıfta bulunuyor."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "Ekran okuyucuların tablolarda daha kolay gezinmeyi sağlayan özellikleri vardır. Tablo başlıklarının her zaman bazı hücre kümelerine atıfta bulunmasını sağlamak, ekran okuyucu kullanıcılarına daha iyi bir deneyim sunabilir. [Daha fazla bilgi](https://web.dev/th-has-data-cells/)."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "`<th>` <PERSON><PERSON><PERSON><PERSON><PERSON> ve `[role=\"columnheader\"/\"rowheader\"]` içeren öğelerin açıkladıkları veri hücreleri yok."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "`<th>` <PERSON><PERSON><PERSON><PERSON><PERSON> ve`[role=\"columnheader\"/\"rowheader\"]` içeren öğelerin açıkladıkları veri hücreleri var."}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "Öğelerde geçerli bir [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) be<PERSON><PERSON><PERSON><PERSON><PERSON>, ekran okuyucunun metni doğru telaffuz etmesini sa<PERSON>. [Daha fazla bilgi](https://web.dev/valid-lang/)."}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "`[lang]` özellikleri geçerli bir değere sahip değil"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "`[lang]` özellikleri geçerli bir değere sahip"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "Bir videoda altyazının yer alması işitme engelli ve işitme zorluğuna sahip kullanıcıların videonun bilgilerine daha kolay erişmelerini sağlar. [Daha fazla bilgi](https://web.dev/video-caption/)."}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "`<video>` ö<PERSON><PERSON><PERSON><PERSON> `[kind=\"captions\"]` i<PERSON>eren bir `<track>` öğ<PERSON> bulunmuyor."}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "`<video>` ö<PERSON><PERSON><PERSON><PERSON> `[kind=\"captions\"]`i<PERSON>eren bir `<track>` ö<PERSON><PERSON> bulunuyor"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "<PERSON><PERSON><PERSON>, videolarla ilgili olarak diyaloğun sağlayamayacağı bilgiler (ör. yüz ifadeleri ve sahneler) sağlar. [Daha fazla bilgi](https://web.dev/video-description/)."}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "`<video>` ö<PERSON><PERSON><PERSON><PERSON> `[kind=\"description\"]` i<PERSON>eren bir `<track>` öğ<PERSON> bulunmuyor."}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "`<video>` ö<PERSON><PERSON><PERSON><PERSON> `[kind=\"description\"]`i<PERSON>eren bir `<track>` öğ<PERSON> bulunuyor"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "Kullanıcılar ana ekranlarına progresif web uygulaması eklerinde iOS'ta ideal görünüm için bir `apple-touch-icon` <PERSON><PERSON><PERSON> tanı<PERSON>n. Tanım, saydam olmayan bir 192 piksel (veya 180 piksel) kare PNG'ye götürmelidir. [<PERSON><PERSON> Fazla Bilgi](https://web.dev/apple-touch-icon/)."}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "Geçerli bir `apple-touch-icon` <PERSON><PERSON><PERSON> içermiyor"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "`apple-touch-icon-precomposed` <PERSON><PERSON><PERSON><PERSON>; `apple-touch-icon` terc<PERSON> edilir."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> bir `apple-touch-icon` sun<PERSON><PERSON>"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chrome uzantıları bu sayfanın yükleme performansını olumsuz etkilemiştir. Sayfayı gizli modda veya uzantı içermeyen bir Chrome profilinden denetlemeyi deneyin."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "Komut Dosyası Değerlendirmesi"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "Komut Dosyası Ayrıştırma"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "Toplam CPU Süresi"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "JS'y<PERSON>, der<PERSON>e ve yürütme için harcanan zamanı kısaltmayı düşünün. Daha küçük JS yüklerinin sağlanması bu konuda yardımcı olabilir. [Daha fazla bilgi](https://web.dev/bootup-time)."}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "JavaScript yürütme süresini azaltın"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "JavaScript yürütme süresi"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Büyük GIF'ler, animasyonlu içeriğin sunulmasında verimli olmaz. Ağ veri miktarından tasarruf etmek üzere animasyonlar için MPEG4/WebM videoları ve statik resimler için GIF yerine PNG/WebP kullanma seçeneğini değerlendirin. [Daha fazla bilgi](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Animasyonlu içerik için video biçimleri kullanın"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Etkileşim için hazır olma süresini kısaltmak için ekran dışı ve gizli resimleri, tüm kritik kaynakların yüklenmesi bittikten sonra gecikmeli olarak yükleme seçeneğini değerlendirin. [Daha fazla bilgi](https://web.dev/offscreen-images)."}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Ekran dışındaki resimleri ertele"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>, say<PERSON><PERSON>zda ilk boyamayı engelliyor. Kritik JS/CSS'yi satır içinde yayınlama ve kritik olmayan tüm JS/stilleri erteleme seçeneğini değerlendirin. [Daha fazla bilgi](https://web.dev/render-blocking-resources)."}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Oluşturmayı engelleyen kaynakları ortadan kaldırın"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Büyük ağ yükleri kullanıcılar için parasal maliyet anlamına gelir ve yükleme sürelerinin uzamasında yüksek bir etkiye sahiptir. [Daha fazla bilgi](https://web.dev/total-byte-weight)."}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Toplam boyut {totalBytes, number, bytes} KB'tı"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Çok büyük ağ yüklerinden kaçının"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Çok büyük ağ yüklerini önler"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "CSS dosyalarının küçültülmesi ağ yükü boyutlarını azaltabilir. [Daha fazla bilgi](https://web.dev/unminified-css)."}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "CSS'yi <PERSON>"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "JavaScript dosyalarının küçültülmesi yük boyutlarını azaltabilir ve komut dosyası ayrıştırma süresini kısaltabilir. [Daha fazla bilgi](https://web.dev/unminified-javascript)."}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "JavaScript'i küçült"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "<PERSON><PERSON>inin gereksiz yere kullandığı bayt sayısını azaltmak için, kullanılmayan kuralları stil sayfalarından kaldırın ve ekranın üst kısmında içerik için kullanılmayan CSS'nin yüklenmesini erteleyin. [Daha fazla bilgi](https://web.dev/unused-css-rules)."}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Kullanılmayan CSS'yi kaldı<PERSON>n"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "<PERSON><PERSON>ğ<PERSON>n kullandığı bayt sayısını azaltmak için kullanılmayan JavaScript'i kaldırın."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Kullanılmayan JavaScript'i kaldırın"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Uzun önbellek ömrü, say<PERSON><PERSON><PERSON><PERSON>n tekrar ziyaret edilmesi sürecini hızlandırabilir. [Daha fazla bilgi](https://web.dev/uses-long-cache-ttl)."}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 kaynak bulundu}other{# kaynak bulundu}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Statik öğeleri verimli bir önbellek politikasıyla yayınlayın"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Statik öğelerde verimli önbellek politikası kullanır"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Optimize edilmiş resimler daha hızlı yüklenir ve daha az hücresel veri kullanır. [Daha fazla bilgi](https://web.dev/uses-optimized-images)."}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Resimleri verimli bir şekilde k<PERSON>layın"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Hücresel veriden tasarruf etmek ve yükleme süresini kısaltmak için uygun boyutlu resimler sunun. [Daha fazla bilgi](https://web.dev/uses-responsive-images)."}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "<PERSON><PERSON><PERSON>uta sahip resimler"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "<PERSON><PERSON> ka<PERSON>, toplam ağ baytı sayısını en aza indirmek için sıkıştırı<PERSON>ak (gizp, deflate veya brotli) yayınlanmalıdır. [Daha fazla bilgi](https://web.dev/uses-text-compression)."}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "<PERSON><PERSON> s<PERSON>ştırmayı etkinleştirin"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "JPEG 2000, JPEG XR ve WebP gibi resim biçimleri genellikle PNG veya JPEG'den daha iyi sıkıştırma sağlar. Böylece indirme işlemleri daha hızlı tamamlanır ve veri tüketimi daha az olur. [Daha fazla bilgi](https://web.dev/uses-webp-images)."}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "Resimleri yeni nesil biçimlerde yayınlayın"}, "lighthouse-core/audits/content-width.js | description": {"message": "Uygulamanızın içeriğinin g<PERSON>ği, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> alanının genişliğiyle eşleşmiyorsa uygulamanız mobil ekranlar için optimize edilmemiş olabilir. [Daha fazla bilgi](https://web.dev/content-width)."}, "lighthouse-core/audits/content-width.js | explanation": {"message": "{innerWidth} piksel g<PERSON> boyut<PERSON>, {outerWidth} piksel pencere boyut<PERSON>la eşleşmiyor."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>, görü<PERSON>ü alanı için doğru boyutlandırılmadı"}, "lighthouse-core/audits/content-width.js | title": {"message": "İçerik görüntü alanı için doğru boyutlandırıldı"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "Aşağıdaki Kritik İstek Zincirleri, hangi kaynakların yüksek öncelikle yüklendiğini göstermektedir. Sayfa yüklemesini iyileştirmek için zincir uzunluğunu azaltma, kaynakların indirme boyutunu küçültme veya gereksiz kaynakların indirilmesini erteleme seçeneğini değerlendirin. [Daha fazla bilgi](https://web.dev/critical-request-chains)."}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 zincir bulundu}other{# zincir bulundu}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "Kritik İsteklerin Derinliğini En Aza İndirin"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "Kullanı<PERSON>dan <PERSON> / Uyarı"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "Satır"}, "lighthouse-core/audits/deprecations.js | description": {"message": "Kullanımdan kaldırılan API'ler sonunda tarayıcıdan kaldırılacaktır. [Daha fazla bilgi](https://web.dev/deprecations)."}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 uyarı bulundu}other{# uyarı bulundu}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "Kullanımdan kaldırılan API'leri kullanıyor"}, "lighthouse-core/audits/deprecations.js | title": {"message": "Kullanımdan kaldırılan API'leri içermiyor"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "Application Cache API'si kullanımdan kaldırılmıştır. [Daha fazla bilgi](https://web.dev/appcache-manifest)."}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "\"{AppCacheManifest}\" bulundu"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "<PERSON>ygulam<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "Uygulama Önbelleği Kullanılmıyor"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "Bir DOCTYPE belirlemek, tarayıcının Quirks moduna geçmesini önler. [Daha fazla bilgi](https://web.dev/doctype)."}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "DOCTYPE adı, k<PERSON><PERSON><PERSON><PERSON> harf `html` dizesi olmalıdır"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Doküman bir DOCTYPE içermelidir"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Beklenen publicId boş bir dize olmalı"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Beklenen systemId boş bir dize olmalı"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Sayfada HTML DOCTYPE eksik, bu nedenle Quirks modunu tetikliyor"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "Sayfa HTML DOCTYPE içeriyor"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "İstatistik"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> mü<PERSON><PERSON><PERSON>i, say<PERSON><PERSON><PERSON><PERSON> yaklaşık olarak 1.500'den az DOM öğesi içermesini önerir. En iyisi, 32 öğeden ve 60 alt/üst öğeden az olan bir ağaç derinliğidir. Büyük bir DOM, bellek kullanımını artırarak daha uzun [stil hesaplamalarına](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations), yol açabilir ve yüksek maliyetli [düzen yeniden düzenlemeleri](https://developers.google.com/speed/articles/reflow). [Daha fazla bilgi](https://web.dev/dom-size)."}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 öğe}other{# öğe}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Aşırı büyük bir DOM boyutundan kaçının"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Maksimum DOM Derinliği"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Tüm DOM Öğeleri"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "<PERSON><PERSON><PERSON>um Alt Öğe"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "Aşırı büyük bir DOM boyutunu önler"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "Performansı artırmak ve güvenlik açıklarını önlemek için harici bağlantılara `rel=\"noopener\"` veya `rel=\"noreferrer\"` ekleyin. [Daha fazla bilgi](https://web.dev/external-anchors-use-rel-noopener)."}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "Çapraz kökenli hedeflere bağlantılar güvenli değildir"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "Çapraz kökenli hedeflere bağlantılar güvenli"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "Bağlantı için hedef beli<PERSON> ({anchorHTML}). Köprü olarak kullanılmadıysa target=_blank öğesini kaldırmayı değerlendirin."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Kullanıcılar herhangi bir bağlam olmadan konum bilgilerini isteyen sitelere şüpheyle bakarlar veya bu istek karşısında şaşırırlar. Onun yerine isteği bir kullanıcı işlemine bağlamayı değerlendirin. [Daha fazla bilgi](https://web.dev/geolocation-on-start)."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "<PERSON><PERSON>mede coğrafi konum izni istiyor"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "<PERSON><PERSON>mede coğrafi konum izni istemiyor"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "S<PERSON>r<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "Sayfadaki tüm JavaScript kitaplıkları kullanıcı arabirimleri algılandı. [Daha fazla bilgi](https://web.dev/js-libraries)."}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "JavaScript kitaplıkları algılandı"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "`document.write()` arac<PERSON>lığıyla dinamik olarak enjekte edilen harici komut dosyaları, yavaş bağlantıdaki kullanıcılar için sayfa yüklemeyi onlarca saniye geciktirebilir. [Daha fazla bilgi](https://web.dev/no-document-write)."}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "`document.write()` öğesini kullanıyor"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "`document.write()` öğesinden kaçınıyor"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "En Yüksek Önem Derecesi"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "Kitaplık Sürümü"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "Güvenlik Açığı Sayısı"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "Bazı üçüncü taraf komut dosyaları, sald<PERSON>rganlar tarafından kolayca belirlenen ve istismar edilen bilinen güvenlik açıkları içerebilir. [Daha fazla bilgi](https://web.dev/no-vulnerable-libraries)."}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{1 güvenlik açığı algılandı}other{# güvenlik açığı algılandı}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "Bilinen güvenlik açıklarına sahip JavaScript kitaplıkları kullanıcı arabirimi içeriyor"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "Düşük"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "Orta"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "Bilinen güvenlik açıklarına sahip JavaScript kitaplıkları kullanıcı arabirimini önlüyor"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "Kullanıcılar herhangi bir bağlam olmadan bildirim göndermek isteyen sitelere şüpheyle bakarlar veya bu istek karşısında şaşırırlar. Onun yerine isteği kullanıcı hareketlerine bağlamayı değerlendirin. [Daha fazla bilgi](https://web.dev/notification-on-start)."}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "<PERSON><PERSON>mede bildirim izni istiyor"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "<PERSON><PERSON> bildirim izni istemiyor"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "Başarısız <PERSON>"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, iyi güvenlik politikasına zarar verir. [Daha fazla bilgi](https://web.dev/password-inputs-can-be-pasted-into)."}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "Kullanıcıların şifre alanlarına yapıştırmalarını önler"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "Kullanıcıların şifre alanlarına yapıştırmalarına izin verir"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokol"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2, HTTP/1.1'e kı<PERSON><PERSON> i<PERSON> b<PERSON>, <PERSON><PERSON><PERSON><PERSON>ama ve sunucu push mesajları dahil olmak üzere birçok avantaj sunar. [Daha fazla bilgi](https://web.dev/uses-http2)."}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 istek HTTP/2 üzerinden sunulmuyor}other{# istek HTTP/2 üzerinden sunulmuyor}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "Tüm kaynakları için HTTP/2 kullanmıyor"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "Kendi kaynakları için HTTP/2 kullanıyor"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Sayfanızın kaydırma performansını artırmak için dokunma ve ve tekerlek etkinliği işleyicilerini `passive` olarak işaretlemeyi değerlendirin. [Daha fazla bilgi](https://web.dev/uses-passive-event-listeners)."}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Kaydırma performansını artırmak için pasif işleyicileri kullanmıyor"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Kaydırma performansını artırmak için pasif işleyicileri kullanıyor"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "Konsola kaydedilen hatalar çözülmemiş problemleri belirtir Bunlar, ağ istek hatalarından ve diğer tarayıcı sorunlarından gelebilir. [Daha fazla bilgi](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "Tarayıcı hataları konsola kaydedildi"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "Konsola tarayıcı hatası kaydedilmedi"}, "lighthouse-core/audits/font-display.js | description": {"message": "Web yazı tipleri yüklenirken kullanıcının metni görebilmesini sağlamak için yazı tipi görüntüleme CSS özelliğinden yararlanın. [Daha fazla bilgi](https://web.dev/font-display)."}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "Web yazı tipi yüklemesi sırasında metnin görün<PERSON>r halde kalmasını sağlayın"}, "lighthouse-core/audits/font-display.js | title": {"message": "Web yazı tipi yüklenirken tüm metin gör<PERSON><PERSON><PERSON>r halde kalır"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "Lighthouse, şu URL için yazı tipi görüntüleme değerini otomatik olarak kontrol edemedi: {fontURL}."}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "En Boy Oranı (Gerçek)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "En Boy <PERSON> (Görüntülenen)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "<PERSON>sim görü<PERSON><PERSON><PERSON>e boy<PERSON>ları doğal en boy oran<PERSON><PERSON> eşleşmeli. [Daha fazla bilgi](https://web.dev/image-aspect-ratio)."}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "Yanlış en boy or<PERSON><PERSON>na sahip resimler görüntülüyor"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "Resimleri doğru en boy oran<PERSON><PERSON> görüntülüyor"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "Geçersiz resim boyutlandırma bilgileri {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "Tarayıcılar proaktif olarak kullanıcılardan uygulamanızı ana ekranlarına eklemelerini isteyebilirler ve bu da daha yüksek etkileşim sağlayabilir. [Daha fazla bilgi](https://web.dev/installable-manifest)."}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "Web uygulaması manifest dosyası yüklenebilir olma gerekliliklerini karşılamıyor"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "Web uygulaması manifest dosyası yüklenebilir olma gerekle<PERSON> karşılar"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "Güvenli olmayan URL"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "Tüm siteler (hassas veriler işlemeyenler dahi) HTTPS ile korunmalıdır . HTTPS, izinsiz kişilerin uygulamanızla kullanıcılarınız arasındaki iletişime müdahale etmelerine veya pasif olarak dinlemelerini önler ayrıca HTTP/2 ve birçok yeni web platformu için ön koşuldur. [Daha fazla bilgi](https://web.dev/is-on-https)."}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Güvenli olmayan 1 istek bulundu}other{Güvenli olmayan # istek bulundu}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "HTTPS kullanmıyor"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "HTTPS kullanıyor"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "Say<PERSON>lar<PERSON>n hü<PERSON> ağ üzerinden hızlı bir <PERSON><PERSON>, mobil cihaz kullanıcılarının iyi bir deneyim yaşamalarını sağlar. [Daha fazla bilgi](https://web.dev/load-fast-enough-for-pwa)."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "E<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hale gelme süresi: {timeInMs, number, seconds} sn."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "<PERSON><PERSON><PERSON>n simüle mobil ağda etkileşime hazır hale gelmesi {timeInMs, number, seconds} sn. sürdü"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "Sayfanız çok yavaş yükleniyor ve etkileşimli hale gelmesi 10 saniyeyi geçiyor. Nasıl iyileştireceğinizi öğrenmek için \"Performans\" bölümünde fırsatlara ve teşhise bakın."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "Sayfalar mobil ağlarda yeterince hızlı yüklenmiyor"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "Sayfalar mobil ağlarda yeterince hızlı yükleniyor"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "JS'y<PERSON><PERSON>, der<PERSON>e ve yürütme için harcanan zamanı kısaltmayı düşünün. Daha küçük JS yüklerinin sağlanması bu konuda yardımcı olabilir. [Daha fazla bilgi](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Ana iş parçacığı çalışmasını en aza indir"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "Ana iş parçacığının çalışmasını en aza indirir"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "Mümkün olduğunca fazla sayıda kullanıcıya ulaşmak için sitelerin belli başlı her tarayıcıda çalışması gerekir. [Daha fazla bilgi](https://web.dev/pwa-cross-browser)."}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "Site farklı tarayıcılarda çalışıyor"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Her bir sayfaya URL aracılığıyla derin bağlantı verilebildiğinden ve URL'lerin sosyal medyada paylaşılabilmesi için ben<PERSON>iz olduğundan emin olun. [Daha fazla bilgi](https://web.dev/pwa-each-page-has-url)."}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Her sayfa bir URL'ye sahip"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "Yavaş ağlarda bile sağa sola dokundukça geçişlerin hızlı gerçekleştiği hissedilmelidir. Bu deneyim, kullanıcının performans algısının temelinde yatar. [Daha fazla bilgi](https://web.dev/pwa-page-transitions)."}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "Sayfa geçişleri ağda takılıyorlarmış gibi hissedilmiyor"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "<PERSON><PERSON><PERSON> G<PERSON>, say<PERSON> yüklemesinin en yoğun olduğu 5 saniyelik zaman aralığında uygulamanızın kullanıcı girişine kaç milisaniye içinde yanıt vereceğine dair bir tahmindir. Gecikmeniz 50 ms.nin üzerinde olursa kullanıcılar uygulamanızın durakladığını düşünebilir. [Daha fazla bilgi](https://web.dev/estimated-input-latency)."}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "<PERSON>lk Zengin İçerikli <PERSON>, ilk metnin veya resmin boyand<PERSON> zamanı işaret eder. [Daha fazla bilgi](https://web.dev/first-contentful-paint)."}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "İlk Zengin İçerikli Boya"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "İlk CPU Boşta metriği, say<PERSON><PERSON><PERSON> ana iş par<PERSON>cığının girişi işlemek için yeterli olduğu ilk anı işaret eder.  [Daha fazla bilgi](https://web.dev/first-cpu-idle)."}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "İlk CPU Boşta"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "<PERSON><PERSON>, bir say<PERSON>ın ana i<PERSON>eri<PERSON>ini<PERSON> ne zaman gö<PERSON>ü<PERSON><PERSON>r hale geldiğini ölçer. [Daha fazla bilgi](https://web.dev/first-meaningful-paint)."}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "İlk Anlamlı Boya"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "Etkileşime hazır olma <PERSON>, say<PERSON><PERSON><PERSON> tamamen etkileşime hazır hale gelmesi için geçmesi gereken süreyi ifade eder. [Daha fazla bilgi](https://web.dev/interactive)."}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "Kullanıcılarınızın karşılaşabileceği olası maksimum İlk Giriş Gecikmesi, en uzun görevin milisaniye cinsinden süresidir. [Daha fazla bilgi](https://developers.google.com/web/updates/2018/05/first-input-delay)."}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "<PERSON><PERSON><PERSON>um Olası İlk Giriş Gecikmesi"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "<PERSON><PERSON><PERSON>, bir say<PERSON> içeriğinin görsel olarak ne kadar hızlı doldurulabildiğini gösterir. [Daha fazla bilgi](https://web.dev/speed-index)."}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "Hız İndeksi"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "Görev süresi 50 ms.yi <PERSON>, FCP ile Etkileşime Hazır Olma Süresi arasındaki tüm dönemlerin milisaniye cinsinden toplamı."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "Toplam Engellenme Süresi"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "<PERSON><PERSON> gid<PERSON><PERSON> dö<PERSON><PERSON><PERSON> s<PERSON> (RTT) performans üzerinde büyük bir etkisi vardır. Kaynağa RTT'nin fazla olması, kullanıcıya daha yakın olan sunucuların performansı iyileştirebileceğinin göstergesidir. [Daha fazla bilgi](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "lighthouse-core/audits/network-rtt.js | title": {"message": "<PERSON><PERSON>ö<PERSON>üş Süreleri"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "Sunucudan kaynaklanan gecikmeler web performansını etkileyebilir. Bir kaynakta sunucu gecikmesinin fazla olması, sunucunun aşırı yüklendiğinin veya arka uç performansının kötü olduğunun göstergesidir. [Daha fazla bilgi](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "Sunucunun Arka Uç Gecikmeleri"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "Hizmet çalışanı web uygulamanızın öngörülemeyen ağ koşullarında güvenilir olmasını sağlar. [Daha fazla bilgi](https://web.dev/offline-start-url)."}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url` çevrimdışıyken 200 koduyla yanıt vermiyor"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url` çevrimdışıyken 200 koduyla yanıt veriyor"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "Lighthouse, manifest dosyasından `start_url` öğesini okuyamadı. Bu yüzden `start_url` dokümanın URL'si olarak kabul edildi. Hata mesajı: \"{manifestWarning}\"."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "Bütçe Aşımı"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON>n miktarını ve büyüklüğünü, performans bütçesi tarafından belirlenen hedeflerin altında tutun. [Daha fazla bilgi](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 istek}other{# istek}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "Performans bütçesi"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "Zaten HTTPS kurduysanız kullanıcılarınıza güvenli web özellikleri sağlayabilmek için tüm HTTP trafiğini HTTPS'ye yönlendirdiğinizden emin olun. [Daha fazla bilgi](https://web.dev/redirects-http)."}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "HTTP trafiğini HTTPS'ye yönlendirmiyor"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "HTTP trafiğini HTTPS'ye yönlendiriyor"}, "lighthouse-core/audits/redirects.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> yüklenmesinden önce ek gecikmelere neden olur. [Daha fazla bilgi](https://web.dev/redirects)."}, "lighthouse-core/audits/redirects.js | title": {"message": "Birden çok sayfa yönlendirmesini önleyin"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "Sayfa kaynaklarının miktarı ve büyüklüğü için bütçeler belirlemek üzere bir budget.json dosyası ekleyin. [Daha fazla bilgi](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 istek • {byteCount, number, bytes} KB}other{# istek • {byteCount, number, bytes} KB}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "İstek sayısını az ve aktarma boyutlarını küçük tutun"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "<PERSON>art b<PERSON><PERSON><PERSON><PERSON><PERSON>, arama sonuçlarında hangi URL'nin gösterileceğini belirtir. [Daha fazla bilgi](https://web.dev/canonical)."}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "Birden fazla çakışan URL ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "Farklı bir alan adına <PERSON>iyo<PERSON> ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "Geçersiz URL ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Başka bir `hreflang` konumuna ({url}) yönlendiriyor"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "Göreli URL ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "İçeriğe eşdeğer bir sayfanın yerine alan adının kök URL'sine (ana sayfa) yönlendiriyor"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "Doküman geçerli bir `rel=canonical` de<PERSON><PERSON> içermiyor"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "Doküman geçerli bir `rel=canonical` öğesi içeriyor"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "12 pikselden küçük yazı tipi boyutları okunamayacak kadar küçüktür ve mobil cihaz kullanıcılarının içeriği okuyabilmek için parmaklarıyla sıkıştırma hareketi yaparak yakınlaştırmalarını gerektirir. Sayfanın %60'ından fazlasının en az 12 piksel boyutunda olmasını sağlamaya çalışın. [Daha fazla bilgi](https://web.dev/font-size)."}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} kadar okunabilir metin"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "Mobil ekranlar için optimize edilmiş görüntü alanı meta etiketi olmadığından metin okunaklı değil."}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "Metnin {decimalProportion, number, extendedPercent} kadarı çok küçük ({decimalProportionVisited, number, extendedPercent} <PERSON><PERSON>ği baz alınarak)."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "Dokümanda okunabilir yazı tipi boyutları kullanılmıyor"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "Dokümanda okunabilir yazı tipi boyutları kullanılıyor"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "hreflang ba<PERSON><PERSON><PERSON>lar<PERSON>, arama <PERSON>larına, beli<PERSON>i bir dildeki veya bölgedeki arama sonuçlarında bir sayfanın hangi sürümünün listeleneceğini bildirir. [Daha fazla bilgi](https://web.dev/hreflang)."}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "Doküman geçerli bir `hreflang` öğesi içermiyor"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "Doküman geçerli bir `hreflang` öğesi içeriyor"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "Başarısız HTTP durum kodlarına sahip olan sayfalar düzgün bir şekilde dizine eklenmeyebilir. [Daha fazla bilgi](https://web.dev/http-status-code)."}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "Sayfa başarısız bir HTTP durum koduna sahip"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "Sayfa başarılı bir HTTP durum koduna sahip"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, tarama iznine sahip olmayan arama motorları tarafından arama sonuçlarına eklenemez. [Daha fazla bilgi](https://web.dev/is-crawable)."}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "Sayfanın dizine eklenmesi engellenmiş"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "Sayfanın dizine eklenmesi engellenmemiş"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "Açıklayıcı ba<PERSON><PERSON>ı metni, arama motorlarının içeriğinizi anlamasına yardımcı olur. [Daha fazla bilgi](https://web.dev/link-text)."}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 bağlantı bulundu}other{# bağlantı bulundu}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "Bağlantılar açıklayıcı metin içermiyor"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "Bağlantılar açıklayıcı metin içeriyor"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "Yapılandırılmış verileri doğrulamak için [Yapılandırılmış Veri Test Aracı](https://search.google.com/structured-data/testing-tool/)'nı ve [Structured Data Linter](http://linter.structured-data.org/)'ı çalıştırın. [Daha fazla bilgi](https://web.dev/structured-data)."}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "Yapılandırılmış veriler geçerli"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "Sayfa içeriğini kısa ve öz bir şekilde özetlemek amacıyla arama sonuçlarına meta tanımlar eklenebilir. [Daha fazla bilgi](https://web.dev/meta-description)."}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "Açıklama metni boş."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "Doküman meta tanım içermiyor"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "Doküman meta tanım içeriyor"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "Arama motorları eklenti içeriğini dizine ekleyemez. Ayrıca birçok cihaz eklentileri kısıtlar veya desteklemez. [Daha fazla bilgi](https://web.dev/plugins)."}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>, eklenti kullanıyor"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "Doküman eklenti içermiyor"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "robots.txt dosyanız yanlış biçimlendirilmişse, tarayıcılar web sitenizin nasıl taranmasını veya dizine eklenmesini istediğinizi anlayamayabilir. [Daha fazla bilgi](https://web.dev/robots-txt)."}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Robots.txt isteği şu HTTP durumunu döndürdü: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 hata bulundu}other{# hata bulundu}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse bir robots.txt dosyasını indiremedi"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt dosyası geçerli değil"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "robots.txt dosyası geçerli"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "Düğmeler ve bağlantılar gibi etkileş<PERSON>li ö<PERSON>, başka öğelerle üst üste binmeden rahatlıkla dokunulabilecek kadar büyük olması (48x48 piksel) ve etrafında yeterli boşluk bulunması gerekir. [Daha fazla bilgi](https://web.dev/tap-targets)."}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "<PERSON><PERSON><PERSON><PERSON> hede<PERSON>rinin {decimalProportion, number, percent} ka<PERSON><PERSON> uygun boyutta"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Mobil ekranlar için optimize edilmiş görüntü alanı meta etiketi olmadığından dokunma hedefleri çok küçük"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> he<PERSON>"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Çakış<PERSON>"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> he<PERSON>a"}, "lighthouse-core/audits/service-worker.js | description": {"message": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>evrimd<PERSON>ş<PERSON>, ana ekrana ekleme ve push bildirimleri gibi pek çok Progresif Web Uygulaması özelliğini kullanmasını sağlayan teknolojidir. [Daha fazla bilgi](https://web.dev/service-worker)."}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "Sayfa bir hizmet çalışanı tarafından kontrol ediliyor ancak manifest dosyası geçerli JSON olarak ayrışmadığından `start_url` bulunamadı"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "Sayfa bir hizmet çalışanı tarafından kontrol ediliyor ancak `start_url` ({startUrl}) öğesi hizmet çalışanının kapsamında ({scopeUrl}) değil"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "Bu sayfa bir hizmet çalışanı tarafından yönetiliyor ancak manifest dosyası getirilmediğinden `start_url` öğesi bulunamadı."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "Bu kaynak bir veya daha fazla hizmet çalışanına sahip ancak sayfa ({pageUrl}) kapsam içinde değil."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "Sayfayı kontrol eden bir hizmet çalışanı ve `start_url` ö<PERSON><PERSON> kaydedilmiyor"}, "lighthouse-core/audits/service-worker.js | title": {"message": "Sayfayı kontrol eden bir hizmet çalışanı ve `start_url` ö<PERSON><PERSON> kaydediliyor"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "Temalı başlangıç ekranı, kullanıcılar uygulamanızı ana ekranlarında başlattığında yüksek kaliteli bir deneyim sağ<PERSON>. [Daha fazla bilgi](https://web.dev/splash-screen)."}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "Özel ba<PERSON>langıç ekranı için yapılandırılmadı"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "Özel başlangıç ekranı için ya<PERSON>ılandırıldı"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "Sitenizle eşleşmesi için tarayıcı adres çubuğu temalı yapılabilir. [Daha fazla bilgi](https://web.dev/themed-omnibox)."}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "<PERSON><PERSON> için tema rengi ayarlamıyor."}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "<PERSON><PERSON> i<PERSON> tema rengi a<PERSON>."}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "Ana İleti Dizisi Engelleme Süresi"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "Üçünc<PERSON>"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "Üçüncü taraf kodu, yükleme performansını önemli ölçüde etkileyebilir. Yedekli üçüncü taraf sağlayıcıların sayısını sınırlayın ve öncelikle sayfanızın yüklenmesi tamamlandıktan sonra üçüncü taraf kodunu yükleyin. [Daha fazla bilgi](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "Üçünc<PERSON> taraf kodu, ana ileti dizisini {timeInMs, number, milliseconds} ms. s<PERSON><PERSON><PERSON> engelledi"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "Üçüncü taraf kodun etkisini azaltın"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "Üçüncü <PERSON>"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "<PERSON><PERSON>, sun<PERSON><PERSON>uz<PERSON> yanıt gönderme zamanını tanımlar. [Daha fazla bilgi](https://web.dev/time-to-first-byte)."}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "Root doküman {timeInMs, number, milliseconds} ms. sürdü"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "Sun<PERSON>u yanıt sürelerini kısaltın (TTFB)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "<PERSON><PERSON>u yanıt süreleri düşük (TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "Başlangıç <PERSON>"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/user-timings.js | description": {"message": "Önemli kullanıcı deneyimleri esnasında uygulamanızın gerçek dünya performansını ölçmek için uygulamanıza User Timing API'si ekleme seçeneğini değerlendirin. [Daha fazla bilgi](https://web.dev/user-timings)."}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 kullanıcı zamanlaması}other{# kullanıcı zamanlaması}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "Kullanıcı Zamanlaması işaretleri ve ölçüleri"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "\"{security<PERSON><PERSON><PERSON>}\" i<PERSON>in bir <PERSON> bağlanma <link> <PERSON><PERSON><PERSON> bulundu ancak tarayıcı tarafından kullanılmadı. `crossorigin` özelliğini gerektiği gibi kullandığınızdan emin olun."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "Önemli üçüncü taraf kaynaklarına erken bağlantılar oluşturmak için `preconnect` veya `dns-prefetch` kaynak ipuçları ekleme seçeneğini değerlendirin. [Daha fazla bilgi](https://web.dev/uses-rel-preconnect)."}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "Gerekli kaynaklara önceden bağlan"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "\"{preloadURL}\" için bir önceden yükleme <link> bulundu ancak tarayıcı tarafından kullanılmadı. `crossorigin` özelliğini gerektiği gibi kullandığınızdan emin olun."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "Mevcut durumda sayfa yüklemesinden sonra istenen kaynakları daha önce getirmek için `<link rel=preload>` kullanmayı düşünün. [Daha fazla bilgi](https://web.dev/uses-rel-preload)."}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "Önemli istekleri <PERSON>"}, "lighthouse-core/audits/viewport.js | description": {"message": "Uygulamanızı mobil ekranlar için optimize etmek üzere `<meta name=\"viewport\">` etiketi e<PERSON>in. [Daha fazla bilgi](https://web.dev/viewport)."}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "`<meta name=\"viewport\">` et<PERSON><PERSON> bulu<PERSON>adı"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "`width` veya `initial-scale` de<PERSON><PERSON><PERSON><PERSON> olan bir `<meta name=\"viewport\">` etiketi yok"}, "lighthouse-core/audits/viewport.js | title": {"message": "`width` veya `initial-scale` de<PERSON><PERSON><PERSON><PERSON> olan bir `<meta name=\"viewport\">` etiketi var"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, JavaScript devre dışı bırakıldığında bazı içerikler görüntülemelidir. Bu içerik, kullanıcıya uygulamayı kullanmak için JavaScript gerektiğini belirten bir uyarı bile olabilir. [Daha fazla bilgi](https://web.dev/without-javascript)."}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "Komut dosyaları kullanılamıyorsa sayfa gövdesi bazı içerikler oluşturmalıdır."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "JavaScript kullanılamadığında ikame içerik sağlamıyor"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "JavaScript kullanılamadığında bazı içerikler bulundurur"}, "lighthouse-core/audits/works-offline.js | description": {"message": "Progresif Web Uygulaması oluşturuyorsanız uygulamanızın çevrimdışı da çalışabilmesi için hizmet çalışanı kullanmayı düşünün. [Daha fazla bilgi](https://web.dev/works-offline)."}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "Geçerli sayfa çevrimdışıyken 200 koduyla yanıt vermiyor"}, "lighthouse-core/audits/works-offline.js | title": {"message": "Geçerli sayfa çevrimdışıyken 200 koduyla yanıt veriyor"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "<PERSON><PERSON>, test URL'niz ({requested}) \"{final}\" adresine yönlendirildiğinden çevrimdışı yüklenmiyor olabilir. Do<PERSON><PERSON>an ikinci URL'yi test etmeyi deneyin."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, uygulamanızda ARIA kullanımının iyileştirilmesine olanak tanır. Bu da ekran okuyucu gibi yardımcı teknolojilerin kullanıcılarına daha iyi bir deneyim sunulmasını sağlayabilir."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, alternatif ses ve video içerikleri sağlanmasına olanak tanır. <PERSON><PERSON> durum, işitme veya görme bozukluğu olan kullanıcıların daha iyi bir deneyim yaşamalarını sağlayabilir."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Ses ve video"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "<PERSON><PERSON>, erişilebilirlikle ilgili yaygın en iyi uygulamaları öne çıkarır."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "En iyi u<PERSON>r"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "<PERSON><PERSON> kontroller, [web uygulamanızın erişilebilirliğini iyileştirme](https://developers.google.com/web/fundamentals/accessibility) fırsatlarını ön plana çıkarır. Erişilebilirlik sorunlarının yalnızca bir alt kümesi otomatik olarak algılanabildiğinden manuel test yapılması da önerilir."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "<PERSON><PERSON>, otomatik test aracının kapsamında yer almayan alanları ele alır. [Erişilebilirlik incelemesi gerçekleştirme](https://developers.google.com/web/fundamentals/accessibility/how-to-review) hakkında daha fazla bilgiyi rehberimizde bulabilirsiniz."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "Erişilebilirlik"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, içeriğinizin daha rahat okunmasına olanak tanır."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, içeriğinizin farklı ülkelerdeki kullanıcılar tarafından daha iyi yorumlanmasına olanak tanır."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Uluslararası hale getirme ve yerelleştirme"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, uygulamanızdaki kontrollerin anlamının iyileştirilmesine olanak tanır. Bu durum, ekran okuyucu gibi yardımcı teknolojilerin kullanıcılarına daha iyi bir deneyim sunulmasını sağlayabilir."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "<PERSON><PERSON> <PERSON>"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, uygulamanızda klavyeyle gezinmeyi kolaylaştırır."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, tablo veya liste halindeki verilerin ekran okuyucu gibi yardımcı teknolojiler kullanılarak daha rahat okunmasına olanak tanır."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> ve <PERSON>eler"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "En İ<PERSON> Uygulamalar"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "Performans b<PERSON><PERSON><PERSON><PERSON><PERSON>, sitenizin performansı için standartları belirler."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "Bütçeler"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "Uygulamanızın performansı hakkında daha fazla bilgi. Bu sayılar Performan skoruna [doğrudan etki](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) etmezler."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "Teşhis"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Performansın en kritik unsuru, piksellerin ekranda oluşturulma hızıdır. Önemli metrikler: <PERSON><PERSON>gin İçerikli <PERSON>, <PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "İlk Boya İyileştirmeleri"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> daha hızlı yüklenmesine yardımcı olabilir. Performan skoruna [doğrudan etki](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) etmezler."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Fırsatlar"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Toplam yükleme deneyimini geliştirerek sayfanın mümkün olan en kısa sürede duyarlı ve kullanıma hazır olmasını sağlayın. Önemli metrikler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Toplam İyileştirmeler"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "Performans"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "<PERSON><PERSON>, bir Progresif Web Uygulamasının çeşitli yönlerini doğrular. [Daha fazla bilgi](https://developers.google.com/web/progressive-web-apps/checklist)."}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "Temel [Progresif Web Uygulaması Yapılacaklar Listesi](https://developers.google.com/web/progressive-web-apps/checklist) tarafından zorunlu tutulan bu denetimler, Lighthouse tarafından otomatik olarak kontrol edilmez. Bunlar skorunuzu etkilemez ancak manuel olarak doğrulamanız önemlidir."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "Progresif Web Uygulaması"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "Hızlı ve güvenilir"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Yüklenebilir"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Optimize Edilmiş PWA (Progresif Web Uygulaması)"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "<PERSON><PERSON> kontroller, say<PERSON><PERSON><PERSON><PERSON><PERSON> arama motoru sonuç sıralaması için optimize edilmesini sağlar. Lighthouse tarafından kontrol edilmeyen, ancak aramadaki sıralamanızı etkileyebilecek başka faktörler de vardır. [Daha fazla bilgi](https://support.google.com/webmasters/answer/35769)."}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "SEO ile ilgili diğer en iyi uygulamaları kontrol etmek için sitenizde bu ek doğrulayıcıları çalıştırın."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "Tarayıcıların uygulamanızın içeriğini daha iyi anlamasını sağlayacak şekilde HTML kodunuzu biçimlendirin."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "İçerik En İyi Uygulamaları"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Uygulamanızın arama sonuçlarında görünmesi için, tarayıcıların uygulamanıza erişmesi gerekir."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Tarama ve Dizine Ekleme"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "Kullanıcıların küçültme veya yakınlaştırma hareketi yapmadan sayfadaki içerikleri okuyabilmelerini sağlamak için sayfalarınızın mobil uyumlu olduğundan emin olun. [Daha fazla bilgi](https://developers.google.com/search/mobile-sites/)."}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "TTL'yi <PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "Ad"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "İstek Sayısı"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "Kaynak Tü<PERSON>ü"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "{wastedBytes, number, bytes} KB potansiyel tasarruf"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "{wastedMs, number, milliseconds} ms. potansi<PERSON>l tasarruf"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "Yazı tipi"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "Resim"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms."}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} sn."}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Stil Sayfası"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Üçüncü taraf"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "Toplam"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "Sayfa yüklemenizin izlemesi kaydedilirken bir sorun oluştu. Lütfen Lighthouse'u tekrar çalıştırın. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "İlk Hata Ayıklayıcı Protokolü bağlantısı beklenirken zaman aşımı oluştu."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome, sayfa yükleme sırasında ekran görüntüsü toplayamadı. Lütfen sayfada görülebilir içerik olduğundan emin olun ve sonra Lighthouse'u tekrar çalıştırmayı deneyin. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "DNS sunucuları sağlanan alan adını çözümleyemedi."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Zorunlu {artifactName} toplayıcı bir hatayla karşılaştı: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "Dahili bir Chrome hatası oluştu. Lütfen Chrome'u yeniden başlatıp Lighthouse'u tekrar çalıştırmayı deneyin."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "Zorunlu {artifactName} toplayıcı çalışmadı."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse istediğiniz sayfayı güvenli bir şekilde yükleyemedi. Doğru URL'yi test ettiğinizden ve sunucunun tüm isteklere gerektiği gibi yanıt verdiğinden emin olun."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "Sayfa yanıt vermeyi durdurduğu için Lighthouse istediğiniz URL'yi güvenli bir şekilde yükleyemedi."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Sağladığ<PERSON><PERSON><PERSON>z URL, geçerli güvenlik sertifikasına sahip değil. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome bir geçiş reklamıyla sayfa yüklemesini önledi. Doğru URL'yi test ettiğinizden ve sunucunun tüm isteklere gerektiği gibi yanıt verdiğinden emin olun."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse istediğiniz sayfayı güvenli bir şekilde yükleyemedi. Doğru URL'yi test ettiğinizden ve sunucunun tüm isteklere gerektiği gibi yanıt verdiğinden emin olun. (Ayrıntılar: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse istediğiniz sayfayı güvenli bir şekilde yükleyemedi. Doğru URL'yi test ettiğinizden ve sunucunun tüm isteklere gerektiği gibi yanıt verdiğinden emin olun. (Durum kodu: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "<PERSON><PERSON>ın yüklenmesi çok uzun sürdü. <PERSON><PERSON><PERSON>zın yüklenme süresini azaltmak için lütfen rapordaki fırsatları uygulayın ve sonra Lighthouse'u tekrar çalıştırmayı deneyin. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "DevTools protokol yanıtının beklenmesi için ayrılan süre aşıldı. (Yöntem: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "Kaynak içeriğinin getirilmesi için ayrılan süre aşıldı"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "Sağladığınız URL'nin geçersiz olduğu anlaşılıyor."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "Denetimleri göster"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "<PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> kritik yol gecikmesi:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "Hata!"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "Bildirme hatası: denetim bilgisi yok"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "Test Verileri"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "Mevcut sayfanın mobil ağ kullanılarak gerçekleştirilen [Lighthouse](https://developers.google.com/web/tools/lighthouse/) analizi. Değerler tahminidir ve değişiklik gösterebilir."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "<PERSON> o<PERSON>ak kontrol edilecek ek öğeler"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "Geç<PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "Başarılı denetimler"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "Snippe<PERSON>'<PERSON> daralt"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "Snippe<PERSON>'<PERSON> geni<PERSON>let"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "3. <PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "Şu Lighthouse çalışmasını etkileyen sorunlar vardı:"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "De<PERSON><PERSON>ler tahminidir ve değişiklik gösterebilir. Performans skoru [sadece bu metriklere dayanır](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "Kontrollerden geçti, ancak uyarılar var"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "Uyarılar: "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "GIF dosyanızı, HTML5 videosu olarak katıştırmak için kullanılabilmesini sağlayacak bir hizmete yüklemeyi düşünün."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "Ekran dışı görüntüleri erteleme veya bu işlevselliği sağlayan bir temaya geçiş yapma olanağı veren bir [WordPress geç yükleme (lazy-load) eklentisi](https://wordpress.org/plugins/search/lazy+load/) yükleyin. Ayrıca [AMP eklentisini](https://wordpress.org/plugins/amp/) kullanmayı da değerlendirin."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "[Kritik öğeleri satır içi yapmanıza](https://wordpress.org/plugins/search/critical+css/) veya [daha az önemli kaynakları ertelemenize](https://wordpress.org/plugins/search/defer+css+javascript/) yardımcı olabilecek çeşitli WordPress eklentileri vardır. Bu eklentilerin sağlayacağı optimizasyonların tema veya eklentilerinizin özelliklerini bozabileceğine dikkat edin. Bunu engellemek için muhtemelen kodda değişiklik yapmanız gerekecektir."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "<PERSON><PERSON><PERSON>, eklentiler ve sunucu özellikleri öğelerinin tümü sunucunun yanıt süresini etkiler. Bir optimizayon eklentisini dikkatle seçerek ve/veya sunucunuzu yeni sürüme geç<PERSON>rek, daha ileri düzeyde optimize edilmiş bir tema bulmayı düşünün."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "Yayın listelerinizde alıntılar göstermeyi (ör. daha fazla etiket kullanarak), belirli bir sayfada gösterilen yayınların sayısını azaltmayı, uzun yayınlarınızı birden fazla sayfaya bölmeyi veya yorumların geç yüklenmesi için bir eklenti kullanmayı düşünün."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "Çeşitli [WordPress eklentileri](https://wordpress.org/plugins/search/minify+css/) stillerinizi sıralayarak, küçülterek ve sıkıştırarak sitenizi hızlandırabilir. Ayrıca mümkünse bu küçültme yapmak için bir derleme işlemi kullanmak isteyebilirsiniz."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "Çeşitli [WordPress eklentileri](https://wordpress.org/plugins/search/minify+javascript/) komut dosyalarınızı sıralayarak, küçülterek ve sıkıştırarak sitenizi hızlandırabilir. Ayrıca mümkünse bu sadeleştirmeyi yapmak için bir derleme işlemi kullanmak isteyebilirsiniz."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "Sayfanızda kullanılmayan CSS'ler yükleyen [WordPress eklentilerinin](https://wordpress.org/plugins/) sayısını azaltmayı veya değiştirmeyi düşünün. Gereksiz CSS ekleyen eklentileri belirlemek için Chrome Geliştirme Araçlarında [kod kapsamını](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) çalıştırmayı deneyin. Sorumlu temayı/eklentiyi stil sayfasının URL'sinden belirleyebilirsiniz. Listede çok sayıda stil sayfasına sahip olan ve kod kapsamında çok sayıda kırmızı işaret taşıyan eklentileri arayın. Bir eklenti sadece sayfada gerçekten kullanılan bir stil sayfasını kuyruğa almalıdır."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "Sayfanızda kullanılmayan JavaScript'ler yükleyen [WordPress eklentilerinin](https://wordpress.org/plugins/) sayısını azaltmayı veya değiştirmeyi değerlendirin. Gereksiz JS ekleyen eklentileri belirlemek için Chrome Geliştirme Araçlarında [kod kapsamını](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) çalıştırmayı düşünün. Sorumlu temayı/eklentiyi komut dosyasının URL'sinden belirleyebilirsiniz. Listede çok sayıda komut dosyasına sahip olan ve kod kapsamında çok sayıda kırmızı işaret taşıyan eklentileri arayın. Bir eklenti sadece sayfada gerçekten kullanılan bir komut dosyasını kuyruğa almalıdır."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "[WordPress'te <PERSON><PERSON><PERSON><PERSON>ı Önbelleği](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching) ile ilgili bilgi edinin."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "Kaliteden ödün vermeden resimlerinizi sıkıştıran bir [resim optimizasyonu WordPress eklentisi](https://wordpress.org/plugins/search/optimize+images/) kullanmayı değerlendirin."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "Gerekli resim boyutlarının kullanılabilmesi için resimleri doğrudan [medya kitaplığından](https://codex.wordpress.org/Media_Library_Screen) yükleyin, ardından ideal resim boyutlarının kullanıldığından (esnek ayrılma noktası için olan<PERSON> dahil) emin olmak için resimleri medya kitaplığından ekleyin veya resim widget'ını kullanın. Boyutları kullanım için yeterli değ<PERSON>e `Full Size` resimler kullanmaktan kaçının. [Daha fazla bilgi](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)."}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "Web sunucunuzun yapılandırmasında metin sıkıştırmayı etkinleştirebilirsiniz."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "Yüklediğiniz resimleri ideal biçimlere otomatik olarak dönüştürecek bir [eklenti](https://wordpress.org/plugins/search/convert+webp/) veya hizmet kullanmayı değerlendirin."}}