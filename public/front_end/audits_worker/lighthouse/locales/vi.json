{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "<PERSON><PERSON><PERSON> phím truy cập cho phép người dùng chuyển nhanh đến một phần của trang. Đ<PERSON> di chuyển đúng cách, mỗi phím truy cập phải là duy nhất. [Tìm hiểu thêm](https://web.dev/accesskeys/)."}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> giá trị của `[accesskey]` không phải là duy nhất"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "<PERSON><PERSON><PERSON> giá trị của `[accesskey]` là duy nhất"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Mỗi `role` của Ứng dụng Internet phong phú dễ dùng (ARIA) hỗ trợ một tập hợp con cụ thể các thuộc tính `aria-*`. Nếu không trùng khớp, các thuộc tính `aria-*` sẽ bị vô hiệu. [Tìm hiểu thêm](https://web.dev/aria-allowed-attr/)."}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> thu<PERSON> `[aria-*]` không khớp với vai trò tương <PERSON>ng"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "<PERSON><PERSON><PERSON> thu<PERSON> `[aria-*]` khớp với vai trò tương <PERSON>ng"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "Một số vai trò ARIA có các thuộc tính bắt buộc mô tả trạng thái của phần tử cho trình đọc màn hình. [Tìm hiểu thêm](https://web.dev/aria-required-attr/)."}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]` kh<PERSON><PERSON> có tất cả các thuộc t<PERSON>h `[aria-*]` b<PERSON>t buộc"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]` c<PERSON> tất cả các thuộc t<PERSON> `[aria-*]` b<PERSON>t buộc"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "Một số vai trò mẹ của Ứng dụng Internet phong phú dễ dùng (ARIA) phải chứa vai trò con cụ thể để thực hiện các chức năng hỗ trợ tiếp cận chủ định tương ứng. [Tìm hiểu thêm](https://web.dev/aria-required-children/)."}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử có `[role]` ARIA yêu cầu phần tử con phải chứa một `[role]` cụ thể hiện đang thiếu một số hoặc tất cả các phần tử con bắt buộc đó."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử có `[role]` ARIA yêu cầu phần tử con phải chứa một `[role]` cụ thể có tất cả các phần tử con bắt buộc."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "<PERSON><PERSON><PERSON> vai trò mẹ cụ thể phải chứa một số vai trò con của Ứng dụng Internet phong phú dễ dùng (ARIA) để thực hiện đúng cách các chức năng hỗ trợ tiếp cận chủ định tương ứng. [Tìm hiểu thêm](https://web.dev/aria-required-parent/)."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]` kh<PERSON><PERSON> có trong phần tử mẹ bắt buộc tương <PERSON>ng"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]` c<PERSON> trong phần tử mẹ bắt buộc tương <PERSON>ng"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "<PERSON><PERSON><PERSON> vai trò của Ứng dụng Internet phong phú dễ dùng (ARIA) phải có giá trị hợp lệ để thực hiện những chức năng hỗ trợ tiếp cận chủ định tương ứng. [Tìm hiểu thêm](https://web.dev/aria-roles/)."}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> giá trị của `[role]` l<PERSON> không hợp lệ"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "<PERSON><PERSON><PERSON> giá trị của `[role]` l<PERSON> hợp lệ"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "<PERSON><PERSON><PERSON> công nghệ hỗ trợ, chẳng hạn như trình đọc màn hình, không thể diễn gi<PERSON>i thuộc tính của Ứng dụng Internet phong phú dễ dùng (ARIA) có giá trị không hợp lệ. [Tìm hiểu thêm](https://web.dev/aria-valid-attr-value/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> trị của các thuộc t<PERSON>h `[aria-*]` là không hợp lệ"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "<PERSON><PERSON><PERSON> th<PERSON> `[aria-*]` có giá trị hợp lệ"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "<PERSON><PERSON><PERSON> công nghệ hỗ trợ, chẳng hạn như trình đọc màn hình, không thể diễn gi<PERSON>i các thuộc tính của Ứng dụng Internet phong phú dễ dùng (ARIA) có tên không hợp lệ. [Tìm hiểu thêm](https://web.dev/aria-valid-attr/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> th<PERSON> `[aria-*]` là không hợp lệ hoặc bị sai chính tả"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "<PERSON><PERSON><PERSON> `[aria-*]` là hợp lệ và không bị sai chính tả"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "<PERSON><PERSON> đề giúp người điếc hoặc người dùng khiếm thính có thể sử dụng các phần tử âm thanh, cung cấp cho họ những thông tin quan trọng như ai đang nói chuy<PERSON>, những người đó đang nói gì và các thông tin khác không phải lời nói. [Tìm hiểu thêm](https://web.dev/audio-caption/)."}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử `<audio>` bị thiếu phần tử `<track>` có `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử `<audio>` chứa phần tử `<track>` có `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "<PERSON><PERSON><PERSON> ph<PERSON>n tử không đạt"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "<PERSON>hi một nút không có tên có thể tiếp cận, trình đọc màn hình sẽ thông báo đó là \"nút\", khiến người dùng trình đọc màn hình không sử dụng được nút này. [Tìm hiểu thêm](https://web.dev/button-name/)."}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> nút không có tên có thể tiếp cận được"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "<PERSON><PERSON><PERSON> nút có tên tiếp cận đư<PERSON>c"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "Bằng việc thêm các cách bỏ qua nội dung lặp lại, người dùng bàn phím có thể khám phá trang một cách hiệu quả hơn. [Tìm hiểu thêm](https://web.dev/bypass/)."}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "<PERSON><PERSON> này không chứa tiêu đề, đường dẫn liên kết bỏ qua hoặc vùng mốc"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "<PERSON><PERSON> này chứa tiêu đề, phần tử liên kết bỏ qua hoặc vùng mốc"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "<PERSON>hiều người dùng gặp khó khăn khi đọc hoặc không thể đọc được văn bản có độ tương phản thấp. [Tìm hiểu thêm](https://web.dev/color-contrast/)."}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> nền trước và nền sau không có đủ tỷ lệ tương phản."}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "<PERSON><PERSON><PERSON> nền trước và nền sau có đủ tỷ lệ tương phản"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "<PERSON>hi danh sách định nghĩa không đượ<PERSON> đánh dấu đúng cách, thì trình đọc màn hình có thể tạo ra thông báo gây nhầm lẫn hoặc không chính xác. [Tìm hiểu thêm](https://web.dev/definition-list/)."}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>` không chỉ chứa các nhó<PERSON> `<dt>` và `<dd>` đư<PERSON>c sắp xếp đúng cách, các phần tử `<script>` hoặc `<template>`."}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` chỉ chứa các nh<PERSON> `<dt>` và `<dd>` được sắp xếp đúng cách, các phần tử `<script>` hoặc `<template>`."}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "<PERSON><PERSON><PERSON> mục trong danh sách định nghĩa (`<dt>` và `<dd>`) phải được đưa vào một phần tử `<dl>` mẹ để đảm bảo rằng trình đọc màn hình có thể thông báo đúng cách các mục này. [Tìm hiểu thêm](https://web.dev/dlitem/)."}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử `<dl>` kh<PERSON><PERSON> bao gồm những mục trong danh sách định ngh<PERSON>a"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử `<dl>` ch<PERSON><PERSON> mục trong danh sách định ngh<PERSON>a"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "Tiêu đề giúp người dùng trình đọc màn hình nắm được thông tin tổng quan về trang, và giúp người dùng công cụ tìm kiếm chủ yếu dựa vào tiêu đề này xác định xem một trang có liên quan đến nội dung tìm kiếm của họ hay không. [Tìm hiểu thêm](https://web.dev/document-title/)."}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u không có phần tử `<title>`"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u có phần tử `<title>`"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "<PERSON><PERSON><PERSON> trị của thuộc tính id phải là duy nhất, c<PERSON> như vậy công nghệ hỗ trợ mới không bỏ qua các điểm dữ liệu khác. [Tìm hiểu thêm](https://web.dev/duplicate-id/)."}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> thu<PERSON> t<PERSON> `[id]` trên trang không phải là duy nhất"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "<PERSON><PERSON><PERSON> thu<PERSON> t<PERSON> `[id]` trên trang là duy nhất"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "Người dùng trình đọc màn hình dựa vào tiêu đề khung để mô tả nội dung của khung. [Tìm hiểu thêm](https://web.dev/frame-title/)."}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "<PERSON><PERSON>n tử `<frame>` hoặc `<iframe>` không có tiêu đề"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử `<frame>` hoặc `<iframe>` có tiêu đề"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "Nếu một trang chưa chỉ định thuộc tính ngôn ngữ, thì trình đọc màn hình sẽ xem như trang đó đang hiển thị bằng ngôn ngữ mặc định mà người dùng chọn khi thiết lập trình đọc màn hình. Nếu trên thực tế, trang đó không hiển thị bằng ngôn ngữ mặc định, tức là trình đọc màn hình có thể thông báo sai về văn bản của trang đó. [Tìm hiểu thêm](https://web.dev/html-has-lang/)."}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> tử `<html>` ch<PERSON>a có thuộc t<PERSON>h `[lang]`"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "<PERSON><PERSON>n tử `<html>` có thuộc t<PERSON> `[lang]`"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "Việc chỉ định [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) hợp lệ sẽ giúp trình đọc màn hình thông báo văn bản đúng cách. [Tìm hiểu thêm](https://web.dev/html-lang-valid/)."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> `[lang]` của phần tử `<html>` không có giá trị hợp lệ."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> `[lang]` c<PERSON><PERSON> phần tử `<html>` có giá trị hợp lệ"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "<PERSON><PERSON><PERSON> phần tử thông tin nên là đoạn văn bản thay thế ngắn, có tính mô tả. C<PERSON> thể bỏ qua phần tử trang trí bằng một thuộc tính alt trống. [Tìm hiểu thêm](https://web.dev/image-alt/)."}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "Phần tử hình <PERSON>nh không có thuộc tính `[alt]`"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử hình ảnh có thuộc tính `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "<PERSON>hi dùng một hình ảnh làm nút `<input>`, thì việc cung cấp văn bản thay thế có thể giúp người dùng trình đọc màn hình hiểu rõ mục đích của nút đó. [Tìm hiểu thêm](https://web.dev/input-image-alt/)."}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử `<input type=\"image\">` không có văn bản `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "<PERSON><PERSON><PERSON> p<PERSON>ần tử `<input type=\"image\">` c<PERSON> văn bản `[alt]`"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "<PERSON><PERSON><PERSON> nhãn đảm bảo rằng những công nghệ hỗ trợ, chẳng hạn như trình đọc màn hình, thông báo các biện pháp kiểm soát biểu mẫu đúng cách. [Tìm hiểu thêm](https://web.dev/label/)."}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử biểu mẫu không có nhãn liên kết"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử biểu mẫu có nhãn liên quan"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "Bảng đang dùng cho mục đích bố cục không nên bao gồm phần tử dữ liệu, chẳng hạn như th, phần tử chú thích hoặc thuộc tính tóm tắt, vì điều này có thể tạo ra trải nghiệm gây bối rối cho người dùng trình đọc màn hình. [Tìm hiểu thêm](https://web.dev/layout-table/)."}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử `<table>` dạng trình bày không tránh sử dụng `<th>`, `<caption>` hoặc thuộc t<PERSON>h `[summary]`."}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử `<table>` dạng trình bày tránh sử dụng `<th>`, `<caption>` hoặc thuộc tính `[summary]`."}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "V<PERSON>n bản liên kết (và văn bản thay thế cho hình ảnh, khi dùng làm đường dẫn liên kết) có thể thấy rõ, duy nhất và có thể lấy tiêu điểm sẽ cải thiện trải nghiệm khám phá cho người dùng trình đọc màn hình. [Tìm hiểu thêm](https://web.dev/link-name/)."}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử liên kết không có tên có thể nhận rõ"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử liên kết có tên có thể nhận rõ"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "<PERSON><PERSON><PERSON> trình đọc màn hình có cách riêng để thông báo về danh sách. Khi danh sách có cấu trúc phù hợp, trình đọc màn hình sẽ thông báo về danh sách chính xác hơn. [Tìm hiểu thêm](https://web.dev/list/)."}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "<PERSON><PERSON> sách không chỉ chứa các phần tử `<li>` và phần tử hỗ trợ tập lệnh (`<script>` và `<template>`)."}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "<PERSON>h sách chỉ chứa các phần tử `<li>` và phần tử hỗ trợ tập lệnh (`<script>` và `<template>`)."}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "Trình đọc màn hình yêu cầu các mục danh s<PERSON>ch (`<li>`) phải có trong một `<ul>` hoặc `<ol>` mẹ để được thông báo đúng cách. [Tìm hiểu thêm](https://web.dev/listitem/)."}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> mục trong danh sách (`<li>`) không có trong phần tử `<ul>` hoặc `<ol>` mẹ."}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "<PERSON><PERSON><PERSON> mục trong danh sách (`<li>`) có trong phần tử mẹ `<ul>` hoặc `<ol>`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "Người dùng không muốn trang tự động làm mới. Khi trang tự động làm mới, tiêu điểm sẽ quay về đầu trang. Người dùng có thể cảm thấy khó chịu hoặc bị nhầm lẫn nếu gặp trường hợp này. [Tìm hiểu thêm](https://web.dev/meta-refresh/)."}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u này sử dụng `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u này không dùng `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "Vi<PERSON><PERSON> tắt tính năng thu phóng sẽ gây trở ngại cho những người dùng có thị lực kém bị lệ thuộc vào chức năng phóng to màn hình để thấy rõ nội dung trang web. [Tìm hiểu thêm](https://web.dev/meta-viewport/)."}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` đư<PERSON><PERSON> dùng trong phần tử `<meta name=\"viewport\">` hoặc thuộc tính `[maximum-scale]` nhỏ hơn 5."}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` không được sử dụng trong phần tử `<meta name=\"viewport\">` và thuộc t<PERSON>h `[maximum-scale]` không nhỏ hơn 5."}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "Trình đọc màn hình không thể dịch nội dung không ở dạng văn bản. Khi bạn thêm văn bản thay thế vào các phần tử `<object>`, trình đọc màn hình sẽ truyền đạt được ý nghĩa cho người dùng. [Tìm hiểu thêm](https://web.dev/object-alt/)."}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử `<object>` không có văn bản `[alt]`"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử `<object>` có văn bản `[alt]`"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "G<PERSON><PERSON> trị lớn hơn 0 ngụ ý thứ tự di chuyển rõ ràng. Mặc dù hợp lệ về mặt kỹ thuật nhưng điều này thường tạo ra trải nghiệm khó chịu cho những người dùng bị lệ thuộc vào công nghệ hỗ trợ. [Tìm hiểu thêm](https://web.dev/tabindex/)."}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "Một số phần tử có giá trị `[tabindex]` l<PERSON><PERSON> hơ<PERSON> 0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "<PERSON><PERSON><PERSON>ng phần tử nào có giá trị `[tabindex]` l<PERSON><PERSON> hơn 0"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "Trình đọc màn hình có các tính năng giúp người dùng dễ sử dụng bảng hơn. Việc đảm bảo các ô `<td>` sử dụng thuộc tính `[headers]` chỉ tham chiếu các ô khác trong cùng bảng có thể cải thiện trải nghiệm của người dùng trình đọc màn hình. [Tìm hiểu thêm](https://web.dev/td-headers-attr/)."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> ô trong phần tử `<table>` sử dụng thuộc tính `[headers]` tham chiếu đến một phần tử `id` không tìm thấy trong cùng một bảng."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "<PERSON><PERSON><PERSON> ô trong phần tử `<table>` sử dụng thuộc tính `[headers]` tham chiếu đến các ô trong cùng một bảng."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "Trình đọc màn hình có các tính năng giúp người dùng dễ sử dụng bảng hơn. Việc đảm bảo tiêu đề bảng luôn tham chiếu đến một nhóm ô nào đó có thể cải thiện trải nghiệm của người dùng trình đọc màn hình. [Tìm hiểu thêm](https://web.dev/th-has-data-cells/)."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử `<th>` và phần tử có `[role=\"columnheader\"/\"rowheader\"]` không chứa các ô dữ liệu mà các phần tử đó mô tả."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử `<th>` và phần tử có `[role=\"columnheader\"/\"rowheader\"]` ch<PERSON><PERSON> các ô dữ liệu mà các phần tử này mô tả."}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "Việc chỉ định một [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) hợp lệ cho các phần tử sẽ giúp trình đọc màn hình phát âm văn bản chính xác. [Tìm hiểu thêm](https://web.dev/valid-lang/)."}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> th<PERSON> `[lang]` không có giá trị hợp lệ"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "<PERSON><PERSON><PERSON> th<PERSON> `[lang]` có giá trị hợp lệ"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "Video có phụ đề có thể giúp người dùng bị khiếm thính và nặng tai dễ dàng tiếp cận nội dung video hơn. [Tìm hiểu thêm](https://web.dev/video-caption/)."}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử `<video>` không chứa phần tử `<track>` có `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử `<video>` chứa phần tử `<track>` có `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "Phần mô tả âm thanh cung cấp thông tin liên quan cho video mà phần hội thoại không thể làm đượ<PERSON>, chẳng hạn như cảnh và biểu cảm khuôn mặt. [Tìm hiểu thêm](https://web.dev/video-description/)."}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử `<video>` không chứa phần tử `<track>` có `[kind=\"description\"]`."}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử `<video>` chứa phần tử `<track>` có `[kind=\"description\"]`"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "<PERSON><PERSON><PERSON> x<PERSON>c định `apple-touch-icon` để khi người dùng thêm một ứng dụng web tiến bộ vào màn hình chính trên iOS, biểu tượng ứng dụng sẽ hiển thị chính xác. Thuộc tính này phải trỏ đến ảnh PNG vuông có kích thước 192px (hoặc 180px) ở dạng không trong suốt. [Tìm hiểu thêm](https://web.dev/apple-touch-icon/)"}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> cung cấp `apple-touch-icon` hợp lệ"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "`apple-touch-icon-precomposed` đã lỗi thời, nên dùng `apple-touch-icon`."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "<PERSON><PERSON><PERSON> cung cấp `apple-touch-icon` hợp lệ"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "<PERSON>ác tiện ích của Chrome ảnh hưởng tiêu cực đến hiệu suất tải của trang này. H<PERSON>y thử kiểm tra trang ở chế độ ẩn danh hoặc từ một hồ sơ trên Chrome không có tiện ích."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "<PERSON><PERSON><PERSON> gi<PERSON> tập l<PERSON>nh"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "<PERSON><PERSON> tích cú pháp tập lệnh"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "<PERSON><PERSON>ng thời gian c<PERSON> CPU"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "<PERSON><PERSON>y cân nhắc giảm thời gian dùng để phân tích cú pháp, biên soạn và thực thi JS. Bạn có thể giải quyết vấn đề này bằng cách phân phối các tải trọng JS nhỏ hơn. [Tìm hiểu thêm](https://web.dev/bootup-time)."}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> thời gian thực thi JavaScript"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "<PERSON>h<PERSON>i gian thực thi JavaScript"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Ảnh GIF lớn không có hiệu quả trong việc phân phối nội dung động. H<PERSON>y cân nhắc sử dụng video MPEG4/WebM cho ảnh động và PNG/WebP cho ảnh tĩnh thay vì ảnh GIF để tiết kiệm dữ liệu mạng. [Tìm hiểu thêm](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "<PERSON><PERSON> dụng các định dạng video cho nội dung động"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "<PERSON><PERSON><PERSON> cân nhắc tải từng phần các hình ảnh ẩn và nằm ngoài màn hình sau khi tải xong tất cả tài nguyên quan trọng nhằm giảm thời gian tương tác. [Tìm hiểu thêm](https://web.dev/offscreen-images)."}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "<PERSON><PERSON><PERSON> hoãn tải các hình <PERSON>nh ngoài màn hình"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "<PERSON><PERSON><PERSON> tài nguyên đang chặn lần hiển thị đầu tiên của trang. H<PERSON>y cân nhắc phân phối nội dòng JS/Biểu định kiểu xếp chồng (CSS) quan trọng và trì hoãn mọi JS/kiểu không quan trọng. [Tìm hiểu thêm](https://web.dev/render-blocking-resources)."}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Loại bỏ các tài nguyên chặn hiển thị"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Tải trọng mạng lớn gây tốn kém cho người dùng và thường khiến thời gian tải lâu. [Tìm hiểu thêm](https://web.dev/total-byte-weight)."}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "T<PERSON>ng kích thước là {totalBytes, number, bytes} KB"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> các tài nguyên lớn trên mạng"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "<PERSON>r<PERSON><PERSON> tài nguyên lớn trên mạng"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> giảm kích thước tệp <PERSON><PERSON><PERSON><PERSON> định kiểu xếp chồng (CSS) có thể giảm kích thước tải trọng mạng. [Tìm hiểu thêm](https://web.dev/unminified-css)."}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "<PERSON><PERSON><PERSON> g<PERSON>n CSS"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Vi<PERSON><PERSON> giảm kích thước tệp JavaScript có thể giảm kích thước tải trọng và thời gian phân tích cú pháp tập lệnh. [Tìm hiểu thêm](https://web.dev/unminified-javascript)."}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "<PERSON><PERSON><PERSON> g<PERSON>n JavaScript"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "<PERSON><PERSON><PERSON> x<PERSON>a các quy tắc vô hiệu khỏi biểu định kiểu và trì hoãn tải Biểu định kiểu xế<PERSON> chồng (CSS) không dùng cho nội dung trong màn hình đầu tiên để giảm số byte không cần thiết mà hoạt động mạng sử dụng. [Tìm hiểu thêm](https://web.dev/unused-css-rules)."}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "<PERSON><PERSON><PERSON> biểu định kiểu xế<PERSON> (CSS) không dùng"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Xóa JavaScript không dùng để giảm số byte tiêu tốn vào hoạt động mạng."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Xóa JavaScript không dùng"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Bộ nhớ đệm có thời gian hữu dụng dài có thể giúp tăng tốc số lượt truy cập lặp lại vào trang của bạn. [Tìm hiểu thêm](https://web.dev/uses-long-cache-ttl)."}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Đã tìm thấy 1 tài nguyên}other{Đã tìm thấy # tài nguyên}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "<PERSON><PERSON> phối các nội dung tĩnh bằng ch<PERSON>h sách bộ nhớ đệm hiệu quả"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Sử dụng ch<PERSON>h sách bộ nhớ đệm hiệu quả cho các nội dung tĩnh"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "<PERSON><PERSON>nh ảnh được tối ưu hóa sẽ tải nhanh hơn và tiêu tốn ít dữ liệu di động hơn. [Tìm hiểu thêm](https://web.dev/uses-optimized-images)."}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "<PERSON><PERSON> hóa hình <PERSON>nh hiệu quả"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "<PERSON><PERSON> phát hình ảnh có kích thước phù hợp để tiết kiệm dữ liệu di động và cải thiện thời gian tải. [Tìm hiểu thêm](https://web.dev/uses-responsive-images)."}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "<PERSON>hay đ<PERSON>i kích thư<PERSON><PERSON> hình <PERSON>nh cho phù hợp"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "<PERSON><PERSON>i phân phát các tài nguyên dựa trên văn bản ở định dạng nén (gzip, deflate hoặc brotli) để giảm thiểu tổng số byte mạng. [Tìm hiểu thêm](https://web.dev/uses-text-compression)."}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "<PERSON><PERSON><PERSON> t<PERSON>h năng nén văn bản"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "Các định dạng hình ảnh như JPEG 2000, JPEG XR và WebP thường nén tốt hơn PNG hoặc JPEG, tức là tải xuống nhanh hơn và tiêu tốn ít dữ liệu hơn. [Tìm hiểu thêm](https://web.dev/uses-webp-images)."}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "<PERSON><PERSON> phối hình ảnh ở định dạng mới và hiệu quả hơn"}, "lighthouse-core/audits/content-width.js | description": {"message": "<PERSON><PERSON><PERSON> chiều rộng của nội dung trong ứng dụng không khớp với chiều rộng của khung nh<PERSON>, thì <PERSON>ng dụng có thể không được tối ưu hóa cho màn hình thiết bị di động. [Tìm hiểu thêm](https://web.dev/content-width)"}, "lighthouse-core/audits/content-width.js | explanation": {"message": "<PERSON><PERSON><PERSON> thướ<PERSON> khung nhìn {innerWidth}px không khớp với kích thước c<PERSON>a sổ {outerWidth}px."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> của nội dung không phù hợp với khung nh<PERSON>n"}, "lighthouse-core/audits/content-width.js | title": {"message": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> của nội dung phù hợp với khung nh<PERSON>n"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "<PERSON>ác Chuỗi yêu cầu quan trọng dưới đây cho bạn biết những tài nguyên có mức độ ưu tiên cao sẽ được tải. Hãy cân nhắc giảm độ dài chuỗi, gi<PERSON><PERSON> kích thước tài nguyên tải xuống hoặc trì hoãn tải xuống các tài nguyên không cần thiết để cải thiện tốc độ tải trang. [Tìm hiểu thêm](https://web.dev/critical-request-chains)."}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Đã tìm thấy 1 chuỗi}other{Đã tìm thấy # chuỗi}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> thiểu độ sâu của các yêu cầu quan trọng"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "<PERSON><PERSON><PERSON><PERSON> dùng n<PERSON>a/<PERSON><PERSON><PERSON> báo"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "Dòng"}, "lighthouse-core/audits/deprecations.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> cùng, c<PERSON>c <PERSON> không dùng nữa sẽ bị xóa khỏi trình duyệt. [Tìm hiểu thêm](https://web.dev/deprecations)."}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Tìm thấy 1 cảnh báo}other{Tìm thấy # cảnh báo}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "Sử dụng các API không dùng nữa"}, "lighthouse-core/audits/deprecations.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>c API không dùng nữa"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "Bộ nhớ đệm của ứng dụng không còn dùng nữa. [Tìm hiểu thêm](https://web.dev/appcache-manifest)."}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "Tìm thấy \"{AppCacheManifest}\""}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> bộ nhớ đệm của ứng dụng"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> sử dụng bộ nhớ đệm của ứng dụng"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "<PERSON><PERSON> bạn chỉ định loại tài li<PERSON>, trình duy<PERSON> sẽ không chuyển sang chế độ tương thích ngược. [Tìm hiểu thêm](https://web.dev/doctype)"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Tên loại tài liệu phải là chuỗi `html` vi<PERSON><PERSON> thường"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Tài liệu phải chứa một loại tài liệu"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "publicId lẽ ra phải là một chuỗi trống"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "systemId lẽ ra phải là một chuỗi trống"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Trang thiếu loại tài liệu HTML nên đã kích hoạt chế độ tương thích ngược"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "Trang có loại tài liệu HTML"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "<PERSON><PERSON><PERSON> tử"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "<PERSON><PERSON><PERSON><PERSON> kê"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "<PERSON><PERSON><PERSON> trị"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "<PERSON><PERSON><PERSON> kỹ sư trình duyệt khuyến cáo rằng các trang không nên chứa quá 1.500 phần tử DOM. Tốt nhất là có độ sâu của cây nhỏ hơn 32 phần tử và có ít hơn 60 phần tử con/phần tử mẹ. Một DOM lớn có thể làm tăng mức sử dụng bộ nhớ, khiến [các phép tính về kiểu](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) d<PERSON><PERSON> h<PERSON>, đồng thời tạo ra [các quy trình trình bày lại bố cục](https://developers.google.com/speed/articles/reflow) tốn kém. [Tìm hiểu thêm](https://web.dev/dom-size)."}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 phần tử}other{# phần tử}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ch thư<PERSON> DOM quá lớn"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "<PERSON><PERSON> sâu DOM tối đa"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Tổng số các phần tử DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "<PERSON>ần tử con tối đa"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ch thư<PERSON> DOM quá lớn"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "<PERSON><PERSON><PERSON> ti<PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "Thêm `rel=\"noopener\"` hoặc `rel=\"noreferrer\"` vào mọi đường dẫn liên kết ngoài để cải thiện hiệu suất và ngăn chặn các lỗ hổng bảo mật. [Tìm hiểu thêm](https://web.dev/external-anchors-use-rel-noopener)."}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> đường dẫn liên kết đến các trang đích trên nhiều nguồn gốc là không an toàn"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "<PERSON><PERSON><PERSON> đường dẫn liên kết đến các trang đích trên nhiều nguồn gốc là an toàn"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "<PERSON>h<PERSON>ng thể xác định trang đích cho phần tử neo ({anchorHTML}). <PERSON><PERSON>u không dùng làm siêu liên kết, h<PERSON><PERSON> cân nhắc xóa target=_blank."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Các trang web yêu cầu vị trí của người dùng mà không cung cấp ngữ cảnh sẽ khiến người dùng không tin tưởng hoặc bối rối. <PERSON><PERSON>y cân nhắc liên kết yêu cầu với hành động của người dùng. [Tìm hiểu thêm](https://web.dev/geolocation-on-start)."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u quyền truy cập vị trí địa lý khi tải trang"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> yêu cầu quyền truy cập vị trí địa lý khi tải trang"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "<PERSON><PERSON>t hiện tất cả thư viện JavaScript ở phía giao diện người dùng trên trang này. [Tìm hiểu thêm](https://web.dev/js-libraries)"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "Thư viện JavaScript phát hiện được"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "Đ<PERSON>i với những người dùng có kết nối chậm, các tập lệnh bên ngoài tự động được đưa vào qua `document.write()` có thể làm trang tải chậm hàng chục g<PERSON>. [Tìm hiểu thêm](https://web.dev/no-document-write)."}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Sử dụng `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "Tr<PERSON>h `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "<PERSON><PERSON><PERSON> <PERSON>ộ nghiêm trọng cao nhất"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "<PERSON><PERSON><PERSON> bản thư viện"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "<PERSON><PERSON> lượng lỗ hổng"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "Một số tập lệnh của bên thứ ba có thể chứa các lỗ hổng bảo mật đã biết mà kẻ tấn công dễ dàng xác định và khai thác. [Tìm hiểu thêm](https://web.dev/no-vulnerable-libraries)."}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{<PERSON><PERSON><PERSON> hiện thấy 1 lỗ hổng}other{Phát hiện thấy # lỗ hổng}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "<PERSON><PERSON> gồm các thư viện giao diện người dùng JavaScript có những lỗ hổng bảo mật đã biết"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "<PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "<PERSON>rung bình"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> các thư viện giao diện người dùng JavaScript có lỗ hổng bảo mật đã biết"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "Các trang web gửi thông báo không có ngữ cảnh sẽ khiến người dùng không tin tưởng hoặc bối rối. H<PERSON>y cân nhắc liên kết yêu cầu với cử chỉ của người dùng. [Tìm hiểu thêm](https://web.dev/notification-on-start)."}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u quyền truy cập thông báo khi tải trang"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> yêu cầu quyền truy cập thông báo khi tải trang"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "<PERSON><PERSON><PERSON> ph<PERSON>n tử không đạt"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> ngăn dán mật khẩu sẽ làm giảm tác dụng của chính sách bảo mật hiệu quả. [Tìm hiểu thêm](https://web.dev/password-inputs-can-be-pasted-into)."}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> ng<PERSON>ời dùng dán vào các trường mật kh<PERSON>u"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "<PERSON> phép người dùng dán vào các trường mật khẩu"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "<PERSON><PERSON><PERSON> th<PERSON>"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 mang lại nhiều lợi ích hơn HTTP/1.1, trong đó có tiêu đề nhị phân, đa hợp và máy chủ tự quyết. [Tìm hiểu thêm](https://web.dev/uses-http2)."}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{Chưa phân phát 1 yêu cầu qua HTTP/2}other{Chưa phân phát # yêu cầu qua HTTP/2}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "Không sử dụng HTTP/2 cho tất cả các tài nguyên của trang web"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "Sử dụng HTTP/2 cho các tài nguyên của ch<PERSON>h trang web"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "<PERSON><PERSON><PERSON> cân nhắc đánh dấu trình xử lý sự kiện chạm và di chuyển con lăn là `passive` để cải thiện hiệu suất cuộn trên trang. [Tìm hiểu thêm](https://web.dev/uses-passive-event-listeners)."}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Không sử dụng trình nghe bị động để cải thiện hiệu suất cuộn"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "<PERSON>ử dụng trình nghe bị động để cải thiện hiệu suất cuộn"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "<PERSON><PERSON>i dung mô tả"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "Các lỗi được ghi vào bảng điều khiển là những sự cố chưa giải quyết. Những sự cố này có thể do lỗi yêu cầu mạng và các vấn đề khác của trình duyệt gây ra. [Tìm hiểu thêm](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "<PERSON><PERSON> ghi lỗi của trình duyệt vào bảng điều khiển"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "Chưa ghi lỗi nào của trình duyệt vào bảng điều khiển"}, "lighthouse-core/audits/font-display.js | description": {"message": "Sử dụng tính năng Biểu định kiểu xế<PERSON> chồng (CSS) hiển thị phông chữ để đảm bảo văn bản hiển thị với người dùng khi phông chữ web đang tải. [Tìm hiểu thêm](https://web.dev/font-display)."}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> bảo văn bản vẫn hiển thị trong khi tải phông chữ web"}, "lighthouse-core/audits/font-display.js | title": {"message": "Tất cả văn bản vẫn hiển thị trong khi tải phông chữ web"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "Lighthouse không thể tự động kiểm tra giá trị hiển thị phông chữ cho URL sau: {fontURL}."}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "Tỷ l<PERSON> khung hình (Thực tế)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Tỷ l<PERSON> khung hình (Hiển thị)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "<PERSON><PERSON><PERSON> thước hiển thị của hình ảnh phải khớp với tỷ lệ khung hình tự nhiên. [Tìm hiểu thêm](https://web.dev/image-aspect-ratio)."}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "<PERSON><PERSON>n thị hình ảnh có tỷ lệ khung hình không chính xác"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "<PERSON><PERSON>n thị hình ảnh có tỷ lệ khung hình chính xác"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "Thông tin kích thước hình <PERSON>nh không hợp lệ {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "<PERSON><PERSON><PERSON> trình duyệt có thể chủ động nhắc người dùng thêm ứng dụng của bạn vào màn hình chính để tăng mức độ tương tác. [Tìm hiểu thêm](https://web.dev/installable-manifest)"}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "Tệ<PERSON> kê khai ứng dụng web không đáp ứng các yêu cầu về khả năng cài đặt"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "Tệ<PERSON> kê khai ứng dụng web đáp ứng các yêu cầu về khả năng cài đặt"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "URL không an toàn"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "Bạn phải bảo vệ tất cả các trang web bằng HTTPS, kể cả những trang web không xử lý dữ liệu nhạy cảm. HTTPS ngăn kẻ xâm nhập can thiệp hoặc vô tình biết được nội dung trao đổi giữa ứng dụng và người dùng của bạn, đồng thời là điều kiện tiên quyết nếu bạn muốn dùng HTTP/2 và nhiều API nền tảng web mới. [Tìm hiểu thêm](https://web.dev/is-on-https)."}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Tìm thấy 1 yêu cầu không an toàn}other{Tìm thấy # yêu cầu không an toàn}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "Không sử dụng HTTPS"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "Sử dụng HTTPS"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "Tốc độ tải trang nhanh trên mạng di động giúp mang đến trải nghiệm tích cực cho người dùng thiết bị di động. [Tìm hiểu thêm](https://web.dev/load-fast-enough-for-pwa)."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "<PERSON><PERSON><PERSON><PERSON> tác ở gi<PERSON>y thứ {timeInMs, number, seconds}"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "<PERSON><PERSON><PERSON><PERSON> tác trên mạng di động mô phỏng ở gi<PERSON>y thứ {timeInMs, number, seconds}"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "Trang của bạn tải quá chậm và không tương tác trong vòng 10 giây. Hãy xem các lựa chọn tối ưu hóa và thông tin chẩn đoán trong phần \"Hiệu suất\" để tìm hiểu cách cải thiện."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "Tốc độ tải trang không đủ nhanh trên mạng di động"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "Tốc độ tải trang đủ nhanh trên mạng di động"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "<PERSON><PERSON><PERSON> cân nhắc giảm thời gian dùng để phân tích cú pháp, biên soạn và thực thi JS. Bạn có thể giải quyết vấn đề này bằng cách phân phối các tải trọng JS nhỏ hơn. [Tìm hiểu thêm](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> thiểu công việc theo chuỗi chính"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> thiểu công việc theo chuỗi chính"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "<PERSON><PERSON> tiếp cận nhiều người dùng nhất, các trang web phải hoạt động trên mọi trình duyệt chính. [Tìm hiểu thêm](https://web.dev/pwa-cross-browser)"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "Trang web hoạt động trên nhiều trình du<PERSON>t"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "<PERSON><PERSON><PERSON> bảo từng trang có thể liên kết sâu qua URL và URL là duy nhất để có thể chia sẻ trên mạng xã hội. [Tìm hiểu thêm](https://web.dev/pwa-each-page-has-url)"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Mỗi trang có một URL"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "<PERSON>u<PERSON> trình chuyển tiếp phải diễn ra ngay khi bạn nhấn vào, kể cả khi mạng chậm. Tr<PERSON>i nghiệm này là một yếu tố quan trọng quyết định cảm nhận của người dùng về hiệu suất. [Tìm hiểu thêm](https://web.dev/pwa-page-transitions)"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> dùng không cảm thấy mạng chậm khi chuyển trang"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "Thời gian chờ nhập thông tin theo ước tính là thời gian ước tính mà ứng dụng cần để phản hồi thông tin do người dùng nhập (tính bằng mili giây) trong khoảng thời gian 5 giây tải nhiều trang nhất. Nếu người dùng phải chờ quá 50 mili giây, thì ứng dụng của bạn bị coi là chạy chậm. [Tìm hiểu thêm](https://web.dev/estimated-input-latency)."}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "<PERSON>h<PERSON><PERSON> gian chờ nhập thông tin theo ước tính"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "Chỉ số Hiển thị nội dung đầu tiên đánh dấu thời điểm hiển thị văn bản hoặc hình ảnh đầu tiên. [Tìm hiểu thêm](https://web.dev/first-contentful-paint)."}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "<PERSON><PERSON><PERSON>nh có nội dung đầu tiên"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "Chỉ số CPU nhàn rỗi đầu tiên đánh dấu thời điểm đầu tiên chuỗi chính của trang không phải xử lý quá nhiều thông tin nhập vào.  [Tìm hiểu thêm](https://web.dev/first-cpu-idle)"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "CPU nhàn rỗi đầu tiên"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "Chỉ số Hiển thị nội dung đầu tiên đo lường thời điểm hiển thị nội dung chính của trang. [Tìm hiểu thêm](https://web.dev/first-meaningful-paint)."}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "<PERSON><PERSON><PERSON>nh có ý nghĩa đầu tiên"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "Thời điểm tương tác là khoảng thời gian mà trang cần để trở nên hoàn toàn tương tác. [Tìm hiểu thêm](https://web.dev/interactive)."}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> điểm tương tác"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "Thời gian phản hồi lần tương tác đầu tiên tối đa mà người dùng có thể gặp phải là thời gian thực hiện nhiệm vụ lâu nh<PERSON>t (tính bằng mili giâ<PERSON>). [Tìm hiểu thêm](https://developers.google.com/web/updates/2018/05/first-input-delay)."}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "<PERSON>h<PERSON><PERSON> gian ph<PERSON>n hồi lần tương tác đầu tiên tối đa có thể"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "Chỉ số tốc độ cho biết nội dung của một trang hiển thị nhanh chóng đến mức nào. [Tìm hiểu thêm](https://web.dev/speed-index)."}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "Chỉ số tốc độ"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "Tổng tất cả các khoảng thời gian giữa thời điểm Hiển thị nội dung đầu tiên (FCP) và Thời điểm tương tác khi thời gian nhiệm vụ vượt quá 50 mili giây được biểu thị bằng đơn vị mili giây."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "T<PERSON>ng thời gian chặn"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "Thời gian trọn vòng (RTT) của mạng có ảnh hưởng lớn đến hiệu suất. Nếu mất nhiều thời gian trọn vòng (RTT) để đến một nguồn gốc, tức là máy chủ càng gần người dùng thì hiệu quả càng cao. [Tìm hiểu thêm](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "lighthouse-core/audits/network-rtt.js | title": {"message": "<PERSON>h<PERSON><PERSON> gian trọn vòng của mạng"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "Thời gian chờ của máy chủ có thể ảnh hưởng đến hiệu suất của trang web. Nếu máy chủ của một nguồn gốc có thời gian chờ cao, tức là máy chủ bị quá tải hoặc có hiệu suất phụ trợ thấp. [Tìm hiểu thêm](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "<PERSON>h<PERSON><PERSON> gian dư<PERSON>i nền của máy chủ"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> chạy dịch vụ giúp ứng dụng web của bạn hoạt động ổn định trong điều kiện mạng chập chờn. [Tìm hiểu thêm](https://web.dev/offline-start-url)"}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url` không trả về mã trạng thái 200 khi không có mạng"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url` tr<PERSON> về mã trạng thái 200 khi không có mạng"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "Lighthouse không đọc được `start_url` trong tệp kê khai. <PERSON>, `start_url` được giả định là URL của tài liệu. Thông báo lỗi: '{manifestWarning}'."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ch"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "<PERSON><PERSON><PERSON> bảo số lượng và quy mô của yêu cầu mạng thấp hơn mục tiêu đặt ra của ngân sách hiệu suất đã cung cấp. [Tìm hiểu thêm](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 yêu cầu}other{# yêu cầu}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "<PERSON><PERSON> s<PERSON>ch hi<PERSON>"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "<PERSON><PERSON><PERSON> bạn thiết lập HTTPS từ trước, hãy nhớ chuyển hướng toàn bộ lưu lượng truy cập HTTP tới HTTPS để bật các tính năng web an toàn cho tất cả người dùng. [Tìm hiểu thêm](https://web.dev/redirects-http)"}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> chuyển hướng lưu lượ<PERSON> truy cập HTTP tới HTTPS"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "Chuyển hướ<PERSON> lưu lượ<PERSON> truy cập HTTP tới HTTPS"}, "lighthouse-core/audits/redirects.js | description": {"message": "<PERSON><PERSON><PERSON> lần chuyển hướng sẽ làm giảm tốc độ tải trang. [Tìm hiểu thêm](https://web.dev/redirects)."}, "lighthouse-core/audits/redirects.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> chuyển hướng trang nhiều lần"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "<PERSON><PERSON> thiết lập ngân sách cho số lượng và quy mô của tài nguyên trang, h<PERSON><PERSON> thêm tệp budget.json. [Tìm hiểu thêm](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 yêu cầu • {byteCount, number, bytes} KB}other{# yêu cầu • {byteCount, number, bytes} KB}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "<PERSON><PERSON><PERSON>m số lượng yêu cầu và chuyển những tài nguyên có kích thước nhỏ"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "Phần tử liên kết chính tắc đề xuất URL nào nên hiển thị trong kết quả tìm kiếm. [Tìm hiểu thêm](https://web.dev/canonical)."}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "Nhiều URL xung đột ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "Trỏ đến một miền kh<PERSON> ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "URL không hợp lệ ({url})."}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Trỏ đến một vị trí `hreflang` khác ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "URL tương đối ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "Trỏ đến URL gốc của miền (trang chủ) thay vì trang nội dung tương đương"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u không có `rel=canonical` hợp lệ"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u có `rel=canonical` hợp lệ"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "Cỡ chữ dưới 12 pixel là quá nhỏ và khó đọc, bu<PERSON><PERSON> khách truy cập trên thiết bị di động phải “chụm để thu phóng” mới đọc được. Tốt nhất là hơn 60% văn bản trên trang có cỡ chữ lớn hơn hoặc bằng 12 pixel. [Tìm hiểu thêm](https://web.dev/font-size)."}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} v<PERSON>n bản dễ đọc"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "V<PERSON>n bản không đọc được vì không có thẻ meta viewport nào được tối ưu hóa cho màn hình thiết bị di động."}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "<PERSON><PERSON><PERSON> bản có tỷ lệ {decimalProportion, number, extendedPercent} là quá nhỏ (căn cứ vào mẫu {decimalProportionVisited, number, extendedPercent})."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u không sử dụng cỡ chữ dễ đọc"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "<PERSON><PERSON><PERSON> liệu sử dụng cỡ chữ dễ đọc"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "<PERSON><PERSON><PERSON> phần tử liên kết hreflang cho công cụ tìm kiếm biết nên liệt kê phiên bản trang nào trong kết quả tìm kiếm cho một ngôn ngữ hoặc khu vực cụ thể. [Tìm hiểu thêm](https://web.dev/hreflang)."}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u không có `hreflang` hợp lệ"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u có `hreflang` hợp lệ"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "<PERSON><PERSON><PERSON> trang có mã trạng thái HTTP không thành công có thể được lập chỉ mục không chính xác. [Tìm hiểu thêm](https://web.dev/http-status-code)."}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "Trang có mã trạng thái HTTP không thành công"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "Trang có mã trạng thái HTTP thành công"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "<PERSON><PERSON><PERSON> công cụ tìm kiếm không thể đưa trang của bạn vào kết quả tìm kiếm nếu bạn không cấp cho các công cụ này quyền thu thập thông tin của trang. [Tìm hiểu thêm](https://web.dev/is-crawable)."}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "<PERSON><PERSON> bị chặn lập chỉ mục"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "<PERSON><PERSON> không bị chặn lập chỉ mục"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "<PERSON><PERSON><PERSON> bản mô tả trên đường dẫn liên kết giúp công cụ tìm kiếm hiểu được nội dung của bạn. [Tìm hiểu thêm](https://web.dev/link-text)."}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Đã tìm thấy 1 liên kết}other{Đã tìm thấy # liên kết}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> phần tử liên kết không có văn bản mô tả"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "<PERSON><PERSON><PERSON> phần tử liên kết có văn bản mô tả"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "Chạy [<PERSON><PERSON><PERSON> cụ kiểm tra dữ liệu có cấu trúc](https://search.google.com/structured-data/testing-tool/) và [Công cụ khử lỗi dữ liệu có cấu trúc](http://linter.structured-data.org/) để xác thực loại dữ liệu này. [Tìm hiểu thêm](https://web.dev/structured-data)."}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "<PERSON><PERSON> liệu có cấu trúc là hợp lệ"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "Bạn có thể thêm phần mô tả meta vào kết quả tìm kiếm để tóm tắt ngắn gọn nội dung trang. [Tìm hiểu thêm](https://web.dev/meta-description)."}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "<PERSON><PERSON><PERSON> bản mô tả hiện đang trống."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u không có phần mô tả meta"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u có phần mô tả meta"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "<PERSON><PERSON><PERSON> công cụ tìm kiếm không thể lập chỉ mục nội dung plugin và nhiều thiết bị hạn chế hoặc không hỗ trợ plugin. [Tìm hiểu thêm](https://web.dev/plugins)."}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u sử dụng plugin"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u tr<PERSON>h sử dụng plugin"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "Nếu định dạng của tệp robots.txt không đúng, thì trình thu thập thông tin có thể không hiểu được cách bạn muốn thu thập thông tin hoặc lập chỉ mục trang web của mình. [Tìm hiểu thêm](https://web.dev/robots-txt)"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "<PERSON><PERSON><PERSON> cầu robots.txt trả về trạng thái HTTP: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Đã tìm thấy 1 lỗi}other{Đã tìm thấy # lỗi}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse không tải đ<PERSON><PERSON> tệp robots.txt xuống"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt không hợp lệ"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "<PERSON>.txt hợp lệ"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "<PERSON><PERSON><PERSON> phần tử tương tác, chẳng hạn như nút và phần tử liên kết, nên có kích thước đủ lớn (48x48 pixel) và khoảng cách xung quanh các phần tử này đủ để nhấn mà không chồng chéo lên các phần tử khác. [Tìm hiểu thêm](https://web.dev/tap-targets)."}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} mục tiêu nhấn có kích thước phù hợp"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "<PERSON><PERSON><PERSON> tiêu nhấn có kích thước quá nhỏ do không có thẻ meta viewport nào được tối ưu hóa cho màn hình thiết bị di động"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> mục tiêu nhấn có kích thư<PERSON>c không phù hợp"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "<PERSON><PERSON><PERSON> ti<PERSON>u chồng chéo"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON><PERSON><PERSON> ti<PERSON>n"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "<PERSON><PERSON><PERSON> tiêu nhấn có kích thư<PERSON>c phù hợp"}, "lighthouse-core/audits/service-worker.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> chạy dịch vụ là công nghệ cho phép ứng dụng của bạn dùng nhiều tính năng của Ứng dụng web tiến bộ, chẳng hạn như hoạt động khi không có mạng, thêm vào màn hình ch<PERSON>, và thông báo đẩy. [Tìm hiểu thêm](https://web.dev/service-worker)"}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "Trang này do một trình chạy dịch vụ kiểm soát. <PERSON><PERSON>, không tìm thấy `start_url` vì tệp kê khai không thể phân tích cú pháp thành tệp JSON hợp lệ"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "Trang này do một trình chạy dịch vụ kiểm soát. <PERSON><PERSON>, `start_url` ({startUrl}) không thuộc phạm vi của trình chạy dịch vụ này ({scopeUrl})"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "Trang này do một trình chạy dịch vụ kiểm soát. <PERSON><PERSON>, không tìm thấy `start_url` vì không tìm nạp được tệp kê khai."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "<PERSON><PERSON><PERSON><PERSON> gốc này có một hoặc nhiều trình chạy dịch vụ. <PERSON><PERSON>, trang ({pageUrl}) không thuộc phạm vi của các trình chạy dịch vụ này."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "<PERSON>h<PERSON>ng đăng ký một trình chạy dịch vụ kiểm soát trang và `start_url`"}, "lighthouse-core/audits/service-worker.js | title": {"message": "<PERSON><PERSON><PERSON> ký một trình chạy dịch vụ kiểm soát trang và `start_url`"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "<PERSON><PERSON><PERSON> hình chờ có giao diện gi<PERSON>p đảm bảo người dùng có trải nghiệm chất lượng cao khi chạy ứng dụng từ màn hình chính. [Tìm hiểu thêm](https://web.dev/splash-screen)"}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> đư<PERSON><PERSON> định cấu hình cho màn hình chờ tùy chỉnh"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "<PERSON><PERSON> định cấu hình cho màn hình chờ tùy chỉnh"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "Bạn có thể đặt giao diện cho thanh địa chỉ trên trình duyệt để phù hợp với trang web của mình. [Tìm hiểu thêm](https://web.dev/themed-omnibox)"}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> đặt màu giao diện cho thanh địa chỉ."}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "Đặt màu giao diện cho thanh địa chỉ."}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "Th<PERSON>i gian chặn chuỗi chính"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "<PERSON><PERSON><PERSON>ứ ba"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "Mã của bên thứ ba có thể tác động đáng kể đến hiệu suất tải. <PERSON><PERSON><PERSON> hạn chế số nhà cung cấp bên thứ ba dư thừa và cố gắng tải mã của bên thứ ba sau khi trang của bạn hoàn thành phần lớn quá trình tải. [Tìm hiểu thêm](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "<PERSON>ã của bên thứ ba đã chặn chuỗi chính trong {timeInMs, number, milliseconds} mili giây"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> mức ảnh hưởng của mã bên thứ ba"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "<PERSON><PERSON>ệc sử dụng mã của bên thứ ba"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "Chỉ số Thời gian cho byte đầu tiên xác định thời gian máy chủ của bạn gửi phản hồi. [Tìm hiểu thêm](https://web.dev/time-to-first-byte)."}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "<PERSON><PERSON><PERSON> li<PERSON>u gốc mất {timeInMs, number, milliseconds} mili gi<PERSON>y"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> thời gian phản hồi của máy chủ (TTFB)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "<PERSON>h<PERSON><PERSON> gian ph<PERSON>n hồi của máy chủ chậm (TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/user-timings.js | description": {"message": "<PERSON><PERSON><PERSON> cân nhắc trang bị API Thời gian người dùng cho ứng dụng để đo lường hiệu suất thực tế của ứng dụng trong trải nghiệm người dùng chính. [Tìm hiểu thêm](https://web.dev/user-timings)."}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 dấu thời gian người dùng}other{# dấu thời gian người dùng}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "<PERSON><PERSON><PERSON> thời điểm cụ thể và khoảng thời gian được ghi lại bằng API Thời gian người dùng"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "T<PERSON>m thấy <link> kết n<PERSON>i trước cho \"{securityOrigin}\" nhưng trình duyệt không sử dụng phần tử liên kết này. Hãy kiểm tra để đảm bảo rằng bạn đang sử dụng thuộc tính `crossorigin` đ<PERSON>g cách."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "<PERSON><PERSON><PERSON> cân nhắc thêm các gợi ý về tài nguyên `preconnect` hoặc `dns-prefetch` để thiết lập kết nối sớm tới những nguồn gốc quan trọng của bên thứ ba. [Tìm hiểu thêm](https://web.dev/uses-rel-preconnect)"}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "<PERSON><PERSON><PERSON> n<PERSON>i trư<PERSON>c với các tên miền b<PERSON><PERSON> buộc"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "T<PERSON><PERSON> thấy <link> tả<PERSON> trước cho \"{preloadURL}\" nhưng trình duyệt không sử dụng phần tử liên kết này. Hãy kiểm tra để đảm bảo rằng bạn đang sử dụng thuộc tính `crossorigin` đ<PERSON>g cách."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "<PERSON><PERSON><PERSON> cân nhắc sử dụng `<link rel=preload>` để ưu tiên tìm nạp các tài nguyên đang được yêu cầu vào một thời điểm khác trong quá trình tải trang. [Tìm hiểu thêm](https://web.dev/uses-rel-preload)."}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> các yêu cầu ch<PERSON>h"}, "lighthouse-core/audits/viewport.js | description": {"message": "Thêm thẻ `<meta name=\"viewport\">` để tối ưu hóa ứng dụng của bạn cho màn hình thiết bị di động. [Tìm hiểu thêm](https://web.dev/viewport)"}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thẻ `<meta name=\"viewport\">`"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "Không có thẻ `<meta name=\"viewport\">` có `width` hoặc `initial-scale`"}, "lighthouse-core/audits/viewport.js | title": {"message": "Có thẻ `<meta name=\"viewport\">` có `width` hoặc `initial-scale`"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "Ứng dụng của bạn phải hiển thị một số nội dung khi JavaScript bị tắt, ngay cả khi đó chỉ là một cảnh báo cho người dùng biết phải có JavaScript mới dùng được ứng dụng. [Tìm hiểu thêm](https://web.dev/without-javascript)."}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "<PERSON>ần nội dung trang phải hiển thị một số nội dung nếu không có sẵn tập lệnh."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "Không cung cấp nội dung dự phòng khi JavaScript không có sẵn"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "<PERSON><PERSON><PERSON> một số nội dung khi JavaScript không có sẵn"}, "lighthouse-core/audits/works-offline.js | description": {"message": "<PERSON>ếu bạn đang tạo một Ứng dụng web tiến bộ, hãy cân nhắc dùng trình chạy dịch vụ để ứng dụng có thể hoạt động khi không có mạng. [Tìm hiểu thêm](https://web.dev/works-offline)"}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "Trang hiện tại không trả về mã trạng thái 200 khi không có mạng"}, "lighthouse-core/audits/works-offline.js | title": {"message": "Trang hiện tại trả về mã trạng thái 200 khi không có mạng"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "<PERSON>rang này có thể không tải được khi không có mạng vì URL kiểm tra ({requested}) đã được chuyển hướng tới \"{final}\". <PERSON><PERSON><PERSON> thử kiểm tra trực tiếp URL thứ hai."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "<PERSON><PERSON><PERSON> là những cơ hội giúp cải thiện việc sử dụng ARIA trong ứng dụng của bạn, nhờ đó có thể nâng cao trải nghiệm cho người dùng công nghệ hỗ trợ, chẳng hạn như trình đọc màn hình."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "<PERSON><PERSON><PERSON> là các cơ hội để cung cấp nội dung thay thế cho âm thanh và video. <PERSON><PERSON><PERSON><PERSON> này có thể cải thiện trải nghiệm của người dùng khiếm thính hoặc khiếm thị."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Âm thanh và video"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> mục này nêu bật các phương pháp hay nhất thường dùng cho hỗ trợ tiếp cận"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> pháp hay nhất"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "<PERSON><PERSON>c hoạt động kiểm tra này giúp xác định cơ hội [cải thiện khả năng hỗ trợ tiếp cận của ứng dụng web](https://developers.google.com/web/fundamentals/accessibility). Các hoạt động này chỉ có thể tự động phát hiện một phần vấn đề liên quan đến khả năng hỗ trợ tiếp cận, do đó, bạn nên kiểm tra cả theo cách thủ công."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "<PERSON><PERSON><PERSON> mụ<PERSON> này nằm trong vùng không thể sử dụng công cụ kiểm tra tự động. Tìm hiểu thêm trong hướng dẫn của chúng tôi về cách [đánh gi<PERSON> khả năng hỗ trợ tiếp cận](https://developers.google.com/web/fundamentals/accessibility/how-to-review)."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "Hỗ trợ tiếp cận"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "<PERSON><PERSON><PERSON> là các cơ hội gi<PERSON><PERSON> cải thiện độ dễ đọc cho nội dung của bạn."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON> tư<PERSON> phản"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "<PERSON><PERSON><PERSON> là những cơ hội giúp cải thiện khả năng diễn giải của người dùng đối với nội dung của bạn ở các ngôn ngữ khác nhau."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> tế hóa và bản địa hóa"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "<PERSON><PERSON><PERSON> là những cơ hội giúp cải thiện chức năng diễn giải ngữ nghĩa của các biện pháp kiểm soát trong ứng dụng của bạn. <PERSON><PERSON><PERSON>u này có thể nâng cao trải nghiệm cho những người dùng công nghệ hỗ trợ, chẳng hạn như trình đọc màn hình."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "<PERSON><PERSON>n và nhãn"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "<PERSON><PERSON><PERSON> là các cơ hội để cải thiện khả năng điều hướng bằng bàn phím trong ứng dụng của bạn."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "<PERSON><PERSON><PERSON> là các cơ hội để cải thiện trải nghiệm đọc dữ liệu trong bảng hoặc danh sách bằng công nghệ hỗ trợ, chẳng hạn như trình đọc màn hình."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Bảng và danh sách"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Phương ph<PERSON>p hay nhất"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "<PERSON><PERSON><PERSON>ân sách hiệu suất đặt ra tiêu chuẩn về hiệu suất của trang web."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "<PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "Thông tin bổ sung về hiệu suất của ứng dụng. Những số liệu này không [trực tiếp ảnh hưởng](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) đến <PERSON><PERSON>ểm hiệu suất."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "<PERSON><PERSON><PERSON> đ<PERSON>"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "<PERSON><PERSON><PERSON> cạnh quan trọng nhất của hiệu suất là tốc độ hiển thị pixel nhanh chóng trên màn hình. Các chỉ số chính: <PERSON><PERSON><PERSON> ảnh có nội dung đầu tiên, <PERSON><PERSON><PERSON> ảnh có ý nghĩa đầu tiên"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "<PERSON><PERSON><PERSON> thao tác để cải thiện thời gian hiển thị hình ảnh đầu tiên"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> đề xuất này có thể giúp trang của bạn tả<PERSON> n<PERSON> hơn, chứ không [trực tiếp ảnh hưởng](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) đến Điểm hiệu suất."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "<PERSON><PERSON> hội"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "Các chỉ số"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "<PERSON><PERSON>i thiện trải nghiệm tải tổng thể để trang phản hồi và sẵn sàng cho bạn sử dụng sớm nhất có thể. Các số liệu chính: Thời điểm tương tác, Chỉ số tốc độ"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "<PERSON><PERSON><PERSON> thao tác để cải thiện hiệu suất tổng thể"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "Những hoạt động kiểm tra này sẽ xác thực các khía cạnh của một Ứng dụng web tiến bộ. [Tìm hiểu thêm](https://developers.google.com/web/progressive-web-apps/checklist)."}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "<PERSON><PERSON><PERSON> là những hoạt động kiểm tra được chỉ định trong [Danh sách kiểm tra ứng dụng web tiến bộ (PWA)](https://developers.google.com/web/progressive-web-apps/checklist) c<PERSON> bản, nhưng Lighthouse không tự động kiểm tra các mục này. Các hoạt động kiểm tra này không ảnh hưởng đến điểm số của bạn, nhưng bạn nên xác minh theo cách thủ công."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "Ứng dụng web tiến bộ"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "<PERSON><PERSON>h và đáng tin cậy"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "<PERSON><PERSON> thể cài đặt"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PWA đã tối ưu hóa"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "<PERSON>ác hoạt động kiểm tra này đảm bảo rằng trang của bạn được tối ưu hóa cho thứ hạng trong kết quả của công cụ tìm kiếm. Những yếu tố bổ sung mà Lighthouse không kiểm tra có thể ảnh hưởng đến thứ hạng của bạn trong kết quả tìm kiếm. [Tìm hiểu thêm](https://support.google.com/webmasters/answer/35769)."}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "Chạy những trình xác thực bổ sung này trên trang web của bạn để xem thêm các phương pháp hay nhất dành cho quy trình Tối ưu hóa cho công cụ tìm kiếm (SEO)."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "Định dạng HTML sao cho các trình thu thập thông tin có thể hiểu rõ hơn nội dung trong ứng dụng của bạn."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "<PERSON><PERSON><PERSON> phư<PERSON><PERSON> pháp hay nhất về nội dung"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "<PERSON><PERSON> xuất hiện trong kết quả tìm kiếm, c<PERSON><PERSON> trình thu thập thông tin cần quyền truy cập vào ứng dụng của bạn."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "<PERSON><PERSON> thập thông tin và lập chỉ mục"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "<PERSON><PERSON><PERSON> bảo các trang của bạn thân thiện với thiết bị di động để người dùng có thể đọc các trang nội dung mà không cần phải chụm hoặc thu phóng. [Tìm hiểu thêm](https://developers.google.com/search/mobile-sites/)."}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "<PERSON><PERSON><PERSON> thiện với thiết bị di động"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "<PERSON>h<PERSON><PERSON> gian tồn tạ<PERSON> (TTL) của bộ nhớ đệm"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "<PERSON><PERSON> trí"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON> yêu cầu"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "<PERSON><PERSON><PERSON> tài nguyên"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Thời gian sử dụng"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "<PERSON><PERSON><PERSON> c<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "<PERSON><PERSON><PERSON><PERSON> dữ liệu có thể tiết kiệm được"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "<PERSON>h<PERSON><PERSON> gian có thể tiết kiệm được"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "<PERSON><PERSON><PERSON><PERSON> dữ liệu có thể tiết kiệm đư<PERSON><PERSON> là {wastedBytes, number, bytes} KB"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Th<PERSON><PERSON> gian có thể tiết kiệm đ<PERSON><PERSON><PERSON> là {wastedMs, number, milliseconds} mili giây"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "<PERSON><PERSON><PERSON> l<PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "Phông chữ"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "<PERSON><PERSON><PERSON> dung nghe nhìn"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} mili giây"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "K<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} gi<PERSON>y"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON> định kiểu"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "<PERSON><PERSON><PERSON>ứ ba"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "Tổng"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "Đã xảy ra lỗi khi ghi dấu vết quá trình tải trang của bạn. Vui lòng chạy lại Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "<PERSON><PERSON> hết thời gian chờ kết nối với <PERSON> thức của trình gỡ lỗi ban đầu ban đầu."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome không thu thập đ<PERSON><PERSON><PERSON> bất kỳ ảnh chụp màn hình nào trong quá trình tải trang. H<PERSON>y đảm bảo trang có nội dung hiển thị, sau đó thử chạy lại Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "Máy chủ DNS không thể phân giải miền đã cung cấp."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "<PERSON><PERSON><PERSON><PERSON> thu thập {artifactName} bắt buộc đã gặp lỗi: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "Đã xảy ra lỗi Chrome nội bộ. V<PERSON> lòng khởi động lại Chrome và thử chạy lại Lighthouse."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "<PERSON><PERSON><PERSON><PERSON> thu thập {artifactName} b<PERSON><PERSON> bu<PERSON><PERSON> không chạy."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse không thể tải trang bạn đã yêu cầu một cách đáng tin cậy. H<PERSON>y đảm bảo bạn đang kiểm tra URL chính xác và máy chủ đang phản hồi tất cả các yêu cầu đúng cách."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse không thể tải URL bạn yêu cầu một cách đáng tin cậy vì trang này đã ngừng phản hồi."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "URL bạn cung cấp không có chứng chỉ bảo mật hợp lệ. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome đã ngăn tải trang có quảng cáo xen kẽ. <PERSON><PERSON><PERSON> đảm bảo bạn đang kiểm tra URL chính xác và máy chủ đang phản hồi tất cả các yêu cầu đúng cách."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse không thể tải trang bạn đã yêu cầu một cách đáng tin cậy. <PERSON><PERSON><PERSON> đảm bảo bạn đang kiểm tra URL chính xác và máy chủ đang phản hồi tất cả các yêu cầu đúng cách. (Chi tiết: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse không thể tải trang bạn đã yêu cầu một cách đáng tin cậy. <PERSON><PERSON><PERSON> đảm bảo bạn đang kiểm tra URL chính xác và máy chủ đang phản hồi tất cả các yêu cầu đúng cách. (Mã trạng thái: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Thời gian tải trang quá lâu. <PERSON><PERSON><PERSON> thực hiện các khuyến nghị trong báo cáo để giảm thời gian tải trang rồi thử chạy lại Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "Quá trình chờ phản hồi của giao thức DevTools đã vượt quá thời gian phân bổ. (<PERSON>ư<PERSON><PERSON> thức: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "Quá trình tìm nạp nội dung tài nguyên đã vư<PERSON>t quá thời gian phân bổ"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "URL bạn cung cấp có vẻ không hợp lệ."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "<PERSON><PERSON><PERSON> thị kết quả kiểm tra"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "<PERSON><PERSON><PERSON><PERSON> hướng ban đầu"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "<PERSON><PERSON> trễ tối đa của đường dẫn quan trọng:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "Lỗi!"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "Lỗi báo cáo: không có thông tin kiểm tra"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "<PERSON><PERSON> liệu của phòng thí nghiệm"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "Kết quả phân tích [Lighthouse](https://developers.google.com/web/tools/lighthouse/) cho trang hiện tại dựa trên một mạng di động mô phỏng. Các giá trị chỉ là ước tính và có thể thay đổi."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> sung cần kiểm tra theo cách thủ công"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> dụng"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "<PERSON><PERSON> hội"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "<PERSON>h<PERSON><PERSON> lượng tiết kiệm đư<PERSON><PERSON> theo <PERSON> t<PERSON>h"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "<PERSON><PERSON> lần kiểm tra đạt yêu cầu"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "<PERSON><PERSON> gọn đoạn mã"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "Mở rộng đoạn mã"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "<PERSON><PERSON>n thị tài nguyên của bên thứ ba"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "<PERSON><PERSON> xảy ra sự cố ảnh hưởng đến lần chạy Lighthouse này:"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "<PERSON><PERSON><PERSON> giá trị chỉ là ước tính và có thể thay đổi. Điểm hiệu suất [chỉ căn cứ vào các chỉ số này](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "<PERSON><PERSON> vượt qua bài kiểm tra nhưng có cảnh báo"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "Cảnh báo: "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "<PERSON><PERSON><PERSON> cân nhắc tải tệp GIF lên một dịch vụ mà nhờ đó, tệp GIF có thể được nhúng ở dạng video HTML5."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "Cài đặt một [plugin tải từng phần của WordPress](https://wordpress.org/plugins/search/lazy+load/) để trì hoãn mọi hình ảnh ngoài màn hình, hoặc chuy<PERSON>n sang một giao diện cung cấp chức năng đó. <PERSON><PERSON><PERSON><PERSON> ra, hã<PERSON> cân nhắc sử dụng [plugin AMP (Accelerated Mobile Pages)](https://wordpress.org/plugins/amp/)."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "C<PERSON> một số plugin của WordPress có thể giúp bạn [đưa phần tử quan trọng vào nội tuyến](https://wordpress.org/plugins/search/critical+css/) hoặc [trì hoãn tài nguyên ít quan trọng hơn](https://wordpress.org/plugins/search/defer+css+javascript/). Xin lưu ý rằng những phương thức tối ưu hóa mà các plugin này cung cấp có thể làm gián đoạn tính năng của giao diện hoặc plugin, do đó, có thể bạn cần thay đổi mã."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "<PERSON><PERSON><PERSON>, plugin và thông số máy chủ đều tác động đến thời gian phản hồi của máy chủ. H<PERSON>y cân nhắc tìm một giao diện được tối ưu hóa, lựa chọn cẩn thận một plugin tối ưu hóa và/hoặc nâng cấp máy chủ."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "<PERSON><PERSON><PERSON> cân nhắc hiển thị phần trích dẫn trong danh sách bài đăng (ví dụ như qua thẻ thêm), gi<PERSON><PERSON> số lượng bài đăng hiển thị trên một trang cụ thể, chia các bài đăng dài thành nhiều trang hoặc sử dụng một plugin để tải nhận xét theo từng phần."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "Một số [plugin của WordPress](https://wordpress.org/plugins/search/minify+css/) có thể làm tăng tốc độ của trang web bằng cách ghép, gi<PERSON><PERSON> kích thước và nén kiểu. Bạn cũng có thể dùng quy trình tạo để tiến hành thu nhỏ trước, nếu có thể."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "Một số [plugin của WordPress](https://wordpress.org/plugins/search/minify+javascript/) có thể làm tăng tốc độ của trang web bằng cách ghép, gi<PERSON><PERSON> kích thước và nén tập lệnh. <PERSON><PERSON><PERSON><PERSON> ra, bạn nên dùng quy trình tạo để tiến hành thu nhỏ trước, nếu có thể."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "Hãy cân nhắc giảm hoặc chuyển đổi số lượng [plugin của WordPress](https://wordpress.org/plugins/) tải Biểu định kiểu xếp chồng (CSS) không dùng đến trong trang của bạn. Để xác định các plugin đang thêm Biểu định kiểu xếp chồng (CSS) không liên quan, hãy thử chạy [phạm vi của mã](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) trong Chrome DevTools. Bạn có thể xác định giao diện/plugin gây ra tình trạng này từ URL của biểu định kiểu. Hãy chú ý đến những plugin có nhiều biểu định kiểu trong danh sách. Những plugin này có nhiều màu đỏ trong phạm vi của mã. Một plugin chỉ nên thêm một biểu định kiểu vào hàng đợi nếu biểu định kiểu đó thực sự được sử dụng trên trang."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "Hãy cân nhắc giảm hoặc chuyển đổi số lượng [plugin của WordPress](https://wordpress.org/plugins/) tải JavaScript không dùng đến trong trang của bạn. Để xác định các plugin đang thêm JS không liên quan, hãy thử chạy [phạm vi của mã](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) trong Chrome DevTools. Bạn có thể xác định giao diện/plugin gây ra tình trạng này từ URL của tập lệnh. Hãy chú ý đến những plugin có nhiều tập lệnh trong danh sách. Những plugin này có nhiều màu đỏ trong phạm vi của mã. Một plugin chỉ nên thêm một tập lệnh vào hàng đợi nếu tập lệnh đó thực sự được dùng trên trang."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "<PERSON><PERSON><PERSON> đ<PERSON> về [Bộ nhớ đệm của trình duyệt trong WordPress](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "<PERSON><PERSON><PERSON> cân nhắc sử dụng một [plugin tối ưu hóa hình ảnh của WordPress](https://wordpress.org/plugins/search/optimize+images/) c<PERSON> khả năng nén hình ảnh mà vẫn đảm bảo chất lượng."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "Trực tiếp tải hình ảnh lên thông qua [thư viện nội dung đa phương tiện](https://codex.wordpress.org/Media_Library_Screen) để đảm bảo có các kích thước hình ảnh theo yêu cầu, sau đó chèn hình ảnh từ thư viện nội dung đa phương tiện hoặc sử dụng tiện ích hình ảnh để đảm bảo sử dụng kích thước hình ảnh tối ưu (bao gồm cả các kích thước dành cho điểm ngắt thích ứng). Tránh sử dụng hình ảnh có `Full Size` trừ khi những hình ảnh đó có kích thước phù hợp. [Tìm hiểu thêm](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)."}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "<PERSON><PERSON>n có thể bật tính năng nén văn bản trong cấu hình máy chủ web."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "<PERSON><PERSON><PERSON> cân nhắc sử dụng một [plugin](https://wordpress.org/plugins/search/convert+webp/) hoặc dịch vụ tự động chuyển các hình ảnh đã tải lên sang định dạng tối ưu."}}