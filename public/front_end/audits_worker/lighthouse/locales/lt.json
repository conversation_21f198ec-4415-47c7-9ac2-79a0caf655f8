{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "Naudodami prieigos raktus naudotojai gali greitai suaktyvinti puslapio dalį. Kad nar<PERSON><PERSON>as veikt<PERSON> tin<PERSON>, kiekvienas prieigos raktas turi būti unikalus. [Sužinokite daugiau](https://web.dev/accesskeys/)."}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Elemento „`[accesskey]`“ vertės nėra unikalios"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "„`[accesskey]`“ vert<PERSON><PERSON> yra un<PERSON>s"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Kiekviename ARIA elemente „`role`“ palaikoma tik dalis konkrečių atributų „`aria-*`“. Jei jie nebus suderinti, atributai „`aria-*`“ taps netinkami. [Sužinokite daugiau](https://web.dev/aria-allowed-attr/)."}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Atributai „`[aria-*]`“ neatitinka savo vaidmenų"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Atributai „`[aria-*]`“ atitinka savo vaidmenis"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "Kai kuriems ARIA vaidmenims reikia atributų, aprašančių elementų būseną ekrano skaitytuvams. [Sužinokite daugiau](https://web.dev/aria-required-attr/)."}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Elementuose „`[role]`“ nėra visų privalomų atributų „`[aria-*]`“"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "Elementuose „`[role]`“ yra visi būtini atributai „`[aria-*]`“"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "<PERSON> kuriuose ARIA pirminiuose vaidmenyse turi būti konkrečių antrinių vaidmenų, kad būtų galima atlikti numatytas pritaikomumo funkcijas. [Sužinokite daugiau](https://web.dev/aria-required-children/)."}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Elementuose su <PERSON> vaidmeniu „`[role]`“, kurių antriniuose elementuose turi būti nurodytas konkretus „`[role]`“, trūksta kai kurių arba visų būtinų antrinių elementų."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "Elementuose su <PERSON> vaid<PERSON>iu „`[role]`“, kurių antriniuose elementuose turi būti nurodytas konkretus „`[role]`“, yra visi būtini antriniai elementai."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "<PERSON> kurie ARIA antriniai vaidmenys turi būti konkrečiuose pirminiuose vaidmenyse, kad būt<PERSON> tinkamai vykdomos numatytosios pritaikomumo funkcijos. [Sužinokite daugiau](https://web.dev/aria-required-parent/)."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Elementų „`[role]`“ n<PERSON>ra būtiname pirminiame elemente"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "Elementai „`[role]`“ yra bū<PERSON>me pirminiame elemente"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "ARIA vaidmenų vertės turi būti <PERSON>, kad būt<PERSON> galima atlikti numatytas pritaikomumo funkcijas. [Sužinokite daugiau](https://web.dev/aria-roles/)."}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "Elemento „`[role]`“ vert<PERSON>s yra net<PERSON>s"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "Element<PERSON> „`[role]`“ vert<PERSON>s yra tin<PERSON>mos"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Pagalbinės technologijos, pvz., e<PERSON>no <PERSON>, negali interpretuoti ARIA atributų su netinkamomis vertėmis. [Sužinokite daugiau](https://web.dev/aria-valid-attr-value/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Atributuose „`[aria-*]`“ nėra tinkamų verčių"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Atribut<PERSON> „`[aria-*]`“ vert<PERSON>s yra tin<PERSON>mos"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "Pagalbinės technologijos, pvz., e<PERSON><PERSON>, negali tinkamai interpretuoti ARIA atributų su netinkamais pavadinimais. [Sužinokite daugiau](https://web.dev/aria-valid-attr/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Atributai „`[aria-*]`“ netinkami arba yra rašybos klaidų"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "Atributai „`[aria-*]`“ yra <PERSON>, rašybos klaidų nėra"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> antraštes garso įrašų elementus gali naudoti kurtieji arba sutrikusios klausos naudotojai. Jiems teikiama svarbi informacija, pvz., kas kalba, kas sakoma ir kita nekalbinė informacija. [Sužinokite daugiau](https://web.dev/audio-caption/)."}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "Elementuose „`<audio>`“ trūksta elemento „`<track>`“ su atributu „`[kind=\"captions\"]`“."}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "Elementuose „`<audio>`“ yra elementas „`<track>`“ su atributu „`[kind=\"captions\"]`“"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Netinkami elementai"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "<PERSON> neįgaliesiems pritaikytas mygtuk<PERSON>, e<PERSON>no skaitytu<PERSON>i apie jį praneša kaip „button“ (mygtukas), to<PERSON><PERSON><PERSON> jo negali naudoti ekrano skaitytuvo naudotojai. [Sužinokite daugiau](https://web.dev/button-name/)."}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "Nenurodyti neįgaliesiems pritaikyti mygtukų pavadinimai"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "Mygtukų pavadinimai pritaikyti neįgaliesiems"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "Prid<PERSON><PERSON><PERSON> būd<PERSON> apeiti pasikartojantį turinį, klaviatūros naudotojai galės efektyviau naršyti puslapį. [Sužinokite daugiau](https://web.dev/bypass/)."}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "Puslapyje nėra <PERSON>, praleidimo nuorodos arba orientyro regiono"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "Puslapyje yra ant<PERSON>š<PERSON>ė, praleidžiama nuoroda arba orientyro regionas"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "<PERSON><PERSON><PERSON> k<PERSON> tekst<PERSON> daugumai naudotojų yra sudėtinga arba neįmanoma perskaityti. [Sužinokite daugiau](https://web.dev/color-contrast/)."}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Nepakankamas fono ir priekinio fono spalvų kontrasto <PERSON>."}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "Fono ir priekinio plano spalvų kontrasto santyk<PERSON> paka<PERSON>"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "<PERSON> s<PERSON>šai netinkamai p<PERSON>, ekrano skaitytuvai gali pateikti klaidinančią arba netikslią išvestį. [Sužinokite daugiau](https://web.dev/definition-list/)."}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "Elementuose „`<dl>`“ nurodytos ne tik tinkamai surikiuotos grupės „`<dt>`“ ir „`<dd>`“ bei elementai „`<script>`“ arba „`<template>`“."}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "Elementuose „`<dl>`“ nurodytos tik tinkamai surikiuotos grupės „`<dt>`“ ir „`<dd>`“ ir elementai „`<script>`“ arba „`<template>`“."}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "<PERSON>a<PERSON><PERSON> s<PERSON> element<PERSON> („`<dt>`“ ir „`<dd>`“) turi būti sujungti pirminiame elemente „`<dl>`“, kad ekrano skaitytuvai galėtų apie juos tinkamai pranešti. [Sužinokite daugiau](https://web.dev/dlitem/)."}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "Apr<PERSON>šo sąrašo elementai nesujungti elementuose „`<dl>`“"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "Apr<PERSON>šo sąrašo elementai sujungti elementuose „`<dl>`“"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "<PERSON><PERSON> pavadinimą ekrano skaitytuvo naudotojai su<PERSON>, apie ką yra puslapio turiny<PERSON>, o paieškos variklio naudotojai gali nuspręsti, ar puslapis atitinka jų paiešką. [Sužinokite daugiau](https://web.dev/document-title/)."}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "Dokumente nėra elemento „`<title>`“"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "Dokumente yra elementas „`<title>`“"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "Atributo „id“ vertė turi būti <PERSON>, kad pagalbinės technologijos nepraleistų kitų objektų. [Sužinokite daugiau](https://web.dev/duplicate-id/)."}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "Puslapio atributai „`[id]`“ nėra unikalūs"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "Puslapio atributai „`[id]`“ yra unikalūs"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "Pagal rėmelių pavadinimus ekrano skaitytuvų naudotojai suž<PERSON>, koks yra rėmelių turinys. [Sužinokite daugiau](https://web.dev/frame-title/)."}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "Elementas „`<frame>`“ arba „`<iframe>`“ neturi pavadinimo"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "Elementai „`<frame>`“ arba „`<iframe>`“ turi pavadin<PERSON>ą"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "Jei puslapyje nenurodytas atributas „lang“, ekrano skaitytuvas manys, kad puslapio kalba atitinka naudotojo pasirinktą numatytąją kalbą, kai buvo nustatomas ekrano skaitytuvas. Jei puslapio kalba neatitinka numatytosios kalbos, gal<PERSON> b<PERSON><PERSON>, kad ekrano skaitytuvas netinkamai skaitys puslapio tekstą. [Sužinokite daugiau](https://web.dev/html-has-lang/)."}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Elemente „`<html>`“ nėra atributo „`[lang]`“"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "Elemente „`<html>`“ yra atributas „`[lang]`“"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> tinkamą [BCP 47 kalbą](https://www.w3.org/International/questions/qa-choosing-language-tags#question) ekrano skaitytuvai gali tinkamai pranešti tekstą. [Sužinokite daugiau](https://web.dev/html-lang-valid/)."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Elemente „`<html>`“ nenurodyta tinkama atributo „`[lang]`“ vertė."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "Elemento „`<html>`“ atributo „`[lang]`“ vertė yra tinkama"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "Informaciniuose elementuose turėtų būti pateiktas trumpas, aprašomasis alternatyvus tekstas. Dekoratyvinių elementų galima nepaisyti nurodžius tuščią alternatyvų atributą. [Sužinokite daugiau](https://web.dev/image-alt/)."}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "Vaizdo elementuose nėra atributų „`[alt]`“"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "Vaizdo elementuose yra atributų „`[alt]`“"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "Kai vaizdas naudojamas kaip mygtukas „`<input>`“, pateikus alternatyvų tekstą ekrano skaitytuvo naudotojams bus lengviau suprasti mygtuko tikslą. [Sužinokite daugiau](https://web.dev/input-image-alt/)."}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Elementuose „`<input type=\"image\">`“ n<PERSON>ra „`[alt]`“ teksto"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "Elementuose „`<input type=\"image\">`“ yra „`[alt]`“ teksto"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "Etiket<PERSON><PERSON>, kad p<PERSON><PERSON> technologijos, pvz., ekrano <PERSON>, tinkamai praneštų apie formų valdiklius. [Sužinokite daugiau](https://web.dev/label/)."}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "Nėra susietų formos elementų etikečių"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "Formos elementuose yra atitinkamų etikečių"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>, kuri naudo<PERSON> i<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>, neturėtų būti duomenų elementų, pvz., element<PERSON> „th“ ar „caption“ arba atributo „summary“, nes taip ekrano skaitytuvų naudotojams gali būti teikiama trikdanti patirtis. [Sužinokite daugiau](https://web.dev/layout-table/)."}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "<PERSON>rist<PERSON><PERSON><PERSON> elementuose „`<table>`“ nevengiama naudoti atributų „`<th>`“, „`<caption>`“ arba „`[summary]`“."}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> elementuose „`<table>`“ vengiama naudoti atributus „`<th>`“, „`<caption>`“ ar „`[summary]`“."}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "Aiškus ir unikalus nuorodos tekstas (ir alternatyvusis vaizdų tekstas, kai jie naudojami kaip nuorodos), į kurį lengva sutelkti dėmesį, pagerina naršymo patirtį ekrano skaitytuvų naudotojams. [Sužinokite daugiau](https://web.dev/link-name/)."}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "Nėra aiškių nuorodų pavadinimų"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "Nuorodų pavadinimai aiškūs"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "Ekrano skaitytuvai apie sąrašus p<PERSON>ša tam tikru būdu. Tinkama sąrašo struktūra padeda ekrano skaitytuvams pateikti tinkamą išvestį. [Sužinokite daugiau](https://web.dev/list/)."}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "Sąrašuose patei<PERSON>ami ne tik elementai „`<li>`“ ir scenarijaus palaikymo elementai („`<script>`“ ir „`<template>`“)."}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "Sąrašuose pateikiami tik elementai „`<li>`“ ir scenarijaus palaikymo elementai („`<script>`“ ir „`<template>`“)."}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "Naudojant ekrano skaitytuvus reiki<PERSON>, kad s<PERSON> element<PERSON> („`<li>`“) būtų pirminiame elemente „`<ul>`“ arba „`<ol>`“, kad apie juos būtų galima tinkamai pranešti. [Sužinokite daugiau](https://web.dev/listitem/)."}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "Sąrašo elementų („`<li>`“) nėra pirminiuose elementuose „`<ul>`“ arba „`<ol>`“."}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> elementai („`<li>`“) yra pirminiuose elementuose „`<ul>`“ arba „`<ol>`“"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "Naudoto<PERSON>i nesitiki, kad puslapis bus atnaujintas automatiškai. Tai atlikus dėmesys vėl sutelkiamas į puslapio viršų. Dėl to galima erzinanti arba klaidinanti patirtis. [Sužinokite daugiau](https://web.dev/meta-refresh/)."}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Dokumente naudojama metažyma „`<meta http-equiv=\"refresh\">`“"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "Dokumente nenaudojama metažyma „`<meta http-equiv=\"refresh\">`“"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "Išjungus mastelio keitimą gali kilti problemų sutrikusio regėjimo naudotojams, kurie padid<PERSON> e<PERSON>, kad tinkamai matytų tinklalapio turinį. [Sužinokite daugiau](https://web.dev/meta-viewport/)."}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "Atributas „`[user-scalable=\"no\"]`“ naudojamas elemente „`<meta name=\"viewport\">`“ arba atributas „`[maximum-scale]`“ yra maž<PERSON> nei 5."}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "Atributas „`[user-scalable=\"no\"]`“ nenaudojamas elemente „`<meta name=\"viewport\">`“, o atributas „`[maximum-scale]`“ yra ne mažes<PERSON> nei 5."}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "Ekrano skaitytuvai negali išversti turinio, kuris nėra tekstas. Prie elementų „`<object>`“ pridėjus alternatyviojo teksto, ekrano skaitytuvai naudotojams geriau perteikia reiškmę. [Sužinokite daugiau](https://web.dev/object-alt/)."}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "Elementuose „`<object>`“ n<PERSON>ra „`[alt]`“ teksto"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "Elementuose „`<object>`“ yra „`[alt]`“ teksto"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "Didesnė nei 0 vertė aiškiai nurodo naršymo tvarką. <PERSON><PERSON> tai yra tinkamas sprendimas, tačiau tai dažnai gali erzinti pagalbinių technologijų naudotojus. [Sužinokite daugiau](https://web.dev/tabindex/)."}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "Kai kurių elementų „`[tabindex]`“ vertė yra did<PERSON>nė nei 0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "Nėra elemento su didesne nei 0 „`[tabindex]`“ verte"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "Ekrano skaitytuvuose naudoja<PERSON>, pad<PERSON><PERSON><PERSON><PERSON> lengviau naršyti lenteles. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad langeliai „`<td>`“, kuriuose naudojamas atributas „`[headers]`“, nurodo tik langelius toje pa<PERSON> lent<PERSON>, gali būti pagerinta ekrano skaitytuvų naudotojų patirtis. [Sužinokite daugiau](https://web.dev/td-headers-attr/)."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Langeliai elemente „`<table>`“, kuriuose naudojamas atributas „`[headers]`“, nurodo elementą „`id`“, nerastą toje pačioje <PERSON>."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "Langeliai elemente „`<table>`“, kuriuose naudojamas atributas „`[headers]`“, nuro<PERSON> lent<PERSON> la<PERSON>, es<PERSON><PERSON><PERSON> toje pač<PERSON> lent<PERSON>."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "<PERSON>krano skaitytuvuose naudo<PERSON>, pad<PERSON><PERSON><PERSON><PERSON> leng<PERSON>u naršyti lenteles. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad lentel<PERSON>s antraštės visada nurodo tam tikrą langelių rinkinį, gali būti pagerinta ekrano skaitytuvų naudotojų patirtis. [Sužinokite daugiau](https://web.dev/th-has-data-cells/)."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Elementuose „`<th>`“ ir elementuose su atributu „`[role=\"columnheader\"/\"rowheader\"]`“ nėra duomenų langelių, kuriuos jie a<PERSON>."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "Elementuose „`<th>`“ ir elementuose su atributu „`[role=\"columnheader\"/\"rowheader\"]`“ yra duomenų langelių, kuriuos jie a<PERSON>."}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "Nurodžius tinkamą elementų [BCP 47 kalbą](https://www.w3.org/International/questions/qa-choosing-language-tags#question), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad ekrano skaitytuvas tinkamai ištars tekstą. [Sužinokite daugiau](https://web.dev/valid-lang/)."}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Atributuose „`[lang]`“ nėra tinkamos vertės"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "Atributų „`[lang]`“ vert<PERSON> tin<PERSON>ma"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "Kai pateikiama vaizdo įrašo antrašt<PERSON>, kurtie<PERSON> ir sutrikusios klausos naudotojams lengviau suprasti pateikiamą informaciją. [Sužinokite daugiau](https://web.dev/video-caption/)."}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "Elementuose „`<video>`“ nėra elemento „`<track>`“ su atributu „`[kind=\"captions\"]`“."}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "Elementuose „`<video>`“ yra elementas „`<track>`“ su atributu „`[kind=\"captions\"]`“"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "Garso įrašų aprašai teikia reikiamą informaciją apie vaizdo įrašus, kurios negalima pateikti dialoge, pvz., susijusios su veido išraiškomis ir scenomis. [Sužinokite daugiau](https://web.dev/video-description/)."}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "Elementuose „`<video>`“ nėra elemento „`<track>`“ su atributu „`[kind=\"description\"]`“."}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "Elementuose „`<video>`“ yra elementas „`<track>`“ su atributu „`[kind=\"description\"]`“"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "Kad b<PERSON>t<PERSON> tinkamai rodoma sistemoje „iOS“, kai naudotojai prideda laipsniškąją žiniatinklio programą prie pagrindinio ekrano, nurodykite `apple-touch-icon`. Ji turi nukreipti į neskaidrų 192 tšk. (arba 180 tšk.) kvadratinį PNG failą. [Sužinokite daugiau](https://web.dev/apple-touch-icon/)"}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "Nepateikiama tinkama piktograma „`apple-touch-icon`“"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "„`apple-touch-icon-precomposed`“ p<PERSON><PERSON>, ver<PERSON><PERSON><PERSON> naudo<PERSON>te „`apple-touch-icon`“."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> tinkama piktograma „`apple-touch-icon`“"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "„Chrome“ plėtiniai neigiamai paveikė šio puslapio įkėlimo našumą. Pabandykite patikrinti puslapį inkognito režimu arba naudodami „Chrome“ profilį be plėtinių."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "Scenarijaus įvertinimas"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "Scenarijaus analiz<PERSON>"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "Bendras centrinio procesoriaus laikas"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "Apsvarstykite galimybę sutrumpinti JS analizei, kompiliavimui ir vykdymui skiriamą laiką. Mažesnės JS naudingosios apkrovos gali padėti tai padaryti. [Sužinokite daugiau](https://web.dev/bootup-time)."}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "Sutrumpinkite „JavaScript“ vykdymo laik<PERSON>"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "„JavaScript“ vykdymo laikas"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Dideli GIF failai nėra efektyvus animuoto turinio pat<PERSON> b<PERSON>. Kad sutaupytumėte tinklo baitų, vietoje GIF failų galite naudoti MPEG4 ar „WebM“ vaizdo įrašų animacijai ir PNG ar „WebP“ statiniams vaizdams pateikti. [Sužinokite daugiau](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Naudokite vaizdo įrašo formatus animuotam turiniui pateikti"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Apsvarstykite galimybę įkelti ne ekraninius ir paslėptus vaizdus tik tada, kai bus įkelti visi svarbiausi ištekliai, kad sutrump<PERSON>t<PERSON> laikas iki sąveikos. [Sužinokite daugiau](https://web.dev/offscreen-images)."}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Atidėkite ne ekraninius vaizdus"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Šaltiniai blokuoja puslapio pirmą žymėjimą. Apsvarstykite galimybę pateikti svarbiausius JS ar CSS kaip eilutinius elementus ir atidėti visus nesvarbius JS ar stilius. [Sužinokite daugiau](https://web.dev/render-blocking-resources)."}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Pašalinkite pateikim<PERSON> blo<PERSON><PERSON><PERSON> i<PERSON>"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Naudotojai turi mokėti už <PERSON>eles tinklo na<PERSON>, be to, jos glaud<PERSON> susijusios su ilgu įkėlimo laiku. [Sužinokite daugiau](https://web.dev/total-byte-weight)."}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Bendras dydis: {totalBytes, number, bytes} KB"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Išvenkite didelių tinklo naudingųjų apkrovų"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Išvengiama didelių tinklo naudingųjų apkrovų"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "Sumažinus CSS failų dydį, gali sumažėti tinklo naudingosios apkrovos. [Sužinokite daugiau](https://web.dev/unminified-css)."}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "Sumažinkite CSS failus"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> „JavaScript“ failus, galima sumažinti naudingąsias apkrovas ir sutrumpinti scenarijaus analizavimo laiką. [Sužinokite daugiau](https://web.dev/unminified-javascript)."}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Sumažinkite „JavaScript“"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Pašalinkite neveikiančias taisykles iš stiliaus failų ir atidėkite CSS kalbos, nenaudojamos turiniui virš <PERSON>, įkėlimą, kad tinklo veikla sunaudotų mažiau baitų. [Sužinokite daugiau](https://web.dev/unused-css-rules)."}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Pašalinkite nevartojamą CSS kalbą"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Pašalinkite nenaudojamą „JavaScript“, kad tinklo veikla sunaudotų mažiau bait<PERSON>."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Pašalinkite nenaudojamą „JavaScript“"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Jei talpykla galios ilgiau, greičiau sulauksite pakartotinių apsilankymų puslapyje. [Sužinokite daugiau](https://web.dev/uses-long-cache-ttl)."}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{<PERSON>sta<PERSON> 1 i<PERSON><PERSON><PERSON><PERSON>}one{<PERSON>sta<PERSON> # i<PERSON><PERSON><PERSON><PERSON>}few{Rasti # ištekliai}many{Rasta # ištekliaus}other{Rasta # išteklių}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Statiniams ištekliams taikykite efektyvią talpyklos politiką"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Naudojama efektyvi statinių išteklių talpyklos politika"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Optimizuoti vaizdai įkeliami greičiau ir sunaudoja mažiau mobiliojo ryšio duomenų. [Sužinokite daugiau](https://web.dev/uses-optimized-images)."}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Efektyviai koduokite vaizdus"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Teikite tinkamo d<PERSON>, kad būtų taupomi mobiliojo ryšio duomenys ir puslapis būtų įkeliamas greičiau. [Sužinokite daugiau](https://web.dev/uses-responsive-images)."}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Pasirinkite tinkamo d<PERSON>ž<PERSON> v<PERSON>dus"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> i<PERSON> re<PERSON> suglaudinti (naudo<PERSON><PERSON> „Gzip“, „Deflate“ arba „Brotli“), kad bendrai būtų sunaudojama kuo mažiau tinklo baitų. [Sužinokite daugiau](https://web.dev/uses-text-compression)."}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Įgalinkite teksto glaudin<PERSON>"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "Tokių formatų kaip JPEG 2000, JPEG XR ir „WebP“ vaizdai dažniausiai glaudinami geriau nei PNG ar JPEG vaizdai, tod<PERSON><PERSON> yra atsisiunčiami greičiau ir sunaudoja mažiau duomenų. [Sužinokite daugiau](https://web.dev/uses-webp-images)."}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "Pateikite naujos kartos formatų vaizdus"}, "lighthouse-core/audits/content-width.js | description": {"message": "Jei programos turinio plotis nesutampa su peržiūros srities pločiu, jūsų programa gali būti neoptimizuota mobiliųjų įrenginių ekranams. [Sužinokite daugiau](https://web.dev/content-width)"}, "lighthouse-core/audits/content-width.js | explanation": {"message": "{innerWidth} tšk. per<PERSON><PERSON><PERSON><PERSON> sritis neatitinka {outerWidth} tšk. lango dydž<PERSON>."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "Turinys nėra tinkamo d<PERSON>č<PERSON>"}, "lighthouse-core/audits/content-width.js | title": {"message": "Turinys yra tin<PERSON>mo d<PERSON>"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "Toliau pateiktose svarbiausių užklausų grandinėse nurodoma, kurie ištekliai įkelti nurodant aukštą prioritetą. Kad puslapio įkėlimas būt<PERSON> sklandes<PERSON>, apsvarstykite galimybę sutrumpinti grandines, sumažinti atsisiunčiamų išteklių dydį arba atidėti nebūtinų išteklių atsisiuntimą. [Sužinokite daugiau](https://web.dev/critical-request-chains)."}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Rasta 1 grandinė}one{Rasta # grandinė}few{Rastos # grandinės}many{Rasta # grandinės}other{Rasta # grandinių}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "Sumažinkite svarbiausių užklausų gylį"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "<PERSON><PERSON><PERSON><PERSON> nutraukimas / įspėjimas"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/deprecations.js | description": {"message": "Nebenaudojamos API galiausiai bus pašalintos iš <PERSON>. [Sužinokite daugiau](https://web.dev/deprecations)."}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Rastas 1 įspėjimas}one{Rastas # įspėjimas}few{<PERSON><PERSON><PERSON> # įspėjimai}many{Rasta # įspėjimo}other{Rasta # įspėjimų}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "Naudojamos p<PERSON> API"}, "lighthouse-core/audits/deprecations.js | title": {"message": "Vengiama nebenaudojamų API"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "Programos talpykla nebenaudojama. [Sužinokite daugiau](https://web.dev/appcache-manifest)."}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "<PERSON><PERSON><PERSON> elementas „{AppCacheManifest}“"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "Naudojama programos talpykla"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "Vengiama naudoti programos talpyklą"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> DOCTYPE, na<PERSON><PERSON><PERSON><PERSON>ė neperjungia į seno standarto tinklalapių palaikymo režimą. [Sužinokite daugiau](https://web.dev/doctype)"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "DOCTYPE pavadinimas turi būti mažosiomis raidėmis nurodyta eilutė „`html`“"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Dokumente turi būti nurodytas DOCTYPE"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Laukas „publicId“ turėtų būti tušč<PERSON>"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Laukas „systemId“ turėtų būti tušč<PERSON>"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Puslapyje trūksta HTML DOCTYPE, todėl aktyvinamas seno standarto tinklalapių palaikymo režimas"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "Puslapyje yra HTML DOCTYPE"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "Elementas"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistika"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Vertė"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "Naršyklių inžinieriai rekomenduoja puslapiuose naudoti ne daugiau kaip apytiksliai 1,5 tūkst. DOM elementų. Optimaliausias medžio gylis – mažiau nei 32 elementai ir mažiau nei 60 antrinių bei pirminių elementų. Dėl didelio DOM elementų skaičiaus gali būti sunaudojama daugiau atminties, ilgiau [skaičiuojami stiliai](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) ir gali reikėti brangių [išdėstymo perskaičiavimų](https://developers.google.com/speed/articles/reflow). [Sužinokite daugiau](https://web.dev/dom-size)."}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 elementas}one{# elementas}few{# elementai}many{# elemento}other{# elementų}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Venkite per didelių DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Maksimalus D<PERSON> gylis"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Bendras DOM elementų skaičius"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Maksimalus antrinių elementų s<PERSON>čius"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "Išvengiama per didelių DOM"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "Atributas „rel“"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "Tai<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "Pridėkite fragmentus „`rel=\"noopener\"`“ arba „`rel=\"noreferrer\"`“ prie bet kurių išorinių nuorodų, kad pagerintumėte našumą ir išvengtumėte saugos pažeidimų. [Sužinokite daugiau](https://web.dev/external-anchors-use-rel-noopener)."}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "Nuorodos į mišrios kilmės paskirties vietas yra nesaugios"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "Nuorodos į mišrios kilmės paskirties vietas yra saugios"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "Nepavyko nustatyti p<PERSON>aišo ({anchorHTML}) paskirties vietos. Jei jo nenaudojate kaip <PERSON>, apsvarstykite galimybę pašalinti atributą „target=_blank“."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Naudotojai gali būti įtarūs arba sumišę, jei svet<PERSON> bus prašoma suteikti leidimą pasiekti vietovės duomenis be jokio konte<PERSON>. Apsvarstykite galimybę susieti užklausą su naudotojų veiksmu. [Sužinokite daugiau](https://web.dev/geolocation-on-start)."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Įkeliant puslapį pateikiama užklausa dėl pranešimų leidimo"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Įkeliant puslapį vengiama pateikti užklausą dėl leidimo nustatyti geografinę vietovę"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "Puslapyje aptiktos visos „JavaScript“ sąsajos bibliotekos. [Sužinokite daugiau](https://web.dev/js-libraries)"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "Aptikta „JavaScript“ bibliotekų"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "<PERSON><PERSON><PERSON> l<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> scenar<PERSON>, dinamiš<PERSON> įterpiami naudojant „`document.write()`“, gali atidėti puslapio įkėlimą dešimtimis sekundžių. [Sužinokite daugiau](https://web.dev/no-document-write)."}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> „`document.write()`“"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> naudo<PERSON> „`document.write()`“"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "Rimč<PERSON><PERSON><PERSON> p<PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "Bibliotekos versija"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "Pažeidim<PERSON> s<PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "Kai kuriuose trečiųjų šalių scenarijuose gali būti žinomų saugos pažeidimų, kuriuos užpuolėjai gali lengvai nustatyti ir jais pasinaudoti. [Sužinokite daugiau](https://web.dev/no-vulnerable-libraries)."}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{Aptiktas 1 pažeidimas}one{Aptiktas # pažeidimas}few{Aptikti # pažeidimai}many{Aptikta # pažeidimo}other{Aptikta # pažeidimų}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "Puslapyje yra „JavaScript“ sąsajos bibliotekų, kuriose yra žinomų saugos pažeidimų"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "Vengiama „JavaScript“ sąsajos bibliotekų, kuriose yra ž<PERSON>ų saugos pažeidimų"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "Naudotojai gali būti įtarūs arba sumišę, jei s<PERSON> bus prašoma suteikti pranešimų leidimą be jokio konteksto. Apsvarstykite galimybę susieti užklausą su naudotojų gestais. [Sužinokite daugiau](https://web.dev/notification-on-start)."}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Įkeliant puslapį pateikiama užklausa dėl pranešimų leidimo"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "Įkeliant puslapį vengiama pateikti užklausą dėl p<PERSON>š<PERSON> leidimo"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "Netinkami elementai"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "Neleidžiant įklijuoti slap<PERSON>žodžių, pažeidžiama tinkamos saugos politika. [Sužinokite daugiau](https://web.dev/password-inputs-can-be-pasted-into)."}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "Naudotojams neleidžiama įklijuoti teksto į slaptažodžių laukus"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "Naudotojams leidžiama įklijuoti tekstą į slaptaž<PERSON>ž<PERSON> laukus"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokolas"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "Naudodami HTTP/2 gaunate daugiau privalumų, nei naudodami HTTP/1.1, pvz., d<PERSON><PERSON><PERSON>, tankinimą ir serverio įkėlimo funkciją. [Sužinokite daugiau](https://web.dev/uses-http2)."}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 užklausa nepateikta naudojant HTTP/2}one{# užklausa nepateikta naudojant HTTP/2}few{# užklausos nepateiktos naudojant HTTP/2}many{# užklausos nepateikta naudojant HTTP/2}other{# užklausų nepateikta naudojant HTTP/2}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "Nenaudojamas HTTP/2 visuose <PERSON>"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "Naudojamas HTTP/2 pu<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Apsvarstykite galimybę pažymėti lietimo ir pelės ratuko sukimo įvykių apdorojimą kaip „`passive`“, kad pagerintumėte puslapio slinkimo našumą. [Sužinokite daugiau](https://web.dev/uses-passive-event-listeners)."}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Nenaudojamos pasyvios apdorojimo priemonės siekiant pagerinti slink<PERSON>"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Naudojamos pasyvios apdorojimo priemonės siekiant pagerinti slink<PERSON>"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "Pulte užregistruo<PERSON> k<PERSON>, kad esama neišspręstų problemų. Jos gal<PERSON> kilti nepavykus pateikti tinklo užklausų arba dėl kitų naršyklės problemų. [Sužinokite daugiau](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> klaidos registruojamos pulte"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "Pulte neužregistruota naršyklės klaidų"}, "lighthouse-core/audits/font-display.js | description": {"message": "Pasinaudokite šriftų pateikimo CSS funkcija, kad tekstas būtų matoma<PERSON>, kol įkeliami žiniatinklio šriftai. [Sužinokite daugiau](https://web.dev/font-display)."}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad įkeliant žiniatinklio šriftą būtų matomas te<PERSON>tas"}, "lighthouse-core/audits/font-display.js | title": {"message": "Įkeliant žiniatinklio šriftą matomas visas tekstas"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "„Lighthouse“ nepavyko automatiškai patikrinti toliau nurodyto URL šriftų pateikimo vertės: {fontURL}."}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "Formato <PERSON> (faktinis)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "<PERSON><PERSON> (rodomas)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "Rodomo vaizdo matmenys turi atitikti natūralų formato koeficientą. [Sužinokite daugiau](https://web.dev/image-aspect-ratio)."}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "Rodomi vaizdai su netinkamu formato koeficientu"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "Rod<PERSON> vaizdai su tinkamu formato koeficientu"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "Netinkama vaizdo dydžio informacija: {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>ės gali aktyviai raginti naudotojus pridėti jūsų programą prie pagrindinio ekrano, tai paskatintų geresnį įtraukimą. [Sužinokite daugiau](https://web.dev/installable-manifest)"}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "Žiniatinklio programos aprašas neatitinka diegiamumo reikalavimų."}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "Žiniatinklio programos aprašas atitinka diegiamumo reikalavimus"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "Nesaugus URL"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "Visos svetain<PERSON> t<PERSON>ų būti apsaugotos naudojant HTTPS, net ir tos, kuriose nėra neskelbtinų duomenų. Naudojant HTTPS įsibrovėliams neleidžiama gadinti ar pasyviai klausytis jūsų programos ir jos naudotojų komunikacijos, be to, jį privaloma naudoti HTTP/2 ir daugelyje naujų žiniatinklio platformų API. [Sužinokite daugiau](https://web.dev/is-on-https)."}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Rasta 1 nesaugi užklausa}one{Rasta # nesaugi užklausa}few{Rastos # nesaugios užklausos}many{Rasta # nesaugos užklausos}other{Rasta # nesaugių užklausų}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "Nenaudojamas HTTPS"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "Naudojamas HTTPS"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "Spartus puslapių įkėlimas mobiliojo ryšio tinkle užtik<PERSON> gerą mobiliųjų įrenginių naudotojų patirtį. [Sužinokite daugiau](https://web.dev/load-fast-enough-for-pwa)."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "<PERSON><PERSON> iki s<PERSON> – {timeInMs, number, seconds} sek."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "Laikas iki sąveikos imituojamame mobiliojo ry<PERSON>io tinkle – {timeInMs, number, seconds} sek."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "Puslapis įkeliamas per lėtai ir nėra interaktyvus 10 sekundžių. Peržiūrėkite galimybes ir diagnostiką skiltyje „Našumas“, kad su<PERSON>, kaip <PERSON>ua<PERSON>."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "Puslapis įkeliamas nepakankamai sparčiai mobiliojo ryš<PERSON> tin<PERSON>"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "Puslapis įkeliamas pakankamai sparčiai mobiliojo ryš<PERSON> tin<PERSON>"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Kategorija"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "Apsvarstykite galimybę sutrumpinti JS analizei, kompiliavimui ir vykdymui skiriamą laiką. Mažesnės JS naudingosios apkrovos gali padėti tai padaryti. [Sužinokite daugiau](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Pagrindinės grup<PERSON>s ve<PERSON> sutr<PERSON>"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "Su<PERSON><PERSON><PERSON><PERSON> pag<PERSON> grup<PERSON>"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "<PERSON><PERSON> p<PERSON>ti daugiausia naudotojų, s<PERSON><PERSON><PERSON><PERSON> turi veikti visose svarbiausiose naršyklėse. [Sužinokite daugiau](https://web.dev/pwa-cross-browser)"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>a skirtingose na<PERSON>š<PERSON>ė<PERSON>"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad atskiri puslapiai būtų susiejami giliąja nuoroda naudojant URL ir kad URL yra unikalūs pagal bendrinimo visuomeninėje medijoje tikslą. [Sužinokite daugiau](https://web.dev/pwa-each-page-has-url)"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Kiekvienas puslapis turi URL"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "<PERSON><PERSON><PERSON> e<PERSON> per<PERSON>jimai turi būti s<PERSON>, net esant lėtam interneto ryšiui. Tai labai svarbu, kad naudotojas tinkamai suvoktų našumą. [Sužinokite daugiau](https://web.dev/pwa-page-transitions)"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON>, kad pu<PERSON>i būtų blokuojami tinklo"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "Įvertinta įvesties delsa yra įvertintas laikas mi<PERSON>, per kurį programa atsako į naudotojo įvestį per labiausiai užimtas puslapio įkėlimo 5 sekundes. Jei delsa ilgesnė nei 50 ms, naudotojams gali atrodyti, kad programa veikia lėtai. [Sužinokite daugiau](https://web.dev/estimated-input-latency)."}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "Įvertinta įvesties delsa"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "<PERSON><PERSON>s turi<PERSON> nuro<PERSON>, kada pažymimas pirmasis tekstas ar vaizdas. [Sužinokite daugiau](https://web.dev/first-contentful-paint)."}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "Pirmasis „Contentful“ parodymas"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "Pirmas centrinio procesoriaus neaktyvumo laikas nurodo pirmą kartą, kai pagrindinė puslapio grupė yra pakankamai neaktyvi, kad gal<PERSON> apdoroti įvestį.  [Sužinokite daugiau](https://web.dev/first-cpu-idle)"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "Pirmas cent. procesoriaus laisvas laikas"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "<PERSON><PERSON><PERSON> re<PERSON> nurodo, kada parodomas pagrindinis puslapio turinys. [Sužinokite daugiau](https://web.dev/first-meaningful-paint)."}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "<PERSON><PERSON><PERSON> re<PERSON>"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "Laikas iki sąveikos yra vert<PERSON>, <PERSON><PERSON><PERSON><PERSON>, kiek laiko reiki<PERSON>, kol puslapis tampa visiškai interaktyvus. [Sužinokite daugiau](https://web.dev/interactive)."}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "Laikas iki sąveikos"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "Didžiausia potenciali naudotojų patiriama pirmosios įvesties delsa yra ilgiausios užduoties trukmė milisekundėmis. [Sužinokite daugiau](https://developers.google.com/web/updates/2018/05/first-input-delay)."}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> potenciali pirmosios įvesties delsa"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> rod<PERSON> parodo, kaip greitai pavaiz<PERSON><PERSON> puslapio turiny<PERSON>. [Sužinokite daugiau](https://web.dev/speed-index)."}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "Visų laikotarpių tarp PTŽ ir laiko iki sąveikos suma milisekundėmis, kai už<PERSON><PERSON><PERSON> atlikimo laikas virš<PERSON> 50 ms."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "<PERSON><PERSON> bloka<PERSON><PERSON> laikas"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "Abi<PERSON><PERSON> tin<PERSON> (RTT) turi didel<PERSON> įtakos našumui. Didelė RTT į šaltinį vertė nurodo, kad arčiau naudotojo esantys serveriai galėtų padidinti našumą. [Sužinokite daugiau](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "lighthouse-core/audits/network-rtt.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> tin<PERSON>"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "Serverio delsa gali turėti įtakos žiniatinklio našumui. Didelė šaltinio serverio delsa nurodo, kad serveris per daug apkrautas arba mažas vidinės pusės našumas. [Sužinokite daugiau](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "<PERSON><PERSON> vid<PERSON> pu<PERSON>"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> „JavaScript“ failas įgalina žiniatinklio programą veikti patikimai nenuspėjamomis tinklo sąlygomis. [Sužinokite daugiau](https://web.dev/offline-start-url)"}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "<PERSON> p<PERSON>o, `start_url` neat<PERSON><PERSON> klaidos kodą 200."}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "<PERSON> p<PERSON>o, `start_url` at<PERSON><PERSON> rodyd<PERSON> klaidos kodą 200."}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "„Lighthouse“ nepavyko nuskaityti `start_url` <PERSON><PERSON>. <PERSON><PERSON><PERSON> to `start_url` buvo laikomas dokumento URL. Klaidos pranešimas: „{manifestWarning}“."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "Tinklo <PERSON> kiekis ir dydis neturi viršyti tikslinių verčių, nustatytų našumo biudžete. [Sužinokite daugiau](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 užklausa}one{# užklausa}few{# užklausos}many{# užklausos}other{# užklausų}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "<PERSON>i jau nustatėte HTTPS, būtinai peradresuokite visą HTTP srautą į HTTPS, kad visi naudotojai galėtų naudotis saugiomis žiniatinklio funkcijomis. [Sužinokite daugiau](https://web.dev/redirects-http)"}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "Nenukreipia HTTP srauto į HTTPS"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "Nukreipia HTTP srautą į HTTPS"}, "lighthouse-core/audits/redirects.js | description": {"message": "Peradresuojant puslapio įkėlimo delsos laikas dar labiau pailg<PERSON>ja. [Sužinokite daugiau](https://web.dev/redirects)."}, "lighthouse-core/audits/redirects.js | title": {"message": "Venkite kelių puslapio peradresavimų"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "Kad nustatytumėte puslapio išteklių kiekio ir dyd<PERSON> bi<PERSON>, pridėkite biudžeto .json failą. [Sužinokite daugiau](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 užklausa • {byteCount, number, bytes} KB}one{# užklausa • {byteCount, number, bytes} KB}few{# u<PERSON>klausos • {byteCount, number, bytes} KB}many{# u<PERSON><PERSON>usos • {byteCount, number, bytes} KB}other{# užkla<PERSON>ų • {byteCount, number, bytes} KB}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "Pasistenkite neteikti daug užklausų ir neperkelti daug duomenų"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "Standartizuotos nuorod<PERSON> si<PERSON>, kurį URL rodyti paieškos rezultatuose. [Sužinokite daugiau](https://web.dev/canonical)."}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "<PERSON><PERSON><PERSON> URL ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "Nukreipia į kitą domeną ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "Negaliojantis URL ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Nukreipia į kitą „`hreflang`“ vietą ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "Nukreipiama į domeno šakninį URL (pagrindinį puslapį) vietoje atitinkamo turinio puslapio"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "Dokumente nėra tinkamo atributo „`rel=canonical`“"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "Dokumente yra tinkamas atributas „`rel=canonical`“"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "Mažesnis nei 12 piks. šriftas yra per <PERSON>, kad b<PERSON><PERSON><PERSON> įskaitomas, to<PERSON><PERSON><PERSON> mobiliuosius įrenginius naudojantys lankytojai turi keisti mastelį su<PERSON><PERSON><PERSON> pirštai<PERSON>, kad galėtų perskaityti. Pasistenkite, kad daugiau nei 60 proc. puslapio teksto šrifto dydis būtų 12 piks. arba didesnis. [Sužinokite daugiau](https://web.dev/font-size)."}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} a<PERSON><PERSON><PERSON><PERSON> teksto"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "Te<PERSON><PERSON> neįskaitomas, nes nėra mobiliųjų įrenginių ekranams optimizuotos peržiūros srities metažymos"}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "{decimalProportion, number, extendedPercent} teksto yra per ma<PERSON> (atsižvelgiant į pavyzdį – {decimalProportionVisited, number, extendedPercent})."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "Dokumente naudojami neįskaitomo d<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "Dokumente naudojami a<PERSON>š<PERSON>us d<PERSON><PERSON>"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "„hreflang“ nuorodos nurodo paieškos varikliams, kokią puslapio versiją jie turėtų pateikti paieškos rezultatuose pagal nurodytą kalbą ar regioną. [Sužinokite daugiau](https://web.dev/hreflang)."}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "Dokumente nėra tinkamo atributo „`hreflang`“"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "Dokumente yra tinkamas atributas „`hreflang`“"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kurių HTTP būsenos kodų nepavyko pateikti, gali būti indek<PERSON> netink<PERSON>i. [Sužinokite daugiau](https://web.dev/http-status-code)."}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "Nepavyko pateikti puslapio HTTP būsenos kodo"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "Sėkmingai pateiktas puslapio HTTP būsenos kodas"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "Paieškos varikliai negali įtraukti puslapių į paie<PERSON>kos rezultatus, jei neturi leidimo jų tikrinti. [Sužinokite daugiau](https://web.dev/is-crawable)."}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> indek<PERSON> blo<PERSON>"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "Puslapio indeksavimas nėra užblokuotas"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "Pagal aprašomąjį nuorodų tekstą paieškos varikliai gali lengviau suprasti jūsų turinį. [Sužinokite daugiau](https://web.dev/link-text)."}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Rasta 1 nuoroda}one{Rasta # nuoroda}few{Rastos # nuorodos}many{Rasta # nuorodos}other{Rasta # nuorodų}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "Nuorodose nėra aprašomojo teksto"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "Nuorodose yra aprašomojo teksto"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "Paleiskite [struktūrinių duomenų bandymo įrankį](https://search.google.com/structured-data/testing-tool/) ir [„Structured Data Linter“](http://linter.structured-data.org/), kad būt<PERSON> patvirtinti struktūriniai duomenys. [Sužinokite daugiau](https://web.dev/structured-data)."}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "Struktūriniai duomenys <PERSON>"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "<PERSON>int glaustai apibendrinti puslapio turinį, į paie<PERSON>kos rezultatus galima įtraukti metaaprašų. [Sužinokite daugiau](https://web.dev/meta-description)."}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "Nepateiktas apraš<PERSON> teks<PERSON>."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "Dokumente nėra metaaprašo"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "Dokumente yra <PERSON>"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "Paieškos varikliai negali indeksuoti papildinių turinio ir daug įrenginių riboja papildinius arba jų nepalaiko. [Sužinokite daugiau](https://web.dev/plugins)."}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "Dokumente naudojami papil<PERSON>i"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "Dokumente vengiama papildinių"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "<PERSON><PERSON> failas „robots.txt“ netinkamai suformatuotas, tikrintuvai gali nesuprasti, kaip norite tikrinti ar indeksuoti svetainę. [Sužinokite daugiau](https://web.dev/robots-txt)"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Pateikus „robots.txt“ užklausą gauta HTTP būsena: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Rasta 1 klaida}one{Rasta # klaida}few{Rastos # klaidos}many{Rasta # klaidos}other{Rasta # klaidų}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "„Lighthouse“ nepavyko atsisiųsti failo robots.txt"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "Failas robots.txt netinkamas"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "robots.txt tinkamas"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "Interaktyvūs element<PERSON>, pvz., my<PERSON><PERSON><PERSON> ir nuo<PERSON>, turi būti pakanka<PERSON> did<PERSON> (48 x 48 piks.) ir aplink juos turi būti pakankamai vietos, kad būtų galima lengvai paliesti ir kad jie nepersidengtų su kitais elementais. [Sužinokite daugiau](https://web.dev/tap-targets)."}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} tinkamo dydžio liečiamų objektų"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Liečiami objektai per ma<PERSON><PERSON>, nes nėra mobiliųjų įrenginių ekranams optimizuotos peržiūros srities metažymos"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "Liečiami objektai netinkamo dydžio"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Persideng<PERSON><PERSON> objektas"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Liečiamas objektas"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "Liečiami objektai tinkamo dydžio"}, "lighthouse-core/audits/service-worker.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> „JavaScript“ failas – tai technologija, įgalinanti jūsų programą naudoti daug laipsniškosios žiniatinklio programos funkcijų, pvz., naudoti neprisijungus, prid<PERSON>ti prie pagrindinio ekrano ar naudoti iš karto gaunamus pranešimus. [Sužinokite daugiau](https://web.dev/service-worker)"}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "<PERSON><PERSON> puslapis v<PERSON><PERSON> naudo<PERSON> pagalbinį „JavaScript“ failą, tačiau `start_url` ne<PERSON><PERSON>, nes apra<PERSON>o nepa<PERSON>ko analizuoti kaip gal<PERSON>jančio JSON"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "<PERSON><PERSON> puslapis v<PERSON><PERSON> na<PERSON> pagalbinį „JavaScript“ failą, tačiau `start_url` ({startUrl}) nepatenka į pagalbinio „JavaScript“ failo aprėptį ({scopeUrl})"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "<PERSON><PERSON> pu<PERSON> v<PERSON> pagalbinį „JavaScript“ failą, tačiau neaptikta `start_url`, nes neįkeltas joks a<PERSON>."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "Šiame pradiniame puslapyje yra vienas ar daugiau pagalbinių „JavaScript“ failų, tačiau puslapis ({pageUrl}) neįtrauktas."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "Neregis<PERSON><PERSON><PERSON><PERSON> pagal<PERSON>is „JavaScript“ failas, naudojamas puslapiams ir `start_url` vald<PERSON>i"}, "lighthouse-core/audits/service-worker.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> pagal<PERSON>is „JavaScript“ failas, naudojamas puslapiams ir `start_url` vald<PERSON>i"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "Teminis prisistatymo langas užtikrina kokybišką patirtį naudotojui paleidžiant jūsų programą iš pagrindinio ekrano. [Sužinokite daugiau](https://web.dev/splash-screen)"}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "Nesukonfigūruota tinkintam prisistatymo langui"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "Sukonfigūruota tinkintam prisistatymo langui"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s adreso juostos temos negalima pakeisti taip, kad atitiktų svetainės temą. [Sužinokite daugiau](https://web.dev/themed-omnibox)"}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "Nenustatoma adreso juostos temos spalva."}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "Nustatoma adreso juostos temos spalva."}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON> blo<PERSON><PERSON> la<PERSON>s"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "Treč<PERSON><PERSON>"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "Trečiosios šalies kodas gali smarkiai paveikti įkėlimo našumą. Apribokite trečiosios šalies teikėjų skaičių ir pabandykite įkelti trečiosios šalies kodą, kai puslapis bus įkeltas. [Sužinokite daugiau](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "Trečiosios šalies kodas {timeInMs, number, milliseconds} ms užblokavo pagrindinę grupę"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "Sumažina trečiosios šalies kodo poveikį"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "Trečiosios šalies <PERSON>udo<PERSON>"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "Laikas iki pirmojo baito n<PERSON>, per kurį serveris atsako. [Sužinokite daugiau](https://web.dev/time-to-first-byte)."}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "Šakninio dokumento įkėlimas užtruko {timeInMs, number, milliseconds} ms"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "<PERSON><PERSON> atsako laiko su<PERSON> (TTFB)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "Serverio atsako laikas yra trumpas (TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "Trukmė"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "Tipas"}, "lighthouse-core/audits/user-timings.js | description": {"message": "Apsvarstykite galimybę apdoroti programą naudojant naudotojo laiko API ir įvertinti programos realų našumą, kai naudotojas naudoja pagrindines funkcijas. [Sužinokite daugiau](https://web.dev/user-timings)."}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 naudotojo laikas}one{# naudotojo laikas}few{# naudotojo laikai}many{# naudotojo laiko}other{# naudotojo laikų}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> ir <PERSON>"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "<PERSON><PERSON> atributo „{security<PERSON><PERSON><PERSON>}“ išankstinio sujungimo nuoroda „<link>“, bet naršyklė jos nenaudojo. Patikrinkite, ar atributas „`crossorigin`“ naudo<PERSON><PERSON>."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "Apsvarstykite galimybę pridėti `preconnect` arba `dns-prefetch` ištekliaus nurodymus, kad ryšys su svarbiais trečiųjų šalių šaltiniais būtų užmezgamas iš anksto. [Sužinokite daugiau](https://web.dev/uses-rel-preconnect)"}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "<PERSON><PERSON> prisijunkite prie reikiamų šaltinių"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Rasta atributo „{preloadURL}“ išankstinio įkėlimo nuoroda „<link>“, bet naršyklė jos nenaudojo. Patikrinkite, ar atributas „`crossorigin`“ naudo<PERSON><PERSON>."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "Apsvarstykite galimybę naudoti „`<link rel=preload>`“ ir suteikti pirmenybę gaunamiems ištekliams, kurių užklausos šiuo metu teikiamos vėliau įkeliant puslapį. [Sužinokite daugiau](https://web.dev/uses-rel-preload)."}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "<PERSON><PERSON> įkelkite svar<PERSON>us<PERSON> užklausa<PERSON>"}, "lighthouse-core/audits/viewport.js | description": {"message": "<PERSON><PERSON><PERSON> `<meta name=\"viewport\">`, siekiant optimizuoti programą mobiliųjų ekranams. [Sužinokite daugiau](https://web.dev/viewport)"}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "<PERSON><PERSON><PERSON> `<meta name=\"viewport\">` nerasta"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><meta name=\"viewport\">` su `width` arba `initial-scale`"}, "lighthouse-core/audits/viewport.js | title": {"message": "<PERSON>ra <PERSON> `<meta name=\"viewport\">` su `width` arba `initial-scale`"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "<PERSON> „JavaScript“ i<PERSON><PERSON><PERSON><PERSON>, jūsų programoje turėtų būti rodoma turinio, net jei tai tik įspėjimas naudotojui, kad „JavaScript“ būtina norint naudoti programą. [Sužinokite daugiau](https://web.dev/without-javascript)"}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "<PERSON><PERSON><PERSON><PERSON> pagrindinėje dalyje turi būti pateikta turinio, jei scenari<PERSON>."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "Atsarginis turi<PERSON>, kai „JavaScript“ nepasiekiama"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "<PERSON><PERSON>, kai „<PERSON>“ nepasiekiama"}, "lighthouse-core/audits/works-offline.js | description": {"message": "Jei kuriate laipsniškąją žiniatinklio programą, apsvarstykite galimybę naudoti pagalbinį „JavaScript“ failą, kad programa veiktų neprisijungus. [Sužinokite daugiau](https://web.dev/works-offline)"}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "<PERSON>, da<PERSON><PERSON><PERSON> pu<PERSON> klaidos kodą 200."}, "lighthouse-core/audits/works-offline.js | title": {"message": "<PERSON>, da<PERSON><PERSON><PERSON> pu<PERSON> atsako rod<PERSON> klaidos kodą 200."}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "Pus<PERSON><PERSON> gali būti neįkeliamas neprisijungus, nes bandomasis URL ({requested}) peradresavo į {final}. Išbandykite antrąjį URL tiesiogiai."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "<PERSON> yra <PERSON>, <PERSON>os pagerinti ARIA naudojimą programoje. Jos gali pagerinti pagalbinių technologijų, pvz., ekrano s<PERSON>tytuv<PERSON>, naudotojų patirtį."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Tai yra gal<PERSON>, skirtos alternatyviam garso ir vaizdo įrašų turiniui teikti. Tai gali pagerinti klausos ar regėjimo sutrikimų turinčių naudotojų patirtį."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "<PERSON><PERSON><PERSON> ir vaizda<PERSON>"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Šie elementai paryškina dažniausiai naudojamus pritaikomumo geriausios praktikos metodus."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Geriausia praktika"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "Atlikę šias patikras sužinosite, kaip galite [geriau pritaikyti žiniatinklio programą](https://developers.google.com/web/fundamentals/accessibility). Automatiškai galima aptikti tik dalį pritaikomumo problemų, todė<PERSON> rekomenduojama atlikti ir neautomatinį tikrinimą."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "<PERSON><PERSON> elementai apima srit<PERSON>, kurių automatinio bandymo įrankis negali aprėpti. Mūsų vadove sužinokite daugiau apie tai, kaip [atlikti pritaikomumo peržiūrą](https://developers.google.com/web/fundamentals/accessibility/how-to-review)."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "<PERSON> y<PERSON>, ka<PERSON> <PERSON><PERSON><PERSON>."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "<PERSON> yra <PERSON>, <PERSON><PERSON>, ka<PERSON> skirtingų lokalių naudotojai interpretuoja jūsų turinį."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internacionalizavimas ir lokalizavimas"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Tai yra <PERSON>, skirtos pagerinti programos valdiklių semantiką. Tai gali pagerinti pagalbinių technologijų, pvz., ekrano s<PERSON>tytuv<PERSON>, naudotojų patirtį."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Pavadinimai ir et<PERSON>"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Tai yra <PERSON>, skirtos pagerinti naršymą klaviatūra programoje."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Tai y<PERSON>, <PERSON><PERSON> ar sąrašuose pateiktų duomenų skaitymą naudojant pagalbines technologijas, pvz., ekrano skaitytuvą."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> i<PERSON>"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Geriausios praktikos pavyzdžiai"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "Nuo kate<PERSON><PERSON><PERSON><PERSON> „Našumas“ nustatyto biudžeto priklauso svet<PERSON> našuma<PERSON>."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "Biudžetai"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "Daugiau informacijos apie programos našumą. <PERSON><PERSON> s<PERSON> [tiesiogiai nepaveikia](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) našumo įvertinimo."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostika"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Svarbiausias našum<PERSON> rod<PERSON> – kaip greitai taškai pateikiami ekrane. Svarbiausia metrika: pirmasis „Contentful“ parodymas, pirmasis reikšmingas parodymas"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Pirm<PERSON>jo <PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> šiais pasiūlymais puslapis gali būti įkeliamas greičiau. Tai [tiesiogiai nepaveiks](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) našumo įvertinimo."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "Met<PERSON>"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Pagerinkite bendrą įkėlimo našumą, kad puslapis reaguotų ir būtų parengtas naudoti kuo greičiau. Svarbiausia metrika: laikas iki <PERSON>, g<PERSON><PERSON><PERSON><PERSON> rod<PERSON>"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "<PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "Per <PERSON><PERSON> patik<PERSON> vertinami laipsniškosios žiniatinklio programos aspektai. [Sužinokite daugiau](https://developers.google.com/web/progressive-web-apps/checklist)"}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "<PERSON><PERSON> pat<PERSON> bū<PERSON> pagal pradinį [LŽP kontrolinį sąrašą](https://developers.google.com/web/progressive-web-apps/checklist), tačiau „Lighthouse“ jų neatlieka automatiškai. Jos neturi įtakos jūs<PERSON> rezultatui, ta<PERSON><PERSON><PERSON> svarbu, kad pat<PERSON> patys."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "Laipsniškoji žiniatinklio programa"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "Spar<PERSON><PERSON> i<PERSON> pat<PERSON>"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "G<PERSON><PERSON> įdiegti"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Optimizuota PWA"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "<PERSON><PERSON> patik<PERSON>, kad jū<PERSON><PERSON> puslapis optimizuotas paieškos variklio rezultatams reitinguoti. Yra papildomų veiksnių, galinčių paveikti paieškos reitingavimą, kurių „Lighthouse“ netikrina. [Sužinokite daugiau](https://support.google.com/webmasters/answer/35769)."}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "Paleiskite š<PERSON> papildomas patvirtinimo priemon<PERSON> s<PERSON>, kad patik<PERSON>te papildomus PVO geriausios praktikos metodus."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "PVO"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "Formatuokite HTML taip, kad tikrintuvai galėtų geriau suprasti programos turinį."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "Geriausios turinio praktikos pavyzdžiai"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Kad programa būtų rodoma paieškos rezultatuose, tikrintuvams reikalinga prieiga prie jos."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> ir in<PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "Įsitikinkite, kad puslapiai yra pritaikyti mobiliesiems, kad naudotojams nereikėtų suėmus artinti ar keisti mastelio norint perskaityti puslapių turinį. [Sužinokite daugiau](https://developers.google.com/search/mobile-sites/)."}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "Pritaikyta mobiliesiems"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Talpyklos TTL"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "Vietovė"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "Pavadinimas"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tipas"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "<PERSON><PERSON><PERSON><PERSON> laikas"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "Failų perkėlimo dyd<PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "<PERSON><PERSON><PERSON> su<PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "<PERSON><PERSON><PERSON> su<PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Galima sutaupyti {wastedBytes, number, bytes} KB"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Galima sutaupyti {wastedMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokumentas"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "Vaizdas"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "Medija"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} sek."}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "<PERSON><PERSON><PERSON> failas"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Treč<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "<PERSON><PERSON> viso"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "Įkeliant puslapį kilo su pėdsako įrašymu susijusi problema. Dar kartą paleiskite „Lighthouse“. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "<PERSON><PERSON><PERSON>, kol bus inicijuotas derintuvės protokolo <PERSON>, baigėsi skirtasis laikas."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "Įkeliant puslapį „Chrome“ nesukūrė ekrano kopijų. <PERSON><PERSON><PERSON>krinkite, kad puslapyje yra matomo turinio, tada pabandykite iš naujo paleisti „Lighthouse“. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "DNS serveriams nepavyko nustatyti pateikto domeno."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Įvyko su reikiamo ištekliaus „{artifactName}“ rinkimo priemone susijusi klaida: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "Įvyko vid<PERSON><PERSON>s „Chrome“ klaida. <PERSON><PERSON> naujo paleiskite „Chrome“ ir pabandykite iš naujo paleisti „Lighthouse“."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "Reikiamo ištekliaus „{artifactName}“ rinkimo priemonė nebuvo paleista."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "„Lighthouse“ nepavyko patikimai įkelti pusla<PERSON>, kurio už<PERSON> patei<PERSON>. Įsitikinkite, kad testuojate tinkamą URL ir kad serveris tinkamai atsako į visas užklausas."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "„Lighthouse“ nepavyko patikimai įkelti URL, kurio už<PERSON>, nes puslapis ne<PERSON>."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Pateiktame URL nėra tinkamo saugos sertifikato: {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "„Chrome“ neleido įkelti puslapio ir buvo parodytas tarpinis ekranas. Įsitikinkite, kad tikrinate tinkamą URL ir kad serveris tinkamai atsako į visas užklausas."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "„Lighthouse“ nepavyko patikimai įkelti pusla<PERSON>, kurio už<PERSON> patei<PERSON>. Įsitikinkite, kad tikrinate tinkamą URL ir kad serveris tinkamai atsako į visas užklausas. (Išsami informacija: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "„Lighthouse“ nepavyko patikimai įkelti pusla<PERSON>, kurio u<PERSON> patei<PERSON>. Įsitikinkite, kad tikrinate tinkamą URL ir kad serveris tinkamai atsako į visas užklausas. (Būsenos kodas: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Puslapis buvo įkeliamas per ilgai. Pasinaudokite ataskaitoje pateiktomis galimybėmis ir sumažinkite puslapio įkėlimo laiką, tada pabandykite iš naujo paleisti „Lighthouse“. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "<PERSON><PERSON><PERSON>, kol „DevTools“ protokolo atsakymas viršys skirtąjį laiką. (Metodas: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "<PERSON><PERSON><PERSON><PERSON> turinio gavi<PERSON> vir<PERSON> skirtąjį laiką"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "<PERSON><PERSON><PERSON><PERSON>, kad pateiktas URL netinkamas."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "Didžiausia s<PERSON>usio kelio <PERSON>:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "<PERSON><PERSON><PERSON>!"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "Ataskaitos klaida: n<PERSON><PERSON> pat<PERSON> informaci<PERSON>"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "Laboratorijos duomenys"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "[„Lighthouse“](https://developers.google.com/web/tools/lighthouse/) dabartinio puslapio analizė emuliuotame mobiliojo ryšio tinkle. Vertės yra a<PERSON>s ir gali skirt<PERSON>."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>s reikia patikrinti <PERSON>automa<PERSON>"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "Galimybė"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "Numatomos <PERSON>"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "Sutraukti fragmentą"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "Išplėsti fragmentą"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "Rodyti trečiųjų šalių išteklius"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "Paleidžiant „Lighthouse“ kilo problemų."}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "<PERSON>ert<PERSON><PERSON> yra a<PERSON> ir gali skirt<PERSON>. Našumo įvertinimas nustatomas [tik pagal šias metrikas](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, bet pateikta įspėjimų"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "Įspėjimai: "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "Apsvarstykite galimybę įkelti savo GIF į paslaugą, kuri le<PERSON> jį įterpti kaip HTML5 vaizdo įrašą."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "Įdiekite [atidėtojo įkėlimo „WordPress“ papildinį](https://wordpress.org/plugins/search/lazy+load/), leidžiantį atidėti bet kokius ne ekraninius vaizdus, arba perjunkite į šią funkciją teikiančią temą. Be to, apsvarstykite galimybę naudoti [AMP papildinį](https://wordpress.org/plugins/amp/)."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "<PERSON>ra įvairių „WordPress“ papildini<PERSON>, kuriuos naudodami galite [įterpti svarbių išteklių](https://wordpress.org/plugins/search/critical+css/) arba [atidėti mažiau svar<PERSON>](https://wordpress.org/plugins/search/defer+css+javascript/). Atminkite, kad dėl šių papildinių teikiamo optimizavimo gali būti pažeistos jūsų temos ar papildinių funkcijos, todėl tikriausiai turėsite atlikti kodo pakeitimų."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "Te<PERSON>, papildiniai ir serverio specifikacijos – visa tai turi įtakos serverio atsako laikui. Galbūt vertėtų surasti geriau optimizuotą temą, atidžiai pasirinkti optimizavimo papildinį ir (arba) naujovinti serverį."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "Apsvarstykite galimybę rodyti ištraukas įrašų sąra<PERSON><PERSON>se (pvz., naudodami daugiau elementų žymą), sumažinti nurodytame puslapyje rodomų įrašų skaičių, suskaidyti ilgus įrašus į kelis puslapius arba naudoti komentarų atidėtojo įkėlimo papildinį."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "Įvairūs [„WordPress“ papildiniai](https://wordpress.org/plugins/search/minify+css/) gali padėti svetainei sparčiau veikti sujungdami, suma<PERSON><PERSON>mi ar suglaudindami stilius. Be to, galb<PERSON>t norėsite pasinaudoti kūrimo procesu, kad suma<PERSON> i<PERSON> (jei tai įmanoma)."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "Įvairūs [„WordPress“ papildiniai](https://wordpress.org/plugins/search/minify+javascript/) gali padėti svetainei sparčiau veikti sujun<PERSON>dam<PERSON>, suma<PERSON><PERSON>mi ar suglaudindami scenarijus. Be to, galb<PERSON><PERSON> norėsite pasinaudoti kūrimo procesu, kad suma<PERSON> i<PERSON> (jei tai įmanoma)."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "Apsvarstykite galimybę sumažinti [„WordPress“ papildinių](https://wordpress.org/plugins/), įkeliančių nenaudojamą CSS kalbą jūsų puslapyje, skaičių arba juos pakeisti. Ka<PERSON> nustat<PERSON> papildinius, <PERSON><PERSON><PERSON><PERSON> nesusi<PERSON><PERSON>s CSS kalbos, pabandykite vykdyti [kodo aprėpties procesą](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) naudodami „Chrome DevTools“. Susijusią temą / papildinį galite nustatyti pagal stiliaus failo URL. Ieškokite papildinių, kurių sąraše pateikta daug stiliaus failų, kurių kodo aprėptyje yra daug raudonos spalvos. Papildinys įtraukti stiliaus failą į eilę turėtų tik tokiu atveju, jei jis tikrai naudojamas puslapyje."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "Apsvarstykite galimybę sumažinti [„WordPress“ papildinių](https://wordpress.org/plugins/), įkeliančių nenaudojamą „JavaScript“ jūsų puslapyje, skaičių arba juos pakeisti. Kad nustatyt<PERSON><PERSON> papildinius, <PERSON><PERSON><PERSON><PERSON> nesusiju<PERSON>s JS, pabandykite vykdyti [kodo aprėpties procesą](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) naudodami „Chrome DevTools“. Susijusią temą / papildinį galite nustatyti pagal scenarijaus URL. Ieškokite papildinių, kurių sąraše pateikta daug scenarijų, kurių kodo aprėptyje yra daug raudonos spalvos. Papildinys įtraukti scenarijų į eilę turėtų tik tokiu atveju, jei jis tikrai naudojamas puslapyje."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "Skaitykite apie [nar<PERSON><PERSON><PERSON><PERSON><PERSON> saugojimo talpykloje funkciją sistemoje „WordPress“](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "Apsvarstykite galimybę naudoti [vaizdų optimizavimo „WordPress“ papildinį](https://wordpress.org/plugins/search/optimize+images/), leidžiantį suglaudinti vaizdus išlaikant kokybę."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "Įkelkite vaizdus tiesiai per [medijos biblioteką](https://codex.wordpress.org/Media_Library_Screen), kad būtų pasiekiami reikiami vaizdų dydž<PERSON>i, tada įterpkite juos iš medijos bibliotekos arba naudokite vaizdų valdiklį, kad <PERSON><PERSON><PERSON>, jog naudojami optimalaus dydž<PERSON> vaizda<PERSON> (įskaitant tuos, kurie naudojami interaktyviems atskaitos taškams). Stenkitės nenaudoti vaizdų „`Full Size`“, nebent galima naudoti tokių matmenų vaizdus. [Sužinokite daugiau](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)."}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "Galite įgalinti teksto glaudinimą žiniatinklio serverio konfigūracijoje."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "Apsvarstykite galimybę naudoti [papildinį](https://wordpress.org/plugins/search/convert+webp/) arba paslaugą, kuri automatiškai konvertuotų įkeltus vaizdus į optimalius formatus."}}