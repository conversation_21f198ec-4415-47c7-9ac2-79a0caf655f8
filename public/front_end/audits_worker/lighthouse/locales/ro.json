{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "Tastele de acces permit utilizatorilor să focalizeze rapid o parte a paginii. Pentru o navigare corectă, fiecare tastă de acces trebuie să fie unică. [Află mai multe](https://web.dev/accesskeys/)."}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Valorile `[accesskey]` nu sunt unice"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "Valorile `[accesskey]`sunt unice"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON><PERSON> `role` ARIA acceptă un anumit subset de atribute `aria-*`. Nepotrivirea acestora anulează atributele `aria-*`. [Află mai multe](https://web.dev/aria-allowed-attr/)."}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Atributele `[aria-*]` nu se potrivesc cu rolurile"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Atributele `[aria-*]` se potrivesc cu rolurile"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "Unele roluri ARIA au atribute obligatorii care descriu starea elementului cititoarelor de ecrane. [Află mai multe](https://web.dev/aria-required-attr/)."}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]` nu au toate atributele `[aria-*]` necesare"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]` au toate atributele `[aria-*]` obligatorii"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "Unele roluri ARIA principale trebuie să conțină roluri secundare specifice pentru a-și îndeplini funcțiile de accesibilitate pentru care au fost concepute. [Află mai multe](https://web.dev/aria-required-children/)."}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Din elementele unui rol ARIA `[role]` care impun ca elementele secundare să conțină un anumit element `[role]` lipsește unul sau toate elementele secundare necesare respective."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "Elementele unui rol ARIA `[role]` care impun ca elementele secundare să conțină un anumit element `[role]` includ toate elementele secundare necesare."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "Unele roluri ARIA secundare trebuie să fie conținute de roluri principale specifice pentru a-și îndeplini corect funcțiile de accesibilitate pentru care au fost concepute. [Află mai multe](https://web.dev/aria-required-parent/)."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]` nu sunt conținute de elementul părinte impus"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]` sunt conținute de elementul părinte impus"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "Rolurile ARIA trebuie să aibă valori valide pentru a-și îndeplini funcțiile de accesibilitate pentru care au fost concepute. [Află mai multe](https://web.dev/aria-roles/)."}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> `[role]` nu sunt valide"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "<PERSON><PERSON><PERSON> `[role]` sunt valide"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Tehnologiile care asigură asistență, precum cititoarele de ecran, nu pot interpreta atributele ARIA cu valori nevalide. [Află mai multe](https://web.dev/aria-valid-attr-value/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Atributele `[aria-*]` nu au valori valide"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Atributele `[aria-*]` au valori valide"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "Tehnologiile care oferă asistență, precum cititoarele de ecran, nu pot interpreta atributele ARIA cu nume nevalide. [Află mai multe](https://web.dev/aria-valid-attr/)."}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Atributele `[aria-*]` sunt nevalide sau scrise greșit"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "Atributele `[aria-*]` sunt valide și nu sunt scrise greșit"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "Subtitrările fac ca elementele audio să poată fi folosite de utilizatorii surzi sau cu dizabilități de auz, furnizând informații esențiale, precum cine vorbește, ce spune și alte informații care nu sunt legate de vorbire. [Află mai multe](https://web.dev/audio-caption/)."}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "Elementele `<audio>` au un element `<track>` cu `[kind=\"captions\"]` lipsă."}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "Elementele `<audio>` conțin un element `<track>` cu `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Elemente cu probleme"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "Când un buton nu are un nume accesibil, cititoarele de ecran îl anunță ca „buton”, făc<PERSON>du-l inutil pentru utilizatorii care se bazează pe cititoarele de ecran. [Află mai multe](https://web.dev/button-name/)."}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "Butoanele nu au un nume accesibil"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "Butoanele au un nume accesibil"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "Dacă adaugi modalități de a ocoli conținutul repetitiv, utilizatorii tastaturii vor naviga pe pagină mai eficient. [Află mai multe](https://web.dev/bypass/)."}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "Această pagină nu conține un titlu, un link de închidere sau o regiune de reper"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "Pagina conține un titlu, un link de închidere sau o regiune de reper"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "Textul cu un contrast redus este dificil sau imposibil de citit pentru mulți utilizatori. [Află mai multe](https://web.dev/color-contrast/)."}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Culorile din fundal și din prim-plan nu au un raport de contrast suficient."}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "Culorile din fundal și din prim-plan au un raport de contrast suficient"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "Când listele de definiții nu sunt marcate corect, cititoarele de ecran pot produce un rezultat derutant sau inexact. [Află mai multe](https://web.dev/definition-list/)."}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>` nu conțin doar grupurile `<dt>` și `<dd>` ordonate corect, `<script>` sau elementele `<template>`."}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` con<PERSON>in doar grup<PERSON>le `<dt>` și `<dd>` ordonate corect, `<script>` sau elemente `<template>`."}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "Elementele din lista de definiții (`<dt>` și `<dd>`) trebuie grupate într-un element principal `<dl>` pentru a se asigura că cititoarele de ecran le pot anunța corect. [Află mai multe](https://web.dev/dlitem/)."}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "Elementele din lista de definiții nu sunt incluse în elementele `<dl>`"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "Elementele din lista de definiții sunt incluse în elementele `<dl>`"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "Titlul le oferă utilizatorilor de cititoare de ecran o prezentare generală a paginii, iar utilizatorii de motoare de căutare îl folosesc intensiv pentru a stabili dacă o pagină este relevantă pentru căutarea lor. [Află mai multe](https://web.dev/document-title/)."}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "Documentul nu are un element `<title>`"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "Documentul are un element `<title>`"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "Valoarea unui atribut ID trebuie să fie unică pentru a preveni omiterea altor instanțe de tehnologiile care asigură asistență. [Află mai multe](https://web.dev/duplicate-id/)."}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "Atributele `[id]` de pe pagină nu sunt unice"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "Atributele `[id]` de pe pagină sunt unice"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "Utilizatorii cititoarelor de ecran se bazează pe titlurile cadrelor pentru a descrie conținutul cadrelor. [Află mai multe](https://web.dev/frame-title/)."}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "Elementele `<frame>` sau `<iframe>` nu au un titlu"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "Elementele `<frame>` sau `<iframe>` au un titlu"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "Dacă o pagină nu specifică un atribut de limbă, cititorul de ecran presupune că pagina este în limba prestabilită pe care utilizatorul a ales-o când a configurat cititorul de ecran. Dacă pagina nu este în limba prestabilită, atunci cititorul de ecran este posibil să nu citească corect textul paginii. [Află mai multe](https://web.dev/html-has-lang/)."}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Elementul `<html>` nu are un atribut `[lang]`"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "Elementul `<html>` are un atribut `[lang]`"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "Indicarea unei etichete de [limbă BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) valide ajută cititoarele de ecran să anunțe corect textul. [Află mai multe](https://web.dev/html-lang-valid/)."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Elementul `<html>` nu are o valoare validă pentru atributul `[lang]`."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "Elementul `<html>` are o valoare validă pentru atributul `[lang]`"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "Elementele informative ar trebui să conțină texte alternative descriptive scurte. Elementele decorative pot fi ignorate cu un atribut Alt gol. [Află mai multe](https://web.dev/image-alt/)."}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "Elementele imaginii nu au atribute `[alt]`"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "Elementele imagine au atribute `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "<PERSON><PERSON>d o imagine este folosită ca buton `<input>`, furnizarea unui text alternativ poate ajuta utilizatorii cititorului de ecran să înțeleagă scopul butonului. [Află mai multe](https://web.dev/input-image-alt/)."}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Elementele `<input type=\"image\">` nu au text `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "Elementele `<input type=\"image\">` au text `[alt]`"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "Etichetele asigură că opțiunile formularelor sunt anunțate corect de tehnologiile care asigură asistență, precum cititoarele de ecran. [Află mai multe](https://web.dev/label/)."}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "Elementele formularului nu au etichete asociate"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "Elementele formularului au etichete asociate"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "Un tabel care este folosit pentru aspect nu ar trebui să includă elemente de date, precum elementele th sau de subtitrare sau atributul rezumat, deoarece poate crea confuzie pentru utilizatorii cititoarelor de ecran. [Află mai multe](https://web.dev/layout-table/)."}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "Elementele `<table>` de prezentare nu evită folosirea `<th>`, `<caption>` sau a atributului `[summary]`."}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "Elementele `<table>` de prezentare evită să folosească `<th>`, `<caption>` sau atributul `[summary]`."}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "Textul linkurilor (și textul alternativ pentru imagini, când sunt folosite ca linkuri) care se poate distinge, unic și pe care se poate focaliza, îmbunătățește navigarea pentru utilizatorii de cititoare de ecran. [Află mai multe](https://web.dev/link-name/)."}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> nu au un nume care se poate distinge"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "<PERSON><PERSON><PERSON> au un nume care se poate distinge"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "Cititoarele de ecran au un anumit mod de anunțare a listelor. Asigurarea unei structuri corecte a listei îmbunătățește rezultatele cititorului de ecran. [Află mai multe](https://web.dev/list/)."}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "Listele nu conțin doar elemente `<li>` și elemente pe care se bazează scriptul (`<script>` și `<template>`)."}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "<PERSON>ele conțin doar elemente `<li>` și elemente pe care se bazează scriptul (`<script>` și `<template>`)."}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "Cititoarele de ecran necesită ca elementele din listă (`<li>`) să fie conținute într-un element principal `<ul>` sau `<ol>` pentru a fi anunțate corect. [Află mai multe](https://web.dev/listitem/)."}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "Elementele din listă (`<li>`) nu sunt incluse în elementele principale `<ul>` sau `<ol>`."}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "Elementele din listă (`<li>`) sunt conținute în elementele principale `<ul>` sau `<ol>`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "Utilizatorii nu se așteaptă ca o pagină să se actualizeze automat, iar dacă ei actualizează, focalizarea se va muta din nou în partea de sus a paginii. Acest lucru poate crea frustrare sau confuzie. [Află mai multe](https://web.dev/meta-refresh/)."}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Documentul folosește `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "Documentul nu folosește `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "Dezactivarea măririi sau micșorării este o problemă pentru utilizatorii cu vedere slabă, care se bazează pe mărirea ecranului pentru a vedea bine conținutul unei pagini web. [Află mai multe](https://web.dev/meta-viewport/)."}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` este folosit în elementul `<meta name=\"viewport\">` sau atributul `[maximum-scale]` este mai mic decât 5."}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` nu se folosește în elementul `<meta name=\"viewport\">` și atributul `[maximum-scale]` nu este mai mic decât 5."}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "Cititoarele de ecran nu pot traduce conținut în alt format decât text. Adăugarea de text alternativ la elementele `<object>` ajută cititoarele de ecran să le transmită utilizatorilor înțelesul. [Află mai multe](https://web.dev/object-alt/)."}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "Elementele `<object>` nu au text `[alt]`"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "Elementele `<object>` au text `[alt]`"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "O valoare mai mare decât 0 implică o ordine explicită de navigare. Deși valid din punct de vedere tehnic, acest lucru creează adesea frustrări utilizatorilor care se bazează pe tehnologii care oferă asistență. [Află mai multe](https://web.dev/tabindex/)."}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "Unele elemente au o valoare `[tabindex]` mai mare decât 0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "Niciun element nu are o valoare `[tabindex]` mai mare decât 0"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "Cititoarele de ecran au funcții care facilitează navigarea în tabele. Dacă se asigură că celulele `<td>` care folosesc atributul `[headers]` se referă doar la alte celule din același tabel, se poate îmbunătăți experiența pentru utilizatorii de cititoare de ecran. [Află mai multe](https://web.dev/td-headers-attr/)."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "<PERSON><PERSON><PERSON>le dintr-un element `<table>` care folosesc atributul `[headers]` se referă la un element `id` care nu se găsește în același tabel."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "<PERSON><PERSON><PERSON>le dintr-un element `<table>` care folosesc atributul `[headers]` se referă la celule ale aceluiași tabel."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "Cititoarele de ecran au funcții care facilitează navigarea în tabele. Dacă se asigură că antetele tabelelor se referă întotdeauna la unele seturi de celule, se poate îmbunătăți experiența pentru utilizatorii de cititoare de ecran. [Află mai multe](https://web.dev/th-has-data-cells/)."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Elementele `<th>` și elementele cu `[role=\"columnheader\"/\"rowheader\"]` nu au celule de date pe care să le descrie."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "Elementele `<th>` și elementele cu `[role=\"columnheader\"/\"rowheader\"]` au celule de date pe care le descriu."}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "Indicarea unei etichete de [limbă BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) valide la elemente ajută la pronunțarea corectă a textului de un cititor de ecran. [Află mai multe](https://web.dev/valid-lang/)."}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Atributele `[lang]` nu au o valoare validă"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "Atributele `[lang]` au o valoare validă"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "Când un videoclip are subtitrare, este mai simplu pentru utilizatorii surzi și cu dizabilități de auz să îi acceseze informațiile. [Află mai multe](https://web.dev/video-caption/)."}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "Elementele `<video>` nu conțin un element `<track>` cu `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "Elementele `<video>` conțin un element `<track>` cu `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "Descrierile audio oferă informații relevante pentru videoclipuri pe care dialogul nu le poate oferi, precum scene și expresii faciale. [Află mai multe](https://web.dev/video-description/)."}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "Elementele `<video>` nu conțin un element `<track>` cu `[kind=\"description\"]`."}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "Elementele `<video>` conțin un element `<track>` cu `[kind=\"description\"]`"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "Pentru un aspect ideal în iOS atunci când utilizatorii adaugă o aplicație web progresivă în ecranul de pornire, define<PERSON><PERSON> o `apple-touch-icon`. Aceasta trebuie să indice spre un PNG pătrat netransparent, de 192 px (sau 180 px). [Află mai multe](https://web.dev/apple-touch-icon/)."}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "<PERSON><PERSON> of<PERSON><PERSON> o `apple-touch-icon` validă"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "`apple-touch-icon-precomposed` este în<PERSON><PERSON><PERSON>; se preferă `apple-touch-icon`."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "Oferă o `apple-touch-icon` validă"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Extensiile Chrome au afectat performanța de încărcare a acestei pagini. Încearcă să auditezi pagina în modul incognito sau dintr-un profil Chrome fără extensii."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "Evaluarea scripturilor"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "<PERSON><PERSON><PERSON><PERSON> scripturilor"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "Timp CPU total"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "Poți reduce timpul petrecut cu analizarea, compilarea și executarea JS. Livrarea unor sarcini JS mai mici poate ajuta în acest sens. [Află mai multe](https://web.dev/bootup-time)."}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "Redu timpul de execuție JavaScript"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "Timpul de executare JavaScript"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "GIF-urile mari nu sunt eficiente pentru difuzarea conținutului animat. Folosește videoclipuri MPEG4/WebM pentru animații și PNG/WebP pentru imagini statice în locul GIF-urilor ca să economisești date în rețea. [Află mai multe](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Folosește formate video pentru conținut animat"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "<PERSON><PERSON><PERSON> î<PERSON>ă<PERSON> lent imaginile ascunse sau pe cele din afara ecranului după ce toate resursele esențiale s-au încărcat, pentru a micșora timpul până la interactivitate. [Află mai multe](https://web.dev/offscreen-images)."}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Amână imaginile din afara ecranului"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Resursele blochează prima redare a paginii. Poți să livrezi conținutul JS/CSS esențial inline și să amâni toate elementele JS/stilurile neesențiale. [Află mai multe](https://web.dev/render-blocking-resources)."}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Elimină resursele care blochează redarea"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Sarcinile mari de rețea îi costă pe utilizatori și sunt corelate cu timpi de încărcare îndelungați. [Află mai multe](https://web.dev/total-byte-weight)."}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Dimensiunea totală a fost de {totalBytes, number, bytes} KB"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Evită sarcinile uriașe de rețea"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Evită sarcinile uriașe de rețea"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "Comprimarea fișierelor CSS poate reduce dimensiunea sarcinilor de rețea. [Află mai multe](https://web.dev/unminified-css)."}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "Comprimă codul CSS"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Comprimarea fișierelor JavaScript poate reduce dimensiunea sarcinilor și timpul de analizare a scripturilor. [Află mai multe](https://web.dev/unminified-javascript)."}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Comprimă codul JavaScript"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Elimină regulile nefolosite din foile de stil și amână încărcarea conținutului CSS nefolosit pentru conținutul din partea superioară a paginii ca să reduci numărul de byți consumați de activitatea în rețea. [Află mai multe](https://web.dev/unused-css-rules)."}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Elimină conținutul CSS nefolosit"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Elimină codul JavaScript nefolosit pentru a reduce byții consumați de activitatea rețelei."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Elimină codul JavaScript nefolosit"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "O durată lungă a memoriei cache poate grăbi accesările repetate ale paginii. [Află mai multe](https://web.dev/uses-long-cache-ttl)."}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{o resursă găsită}few{# resurse găsite}other{# de resurse găsite}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Difuzează elementele statice cu o politică eficientă de stocare în memoria cache"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Folosește o politică eficientă de stocare în memoria cache pentru elementele statice"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Imaginile optimizate se încarcă mai repede și consumă mai puține date mobile. [Află mai multe](https://web.dev/uses-optimized-images)."}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Codifică eficient imaginile"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Difuzează imagini de dimensiuni corespunzătoare ca să economisești date mobile și să obții o încărcare mai rapidă. [Află mai multe](https://web.dev/uses-responsive-images)."}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Dimensionează corect imaginile"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Resursele bazate pe text trebuie comprimate (gzip, deflate sau brotli) pentru a minimiza numărul total de byți în rețea. [Află mai multe](https://web.dev/uses-text-compression)."}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Activează comprimarea textului"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "Formatele de imagine ca JPEG 2000, JPEG XR și WebP oferă adesea o comprimare mai bună decât PNG sau JPEG, ceea ce înseamnă descărcări mai rapide și mai puțin consum de date. [Află mai multe](https://web.dev/uses-webp-images)."}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "Difuzează imagini în formate moderne"}, "lighthouse-core/audits/content-width.js | description": {"message": "Dacă lățimea conținutului aplicației nu se potrivește cu lățimea ariei vizibile, este posibil ca aplicația să nu fie optimizată pentru ecrane mobile. [Află mai multe](https://web.dev/content-width)."}, "lighthouse-core/audits/content-width.js | explanation": {"message": "Aria vizibilă de {innerWidth} px nu corespunde cu dimensiunea ferestrei, de {outerWidth} px."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "Conținutul nu este dimensionat corect pentru aria vizibilă"}, "lighthouse-core/audits/content-width.js | title": {"message": "Conținutul este dimensionat corect pentru aria vizibilă"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "Lanțurile de solicitări esențiale de mai jos îți arată ce resurse sunt încărcate cu prioritate ridicată. Poți să reduci lungimea lanțurilor, să reduci dimensiunea de descărcare a resurselor sau să amâni descărcarea de resurse inutile pentru a îmbunătăți încărcarea paginilor. [Află mai multe](https://web.dev/critical-request-chains)."}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{un lanț găsit}few{# lanțuri găsite}other{# de lanțuri găsite}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "Redu profunzimea solicitărilor esențiale"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "Renunțare la dezvoltare/Avertisment"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "Rând"}, "lighthouse-core/audits/deprecations.js | description": {"message": "API-urile învechite vor fi eliminate, în final, din browser. [Află mai multe](https://web.dev/deprecations)."}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{A fost identificat un avertisment}few{Au fost identificate # avertismente}other{Au fost identificate # de avertismente}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "Folosește API-uri învechite"}, "lighthouse-core/audits/deprecations.js | title": {"message": "Evită API-urile învechite"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "Memoria cache a aplicației este învechită. [Află mai multe](https://web.dev/appcache-manifest)."}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "Am găsit „{AppCacheManifest}”"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "Folosește memoria cache de aplicație"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "Evită memoria cache a aplicației"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "Specificarea unui doctype împiedică browserul să treacă la modul caracteristici speciale. [Află mai multe](https://web.dev/doctype)."}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Numele doctype trebuie să fie șirul cu litere mici `html`"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Documentul trebuie să conțină un doctype"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Se aștepta ca publicId să fie un șir gol"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Se aștepta ca systemId să fie un șir gol"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Pagina nu are doctype HTML, așadar declanșează modul caracteristici speciale"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "Pagina are doctype HTML"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "Element"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistică"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Valoare"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "Specialiștii în browsere recomandă ca paginile să conțină mai puțin de ~1.500 de elemente DOM. Ideal este ca arborele să aibă o profunzime mai mică de 32 de elemente și mai puțin de 60 de elemente principale/subordonate. Un DOM mare poate crește folosirea memoriei, poate produce [calcule de stil](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) mai lungi și [rearanjări ale aspectului](https://developers.google.com/speed/articles/reflow) costisitoare. [Află mai multe](https://web.dev/dom-size)."}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 element}few{# elemente}other{# de elemente}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Evită o dimensiune DOM excesivă"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Adâncimea DOM maximă"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Numărul total de elemente DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Numărul maxim de elemente subordonate"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "Evită o dimensiune DOM excesivă"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "Obiectiv"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "Adaugă `rel=\"noopener\"` sau `rel=\"noreferrer\"` la orice linkuri externe pentru a îmbunătăți rezultatele și a preveni vulnerabilitățile de securitate. [Află mai multe](https://web.dev/external-anchors-use-rel-noopener)."}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "<PERSON><PERSON>le spre destinații cu mai multe origini sunt nesigure"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "<PERSON><PERSON><PERSON> spre destinații cu mai multe origini sunt sigure"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "Nu se poate determina destinația pentru ancora ({anchorHTML}). Dacă nu se folosește ca hyperlink, elimină target=_blank."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Utilizatorii nu au încredere sau sunt derutați de site-urile care le solicită locația fără context. Asociază solicitarea cu o acțiune a utilizatorilor. [Află mai multe](https://web.dev/geolocation-on-start)."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Solicită permisiunea de localizare geografică la încărcarea paginii"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Evită solicitarea permisiunii de localizare geografică la încărcarea paginii"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Versiune"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "Toate bibliotecile JavaScript front-end detectate în pagină. [Află mai multe](https://web.dev/js-libraries)."}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "Biblioteci JavaScript detectate"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "Pentru utilizatorii cu conexiuni lente, scripturile externe injectate dinamic prin `document.write()` pot întârzia încărcarea paginilor cu zeci de secunde. [Află mai multe](https://web.dev/no-document-write)."}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "Evită `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "Cea mai mare severitate"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "Versiunea bibliotecii"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "Numărul de vulnerabilități"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "Unele scripturi terță parte pot conține vulnerabilități de securitate cunoscute, ușor de identificat și de exploatat de atacatori. [Află mai multe](https://web.dev/no-vulnerable-libraries)."}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{O vulnerabilitate detectată}few{# vulnerabilități detectate}other{# de vulnerabilități detectate}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "Include bibliotecile JavaScript front-end cu vulnerabilități de securitate cunoscute"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "Înaltă"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "Redusă"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "Me<PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "Evită bibliotecile JavaScript front-end cu vulnerabilități de securitate cunoscute"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "Utilizatorii nu au încredere sau sunt derutați de site-urile care solicită să trimită notificări fără context. Asociază solicitarea cu gesturile utilizatorilor. [Află mai multe](https://web.dev/notification-on-start)."}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Solicită permisiunea de notificare la încărcarea paginii"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "Evită solicitarea permisiunii de notificare la încărcarea paginii"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "Elemente cu probleme"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "Împiedicarea inserării parolelor subminează buna politică de securitate. [Află mai multe](https://web.dev/password-inputs-can-be-pasted-into)."}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "Împiedică utilizatorii să insereze în câmpurile pentru parole"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "Permite-le utilizatorilor să insereze în câmpurile pentru parole"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protocol"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 oferă multe beneficii față de HTTP/1.1, inclusiv antete binare, anunțuri multiplex și tehnologie push pentru server. [Află mai multe](https://web.dev/uses-http2)."}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{O solicitare nu a fost difuzată prin HTTP/2}few{# solicitări nu au fost difuzate prin HTTP/2}other{# de solicitări nu au fost difuzate prin HTTP/2}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "<PERSON><PERSON> folosește HTTP/2 pentru toate resursele"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "Folosește HTTP/2 pentru propriile sale resurse"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Poți marca funcțiile de procesare a evenimentelor prin atingere sau derulare `passive` pentru a îmbunătăți capacitatea de derulare a paginii. [Află mai multe](https://web.dev/uses-passive-event-listeners)."}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Nu folosește ascultători pasivi pentru a îmbunătăți performanța la derulare"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Folosește ascultători pasivi pentru a îmbunătăți performanța la derulare"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "Des<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "Erorile înregistrate pe consolă indică probleme nerezolvate Acestea pot fi provocate de erorile de solicitare din rețea și de alte probleme ale browserului. [Află mai multe](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "S-au înregistrat erori de browser pe consolă"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "Nu s-a înregistrat nicio eroare de browser pe consolă"}, "lighthouse-core/audits/font-display.js | description": {"message": "Poți folosi funcția CSS de afișare a fonturilor pentru a verifica dacă textul este vizibil pentru utilizatori în timp ce se încarcă fonturile web. [Află mai multe](https://web.dev/font-display)."}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "Asigură-te că textul rămâne vizibil în timpul încărcării fonturilor web"}, "lighthouse-core/audits/font-display.js | title": {"message": "Tot textul rămâne vizibil în timpul încărcării fonturilor web"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "Lighthouse nu a putut verifica automat valoarea de afișare a fonturilor pentru următoarea adresă URL: {fontURL}."}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "Raport de dimensiuni (real)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Raport de dimensiuni (afișat)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "Dimensiunile de afișare a imaginilor trebuie să se potrivească raportului natural de dimensiuni. [Află mai multe](https://web.dev/image-aspect-ratio)."}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "Afișează imaginile cu rapoarte de dimensiuni incorecte"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "Afișează imaginile cu rapoarte de dimensiuni corecte"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "Informații nevalide privind dimensiunea imaginilor {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "Browserele le pot solicita utilizatorilor să adauge aplicația pe ecranele de pornire, lucru care poate duce la o implicare mai mare. [Află mai multe](https://web.dev/installable-manifest)."}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "Manifestul aplicației web nu îndeplinește cerințele de instalare"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "Manifestul aplicației web îndeplinește cerințele de instalare"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "Adresă URL nesigură"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "Toate site-urile trebuie protejate cu HTTPS, chiar și cele care nu gestionează date sensibile. HTTPS împiedică persoanele străine să modifice sau să asculte pasiv comunicațiile dintre aplicația ta și utilizatori, fiind o cerință obligatorie pentru HTTP/2 și multe API-uri noi pentru platformele web. [Află mai multe](https://web.dev/is-on-https)."}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{S-a găsit o solicitare nesigură}few{S-au găsit # solicitări nesigure}other{S-au găsit # de solicitări nesigure}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "Nu folosește HTTPS"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "Folosește HTTPS"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "O încărcare rapidă a paginii într-o rețea celulară asigură o experiență bună a utilizatorului pe mobil. [Află mai multe](https://web.dev/load-fast-enough-for-pwa)."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "Interactiv la {timeInMs, number, seconds} s"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "Interactivă pe rețeaua mobilă simulată la {timeInMs, number, seconds} s"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "Pagina se încarcă prea lent și nu este interactivă în 10 secunde. Analizează oportunitățile și diagnosticele din secțiunea „Performanță” ca să afli cum s-o îmbunătățești."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "Încărcarea paginii nu este suficient de rapidă în rețele mobile"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "Încărcarea paginii este suficient de rapidă în rețele mobile"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Categorie"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "Poți reduce timpul petrecut cu analizarea, compilarea și executarea JS. Livrarea unor sarcini JS mai mici poate ajuta în acest sens. [Află mai multe](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimizează procesarea firului principal"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "Minimizează procesarea firului principal"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "Ca să se adreseze cât mai multor utilizatori, site-urile trebuie să funcționeze în toate browserele importante. [Află mai multe](https://web.dev/pwa-cross-browser)."}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "Site-ul funcționează pe orice browser"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Asigură-te că se pot crea linkuri directe către pagini individuale prin adresa URL și că adresele URL sunt unice, pentru a putea fi trimise în rețelele sociale. [Află mai multe](https://web.dev/pwa-each-page-has-url)."}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Fiecare pagină are o adresă URL"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "Tranzițiile trebuie să aibă loc repede atunci când atingi ecranul, chiar și în rețele lente. Această experiență este esențială pentru performanța percepută de utilizator. [Află mai multe](https://web.dev/pwa-page-transitions)."}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "Tranzițiile de pagini nu par să blocheze rețeaua"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "Latența estimată a comenzilor reprezintă o estimare a duratei necesare pentru ca aplicația să răspundă la comanda utilizatorului, în milisecunde, în cea mai aglomerată fereastră de cinci secunde de încărcare a paginii. Dacă latența este mai mare de 50 ms, utilizatorii îți pot percepe aplicația ca fiind lentă. [Află mai multe](https://web.dev/estimated-input-latency)."}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "Latența estimată a comenzilor"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "Prima redare de conținut arată momentul când se redă primul text sau prima imagine. [Află mai multe](https://web.dev/first-contentful-paint)."}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "Prima redare de conținut"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "Primul „CPU inactiv” semnalează primul moment în care firul principal al paginii este suficient de liber pentru a accepta comenzi.  [Află mai multe](https://web.dev/first-cpu-idle)."}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "Primul „CPU inactiv”"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "Prima redare semnificativă arată momentul când este vizibil conținutul principal al unei pagini. [Află mai multe](https://web.dev/first-meaningful-paint)."}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "Prima redare semnificativă"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "Timpul până la interactivitate este timpul necesar pentru ca pagina să devină complet interactivă. [Află mai multe](https://web.dev/interactive)."}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "Timpul până la interactivitate"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "Timpul maxim potențial de la prima interacțiune cu care utilizatorii se pot confrunta este durata, în milisecunde, a celei mai lungi activități. [Află mai multe](https://developers.google.com/web/updates/2018/05/first-input-delay)."}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "Timpul maxim potențial de la prima interacțiune"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "Indexul de viteză arată cât de repede se completează vizibil conținutul unei pagini. [Află mai multe](https://web.dev/speed-index)."}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "Index de viteză"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "Suma tuturor perioadelor de timp dintre FCP și Timpul până la interactivitate, când lungimea activității a depășit 50 ms, exprimate în milisecunde."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "Durata totală de blocare"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "Durata pingului în rețea (RTT) are impact important asupra performanței. Dacă RTT către o origine este mare, înseamnă că serverele mai apropiate de utilizator pot îmbunătăți performanța. [Află mai multe](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "lighthouse-core/audits/network-rtt.js | title": {"message": "Durata pingului în rețea"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "Latențele serverului pot influența rezultatele pe web. Dacă latența serverului unei origini este mare, înseamnă că serverul este supraîncărcat sau are performanțe slabe în backend. [Află mai multe](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "Latențe ale backendului serverului"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "Un service worker permite ca aplicația să fie fiabilă în condiții de rețea imprevizibile. [Află mai multe](https://web.dev/offline-start-url)."}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url` nu răspunde cu codul de stare 200 când este offline"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url` ră<PERSON>unde cu codul de stare 200 când este offline"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "Lighthouse nu a putut citi `start_url` din manifest. <PERSON>rin urmare, s-a presupus că `start_url` este adresa URL a documentului. <PERSON>j de eroare: „{manifestWarning}”."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "Peste buget"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "Păstrează cantitatea și dimensiunea solicitărilor din rețea sub obiectivele stabilite de bugetul de performanță solicitat. [Află mai multe](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{O solicitare}few{# solicitări}other{# de solicitări}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "Buget de performanță"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "Dacă ai configurat deja HTTPS, redirecționează tot traficul HTTP spre HTTPS, ca să activezi funcțiile web sigure pentru toți utilizatorii. [Află mai multe](https://web.dev/redirects-http)."}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "Nu redirecționează traficul HTTP spre HTTPS"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "Redirecționează traficul HTTP spre HTTPS"}, "lighthouse-core/audits/redirects.js | description": {"message": "Redirecționările introduc întârzieri suplimentare înainte ca pagina să se poată încărca. [Află mai multe](https://web.dev/redirects)."}, "lighthouse-core/audits/redirects.js | title": {"message": "Evită mai multe redirecționări ale paginii"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "Ca să setezi bugetele pentru cantitatea și dimensiunea resurselor de pagină, adaugă un fișier budget.json. [Află mai multe](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{O solicitare • {byteCount, number, bytes} KB}few{# solicitări • {byteCount, number, bytes} KB}other{# de solicitări • {byteCount, number, bytes} KB}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "Păstrează numărul de solicitări scăzut și dimensiunea transferurilor redusă"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "Linkurile canonice sugerează care adresă URL să se afișeze în rezultatele căutării. [Află mai multe](https://web.dev/canonical)."}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "<PERSON> multe adrese URL incompatibile ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "Indică spre alt domeniu ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "Adresă URL nevalidă ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Indică spre altă locație `hreflang` ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "Adresa URL relativă ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "Direcționează către adresa URL a rădăcinii domeniului (pagina principală), nu către o pagină de conținut echivalentă"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "Documentul nu are un `rel=canonical` valid"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "Documentul are un `rel=canonical` valid"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "Dimensiunile fontului mai mici de 12 pixeli sunt prea mici pentru a putea fi citite, iar utilizatorii de dispozitive mobile trebuie să „ciupească pentru zoom” pentru a citi. Încearcă să ai peste 60 % din textul paginii cu font ≥12 px. [Află mai multe](https://web.dev/font-size)."}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "Text lizibil {decimalProportion, number, extendedPercent}"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "Textul nu este lizibil deoarece nu există nicio metaetichetă viewport optimizată pentru ecranele dispozitivelor mobile."}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "{decimalProportion, number, extendedPercent} din text este prea mic (pornind de la un eșantion de {decimalProportionVisited, number, extendedPercent})."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "Documentul nu folosește dimensiuni de fonturi lizibile"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "Documentul folosește dimensiuni de font lizibile"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "<PERSON><PERSON><PERSON> hreflang spun motoarelor de căutare ce versiune a unei pagini să înregistreze în rezultatele căutării pentru o limbă sau regiune dată. [Află mai multe](https://web.dev/hreflang)."}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "Document nu are un `hreflang` valid"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "Documentul are un `hreflang` valid"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "Este posibil ca paginile cu Coduri de stare HTTP nefuncționale să nu fie indexate corect. [Află mai multe](https://web.dev/http-status-code)."}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "Pagina are Cod de stare HTTP nefuncțional"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "Pagina are Cod de stare HTTP funcțional"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "Motoarele de căutare nu pot include paginile în rezultatele căutării dacă nu au permisiunea de a le accesa cu crawlere. [Află mai multe](https://web.dev/is-crawable)."}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "Indexarea este blocată pentru pagină"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "Indexarea nu este blocată pentru pagină"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "Textul descriptiv al linkului ajută motoarele de căutare să înțeleagă conținutul. [Află mai multe](https://web.dev/link-text)."}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{S-a găsit un link}few{S-au găsit # linkuri}other{S-au găsit # de linkuri}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "Linkurile nu au text descriptiv"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "Linkurile au text descriptiv"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "Rulează [Instrumentul de testare pentru date structurate](https://search.google.com/structured-data/testing-tool/) și [Structured Data Linter](http://linter.structured-data.org/) pentru a valida datele structurate. [Află mai multe](https://web.dev/structured-data)."}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "Datele structurate sunt valide"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "Metadescrierile pot fi incluse în rezultatele căutării pentru a rezuma conținutul paginii. [Află mai multe](https://web.dev/meta-description)."}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "Textul de descriere nu este completat."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "Documentul nu are o metadescriere"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "Documentul are o metadescriere"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "Motoarele de căutare nu pot indexa conținutul pluginurilor și multe dispozitive restricționează pluginurile sau nu le acceptă. [Află mai multe](https://web.dev/plugins)."}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "Documentul folosește pluginuri"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "Documentul evită pluginurile"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "Dacă fișierul robots.txt este deteriorat, este posibil ca aplicațiile crawler să nu poată înțelege cum vrei să fie site-ul tău accesat cu crawlere sau indexat. [Află mai multe](https://web.dev/robots-txt)."}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Cererea pentru robots.txt a returnat starea HTTP: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{A fost identificată o eroare}few{Au fost identificate # erori}other{Au fost identificate # de erori}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse nu a putut să descarce un fișier robots.txt"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "Fișierul robots.txt nu este valid"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "Fișierul robots.txt este valid"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "Elementele interactive, precum butoanele și linkurile, trebuie să fie suficient de mari (48x48 px) și să aibă suficient spațiu în jurul lor, pentru a fi ușor de atins, fără a se suprapune peste alte elemente. [Află mai multe](https://web.dev/tap-targets)."}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} direcționări ale atingerilor au mărimea corectă"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Direcționările atingerilor sunt prea mici, deoarece nu există o metaetichetă Viewport optimizată pentru ecranele dispozitivelor mobile"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "Direcționările atingerilor nu sunt dimensionate corect"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Direcționarea se suprapune"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Direcționarea atingerii"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "Direcționările atingerilor sunt dimensionate corect"}, "lighthouse-core/audits/service-worker.js | description": {"message": "Service worker este tehnologia care îi permite aplicației să folosească mai multe funcții de aplicații web progresive, cum ar fi cele offline, adăugarea în ecranul de pornire și notificările push. [Află mai multe](https://web.dev/service-worker)."}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "Pagina este controlată de un service worker, dar nu s-a g<PERSON><PERSON><PERSON> niciun `start_url`, deoarece manifestul nu s-a putut analiza ca un JSON valid"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "Pagina este controlată de un service worker, dar `start_url` ({startUrl}) nu este în aria de acoperire a acestuia ({scopeUrl})"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "Pagina este controlată de un service worker, dar nu s-a găsit un `start_url`, deoarece nu s-a preluat niciun manifest."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "Această origine are unul sau mai multe elemente service worker, însă pagina ({pageUrl}) nu este în aria de acoperire."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "Nu înregistrează un service worker care să controleze pagina și `start_url`"}, "lighthouse-core/audits/service-worker.js | title": {"message": "Înregistrează un service worker care să controleze pagina și `start_url`"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "Un ecran de întâmpinare tematic asigură o experiență de calitate atunci când utilizatorii lansează aplicația din ecranele de pornire. [Află mai multe](https://web.dev/splash-screen)."}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "Nu este configurat pentru un ecran de întâmpinare personalizat"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "Configurat pentru un ecran de întâmpinare personalizat"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "Bara de adrese din browser poate avea o temă ce corespunde cu site-ul tău. [Află mai multe](https://web.dev/themed-omnibox)."}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "Nu setează o culoare tematică pentru bara de adrese."}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "Setează o culoare tematică pentru bara de adrese."}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "Timpul de blocare a firului principal"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "Terță parte"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "Codul terță parte poate influența semnificativ performanța de încărcare. Limitează numărul de furnizori terță parte redundanți și încearcă să încarce cod terță parte după ce pagina a terminat încărcarea de bază. [Află mai multe](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "Codul terță parte a blocat firul principal pentru {timeInMs, number, milliseconds} ms"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "Redu impactul codului de la terți"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "Utilizarea terță parte"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "Timpul până la primul byte identifică timpul în care serverul trimite un răspuns. [Află mai multe](https://web.dev/time-to-first-byte)."}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "Documentul rădăcină a avut nevoie de {timeInMs, number, milliseconds} ms"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "Redu timpii de răspuns ai serverului (TTFB)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "Serverul răspunde repede (TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "Ora de începere"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "Tip"}, "lighthouse-core/audits/user-timings.js | description": {"message": "Îți poți îmbunătăți aplicația cu API-ul User Timing ca să măsori performanța reală a acesteia în timpul experiențelor de utilizare principale. [Află mai multe](https://web.dev/user-timings)."}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{o durată a utilizării}few{# durate ale utilizărilor}other{# de durate ale utilizărilor}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "Mărci și dimensiuni pentru Duratele utilizărilor"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Un <link> preconectat a fost găsit pentru „{securityOrigin}”, dar nu a fost folosit de browser. Verifică dacă folosești corect atributul `crossorigin`."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "Adaugă indicii de resurse `preconnect` sau `dns-prefetch` pentru a crea conexiunile inițiale la originile terță parte importante. [Află mai multe](https://web.dev/uses-rel-preconnect)."}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "Preconectează la originile necesare"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Un <link> preîncărcat a fost găsit pentru „{preloadURL}”, dar nu a fost folosit de browser. Verifică dacă folosești corect atributul `crossorigin`."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "<PERSON><PERSON>i folosi `<link rel=preload>` ca să acorzi prioritate preluării resurselor care sunt momentan solicitate mai târziu la încărcarea paginii. [Află mai multe](https://web.dev/uses-rel-preload)."}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "Preîncarcă solicitările importante"}, "lighthouse-core/audits/viewport.js | description": {"message": "Adaugă o etichetă `<meta name=\"viewport\">` ca să-ți optimizezi aplicația pentru ecranele mobile. [Află mai multe](https://web.dev/viewport)."}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "Nu s-a gă<PERSON>t nicio etichet<PERSON> `<meta name=\"viewport\">`"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "Nu are o etichetă `<meta name=\"viewport\">` cu `width` sau `initial-scale`"}, "lighthouse-core/audits/viewport.js | title": {"message": "Are o etichetă `<meta name=\"viewport\">` cu `width` sau `initial-scale`"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "Aplicația ta trebuie să afișeze o parte de conținut când JavaScript este dezactivat, chiar dacă este doar o atenționare pentru utilizator că JavaScript este obligatoriu pentru a folosi aplicația. [Află mai multe](https://web.dev/without-javascript)."}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "Corpul paginii trebuie să redea o parte de conținut dacă scripturile sale nu sunt disponibile."}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "Nu oferă conținut alternativ când JavaScript nu este disponibil"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "Include o parte de conținut când JavaScript nu este disponibil"}, "lighthouse-core/audits/works-offline.js | description": {"message": "Dacă creezi o aplicație web progresivă, poți folosi un service worker pentru ca aplicația să poată funcționa offline. [Află mai multe](https://web.dev/works-offline)."}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "Pagina curentă nu răspunde cu codul de stare 200 când este offline"}, "lighthouse-core/audits/works-offline.js | title": {"message": "Pagina curentă răspunde cu codul de stare 200 când este offline"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "Este posibil ca pagina să nu se încarce offline, deoarece adresa URL de testare ({requested}) a fost redirecționată spre „{final}”. Încearcă să testezi direct a doua adresă URL."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "Acestea sunt oportunități de a îmbunătăți utilizarea ARIA în aplicație, lucru care poate îmbunătăți experiența pentru utilizatorii tehnologiei care asigură asistență, precum un cititor de ecran."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Acestea sunt oportunități de a oferi conținut secundar audio și video. Astfel se poate îmbunătăți experiența utilizatorilor cu deficiențe de auz sau de vedere."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Audio și video"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Aceste elemente evidențiază cele mai bune practici privind accesibilitatea."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "<PERSON><PERSON> mai bune practici"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "Aceste verificări evidențiază oportunitățile pentru a [îmbunătăți accesibilitatea aplicației web](https://developers.google.com/web/fundamentals/accessibility). Doar un subset de probleme de accesibilitate pot fi detectate automat, astfel încât se recomandă și testarea manuală."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "Aceste elemente se adresează zonelor pe care un instrument de testare automată nu le poate acoperi. Află mai multe din ghidul nostru despre [realizarea unei evaluări a accesibilității](https://developers.google.com/web/fundamentals/accessibility/how-to-review)."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "Accesibilitate"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Acestea sunt oportunități de a-ți îmbunătăți lizibilitatea conținutului."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Contrast"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Acestea sunt oportunități de a îmbunătăți interpretarea conținutului tău de utilizatori, în diferite coduri locale."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internaționalizare și localizare"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Acestea sunt oportunități de a îmbunătăți semantica opțiunilor de control din aplicația ta. Astfel se poate îmbunătăți experiența utilizatorilor de tehnologii care asigură asistență, precum un cititor de ecran."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Nume și etichete"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Acestea sunt oportunități de a îmbunătăți navigarea pe tastatură în aplicația ta."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navigare"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Acestea sunt oportunități de a îmbunătăți experiența citirii datelor din tabele sau din liste folosind tehnologia care asigură asistență, cum ar fi un cititor de ecran."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tabele și liste"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "<PERSON><PERSON> mai bune practici"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "Bugetele de performanță stabilesc standarde pentru performanța site-ului."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "Bugete"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "Mai multe informații despre performanța aplicației tale. Numerele nu [influențează direct](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) Scorul de performanță."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnosticare"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Cel mai important aspect al performanței este cât de repede se redau pixelii pe ecran. Valori cheie: prima redare de conținut, prima redare semnificativă"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Îmbunătățirile pentru prima redare"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Aceste sugestii îți pot face pagina să se încarce mai repede. Ele nu [influențează direct](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) scorul de performanță."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Oportunități"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "Valori"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Îmbunătățește experiența globală de încărcare, astfel ca pagina să se afișeze și să fie gata de utilizare cât mai curând posibil. Valori cheie: timpul până la interactivitate, indexul de viteză"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Îmbunătățiri generale"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "Performanță"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "Aceste verificări validează aspectele unei aplicații web progresive. [Află mai multe](https://developers.google.com/web/progressive-web-apps/checklist)."}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "Aceste verificări sunt impuse de [Lista de verificare PWA](https://developers.google.com/web/progressive-web-apps/checklist) de referință, dar nu sunt verificate automat de Lighthouse. Ele nu-ți afectează scorul, dar e important să fie făcute manual."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "Aplicație web progresivă"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "Rapid și sigur"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Poate fi instalat"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Optimizat pentru PWA"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "Aceste verificări asigură că pagina este optimizată pentru poziționarea în rezultatele motorului de căutare. Există alți factori pe care Lighthouse nu îi verifică și care pot afecta poziția în rezultatele căutării. [Află mai multe](https://support.google.com/webmasters/answer/35769)."}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "Rulează acești validatori suplimentari pe site pentru a căuta alte recomandări SEO."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "Formatează codul HTML în așa fel încât crawlerele să înțeleagă mai bine conținutul aplicației."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "<PERSON><PERSON> mai bune practici privind conținutul"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Pentru a apărea în rezultatele căutării, crawlerele trebuie să îți acceseze aplicația."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Accesarea cu crawlere și indexarea"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "Asigură-te că paginile sunt create pentru dispozitivele mobile, pentru ca utilizatorii să nu trebuiască să ciupească sau să mărească pentru a citi paginile conținutului. [Află mai multe](https://developers.google.com/search/mobile-sites/)."}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "Pentru dispozitivele mobile"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "TTL cache"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "Locație"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "Nume"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "Tip de resursă"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "<PERSON><PERSON> petre<PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "Dimensiunea transferului"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "Adresa URL"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Economii potențiale"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "Economii potențiale"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Poți economisi {wastedBytes, number, bytes} KB"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Poți economisi {wastedMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "Document"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "Font"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "Imagine"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "Media"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "Altele"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "<PERSON><PERSON><PERSON> de stil"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Terță parte"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "Total"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "A apărut o eroare la înregistrarea urmei de la încărcarea paginii. Rulează din nou Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "Timpul a expirat așteptând conexiunea inițială la protocolul Debugger."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome nu a colectat capturi de ecran în timpul încărcării paginii. Asigură-te că există conținut vizibil în pagină și încearcă să rulezi din nou Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "Serverele DNS nu au putut rezolva domeniul furnizat."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Colectorul de {artifactName} obligatorii a întâmpinat o eroare: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "A apărut o eroare Chrome internă. Repornește Chrome și încearcă să rulezi din nou Lighthouse."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "Colectorul {artifactName} obligatoriu nu a rulat."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse nu a putut încărca în mod sigur pagina solicitată. Asigură-te că testezi adresa URL corectă și că serverul răspunde corect la toate solicitările."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse nu a putut încărca în mod sigur adresa URL solicitată, deoarece pagina nu mai răspunde."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Adresa URL furnizată nu are un certificat de securitate valid. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome a împiedicat încărcarea paginii cu un interstițial. Asigură-te că testezi adresa URL corectă și că serverul răspunde corect la toate solicitările."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse nu a putut încărca în mod sigur pagina solicitată. Asigură-te că testezi adresa URL corectă și că serverul răspunde corect la toate solicitările. (Detalii: {errorDetails})"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse nu a putut încărca în mod sigur pagina solicitată. Asigură-te că testezi adresa URL corectă și că serverul răspunde corect la toate solicitările. (Cod de stare: {statusCode})"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Încărcarea paginii a durat prea mult. Urmează recomandările din raport pentru a reduce durata de încărcare a paginii, apoi încearcă din nou să rulezi Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "Așteptarea răspunsului la protocolul DevTools a depășit timpul alocat. (Metoda: {protocolMethod})"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "Preluarea conținutului resursei a depășit timpul alocat"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "Adresa URL furnizată pare să fie nevalidă."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "Afișeaz<PERSON> audituri"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "Navigare inițială"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "Latența maximă a căii critice:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "E<PERSON>re!"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "Eroare de raport: nu există informații de auditare"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "<PERSON><PERSON> testului"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "<PERSON><PERSON><PERSON> [Lighthouse](https://developers.google.com/web/tools/lighthouse/) a paginii actuale cu o rețea mobilă simulată. Valorile sunt estimate și pot varia."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "Elemente suplimentare de verificat manual"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "Nu se aplică"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "Oportunitate"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "Economii estimate"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> tre<PERSON>"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "Restrânge fragmentul"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "Extinde fragmentul"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "Afișează resursele terță parte"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "Au apărut probleme care au afectat această rulare Lighthouse:"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "Valorile sunt estimate și pot varia. Scorul de performanță [se bazează doar pe aceste valori](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> trecute, dar cu avertismente"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "Avertismente: "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "Îți recomandăm să încarci GIF-ul într-un serviciu care îl va pune dispoziție pentru încorporare ca videoclip HTML5."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "Instalează un [plugin WordPress de încărcare lentă](https://wordpress.org/plugins/search/lazy+load/) care oferă capacitatea de a amâna imaginile din afara ecranului sau schimbă-ți tema cu una care oferă acea funcție. Îți recomandăm [pluginul AMP](https://wordpress.org/plugins/amp/)."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "Există o serie de pluginuri Wordpress care te pot ajuta să [activezi inline elementele critice](https://wordpress.org/plugins/search/critical+css/) sau să [amâni resurse mai puțin importante](https://wordpress.org/plugins/search/defer+css+javascript/). Atenție, optimizările oferite de aceste pluginuri pot să întrerupă funcțiile temei sau pluginurilor tale, astfel încât este posibil să trebuiască să modifici codul."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "<PERSON><PERSON><PERSON>, pluginurile și specificațiile serverului contribuie toate la timpul de răspuns al serverului. Îți recomandăm să găsești o temă mai optimizată, selectând cu atenție un plugin de optimizare și/sau făcând upgrade la server."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "Îți recomandăm să afișezi extrase în listele postărilor tale (de exemplu, prin intermediul filei Mai multe), reducând numărul de postări afișate pe o anumită pagină, împărțind postările lungi pe mai multe pagini sau folosind un plugin pentru a încărca lent comentariile."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "<PERSON> multe [pluginuri WordPress](https://wordpress.org/plugins/search/minify+css/) îți pot accelera site-ul prin concatenarea, minimizarea și comprimarea stilurilor. Ai putea să folosești și un proces de design pentru a face minimizarea în avans, dacă este posibil."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "<PERSON> multe [pluginuri WordPress](https://wordpress.org/plugins/search/minify+javascript/) îți pot accelera site-ul prin concatenarea, minimizarea și comprimarea scripturilor. Ai putea să folosești și un proces de design pentru a face minimizarea în avans, dacă este posibil."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "Îți recomandăm să reduci sau să schimbi numărul de [pluginuri WordPress](https://wordpress.org/plugins/) care încarcă CSS nefolosit în pagină. Ca să identifici pluginurile care adaugă conținut CSS neesențial, încearcă să rulezi [acoperirea codului](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) în Chrome DevTools. Poți identifica tema/pluginul responsabil din adresa URL a foii de stil. Caută pluginuri care au multe foi de stil în listă cu mult roșu în bara acoperirii codului. Un plugin ar trebui să pună în coadă o foaie de stil numai dacă este într-adevăr folosită în pagină."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "Îți recomandăm să reduci sau să schimbi numărul de [pluginuri WordPress](https://wordpress.org/plugins/) care încarcă JavaScript nefolosit în pagină. Ca să identifici pluginurile care adaugă conținut JS neesențial, încearcă să rulezi [acoperirea codului](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) în Chrome DevTools. Poți identifica tema/pluginul responsabil din adresa URL a scriptului. Caută pluginuri care au multe scripturi în listă cu mult roșu în bara acoperirii codului. Un plugin ar trebui să pună în coadă un script numai dacă este într-adevăr folosit în pagină."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "Citește despre [stocarea în memoria cache a browserului în WordPress](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "Îți recomandăm să folosești un [plugin WordPress de optimizare a imaginii](https://wordpress.org/plugins/search/optimize+images/) care comprimă imaginile, păstrând calitatea."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "Încarcă imaginile direct prin [biblioteca de media](https://codex.wordpress.org/Media_Library_Screen) pentru a te asigura că sunt disponibile dimensiunile de imagine necesare, apoi inserează-le din biblioteca de media sau folosește widgetul de imagine pentru a te asigura că se folosesc dimensiunile de imagine optime (inclusiv cele pentru punctele de întrerupere adaptabile). Evită să folosești imagini `Full Size`, cu excepția cazului în care dimensiunile sunt adecvate pentru utilizare. [Află mai multe](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)."}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "Poți activa comprimarea textului în configurarea serverului web."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "Îți recomandăm să folosești un [plugin](https://wordpress.org/plugins/search/convert+webp/) sau un serviciu care convertește automat imaginile încărcate în formatele optime."}}