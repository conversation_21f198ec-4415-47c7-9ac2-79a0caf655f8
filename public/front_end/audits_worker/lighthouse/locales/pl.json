{"lighthouse-core/audits/accessibility/accesskeys.js | description": {"message": "<PERSON>law<PERSON><PERSON> dostępu umożliwiają szybkie ustawienie fokusu na określonej części strony. Aby nawigacja działała dobrze, każdy klawisz dostępu musi być unikalny. [Więcej informacji](https://web.dev/accesskeys/)"}, "lighthouse-core/audits/accessibility/accesskeys.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> `[accesskey]` nie są unikalne"}, "lighthouse-core/audits/accessibility/accesskeys.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> `[accesskey]` są unikalne"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON><PERSON> `role` ARIA obsługuje podzbiór atrybutów `aria-*`. Brak ich dopasowania skutkuje niepoprawnością atrybutów `aria-*`. [Więcej informacji](https://web.dev/aria-allowed-attr/)"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Atrybuty `[aria-*]` nie pasują do swoich ról"}, "lighthouse-core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Atrybuty `[aria-*]` od<PERSON><PERSON><PERSON><PERSON><PERSON> swoim rolom"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | description": {"message": "<PERSON>ek<PERSON><PERSON><PERSON> role ARIA mają atry<PERSON> w<PERSON>, które opisują stan elementu na potrzeby czytników ekranu. [Więcej informacji](https://web.dev/aria-required-attr/)"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Elementy z atrybutem `[role]` nie mają wszystkich wymaganych atrybutów `[aria-*]`"}, "lighthouse-core/audits/accessibility/aria-required-attr.js | title": {"message": "Elementy `[role]` mają wszystkie wymagane atrybuty `[aria-*]`"}, "lighthouse-core/audits/accessibility/aria-required-children.js | description": {"message": "Niektóre role nadrz<PERSON>dne ARIA muszą zawierać określone role podrzędne, by pop<PERSON><PERSON> realizować funkcje ułatwień dostępu. [Wi<PERSON><PERSON>j informacji](https://web.dev/aria-required-children/)"}, "lighthouse-core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Elementy z atrybutem ARIA `[role]`, których elementy podrzędne muszą zawierać określony atrybut `[role]`, nie mają niektórych lub wszystkich tych wymaganych elementów podrzędnych."}, "lighthouse-core/audits/accessibility/aria-required-children.js | title": {"message": "Elementy z atrybutem ARIA `[role]`, których elementy podrzędne muszą zawierać określony atrybut `[role]`, mają wszystkie wymagane elementy podrzędne."}, "lighthouse-core/audits/accessibility/aria-required-parent.js | description": {"message": "Niektóre role podrzędne ARIA muszą znajdować się wewnątrz określonych ról nadrzędnych, by poprawnie realizować funkcje ułatwień dostępu. [Wi<PERSON><PERSON>j informacji](https://web.dev/aria-required-parent/)"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Elementy `[role]` nie znajdują się wewnątrz wymaganych elementów nadrzędnych"}, "lighthouse-core/audits/accessibility/aria-required-parent.js | title": {"message": "Elementy `[role]` zna<PERSON><PERSON><PERSON><PERSON> się wewnątrz wymaganych elementów nadrzędnych"}, "lighthouse-core/audits/accessibility/aria-roles.js | description": {"message": "Role ARIA muszą mieć prawidł<PERSON>e wartoś<PERSON>, by poprawnie realizowa<PERSON> funkcje ułatwień dostępu. [Więcej informacji](https://web.dev/aria-roles/)"}, "lighthouse-core/audits/accessibility/aria-roles.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> `[role]` s<PERSON>e"}, "lighthouse-core/audits/accessibility/aria-roles.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> `[role]` s<PERSON>e"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Technologie wspomagające, takie jak czytniki ekranu, nie potrafią interpretować atrybutów ARIA o nieprawidłowej wartości. [Więcej informacji](https://web.dev/aria-valid-attr-value/)"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Atrybuty `[aria-*]` nie maj<PERSON> p<PERSON> war<PERSON>"}, "lighthouse-core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Atrybuty `[aria-*]` maj<PERSON> p<PERSON>e warto<PERSON>ci"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | description": {"message": "Technologie wspomagające, takie jak czytniki ekranu, nie potrafią interpretować atrybutów ARIA o nieprawidłowych nazwach. [Więcej informacji](https://web.dev/aria-valid-attr/)"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Atrybuty `[aria-*]` są niep<PERSON>idłowe lub są w nich literówki"}, "lighthouse-core/audits/accessibility/aria-valid-attr.js | title": {"message": "Atrybuty `[aria-*]` są prawidłowe i nie ma w nich literówek"}, "lighthouse-core/audits/accessibility/audio-caption.js | description": {"message": "Dzięki napisom z elementów audio mogą korzystać osoby niesłyszące i słabosłyszące. Napisy zawierają ważne informacje na przykład o tym, kto i co mówi, a także informacje niedotyczące mowy. [Więcej informacji](https://web.dev/audio-caption/)"}, "lighthouse-core/audits/accessibility/audio-caption.js | failureTitle": {"message": "Elementy `<audio>` nie mają elementu `<track>` z atrybutem `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/audio-caption.js | title": {"message": "Elementy `<audio>` zawierają element `<track>` z atrybutem `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Nieprawidłowe elementy"}, "lighthouse-core/audits/accessibility/button-name.js | description": {"message": "Gdy przycisk nie ma nazwy na potrzeby ułatwień dostępu, czytniki ekranu określają go jako „przycisk”, przez co jest on bezużyteczny dla ich użytkowników. [Więcej informacji](https://web.dev/button-name/)"}, "lighthouse-core/audits/accessibility/button-name.js | failureTitle": {"message": "Przyciski nie mają nazw dostępnych dla czytników ekranu"}, "lighthouse-core/audits/accessibility/button-name.js | title": {"message": "Przyciski mają nazwy dostępne dla czytników ekranu"}, "lighthouse-core/audits/accessibility/bypass.js | description": {"message": "Dodanie sposobu na ominięcie powtarzających się treści ułatwia nawigację na stronie za pomocą klawiatury. [Więcej informacji](https://web.dev/bypass/)"}, "lighthouse-core/audits/accessibility/bypass.js | failureTitle": {"message": "Strona nie zawiera nagłówka, linku pomijającego ani regionu orientacyjnego"}, "lighthouse-core/audits/accessibility/bypass.js | title": {"message": "Strona zawiera nagłówek, link pomijający lub region orientacyjny"}, "lighthouse-core/audits/accessibility/color-contrast.js | description": {"message": "Wielu użytkowników ma problemy z czytaniem tekstu o niskim kontraście. [Więcej informacji](https://web.dev/color-contrast/)"}, "lighthouse-core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Kolory tła i pierwszego planu mają niewystarczający współczynnik kontrastu."}, "lighthouse-core/audits/accessibility/color-contrast.js | title": {"message": "Kolory tła i pierwszego planu mają wystarczający współczynnik kontrastu"}, "lighthouse-core/audits/accessibility/definition-list.js | description": {"message": "Gdy listy definicji nie mają właś<PERSON><PERSON>j struktury, czytniki ekranu mogą odczytywać je niedokładnie lub błędnie. [Więcej informacji](https://web.dev/definition-list/)"}, "lighthouse-core/audits/accessibility/definition-list.js | failureTitle": {"message": "Elementy `<dl>` nie zawierają tylko właściwie uporządkowanych grup elementów `<dt>` i `<dd>` oraz elementów `<script>` lub `<template>`."}, "lighthouse-core/audits/accessibility/definition-list.js | title": {"message": "Elementy `<dl>` zawierają tylko właściwie uporządkowane grupy elementów `<dt>` i `<dd>` oraz elementy `<script>` lub `<template>`."}, "lighthouse-core/audits/accessibility/dlitem.js | description": {"message": "Elementy listy definicji (`<dt>` i `<dd>`) muszą znajdować się wewnątrz nadrzędnego elementu `<dl>`, by mogły je poprawnie odczytać czytniki ekranu. [Więcej informacji](https://web.dev/dlitem/)"}, "lighthouse-core/audits/accessibility/dlitem.js | failureTitle": {"message": "Elementy listy definicji nie znajdują się wewnątrz elementów `<dl>`"}, "lighthouse-core/audits/accessibility/dlitem.js | title": {"message": "Elementy listy definicji znajdują się wewnątrz elementów `<dl>`"}, "lighthouse-core/audits/accessibility/document-title.js | description": {"message": "Tytuł informuje użytkowników czytnika ekranu o ogólnej zawartości strony, a użytkownicy wyszukiwarki mogą dowiedzieć się z niego, czy strona zawiera szukane informacje. [Więcej informacji](https://web.dev/document-title/)"}, "lighthouse-core/audits/accessibility/document-title.js | failureTitle": {"message": "W dokumencie nie ma elementu `<title>`"}, "lighthouse-core/audits/accessibility/document-title.js | title": {"message": "Dokument zawiera element `<title>`"}, "lighthouse-core/audits/accessibility/duplicate-id.js | description": {"message": "Wartość atrybutu id musi być unikalna, by inne wystąpienia nie zostały pominięte przez technologie wspomagające. [Więcej informacji](https://web.dev/duplicate-id/)"}, "lighthouse-core/audits/accessibility/duplicate-id.js | failureTitle": {"message": "Atrybuty `[id]` na stronie nie są unikalne"}, "lighthouse-core/audits/accessibility/duplicate-id.js | title": {"message": "Atrybuty `[id]` na stronie są unikalne"}, "lighthouse-core/audits/accessibility/frame-title.js | description": {"message": "Tytuły ramek służą użytkownikom czytników ekranu jako opisy zawartości ramek. [Więcej informacji](https://web.dev/frame-title/)"}, "lighthouse-core/audits/accessibility/frame-title.js | failureTitle": {"message": "Element `<frame>` lub `<iframe>` nie ma tytułu"}, "lighthouse-core/audits/accessibility/frame-title.js | title": {"message": "Elementy `<frame>` i `<iframe>` mają tytuł"}, "lighthouse-core/audits/accessibility/html-has-lang.js | description": {"message": "<PERSON><PERSON><PERSON> strona nie ma atrybutu lang, czytnik ekranu przy<PERSON>, że strona jest w języku do<PERSON>, kt<PERSON>ry użytkownik wybrał podczas konfigurowania czytnika. Jeśli strona nie jest w języku do<PERSON>, czytnik ekranu może niepoprawnie wymawiać tekst strony. [Więcej informacji](https://web.dev/html-has-lang/)"}, "lighthouse-core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Element `<html>` nie ma atrybutu `[lang]`"}, "lighthouse-core/audits/accessibility/html-has-lang.js | title": {"message": "Element `<html>` ma atrybut `[lang]`"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | description": {"message": "Określenie prawidłowego [języka w formacie BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) pomaga czytnikom ekranu prawidłowo wymawiać tekst. [Więcej informacji](https://web.dev/html-lang-valid/)"}, "lighthouse-core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Element `<html>` nie ma prawidłowej wartości atrybutu `[lang]`."}, "lighthouse-core/audits/accessibility/html-lang-valid.js | title": {"message": "Element `<html>` ma prawidłową wartość atrybutu `[lang]`"}, "lighthouse-core/audits/accessibility/image-alt.js | description": {"message": "Elementy informacyjne powinny mi<PERSON> kr<PERSON>, opisowy tekst zastępczy. Elementy dekoracyjne można zignorować, podając pusty atrybut alt. [Więcej informacji](https://web.dev/image-alt/)"}, "lighthouse-core/audits/accessibility/image-alt.js | failureTitle": {"message": "Elementy graficzne nie mają atrybutów `[alt]`"}, "lighthouse-core/audits/accessibility/image-alt.js | title": {"message": "Elementy graficzne mają atrybuty `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | description": {"message": "<PERSON><PERSON> jako przy<PERSON> `<input>` <PERSON><PERSON><PERSON><PERSON><PERSON> jest obraz, warto dodać tekst zastępczy, by <PERSON><PERSON><PERSON><PERSON><PERSON>żytkownikom czytnika ekranu zrozumienie, do czego służy ten przycisk. [Więcej informacji](https://web.dev/input-image-alt/)"}, "lighthouse-core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Elementy `<input type=\"image\">` nie mają teks<PERSON> `[alt]`"}, "lighthouse-core/audits/accessibility/input-image-alt.js | title": {"message": "Elementy `<input type=\"image\">` mają tekst `[alt]`"}, "lighthouse-core/audits/accessibility/label.js | description": {"message": "Etykiety zapewniają prawidłowe odczytywanie kontrolek formularzy przez technologie wspomagające takie jak czytniki ekranu. [Więcej informacji](https://web.dev/label/)"}, "lighthouse-core/audits/accessibility/label.js | failureTitle": {"message": "Z elementami formularzy nie są powiązane etykiety"}, "lighthouse-core/audits/accessibility/label.js | title": {"message": "Z elementami formularzy są powiązane etykiety"}, "lighthouse-core/audits/accessibility/layout-table.js | description": {"message": "Tabela używana do tworzenia układu graficznego nie powinna zawierać elementów danych, takich jak elementy th czy caption albo atrybut summary, ponieważ mogą one utrudniać korzystanie z czytnika ekranu. [W<PERSON>ęcej informacji](https://web.dev/layout-table/)"}, "lighthouse-core/audits/accessibility/layout-table.js | failureTitle": {"message": "W prezentacyjnych elementach `<table>` są używane elementy `<th>` lub `<caption>` albo atrybut `[summary]`."}, "lighthouse-core/audits/accessibility/layout-table.js | title": {"message": "W prezentacyjnych elementach `<table>` nie są używane elementy `<th>` i `<caption>` ani atrybut `[summary]`."}, "lighthouse-core/audits/accessibility/link-name.js | description": {"message": "Tekst linków (i tekst zastępczy obrazów używanych jako linki), kt<PERSON>ry jest charakterystyczny, unikalny i możliwy do wybrania, ułatwia nawigację użytkownikom czytników ekranu. [Więcej informacji](https://web.dev/link-name/)"}, "lighthouse-core/audits/accessibility/link-name.js | failureTitle": {"message": "Linki nie mają wyróżniających je nazw"}, "lighthouse-core/audits/accessibility/link-name.js | title": {"message": "Linki mają wyróżniające je nazwy"}, "lighthouse-core/audits/accessibility/list.js | description": {"message": "Czytniki ekranu odczytują listy w specjalny sposób. Właściwa struktura list pomaga czytnikom poprawnie odczytać tekst. [Więcej informacji](https://web.dev/list/)"}, "lighthouse-core/audits/accessibility/list.js | failureTitle": {"message": "Listy nie zawierają tylko elementów `<li>` i elementów skryptowych (`<script>` i `<template>`)."}, "lighthouse-core/audits/accessibility/list.js | title": {"message": "Listy zawierają tylko elementy `<li>` i elementy skryptowe (`<script>` i `<template>`)."}, "lighthouse-core/audits/accessibility/listitem.js | description": {"message": "Elementy list (`<li>`) muszą być zawarte w elementach nadrzędnych `<ul>` lub `<ol>`, by czytniki ekranu mogły je poprawnie odczytać. [Więcej informacji](https://web.dev/listitem/)"}, "lighthouse-core/audits/accessibility/listitem.js | failureTitle": {"message": "Elementy list (`<li>`) nie znajdują się wewnątrz elementów nadrzędnych `<ul>` lub `<ol>`."}, "lighthouse-core/audits/accessibility/listitem.js | title": {"message": "Elementy list (`<li>`) znajdują się wewnątrz elementów nadrzędnych `<ul>` lub `<ol>`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | description": {"message": "Użytkownicy nie spodziewają się automatycznego odświeżania strony – powoduje ono powrót zaznaczenia na jej początek. Może to dezorientować i irytować użytkowników. [Więcej informacji](https://web.dev/meta-refresh/)"}, "lighthouse-core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Dokument używa tagu `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-refresh.js | title": {"message": "Dokument nie używa tagu `<meta http-equiv=\"refresh\">`"}, "lighthouse-core/audits/accessibility/meta-viewport.js | description": {"message": "Wyłączenie powiększania to problem dla użytkowników niedowid<PERSON>, którzy muszą korzystać z powiększenia ekranu, by do<PERSON><PERSON><PERSON> w<PERSON><PERSON><PERSON> z<PERSON> stron internetowych. [Więcej informacji](https://web.dev/meta-viewport/)"}, "lighthouse-core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "W elemencie `<meta name=\"viewport\">` jest u<PERSON><PERSON><PERSON>y atrybut `[user-scalable=\"no\"]` lub atrybut `[maximum-scale]` ma warto<PERSON>ć mniejszą niż 5."}, "lighthouse-core/audits/accessibility/meta-viewport.js | title": {"message": "W elemencie `<meta name=\"viewport\">` nie jest używany atrybut `[user-scalable=\"no\"]`, a atrybut `[maximum-scale]` ma wartość nie mniejszą niż 5."}, "lighthouse-core/audits/accessibility/object-alt.js | description": {"message": "Czytniki ekranu nie potrafią tłumaczyć treści innych niż tekst. Dodanie do elementów `<object>` tekstu zastępczego pomaga czytnikom ekranu w przekazywaniu użytkownikom właściwego znaczenia. [Więcej informacji](https://web.dev/object-alt/)"}, "lighthouse-core/audits/accessibility/object-alt.js | failureTitle": {"message": "Elementy `<object>` nie mają tekstu `[alt]`"}, "lighthouse-core/audits/accessibility/object-alt.js | title": {"message": "Elementy `<object>` mają tekst `[alt]`"}, "lighthouse-core/audits/accessibility/tabindex.js | description": {"message": "Wartość większa niż 0 implikuje określoną wprost kolejność nawigacji. Chociaż takie rozwiązanie jest technicznie poprawne, często powoduje frustrację użytkowników technologii wspomagających. [Więcej informacji](https://web.dev/tabindex/)"}, "lighthouse-core/audits/accessibility/tabindex.js | failureTitle": {"message": "Niektóre elementy mają atrybut `[tabindex]` o wartości większej niż 0"}, "lighthouse-core/audits/accessibility/tabindex.js | title": {"message": "Żaden element nie ma wartości atrybutu `[tabindex]` większej niż 0"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | description": {"message": "Czytniki ekranu mają <PERSON>, które ułatwiają nawigację w tabelach. Gdy komórki `<td>` używające atrybutu `[headers]` odwołują się tylko do innych komórek w tej samej tabeli, użytkownicy czytników ekranu mogą wygodniej korzystać z tabel. [Więcej informacji](https://web.dev/td-headers-attr/)"}, "lighthouse-core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Komórki w elemencie `<table>`, które używają atrybutu `[headers]`, odwołują się do elementu `id`, którego nie znaleziono w tej samej tabeli."}, "lighthouse-core/audits/accessibility/td-headers-attr.js | title": {"message": "Komórki w elemencie `<table>`, które używają atrybutu `[headers]`, odwołują się do komórek w tej samej tabeli."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | description": {"message": "Czytniki ekranu mają <PERSON>, które ułatwiają nawigację w tabelach. Gdy nagłówki tabel zawsze odwołują się do jakiegoś zbioru komórek, użytkownicy czytników ekranu mogą wygodniej korzystać z tabel. [Więcej informacji](https://web.dev/th-has-data-cells/)"}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "<PERSON>e istnieją komórki danych opisywane przez elementy `<th>` i elementy z atrybutem `[role=\"columnheader\"/\"rowheader\"]`."}, "lighthouse-core/audits/accessibility/th-has-data-cells.js | title": {"message": "Istnieją komórki danych opisywane przez elementy `<th>` i elementy z atrybutem `[role=\"columnheader\"/\"rowheader\"]`."}, "lighthouse-core/audits/accessibility/valid-lang.js | description": {"message": "Określenie w elementach prawidłowego [tagu języka w formacie BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) pomaga zapewnić prawidłową wymowę tekstu przez czytnik ekranu. [Więcej informacji](https://web.dev/valid-lang/)"}, "lighthouse-core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Atrybuty `[lang]` nie maj<PERSON> praw<PERSON>ł<PERSON>"}, "lighthouse-core/audits/accessibility/valid-lang.js | title": {"message": "Atrybuty `[lang]` maj<PERSON> p<PERSON> wartość"}, "lighthouse-core/audits/accessibility/video-caption.js | description": {"message": "Filmy z napisami są bardziej dostępne dla osób niesłyszących i niedosłyszących. [Więcej informacji](https://web.dev/video-caption/)"}, "lighthouse-core/audits/accessibility/video-caption.js | failureTitle": {"message": "Elementy `<video>` nie zaw<PERSON> elementu `<track>` z atrybutem `[kind=\"captions\"]`."}, "lighthouse-core/audits/accessibility/video-caption.js | title": {"message": "Elementy `<video>` zawierają element `<track>` z atrybutem `[kind=\"captions\"]`"}, "lighthouse-core/audits/accessibility/video-description.js | description": {"message": "Audiodeskrypcja dostarcza ważnych informacji, których nie ma w dialogach – na przykład o wyrazie twarzy i scenerii. [Więcej informacji](https://web.dev/video-description/)"}, "lighthouse-core/audits/accessibility/video-description.js | failureTitle": {"message": "Elementy `<video>` nie zaw<PERSON> elementu `<track>` z atrybutem `[kind=\"description\"]`."}, "lighthouse-core/audits/accessibility/video-description.js | title": {"message": "Elementy `<video>` zawierają element `<track>` z atrybutem `[kind=\"description\"]`"}, "lighthouse-core/audits/apple-touch-icon.js | description": {"message": "Zdefiniuj element `apple-touch-icon`, by <PERSON>ja progresywna aplikacja internetowa wyglądała idealnie na iOS, gdy użytkownicy dodadzą ją do ekranu głównego. Element musi wskazywać kwadratowy obraz PNG o rozmiarze 192 (lub 180) pikseli bez przezroczystości. [Więcej informacji](https://web.dev/apple-touch-icon/)"}, "lighthouse-core/audits/apple-touch-icon.js | failureTitle": {"message": "<PERSON><PERSON> <PERSON>a prawidłowego atrybutu `apple-touch-icon`"}, "lighthouse-core/audits/apple-touch-icon.js | precomposedWarning": {"message": "Atrybut `apple-touch-icon-precomposed` jest prz<PERSON><PERSON><PERSON><PERSON><PERSON>. Preferowany jest `apple-touch-icon`."}, "lighthouse-core/audits/apple-touch-icon.js | title": {"message": "Dostarcza prawidłowy atrybut `apple-touch-icon`"}, "lighthouse-core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Rozszerzenia Chrome pogorszyły szybkość ładowania tej strony. Przeprowadź audyt strony w trybie incognito lub w profilu Chrome bez rozszerzeń."}, "lighthouse-core/audits/bootup-time.js | columnScriptEval": {"message": "<PERSON><PERSON><PERSON> skryptu"}, "lighthouse-core/audits/bootup-time.js | columnScriptParse": {"message": "<PERSON><PERSON>za s<PERSON>ów"}, "lighthouse-core/audits/bootup-time.js | columnTotal": {"message": "Łączny czas pracy procesora"}, "lighthouse-core/audits/bootup-time.js | description": {"message": "Pomyśl o skróceniu czasu poświęcanego na analizowanie, kompilowanie i wykonywanie kodu JS. Może w tym pomóc dostarczanie mniejszych ładunków JS. [Więcej informacji](https://web.dev/bootup-time)"}, "lighthouse-core/audits/bootup-time.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> czas wykonywania JavaScriptu"}, "lighthouse-core/audits/bootup-time.js | title": {"message": "Czas wykonania JavaScriptu"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Duże pliki GIF są nieefektywnym sposobem dostarczania animacji. Proponujemy użyć zamiast nich filmów MPEG4/WebM (animacje) lub plików PNG/WebP (obrazy statyczne), by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ilo<PERSON> przesyłanych danych. [Wię<PERSON>j informacji](https://web.dev/efficient-animated-content)"}, "lighthouse-core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Użyj formatów wideo dla animacji"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | description": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> czas do pełnej interaktywności, warto skorzystać z leniwego ładowania. Dzięki temu najpierw będą ładowane wszystkie zasoby kluczowe, a dopiero potem obrazy ukryte i znajdujące się poza ekranem. [Więcej informacji](https://web.dev/offscreen-images)"}, "lighthouse-core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Odłóż ładowanie obrazów poza ekranem"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Zasoby blokują pierwsze wyrenderowanie strony. Sugerujemy umieszczenie krytycznego kodu JS/CSS w kodzie strony i opóźnienie ładowania wszystkich niekrytycznych plików JS i stylów. [Więcej informacji](https://web.dev/render-blocking-resources)"}, "lighthouse-core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Wyeliminuj zasoby blokujące renderowanie"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Duże ładunki sieciowe powodują wyższe koszty dla użytkowników i są mocno powiązane z długim czasem ładowania. [Więcej informacji](https://web.dev/total-byte-weight)"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Łączny rozmiar to {totalBytes, number, bytes} KB"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Unikaj bardzo dużych ładunków sieciowych"}, "lighthouse-core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Unikaj bardzo dużych ładunków sieciowych"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | description": {"message": "Minifikacja plików CSS może zmniejszyć ładunki sieciowe. [Więcej informacji](https://web.dev/unminified-css)"}, "lighthouse-core/audits/byte-efficiency/unminified-css.js | title": {"message": "Minifikuj CSS"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Minifikacja plików JavaScript może zmniejszyć ładunki i skrócić czas analizowania skryptów. [Więcej informacji](https://web.dev/unminified-javascript)"}, "lighthouse-core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Minifikuj JavaScript"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Usuń nieużywane reguły z arkuszy stylów i opóźnij ładowanie kodu CSS, który nie jest używany w części strony widocznej na ekranie bez przewijania, by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ilość danych niepotrzebnie przesyłanych w sieci. [Więcej informacji](https://web.dev/unused-css-rules)"}, "lighthouse-core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Usuń nieużywany kod CSS"}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Usuń nieużywany kod JavaScript, by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ilo<PERSON><PERSON> danych przesyłanych w sieci."}, "lighthouse-core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Usuń nieużywany JavaScript"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Długi czas przechowywania w pamięci podręcznej może przyśpieszyć ponowne otwarcie strony. [Więcej informacji](https://web.dev/uses-long-cache-ttl)"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Znaleziono 1 zasób}few{Znaleziono # zasoby}many{Znaleziono # zasobów}other{Znaleziono # zasobu}}"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Wyświetlaj zasoby statyczne, stosując efektywne zasady pamięci podręcznej"}, "lighthouse-core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Stosuje efektywne zasady pamięci podręcznej dla zasobów statycznych"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Zoptymalizowane obrazy ładują się szybciej i wykorzystują mniej komórkowej transmisji danych. [Więcej informacji](https://web.dev/uses-optimized-images)"}, "lighthouse-core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Użyj efektywnego kodowania obrazów"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Wyświetlaj obrazy o odpowiednim rozmiarze, by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> komórkową transmisję danych i przyśpieszyć ładowanie. [Więcej informacji](https://web.dev/uses-responsive-images)"}, "lighthouse-core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Zmień rozmiar obrazów"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Zasoby tekstowe powinny by<PERSON> kompresowane (gzip, deflate lub brotli), by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ilo<PERSON> danych przesyłanych w sieci. [Więcej informacji](https://web.dev/uses-text-compression)"}, "lighthouse-core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Włącz kompresję tekstu"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | description": {"message": "Takie formaty obrazów jak JPEG 2000, JPEG XR i WebP często zapewniają lepszą kompresję niż PNG czy JPEG, co przekłada się na szybsze pobieranie i mniejsze wykorzystanie danych. [Więcej informacji](https://web.dev/uses-webp-images)"}, "lighthouse-core/audits/byte-efficiency/uses-webp-images.js | title": {"message": "Wyświetlaj obrazy w formatach nowej generacji"}, "lighthouse-core/audits/content-width.js | description": {"message": "<PERSON><PERSON><PERSON> szeroko<PERSON> zawartości aplikacji nie odpowiada szerokości widocznego obszaru, aplikacja może nie być zoptymalizowana pod kątem ekranów urządzeń mobilnych. [Więcej informacji](https://web.dev/content-width)"}, "lighthouse-core/audits/content-width.js | explanation": {"message": "Rozmiar widocznego obszaru ({innerWidth} piks.) nie odpowiada rozmiarowi okna ({outerWidth} piks.)."}, "lighthouse-core/audits/content-width.js | failureTitle": {"message": "Zawart<PERSON>ść nie jest odpowiednio dopasowana do widocznego obszaru"}, "lighthouse-core/audits/content-width.js | title": {"message": "Z<PERSON>rt<PERSON><PERSON><PERSON> jest odpowiednio dopasowana do widocznego obszaru"}, "lighthouse-core/audits/critical-request-chains.js | description": {"message": "Poniższe łańcuchy żądań krytycznych pokazują zasoby ładowane z wysokim priorytetem. Aby przy<PERSON><PERSON><PERSON><PERSON> ładowanie strony, mo<PERSON><PERSON><PERSON> skr<PERSON><PERSON>ć ła<PERSON>, zmniejszyć rozmiar pobieranych zasobów lub opóźnić pobieranie zasobów, które nie są niezbędne. [Więcej informacji](https://web.dev/critical-request-chains)"}, "lighthouse-core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Znaleziono 1 łańcuch}few{Znaleziono # łańcuchy}many{Znaleziono # łańcuchów}other{Znaleziono # łańcucha}}"}, "lighthouse-core/audits/critical-request-chains.js | title": {"message": "Zminimalizuj głębię żądań krytycznych"}, "lighthouse-core/audits/deprecations.js | columnDeprecate": {"message": "Wycofanie/ostrzeżenie"}, "lighthouse-core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/deprecations.js | description": {"message": "Wycofane interfejsy API zostaną w przyszłości usunięte z przeglądarki. [Więcej informacji](https://web.dev/deprecations)"}, "lighthouse-core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Znaleziono 1 ostrzeżenie}few{Znaleziono # ostrzeżenia}many{Znaleziono # ostrzeżeń}other{Znaleziono # ostrzeżenia}}"}, "lighthouse-core/audits/deprecations.js | failureTitle": {"message": "Używa wycofanych interfejsów API"}, "lighthouse-core/audits/deprecations.js | title": {"message": "Nie używa wycofanych interfejsów API"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> podręczna aplikacji została wycofana. [Więcej informacji](https://web.dev/appcache-manifest)"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | displayValue": {"message": "Znaleziono „{AppCacheManifest}”"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | failureTitle": {"message": "Używa pamięci podręcznej aplikacji"}, "lighthouse-core/audits/dobetterweb/appcache-manifest.js | title": {"message": "Nie używa pamięci podręcznej aplikacji"}, "lighthouse-core/audits/dobetterweb/doctype.js | description": {"message": "Podanie definicji doctype zapobiega przełączaniu przeglądarki w tryb osobliwości. [Więcej informacji](https://web.dev/doctype)"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Nazwa określona w doctype musi być ciągiem małych liter `html`"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Dokument musi zaw<PERSON> deklarację doctype"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Oczekiwano pustego ciągu w polu publicId"}, "lighthouse-core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Oczekiwano pustego ciągu w polu systemId"}, "lighthouse-core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Strona nie zawiera elementu HTML doctype, przez co aktywuje tryb osobliwości"}, "lighthouse-core/audits/dobetterweb/doctype.js | title": {"message": "Strona ma deklarację doctype HTML"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnElement": {"message": "Element"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statystyki"}, "lighthouse-core/audits/dobetterweb/dom-size.js | columnValue": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/dom-size.js | description": {"message": "Twórcy przeglądarek zalecają, by s<PERSON><PERSON> zawierały mniej niż około 1500 elementów DOM. Optymalne jest drzewo o głębokości mniejszej niż 32 elementy i zawierające mniej niż 60 elementów podrzędnych/nadrzędnych. Duży DOM może zwiększyć wykorzystanie pamięci, wyd<PERSON><PERSON><PERSON><PERSON><PERSON> [obliczanie stylów](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) i powodować kosztowne [przeformatowania układu](https://developers.google.com/speed/articles/reflow). [Więcej informacji](https://web.dev/dom-size)"}, "lighthouse-core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 element}few{# elementy}many{# elementów}other{# elementu}}"}, "lighthouse-core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Unikaj zbyt dużego DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Maksymalna głębokość DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Łączna liczba elementów DOM"}, "lighthouse-core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Maksymalna liczba elementów podrzędnych"}, "lighthouse-core/audits/dobetterweb/dom-size.js | title": {"message": "Unika zbyt dużego DOM"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnRel": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | columnTarget": {"message": "<PERSON>l"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | description": {"message": "Dodaj atrybut `rel=\"noopener\"` lub `rel=\"noreferrer\"` do wszystkich linków zewnętrznych, by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>łanie i zapobiec lukom w zabezpieczeniach. [Więcej informacji](https://web.dev/external-anchors-use-rel-noopener)"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | failureTitle": {"message": "Linki do innych domen są niebezpieczne"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | title": {"message": "Linki do innych domen są bezpieczne"}, "lighthouse-core/audits/dobetterweb/external-anchors-use-rel-noopener.js | warning": {"message": "<PERSON><PERSON> mo<PERSON><PERSON> us<PERSON> miejsca docelowego dla kotwicy ({anchorHTML}). <PERSON><PERSON><PERSON> nie jest używana jako hiperlink, sugeru<PERSON><PERSON> usunięcie atrybutu target=_blank."}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>, kt<PERSON>re bez kontekstu pytają o zgodę na dostęp do lokalizacji, nie budzą zaufania użytkowników lub ich dezorientują. Sugerujemy powiązanie wyświetlenia tej prośby z działaniem użytkownika. [Więcej informacji](https://web.dev/geolocation-on-start)"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Pyta o zgodę na geolokalizację podczas wczytywania strony"}, "lighthouse-core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Nie pyta o zgodę na geolokalizację podczas wczytywania strony"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | description": {"message": "Wszystkie biblioteki JavaScript interfejsu użytkownika wykryte na stronie. [Więcej informacji](https://web.dev/js-libraries)"}, "lighthouse-core/audits/dobetterweb/js-libraries.js | title": {"message": "Wykryte biblioteki JavaScript"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | description": {"message": "W przypadku wolnego połączenia sieciowego skrypty zewnętrzne dodawane dynamicznie przy użyciu instrukcji `document.write()` mogą opóźnić wczytanie strony o dziesiątki sekund. [Więcej informacji](https://web.dev/no-document-write)"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Używa instrukcji `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-document-write.js | title": {"message": "Nie używa instrukcji `document.write()`"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnSeverity": {"message": "Najwyższy poziom zagrożenia"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVersion": {"message": "Wersja biblioteki"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | columnVuln": {"message": "Liczba luk w zabezpieczeniach"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | description": {"message": "Niektóre skrypty spoza witryny mogą mieć znane luki w z<PERSON>zpiecz<PERSON>ch, które mogą łatwo odkryć i wykorzystać hakerzy. [Więcej informacji](https://web.dev/no-vulnerable-libraries)"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | displayValue": {"message": "{itemCount,plural, =1{Wykryto 1 lukę w zabe<PERSON><PERSON>ch}few{Wykryto # luki w zabe<PERSON><PERSON>ch}many{Wykryto # luk w zabe<PERSON><PERSON>eniach}other{Wykryto # luki w zabez<PERSON>ch}}"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | failureTitle": {"message": "Używa bibliotek JavaScript interfejsu użytkownika, które mają znane luki w zabezpieczeniach"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityHigh": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityLow": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | rowSeverityMedium": {"message": "Średni"}, "lighthouse-core/audits/dobetterweb/no-vulnerable-libraries.js | title": {"message": "Nie używa bibliotek JavaScript interfejsu użytkownika, które mają znane luki w zabezpieczeniach"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>, kt<PERSON>re bez kontekstu pytają o zgodę na wyświetlanie powiadomień, nie budzą zaufania użytkowników lub ich dezorientują. Sugerujemy powiązanie wyświetlenia tej prośby z gestami użytkownika. [Więcej informacji](https://web.dev/notification-on-start)"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Pyta o zgodę na wyświetlanie powiadomień podczas wczytywania strony"}, "lighthouse-core/audits/dobetterweb/notification-on-start.js | title": {"message": "Nie pyta o zgodę na wyświetlanie powiadomień podczas wczytywania strony"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | columnFailingElem": {"message": "Nieprawidłowe elementy"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | description": {"message": "Uniemożliwianie wklejania haseł jest sprzeczne z dobrymi zasadami bezpieczeństwa. [Więcej informacji](https://web.dev/password-inputs-can-be-pasted-into)"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | failureTitle": {"message": "Uniemożliwia wklejanie tekstu w polach haseł"}, "lighthouse-core/audits/dobetterweb/password-inputs-can-be-pasted-into.js | title": {"message": "Pozwala wklejać tekst w polach haseł"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokół"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 ma wiele funkcji niedostępnych w HTTP/1.1, m.in. nagłówki binarne, multipleksowanie i komunikaty push z serwera. [Więcej informacji](https://web.dev/uses-http2)"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 żądanie nieprzesłane przez HTTP/2}few{# żądania nieprzesłane przez HTTP/2}many{# żądań nieprzesłanych przez HTTP/2}other{# żądania nieprzesłanego przez HTTP/2}}"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | failureTitle": {"message": "Nie używa HTTP/2 dla wszystkich swoich zasobów"}, "lighthouse-core/audits/dobetterweb/uses-http2.js | title": {"message": "Używa HTTP/2 dla własnych zasobów"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Proponujemy oznaczenie detektorów zdarzeń dotyku i kółka myszy jako `passive`, by <PERSON><PERSON><PERSON> d<PERSON>nie przewijania strony. [Więcej informacji](https://web.dev/uses-passive-event-listeners)"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Nie używa pasywnych detektorów do poprawy działania przewijania"}, "lighthouse-core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Używa detektorów pasywnych do poprawy działania przewijania"}, "lighthouse-core/audits/errors-in-console.js | columnDesc": {"message": "Opis"}, "lighthouse-core/audits/errors-in-console.js | description": {"message": "Błędy zarejestrowane w konsoli wskazują na nierozwiązane problemy. Mogą być spowodowane nieudanymi żądaniami sieciowymi i innymi problemami w przeglądarce. [Więcej informacji](https://web.dev/errors-in-console)"}, "lighthouse-core/audits/errors-in-console.js | failureTitle": {"message": "Błędy przeglądarki zostały zarejestrowane w konsoli"}, "lighthouse-core/audits/errors-in-console.js | title": {"message": "W konsoli nie zostały zarejestrowane żadne błędy przeglądarki"}, "lighthouse-core/audits/font-display.js | description": {"message": "Uż<PERSON>j funkcji CSS „font-display”, by <PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON> tekstu dla użytkownika podczas ładowania czcionek internetowych. [Więcej informacji](https://web.dev/font-display)"}, "lighthouse-core/audits/font-display.js | failureTitle": {"message": "Zapewnij widoczność tekstu podczas ładowania czcionek internetowych"}, "lighthouse-core/audits/font-display.js | title": {"message": "Cały tekst pozostaje widoczny podczas ładowania czcionek internetowych"}, "lighthouse-core/audits/font-display.js | undeclaredFontURLWarning": {"message": "Narzędziu Lighthouse nie udało się automatycznie sprawdzić wartości font-display dla tego adresu URL: {fontURL}."}, "lighthouse-core/audits/image-aspect-ratio.js | columnActual": {"message": "Współczynnik proporcji (rzeczywisty)"}, "lighthouse-core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Współczynnik proporcji (wyświetlany)"}, "lighthouse-core/audits/image-aspect-ratio.js | description": {"message": "Wymiary wyświetlanego obrazu muszą odpowiadać naturalnemu współczynnikowi proporcji. [Więcej informacji](https://web.dev/image-aspect-ratio)"}, "lighthouse-core/audits/image-aspect-ratio.js | failureTitle": {"message": "Wyświetla obrazy o niepoprawnym współczynniku proporcji"}, "lighthouse-core/audits/image-aspect-ratio.js | title": {"message": "Wyświetla obrazy o poprawnym współczynniku proporcji"}, "lighthouse-core/audits/image-aspect-ratio.js | warningCompute": {"message": "Nieprawidłowa informacja o rozmiarze obrazu {url}"}, "lighthouse-core/audits/installable-manifest.js | description": {"message": "Przeglądarki mogą aktywnie prosić użytkowników o dodanie Twojej aplikacji do ekranu głównego, co może przekładać się na większe zaangażowanie. [Więcej informacji](https://web.dev/installable-manifest)"}, "lighthouse-core/audits/installable-manifest.js | failureTitle": {"message": "Plik manifestu aplikacji internetowej nie spełnia wymagań instalowalności"}, "lighthouse-core/audits/installable-manifest.js | title": {"message": "Plik manifestu aplikacji internetowej spełnia wymagania instalowalności"}, "lighthouse-core/audits/is-on-https.js | columnInsecureURL": {"message": "Niezabezpieczony URL"}, "lighthouse-core/audits/is-on-https.js | description": {"message": "Wszystkie witryny powinny być zabezpieczone przy użyciu HTTPS – również te, które nie obsługują danych wrażliwych. HTTPS uniemożliwia intruzom modyfikowanie i podsłuchiwanie komunikacji między aplikacją a użytkownikami. HTTP/2 i liczne nowe interfejsy API platformy WWW wymagają używania HTTPS. [Więcej informacji](https://web.dev/is-on-https)"}, "lighthouse-core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Wykryto 1 niezabezpieczone żądanie}few{Wykryto # niezabezpieczone żądania}many{Wykryto # niezabezpieczonych żądań}other{Wykryto # niezabezpieczonego żądania}}"}, "lighthouse-core/audits/is-on-https.js | failureTitle": {"message": "Nie używa HTTPS"}, "lighthouse-core/audits/is-on-https.js | title": {"message": "Używa HTTPS"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | description": {"message": "Szybkie wczytywanie stron przez sieć komórkową zapewnia dobre wrażenia użytkowników telefonów. [Więcej informacji](https://web.dev/load-fast-enough-for-pwa)"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueText": {"message": "Interaktywna po {timeInMs, number, seconds} s"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | displayValueTextWithOverride": {"message": "Interaktywna w symulowanej sieci komórkowej po {timeInMs, number, seconds} s"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | explanationLoadSlow": {"message": "Strona wczytuje się zbyt wolno i nie jest interaktywna w ciągu 10 sekund. Zapoznaj się z możliwościami usprawnień i diagnostyką w sekcji „Wydajność”, by <PERSON><PERSON><PERSON><PERSON><PERSON>, co mo<PERSON><PERSON><PERSON>."}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | failureTitle": {"message": "Wczytywanie strony przez sieć komórkową nie jest dostatecznie szybkie"}, "lighthouse-core/audits/load-fast-enough-for-pwa.js | title": {"message": "Wczytywanie strony przez sieć komórkową jest dostatecznie szybkie"}, "lighthouse-core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Kategoria"}, "lighthouse-core/audits/mainthread-work-breakdown.js | description": {"message": "Pomyśl o skróceniu czasu poświęcanego na analizowanie, kompilowanie i wykonywanie kodu JS. Może w tym pomóc dostarczanie mniejszych ładunków JS. [Więcej informacji](https://web.dev/mainthread-work-breakdown)"}, "lighthouse-core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Zminimalizuj aktywność głównego wątku"}, "lighthouse-core/audits/mainthread-work-breakdown.js | title": {"message": "Minimalizuje aktywność głównego wątku"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | description": {"message": "Witryny powinny działać w każdej popularnej przeglądarce, by mogło do nich dotrzeć jak najwięcej użytkowników. [Więcej informacji](https://web.dev/pwa-cross-browser)"}, "lighthouse-core/audits/manual/pwa-cross-browser.js | title": {"message": "Witryna działa w różnych przeglądarkach"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Upewnij się, że do poszczególnych stron można dotrzeć za pomocą precyzyjnych linków w postaci adresów URL i że adresy URL są unikalne na potrzeby udostępniania w mediach społecznościowych. [Więcej informacji](https://web.dev/pwa-each-page-has-url)"}, "lighthouse-core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Każda strona ma swój URL"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | description": {"message": "P<PERSON><PERSON>ścia po kliknięciu powinny być płynne, nawet jeśli sieć jest wolna. Ma to kluczowe znaczenie dla postrzegania szybkości działania przeglądarki. [Więcej informacji](https://web.dev/pwa-page-transitions)"}, "lighthouse-core/audits/manual/pwa-page-transitions.js | title": {"message": "Przejścia między stronami nie sprawiają wrażenia, jakby zacinały się z powodu opóźnień w sieci"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | description": {"message": "Szacowane opóźnienie reakcji jest szacunkowym czasem (w milisekundach), po którym aplikacja reaguje na działanie użytkownika w trakcie najbardziej intensywnego, pięciosekundowego okresu ładowania strony. Jeśli opóźnienie jest więks<PERSON> niż 50 ms, użytkownicy mogą uznać aplikację za powolną. [Więcej informacji](https://web.dev/estimated-input-latency)"}, "lighthouse-core/audits/metrics/estimated-input-latency.js | title": {"message": "Szacowane opóźnienie reakcji"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | description": {"message": "Pierwsze wyrenderowanie treści oznacza czas wyrenderowania pierwszego tekstu lub obrazu. [Wi<PERSON><PERSON>j informacji](https://web.dev/first-contentful-paint)"}, "lighthouse-core/audits/metrics/first-contentful-paint.js | title": {"message": "<PERSON><PERSON><PERSON> wyrenderowanie treści"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | description": {"message": "CPU bezczynny po raz pierwszy oznacza czas, gdy wątek główny na stronie jest po raz pierwszy na tyle mało obciążony, że może obsługiwać działania użytkownika.  [Więcej informacji](https://web.dev/first-cpu-idle)"}, "lighthouse-core/audits/metrics/first-cpu-idle.js | title": {"message": "CPU bezczynny po raz pierwszy"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | description": {"message": "Pierwsze wyrenderowanie elementu znaczącego oznacza czas pojawienia się na ekranie głównej zawartości strony. [Więcej informacji](https://web.dev/first-meaningful-paint)"}, "lighthouse-core/audits/metrics/first-meaningful-paint.js | title": {"message": "Pierwsze wyrenderowanie elementu znaczącego"}, "lighthouse-core/audits/metrics/interactive.js | description": {"message": "Czas do pełnej interaktywności to czas, po którym strona staje się w pełni interaktywna. [Więcej informacji](https://web.dev/interactive)"}, "lighthouse-core/audits/metrics/interactive.js | title": {"message": "Czas do pełnej interaktywności"}, "lighthouse-core/audits/metrics/max-potential-fid.js | description": {"message": "Maksymalne opóźnienie przy pierwszym d<PERSON>łaniu, którego mogą doświadczyć użytkownicy, to czas wykonywania (w milisekundach) najdłuższego zadania. [Więcej informacji](https://developers.google.com/web/updates/2018/05/first-input-delay)"}, "lighthouse-core/audits/metrics/max-potential-fid.js | title": {"message": "Maks. potencjalne opóźnienie przy 1. działaniu"}, "lighthouse-core/audits/metrics/speed-index.js | description": {"message": "In<PERSON><PERSON> szy<PERSON> w<PERSON>, jak szybko strona zapełnia się widocznymi treściami. [Więcej informacji](https://web.dev/speed-index)"}, "lighthouse-core/audits/metrics/speed-index.js | title": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/audits/metrics/total-blocking-time.js | description": {"message": "Wyrażona w milisekundach suma wszystkich okresów między pierwszym wyrenderowaniem treści a czasem do pełnej interaktywności, gdy dług<PERSON><PERSON><PERSON> zadania przekroczyła 50 ms."}, "lighthouse-core/audits/metrics/total-blocking-time.js | title": {"message": "Łączny czas zablokowania"}, "lighthouse-core/audits/network-rtt.js | description": {"message": "<PERSON>zas błądzenia w sieci (RTT) ma duży wpływ na szybkość działania. Jeśli RTT do źródła jest duży, serwery znajdujące się bliżej użytkownika mogą przyśpieszyć działanie. [Więcej informacji](https://hpbn.co/primer-on-latency-and-bandwidth/)"}, "lighthouse-core/audits/network-rtt.js | title": {"message": "Czasy błądzenia w sieci (Network Round Trip Times)"}, "lighthouse-core/audits/network-server-latency.js | description": {"message": "Opóźnienie serwera może wpływać na szybkość stron internetowych. Jeśli opóźnienie serwera źródłowego jest duże, serwer może być przeciążony lub mieć mało wydajny backend. [Więcej informacji](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)"}, "lighthouse-core/audits/network-server-latency.js | title": {"message": "Opóźnienia backendu serwera"}, "lighthouse-core/audits/offline-start-url.js | description": {"message": "Skrypt service worker pozwala aplikacji działać stabilnie w nieprzewidywalnych warunkach sieciowych. [Więcej informacji](https://web.dev/offline-start-url)"}, "lighthouse-core/audits/offline-start-url.js | failureTitle": {"message": "`start_url` nie wy<PERSON>wi<PERSON><PERSON> błędu 200, kiedy jest offline"}, "lighthouse-core/audits/offline-start-url.js | title": {"message": "`start_url` w<PERSON><PERSON>wi<PERSON>la błąd 200, kiedy jest offline"}, "lighthouse-core/audits/offline-start-url.js | warningCantStart": {"message": "Narzędziu Lighthouse nie udało się odczytać elementu `start_url` z pliku manifestu. Dlatego uznano, że `start_url` to URL dokumentu. Komunikat o błędzie: „{manifestWarning}”."}, "lighthouse-core/audits/performance-budget.js | columnOverBudget": {"message": "Przekroczenie budżetu"}, "lighthouse-core/audits/performance-budget.js | description": {"message": "Liczba ani rozmiar żądań sieciowych nie mogą przekraczać wartości określonych przez podany budżet wydajności. [Więcej informacji](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "lighthouse-core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 żądanie}few{# żądania}many{# żądań}other{# żądania}}"}, "lighthouse-core/audits/performance-budget.js | title": {"message": "Budżet wydajności"}, "lighthouse-core/audits/redirects-http.js | description": {"message": "<PERSON><PERSON><PERSON> HTTPS jest już skonfigurowany, up<PERSON><PERSON><PERSON> si<PERSON>, że cały ruch HTTP jest przekierowywany do HTTPS, by w<PERSON><PERSON><PERSON> użytkownicy mogli korzystać z bezpiecznych funkcji. [Więcej informacji](https://web.dev/redirects-http)"}, "lighthouse-core/audits/redirects-http.js | failureTitle": {"message": "Nie przekierowuje ruchu HTTP do HTTPS"}, "lighthouse-core/audits/redirects-http.js | title": {"message": "Przekierowuje ruch HTTP do HTTPS"}, "lighthouse-core/audits/redirects.js | description": {"message": "Przekierowania wprowadzają dodatkowe opóźnienia przed rozpoczęciem ładowania strony. [Więcej informacji](https://web.dev/redirects)"}, "lighthouse-core/audits/redirects.js | title": {"message": "Unikaj wielokrotnych przekierowań"}, "lighthouse-core/audits/resource-summary.js | description": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> budżety dla liczby i rozmiaru zasobów strony, dodaj plik budget.json. [Więcej informacji](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "lighthouse-core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 żądanie • {byteCount, number, bytes} KB}few{# żądania • {byteCount, number, bytes} KB}many{# żądań • {byteCount, number, bytes} KB}other{# żądania • {byteCount, number, bytes} KB}}"}, "lighthouse-core/audits/resource-summary.js | title": {"message": "Liczba żądań i ilość przesyłanych danych powinny być małe"}, "lighthouse-core/audits/seo/canonical.js | description": {"message": "Linki kanoniczne sugerują URL, kt<PERSON>ry ma być poka<PERSON>wany w wynikach wyszukiwania. [Więcej informacji](https://web.dev/canonical)"}, "lighthouse-core/audits/seo/canonical.js | explanationConflict": {"message": "Niezgodne ze sobą adresy URL ({urlList})"}, "lighthouse-core/audits/seo/canonical.js | explanationDifferentDomain": {"message": "Wskazuje inną domenę ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationInvalid": {"message": "Nieprawidłowy URL ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Wskazuje inną lokalizację atrybutu `hreflang` ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRelative": {"message": "Względny URL ({url})"}, "lighthouse-core/audits/seo/canonical.js | explanationRoot": {"message": "Wskazuje główny URL domeny (stronę główną) zamiast odpowiedniej strony z treścią"}, "lighthouse-core/audits/seo/canonical.js | failureTitle": {"message": "Dokument nie zawiera prawidłowego atrybutu `rel=canonical`"}, "lighthouse-core/audits/seo/canonical.js | title": {"message": "Dokument ma prawidłowy atrybut `rel=canonical`"}, "lighthouse-core/audits/seo/font-size.js | description": {"message": "Na urządzeniach mobilnych czcionka o rozmiarze mniejszym niż 12 pikseli jest nieczytelna, dlatego użytkownicy muszą powiększać ekran, by o<PERSON><PERSON><PERSON><PERSON><PERSON> tekst. Ponad 60% tekstu na stronie powinno mieć rozmiar co najmniej 12 pikseli. [Więcej informacji](https://web.dev/font-size)"}, "lighthouse-core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} czytelnego te<PERSON>tu"}, "lighthouse-core/audits/seo/font-size.js | explanationViewport": {"message": "Tekst jest nieczytelny z powodu braku metatagu viewport zoptymalizowanego na potrzeby ekranów telefonów."}, "lighthouse-core/audits/seo/font-size.js | explanationWithDisclaimer": {"message": "{decimalProportion, number, extendedPercent} tekstu jest zbyt małe (na podstawie próbki {decimalProportionVisited, number, extendedPercent})."}, "lighthouse-core/audits/seo/font-size.js | failureTitle": {"message": "W dokumencie nie są używane czytelne rozmiary czcionek"}, "lighthouse-core/audits/seo/font-size.js | title": {"message": "W dokumencie używane są czytelne rozmiary czcionek"}, "lighthouse-core/audits/seo/hreflang.js | description": {"message": "Linki hreflang informują w<PERSON>, kt<PERSON><PERSON><PERSON> wersję strony poka<PERSON> w wynikach wyszukiwania dla danego języka lub regionu. [Więcej informacji](https://web.dev/hreflang)"}, "lighthouse-core/audits/seo/hreflang.js | failureTitle": {"message": "Dokument nie ma prawidłowego atrybutu `hreflang`"}, "lighthouse-core/audits/seo/hreflang.js | title": {"message": "Dokument ma prawidłowy atrybut `hreflang`"}, "lighthouse-core/audits/seo/http-status-code.js | description": {"message": "Strony z kodem stanu HTTP oznaczającym niepowodzenie mogą nie być indeksowane poprawnie. [Więcej informacji](https://web.dev/http-status-code)"}, "lighthouse-core/audits/seo/http-status-code.js | failureTitle": {"message": "Strona ma kod stanu HTTP oznaczający niepowodzenie"}, "lighthouse-core/audits/seo/http-status-code.js | title": {"message": "Strona ma kod stanu HTTP oznaczający powodzenie"}, "lighthouse-core/audits/seo/is-crawlable.js | description": {"message": "Wyszukiwarki nie mogą umiesz<PERSON>ich stron w wynikach wyszukiwania, jeśli nie mają uprawnień, by je <PERSON><PERSON><PERSON><PERSON><PERSON>. [Wi<PERSON><PERSON>j informacji](https://web.dev/is-crawable)"}, "lighthouse-core/audits/seo/is-crawlable.js | failureTitle": {"message": "Zablokowano indeksowanie strony"}, "lighthouse-core/audits/seo/is-crawlable.js | title": {"message": "Indeksowanie strony nie jest zablokowane"}, "lighthouse-core/audits/seo/link-text.js | description": {"message": "Opisowy tekst linków ułatwia wyszukiwarkom zrozumienie zawartości stron. [Więcej informacji](https://web.dev/link-text)"}, "lighthouse-core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Znaleziono 1 link}few{Znaleziono # linki}many{Znaleziono # linków}other{Znaleziono # linku}}"}, "lighthouse-core/audits/seo/link-text.js | failureTitle": {"message": "Linki nie mają opisowego tekstu"}, "lighthouse-core/audits/seo/link-text.js | title": {"message": "Linki mają tekst opisowy"}, "lighthouse-core/audits/seo/manual/structured-data.js | description": {"message": "Uruchom [Narzędzie do testowania uporządkowanych danych](https://search.google.com/structured-data/testing-tool/) i narzędzie [Structured Data Linter](http://linter.structured-data.org/), by spraw<PERSON><PERSON><PERSON> uporządkowane dane. [Więcej informacji](https://web.dev/structured-data)"}, "lighthouse-core/audits/seo/manual/structured-data.js | title": {"message": "Uporządkowane dane są prawidłowe"}, "lighthouse-core/audits/seo/meta-description.js | description": {"message": "Metaopis można umieś<PERSON> w wynikach wyszukiwania, by <PERSON><PERSON><PERSON><PERSON><PERSON> zawa<PERSON> stron<PERSON>. [Więcej informacji](https://web.dev/meta-description)"}, "lighthouse-core/audits/seo/meta-description.js | explanation": {"message": "Tekst opisu jest pusty."}, "lighthouse-core/audits/seo/meta-description.js | failureTitle": {"message": "Dokument nie ma metaopisu"}, "lighthouse-core/audits/seo/meta-description.js | title": {"message": "Dokument ma metaopis"}, "lighthouse-core/audits/seo/plugins.js | description": {"message": "Wyszukiwarki nie potrafią indeksować treści z wtyczek, a wiele urządzeń nie obsługuje wtyczek lub ogranicza ich działanie. [Więcej informacji](https://web.dev/plugins)"}, "lighthouse-core/audits/seo/plugins.js | failureTitle": {"message": "Dokument używa wtyczek"}, "lighthouse-core/audits/seo/plugins.js | title": {"message": "Dokument nie wymaga wtyczek"}, "lighthouse-core/audits/seo/robots-txt.js | description": {"message": "Je<PERSON>li plik robots.txt ma nieprawidłowy format, roboty indeksujące mogą nie wied<PERSON>, jak mają indeksowa<PERSON> witrynę. [Wię<PERSON>j informacji](https://web.dev/robots-txt)"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Żądanie pliku robots.txt zwróciło stan HTTP: {statusCode}"}, "lighthouse-core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Znaleziono 1 błąd}few{Znaleziono # błędy}many{Znaleziono # błędów}other{Znaleziono # błędu}}"}, "lighthouse-core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse nie udało się pobrać pliku robots.txt"}, "lighthouse-core/audits/seo/robots-txt.js | failureTitle": {"message": "Plik robots.txt jest nieprawidłowy"}, "lighthouse-core/audits/seo/robots-txt.js | title": {"message": "Plik robots.txt jest prawidłowy"}, "lighthouse-core/audits/seo/tap-targets.js | description": {"message": "Elementy interaktywne, takie jak przyciski i linki, pow<PERSON><PERSON> być dostatecznie duże (48 x 48 pikseli) i mieć wokół siebie odpowiednią ilość miejsca, by m<PERSON><PERSON><PERSON> było je łatwo dotknąć, nie zah<PERSON> o inne elementy. [Więcej informacji](https://web.dev/tap-targets)"}, "lighthouse-core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} elementów dotykowych ma odpowiednią wielkość"}, "lighthouse-core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Elementy dotykowe są zbyt małe z powodu braku metatagu viewport zoptymalizowanego na potrzeby ekranów telefonów"}, "lighthouse-core/audits/seo/tap-targets.js | failureTitle": {"message": "Elementy dotykowe nie mają odpowiedniej wielkości"}, "lighthouse-core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Pokrywające się elementy dotykowe"}, "lighthouse-core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Element dotykowy"}, "lighthouse-core/audits/seo/tap-targets.js | title": {"message": "Elementy dotykowe mają odpowiednią wielkość"}, "lighthouse-core/audits/service-worker.js | description": {"message": "Skrypt service worker pozwala aplikacji na korzystanie z wielu funkcji progresywnych aplikacji internetowych – takich jak działanie offline, dodawanie do ekranu głównego czy powiadomienia push. [Więcej informacji](https://web.dev/service-worker)"}, "lighthouse-core/audits/service-worker.js | explanationBadManifest": {"message": "St<PERSON>ą steruje skrypt service worker, ale nie znaleziono elementu `start_url`, ponieważ nie udało się przetworzyć pliku manifestu jako prawidłowego pliku JSON"}, "lighthouse-core/audits/service-worker.js | explanationBadStartUrl": {"message": "Tą stroną steruje skrypt service worker, ale element `start_url` ({startUrl}) nie znajduje się w jego zakresie ({scopeUrl})"}, "lighthouse-core/audits/service-worker.js | explanationNoManifest": {"message": "<PERSON>ą stroną steruje skrypt service worker, ale nie znaleziono elementu `start_url`, ponieważ nie został pobrany żaden plik manifestu."}, "lighthouse-core/audits/service-worker.js | explanationOutOfScope": {"message": "Ta witryna zawiera co najmniej jeden skrypt service worker, ale strona ({pageUrl}) nie jest w zak<PERSON>ie."}, "lighthouse-core/audits/service-worker.js | failureTitle": {"message": "<PERSON><PERSON> rejest<PERSON><PERSON> skryptu service worker, kt<PERSON><PERSON> steruje stron<PERSON> i elementem `start_url`"}, "lighthouse-core/audits/service-worker.js | title": {"message": "Rejest<PERSON>je skrypt service worker, kt<PERSON>ry steruje stron<PERSON> i elementem `start_url`"}, "lighthouse-core/audits/splash-screen.js | description": {"message": "Ekran powitalny z niestandardowym motywem zapewnia użytkownikom lepsze wrażenia podczas otwierania aplikacji z ekranu głównego. [Więcej informacji](https://web.dev/splash-screen)"}, "lighthouse-core/audits/splash-screen.js | failureTitle": {"message": "<PERSON><PERSON> skonfigurowano niestandardowego ekranu powitalnego"}, "lighthouse-core/audits/splash-screen.js | title": {"message": "Skonfigurowano niestandardowy ekran powitalny"}, "lighthouse-core/audits/themed-omnibox.js | description": {"message": "Motyw paska adresu w przeglądarce możesz dopasować do swojej witryny. [Więcej informacji](https://web.dev/themed-omnibox)"}, "lighthouse-core/audits/themed-omnibox.js | failureTitle": {"message": "<PERSON>e ustawia motywu kolorystycznego paska adresu."}, "lighthouse-core/audits/themed-omnibox.js | title": {"message": "Ustawia motyw kolorystyczny paska adresu."}, "lighthouse-core/audits/third-party-summary.js | columnBlockingTime": {"message": "<PERSON>zas blokowania głównego wątku"}, "lighthouse-core/audits/third-party-summary.js | columnThirdParty": {"message": "Dostawca zewnętrzny"}, "lighthouse-core/audits/third-party-summary.js | description": {"message": "Kod spoza witryny może znacznie spowalniać wczytywanie stron. Ogranicz liczbę zewnętrznych dostawców kodu i spróbuj wczytywać kod spoza witryny dopiero po zakończeniu wczytywania podstawowej strony. [Wię<PERSON>j informacji](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)"}, "lighthouse-core/audits/third-party-summary.js | displayValue": {"message": "Kod spoza witryny zablokował główny wątek na {timeInMs, number, milliseconds} ms"}, "lighthouse-core/audits/third-party-summary.js | failureTitle": {"message": "Ogranicz wpływ kodu spoza witryny"}, "lighthouse-core/audits/third-party-summary.js | title": {"message": "Używanie kodu spoza witryny"}, "lighthouse-core/audits/time-to-first-byte.js | description": {"message": "Czas do pierwszego bajta oznacza czas wysłania odpowiedzi przez serwer. [Więcej informacji](https://web.dev/time-to-first-byte)"}, "lighthouse-core/audits/time-to-first-byte.js | displayValue": {"message": "<PERSON>zas odpowiedzi głównego dokumentu: {timeInMs, number, milliseconds} ms"}, "lighthouse-core/audits/time-to-first-byte.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>y reakcji serwera (TTFB)"}, "lighthouse-core/audits/time-to-first-byte.js | title": {"message": "Czasy odpowiedzi serwera są krótkie (TTFB)"}, "lighthouse-core/audits/user-timings.js | columnDuration": {"message": "Czas trwania"}, "lighthouse-core/audits/user-timings.js | columnStartTime": {"message": "<PERSON><PERSON> roz<PERSON>"}, "lighthouse-core/audits/user-timings.js | columnType": {"message": "<PERSON><PERSON>"}, "lighthouse-core/audits/user-timings.js | description": {"message": "Do aplikacji możesz dodać obsługę interfejsu User Timing API, by <PERSON><PERSON><PERSON><PERSON> r<PERSON>wi<PERSON> szy<PERSON><PERSON>ść aplikacji z punktu widzenia użytkownika. [Więcej informacji](https://web.dev/user-timings)"}, "lighthouse-core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 czas działań użytkownika}few{# czasy działań użytkownika}many{# czasów działań użytkownika}other{# czasu działań użytkownika}}"}, "lighthouse-core/audits/user-timings.js | title": {"message": "Znaczniki i odcinki Czasu działań użytkownika"}, "lighthouse-core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Znaleziono element <link> typu preconnect dla „{securityOrigin}”, który nie jest używany przez przeglądarkę. Sprawdź, czy poprawnie używasz atrybutu `crossorigin`."}, "lighthouse-core/audits/uses-rel-preconnect.js | description": {"message": "Rozważ dodanie wskazówek `preconnect` lub `dns-prefetch`, by w<PERSON><PERSON><PERSON><PERSON><PERSON> połączenia z ważnymi źródłami w innych domenach. [Więcej informacji](https://web.dev/uses-rel-preconnect)"}, "lighthouse-core/audits/uses-rel-preconnect.js | title": {"message": "Wcześniej nawiąż połączenia z wymaganymi źródłami"}, "lighthouse-core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Znaleziono element <link> wstępnego wczytywania dla „{preloadURL}”, który nie jest używany przez przeglądarkę. Sprawdź, czy poprawnie używasz atrybutu `crossorigin`."}, "lighthouse-core/audits/uses-rel-preload.js | description": {"message": "Pomyśl o użyciu elementu `<link rel=preload>`, by s<PERSON><PERSON><PERSON><PERSON> pobierały się zasoby, które są obecnie żądane na dalszym etapie ładowania strony. [Więcej informacji](https://web.dev/uses-rel-preload)"}, "lighthouse-core/audits/uses-rel-preload.js | title": {"message": "Załaduj wstępnie kluczowe żądania"}, "lighthouse-core/audits/viewport.js | description": {"message": "<PERSON><PERSON><PERSON> tag `<meta name=\"viewport\">`, by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> aplik<PERSON> pod kątem ekranów urządzeń mobilnych. [Więcej informacji](https://web.dev/viewport)"}, "lighthouse-core/audits/viewport.js | explanationNoTag": {"message": "Nie znaleziono tagu `<meta name=\"viewport\">`"}, "lighthouse-core/audits/viewport.js | failureTitle": {"message": "Nie zawiera tagu `<meta name=\"viewport\">` z elementem `width` lub `initial-scale`"}, "lighthouse-core/audits/viewport.js | title": {"message": "Zawiera tag `<meta name=\"viewport\">` z elementem `width` lub `initial-scale`"}, "lighthouse-core/audits/without-javascript.js | description": {"message": "Aplikacja powinna w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaki<PERSON>, je<PERSON>li JavaScript jest wyłączony. Wystarczy nawet ostrzeżenie, że JavaScript jest wymagany do korzystania z aplikacji. [Więcej informacji](https://web.dev/without-javascript)"}, "lighthouse-core/audits/without-javascript.js | explanation": {"message": "Główna c<PERSON><PERSON> strony powinna <PERSON> jaki<PERSON> tre<PERSON>, kiedy jej skrypty są niedostępne"}, "lighthouse-core/audits/without-javascript.js | failureTitle": {"message": "Nie zawiera treści zastępczych na wypadek niedostępności JavaScriptu"}, "lighthouse-core/audits/without-javascript.js | title": {"message": "Zawiera jakieś treści na wypadek niedostępności JavaScriptu"}, "lighthouse-core/audits/works-offline.js | description": {"message": "<PERSON><PERSON><PERSON> tworzysz progresywną aplikację internetową, r<PERSON><PERSON><PERSON> użycie skryptu service worker, który pozwoli na działanie aplikacji w trybie offline. [Więcej informacji](https://web.dev/works-offline)"}, "lighthouse-core/audits/works-offline.js | failureTitle": {"message": "Strona nie wyświetla błędu 200, kiedy jest offline"}, "lighthouse-core/audits/works-offline.js | title": {"message": "Bieżąca strona wyświetla błąd 200, kiedy jest offline"}, "lighthouse-core/audits/works-offline.js | warningNoLoad": {"message": "Strona może nie wczytywać się offline, ponieważ testowy URL ({requested}) został przekierowany pod adres „{final}”. Przetestuj drugi URL bezpośrednio."}, "lighthouse-core/config/default-config.js | a11yAriaGroupDescription": {"message": "To są możliwości lepszego wykorzystania atrybutów ARIA w Twojej aplikacji, by by<PERSON> ona wygodniejsza dla użytkowników technologii wspomagających, takich jak czytniki ekranu."}, "lighthouse-core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "To są możliwości dostarczenia alternatywnej treści dla audio i wideo. Dzięki temu treści mogą stać się bardziej przystępne dla osób niedowidzących i niedosłyszących."}, "lighthouse-core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Dźwięk i obraz"}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Te pozycje wskazują typowe sprawdzone metody ułatwień dostępu."}, "lighthouse-core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Sprawdzone metody"}, "lighthouse-core/config/default-config.js | a11yCategoryDescription": {"message": "Te testy wskazują moż<PERSON>ci [poprawy ułatwień dostępu Twojej aplikacji internetowej](https://developers.google.com/web/fundamentals/accessibility). Ponieważ wykryć automatycznie można tylko część problemów z ułatwieniami dostępu, wskazane jest przeprowadzenie też testów ręcznych."}, "lighthouse-core/config/default-config.js | a11yCategoryManualDescription": {"message": "Te pozycje dotyczą obszarów, których narzędzie do testów automatycznych nie może zbadać. Więcej informacji w naszym przewodniku po [prowadzeniu przeglądu ułatwień dostępu](https://developers.google.com/web/fundamentals/accessibility/how-to-review)."}, "lighthouse-core/config/default-config.js | a11yCategoryTitle": {"message": "Ułatwienia dostępu"}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "To są możliwości poprawy czytelności treści."}, "lighthouse-core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | a11yLanguageGroupDescription": {"message": "To są możliwości ulepszenia interpretacji treści przez użytkowników mających różne ustawienia języka."}, "lighthouse-core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internacjonalizacja i lokalizacja"}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "To są możliwości ulepszenia semantyki kontrolek aplikacji. Dzięki temu treści mogą stać się bardziej przystępne dla użytkowników technologii wspomagających, takich jak czytniki ekranu."}, "lighthouse-core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Nazwy i etykiety"}, "lighthouse-core/config/default-config.js | a11yNavigationGroupDescription": {"message": "To są możliwości ulepszenia nawigacji za pomocą klawiatury w Twojej aplikacji."}, "lighthouse-core/config/default-config.js | a11yNavigationGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "To są możliwości lepszego odczytywania danych z tabel i list za pomocą technologii wspomagających, takich jak czytnik ekranu."}, "lighthouse-core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tabele i listy"}, "lighthouse-core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Sprawdzone metody"}, "lighthouse-core/config/default-config.js | budgetsGroupDescription": {"message": "Budżety wydajności są podstawą do określania standardów wydajności witryny."}, "lighthouse-core/config/default-config.js | budgetsGroupTitle": {"message": "Budżety"}, "lighthouse-core/config/default-config.js | diagnosticsGroupDescription": {"message": "Więcej o wydajności aplikacji. Te liczby nie mają [bezpośredniego wpływu](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) na wyniki w kategorii Wydajność."}, "lighthouse-core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostyka"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Najważnie<PERSON><PERSON><PERSON> aspektem wydaj<PERSON>ści jest to, jak szybko piksele zostaną wyświetlone na ekranie. Kluczowe wskaźniki: <PERSON><PERSON><PERSON> wyrenderowanie treści, <PERSON><PERSON><PERSON> wyrenderowanie elementu znaczącego"}, "lighthouse-core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Ulepszenia pierwszego renderowania"}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Te sugestie mogą pomóc prz<PERSON><PERSON><PERSON> wczytywanie strony. Nie mają one [bezpośredniego wpływu](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted) na wynik w kategorii Wydajność."}, "lighthouse-core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>ści"}, "lighthouse-core/config/default-config.js | metricGroupTitle": {"message": "<PERSON>"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Usprawnij całe ładowanie, by strona jak naj<PERSON><PERSON>b<PERSON>j była gotowa do używania i reagowała na działania użytkownika. Główne wskaźniki: Czas do pełnej interaktywności, <PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Ogólne usprawnienia"}, "lighthouse-core/config/default-config.js | performanceCategoryTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/config/default-config.js | pwaCategoryDescription": {"message": "Te testy służą do sprawdzenia różnych aspektów progresywnej aplikacji internetowej. [Więcej informacji](https://developers.google.com/web/progressive-web-apps/checklist)"}, "lighthouse-core/config/default-config.js | pwaCategoryManualDescription": {"message": "Te testy są wymagane w ramach podstawowej [listy kontrolnej PWA](https://developers.google.com/web/progressive-web-apps/checklist), ale narzędzie Lighthouse nie przeprowadza ich automatycznie. Nie mają wpływu na wynik, ale należy pamiętać o wykonaniu ich ręcznie."}, "lighthouse-core/config/default-config.js | pwaCategoryTitle": {"message": "Progresywna aplikacja internetowa"}, "lighthouse-core/config/default-config.js | pwaFastReliableGroupTitle": {"message": "Szybka i niezawodna"}, "lighthouse-core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Możliwa do zainstalowania"}, "lighthouse-core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Optymalizacja dla PWA"}, "lighthouse-core/config/default-config.js | seoCategoryDescription": {"message": "Te testy sprawdza<PERSON>, czy strona jest zoptymalizowana pod kątem rankingu wyników wyszukiwarki. Na ranking mogą wpływać inne czynniki, których Lighthouse nie sprawdza. [Więcej informacji](https://support.google.com/webmasters/answer/35769)"}, "lighthouse-core/config/default-config.js | seoCategoryManualDescription": {"message": "Uruchom te dodatkowe walidatory w swojej witrynie, by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wi<PERSON><PERSON>j sprawdzonych metod SEO."}, "lighthouse-core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "lighthouse-core/config/default-config.js | seoContentGroupDescription": {"message": "Dostosuj kod HTML w taki sposób, by <PERSON><PERSON> le<PERSON>j rozumiały z<PERSON>ć aplik<PERSON>."}, "lighthouse-core/config/default-config.js | seoContentGroupTitle": {"message": "Sprawdzone metody publikowania treści"}, "lighthouse-core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Jeśli Twoja aplikacja ma pojawiać się w wynikach wyszukiwania, muszą mieć do niej dostęp roboty."}, "lighthouse-core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Skanowanie i indeksowanie"}, "lighthouse-core/config/default-config.js | seoMobileGroupDescription": {"message": "Dostosuj strony do komórek, by <PERSON><PERSON><PERSON><PERSON>wn<PERSON>y nie musieli pomniej<PERSON><PERSON> ani powięks<PERSON> ekranu, gdy będą chcieli coś przeczytać. [Wię<PERSON>j informacji](https://developers.google.com/search/mobile-sites/)"}, "lighthouse-core/config/default-config.js | seoMobileGroupTitle": {"message": "Na komórki"}, "lighthouse-core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Czas przechowywania danych w pamięci podręcznej"}, "lighthouse-core/lib/i18n/i18n.js | columnLocation": {"message": "Lokalizacja"}, "lighthouse-core/lib/i18n/i18n.js | columnName": {"message": "Nazwa"}, "lighthouse-core/lib/i18n/i18n.js | columnRequests": {"message": "Żądania"}, "lighthouse-core/lib/i18n/i18n.js | columnResourceType": {"message": "<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | columnSize": {"message": "Rozmiar"}, "lighthouse-core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Spędzony czas"}, "lighthouse-core/lib/i18n/i18n.js | columnTransferSize": {"message": "Rozmiar przesłanych danych"}, "lighthouse-core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Potencjalne oszczędności"}, "lighthouse-core/lib/i18n/i18n.js | columnWastedMs": {"message": "Potencjalne oszczędności"}, "lighthouse-core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Potencjalne przyspieszenie o {wastedBytes, number, bytes} KB"}, "lighthouse-core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Potencjalne przyspieszenie o {wastedMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokument"}, "lighthouse-core/lib/i18n/i18n.js | fontResourceType": {"message": "Czcionka"}, "lighthouse-core/lib/i18n/i18n.js | imageResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | mediaResourceType": {"message": "Multimedia"}, "lighthouse-core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "lighthouse-core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON>"}, "lighthouse-core/lib/i18n/i18n.js | scriptResourceType": {"message": "Skrypt"}, "lighthouse-core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "lighthouse-core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Arkusz stylów"}, "lighthouse-core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Zewnętrzne"}, "lighthouse-core/lib/i18n/i18n.js | totalResourceType": {"message": "Razem"}, "lighthouse-core/lib/lh-error.js | badTraceRecording": {"message": "Podczas rejestrowania śladu wczytywania strony wystąpił błąd. Uruchom Lighthouse ponownie. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | criTimeout": {"message": "Upłynął czas oczekiwania na wstępne połączenie z Protokołem debugującym."}, "lighthouse-core/lib/lh-error.js | didntCollectScreenshots": {"message": "Podczas wczytywania strony w Chrome nie zostały utworzone zrzuty ekranu. Zapewnij widoczność treści na stronie, a potem uruchom Lighthouse jeszcze raz. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | dnsFailure": {"message": "Serwery DNS nie potrafiły ustalić adresu podanej domeny."}, "lighthouse-core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Wys<PERSON>ą<PERSON>ł błąd wymaganego elementu zbierającego {artifactName}: {errorMessage}"}, "lighthouse-core/lib/lh-error.js | internalChromeError": {"message": "Wystąpił wewnętrzny błąd Chrome. Uruchom ponownie Chrome, a następnie Lighthouse."}, "lighthouse-core/lib/lh-error.js | missingRequiredArtifact": {"message": "Wymagany element zbierający {artifactName} nie został uruchomiony."}, "lighthouse-core/lib/lh-error.js | pageLoadFailed": {"message": "Narzędziu Lighthouse nie udało się całkowicie wczytać żądanej strony. Upewnij się, że testujesz właściwy URL, a serwer poprawnie odpowiada na wszystkie żądania."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedHung": {"message": "Narzędziu Lighthouse nie udało się całkowicie wczytać żądanego URL-a, ponieważ strona przestała odpowiadać."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Podany URL nie ma prawidłowego certyfikatu bezpieczeństwa. {securityMessages}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Przeglądarka Chrome zablokowała wczytywanie strony i wyświetliła komunikat pełnoekranowy. Upewnij się, że testujesz właściwy URL, a serwer poprawnie odpowiada na wszystkie żądania."}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Narzędziu Lighthouse nie udało się całkowicie wczytać żądanej strony. Upewnij się, że testujesz właściwy URL, a serwer poprawnie odpowiada na wszystkie żądania. Szczegóły: {errorDetails}"}, "lighthouse-core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Narzędziu Lighthouse nie udało się całkowicie wczytać żądanej strony. Upewnij się, że testujesz właściwy URL, a serwer poprawnie odpowiada na wszystkie żądania. Kod stanu: {statusCode}"}, "lighthouse-core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Wczytywanie strony trwało zbyt długo. Skorzystaj z możliwości przyspieszenia wczytywania strony podanych w raporcie, a następnie ponownie uruchom Lighthouse. ({errorCode})"}, "lighthouse-core/lib/lh-error.js | protocolTimeout": {"message": "Przekroczono przydzielony czas oczekiwania na odpowiedź protokołu DevTools. Metoda: {protocolMethod}"}, "lighthouse-core/lib/lh-error.js | requestContentTimeout": {"message": "Przekroczono czas przydzielony na pobranie zasobów"}, "lighthouse-core/lib/lh-error.js | urlInvalid": {"message": "Podany URL jest nieprawidłowy."}, "lighthouse-core/report/html/renderer/util.js | auditGroupExpandTooltip": {"message": "Pokaż audyty"}, "lighthouse-core/report/html/renderer/util.js | crcInitialNavigation": {"message": "Początkowa nawigacja"}, "lighthouse-core/report/html/renderer/util.js | crcLongestDurationLabel": {"message": "Maksymalne opóźnienie ścieżki krytycznej:"}, "lighthouse-core/report/html/renderer/util.js | errorLabel": {"message": "Błąd"}, "lighthouse-core/report/html/renderer/util.js | errorMissingAuditInfo": {"message": "Błąd raportu: brak informacji o audycie"}, "lighthouse-core/report/html/renderer/util.js | labDataTitle": {"message": "Dane <PERSON>jn<PERSON>"}, "lighthouse-core/report/html/renderer/util.js | lsPerformanceCategoryDescription": {"message": "Bieżąca strona została przeanalizowana przez narzędzie [Lighthouse](https://developers.google.com/web/tools/lighthouse/) wraz z emulacją sieci komórkowej. Wartości są szacunkowe i mogą się zmieniać."}, "lighthouse-core/report/html/renderer/util.js | manualAuditsGroupTitle": {"message": "Dodatkowe elementy do ręcznego sprawdzenia"}, "lighthouse-core/report/html/renderer/util.js | notApplicableAuditsGroupTitle": {"message": "<PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | opportunityResourceColumnLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lighthouse-core/report/html/renderer/util.js | opportunitySavingsColumnLabel": {"message": "Szacowane oszczędności"}, "lighthouse-core/report/html/renderer/util.js | passedAuditsGroupTitle": {"message": "Zaliczone audyty"}, "lighthouse-core/report/html/renderer/util.js | snippetCollapseButtonLabel": {"message": "Zwiń fragment"}, "lighthouse-core/report/html/renderer/util.js | snippetExpandButtonLabel": {"message": "Rozwiń fragment"}, "lighthouse-core/report/html/renderer/util.js | thirdPartyResourcesLabel": {"message": "Pokaż zasoby zewnętrzne"}, "lighthouse-core/report/html/renderer/util.js | toplevelWarningsMessage": {"message": "Podczas tego uruchomienia Lighthouse wystąpiły problemy:"}, "lighthouse-core/report/html/renderer/util.js | varianceDisclaimer": {"message": "Wartości są szacunkowe i mogą się zmieniać. Wynik wydajności jest [oparty tylko na tych danych](https://github.com/GoogleChrome/lighthouse/blob/d2ec9ffbb21de9ad1a0f86ed24575eda32c796f0/docs/scoring.md#how-are-the-scores-weighted)."}, "lighthouse-core/report/html/renderer/util.js | warningAuditsGroupTitle": {"message": "Audyty zaliczone z ostrzeżeniami"}, "lighthouse-core/report/html/renderer/util.js | warningHeader": {"message": "Ostrzeżenia "}, "stack-packs/packs/wordpress.js | efficient_animated_content": {"message": "Możesz przesłać plik GIF do usługi, która umożliwi umieszczanie go jako pliku wideo HTML5."}, "stack-packs/packs/wordpress.js | offscreen_images": {"message": "Zainstaluj [wtyczkę WordPressa do leniwego ładowania](https://wordpress.org/plugins/search/lazy+load/), która umożliwia odłożenie ładowania obrazów niewyświetlanych na ekranie, albo wybierz motyw, który ma tę funkcję. Warto też skorzystać z [wtyczki AMP](https://wordpress.org/plugins/amp/)."}, "stack-packs/packs/wordpress.js | render_blocking_resources": {"message": "Jest wiele wtyczek WordPressa, które mogą pomóc Ci [umie<PERSON><PERSON><PERSON> w tekście najważniejsze zasoby](https://wordpress.org/plugins/search/critical+css/) lub [opóź<PERSON>ć wczytywanie mniej ważnych zasobów](https://wordpress.org/plugins/search/defer+css+javascript/). Pamiętaj, że optymalizacje dostarczane przez te wtyczki mogą uszkodzić funkcje Twojego motywu lub innych wtyczek i konieczne może być wprowadzenie zmian w kodzie."}, "stack-packs/packs/wordpress.js | time_to_first_byte": {"message": "<PERSON><PERSON><PERSON>, wtyczki i specyfikacje serwera są nie bez znaczenia dla jego czasu reakcji. <PERSON><PERSON> może warto znaleźć lepiej zoptymalizowany motyw, starannie wybierając wtyczkę optymalizującą, i przejść na nowszą wersję serwera."}, "stack-packs/packs/wordpress.js | total_byte_weight": {"message": "Korzystne może być wyświetlanie fragmentów na liście postów (np. przy użyciu tagu more), zmniejszenie liczby postów wyświetlanych na danej stronie, podział długich postów na kilka stron lub użycie wtyczki umożliwiającej opóźnione wczytywanie komentarzy."}, "stack-packs/packs/wordpress.js | unminified_css": {"message": "Kilka [wtyczek WordPressa](https://wordpress.org/plugins/search/minify+css/) może przyśpieszyć działanie strony dzięki konkatenacji, minifikacji i kompresji stylów. Jeś<PERSON> masz taką mo<PERSON>, prz<PERSON><PERSON><PERSON>ż minifikację, zanim opublikujesz skrypty."}, "stack-packs/packs/wordpress.js | unminified_javascript": {"message": "Kilka [wtyczek WordPressa](https://wordpress.org/plugins/search/minify+javascript/) może przyśpieszyć działanie strony dzięki konkatenacji, minifikacji i kompresji skryptów. Jeś<PERSON> masz taką mo<PERSON>, prz<PERSON><PERSON><PERSON>ź minifikację, zanim opublikujesz skrypty."}, "stack-packs/packs/wordpress.js | unused_css_rules": {"message": "Dobrym rozwiązaniem może być ograniczenie liczby [wtyczek WordPressa](https://wordpress.org/plugins/) wczytujących na stronie nieużywany kod CSS albo zmiana tych wtyczek na inne. Aby zidenty<PERSON><PERSON> wtyczki, które dodają nieistotny kod CSS, uruchom [zasięg kodu](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) w Chrome DevTools. Możesz zidentyfikować taki motyw/wtyczkę w adresie URL arkusza stylów. Szukaj wtyczek, które mają na liście wiele arkuszy stylów z dużą ilością czerwonego koloru w zasięgu kodu. Wtyczka powinna umieszczać arkusz stylów w kolejce tylko wtedy, gdy r<PERSON><PERSON><PERSON><PERSON><PERSON> jest on używany na stronie."}, "stack-packs/packs/wordpress.js | unused_javascript": {"message": "Dobrym rozwiązaniem może być ograniczenie liczby [wtyczek WordPressa](https://wordpress.org/plugins/) wczytujących na stronie nieużywany kod JavaScript albo zmiana tych wtyczek na inne. Aby zidentyfi<PERSON> wtyczki, które dodają nieistotny kod JS, uruchom [zasięg kodu](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) w Chrome DevTools. Możesz zidentyfikować taki motyw/wtyczkę w adresie URL skryptu. Szukaj wtyczek, które mają na liście wiele skryptów z dużą ilością czerwonego koloru w zasięgu kodu. Wtyczka powinna umieszczać skrypt w kolejce, tylko jeśli rzeczywiście jest on używany na stronie."}, "stack-packs/packs/wordpress.js | uses_long_cache_ttl": {"message": "Dowiedz się więcej o [pamięci podręcznej przeglądarki w WordPressie](https://codex.wordpress.org/WordPress_Optimization#Browser_Caching)."}, "stack-packs/packs/wordpress.js | uses_optimized_images": {"message": "Pomocna może być [wtyczka WordPressa do optymalizacji obrazów](https://wordpress.org/plugins/search/optimize+images/), która kompresuje obrazy, zachow<PERSON><PERSON><PERSON><PERSON> ich jako<PERSON>."}, "stack-packs/packs/wordpress.js | uses_responsive_images": {"message": "Przesy<PERSON><PERSON> obrazy bezpośred<PERSON> przez [bibliotekę multimediów](https://codex.wordpress.org/Media_Library_Screen), by <PERSON><PERSON><PERSON>, <PERSON>e wymagane rozmiary obrazów są dostępne, a następnie wstawiaj je z biblioteki multimediów lub używaj widżetu obrazów, by zapewnić użycie optymalnych rozmiarów obrazów (także w dynamicznych punktach przerwania). Unikaj używania obrazów `Full Size`, chyba że wymiary są adekwatne do danego zastosowania. [Więcej informacji](https://codex.wordpress.org/Inserting_Images_into_Posts_and_Pages#Image_Size)"}, "stack-packs/packs/wordpress.js | uses_text_compression": {"message": "Możesz włączyć kompresję tekstu w konfiguracji swojego serwera WWW."}, "stack-packs/packs/wordpress.js | uses_webp_images": {"message": "<PERSON><PERSON><PERSON><PERSON> skorzy<PERSON> z [wtyczki](https://wordpress.org/plugins/search/convert+webp/) lub usług<PERSON>, która będzie automatycznie konwertować przesłane obrazy do optymalnych formatów."}}