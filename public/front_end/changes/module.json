{"extensions": [{"type": "view", "location": "drawer-view", "id": "changes.changes", "title": "Changes", "persistence": "closeable", "className": "Changes.Changes<PERSON>iew"}, {"type": "@<PERSON>.Revealer", "contextTypes": ["WorkspaceDiff.DiffUILocation"], "destination": "Changes drawer", "className": "Changes.ChangesView.DiffUILocationRevealer"}], "dependencies": ["workspace_diff", "text_editor", "workspace", "diff", "bindings", "persistence", "snippets", "ui"], "scripts": [], "modules": ["changes.js", "changes-legacy.js", "ChangesHighlighter.js", "ChangesView.js", "ChangesSidebar.js", "ChangesTextEditor.js"], "resources": ["changesView.css", "changesSidebar.css"]}