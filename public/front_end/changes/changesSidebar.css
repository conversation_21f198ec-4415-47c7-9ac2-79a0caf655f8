li .icon {
  margin: -3px -5px -3px -5px;
  background: linear-gradient(45deg, hsl(0, 0%, 50%), hsl(0, 0%, 70%));
}

.tree-outline li {
  min-height: 20px;
}

.tree-outline li:hover:not(.selected) .selection {
  display: block;
  background-color: var(--item-hover-color);
}

.navigator-fs-tree-item .icon{
  background: linear-gradient(45deg, hsl(28, 75%, 50%), hsl(28, 75%, 70%));
}

.navigator-sm-script-tree-item .icon,
.navigator-script-tree-item .icon,
.navigator-snippet-tree-item .icon {
  background: linear-gradient(45deg, hsl(48, 70%, 50%), hsl(48, 70%, 70%));
}

.navigator-sm-stylesheet-tree-item .icon,
.navigator-stylesheet-tree-item .icon {
  background: linear-gradient(45deg, hsl(256, 50%, 50%), hsl(256, 50%, 70%));
}

.navigator-image-tree-item .icon,
.navigator-font-tree-item .icon {
  background: linear-gradient(45deg, hsl(109, 33%, 50%), hsl(109, 33%, 70%));
}