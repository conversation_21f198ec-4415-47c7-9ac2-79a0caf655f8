/**
 * Copyright 2017 The Chromium Authors. All rights reserved.
 * Use of this source code is governed by a BSD-style license that can be
 * found in the LICENSE file.
 */

.styles-element-state-pane {
    overflow: hidden;
    padding-left: 2px;
    background-color: var(--toolbar-bg-color);
    border-bottom: 1px solid rgb(189, 189, 189);
    margin-top: 0;
    padding-bottom: 2px;
}

.styles-element-state-pane > div {
    margin: 8px 4px 6px;
}

.styles-element-state-pane > table {
    width: 100%;
    border-spacing: 0;
}

.styles-element-state-pane td {
    padding: 0;
}
