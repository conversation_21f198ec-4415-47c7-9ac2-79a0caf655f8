<?xml version="1.0" encoding="utf-8"?>
<grit-part>
  <message name="IDS_DEVTOOLS_1106d01848580ffc52dd36e2d60316d9" desc="Title of the 'XHR/fetch Breakpoints' tool in the bottom sidebar of the Sources tool">
    XHR/fetch Breakpoints
  </message>
  <message name="IDS_DEVTOOLS_2daf59f0ff992924e42c14c1e674bd6a" desc="Text in DOMBreakpoints Sidebar Pane of the JavaScript Debugging pane in the Sources panel or the DOM Breakpoints pane in the Elements panel">
    Attribute modified
  </message>
  <message name="IDS_DEVTOOLS_30d67fd6c32c9a9ff5ecb4472b44ed54" desc="Input element container text content in XHRBreakpoints Sidebar Pane of the JavaScript Debugging pane in the Sources panel or the DOM Breakpoints pane in the Elements panel">
    Break when URL contains:
  </message>
  <message name="IDS_DEVTOOLS_39c145d69ad05e44e74017346c116251" desc="A context menu item in the DOMBreakpoints Sidebar Pane of the JavaScript Debugging pane in the Sources panel or the DOM Breakpoints pane in the Elements panel">
    Remove all DOM breakpoints
  </message>
  <message name="IDS_DEVTOOLS_3ea566249a507705d9a7ff4d3bd31440" desc="Screen reader description of a hit breakpoint in the Sources panel">
    breakpoint hit
  </message>
  <message name="IDS_DEVTOOLS_57aab6f08efb5f158365bef707ea951d" desc="A context menu item in the DOM Breakpoints sidebar that reveals the node on which the current breakpoint is set.">
    Reveal DOM node in Elements panel
  </message>
  <message name="IDS_DEVTOOLS_59eaf6955f44a94237b6d26911c1d983" desc="A context menu item in the DOMBreakpoints Sidebar Pane of the JavaScript Debugging pane in the Sources panel or the DOM Breakpoints pane in the Elements panel">
    Break on
  </message>
  <message name="IDS_DEVTOOLS_626585724f35f9d9fce0bd36525cb7de" desc="Title of the Marker Decorator of Elements">
    DOM Breakpoint
  </message>
  <message name="IDS_DEVTOOLS_66b74432bdc2797086f419010cc5ff86" desc="Title of the 'DOM Breakpoints' tool in the bottom sidebar of the Sources tool">
    DOM Breakpoints
  </message>
  <message name="IDS_DEVTOOLS_9f76c421048cb58ab03988d2ce1c813e" desc="Label for a button in the sources panel that refreshes the list of global event listeners.">
    Refresh global listeners
  </message>
  <message name="IDS_DEVTOOLS_b839f802a330e4d4145cb182e6767f45" desc="Text in XHRBreakpoints Sidebar Pane of the JavaScript Debugging pane in the Sources panel or the DOM Breakpoints pane in the Elements panel">
    Any XHR or fetch
  </message>
  <message name="IDS_DEVTOOLS_c22470110316d6334bed53ae61a08f59" desc="Text in XHRBreakpoints Sidebar Pane of the JavaScript Debugging pane in the Sources panel or the DOM Breakpoints pane in the Elements panel">
    URL contains &quot;<ph name="URL">$1s<ex>example.com</ex></ph>&quot;
  </message>
  <message name="IDS_DEVTOOLS_c4bd9c57c6b315e3f7374cf77143ca58" desc="Text in DOMBreakpoints Sidebar Pane of the JavaScript Debugging pane in the Sources panel or the DOM Breakpoints pane in the Elements panel">
    Subtree modified
  </message>
  <message name="IDS_DEVTOOLS_e30c4292775f68b2bc9eb3957a69f899" desc="Title of the 'Global Listeners' tool in the bottom sidebar of the Sources tool">
    Global Listeners
  </message>
  <message name="IDS_DEVTOOLS_e4fc5497303105b60ef0b8c90be3fbd2" desc="Accessibility label for the DOM breakpoints list in the Sources panel">
    DOM Breakpoints list
  </message>
  <message name="IDS_DEVTOOLS_ebdb33cde9015fa4b294220ee89cff00" desc="Title of the 'Event Listener Breakpoints' tool in the bottom sidebar of the Sources tool">
    Event Listener Breakpoints
  </message>
  <message name="IDS_DEVTOOLS_f7b864eedd9a968c73e7b0f08fe1f62d" desc="Label for a button in the Sources panel that opens the input field to create a new XHR/fetch breakpoint.">
    Add XHR/fetch breakpoint
  </message>
  <message name="IDS_DEVTOOLS_f80938ab8ce27f3427af09c97a718fff" desc="Text in DOMBreakpoints Sidebar Pane of the JavaScript Debugging pane in the Sources panel or the DOM Breakpoints pane in the Elements panel">
    Node removed
  </message>
</grit-part>
