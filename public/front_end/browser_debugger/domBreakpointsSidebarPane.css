/*
 * Copyright 2017 The Chromium Authors. All rights reserved.
 * Use of this source code is governed by a BSD-style license that can be
 * found in the LICENSE file.
 */

.breakpoint-list {
    padding-bottom: 3px;
}

.breakpoint-list .dom-breakpoint > div {
    overflow: hidden;
    text-overflow: ellipsis;
}

.breakpoint-entry {
    display: flex;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    padding: 2px 0;
}

.breakpoint-entry[data-keyboard-focus="true"] {
    background-color: var(--focus-bg-color);
}

.breakpoint-list .breakpoint-entry:hover {
    background-color: #eee;
}

.breakpoint-hit {
    background-color: rgb(255, 255, 194);
    border-right: 3px solid rgb(107, 97, 48);
}

:host-context(.-theme-with-dark-background) .breakpoint-hit {
    background-color: hsl(46, 98%, 22%);
    color: #ccc;
}
