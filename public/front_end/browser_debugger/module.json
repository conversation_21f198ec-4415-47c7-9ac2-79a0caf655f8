{"extensions": [{"type": "view", "location": "sources.sidebar-bottom", "id": "sources.eventListenerBreakpoints", "title": "Event Listener Breakpoints", "order": 9, "persistence": "permanent", "className": "BrowserDebugger.EventListenerBreakpointsSidebarPane"}, {"type": "@UI.ContextFlavorListener", "contextTypes": ["SDK.DebuggerPausedDetails"], "className": "BrowserDebugger.XHRBreakpointsSidebarPane"}, {"type": "view", "location": "sources.sidebar-bottom", "id": "sources.xhrBreakpoints", "title": "XHR/fetch Breakpoints", "order": 5, "hasToolbar": true, "persistence": "permanent", "className": "BrowserDebugger.XHRBreakpointsSidebarPane"}, {"type": "view", "location": "sources.sidebar-bottom", "id": "sources.domBreakpoints", "title": "DOM Breakpoints", "order": 7, "persistence": "permanent", "className": "BrowserDebugger.DOMBreakpointsSidebarPane"}, {"type": "view", "location": "elements-sidebar", "id": "elements.domBreakpoints", "title": "DOM Breakpoints", "order": 6, "persistence": "permanent", "className": "BrowserDebugger.DOMBreakpointsSidebarPane"}, {"type": "@Elements.MarkerDecorator", "factoryName": "Elements.GenericDecorator", "marker": "breakpoint-marker", "title": "DOM Breakpoint", "color": "rgb(105, 140, 254)"}, {"type": "@UI.ContextMenu.Provider", "contextTypes": ["SDK.DOMNode"], "className": "BrowserDebugger.DOMBreakpointsSidebarPane.ContextMenuProvider"}, {"type": "@UI.ContextFlavorListener", "contextTypes": ["SDK.DebuggerPausedDetails"], "className": "BrowserDebugger.DOMBreakpointsSidebarPane"}, {"type": "view", "location": "sources.sidebar-bottom", "id": "sources.globalListeners", "title": "Global Listeners", "order": 8, "hasToolbar": true, "persistence": "permanent", "className": "BrowserDebugger.ObjectEventListenersSidebarPane"}, {"type": "view", "location": "navigator-view", "id": "navigator-network", "title": "Page", "order": 2, "persistence": "permanent", "className": "Sources.NetworkNavigatorView"}, {"type": "view", "location": "navigator-view", "id": "navigator-overrides", "title": "Overrides", "order": 4, "persistence": "permanent", "className": "Sources.OverridesNavigatorView"}, {"type": "view", "location": "navigator-view", "id": "navigator-contentScripts", "title": "Content scripts", "order": 5, "persistence": "permanent", "className": "Sources.ContentScriptsNavigatorView"}, {"type": "@Sources.NavigatorView", "viewId": "navigator-overrides", "className": "Sources.OverridesNavigatorView"}, {"type": "@Sources.NavigatorView", "viewId": "navigator-contentScripts", "className": "Sources.ContentScriptsNavigatorView"}], "dependencies": ["elements", "sources", "console"], "modules": ["browser_debugger.js", "browser_debugger-legacy.js", "DOMBreakpointsSidebarPane.js", "EventListenerBreakpointsSidebarPane.js", "ObjectEventListenersSidebarPane.js", "XHRBreakpointsSidebarPane.js"], "resources": ["domBreakpointsSidebarPane.css", "eventListenerBreakpoints.css", "xhrBreakpointsSidebarPane.css"]}