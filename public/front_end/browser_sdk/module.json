{"extensions": [{"type": "setting", "category": "Network", "title": "Preserve log", "settingName": "network_log.preserve-log", "settingType": "boolean", "defaultValue": false, "tags": "preserve, clear, reset", "options": [{"value": true, "title": "Do not preserve log on page reload / navigation"}, {"value": false, "title": "Preserve log on page reload / navigation"}]}], "scripts": [], "modules": ["browser_sdk.js", "browser_sdk-legacy.js", "LogManager.js"], "dependencies": ["sdk"]}