// Copyright 2019 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import * as ColorPickerModule from './color_picker.js';

self.ColorPicker = self.ColorPicker || {};
ColorPicker = ColorPicker || {};

/** @constructor */
ColorPicker.ContrastDetails = ColorPickerModule.ContrastDetails.ContrastDetails;

ColorPicker.ContrastDetails.Events = ColorPickerModule.ContrastDetails.Events;

/** @constructor */
ColorPicker.ContrastDetails.Swatch = ColorPickerModule.ContrastDetails.Swatch;

/** @constructor */
ColorPicker.ContrastInfo = ColorPickerModule.ContrastInfo.ContrastInfo;

ColorPicker.ContrastInfo.Events = ColorPickerModule.ContrastInfo.Events;

/** @constructor */
ColorPicker.ContrastOverlay = ColorPickerModule.ContrastOverlay.ContrastOverlay;

/** @constructor */
ColorPicker.ContrastRatioLineBuilder = ColorPickerModule.ContrastOverlay.ContrastRatioLineBuilder;

/** @constructor */
ColorPicker.Spectrum = ColorPickerModule.Spectrum.Spectrum;

/** @constructor */
ColorPicker.Spectrum.PaletteGenerator = ColorPickerModule.Spectrum.PaletteGenerator;

/** @constructor */
ColorPicker.Spectrum.Swatch = ColorPickerModule.Spectrum.Swatch;

ColorPicker.Spectrum.Events = ColorPickerModule.Spectrum.Events;

ColorPicker.Spectrum._ChangeSource = ColorPickerModule.Spectrum.ChangeSource;

/** @typedef {{ title: string, colors: !Array<string>, colorNames: !Array<string>, mutable: boolean }} */
ColorPicker.Spectrum.Palette;
