<?xml version="1.0" encoding="utf-8"?>
<grit-part>
  <message name="IDS_DEVTOOLS_158eabad9d23e8a99bbcfd6f411c575f" desc="A context menu item in the Spectrum of the Color Picker">
    Remove color
  </message>
  <message name="IDS_DEVTOOLS_1690e1c6a6b4c615cc36ab66570996b4" desc="Tooltip text that appears when hovering over largeicon eyedropper button in Spectrum of the Color Picker">
    Toggle color picker
  </message>
  <message name="IDS_DEVTOOLS_19dcdfb6d075db15609e75214f67189f" desc="A context menu item in the Spectrum of the Color Picker">
    Remove all to the right
  </message>
  <message name="IDS_DEVTOOLS_2e8882b1e1deaa1bb50cbdfe188755f6" desc="Swatch copy icon title in Spectrum of the Color Picker">
    Copy color to clipboard
  </message>
  <message name="IDS_DEVTOOLS_38d7dc102a918c3b0031c9fde807308a" desc="Choose bg color text content in Contrast Details of the Color Picker">
    Pick background color
  </message>
  <message name="IDS_DEVTOOLS_39186fc9ea0f3ccf697b31a288e0723f" desc="Tooltip text that appears when hovering over largeicon eyedropper button in Contrast Details of the Color Picker">
    Toggle background color picker
  </message>
  <message name="IDS_DEVTOOLS_3b98e2dffc6cb06a89dcb0d5c60a0206" desc="Label aa text content in Contrast Details of the Color Picker">
    AA
  </message>
  <message name="IDS_DEVTOOLS_58c9566f3c750ce7147b045fdd14202b" desc="Aria label for HEX color format input">
    HEX
  </message>
  <message name="IDS_DEVTOOLS_704c5365ab9b8fb8d8be5f08b8c0f19c" desc="Aria label for alpha slider in Color Picker">
    Change alpha
  </message>
  <message name="IDS_DEVTOOLS_74421a69b1bc40c166be4de5caf0b036" desc="Text of a DOM element in Contrast Details of the Color Picker">
    Contrast ratio
  </message>
  <message name="IDS_DEVTOOLS_8ddbf32368d9216105c893235f8f634e" desc="Aria label for color format switcher button in Color Picker">
    Change color format
  </message>
  <message name="IDS_DEVTOOLS_a03d142f9d52eae6793de3951b4b58f9" desc="Screen reader reads this text when palette switcher button receives focus">
    Preview palettes
  </message>
  <message name="IDS_DEVTOOLS_aba9beb2d259a7cff58a65229dcbbaf4" desc="Aria label which declares hex value of a swatch in the Color Picker">
    Color <ph name="PALETTE_COLORS_I_">$1s<ex>#969696</ex></ph>
  </message>
  <message name="IDS_DEVTOOLS_78c7d6050bd871663f565433ecce80ff" desc="Aria label for hue slider in Color Picker">
    Change hue
  </message>
  <message name="IDS_DEVTOOLS_b201b2e7e7a20df48b625f20c2f0933e" desc="Title text content in Spectrum of the Color Picker">
    Color Palettes
  </message>
  <message name="IDS_DEVTOOLS_b74447172f9edfe492a21feee374c1fd" desc="A context menu item in the Spectrum of the Color Picker">
    Clear palette
  </message>
  <message name="IDS_DEVTOOLS_bbe0cb0a04956e97e8fc70f519d10e0d" desc="Color element title in Spectrum of the Color Picker">
    <ph name="PALETTE_COLORS_I_">$1s<ex>#9c1724</ex></ph>. Long-click to show alternate shades.
  </message>
  <message name="IDS_DEVTOOLS_c5bdbf2fd625704541f51288fd37b726" desc="Label for close button in Color Picker">
    Return to color picker
  </message>
  <message name="IDS_DEVTOOLS_d1e2107b34fa404fabd54bcce4fd858f" desc="Tooltip text that appears when hovering over the largeicon add button in the Spectrum of the Color Picker">
    Add to palette
  </message>
  <message name="IDS_DEVTOOLS_df5b49c2e7eb48841103d5bec2d12ffd" desc="Aria label for RGBA and HSLA color format inputs in Color Picker">
    <ph name="THIS__TEXTLABELS_TEXTCONTENT_CHARAT_I_">$1s<ex>R</ex></ph> in <ph name="THIS__TEXTLABELS_TEXTCONTENT">$2s<ex>RGBA</ex></ph>
  </message>
  <message name="IDS_DEVTOOLS_e1faffb3e614e6c2fba74296962386b7" desc="Label aaa text content in Contrast Details of the Color Picker">
    AAA
  </message>
</grit-part>