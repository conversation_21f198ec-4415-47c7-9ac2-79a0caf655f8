/* https://github.com/bgrins/spectrum */
:host {
    width: 232px;
    height: 319px;
    user-select: none;
    overflow: hidden;
}

:selection {
    background-color: blue;
    color: white;
}

.spectrum-color {
    position: relative;
    width: 232px;
    height: 124px;
    border-radius: 2px 2px 0 0;
    overflow: hidden;
    flex: none;
}

.spectrum-tools {
    position: relative;
    height: 111px;
    width: 100%;
    flex: none;
}

.spectrum-hue {
    top: 16px;
}

.spectrum-alpha {
    top: 35px;
    background-image: url(Images/checker.png);
    background-size: 12px 11px;
}

.spectrum-alpha-background {
    height: 100%;
    border-radius: 2px;
}

.spectrum-hue, .spectrum-alpha {
    position: absolute;
    left: 86px;
    width: 130px;
    height: 11px;
    border-radius: 2px;
}

.spectrum-hue[data-keyboard-focus="true"] .spectrum-slider,
.spectrum-alpha[data-keyboard-focus="true"] .spectrum-slider {
  border: 1px solid var(--accent-color-hover);
  width: 14px;
  height: 14px;
  border-radius: 14px;
}

.spectrum-dragger,
.spectrum-slider {
    user-select: none;
}

.spectrum-sat,
.-theme-preserve {
    background-image: linear-gradient(to right, white, rgba(204, 154, 129, 0));
}

.spectrum-val,
.-theme-preserve {
    background-image: linear-gradient(to top, black, rgba(204, 154, 129, 0));
}

.spectrum-hue {
    background: linear-gradient(to left, #ff0000 0%, #ffff00 17%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%);
}

.spectrum-dragger {
    border-radius: 12px;
    height: 12px;
    width: 12px;
    border: 1px solid white;
    cursor: pointer;
    position: absolute;
    top: 0;
    left: 0;
    background: black;
    box-shadow: 0 0 2px 0px rgba(0, 0, 0, 0.24);
}

.spectrum-slider {
    position: absolute;
    top: -1px;
    cursor: pointer;
    width: 13px;
    height: 13px;
    border-radius: 13px;
    background-color: rgb(248, 248, 248);
    box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.37);
}

.spectrum-contrast-details {
    position: relative;
    background-color: white;
    width: 100%;
    height: 111px;
    top: 0;
    font-size: 13px;
    color: #333;
    border-top: var(--divider-border);
    line-height: initial;
    overflow: hidden;
    flex: none;
}

.spectrum-contrast-details {
    height: 78px;
    flex: none;
}

.spectrum-contrast-details.collapsed {
    height: 36px;
    flex: none;
}

.spectrum-contrast-details div.toolbar.expand {
    position: absolute;
    right: 6px;
    top: 6px;
    margin: 0;
}

.spectrum-contrast-details .toolbar-state-on [is=ui-icon] {
    background-color: rgb(110, 110, 110);
}

.spectrum-contrast-details.visible {
    display: initial;
}

.spectrum-contrast-details div.container {
    margin: 10px;
}

.spectrum-contrast-details.collapsed .expanded-details {
    display: none;
}

.spectrum-contrast-details .expanded-details {
    display: flex;
    margin: 12px 12px 0 4px;
}

.contrast-pass-fail {
    margin-left: 0.5em;
}

.spectrum-contrast-details .contrast-choose-bg-color,
.spectrum-contrast-details .contrast-thresholds {
    width: 145px;
}

.contrast-choose-bg-color {
    margin: 8px 0 0 5px;
    font-style: italic;
}

.contrast-link-label {
    cursor: pointer;
}

.contrast-link-label:hover {
    text-decoration: underline;
}

.spectrum-contrast-details .background-color {
    position: absolute;
    flex: none;
    right: 12px;
}

.spectrum-contrast-details .spectrum-eye-dropper {
    top: 2px;
    right: 34px;
    position: absolute;
    left: auto;
}

.contrast-details-value {
    color: #333;
    margin: 1px 5px;
    user-select: text;
}

.contrast-details-value [is=ui-icon] {
    display: none;
    margin-left: 5px;
    background-color: #333;
}

[is=ui-icon].smallicon-no {
    background-color: red;
}

.contrast-pass-fail span[is=ui-icon] {
    margin-left: 5px;
}

[is=ui-icon].smallicon-checkmark-square,
[is=ui-icon].smallicon-checkmark-behind {
    background-color: #00b06f;
}

.spectrum-contrast-details .contrast-details-value.contrast-unknown {
    background-color: white;
    color: #333;
    width: 3em;
    text-align: center;
}

.contrast-details-value .smallicon-checkmark-behind {
    margin-left: -6px;
}

.spectrum-contrast-details.contrast-fail .contrast-details-value .smallicon-no,
.contrast-details-value.contrast-aa .smallicon-checkmark-square,
.contrast-details-value.contrast-aaa .smallicon-checkmark-behind {
    display: inline-block;
}


.contrast-details-value .smallicon-no,
.contrast-details-value .smallicon-checkmark-square,
.contrast-details-value .smallicon-checkmark-behind {
    cursor: pointer;
}

.swatch {
    width: 32px;
    height: 32px;
    margin: 0;
    position: absolute;
    top: 15px;
    left: 44px;
    background-image: url(Images/checker.png);
    border-radius: 16px;
}

.swatch-inner,
.swatch-overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    display: inline-block;
    border-radius: 16px;
}

.swatch-inner-white {
    border: 1px solid #ddd;
}

.swatch-overlay {
    cursor: pointer;
    opacity: 0;
    padding: 4px;
}

.swatch-overlay:hover,
.swatch-overlay[data-keyboard-focus="true"]:focus {
    background-color: rgba(0, 0, 0, .3);
    opacity: 1;
}

.swatch-overlay:active {
    background-color: rgba(0, 0, 0, .5);
}

[is=ui-icon].icon-mask.copy-color-icon {
    background-color: white;
}

.spectrum-text {
    position: absolute;
    top: 60px;
    left: 16px;
}

.spectrum-text-value {
    display: inline-block;
    width: 40px;
    overflow: hidden;
    text-align: center;
    margin-right: 6px;
    line-height: 20px;
    padding: 0;
    color: #333;
    white-space: nowrap;
    box-shadow: var(--focus-ring-inactive-shadow);
}

.spectrum-text-label {
    letter-spacing: 39.5px;
    margin-top: 8px;
    display: block;
    color: #969696;
    margin-left: 16px;
    width: 174px;
}

.spectrum-text-hex > .spectrum-text-value {
    width: 178px;
}

.spectrum-text-hex > .spectrum-text-label {
    letter-spacing: normal;
    margin-left: 0px;
    text-align: center;
}

.spectrum-switcher {
    border-radius: 2px;
    height: 20px;
    width: 20px;
    padding: 2px;
}

:host-context(.-theme-with-dark-background) .spectrum-switcher {
    -webkit-filter: invert(60%);
}

.spectrum-display-switcher {
    top: 72px;
    position: absolute;
    right: 10px;
}

.spectrum-switcher:hover {
    background-color: #EEEEEE;
}

.spectrum-switcher[data-keyboard-focus="true"]:focus {
  background-color: var(--focus-bg-color);
}

.spectrum-eye-dropper {
    width: 32px;
    height: 24px;
    position: relative;
    left: 8px;
    top: 17px;
    cursor: pointer;
}

.spectrum-palette-container {
    border-top: var(--divider-border);
    position: relative;
    width: 100%;
    padding: 6px 24px 6px 6px;
    display: flex;
    flex-wrap: wrap;
}

.spectrum-palette {
    display: flex;
    flex-wrap: wrap;
    width: 198px;
}

.spectrum-palette-color {
    width: 12px;
    height: 12px;
    flex: 0 0 12px;
    border-radius: 2px;
    margin: 6px;
    cursor: pointer;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.1);
    background-position: -1px !important;
    z-index: 14;
}

.spectrum-palette-color:hover:not(.spectrum-shades-shown) > .spectrum-palette-color-shadow,
.spectrum-palette-color:focus:not(.spectrum-shades-shown) > .spectrum-palette-color-shadow {
    opacity: 0.2;
}

.spectrum-palette-color:hover:not(.spectrum-shades-shown) > .spectrum-palette-color-shadow:first-child,
.spectrum-palette-color:focus:not(.spectrum-shades-shown) > .spectrum-palette-color-shadow:first-child {
    opacity: 0.6;
    top: -3px;
    left: 1px;
}

.spectrum-palette-color-shadow {
    position: absolute;
    opacity: 0;
    margin: 0;
    top: -5px;
    left: 3px;
}

.palette-color-shades {
    position: absolute;
    background-color: white;
    height: 228px;
    width: 28px;
    box-shadow: 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12), 0 2px 4px -1px rgba(0, 0, 0, 0.4);
    z-index: 14;
    border-radius: 2px;
    transform-origin: 0px 228px;
    margin-top: 16px;
    margin-left: -8px;
}

.spectrum-palette > .spectrum-palette-color.spectrum-shades-shown {
    z-index: 15;
}

.palette-color-shades > .spectrum-palette-color {
    margin: 8px 0 0 0;
    margin-left: 8px;
    width: 12px;
}

.spectrum-palette > .spectrum-palette-color {
    transition: transform 100ms cubic-bezier(0, 0, 0.2, 1);
    will-change: transform;
    z-index: 13;
}

.spectrum-palette > .spectrum-palette-color.empty-color {
    border-color: transparent;
}

.spectrum-palette > .spectrum-palette-color:not(.empty-color):not(.has-material-shades):hover,
.palette-color-shades > .spectrum-palette-color:not(.empty-color):hover {
    transform: scale(1.15);
}

.spectrum-palette-color:not(.has-material-shades):focus {
  border: 1px solid var(--accent-color-hover);
  transform: scale(1.4);
}

.add-color-toolbar {
    margin-left: -3px;
    margin-top: -1px;
}

.spectrum-palette-switcher {
    right: 10px;
    top: 0;
    margin-top: 9px;
    position: absolute;
}

.palette-panel {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 100%;
    display: flex;
    flex-direction: column;
    background-color: white;
    z-index: 14;
    transition: transform 200ms cubic-bezier(0, 0, 0.2, 1), visibility 0s 200ms;
    border-top: var(--divider-border);
    visibility: hidden;
}

.palette-panel-showing > .palette-panel {
    transform: translateY(calc(-100% + 104px));
    transition-delay: 0s;
    visibility: visible;
}

.palette-panel > div.toolbar {
    position: absolute;
    right: 6px;
    top: 6px;
}

.palette-panel > div:not(.toolbar) {
    flex: 0 0 38px;
    border-bottom: var(--divider-border);
    padding: 12px;
    line-height: 14px;
    color: #333;
}

.palette-panel > div.palette-title {
    font-size: 14px;
    line-height: 16px;
    color: #333;
    flex-basis: 40px;
}

div.palette-preview {
    display: flex;
    cursor: pointer;
}

.palette-preview-title {
    flex: 0 0 84px;
}

.palette-preview > .spectrum-palette-color {
    margin-top: 1px;
}

.palette-preview[data-keyboard-focus="true"]:focus,
.palette-preview:hover {
    background-color: #eee;
}

.spectrum-overlay {
    z-index: 13;
    visibility: hidden;
    background-color: hsla(0, 0%, 0%, 0.5);
    opacity: 0;
    transition: opacity 100ms cubic-bezier(0, 0, 0.2, 1), visibility 0s 100ms;
}

.palette-panel-showing > .spectrum-overlay {
    transition-delay: 0s;
    visibility: visible;
    opacity: 1;
}

.spectrum-contrast-container {
    width: 100%;
    height: 100%;
}

.spectrum-contrast-line,
:host-context(.-theme-with-dark-background) .spectrum-contrast-line {
    fill: none;
    stroke: white;
    opacity: 0.7;
    stroke-width: 1.5px;
}

.delete-color-toolbar {
    position: absolute;
    right: 0;
    top: 0;
    background-color: #EFEFEF;
    visibility: hidden;
    z-index: 3;
    width: 36px;
    display: flex;
    align-items: center;
    padding-left: 4px;
    bottom: 2px;
    border-bottom-right-radius: 2px;
}

@keyframes showDeleteToolbar {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.delete-color-toolbar.dragging {
    visibility: visible;
    animation: showDeleteToolbar 100ms 150ms cubic-bezier(0, 0, 0.2, 1) backwards;
}

.delete-color-toolbar-active {
    background-color: #ddd;
    color: white;
}

.swatch.contrast {
    width: 30px;
    height: 30px;
    position: absolute;
    top: 0px;
    right: 0;
    left: auto;
    background-image: url(Images/checker.png);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.swatch.contrast .swatch-overlay {
    padding: 0;
}

.background-color .text-preview {
    color: black;
    font-size: 16px;
    position: relative;
    padding-bottom: 2px;
}

.swatch.contrast [is=ui-icon] {
    margin: -2px;
}
