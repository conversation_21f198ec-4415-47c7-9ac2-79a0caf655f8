{"extensions": [{"type": "@TextEditor.CodeMirrorMimeMode", "className": "CmModes.DefaultCodeMirrorMimeMode", "fileName": "clike.js", "mimeTypes": ["text/x-csrc", "text/x-c", "text/x-chdr", "text/x-c++src", "text/x-c++hdr", "text/x-java", "text/x-csharp", "text/x-scala", "x-shader/x-vertex", "x-shader/x-fragment"]}, {"type": "@TextEditor.CodeMirrorMimeMode", "className": "CmModes.DefaultCodeMirrorMimeMode", "fileName": "coffeescript.js", "mimeTypes": ["text/x-coffeescript"]}, {"type": "@TextEditor.CodeMirrorMimeMode", "className": "CmModes.DefaultCodeMirrorMimeMode", "fileName": "markdown.js", "mimeTypes": ["text/markdown", "text/x-markdown"]}, {"type": "@TextEditor.CodeMirrorMimeMode", "className": "CmModes.DefaultCodeMirrorMimeMode", "fileName": "php.js", "dependencies": ["clike.js"], "mimeTypes": ["application/x-httpd-php", "application/x-httpd-php-open", "text/x-php"]}, {"type": "@TextEditor.CodeMirrorMimeMode", "className": "CmModes.DefaultCodeMirrorMimeMode", "fileName": "python.js", "mimeTypes": ["text/x-python", "text/x-cython"]}, {"type": "@TextEditor.CodeMirrorMimeMode", "className": "CmModes.DefaultCodeMirrorMimeMode", "fileName": "shell.js", "mimeTypes": ["text/x-sh"]}, {"type": "@TextEditor.CodeMirrorMimeMode", "className": "CmModes.DefaultCodeMirrorMimeMode", "fileName": "livescript.js", "mimeTypes": ["text/x-livescript"]}, {"type": "@TextEditor.CodeMirrorMimeMode", "className": "CmModes.DefaultCodeMirrorMimeMode", "fileName": "clojure.js", "mimeTypes": ["text/x-clojure"]}, {"type": "@TextEditor.CodeMirrorMimeMode", "className": "CmModes.DefaultCodeMirrorMimeMode", "fileName": "jsx.js", "mimeTypes": ["text/jsx", "text/typescript-jsx"]}], "dependencies": ["text_editor"], "modules": ["cm_modes.js", "DefaultCodeMirrorMimeMode.js", "clike.js", "coffeescript.js", "php.js", "python.js", "shell.js", "livescript.js", "markdown.js", "clojure.js", "jsx.js"], "skip_compilation": ["clike.js", "coffeescript.js", "php.js", "python.js", "shell.js", "livescript.js", "markdown.js", "clojure.js", "jsx.js"]}