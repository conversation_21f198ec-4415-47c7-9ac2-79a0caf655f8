/*
 * Copyright 2018 The Chromium Authors. All rights reserved.
 * Use of this source code is governed by a BSD-style license that can be
 * found in the LICENSE file.
 */

.console-pins {
  max-height: 200px;
  overflow-y: auto;
  background: var(--toolbar-bg-color);
  --error-background-color: hsl(0, 100%, 97%);
  --error-border-color: hsl(0, 100%, 92%);
  --error-text-color: red;
}

:host-context(.-theme-with-dark-background) .console-pins {
  --error-background-color: hsl(0, 100%, 8%);
  --error-border-color: rgb(92, 0, 0);
  --error-text-color: hsl(0, 100%, 75%);
}

.console-pins:not(:empty) {
  border-bottom: 1px solid var(--divider-color);
}

.console-pin {
  position: relative;
  user-select: text;
  flex: none;
  padding: 2px 0 6px 24px;
}

.console-pin:not(:last-child) {
  border-bottom: 1px solid #e4e4e4;
}

.console-pin:not(:last-child).error-level:not(:focus-within) {
  border-top: 1px solid var(--error-border-color);
  border-bottom: 1px solid var(--error-border-color);
  margin-top: -1px;
}

.console-pin.error-level:not(:focus-within) {
  background-color: var(--error-background-color);
  color: var(--error-text-color);
}

.console-pin-name {
  margin-left: -4px;
  margin-bottom: 1px;
  height: auto;
}

.console-pin-name,
.console-pin-preview {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-height: 13px;
}

:host-context(.-theme-with-dark-background) .console-delete-pin {
  filter: brightness(2);
}

.console-delete-pin {
  position: absolute;
  top: 8px;
  left: 8px;
  opacity: 0.7;
  cursor: pointer;
}

.console-delete-pin:hover,
.console-delete-pin[data-keyboard-focus="true"]:focus {
  opacity: 1;
}

.console-pin-name:focus-within {
  background: #fff;
  box-shadow: var(--focus-ring-active-shadow) inset;
}

.console-pin:focus-within .console-pin-preview,
.console-pin-name:not(:focus-within):not(:hover) {
  opacity: 0.6;
}
