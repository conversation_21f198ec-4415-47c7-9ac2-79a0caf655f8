/*
 * Copyright 2018 The Chromium Authors. All rights reserved.
 * Use of this source code is governed by a BSD-style license that can be
 * found in the LICENSE file.
 */

#console-prompt .CodeMirror {
  padding: 3px 0 1px 0;
}

#console-prompt .CodeMirror-line {
  padding-top: 0;
}

#console-prompt .CodeMirror-lines {
  padding-top: 0;
}

#console-prompt .console-prompt-icon {
  position: absolute;
  left: -13px;
  top: 5px;
  user-select: none;
}

.console-eager-preview {
  padding-bottom: 2px;
  opacity: 0.6;
  position: relative;
  height: 15px;
}

.console-eager-inner-preview {
  text-overflow: ellipsis;
  overflow: hidden;
  margin-left: 4px;
  height: 100%;
}

.console-eager-inner-preview {
  white-space: nowrap;
}

.console-eager-inner-preview:empty,
.console-eager-inner-preview:empty + .preview-result-icon {
  opacity: 0;
}

.preview-result-icon {
  position: absolute;
  left: -13px;
  top: 1px;
}

.console-prompt-icon.console-prompt-incomplete {
  opacity: 0.65;
}
