{"extensions": [{"type": "view", "location": "panel", "id": "console", "title": "<PERSON><PERSON><PERSON>", "order": 20, "className": "Console.ConsolePanel"}, {"type": "view", "location": "drawer-view", "id": "console-view", "title": "<PERSON><PERSON><PERSON>", "persistence": "permanent", "order": 0, "className": "Console.ConsolePanel.WrapperView"}, {"type": "@<PERSON>.Revealer", "contextTypes": ["<PERSON><PERSON>Console"], "className": "Console.ConsolePanel.ConsoleRevealer"}, {"type": "action", "actionId": "console.show", "className": "Console.ConsoleView.ActionDelegate", "bindings": [{"shortcut": "Ctrl+`"}]}, {"type": "action", "category": "<PERSON><PERSON><PERSON>", "contextTypes": ["Console.ConsoleView"], "actionId": "console.clear", "title": "Clear console", "iconClass": "largeicon-clear", "className": "Console.ConsoleView.ActionDelegate", "bindings": [{"platform": "windows,linux", "shortcut": "Ctrl+L"}, {"platform": "mac", "shortcut": "Ctrl+L Meta+K"}]}, {"type": "action", "category": "<PERSON><PERSON><PERSON>", "actionId": "console.clear.history", "title": "Clear console history", "className": "Console.ConsoleView.ActionDelegate"}, {"type": "action", "category": "<PERSON><PERSON><PERSON>", "actionId": "console.create-pin", "iconClass": "largeicon-visibility", "className": "Console.ConsoleView.ActionDelegate", "title": "Create live expression"}, {"type": "setting", "category": "<PERSON><PERSON><PERSON>", "title": "Hide network messages", "settingName": "hideNetworkMessages", "settingType": "boolean", "defaultValue": false, "options": [{"value": true, "title": "Hide network messages"}, {"value": false, "title": "Show network messages"}]}, {"type": "setting", "category": "<PERSON><PERSON><PERSON>", "title": "Selected context only", "settingName": "selectedContext<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settingType": "boolean", "storageType": "session", "defaultValue": false, "options": [{"value": true, "title": "Only show messages from the current context (top, iframe, worker, extension)"}, {"value": false, "title": "Show messages from all contexts"}]}, {"type": "setting", "category": "<PERSON><PERSON><PERSON>", "title": "Log XMLHttpRequests", "settingName": "monitoringXHREnabled", "settingType": "boolean", "defaultValue": false}, {"type": "setting", "category": "<PERSON><PERSON><PERSON>", "title": "Show timestamps", "settingName": "consoleTimestampsEnabled", "settingType": "boolean", "defaultValue": false, "options": [{"value": true, "title": "Show timestamps"}, {"value": false, "title": "Hide timestamps"}]}, {"type": "setting", "category": "<PERSON><PERSON><PERSON>", "title": "Autocomplete from history", "settingName": "consoleHistoryAutocomplete", "settingType": "boolean", "defaultValue": true, "options": [{"value": true, "title": "Autocomplete from history"}, {"value": false, "title": "Do not autocomplete from history"}]}, {"type": "setting", "category": "<PERSON><PERSON><PERSON>", "title": "Group similar", "settingName": "consoleGroupSimilar", "settingType": "boolean", "defaultValue": true, "options": [{"value": true, "title": "Group similar messages in console"}, {"value": false, "title": "Do not group similar messages in console"}]}, {"type": "setting", "category": "<PERSON><PERSON><PERSON>", "title": "Eager evaluation", "settingName": "consoleEagerEval", "settingType": "boolean", "defaultValue": true, "options": [{"value": true, "title": "Eagerly evaluate console prompt text"}, {"value": false, "title": "Do not eagerly evaluate console prompt text"}]}, {"type": "setting", "category": "<PERSON><PERSON><PERSON>", "title": "Evaluate triggers user activation", "settingName": "consoleUserActivationEval", "settingType": "boolean", "defaultValue": true, "options": [{"value": true, "title": "Treat evaluation as user activation"}, {"value": false, "title": "Do not treat evaluation as user activation"}]}], "dependencies": ["components", "data_grid", "object_ui", "sdk", "formatter"], "scripts": [], "modules": ["console.js", "console-legacy.js", "ConsoleContextSelector.js", "ConsoleFilter.js", "ConsolePinPane.js", "ConsoleSidebar.js", "ConsoleViewport.js", "ConsoleViewMessage.js", "ConsolePrompt.js", "ConsoleView.js", "ConsolePanel.js"], "resources": ["consoleContextSelector.css", "consolePinPane.css", "consolePrompt.css", "consoleSidebar.css", "consoleView.css"]}