/*
 * Copyright (C) 2006, 2007, 2008 Apple Inc.  All rights reserved.
 * Copyright (C) 2009 <PERSON> <<EMAIL>>
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1.  Redistributions of source code must retain the above copyright
 *     notice, this list of conditions and the following disclaimer.
 * 2.  Redistributions in binary form must reproduce the above copyright
 *     notice, this list of conditions and the following disclaimer in the
 *     documentation and/or other materials provided with the distribution.
 * 3.  Neither the name of Apple Computer, Inc. ("Apple") nor the names of
 *     its contributors may be used to endorse or promote products derived
 *     from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY APPLE AND ITS CONTRIBUTORS "AS IS" AND ANY
 * EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL APPLE OR ITS CONTRIBUTORS BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
 * THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

.console-view {
    background-color: white;
    overflow: hidden;
    --message-border-color: rgb(240, 240, 240);
    --warning-border-color: hsl(50, 100%, 88%);
    --error-border-color: hsl(0, 100%, 92%);
    --error-text-color: red;
}

.-theme-with-dark-background .console-view {
    --message-border-color: rgb(58, 58, 58);
    --warning-border-color: rgb(102, 85, 0);
    --error-border-color: rgb(92, 0, 0);
    --error-text-color: hsl(0, 100%, 75%);
}

.console-toolbar-container {
    display: flex;
    flex: none;
}

.console-main-toolbar {
    flex: 1 1 auto;
}

.console-toolbar-container > .toolbar {
    background-color: var(--toolbar-bg-color);
    border-bottom: var(--divider-border);
}

.console-view-wrapper {
    background-color: #eee;
}

.console-view-fix-select-all {
    height: 0;
    overflow: hidden;
}

.console-settings-pane {
    flex: none;
    background-color: var(--toolbar-bg-color);
    border-bottom: var(--divider-border);
}

.console-settings-pane .toolbar {
    flex: 1 1;
}

#console-messages {
    flex: 1 1;
    overflow-y: auto;
    word-wrap: break-word;
    user-select: text;
    transform: translateZ(0);
    overflow-anchor: none;  /* Chrome-specific scroll-anchoring opt-out */
}

#console-prompt {
    clear: right;
    position: relative;
    margin: 0 22px 0 20px;
}

.console-prompt-editor-container {
    min-height: 21px;
}

.console-message,
.console-user-command {
    clear: right;
    position: relative;
    padding: 3px 22px 1px 0;
    margin-left: 24px;
    min-height: 17px;  /* Sync with ConsoleViewMessage.js */
    flex: auto;
    display: flex;
}

.console-message > * {
    flex: auto;
}

.console-timestamp {
    color: gray;
    user-select: none;
    flex: none;
    margin-right: 5px;
}

.message-level-icon, .command-result-icon {
    position: absolute;
    left: -17px;
    top: 4px;
    user-select: none;
}

.console-message-repeat-count {
    margin: 2px 0 0 10px;
    flex: none;
}

.repeated-message {
    margin-left: 4px;
}

.repeated-message .message-level-icon {
    display: none;
}

.repeated-message .console-message-stack-trace-toggle,
.repeated-message > .console-message-text {
    flex: 1;
}

.console-error-level .repeated-message,
.console-warning-level .repeated-message,
.console-verbose-level .repeated-message,
.console-info-level .repeated-message {
    display: flex;
}

.console-info {
    color: rgb(128, 128, 128);
    font-style: italic;
    padding-bottom: 2px;
}

.console-group .console-group > .console-group-messages {
    margin-left: 16px;
}

.console-group-title.console-from-api {
    font-weight: bold;
}

.console-group-title .console-message {
    margin-left: 12px;
}

.expand-group-icon {
    user-select: none;
    flex: none;
    background-color: rgb(110, 110, 110);
    position: relative;
    left: 10px;
    top: 5px;
    margin-right: 2px;
}

.console-group-title .message-level-icon {
    display: none;
}

.console-message-repeat-count .expand-group-icon {
    left: 2px;
    top: 2px;
    background-color: #fff;
    margin-right: 4px;
}

.console-group {
    position: relative;
}

.console-message-wrapper {
    display: flex;
    border-top: 1px solid var(--message-border-color);
    border-bottom: 1px solid transparent;
}

.console-message-wrapper:first-of-type {
    border-top-color: transparent;
}

.console-message-wrapper.console-adjacent-user-command-result:not(.console-error-level):not(.console-warning-level) {
    border-top-width: 0px;
}

.console-message-wrapper.console-error-level,
.console-message-wrapper.console-error-level:not(:focus) + .console-message-wrapper:not(.console-warning-level):not(:focus) {
    border-top-color: var(--error-border-color);
}

.console-message-wrapper.console-warning-level,
.console-message-wrapper.console-warning-level:not(:focus) + .console-message-wrapper:not(.console-error-level):not(:focus) {
    border-top-color: var(--warning-border-color);
}

.console-message-wrapper:last-of-type {
    border-bottom-color: var(--message-border-color);
}

.console-message-wrapper.console-error-level:last-of-type {
    border-bottom-color: var(--error-border-color);
}

.console-message-wrapper.console-warning-level:last-of-type {
    border-bottom-color: var(--warning-border-color);
}

.console-message-wrapper.console-adjacent-user-command-result:not(.console-error-level):not(.console-warning-level):focus {
    border-top-width: 1px;
}

.console-message-wrapper.console-adjacent-user-command-result:not(.console-error-level):not(.console-warning-level):focus .console-message {
    padding-top: 2px;
    min-height: 16px;
}

.console-message-wrapper.console-adjacent-user-command-result:not(.console-error-level):not(.console-warning-level):focus .command-result-icon {
    top: 3px;
}

.console-message-wrapper:focus,
.console-message-wrapper:focus:last-of-type {
    border-top-color: hsl(214, 67%, 88%);
    border-bottom-color: hsl(214, 67%, 88%);
    background-color: hsl(214, 48%, 95%);
}

.console-message-wrapper.console-error-level:focus,
.console-message-wrapper.console-error-level:focus:last-of-type {
    --error-text-color: rgb(200, 0, 0);
}

.-theme-with-dark-background .console-message-wrapper.console-error-level:focus,
.-theme-with-dark-background .console-message-wrapper.console-error-level:focus:last-of-type {
    --error-text-color: hsl(0, 100%, 75%);
}

.-theme-with-dark-background .console-message-wrapper:focus,
.-theme-with-dark-background .console-message-wrapper:focus:last-of-type {
    border-top-color: hsl(214, 47%, 48%);
    border-bottom-color: hsl(214, 47%, 48%);
    background-color: hsl(214, 19%, 20%);
}

.console-message-wrapper:focus + .console-message-wrapper {
    border-top-color: transparent;
}

.console-message-wrapper .nesting-level-marker {
    width: 14px;
    flex: 0 0 auto;
    border-right: 1px solid #a5a5a5;
    position: relative;
    margin-bottom: -1px;
    margin-top: -1px;
}

.console-message-wrapper:last-child .nesting-level-marker::before,
.console-message-wrapper .nesting-level-marker.group-closed::before {
    content: "";
}

.console-message-wrapper .nesting-level-marker::before {
    border-bottom: 1px solid #a5a5a5;
    position: absolute;
    top: 0;
    left: 0;
    margin-left: 100%;
    width: 3px;
    height: 100%;
    box-sizing: border-box;
}

.console-error-level {
    background-color: hsl(0, 100%, 97%);
}

.-theme-with-dark-background .console-error-level {
    background-color: hsl(0, 100%, 8%);
}

.console-warning-level {
    background-color: hsl(50, 100%, 95%);
}

.-theme-with-dark-background .console-warning-level {
    background-color: hsl(50, 100%, 10%);
}

.console-warning-level .console-message-text {
    color: hsl(39, 100%, 18%);
}

.console-error-level .console-message-text,
.console-error-level .console-view-object-properties-section {
    color: var(--error-text-color) !important;
}

.console-system-type.console-info-level {
    color: blue;
}

.-theme-with-dark-background .console-verbose-level:not(.console-warning-level) .console-message-text,
.-theme-with-dark-background .console-system-type.console-info-level {
    color: hsl(220, 100%, 65%) !important;
}

.console-message.console-warning-level {
    background-color: rgb(255, 250, 224);
}

#console-messages .link {
    text-decoration: underline;
}

#console-messages .link,
#console-messages .devtools-link {
    color: rgb(33%, 33%, 33%);
    cursor: pointer;
    word-break: break-all;
}

#console-messages .link:hover,
#console-messages .devtools-link:hover {
    color: rgb(15%, 15%, 15%);
}

.console-group-messages .section {
    margin: 0 0 0 12px !important;
}

.console-group-messages .section > .header {
    padding: 0 8px 0 0;
    background-image: none;
    border: none;
    min-height: 0;
}

.console-group-messages .section > .header::before {
    margin-left: -12px;
}

.console-group-messages .section > .header .title {
    color: #222;
    font-weight: normal;
    line-height: 13px;
}

.console-group-messages .section .properties li .info {
    padding-top: 0;
    padding-bottom: 0;
    color: rgb(60%, 60%, 60%);
}

.console-object-preview {
    white-space: normal;
    word-wrap: break-word;
    font-style: italic;
}

.console-object-preview .name {
    /* Follows .section .properties .name, .event-properties .name */
    color: rgb(136, 19, 145);
    flex-shrink: 0;
}

.console-message-text .object-value-string,
.console-message-text .object-value-regexp,
.console-message-text .object-value-symbol {
    white-space: pre-wrap;
    word-break: break-all;
}

.console-message-formatted-table {
    clear: both;
}

.console-message .source-code {
    line-height: 1.2;
}

.console-message-anchor {
    float: right;
    text-align: right;
    max-width: 100%;
    margin-left: 4px;
}

.console-message-badge {
    float: right;
    margin-left: 4px;
}

.console-message-nowrap-below,
.console-message-nowrap-below div,
.console-message-nowrap-below span {
    white-space: nowrap !important;
}

.object-state-note {
    display: inline-block;
    width: 11px;
    height: 11px;
    color: white;
    text-align: center;
    border-radius: 3px;
    line-height: 13px;
    margin: 0 6px;
    font-size: 9px;
}

.-theme-with-dark-background .object-state-note {
    background-color: hsl(230, 100%, 80%);
}

.info-note {
    background-color: rgb(179, 203, 247);
}

.info-note::before {
    content: "i";
}

.console-view-object-properties-section:not(.expanded) .info-note {
    display: none;
}

.console-view-object-properties-section {
    padding: 0px;
    position: relative;
    vertical-align: baseline;
    color: inherit;
    display: inline-block;
    overflow-wrap: break-word;
    max-width: 100%;
}

.console-object {
    white-space: pre-wrap;
    word-break: break-all;
}

.console-message-stack-trace-toggle {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
}

.console-message-stack-trace-wrapper {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    align-items: stretch;
}

.console-message-stack-trace-wrapper > * {
    flex: none;
}

.console-message-expand-icon {
    margin-bottom: -2px;
}

.console-searchable-view {
    max-height: 100%;
}

.console-view-pinpane {
    flex: none;
    max-height: 50%;
}
