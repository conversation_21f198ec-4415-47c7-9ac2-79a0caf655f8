<?xml version="1.0" encoding="utf-8"?>
<grit-part>
  <message name="IDS_DEVTOOLS_003dec76aeb8e364febdc819d0587626" desc="Title of the sidebar in the Console">
    console sidebar
  </message>
  <message name="IDS_DEVTOOLS_02969932cf572213e2401893f4182af2" desc="A context menu item in the Console Pin Pane of the Console panel">
    Remove all expressions
  </message>
  <message name="IDS_DEVTOOLS_0351df5b6bdc3a8cfb762210881619a4" desc="Title of toolbar item in console context selector of the console panel">
    JavaScript context: Not selected
  </message>
  <message name="IDS_DEVTOOLS_047e62ee63b0c14a249a79bc6be0a493" desc="Title of a setting under the Console category that can be invoked through the Command Menu">
    Do not group similar messages in console
  </message>
  <message name="IDS_DEVTOOLS_04c247c2ba261c7511e4f479728bd7ea" desc="Title of a setting under the Console category that can be invoked through the Command Menu">
    Do not treat evaluation as user activation
  </message>
  <message name="IDS_DEVTOOLS_0598dbc1dda8ffdd9f5015fccf675ae1" desc="Text in Console Sidebar of the Console panel to show that there are no user messages">
    No user messages
  </message>
  <message name="IDS_DEVTOOLS_0a2f561bf52ee6d8cd2dda123c8f74fe" desc="Text in Console View of the Console panel">
    Default levels
  </message>
  <message name="IDS_DEVTOOLS_0b03bf8af8380330766ad55ff2004a95" desc="Alternative title text of a setting in Console View of the Console panel">
    Selected context only
  </message>
  <message name="IDS_DEVTOOLS_0bafe921e13474bf5fc3f1ad62496905" desc="Message text in Console View Message of the Console panel">
    [Intervention] <ph name="MESSAGETEXT">$1s<ex>console.log(1)</ex></ph>
  </message>
  <message name="IDS_DEVTOOLS_0bbce02e304562c295a1d57d66c296d3" desc="Text in Console View Message of the Console panel">
    &lt;URL&gt;
  </message>
  <message name="IDS_DEVTOOLS_0fa06588bd5c83bd6f3b1734eefc6b16" desc="Text in Console View of the Console panel">
    Hide messages from <ph name="NEW_COMMON_PARSEDURL_CONSOLEMESSAGE_URL__DISPLAYNAME">$1s<ex>index.js</ex></ph>
  </message>
  <message name="IDS_DEVTOOLS_1a37d25ba6689d174f51ba8e2425fde6" desc="Filter name in Console Sidebar of the Console panel">
    &lt;other&gt;
  </message>
  <message name="IDS_DEVTOOLS_20b290881fd9c9f310c38f92e963193f" desc="Text in Console Sidebar of the Console panel to show that there are more than 1 verbose messages">
    <ph name="PH1">$1d<ex>2</ex></ph> verbose
  </message>
  <message name="IDS_DEVTOOLS_2169b4627df97333ed94d1e30a9b8148" desc="Text in Console Sidebar of the Console panel to show that there are more than 1 errors">
    <ph name="PH1">$1d<ex>2</ex></ph> errors
  </message>
  <message name="IDS_DEVTOOLS_26649c8f3cadc9c0170f2443e6fc0252" desc="Text in Console View Message of the Console panel">
    &lt;attribute&gt;
  </message>
  <message name="IDS_DEVTOOLS_278cf66841e1d342f3115006e82c5dd9" desc="Text in Console View Message of the Console panel">
    Repeat <ph name="THIS__REPEATCOUNT">$1s<ex>3</ex></ph>
  </message>
  <message name="IDS_DEVTOOLS_2a5a5a712c2bfb6c506cb669b8d81709" desc="Title of an action in the console tool to clear">
    Clear console history
  </message>
  <message name="IDS_DEVTOOLS_2b76cfdf5b790de0a27ba7fc58a919c8" desc="Accessible name in Console View Message of the Console panel">
    Warning <ph name="ACCESSIBLENAME">$1s<ex>Repeat 4</ex></ph>
  </message>
  <message name="IDS_DEVTOOLS_2b918231caba2d08f2223246ac06222c" desc="A context menu item in the Console View of the Console panel">
    Copy visible styled selection
  </message>
  <message name="IDS_DEVTOOLS_2d82fe5a069854a35204b4e64e8e08ae" desc="Tooltip text that appears when hovering over the largeicon settings gear in show settings pane setting in console view of the console panel">
    Console settings
  </message>
  <message name="IDS_DEVTOOLS_2f20693f226f0545a8b68ef4f59fb02e" desc="Message text in Console View Message of the Console panel">
    [Violation] <ph name="MESSAGETEXT">$1s<ex>console.log(1)</ex></ph>
  </message>
  <message name="IDS_DEVTOOLS_375855385115b292f160b5131495003d" desc="Message element text content in Console View Message of the Console panel">
    <ph name="LOCKED_1">console.clear()</ph> was prevented due to &apos;Preserve log&apos;
  </message>
  <message name="IDS_DEVTOOLS_383b1deb90603a79d86f3ae82e55a9e2" desc="Text in Console View of the Console panel">
    <ph name="THIS__HIDDENBYFILTERCOUNT">$1s<ex>3</ex></ph> hidden
  </message>
  <message name="IDS_DEVTOOLS_388024c56c38c3d1c635b09a2b28b8ac" desc="Note title in Console View Message of the Console panel">
    Value below was evaluated just now.
  </message>
  <message name="IDS_DEVTOOLS_3cec12c2368b11d9585823ac9d631edb" desc="Text in Console View of the Console panel">
    Hide all
  </message>
  <message name="IDS_DEVTOOLS_405b66a12f196edc715fe9f3f2c84b04" desc="Title of a setting under the Console category that can be invoked through the Command Menu">
    Hide timestamps
  </message>
  <message name="IDS_DEVTOOLS_411f3a865d83fba7f4772925666cfa7c" desc="Title of a setting under the Console category that can be invoked through the Command Menu">
    Hide network messages
  </message>
  <message name="IDS_DEVTOOLS_468312e6ff2ebf1b104e5d7f489de74d" desc="Tooltip text that appears on the setting when hovering over it in Console View of the Console panel">
    Eagerly evaluate text in the prompt
  </message>
  <message name="IDS_DEVTOOLS_4c8b530161336f95d0c2f13337556954" desc="Title of a setting under the Console category that can be invoked through the Command Menu">
    Show network messages
  </message>
  <message name="IDS_DEVTOOLS_4cad9e20fde3f8991c5dd1d6a0fe13e7" desc="A context menu item in the Console Pin Pane of the Console panel">
    Remove expression
  </message>
  <message name="IDS_DEVTOOLS_53f0fca28a013c116a1df533d9bdf764" desc="Message text in Console View Message of the Console panel">
    [Deprecation] <ph name="MESSAGETEXT">$1s<ex>console.log(1)</ex></ph>
  </message>
  <message name="IDS_DEVTOOLS_5045f70382e14de07fd8328426d7083b" desc="Text in Console Sidebar of the Console panel to show that there is 1 error">
    1 error
  </message>
  <message name="IDS_DEVTOOLS_58a747ef5d07d22101bdcd058e772ff9" desc="Text in Console Sidebar of the Console panel to show that there are no messages">
    No messages
  </message>
  <message name="IDS_DEVTOOLS_59d96b10d94add90060c79efdd8db191" desc="Text in Console Sidebar of the Console panel to show that there are no warnings">
    No warnings
  </message>
  <message name="IDS_DEVTOOLS_5cc9e9d6312328b3f61b7aa86b0b5b5e" desc="Text in Console Sidebar of the Console panel to show that there is 1 user message">
    1 user message
  </message>
  <message name="IDS_DEVTOOLS_6047a6c9fb775557639afb3fbc90b4e7" desc="Text in Console View of the Console panel">
    Log levels
  </message>
  <message name="IDS_DEVTOOLS_63e4e92bb7d207ca577b11c07f827279" desc="Text in Console Context Selector of the Console panel">
    Extension
  </message>
  <message name="IDS_DEVTOOLS_650140bd73628d283d870d4648ae3324" desc="Text in Console View of the Console panel">
    Find string in logs
  </message>
  <message name="IDS_DEVTOOLS_6775864ce3df150aabbf2d60eb12f513" desc="Text in Console Pin Pane of the Console panel">
    Live expression editor
  </message>
  <message name="IDS_DEVTOOLS_6cdd8769d46bb96331ac4a96d3ea84b8" desc="Message element title in Console View Message of the Console panel">
    Clear all messages with <ph name="UI_SHORTCUTREGISTRY_SHORTCUTTITLEFORACTION__CONSOLE_CLEAR__">$1s<ex>Ctrl+L</ex></ph>
  </message>
  <message name="IDS_DEVTOOLS_6eed4ce2d5859b11dc44a5e3bd91af20" desc="Title of a setting under the Console category in Settings">
    Eager evaluation
  </message>
  <message name="IDS_DEVTOOLS_7a1920d61156abc05a60135aefe8bc67" desc="A context menu item in the Console View of the Console panel">
    Default
  </message>
  <message name="IDS_DEVTOOLS_7be1a30a7758269755609f1f7434d828" desc="Title of a setting under the Console category that can be invoked through the Command Menu">
    Group similar messages in console
  </message>
  <message name="IDS_DEVTOOLS_7c1e3c25af10fa5d8046dff929148280" desc="Text in Console Sidebar of the Console panel to show that there is 1 info">
    1 info
  </message>
  <message name="IDS_DEVTOOLS_93977f1310f482395375a9950b512462" desc="Title of a setting under the Console category that can be invoked through the Command Menu">
    Eagerly evaluate console prompt text
  </message>
  <message name="IDS_DEVTOOLS_95c74dafb449d894014c2eb1d80ded01" desc="A context menu item in the Console Pin Pane of the Console panel">
    Edit expression
  </message>
  <message name="IDS_DEVTOOLS_97b0da743d68f96933d990003383ccf1" desc="Text in Console Sidebar of the Console panel to show that there is 1 warning">
    1 warning
  </message>
  <message name="IDS_DEVTOOLS_a092483ed730ed040e5df5776dca49e5" desc="Title of a setting under the Console category in Settings">
    Log XMLHttpRequests
  </message>
  <message name="IDS_DEVTOOLS_a1948ddb50d0bbd31e36f1fbc45479a0" desc="Title of a setting under the Console category that can be invoked through the Command Menu">
    Autocomplete from history
  </message>
  <message name="IDS_DEVTOOLS_a1a6657be79cc0fc1e9b23b9e108f043" desc="Text in Console Pin Pane of the Console panel">
    Expression
  </message>
  <message name="IDS_DEVTOOLS_a3eb3c95c4cb8e06fd3682c1f0d70bc0" desc="Tooltip text that appears on the setting when hovering over it in Console View of the Console panel">
    Only show messages from the current context (top, iframe, worker, extension)
  </message>
  <message name="IDS_DEVTOOLS_a5e4febd4637e35098ee6e8f493ca390" desc="Text in Console Sidebar of the Console panel to show that there is 1 message">
    1 message
  </message>
  <message name="IDS_DEVTOOLS_a6f9c9bba5831ed632218f165adcfeed" desc="Text in Console Sidebar of the Console panel to show that there are more than 1 messages">
    <ph name="PH1">$1d<ex>2</ex></ph> messages
  </message>
  <message name="IDS_DEVTOOLS_abba8787b900565790eae8ceceed3c2b" desc="Title of a setting under the Console category that can be invoked through the Command Menu">
    Do not autocomplete from history
  </message>
  <message name="IDS_DEVTOOLS_ac17bdcb3f6c8d86ccb64aff1ab8db7c" desc="Message element text content in Console View Message of the Console panel">
    Console was cleared
  </message>
  <message name="IDS_DEVTOOLS_ace3e0307b2ce23a81b18747fc5f555f" desc="Note title in Console View Message of the Console panel">
    Function was resolved from bound function.
  </message>
  <message name="IDS_DEVTOOLS_ad410611ff249a09acdba47cc8505186" desc="Text in Console Sidebar of the Console panel to show that there is no verbose messages">
    No verbose
  </message>
  <message name="IDS_DEVTOOLS_b184e7a44bed11a41d9c104529010e23" desc="Text in Console View of the Console panel">
    <ph name="THIS__LEVELLABELS_NAME_">$1s<ex>Warnings</ex></ph> only
  </message>
  <message name="IDS_DEVTOOLS_b2f8489bbd55a4e9b9edbbbfe49edf70" desc="Text of a DOM element in Console Pin Pane of the Console panel">
    not available
  </message>
  <message name="IDS_DEVTOOLS_b4372757bcd49be89af758f485f8666f" desc="Text in Console Sidebar of the Console panel to show that there are more than 1 warnings">
    <ph name="PH1">$1d<ex>2</ex></ph> warnings
  </message>
  <message name="IDS_DEVTOOLS_bb9a56bce1ad1ae114ffd9807146f3f4" desc="Text in Console Sidebar of the Console panel to show that there is no info">
    No info
  </message>
  <message name="IDS_DEVTOOLS_c0274fa278f2e0dfef862234e0eb9b8b" desc="Element text content in Console View Message of the Console panel">
    &lt;exception&gt;
  </message>
  <message name="IDS_DEVTOOLS_c43a34d2abee57824fac5bb704994d88" desc="Text in Console Prompt of the Console panel">
    Console prompt
  </message>
  <message name="IDS_DEVTOOLS_c795329f17cb738005b904b650167704" desc="Text in Console Sidebar of the Console panel to show that there is 1 verbose message">
    1 verbose
  </message>
  <message name="IDS_DEVTOOLS_c9818b2c0890a205d294a2b31354f8b4" desc="Text in Console View of the Console panel">
    All levels
  </message>
  <message name="IDS_DEVTOOLS_ca3c4b269c23985d3c1204c203ad84d8" desc="Tooltip text that appears on the setting when hovering over it in Console View of the Console panel">
    Group similar
  </message>
  <message name="IDS_DEVTOOLS_ca6ceb8e597abb1298b84b1aaadc229b" desc="Title of a setting under the Console category that can be invoked through the Command Menu">
    Show timestamps
  </message>
  <message name="IDS_DEVTOOLS_caf037034c3205725511c1216f772402" desc="Text in Console View Message of the Console panel">
    &lt;some&gt; event
  </message>
  <message name="IDS_DEVTOOLS_cf3eed6aa30c47cec1321a835dc7b1ac" desc="Title of a setting under the Console category that can be invoked through the Command Menu">
    Treat evaluation as user activation
  </message>
  <message name="IDS_DEVTOOLS_d0990066fbdfd3753fb45300d57e4364" desc="Text in Console View of the Console panel">
    Hide network
  </message>
  <message name="IDS_DEVTOOLS_d35866a5d1161b48670e28e8c8550e89" desc="Message prefix in Console View Message of the Console panel">
    Assertion failed: '''
  </message>
  <message name="IDS_DEVTOOLS_d3f526e5326c06d47ab12240d604f87a" desc="Text in Console View Message of the Console panel">
    took &lt;N&gt;ms
  </message>
  <message name="IDS_DEVTOOLS_d4a9fa383ab700c5bdd6f31cf7df0faf" desc="Sdk console message message level verbose of level Labels in Console View of the Console panel">
    Verbose
  </message>
  <message name="IDS_DEVTOOLS_d61de8e8b9dfdf0e7a52bf2e4fd23e72" desc="Text in Console View Message of the Console panel">
    ''' M&lt;XX&gt;
  </message>
  <message name="IDS_DEVTOOLS_d7746aa6b562c73487a4015eef3244e9" desc="Text in Console Context Selector of the Console panel">
    IFrame
  </message>
  <message name="IDS_DEVTOOLS_d779282283c011149d163edbcd5e5f11" desc="Side effect label title in Console Pin Pane of the Console panel">
    Evaluate, allowing side effects
  </message>
  <message name="IDS_DEVTOOLS_dd4a2c85f951ba07ba460abc6b726615" desc="Text in Console Sidebar of the Console panel to show that there are more than 1 user messages">
    <ph name="PH1">$1d<ex>2</ex></ph> user messages
  </message>
  <message name="IDS_DEVTOOLS_de6fc8cb2d1b20158aeda5cd1cb2e03c" desc="Title of a setting under the Console category that can be invoked through the Command Menu">
    Show messages from all contexts
  </message>
  <message name="IDS_DEVTOOLS_dfcc689f6e70e39a408b78912822cec9" desc="Accessible name in Console View Message of the Console panel">
    Error <ph name="ACCESSIBLENAME">$1s<ex>Repeat 4</ex></ph>
  </message>
  <message name="IDS_DEVTOOLS_e178ba306750ffbfdba284f60a3743b0" desc="Title of a setting under the Console category that can be invoked through the Command Menu">
    Do not eagerly evaluate console prompt text
  </message>
  <message name="IDS_DEVTOOLS_e17fdb615452f440c793b5ddad90dd5e" desc="Sdk console message message level warning of level Labels in Console View of the Console panel">
    Warnings
  </message>
  <message name="IDS_DEVTOOLS_e1dd1b8e32626f508bd3a5612a60ab97" desc="Title of an action in the console tool to create pin">
    Create live expression
  </message>
  <message name="IDS_DEVTOOLS_e6009dd637bf0d330cc5316cd67d9533" desc="Text in Console Sidebar of the Console panel to show that there are more than 1 info">
    <ph name="PH1">$1d<ex>2</ex></ph> info
  </message>
  <message name="IDS_DEVTOOLS_e6320ee2b5f66ac1520e6156e5ac8fb3" desc="Text in Console Context Selector of the Console panel">
    JavaScript context: <ph name="THIS_TITLEFOR_ITEM_">$1s<ex>top</ex></ph>
  </message>
  <message name="IDS_DEVTOOLS_eacaf2bd1c1414e5086cf04573e154ef" desc="Text in Console View of the Console panel">
    e.g. <ph name="LOCKED_1">/event\d/ -cdn url:a.com</ph>
  </message>
  <message name="IDS_DEVTOOLS_eb7431d253e289bd63b3d73927cd40c1" desc="Text in Console Sidebar of the Console panel to show that there are no errors">
    No errors
  </message>
  <message name="IDS_DEVTOOLS_ebfab4df1bb91688c62d4523eba870a5" desc="Title of a setting under the Console category in Settings">
    Evaluate triggers user activation
  </message>
  <message name="IDS_DEVTOOLS_f0b0e075af71ecd9b3435697417c1f05" desc="Title of level menu button in console view of the console panel">
    Log level: <ph name="TEXT">$1s<ex>All levels</ex></ph>
  </message>
  <message name="IDS_DEVTOOLS_fb554075057f4d116097df434bcef393" desc="Note title in Console View Message of the Console panel">
    This value will not be collected until console is cleared.
  </message>
  <message name="IDS_DEVTOOLS_fbb5dc83cad4daf746720c90bfb8d306" desc="Text in Console View of the Console panel">
    Custom levels
  </message>
</grit-part>