/*
 * Copyright 2017 The Chromium Authors. All rights reserved.
 * Use of this source code is governed by a BSD-style license that can be
 * found in the LICENSE file.
 */

.image-preview-container {
    background: transparent;
    text-align: center;
    border-spacing: 0;
}

.image-preview-container img {
    margin: 2px auto;
    max-width: 100px;
    max-height: 100px;
    background-image: url(Images/checker.png);
    user-select: text;
    -webkit-user-drag: auto;
}

.image-container {
    padding: 0;
}
