/*
 * Copyright 2015 The Chromium Authors. All rights reserved.
 * Use of this source code is governed by a BSD-style license that can be
 * found in the LICENSE file.
 */

:host {
  display: inline;
}

.stack-preview-async-description {
    padding: 3px 0 1px;
    font-style: italic;
}

.stack-preview-container .webkit-html-blackbox-link {
    opacity: 0.6;
}

.stack-preview-container > tr {
    height: 16px;
    line-height: 16px;
}

.stack-preview-container td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.stack-preview-container .function-name {
    max-width: 80em;
}

.stack-preview-container:not(.show-blackboxed) > tr.blackboxed {
    display: none;
}

.stack-preview-container.show-blackboxed > tr.show-blackboxed-link {
    display: none;
}

.stack-preview-container > tr.show-blackboxed-link {
    font-style: italic;
}
