function e(e,t,n,o){Object.defineProperty(e,t,{get:n,set:o,enumerable:!0,configurable:!0})}var t=("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}).parcelRequire20b5;t.register("dYxX8",(function(n,o){e(n.exports,"CSSOverviewCompletedView",(()=>t("bnRQQ"))),e(n.exports,"CSSOverviewController",(()=>t("jVHKV"))),e(n.exports,"CSSOverviewModel",(()=>t("1rQgP"))),e(n.exports,"CSSOverviewPanel",(()=>t("hjEJK"))),e(n.exports,"CSSOverviewProcessingView",(()=>t("j1L5M"))),e(n.exports,"CSSOverviewSidebarPanel",(()=>t("dnEPG"))),e(n.exports,"CSSOverviewUnusedDeclarations",(()=>t("iaAfh")));t("jVHKV"),t("iaAfh"),t("1rQgP"),t("j1L5M"),t("bnRQQ"),t("dnEPG"),t("hjEJK"),t("bnRQQ"),t("jVHKV"),t("1rQgP"),t("hjEJK"),t("j1L5M"),t("dnEPG"),t("iaAfh")})),t.register("jVHKV",(function(n,o){e(n.exports,"OverviewController",(()=>r));var i=t("koSS8"),s=t("eQFvP");class r extends i.ObjectWrapper.ObjectWrapper{currentUrl;constructor(){super(),this.currentUrl=s.TargetManager.TargetManager.instance().inspectedURL(),s.TargetManager.TargetManager.instance().addEventListener(s.TargetManager.Events.InspectedURLChanged,this.#e,this)}#e(){this.currentUrl!==s.TargetManager.TargetManager.instance().inspectedURL()&&(this.currentUrl=s.TargetManager.TargetManager.instance().inspectedURL(),this.dispatchEventToListeners("Reset"))}}})),t.register("iaAfh",(function(n,o){e(n.exports,"CSSOverviewUnusedDeclarations",(()=>l));var i=t("ixFnt");const s={topAppliedToAStatically:"`Top` applied to a statically positioned element",leftAppliedToAStatically:"`Left` applied to a statically positioned element",rightAppliedToAStatically:"`Right` applied to a statically positioned element",bottomAppliedToAStatically:"`Bottom` applied to a statically positioned element",widthAppliedToAnInlineElement:"`Width` applied to an inline element",heightAppliedToAnInlineElement:"`Height` applied to an inline element",verticalAlignmentAppliedTo:"Vertical alignment applied to element which is neither `inline` nor `table-cell`"},r=i.i18n.registerUIStrings("panels/css_overview/CSSOverviewUnusedDeclarations.ts",s),a=i.i18n.getLocalizedString.bind(void 0,r);class l{static add(e,t,n){const o=e.get(t)||[];o.push(n),e.set(t,o)}static checkForUnusedPositionValues(e,t,n,o,i,r,l,c){if("static"===n[o]){if("auto"!==n[i]){const o=a(s.topAppliedToAStatically);this.add(e,o,{declaration:`top: ${n[i]}`,nodeId:t})}if("auto"!==n[r]){const o=a(s.leftAppliedToAStatically);this.add(e,o,{declaration:`left: ${n[r]}`,nodeId:t})}if("auto"!==n[l]){const o=a(s.rightAppliedToAStatically);this.add(e,o,{declaration:`right: ${n[l]}`,nodeId:t})}if("auto"!==n[c]){const o=a(s.bottomAppliedToAStatically);this.add(e,o,{declaration:`bottom: ${n[c]}`,nodeId:t})}}}static checkForUnusedWidthAndHeightValues(e,t,n,o,i,r){if("inline"===n[o]){if("auto"!==n[i]){const o=a(s.widthAppliedToAnInlineElement);this.add(e,o,{declaration:`width: ${n[i]}`,nodeId:t})}if("auto"!==n[r]){const o=a(s.heightAppliedToAnInlineElement);this.add(e,o,{declaration:`height: ${n[r]}`,nodeId:t})}}}static checkForInvalidVerticalAlignment(e,t,n,o,i){if(n[o]&&"inline"!==n[o]&&!n[o].startsWith("table")&&"baseline"!==n[i]){const o=a(s.verticalAlignmentAppliedTo);this.add(e,o,{declaration:`vertical-align: ${n[i]}`,nodeId:t})}}}})),t.register("1rQgP",(function(n,o){debugger; e(n.exports,"CSSOverviewModel",(()=>c));var i=t("koSS8"),s=t("9X2mn"),r=t("eQFvP");t("XILzK");var a=t("anTZG"),l=t("iaAfh");class c extends r.SDKModel.SDKModel{#t;#n;#o;#i;constructor(e){super(e),this.#t=e.runtimeAgent(),this.#n=e.cssAgent(),this.#o=e.domsnapshotAgent(),this.#i=e.overlayAgent()}highlightNode(e){const t={contentColor:i.Color.PageHighlight.Content.toProtocolRGBA(),showInfo:!0,contrastAlgorithm:s.Runtime.experiments.isEnabled("APCA")?"apca":"aa"};this.#i.invoke_hideHighlight(),this.#i.invoke_highlightNode({backendNodeId:e,highlightConfig:t})}async getNodeStyleStats(){const e=new Map,t=new Map,n=new Map,o=new Map,r=new Map,c=new Map,d=new Map,h=e=>e.hasAlpha()?e.asString(i.Color.Format.HEXA):e.asString(i.Color.Format.HEX),p=(e,t,n)=>{if(-1===e)return;const o=C[e];if(!o)return;const s=i.Color.Color.parse(o);if(!s||0===s.rgba()[3])return;const r=h(s);if(!r)return;const a=n.get(r)||new Set;return a.add(t),n.set(r,a),s},u=e=>new Set(["altglyph","circle","ellipse","path","polygon","polyline","rect","svg","text","textpath","tref","tspan"]).has(e.toLowerCase()),g=e=>new Set(["iframe","video","embed","img"]).has(e.toLowerCase()),m=(e,t)=>new Set(["tr","td","thead","tbody"]).has(e.toLowerCase())&&t.startsWith("table");let v=0;const{documents:b,strings:C}=await this.#o.invoke_captureSnapshot({computedStyles:["background-color","color","fill","border-top-width","border-top-color","border-bottom-width","border-bottom-color","border-left-width","border-left-color","border-right-width","border-right-color","font-family","font-size","font-weight","line-height","position","top","right","bottom","left","display","width","height","vertical-align"],includeTextColorOpacities:!0,includeBlendedBackgroundColors:!0});for(const{nodes:f,layout:w}of b){v+=w.nodeIndex.length;for(let v=0;v<w.styles.length;v++){const b=w.styles[v],x=w.nodeIndex[v];if(!f.backendNodeId||!f.nodeName)continue;const S=f.backendNodeId[x],k=f.nodeName[x],[y,A,I,E,F,P,T,B,L,R,D,U,H,M,$,V,O,N,z,W,G,q,X,j]=b;p(y,S,e);const _=p(A,S,t);if(u(C[k])&&p(I,S,o),"0px"!==C[E]&&p(F,S,r),"0px"!==C[P]&&p(T,S,r),"0px"!==C[B]&&p(L,S,r),"0px"!==C[R]&&p(D,S,r),U&&-1!==U){const e=C[U],t=c.get(e)||new Map,n="font-size",o="font-weight",i="line-height",s=t.get(n)||new Map,r=t.get(o)||new Map,a=t.get(i)||new Map;if(-1!==H){const e=C[H],t=s.get(e)||[];t.push(S),s.set(e,t)}if(-1!==M){const e=C[M],t=r.get(e)||[];t.push(S),r.set(e,t)}if(-1!==$){const e=C[$],t=a.get(e)||[];t.push(S),a.set(e,t)}t.set(n,s),t.set(o,r),t.set(i,a),c.set(e,t)}const Q=_&&w.blendedBackgroundColors&&-1!==w.blendedBackgroundColors[v]?i.Color.Color.parse(C[w.blendedBackgroundColors[v]]):null;if(_&&Q){const e=new a.ContrastInfo({backgroundColors:[Q.asString(i.Color.Format.HEXA)],computedFontSize:-1!==H?C[H]:"",computedFontWeight:-1!==M?C[M]:""}),t=_.blendWithAlpha(w.textColorOpacities?w.textColorOpacities[v]:1);e.setColor(t);const o=`${h(t)}_${h(Q)}`;if(s.Runtime.experiments.isEnabled("APCA")){const i=e.contrastRatioAPCA(),s=e.contrastRatioAPCAThreshold();if(!(!(!i||!s)&&Math.abs(i)>=s)){const e={nodeId:S,contrastRatio:i,textColor:t,backgroundColor:Q,thresholdsViolated:{aa:!1,aaa:!1,apca:!0}};n.has(o)?n.get(o).push(e):n.set(o,[e])}}else{const i=e.contrastRatioThreshold("aa")||0,s=e.contrastRatioThreshold("aaa")||0,r=e.contrastRatio()||0;if(i>r||s>r){const e={nodeId:S,contrastRatio:r,textColor:t,backgroundColor:Q,thresholdsViolated:{aa:i>r,aaa:s>r,apca:!1}};n.has(o)?n.get(o).push(e):n.set(o,[e])}}}l.CSSOverviewUnusedDeclarations.checkForUnusedPositionValues(d,S,C,V,O,W,N,z),u(C[k])||g(C[k])||l.CSSOverviewUnusedDeclarations.checkForUnusedWidthAndHeightValues(d,S,C,G,q,X),-1===j||m(C[k],C[G])||l.CSSOverviewUnusedDeclarations.checkForInvalidVerticalAlignment(d,S,C,G,j)}}return{backgroundColors:e,textColors:t,textColorContrastIssues:n,fillColors:o,borderColors:r,fontInfo:c,unusedDeclarations:d,elementCount:v}}getComputedStyleForNode(e){return this.#n.invoke_getComputedStyleForNode({nodeId:e})}async getMediaQueries(){const e=await this.#n.invoke_getMediaQueries(),t=new Map;if(!e)return t;for(const n of e.medias){if("linkedSheet"===n.source)continue;const e=t.get(n.text)||[];e.push(n),t.set(n.text,e)}return t}async getGlobalStylesheetStats(){const{result:e}=await this.#t.invoke_evaluate({expression:"(function() {\n      let styleRules = 0;\n      let inlineStyles = 0;\n      let externalSheets = 0;\n      const stats = {\n        // Simple.\n        type: new Set(),\n        class: new Set(),\n        id: new Set(),\n        universal: new Set(),\n        attribute: new Set(),\n\n        // Non-simple.\n        nonSimple: new Set()\n      };\n\n      for (const styleSheet of document.styleSheets) {\n        if (styleSheet.href) {\n          externalSheets++;\n        } else {\n          inlineStyles++;\n        }\n\n        // Attempting to grab rules can trigger a DOMException.\n        // Try it and if it fails skip to the next stylesheet.\n        let rules;\n        try {\n          rules = styleSheet.rules;\n        } catch (err) {\n          continue;\n        }\n\n        for (const rule of rules) {\n          if ('selectorText' in rule) {\n            styleRules++;\n\n            // Each group that was used.\n            for (const selectorGroup of rule.selectorText.split(',')) {\n              // Each selector in the group.\n              for (const selector of selectorGroup.split(/[\\t\\n\\f\\r ]+/g)) {\n                if (selector.startsWith('.')) {\n                  // Class.\n                  stats.class.add(selector);\n                } else if (selector.startsWith('#')) {\n                  // Id.\n                  stats.id.add(selector);\n                } else if (selector.startsWith('*')) {\n                  // Universal.\n                  stats.universal.add(selector);\n                } else if (selector.startsWith('[')) {\n                  // Attribute.\n                  stats.attribute.add(selector);\n                } else {\n                  // Type or non-simple selector.\n                  const specialChars = /[#.:\\[\\]|\\+>~]/;\n                  if (specialChars.test(selector)) {\n                    stats.nonSimple.add(selector);\n                  } else {\n                    stats.type.add(selector);\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n\n      return {\n        styleRules,\n        inlineStyles,\n        externalSheets,\n        stats: {\n          // Simple.\n          type: stats.type.size,\n          class: stats.class.size,\n          id: stats.id.size,\n          universal: stats.universal.size,\n          attribute: stats.attribute.size,\n\n          // Non-simple.\n          nonSimple: stats.nonSimple.size\n        }\n      }\n    })()",returnByValue:!0});if("object"===e.type)return e.value}}r.SDKModel.SDKModel.register(c,{capabilities:r.Target.Capability.DOM,autostart:!1})})),t.register("XILzK",(function(n,o){e(n.exports,"ContrastInfo",(()=>t("anTZG"))),e(n.exports,"Spectrum",(()=>t("2LRYL")));t("7HPGy"),t("anTZG"),t("j5uRV"),t("2LRYL")})),t.register("7HPGy",(function(n,o){e(n.exports,"ContrastDetails",(()=>u));var i=t("koSS8"),s=t("e7bLS"),r=t("ixFnt"),a=t("lz7WY"),l=t("9X2mn"),c=t("9z2ZV");const d={noContrastInformationAvailable:"No contrast information available",contrastRatio:"Contrast ratio",showMore:"Show more",pickBackgroundColor:"Pick background color",toggleBackgroundColorPicker:"Toggle background color picker",useSuggestedColorStoFixLow:"Use suggested color {PH1}to fix low contrast",apca:"APCA",aa:"AA",placeholderWithColon:": {PH1}",aaa:"AAA",showLess:"Show less"},h=r.i18n.registerUIStrings("ui/legacy/components/color_picker/ContrastDetails.ts",d),p=r.i18n.getLocalizedString.bind(void 0,h);class u extends i.ObjectWrapper.ObjectWrapper{contrastInfo;elementInternal;toggleMainColorPicker;expandedChangedCallback;colorSelectedCallback;expandedInternal;passesAA;contrastUnknown;visibleInternal;noContrastInfoAvailable;contrastValueBubble;contrastValue;contrastValueBubbleIcons;expandButton;expandedDetails;contrastThresholds;contrastAA;contrastPassFailAA;contrastAAA;contrastPassFailAAA;contrastAPCA;contrastPassFailAPCA;chooseBgColor;bgColorPickerButton;bgColorPickedBound;bgColorSwatch;constructor(e,t,n,o,i){super(),this.contrastInfo=e,this.elementInternal=t.createChild("div","spectrum-contrast-details collapsed"),this.toggleMainColorPicker=n,this.expandedChangedCallback=o,this.colorSelectedCallback=i,this.expandedInternal=!1,this.passesAA=!0,this.contrastUnknown=!1,this.visibleInternal=!1,this.noContrastInfoAvailable=t.createChild("div","no-contrast-info-available"),this.noContrastInfoAvailable.textContent=p(d.noContrastInformationAvailable),this.noContrastInfoAvailable.classList.add("hidden");const s=this.elementInternal.createChild("div");s.addEventListener("click",this.topRowClicked.bind(this));const r=s.createChild("div","container");c.UIUtils.createTextChild(r,p(d.contrastRatio)),this.contrastValueBubble=r.createChild("span","contrast-details-value"),this.contrastValue=this.contrastValueBubble.createChild("span"),this.contrastValueBubbleIcons=[],this.contrastValueBubbleIcons.push(this.contrastValueBubble.appendChild(c.Icon.Icon.create("smallicon-checkmark-square"))),this.contrastValueBubbleIcons.push(this.contrastValueBubble.appendChild(c.Icon.Icon.create("smallicon-checkmark-behind"))),this.contrastValueBubbleIcons.push(this.contrastValueBubble.appendChild(c.Icon.Icon.create("smallicon-no"))),this.contrastValueBubbleIcons.forEach((e=>e.addEventListener("click",(e=>{u.showHelp(),e.consume(!1)}))));const a=new c.Toolbar.Toolbar("expand",r);this.expandButton=new c.Toolbar.ToolbarButton(p(d.showMore),"smallicon-expand-more"),this.expandButton.addEventListener(c.Toolbar.ToolbarButton.Events.Click,this.expandButtonClicked.bind(this)),c.ARIAUtils.setExpanded(this.expandButton.element,!1),a.appendToolbarItem(this.expandButton),this.expandedDetails=this.elementInternal.createChild("div","expanded-details"),c.ARIAUtils.setControls(this.expandButton.element,this.expandedDetails),this.contrastThresholds=this.expandedDetails.createChild("div","contrast-thresholds"),this.contrastAA=this.contrastThresholds.createChild("div","contrast-threshold"),this.contrastPassFailAA=this.contrastAA.createChild("div","contrast-pass-fail"),this.contrastAAA=this.contrastThresholds.createChild("div","contrast-threshold"),this.contrastPassFailAAA=this.contrastAAA.createChild("div","contrast-pass-fail"),this.contrastAPCA=this.contrastThresholds.createChild("div","contrast-threshold"),this.contrastPassFailAPCA=this.contrastAPCA.createChild("div","contrast-pass-fail"),this.chooseBgColor=this.expandedDetails.createChild("div","contrast-choose-bg-color"),this.chooseBgColor.textContent=p(d.pickBackgroundColor);const l=this.expandedDetails.createChild("div","background-color"),h=new c.Toolbar.Toolbar("spectrum-eye-dropper",l);this.bgColorPickerButton=new c.Toolbar.ToolbarToggle(p(d.toggleBackgroundColorPicker),"largeicon-eyedropper"),this.bgColorPickerButton.addEventListener(c.Toolbar.ToolbarButton.Events.Click,this.toggleBackgroundColorPickerInternal.bind(this,void 0,!0)),h.appendToolbarItem(this.bgColorPickerButton),this.bgColorPickedBound=this.bgColorPicked.bind(this),this.bgColorSwatch=new g(l),this.contrastInfo.addEventListener("ContrastInfoUpdated",this.update.bind(this))}showNoContrastInfoAvailableMessage(){this.noContrastInfoAvailable.classList.remove("hidden")}hideNoContrastInfoAvailableMessage(){this.noContrastInfoAvailable.classList.add("hidden")}computeSuggestedColor(e){const t=this.contrastInfo.color(),n=this.contrastInfo.bgColor();if(!t||!n)return;if("APCA"===e){const e=this.contrastInfo.contrastRatioAPCAThreshold();if(null===e)return;return i.Color.Color.findFgColorForContrastAPCA(t,n,e+1)}const o=this.contrastInfo.contrastRatioThreshold(e);return o?i.Color.Color.findFgColorForContrast(t,n,o+.1):void 0}onSuggestColor(e){const t=this.computeSuggestedColor(e);t&&this.colorSelectedCallback(t)}createFixColorButton(e,t){const n=e.createChild("button","contrast-fix-button"),o=this.contrastInfo.colorFormat(),s=o&&o!==i.Color.Format.Nickname&&o!==i.Color.Format.Original?o:i.Color.Format.HEXA,r=t.asString(s),a=r?r+" ":"",l=p(d.useSuggestedColorStoFixLow,{PH1:a});return c.ARIAUtils.setAccessibleName(n,l),c.Tooltip.Tooltip.install(n,l),n.tabIndex=0,n.style.backgroundColor=a,n}update(){if(this.contrastInfo.isNull())return this.showNoContrastInfoAvailableMessage(),void this.setVisible(!1);this.setVisible(!0),this.hideNoContrastInfoAvailableMessage();const e=l.Runtime.experiments.isEnabled("APCA"),t=this.contrastInfo.color(),n=this.contrastInfo.bgColor();if(e){const e=this.contrastInfo.contrastRatioAPCA();if(null===e||!n||!t)return this.contrastUnknown=!0,this.contrastValue.textContent="",this.contrastValueBubble.classList.add("contrast-unknown"),this.chooseBgColor.classList.remove("hidden"),this.contrastThresholds.classList.add("hidden"),void this.showNoContrastInfoAvailableMessage();this.contrastUnknown=!1,this.chooseBgColor.classList.add("hidden"),this.contrastThresholds.classList.remove("hidden"),this.contrastValueBubble.classList.remove("contrast-unknown"),this.contrastValue.textContent=`${a.NumberUtilities.floor(e,2)}%`;const o=this.contrastInfo.contrastRatioAPCAThreshold(),i=!(!e||!o)&&Math.abs(e)>=o;this.contrastPassFailAPCA.removeChildren();const s=this.contrastPassFailAPCA.createChild("span","contrast-link-label");if(s.textContent=p(d.apca),null!==o&&(this.contrastPassFailAPCA.createChild("span").textContent=`: ${o.toFixed(2)}%`),i)this.contrastPassFailAPCA.appendChild(c.Icon.Icon.create("smallicon-checkmark-square"));else{this.contrastPassFailAPCA.appendChild(c.Icon.Icon.create("smallicon-no"));const e=this.computeSuggestedColor("APCA");if(e){this.createFixColorButton(this.contrastPassFailAPCA,e).addEventListener("click",(()=>this.onSuggestColor("APCA")))}}return s.addEventListener("click",(e=>u.showHelp())),this.elementInternal.classList.toggle("contrast-fail",!i),void this.contrastValueBubble.classList.toggle("contrast-aa",i)}const o=this.contrastInfo.contrastRatio();if(!o||!n||!t)return this.contrastUnknown=!0,this.contrastValue.textContent="",this.contrastValueBubble.classList.add("contrast-unknown"),this.chooseBgColor.classList.remove("hidden"),this.contrastThresholds.classList.add("hidden"),void this.showNoContrastInfoAvailableMessage();this.contrastUnknown=!1,this.chooseBgColor.classList.add("hidden"),this.contrastThresholds.classList.remove("hidden"),this.contrastValueBubble.classList.remove("contrast-unknown"),this.contrastValue.textContent=String(a.NumberUtilities.floor(o,2)),this.bgColorSwatch.setColors(t,n);const i=this.contrastInfo.contrastRatioThreshold("aa")||0;this.passesAA=(this.contrastInfo.contrastRatio()||0)>=i,this.contrastPassFailAA.removeChildren();const s=this.contrastPassFailAA.createChild("span","contrast-link-label");if(s.textContent=p(d.aa),this.contrastPassFailAA.createChild("span").textContent=p(d.placeholderWithColon,{PH1:i.toFixed(1)}),this.passesAA)this.contrastPassFailAA.appendChild(c.Icon.Icon.create("smallicon-checkmark-square"));else{this.contrastPassFailAA.appendChild(c.Icon.Icon.create("smallicon-no"));const e=this.computeSuggestedColor("aa");if(e){this.createFixColorButton(this.contrastPassFailAA,e).addEventListener("click",(()=>this.onSuggestColor("aa")))}}const r=this.contrastInfo.contrastRatioThreshold("aaa")||0,h=(this.contrastInfo.contrastRatio()||0)>=r;this.contrastPassFailAAA.removeChildren();const g=this.contrastPassFailAAA.createChild("span","contrast-link-label");if(g.textContent=p(d.aaa),this.contrastPassFailAAA.createChild("span").textContent=p(d.placeholderWithColon,{PH1:r.toFixed(1)}),h)this.contrastPassFailAAA.appendChild(c.Icon.Icon.create("smallicon-checkmark-square"));else{this.contrastPassFailAAA.appendChild(c.Icon.Icon.create("smallicon-no"));const e=this.computeSuggestedColor("aaa");if(e){this.createFixColorButton(this.contrastPassFailAAA,e).addEventListener("click",(()=>this.onSuggestColor("aaa")))}}[s,g].forEach((e=>e.addEventListener("click",(()=>u.showHelp())))),this.elementInternal.classList.toggle("contrast-fail",!this.passesAA),this.contrastValueBubble.classList.toggle("contrast-aa",this.passesAA),this.contrastValueBubble.classList.toggle("contrast-aaa",h)}static showHelp(){s.InspectorFrontendHost.InspectorFrontendHostInstance.openInNewTab(c.UIUtils.addReferrerToURL("https://web.dev/color-and-contrast-accessibility/"))}setVisible(e){this.visibleInternal=e,this.elementInternal.classList.toggle("hidden",!e)}visible(){return this.visibleInternal}element(){return this.elementInternal}expandButtonClicked(){const e=this.contrastValueBubble.getComponentSelection();e&&e.empty(),this.toggleExpanded()}topRowClicked(e){const t=this.contrastValueBubble.getComponentSelection();t&&t.empty(),this.toggleExpanded(),e.consume(!0)}toggleExpanded(){this.expandedInternal=!this.expandedInternal,c.ARIAUtils.setExpanded(this.expandButton.element,this.expandedInternal),this.elementInternal.classList.toggle("collapsed",!this.expandedInternal),this.expandedInternal?(this.toggleMainColorPicker(!1),this.expandButton.setGlyph("smallicon-expand-less"),this.expandButton.setTitle(p(d.showLess)),this.contrastUnknown&&this.toggleBackgroundColorPickerInternal(!0)):(this.toggleBackgroundColorPickerInternal(!1),this.expandButton.setGlyph("smallicon-expand-more"),this.expandButton.setTitle(p(d.showMore))),this.expandedChangedCallback()}collapse(){this.elementInternal.classList.remove("expanded"),this.toggleBackgroundColorPickerInternal(!1),this.toggleMainColorPicker(!1)}expanded(){return this.expandedInternal}backgroundColorPickerEnabled(){return this.bgColorPickerButton.toggled()}toggleBackgroundColorPicker(e){this.toggleBackgroundColorPickerInternal(e,!1)}toggleBackgroundColorPickerInternal(e,t=!0){void 0===e&&(e=!this.bgColorPickerButton.toggled()),this.bgColorPickerButton.setToggled(e),t&&this.dispatchEventToListeners("BackgroundColorPickerWillBeToggled",e),s.InspectorFrontendHost.InspectorFrontendHostInstance.setEyeDropperActive(e),e?s.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(s.InspectorFrontendHostAPI.Events.EyeDropperPickedColor,this.bgColorPickedBound):s.InspectorFrontendHost.InspectorFrontendHostInstance.events.removeEventListener(s.InspectorFrontendHostAPI.Events.EyeDropperPickedColor,this.bgColorPickedBound)}bgColorPicked({data:e}){const t=[e.r,e.g,e.b,(e.a/2.55|0)/100],n=i.Color.Color.fromRGBA(t);this.contrastInfo.setBgColor(n),this.toggleBackgroundColorPickerInternal(!1),s.InspectorFrontendHost.InspectorFrontendHostInstance.bringToFront()}}class g{parentElement;swatchElement;swatchInnerElement;textPreview;constructor(e){this.parentElement=e,this.swatchElement=e.createChild("span","swatch contrast swatch-inner-white"),this.swatchInnerElement=this.swatchElement.createChild("span","swatch-inner"),this.textPreview=this.swatchElement.createChild("div","text-preview"),this.textPreview.textContent="Aa"}setColors(e,t){this.textPreview.style.color=e.asString(i.Color.Format.RGBA),this.swatchInnerElement.style.backgroundColor=t.asString(i.Color.Format.RGBA),this.swatchElement.classList.toggle("swatch-inner-white",t.hsla()[2]>.9)}}})),t.register("anTZG",(function(n,o){e(n.exports,"ContrastInfo",(()=>s));var i=t("koSS8");class s extends i.ObjectWrapper.ObjectWrapper{isNullInternal;contrastRatioInternal;contrastRatioAPCAInternal;contrastRatioThresholds;contrastRationAPCAThreshold;fgColor;bgColorInternal;colorFormatInternal;constructor(e){if(super(),this.isNullInternal=!0,this.contrastRatioInternal=null,this.contrastRatioAPCAInternal=null,this.contrastRatioThresholds=null,this.contrastRationAPCAThreshold=0,this.fgColor=null,this.bgColorInternal=null,!e)return;if(!e.computedFontSize||!e.computedFontWeight||!e.backgroundColors||1!==e.backgroundColors.length)return;this.isNullInternal=!1,this.contrastRatioThresholds=i.ColorUtils.getContrastThreshold(e.computedFontSize,e.computedFontWeight),this.contrastRationAPCAThreshold=i.ColorUtils.getAPCAThreshold(e.computedFontSize,e.computedFontWeight);const t=e.backgroundColors[0],n=i.Color.Color.parse(t);n&&this.setBgColorInternal(n)}isNull(){return this.isNullInternal}setColor(e,t){this.fgColor=e,this.colorFormatInternal=t,this.updateContrastRatio(),this.dispatchEventToListeners("ContrastInfoUpdated")}colorFormat(){return this.colorFormatInternal}color(){return this.fgColor}contrastRatio(){return this.contrastRatioInternal}contrastRatioAPCA(){return this.contrastRatioAPCAInternal}contrastRatioAPCAThreshold(){return this.contrastRationAPCAThreshold}setBgColor(e){this.setBgColorInternal(e),this.dispatchEventToListeners("ContrastInfoUpdated")}setBgColorInternal(e){if(this.bgColorInternal=e,!this.fgColor)return;const t=this.fgColor.rgba();if(e.hasAlpha()){const n=i.ColorUtils.blendColors(e.rgba(),t);this.bgColorInternal=new i.Color.Color(n,i.Color.Format.RGBA)}this.contrastRatioInternal=i.ColorUtils.contrastRatio(t,this.bgColorInternal.rgba()),this.contrastRatioAPCAInternal=i.ColorUtils.contrastRatioAPCA(this.fgColor.rgba(),this.bgColorInternal.rgba())}bgColor(){return this.bgColorInternal}updateContrastRatio(){this.bgColorInternal&&this.fgColor&&(this.contrastRatioInternal=i.ColorUtils.contrastRatio(this.fgColor.rgba(),this.bgColorInternal.rgba()),this.contrastRatioAPCAInternal=i.ColorUtils.contrastRatioAPCA(this.fgColor.rgba(),this.bgColorInternal.rgba()))}contrastRatioThreshold(e){return this.contrastRatioThresholds?this.contrastRatioThresholds[e]:null}}})),t.register("j5uRV",(function(n,o){e(n.exports,"ContrastOverlay",(()=>a));var i=t("koSS8"),s=t("9X2mn"),r=t("9z2ZV");class a{contrastInfo;visible;contrastRatioSVG;contrastRatioLines;width;height;contrastRatioLineBuilder;contrastRatioLinesThrottler;drawContrastRatioLinesBound;constructor(e,t){this.contrastInfo=e,this.visible=!1,this.contrastRatioSVG=r.UIUtils.createSVGChild(t,"svg","spectrum-contrast-container fill"),this.contrastRatioLines=new Map,s.Runtime.experiments.isEnabled("APCA")?this.contrastRatioLines.set("APCA",r.UIUtils.createSVGChild(this.contrastRatioSVG,"path","spectrum-contrast-line")):(this.contrastRatioLines.set("aa",r.UIUtils.createSVGChild(this.contrastRatioSVG,"path","spectrum-contrast-line")),this.contrastRatioLines.set("aaa",r.UIUtils.createSVGChild(this.contrastRatioSVG,"path","spectrum-contrast-line"))),this.width=0,this.height=0,this.contrastRatioLineBuilder=new l(this.contrastInfo),this.contrastRatioLinesThrottler=new i.Throttler.Throttler(0),this.drawContrastRatioLinesBound=this.drawContrastRatioLines.bind(this),this.contrastInfo.addEventListener("ContrastInfoUpdated",this.update.bind(this))}update(){this.visible&&!this.contrastInfo.isNull()&&(s.Runtime.experiments.isEnabled("APCA")&&null===this.contrastInfo.contrastRatioAPCA()||this.contrastInfo.contrastRatio()&&this.contrastRatioLinesThrottler.schedule(this.drawContrastRatioLinesBound))}setDimensions(e,t){this.width=e,this.height=t,this.update()}setVisible(e){this.visible=e,this.contrastRatioSVG.classList.toggle("hidden",!e),this.update()}async drawContrastRatioLines(){for(const[e,t]of this.contrastRatioLines){const n=this.contrastRatioLineBuilder.drawContrastRatioLine(this.width,this.height,e);n?t.setAttribute("d",n):t.removeAttribute("d")}}}class l{contrastInfo;constructor(e){this.contrastInfo=e}drawContrastRatioLine(e,t,n){const o=s.Runtime.experiments.isEnabled("APCA"),r=o?this.contrastInfo.contrastRatioAPCAThreshold():this.contrastInfo.contrastRatioThreshold(n);if(!e||!t||null===r)return null;const a=.02,l=this.contrastInfo.color(),c=this.contrastInfo.bgColor();if(!l||!c)return null;const d=l.rgba(),h=l.hsva(),p=c.rgba(),u=i.ColorUtils.luminance(p);let g=i.ColorUtils.blendColors(d,p);const m=i.ColorUtils.luminance(g)>u,v=o?i.ColorUtils.desiredLuminanceAPCA(u,r,m):i.Color.Color.desiredLuminance(u,r,m);if(o&&Math.abs(Math.round(i.ColorUtils.contrastRatioByLuminanceAPCA(v,u)))<r)return null;let b=h[2],C=0;const f=[h[0],0,0,h[3]];let w=[];const x=[];i.Color.Color.hsva2rgba(f,x),g=i.ColorUtils.blendColors(x,p);let S,k=e=>i.ColorUtils.luminance(i.ColorUtils.blendColors(i.Color.Color.fromHSVA(e).rgba(),p));for(s.Runtime.experiments.isEnabled("APCA")&&(k=e=>i.ColorUtils.luminanceAPCA(i.ColorUtils.blendColors(i.Color.Color.fromHSVA(e).rgba(),p))),S=0;S<1.02;S+=a){S=Math.min(1,S),f[1]=S,f[2]=b+C*a;const n=i.Color.Color.approachColorValue(f,p,2,v,k);if(null===n)break;C=0===S?0:(n-b)/a,b=n,w.push(w.length?"L":"M"),w.push((S*e).toFixed(2)),w.push(((1-n)*t).toFixed(2))}return S<1.02&&(S-=a,f[2]=1,S=i.Color.Color.approachColorValue(f,p,1,v,k),null!==S&&(w=w.concat(["L",(S*e).toFixed(2),"-0.1"]))),0===w.length?null:w.join(" ")}}})),t.register("2LRYL",(function(n,o){e(n.exports,"Spectrum",(()=>f)),e(n.exports,"Swatch",(()=>P)),e(n.exports,"PaletteGenerator",(()=>I)),e(n.exports,"MaterialPalette",(()=>F)),e(n.exports,"ChangeSource",(()=>w)),e(n.exports,"Events",(()=>x));var i=t("koSS8"),s=t("e7bLS"),r=t("ixFnt"),a=t("lz7WY"),l=t("eQFvP"),c=t("cY3yZ"),d=t("9z2ZV"),h=t("7HPGy"),p=t("j5uRV"),u=t("dFG0m");const g={toggleColorPicker:"Toggle color picker",changeHue:"Change hue",changeAlpha:"Change alpha",hex:"HEX",changeColorFormat:"Change color format",previewPalettes:"Preview palettes",addToPalette:"Add to palette",colorPalettes:"Color Palettes",returnToColorPicker:"Return to color picker",colorS:"Color {PH1}",longclickOrLongpressSpaceToShow:"Long-click or long-press space to show alternate shades of {PH1}",removeColor:"Remove color",removeAllToTheRight:"Remove all to the right",clearPalette:"Clear palette",sInS:"{PH1} in {PH2}",copyColorToClipboard:"Copy color to clipboard",pressArrowKeysMessage:"Press arrow keys with or without modifiers to move swatch position. Arrow key with Shift key moves position largely, with Ctrl key it is less and with Alt key it is even less"},m=r.i18n.registerUIStrings("ui/legacy/components/color_picker/Spectrum.ts",g),v=r.i18n.getLocalizedString.bind(void 0,m),b=new WeakMap,C=new WeakMap;class f extends(i.ObjectWrapper.eventMixin(d.Widget.VBox)){colorElement;colorDragElement;dragX;dragY;colorPickerButton;swatch;hueElement;hueSlider;alphaElement;alphaElementBackground;alphaSlider;displayContainer;textValues;textLabels;hexContainer;hexValue;contrastInfo;contrastOverlay;contrastDetails;contrastDetailsBackgroundColorPickedToggledBound;palettes;palettePanel;palettePanelShowing;paletteSectionContainer;paletteContainer;shadesContainer;deleteIconToolbar;deleteButton;addColorToolbar;colorPickedBound;hsv;hueAlphaWidth;dragWidth;dragHeight;colorDragElementHeight;slideHelperWidth;numPaletteRowsShown;selectedColorPalette;customPaletteSetting;colorOffset;closeButton;paletteContainerMutable;shadesCloseHandler;dragElement;dragHotSpotX;dragHotSpotY;originalFormat;colorNameInternal;colorStringInternal;colorFormat;constructor(e){super(!0),this.contentElement.tabIndex=0,this.colorElement=this.contentElement.createChild("div","spectrum-color"),this.colorElement.tabIndex=0,this.setDefaultFocusedElement(this.colorElement),this.colorElement.addEventListener("keydown",this.onSliderKeydown.bind(this,C.bind(this)));const t=v(g.pressArrowKeysMessage);d.ARIAUtils.setAccessibleName(this.colorElement,t),d.ARIAUtils.markAsApplication(this.colorElement),this.colorDragElement=this.colorElement.createChild("div","spectrum-sat fill").createChild("div","spectrum-val fill").createChild("div","spectrum-dragger"),this.dragX=0,this.dragY=0;const n=this.contentElement.createChild("div","spectrum-tools"),o=new d.Toolbar.Toolbar("spectrum-eye-dropper",n);this.colorPickerButton=new d.Toolbar.ToolbarToggle(v(g.toggleColorPicker),"largeicon-eyedropper"),this.colorPickerButton.setToggled(!0),this.colorPickerButton.addEventListener(d.Toolbar.ToolbarButton.Events.Click,this.toggleColorPicker.bind(this,void 0)),o.appendToolbarItem(this.colorPickerButton),this.swatch=new P(n),this.hueElement=n.createChild("div","spectrum-hue"),this.hueElement.tabIndex=0,this.hueElement.addEventListener("keydown",this.onSliderKeydown.bind(this,m.bind(this))),d.ARIAUtils.setAccessibleName(this.hueElement,v(g.changeHue)),d.ARIAUtils.markAsSlider(this.hueElement,0,360),this.hueSlider=this.hueElement.createChild("div","spectrum-slider"),this.alphaElement=n.createChild("div","spectrum-alpha"),this.alphaElement.tabIndex=0,this.alphaElement.addEventListener("keydown",this.onSliderKeydown.bind(this,b.bind(this))),d.ARIAUtils.setAccessibleName(this.alphaElement,v(g.changeAlpha)),d.ARIAUtils.markAsSlider(this.alphaElement,0,1),this.alphaElementBackground=this.alphaElement.createChild("div","spectrum-alpha-background"),this.alphaSlider=this.alphaElement.createChild("div","spectrum-slider"),this.displayContainer=n.createChild("div","spectrum-text source-code"),d.ARIAUtils.markAsPoliteLiveRegion(this.displayContainer,!0),this.textValues=[];for(let e=0;e<4;++e){const e=d.UIUtils.createInput("spectrum-text-value");this.displayContainer.appendChild(e),e.maxLength=4,this.textValues.push(e),e.addEventListener("keydown",this.inputChanged.bind(this),!1),e.addEventListener("input",this.inputChanged.bind(this),!1),e.addEventListener("wheel",this.inputChanged.bind(this),!1),e.addEventListener("paste",this.pasted.bind(this),!1)}this.textLabels=this.displayContainer.createChild("div","spectrum-text-label"),this.hexContainer=n.createChild("div","spectrum-text spectrum-text-hex source-code"),d.ARIAUtils.markAsPoliteLiveRegion(this.hexContainer,!0),this.hexValue=d.UIUtils.createInput("spectrum-text-value"),this.hexContainer.appendChild(this.hexValue),this.hexValue.maxLength=9,this.hexValue.addEventListener("keydown",this.inputChanged.bind(this),!1),this.hexValue.addEventListener("input",this.inputChanged.bind(this),!1),this.hexValue.addEventListener("wheel",this.inputChanged.bind(this),!1),this.hexValue.addEventListener("paste",this.pasted.bind(this),!1);const i=this.hexContainer.createChild("div","spectrum-text-label");i.textContent=v(g.hex),d.ARIAUtils.setAccessibleName(this.hexValue,i.textContent);const s=n.createChild("div","spectrum-display-switcher spectrum-switcher");f(s),d.UIUtils.setTitle(s,v(g.changeColorFormat)),s.tabIndex=0,self.onInvokeElement(s,(e=>{this.formatViewSwitch(),e.consume(!0)})),d.ARIAUtils.markAsButton(s),d.UIUtils.installDragHandle(this.hueElement,this.dragStart.bind(this,m.bind(this)),m.bind(this),null,"ew-resize","crosshair"),d.UIUtils.installDragHandle(this.alphaElement,this.dragStart.bind(this,b.bind(this)),b.bind(this),null,"ew-resize","crosshair"),d.UIUtils.installDragHandle(this.colorElement,this.dragStart.bind(this,C.bind(this)),C.bind(this),null,"move","crosshair"),e&&(this.contrastInfo=e,this.contrastOverlay=new(0,p.ContrastOverlay)(this.contrastInfo,this.colorElement),this.contrastDetails=new(0,h.ContrastDetails)(this.contrastInfo,this.contentElement,this.toggleColorPicker.bind(this),this.contrastPanelExpanded.bind(this),this.colorSelected.bind(this)),this.contrastDetailsBackgroundColorPickedToggledBound=this.contrastDetailsBackgroundColorPickedToggled.bind(this)),this.element.classList.add("flex-none"),this.palettes=new Map,this.palettePanel=this.contentElement.createChild("div","palette-panel"),this.palettePanelShowing=!1,this.paletteSectionContainer=this.contentElement.createChild("div","spectrum-palette-container"),this.paletteContainer=this.paletteSectionContainer.createChild("div","spectrum-palette"),this.paletteContainer.addEventListener("contextmenu",this.showPaletteColorContextMenu.bind(this,-1)),this.shadesContainer=this.contentElement.createChild("div","palette-color-shades hidden"),d.UIUtils.installDragHandle(this.paletteContainer,this.paletteDragStart.bind(this),this.paletteDrag.bind(this),this.paletteDragEnd.bind(this),"default");const r=this.paletteSectionContainer.createChild("div","spectrum-palette-switcher spectrum-switcher");f(r),d.UIUtils.setTitle(r,v(g.previewPalettes)),d.ARIAUtils.markAsButton(r),r.tabIndex=0,self.onInvokeElement(r,(e=>{this.togglePalettePanel(!0),e.consume(!0)})),this.deleteIconToolbar=new d.Toolbar.Toolbar("delete-color-toolbar"),this.deleteButton=new d.Toolbar.ToolbarButton("","largeicon-trash-bin"),this.deleteIconToolbar.appendToolbarItem(this.deleteButton);this.contentElement.createChild("div","spectrum-overlay fill").addEventListener("click",this.togglePalettePanel.bind(this,!1)),this.addColorToolbar=new d.Toolbar.Toolbar("add-color-toolbar");const l=new d.Toolbar.ToolbarButton(v(g.addToPalette),"largeicon-add");function u(e,t){const n=t,o=e.getBoundingClientRect();switch(n.key){case"ArrowLeft":case"ArrowDown":return o.left-1;case"ArrowRight":case"ArrowUp":return o.right+1;default:return t.x}}function m(e){const t=this.hsv.slice(),n=1-(u(this.hueSlider,e)-this.hueElement.getBoundingClientRect().left)/this.hueAlphaWidth;t[0]=a.NumberUtilities.clamp(n,0,1),this.innerSetColor(t,"",void 0,void 0,w.Other);const o=this.color().canonicalHSLA();d.ARIAUtils.setValueNow(this.hueElement,o[0])}function b(e){const t=this.hsv.slice(),n=(u(this.alphaSlider,e)-this.hueElement.getBoundingClientRect().left)/this.hueAlphaWidth,o=Math.round(100*n)/100;t[3]=a.NumberUtilities.clamp(o,0,1),this.innerSetColor(t,"",void 0,void 0,w.Other);const i=this.color().canonicalHSLA();d.ARIAUtils.setValueText(this.alphaElement,i[3])}function C(e){const t=this.hsv.slice(),n=function(e,t){const n=e.getBoundingClientRect(),o=n.x+n.width/2,i=n.y+n.width/2,s=function(e,t){const n=t;n.altKey?e=1:n.ctrlKey?e=10:n.shiftKey&&(e=20);return e}(n.width/4,t);switch(t.key){case"ArrowLeft":return{x:n.left-s,y:i};case"ArrowRight":return{x:n.right+s,y:i};case"ArrowDown":return{x:o,y:n.bottom+s};case"ArrowUp":return{x:o,y:n.top-s};default:return{x:t.x,y:t.y}}}(this.colorDragElement,e);this.colorOffset=this.colorElement.totalOffset(),t[1]=a.NumberUtilities.clamp((n.x-this.colorOffset.left)/this.dragWidth,0,1),t[2]=a.NumberUtilities.clamp(1-(n.y-this.colorOffset.top)/this.dragHeight,0,1),this.innerSetColor(t,"",void 0,void 0,w.Other)}function f(e){const t=new c.Icon.Icon;t.data={iconName:"switcherIcon",color:"var(--color-text-primary)",width:"16px",height:"16px"},e.appendChild(t)}l.addEventListener(d.Toolbar.ToolbarButton.Events.Click,this.onAddColorMousedown.bind(this)),l.element.addEventListener("keydown",this.onAddColorKeydown.bind(this)),this.addColorToolbar.appendToolbarItem(l),this.colorPickedBound=this.colorPicked.bind(this),this.numPaletteRowsShown=-1,this.loadPalettes(),new I((e=>{e.colors.length?this.addPalette(e):this.selectedColorPalette.get()===e.title&&this.paletteSelected(F)}))}dragStart(e,t){return this.colorOffset=this.colorElement.totalOffset(),e(t),!0}contrastDetailsBackgroundColorPickedToggled(e){e.data&&this.toggleColorPicker(!1)}contrastPanelExpanded(){this.contrastOverlay&&this.contrastDetails&&(this.contrastOverlay.setVisible(this.contrastDetails.expanded()),this.resizeForSelectedPalette(!0))}updatePalettePanel(){this.palettePanel.removeChildren();this.palettePanel.createChild("div","palette-title").textContent=v(g.colorPalettes);const e=new d.Toolbar.Toolbar("",this.palettePanel);this.closeButton=new d.Toolbar.ToolbarButton(v(g.returnToColorPicker),"largeicon-delete"),this.closeButton.addEventListener(d.Toolbar.ToolbarButton.Events.Click,this.togglePalettePanel.bind(this,!1)),this.closeButton.element.addEventListener("keydown",this.onCloseBtnKeydown.bind(this)),e.appendToolbarItem(this.closeButton);for(const e of this.palettes.values())this.palettePanel.appendChild(this.createPreviewPaletteElement(e))}togglePalettePanel(e){this.palettePanelShowing!==e&&(e&&this.updatePalettePanel(),this.palettePanelShowing=e,this.contentElement.classList.toggle("palette-panel-showing",e),this.focusInternal())}onCloseBtnKeydown(e){(isEscKey(e)||isEnterOrSpaceKey(e))&&(this.togglePalettePanel(!1),e.consume(!0))}onSliderKeydown(e,t){switch(t.key){case"ArrowLeft":case"ArrowRight":case"ArrowDown":case"ArrowUp":e(t),t.consume(!0)}}focusInternal(){this.isShowing()&&(this.palettePanelShowing&&this.closeButton?this.closeButton.element.focus({preventScroll:!0}):this.contentElement.focus())}createPaletteColor(e,t,n){const o=document.createElement("div");return o.classList.add("spectrum-palette-color"),o.style.background=a.StringUtilities.sprintf("linear-gradient(%s, %s), var(--image-file-checker)",e,e),n&&o.animate([{opacity:0},{opacity:1}],{duration:100,delay:n,fill:"backwards"}),d.Tooltip.Tooltip.install(o,t||e),o}showPalette(e,t,n){this.resizeForSelectedPalette(),this.paletteContainer.removeChildren();for(let n=0;n<e.colors.length;n++){const o=t?100*n/e.colors.length:0,i=this.createPaletteColor(e.colors[n],e.colorNames[n],o);if(d.ARIAUtils.markAsButton(i),d.ARIAUtils.setAccessibleName(i,v(g.colorS,{PH1:e.colors[n]})),i.tabIndex=-1,i.addEventListener("mousedown",this.paletteColorSelected.bind(this,e.colors[n],e.colorNames[n],Boolean(e.matchUserFormat))),i.addEventListener("focus",this.paletteColorSelected.bind(this,e.colors[n],e.colorNames[n],Boolean(e.matchUserFormat))),i.addEventListener("keydown",this.onPaletteColorKeydown.bind(this,n)),e.mutable)b.set(i,!0),C.set(i,e.colors[n]),i.addEventListener("contextmenu",this.showPaletteColorContextMenu.bind(this,n));else if(e===F){i.classList.add("has-material-shades");let t=i.createChild("div","spectrum-palette-color spectrum-palette-color-shadow");t.style.background=e.colors[n],t=i.createChild("div","spectrum-palette-color spectrum-palette-color-shadow"),t.style.background=e.colors[n];const o=v(g.longclickOrLongpressSpaceToShow,{PH1:e.colors[n]});d.Tooltip.Tooltip.install(i,o),d.ARIAUtils.setAccessibleName(i,o),new d.UIUtils.LongClickController(i,this.showLightnessShades.bind(this,i,e.colors[n]))}this.paletteContainer.appendChild(i)}this.paletteContainer.childNodes.length>0&&(this.paletteContainer.childNodes[0].tabIndex=0),this.paletteContainerMutable=e.mutable,e.mutable?(this.paletteContainer.appendChild(this.addColorToolbar.element),this.paletteContainer.appendChild(this.deleteIconToolbar.element)):(this.addColorToolbar.element.remove(),this.deleteIconToolbar.element.remove()),this.togglePalettePanel(!1),this.focusInternal()}showLightnessShades(e,t,n){this.shadesCloseHandler&&this.shadesCloseHandler(),this.shadesContainer.classList.remove("hidden"),this.shadesContainer.removeChildren(),this.shadesContainer.animate([{transform:"scaleY(0)",opacity:"0"},{transform:"scaleY(1)",opacity:"1"}],{duration:200,easing:"cubic-bezier(0.4, 0, 0.2, 1)"});let o=this.paletteContainer.offsetTop+e.offsetTop+(e.parentElement?e.parentElement.offsetTop:0);this.contrastDetails&&(o+=this.contrastDetails.element().offsetHeight),this.shadesContainer.style.top=o+"px",this.shadesContainer.style.left=e.offsetLeft+"px",e.classList.add("spectrum-shades-shown");const i=E.get(t);if(void 0!==i)for(let t=i.length-1;t>=0;t--){const n=this.createPaletteColor(i[t],void 0,200*t/i.length+100);d.ARIAUtils.markAsButton(n),d.ARIAUtils.setAccessibleName(n,v(g.colorS,{PH1:i[t]})),n.tabIndex=-1,n.addEventListener("mousedown",this.paletteColorSelected.bind(this,i[t],i[t],!1)),n.addEventListener("focus",this.paletteColorSelected.bind(this,i[t],i[t],!1)),n.addEventListener("keydown",this.onShadeColorKeydown.bind(this,e)),this.shadesContainer.appendChild(n)}this.shadesContainer.childNodes.length>0&&this.shadesContainer.childNodes[this.shadesContainer.childNodes.length-1].focus(),this.shadesCloseHandler=function(e){this.shadesContainer.classList.add("hidden"),e.classList.remove("spectrum-shades-shown"),this.shadesCloseHandler&&this.shadesContainer.ownerDocument.removeEventListener("mousedown",this.shadesCloseHandler,!0),delete this.shadesCloseHandler}.bind(this,e),this.shadesContainer.ownerDocument.addEventListener("mousedown",this.shadesCloseHandler,!0)}slotIndexForEvent(e){const t=e,n=t.pageX-this.paletteContainer.totalOffsetLeft(),o=t.pageY-this.paletteContainer.totalOffsetTop(),i=Math.min(n/k|0,y-1),s=o/k|0;return Math.min(s*y+i,this.customPaletteSetting.get().colors.length-1)}isDraggingToBin(e){return e.pageX>this.deleteIconToolbar.element.totalOffsetLeft()}paletteDragStart(e){const t=d.UIUtils.deepElementFromEvent(e);if(!t||!b.get(t))return!1;const n=this.slotIndexForEvent(e);this.dragElement=t;const o=e;return this.dragHotSpotX=o.pageX-n%y*k,this.dragHotSpotY=o.pageY-(n/y|0)*k,!0}paletteDrag(e){const t=e;if(t.pageX<this.paletteContainer.totalOffsetLeft()||t.pageY<this.paletteContainer.totalOffsetTop())return;if(!this.dragElement||void 0===this.dragHotSpotX||void 0===this.dragHotSpotY)return;const n=this.slotIndexForEvent(e),o=t.pageX-n%y*k,i=t.pageY-(n/y|0)*k,s=this.isDraggingToBin(e);this.deleteIconToolbar.element.classList.add("dragging"),this.deleteIconToolbar.element.classList.toggle("delete-color-toolbar-active",s);const r="translateX("+(o-this.dragHotSpotX)+"px) translateY("+(i-this.dragHotSpotY)+"px)";this.dragElement.style.transform=s?r+" scale(0.8)":r;const a=Array.prototype.slice.call(this.paletteContainer.children),l=a.indexOf(this.dragElement),c=new Map;for(const e of a)c.set(e,e.totalOffset());l!==n&&this.paletteContainer.insertBefore(this.dragElement,a[n>l?n+1:n]);for(const e of a){if(e===this.dragElement)continue;const t=c.get(e),n=e.totalOffset();!t||t.left===n.left&&t.top===n.top||e.animate([{transform:"translateX("+(t.left-n.left)+"px) translateY("+(t.top-n.top)+"px)"},{transform:"none"}],{duration:100,easing:"cubic-bezier(0, 0, 0.2, 1)"})}}paletteDragEnd(e){if(!this.dragElement)return;this.isDraggingToBin(e)&&this.dragElement.remove(),this.dragElement.style.removeProperty("transform");const t=this.paletteContainer.children,n=[];for(let e=0;e<t.length;++e){const o=C.get(t[e]);o&&n.push(o)}const o=this.customPaletteSetting.get();o.colors=n,this.customPaletteSetting.set(o),this.showPalette(o,!1),this.deleteIconToolbar.element.classList.remove("dragging"),this.deleteIconToolbar.element.classList.remove("delete-color-toolbar-active")}loadPalettes(){this.palettes.set(F.title,F);const e={title:"Custom",colors:[],colorNames:[],mutable:!0,matchUserFormat:void 0};this.customPaletteSetting=i.Settings.Settings.instance().createSetting("customColorPalette",e);const t=this.customPaletteSetting.get();t.colorNames=t.colorNames||[],this.palettes.set(t.title,t),this.selectedColorPalette=i.Settings.Settings.instance().createSetting("selectedColorPalette",A);const n=this.palettes.get(this.selectedColorPalette.get());n&&this.showPalette(n,!0)}addPalette(e){this.palettes.set(e.title,e),this.selectedColorPalette.get()===e.title&&this.showPalette(e,!0)}createPreviewPaletteElement(e){const t=document.createElement("div");t.classList.add("palette-preview"),d.ARIAUtils.markAsButton(t),t.tabIndex=0;let n;for(t.createChild("div","palette-preview-title").textContent=e.title,n=0;n<5&&n<e.colors.length;n++)t.appendChild(this.createPaletteColor(e.colors[n],e.colorNames[n]));for(;n<5;n++)t.createChild("div","spectrum-palette-color empty-color");return self.onInvokeElement(t,(t=>{this.paletteSelected(e),t.consume(!0)})),t}paletteSelected(e){this.selectedColorPalette.set(e.title),this.showPalette(e,!0)}resizeForSelectedPalette(e){const t=this.palettes.get(this.selectedColorPalette.get());if(!t)return;let n=t.colors.length;t===this.customPaletteSetting.get()&&n++;const o=Math.max(1,Math.ceil(n/y));if(this.numPaletteRowsShown===o&&!e)return;this.numPaletteRowsShown=o;let i=236;this.contrastDetails&&(this.contrastDetails.expanded()?i+=78:i+=36),this.element.style.height=i+12+24*o+"px",this.dispatchEventToListeners(x.SizeChanged)}paletteColorSelected(e,t,n){const o=i.Color.Color.parse(e);o&&this.innerSetColor(o.hsva(),e,t,n?this.colorFormat:o.format(),w.Other)}onPaletteColorKeydown(e,t){let n;switch(t.key){case"ArrowLeft":n=e-1;break;case"ArrowRight":n=e+1;break;case"ArrowUp":n=e-y;break;case"ArrowDown":n=e+y}void 0!==n&&n>-1&&n<this.paletteContainer.childNodes.length&&this.paletteContainer.childNodes[n].focus()}onShadeColorKeydown(e,t){const n=t,o=n.target;isEscKey(t)||"Tab"===n.key?(e.focus(),this.shadesCloseHandler&&this.shadesCloseHandler(),t.consume(!0)):"ArrowUp"===n.key&&o.previousElementSibling?(o.previousElementSibling.focus(),t.consume(!0)):"ArrowDown"===n.key&&o.nextElementSibling&&(o.nextElementSibling.focus(),t.consume(!0))}onAddColorMousedown(){this.addColorToCustomPalette()}onAddColorKeydown(e){isEnterOrSpaceKey(e)&&(this.addColorToCustomPalette(),e.consume(!0))}addColorToCustomPalette(){const e=this.customPaletteSetting.get();e.colors.push(this.colorString()),this.customPaletteSetting.set(e),this.showPalette(e,!1);const t=this.paletteContainer.querySelectorAll(".spectrum-palette-color");t[t.length-1].focus()}showPaletteColorContextMenu(e,t){if(!this.paletteContainerMutable)return;const n=new d.ContextMenu.ContextMenu(t);-1!==e&&(n.defaultSection().appendItem(v(g.removeColor),this.deletePaletteColors.bind(this,e,!1)),n.defaultSection().appendItem(v(g.removeAllToTheRight),this.deletePaletteColors.bind(this,e,!0))),n.defaultSection().appendItem(v(g.clearPalette),this.deletePaletteColors.bind(this,-1,!0)),n.show()}deletePaletteColors(e,t){const n=this.customPaletteSetting.get();t?n.colors.splice(e+1,n.colors.length-e-1):n.colors.splice(e,1),this.customPaletteSetting.set(n),this.showPalette(n,!1)}setColor(e,t){this.originalFormat=t,this.innerSetColor(e.hsva(),"",void 0,t,w.Model);const n=this.color().canonicalHSLA();d.ARIAUtils.setValueNow(this.hueElement,n[0]),d.ARIAUtils.setValueText(this.alphaElement,n[3])}colorSelected(e){this.innerSetColor(e.hsva(),"",void 0,void 0,w.Other)}innerSetColor(e,t,n,o,s){if(void 0!==e&&(this.hsv=e),this.colorNameInternal=n,void 0!==t&&(this.colorStringInternal=t),void 0!==o){const e=i.Color.Format;console.assert(o!==e.Original,"Spectrum's color format cannot be Original"),o===e.RGBA?o=e.RGB:o===e.HSLA?o=e.HSL:o===e.HWBA?o=e.HWB:o===e.HEXA?o=e.HEX:o===e.ShortHEXA&&(o=e.ShortHEX),this.colorFormat=o}this.contrastInfo&&this.contrastInfo.setColor(i.Color.Color.fromHSVA(this.hsv),this.colorFormat),this.updateHelperLocations(),this.updateUI(),s!==w.Input&&this.updateInput(),s!==w.Model&&this.dispatchEventToListeners(x.ColorChanged,this.colorString())}color(){return i.Color.Color.fromHSVA(this.hsv)}colorName(){return this.colorNameInternal}colorString(){if(this.colorStringInternal)return this.colorStringInternal;const e=i.Color.Format,t=this.color();let n=t.asString(this.colorFormat);return n||(n=this.colorFormat===e.Nickname?t.asString(t.hasAlpha()?e.HEXA:e.HEX):this.colorFormat===e.ShortHEX?t.asString(t.detectHEXFormat()):this.colorFormat===e.HEX?t.asString(e.HEXA):this.colorFormat===e.HSL?t.asString(e.HSLA):this.colorFormat===e.HWB?t.asString(e.HWBA):t.asString(e.RGBA),console.assert(Boolean(n)),n||"")}updateHelperLocations(){const e=this.hsv[0],t=this.hsv[1],n=this.hsv[2],o=this.hsv[3];this.dragX=t*this.dragWidth,this.dragY=this.dragHeight-n*this.dragHeight;const i=Math.max(-this.colorDragElementHeight,Math.min(this.dragWidth-this.colorDragElementHeight,this.dragX-this.colorDragElementHeight)),s=Math.max(-this.colorDragElementHeight,Math.min(this.dragHeight-this.colorDragElementHeight,this.dragY-this.colorDragElementHeight));this.colorDragElement.positionAt(i,s);const r=(1-e)*this.hueAlphaWidth-this.slideHelperWidth;this.hueSlider.style.left=r+"px";const a=o*this.hueAlphaWidth-this.slideHelperWidth;this.alphaSlider.style.left=a+"px"}updateInput(){const e=i.Color.Format;if(this.colorFormat===e.HEX||this.colorFormat===e.ShortHEX||this.colorFormat===e.Nickname)this.hexContainer.hidden=!1,this.displayContainer.hidden=!0,this.colorFormat===e.ShortHEX?this.hexValue.value=String(this.color().asString(this.color().detectHEXFormat())):this.hexValue.value=String(this.color().asString(this.color().hasAlpha()?e.HEXA:e.HEX));else{let t;this.hexContainer.hidden=!0,this.displayContainer.hidden=!1,this.colorFormat===e.RGB?(this.textLabels.textContent="RGBA",t=this.color().canonicalRGBA()):this.colorFormat===e.HSL?(this.textLabels.textContent="HSLA",t=this.color().canonicalHSLA()):(this.textLabels.textContent="HWBA",t=this.color().canonicalHWBA());for(let n=0;n<3;++n)d.ARIAUtils.setAccessibleName(this.textValues[n],v(g.sInS,{PH1:this.textLabels.textContent.charAt(n),PH2:this.textLabels.textContent})),this.textValues[n].value=String(t[n]),this.colorFormat===e.RGB||1!==n&&2!==n||(this.textValues[n].value+="%");d.ARIAUtils.setAccessibleName(this.textValues[3],v(g.sInS,{PH1:this.textLabels.textContent.charAt(3),PH2:this.textLabels.textContent})),this.textValues[3].value=String(Math.round(100*t[3])/100)}}updateUI(){const e=i.Color.Color.fromHSVA([this.hsv[0],1,1,1]);this.colorElement.style.backgroundColor=e.asString(i.Color.Format.RGB),this.contrastOverlay&&this.contrastOverlay.setDimensions(this.dragWidth,this.dragHeight),this.swatch.setColor(this.color(),this.colorString()),this.colorDragElement.style.backgroundColor=this.color().asString(i.Color.Format.RGBA);const t=i.Color.Color.fromHSVA(this.hsv.slice(0,3).concat(1));this.alphaElementBackground.style.backgroundImage=a.StringUtilities.sprintf("linear-gradient(to right, rgba(0,0,0,0), %s)",t.asString(i.Color.Format.RGB))}formatViewSwitch(){const e=i.Color.Format;let t=e.RGB;this.colorFormat===e.RGB?t=e.HSL:this.colorFormat===e.HSL?t=e.HWB:this.colorFormat===e.HWB&&(t=this.originalFormat===e.ShortHEX||this.originalFormat===e.ShortHEXA?e.ShortHEX:e.HEX),this.innerSetColor(void 0,"",void 0,t,w.Other)}pasted(e){if(!e.clipboardData)return;const t=e.clipboardData.getData("text"),n=i.Color.Color.parse(t);n&&(this.innerSetColor(n.hsva(),t,void 0,void 0,w.Other),e.preventDefault())}inputChanged(e){function t(e){return e.value}const n=e.currentTarget,o=d.UIUtils.createReplacementString(n.value,e);o&&(n.value=o,n.selectionStart=0,n.selectionEnd=o.length,e.consume(!0));const s=i.Color.Format;let r;if(this.colorFormat===s.Nickname||this.colorFormat===s.HEX||this.colorFormat===s.ShortHEX)r=this.hexValue.value;else{const e=this.textValues.slice(0,-1).map(t).join(" "),n=this.textValues.slice(-1).map(t).join(" ");r=a.StringUtilities.sprintf("%s(%s)",this.colorFormat,[e,n].join(" / "))}const l=i.Color.Color.parse(r);if(!l)return;let c;this.colorFormat!==s.HEX&&this.colorFormat!==s.ShortHEX||(c=l.detectHEXFormat()),this.innerSetColor(l.hsva(),r,void 0,c,w.Input)}wasShown(){this.registerCSSFiles([u.default]),this.hueAlphaWidth=this.hueElement.offsetWidth,this.slideHelperWidth=this.hueSlider.offsetWidth/2,this.dragWidth=this.colorElement.offsetWidth,this.dragHeight=this.colorElement.offsetHeight,this.colorDragElementHeight=this.colorDragElement.offsetHeight/2,this.innerSetColor(void 0,void 0,void 0,void 0,w.Model),this.toggleColorPicker(!0),this.contrastDetails&&this.contrastDetailsBackgroundColorPickedToggledBound&&this.contrastDetails.addEventListener("BackgroundColorPickerWillBeToggled",this.contrastDetailsBackgroundColorPickedToggledBound)}willHide(){this.toggleColorPicker(!1),this.contrastDetails&&this.contrastDetailsBackgroundColorPickedToggledBound&&this.contrastDetails.removeEventListener("BackgroundColorPickerWillBeToggled",this.contrastDetailsBackgroundColorPickedToggledBound)}toggleColorPicker(e){void 0===e&&(e=!this.colorPickerButton.toggled()),this.colorPickerButton.setToggled(e),this.contrastDetails&&e&&this.contrastDetails.backgroundColorPickerEnabled()&&this.contrastDetails.toggleBackgroundColorPicker(!1),s.InspectorFrontendHost.InspectorFrontendHostInstance.setEyeDropperActive(e),e?s.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(s.InspectorFrontendHostAPI.Events.EyeDropperPickedColor,this.colorPickedBound):s.InspectorFrontendHost.InspectorFrontendHostInstance.events.removeEventListener(s.InspectorFrontendHostAPI.Events.EyeDropperPickedColor,this.colorPickedBound)}colorPicked({data:e}){const t=[e.r,e.g,e.b,(e.a/2.55|0)/100],n=i.Color.Color.fromRGBA(t);this.innerSetColor(n.hsva(),"",void 0,void 0,w.Other),s.InspectorFrontendHost.InspectorFrontendHostInstance.bringToFront()}}const w={Input:"Input",Model:"Model",Other:"Other"};var x,S;(S=x||(x={})).ColorChanged="ColorChanged",S.SizeChanged="SizeChanged";const k=24,y=8,A="Page colors";class I{callback;frequencyMap;constructor(e){this.callback=e,this.frequencyMap=new Map;const t=[];for(const e of l.TargetManager.TargetManager.instance().models(l.CSSModel.CSSModel))for(const n of e.allStyleSheets())t.push(this.processStylesheet(n));Promise.all(t).catch((e=>{console.error(e)})).then(this.finish.bind(this))}frequencyComparator(e,t){return this.frequencyMap.get(t)-this.frequencyMap.get(e)}finish(){let e=[...this.frequencyMap.keys()];e=e.sort(this.frequencyComparator.bind(this));const t=new Map;for(;t.size<24&&e.length;){const n=e.shift(),o=i.Color.Color.parse(n);o&&"white"!==o.nickname()&&"black"!==o.nickname()&&t.set(n,o)}this.callback({title:A,colors:[...t.keys()].sort((function(e,n){const o=t.get(e).hsva(),i=t.get(n).hsva();return i[1]<.12&&o[1]<.12?i[2]*i[3]-o[2]*o[3]:i[1]<.12?-1:o[1]<.12?1:i[0]===o[0]?i[1]*i[3]-o[1]*o[3]:(i[0]+.94)%1-(o[0]+.94)%1})),colorNames:[],mutable:!1,matchUserFormat:void 0})}async processStylesheet(e){let t=(await e.requestContent()).content||"";t=t.toLowerCase();const n=t.match(/((?:rgb|hsl|hwb)a?\([^)]+\)|#[0-9a-f]{6}|#[0-9a-f]{3})/g)||[];for(const e of n){let t=this.frequencyMap.get(e)||0;this.frequencyMap.set(e,++t)}}}const E=new Map([["#F44336",["#FFEBEE","#FFCDD2","#EF9A9A","#E57373","#EF5350","#F44336","#E53935","#D32F2F","#C62828","#B71C1C"]],["#E91E63",["#FCE4EC","#F8BBD0","#F48FB1","#F06292","#EC407A","#E91E63","#D81B60","#C2185B","#AD1457","#880E4F"]],["#9C27B0",["#F3E5F5","#E1BEE7","#CE93D8","#BA68C8","#AB47BC","#9C27B0","#8E24AA","#7B1FA2","#6A1B9A","#4A148C"]],["#673AB7",["#EDE7F6","#D1C4E9","#B39DDB","#9575CD","#7E57C2","#673AB7","#5E35B1","#512DA8","#4527A0","#311B92"]],["#3F51B5",["#E8EAF6","#C5CAE9","#9FA8DA","#7986CB","#5C6BC0","#3F51B5","#3949AB","#303F9F","#283593","#1A237E"]],["#2196F3",["#E3F2FD","#BBDEFB","#90CAF9","#64B5F6","#42A5F5","#2196F3","#1E88E5","#1976D2","#1565C0","#0D47A1"]],["#03A9F4",["#E1F5FE","#B3E5FC","#81D4FA","#4FC3F7","#29B6F6","#03A9F4","#039BE5","#0288D1","#0277BD","#01579B"]],["#00BCD4",["#E0F7FA","#B2EBF2","#80DEEA","#4DD0E1","#26C6DA","#00BCD4","#00ACC1","#0097A7","#00838F","#006064"]],["#009688",["#E0F2F1","#B2DFDB","#80CBC4","#4DB6AC","#26A69A","#009688","#00897B","#00796B","#00695C","#004D40"]],["#4CAF50",["#E8F5E9","#C8E6C9","#A5D6A7","#81C784","#66BB6A","#4CAF50","#43A047","#388E3C","#2E7D32","#1B5E20"]],["#8BC34A",["#F1F8E9","#DCEDC8","#C5E1A5","#AED581","#9CCC65","#8BC34A","#7CB342","#689F38","#558B2F","#33691E"]],["#CDDC39",["#F9FBE7","#F0F4C3","#E6EE9C","#DCE775","#D4E157","#CDDC39","#C0CA33","#AFB42B","#9E9D24","#827717"]],["#FFEB3B",["#FFFDE7","#FFF9C4","#FFF59D","#FFF176","#FFEE58","#FFEB3B","#FDD835","#FBC02D","#F9A825","#F57F17"]],["#FFC107",["#FFF8E1","#FFECB3","#FFE082","#FFD54F","#FFCA28","#FFC107","#FFB300","#FFA000","#FF8F00","#FF6F00"]],["#FF9800",["#FFF3E0","#FFE0B2","#FFCC80","#FFB74D","#FFA726","#FF9800","#FB8C00","#F57C00","#EF6C00","#E65100"]],["#FF5722",["#FBE9E7","#FFCCBC","#FFAB91","#FF8A65","#FF7043","#FF5722","#F4511E","#E64A19","#D84315","#BF360C"]],["#795548",["#EFEBE9","#D7CCC8","#BCAAA4","#A1887F","#8D6E63","#795548","#6D4C41","#5D4037","#4E342E","#3E2723"]],["#9E9E9E",["#FAFAFA","#F5F5F5","#EEEEEE","#E0E0E0","#BDBDBD","#9E9E9E","#757575","#616161","#424242","#212121"]],["#607D8B",["#ECEFF1","#CFD8DC","#B0BEC5","#90A4AE","#78909C","#607D8B","#546E7A","#455A64","#37474F","#263238"]]]),F={title:"Material",mutable:!1,matchUserFormat:!0,colors:[...E.keys()],colorNames:[]};class P{colorString;swatchInnerElement;swatchOverlayElement;swatchCopyIcon;constructor(e){const t=e.createChild("span","swatch");this.swatchInnerElement=t.createChild("span","swatch-inner"),this.swatchOverlayElement=t.createChild("span","swatch-overlay"),d.ARIAUtils.markAsButton(this.swatchOverlayElement),d.ARIAUtils.setPressed(this.swatchOverlayElement,!1),this.swatchOverlayElement.tabIndex=0,self.onInvokeElement(this.swatchOverlayElement,this.onCopyText.bind(this)),this.swatchOverlayElement.addEventListener("mouseout",this.onCopyIconMouseout.bind(this)),this.swatchOverlayElement.addEventListener("blur",this.onCopyIconMouseout.bind(this)),this.swatchCopyIcon=d.Icon.Icon.create("largeicon-copy","copy-color-icon"),d.Tooltip.Tooltip.install(this.swatchCopyIcon,v(g.copyColorToClipboard)),this.swatchOverlayElement.appendChild(this.swatchCopyIcon),d.ARIAUtils.setAccessibleName(this.swatchOverlayElement,this.swatchCopyIcon.title)}setColor(e,t){this.swatchInnerElement.style.backgroundColor=e.asString(i.Color.Format.RGBA),this.swatchInnerElement.classList.toggle("swatch-inner-white",e.hsla()[2]>.9),this.colorString=t||null,this.swatchOverlayElement.hidden=!t}onCopyText(e){this.swatchCopyIcon.setIconType("largeicon-checkmark"),s.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(this.colorString),d.ARIAUtils.setPressed(this.swatchOverlayElement,!0),e.consume()}onCopyIconMouseout(){this.swatchCopyIcon.setIconType("largeicon-copy"),d.ARIAUtils.setPressed(this.swatchOverlayElement,!1)}}})),t.register("dFG0m",(function(t,n){e(t.exports,"default",(()=>i));const o=new CSSStyleSheet;o.replaceSync('/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n/* https://github.com/bgrins/spectrum */\n\n:host {\n  width: 232px;\n  height: 319px;\n  user-select: none;\n  overflow: hidden;\n}\n\n:selection {\n  background-color: var(--color-primary);\n  color: var(--color-background);\n}\n\n.spectrum-color {\n  position: relative;\n  width: 232px;\n  height: 127px;\n  border-radius: 2px 2px 0 0;\n  overflow: hidden;\n  flex: none;\n  touch-action: none;\n}\n\n.spectrum-dragger,\n.spectrum-slider {\n  user-select: none;\n}\n\n.spectrum-dragger {\n  border-radius: 12px;\n  height: 12px;\n  width: 12px;\n  border: 1px solid var(--color-background);\n  cursor: move;\n  z-index: 1;\n  position: absolute;\n  top: 0;\n  left: 0;\n  background: var(--color-background-inverted);\n  box-shadow: var(--drop-shadow);\n}\n\n.spectrum-slider {\n  position: absolute;\n  top: -1px;\n  cursor: ew-resize;\n  width: 13px;\n  height: 13px;\n  border-radius: 13px;\n  background-color: var(--color-background-elevation-1);\n  box-shadow: var(--drop-shadow);\n}\n\n.spectrum-color:focus .spectrum-dragger {\n  border: 1px solid var(--legacy-accent-color-hover);\n}\n\n.spectrum-tools {\n  position: relative;\n  height: 110px;\n  width: 100%;\n  flex: none;\n}\n\n.spectrum-hue {\n  top: 16px;\n  background: linear-gradient(to left, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%); /* stylelint-disable-line plugin/use_theme_colors */\n}\n\n.spectrum-alpha {\n  top: 35px;\n  background-image: var(--image-file-checker);\n  background-size: 12px 11px;\n}\n\n.spectrum-alpha-background {\n  height: 100%;\n  border-radius: 2px;\n}\n\n.spectrum-hue,\n.spectrum-alpha {\n  position: absolute;\n  left: 86px;\n  width: 130px;\n  height: 11px;\n  border-radius: 2px;\n  touch-action: none;\n}\n\n.spectrum-hue:focus-visible .spectrum-slider,\n.spectrum-alpha:focus-visible .spectrum-slider {\n  border: 1px solid var(--legacy-accent-color-hover);\n  width: 14px;\n  height: 14px;\n  border-radius: 14px;\n}\n\n.spectrum-sat,\n.-theme-preserve {\n  background-image: linear-gradient(to right, #fff, rgb(204 154 129 / 0%)); /* stylelint-disable-line plugin/use_theme_colors */\n}\n\n.spectrum-val,\n.-theme-preserve {\n  background-image: linear-gradient(to top, #000, rgb(204 154 129 / 0%)); /* stylelint-disable-line plugin/use_theme_colors */\n}\n\n.spectrum-contrast-details {\n  position: relative;\n  background-color: var(--color-background);\n  width: 100%;\n  height: 83px;\n  top: 0;\n  font-size: 13px;\n  color: var(--color-text-primary);\n  border-top: var(--legacy-divider-border);\n  line-height: initial;\n  overflow: hidden;\n  flex: none;\n}\n\n.spectrum-contrast-details.collapsed {\n  height: 36px;\n  flex: none;\n}\n\n.spectrum-contrast-details div.toolbar.expand {\n  position: absolute;\n  right: 6px;\n  top: 6px;\n  margin: 0;\n}\n\n.spectrum-contrast-details.visible {\n  display: initial;\n}\n\n.spectrum-contrast-details div.container {\n  margin: 10px;\n}\n\n.spectrum-contrast-details .expanded-details {\n  display: flex;\n  margin: 12px 12px 0 4px;\n}\n\n.spectrum-contrast-details.collapsed .expanded-details {\n  display: none;\n}\n\n.contrast-pass-fail {\n  margin-left: 0.5em;\n  display: flex;\n  align-items: center;\n}\n\n.contrast-choose-bg-color {\n  margin: 8px 0 0 5px;\n  font-style: italic;\n}\n\n.spectrum-contrast-details .contrast-choose-bg-color,\n.spectrum-contrast-details .contrast-thresholds {\n  width: 150px;\n}\n\n.contrast-threshold:first-child {\n  margin-bottom: 5px;\n}\n\n.contrast-fix-button {\n  cursor: pointer;\n  font-size: 13px;\n  padding: 0;\n  margin: 0 0 0 10px;\n  background: 0;\n  width: 12px;\n  height: 12px;\n  border: 1px solid rgb(0 0 0 / 10%); /* stylelint-disable-line plugin/use_theme_colors */\n  /* See: crbug.com/1152736 for color variable migration. */\n  display: inline-block;\n  position: relative;\n}\n\n.contrast-fix-button::after {\n  content: " ";\n  width: 13px;\n  height: 13px;\n  background-image: var(--image-file-ic_suggest_color);\n  background-size: contain;\n  position: absolute;\n  left: 5.5px;\n  top: 3.5px;\n  background-color: var(--color-background);\n  border-radius: 50%;\n}\n\n.contrast-fix-button:hover,\n.contrast-fix-button:focus {\n  border: 1px solid var(--legacy-accent-color-hover);\n  transform: scale(1.2);\n}\n\n.contrast-link-label {\n  cursor: pointer;\n}\n\n.contrast-link-label:hover {\n  text-decoration: underline;\n}\n\n.spectrum-contrast-details .background-color {\n  position: absolute;\n  flex: none;\n  right: 12px;\n}\n\n.spectrum-eye-dropper {\n  width: 32px;\n  height: 24px;\n  position: relative;\n  left: 8px;\n  top: 17px;\n  cursor: pointer;\n}\n\n.spectrum-contrast-details .spectrum-eye-dropper {\n  top: 2px;\n  right: 34px;\n  position: absolute;\n  left: auto;\n}\n\n.contrast-details-value {\n  color: var(--color-text-primary);\n  margin: 1px 5px;\n  user-select: text;\n}\n\n.contrast-details-value [is="ui-icon"] {\n  display: none;\n  margin-left: 5px;\n  background-color: var(--color-text-primary);\n}\n\n.spectrum-contrast-details .toolbar-state-on [is="ui-icon"] {\n  background-color: var(--color-text-secondary);\n}\n\n[is="ui-icon"].smallicon-no {\n  background-color: var(--color-accent-red);\n}\n\n.contrast-pass-fail span[is="ui-icon"] {\n  margin-left: 5px;\n}\n\n[is="ui-icon"].smallicon-checkmark-square,\n[is="ui-icon"].smallicon-checkmark-behind {\n  background-color: var(--color-accent-green);\n}\n\n.spectrum-contrast-details .contrast-details-value.contrast-unknown {\n  background-color: var(--color-background);\n  color: var(--color-text-primary);\n  width: 3em;\n  text-align: center;\n}\n\n.contrast-details-value .smallicon-checkmark-behind {\n  margin-left: -6px;\n}\n\n.contrast-details-value .smallicon-no,\n.contrast-details-value .smallicon-checkmark-square,\n.contrast-details-value .smallicon-checkmark-behind {\n  cursor: pointer;\n}\n\n.spectrum-contrast-details.contrast-fail .contrast-details-value .smallicon-no,\n.contrast-details-value.contrast-aa .smallicon-checkmark-square,\n.contrast-details-value.contrast-aaa .smallicon-checkmark-behind {\n  display: inline-block;\n}\n\n.swatch {\n  width: 32px;\n  height: 32px;\n  margin: 0;\n  position: absolute;\n  top: 15px;\n  left: 44px;\n  background-image: var(--image-file-checker);\n  border-radius: 16px;\n}\n\n.swatch-inner,\n.swatch-overlay {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  display: inline-block;\n  border-radius: 16px;\n}\n\n.swatch-inner-white {\n  border: 1px solid var(--color-background-elevation-2);\n}\n\n.swatch-overlay {\n  cursor: pointer;\n  opacity: 0%;\n  padding: 4px;\n}\n\n.swatch-overlay:hover,\n.swatch-overlay:focus-visible {\n  background-color: var(--color-background-inverted-opacity-30);\n  opacity: 100%;\n}\n\n.swatch-overlay:active {\n  background-color: var(--color-background-inverted-opacity-50);\n}\n\n[is="ui-icon"].icon-mask.copy-color-icon {\n  background-color: var(--color-background);\n}\n\n.spectrum-text {\n  position: absolute;\n  top: 60px;\n  left: 16px;\n}\n\n.spectrum-text-value {\n  display: inline-block;\n  width: 40px;\n  overflow: hidden;\n  text-align: center;\n  margin-right: 6px;\n  line-height: 20px;\n  padding: 0;\n  color: var(--color-text-primary);\n  white-space: nowrap;\n  box-shadow: var(--legacy-focus-ring-inactive-shadow);\n}\n\n.spectrum-text-label {\n  letter-spacing: 39.5px;\n  margin-top: 8px;\n  display: block;\n  color: var(--color-text-disabled);\n  margin-left: 16px;\n  width: 174px;\n}\n\n.spectrum-text-hex > .spectrum-text-value {\n  width: 178px;\n}\n\n.spectrum-text-hex > .spectrum-text-label {\n  letter-spacing: normal;\n  margin-left: 0;\n  text-align: center;\n}\n\n.spectrum-switcher {\n  border-radius: 2px;\n  height: 20px;\n  width: 20px;\n  padding: 2px;\n}\n\n.spectrum-display-switcher {\n  top: 72px;\n  position: absolute;\n  right: 10px;\n}\n\n.spectrum-switcher:hover {\n  background-color: var(--color-background-elevation-2);\n}\n\n.spectrum-switcher:focus-visible {\n  background-color: var(--legacy-focus-bg-color);\n}\n\n.spectrum-palette-container {\n  border-top: var(--legacy-divider-border);\n  position: relative;\n  width: 100%;\n  padding: 6px 24px 6px 6px;\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.spectrum-palette {\n  display: flex;\n  flex-wrap: wrap;\n  width: 198px;\n}\n\n.spectrum-palette-color {\n  width: 12px;\n  height: 12px;\n  flex: 0 0 12px;\n  border-radius: 2px;\n  margin: 6px;\n  cursor: pointer;\n  position: relative;\n  border: 1px solid var(--color-details-hairline);\n  background-position: -1px !important; /* stylelint-disable-line declaration-no-important */\n  z-index: 14;\n}\n\n.spectrum-palette-color-shadow {\n  position: absolute;\n  opacity: 0%;\n  margin: 0;\n  top: -5px;\n  left: 3px;\n  border: 0;\n  border-radius: 1px;\n  width: 11px;\n  height: 11px;\n}\n\n.spectrum-palette-color:hover:not(.spectrum-shades-shown) > .spectrum-palette-color-shadow,\n.spectrum-palette-color:focus:not(.spectrum-shades-shown) > .spectrum-palette-color-shadow {\n  opacity: 20%;\n}\n\n.spectrum-palette-color:hover:not(.spectrum-shades-shown) > .spectrum-palette-color-shadow:first-child,\n.spectrum-palette-color:focus:not(.spectrum-shades-shown) > .spectrum-palette-color-shadow:first-child {\n  opacity: 60%;\n  top: -3px;\n  left: 1px;\n}\n\n.palette-color-shades {\n  position: absolute;\n  background-color: var(--color-background);\n  height: 228px;\n  width: 28px;\n  box-shadow: var(--drop-shadow);\n  z-index: 14;\n  border-radius: 2px;\n  transform-origin: 0 228px;\n  margin-top: 16px;\n  margin-left: -8px;\n}\n\n.spectrum-palette > .spectrum-palette-color.spectrum-shades-shown {\n  z-index: 15;\n}\n\n.palette-color-shades > .spectrum-palette-color {\n  margin: 8px 0 0;\n  margin-left: 8px;\n  width: 12px;\n}\n\n.spectrum-palette > .spectrum-palette-color {\n  transition: transform 100ms cubic-bezier(0, 0, 0.2, 1);\n  will-change: transform;\n  z-index: 13;\n}\n\n.palette-preview > .spectrum-palette-color {\n  margin-top: 1px;\n}\n\n.spectrum-palette > .spectrum-palette-color.empty-color {\n  border-color: transparent;\n}\n\n.spectrum-palette-color:not(.has-material-shades):focus {\n  border: 1px solid var(--legacy-accent-color-hover);\n  transform: scale(1.4);\n}\n\n.palette-color-shades > .spectrum-palette-color:not(.empty-color):hover,\n.spectrum-palette > .spectrum-palette-color:not(.empty-color):not(.has-material-shades):hover {\n  transform: scale(1.15);\n}\n\n.add-color-toolbar {\n  margin-left: -3px;\n  margin-top: -1px;\n}\n\n.spectrum-palette-switcher {\n  right: 10px;\n  top: 0;\n  margin-top: 9px;\n  position: absolute;\n}\n\n.palette-panel {\n  width: 100%;\n  position: absolute;\n  top: 100%;\n  display: flex;\n  flex-direction: column;\n  background-color: var(--color-background);\n  z-index: 14;\n  transition: transform 200ms cubic-bezier(0, 0, 0.2, 1), visibility 0s 200ms;\n  border-top: var(--legacy-divider-border);\n  visibility: hidden;\n}\n\n.palette-panel-showing > .palette-panel {\n  transform: translateY(-100%);\n  transition-delay: 0s;\n  visibility: visible;\n}\n\n.palette-panel > div.toolbar {\n  position: absolute;\n  right: 6px;\n  top: 6px;\n}\n\n.palette-panel > div:not(.toolbar) {\n  flex: 0 0 38px;\n  border-bottom: var(--legacy-divider-border);\n  padding: 12px;\n  line-height: 14px;\n  color: var(--color-text-primary);\n}\n\n.palette-panel > div.palette-title {\n  font-size: 14px;\n  line-height: 16px;\n  color: var(--color-text-primary);\n  flex-basis: 40px;\n}\n\ndiv.palette-preview {\n  display: flex;\n  cursor: pointer;\n}\n\n.palette-preview-title {\n  flex: 0 0 84px;\n}\n\n.palette-preview:focus-visible,\n.palette-preview:hover {\n  background-color: var(--color-background-elevation-1);\n}\n\n.spectrum-overlay {\n  z-index: 13;\n  visibility: hidden;\n  background-color: hsl(0deg 0% 0% / 50%); /* stylelint-disable-line plugin/use_theme_colors */\n  /* See: crbug.com/1152736 for color variable migration. */\n  opacity: 0%;\n  transition: opacity 100ms cubic-bezier(0, 0, 0.2, 1), visibility 0s 100ms;\n}\n\n.palette-panel-showing > .spectrum-overlay {\n  transition-delay: 0s;\n  visibility: visible;\n  opacity: 100%;\n}\n\n.spectrum-contrast-container {\n  width: 100%;\n  height: 100%;\n}\n\n.spectrum-contrast-line,\n:host-context(.-theme-with-dark-background) .spectrum-contrast-line {\n  fill: none;\n  stroke: #fff; /* stylelint-disable-line plugin/use_theme_colors */\n  opacity: 70%;\n  stroke-width: 1.5px;\n}\n\n.delete-color-toolbar {\n  position: absolute;\n  right: 0;\n  top: 0;\n  background-color: var(--color-background-elevation-1);\n  visibility: hidden;\n  z-index: 3;\n  width: 36px;\n  display: flex;\n  align-items: center;\n  padding-left: 4px;\n  bottom: 2px;\n  border-bottom-right-radius: 2px;\n}\n\n@keyframes showDeleteToolbar {\n  from {\n    opacity: 0%;\n  }\n\n  to {\n    opacity: 100%;\n  }\n}\n\n.delete-color-toolbar.dragging {\n  visibility: visible;\n  animation: showDeleteToolbar 100ms 150ms cubic-bezier(0, 0, 0.2, 1) backwards;\n}\n\n.delete-color-toolbar-active {\n  background-color: var(--color-background-elevation-2);\n  color: var(--color-background);\n}\n\n.swatch.contrast {\n  width: 30px;\n  height: 30px;\n  position: absolute;\n  top: 0;\n  right: 0;\n  left: auto;\n  background-image: var(--image-file-checker);\n  border-radius: 15px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.swatch.contrast .swatch-overlay {\n  padding: 0;\n}\n\n.background-color .text-preview {\n  color: var(--color-background-inverted);\n  font-size: 16px;\n  position: relative;\n  padding-bottom: 2px;\n}\n\n.swatch.contrast [is="ui-icon"] {\n  margin: -2px;\n}\n\n.no-contrast-info-available {\n  border-top: var(--legacy-divider-border);\n  position: relative;\n  width: 100%;\n  padding: 10px;\n  justify-content: center;\n  display: flex;\n  flex-wrap: wrap;\n}\n\n@media (forced-colors: active) {\n  :host {\n    border: 1px solid canvastext !important; /* stylelint-disable-line declaration-no-important */\n  }\n\n  .spectrum-color {\n    forced-color-adjust: none;\n  }\n\n  .spectrum-switcher:hover,\n  .spectrum-switcher:focus-visible {\n    forced-color-adjust: none;\n    background-color: Highlight !important; /* stylelint-disable-line declaration-no-important */\n  }\n\n  :host-context(.-theme-with-dark-background) .spectrum-switcher {\n    filter: unset;\n  }\n\n  .spectrum-switcher:hover svg,\n  .spectrum-switcher:focus-visible svg {\n    fill: HighlightText;\n  }\n\n  .swatch {\n    forced-color-adjust: none;\n  }\n\n  .swatch-inner,\n  .swatch-overlay,\n  .swatch-inner-white {\n    border: 1px solid ButtonText;\n  }\n\n  .swatch-overlay:hover,\n  .swatch-overlay:focus-visible {\n    background-color: canvas !important; /* stylelint-disable-line declaration-no-important */\n  }\n\n  .spectrum-slider {\n    forced-color-adjust: none;\n    background-color: ButtonText !important; /* stylelint-disable-line declaration-no-important */\n    box-shadow: 0 1px 4px 0 ButtonFace !important; /* stylelint-disable-line declaration-no-important */\n  }\n}\n\n/*# sourceURL=spectrum.css */\n');var i=o})),t.register("j1L5M",(function(n,o){e(n.exports,"CSSOverviewProcessingView",(()=>d));var i=t("ixFnt"),s=t("9z2ZV"),r=t("04qpj");const a={cancel:"Cancel"},l=i.i18n.registerUIStrings("panels/css_overview/CSSOverviewProcessingView.ts",a),c=i.i18n.getLocalizedString.bind(void 0,l);class d extends s.Widget.Widget{#s;fragment;constructor(e){super(),this.#s=e,this.#r()}#r(){const e=s.UIUtils.createTextButton(c(a.cancel),(()=>this.#s.dispatchEventToListeners("RequestOverviewCancel")),"",!0);this.setDefaultFocusedElement(e),this.fragment=s.Fragment.Fragment.build`
      <div class="vbox overview-processing-view">
        <h1>Processing page</h1>
        <div>${e}</div>
      </div>
    `,this.contentElement.appendChild(this.fragment.element()),this.contentElement.style.overflow="auto"}wasShown(){super.wasShown(),this.registerCSSFiles([r.default])}}})),t.register("04qpj",(function(t,n){e(t.exports,"default",(()=>i));const o=new CSSStyleSheet;o.replaceSync("/**\n * Copyright 2019 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.overview-processing-view {\n  overflow: hidden;\n  padding: 16px;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n}\n\n.overview-processing-view h1 {\n  font-size: 16px;\n  text-align: center;\n  font-weight: normal;\n  margin: 0;\n  padding: 8px;\n}\n\n.overview-processing-view h2 {\n  font-size: 12px;\n  text-align: center;\n  font-weight: normal;\n  margin: 0;\n  padding-top: 32px;\n}\n\n/*# sourceURL=cssOverviewProcessingView.css */\n");var i=o})),t.register("bnRQQ",(function(n,o){e(n.exports,"CSSOverviewCompletedView",(()=>f)),e(n.exports,"DetailsView",(()=>w)),e(n.exports,"ElementDetailsView",(()=>x)),e(n.exports,"ElementNode",(()=>S));var i=t("koSS8"),s=t("ixFnt"),r=t("lz7WY"),a=t("9X2mn"),l=t("eQFvP"),c=t("7f6zc"),d=t("cObcK"),h=t("a3yig"),p=t("9z2ZV"),u=t("2Stov"),g=t("dnEPG");const m={overviewSummary:"Overview summary",colors:"Colors",fontInfo:"Font info",unusedDeclarations:"Unused declarations",mediaQueries:"Media queries",elements:"Elements",externalStylesheets:"External stylesheets",inlineStyleElements:"Inline style elements",styleRules:"Style rules",typeSelectors:"Type selectors",idSelectors:"ID selectors",classSelectors:"Class selectors",universalSelectors:"Universal selectors",attributeSelectors:"Attribute selectors",nonsimpleSelectors:"Non-simple selectors",backgroundColorsS:"Background colors: {PH1}",textColorsS:"Text colors: {PH1}",fillColorsS:"Fill colors: {PH1}",borderColorsS:"Border colors: {PH1}",thereAreNoFonts:"There are no fonts.",thereAreNoUnusedDeclarations:"There are no unused declarations.",thereAreNoMediaQueries:"There are no media queries.",contrastIssues:"Contrast issues",nOccurrences:"{n, plural, =1 {# occurrence} other {# occurrences}}",contrastIssuesS:"Contrast issues: {PH1}",textColorSOverSBackgroundResults:"Text color {PH1} over {PH2} background results in low contrast for {PH3} elements",aa:"AA",aaa:"AAA",apca:"APCA",element:"Element",declaration:"Declaration",source:"Source",contrastRatio:"Contrast ratio",cssOverviewElements:"CSS Overview Elements",showElement:"Show element"},v=s.i18n.registerUIStrings("panels/css_overview/CSSOverviewCompletedView.ts",m),b=s.i18n.getLocalizedString.bind(void 0,v);function C(e){let[t,n,o]=e.hsla();return t=Math.round(360*t),n=Math.round(100*n),o=Math.round(100*o),o=Math.max(0,o-15),`1px solid hsl(${t}deg ${n}% ${o}%)`}class f extends p.Panel.PanelWithSidebar{#s;#a;#l;#c;#d;#h;#p;#u;#g;#m;#v;#b;constructor(e){super("css_overview_completed_view"),this.#s=e,this.#a=new Intl.NumberFormat("en-US"),this.#l=new p.SplitWidget.SplitWidget(!0,!0),this.#c=new p.Widget.VBox,this.#d=new w,this.#d.addEventListener("TabClosed",(e=>{0===e.data&&this.#l.setSidebarMinimized(!0)})),this.#l.setMainWidget(this.#c),this.#l.setSidebarWidget(this.#d),this.#l.setVertical(!1),this.#l.setSecondIsSidebar(!0),this.#l.setSidebarMinimized(!0),this.#h=new(0,g.CSSOverviewSidebarPanel),this.splitWidget().setSidebarWidget(this.#h),this.splitWidget().setMainWidget(this.#l),this.#g=new h.Linkifier.Linkifier(20,!0),this.#m=new Map,this.#h.addItem(b(m.overviewSummary),"summary"),this.#h.addItem(b(m.colors),"colors"),this.#h.addItem(b(m.fontInfo),"font-info"),this.#h.addItem(b(m.unusedDeclarations),"unused-declarations"),this.#h.addItem(b(m.mediaQueries),"media-queries"),this.#h.select("summary"),this.#h.addEventListener("ItemSelected",this.#C,this),this.#h.addEventListener("Reset",this.#f,this),this.#s.addEventListener("Reset",this.#w,this),this.#s.addEventListener("PopulateNodes",this.#x,this),this.#c.element.addEventListener("click",this.#S.bind(this)),this.#v=null}wasShown(){super.wasShown(),this.#l.registerCSSFiles([u.default]),this.registerCSSFiles([u.default])}initializeModels(e){const t=e.model(l.CSSModel.CSSModel),n=e.model(l.DOMModel.DOMModel);if(!t||!n)throw new Error("Target must provide CSS and DOM models");this.#p=t,this.#u=n}#C(e){const{data:t}=e,n=this.#b.$(t);n&&n.scrollIntoView()}#f(){this.#s.dispatchEventToListeners("Reset")}#w(){this.#c.element.removeChildren(),this.#l.setSidebarMinimized(!0),this.#d.closeTabs(),this.#m=new Map,f.pushedNodes.clear(),this.#h.select("summary")}#S(e){if(!e.target)return;const t=e.target.dataset,n=t.type;if(!n||!this.#v)return;let o;switch(n){case"contrast":{const e=t.section,i=t.key;if(!i)return;o={type:n,key:i,nodes:this.#v.textColorContrastIssues.get(i)||[],section:e};break}case"color":{const e=t.color,i=t.section;if(!e)return;let s;switch(i){case"text":s=this.#v.textColors.get(e);break;case"background":s=this.#v.backgroundColors.get(e);break;case"fill":s=this.#v.fillColors.get(e);break;case"border":s=this.#v.borderColors.get(e)}if(!s)return;s=Array.from(s).map((e=>({nodeId:e}))),o={type:n,color:e,nodes:s,section:i};break}case"unused-declarations":{const e=t.declaration;if(!e)return;const i=this.#v.unusedDeclarations.get(e);if(!i)return;o={type:n,declaration:e,nodes:i};break}case"media-queries":{const e=t.text;if(!e)return;const i=this.#v.mediaQueries.get(e);if(!i)return;o={type:n,text:e,nodes:i};break}case"font-info":{const e=t.value;if(!t.path)return;const[i,s]=t.path.split("/");if(!e)return;const r=this.#v.fontInfo.get(i);if(!r)return;const a=r.get(s);if(!a)return;const l=a.get(e);if(!l)return;o={type:n,name:`${e} (${i}, ${s})`,nodes:l.map((e=>({nodeId:e})))};break}default:return}e.consume(),this.#s.dispatchEventToListeners("PopulateNodes",{payload:o}),this.#l.setSidebarMinimized(!1)}async#r(e){if(!e||!("backgroundColors"in e)||!("textColors"in e))return;this.#v=e;const{elementCount:t,backgroundColors:n,textColors:o,textColorContrastIssues:i,fillColors:s,borderColors:r,globalStyleStats:a,mediaQueries:l,unusedDeclarations:c,fontInfo:d}=this.#v,h=this.#k(n),u=this.#k(o),g=this.#k(s),v=this.#k(r);this.#b=p.Fragment.Fragment.build`
    <div class="vbox overview-completed-view">
      <div $="summary" class="results-section horizontally-padded summary">
        <h1>${b(m.overviewSummary)}</h1>

        <ul>
          <li>
            <div class="label">${b(m.elements)}</div>
            <div class="value">${this.#a.format(t)}</div>
          </li>
          <li>
            <div class="label">${b(m.externalStylesheets)}</div>
            <div class="value">${this.#a.format(a.externalSheets)}</div>
          </li>
          <li>
            <div class="label">${b(m.inlineStyleElements)}</div>
            <div class="value">${this.#a.format(a.inlineStyles)}</div>
          </li>
          <li>
            <div class="label">${b(m.styleRules)}</div>
            <div class="value">${this.#a.format(a.styleRules)}</div>
          </li>
          <li>
            <div class="label">${b(m.mediaQueries)}</div>
            <div class="value">${this.#a.format(l.size)}</div>
          </li>
          <li>
            <div class="label">${b(m.typeSelectors)}</div>
            <div class="value">${this.#a.format(a.stats.type)}</div>
          </li>
          <li>
            <div class="label">${b(m.idSelectors)}</div>
            <div class="value">${this.#a.format(a.stats.id)}</div>
          </li>
          <li>
            <div class="label">${b(m.classSelectors)}</div>
            <div class="value">${this.#a.format(a.stats.class)}</div>
          </li>
          <li>
            <div class="label">${b(m.universalSelectors)}</div>
            <div class="value">${this.#a.format(a.stats.universal)}</div>
          </li>
          <li>
            <div class="label">${b(m.attributeSelectors)}</div>
            <div class="value">${this.#a.format(a.stats.attribute)}</div>
          </li>
          <li>
            <div class="label">${b(m.nonsimpleSelectors)}</div>
            <div class="value">${this.#a.format(a.stats.nonSimple)}</div>
          </li>
        </ul>
      </div>

      <div $="colors" class="results-section horizontally-padded colors">
        <h1>${b(m.colors)}</h1>
        <h2>${b(m.backgroundColorsS,{PH1:h.length})}</h2>
        <ul>
          ${h.map(this.#y.bind(this,"background"))}
        </ul>

        <h2>${b(m.textColorsS,{PH1:u.length})}</h2>
        <ul>
          ${u.map(this.#y.bind(this,"text"))}
        </ul>

        ${i.size>0?this.#A(i):""}

        <h2>${b(m.fillColorsS,{PH1:g.length})}</h2>
        <ul>
          ${g.map(this.#y.bind(this,"fill"))}
        </ul>

        <h2>${b(m.borderColorsS,{PH1:v.length})}</h2>
        <ul>
          ${v.map(this.#y.bind(this,"border"))}
        </ul>
      </div>

      <div $="font-info" class="results-section font-info">
        <h1>${b(m.fontInfo)}</h1>
        ${d.size>0?this.#I(d):p.Fragment.Fragment.build`<div>${b(m.thereAreNoFonts)}</div>`}
      </div>

      <div $="unused-declarations" class="results-section unused-declarations">
        <h1>${b(m.unusedDeclarations)}</h1>
        ${c.size>0?this.#E(c,"unused-declarations","declaration"):p.Fragment.Fragment.build`<div class="horizontally-padded">${b(m.thereAreNoUnusedDeclarations)}</div>`}
      </div>

      <div $="media-queries" class="results-section media-queries">
        <h1>${b(m.mediaQueries)}</h1>
        ${l.size>0?this.#E(l,"media-queries","text"):p.Fragment.Fragment.build`<div class="horizontally-padded">${b(m.thereAreNoMediaQueries)}</div>`}
      </div>
    </div>`,this.#c.element.appendChild(this.#b.element())}#x(e){const{payload:t}=e.data;let n="",o="";switch(t.type){case"contrast":{const{section:e,key:i}=t;n=`${e}-${i}`,o=b(m.contrastIssues);break}case"color":{const{section:e,color:i}=t;n=`${e}-${i}`,o=`${i.toUpperCase()} (${e})`;break}case"unused-declarations":{const{declaration:e}=t;n=`${e}`,o=`${e}`;break}case"media-queries":{const{text:e}=t;n=`${e}`,o=`${e}`;break}case"font-info":{const{name:e}=t;n=`${e}`,o=`${e}`;break}}let i=this.#m.get(n);if(!i){if(!this.#u||!this.#p)throw new Error("Unable to initialize CSS Overview, missing models");i=new x(this.#s,this.#u,this.#p,this.#g),i.populateNodes(t.nodes),this.#m.set(n,i)}this.#d.appendTab(n,o,i,!0)}#I(e){const t=Array.from(e.entries());return p.Fragment.Fragment.build`
  ${t.map((([e,t])=>p.Fragment.Fragment.build`<section class="font-family"><h2>${e}</h2> ${this.#F(e,t)}</section>`))}
  `}#F(e,t){const n=Array.from(t.entries());return p.Fragment.Fragment.build`
  <div class="font-metric">
  ${n.map((([t,n])=>{const o=`${e}/${t}`;return p.Fragment.Fragment.build`
  <div>
  <h3>${t}</h3>
  ${this.#E(n,"font-info","value",o)}
  </div>`}))}
  </div>`}#E(e,t,n,o=""){const i=Array.from(e.entries()).sort(((e,t)=>{const n=e[1];return t[1].length-n.length})),s=i.reduce(((e,t)=>e+t[1].length),0);return p.Fragment.Fragment.build`<ul>
    ${i.map((([e,i])=>{const r=100*i.length/s,a=b(m.nOccurrences,{n:i.length});return p.Fragment.Fragment.build`<li>
        <div class="title">${e}</div>
        <button data-type="${t}" data-path="${o}" data-${n}="${e}">
          <div class="details">${a}</div>
          <div class="bar-container">
            <div class="bar" style="width: ${r}%;"></div>
          </div>
        </button>
      </li>`}))}
    </ul>`}#A(e){return p.Fragment.Fragment.build`
  <h2>${b(m.contrastIssuesS,{PH1:e.size})}</h2>
  <ul>
  ${[...e.entries()].map((([e,t])=>this.#P(e,t)))}
  </ul>
  `}#P(e,t){console.assert(t.length>0);let n=t[0];for(const e of t)Math.abs(e.contrastRatio)<Math.abs(n.contrastRatio)&&(n=e);const o=n.textColor.asString(i.Color.Format.HEXA),s=n.backgroundColor.asString(i.Color.Format.HEXA),r=a.Runtime.experiments.isEnabled("APCA"),l=p.Fragment.Fragment.build`<li>
      <button
        title="${b(m.textColorSOverSBackgroundResults,{PH1:o,PH2:s,PH3:t.length})}"
        data-type="contrast" data-key="${e}" data-section="contrast" class="block" $="color">
        Text
      </button>
      <div class="block-title">
        <div class="contrast-warning hidden" $="aa"><span class="threshold-label">${b(m.aa)}</span></div>
        <div class="contrast-warning hidden" $="aaa"><span class="threshold-label">${b(m.aaa)}</span></div>
        <div class="contrast-warning hidden" $="apca"><span class="threshold-label">${b(m.apca)}</span></div>
      </div>
    </li>`;if(r){const e=l.$("apca");n.thresholdsViolated.apca?e.appendChild(p.Icon.Icon.create("smallicon-no")):e.appendChild(p.Icon.Icon.create("smallicon-checkmark-square")),e.classList.remove("hidden")}else{const e=l.$("aa");n.thresholdsViolated.aa?e.appendChild(p.Icon.Icon.create("smallicon-no")):e.appendChild(p.Icon.Icon.create("smallicon-checkmark-square"));const t=l.$("aaa");n.thresholdsViolated.aaa?t.appendChild(p.Icon.Icon.create("smallicon-no")):t.appendChild(p.Icon.Icon.create("smallicon-checkmark-square")),e.classList.remove("hidden"),t.classList.remove("hidden")}const c=l.$("color");return c.style.backgroundColor=s,c.style.color=o,c.style.border=C(n.backgroundColor),l}#y(e,t){const n=p.Fragment.Fragment.build`<li>
      <button data-type="color" data-color="${t}" data-section="${e}" class="block" $="color"></button>
      <div class="block-title">${t}</div>
    </li>`,o=n.$("color");o.style.backgroundColor=t;const s=i.Color.Color.parse(t);if(s)return o.style.border=C(s),n}#k(e){return Array.from(e.keys()).sort(((e,t)=>{const n=i.Color.Color.parse(e),o=i.Color.Color.parse(t);return n&&o?i.ColorUtils.luminance(o.rgba())-i.ColorUtils.luminance(n.rgba()):0}))}setOverviewData(e){this.#r(e)}static pushedNodes=new Set}class w extends(i.ObjectWrapper.eventMixin(p.Widget.VBox)){#T;constructor(){super(),this.#T=new p.TabbedPane.TabbedPane,this.#T.show(this.element),this.#T.addEventListener(p.TabbedPane.Events.TabClosed,(()=>{this.dispatchEventToListeners("TabClosed",this.#T.tabIds().length)}))}appendTab(e,t,n,o){this.#T.hasTab(e)||this.#T.appendTab(e,t,n,void 0,void 0,o),this.#T.selectTab(e)}closeTabs(){this.#T.closeTabs(this.#T.tabIds())}}class x extends p.Widget.Widget{#s;#u;#p;#g;#B;#L;constructor(e,t,n,o){super(),this.#s=e,this.#u=t,this.#p=n,this.#g=o,this.#B=[{id:"nodeId",title:b(m.element),sortable:!0,weight:50,titleDOMFragment:void 0,sort:void 0,align:void 0,width:void 0,fixedWidth:void 0,editable:void 0,nonSelectable:void 0,longText:void 0,disclosure:void 0,allowInSortByEvenWhenHidden:void 0,dataType:void 0,defaultWeight:void 0},{id:"declaration",title:b(m.declaration),sortable:!0,weight:50,titleDOMFragment:void 0,sort:void 0,align:void 0,width:void 0,fixedWidth:void 0,editable:void 0,nonSelectable:void 0,longText:void 0,disclosure:void 0,allowInSortByEvenWhenHidden:void 0,dataType:void 0,defaultWeight:void 0},{id:"sourceURL",title:b(m.source),sortable:!1,weight:100,titleDOMFragment:void 0,sort:void 0,align:void 0,width:void 0,fixedWidth:void 0,editable:void 0,nonSelectable:void 0,longText:void 0,disclosure:void 0,allowInSortByEvenWhenHidden:void 0,dataType:void 0,defaultWeight:void 0},{id:"contrastRatio",title:b(m.contrastRatio),sortable:!0,weight:25,titleDOMFragment:void 0,sort:void 0,align:void 0,width:"150px",fixedWidth:!0,editable:void 0,nonSelectable:void 0,longText:void 0,disclosure:void 0,allowInSortByEvenWhenHidden:void 0,dataType:void 0,defaultWeight:void 0}],this.#L=new d.SortableDataGrid.SortableDataGrid({displayName:b(m.cssOverviewElements),columns:this.#B,editCallback:void 0,deleteCallback:void 0,refreshCallback:void 0}),this.#L.element.classList.add("element-grid"),this.#L.element.addEventListener("mouseover",this.#R.bind(this)),this.#L.setStriped(!0),this.#L.addEventListener(d.DataGrid.Events.SortingChanged,this.#D.bind(this)),this.#L.asWidget().show(this.element)}#D(){const e=this.#L.sortColumnId();if(!e)return;const t=d.SortableDataGrid.SortableDataGrid.StringComparator.bind(null,e);this.#L.sortNodes(t,!this.#L.isSortOrderAscending())}#R(e){const t=e.composedPath().find((e=>e.dataset&&e.dataset.backendNodeId));if(!t)return;const n=Number(t.dataset.backendNodeId);this.#s.dispatchEventToListeners("RequestNodeHighlight",n)}async populateNodes(e){if(this.#L.rootNode().removeChildren(),!e.length)return;const[t]=e,n=new Set;let o;if("nodeId"in t&&t.nodeId&&n.add("nodeId"),"declaration"in t&&t.declaration&&n.add("declaration"),"sourceURL"in t&&t.sourceURL&&n.add("sourceURL"),"contrastRatio"in t&&t.contrastRatio&&n.add("contrastRatio"),"nodeId"in t&&n.has("nodeId")){const t=e.reduce(((e,t)=>{const n=t.nodeId;return f.pushedNodes.has(n)?e:(f.pushedNodes.add(n),e.add(n))}),new Set);o=await this.#u.pushNodesByBackendIdsToFrontend(t)}for(const t of e){let e;if("nodeId"in t&&n.has("nodeId")){if(!o)continue;if(e=o.get(t.nodeId),!e)continue}const i=new S(t,e,this.#g,this.#p);i.selectable=!1,this.#L.insertChild(i)}this.#L.setColumnsVisiblity(n),this.#L.renderInline(),this.#L.wasShown()}}class S extends d.SortableDataGrid.SortableDataGridNode{#g;#p;#U;constructor(e,t,n,o){super(e),this.#U=t,this.#g=n,this.#p=o}createCell(e){const t=this.#U;if("nodeId"===e){const n=this.createTD(e);if(n.textContent="...",!t)throw new Error("Node entry is missing a related frontend node.");return i.Linkifier.Linkifier.linkify(t).then((e=>{n.textContent="",e.dataset.backendNodeId=t.backendNodeId().toString(),n.appendChild(e);const o=document.createElement("button");o.classList.add("show-element"),p.Tooltip.Tooltip.install(o,b(m.showElement)),o.tabIndex=0,o.onclick=()=>this.data.node.scrollIntoView(),n.appendChild(o)})),n}if("sourceURL"===e){const t=this.createTD(e);if(this.data.range){const e=this.#H(this.#p,this.#g,this.data.styleSheetId,c.TextRange.TextRange.fromObject(this.data.range));e&&""!==e.textContent?t.appendChild(e):t.textContent="(unable to link)"}else t.textContent="(unable to link to inlined styles)";return t}if("contrastRatio"===e){const t=this.createTD(e),n=a.Runtime.experiments.isEnabled("APCA"),o=r.NumberUtilities.floor(this.data.contrastRatio,2),i=n?o+"%":o,s=C(this.data.backgroundColor),l=this.data.textColor.asString(),c=this.data.backgroundColor.asString(),d=p.Fragment.Fragment.build`
        <div class="contrast-container-in-grid" $="container">
          <span class="contrast-preview" style="border: ${s};
          color: ${l};
          background-color: ${c};">Aa</span>
          <span>${i}</span>
        </div>
      `,h=d.$("container");return n?(h.append(p.Fragment.Fragment.build`<span>${b(m.apca)}</span>`.element()),this.data.thresholdsViolated.apca?h.appendChild(p.Icon.Icon.create("smallicon-no")):h.appendChild(p.Icon.Icon.create("smallicon-checkmark-square"))):(h.append(p.Fragment.Fragment.build`<span>${b(m.aa)}</span>`.element()),this.data.thresholdsViolated.aa?h.appendChild(p.Icon.Icon.create("smallicon-no")):h.appendChild(p.Icon.Icon.create("smallicon-checkmark-square")),h.append(p.Fragment.Fragment.build`<span>${b(m.aaa)}</span>`.element()),this.data.thresholdsViolated.aaa?h.appendChild(p.Icon.Icon.create("smallicon-no")):h.appendChild(p.Icon.Icon.create("smallicon-checkmark-square"))),t.appendChild(d.element()),t}return super.createCell(e)}#H(e,t,n,o){const i=e.styleSheetHeaderForId(n);if(!i)return;const s=i.lineNumberInSource(o.startLine),r=i.columnNumberInSource(o.startLine,o.startColumn),a=new l.CSSModel.CSSLocation(i,s,r);return t.linkifyCSSLocation(a)}}})),t.register("2Stov",(function(t,n){e(t.exports,"default",(()=>i));const o=new CSSStyleSheet;o.replaceSync('/**\n * Copyright 2019 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.overview-completed-view {\n  overflow: auto;\n\n  --overview-default-padding: 28px;\n  --overview-icon-padding: 32px;\n}\n\n.overview-completed-view .summary ul,\n.overview-completed-view .colors ul {\n  display: flex;\n  flex-wrap: wrap;\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n.overview-completed-view .summary ul {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, 140px);\n  grid-gap: 16px;\n}\n\n.overview-completed-view .colors ul li {\n  display: inline-block;\n  margin: 0 0 16px;\n  padding: 0 8px 0 0;\n}\n\n.overview-completed-view .summary ul li {\n  display: flex;\n  flex-direction: column;\n  grid-column-start: auto;\n}\n\n.overview-completed-view li .label {\n  font-size: 12px;\n  padding-bottom: 2px;\n}\n\n.overview-completed-view li .value {\n  font-size: 17px;\n}\n\n.overview-completed-view ul li span {\n  font-weight: bold;\n}\n\n.unused-rules-grid .header-container,\n.unused-rules-grid .data-container,\n.unused-rules-grid table.data {\n  position: relative;\n}\n\n.unused-rules-grid .data-container {\n  top: 0;\n  max-height: 350px;\n}\n\n.unused-rules-grid {\n  border-left: none;\n  border-right: none;\n}\n/** Ensure links are rendered at the correct height */\n\n.unused-rules-grid .monospace {\n  display: block;\n  height: 18px;\n}\n\n.element-grid {\n  flex: 1;\n  border-left: none;\n  border-right: none;\n  overflow: auto;\n}\n\n.block {\n  width: 65px;\n  height: 25px;\n  border-radius: 3px;\n  margin-right: 16px;\n}\n\n.block-title {\n  padding-top: 4px;\n  font-size: 12px;\n  color: var(--color-text-primary);\n  text-transform: uppercase;\n  letter-spacing: 0;\n}\n\n.results-section {\n  flex-shrink: 0;\n  border-bottom: 1px solid var(--color-details-hairline);\n  padding: var(--overview-default-padding) 0 var(--overview-default-padding) 0;\n}\n\n.horizontally-padded {\n  padding-left: var(--overview-default-padding);\n  padding-right: var(--overview-default-padding);\n}\n\n.results-section h1 {\n  font-size: 15px;\n  font-weight: normal;\n  padding: 0;\n  margin: 0 0 20px;\n  padding-left: calc(var(--overview-default-padding) + var(--overview-icon-padding));\n  position: relative;\n  height: 26px;\n  line-height: 26px;\n}\n\n.results-section h1::before {\n  content: "";\n  display: block;\n  position: absolute;\n  left: var(--overview-default-padding);\n  top: 0;\n  width: 26px;\n  height: 26px;\n  background-image: var(--image-file-cssoverview_icons_2x);\n  background-size: 104px 26px;\n}\n\n.results-section.horizontally-padded h1 {\n  padding-left: var(--overview-icon-padding);\n}\n\n.results-section.horizontally-padded h1::before {\n  left: 0;\n}\n\n.results-section.summary h1 {\n  padding-left: 0;\n}\n\n.results-section.summary h1::before {\n  display: none;\n}\n\n.results-section.colors h1::before {\n  background-position: 0 0;\n}\n\n.results-section.font-info h1::before {\n  background-position: -26px 0;\n}\n\n.results-section.unused-declarations h1::before {\n  background-position: -52px 0;\n}\n\n.results-section.media-queries h1::before {\n  background-position: -78px 0;\n}\n\n.results-section.colors h2 {\n  margin-top: 20px;\n  font-size: 13px;\n  font-weight: normal;\n}\n\n.overview-completed-view .font-info ul,\n.overview-completed-view .media-queries ul,\n.overview-completed-view .unused-declarations ul {\n  width: 100%;\n  list-style: none;\n  margin: 0;\n  padding: 0 var(--overview-default-padding);\n}\n\n.overview-completed-view .font-info ul li,\n.overview-completed-view .media-queries ul li,\n.overview-completed-view .unused-declarations ul li {\n  display: grid;\n  grid-template-columns: 2fr 3fr;\n  grid-gap: 12px;\n  margin-bottom: 4px;\n  align-items: center;\n}\n\n.overview-completed-view .font-info button,\n.overview-completed-view .media-queries button,\n.overview-completed-view .unused-declarations button {\n  border: none;\n  padding: 0;\n  margin: 0;\n  display: flex;\n  align-items: center;\n  border-radius: 2px;\n  cursor: pointer;\n  height: 28px;\n  background: none;\n}\n\n.overview-completed-view .font-info button .details,\n.overview-completed-view .media-queries button .details,\n.overview-completed-view .unused-declarations button .details {\n  min-width: 100px;\n  text-align: right;\n  margin-right: 8px;\n  color: var(--color-primary);\n  pointer-events: none;\n}\n\n.overview-completed-view .font-info button .bar-container,\n.overview-completed-view .media-queries button .bar-container,\n.overview-completed-view .unused-declarations button .bar-container {\n  flex: 1;\n  pointer-events: none;\n}\n\n.overview-completed-view .font-info button .bar,\n.overview-completed-view .media-queries button .bar,\n.overview-completed-view .unused-declarations button .bar {\n  height: 8px;\n  background: var(--color-primary);\n  border-radius: 2px;\n  min-width: 2px;\n}\n\n.overview-completed-view .font-info button:hover .details,\n.overview-completed-view .font-info button:focus .details,\n.overview-completed-view .media-queries button:hover .details,\n.overview-completed-view .media-queries button:focus .details,\n.overview-completed-view .unused-declarations button:hover .details,\n.overview-completed-view .unused-declarations button:focus .details {\n  color: var(--color-primary-variant);\n}\n\n.overview-completed-view .font-info button:hover .bar,\n.overview-completed-view .font-info button:focus .bar,\n.overview-completed-view .media-queries button:hover .bar,\n.overview-completed-view .media-queries button:focus .bar,\n.overview-completed-view .unused-declarations button:hover .bar,\n.overview-completed-view .unused-declarations button:focus .bar {\n  background-color: var(--color-primary-variant);\n  box-shadow: 0 1px 2px var(--divider-line), 0 0 0 2px var(--color-primary-variant);\n  color: var(--color-background);\n}\n\n.overview-completed-view .font-info .font-metric {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  grid-gap: 12px;\n}\n\n.overview-completed-view .font-info ul {\n  padding: 0;\n}\n\n.overview-completed-view .font-info ul li {\n  grid-template-columns: 1fr 4fr;\n}\n\n.overview-completed-view .font-info h2 {\n  font-size: 14px;\n  font-weight: bold;\n  margin: 0 0 1em;\n}\n\n.overview-completed-view .font-info h3 {\n  font-size: 13px;\n  font-weight: normal;\n  font-style: italic;\n  margin: 0 0 0.5em;\n}\n\n.overview-completed-view .font-info {\n  padding-bottom: 0;\n}\n\n.overview-completed-view .font-family {\n  padding: var(--overview-default-padding);\n}\n\n.overview-completed-view .font-family:nth-child(2n+1) {\n  background: var(--color-background);\n}\n\n.overview-completed-view .font-family:first-of-type {\n  padding-top: 0;\n}\n\n.contrast-warning {\n  display: flex;\n  align-items: baseline;\n}\n\n.contrast-warning .threshold-label {\n  font-weight: normal;\n  width: 30px;\n}\n\n.contrast-warning [is="ui-icon"] {\n  margin-left: 5px;\n}\n\n.contrast-preview {\n  padding: 0 5px;\n}\n\n.contrast-container-in-grid {\n  display: flex;\n  align-items: baseline;\n}\n\n.contrast-container-in-grid > * {\n  margin-right: 5px;\n  min-width: initial;\n}\n\n[is="ui-icon"].smallicon-checkmark-square {\n  background-color: var(--color-green);\n}\n\n[is="ui-icon"].smallicon-no {\n  background-color: var(--color-red);\n}\n\n.data .nodeId-column {\n  align-items: center;\n  display: flex;\n  height: 20px;\n}\n\n.show-element {\n  margin: 0 0 0 8px;\n  padding: 0;\n  background: none;\n  border: none;\n  -webkit-mask-image: var(--image-file-ic_show_node_16x16);\n  background-color: var(--color-text-secondary);\n  width: 16px;\n  height: 16px;\n  display: none;\n  cursor: pointer;\n  flex: none;\n}\n\n.show-element:focus,\n.show-element:hover {\n  background-color: var(--color-primary);\n}\n\n.nodeId-column:focus-within .show-element,\n.nodeId-column:hover .show-element {\n  display: inline-block;\n}\n\n/*# sourceURL=cssOverviewCompletedView.css */\n');var i=o})),t.register("dnEPG",(function(n,o){e(n.exports,"CSSOverviewSidebarPanel",(()=>h));var i=t("koSS8"),s=t("ixFnt"),r=t("9z2ZV"),a=t("51wuq");const l={clearOverview:"Clear overview"},c=s.i18n.registerUIStrings("panels/css_overview/CSSOverviewSidebarPanel.ts",l),d=s.i18n.getLocalizedString.bind(void 0,c);class h extends(i.ObjectWrapper.eventMixin(r.Widget.VBox)){static get ITEM_CLASS_NAME(){return"overview-sidebar-panel-item"}static get SELECTED(){return"selected"}constructor(){super(!0),this.contentElement.classList.add("overview-sidebar-panel"),this.contentElement.addEventListener("click",this.#M.bind(this));const e=new r.Toolbar.ToolbarButton(d(l.clearOverview),"largeicon-clear");e.addEventListener(r.Toolbar.ToolbarButton.Events.Click,this.#w,this);const t=this.contentElement.createChild("div","overview-toolbar");new r.Toolbar.Toolbar("",t).appendToolbarItem(e)}addItem(e,t){const n=this.contentElement.createChild("div",h.ITEM_CLASS_NAME);n.textContent=e,n.dataset.id=t}#w(){this.dispatchEventToListeners("Reset")}#$(){this.contentElement.querySelectorAll(`.${h.ITEM_CLASS_NAME}`).forEach((e=>{e.classList.remove(h.SELECTED)}))}#M(e){const t=e.composedPath()[0];if(!t.classList.contains(h.ITEM_CLASS_NAME))return;const{id:n}=t.dataset;n&&(this.select(n),this.dispatchEventToListeners("ItemSelected",n))}select(e){const t=this.contentElement.querySelector(`[data-id=${CSS.escape(e)}]`);t&&(t.classList.contains(h.SELECTED)||(this.#$(),t.classList.add(h.SELECTED)))}wasShown(){super.wasShown(),this.registerCSSFiles([a.default])}}})),t.register("51wuq",(function(t,n){e(t.exports,"default",(()=>i));const o=new CSSStyleSheet;o.replaceSync("/**\n * Copyright 2019 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.overview-sidebar-panel {\n  overflow: auto;\n  display: flex;\n  background: var(--color-background-elevation-1);\n}\n\n.overview-sidebar-panel-item {\n  height: 30px;\n  padding-left: 30px;\n  display: flex;\n  align-items: center;\n  cursor: pointer;\n  color: var(--color-text-primary);\n}\n\n.overview-sidebar-panel-item:hover,\n.overview-sidebar-panel-item:focus {\n  background: var(--color-background-highlight);\n}\n\n.overview-sidebar-panel-item.selected {\n  background: var(--color-primary);\n  color: var(--color-background);\n}\n\n.overview-toolbar {\n  border-bottom: 1px solid var(--color-details-hairline);\n}\n\n/*# sourceURL=cssOverviewSidebarPanel.css */\n");var i=o})),t.register("hjEJK",(function(n,o){e(n.exports,"CSSOverviewPanel",(()=>g)),t("d5H6j");var i=t("0JDfs"),s=t("ekjNI"),r=t("e7bLS"),a=t("eQFvP"),l=t("9z2ZV"),c=t("bnRQQ"),d=t("jVHKV"),h=t("1rQgP"),p=t("j1L5M");let u;class g extends l.Panel.Panel{#s;#V;#O;#N;#z;#W;#G;#q;#X;#j;#_;#Q;#K;#Y;#Z;#J;constructor(){super("css_overview"),this.element.classList.add("css-overview-panel"),this.#s=new(0,d.OverviewController),this.#V=new i.CSSOverviewStartView,this.#V.addEventListener("overviewstartrequested",(()=>this.#s.dispatchEventToListeners("RequestOverviewStart"))),this.#O=new(0,p.CSSOverviewProcessingView)(this.#s),this.#N=new(0,c.CSSOverviewCompletedView)(this.#s),a.TargetManager.TargetManager.instance().observeTargets(this),this.#s.addEventListener("RequestOverviewStart",(e=>{r.userMetrics.actionTaken(r.UserMetrics.Action.CaptureCssOverviewClicked),this.#ee()}),this),this.#s.addEventListener("OverviewCompleted",this.#te,this),this.#s.addEventListener("Reset",this.#w,this),this.#s.addEventListener("RequestNodeHighlight",this.#ne,this),this.#w()}static instance(){return u||(u=new g),u}targetAdded(e){if(this.#W)return;this.#W=e,this.#N.initializeModels(e);const[t]=a.TargetManager.TargetManager.instance().models(h.CSSOverviewModel);this.#z=t}targetRemoved(){}#oe(){if(!this.#z)throw new Error("Did not retrieve model information yet.");return this.#z}#w(){this.#G=new Map,this.#q=new Map,this.#X=new Map,this.#j=new Map,this.#_=new Map,this.#Q=new Map,this.#K=new Map,this.#Y=0,this.#Z={styleRules:0,inlineStyles:0,externalSheets:0,stats:{type:0,class:0,id:0,universal:0,attribute:0,nonSimple:0}},this.#J=new Map,this.#ie()}#ne(e){this.#oe().highlightNode(e.data)}#ie(){this.#O.hideWidget(),this.#N.hideWidget(),this.contentElement.append(this.#V),this.#V.show()}#se(){this.#V.hide(),this.#N.hideWidget(),this.#O.show(this.contentElement)}#re(){this.#V.hide(),this.#O.hideWidget(),this.#N.show(this.contentElement),this.#N.setOverviewData({backgroundColors:this.#G,textColors:this.#q,textColorContrastIssues:this.#J,fillColors:this.#X,borderColors:this.#j,globalStyleStats:this.#Z,fontInfo:this.#_,elementCount:this.#Y,mediaQueries:this.#Q,unusedDeclarations:this.#K})}async#ee(){this.#se();const e=this.#oe(),[t,{elementCount:n,backgroundColors:o,textColors:i,textColorContrastIssues:s,fillColors:r,borderColors:a,fontInfo:l,unusedDeclarations:c},d]=await Promise.all([e.getGlobalStylesheetStats(),e.getNodeStyleStats(),e.getMediaQueries()]);n&&(this.#Y=n),t&&(this.#Z=t),d&&(this.#Q=d),o&&(this.#G=o),i&&(this.#q=i),s&&(this.#J=s),r&&(this.#X=r),a&&(this.#j=a),l&&(this.#_=l),c&&(this.#K=c),this.#s.dispatchEventToListeners("OverviewCompleted")}#te(){this.#re()}wasShown(){super.wasShown(),this.registerCSSFiles([s.default])}}})),t.register("d5H6j",(function(n,o){e(n.exports,"CSSOverviewStartView",(()=>t("0JDfs")));t("0JDfs")})),t.register("0JDfs",(function(n,o){e(n.exports,"OverviewStartRequestedEvent",(()=>b)),e(n.exports,"CSSOverviewStartView",(()=>C));var i=t("ixFnt"),s=t("9apcb"),r=t("kpUjp");t("lgud6");var a=t("hFANn"),l=t("hfubJ"),c=t("dS5IF"),d=t("7h3ri");const h={captureOverview:"Capture overview",identifyCSSImprovements:"Identify potential CSS improvements",capturePageCSSOverview:"Capture an overview of your page’s CSS",identifyCSSImprovementsWithExampleIssues:"Identify potential CSS improvements (e.g. low contrast issues, unused declarations, color or font mismatches)",locateAffectedElements:"Locate the affected elements in the Elements panel",quickStartWithCSSOverview:"Quick start: get started with the new CSS Overview panel"},p=i.i18n.registerUIStrings("panels/css_overview/components/CSSOverviewStartView.ts",h),u=i.i18n.getLocalizedString.bind(void 0,p),{render:g,html:m}=c,v="https://goo.gle/css-overview-feedback";class b extends Event{static eventName="overviewstartrequested";constructor(){super(b.eventName)}}class C extends HTMLElement{static litTagName=c.literal`devtools-css-overview-start-view`;#ae=this.attachShadow({mode:"open"});connectedCallback(){this.#ae.adoptedStyleSheets=[d.default],this.#r()}show(){this.classList.remove("hidden")}hide(){this.classList.add("hidden")}#le(){this.dispatchEvent(new b)}#r(){g(m`
      <div class="css-overview-start-view">
        <h1 class="summary-header">${u(h.identifyCSSImprovements)}</h1>
        <ol class="summary-list">
          <li>${u(h.capturePageCSSOverview)}</li>
          <li>${u(h.identifyCSSImprovementsWithExampleIssues)}</li>
          <li>${u(h.locateAffectedElements)}</li>
        </ol>
        <div class="start-capture-wrapper">
          <${s.Button.Button.litTagName}
            class="start-capture"
            .variant=${"primary"}
            @click=${this.#le}>
            ${u(h.captureOverview)}
          </${s.Button.Button.litTagName}>
        </div>
        <${a.PanelFeedback.litTagName} .data=${{feedbackUrl:v,quickStartUrl:"https://developer.chrome.com/docs/devtools/css-overview",quickStartLinkText:u(h.quickStartWithCSSOverview)}}>
        </${a.PanelFeedback.litTagName}>
        <${l.FeedbackButton.litTagName} .data=${{feedbackUrl:v}}>
        </${l.FeedbackButton.litTagName}>
      </div>
    `,this.#ae,{host:this});const e=this.#ae.querySelector(".start-capture");e&&e.focus()}}r.CustomElements.defineComponent("devtools-css-overview-start-view",C)})),t.register("lgud6",(function(n,o){e(n.exports,"FeedbackButton",(()=>t("hfubJ"))),e(n.exports,"PanelFeedback",(()=>t("hFANn")));t("hfubJ"),t("hFANn"),t("9o6pL")})),t.register("hfubJ",(function(n,o){e(n.exports,"FeedbackButton",(()=>u));var i=t("e7bLS"),s=t("ixFnt"),r=t("kpUjp"),a=t("dS5IF"),l=t("9apcb");const c={feedback:"Feedback"},d=s.i18n.registerUIStrings("ui/components/panel_feedback/FeedbackButton.ts",c),h=s.i18n.getLocalizedString.bind(void 0,d),p=new URL(t("5TLXw")).toString();class u extends HTMLElement{static litTagName=a.literal`devtools-feedback-button`;#ae=this.attachShadow({mode:"open"});#ce=this.#r.bind(this);#de={feedbackUrl:""};set data(e){this.#de=e,r.ScheduledRender.scheduleRender(this,this.#ce)}#he(){i.InspectorFrontendHost.InspectorFrontendHostInstance.openInNewTab(this.#de.feedbackUrl)}#r(){if(!r.ScheduledRender.isScheduledRender(this))throw new Error("FeedbackButton render was not scheduled");a.render(a.html`
      <${l.Button.Button.litTagName}
          @click=${this.#he}
          .iconUrl=${p}
          .variant=${"secondary"}
      >${h(c.feedback)}</${l.Button.Button.litTagName}>
      `,this.#ae,{host:this})}}r.CustomElements.defineComponent("devtools-feedback-button",u)})),t.register("5TLXw",(function(e,n){e.exports=new URL(t("27Lyk").resolve("dsAHw"),import.meta.url).toString()})),t.register("hFANn",(function(n,o){e(n.exports,"PanelFeedback",(()=>g));var i=t("ixFnt"),s=t("kpUjp"),r=t("dS5IF"),a=t("cY3yZ"),l=t("aXY4Q");const c={previewText:"Our team is actively working on this feature and we would love to know what you think.",previewTextFeedbackLink:"Send us your feedback.",previewFeature:"Preview feature",videoAndDocumentation:"Video and documentation"},d=i.i18n.registerUIStrings("ui/components/panel_feedback/PanelFeedback.ts",c),h=i.i18n.getLocalizedString.bind(void 0,d),p=new URL(t("5dACx")).toString(),u=new URL(t("63iWL")).toString();class g extends HTMLElement{static litTagName=r.literal`devtools-panel-feedback`;#ae=this.attachShadow({mode:"open"});#ce=this.#r.bind(this);#de={feedbackUrl:"",quickStartUrl:"",quickStartLinkText:""};connectedCallback(){this.#ae.adoptedStyleSheets=[l.default]}set data(e){this.#de=e,s.ScheduledRender.scheduleRender(this,this.#ce)}#r(){if(!s.ScheduledRender.isScheduledRender(this))throw new Error("PanelFeedback render was not scheduled");r.render(r.html`
      <div class="preview">
        <h2 class="flex">
          <${a.Icon.Icon.litTagName} .data=${{iconPath:p,width:"24px",height:"24px",color:"var(--color-primary)"}}></${a.Icon.Icon.litTagName}> ${h(c.previewFeature)}
        </h2>
        <p>${h(c.previewText)} <x-link href=${this.#de.feedbackUrl}>${h(c.previewTextFeedbackLink)}</x-link></p>
        <div class="video">
          <div class="thumbnail">
            <img src=${u} role="presentation" />
          </div>
          <div class="video-description">
            <h3>${h(c.videoAndDocumentation)}</h3>
            <x-link class="quick-start-link" href=${this.#de.quickStartUrl}>${this.#de.quickStartLinkText}</x-link>
          </div>
        </div>
      </div>
      `,this.#ae,{host:this})}}s.CustomElements.defineComponent("devtools-panel-feedback",g)})),t.register("aXY4Q",(function(t,n){e(t.exports,"default",(()=>i));const o=new CSSStyleSheet;o.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n/**\n * Copyright 2021 Google LLC. All rights reserved.\n */\n\n:host {\n  display: block;\n}\n\n.preview {\n  padding: 12px 16px;\n  border: 1px solid var(--color-details-hairline);\n  color: var(--color-text-primary);\n  font-size: 13px;\n  line-height: 20px;\n  border-radius: 12px;\n  margin: 42px 0;\n  letter-spacing: 0.01em;\n}\n\nh2 {\n  color: var(--color-primary);\n  font-size: 13px;\n  line-height: 20px;\n  letter-spacing: 0.01em;\n  margin: 9px 0 14px;\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  font-weight: normal;\n}\n\nh3 {\n  font-size: 13px;\n  line-height: 20px;\n  letter-spacing: 0.04em;\n  color: var(--color-text-primary);\n  margin-bottom: 2px;\n  font-weight: normal;\n}\n\n.preview p {\n  margin-bottom: 24px;\n}\n\n.thumbnail {\n  height: 92px;\n}\n\n.video {\n  display: flex;\n  flex-flow: row wrap;\n  gap: 20px;\n}\n\nx-link { /* stylelint-disable-line selector-type-no-unknown */\n  color: var(--color-primary);\n  text-decoration-line: underline;\n}\n\nx-link.quick-start-link { /* stylelint-disable-line selector-type-no-unknown */\n  font-size: 14px;\n  line-height: 22px;\n  letter-spacing: 0.04em;\n}\n\n.video-description {\n  min-width: min-content;\n  flex-basis: min-content;\n  flex-grow: 1;\n}\n\n/*# sourceURL=panelFeedback.css */\n");var i=o})),t.register("5dACx",(function(e,n){e.exports=new URL(t("27Lyk").resolve("gRFRs"),import.meta.url).toString()})),t.register("63iWL",(function(e,n){e.exports=new URL(t("27Lyk").resolve("8gC0S"),import.meta.url).toString()})),t.register("9o6pL",(function(e,n){var o=t("ixFnt"),i=t("9X2mn"),s=t("dS5IF"),r=t("kpUjp"),a=t("cY3yZ"),l=t("lzcO5"),c=t("kEKH6");const{render:d,html:h,nothing:p}=s,u={previewTextFeedbackLink:"Send us your feedback."},g=o.i18n.registerUIStrings("ui/components/panel_feedback/PreviewToggle.ts",u),m=o.i18n.getLocalizedString.bind(void 0,g);class v extends HTMLElement{static litTagName=s.literal`devtools-preview-toggle`;#ae=this.attachShadow({mode:"open"});#pe="";#ue=null;#ge=null;#me="";#ve;connectedCallback(){this.#ae.adoptedStyleSheets=[l.checkboxStyles,c.default]}set data(e){this.#pe=e.name,this.#ue=e.helperText,this.#ge=e.feedbackURL,this.#me=e.experiment,this.#ve=e.onChangeCallback,this.#r()}#r(){const e=i.Runtime.experiments.isEnabled(this.#me);d(h`
      <div class="experiment-preview">
        <input type="checkbox" ?checked=${e} @change=${this.#be} aria-label=${this.#pe}/>
        <${a.Icon.Icon.litTagName} .data=${{iconName:"ic_preview_feature",width:"16px",height:"16px",color:"var(--color-text-secondary)"}}>
        </${a.Icon.Icon.litTagName}>${this.#pe}
      </div>
      <div class="helper">
        ${this.#ue&&this.#ge?h`<p>${this.#ue} <x-link href=${this.#ge}>${m(u.previewTextFeedbackLink)}</x-link></p>`:p}
      </div>`,this.#ae,{host:this})}#be(e){const t=e.target.checked;i.Runtime.experiments.setEnabled(this.#me,t),this.#ve?.(t)}}r.CustomElements.defineComponent("devtools-preview-toggle",v)})),t.register("kEKH6",(function(t,n){e(t.exports,"default",(()=>i));const o=new CSSStyleSheet;o.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  display: block;\n}\n\n.experiment-preview {\n  display: flex;\n  align-items: center;\n}\n\n.helper {\n  text-align: center;\n  font-style: italic;\n}\n\nx-link { /* stylelint-disable-line selector-type-no-unknown */\n  color: var(--color-primary);\n  text-decoration-line: underline;\n}\n\n/*# sourceURL=previewToggle.css */\n");var i=o})),t.register("7h3ri",(function(t,n){e(t.exports,"default",(()=>i));const o=new CSSStyleSheet;o.replaceSync("/**\n * Copyright 2019 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\nh1 {\n  font-weight: normal;\n}\n\n.css-overview-start-view {\n  padding: 24px;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  background-color: var(--color-background);\n  overflow: auto;\n}\n\n.summary-header {\n  color: var(--color-text-primary);\n  font-size: 18px;\n  font-weight: normal;\n  letter-spacing: 0.02em;\n  line-height: 1.33;\n  margin: 0;\n  padding: 0;\n}\n\n.summary-list {\n  counter-reset: custom-counter;\n  list-style: none;\n  margin: 16px 0 30px 30px;\n  padding: 0;\n}\n\n.summary-list li {\n  color: var(--color-text-primary);\n  counter-increment: custom-counter;\n  font-size: 13px;\n  letter-spacing: 0.03em;\n  line-height: 1.54;\n  margin-bottom: 9px;\n  position: relative;\n}\n\n.summary-list li::before {\n  --override-color-counter-background: rgba(26 115 232 / 25%);\n\n  box-sizing: border-box;\n  background: var(--override-color-counter-background);\n  border-radius: 50%;\n  color: var(--color-primary);\n  content: counter(custom-counter);\n  font-size: 12px;\n  height: 18px;\n  left: -30px;\n  line-height: 20px;\n  position: absolute;\n  text-align: center;\n  top: 0;\n  width: 18px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.start-capture-wrapper {\n  width: fit-content;\n}\n\n.preview-feature {\n  padding: 12px 16px;\n  border: 1px solid var(--color-details-hairline);\n  color: var(--color-text-primary);\n  font-size: 13px;\n  line-height: 20px;\n  border-radius: 12px;\n  margin: 42px 0;\n  letter-spacing: 0.01em;\n}\n\n.preview-header {\n  color: var(--color-primary);\n  font-size: 13px;\n  line-height: 20px;\n  letter-spacing: 0.01em;\n  margin: 9px 0 14px;\n}\n\n.preview-icon {\n  vertical-align: middle;\n}\n\n.feedback-prompt {\n  margin-bottom: 24px;\n}\n\n.feedback-prompt .devtools-link {\n  color: -webkit-link;\n  cursor: pointer;\n  text-decoration: underline;\n}\n\n.resources {\n  display: flex;\n  flex-direction: row;\n}\n\n.thumbnail-wrapper {\n  width: 144px;\n  height: 92px;\n  margin-right: 20px;\n}\n\n.video-doc-header {\n  font-size: 13px;\n  line-height: 20px;\n  letter-spacing: 0.04em;\n  color: var(--color-text-primary);\n  margin-bottom: 2px;\n}\n\ndevtools-feedback-button {\n  align-self: flex-end;\n}\n\n.resources .devtools-link {\n  font-size: 14px;\n  line-height: 22px;\n  letter-spacing: 0.04em;\n  text-decoration-line: underline;\n  color: var(--color-primary);\n}\n\n/*# sourceURL=cssOverviewStartView.css */\n");var i=o})),t.register("ekjNI",(function(t,n){e(t.exports,"default",(()=>i));const o=new CSSStyleSheet;o.replaceSync("/**\n * Copyright 2019 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.css-overview-panel {\n  overflow: hidden;\n}\n\n/*# sourceURL=cssOverview.css */\n");var i=o}));
//# sourceMappingURL=css_overview.41ddd216.js.map
