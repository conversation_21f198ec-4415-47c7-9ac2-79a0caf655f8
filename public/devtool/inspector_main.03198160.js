function e(e,t,n,a){Object.defineProperty(e,t,{get:n,set:a,enumerable:!0,configurable:!0})}var t=("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}).parcelRequire20b5;t.register("3yaSJ",(function(n,a){e(n.exports,"InspectorMain",(()=>t("aLuEe"))),e(n.exports,"RenderingOptions",(()=>t("4h5Kp")));t("4h5Kp"),t("aLuEe"),t("aLuEe"),t("4h5Kp")})),t.register("4h5Kp",(function(n,a){e(n.exports,"RenderingOptionsView",(()=>u));var s=t("koSS8"),i=t("ixFnt"),o=t("9z2ZV"),r=t("3MfvG");const c={paintFlashing:"Paint flashing",highlightsAreasOfThePageGreen:"Highlights areas of the page (green) that need to be repainted. May not be suitable for people prone to photosensitive epilepsy.",layoutShiftRegions:"Layout Shift Regions",highlightsAreasOfThePageBlueThat:"Highlights areas of the page (blue) that were shifted. May not be suitable for people prone to photosensitive epilepsy.",layerBorders:"Layer borders",showsLayerBordersOrangeoliveAnd:"Shows layer borders (orange/olive) and tiles (cyan).",frameRenderingStats:"Frame Rendering Stats",plotsFrameThroughputDropped:"Plots frame throughput, dropped frames distribution, and GPU memory.",scrollingPerformanceIssues:"Scrolling performance issues",highlightsElementsTealThatCan:"Highlights elements (teal) that can slow down scrolling, including touch & wheel event handlers and other main-thread scrolling situations.",highlightAdFrames:"Highlight ad frames",highlightsFramesRedDetectedToBe:"Highlights frames (red) detected to be ads.",coreWebVitals:"Core Web Vitals",showsAnOverlayWithCoreWebVitals:"Shows an overlay with Core Web Vitals.",disableLocalFonts:"Disable local fonts",disablesLocalSourcesInFontface:"Disables `local()` sources in `@font-face` rules. Requires a page reload to apply.",emulateAFocusedPage:"Emulate a focused page",emulatesAFocusedPage:"Emulates a focused page.",emulateAutoDarkMode:"Enable automatic dark mode",emulatesAutoDarkMode:"Enables automatic dark mode and sets `prefers-color-scheme` to `dark`.",forcesMediaTypeForTestingPrint:"Forces media type for testing print and screen styles",forcesCssPreferscolorschemeMedia:"Forces CSS `prefers-color-scheme` media feature",forcesCssPrefersreducedmotion:"Forces CSS `prefers-reduced-motion` media feature",forcesCssPreferscontrastMedia:"Forces CSS `prefers-contrast` media feature",forcesCssPrefersreduceddataMedia:"Forces CSS `prefers-reduced-data` media feature",forcesCssColorgamutMediaFeature:"Forces CSS `color-gamut` media feature",forcesVisionDeficiencyEmulation:"Forces vision deficiency emulation",disableAvifImageFormat:"Disable `AVIF` image format",disableJpegXlImageFormat:"Disable `JPEG XL` image format",requiresAPageReloadToApplyAnd:"Requires a page reload to apply and disables caching for image requests.",disableWebpImageFormat:"Disable `WebP` image format",forcesCssForcedColors:"Forces CSS forced-colors media feature"},l=i.i18n.registerUIStrings("entrypoints/inspector_main/RenderingOptions.ts",c),d=i.i18n.getLocalizedString.bind(void 0,l);let g;class u extends o.Widget.VBox{constructor(){super(!0),this.#e(d(c.paintFlashing),d(c.highlightsAreasOfThePageGreen),s.Settings.Settings.instance().moduleSetting("showPaintRects")),this.#e(d(c.layoutShiftRegions),d(c.highlightsAreasOfThePageBlueThat),s.Settings.Settings.instance().moduleSetting("showLayoutShiftRegions")),this.#e(d(c.layerBorders),d(c.showsLayerBordersOrangeoliveAnd),s.Settings.Settings.instance().moduleSetting("showDebugBorders")),this.#e(d(c.frameRenderingStats),d(c.plotsFrameThroughputDropped),s.Settings.Settings.instance().moduleSetting("showFPSCounter")),this.#e(d(c.scrollingPerformanceIssues),d(c.highlightsElementsTealThatCan),s.Settings.Settings.instance().moduleSetting("showScrollBottleneckRects")),this.#e(d(c.highlightAdFrames),d(c.highlightsFramesRedDetectedToBe),s.Settings.Settings.instance().moduleSetting("showAdHighlights")),this.#e(d(c.coreWebVitals),d(c.showsAnOverlayWithCoreWebVitals),s.Settings.Settings.instance().moduleSetting("showWebVitals")),this.#e(d(c.disableLocalFonts),d(c.disablesLocalSourcesInFontface),s.Settings.Settings.instance().moduleSetting("localFontsDisabled")),this.#e(d(c.emulateAFocusedPage),d(c.emulatesAFocusedPage),s.Settings.Settings.instance().moduleSetting("emulatePageFocus")),this.#e(d(c.emulateAutoDarkMode),d(c.emulatesAutoDarkMode),s.Settings.Settings.instance().moduleSetting("emulateAutoDarkMode")),this.contentElement.createChild("div").classList.add("panel-section-separator"),this.#t(d(c.forcesCssPreferscolorschemeMedia),s.Settings.Settings.instance().moduleSetting("emulatedCSSMediaFeaturePrefersColorScheme")),this.#t(d(c.forcesMediaTypeForTestingPrint),s.Settings.Settings.instance().moduleSetting("emulatedCSSMedia")),this.#t(d(c.forcesCssForcedColors),s.Settings.Settings.instance().moduleSetting("emulatedCSSMediaFeatureForcedColors")),(()=>{const e="(prefers-contrast)";return window.matchMedia(e).media===e})()&&this.#t(d(c.forcesCssPreferscontrastMedia),s.Settings.Settings.instance().moduleSetting("emulatedCSSMediaFeaturePrefersContrast")),this.#t(d(c.forcesCssPrefersreducedmotion),s.Settings.Settings.instance().moduleSetting("emulatedCSSMediaFeaturePrefersReducedMotion")),(()=>{const e="(prefers-reduced-data)";return window.matchMedia(e).media===e})()&&this.#t(d(c.forcesCssPrefersreduceddataMedia),s.Settings.Settings.instance().moduleSetting("emulatedCSSMediaFeaturePrefersReducedData")),this.#t(d(c.forcesCssColorgamutMediaFeature),s.Settings.Settings.instance().moduleSetting("emulatedCSSMediaFeatureColorGamut")),this.contentElement.createChild("div").classList.add("panel-section-separator"),this.#t(d(c.forcesVisionDeficiencyEmulation),s.Settings.Settings.instance().moduleSetting("emulatedVisionDeficiency")),this.contentElement.createChild("div").classList.add("panel-section-separator"),this.#e(d(c.disableAvifImageFormat),d(c.requiresAPageReloadToApplyAnd),s.Settings.Settings.instance().moduleSetting("avifFormatDisabled"));const e=this.#e(d(c.disableWebpImageFormat),d(c.requiresAPageReloadToApplyAnd),s.Settings.Settings.instance().moduleSetting("webpFormatDisabled"));this.contentElement.createChild("div").classList.add("panel-section-separator"),(async()=>new Promise((e=>{const t=document.createElement("img");t.onload=()=>e(!0),t.onerror=()=>e(!1),t.src="data:image/jxl;base64,/wr/BwiDBAwASyAY"})))().then((t=>{t&&e.before(this.#n(d(c.disableJpegXlImageFormat),d(c.requiresAPageReloadToApplyAnd),s.Settings.Settings.instance().moduleSetting("jpegXlFormatDisabled")))}))}static instance(e={forceNew:null}){const{forceNew:t}=e;return g&&!t||(g=new u),g}#n(e,t,n){const a=o.UIUtils.CheckboxLabel.create(e,!1,t);return o.SettingsUI.bindCheckbox(a.checkboxElement,n),a}#e(e,t,n){const a=this.#n(e,t,n);return this.contentElement.appendChild(a),a}#t(e,t){const n=o.SettingsUI.createControlForSetting(t,e);n&&this.contentElement.appendChild(n)}wasShown(){super.wasShown(),this.registerCSSFiles([r.default])}}})),t.register("3MfvG",(function(t,n){e(t.exports,"default",(()=>s));const a=new CSSStyleSheet;a.replaceSync('/*\n * Copyright (c) 2015 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  padding: 12px;\n}\n\n[is="dt-checkbox"] {\n  margin: 0 0 10px;\n  flex: none;\n}\n\n.panel-section-separator {\n  height: 1px;\n  margin-bottom: 10px;\n  background: var(--color-details-hairline);\n  flex: none;\n}\n\n.panel-section-separator:last-child {\n  background: transparent;\n}\n\n.chrome-select-label {\n  margin-bottom: 16px;\n}\n\n/*# sourceURL=renderingOptions.css */\n');var s=a})),t.register("aLuEe",(function(n,a){e(n.exports,"InspectorMainImpl",(()=>F)),e(n.exports,"SourcesPanelIndicator",(()=>A)),e(n.exports,"BackendSettingsSync",(()=>v)),e(n.exports,"ReloadActionDelegate",(()=>T)),e(n.exports,"FocusDebuggeeActionDelegate",(()=>y)),e(n.exports,"NodeIndicator",(()=>w));var s=t("koSS8"),i=t("e7bLS"),o=t("ixFnt"),r=t("9X2mn"),c=t("eQFvP"),l=t("8fwyZ"),d=t("a3yig"),g=t("9z2ZV"),u=t("cKnJs");const h={main:"Main",javascriptIsDisabled:"JavaScript is disabled",openDedicatedTools:"Open dedicated DevTools for `Node.js`"},p=o.i18n.registerUIStrings("entrypoints/inspector_main/InspectorMain.ts",h),m=o.i18n.getLocalizedString.bind(void 0,p);let S,f,b,C;class F{static instance(e={forceNew:null}){const{forceNew:t}=e;return S&&!t||(S=new F),S}async run(){let e=!0;await c.Connections.initMainConnection((async()=>{const t=r.Runtime.Runtime.queryParam("v8only")?c.Target.Type.Node:c.Target.Type.Frame,n=t===c.Target.Type.Frame&&"sources"===r.Runtime.Runtime.queryParam("panel"),a=c.TargetManager.TargetManager.instance().createTarget("main",m(h.main),t,null,void 0,n);if(e){if(e=!1,n){const e=a.model(c.DebuggerModel.DebuggerModel);e&&(e.isReadyToPause()||await e.once(c.DebuggerModel.Events.DebuggerIsReadyToPause),e.pause())}a.runtimeAgent().invoke_runIfWaitingForDebugger()}}),d.TargetDetachedDialog.TargetDetachedDialog.webSocketConnectionLost),new A,new v,new l.NetworkPanelIndicator.NetworkPanelIndicator,i.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(i.InspectorFrontendHostAPI.Events.ReloadInspectedPage,(({data:e})=>{c.ResourceTreeModel.ResourceTreeModel.reloadAllPages(e)}))}}s.Runnable.registerEarlyInitializationRunnable(F.instance);class T{static instance(e={forceNew:null}){const{forceNew:t}=e;return f&&!t||(f=new T),f}handleAction(e,t){switch(t){case"inspector_main.reload":return c.ResourceTreeModel.ResourceTreeModel.reloadAllPages(!1),!0;case"inspector_main.hard-reload":return c.ResourceTreeModel.ResourceTreeModel.reloadAllPages(!0),!0}return!1}}class y{static instance(e={forceNew:null}){const{forceNew:t}=e;return b&&!t||(b=new y),b}handleAction(e,t){const n=c.TargetManager.TargetManager.instance().mainTarget();return!!n&&(n.pageAgent().invoke_bringToFront(),!0)}}class w{#a;#s;constructor(){const e=document.createElement("div"),t=g.Utils.createShadowRootWithCoreStyles(e,{cssFile:[u.default],delegatesFocus:void 0});this.#a=t.createChild("div","node-icon"),e.addEventListener("click",(()=>i.InspectorFrontendHost.InspectorFrontendHostInstance.openNodeFrontend()),!1),this.#s=new g.Toolbar.ToolbarItem(e),this.#s.setTitle(m(h.openDedicatedTools)),c.TargetManager.TargetManager.instance().addEventListener(c.TargetManager.Events.AvailableTargetsChanged,(e=>this.#i(e.data))),this.#s.setVisible(!1),this.#i([])}static instance(e={forceNew:null}){const{forceNew:t}=e;return C&&!t||(C=new w),C}#i(e){const t=Boolean(e.find((e=>"node"===e.type&&!e.attached)));this.#a.classList.toggle("inactive",!t),t&&this.#s.setVisible(!0)}item(){return this.#s}}class A{constructor(){function e(){let e=null;s.Settings.Settings.instance().moduleSetting("javaScriptDisabled").get()&&(e=g.Icon.Icon.create("smallicon-warning"),g.Tooltip.Tooltip.install(e,m(h.javascriptIsDisabled))),g.InspectorView.InspectorView.instance().setPanelIcon("sources",e)}s.Settings.Settings.instance().moduleSetting("javaScriptDisabled").addChangeListener(e),e()}}class v{#o;#r;#c;constructor(){this.#o=s.Settings.Settings.instance().moduleSetting("autoAttachToCreatedPages"),this.#o.addChangeListener(this.#l,this),this.#l(),this.#r=s.Settings.Settings.instance().moduleSetting("network.adBlockingEnabled"),this.#r.addChangeListener(this.#i,this),this.#c=s.Settings.Settings.instance().moduleSetting("emulatePageFocus"),this.#c.addChangeListener(this.#i,this),c.TargetManager.TargetManager.instance().observeTargets(this)}#d(e){e.type()!==c.Target.Type.Frame||e.parentTarget()||(e.pageAgent().invoke_setAdBlockingEnabled({enabled:this.#r.get()}),e.emulationAgent().invoke_setFocusEmulationEnabled({enabled:this.#c.get()}))}#l(){i.InspectorFrontendHost.InspectorFrontendHostInstance.setOpenNewWindowForPopups(this.#o.get())}#i(){for(const e of c.TargetManager.TargetManager.instance().targets())this.#d(e)}targetAdded(e){this.#d(e)}targetRemoved(e){}}c.ChildTargetManager.ChildTargetManager.install()})),t.register("cKnJs",(function(t,n){e(t.exports,"default",(()=>s));const a=new CSSStyleSheet;a.replaceSync("/*\n * Copyright 2017 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.node-icon {\n  width: 28px;\n  height: 26px;\n  background-image: var(--image-file-nodeIcon);\n  background-size: 17px 17px;\n  background-repeat: no-repeat;\n  background-position: center;\n  opacity: 80%;\n  cursor: auto;\n}\n\n.node-icon:hover {\n  opacity: 100%;\n}\n\n.node-icon.inactive {\n  filter: grayscale(100%);\n}\n\n/*# sourceURL=nodeIcon.css */\n");var s=a}));
//# sourceMappingURL=inspector_main.03198160.js.map
