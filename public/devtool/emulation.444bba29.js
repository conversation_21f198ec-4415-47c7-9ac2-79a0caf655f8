function e(e,t,i,o){Object.defineProperty(e,t,{get:i,set:o,enumerable:!0,configurable:!0})}var t=("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}).parcelRequire20b5;t.register("a1WJ2",(function(i,o){e(i.exports,"AdvancedApp",(()=>t("e1bry"))),e(i.exports,"DeviceModeToolbar",(()=>t("dzfPF"))),e(i.exports,"DeviceModeView",(()=>t("7F5tK"))),e(i.exports,"DeviceModeWrapper",(()=>t("f8z0A"))),e(i.exports,"InspectedPagePlaceholder",(()=>t("kZOF3"))),e(i.exports,"MediaQueryInspector",(()=>t("hNDuE")));t("e1bry"),t("kZOF3"),t("hNDuE"),t("dzfPF"),t("7F5tK"),t("f8z0A"),t("e1bry"),t("dzfPF"),t("7F5tK"),t("f8z0A"),t("kZOF3"),t("hNDuE")})),t.register("e1bry",(function(i,o){e(i.exports,"AdvancedApp",(()=>c)),e(i.exports,"AdvancedAppProvider",(()=>u));var n=t("e7bLS"),r=t("9z2ZV"),a=t("5887T"),s=t("f8z0A"),l=t("kZOF3");let d,h;class c{rootSplitWidget;deviceModeView;inspectedPagePlaceholder;toolboxWindow;toolboxRootView;changingDockSide;constructor(){r.DockController.DockController.instance().addEventListener("BeforeDockSideChanged",this.openToolboxWindow,this)}static instance(){return d||(d=new c),d}presentUI(e){const t=new r.RootView.RootView;this.rootSplitWidget=new r.SplitWidget.SplitWidget(!1,!0,"InspectorView.splitViewState",555,300,!0),this.rootSplitWidget.show(t.element),this.rootSplitWidget.setSidebarWidget(r.InspectorView.InspectorView.instance()),this.rootSplitWidget.setDefaultFocusedChild(r.InspectorView.InspectorView.instance()),r.InspectorView.InspectorView.instance().setOwnerSplit(this.rootSplitWidget),this.inspectedPagePlaceholder=l.InspectedPagePlaceholder.instance(),this.inspectedPagePlaceholder.addEventListener("Update",this.onSetInspectedPageBounds.bind(this),this),this.deviceModeView=s.DeviceModeWrapper.instance({inspectedPagePlaceholder:this.inspectedPagePlaceholder,forceNew:!1}),r.DockController.DockController.instance().addEventListener("BeforeDockSideChanged",this.onBeforeDockSideChange,this),r.DockController.DockController.instance().addEventListener("DockSideChanged",this.onDockSideChange,this),r.DockController.DockController.instance().addEventListener("AfterDockSideChanged",this.onAfterDockSideChange,this),this.onDockSideChange(),console.timeStamp("AdvancedApp.attachToBody"),t.attachToDocument(e),t.focus(),this.inspectedPagePlaceholder.update()}openToolboxWindow(e){if("undocked"!==e.data.to)return;if(this.toolboxWindow)return;const t=window.location.href.replace("devtools_app.html","device_mode_emulation_frame.html");this.toolboxWindow=window.open(t,void 0)}deviceModeEmulationFrameLoaded(e){a.ThemeSupport.instance().applyTheme(e),a.ThemeSupport.instance().addEventListener(a.ThemeChangeEvent.eventName,(()=>{a.ThemeSupport.instance().applyTheme(e)})),r.UIUtils.initializeUIUtils(e),r.UIUtils.installComponentRootStyles(e.body),r.ContextMenu.ContextMenu.installHandler(e),this.toolboxRootView=new r.RootView.RootView,this.toolboxRootView.attachToDocument(e),this.updateDeviceModeView()}updateDeviceModeView(){this.isDocked()?this.rootSplitWidget.setMainWidget(this.deviceModeView):this.toolboxRootView&&this.deviceModeView.show(this.toolboxRootView.element)}onBeforeDockSideChange(e){"undocked"===e.data.to&&this.toolboxRootView&&(this.rootSplitWidget.hideSidebar(),this.inspectedPagePlaceholder.update()),this.changingDockSide=!0}onDockSideChange(e){this.updateDeviceModeView();const t=e?e.data.to:r.DockController.DockController.instance().dockSide();if(void 0===t)throw new Error("Got onDockSideChange event with unexpected undefined for dockSide()");"undocked"===t?this.updateForUndocked():this.toolboxRootView&&e&&"undocked"===e.data.from?this.rootSplitWidget.hideSidebar():this.updateForDocked(t)}onAfterDockSideChange(e){this.changingDockSide&&(e.data.from&&"undocked"===e.data.from&&this.updateForDocked(e.data.to),this.changingDockSide=!1,this.inspectedPagePlaceholder.update())}updateForDocked(e){this.rootSplitWidget.resizerElement().style.transform="right"===e?"translateX(2px)":"left"===e?"translateX(-2px)":"",this.rootSplitWidget.setVertical("right"===e||"left"===e),this.rootSplitWidget.setSecondIsSidebar("right"===e||"bottom"===e),this.rootSplitWidget.toggleResizer(this.rootSplitWidget.resizerElement(),!0),this.rootSplitWidget.toggleResizer(r.InspectorView.InspectorView.instance().topResizerElement(),"bottom"===e),this.rootSplitWidget.showBoth()}updateForUndocked(){this.rootSplitWidget.toggleResizer(this.rootSplitWidget.resizerElement(),!1),this.rootSplitWidget.toggleResizer(r.InspectorView.InspectorView.instance().topResizerElement(),!1),this.rootSplitWidget.hideMain()}isDocked(){return"undocked"!==r.DockController.DockController.instance().dockSide()}onSetInspectedPageBounds(e){if(this.changingDockSide)return;const t=this.inspectedPagePlaceholder.element.window();if(!t.innerWidth||!t.innerHeight)return;if(!this.inspectedPagePlaceholder.isShowing())return;const i=e.data;console.timeStamp("AdvancedApp.setInspectedPageBounds"),n.InspectorFrontendHost.InspectorFrontendHostInstance.setInspectedPageBounds(i)}}globalThis.Emulation=globalThis.Emulation||{},globalThis.Emulation.AdvancedApp=c;class u{static instance(e={forceNew:null}){const{forceNew:t}=e;return h&&!t||(h=new u),h}createApp(){return c.instance()}}})),t.register("f8z0A",(function(i,o){e(i.exports,"DeviceModeWrapper",(()=>c)),e(i.exports,"ActionDelegate",(()=>u));var n=t("9X2mn"),r=t("eQFvP"),a=t("9z2ZV");t("hCUlx");var s=t("17hbI"),l=t("7F5tK");let d,h;class c extends a.Widget.VBox{inspectedPagePlaceholder;deviceModeView;toggleDeviceModeAction;showDeviceModeSetting;constructor(e){super(),this.inspectedPagePlaceholder=e,this.deviceModeView=null,this.toggleDeviceModeAction=a.ActionRegistry.ActionRegistry.instance().action("emulation.toggle-device-mode");const t=s.DeviceModeModel.instance();this.showDeviceModeSetting=t.enabledSetting(),this.showDeviceModeSetting.setRequiresUserAction(Boolean(n.Runtime.Runtime.queryParam("hasOtherClients"))),this.showDeviceModeSetting.addChangeListener(this.update.bind(this,!1)),r.TargetManager.TargetManager.instance().addModelListener(r.OverlayModel.OverlayModel,r.OverlayModel.Events.ScreenshotRequested,this.screenshotRequestedFromOverlay,this),this.update(!0)}static instance(e={forceNew:null,inspectedPagePlaceholder:null}){const{forceNew:t,inspectedPagePlaceholder:i}=e;if(!d||t){if(!i)throw new Error(`Unable to create DeviceModeWrapper: inspectedPagePlaceholder must be provided: ${(new Error).stack}`);d=new c(i)}return d}toggleDeviceMode(){this.showDeviceModeSetting.set(!this.showDeviceModeSetting.get())}isDeviceModeOn(){return this.showDeviceModeSetting.get()}captureScreenshot(e,t){return this.deviceModeView||(this.deviceModeView=new(0,l.DeviceModeView)),this.deviceModeView.setNonEmulatedAvailableSize(this.inspectedPagePlaceholder.element),e?this.deviceModeView.captureFullSizeScreenshot():t?this.deviceModeView.captureAreaScreenshot(t):this.deviceModeView.captureScreenshot(),!0}screenshotRequestedFromOverlay(e){const t=e.data;this.captureScreenshot(!1,t)}update(e){if(this.toggleDeviceModeAction&&this.toggleDeviceModeAction.setToggled(this.showDeviceModeSetting.get()),!e){const e=this.deviceModeView&&this.deviceModeView.isShowing();if(this.showDeviceModeSetting.get()===e)return}this.showDeviceModeSetting.get()?(this.deviceModeView||(this.deviceModeView=new(0,l.DeviceModeView)),this.deviceModeView.show(this.element),this.inspectedPagePlaceholder.clearMinimumSize(),this.inspectedPagePlaceholder.show(this.deviceModeView.element)):(this.deviceModeView&&(this.deviceModeView.exitHingeMode(),this.deviceModeView.detach()),this.inspectedPagePlaceholder.restoreMinimumSize(),this.inspectedPagePlaceholder.show(this.element))}}class u{handleAction(e,t){if(c.instance())switch(t){case"emulation.capture-screenshot":return c.instance().captureScreenshot();case"emulation.capture-node-screenshot":{const i=a.Context.Context.instance().flavor(r.DOMModel.DOMNode);if(!i)return!0;async function o(){if(!i)return;const e=await i.resolveToObject();if(!e)return;const t=await e.callFunction((function(){const e=this.getBoundingClientRect(),t=this.ownerDocument.documentElement.getBoundingClientRect();return JSON.stringify({x:e.left-t.left,y:e.top-t.top,width:e.width,height:e.height,scale:1})}));if(!t.object)throw new Error("Clipping error: could not get object data.");const o=JSON.parse(t.object.value),n=await i.domModel().target().pageAgent().invoke_getLayoutMetrics(),r=!n.getError()&&n.visualViewport.zoom||1;o.x*=r,o.y*=r,o.width*=r,o.height*=r,c.instance().captureScreenshot(!1,o)}return o(),!0}case"emulation.capture-full-height-screenshot":return c.instance().captureScreenshot(!0);case"emulation.toggle-device-mode":return c.instance().toggleDeviceMode(),!0}return!1}static instance(e={forceNew:null}){const{forceNew:t}=e;return h&&!t||(h=new u),h}}})),t.register("hCUlx",(function(i,o){e(i.exports,"DeviceModeModel",(()=>t("17hbI"))),e(i.exports,"EmulatedDevices",(()=>t("8PHbc")));t("17hbI"),t("8PHbc")})),t.register("17hbI",(function(i,o){e(i.exports,"DeviceModeModel",(()=>g)),e(i.exports,"Rect",(()=>b)),e(i.exports,"UA",(()=>w)),e(i.exports,"MinDeviceSize",(()=>M)),e(i.exports,"MaxDeviceSize",(()=>y)),e(i.exports,"Type",(()=>S)),e(i.exports,"MaxDeviceScaleFactor",(()=>z)),e(i.exports,"MinDeviceScaleFactor",(()=>I)),e(i.exports,"Insets",(()=>v)),e(i.exports,"defaultMobileScaleFactor",(()=>C)),e(i.exports,"MaxDeviceNameLength",(()=>k));var n=t("koSS8"),r=t("e7bLS"),a=t("ixFnt"),s=t("9X2mn"),l=t("eQFvP"),d=t("9z2ZV"),h=t("8PHbc");const c={widthMustBeANumber:"Width must be a number.",widthMustBeLessThanOrEqualToS:"Width must be less than or equal to {PH1}.",widthMustBeGreaterThanOrEqualToS:"Width must be greater than or equal to {PH1}.",heightMustBeANumber:"Height must be a number.",heightMustBeLessThanOrEqualToS:"Height must be less than or equal to {PH1}.",heightMustBeGreaterThanOrEqualTo:"Height must be greater than or equal to {PH1}.",devicePixelRatioMustBeANumberOr:"Device pixel ratio must be a number or blank.",devicePixelRatioMustBeLessThanOr:"Device pixel ratio must be less than or equal to {PH1}.",devicePixelRatioMustBeGreater:"Device pixel ratio must be greater than or equal to {PH1}."},u=a.i18n.registerUIStrings("models/emulation/DeviceModeModel.ts",c),m=a.i18n.getLocalizedString.bind(void 0,u);let p;class g extends n.ObjectWrapper.ObjectWrapper{#e;#t;#i;#o;#n;#r;#a;#s;#l;#d;#h;#c;#u;#m;#p;#g;#v;#b;#S;#f;#w;#x;#M;#y;#I;#z;#k;constructor(){super(),this.#e=new b(0,0,1,1),this.#t=new b(0,0,1,1),this.#i=new d.Geometry.Size(1,1),this.#o=new d.Geometry.Size(1,1),this.#n=!1,this.#r=new d.Geometry.Size(1,1),this.#a=window.devicePixelRatio,this.#s=w.Desktop,this.#l=s.Runtime.experiments.isEnabled("dualScreenSupport"),this.#d="segments"in window.visualViewport,this.#h=n.Settings.Settings.instance().createSetting("emulation.deviceScale",1),this.#h.get()||this.#h.set(1),this.#h.addChangeListener(this.scaleSettingChanged,this),this.#c=1,this.#u=n.Settings.Settings.instance().createSetting("emulation.deviceWidth",400),this.#u.get()<M&&this.#u.set(M),this.#u.get()>y&&this.#u.set(y),this.#u.addChangeListener(this.widthSettingChanged,this),this.#m=n.Settings.Settings.instance().createSetting("emulation.deviceHeight",0),this.#m.get()&&this.#m.get()<M&&this.#m.set(M),this.#m.get()>y&&this.#m.set(y),this.#m.addChangeListener(this.heightSettingChanged,this),this.#p=n.Settings.Settings.instance().createSetting("emulation.deviceUA",w.Mobile),this.#p.addChangeListener(this.uaSettingChanged,this),this.#g=n.Settings.Settings.instance().createSetting("emulation.deviceScaleFactor",0),this.#g.addChangeListener(this.deviceScaleFactorSettingChanged,this),this.#v=n.Settings.Settings.instance().moduleSetting("emulation.showDeviceOutline"),this.#v.addChangeListener(this.deviceOutlineSettingChanged,this),this.#b=n.Settings.Settings.instance().createSetting("emulation.toolbarControlsEnabled",!0,n.Settings.SettingStorageType.Session),this.#S=S.None,this.#f=null,this.#w=null,this.#x=1,this.#M=!1,this.#y=!1,this.#I=null,this.#z=null,l.TargetManager.TargetManager.instance().observeModels(l.EmulationModel.EmulationModel,this)}static instance(e={forceNew:null}){return p&&!e.forceNew||(p=new g),p}static widthValidator(e){let t,i=!1;return/^[\d]+$/.test(e)?Number(e)>y?t=m(c.widthMustBeLessThanOrEqualToS,{PH1:y}):Number(e)<M?t=m(c.widthMustBeGreaterThanOrEqualToS,{PH1:M}):i=!0:t=m(c.widthMustBeANumber),{valid:i,errorMessage:t}}static heightValidator(e){let t,i=!1;return/^[\d]+$/.test(e)?Number(e)>y?t=m(c.heightMustBeLessThanOrEqualToS,{PH1:y}):Number(e)<M?t=m(c.heightMustBeGreaterThanOrEqualTo,{PH1:M}):i=!0:t=m(c.heightMustBeANumber),{valid:i,errorMessage:t}}static scaleValidator(e){let t,i=!1;const o=Number(e.trim());return e?Number.isNaN(o)?t=m(c.devicePixelRatioMustBeANumberOr):Number(e)>z?t=m(c.devicePixelRatioMustBeLessThanOr,{PH1:z}):Number(e)<I?t=m(c.devicePixelRatioMustBeGreater,{PH1:I}):i=!0:i=!0,{valid:i,errorMessage:t}}get scaleSettingInternal(){return this.#h}setAvailableSize(e,t){this.#i=e,this.#o=t,this.#n=!0,this.calculateAndEmulate(!1)}emulate(e,t,i,o){const n=this.#S!==e||this.#f!==t||this.#w!==i;if(this.#S=e,e===S.Device&&t&&i){if(console.assert(Boolean(t)&&Boolean(i),"Must pass device and mode for device emulation"),this.#w=i,this.#f=t,this.#n){const e=t.orientationByName(i.orientation);this.#h.set(o||this.calculateFitScale(e.width,e.height,this.currentOutline(),this.currentInsets()))}}else this.#f=null,this.#w=null;e!==S.None&&r.userMetrics.actionTaken(r.UserMetrics.Action.DeviceModeEnabled),this.calculateAndEmulate(n)}setWidth(e){const t=Math.min(y,this.preferredScaledWidth());e=Math.max(Math.min(e,t),1),this.#u.set(e)}setWidthAndScaleToFit(e){e=Math.max(Math.min(e,y),1),this.#h.set(this.calculateFitScale(e,this.#m.get())),this.#u.set(e)}setHeight(e){const t=Math.min(y,this.preferredScaledHeight());(e=Math.max(Math.min(e,t),0))===this.preferredScaledHeight()&&(e=0),this.#m.set(e)}setHeightAndScaleToFit(e){e=Math.max(Math.min(e,y),0),this.#h.set(this.calculateFitScale(this.#u.get(),e)),this.#m.set(e)}setScale(e){this.#h.set(e)}device(){return this.#f}mode(){return this.#w}type(){return this.#S}screenImage(){return this.#f&&this.#w?this.#f.modeImage(this.#w):""}outlineImage(){return this.#f&&this.#w&&this.#v.get()?this.#f.outlineImage(this.#w):""}outlineRect(){return this.#k||null}screenRect(){return this.#e}visiblePageRect(){return this.#t}scale(){return this.#c}fitScale(){return this.#x}appliedDeviceSize(){return this.#r}appliedDeviceScaleFactor(){return this.#a}appliedUserAgentType(){return this.#s}isFullHeight(){return!this.#m.get()}isMobile(){switch(this.#S){case S.Device:return!!this.#f&&this.#f.mobile();case S.None:return!1;case S.Responsive:return this.#p.get()===w.Mobile||this.#p.get()===w.MobileNoTouch}return!1}enabledSetting(){return n.Settings.Settings.instance().createSetting("emulation.showDeviceMode",!1)}scaleSetting(){return this.#h}uaSetting(){return this.#p}deviceScaleFactorSetting(){return this.#g}deviceOutlineSetting(){return this.#v}toolbarControlsEnabledSetting(){return this.#b}reset(){this.#g.set(0),this.#h.set(1),this.setWidth(400),this.setHeight(0),this.#p.set(w.Mobile)}modelAdded(e){if(!this.#I&&e.supportsDeviceEmulation()){if(this.#I=e,this.#z){const e=this.#z;this.#z=null,e()}const t=e.target().model(l.ResourceTreeModel.ResourceTreeModel);t&&(t.addEventListener(l.ResourceTreeModel.Events.FrameResized,this.onFrameChange,this),t.addEventListener(l.ResourceTreeModel.Events.FrameNavigated,this.onFrameChange,this))}else e.emulateTouch(this.#M,this.#y)}modelRemoved(e){this.#I===e&&(this.#I=null)}inspectedURL(){return this.#I?this.#I.target().inspectedURL():null}onFrameChange(){const e=this.#I?this.#I.overlayModel():null;e&&this.showHingeIfApplicable(e)}scaleSettingChanged(){this.calculateAndEmulate(!1)}widthSettingChanged(){this.calculateAndEmulate(!1)}heightSettingChanged(){this.calculateAndEmulate(!1)}uaSettingChanged(){this.calculateAndEmulate(!0)}deviceScaleFactorSettingChanged(){this.calculateAndEmulate(!1)}deviceOutlineSettingChanged(){this.calculateAndEmulate(!1)}preferredScaledWidth(){return Math.floor(this.#o.width/(this.#h.get()||1))}preferredScaledHeight(){return Math.floor(this.#o.height/(this.#h.get()||1))}currentOutline(){let e=new v(0,0,0,0);if(this.#S!==S.Device||!this.#f||!this.#w)return e;const t=this.#f.orientationByName(this.#w.orientation);return this.#v.get()&&(e=t.outlineInsets||e),e}currentInsets(){return this.#S===S.Device&&this.#w?this.#w.insets:new v(0,0,0,0)}getScreenOrientationType(){if(!this.#w)throw new Error("Mode required to get orientation type.");switch(this.#w.orientation){case h.VerticalSpanned:case h.Vertical:return"portraitPrimary";case h.HorizontalSpanned:case h.Horizontal:default:return"landscapePrimary"}}calculateAndEmulate(e){this.#I||(this.#z=this.calculateAndEmulate.bind(this,e));const t=this.isMobile(),i=this.#I?this.#I.overlayModel():null;if(i&&this.showHingeIfApplicable(i),this.#S===S.Device&&this.#f&&this.#w){const i=this.#f.orientationByName(this.#w.orientation),o=this.currentOutline(),n=this.currentInsets();this.#x=this.calculateFitScale(i.width,i.height,o,n),this.#s=t?this.#f.touch()?w.Mobile:w.MobileNoTouch:this.#f.touch()?w.DesktopTouch:w.Desktop,this.applyDeviceMetrics(new d.Geometry.Size(i.width,i.height),n,o,this.#h.get(),this.#f.deviceScaleFactor,t,this.getScreenOrientationType(),e,this.#d),this.applyUserAgent(this.#f.userAgent,this.#f.userAgentMetadata),this.applyTouch(this.#f.touch(),t)}else if(this.#S===S.None)this.#x=this.calculateFitScale(this.#i.width,this.#i.height),this.#s=w.Desktop,this.applyDeviceMetrics(this.#i,new v(0,0,0,0),new v(0,0,0,0),1,0,t,null,e),this.applyUserAgent("",null),this.applyTouch(!1,!1);else if(this.#S===S.Responsive){let i=this.#u.get();(!i||i>this.preferredScaledWidth())&&(i=this.preferredScaledWidth());let o=this.#m.get();(!o||o>this.preferredScaledHeight())&&(o=this.preferredScaledHeight());const n=t?C:0;this.#x=this.calculateFitScale(this.#u.get(),this.#m.get()),this.#s=this.#p.get(),this.applyDeviceMetrics(new d.Geometry.Size(i,o),new v(0,0,0,0),new v(0,0,0,0),this.#h.get(),this.#g.get()||n,t,o>=i?"portraitPrimary":"landscapePrimary",e),this.applyUserAgent(t?T:"",t?E:null),this.applyTouch(this.#p.get()===w.DesktopTouch||this.#p.get()===w.Mobile,this.#p.get()===w.Mobile)}i&&i.setShowViewportSizeOnResize(this.#S===S.None),this.dispatchEventToListeners("Updated")}calculateFitScale(e,t,i,o){const n=i?i.left+i.right:0,r=i?i.top+i.bottom:0,a=o?o.left+o.right:0,s=o?o.top+o.bottom:0;let l=Math.min(e?this.#o.width/(e+n):1,t?this.#o.height/(t+r):1);l=Math.min(Math.floor(100*l),100);let d=l;for(;d>.7*l;){let i=!0;if(e&&(i=i&&Number.isInteger((e-a)*d/100)),t&&(i=i&&Number.isInteger((t-s)*d/100)),i)return d/100;d-=1}return l/100}setSizeAndScaleToFit(e,t){this.#h.set(this.calculateFitScale(e,t)),this.setWidth(e),this.setHeight(t)}applyUserAgent(e,t){l.NetworkManager.MultitargetNetworkManager.instance().setUserAgentOverride(e,t)}applyDeviceMetrics(e,t,i,o,n,r,a,s,l=!1){e.width=Math.max(1,Math.floor(e.width)),e.height=Math.max(1,Math.floor(e.height));let d=e.width-t.left-t.right,h=e.height-t.top-t.bottom;const c=t.left,u=t.top,m="landscapePrimary"===a?90:0;if(this.#r=e,this.#a=n||window.devicePixelRatio,this.#e=new b(Math.max(0,(this.#i.width-e.width*o)/2),i.top*o,e.width*o,e.height*o),this.#k=new b(this.#e.left-i.left*o,0,(i.left+e.width+i.right)*o,(i.top+e.height+i.bottom)*o),this.#t=new b(c*o,u*o,Math.min(d*o,this.#i.width-this.#e.left-c*o),Math.min(h*o,this.#i.height-this.#e.top-u*o)),this.#c=o,l||(1===o&&this.#i.width>=e.width&&this.#i.height>=e.height&&(d=0,h=0),this.#t.width===d*o&&this.#t.height===h*o&&Number.isInteger(d*o)&&Number.isInteger(h*o)&&(d=0,h=0)),this.#I)if(s&&this.#I.resetPageScaleFactor(),d||h||r||n||1!==o||a||l){const t={width:d,height:h,deviceScaleFactor:n,mobile:r,scale:o,screenWidth:e.width,screenHeight:e.height,positionX:c,positionY:u,dontSetVisibleSize:!0,displayFeature:void 0,screenOrientation:void 0},i=this.getDisplayFeature();i&&(t.displayFeature=i),a&&(t.screenOrientation={type:a,angle:m}),this.#I.emulateDevice(t)}else this.#I.emulateDevice(null)}exitHingeMode(){const e=this.#I?this.#I.overlayModel():null;e&&e.showHingeForDualScreen(null)}webPlatformExperimentalFeaturesEnabled(){return this.#d}shouldReportDisplayFeature(){return this.#d&&this.#l}async captureScreenshot(e,t){const i=this.#I?this.#I.target().model(l.ScreenCaptureModel.ScreenCaptureModel):null;if(!i)return null;const o=this.#I?this.#I.overlayModel():null;if(o&&o.setShowViewportSizeOnResize(!1),e){const e=await i.fetchLayoutMetrics();if(!e)return null;const o=Math.min(16384,e.contentHeight);t={x:0,y:0,width:Math.floor(e.contentWidth),height:Math.floor(o),scale:1}}const n=await i.captureScreenshot("png",100,t),r={width:0,height:0,deviceScaleFactor:0,mobile:!1};if(e&&this.#I){if(this.#f&&this.#w){const e=this.#f.orientationByName(this.#w.orientation);r.width=e.width,r.height=e.height;const t=this.getDisplayFeature();t&&(r.displayFeature=t)}else r.width=0,r.height=0;await this.#I.emulateDevice(r)}return this.calculateAndEmulate(!1),n}applyTouch(e,t){this.#M=e,this.#y=t;for(const i of l.TargetManager.TargetManager.instance().models(l.EmulationModel.EmulationModel))i.emulateTouch(e,t)}showHingeIfApplicable(e){const t=this.#f&&this.#w?this.#f.orientationByName(this.#w.orientation):null;this.#l&&t&&t.hinge?e.showHingeForDualScreen(t.hinge):e.showHingeForDualScreen(null)}getDisplayFeatureOrientation(){if(!this.#w)throw new Error("Mode required to get display feature orientation.");switch(this.#w.orientation){case h.VerticalSpanned:case h.Vertical:return"vertical";case h.HorizontalSpanned:case h.Horizontal:default:return"horizontal"}}getDisplayFeature(){if(!this.shouldReportDisplayFeature())return null;if(!this.#f||!this.#w||this.#w.orientation!==h.VerticalSpanned&&this.#w.orientation!==h.HorizontalSpanned)return null;const e=this.#f.orientationByName(this.#w.orientation);if(!e||!e.hinge)return null;const t=e.hinge;return{orientation:this.getDisplayFeatureOrientation(),offset:this.#w.orientation===h.VerticalSpanned?t.x:t.y,maskLength:this.#w.orientation===h.VerticalSpanned?t.width:t.height}}}class v{left;top;right;bottom;constructor(e,t,i,o){this.left=e,this.top=t,this.right=i,this.bottom=o}isEqual(e){return null!==e&&this.left===e.left&&this.top===e.top&&this.right===e.right&&this.bottom===e.bottom}}class b{left;top;width;height;constructor(e,t,i,o){this.left=e,this.top=t,this.width=i,this.height=o}isEqual(e){return null!==e&&this.left===e.left&&this.top===e.top&&this.width===e.width&&this.height===e.height}scale(e){return new b(this.left*e,this.top*e,this.width*e,this.height*e)}relativeTo(e){return new b(this.left-e.left,this.top-e.top,this.width,this.height)}rebaseTo(e){return new b(this.left+e.left,this.top+e.top,this.width,this.height)}}var S,f,w,x;(f=S||(S={})).None="None",f.Responsive="Responsive",f.Device="Device",(x=w||(w={})).Mobile="Mobile",x.MobileNoTouch="Mobile (no touch)",x.Desktop="Desktop",x.DesktopTouch="Desktop (touch)";const M=50,y=9999,I=0,z=10,k=50,T=l.NetworkManager.MultitargetNetworkManager.patchUserAgentWithChromeVersion("Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Mobile Safari/537.36"),E={platform:"Android",platformVersion:"6.0",architecture:"",model:"Nexus 5",mobile:!0},C=2})),t.register("8PHbc",(function(i,o){e(i.exports,"computeRelativeImageURL",(()=>c)),e(i.exports,"EmulatedDevice",(()=>u)),e(i.exports,"Type",(()=>b)),e(i.exports,"Capability",(()=>S)),e(i.exports,"_Show",(()=>f)),e(i.exports,"Vertical",(()=>p)),e(i.exports,"Horizontal",(()=>m)),e(i.exports,"VerticalSpanned",(()=>v)),e(i.exports,"HorizontalSpanned",(()=>g)),e(i.exports,"EmulatedDevicesList",(()=>x));var n=t("koSS8"),r=t("ixFnt"),a=t("eQFvP"),s=t("17hbI");const l={laptopWithTouch:"Laptop with touch",laptopWithHiDPIScreen:"Laptop with HiDPI screen",laptopWithMDPIScreen:"Laptop with MDPI screen"},d=r.i18n.registerUIStrings("models/emulation/EmulatedDevices.ts",l),h=r.i18n.getLazilyComputedLocalizedString.bind(void 0,d);function c(e){return e.replace(/@url\(([^\)]*?)\)/g,((e,t)=>new URL(`../../emulated_devices/${t}`,"file:///build/front_end/models/emulation/EmulatedDevices.js").toString()))}class u{title;type;order;vertical;horizontal;deviceScaleFactor;capabilities;userAgent;userAgentMetadata;modes;isDualScreen;verticalSpanned;horizontalSpanned;#T;#E;constructor(){this.title="",this.type=b.Unknown,this.vertical={width:0,height:0,outlineInsets:null,outlineImage:null,hinge:null},this.horizontal={width:0,height:0,outlineInsets:null,outlineImage:null,hinge:null},this.deviceScaleFactor=1,this.capabilities=[S.Touch,S.Mobile],this.userAgent="",this.userAgentMetadata=null,this.modes=[],this.isDualScreen=!1,this.verticalSpanned={width:0,height:0,outlineInsets:null,outlineImage:null,hinge:null},this.horizontalSpanned={width:0,height:0,outlineInsets:null,outlineImage:null,hinge:null},this.#T=f.Default,this.#E=!0}static fromJSONV1(e){try{function t(e,t,i,o){if("object"!=typeof e||null===e||!e.hasOwnProperty(t)){if(void 0!==o)return o;throw new Error("Emulated device is missing required property '"+t+"'")}const n=e[t];if(typeof n!==i||null===n)throw new Error("Emulated device property '"+t+"' has wrong type '"+typeof n+"'");return n}function i(e,i){const o=t(e,i,"number");if(o!==Math.abs(o))throw new Error("Emulated device value '"+i+"' must be integer");return o}function o(e){return new(0,s.Insets)(i(e,"left"),i(e,"top"),i(e,"right"),i(e,"bottom"))}function n(e){const o={};if(o.r=i(e,"r"),o.r<0||o.r>255)throw new Error("color has wrong r value: "+o.r);if(o.g=i(e,"g"),o.g<0||o.g>255)throw new Error("color has wrong g value: "+o.g);if(o.b=i(e,"b"),o.b<0||o.b>255)throw new Error("color has wrong b value: "+o.b);if(o.a=t(e,"a","number"),o.a<0||o.a>1)throw new Error("color has wrong a value: "+o.a);return o}function r(e){const t={};if(t.width=i(e,"width"),t.width<0||t.width>s.MaxDeviceSize)throw new Error("Emulated device has wrong hinge width: "+t.width);if(t.height=i(e,"height"),t.height<0||t.height>s.MaxDeviceSize)throw new Error("Emulated device has wrong hinge height: "+t.height);if(t.x=i(e,"x"),t.x<0||t.x>s.MaxDeviceSize)throw new Error("Emulated device has wrong x offset: "+t.height);if(t.y=i(e,"y"),t.x<0||t.x>s.MaxDeviceSize)throw new Error("Emulated device has wrong y offset: "+t.height);return e.contentColor&&(t.contentColor=n(e.contentColor)),e.outlineColor&&(t.outlineColor=n(e.outlineColor)),t}function l(e){const n={};if(n.width=i(e,"width"),n.width<0||n.width>s.MaxDeviceSize||n.width<s.MinDeviceSize)throw new Error("Emulated device has wrong width: "+n.width);if(n.height=i(e,"height"),n.height<0||n.height>s.MaxDeviceSize||n.height<s.MinDeviceSize)throw new Error("Emulated device has wrong height: "+n.height);const a=t(e.outline,"insets","object",null);if(a){if(n.outlineInsets=o(a),n.outlineInsets.left<0||n.outlineInsets.top<0)throw new Error("Emulated device has wrong outline insets");n.outlineImage=t(e.outline,"image","string")}return e.hinge&&(n.hinge=r(t(e,"hinge","object",void 0))),n}const d=new u;d.title=t(e,"title","string"),d.type=t(e,"type","string"),d.order=t(e,"order","number",0);const h=t(e,"user-agent","string");d.userAgent=a.NetworkManager.MultitargetNetworkManager.patchUserAgentWithChromeVersion(h),d.userAgentMetadata=t(e,"user-agent-metadata","object",null);const c=t(e,"capabilities","object",[]);if(!Array.isArray(c))throw new Error("Emulated device capabilities must be an array");d.capabilities=[];for(let S=0;S<c.length;++S){if("string"!=typeof c[S])throw new Error("Emulated device capability must be a string");d.capabilities.push(c[S])}if(d.deviceScaleFactor=t(e.screen,"device-pixel-ratio","number"),d.deviceScaleFactor<0||d.deviceScaleFactor>100)throw new Error("Emulated device has wrong deviceScaleFactor: "+d.deviceScaleFactor);if(d.vertical=l(t(e.screen,"vertical","object")),d.horizontal=l(t(e.screen,"horizontal","object")),d.isDualScreen=t(e,"dual-screen","boolean",!1),d.isDualScreen&&(d.verticalSpanned=l(t(e.screen,"vertical-spanned","object",null)),d.horizontalSpanned=l(t(e.screen,"horizontal-spanned","object",null))),d.isDualScreen&&(!d.verticalSpanned||!d.horizontalSpanned))throw new Error("Emulated device '"+d.title+"'has dual screen without spanned orientations");const b=t(e,"modes","object",[{title:"default",orientation:"vertical"},{title:"default",orientation:"horizontal"}]);if(!Array.isArray(b))throw new Error("Emulated device modes must be an array");d.modes=[];for(let w=0;w<b.length;++w){const x={};if(x.title=t(b[w],"title","string"),x.orientation=t(b[w],"orientation","string"),x.orientation!==p&&x.orientation!==m&&x.orientation!==v&&x.orientation!==g)throw new Error("Emulated device mode has wrong orientation '"+x.orientation+"'");const M=d.orientationByName(x.orientation);if(x.insets=o(t(b[w],"insets","object",{left:0,top:0,right:0,bottom:0})),x.insets.top<0||x.insets.left<0||x.insets.right<0||x.insets.bottom<0||x.insets.top+x.insets.bottom>M.height||x.insets.left+x.insets.right>M.width)throw new Error("Emulated device mode '"+x.title+"'has wrong mode insets");x.image=t(b[w],"image","string",null),d.modes.push(x)}return d.#E=t(e,"show-by-default","boolean",void 0),d.#T=t(e,"show","string",f.Default),d}catch(y){return null}}static deviceComparator(e,t){const i=e.order||0,o=t.order||0;return i>o?1:o>i||e.title<t.title?-1:e.title>t.title?1:0}modesForOrientation(e){const t=[];for(let i=0;i<this.modes.length;i++)this.modes[i].orientation===e&&t.push(this.modes[i]);return t}getSpanPartner(e){switch(e.orientation){case p:return this.modesForOrientation(v)[0];case m:return this.modesForOrientation(g)[0];case v:return this.modesForOrientation(p)[0];default:return this.modesForOrientation(m)[0]}}getRotationPartner(e){switch(e.orientation){case g:return this.modesForOrientation(v)[0];case v:return this.modesForOrientation(g)[0];case m:return this.modesForOrientation(p)[0];default:return this.modesForOrientation(m)[0]}}toJSON(){const e={};e.title=this.title,e.type=this.type,e["user-agent"]=this.userAgent,e.capabilities=this.capabilities,e.screen={"device-pixel-ratio":this.deviceScaleFactor,vertical:this.orientationToJSON(this.vertical),horizontal:this.orientationToJSON(this.horizontal),"vertical-spanned":void 0,"horizontal-spanned":void 0},this.isDualScreen&&(e.screen["vertical-spanned"]=this.orientationToJSON(this.verticalSpanned),e.screen["horizontal-spanned"]=this.orientationToJSON(this.horizontalSpanned)),e.modes=[];for(let t=0;t<this.modes.length;++t){const i={title:this.modes[t].title,orientation:this.modes[t].orientation,insets:{left:this.modes[t].insets.left,top:this.modes[t].insets.top,right:this.modes[t].insets.right,bottom:this.modes[t].insets.bottom},image:this.modes[t].image||void 0};e.modes.push(i)}return e["show-by-default"]=this.#E,e["dual-screen"]=this.isDualScreen,e.show=this.#T,this.userAgentMetadata&&(e["user-agent-metadata"]=this.userAgentMetadata),e}orientationToJSON(e){const t={};return t.width=e.width,t.height=e.height,e.outlineInsets&&(t.outline={insets:{left:e.outlineInsets.left,top:e.outlineInsets.top,right:e.outlineInsets.right,bottom:e.outlineInsets.bottom},image:e.outlineImage}),e.hinge&&(t.hinge={width:e.hinge.width,height:e.hinge.height,x:e.hinge.x,y:e.hinge.y,contentColor:void 0,outlineColor:void 0},e.hinge.contentColor&&(t.hinge.contentColor={r:e.hinge.contentColor.r,g:e.hinge.contentColor.g,b:e.hinge.contentColor.b,a:e.hinge.contentColor.a}),e.hinge.outlineColor&&(t.hinge.outlineColor={r:e.hinge.outlineColor.r,g:e.hinge.outlineColor.g,b:e.hinge.outlineColor.b,a:e.hinge.outlineColor.a})),t}modeImage(e){return e.image?c(e.image):""}outlineImage(e){const t=this.orientationByName(e.orientation);return t.outlineImage?c(t.outlineImage):""}orientationByName(e){switch(e){case v:return this.verticalSpanned;case g:return this.horizontalSpanned;case p:return this.vertical;default:return this.horizontal}}show(){return this.#T===f.Default?this.#E:this.#T===f.Always}setShow(e){this.#T=e?f.Always:f.Never}copyShowFrom(e){this.#T=e.#T}touch(){return-1!==this.capabilities.indexOf(S.Touch)}mobile(){return-1!==this.capabilities.indexOf(S.Mobile)}}const m="horizontal",p="vertical",g="horizontal-spanned",v="vertical-spanned",b={Phone:"phone",Tablet:"tablet",Notebook:"notebook",Desktop:"desktop",Unknown:"unknown"},S={Touch:"touch",Mobile:"mobile"},f={Always:"Always",Default:"Default",Never:"Never"};let w;class x extends n.ObjectWrapper.ObjectWrapper{#C;#D;#A;#L;constructor(){super(),this.#C=n.Settings.Settings.instance().createSetting("standardEmulatedDeviceList",[]),this.#D=new Set,this.listFromJSONV1(this.#C.get(),this.#D),this.updateStandardDevices(),this.#A=n.Settings.Settings.instance().createSetting("customEmulatedDeviceList",[]),this.#L=new Set,this.listFromJSONV1(this.#A.get(),this.#L)||this.saveCustomDevices()}static instance(){return w||(w=new x),w}updateStandardDevices(){const e=new Set;for(const t of M){const i=u.fromJSONV1(t);i&&e.add(i)}this.copyShowValues(this.#D,e),this.#D=e,this.saveStandardDevices()}listFromJSONV1(e,t){if(!Array.isArray(e))return!1;let i=!0;for(let o=0;o<e.length;++o){const n=u.fromJSONV1(e[o]);n?(t.add(n),n.modes.length||(n.modes.push({title:"",orientation:m,insets:new(0,s.Insets)(0,0,0,0),image:null}),n.modes.push({title:"",orientation:p,insets:new(0,s.Insets)(0,0,0,0),image:null}))):i=!1}return i}standard(){return[...this.#D]}custom(){return[...this.#L]}revealCustomSetting(){n.Revealer.reveal(this.#A)}addCustomDevice(e){this.#L.add(e),this.saveCustomDevices()}removeCustomDevice(e){this.#L.delete(e),this.saveCustomDevices()}saveCustomDevices(){const e=[];this.#L.forEach((t=>e.push(t.toJSON()))),this.#A.set(e),this.dispatchEventToListeners("CustomDevicesUpdated")}saveStandardDevices(){const e=[];this.#D.forEach((t=>e.push(t.toJSON()))),this.#C.set(e),this.dispatchEventToListeners("StandardDevicesUpdated")}copyShowValues(e,t){const i=new Map;for(const t of e)i.set(t.title,t);for(const e of t){const t=i.get(e.title);t&&e.copyShowFrom(t)}}}const M=[{order:10,"show-by-default":!0,title:"iPhone SE",screen:{horizontal:{width:667,height:375},"device-pixel-ratio":2,vertical:{width:375,height:667}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1",type:"phone"},{order:12,"show-by-default":!0,title:"iPhone XR",screen:{horizontal:{width:896,height:414},"device-pixel-ratio":2,vertical:{width:414,height:896}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1",type:"phone"},{order:14,"show-by-default":!0,title:"iPhone 12 Pro",screen:{horizontal:{width:844,height:390},"device-pixel-ratio":3,vertical:{width:390,height:844}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1",type:"phone"},{order:16,"show-by-default":!1,title:"Pixel 3 XL",screen:{horizontal:{width:786,height:393},"device-pixel-ratio":2.75,vertical:{width:393,height:786}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android 11; Pixel 3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.181 Mobile Safari/537.36",type:"phone"},{order:18,"show-by-default":!0,title:"Pixel 5",screen:{horizontal:{width:851,height:393},"device-pixel-ratio":2.75,vertical:{width:393,height:851}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android 11; Pixel 5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.91 Mobile Safari/537.36",type:"phone"},{order:20,"show-by-default":!0,title:"Samsung Galaxy S8+",screen:{horizontal:{width:740,height:360},"device-pixel-ratio":4,vertical:{width:360,height:740}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36",type:"phone"},{order:24,"show-by-default":!0,title:"Samsung Galaxy S20 Ultra",screen:{horizontal:{width:915,height:412},"device-pixel-ratio":3.5,vertical:{width:412,height:915}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android 10; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.162 Mobile Safari/537.36",type:"phone"},{order:26,"show-by-default":!0,title:"iPad Air",screen:{horizontal:{width:1180,height:820},"device-pixel-ratio":2,vertical:{width:820,height:1180}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1",type:"tablet"},{order:28,"show-by-default":!0,title:"iPad Mini",screen:{horizontal:{width:1024,height:768},"device-pixel-ratio":2,vertical:{width:768,height:1024}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1",type:"tablet"},{order:30,"show-by-default":!0,title:"Surface Pro 7",screen:{horizontal:{width:1368,height:912},"device-pixel-ratio":2,vertical:{width:912,height:1368}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (iPad; CPU OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/87.0.4280.77 Mobile/15E148 Safari/604.1",type:"tablet"},{order:32,"show-by-default":!0,"dual-screen":!0,title:"Surface Duo",screen:{horizontal:{width:720,height:540},"device-pixel-ratio":2.5,vertical:{width:540,height:720},"vertical-spanned":{width:1114,height:720,hinge:{width:34,height:720,x:540,y:0,contentColor:{r:38,g:38,b:38,a:1}}},"horizontal-spanned":{width:720,height:1114,hinge:{width:720,height:34,x:0,y:540,contentColor:{r:38,g:38,b:38,a:1}}}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Mobile Safari/537.36",type:"phone",modes:[{title:"default",orientation:"vertical",insets:{left:0,top:0,right:0,bottom:0}},{title:"default",orientation:"horizontal",insets:{left:0,top:0,right:0,bottom:0}},{title:"spanned",orientation:"vertical-spanned",insets:{left:0,top:0,right:0,bottom:0}},{title:"spanned",orientation:"horizontal-spanned",insets:{left:0,top:0,right:0,bottom:0}}]},{order:34,"show-by-default":!0,"dual-screen":!0,title:"Galaxy Fold",screen:{horizontal:{width:653,height:280},"device-pixel-ratio":3,vertical:{width:280,height:653},"vertical-spanned":{width:717,height:512},"horizontal-spanned":{width:512,height:717}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Mobile Safari/537.36",type:"phone",modes:[{title:"default",orientation:"vertical",insets:{left:0,top:0,right:0,bottom:0}},{title:"default",orientation:"horizontal",insets:{left:0,top:0,right:0,bottom:0}},{title:"spanned",orientation:"vertical-spanned",insets:{left:0,top:0,right:0,bottom:0}},{title:"spanned",orientation:"horizontal-spanned",insets:{left:0,top:0,right:0,bottom:0}}]},{order:36,"show-by-default":!0,title:"Samsung Galaxy A51/71",screen:{horizontal:{width:914,height:412},"device-pixel-ratio":2.625,vertical:{width:412,height:914}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Mobile Safari/537.36",type:"phone"},{order:52,"show-by-default":!0,title:"Nest Hub Max",screen:{horizontal:{outline:{image:"@url(optimized/google-nest-hub-max-horizontal.avif)",insets:{left:92,top:96,right:91,bottom:248}},width:1280,height:800},"device-pixel-ratio":2,vertical:{width:1280,height:800}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (X11; Linux aarch64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.188 Safari/537.36 CrKey/1.54.250320",type:"tablet",modes:[{title:"default",orientation:"horizontal"}]},{order:50,"show-by-default":!0,title:"Nest Hub",screen:{horizontal:{outline:{image:"@url(optimized/google-nest-hub-horizontal.avif)",insets:{left:82,top:74,right:83,bottom:222}},width:1024,height:600},"device-pixel-ratio":2,vertical:{width:1024,height:600}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.109 Safari/537.36 CrKey/1.54.248666",type:"tablet",modes:[{title:"default",orientation:"horizontal"}]},{"show-by-default":!1,title:"iPhone 4",screen:{horizontal:{width:480,height:320},"device-pixel-ratio":2,vertical:{width:320,height:480}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 7_1_2 like Mac OS X) AppleWebKit/537.51.2 (KHTML, like Gecko) Version/7.0 Mobile/11D257 Safari/9537.53",type:"phone"},{order:130,"show-by-default":!1,title:"iPhone 5/SE",screen:{horizontal:{outline:{image:"@url(optimized/iPhone5-landscape.avif)",insets:{left:115,top:25,right:115,bottom:28}},width:568,height:320},"device-pixel-ratio":2,vertical:{outline:{image:"@url(optimized/iPhone5-portrait.avif)",insets:{left:29,top:105,right:25,bottom:111}},width:320,height:568}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.30 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1",type:"phone"},{order:131,"show-by-default":!1,title:"iPhone 6/7/8",screen:{horizontal:{outline:{image:"@url(optimized/iPhone6-landscape.avif)",insets:{left:106,top:28,right:106,bottom:28}},width:667,height:375},"device-pixel-ratio":2,vertical:{outline:{image:"@url(optimized/iPhone6-portrait.avif)",insets:{left:28,top:105,right:28,bottom:105}},width:375,height:667}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1",type:"phone"},{order:132,"show-by-default":!1,title:"iPhone 6/7/8 Plus",screen:{horizontal:{outline:{image:"@url(optimized/iPhone6Plus-landscape.avif)",insets:{left:109,top:29,right:109,bottom:27}},width:736,height:414},"device-pixel-ratio":3,vertical:{outline:{image:"@url(optimized/iPhone6Plus-portrait.avif)",insets:{left:26,top:107,right:30,bottom:111}},width:414,height:736}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1",type:"phone"},{order:133,"show-by-default":!1,title:"iPhone X",screen:{horizontal:{width:812,height:375},"device-pixel-ratio":3,vertical:{width:375,height:812}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1",type:"phone"},{"show-by-default":!1,title:"BlackBerry Z30",screen:{horizontal:{width:640,height:360},"device-pixel-ratio":2,vertical:{width:360,height:640}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (BB10; Touch) AppleWebKit/537.10+ (KHTML, like Gecko) Version/10.0.9.2372 Mobile Safari/537.10+",type:"phone"},{"show-by-default":!1,title:"Nexus 4",screen:{horizontal:{width:640,height:384},"device-pixel-ratio":2,vertical:{width:384,height:640}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android 4.4.2; Nexus 4 Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Mobile Safari/537.36","user-agent-metadata":{platform:"Android",platformVersion:"4.4.2",architecture:"",model:"Nexus 4",mobile:!0},type:"phone"},{title:"Nexus 5",type:"phone","user-agent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Mobile Safari/537.36","user-agent-metadata":{platform:"Android",platformVersion:"6.0",architecture:"",model:"Nexus 5",mobile:!0},capabilities:["touch","mobile"],"show-by-default":!1,screen:{"device-pixel-ratio":3,vertical:{width:360,height:640},horizontal:{width:640,height:360}},modes:[{title:"default",orientation:"vertical",insets:{left:0,top:25,right:0,bottom:48},image:"@url(optimized/google-nexus-5-vertical-default-1x.avif) 1x, @url(optimized/google-nexus-5-vertical-default-2x.avif) 2x"},{title:"navigation bar",orientation:"vertical",insets:{left:0,top:80,right:0,bottom:48},image:"@url(optimized/google-nexus-5-vertical-navigation-1x.avif) 1x, @url(optimized/google-nexus-5-vertical-navigation-2x.avif) 2x"},{title:"keyboard",orientation:"vertical",insets:{left:0,top:80,right:0,bottom:312},image:"@url(optimized/google-nexus-5-vertical-keyboard-1x.avif) 1x, @url(optimized/google-nexus-5-vertical-keyboard-2x.avif) 2x"},{title:"default",orientation:"horizontal",insets:{left:0,top:25,right:42,bottom:0},image:"@url(optimized/google-nexus-5-horizontal-default-1x.avif) 1x, @url(optimized/google-nexus-5-horizontal-default-2x.avif) 2x"},{title:"navigation bar",orientation:"horizontal",insets:{left:0,top:80,right:42,bottom:0},image:"@url(optimized/google-nexus-5-horizontal-navigation-1x.avif) 1x, @url(optimized/google-nexus-5-horizontal-navigation-2x.avif) 2x"},{title:"keyboard",orientation:"horizontal",insets:{left:0,top:80,right:42,bottom:202},image:"@url(optimized/google-nexus-5-horizontal-keyboard-1x.avif) 1x, @url(optimized/google-nexus-5-horizontal-keyboard-2x.avif) 2x"}]},{title:"Nexus 5X",type:"phone","user-agent":"Mozilla/5.0 (Linux; Android 8.0.0; Nexus 5X Build/OPR4.170623.006) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Mobile Safari/537.36","user-agent-metadata":{platform:"Android",platformVersion:"8.0.0",architecture:"",model:"Nexus 5X",mobile:!0},capabilities:["touch","mobile"],"show-by-default":!1,screen:{"device-pixel-ratio":2.625,vertical:{outline:{image:"@url(optimized/Nexus5X-portrait.avif)",insets:{left:18,top:88,right:22,bottom:98}},width:412,height:732},horizontal:{outline:{image:"@url(optimized/Nexus5X-landscape.avif)",insets:{left:88,top:21,right:98,bottom:19}},width:732,height:412}},modes:[{title:"default",orientation:"vertical",insets:{left:0,top:24,right:0,bottom:48},image:"@url(optimized/google-nexus-5x-vertical-default-1x.avif) 1x, @url(optimized/google-nexus-5x-vertical-default-2x.avif) 2x"},{title:"navigation bar",orientation:"vertical",insets:{left:0,top:80,right:0,bottom:48},image:"@url(optimized/google-nexus-5x-vertical-navigation-1x.avif) 1x, @url(optimized/google-nexus-5x-vertical-navigation-2x.avif) 2x"},{title:"keyboard",orientation:"vertical",insets:{left:0,top:80,right:0,bottom:342},image:"@url(optimized/google-nexus-5x-vertical-keyboard-1x.avif) 1x, @url(optimized/google-nexus-5x-vertical-keyboard-2x.avif) 2x"},{title:"default",orientation:"horizontal",insets:{left:0,top:24,right:48,bottom:0},image:"@url(optimized/google-nexus-5x-horizontal-default-1x.avif) 1x, @url(optimized/google-nexus-5x-horizontal-default-2x.avif) 2x"},{title:"navigation bar",orientation:"horizontal",insets:{left:0,top:80,right:48,bottom:0},image:"@url(optimized/google-nexus-5x-horizontal-navigation-1x.avif) 1x, @url(optimized/google-nexus-5x-horizontal-navigation-2x.avif) 2x"},{title:"keyboard",orientation:"horizontal",insets:{left:0,top:80,right:48,bottom:222},image:"@url(optimized/google-nexus-5x-horizontal-keyboard-1x.avif) 1x, @url(optimized/google-nexus-5x-horizontal-keyboard-2x.avif) 2x"}]},{"show-by-default":!1,title:"Nexus 6",screen:{horizontal:{width:732,height:412},"device-pixel-ratio":3.5,vertical:{width:412,height:732}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android 7.1.1; Nexus 6 Build/N6F26U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Mobile Safari/537.36","user-agent-metadata":{platform:"Android",platformVersion:"7.1.1",architecture:"",model:"Nexus 6",mobile:!0},type:"phone"},{"show-by-default":!1,title:"Nexus 6P",screen:{horizontal:{outline:{image:"@url(optimized/Nexus6P-landscape.avif)",insets:{left:94,top:17,right:88,bottom:17}},width:732,height:412},"device-pixel-ratio":3.5,vertical:{outline:{image:"@url(optimized/Nexus6P-portrait.avif)",insets:{left:16,top:94,right:16,bottom:88}},width:412,height:732}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android 8.0.0; Nexus 6P Build/OPP3.170518.006) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Mobile Safari/537.36","user-agent-metadata":{platform:"Android",platformVersion:"8.0.0",architecture:"",model:"Nexus 6P",mobile:!0},type:"phone"},{order:120,"show-by-default":!1,title:"Pixel 2",screen:{horizontal:{width:731,height:411},"device-pixel-ratio":2.625,vertical:{width:411,height:731}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android 8.0; Pixel 2 Build/OPD3.170816.012) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Mobile Safari/537.36","user-agent-metadata":{platform:"Android",platformVersion:"8.0",architecture:"",model:"Pixel 2",mobile:!0},type:"phone"},{order:121,"show-by-default":!1,title:"Pixel 2 XL",screen:{horizontal:{width:823,height:411},"device-pixel-ratio":3.5,vertical:{width:411,height:823}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android 8.0.0; Pixel 2 XL Build/OPD1.170816.004) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Mobile Safari/537.36","user-agent-metadata":{platform:"Android",platformVersion:"8.0.0",architecture:"",model:"Pixel 2 XL",mobile:!0},type:"phone"},{"show-by-default":!1,title:"Pixel 3",screen:{horizontal:{width:786,height:393},"device-pixel-ratio":2.75,vertical:{width:393,height:786}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android 9; Pixel 3 Build/PQ1A.181105.017.A1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.158 Mobile Safari/537.36","user-agent-metadata":{platform:"Android",platformVersion:"9",architecture:"",model:"Pixel 3",mobile:!0},type:"phone"},{"show-by-default":!1,title:"Pixel 4",screen:{horizontal:{width:745,height:353},"device-pixel-ratio":3,vertical:{width:353,height:745}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android 10; Pixel 4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Mobile Safari/537.36","user-agent-metadata":{platform:"Android",platformVersion:"10",architecture:"",model:"Pixel 4",mobile:!0},type:"phone"},{"show-by-default":!1,title:"LG Optimus L70",screen:{horizontal:{width:640,height:384},"device-pixel-ratio":1.25,vertical:{width:384,height:640}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; U; Android 4.4.2; en-us; LGMS323 Build/KOT49I.MS32310c) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/%s Mobile Safari/537.36","user-agent-metadata":{platform:"Android",platformVersion:"4.4.2",architecture:"",model:"LGMS323",mobile:!0},type:"phone"},{"show-by-default":!1,title:"Nokia N9",screen:{horizontal:{width:854,height:480},"device-pixel-ratio":1,vertical:{width:480,height:854}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (MeeGo; NokiaN9) AppleWebKit/534.13 (KHTML, like Gecko) NokiaBrowser/8.5.0 Mobile Safari/534.13",type:"phone"},{"show-by-default":!1,title:"Nokia Lumia 520",screen:{horizontal:{width:533,height:320},"device-pixel-ratio":1.5,vertical:{width:320,height:533}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (compatible; MSIE 10.0; Windows Phone 8.0; Trident/6.0; IEMobile/10.0; ARM; Touch; NOKIA; Lumia 520)",type:"phone"},{"show-by-default":!1,title:"Microsoft Lumia 550",screen:{horizontal:{width:640,height:360},"device-pixel-ratio":2,vertical:{width:640,height:360}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; Microsoft; Lumia 550) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/14.14263",type:"phone"},{"show-by-default":!1,title:"Microsoft Lumia 950",screen:{horizontal:{width:640,height:360},"device-pixel-ratio":4,vertical:{width:360,height:640}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Windows Phone 10.0; Android 4.2.1; Microsoft; Lumia 950) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/14.14263",type:"phone"},{"show-by-default":!1,title:"Galaxy S III",screen:{horizontal:{width:640,height:360},"device-pixel-ratio":2,vertical:{width:360,height:640}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; U; Android 4.0; en-us; GT-I9300 Build/IMM76D) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30","user-agent-metadata":{platform:"Android",platformVersion:"4.0",architecture:"",model:"GT-I9300",mobile:!0},type:"phone"},{order:110,"show-by-default":!1,title:"Galaxy S5",screen:{horizontal:{width:640,height:360},"device-pixel-ratio":3,vertical:{width:360,height:640}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Mobile Safari/537.36","user-agent-metadata":{platform:"Android",platformVersion:"5.0",architecture:"",model:"SM-G900P",mobile:!0},type:"phone"},{"show-by-default":!1,title:"Galaxy S8",screen:{horizontal:{width:740,height:360},"device-pixel-ratio":3,vertical:{width:360,height:740}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.84 Mobile Safari/537.36","user-agent-metadata":{platform:"Android",platformVersion:"7.0",architecture:"",model:"SM-G950U",mobile:!0},type:"phone"},{"show-by-default":!1,title:"Galaxy S9+",screen:{horizontal:{width:658,height:320},"device-pixel-ratio":4.5,vertical:{width:320,height:658}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android 8.0.0; SM-G965U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.111 Mobile Safari/537.36","user-agent-metadata":{platform:"Android",platformVersion:"8.0.0",architecture:"",model:"SM-G965U",mobile:!0},type:"phone"},{"show-by-default":!1,title:"Galaxy Tab S4",screen:{horizontal:{width:1138,height:712},"device-pixel-ratio":2.25,vertical:{width:712,height:1138}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android 8.1.0; SM-T837A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.80 Safari/537.36","user-agent-metadata":{platform:"Android",platformVersion:"8.1.0",architecture:"",model:"SM-T837A",mobile:!0},type:"phone"},{order:1,"show-by-default":!1,title:"JioPhone 2",screen:{horizontal:{width:320,height:240},"device-pixel-ratio":1,vertical:{width:240,height:320}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Mobile; LYF/F300B/LYF-F300B-001-01-15-130718-i;Android; rv:48.0) Gecko/48.0 Firefox/48.0 KAIOS/2.5",type:"phone"},{"show-by-default":!1,title:"Kindle Fire HDX",screen:{horizontal:{width:1280,height:800},"device-pixel-ratio":2,vertical:{width:800,height:1280}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; U; en-us; KFAPWI Build/JDQ39) AppleWebKit/535.19 (KHTML, like Gecko) Silk/3.13 Safari/535.19 Silk-Accelerated=true",type:"tablet"},{"show-by-default":!1,title:"iPad Mini",screen:{horizontal:{width:1024,height:768},"device-pixel-ratio":2,vertical:{width:768,height:1024}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (iPad; CPU OS 11_0 like Mac OS X) AppleWebKit/604.1.34 (KHTML, like Gecko) Version/11.0 Mobile/15A5341f Safari/604.1",type:"tablet"},{order:140,"show-by-default":!1,title:"iPad",screen:{horizontal:{outline:{image:"@url(optimized/iPad-landscape.avif)",insets:{left:112,top:56,right:116,bottom:52}},width:1024,height:768},"device-pixel-ratio":2,vertical:{outline:{image:"@url(optimized/iPad-portrait.avif)",insets:{left:52,top:114,right:55,bottom:114}},width:768,height:1024}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (iPad; CPU OS 11_0 like Mac OS X) AppleWebKit/604.1.34 (KHTML, like Gecko) Version/11.0 Mobile/15A5341f Safari/604.1",type:"tablet"},{order:141,"show-by-default":!1,title:"iPad Pro",screen:{horizontal:{width:1366,height:1024},"device-pixel-ratio":2,vertical:{width:1024,height:1366}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (iPad; CPU OS 11_0 like Mac OS X) AppleWebKit/604.1.34 (KHTML, like Gecko) Version/11.0 Mobile/15A5341f Safari/604.1",type:"tablet"},{"show-by-default":!1,title:"Blackberry PlayBook",screen:{horizontal:{width:1024,height:600},"device-pixel-ratio":1,vertical:{width:600,height:1024}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (PlayBook; U; RIM Tablet OS 2.1.0; en-US) AppleWebKit/536.2+ (KHTML like Gecko) Version/******* Safari/536.2+",type:"tablet"},{"show-by-default":!1,title:"Nexus 10",screen:{horizontal:{width:1280,height:800},"device-pixel-ratio":2,vertical:{width:800,height:1280}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android 6.0.1; Nexus 10 Build/MOB31T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Safari/537.36","user-agent-metadata":{platform:"Android",platformVersion:"6.0.1",architecture:"",model:"Nexus 10",mobile:!1},type:"tablet"},{"show-by-default":!1,title:"Nexus 7",screen:{horizontal:{width:960,height:600},"device-pixel-ratio":2,vertical:{width:600,height:960}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android 6.0.1; Nexus 7 Build/MOB30X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Safari/537.36","user-agent-metadata":{platform:"Android",platformVersion:"6.0.1",architecture:"",model:"Nexus 7",mobile:!1},type:"tablet"},{"show-by-default":!1,title:"Galaxy Note 3",screen:{horizontal:{width:640,height:360},"device-pixel-ratio":3,vertical:{width:360,height:640}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; U; Android 4.3; en-us; SM-N900T Build/JSS15J) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30","user-agent-metadata":{platform:"Android",platformVersion:"4.3",architecture:"",model:"SM-N900T",mobile:!0},type:"phone"},{"show-by-default":!1,title:"Galaxy Note II",screen:{horizontal:{width:640,height:360},"device-pixel-ratio":2,vertical:{width:360,height:640}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; U; Android 4.1; en-us; GT-N7100 Build/JRO03C) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30","user-agent-metadata":{platform:"Android",platformVersion:"4.1",architecture:"",model:"GT-N7100",mobile:!0},type:"phone"},{"show-by-default":!1,title:h(l.laptopWithTouch),screen:{horizontal:{width:1280,height:950},"device-pixel-ratio":1,vertical:{width:950,height:1280}},capabilities:["touch"],"user-agent":"",type:"notebook",modes:[{title:"default",orientation:"horizontal"}]},{"show-by-default":!1,title:h(l.laptopWithHiDPIScreen),screen:{horizontal:{width:1440,height:900},"device-pixel-ratio":2,vertical:{width:900,height:1440}},capabilities:[],"user-agent":"",type:"notebook",modes:[{title:"default",orientation:"horizontal"}]},{"show-by-default":!1,title:h(l.laptopWithMDPIScreen),screen:{horizontal:{width:1280,height:800},"device-pixel-ratio":1,vertical:{width:800,height:1280}},capabilities:[],"user-agent":"",type:"notebook",modes:[{title:"default",orientation:"horizontal"}]},{"show-by-default":!1,title:"Moto G4",screen:{horizontal:{outline:{image:"@url(optimized/MotoG4-landscape.avif)",insets:{left:91,top:30,right:74,bottom:30}},width:640,height:360},"device-pixel-ratio":3,vertical:{outline:{image:"@url(optimized/MotoG4-portrait.avif)",insets:{left:30,top:91,right:30,bottom:74}},width:360,height:640}},capabilities:["touch","mobile"],"user-agent":"Mozilla/5.0 (Linux; Android 6.0.1; Moto G (4)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Mobile Safari/537.36","user-agent-metadata":{platform:"Android",platformVersion:"6.0.1",architecture:"",model:"Moto G (4)",mobile:!0},type:"phone"}]})),t.register("7F5tK",(function(i,o){e(i.exports,"DeviceModeView",(()=>v)),e(i.exports,"Ruler",(()=>b));var n=t("koSS8"),r=t("e7bLS"),a=t("ixFnt"),s=t("lz7WY"),l=t("9z2ZV");t("hCUlx");var d=t("17hbI"),h=t("dzfPF"),c=t("hNDuE"),u=t("bEzcW");const m={doubleclickForFullHeight:"Double-click for full height",mobileS:"Mobile S",mobileM:"Mobile M",mobileL:"Mobile L",tablet:"Tablet",laptop:"Laptop",laptopL:"Laptop L"},p=a.i18n.registerUIStrings("panels/emulation/DeviceModeView.ts",m),g=a.i18n.getLocalizedString.bind(void 0,p);class v extends l.Widget.VBox{wrapperInstance;blockElementToWidth;model;mediaInspector;showMediaInspectorSetting;showRulersSetting;topRuler;leftRuler;presetBlocks;responsivePresetsContainer;screenArea;pageArea;outlineImage;contentClip;contentArea;rightResizerElement;leftResizerElement;bottomResizerElement;bottomRightResizerElement;bottomLeftResizerElement;cachedResizable;mediaInspectorContainer;screenImage;toolbar;slowPositionStart;resizeStart;cachedCssScreenRect;cachedCssVisiblePageRect;cachedOutlineRect;cachedMediaInspectorVisible;cachedShowRulers;cachedScale;handleWidth;handleHeight;constructor(){super(!0),this.blockElementToWidth=new WeakMap,this.setMinimumSize(150,150),this.element.classList.add("device-mode-view"),this.registerRequiredCSS(u.default),this.model=d.DeviceModeModel.instance(),this.model.addEventListener("Updated",this.updateUI,this),this.mediaInspector=new(0,c.MediaQueryInspector)((()=>this.model.appliedDeviceSize().width),this.model.setWidth.bind(this.model)),this.showMediaInspectorSetting=n.Settings.Settings.instance().moduleSetting("showMediaQueryInspector"),this.showMediaInspectorSetting.addChangeListener(this.updateUI,this),this.showRulersSetting=n.Settings.Settings.instance().moduleSetting("emulation.showRulers"),this.showRulersSetting.addChangeListener(this.updateUI,this),this.topRuler=new b(!0,this.model.setWidthAndScaleToFit.bind(this.model)),this.topRuler.element.classList.add("device-mode-ruler-top"),this.leftRuler=new b(!1,this.model.setHeightAndScaleToFit.bind(this.model)),this.leftRuler.element.classList.add("device-mode-ruler-left"),this.createUI(),l.ZoomManager.ZoomManager.instance().addEventListener("ZoomChanged",this.zoomChanged,this)}createUI(){this.toolbar=new(0,h.DeviceModeToolbar)(this.model,this.showMediaInspectorSetting,this.showRulersSetting),this.contentElement.appendChild(this.toolbar.element()),this.contentClip=this.contentElement.createChild("div","device-mode-content-clip vbox"),this.responsivePresetsContainer=this.contentClip.createChild("div","device-mode-presets-container"),this.populatePresetsContainer(),this.mediaInspectorContainer=this.contentClip.createChild("div","device-mode-media-container"),this.contentArea=this.contentClip.createChild("div","device-mode-content-area"),this.outlineImage=this.contentArea.createChild("img","device-mode-outline-image hidden fill"),this.outlineImage.addEventListener("load",this.onImageLoaded.bind(this,this.outlineImage,!0),!1),this.outlineImage.addEventListener("error",this.onImageLoaded.bind(this,this.outlineImage,!1),!1),this.screenArea=this.contentArea.createChild("div","device-mode-screen-area"),this.screenImage=this.screenArea.createChild("img","device-mode-screen-image hidden"),this.screenImage.addEventListener("load",this.onImageLoaded.bind(this,this.screenImage,!0),!1),this.screenImage.addEventListener("error",this.onImageLoaded.bind(this,this.screenImage,!1),!1),this.bottomRightResizerElement=this.screenArea.createChild("div","device-mode-resizer device-mode-bottom-right-resizer"),this.bottomRightResizerElement.createChild("div",""),this.createResizer(this.bottomRightResizerElement,2,1),this.bottomLeftResizerElement=this.screenArea.createChild("div","device-mode-resizer device-mode-bottom-left-resizer"),this.bottomLeftResizerElement.createChild("div",""),this.createResizer(this.bottomLeftResizerElement,-2,1),this.rightResizerElement=this.screenArea.createChild("div","device-mode-resizer device-mode-right-resizer"),this.rightResizerElement.createChild("div",""),this.createResizer(this.rightResizerElement,2,0),this.leftResizerElement=this.screenArea.createChild("div","device-mode-resizer device-mode-left-resizer"),this.leftResizerElement.createChild("div",""),this.createResizer(this.leftResizerElement,-2,0),this.bottomResizerElement=this.screenArea.createChild("div","device-mode-resizer device-mode-bottom-resizer"),this.bottomResizerElement.createChild("div",""),this.createResizer(this.bottomResizerElement,0,1),this.bottomResizerElement.addEventListener("dblclick",this.model.setHeight.bind(this.model,0),!1),l.Tooltip.Tooltip.install(this.bottomResizerElement,g(m.doubleclickForFullHeight)),this.pageArea=this.screenArea.createChild("div","device-mode-page-area"),this.pageArea.createChild("slot")}populatePresetsContainer(){const e=[320,375,425,768,1024,1440,2560],t=[g(m.mobileS),g(m.mobileM),g(m.mobileL),g(m.tablet),g(m.laptop),g(m.laptopL),"4K"];this.presetBlocks=[];const i=this.responsivePresetsContainer.createChild("div","device-mode-presets-container-inner");for(let n=e.length-1;n>=0;--n){const r=i.createChild("div","fill device-mode-preset-bar-outer").createChild("div","device-mode-preset-bar");r.createChild("span").textContent=t[n]+" – "+e[n]+"px",r.addEventListener("click",o.bind(this,e[n]),!1),this.blockElementToWidth.set(r,e[n]),this.presetBlocks.push(r)}function o(e,t){this.model.emulate(d.Type.Responsive,null,null),this.model.setWidthAndScaleToFit(e),t.consume()}}createResizer(e,t,i){const o=new l.ResizerWidget.ResizerWidget;o.addElement(e);let n=t?"ew-resize":"ns-resize";return t*i>0&&(n="nwse-resize"),t*i<0&&(n="nesw-resize"),o.setCursor(n),o.addEventListener(l.ResizerWidget.Events.ResizeStart,this.onResizeStart,this),o.addEventListener(l.ResizerWidget.Events.ResizeUpdateXY,this.onResizeUpdate.bind(this,t,i)),o.addEventListener(l.ResizerWidget.Events.ResizeEnd,this.onResizeEnd,this),o}onResizeStart(){this.slowPositionStart=null;const e=this.model.screenRect();this.resizeStart=new l.Geometry.Size(e.width,e.height)}onResizeUpdate(e,t,i){i.data.shiftKey!==Boolean(this.slowPositionStart)&&(this.slowPositionStart=i.data.shiftKey?{x:i.data.currentX,y:i.data.currentY}:null);let o=i.data.currentX-i.data.startX,n=i.data.currentY-i.data.startY;if(this.slowPositionStart&&(o=(i.data.currentX-this.slowPositionStart.x)/10+this.slowPositionStart.x-i.data.startX,n=(i.data.currentY-this.slowPositionStart.y)/10+this.slowPositionStart.y-i.data.startY),e&&this.resizeStart){const t=o*l.ZoomManager.ZoomManager.instance().zoomFactor();let i=this.resizeStart.width+t*e;i=Math.round(i/this.model.scale()),i>=d.MinDeviceSize&&i<=d.MaxDeviceSize&&this.model.setWidth(i)}if(t&&this.resizeStart){const e=n*l.ZoomManager.ZoomManager.instance().zoomFactor();let i=this.resizeStart.height+e*t;i=Math.round(i/this.model.scale()),i>=d.MinDeviceSize&&i<=d.MaxDeviceSize&&this.model.setHeight(i)}}exitHingeMode(){this.model&&this.model.exitHingeMode()}onResizeEnd(){delete this.resizeStart,r.userMetrics.actionTaken(r.UserMetrics.Action.ResizedViewInResponsiveMode)}updateUI(){function e(e,t){e.style.left=t.left+"px",e.style.top=t.top+"px",e.style.width=t.width+"px",e.style.height=t.height+"px"}if(!this.isShowing())return;const t=l.ZoomManager.ZoomManager.instance().zoomFactor();let i=!1;const o=this.showRulersSetting.get()&&this.model.type()!==d.Type.None;let n=!1,r=!1;const a=this.model.screenRect().scale(1/t);this.cachedCssScreenRect&&a.isEqual(this.cachedCssScreenRect)||(e(this.screenArea,a),r=!0,i=!0,this.cachedCssScreenRect=a);const s=this.model.visiblePageRect().scale(1/t);this.cachedCssVisiblePageRect&&s.isEqual(this.cachedCssVisiblePageRect)||(e(this.pageArea,s),i=!0,this.cachedCssVisiblePageRect=s);const h=this.model.outlineRect();if(h){const o=h.scale(1/t);this.cachedOutlineRect&&o.isEqual(this.cachedOutlineRect)||(e(this.outlineImage,o),i=!0,this.cachedOutlineRect=o)}this.contentClip.classList.toggle("device-mode-outline-visible",Boolean(this.model.outlineImage()));const c=this.model.type()===d.Type.Responsive;c!==this.cachedResizable&&(this.rightResizerElement.classList.toggle("hidden",!c),this.leftResizerElement.classList.toggle("hidden",!c),this.bottomResizerElement.classList.toggle("hidden",!c),this.bottomRightResizerElement.classList.toggle("hidden",!c),this.bottomLeftResizerElement.classList.toggle("hidden",!c),this.cachedResizable=c);const u=this.showMediaInspectorSetting.get()&&this.model.type()!==d.Type.None;if(u!==this.cachedMediaInspectorVisible&&(u?this.mediaInspector.show(this.mediaInspectorContainer):this.mediaInspector.detach(),n=!0,i=!0,this.cachedMediaInspectorVisible=u),o!==this.cachedShowRulers&&(this.contentClip.classList.toggle("device-mode-rulers-visible",o),o?(this.topRuler.show(this.contentArea),this.leftRuler.show(this.contentArea)):(this.topRuler.detach(),this.leftRuler.detach()),n=!0,i=!0,this.cachedShowRulers=o),this.model.scale()!==this.cachedScale){r=!0,i=!0;for(const e of this.presetBlocks){const t=this.blockElementToWidth.get(e);if(!t)throw new Error("Could not get width for block.");e.style.width=t*this.model.scale()+"px"}this.cachedScale=this.model.scale()}this.toolbar.update(),this.loadImage(this.screenImage,this.model.screenImage()),this.loadImage(this.outlineImage,this.model.outlineImage()),this.mediaInspector.setAxisTransform(this.model.scale()),i&&this.doResize(),r&&(this.topRuler.render(this.model.scale()),this.leftRuler.render(this.model.scale()),this.topRuler.element.positionAt(this.cachedCssScreenRect?this.cachedCssScreenRect.left:0,this.cachedCssScreenRect?this.cachedCssScreenRect.top:0),this.leftRuler.element.positionAt(this.cachedCssScreenRect?this.cachedCssScreenRect.left:0,this.cachedCssScreenRect?this.cachedCssScreenRect.top:0)),n&&this.contentAreaResized()}loadImage(e,t){e.getAttribute("srcset")!==t&&(e.setAttribute("srcset",t),t||e.classList.toggle("hidden",!0))}onImageLoaded(e,t){e.classList.toggle("hidden",!t)}setNonEmulatedAvailableSize(e){if(this.model.type()!==d.Type.None)return;const t=l.ZoomManager.ZoomManager.instance().zoomFactor(),i=e.getBoundingClientRect(),o=new l.Geometry.Size(Math.max(i.width*t,1),Math.max(i.height*t,1));this.model.setAvailableSize(o,o)}contentAreaResized(){const e=l.ZoomManager.ZoomManager.instance().zoomFactor(),t=this.contentArea.getBoundingClientRect(),i=new l.Geometry.Size(Math.max(t.width*e,1),Math.max(t.height*e,1)),o=new l.Geometry.Size(Math.max((t.width-2*(this.handleWidth||0))*e,1),Math.max((t.height-(this.handleHeight||0))*e,1));this.model.setAvailableSize(i,o)}measureHandles(){const e=this.rightResizerElement.classList.contains("hidden");this.rightResizerElement.classList.toggle("hidden",!1),this.bottomResizerElement.classList.toggle("hidden",!1),this.handleWidth=this.rightResizerElement.offsetWidth,this.handleHeight=this.bottomResizerElement.offsetHeight,this.rightResizerElement.classList.toggle("hidden",e),this.bottomResizerElement.classList.toggle("hidden",e)}zoomChanged(){delete this.handleWidth,delete this.handleHeight,this.isShowing()&&(this.measureHandles(),this.contentAreaResized())}onResize(){this.isShowing()&&this.contentAreaResized()}wasShown(){this.measureHandles(),this.toolbar.restore()}willHide(){this.model.emulate(d.Type.None,null,null)}async captureScreenshot(){const e=await this.model.captureScreenshot(!1);if(null===e)return;const t=new Image;t.src="data:image/png;base64,"+e,t.onload=async()=>{const e=t.naturalWidth/this.model.screenRect().width,i=this.model.outlineRect();if(!i)throw new Error("Unable to take screenshot: no outlineRect available.");const o=i.scale(e),n=this.model.screenRect().scale(e),r=this.model.visiblePageRect().scale(e),a=n.left+r.left-o.left,s=n.top+r.top-o.top,l=document.createElement("canvas");l.width=Math.floor(o.width),l.height=Math.min(16384,Math.floor(o.height));const d=l.getContext("2d");if(!d)throw new Error("Could not get 2d context from canvas.");d.imageSmoothingEnabled=!1,this.model.outlineImage()&&await this.paintImage(d,this.model.outlineImage(),o.relativeTo(o)),this.model.screenImage()&&await this.paintImage(d,this.model.screenImage(),n.relativeTo(o)),d.drawImage(t,Math.floor(a),Math.floor(s)),this.saveScreenshot(l)}}async captureFullSizeScreenshot(){const e=await this.model.captureScreenshot(!0);if(null!==e)return this.saveScreenshotBase64(e)}async captureAreaScreenshot(e){const t=await this.model.captureScreenshot(!1,e);if(null!==t)return this.saveScreenshotBase64(t)}saveScreenshotBase64(e){const t=new Image;t.src="data:image/png;base64,"+e,t.onload=()=>{const e=document.createElement("canvas");e.width=t.naturalWidth,e.height=Math.min(16384,Math.floor(t.naturalHeight));const i=e.getContext("2d");if(!i)throw new Error("Could not get 2d context for base64 screenshot.");i.imageSmoothingEnabled=!1,i.drawImage(t,0,0),this.saveScreenshot(e)}}paintImage(e,t,i){return new Promise((o=>{const n=new Image;n.crossOrigin="Anonymous",n.srcset=t,n.onerror=()=>o(),n.onload=()=>{e.drawImage(n,i.left,i.top,i.width,i.height),o()}}))}saveScreenshot(e){const t=this.model.inspectedURL();let i="";if(t){const e=s.StringUtilities.removeURLFragment(t);i=s.StringUtilities.trimURL(e)}const o=this.model.device();o&&this.model.type()===d.Type.Device&&(i+=`(${o.title})`);const n=document.createElement("a");n.download=i+".png",e.toBlob((e=>{null!==e&&(n.href=URL.createObjectURL(e),n.click())}))}}class b extends l.Widget.VBox{contentElementInternal;horizontal;scale;count;throttler;applyCallback;renderedScale;renderedZoomFactor;constructor(e,t){super(),this.element.classList.add("device-mode-ruler"),this.contentElementInternal=this.element.createChild("div","device-mode-ruler-content").createChild("div","device-mode-ruler-inner"),this.horizontal=e,this.scale=1,this.count=0,this.throttler=new n.Throttler.Throttler(0),this.applyCallback=t}render(e){this.scale=e,this.throttler.schedule(this.update.bind(this))}onResize(){this.throttler.schedule(this.update.bind(this))}update(){const e=l.ZoomManager.ZoomManager.instance().zoomFactor(),t=this.horizontal?this.contentElementInternal.offsetWidth:this.contentElementInternal.offsetHeight;this.scale===this.renderedScale&&e===this.renderedZoomFactor||(this.contentElementInternal.removeChildren(),this.count=0,this.renderedScale=this.scale,this.renderedZoomFactor=e);const i=t*e/this.scale,o=Math.ceil(i/5);let n=1;this.scale<.8&&(n=2),this.scale<.6&&(n=4),this.scale<.4&&(n=8),this.scale<.2&&(n=16),this.scale<.1&&(n=32);for(let e=o;e<this.count;e++)if(!(e%n)){const e=this.contentElementInternal.lastChild;e&&e.remove()}for(let t=this.count;t<o;t++){if(t%n)continue;const i=this.contentElementInternal.createChild("div","device-mode-ruler-marker");if(t&&(this.horizontal?i.style.left=5*t*this.scale/e+"px":i.style.top=5*t*this.scale/e+"px",!(t%20))){const e=i.createChild("div","device-mode-ruler-text");e.textContent=String(5*t),e.addEventListener("click",this.onMarkerClick.bind(this,5*t),!1)}t%10?t%5||i.classList.add("device-mode-ruler-marker-medium"):i.classList.add("device-mode-ruler-marker-large")}return this.count=o,Promise.resolve()}onMarkerClick(e){this.applyCallback.call(null,e)}}})),t.register("dzfPF",(function(i,o){e(i.exports,"DeviceModeToolbar",(()=>S));var n=t("koSS8"),r=t("e7bLS"),a=t("ixFnt"),s=t("9X2mn");t("hCUlx");var l=t("8PHbc"),d=t("17hbI"),h=t("9z2ZV"),c=t("8fwyZ");t("goiNQ");var u=t("4mMFH"),m=t("dcc07");const p={dimensions:"Dimensions",width:"Width",heightLeaveEmptyForFull:"Height (leave empty for full)",zoom:"Zoom",devicePixelRatio:"Device pixel ratio",deviceType:"Device type",experimentalWebPlatformFeature:'"`Experimental Web Platform Feature`" flag is enabled. Click to disable it.',experimentalWebPlatformFeatureFlag:'"`Experimental Web Platform Feature`" flag is disabled. Click to enable it.',moreOptions:"More options",fitToWindowF:"Fit to window ({PH1}%)",autoadjustZoom:"Auto-adjust zoom",defaultF:"Default: {PH1}",hideDeviceFrame:"Hide device frame",showDeviceFrame:"Show device frame",hideMediaQueries:"Hide media queries",showMediaQueries:"Show media queries",hideRulers:"Hide rulers",showRulers:"Show rulers",removeDevicePixelRatio:"Remove device pixel ratio",addDevicePixelRatio:"Add device pixel ratio",removeDeviceType:"Remove device type",addDeviceType:"Add device type",resetToDefaults:"Reset to defaults",closeDevtools:"Close DevTools",responsive:"Responsive",edit:"Edit…",portrait:"Portrait",landscape:"Landscape",rotate:"Rotate",none:"None",screenOrientationOptions:"Screen orientation options",toggleDualscreenMode:"Toggle dual-screen mode"},g=a.i18n.registerUIStrings("panels/emulation/DeviceModeToolbar.ts",p),v=a.i18n.getLocalizedString.bind(void 0,g);function b(e,t){e.setTitle(t),e.element.title=t}class S{model;showMediaInspectorSetting;showRulersSetting;experimentDualScreenSupport;deviceOutlineSetting;showDeviceScaleFactorSetting;showUserAgentTypeSetting;autoAdjustScaleSetting;lastMode;elementInternal;emulatedDevicesList;persistenceSetting;spanButton;modeButton;widthInput;heightInput;deviceScaleItem;deviceSelectItem;scaleItem;uaItem;experimentalButton;cachedDeviceScale;cachedUaType;xItem;throttlingConditionsItem;cachedModelType;cachedScale;cachedModelDevice;cachedModelMode;constructor(e,t,i){this.model=e,this.showMediaInspectorSetting=t,this.showRulersSetting=i,this.experimentDualScreenSupport=s.Runtime.experiments.isEnabled("dualScreenSupport"),this.deviceOutlineSetting=this.model.deviceOutlineSetting(),this.showDeviceScaleFactorSetting=n.Settings.Settings.instance().createSetting("emulation.showDeviceScaleFactor",!1),this.showDeviceScaleFactorSetting.addChangeListener(this.updateDeviceScaleFactorVisibility,this),this.showUserAgentTypeSetting=n.Settings.Settings.instance().createSetting("emulation.showUserAgentType",!1),this.showUserAgentTypeSetting.addChangeListener(this.updateUserAgentTypeVisibility,this),this.autoAdjustScaleSetting=n.Settings.Settings.instance().createSetting("emulation.autoAdjustScale",!0),this.lastMode=new Map,this.elementInternal=document.createElement("div"),this.elementInternal.classList.add("device-mode-toolbar");const o=this.elementInternal.createChild("div","device-mode-toolbar-spacer");o.createChild("div","device-mode-toolbar-spacer");const r=new h.Toolbar.Toolbar("",o);this.fillLeftToolbar(r);const a=new h.Toolbar.Toolbar("",this.elementInternal);a.makeWrappable(),this.widthInput=new u.SizeInputElement(v(p.width)),this.widthInput.addEventListener("sizechanged",(({size:e})=>{this.model.setWidthAndScaleToFit(e)})),this.heightInput=new u.SizeInputElement(v(p.heightLeaveEmptyForFull)),this.heightInput.addEventListener("sizechanged",(({size:e})=>{this.model.setHeightAndScaleToFit(e)})),this.fillMainToolbar(a);const d=this.elementInternal.createChild("div","device-mode-toolbar-spacer"),c=new h.Toolbar.Toolbar("device-mode-toolbar-fixed-size",d);c.makeWrappable(),this.fillRightToolbar(c);const m=new h.Toolbar.Toolbar("device-mode-toolbar-fixed-size",d);m.makeWrappable(),this.fillModeToolbar(m),d.createChild("div","device-mode-toolbar-spacer");const g=new h.Toolbar.Toolbar("device-mode-toolbar-options",d);function b(){const t=e.toolbarControlsEnabledSetting().get();r.setEnabled(t),a.setEnabled(t),c.setEnabled(t),m.setEnabled(t),g.setEnabled(t)}g.makeWrappable(),this.fillOptionsToolbar(g),this.emulatedDevicesList=l.EmulatedDevicesList.instance(),this.emulatedDevicesList.addEventListener("CustomDevicesUpdated",this.deviceListChanged,this),this.emulatedDevicesList.addEventListener("StandardDevicesUpdated",this.deviceListChanged,this),this.persistenceSetting=n.Settings.Settings.instance().createSetting("emulation.deviceModeValue",{device:"",orientation:"",mode:""}),this.model.toolbarControlsEnabledSetting().addChangeListener(b),b()}createEmptyToolbarElement(){const e=document.createElement("div");return e.classList.add("device-mode-empty-toolbar-element"),e}fillLeftToolbar(e){e.appendToolbarItem(this.wrapToolbarItem(this.createEmptyToolbarElement())),this.deviceSelectItem=new h.Toolbar.ToolbarMenuButton(this.appendDeviceMenuItems.bind(this)),this.deviceSelectItem.setGlyph(""),this.deviceSelectItem.turnIntoSelect(!0),this.deviceSelectItem.setDarkText(),e.appendToolbarItem(this.deviceSelectItem)}fillMainToolbar(e){e.appendToolbarItem(new h.Toolbar.ToolbarItem(this.widthInput));const t=document.createElement("div");t.classList.add("device-mode-x"),t.textContent="×",this.xItem=this.wrapToolbarItem(t),e.appendToolbarItem(this.xItem),e.appendToolbarItem(new h.Toolbar.ToolbarItem(this.heightInput))}fillRightToolbar(e){e.appendToolbarItem(this.wrapToolbarItem(this.createEmptyToolbarElement())),this.scaleItem=new h.Toolbar.ToolbarMenuButton(this.appendScaleMenuItems.bind(this)),b(this.scaleItem,v(p.zoom)),this.scaleItem.setGlyph(""),this.scaleItem.turnIntoSelect(),this.scaleItem.setDarkText(),e.appendToolbarItem(this.scaleItem),e.appendToolbarItem(this.wrapToolbarItem(this.createEmptyToolbarElement())),this.deviceScaleItem=new h.Toolbar.ToolbarMenuButton(this.appendDeviceScaleMenuItems.bind(this)),this.deviceScaleItem.setVisible(this.showDeviceScaleFactorSetting.get()),b(this.deviceScaleItem,v(p.devicePixelRatio)),this.deviceScaleItem.setGlyph(""),this.deviceScaleItem.turnIntoSelect(),this.deviceScaleItem.setDarkText(),e.appendToolbarItem(this.deviceScaleItem),e.appendToolbarItem(this.wrapToolbarItem(this.createEmptyToolbarElement())),this.uaItem=new h.Toolbar.ToolbarMenuButton(this.appendUserAgentMenuItems.bind(this)),this.uaItem.setVisible(this.showUserAgentTypeSetting.get()),b(this.uaItem,v(p.deviceType)),this.uaItem.setGlyph(""),this.uaItem.turnIntoSelect(),this.uaItem.setDarkText(),e.appendToolbarItem(this.uaItem),this.throttlingConditionsItem=c.ThrottlingManager.throttlingManager().createMobileThrottlingButton(),e.appendToolbarItem(this.throttlingConditionsItem)}fillModeToolbar(e){e.appendToolbarItem(this.wrapToolbarItem(this.createEmptyToolbarElement())),this.modeButton=new h.Toolbar.ToolbarButton("","largeicon-rotate-screen"),this.modeButton.addEventListener(h.Toolbar.ToolbarButton.Events.Click,this.modeMenuClicked,this),e.appendToolbarItem(this.modeButton),this.experimentDualScreenSupport&&(this.spanButton=new h.Toolbar.ToolbarButton("","largeicon-dual-screen"),this.spanButton.addEventListener(h.Toolbar.ToolbarButton.Events.Click,this.spanClicked,this),e.appendToolbarItem(this.spanButton),this.createExperimentalButton(e))}createExperimentalButton(e){e.appendToolbarItem(new h.Toolbar.ToolbarSeparator(!0));const t=this.model.webPlatformExperimentalFeaturesEnabled()?v(p.experimentalWebPlatformFeature):v(p.experimentalWebPlatformFeatureFlag);this.experimentalButton=new h.Toolbar.ToolbarToggle(t,"largeicon-experimental-api"),this.experimentalButton.setToggled(this.model.webPlatformExperimentalFeaturesEnabled()),this.experimentalButton.setEnabled(!0),this.experimentalButton.addEventListener(h.Toolbar.ToolbarButton.Events.Click,this.experimentalClicked,this),e.appendToolbarItem(this.experimentalButton)}experimentalClicked(){r.InspectorFrontendHost.InspectorFrontendHostInstance.openInNewTab("chrome://flags/#enable-experimental-web-platform-features")}fillOptionsToolbar(e){e.appendToolbarItem(this.wrapToolbarItem(this.createEmptyToolbarElement()));const t=new h.Toolbar.ToolbarMenuButton(this.appendOptionsMenuItems.bind(this));b(t,v(p.moreOptions)),e.appendToolbarItem(t)}appendScaleMenuItems(e){this.model.type()===d.Type.Device&&e.footerSection().appendItem(v(p.fitToWindowF,{PH1:this.getPrettyFitZoomPercentage()}),this.onScaleMenuChanged.bind(this,this.model.fitScale()),!1),e.footerSection().appendCheckboxItem(v(p.autoadjustZoom),this.onAutoAdjustScaleChanged.bind(this),this.autoAdjustScaleSetting.get());const t=function(t,i){e.defaultSection().appendCheckboxItem(t,this.onScaleMenuChanged.bind(this,i),this.model.scaleSetting().get()===i,!1)}.bind(this);t("50%",.5),t("75%",.75),t("100%",1),t("125%",1.25),t("150%",1.5)}onScaleMenuChanged(e){this.model.scaleSetting().set(e)}onAutoAdjustScaleChanged(){this.autoAdjustScaleSetting.set(!this.autoAdjustScaleSetting.get())}appendDeviceScaleMenuItems(e){const t=this.model.deviceScaleFactorSetting(),i=this.model.uaSetting().get()===d.UA.Mobile||this.model.uaSetting().get()===d.UA.MobileNoTouch?d.defaultMobileScaleFactor:window.devicePixelRatio;function o(e,i,o){e.appendCheckboxItem(i,t.set.bind(t,o),t.get()===o)}o(e.headerSection(),v(p.defaultF,{PH1:i}),0),o(e.defaultSection(),"1",1),o(e.defaultSection(),"2",2),o(e.defaultSection(),"3",3)}appendUserAgentMenuItems(e){const t=this.model.uaSetting();function i(i,o){e.defaultSection().appendCheckboxItem(i,t.set.bind(t,o),t.get()===o)}i(d.UA.Mobile,d.UA.Mobile),i(d.UA.MobileNoTouch,d.UA.MobileNoTouch),i(d.UA.Desktop,d.UA.Desktop),i(d.UA.DesktopTouch,d.UA.DesktopTouch)}appendOptionsMenuItems(e){const t=this.model;function i(e,i,o,n,r){void 0===r&&(r=t.type()===d.Type.None),e.appendItem(i.get()?o:n,i.set.bind(i,!i.get()),r)}i(e.headerSection(),this.deviceOutlineSetting,v(p.hideDeviceFrame),v(p.showDeviceFrame),t.type()!==d.Type.Device),i(e.headerSection(),this.showMediaInspectorSetting,v(p.hideMediaQueries),v(p.showMediaQueries)),i(e.headerSection(),this.showRulersSetting,v(p.hideRulers),v(p.showRulers)),i(e.defaultSection(),this.showDeviceScaleFactorSetting,v(p.removeDevicePixelRatio),v(p.addDevicePixelRatio)),i(e.defaultSection(),this.showUserAgentTypeSetting,v(p.removeDeviceType),v(p.addDeviceType)),e.appendItemsAtLocation("deviceModeMenu"),e.footerSection().appendItem(v(p.resetToDefaults),this.reset.bind(this)),e.footerSection().appendItem(v(p.closeDevtools),r.InspectorFrontendHost.InspectorFrontendHostInstance.closeWindow.bind(r.InspectorFrontendHost.InspectorFrontendHostInstance))}reset(){this.deviceOutlineSetting.set(!1),this.showDeviceScaleFactorSetting.set(!1),this.showUserAgentTypeSetting.set(!1),this.showMediaInspectorSetting.set(!1),this.showRulersSetting.set(!1),this.model.reset()}wrapToolbarItem(e){const t=document.createElement("div");return h.Utils.createShadowRootWithCoreStyles(t,{cssFile:m.default,delegatesFocus:void 0}).appendChild(e),new h.Toolbar.ToolbarItem(t)}emulateDevice(e){const t=this.autoAdjustScaleSetting.get()?void 0:this.model.scaleSetting().get();this.model.emulate(d.Type.Device,e,this.lastMode.get(e)||e.modes[0],t)}switchToResponsive(){this.model.emulate(d.Type.Responsive,null,null)}filterDevices(e){return(e=e.filter((function(e){return e.show()}))).sort(l.EmulatedDevice.deviceComparator),e}standardDevices(){return this.filterDevices(this.emulatedDevicesList.standard())}customDevices(){return this.filterDevices(this.emulatedDevicesList.custom())}allDevices(){return this.standardDevices().concat(this.customDevices())}appendDeviceMenuItems(e){function t(t){if(!t.length)return;const i=e.section();for(const e of t)i.appendCheckboxItem(e.title,this.emulateDevice.bind(this,e),this.model.device()===e,!1)}e.headerSection().appendCheckboxItem(v(p.responsive),this.switchToResponsive.bind(this),this.model.type()===d.Type.Responsive,!1),t.call(this,this.standardDevices()),t.call(this,this.customDevices()),e.footerSection().appendItem(v(p.edit),this.emulatedDevicesList.revealCustomSetting.bind(this.emulatedDevicesList),!1)}deviceListChanged(){const e=this.model.device();if(!e)return;const t=this.allDevices();-1===t.indexOf(e)?t.length?this.emulateDevice(t[0]):this.model.emulate(d.Type.Responsive,null,null):this.emulateDevice(e)}updateDeviceScaleFactorVisibility(){this.deviceScaleItem&&this.deviceScaleItem.setVisible(this.showDeviceScaleFactorSetting.get())}updateUserAgentTypeVisibility(){this.uaItem&&this.uaItem.setVisible(this.showUserAgentTypeSetting.get())}spanClicked(){const e=this.model.device();if(!e||!e.isDualScreen)return;const t=this.autoAdjustScaleSetting.get()?void 0:this.model.scaleSetting().get(),i=this.model.mode();if(!i)return;const o=e.getSpanPartner(i);o&&this.model.emulate(this.model.type(),e,o,t)}modeMenuClicked(e){const t=this.model.device(),i=this.model,o=this.autoAdjustScaleSetting;if(i.type()===d.Type.Responsive){const e=i.appliedDeviceSize();return void(o.get()?i.setSizeAndScaleToFit(e.height,e.width):(i.setWidth(e.height),i.setHeight(e.width)))}if(!t)return;if((t.isDualScreen||2===t.modes.length)&&t.modes[0].orientation!==t.modes[1].orientation){const e=o.get()?void 0:i.scaleSetting().get(),n=i.mode();if(!n)return;const r=t.getRotationPartner(n);if(!r)return;return void i.emulate(i.type(),i.device(),r,e)}if(!this.modeButton)return;const n=new h.ContextMenu.ContextMenu(e.data,{useSoftMenu:!1,x:this.modeButton.element.totalOffsetLeft(),y:this.modeButton.element.totalOffsetTop()+this.modeButton.element.offsetHeight});function r(e,i){if(!t)return;const o=t.modesForOrientation(e);if(o.length)if(1===o.length)a(o[0],i);else for(let e=0;e<o.length;e++)a(o[e],i+" – "+o[e].title)}function a(e,t){n.defaultSection().appendCheckboxItem(t,s.bind(null,e),i.mode()===e,!1)}function s(e){const t=o.get()?void 0:i.scaleSetting().get();i.emulate(i.type(),i.device(),e,t)}r(l.Vertical,v(p.portrait)),r(l.Horizontal,v(p.landscape)),n.show()}getPrettyFitZoomPercentage(){return`${(100*this.model.fitScale()).toFixed(0)}`}getPrettyZoomPercentage(){return`${(100*this.model.scale()).toFixed(0)}`}element(){return this.elementInternal}update(){this.model.type()!==this.cachedModelType&&(this.cachedModelType=this.model.type(),this.widthInput.disabled=this.model.type()!==d.Type.Responsive,this.heightInput.disabled=this.model.type()!==d.Type.Responsive,this.deviceScaleItem.setEnabled(this.model.type()===d.Type.Responsive),this.uaItem.setEnabled(this.model.type()===d.Type.Responsive),this.model.type()===d.Type.Responsive?(this.modeButton.setEnabled(!0),b(this.modeButton,v(p.rotate))):this.modeButton.setEnabled(!1));const e=this.model.appliedDeviceSize();this.widthInput.size=String(e.width),this.heightInput.size=this.model.type()===d.Type.Responsive&&this.model.isFullHeight()?"":String(e.height),this.heightInput.placeholder=String(e.height),this.model.scale()!==this.cachedScale&&(this.scaleItem.setText(`${this.getPrettyZoomPercentage()}%`),this.cachedScale=this.model.scale());const t=this.model.appliedDeviceScaleFactor();t!==this.cachedDeviceScale&&(this.deviceScaleItem.setText(`DPR: ${t.toFixed(1)}`),this.cachedDeviceScale=t);const i=this.model.appliedUserAgentType();i!==this.cachedUaType&&(this.uaItem.setText(i),this.cachedUaType=i);let o=v(p.none);this.model.type()===d.Type.Responsive&&(o=v(p.responsive));const n=this.model.device();if(this.model.type()===d.Type.Device&&n&&(o=n.title),this.deviceSelectItem.setText(`${v(p.dimensions)}: ${o}`),this.model.device()!==this.cachedModelDevice){const e=this.model.device();if(e){const t=e?e.modes.length:0;this.modeButton.setEnabled(t>=2),b(this.modeButton,v(2===t?p.rotate:p.screenOrientationOptions))}this.cachedModelDevice=e}if(this.experimentDualScreenSupport&&this.experimentalButton){const e=this.model.device();e&&e.isDualScreen?(this.spanButton.setVisible(!0),this.experimentalButton.setVisible(!0)):(this.spanButton.setVisible(!1),this.experimentalButton.setVisible(!1)),b(this.spanButton,v(p.toggleDualscreenMode))}if(this.model.type()===d.Type.Device&&this.lastMode.set(this.model.device(),this.model.mode()),this.model.mode()!==this.cachedModelMode&&this.model.type()!==d.Type.None){this.cachedModelMode=this.model.mode();const e=this.persistenceSetting.get(),t=this.model.device();if(t){e.device=t.title;const i=this.model.mode();e.orientation=i?i.orientation:"",e.mode=i?i.title:""}else e.device="",e.orientation="",e.mode="";this.persistenceSetting.set(e)}}restore(){for(const e of this.allDevices())if(e.title===this.persistenceSetting.get().device)for(const t of e.modes)if(t.orientation===this.persistenceSetting.get().orientation&&t.title===this.persistenceSetting.get().mode)return this.lastMode.set(e,t),void this.emulateDevice(e);this.model.emulate(d.Type.Responsive,null,null)}}})),t.register("goiNQ",(function(i,o){e(i.exports,"DeviceSizeInputElement",(()=>t("4mMFH")));t("4mMFH")})),t.register("4mMFH",(function(i,o){e(i.exports,"SizeInputElement",(()=>h)),t("hCUlx");var n=t("17hbI"),r=t("kpUjp"),a=t("dS5IF"),s=t("9z2ZV");class l extends Event{size;static eventName="sizechanged";constructor(e){super(l.eventName),this.size=e}}function d(e){return Number(e.target.value)}class h extends HTMLElement{#R=this.attachShadow({mode:"open"});#P=!1;#F="0";#W="";#N;static litTagName=a.literal`device-mode-emulation-size-input`;constructor(e){super(),this.#N=e}connectedCallback(){this.render()}set disabled(e){this.#P=e,this.render()}set size(e){this.#F=e,this.render()}set placeholder(e){this.#W=e,this.render()}render(){a.render(a.html`
      <style>
        input {
          /*
           * 4 characters for the maximum size of the value,
           * 2 characters for the width of the step-buttons,
           * 2 pixels padding between the characters and the
           * step-buttons.
           */
          width: calc(4ch + 2ch + 2px);
          max-height: 18px;
          margin: 0 2px;
          text-align: center;
          font-size: inherit;
          font-family: inherit;
        }

        input:disabled {
          user-select: none;
        }

        input:focus::-webkit-input-placeholder {
          color: transparent;
        }
      </style>
      <input type="number"
             max=${n.MaxDeviceSize}
             min=${n.MinDeviceSize}
             maxlength="4"
             title=${this.#N}
             placeholder=${this.#W}
             ?disabled=${this.#P}
             .value=${this.#F}
             @change=${this.#O}
             @keydown=${this.#V} />
    `,this.#R,{host:this})}#O(e){this.dispatchEvent(new l(d(e)))}#V(e){let t=s.UIUtils.modifiedFloatNumber(d(e),e);null!==t&&(t=Math.min(t,n.MaxDeviceSize),t=Math.max(t,n.MinDeviceSize),e.preventDefault(),e.target.value=String(t),this.dispatchEvent(new l(t)))}}r.CustomElements.defineComponent("device-mode-emulation-size-input",h)})),t.register("dcc07",(function(t,i){e(t.exports,"default",(()=>o));var o={cssContent:"/*\n * Copyright 2015 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.device-mode-x {\n  margin: 0 1px;\n  font-size: 16px;\n}\n\n.device-mode-empty-toolbar-element {\n  width: 0;\n}\n"}})),t.register("hNDuE",(function(i,o){e(i.exports,"MediaQueryInspector",(()=>p)),e(i.exports,"MediaQueryUIModel",(()=>g));var n=t("koSS8"),r=t("ixFnt"),a=t("lz7WY"),s=t("eQFvP"),l=t("fMswD"),d=t("9z2ZV"),h=t("2QMQ2");const c={revealInSourceCode:"Reveal in source code"},u=r.i18n.registerUIStrings("panels/emulation/MediaQueryInspector.ts",c),m=r.i18n.getLocalizedString.bind(void 0,u);class p extends d.Widget.Widget{mediaThrottler;getWidthCallback;setWidthCallback;scale;elementsToMediaQueryModel;elementsToCSSLocations;cssModel;cachedQueryModels;constructor(e,t){super(!0),this.registerRequiredCSS(h.default),this.contentElement.classList.add("media-inspector-view"),this.contentElement.addEventListener("click",this.onMediaQueryClicked.bind(this),!1),this.contentElement.addEventListener("contextmenu",this.onContextMenu.bind(this),!1),this.mediaThrottler=new n.Throttler.Throttler(0),this.getWidthCallback=e,this.setWidthCallback=t,this.scale=1,this.elementsToMediaQueryModel=new WeakMap,this.elementsToCSSLocations=new WeakMap,s.TargetManager.TargetManager.instance().observeModels(s.CSSModel.CSSModel,this),d.ZoomManager.ZoomManager.instance().addEventListener("ZoomChanged",this.renderMediaQueries.bind(this),this)}modelAdded(e){this.cssModel||(this.cssModel=e,this.cssModel.addEventListener(s.CSSModel.Events.StyleSheetAdded,this.scheduleMediaQueriesUpdate,this),this.cssModel.addEventListener(s.CSSModel.Events.StyleSheetRemoved,this.scheduleMediaQueriesUpdate,this),this.cssModel.addEventListener(s.CSSModel.Events.StyleSheetChanged,this.scheduleMediaQueriesUpdate,this),this.cssModel.addEventListener(s.CSSModel.Events.MediaQueryResultChanged,this.scheduleMediaQueriesUpdate,this))}modelRemoved(e){e===this.cssModel&&(this.cssModel.removeEventListener(s.CSSModel.Events.StyleSheetAdded,this.scheduleMediaQueriesUpdate,this),this.cssModel.removeEventListener(s.CSSModel.Events.StyleSheetRemoved,this.scheduleMediaQueriesUpdate,this),this.cssModel.removeEventListener(s.CSSModel.Events.StyleSheetChanged,this.scheduleMediaQueriesUpdate,this),this.cssModel.removeEventListener(s.CSSModel.Events.MediaQueryResultChanged,this.scheduleMediaQueriesUpdate,this),delete this.cssModel)}setAxisTransform(e){Math.abs(this.scale-e)<1e-8||(this.scale=e,this.renderMediaQueries())}onMediaQueryClicked(e){const t=e.target.enclosingNodeOrSelfWithClass("media-inspector-bar");if(!t)return;const i=this.elementsToMediaQueryModel.get(t);if(!i)return;const o=i.maxWidthExpression(),n=i.minWidthExpression();if(0===i.section())return void this.setWidthCallback(o&&o.computedLength()||0);if(2===i.section())return void this.setWidthCallback(n&&n.computedLength()||0);const r=this.getWidthCallback();n&&r!==n.computedLength()?this.setWidthCallback(n.computedLength()||0):this.setWidthCallback(o&&o.computedLength()||0)}onContextMenu(e){if(!this.cssModel||!this.cssModel.isEnabled())return;const t=e.target.enclosingNodeOrSelfWithClass("media-inspector-bar");if(!t)return;const i=this.elementsToCSSLocations.get(t)||[],o=new Map;for(let e=0;e<i.length;++e){const t=l.CSSWorkspaceBinding.CSSWorkspaceBinding.instance().rawLocationToUILocation(i[e]);if(!t)continue;const n="number"==typeof t.columnNumber?a.StringUtilities.sprintf("%s:%d:%d",t.uiSourceCode.url(),t.lineNumber+1,t.columnNumber+1):a.StringUtilities.sprintf("%s:%d",t.uiSourceCode.url(),t.lineNumber+1);o.set(n,t)}const n=[...o.keys()].sort(),r=new d.ContextMenu.ContextMenu(e),s=r.defaultSection().appendSubMenuItem(m(c.revealInSourceCode));for(let e=0;e<n.length;++e){const t=n[e];s.defaultSection().appendItem(t,this.revealSourceLocation.bind(this,o.get(t)))}r.show()}revealSourceLocation(e){n.Revealer.reveal(e)}scheduleMediaQueriesUpdate(){this.isShowing()&&this.mediaThrottler.schedule(this.refetchMediaQueries.bind(this))}refetchMediaQueries(){return this.isShowing()&&this.cssModel?this.cssModel.getMediaQueries().then(this.rebuildMediaQueries.bind(this)):Promise.resolve()}squashAdjacentEqual(e){const t=[];for(let i=0;i<e.length;++i){const o=t[t.length-1];o&&o.equals(e[i])||t.push(e[i])}return t}rebuildMediaQueries(e){let t=[];for(let i=0;i<e.length;++i){const o=e[i];if(o.mediaList)for(let e=0;e<o.mediaList.length;++e){const i=o.mediaList[e],n=g.createFromMediaQuery(o,i);n&&t.push(n)}}t.sort((function(e,t){return e.compareTo(t)})),t=this.squashAdjacentEqual(t);let i=this.cachedQueryModels&&this.cachedQueryModels.length===t.length;for(let e=0;i&&e<t.length;++e)i=i&&this.cachedQueryModels&&this.cachedQueryModels[e].equals(t[e]);i||(this.cachedQueryModels=t,this.renderMediaQueries())}renderMediaQueries(){if(!this.cachedQueryModels||!this.isShowing())return;const e=[];let t=null;for(let i=0;i<this.cachedQueryModels.length;++i){const o=this.cachedQueryModels[i];t&&t.model.dimensionsEqual(o)?t.active=t.active||o.active():(t={active:o.active(),model:o,locations:[]},e.push(t));const n=o.rawLocation();n&&t.locations.push(n)}this.contentElement.removeChildren();let i=null;for(let t=0;t<e.length;++t){t&&e[t].model.section()===e[t-1].model.section()||(i=this.contentElement.createChild("div","media-inspector-marker-container"));const o=e[t],n=this.createElementFromMediaQueryModel(o.model);if(this.elementsToMediaQueryModel.set(n,o.model),this.elementsToCSSLocations.set(n,o.locations),n.classList.toggle("media-inspector-marker-inactive",!o.active),!i)throw new Error("Could not find container to render media queries into.");i.appendChild(n)}}zoomFactor(){return d.ZoomManager.ZoomManager.instance().zoomFactor()/this.scale}wasShown(){super.wasShown(),this.scheduleMediaQueriesUpdate()}createElementFromMediaQueryModel(e){const t=this.zoomFactor(),i=e.minWidthExpression(),o=e.maxWidthExpression(),n=i?(i.computedLength()||0)/t:0,r=o?(o.computedLength()||0)/t:0,a=document.createElement("div");if(a.classList.add("media-inspector-bar"),0===e.section()){a.createChild("div","media-inspector-marker-spacer");const t=a.createChild("div","media-inspector-marker media-inspector-marker-max-width");t.style.width=r+"px",d.Tooltip.Tooltip.install(t,e.mediaText()),s(t,e.maxWidthExpression(),!1,!1),s(t,e.maxWidthExpression(),!0,!0),a.createChild("div","media-inspector-marker-spacer")}if(1===e.section()){a.createChild("div","media-inspector-marker-spacer");const t=a.createChild("div","media-inspector-marker media-inspector-marker-min-max-width");t.style.width=.5*(r-n)+"px",d.Tooltip.Tooltip.install(t,e.mediaText()),s(t,e.maxWidthExpression(),!0,!1),s(t,e.minWidthExpression(),!1,!0),a.createChild("div","media-inspector-marker-spacer").style.flex="0 0 "+n+"px";const i=a.createChild("div","media-inspector-marker media-inspector-marker-min-max-width");i.style.width=.5*(r-n)+"px",d.Tooltip.Tooltip.install(i,e.mediaText()),s(i,e.minWidthExpression(),!0,!1),s(i,e.maxWidthExpression(),!1,!0),a.createChild("div","media-inspector-marker-spacer")}if(2===e.section()){const t=a.createChild("div","media-inspector-marker media-inspector-marker-min-width media-inspector-marker-min-width-left");d.Tooltip.Tooltip.install(t,e.mediaText()),s(t,e.minWidthExpression(),!1,!1),a.createChild("div","media-inspector-marker-spacer").style.flex="0 0 "+n+"px";const i=a.createChild("div","media-inspector-marker media-inspector-marker-min-width media-inspector-marker-min-width-right");d.Tooltip.Tooltip.install(i,e.mediaText()),s(i,e.minWidthExpression(),!0,!0)}function s(e,t,i,o){t&&(e.createChild("div","media-inspector-marker-label-container "+(i?"media-inspector-marker-label-container-left":"media-inspector-marker-label-container-right")).createChild("span","media-inspector-marker-label "+(o?"media-inspector-label-left":"media-inspector-label-right")).textContent=t.value()+t.unit())}return a}}class g{cssMedia;minWidthExpressionInternal;maxWidthExpressionInternal;activeInternal;sectionInternal;rawLocationInternal;constructor(e,t,i,o){this.cssMedia=e,this.minWidthExpressionInternal=t,this.maxWidthExpressionInternal=i,this.activeInternal=o,this.sectionInternal=i&&!t?0:t&&i?1:2}static createFromMediaQuery(e,t){let i=null,o=Number.MAX_VALUE,n=null,r=Number.MIN_VALUE;const a=t.expressions();if(!a)return null;for(let e=0;e<a.length;++e){const t=a[e],s=t.feature();if(-1===s.indexOf("width"))continue;const l=t.computedLength();s.startsWith("max-")&&l&&l<o?(i=t,o=l):s.startsWith("min-")&&l&&l>r&&(n=t,r=l)}return r>o||!i&&!n?null:new g(e,n,i,t.active())}equals(e){return 0===this.compareTo(e)}dimensionsEqual(e){const t=this.minWidthExpression(),i=e.minWidthExpression(),o=this.maxWidthExpression(),n=e.maxWidthExpression(),r=this.section()===e.section(),a=!t||t.computedLength()===i?.computedLength(),s=!o||o.computedLength()===n?.computedLength();return r&&a&&s}compareTo(e){if(this.section()!==e.section())return this.section()-e.section();if(this.dimensionsEqual(e)){const t=this.rawLocation(),i=e.rawLocation();return t||i?t&&!i?1:!t&&i?-1:this.active()!==e.active()?this.active()?-1:1:t&&i?a.StringUtilities.compare(t.url,i.url)||t.lineNumber-i.lineNumber||t.columnNumber-i.columnNumber:0:a.StringUtilities.compare(this.mediaText(),e.mediaText())}const t=this.maxWidthExpression(),i=e.maxWidthExpression(),o=t&&t.computedLength()||0,n=i&&i.computedLength()||0,r=this.minWidthExpression(),s=e.minWidthExpression(),l=r&&r.computedLength()||0,d=s&&s.computedLength()||0;return 0===this.section()?n-o:2===this.section()?l-d:l-d||n-o}section(){return this.sectionInternal}mediaText(){return this.cssMedia.text||""}rawLocation(){return this.rawLocationInternal||(this.rawLocationInternal=this.cssMedia.rawLocation()),this.rawLocationInternal}minWidthExpression(){return this.minWidthExpressionInternal}maxWidthExpression(){return this.maxWidthExpressionInternal}active(){return this.activeInternal}}})),t.register("2QMQ2",(function(t,i){e(t.exports,"default",(()=>o));var o={cssContent:"/*\n * Copyright 2015 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n/* Media query bars */\n\n.media-inspector-view {\n  height: 50px;\n  /* (min-width: 50px) */\n  --override-min-width-media-query-selector-background-color: rgb(255 204 128);\n  --override-min-width-media-query-selector-background-color-inactive: rgb(255 243 224);\n  --override-min-width-media-query-selector-marker-color: rgb(245 122 0);\n  /* (min-width: 50px) and (max-width: 50px) */\n  --override-min-and-max-width-media-query-selector-background-color: rgb(196 224 163);\n  --override-min-and-max-width-media-query-selector-background-color-inactive: rgb(234 246 235);\n  --override-min-and-max-width-media-query-selector-marker-color: rgb(104 159 56);\n  /* (max-width: 50px) */\n  --override-max-width-media-query-selector-background-color: rgb(144 202 249);\n  --override-max-width-media-query-selector-background-color-inactive: rgb(225 245 254);\n  --override-max-width-media-query-selector-marker-color: rgb(66 165 245);\n}\n\n.-theme-with-dark-background .media-inspector-view,\n:host-context(.-theme-with-dark-background) .media-inspector-view {\n  /* (min-width: 50px) */\n  --override-min-width-media-query-selector-background-color: rgb(127 76 0);\n  --override-min-width-media-query-selector-background-color-inactive: rgb(31 19 0);\n  --override-min-width-media-query-selector-marker-color: rgb(255 132 10);\n  /* (min-width: 50px) and (max-width: 50px) */\n  --override-min-and-max-width-media-query-selector-background-color: rgb(64 92 31);\n  --override-min-and-max-width-media-query-selector-background-color-inactive: rgb(9 21 10);\n  --override-min-and-max-width-media-query-selector-marker-color: rgb(144 199 96);\n  /* (max-width: 50px) */\n  --override-max-width-media-query-selector-background-color: rgb(6 64 111);\n  --override-max-width-media-query-selector-background-color-inactive: rgb(1 21 30);\n  --override-max-width-media-query-selector-marker-color: rgb(10 109 189);\n}\n\n.media-inspector-marker-container {\n  height: 14px;\n  margin: 2px 0;\n  position: relative;\n}\n\n.media-inspector-bar {\n  display: flex;\n  flex-direction: row;\n  align-items: stretch;\n  pointer-events: none;\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n}\n\n.media-inspector-marker {\n  flex: none;\n  pointer-events: auto;\n  margin: 1px 0;\n  white-space: nowrap;\n  z-index: auto;\n  position: relative;\n}\n\n.media-inspector-marker-spacer {\n  flex: auto;\n}\n\n.media-inspector-marker:hover {\n  margin: -1px 0;\n  opacity: 100%;\n}\n\n.media-inspector-marker-min-width {\n  flex: auto;\n  background-color: var(--override-min-width-media-query-selector-background-color);\n  border-right: 2px solid var(--override-min-width-media-query-selector-marker-color);\n  border-left: 2px solid var(--override-min-width-media-query-selector-marker-color);\n}\n\n.media-inspector-marker-min-width-right {\n  border-left: 2px solid var(--override-min-width-media-query-selector-marker-color);\n}\n\n.media-inspector-marker-min-width-left {\n  border-right: 2px solid var(--override-min-width-media-query-selector-marker-color);\n}\n\n.media-inspector-marker-min-max-width {\n  background-color: var(--override-min-and-max-width-media-query-selector-background-color);\n  border-left: 2px solid var(--override-min-and-max-width-media-query-selector-marker-color);\n  border-right: 2px solid var(--override-min-and-max-width-media-query-selector-marker-color);\n}\n\n.media-inspector-marker-min-max-width:hover {\n  z-index: 1;\n}\n\n.media-inspector-marker-max-width {\n  background-color: var(--override-max-width-media-query-selector-background-color);\n  border-right: 2px solid var(--override-max-width-media-query-selector-marker-color);\n  border-left: 2px solid var(--override-max-width-media-query-selector-marker-color);\n}\n\n/* Clear background colors when query is not active and not hovering */\n\n.media-inspector-marker-inactive .media-inspector-marker-min-width:not(:hover) {\n  background-color: var(--override-min-width-media-query-selector-background-color-inactive);\n}\n\n.media-inspector-marker-inactive .media-inspector-marker-min-max-width:not(:hover) {\n  background-color: var(--override-min-and-max-width-media-query-selector-background-color-inactive);\n}\n\n.media-inspector-marker-inactive .media-inspector-marker-max-width:not(:hover) {\n  background-color: var(--override-max-width-media-query-selector-background-color-inactive);\n}\n\n/* Media query labels */\n\n.media-inspector-marker-label-container {\n  position: absolute;\n  z-index: 1;\n}\n\n.media-inspector-marker:not(:hover) .media-inspector-marker-label-container {\n  display: none;\n}\n\n.media-inspector-marker-label-container-left {\n  left: -2px;\n}\n\n.media-inspector-marker-label-container-right {\n  right: -2px;\n}\n\n.media-inspector-marker-label {\n  color: var(--color-text-primary);\n  position: absolute;\n  top: 1px;\n  bottom: 0;\n  font-size: 12px;\n  pointer-events: none;\n}\n\n.media-inspector-label-right {\n  right: 4px;\n}\n\n.media-inspector-label-left {\n  left: 4px;\n}\n"}})),t.register("bEzcW",(function(t,i){e(t.exports,"default",(()=>o));var o={cssContent:"/*\n * Copyright 2015 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  overflow: hidden;\n  align-items: stretch;\n  flex: auto;\n  background-color: var(--color-background-elevation-0);\n}\n\n.device-mode-toolbar {\n  flex: none;\n  background-color: var(--color-background-elevation-0);\n  border-bottom: 1px solid var(--color-details-hairline);\n  display: flex;\n  flex-direction: row;\n  align-items: stretch;\n}\n\n.device-mode-toolbar .toolbar {\n  overflow: hidden;\n  flex: 0 100000 auto;\n  padding: 0 5px;\n}\n\n.device-mode-toolbar .toolbar.device-mode-toolbar-fixed-size {\n  flex: 0 1 auto;\n}\n\n.device-mode-toolbar-options.toolbar {\n  position: sticky;\n  right: 0;\n  flex: none;\n}\n\n.device-mode-toolbar-spacer {\n  flex: 1 1 0;\n  display: flex;\n  flex-direction: row;\n  overflow: hidden;\n}\n\n.device-mode-content-clip {\n  overflow: hidden;\n  flex: auto;\n}\n\n.device-mode-media-container {\n  flex: none;\n  overflow: hidden;\n  box-shadow: inset 0 -1px var(--color-details-hairline);\n}\n\n.device-mode-content-clip:not(.device-mode-outline-visible) .device-mode-media-container {\n  margin-bottom: 20px;\n}\n\n.device-mode-presets-container {\n  flex: 0 0 20px;\n  display: flex;\n}\n\n.device-mode-presets-container-inner {\n  flex: auto;\n  justify-content: center;\n  position: relative;\n  background-color: var(--color-background-elevation-2);\n  border: 2px solid var(--color-background-elevation-0);\n  border-bottom: 2px solid var(--color-background-elevation-0);\n}\n\n.device-mode-presets-container:hover {\n  transition: opacity 0.1s;\n  transition-delay: 50ms;\n  opacity: 100%;\n}\n\n.device-mode-preset-bar-outer {\n  pointer-events: none;\n  display: flex;\n  justify-content: center;\n}\n\n.device-mode-preset-bar {\n  border-left: 2px solid var(--color-background-elevation-0);\n  border-right: 2px solid var(--color-background-elevation-0);\n  pointer-events: auto;\n  text-align: center;\n  flex: none;\n  color: var(--color-text-primary);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  white-space: nowrap;\n  margin-bottom: 1px;\n}\n\n.device-mode-preset-bar:hover {\n  transition: background-color 0.1s;\n  transition-delay: 50ms;\n  background-color: var(--color-background-highlight);\n}\n\n.device-mode-preset-bar > span {\n  visibility: hidden;\n}\n\n.device-mode-preset-bar:hover > span {\n  transition: visibility 0.1s;\n  transition-delay: 50ms;\n  visibility: visible;\n}\n\n.device-mode-content-area {\n  flex: auto;\n  position: relative;\n  margin: 0;\n}\n\n.device-mode-screen-area {\n  position: absolute;\n  left: 0;\n  right: 0;\n  width: 0;\n  height: 0;\n  background-color: var(--color-background-inverted);\n}\n\n.device-mode-content-clip:not(.device-mode-outline-visible) .device-mode-screen-area {\n  --override-screen-area-box-shadow: hsl(240deg 3% 84%) 0 0 0 0.5px, hsl(0deg 0% 80% / 40%) 0 0 20px;\n\n  box-shadow: var(--override-screen-area-box-shadow);\n}\n\n.-theme-with-dark-background .device-mode-content-clip:not(.device-mode-outline-visible) .device-mode-screen-area,\n:host-context(.-theme-with-dark-background) .device-mode-content-clip:not(.device-mode-outline-visible) .device-mode-screen-area {\n  --override-screen-area-box-shadow: rgb(40 40 42) 0 0 0 0.5px, rgb(51 51 51 / 40%) 0 0 20px;\n}\n\n.device-mode-screen-image {\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n}\n\n.device-mode-resizer {\n  position: absolute;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n  transition: background-color 0.1s ease, opacity 0.1s ease;\n}\n\n.device-mode-resizer:hover {\n  background-color: var(--color-background-elevation-2);\n  opacity: 100%;\n}\n\n.device-mode-resizer > div {\n  pointer-events: none;\n}\n\n.device-mode-right-resizer {\n  top: 0;\n  bottom: -1px;\n  right: -20px;\n  width: 20px;\n}\n\n.device-mode-left-resizer {\n  top: 0;\n  bottom: -1px;\n  left: -20px;\n  width: 20px;\n  opacity: 0%;\n}\n\n.device-mode-bottom-resizer {\n  left: 0;\n  right: -1px;\n  bottom: -20px;\n  height: 20px;\n}\n\n.device-mode-bottom-right-resizer {\n  left: 0;\n  top: 0;\n  right: -20px;\n  bottom: -20px;\n  background-color: var(--color-background-elevation-1);\n}\n\n.device-mode-bottom-left-resizer {\n  left: -20px;\n  top: 0;\n  right: 0;\n  bottom: -20px;\n  opacity: 0%;\n}\n\n.device-mode-right-resizer > div {\n  content: var(--image-file-resizeHorizontal);\n  width: 6px;\n  height: 26px;\n}\n\n.device-mode-left-resizer > div {\n  content: var(--image-file-resizeHorizontal);\n  width: 6px;\n  height: 26px;\n}\n\n.device-mode-bottom-resizer > div {\n  content: var(--image-file-resizeVertical);\n  margin-bottom: -2px;\n  width: 26px;\n  height: 6px;\n}\n\n.device-mode-bottom-right-resizer > div {\n  position: absolute;\n  bottom: 3px;\n  right: 3px;\n  width: 13px;\n  height: 13px;\n  content: var(--image-file-resizeDiagonal);\n}\n\n.device-mode-bottom-left-resizer > div {\n  position: absolute;\n  bottom: 3px;\n  left: 3px;\n  width: 13px;\n  height: 13px;\n  content: var(--image-file-resizeDiagonal);\n  transform: rotate(90deg);\n}\n\n.device-mode-page-area {\n  position: absolute;\n  left: 0;\n  right: 0;\n  width: 0;\n  height: 0;\n  display: flex;\n  background-color: var(--color-background-elevation-0);\n}\n\n.device-mode-ruler {\n  position: absolute;\n  overflow: visible;\n}\n\n.device-mode-ruler-top {\n  height: 20px;\n  right: 0;\n}\n\n.device-mode-ruler-left {\n  width: 20px;\n  bottom: 0;\n}\n\n.device-mode-ruler-content {\n  pointer-events: none;\n  position: absolute;\n  left: -20px;\n  top: -20px;\n\n  --override-device-ruler-border-color: hsl(0deg 0% 50%);\n}\n\n.device-mode-ruler-top .device-mode-ruler-content {\n  border-top: 1px solid transparent;\n  right: 0;\n  bottom: 20px;\n  background-color: var(--color-background-opacity-80);\n}\n\n.device-mode-ruler-left .device-mode-ruler-content {\n  border-left: 1px solid transparent;\n  border-top: 1px solid transparent;\n  right: 20px;\n  bottom: 0;\n}\n\n.-theme-with-dark-background .device-mode-ruler-content,\n:host-context(.-theme-with-dark-background) .device-mode-ruler-content {\n  --override-device-ruler-border-color: rgb(127 127 127);\n}\n\n.device-mode-content-clip.device-mode-outline-visible .device-mode-ruler-top .device-mode-ruler-content {\n  border-top: 1px solid var(--override-device-ruler-border-color);\n}\n\n.device-mode-content-clip.device-mode-outline-visible .device-mode-ruler-left .device-mode-ruler-content {\n  border-left: 1px solid var(--override-device-ruler-border-color);\n  border-top: 1px solid var(--override-device-ruler-border-color);\n}\n\n.device-mode-ruler-inner {\n  position: absolute;\n}\n\n.device-mode-ruler-top .device-mode-ruler-inner {\n  top: 0;\n  bottom: 0;\n  left: 20px;\n  right: 0;\n  border-bottom: 1px solid var(--override-device-ruler-border-color);\n}\n\n.device-mode-ruler-left .device-mode-ruler-inner {\n  left: 0;\n  right: 0;\n  top: 19px;\n  bottom: 0;\n  border-right: 1px solid var(--override-device-ruler-border-color);\n  background-color: var(--color-background-opacity-80);\n}\n\n.device-mode-ruler-marker {\n  position: absolute;\n}\n\n.device-mode-ruler-top .device-mode-ruler-marker {\n  width: 0;\n  height: 5px;\n  bottom: 0;\n  border-right: 1px solid var(--override-device-ruler-border-color);\n  margin-right: -1px;\n}\n\n.device-mode-ruler-top .device-mode-ruler-marker.device-mode-ruler-marker-medium {\n  height: 10px;\n}\n\n.device-mode-ruler-top .device-mode-ruler-marker.device-mode-ruler-marker-large {\n  height: 15px;\n}\n\n.device-mode-ruler-left .device-mode-ruler-marker {\n  height: 0;\n  width: 5px;\n  right: 0;\n  border-bottom: 1px solid var(--override-device-ruler-border-color);\n  margin-bottom: -1px;\n}\n\n.device-mode-ruler-left .device-mode-ruler-marker.device-mode-ruler-marker-medium {\n  width: 10px;\n}\n\n.device-mode-ruler-left .device-mode-ruler-marker.device-mode-ruler-marker-large {\n  width: 15px;\n}\n\n.device-mode-ruler-text {\n  color: var(--color-text-secondary);\n  position: relative;\n  pointer-events: auto;\n}\n\n.device-mode-ruler-text:hover {\n  color: var(--color-text-primary);\n}\n\n.device-mode-ruler-top .device-mode-ruler-text {\n  left: 2px;\n  top: -2px;\n}\n\n.device-mode-ruler-left .device-mode-ruler-text {\n  left: -4px;\n  top: -15px;\n  transform: rotate(270deg);\n}\n"}})),t.register("kZOF3",(function(i,o){e(i.exports,"InspectedPagePlaceholder",(()=>l));var n=t("koSS8"),r=t("9z2ZV"),a=t("7DhT6");let s;class l extends(n.ObjectWrapper.eventMixin(r.Widget.Widget)){updateId;constructor(){super(!0),this.registerRequiredCSS(a.default),r.ZoomManager.ZoomManager.instance().addEventListener("ZoomChanged",this.onResize,this),this.restoreMinimumSize()}static instance(e={forceNew:null}){const{forceNew:t}=e;return s&&!t||(s=new l),s}onResize(){this.updateId&&this.element.window().cancelAnimationFrame(this.updateId),this.updateId=this.element.window().requestAnimationFrame(this.update.bind(this,!1))}restoreMinimumSize(){this.setMinimumSize(150,150)}clearMinimumSize(){this.setMinimumSize(1,1)}dipPageRect(){const e=r.ZoomManager.ZoomManager.instance().zoomFactor(),t=this.element.getBoundingClientRect(),i=this.element.ownerDocument.body.getBoundingClientRect(),o=Math.max(t.left*e,i.left*e),n=Math.max(t.top*e,i.top*e),a=Math.min(t.bottom*e,i.bottom*e);return{x:o,y:n,width:Math.min(t.right*e,i.right*e)-o,height:a-n}}update(e){delete this.updateId;const t=this.dipPageRect(),i={x:Math.round(t.x),y:Math.round(t.y),height:Math.max(1,Math.round(t.height)),width:Math.max(1,Math.round(t.width))};e&&(--i.height,this.dispatchEventToListeners("Update",i),++i.height),this.dispatchEventToListeners("Update",i)}}})),t.register("7DhT6",(function(t,i){e(t.exports,"default",(()=>o));var o={cssContent:"/*\n * Copyright 2016 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  background-color: var(--color-background);\n}\n"}}));
//# sourceMappingURL=emulation.444bba29.js.map
