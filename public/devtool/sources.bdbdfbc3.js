function e(e,t,s,n){Object.defineProperty(e,t,{get:s,set:n,enumerable:!0,configurable:!0})}var t=("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}).parcelRequire20b5;t.register("ehgAU",(function(s,n){e(s.exports,"SearchView",(()=>t("agnCq")));t("Ohm4n"),t("NkXbv"),t("agnCq")})),t.register("Ohm4n",(function(s,n){e(s.exports,"SearchConfig",(()=>r));var i=t("lz7WY");class r{queryInternal;ignoreCaseInternal;isRegexInternal;fileQueries;queriesInternal;fileRegexQueries;constructor(e,t,s){this.queryInternal=e,this.ignoreCaseInternal=t,this.isRegexInternal=s,this.parse()}static fromPlainObject(e){return new r(e.query,e.ignoreCase,e.isRegex)}query(){return this.queryInternal}ignoreCase(){return this.ignoreCaseInternal}isRegex(){return this.isRegexInternal}toPlainObject(){return{query:this.query(),ignoreCase:this.ignoreCase(),isRegex:this.isRegex()}}parse(){const e=/(\s*(?!-?f(ile)?:)[^\\ ]|\\.)+/,t=e.source+"(\\s+"+e.source+")*",s=["(\\s*"+a.source+"\\s*)","("+/"([^\\"]|\\.)+"/.source+")","("+t+")"].join("|"),n=new RegExp(s,"g"),i=this.queryInternal.match(n)||[];this.fileQueries=[],this.queriesInternal=[];for(let e=0;e<i.length;++e){const t=i[e];if(!t)continue;const s=this.parseFileQuery(t);if(s)this.fileQueries.push(s),this.fileRegexQueries=this.fileRegexQueries||[],this.fileRegexQueries.push({regex:new RegExp(s.text,this.ignoreCase()?"i":""),isNegative:s.isNegative});else if(this.isRegexInternal)this.queriesInternal.push(t);else if(t.startsWith('"')){if(!t.endsWith('"'))continue;this.queriesInternal.push(this.parseQuotedQuery(t))}else this.queriesInternal.push(this.parseUnquotedQuery(t))}}filePathMatchesFileQuery(e){if(!this.fileRegexQueries)return!0;for(let t=0;t<this.fileRegexQueries.length;++t)if(Boolean(e.match(this.fileRegexQueries[t].regex))===this.fileRegexQueries[t].isNegative)return!1;return!0}queries(){return this.queriesInternal||[]}parseUnquotedQuery(e){return e.replace(/\\(.)/g,"$1")}parseQuotedQuery(e){return e.substring(1,e.length-1).replace(/\\(.)/g,"$1")}parseFileQuery(e){const t=e.match(a);if(!t)return null;const s=Boolean(t[1]);e=t[3];let n="";for(let t=0;t<e.length;++t){const s=e[t];if("*"===s)n+=".*";else if("\\"===s){++t;" "===e[t]&&(n+=" ")}else-1!==i.StringUtilities.regexSpecialCharacters().indexOf(e.charAt(t))&&(n+="\\"),n+=e.charAt(t)}return new h(n,s)}}const a=/(-)?f(ile)?:((?:[^\\ ]|\\.)+)/;class h{text;isNegative;constructor(e,t){this.text=e,this.isNegative=t}}})),t.register("NkXbv",(function(s,n){e(s.exports,"SearchResultsPane",(()=>p));var i=t("koSS8"),r=t("ixFnt"),a=t("lz7WY"),h=t("7f6zc"),o=t("a3yig"),c=t("9z2ZV"),l=t("eZp1F");const u={matchesCountS:"Matches Count {PH1}",lineS:"Line {PH1}",showDMore:"Show {PH1} more"},d=r.i18n.registerUIStrings("panels/search/SearchResultsPane.ts",u),g=r.i18n.getLocalizedString.bind(void 0,d);class p extends c.Widget.VBox{searchConfig;searchResults;treeElements;treeOutline;matchesExpandedCount;constructor(e){super(!0),this.searchConfig=e,this.searchResults=[],this.treeElements=[],this.treeOutline=new c.TreeOutline.TreeOutlineInShadow,this.treeOutline.hideOverflow(),this.contentElement.appendChild(this.treeOutline.element),this.matchesExpandedCount=0}addSearchResult(e){this.searchResults.push(e),this.addTreeElement(e)}showAllMatches(){this.treeElements.forEach((e=>{e.expand(),e.showAllMatches()}))}collapseAllResults(){this.treeElements.forEach((e=>{e.collapse()}))}addTreeElement(e){const t=new f(this.searchConfig,e);this.treeOutline.appendChild(t),this.treeOutline.selectedTreeElement||t.select(!0,!0),this.matchesExpandedCount<m&&t.expand(),this.matchesExpandedCount+=e.matchesCount(),this.treeElements.push(t)}wasShown(){super.wasShown(),this.treeOutline.registerCSSFiles([l.default])}}const m=200;class f extends c.TreeOutline.TreeElement{searchConfig;searchResult;initialized;toggleOnClick;constructor(e,t){super("",!0),this.searchConfig=e,this.searchResult=t,this.initialized=!1,this.toggleOnClick=!0}onexpand(){this.initialized||(this.updateMatchesUI(),this.initialized=!0)}showAllMatches(){this.removeChildren(),this.appendSearchMatches(0,this.searchResult.matchesCount())}updateMatchesUI(){this.removeChildren();const e=Math.min(this.searchResult.matchesCount(),20);e<this.searchResult.matchesCount()?(this.appendSearchMatches(0,e-1),this.appendShowMoreMatchesElement(e-1)):this.appendSearchMatches(0,e)}onattach(){this.updateSearchMatches()}updateSearchMatches(){this.listItemElement.classList.add("search-result");const e=s(this.searchResult.label(),"search-result-file-name");e.appendChild(s("—","search-result-dash")),e.appendChild(s(this.searchResult.description(),"search-result-qualifier")),this.tooltip=this.searchResult.description(),this.listItemElement.appendChild(e);const t=document.createElement("span");function s(e,t){const s=document.createElement("span");return s.className=t,s.textContent=e,s}t.className="search-result-matches-count",t.textContent=`${this.searchResult.matchesCount()}`,c.ARIAUtils.setAccessibleName(t,g(u.matchesCountS,{PH1:this.searchResult.matchesCount()})),this.listItemElement.appendChild(t),this.expanded&&this.updateMatchesUI()}appendSearchMatches(e,t){const s=this.searchResult,n=this.searchConfig.queries(),r=[];for(let e=0;e<n.length;++e)r.push(a.StringUtilities.createSearchRegex(n[e],!this.searchConfig.ignoreCase(),this.searchConfig.isRegex()));for(let n=e;n<t;++n){const e=s.matchLineContent(n).trim();let t=[];for(let s=0;s<r.length;++s)t=t.concat(this.regexMatchRanges(e,r[s]));const a=o.Linkifier.Linkifier.linkifyRevealable(s.matchRevealable(n),"");a.classList.add("search-match-link");const h=document.createElement("span");h.classList.add("search-match-line-number");const l=s.matchLabel(n);h.textContent=l,"number"!=typeof l||isNaN(l)?c.ARIAUtils.setAccessibleName(h,l):c.ARIAUtils.setAccessibleName(h,g(u.lineS,{PH1:l})),a.appendChild(h);const d=this.createContentSpan(e,t);a.appendChild(d);const p=new c.TreeOutline.TreeElement;this.appendChild(p),p.listItemElement.className="search-match",p.listItemElement.appendChild(a),p.listItemElement.addEventListener("keydown",(e=>{"Enter"===e.key&&(e.consume(!0),i.Revealer.reveal(s.matchRevealable(n)))})),p.tooltip=e}}appendShowMoreMatchesElement(e){const t=this.searchResult.matchesCount()-e,s=g(u.showDMore,{PH1:t}),n=new c.TreeOutline.TreeElement(s);this.appendChild(n),n.listItemElement.classList.add("show-more-matches"),n.onselect=this.showMoreMatchesElementSelected.bind(this,n,e)}createContentSpan(e,t){let s=0;t.length>0&&t[0].offset>20&&(s=15),e=e.substring(s,1e3+s),s&&(t=t.map((e=>new h.TextRange.SourceRange(e.offset-s+1,e.length))),e="…"+e);const n=document.createElement("span");return n.className="search-match-content",n.textContent=e,c.ARIAUtils.setAccessibleName(n,`${e} line`),c.UIUtils.highlightRangesWithStyleClass(n,t,"highlighted-search-result"),n}regexMatchRanges(e,t){let s;t.lastIndex=0;const n=[];for(;t.lastIndex<e.length&&(s=t.exec(e));)n.push(new h.TextRange.SourceRange(s.index,s[0].length));return n}showMoreMatchesElementSelected(e,t){return this.removeChild(e),this.appendSearchMatches(t,this.searchResult.matchesCount()),!1}}})),t.register("eZp1F",(function(t,s){e(t.exports,"default",(()=>i));const n=new CSSStyleSheet;n.replaceSync("/*\n * Copyright 2016 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  padding: 0;\n  margin: 0;\n  overflow-y: auto;\n}\n\n.tree-outline {\n  padding: 0;\n}\n\n.tree-outline ol {\n  padding: 0;\n}\n\n.tree-outline li {\n  height: 16px;\n}\n\nli.search-result {\n  cursor: pointer;\n  font-size: 12px;\n  margin-top: 8px;\n  padding: 2px 0 2px 4px;\n  word-wrap: normal;\n  white-space: pre;\n}\n\nli.search-result:hover {\n  background-color: var(--item-hover-color);\n}\n\nli.search-result .search-result-file-name {\n  color: var(--color-text-primary);\n  flex: 1 1;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\nli.search-result .search-result-matches-count {\n  color: var(--color-text-secondary);\n  margin: 0 8px;\n}\n\nli.search-result.expanded .search-result-matches-count {\n  display: none;\n}\n\nli.show-more-matches {\n  color: var(--color-text-primary);\n  cursor: pointer;\n  margin: 8px 0 0 -4px;\n}\n\nli.show-more-matches:hover {\n  text-decoration: underline;\n}\n\nli.search-match {\n  margin: 2px 0;\n  word-wrap: normal;\n  white-space: pre;\n}\n\nli.search-match::before {\n  display: none;\n}\n\nli.search-match .search-match-line-number {\n  color: var(--color-text-secondary);\n  text-align: right;\n  vertical-align: top;\n  word-break: normal;\n  padding: 2px 4px 2px 6px;\n  margin-right: 5px;\n}\n\nli.search-match:hover {\n  background-color: var(--item-hover-color);\n}\n\n.tree-outline .devtools-link {\n  text-decoration: none;\n  display: block;\n  flex: auto;\n}\n\nli.search-match .search-match-content {\n  color: var(--color-text-primary);\n}\n\nol.children.expanded {\n  padding-bottom: 4px;\n}\n\n.search-match-link {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  margin-left: 9px;\n}\n\n.search-result-qualifier {\n  color: var(--color-text-secondary);\n}\n\n.search-result-dash {\n  color: var(--color-background-elevation-2);\n  margin: 0 4px;\n}\n\n/*# sourceURL=searchResultsPane.css */\n");var i=n})),t.register("agnCq",(function(s,n){e(s.exports,"SearchView",(()=>p));var i=t("koSS8"),r=t("e7bLS"),a=t("ixFnt"),h=t("9z2ZV"),o=t("NrrxA"),c=t("Ohm4n"),l=t("NkXbv");const u={search:"Search",searchQuery:"Search Query",matchCase:"Match Case",useRegularExpression:"Use Regular Expression",refresh:"Refresh",clear:"Clear",indexing:"Indexing…",searching:"Searching…",indexingInterrupted:"Indexing interrupted.",foundMatchingLineInFile:"Found 1 matching line in 1 file.",foundDMatchingLinesInFile:"Found {PH1} matching lines in 1 file.",foundDMatchingLinesInDFiles:"Found {PH1} matching lines in {PH2} files.",noMatchesFound:"No matches found.",searchFinished:"Search finished.",searchInterrupted:"Search interrupted."},d=a.i18n.registerUIStrings("panels/search/SearchView.ts",u),g=a.i18n.getLocalizedString.bind(void 0,d);class p extends h.Widget.VBox{focusOnShow;isIndexing;searchId;searchMatchesCount;searchResultsCount;nonEmptySearchResultsCount;searchingView;notFoundView;searchConfig;pendingSearchConfig;searchResultsPane;progressIndicator;visiblePane;searchPanelElement;searchResultsElement;search;matchCaseButton;regexButton;searchMessageElement;searchProgressPlaceholderElement;searchResultsMessageElement;advancedSearchConfig;searchScope;constructor(e){super(!0),this.setMinimumSize(0,40),this.focusOnShow=!1,this.isIndexing=!1,this.searchId=1,this.searchMatchesCount=0,this.searchResultsCount=0,this.nonEmptySearchResultsCount=0,this.searchingView=null,this.notFoundView=null,this.searchConfig=null,this.pendingSearchConfig=null,this.searchResultsPane=null,this.progressIndicator=null,this.visiblePane=null,this.contentElement.classList.add("search-view"),this.contentElement.addEventListener("keydown",(e=>{this.onKeyDownOnPanel(e)})),this.searchPanelElement=this.contentElement.createChild("div","search-drawer-header"),this.searchResultsElement=this.contentElement.createChild("div"),this.searchResultsElement.className="search-results";const t=document.createElement("div");t.style.flex="auto",t.style.justifyContent="start",t.style.maxWidth="300px",this.search=h.HistoryInput.HistoryInput.create(),this.search.addEventListener("keydown",(e=>{this.onKeyDown(e)})),t.appendChild(this.search),this.search.placeholder=g(u.search),this.search.setAttribute("type","text"),this.search.setAttribute("results","0"),this.search.setAttribute("size","42"),h.ARIAUtils.setAccessibleName(this.search,g(u.searchQuery));const s=new h.Toolbar.ToolbarItem(t),n=new h.Toolbar.Toolbar("search-toolbar",this.searchPanelElement);this.matchCaseButton=p.appendToolbarToggle(n,"Aa",g(u.matchCase)),this.regexButton=p.appendToolbarToggle(n,".*",g(u.useRegularExpression)),n.appendToolbarItem(s);const r=new h.Toolbar.ToolbarButton(g(u.refresh),"largeicon-refresh"),a=new h.Toolbar.ToolbarButton(g(u.clear),"largeicon-clear");n.appendToolbarItem(r),n.appendToolbarItem(a),r.addEventListener(h.Toolbar.ToolbarButton.Events.Click,(()=>this.onAction())),a.addEventListener(h.Toolbar.ToolbarButton.Events.Click,(()=>{this.resetSearch(),this.onSearchInputClear()}));const o=this.contentElement.createChild("div","search-toolbar-summary");this.searchMessageElement=o.createChild("div","search-message"),this.searchProgressPlaceholderElement=o.createChild("div","flex-centered"),this.searchResultsMessageElement=o.createChild("div","search-message"),this.advancedSearchConfig=i.Settings.Settings.instance().createLocalSetting(e+"SearchConfig",new(0,c.SearchConfig)("",!0,!1).toPlainObject()),this.load(),this.searchScope=null}static appendToolbarToggle(e,t,s){const n=new h.Toolbar.ToolbarToggle(s);return n.setText(t),n.addEventListener(h.Toolbar.ToolbarButton.Events.Click,(()=>n.setToggled(!n.toggled()))),e.appendToolbarItem(n),n}buildSearchConfig(){return new(0,c.SearchConfig)(this.search.value,!this.matchCaseButton.toggled(),this.regexButton.toggled())}async toggle(e,t){e&&(this.search.value=e),this.isShowing()?this.focus():this.focusOnShow=!0,this.initScope(),t?this.onAction():this.startIndexing()}createScope(){throw new Error("Not implemented")}initScope(){this.searchScope=this.createScope()}wasShown(){this.focusOnShow&&(this.focus(),this.focusOnShow=!1),this.registerCSSFiles([o.default])}onIndexingFinished(){if(!this.progressIndicator)return;const e=!this.progressIndicator.isCanceled();if(this.progressIndicator.done(),this.progressIndicator=null,this.isIndexing=!1,this.indexingFinished(e),e||(this.pendingSearchConfig=null),!this.pendingSearchConfig)return;const t=this.pendingSearchConfig;this.pendingSearchConfig=null,this.innerStartSearch(t)}startIndexing(){this.isIndexing=!0,this.progressIndicator&&this.progressIndicator.done(),this.progressIndicator=new h.ProgressIndicator.ProgressIndicator,this.searchMessageElement.textContent=g(u.indexing),this.progressIndicator.show(this.searchProgressPlaceholderElement),this.searchScope&&this.searchScope.performIndexing(new i.Progress.ProgressProxy(this.progressIndicator,this.onIndexingFinished.bind(this)))}onSearchInputClear(){this.search.value="",this.save(),this.focus()}onSearchResult(e,t){e===this.searchId&&this.progressIndicator&&(this.progressIndicator&&this.progressIndicator.isCanceled()?this.onIndexingFinished():(this.addSearchResult(t),t.matchesCount()&&(this.searchResultsPane||(this.searchResultsPane=new(0,l.SearchResultsPane)(this.searchConfig),this.showPane(this.searchResultsPane)),this.searchResultsPane.addSearchResult(t))))}onSearchFinished(e,t){e===this.searchId&&this.progressIndicator&&(this.searchResultsPane||this.nothingFound(),this.searchFinished(t),this.searchConfig=null,h.ARIAUtils.alert(this.searchMessageElement.textContent+" "+this.searchResultsMessageElement.textContent))}async startSearch(e){this.resetSearch(),++this.searchId,this.initScope(),this.isIndexing||this.startIndexing(),this.pendingSearchConfig=e}innerStartSearch(e){this.searchConfig=e,this.progressIndicator&&this.progressIndicator.done(),this.progressIndicator=new h.ProgressIndicator.ProgressIndicator,this.searchStarted(this.progressIndicator),this.searchScope&&this.searchScope.performSearch(e,this.progressIndicator,this.onSearchResult.bind(this,this.searchId),this.onSearchFinished.bind(this,this.searchId))}resetSearch(){this.stopSearch(),this.showPane(null),this.searchResultsPane=null,this.clearSearchMessage()}clearSearchMessage(){this.searchMessageElement.textContent="",this.searchResultsMessageElement.textContent=""}stopSearch(){this.progressIndicator&&!this.isIndexing&&this.progressIndicator.cancel(),this.searchScope&&this.searchScope.stopSearch(),this.searchConfig=null}searchStarted(e){this.resetCounters(),this.searchingView||(this.searchingView=new h.EmptyWidget.EmptyWidget(g(u.searching))),this.showPane(this.searchingView),this.searchMessageElement.textContent=g(u.searching),e.show(this.searchProgressPlaceholderElement),this.updateSearchResultsMessage()}indexingFinished(e){this.searchMessageElement.textContent=e?"":g(u.indexingInterrupted)}updateSearchResultsMessage(){this.searchMatchesCount&&this.searchResultsCount?1===this.searchMatchesCount&&1===this.nonEmptySearchResultsCount?this.searchResultsMessageElement.textContent=g(u.foundMatchingLineInFile):this.searchMatchesCount>1&&1===this.nonEmptySearchResultsCount?this.searchResultsMessageElement.textContent=g(u.foundDMatchingLinesInFile,{PH1:this.searchMatchesCount}):this.searchResultsMessageElement.textContent=g(u.foundDMatchingLinesInDFiles,{PH1:this.searchMatchesCount,PH2:this.nonEmptySearchResultsCount}):this.searchResultsMessageElement.textContent=""}showPane(e){this.visiblePane&&this.visiblePane.detach(),e&&e.show(this.searchResultsElement),this.visiblePane=e}resetCounters(){this.searchMatchesCount=0,this.searchResultsCount=0,this.nonEmptySearchResultsCount=0}nothingFound(){this.notFoundView||(this.notFoundView=new h.EmptyWidget.EmptyWidget(g(u.noMatchesFound))),this.showPane(this.notFoundView),this.searchResultsMessageElement.textContent=g(u.noMatchesFound)}addSearchResult(e){const t=e.matchesCount();this.searchMatchesCount+=t,this.searchResultsCount++,t&&this.nonEmptySearchResultsCount++,this.updateSearchResultsMessage()}searchFinished(e){this.searchMessageElement.textContent=g(e?u.searchFinished:u.searchInterrupted)}focus(){this.search.focus(),this.search.select()}willHide(){this.stopSearch()}onKeyDown(e){if(this.save(),e.keyCode===h.KeyboardShortcut.Keys.Enter.code)this.onAction()}onKeyDownOnPanel(e){const t=r.Platform.isMac(),s=t&&e.metaKey&&!e.ctrlKey&&e.altKey&&"BracketRight"===e.code,n=!t&&e.ctrlKey&&!e.metaKey&&e.shiftKey&&"BracketRight"===e.code,i=t&&e.metaKey&&!e.ctrlKey&&e.altKey&&"BracketLeft"===e.code,a=!t&&e.ctrlKey&&!e.metaKey&&e.shiftKey&&"BracketLeft"===e.code;s||n?this.searchResultsPane?.showAllMatches():(i||a)&&this.searchResultsPane?.collapseAllResults()}save(){this.advancedSearchConfig.set(this.buildSearchConfig().toPlainObject())}load(){const e=c.SearchConfig.fromPlainObject(this.advancedSearchConfig.get());this.search.value=e.query(),this.matchCaseButton.setToggled(!e.ignoreCase()),this.regexButton.setToggled(e.isRegex())}onAction(){const e=this.buildSearchConfig();e.query()&&e.query().length&&this.startSearch(e)}}})),t.register("NrrxA",(function(t,s){e(t.exports,"default",(()=>i));const n=new CSSStyleSheet;n.replaceSync("/*\n * Copyright 2014 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.search-drawer-header {\n  align-items: center;\n  flex-shrink: 0;\n  overflow: hidden;\n}\n\n.search-toolbar {\n  background-color: var(--color-background-elevation-1);\n  border-bottom: 1px solid var(--color-details-hairline);\n}\n\n.search-toolbar-summary {\n  background-color: var(--color-background-elevation-1);\n  border-top: 1px solid var(--color-details-hairline);\n  padding-left: 5px;\n  flex: 0 0 19px;\n  display: flex;\n  padding-right: 5px;\n}\n\n.search-toolbar-summary .search-message {\n  padding-top: 2px;\n  padding-left: 1ex;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  overflow: hidden;\n}\n\n.search-view .search-results {\n  overflow-y: auto;\n  display: flex;\n  flex: auto;\n}\n\n.search-view .search-results > div {\n  flex: auto;\n}\n\n/*# sourceURL=searchView.css */\n");var i=n})),t.register("fxJGO",(function(t,s){function n(e){return e.isDocument()?"ic_file_document":e.isImage()?"ic_file_image":e.isFont()?"ic_file_font":e.isScript()?"ic_file_script":e.isStyleSheet()?"ic_file_stylesheet":e.isWebbundle()?"ic_file_webbundle":"ic_file_default"}e(t.exports,"imageNameForResourceType",(()=>n))}));
//# sourceMappingURL=sources.bdbdfbc3.js.map
