function e(e,t,n,s){Object.defineProperty(e,t,{get:n,set:s,enumerable:!0,configurable:!0})}var t=("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}).parcelRequire20b5;t.register("1uT81",(function(n,s){e(n.exports,"DataGrid",(()=>t("hIlx9"))),e(n.exports,"DataGridControllerIntegrator",(()=>t("k01RS"))),e(n.exports,"DataGridRenderers",(()=>t("bOI8u"))),e(n.exports,"DataGridUtils",(()=>t("2GxU3")));t("hIlx9"),t("adQOh"),t("k01RS"),t("lE1VZ"),t("bOI8u"),t("2GxU3")})),t.register("hIlx9",(function(n,s){e(n.exports,"DataGrid",(()=>x));var i=t("e7bLS"),o=t("lz7WY"),r=t("9z2ZV"),l=t("dS5IF"),a=t("kpUjp"),d=t("hveEP"),c=t("4qIrv"),h=t("lE1VZ"),u=t("anPKn"),m=t("2GxU3"),f=t("ixFnt");const p=d.RenderCoordinator.RenderCoordinator.instance(),g={sortBy:"Sort By",resetColumns:"Reset Columns",headerOptions:"Header Options"},w=f.i18n.registerUIStrings("ui/components/data_grid/DataGrid.ts",g),b=f.i18n.getLocalizedString.bind(void 0,w),C=new Set([" ","Enter"]),v=18;class x extends HTMLElement{static litTagName=l.literal`devtools-data-grid`;#e=this.attachShadow({mode:"open"});#t=[];#n=[];#s=null;#i=!1;#o="NOT_SCROLLED";#r=void 0;#l=null;#a=new WeakMap;#d=new ResizeObserver((()=>{this.#c()}));#h=this.#u.bind(this);#m=this.#f.bind(this);#p=this.#g.bind(this);#w=[0,1];#b=null;#C=!1;#v=!1;#x=!1;connectedCallback(){this.#e.adoptedStyleSheets=[c.default],a.SetCSSProperty.set(this,"--table-row-height","18px")}get data(){return{columns:this.#t,rows:this.#n,activeSort:this.#s,contextMenus:this.#r}}set data(e){if(this.#t=e.columns,this.#n=e.rows,this.#n.forEach(((e,t)=>{this.#a.set(e,t)})),this.#s=e.activeSort,this.#r=e.contextMenus,this.#C||(this.#w=(0,m.calculateFirstFocusableCell)({columns:this.#t,rows:this.#n})),this.#C&&this.#y()){const[e,t]=this.#S(),n=e>this.#t.length,s=t>this.#n.length;(n||s)&&(this.#b=[n?this.#t.length:e,s?this.#n.length:t])}this.#R()}#I(){return"SCROLLED_TO_BOTTOM"===this.#o||!this.#v&&"MANUAL_SCROLL_NOT_BOTTOM"!==this.#o}#M(){!1!==this.#C&&this.#I()&&p.read((()=>{const e=this.#e.querySelector(".wrapping-container");if(!e)return;const t=e.scrollHeight;p.scroll((()=>{e.scrollTo(0,t)}))}))}#F(){this.#C||this.#d.observe(this.#e.host)}#y(){return null!==this.#b}#$(){if(!this.#b)return null;const[e,t]=this.#b;return this.#e.querySelector(`[data-row-index="${t}"][data-col-index="${e}"]`)}async#k(e){await p.write((()=>{e.focus()}))}#E([e,t]){if(this.#v=!0,this.#b&&this.#b[0]===e&&this.#b[1]===t)return;this.#b=[e,t],this.#R();const n=this.#$();n&&this.#k(n)}#O(e){const t=e.key;if(!this.#b)return;if(C.has(t)){const[e,t]=this.#b,n=this.#t[e];0===t&&n&&n.sortable&&this.#D(n,e)}if(!o.KeyboardUtilities.keyIsArrowKey(t))return;const n=(0,m.handleArrowKeyNavigation)({key:t,currentFocusedCell:this.#b,columns:this.#t,rows:this.#n});e.preventDefault(),this.#E(n)}#D(e,t){this.dispatchEvent(new(0,h.ColumnHeaderClickEvent)(e,t))}#T(e){return!e.sortable||this.#s&&this.#s.columnId===e.id?this.#s&&this.#s.columnId===e.id?"ASC"===this.#s.direction?"ascending":"descending":void 0:"none"}#z(e){const t=this.#t.map(((e,t)=>{if(!e.visible)return l.nothing;const n=l.Directives.classMap({firstVisibleColumn:0===t});return l.html`<td tabindex="-1" class=${n} data-filler-row-column-index=${t}></td>`})),n=l.Directives.classMap({"filler-row":!0,"padding-row":!0,"empty-table":0===e});return l.html`<tr tabindex="-1" class=${n}>${t}</tr>`}#H(){this.#l&&(this.#l.documentForCursorChange.body.style.cursor=this.#l.cursorToRestore,this.#l=null,this.#c())}#g(e){if(1!==e.buttons||i.Platform.isMac()&&e.ctrlKey)return;e.preventDefault();const t=e.target;if(!t)return;const n=t.dataset.columnIndex;if(!n)return;const s=globalThis.parseInt(n,10),o=this.#t.findIndex(((e,t)=>t>s&&!0===e.visible)),r=this.#e.querySelector(`td[data-filler-row-column-index="${s}"]`),l=this.#e.querySelector(`td[data-filler-row-column-index="${o}"]`);if(!r||!l)return;const a=this.#e.querySelector(`col[data-col-column-index="${s}"]`),d=this.#e.querySelector(`col[data-col-column-index="${o}"]`);if(!a||!d)return;const c=e.target.ownerDocument;c&&(this.#l={leftCellCol:a,rightCellCol:d,leftCellColInitialPercentageWidth:globalThis.parseInt(a.style.width,10),rightCellColInitialPercentageWidth:globalThis.parseInt(d.style.width,10),initialLeftCellWidth:r.clientWidth,initialRightCellWidth:l.clientWidth,initialMouseX:e.x,documentForCursorChange:c,cursorToRestore:t.style.cursor},c.body.style.cursor="col-resize",t.setPointerCapture(e.pointerId),t.addEventListener("pointermove",this.#m))}#f(e){if(e.preventDefault(),!this.#l)return;const t=this.#l.leftCellColInitialPercentageWidth+this.#l.rightCellColInitialPercentageWidth-10,n=e.x-this.#l.initialMouseX,s=Math.abs(n)/(this.#l.initialLeftCellWidth+this.#l.initialRightCellWidth)*100;let i,r;n>0?(i=o.NumberUtilities.clamp(this.#l.leftCellColInitialPercentageWidth+s,10,t),r=o.NumberUtilities.clamp(this.#l.rightCellColInitialPercentageWidth-s,10,t)):n<0&&(i=o.NumberUtilities.clamp(this.#l.leftCellColInitialPercentageWidth-s,10,t),r=o.NumberUtilities.clamp(this.#l.rightCellColInitialPercentageWidth+s,10,t)),i&&r&&(this.#l.leftCellCol.style.width=i.toFixed(2)+"%",this.#l.rightCellCol.style.width=r.toFixed(2)+"%")}#u(e){e.preventDefault();const t=e.target;t&&(t.releasePointerCapture(e.pointerId),t.removeEventListener("pointermove",this.#m),this.#H())}#U(e,t){const[n]=t;return n!==this.#L()&&e.visible?l.html`<span class="cell-resize-handle"
     @pointerdown=${this.#p}
     @pointerup=${this.#h}
     data-column-index=${n}
    ></span>`:l.nothing}#L(){let e=this.#t.length-1;for(;e>-1;e--){if(this.#t[e].visible)break}return e}#A(e){if(2!==e.button)return;const t=new r.ContextMenu.ContextMenu(e);(0,u.addColumnVisibilityCheckboxes)(this,t);const n=t.defaultSection().appendSubMenuItem(b(g.sortBy));(0,u.addSortableColumnItems)(this,n),t.defaultSection().appendItem(b(g.resetColumns),(()=>{this.dispatchEvent(new(0,h.ContextMenuHeaderResetClickEvent))})),this.#r&&this.#r.headerRow&&this.#r.headerRow(t,this.#t),t.show()}#P(e){if(2!==e.button)return;if(!(e.target&&e.target instanceof HTMLElement))return;const t=e.target.dataset.rowIndex;if(!t)return;const n=parseInt(t,10),s=this.#n[n-1],i=new r.ContextMenu.ContextMenu(e),o=i.defaultSection().appendSubMenuItem(b(g.sortBy));(0,u.addSortableColumnItems)(this,o);const l=i.defaultSection().appendSubMenuItem(b(g.headerOptions));(0,u.addColumnVisibilityCheckboxes)(this,l),l.defaultSection().appendItem(b(g.resetColumns),(()=>{this.dispatchEvent(new(0,h.ContextMenuHeaderResetClickEvent))})),this.#r&&this.#r.bodyRow&&this.#r.bodyRow(i,this.#t,s),i.show()}#W(e){const t=e.target;if(!t)return;const n=Math.round(t.scrollTop+t.clientHeight)===Math.round(t.scrollHeight);this.#o=n?"SCROLLED_TO_BOTTOM":"MANUAL_SCROLL_NOT_BOTTOM",this.#R()}#c(){return p.read((()=>{const e=this.#e.querySelectorAll("th:not(.hidden)"),t=this.#e.querySelectorAll(".cell-resize-handle");this.#e.querySelector("table")&&e.forEach((async(e,n)=>{const s=e.clientWidth,i=e.offsetLeft;if(t[n]){const e=t[n].clientWidth;p.write((()=>{t[n].style.left=i+s-e+"px"}))}}))}))}#G(){return p.read((()=>{const e=this.#e.querySelector(".wrapping-container");let t=0,n=window.innerHeight;e&&(t=e.scrollTop,n=e.clientHeight);let s=Math.floor((t-180)/v),i=Math.ceil((t+n+180)/v);return s=Math.max(0,s),i=Math.min(this.#n.filter((e=>!e.hidden)).length,i),{topVisibleRow:s,bottomVisibleRow:i}}))}#N(){this.#v=!1}#S(){return this.#b||this.#w}async#R(){if(this.#i)return void(this.#x=!0);this.#i=!0;const{topVisibleRow:e,bottomVisibleRow:t}=await this.#G(),n=this.#n.filter((e=>!e.hidden)),s=n.filter(((n,s)=>s>=e&&s<=t)),i=this.#t.findIndex((e=>e.visible)),o=this.#t.some((e=>!0===e.sortable));await p.write((()=>{l.render(l.html`
      ${this.#t.map(((e,t)=>this.#U(e,[t,0])))}
      <div class="wrapping-container" @scroll=${this.#W} @focusout=${this.#N}>
        <table
          aria-rowcount=${this.#n.length}
          aria-colcount=${this.#t.length}
          @keydown=${this.#O}
        >
          <colgroup>
            ${this.#t.map(((e,t)=>{const n=`width: ${(0,m.calculateColumnWidthPercentageFromWeighting)(this.#t,e.id)}%`;return e.visible?l.html`<col style=${n} data-col-column-index=${t}>`:l.nothing}))}
          </colgroup>
          <thead>
            <tr @contextmenu=${this.#A}>
              ${this.#t.map(((e,t)=>{const n=l.Directives.classMap({hidden:!e.visible,firstVisibleColumn:t===i}),s=this.#S(),r=o&&t===s[0]&&0===s[1];return l.html`<th class=${n}
                  style=${l.Directives.ifDefined(e.styles?l.Directives.styleMap(e.styles):void 0)}
                  data-grid-header-cell=${e.id}
                  @focus=${()=>{this.#E([t,0])}}
                  @click=${()=>{this.#D(e,t)}}
                  title=${e.title}
                  aria-sort=${l.Directives.ifDefined(this.#T(e))}
                  aria-colindex=${t+1}
                  data-row-index='0'
                  data-col-index=${t}
                  tabindex=${l.Directives.ifDefined(o?r?"0":"-1":void 0)}
                >${e.titleElement||e.title}</th>`}))}
            </tr>
          </thead>
          <tbody>
            <tr class="filler-row-top padding-row" style=${l.Directives.styleMap({height:e*v+"px"})}></tr>
            ${l.Directives.repeat(s,(e=>this.#a.get(e)),(e=>{const t=this.#a.get(e);if(void 0===t)throw new Error("Trying to render a row that has no index in the rowIndexMap");const n=this.#S(),s=t+1,o=!!this.#b&&s===this.#b[1],r=l.Directives.classMap({selected:o,hidden:!0===e.hidden});return l.html`
                <tr
                  aria-rowindex=${t+1}
                  class=${r}
                  style=${l.Directives.ifDefined(e.styles?l.Directives.styleMap(e.styles):void 0)}
                  @contextmenu=${this.#P}
                >${this.#t.map(((t,o)=>{const r=(0,m.getRowEntryForColumnId)(e,t.id),a=l.Directives.classMap({hidden:!t.visible,firstVisibleColumn:o===i}),d=o===n[0]&&s===n[1],c=t.visible?(0,m.renderCellValue)(r):null;return l.html`<td
                    class=${a}
                    style=${l.Directives.ifDefined(t.styles?l.Directives.styleMap(t.styles):void 0)}
                    tabindex=${d?"0":"-1"}
                    aria-colindex=${o+1}
                    title=${r.title||(0,m.getCellTitleFromCellContent)(String(r.value))}
                    data-row-index=${s}
                    data-col-index=${o}
                    data-grid-value-cell-for-column=${t.id}
                    @focus=${()=>{this.#E([o,s]),this.dispatchEvent(new(0,h.BodyCellFocusedEvent)(r,e))}}
                  >${c}</td>`}))}
              `}))}
            ${this.#z(s.length)}
            <tr class="filler-row-bottom padding-row" style=${l.Directives.styleMap({height:Math.max(0,n.length-t)*v+"px"})}></tr>
          </tbody>
        </table>
      </div>
      `,this.#e,{host:this})}));const r=this.#S()[1],a=this.#$();this.#v&&r>0&&a&&this.#k(a),this.#M(),this.#F(),this.#C&&this.#c(),this.#i=!1,this.#C=!0,this.#x&&(this.#x=!1,this.#R())}}a.CustomElements.defineComponent("devtools-data-grid",x)})),t.register("4qIrv",(function(t,n){e(t.exports,"default",(()=>i));const s=new CSSStyleSheet;s.replaceSync('/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  height: 100%;\n  display: block;\n  position: relative;\n}\n\n/* Ensure that vertically we don\'t overflow */\n.wrapping-container {\n  overflow-y: auto;\n  /* Use max-height instead of height to ensure that the\n    table does not use more space than necessary. */\n  height: 100%;\n}\n\n.wrapping-container::-webkit-scrollbar {\n  display: none;\n}\n\ntable {\n  border-spacing: 0;\n  width: 100%;\n  height: 100%;\n  /* To make sure that we properly hide overflowing text\n    when horizontal space is too narrow. */\n  table-layout: fixed;\n}\n\ntr {\n  outline: none;\n}\n\nthead tr {\n  height: 27px;\n}\n\ntbody tr {\n  background-color: var(--override-data-grid-row-background-color, --color-background);\n}\n\ntbody tr.selected {\n  background-color: var(--color-background-elevation-1);\n}\n\ntd,\nth {\n  padding: 1px 4px;\n  border-left: 1px solid var(--color-details-hairline);\n  color: var(--color-text-primary);\n  line-height: var(--table-row-height);\n  height: var(--table-row-height);\n  user-select: text;\n  /* Ensure that text properly cuts off if horizontal space is too narrow */\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  overflow: hidden;\n}\n\nth {\n  font-weight: normal;\n  text-align: left;\n  border-bottom: 1px solid var(--color-details-hairline);\n  position: sticky;\n  top: 0;\n  z-index: 2;\n  background-color: var(--color-background-elevation-1);\n}\n\ntd:focus,\nth:focus {\n  outline: var(--color-primary) auto 1px;\n}\n\n.cell-resize-handle {\n  top: 0;\n  height: 100%;\n  z-index: 3;\n  width: 20px;\n  cursor: col-resize;\n  position: absolute;\n}\n\n/* There is no divider before the first cell */\ntd.firstVisibleColumn,\nth.firstVisibleColumn {\n  border-left: none;\n}\n\n.hidden {\n  display: none;\n}\n\n.filler-row td {\n  /* By making the filler row cells 100% they take up any extra height,\n  * leaving the cells with content to be the regular height, and the\n  * final filler row to be as high as it needs to be to fill the empty\n  * space.\n  */\n  height: 100%;\n  pointer-events: none;\n  padding: 0;\n}\n\n.filler-row.empty-table td {\n  /* If the table is empty and we have no renderable rows, the filler row cell\n   * needs to have some form of padding to make it have some height, else it\n   * stays at a height of 0px and doesn\'t fill the table. With this padding set,\n   * it will then have some height & expand the row it\'s in to 100% high as\n   * desired. */\n  padding: 1px;\n}\n\n[aria-sort="descending"]::after {\n  content: "";\n  width: 0;\n  border-left: 0.4em solid transparent;\n  border-right: 0.4em solid transparent;\n  border-top: 0.4em solid var(--color-text-secondary);\n  position: absolute;\n  right: 0.5em;\n  top: 0.85em;\n}\n\n[aria-sort="ascending"]::after {\n  content: "";\n  width: 0;\n  border-bottom: 0.4em solid var(--color-text-secondary);\n  border-left: 0.4em solid transparent;\n  border-right: 0.4em solid transparent;\n  position: absolute;\n  right: 0.5em;\n  top: 0.7em;\n}\n\n/*# sourceURL=dataGrid.css */\n');var i=s})),t.register("lE1VZ",(function(t,n){e(t.exports,"ColumnHeaderClickEvent",(()=>s)),e(t.exports,"ContextMenuColumnSortClickEvent",(()=>i)),e(t.exports,"ContextMenuHeaderResetClickEvent",(()=>o)),e(t.exports,"BodyCellFocusedEvent",(()=>r));class s extends Event{static eventName="columnheaderclick";data;constructor(e,t){super(s.eventName),this.data={column:e,columnIndex:t}}}class i extends Event{static eventName="contextmenucolumnsortclick";data;constructor(e){super(i.eventName),this.data={column:e}}}class o extends Event{static eventName="contextmenuheaderresetclick";constructor(){super(o.eventName)}}Event;class r extends Event{static eventName="cellfocused";data;constructor(e,t){super(r.eventName,{composed:!0}),this.data={cell:e,row:t}}}})),t.register("anPKn",(function(n,s){e(n.exports,"addColumnVisibilityCheckboxes",(()=>r)),e(n.exports,"addSortableColumnItems",(()=>l));var i=t("lE1VZ");function o(e,t){const n=!t.visible,s=e.data.columns.map((e=>(e===t&&(e.visible=n),e)));e.data={...e.data,columns:s}}function r(e,t){const{columns:n}=e.data;for(const s of n)s.hideable&&t.defaultSection().appendCheckboxItem(s.title,(()=>{o(e,s)}),s.visible)}function l(e,t){const n=e.data.columns.filter((e=>!0===e.sortable));if(n.length>0)for(const s of n)t.defaultSection().appendItem(s.title,(()=>{e.dispatchEvent(new(0,i.ContextMenuColumnSortClickEvent)(s))}))}})),t.register("2GxU3",(function(n,s){e(n.exports,"getStringifiedCellValues",(()=>l)),e(n.exports,"getRowEntryForColumnId",(()=>a)),e(n.exports,"renderCellValue",(()=>d)),e(n.exports,"calculateColumnWidthPercentageFromWeighting",(()=>c)),e(n.exports,"handleArrowKeyNavigation",(()=>h)),e(n.exports,"calculateFirstFocusableCell",(()=>u)),e(n.exports,"getCellTitleFromCellContent",(()=>m));var i=t("lz7WY"),o=t("bOI8u"),r=t("cY3yZ");function l(e){return JSON.stringify(e.map((e=>e.value instanceof r.Icon.Icon?null:e.value))).toLowerCase()}function a(e,t){const n=e.cells.find((e=>e.columnId===t));if(void 0===n)throw new Error(`Found a row that was missing an entry for column ${t}.`);return n}function d(e){return e.renderer?e.renderer(e.value):o.primitiveRenderer(e.value)}function c(e,t){const n=e.filter((e=>e.visible)).reduce(((e,t)=>e+t.widthWeighting),0),s=e.find((e=>e.id===t));if(!s)throw new Error(`Could not find column with ID ${t}`);if(s.widthWeighting<1)throw new Error(`Error with column ${t}: width weightings must be >= 1.`);return s.visible?Math.round(s.widthWeighting/n*100):0}function h(e){const{key:t,currentFocusedCell:n,columns:s,rows:o}=e,[r,l]=n;switch(t){case"ArrowLeft":{if(r===s.findIndex((e=>e.visible)))return[r,l];let e=r;for(let t=e-1;t>=0;t--){if(s[t].visible){e=t;break}}return[e,l]}case"ArrowRight":{let e=r;for(let t=e+1;t<s.length;t++){if(s[t].visible){e=t;break}}return[e,l]}case"ArrowUp":{const e=s.some((e=>!0===e.sortable))?0:1;if(l===e)return[r,l];let t=l;for(let n=l-1;n>=e;n--){if(0===n){t=0;break}if(!o[n-1].hidden){t=n;break}}return[r,t]}case"ArrowDown":{if(0===l){const e=o.findIndex((e=>!e.hidden));return e>-1?[r,e+1]:[r,l]}let e=l;for(let t=e+1;t<o.length+1;t++){if(!o[t-1].hidden){e=t;break}}return[r,e]}default:return i.assertNever(t,`Unknown arrow key: ${t}`)}}const u=e=>{const{columns:t,rows:n}=e,s=t.some((e=>!0===e.sortable))?0:n.findIndex((e=>!e.hidden))+1;return[t.findIndex((e=>e.visible)),s]},m=e=>e.length<25?e:e.substr(0,20)+"…"})),t.register("bOI8u",(function(n,s){e(n.exports,"primitiveRenderer",(()=>o)),e(n.exports,"codeBlockRenderer",(()=>r));var i=t("dS5IF");const o=e=>i.html`${e}`,r=e=>{if(!e)return i.nothing;const t=String(e);return i.html`<code>${t}</code>`}})),t.register("adQOh",(function(n,s){e(n.exports,"DataGridController",(()=>d));var i=t("dS5IF"),o=t("kpUjp"),r=t("2GxU3"),l=t("hIlx9"),a=t("1CI57");class d extends HTMLElement{static litTagName=i.literal`devtools-data-grid-controller`;#e=this.attachShadow({mode:"open"});#C=!1;#t=[];#n=[];#r=void 0;#V=[];#B=[];#s=null;#q=[];connectedCallback(){this.#e.adoptedStyleSheets=[a.default]}get data(){return{columns:this.#V,rows:this.#B,filters:this.#q,contextMenus:this.#r}}set data(e){this.#V=e.columns,this.#B=e.rows,this.#r=e.contextMenus,this.#q=e.filters||[],this.#r=e.contextMenus,this.#t=[...this.#V],this.#n=this.#_(e.rows,this.#q),!this.#C&&e.initialSort&&(this.#s=e.initialSort),this.#s&&this.#K(this.#s),this.#R()}#Z(e,t){let n=!1;const{key:s,text:i,negative:o,regex:l}=t;let a;return a=s?(0,r.getStringifiedCellValues)([(0,r.getRowEntryForColumnId)(e,s)]):(0,r.getStringifiedCellValues)(e.cells),l?n=l.test(a):i&&(n=a.includes(i.toLowerCase())),o?!n:n}#_(e,t){return 0===t.length?[...e]:e.map((e=>{let n=!0;for(const s of t){if(!this.#Z(e,s)){n=!1;break}}return{...e,hidden:!n}}))}#K(e){const{columnId:t,direction:n}=e;this.#n.sort(((e,s)=>{const i=(0,r.getRowEntryForColumnId)(e,t),o=(0,r.getRowEntryForColumnId)(s,t),l="number"==typeof i.value?i.value:String(i.value).toUpperCase(),a="number"==typeof o.value?o.value:String(o.value).toUpperCase();return l<a?"ASC"===n?-1:1:l>a?"ASC"===n?1:-1:0})),this.#R()}#D(e){const{column:t}=e.data;this.#j(t)}#j(e){if(this.#s&&this.#s.columnId===e.id){const{columnId:e,direction:t}=this.#s;this.#s="DESC"===t?null:{columnId:e,direction:"DESC"}}else this.#s={columnId:e.id,direction:"ASC"};this.#s?this.#K(this.#s):(this.#n=this.#_(this.#B,this.#q),this.#R())}#Q(e){this.#j(e.data.column)}#Y(){this.#s=null,this.#n=[...this.#B],this.#R()}#R(){i.render(i.html`
      <${l.DataGrid.litTagName} .data=${{columns:this.#t,rows:this.#n,activeSort:this.#s,contextMenus:this.#r}}
        @columnheaderclick=${this.#D}
        @contextmenucolumnsortclick=${this.#Q}
        @contextmenuheaderresetclick=${this.#Y}
     ></${l.DataGrid.litTagName}>
    `,this.#e,{host:this}),this.#C=!0}}o.CustomElements.defineComponent("devtools-data-grid-controller",d)})),t.register("1CI57",(function(t,n){e(t.exports,"default",(()=>i));const s=new CSSStyleSheet;s.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  display: block;\n  height: 100%;\n  overflow: hidden;\n}\n\n/*# sourceURL=dataGridController.css */\n");var i=s})),t.register("k01RS",(function(n,s){e(n.exports,"DataGridControllerIntegrator",(()=>r));var i=t("9z2ZV"),o=t("adQOh");class r extends i.Widget.VBox{dataGrid;constructor(e){super(!0,!0),this.dataGrid=new(0,o.DataGridController),this.dataGrid.data=e,this.contentElement.appendChild(this.dataGrid)}data(){return this.dataGrid.data}update(e){this.dataGrid.data=e}}}));
//# sourceMappingURL=protocol_monitor.0e1b09e2.js.map
