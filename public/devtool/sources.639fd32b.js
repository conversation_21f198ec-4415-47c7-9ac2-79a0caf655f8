function e(e,t,r,n){Object.defineProperty(e,t,{get:r,set:n,enumerable:!0,configurable:!0})}var t=("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}).parcelRequire20b5;t.register("zPo9O",(function(r,n){e(r.exports,"NamesResolver",(()=>t("714He")));t("714He")})),t.register("714He",(function(r,n){e(r.exports,"Identifier",(()=>p)),e(r.exports,"scopeIdentifiers",(()=>b)),e(r.exports,"resolveScopeChain",(()=>g)),e(r.exports,"resolveScope",(()=>f)),e(r.exports,"getScopeResolvedForTest",(()=>x)),e(r.exports,"allVariablesInCallFrame",(()=>m)),e(r.exports,"resolveExpression",(()=>d)),e(r.exports,"resolveThisObject",(()=>h)),e(r.exports,"resolveScopeInObject",(()=>w)),e(r.exports,"RemoteObject",(()=>j)),e(r.exports,"setScopeResolvedForTest",(()=>v));var o=t("lz7WY"),s=t("eQFvP"),i=t("fMswD"),c=t("kaczT"),a=t("7f6zc");const u=new WeakMap,l=new WeakMap;class p{name;lineNumber;columnNumber;constructor(e,t,r){this.name=e,this.lineNumber=t,this.columnNumber=r}}const b=async function(e){if("global"===e.type())return[];const t=e.startLocation(),r=e.endLocation();if(!t||!r)return[];const n=t.script();if(!n||!n.sourceMapURL||n!==r.script())return[];const{content:o}=await n.requestContent();if(!o)return[];const s=new a.Text.Text(o),i=new a.TextRange.TextRange(t.lineNumber,t.columnNumber,r.lineNumber,r.columnNumber),u=s.extract(i),l=s.toSourceRange(i).offset,b="function fui",g=await c.FormatterWorkerPool.formatterWorkerPool().javaScriptIdentifiers(b+u),f=[],m=new a.TextCursor.TextCursor(s.lineEndings());for(const e of g){if(e.offset<b.length)continue;const t=l+e.offset-b.length;m.resetTo(t),f.push(new p(e.name,m.lineNumber(),m.columnNumber()))}return f},g=async function(e){if(!e)return null;const{pluginManager:t}=i.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance();if(t){const r=await t.resolveScopeChain(e);if(r)return r}return e.scopeChain()},f=async e=>{let t=u.get(e);const r=e.callFrame().script,n=i.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().sourceMapForScript(r);if(!t||t.sourceMap!==n){const s=(async()=>{const t=new Map;if(n){const s=new Map,i=[];for(const c of await b(e)){const e=n.findEntry(c.lineNumber,c.columnNumber);e&&e.name?t.set(c.name,e.name):i.push(o(r,n,c,s).then((e=>{e&&t.set(c.name,e)})))}await Promise.all(i).then(x())}return t})();t={sourceMap:n,identifiersPromise:s},u.set(e,{sourceMap:n,identifiersPromise:s})}return await t.identifiersPromise;async function o(e,t,r,n){const o=t.findEntry(r.lineNumber,r.columnNumber),s=t.findEntry(r.lineNumber,r.columnNumber+r.name.length);if(!(o&&s&&o.sourceURL&&o.sourceURL===s.sourceURL&&o.sourceLineNumber&&o.sourceColumnNumber&&s.sourceLineNumber&&s.sourceColumnNumber))return null;const c=new a.TextRange.TextRange(o.sourceLineNumber,o.sourceColumnNumber,s.sourceLineNumber,s.sourceColumnNumber),u=i.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().uiSourceCodeForSourceMapSourceURL(e.debuggerModel,o.sourceURL,e.isContentScript());if(!u)return null;const{content:l}=await u.requestContent();if(!l)return null;let p=n.get(l);p||(p=new a.Text.Text(l),n.set(l,p));const b=p.extract(c).trim();return/[a-zA-Z0-9_$]+/.test(b)?b:null}},m=async e=>{const t=l.get(e);if(t)return t;const r=e.scopeChain(),n=await Promise.all(r.map(f)),o=new Map;for(const e of n)for(const[t,r]of e)r&&!o.has(r)&&o.set(r,t);return l.set(e,o),o},d=async(e,t,r,n,o,s)=>{if("application/wasm"===r.mimeType())return`memories["${t}"] ?? locals["${t}"] ?? tables["${t}"] ?? functions["${t}"] ?? globals["${t}"]`;if(!r.contentType().isFromSourceMap())return"";const u=await m(e);if(u.has(t))return u.get(t);const l=(await i.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().uiLocationToRawLocations(r,n,o)).find((t=>t.debuggerModel===e.debuggerModel));if(!l)return"";const p=l.script();if(!p)return"";const b=i.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().sourceMapForScript(p);if(!b)return"";const{content:g}=await p.requestContent();if(!g)return"";const f=new a.Text.Text(g),d=b.reverseMapTextRange(r.url(),new a.TextRange.TextRange(n,o,n,s));if(!d)return"";const h=f.extract(d);return h?await c.FormatterWorkerPool.formatterWorkerPool().evaluatableJavaScriptSubstring(h):""},h=async e=>{if(!e)return null;const t=e.scopeChain();if(0===t.length)return e.thisObject();const r=await f(t[0]),n=o.MapUtilities.inverse(r).get("this");if(!n||1!==n.size)return e.thisObject();const[s]=n.values(),i=await e.evaluate({expression:s,objectGroup:"backtrace",includeCommandLineAPI:!1,silent:!0,returnByValue:!1,generatePreview:!0});return"exceptionDetails"in i?!i.exceptionDetails&&i.object?i.object:e.thisObject():null},w=function(e){const t=e.startLocation(),r=e.endLocation(),n=t?t.script():null;return"global"!==e.type()&&n&&r&&n.sourceMapURL&&n===r.script()?new j(e):e.object()};class j extends s.RemoteObject.RemoteObject{scope;object;constructor(e){super(),this.scope=e,this.object=e.object()}customPreview(){return this.object.customPreview()}get objectId(){return this.object.objectId}get type(){return this.object.type}get subtype(){return this.object.subtype}get value(){return this.object.value}get description(){return this.object.description}get hasChildren(){return this.object.hasChildren}get preview(){return this.object.preview}arrayLength(){return this.object.arrayLength()}getOwnProperties(e){return this.object.getOwnProperties(e)}async getAllProperties(e,t){const r=await this.object.getAllProperties(e,t),n=await f(this.scope),o=r.properties,i=r.internalProperties,c=[];if(o)for(let e=0;e<o.length;++e){const t=o[e],r=n.get(t.name)||o[e].name;t.value&&c.push(new s.RemoteObject.RemoteObjectProperty(r,t.value,t.enumerable,t.writable,t.isOwn,t.wasThrown,t.symbol,t.synthetic))}return{properties:c,internalProperties:i}}async setPropertyValue(e,t){const r=await f(this.scope);let n;n="string"==typeof e?e:e.value;let o=n;for(const e of r.keys())if(r.get(e)===n){o=e;break}return this.object.setPropertyValue(o,t)}async deleteProperty(e){return this.object.deleteProperty(e)}callFunction(e,t){return this.object.callFunction(e,t)}callFunctionJSON(e,t){return this.object.callFunctionJSON(e,t)}release(){this.object.release()}debuggerModel(){return this.object.debuggerModel()}runtimeModel(){return this.object.runtimeModel()}isNode(){return this.object.isNode()}}let y=function(){};const x=()=>y,v=e=>{y=e}}));
//# sourceMappingURL=sources.639fd32b.js.map
