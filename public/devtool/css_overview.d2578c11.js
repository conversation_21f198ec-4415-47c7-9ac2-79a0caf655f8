function n(n,o,t,e){Object.defineProperty(n,o,{get:t,set:e,enumerable:!0,configurable:!0})}var o=("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}).parcelRequire20b5;o.register("9apcb",(function(t,e){n(t.exports,"Button",(()=>o("9g3AZ")));o("9g3AZ")})),o.register("9g3AZ",(function(t,e){n(t.exports,"Button",(()=>d));var r=o("dS5IF"),i=o("kpUjp"),s=o("cY3yZ"),a=o("4I9gW");class d extends HTMLElement{static formAssociated=!0;static litTagName=r.literal`devtools-button`;#n=this.attachShadow({mode:"open",delegatesFocus:!0});#o=this.#t.bind(this);#e=this.#r.bind(this);#i={size:"MEDIUM",disabled:!1,active:!1,spinner:!1,type:"button"};#s=!0;#a=this.attachInternals();constructor(){super(),this.setAttribute("role","presentation"),this.addEventListener("click",this.#e,!0)}set data(n){this.#i.variant=n.variant,this.#i.iconUrl=n.iconUrl,this.#i.size=n.size||"MEDIUM",this.#i.active=Boolean(n.active),this.#i.spinner=Boolean(n.spinner),this.#i.type=n.type||"button",this.#d(n.disabled||!1),this.#i.title=n.title,i.ScheduledRender.scheduleRender(this,this.#o)}set iconUrl(n){this.#i.iconUrl=n,i.ScheduledRender.scheduleRender(this,this.#o)}set variant(n){this.#i.variant=n,i.ScheduledRender.scheduleRender(this,this.#o)}set size(n){this.#i.size=n,i.ScheduledRender.scheduleRender(this,this.#o)}set type(n){this.#i.type=n,i.ScheduledRender.scheduleRender(this,this.#o)}set title(n){this.#i.title=n,i.ScheduledRender.scheduleRender(this,this.#o)}set disabled(n){this.#d(n),i.ScheduledRender.scheduleRender(this,this.#o)}set active(n){this.#i.active=n,i.ScheduledRender.scheduleRender(this,this.#o)}set spinner(n){this.#i.spinner=n,i.ScheduledRender.scheduleRender(this,this.#o)}#d(n){this.#i.disabled=n,this.toggleAttribute("disabled",n)}focus(){this.#n.querySelector("button")?.focus()}connectedCallback(){this.#n.adoptedStyleSheets=[a.default],i.ScheduledRender.scheduleRender(this,this.#o)}#r(n){if(this.#i.disabled)return n.stopPropagation(),void n.preventDefault();this.form&&"submit"===this.#i.type&&(n.preventDefault(),this.form.dispatchEvent(new SubmitEvent("submit",{submitter:this}))),this.form&&"reset"===this.#i.type&&(n.preventDefault(),this.form.reset())}#l(n){const o=n.target?.assignedNodes();this.#s=!o||!Boolean(o.length),i.ScheduledRender.scheduleRender(this,this.#o)}#t(){if(!this.#i.variant)throw new Error("Button requires a variant to be defined");if("toolbar"===this.#i.variant){if(!this.#i.iconUrl)throw new Error("Toolbar button requires an icon");if(!this.#s)throw new Error("Tooblar button does not accept children")}if("round"===this.#i.variant){if(!this.#i.iconUrl)throw new Error("Round button requires an icon");if(!this.#s)throw new Error("Round button does not accept children")}const n={primary:"primary"===this.#i.variant,secondary:"secondary"===this.#i.variant,toolbar:"toolbar"===this.#i.variant,round:"round"===this.#i.variant,"text-with-icon":Boolean(this.#i.iconUrl)&&!this.#s,"only-icon":Boolean(this.#i.iconUrl)&&this.#s,small:Boolean("SMALL"===this.#i.size),active:this.#i.active},o={primary:"primary"===this.#i.variant,secondary:"secondary"===this.#i.variant,disabled:Boolean(this.#i.disabled),"spinner-component":!0};r.render(r.html`
        <button title=${r.Directives.ifDefined(this.#i.title)} .disabled=${this.#i.disabled} class=${r.Directives.classMap(n)}>
          ${this.#i.iconUrl?r.html`<${s.Icon.Icon.litTagName}
            .data=${{iconPath:this.#i.iconUrl,color:"var(--color-background)"}}
          >
          </${s.Icon.Icon.litTagName}>`:""}
          ${this.#i.spinner?r.html`<span class=${r.Directives.classMap(o)}></span>`:""}
          <slot @slotchange=${this.#l}></slot>
        </button>
      `,this.#n,{host:this})}get value(){return this.#i.value||""}set value(n){this.#i.value=n}get form(){return this.#a.form}get name(){return this.getAttribute("name")}get type(){return this.#i.type}get validity(){return this.#a.validity}get validationMessage(){return this.#a.validationMessage}get willValidate(){return this.#a.willValidate}checkValidity(){return this.#a.checkValidity()}reportValidity(){return this.#a.reportValidity()}}i.CustomElements.defineComponent("devtools-button",d)})),o.register("4I9gW",(function(o,t){n(o.exports,"default",(()=>r));const e=new CSSStyleSheet;e.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\n/**\n* Reset default UA styles for focused elements.\n* The button styles below explicitly implement custom focus styles.\n*/\n*:focus,\n*:focus-visible,\n:host(:focus),\n:host(:focus-visible) {\n  outline: none;\n}\n\n:host {\n  display: inline-flex;\n  flex-direction: row;\n}\n\nbutton {\n  align-items: center;\n  border-radius: 4px;\n  display: inline-flex;\n  font-family: inherit;\n  font-size: 12px;\n  font-weight: 500;\n  height: 24px;\n  line-height: 14px;\n  padding: 5px 12px;\n  justify-content: center;\n  width: 100%;\n}\n\nbutton.small {\n  height: 18px;\n  border-radius: 2px;\n}\n\nbutton:focus-visible {\n  box-shadow: 0 0 0 2px var(--color-button-outline-focus);\n}\n\nbutton.toolbar,\nbutton.round {\n  background: transparent;\n  border-radius: 2px;\n  border: none;\n  height: 24px;\n  width: 24px;\n  overflow: hidden;\n  padding: 0;\n  white-space: nowrap;\n}\n\nbutton.round {\n  border-radius: 100%;\n  height: 30px;\n  width: 30px;\n}\n\nbutton.toolbar.small {\n  height: 18px;\n  width: 18px;\n}\n\nbutton.round.small {\n  height: 24px;\n  width: 24px;\n}\n\nbutton.primary {\n  border: 1px solid var(--color-primary);\n  background: var(--color-primary);\n  color: var(--color-background);\n}\n\nbutton.primary:hover {\n  background: var(--color-button-primary-background-hovering);\n  border: 1px solid var(--color-button-primary-background-hovering);\n}\n\nbutton.primary.active,\nbutton.primary:active {\n  background: var(--color-button-primary-background-pressed);\n  border: 1px solid var(--color-button-primary-background-pressed);\n}\n\nbutton.primary:disabled,\nbutton.primary:disabled:hover {\n  border: 1px solid transparent;\n  background: var(--color-background-elevation-1);\n  color: var(--color-text-disabled);\n  cursor: not-allowed;\n}\n\nbutton.secondary {\n  border: 1px solid var(--color-details-hairline);\n  background: var(--color-background);\n  color: var(--color-primary);\n}\n\nbutton.secondary:hover {\n  background: var(--color-button-secondary-background-hovering);\n}\n\nbutton.secondary.active,\nbutton.secondary:active {\n  background: var(--color-button-secondary-background-pressed);\n  border: 1px solid var(--color-button-secondary-background-pressed);\n}\n\nbutton.secondary:focus-visible {\n  border: 1px solid var(--color-background);\n}\n\nbutton.secondary:disabled,\nbutton.secondary:disabled:hover {\n  border: 1px solid var(--color-background-elevation-1);\n  background: var(--color-background);\n  color: var(--color-text-disabled);\n  cursor: not-allowed;\n}\n\nbutton.secondary.active:focus-visible,\nbutton.secondary:active:focus-visible {\n  border: 1px solid var(--color-button-secondary-background-pressed);\n}\n\nbutton.toolbar:hover,\nbutton.round:hover {\n  background-color: var(--color-iconbutton-hover);\n}\n\nbutton.toolbar.active,\nbutton.toolbar:active,\nbutton.round.active,\nbutton.round:active {\n  background-color: var(--color-iconbutton-pressed);\n}\n\nbutton.toolbar:focus-visible,\nbutton.round:focus-visible {\n  background-color: var(--color-background-elevation-2);\n}\n\nbutton.toolbar:disabled,\nbutton.toolbar:disabled:hover,\nbutton.round:disabled,\nbutton.round:disabled:hover {\n  background: var(--color-background);\n  color: var(--color-text-disabled);\n  cursor: not-allowed;\n}\n\nbutton.text-with-icon {\n  padding: 0 12px 0 4px;\n}\n\nbutton.small.text-with-icon {\n  padding: 0 9px 0 3px;\n}\n\nbutton.only-icon {\n  padding: 0;\n}\n\nbutton devtools-icon {\n  width: 19px;\n  height: 19px;\n}\n\nbutton.toolbar devtools-icon,\nbutton.round devtools-icon {\n  width: 24px;\n  height: 24px;\n\n  --icon-color: var(--color-text-secondary);\n}\n\nbutton.primary devtools-icon {\n  --icon-color: var(--color-background);\n}\n\nbutton.secondary devtools-icon {\n  --icon-color: var(--color-primary);\n}\n\nbutton.small devtools-icon {\n  width: 14px;\n  height: 14px;\n}\n\nbutton.toolbar.small devtools-icon,\nbutton.round.small devtools-icon {\n  width: 18px;\n  height: 18px;\n}\n\nbutton.toolbar.active devtools-icon,\nbutton.toolbar:active devtools-icon {\n  --icon-color: var(--color-primary);\n}\n\nbutton.toolbar:hover devtools-icon {\n  --icon-color: var(--color-text-primary);\n}\n\nbutton.toolbar:disabled devtools-icon,\nbutton.round:disabled devtools-icon {\n  --icon-color: var(--color-text-disabled);\n}\n\nbutton.primary:disabled devtools-icon {\n  --icon-color: var(--color-text-disabled);\n}\n\nbutton.secondary:disabled devtools-icon {\n  --icon-color: var(--color-text-disabled);\n}\n\n.spinner-component.secondary {\n  border: 2px solid var(--color-primary);\n  border-right-color: transparent;\n}\n\n.spinner-component.disabled {\n  border: 2px solid var(--color-text-disabled);\n  border-right-color: transparent;\n}\n\n.spinner-component {\n  display: block;\n  width: 12px;\n  height: 12px;\n  border-radius: 6px;\n  border: 2px solid var(--color-background);\n  animation: spinner-animation 1s linear infinite;\n  border-right-color: transparent;\n  margin-right: 6px;\n}\n\n@keyframes spinner-animation {\n  from {\n    transform: rotate(0);\n  }\n\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/*# sourceURL=button.css */\n");var r=e}));
//# sourceMappingURL=css_overview.d2578c11.js.map
