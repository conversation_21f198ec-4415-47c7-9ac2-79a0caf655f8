function e(e,t,n,i){Object.defineProperty(e,t,{get:n,set:i,enumerable:!0,configurable:!0})}var t=("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}).parcelRequire20b5;t.register("fuChX",(function(n,i){e(n.exports,"BezierEditor",(()=>t("lveKp"))),e(n.exports,"ColorSwatch",(()=>t("ePWMq"))),e(n.exports,"CSSAngle",(()=>t("kKMaw"))),e(n.exports,"CSSAngleUtils",(()=>t("9rj5e"))),e(n.exports,"CSSLength",(()=>t("conuI"))),e(n.exports,"CSSLengthUtils",(()=>t("lmBV6"))),e(n.exports,"CSSShadowEditor",(()=>t("95or5"))),e(n.exports,"CSSShadowModel",(()=>t("3ep6O"))),e(n.exports,"CSSVarSwatch",(()=>t("3jpYY"))),e(n.exports,"FontEditor",(()=>t("7uOxM"))),e(n.exports,"FontEditorUtils",(()=>t("5nbJO"))),e(n.exports,"Swatches",(()=>t("1NVFN"))),e(n.exports,"SwatchPopoverHelper",(()=>t("kJnGS")));t("lveKp"),t("cl5Ri"),t("ePWMq"),t("kKMaw"),t("9rj5e"),t("conuI"),t("lmBV6"),t("95or5"),t("3ep6O"),t("3jpYY"),t("7uOxM"),t("gdQYz"),t("5nbJO"),t("5LeVJ"),t("1NVFN"),t("kJnGS")})),t.register("lveKp",(function(n,i){e(n.exports,"BezierEditor",(()=>d)),e(n.exports,"Presets",(()=>c)),e(n.exports,"Events",(()=>s));var s,o=t("koSS8"),r=t("lz7WY"),a=t("9z2ZV"),l=t("isOpE"),h=t("cl5Ri");class d extends(o.ObjectWrapper.eventMixin(a.Widget.VBox)){bezierInternal;previewElement;previewOnion;outerContainer;selectedCategory;presetsContainer;presetUI;presetCategories;curveUI;curve;header;label;mouseDownPosition;controlPosition;selectedPoint;previewAnimation;constructor(e){super(!0),this.bezierInternal=e,this.contentElement.tabIndex=0,this.setDefaultFocusedElement(this.contentElement),this.previewElement=this.contentElement.createChild("div","bezier-preview-container"),this.previewElement.createChild("div","bezier-preview-animation"),this.previewElement.addEventListener("click",this.startPreviewAnimation.bind(this)),this.previewOnion=this.contentElement.createChild("div","bezier-preview-onion"),this.previewOnion.addEventListener("click",this.startPreviewAnimation.bind(this)),this.outerContainer=this.contentElement.createChild("div","bezier-container"),this.selectedCategory=null,this.presetsContainer=this.outerContainer.createChild("div","bezier-presets"),this.presetUI=new(0,h.BezierUI)(40,40,0,2,!1),this.presetCategories=[];for(let e=0;e<c.length;e++)this.presetCategories[e]=this.createCategory(c[e]),this.presetsContainer.appendChild(this.presetCategories[e].icon);this.curveUI=new(0,h.BezierUI)(150,250,50,7,!0),this.curve=a.UIUtils.createSVGChild(this.outerContainer,"svg","bezier-curve"),a.UIUtils.installDragHandle(this.curve,this.dragStart.bind(this),this.dragMove.bind(this),this.dragEnd.bind(this),"default"),this.header=this.contentElement.createChild("div","bezier-header");const t=this.createPresetModifyIcon(this.header,"bezier-preset-minus","M 12 6 L 8 10 L 12 14"),n=this.createPresetModifyIcon(this.header,"bezier-preset-plus","M 8 6 L 12 10 L 8 14");t.addEventListener("click",this.presetModifyClicked.bind(this,!1)),n.addEventListener("click",this.presetModifyClicked.bind(this,!0)),this.label=this.header.createChild("span","source-code bezier-display-value")}setBezier(e){e&&(this.bezierInternal=e,this.updateUI())}bezier(){return this.bezierInternal}wasShown(){this.registerCSSFiles([l.default]),this.unselectPresets();for(const e of this.presetCategories)for(let t=0;t<e.presets.length;t++)this.bezierInternal.asCSSText()===e.presets[t].value&&(e.presetIndex=t,this.presetCategorySelected(e));this.updateUI(),this.startPreviewAnimation()}onchange(){this.updateUI(),this.dispatchEventToListeners(s.BezierChanged,this.bezierInternal.asCSSText())}updateUI(){const e=this.selectedCategory?this.selectedCategory.presets[this.selectedCategory.presetIndex].name:this.bezierInternal.asCSSText().replace(/\s(-\d\.\d)/g,"$1");this.label.textContent=e,this.curveUI.drawCurve(this.bezierInternal,this.curve),this.previewOnion.removeChildren()}dragStart(e){this.mouseDownPosition=new a.Geometry.Point(e.x,e.y);const t=this.curveUI;this.controlPosition=new a.Geometry.Point(r.NumberUtilities.clamp((e.offsetX-t.radius)/t.curveWidth(),0,1),(t.curveHeight()+t.marginTop+t.radius-e.offsetY)/t.curveHeight());const n=this.controlPosition.distanceTo(this.bezierInternal.controlPoints[0])<this.controlPosition.distanceTo(this.bezierInternal.controlPoints[1]);return this.selectedPoint=n?0:1,this.bezierInternal.controlPoints[this.selectedPoint]=this.controlPosition,this.unselectPresets(),this.onchange(),e.consume(!0),!0}updateControlPosition(e,t){if(void 0===this.mouseDownPosition||void 0===this.controlPosition||void 0===this.selectedPoint)return;const n=(e-this.mouseDownPosition.x)/this.curveUI.curveWidth(),i=(t-this.mouseDownPosition.y)/this.curveUI.curveHeight(),s=new a.Geometry.Point(r.NumberUtilities.clamp(this.controlPosition.x+n,0,1),this.controlPosition.y-i);this.bezierInternal.controlPoints[this.selectedPoint]=s}dragMove(e){this.updateControlPosition(e.x,e.y),this.onchange()}dragEnd(e){this.updateControlPosition(e.x,e.y),this.onchange(),this.startPreviewAnimation()}createCategory(e){const t=document.createElement("div");t.classList.add("bezier-preset-category");const n=a.UIUtils.createSVGChild(t,"svg","bezier-preset monospace"),i={presets:e,presetIndex:0,icon:t};return this.presetUI.drawCurve(a.Geometry.CubicBezier.parse(i.presets[0].value),n),n.addEventListener("click",this.presetCategorySelected.bind(this,i)),i}createPresetModifyIcon(e,t,n){const i=a.UIUtils.createSVGChild(e,"svg","bezier-preset-modify "+t);i.setAttribute("width","20"),i.setAttribute("height","20");return a.UIUtils.createSVGChild(i,"path").setAttribute("d",n),i}unselectPresets(){for(const e of this.presetCategories)e.icon.classList.remove("bezier-preset-selected");this.selectedCategory=null,this.header.classList.remove("bezier-header-active")}presetCategorySelected(e,t){if(this.selectedCategory===e)return;this.unselectPresets(),this.header.classList.add("bezier-header-active"),this.selectedCategory=e,this.selectedCategory.icon.classList.add("bezier-preset-selected");const n=a.Geometry.CubicBezier.parse(e.presets[e.presetIndex].value);n&&(this.setBezier(n),this.onchange(),this.startPreviewAnimation()),t&&t.consume(!0)}presetModifyClicked(e,t){if(null===this.selectedCategory)return;const n=this.selectedCategory.presets.length;this.selectedCategory.presetIndex=(this.selectedCategory.presetIndex+(e?1:-1)+n)%n;const i=a.Geometry.CubicBezier.parse(this.selectedCategory.presets[this.selectedCategory.presetIndex].value);i&&(this.setBezier(i),this.onchange(),this.startPreviewAnimation())}startPreviewAnimation(){this.previewAnimation&&this.previewAnimation.cancel();const e=1600,t=[{offset:0,transform:"translateX(0px)",easing:this.bezierInternal.asCSSText(),opacity:1},{offset:.9,transform:"translateX(218px)",opacity:1},{offset:1,transform:"translateX(218px)",opacity:0}];this.previewAnimation=this.previewElement.animate(t,e),this.previewOnion.removeChildren();for(let t=0;t<=20;t++){const n=this.previewOnion.createChild("div","bezier-preview-animation").animate([{transform:"translateX(0px)",easing:this.bezierInternal.asCSSText()},{transform:"translateX(218px)"}],{duration:e,fill:"forwards"});n.pause(),n.currentTime=e*t/20}}}(s||(s={})).BezierChanged="BezierChanged";const c=[[{name:"ease-in-out",value:"ease-in-out"},{name:"In Out · Sine",value:"cubic-bezier(0.45, 0.05, 0.55, 0.95)"},{name:"In Out · Quadratic",value:"cubic-bezier(0.46, 0.03, 0.52, 0.96)"},{name:"In Out · Cubic",value:"cubic-bezier(0.65, 0.05, 0.36, 1)"},{name:"Fast Out, Slow In",value:"cubic-bezier(0.4, 0, 0.2, 1)"},{name:"In Out · Back",value:"cubic-bezier(0.68, -0.55, 0.27, 1.55)"}],[{name:"Fast Out, Linear In",value:"cubic-bezier(0.4, 0, 1, 1)"},{name:"ease-in",value:"ease-in"},{name:"In · Sine",value:"cubic-bezier(0.47, 0, 0.75, 0.72)"},{name:"In · Quadratic",value:"cubic-bezier(0.55, 0.09, 0.68, 0.53)"},{name:"In · Cubic",value:"cubic-bezier(0.55, 0.06, 0.68, 0.19)"},{name:"In · Back",value:"cubic-bezier(0.6, -0.28, 0.74, 0.05)"}],[{name:"ease-out",value:"ease-out"},{name:"Out · Sine",value:"cubic-bezier(0.39, 0.58, 0.57, 1)"},{name:"Out · Quadratic",value:"cubic-bezier(0.25, 0.46, 0.45, 0.94)"},{name:"Out · Cubic",value:"cubic-bezier(0.22, 0.61, 0.36, 1)"},{name:"Linear Out, Slow In",value:"cubic-bezier(0, 0, 0.2, 1)"},{name:"Out · Back",value:"cubic-bezier(0.18, 0.89, 0.32, 1.28)"}]]})),t.register("isOpE",(function(t,n){e(t.exports,"default",(()=>s));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright (c) 2015 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  width: 270px;\n  height: 350px;\n  user-select: none;\n  padding: 16px;\n  overflow: hidden;\n}\n\n.bezier-preset-selected > svg {\n  background-color: var(--legacy-selection-bg-color);\n}\n\n.bezier-container {\n  --override-bezier-control-color: #9c27b0;\n\n  display: flex;\n  margin-top: 38px;\n}\n\n.-theme-with-dark-background .bezier-container,\n:host-context(.-theme-with-dark-background) .bezier-container {\n  --override-bezier-control-color: rgb(196 79 216);\n}\n\n.bezier-preset {\n  width: 50px;\n  height: 50px;\n  padding: 5px;\n  margin: auto;\n  background-color: var(--color-background-elevation-1);\n  border-radius: 3px;\n}\n\n.bezier-preset line.bezier-control-line {\n  stroke: var(--color-text-secondary);\n  stroke-width: 1;\n  stroke-linecap: round;\n  fill: none;\n}\n\n.bezier-preset circle.bezier-control-circle {\n  fill: var(--color-text-secondary);\n}\n\n.bezier-preset path.bezier-path {\n  stroke: var(--color-background-inverted);\n  stroke-width: 2;\n  stroke-linecap: round;\n  fill: none;\n}\n\n.bezier-preset-selected path.bezier-path,\n.bezier-preset-selected line.bezier-control-line {\n  stroke: var(--color-background);\n}\n\n.bezier-preset-selected circle.bezier-control-circle {\n  fill: var(--color-background);\n}\n\n.bezier-curve line.linear-line {\n  stroke: var(--color-background-elevation-2);\n  stroke-width: 2;\n  stroke-linecap: round;\n  fill: none;\n}\n\n.bezier-curve line.bezier-control-line {\n  stroke: var(--override-bezier-control-color);\n  stroke-width: 2;\n  stroke-linecap: round;\n  fill: none;\n  opacity: 60%;\n}\n\n.bezier-curve circle.bezier-control-circle {\n  fill: var(--override-bezier-control-color);\n  cursor: pointer;\n}\n\n.bezier-curve path.bezier-path {\n  stroke: var(--color-background-inverted);\n  stroke-width: 3;\n  stroke-linecap: round;\n  fill: none;\n}\n\n.bezier-preview-container {\n  position: relative;\n  background-color: var(--color-background);\n  overflow: hidden;\n  border-radius: 20px;\n  width: 200%;\n  height: 20px;\n  z-index: 2;\n  flex-shrink: 0;\n  opacity: 0%;\n}\n\n.bezier-preview-animation {\n  background-color: #9c27b0; /* stylelint-disable-line plugin/use_theme_colors */\n  /* See: crbug.com/1152736 for color variable migration. */\n  width: 20px;\n  height: 20px;\n  border-radius: 20px;\n  position: absolute;\n}\n\n.bezier-preview-onion {\n  margin-top: -20px;\n  position: relative;\n  z-index: 1;\n}\n\n.bezier-preview-onion > .bezier-preview-animation {\n  opacity: 10%;\n}\n\nsvg.bezier-preset-modify {\n  background-color: var(--color-background-elevation-1);\n  border-radius: 35px;\n  display: inline-block;\n  visibility: hidden;\n  transition: transform 100ms cubic-bezier(0.4, 0, 0.2, 1);\n  cursor: pointer;\n  position: absolute;\n}\n\nsvg.bezier-preset-modify:hover,\n.bezier-preset:hover {\n  background-color: var(--color-background-elevation-2);\n}\n\n.bezier-preset-selected .bezier-preset:hover {\n  background-color: var(--legacy-selection-bg-color);\n}\n\n.bezier-preset-modify path {\n  stroke-width: 2;\n  stroke: var(--color-background-inverted);\n  fill: none;\n}\n\n.bezier-preset-selected .bezier-preset-modify {\n  opacity: 100%;\n}\n\n.bezier-preset-category {\n  width: 50px;\n  margin: 20px 0;\n  cursor: pointer;\n  transition: transform 100ms cubic-bezier(0.4, 0, 0.2, 1);\n}\n\nspan.bezier-display-value {\n  width: 100%;\n  user-select: text;\n  display: block;\n  text-align: center;\n  line-height: 20px;\n  height: 20px;\n  cursor: text;\n  white-space: nowrap !important; /* stylelint-disable-line declaration-no-important */\n}\n\nsvg.bezier-curve {\n  margin-left: 32px;\n  margin-top: -8px;\n}\n\nsvg.bezier-preset-modify.bezier-preset-plus {\n  right: 0;\n}\n\n.bezier-header {\n  margin-top: 16px;\n}\n\nsvg.bezier-preset-modify:active {\n  transform: scale(1.1);\n  background-color: var(--legacy-selection-bg-color);\n}\n\n.bezier-preset-category:active {\n  transform: scale(1.05);\n}\n\n.bezier-header-active > svg.bezier-preset-modify {\n  visibility: visible;\n}\n\n.bezier-preset-modify:active path {\n  stroke: var(--color-background);\n}\n\n/*# sourceURL=bezierEditor.css */\n");var s=i})),t.register("cl5Ri",(function(n,i){e(n.exports,"BezierUI",(()=>o));var s=t("9z2ZV");class o{width;height;marginTop;radius;linearLine;constructor(e,t,n,i,s){this.width=e,this.height=t,this.marginTop=n,this.radius=i,this.linearLine=s}static drawVelocityChart(e,t,n){const i=r;let s=["M",0,i];const o=.025;let a=e.evaluateAt(0);for(let t=o;t<1.025;t+=o){const o=e.evaluateAt(t);let r=(o.y-a.y)/(o.x-a.x);const l=a.x*(1-t)+o.x*t;r=Math.tanh(r/1.5),s=s.concat(["L",(l*n).toFixed(2),(i-r*i).toFixed(2)]),a=o}s=s.concat(["L",n.toFixed(2),i,"Z"]),t.setAttribute("d",s.join(" "))}curveWidth(){return this.width-2*this.radius}curveHeight(){return this.height-2*this.radius-2*this.marginTop}drawLine(e,t,n,i,o,r){const a=s.UIUtils.createSVGChild(e,"line",t);a.setAttribute("x1",String(n+this.radius)),a.setAttribute("y1",String(i+this.radius+this.marginTop)),a.setAttribute("x2",String(o+this.radius)),a.setAttribute("y2",String(r+this.radius+this.marginTop))}drawControlPoints(e,t,n,i,o){this.drawLine(e,"bezier-control-line",t,n,i,o);const r=s.UIUtils.createSVGChild(e,"circle","bezier-control-circle");r.setAttribute("cx",String(i+this.radius)),r.setAttribute("cy",String(o+this.radius+this.marginTop)),r.setAttribute("r",String(this.radius))}drawCurve(e,t){if(!e)return;const n=this.curveWidth(),i=this.curveHeight();t.setAttribute("width",String(this.width)),t.setAttribute("height",String(this.height)),t.removeChildren();const o=s.UIUtils.createSVGChild(t,"g");this.linearLine&&this.drawLine(o,"linear-line",0,i,n,0);const r=s.UIUtils.createSVGChild(o,"path","bezier-path"),a=[new s.Geometry.Point(e.controlPoints[0].x*n+this.radius,(1-e.controlPoints[0].y)*i+this.radius+this.marginTop),new s.Geometry.Point(e.controlPoints[1].x*n+this.radius,(1-e.controlPoints[1].y)*i+this.radius+this.marginTop),new s.Geometry.Point(n+this.radius,this.marginTop+this.radius)];r.setAttribute("d","M"+this.radius+","+(i+this.radius+this.marginTop)+" C"+a.join(" ")),this.drawControlPoints(o,0,i,e.controlPoints[0].x*n,(1-e.controlPoints[0].y)*i),this.drawControlPoints(o,n,0,e.controlPoints[1].x*n,(1-e.controlPoints[1].y)*i)}}const r=26})),t.register("ePWMq",(function(n,i){e(n.exports,"FormatChangedEvent",(()=>u)),e(n.exports,"ClickEvent",(()=>p)),e(n.exports,"ColorSwatch",(()=>g));var s=t("koSS8"),o=t("ixFnt"),r=t("kpUjp"),a=t("dS5IF"),l=t("drtCZ");const h={shiftclickToChangeColorFormat:"Shift-click to change color format"},d=o.i18n.registerUIStrings("ui/legacy/components/inline_editor/ColorSwatch.ts",h),c=o.i18n.getLocalizedString.bind(void 0,d);class u extends Event{static eventName="formatchanged";data;constructor(e,t){super(u.eventName,{}),this.data={format:e,text:t}}}class p extends Event{static eventName="swatchclick";constructor(){super(p.eventName,{})}}class g extends HTMLElement{static litTagName=a.literal`devtools-color-swatch`;shadow=this.attachShadow({mode:"open"});tooltip=c(h.shiftclickToChangeColorFormat);text=null;color=null;format=null;constructor(){super(),this.shadow.adoptedStyleSheets=[l.default]}static isColorSwatch(e){return"devtools-color-swatch"===e.localName}getColor(){return this.color}getFormat(){return this.format}get anchorBox(){const e=this.shadow.querySelector(".color-swatch");return e?e.boxInWindow():null}renderColor(e,t,n){if("string"==typeof e){if(this.color=s.Color.Color.parse(e),this.text=e,!this.color)return void this.renderTextOnly()}else this.color=e;this.format="boolean"==typeof t&&t?s.Settings.detectColorFormat(this.color):"string"==typeof t?t:this.color.format(),this.text=this.color.asString(this.format),n&&(this.tooltip=n),this.render()}renderTextOnly(){a.render(this.text,this.shadow,{host:this})}render(){a.render(a.html`<span class="color-swatch" title=${this.tooltip}><span class="color-swatch-inner"
        style="background-color: ${this.text};"
        @click=${this.onClick}
        @mousedown=${this.consume}
        @dblclick=${this.consume}></span></span><slot><span>${this.text}</span></slot>`,this.shadow,{host:this})}onClick(e){e.stopPropagation(),e.shiftKey?this.toggleNextFormat():this.dispatchEvent(new p)}consume(e){e.stopPropagation()}toggleNextFormat(){if(!this.color||!this.format)return;let e;do{this.format=m(this.color,this.format),e=this.color.asString(this.format)}while(e===this.text);e&&(this.text=e,this.render(),this.dispatchEvent(new u(this.format,this.text)))}}function m(e,t){const n=s.Color.Format;switch(t){case n.Original:return e.hasAlpha()?n.RGBA:n.RGB;case n.RGB:case n.RGBA:return e.hasAlpha()?n.HSLA:n.HSL;case n.HSL:case n.HSLA:return e.hasAlpha()?n.HWBA:n.HWB;case n.HWB:case n.HWBA:return e.nickname()?n.Nickname:e.detectHEXFormat();case n.ShortHEX:return n.HEX;case n.ShortHEXA:return n.HEXA;case n.HEXA:case n.HEX:return n.Original;case n.Nickname:return e.detectHEXFormat();default:return n.RGBA}}r.CustomElements.defineComponent("devtools-color-swatch",g)})),t.register("drtCZ",(function(t,n){e(t.exports,"default",(()=>s));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright (c) 2015 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  white-space: nowrap;\n}\n\n.color-swatch {\n  position: relative;\n  margin-left: 1px;\n  margin-right: 2px;\n  width: 10px;\n  height: 10px;\n  top: 1px;\n  display: inline-block;\n  user-select: none;\n  background-image: var(--image-file-checker);\n  line-height: 10px;\n}\n\n.color-swatch-inner {\n  width: 100%;\n  height: 100%;\n  display: inline-block;\n  border: 1px solid rgb(128 128 128 / 60%); /* stylelint-disable-line plugin/use_theme_colors */\n  /* See: crbug.com/1152736 for color variable migration. */\n  box-sizing: border-box;\n  cursor: pointer;\n}\n\n.color-swatch:not(.read-only) .color-swatch-inner:hover {\n  border: 1px solid rgb(64 64 64 / 80%); /* stylelint-disable-line plugin/use_theme_colors */\n  /* See: crbug.com/1152736 for color variable migration. */\n}\n\n@media (forced-colors: active) {\n  .color-swatch {\n    forced-color-adjust: none;\n  }\n}\n\n/*# sourceURL=colorSwatch.css */\n");var s=i})),t.register("kKMaw",(function(n,i){e(n.exports,"PopoverToggledEvent",(()=>m)),e(n.exports,"UnitChangedEvent",(()=>v)),e(n.exports,"CSSAngle",(()=>x));var s=t("kpUjp"),o=t("dS5IF"),r=t("foPOV"),a=t("9rj5e"),l=t("5LeVJ"),h=t("gDEBO"),d=t("1djgd");const{render:c,html:u}=o,p=o.Directives.styleMap,g=new Set(["color","background","background-color"]);class m extends Event{static eventName="popovertoggled";data;constructor(e){super(m.eventName,{}),this.data={open:e}}}class v extends Event{static eventName="unitchanged";data;constructor(e){super(v.eventName,{}),this.data={value:e}}}const f={value:0,unit:"rad"};class x extends HTMLElement{static litTagName=o.literal`devtools-css-angle`;shadow=this.attachShadow({mode:"open"});angle=f;displayedAngle=f;propertyName="";propertyValue="";containingPane;angleElement=null;swatchElement=null;popoverOpen=!1;popoverStyleTop="";popoverStyleLeft="";onMinifyingAction=this.minify.bind(this);onAngleUpdate=this.updateAngle.bind(this);connectedCallback(){this.shadow.adoptedStyleSheets=[r.default]}set data(e){const t=(0,a.parseText)(e.angleText);t&&(this.angle=t,this.displayedAngle={...t},this.propertyName=e.propertyName,this.propertyValue=e.propertyValue,this.containingPane=e.containingPane,this.render())}disconnectedCallback(){this.unbindMinifyingAction()}popover(){if(!this.containingPane)return;if(this.angleElement||(this.angleElement=this.shadow.querySelector(".css-angle")),this.swatchElement||(this.swatchElement=this.shadow.querySelector("devtools-css-angle-swatch")),!this.angleElement||!this.swatchElement)return;this.dispatchEvent(new m(!0)),this.bindMinifyingAction();const e=this.swatchElement.getBoundingClientRect().bottom,t=this.swatchElement.getBoundingClientRect().left;if(e&&t){const n=this.containingPane.getBoundingClientRect().top,i=this.containingPane.getBoundingClientRect().left;this.popoverStyleTop=e-n+"px",this.popoverStyleLeft=t-i+"px"}this.popoverOpen=!0,this.render(),this.angleElement.focus()}minify(){!1!==this.popoverOpen&&(this.popoverOpen=!1,this.dispatchEvent(new m(!1)),this.unbindMinifyingAction(),this.render())}updateProperty(e,t){this.propertyName=e,this.propertyValue=t,this.render()}updateAngle(e){this.displayedAngle=(0,a.roundAngleByUnit)((0,a.convertAngleUnit)(e,this.displayedAngle.unit)),this.angle=this.displayedAngle,this.dispatchEvent(new(0,l.ValueChangedEvent)(`${this.angle.value}${this.angle.unit}`))}displayNextUnit(){const e=(0,a.getNextUnit)(this.displayedAngle.unit);this.displayedAngle=(0,a.roundAngleByUnit)((0,a.convertAngleUnit)(this.angle,e)),this.dispatchEvent(new v(`${this.displayedAngle.value}${this.displayedAngle.unit}`))}bindMinifyingAction(){document.addEventListener("mousedown",this.onMinifyingAction),this.containingPane&&this.containingPane.addEventListener("scroll",this.onMinifyingAction)}unbindMinifyingAction(){document.removeEventListener("mousedown",this.onMinifyingAction),this.containingPane&&this.containingPane.removeEventListener("scroll",this.onMinifyingAction)}onMiniIconClick(e){e.stopPropagation(),!e.shiftKey||this.popoverOpen?this.popoverOpen?this.minify():this.popover():this.displayNextUnit()}consume(e){e.stopPropagation()}onKeydown(e){if(this.popoverOpen)switch(e.key){case"Escape":e.stopPropagation(),this.minify(),this.blur();break;case"ArrowUp":case"ArrowDown":{const t=(0,a.getNewAngleFromEvent)(this.angle,e);t&&this.updateAngle(t),e.preventDefault();break}}}render(){c(u`
      <div class="css-angle" @keydown=${this.onKeydown} tabindex="-1">
        <div class="preview">
          <${d.CSSAngleSwatch.litTagName}
            @click=${this.onMiniIconClick}
            @mousedown=${this.consume}
            @dblclick=${this.consume}
            .data=${{angle:this.angle}}>
          </${d.CSSAngleSwatch.litTagName}><slot></slot></div>
        ${this.popoverOpen?this.renderPopover():null}
      </div>
    `,this.shadow,{host:this})}renderPopover(){let e="";return g.has(this.propertyName)&&!this.propertyValue.match(/url\(.*\)/i)&&(e=this.propertyValue),u`
    <${h.CSSAngleEditor.litTagName}
      class="popover popover-css-angle"
      style=${p({top:this.popoverStyleTop,left:this.popoverStyleLeft})}
      .data=${{angle:this.angle,onAngleUpdate:this.onAngleUpdate,background:e}}
    ></${h.CSSAngleEditor.litTagName}>
    `}}s.CustomElements.defineComponent("devtools-css-angle",x)})),t.register("foPOV",(function(t,n){e(t.exports,"default",(()=>s));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.css-angle {\n  display: inline-block;\n  position: relative;\n  outline: none;\n}\n\ndevtools-css-angle-swatch {\n  display: inline-block;\n  margin-right: 2px;\n  user-select: none;\n}\n\ndevtools-css-angle-editor {\n  --override-dial-color: #a3a3a3;\n\n  position: fixed;\n  z-index: 2;\n}\n\n.preview {\n  display: inline-block;\n}\n\n/*# sourceURL=cssAngle.css */\n");var s=i})),t.register("9rj5e",(function(n,i){e(n.exports,"CSSAngleRegex",(()=>r)),e(n.exports,"parseText",(()=>a)),e(n.exports,"getAngleFromRadians",(()=>l)),e(n.exports,"getRadiansFromAngle",(()=>h)),e(n.exports,"get2DTranslationsForAngle",(()=>d)),e(n.exports,"roundAngleByUnit",(()=>c)),e(n.exports,"getNextUnit",(()=>u)),e(n.exports,"convertAngleUnit",(()=>p)),e(n.exports,"getNewAngleFromEvent",(()=>g));var s=t("lz7WY"),o=t("9z2ZV");const r=/(?<value>[+-]?\d*\.?\d+)(?<unit>deg|grad|rad|turn)/,a=e=>{const t=e.match(r);return t&&t.groups?{value:Number(t.groups.value),unit:t.groups.unit}:null},l=(e,t)=>{let n=e;switch(t){case"grad":n=o.Geometry.radiansToGradians(e);break;case"deg":n=o.Geometry.radiansToDegrees(e);break;case"turn":n=o.Geometry.radiansToTurns(e)}return{value:n,unit:t}},h=e=>{switch(e.unit){case"deg":return o.Geometry.degreesToRadians(e.value);case"grad":return o.Geometry.gradiansToRadians(e.value);case"turn":return o.Geometry.turnsToRadians(e.value)}return e.value},d=(e,t)=>{const n=h(e);return{translateX:Math.sin(n)*t,translateY:-Math.cos(n)*t}},c=e=>{let t=e.value;switch(e.unit){case"deg":case"grad":t=Math.round(e.value);break;case"rad":t=Math.round(1e4*e.value)/1e4;break;case"turn":t=Math.round(100*e.value)/100;break;default:s.assertNever(e.unit,`Unknown angle unit: ${e.unit}`)}return{value:t,unit:e.unit}},u=e=>{switch(e){case"deg":return"grad";case"grad":return"rad";case"rad":return"turn";default:return"deg"}},p=(e,t)=>{if(e.unit===t)return e;const n=h(e);return l(n,t)},g=(e,t)=>{const n=o.UIUtils.getValueModificationDirection(t);if(null===n)return;let i="Up"===n?Math.PI/180:-Math.PI/180;t.shiftKey&&(i*=10);const s=h(e);return l(s+i,e.unit)}})),t.register("5LeVJ",(function(t,n){e(t.exports,"ValueChangedEvent",(()=>i));class i extends Event{static eventName="valuechanged";data;constructor(e){super(i.eventName,{}),this.data={value:e}}}})),t.register("gDEBO",(function(n,i){e(n.exports,"CSSAngleEditor",(()=>u));var s=t("koSS8"),o=t("kpUjp"),r=t("dS5IF"),a=t("bB2iV"),l=t("9rj5e");const{render:h,html:d}=r,c=r.Directives.styleMap;class u extends HTMLElement{static litTagName=r.literal`devtools-css-angle-editor`;shadow=this.attachShadow({mode:"open"});angle={value:0,unit:"rad"};onAngleUpdate;background="";clockRadius=38.5;dialTemplates;mousemoveThrottler=new s.Throttler.Throttler(16.67);mousemoveListener=this.onMousemove.bind(this);connectedCallback(){this.shadow.adoptedStyleSheets=[a.default],o.SetCSSProperty.set(this,"--clock-dial-length","6px")}set data(e){this.angle=e.angle,this.onAngleUpdate=e.onAngleUpdate,this.background=e.background,this.render()}updateAngleFromMousePosition(e,t,n){const i=this.shadow.querySelector(".clock");if(!i||!this.onAngleUpdate)return;const{top:s,right:o,bottom:r,left:a}=i.getBoundingClientRect();this.clockRadius=(o-a)/2;const h=(a+o)/2,d=(r+s)/2,c=-Math.atan2(e-h,t-d)+Math.PI;if(n){const e=(0,l.getRadiansFromAngle)({value:15,unit:"deg"}),t=Math.round(c/e)*e;this.onAngleUpdate((0,l.getAngleFromRadians)(t,this.angle.unit))}else this.onAngleUpdate((0,l.getAngleFromRadians)(c,this.angle.unit))}onEditorMousedown(e){e.stopPropagation(),this.updateAngleFromMousePosition(e.pageX,e.pageY,e.shiftKey);const t=e.target instanceof Node&&e.target.ownerDocument,n=this.shadow.querySelector(".editor");t&&n&&(t.addEventListener("mousemove",this.mousemoveListener,{capture:!0}),n.classList.add("interacting"),t.addEventListener("mouseup",(()=>{t.removeEventListener("mousemove",this.mousemoveListener,{capture:!0}),n.classList.remove("interacting")}),{once:!0}))}onMousemove(e){1===e.buttons&&(e.preventDefault(),this.mousemoveThrottler.schedule((()=>(this.updateAngleFromMousePosition(e.pageX,e.pageY,e.shiftKey),Promise.resolve()))))}onEditorWheel(e){if(!this.onAngleUpdate)return;const t=(0,l.getNewAngleFromEvent)(this.angle,e);t&&this.onAngleUpdate(t),e.preventDefault()}render(){const e={background:this.background},{translateX:t,translateY:n}=(0,l.get2DTranslationsForAngle)(this.angle,this.clockRadius/2),i={transform:`translate(${t}px, ${n}px) rotate(${this.angle.value}${this.angle.unit})`};h(d`
      <div class="editor">
        <span class="pointer"></span>
        <div
          class="clock"
          style=${c(e)}
          @mousedown=${this.onEditorMousedown}
          @wheel=${this.onEditorWheel}>
          ${this.renderDials()}
          <div class="hand" style=${c(i)}></div>
          <span class="center"></span>
        </div>
      </div>
    `,this.shadow,{host:this})}renderDials(){return this.dialTemplates||(this.dialTemplates=[0,45,90,135,180,225,270,315].map((e=>{const t=this.clockRadius-6-3,{translateX:n,translateY:i}=(0,l.get2DTranslationsForAngle)({value:e,unit:"deg"},t);return d`<span class="dial" style=${c({transform:`translate(${n}px, ${i}px) rotate(${e}deg)`})}></span>`}))),this.dialTemplates}}o.CustomElements.defineComponent("devtools-css-angle-editor",u)})),t.register("bB2iV",(function(t,n){e(t.exports,"default",(()=>s));const i=new CSSStyleSheet;i.replaceSync('/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.editor.interacting::before {\n  content: "";\n  position: fixed;\n  inset: 0;\n}\n\n.clock,\n.pointer,\n.center,\n.hand,\n.dial {\n  position: absolute;\n}\n\n.clock {\n  top: 6px;\n  width: 6em;\n  height: 6em;\n  background-color: var(--color-background);\n  border: 0.5em solid var(--color-background-elevation-1);\n  border-radius: 9em;\n  box-shadow: var(--drop-shadow), inset 0 0 15px var(--box-shadow-outline-color);\n  transform: translateX(-3em);\n}\n\n.center,\n.hand {\n  box-shadow: 0 0 2px var(--box-shadow-outline-color);\n}\n\n.pointer {\n  margin: auto;\n  top: 0;\n  left: -0.4em;\n  right: 0;\n  width: 0;\n  height: 0;\n  border-style: solid;\n  border-width: 0 0.9em 0.9em;\n  border-color: transparent transparent var(--color-background-elevation-1) transparent;\n}\n\n.center,\n.hand,\n.dial {\n  margin: auto;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n}\n\n.center {\n  width: 0.7em;\n  height: 0.7em;\n  border-radius: 10px;\n}\n\n.dial {\n  width: 2px;\n  height: var(--clock-dial-length);\n  background-color: var(--override-dial-color);\n  border-radius: 1px;\n}\n\n.hand {\n  height: 50%;\n  width: 0.3em;\n  background: var(--legacy-accent-fg-color);\n}\n\n.hand::before {\n  content: "";\n  display: inline-block;\n  position: absolute;\n  top: -0.6em;\n  left: -0.35em;\n  width: 1em;\n  height: 1em;\n  border-radius: 50%;\n  cursor: pointer;\n  box-shadow: 0 0 5px var(--box-shadow-outline-color);\n}\n\n.hand::before,\n.center {\n  background-color: var(--legacy-accent-fg-color);\n}\n\n:host-context(.-theme-with-dark-background) .hand::before {\n  box-shadow: 0 0 5px hsl(0deg 0% 0% / 80%);\n}\n\n:host-context(.-theme-with-dark-background) .center,\n:host-context(.-theme-with-dark-background) .hand {\n  box-shadow: 0 0 2px hsl(0deg 0% 0% / 60%);\n}\n\n:host-context(.-theme-with-dark-background) .clock {\n  background-color: hsl(225deg 5% 27%);\n}\n\n/*# sourceURL=cssAngleEditor.css */\n');var s=i})),t.register("1djgd",(function(n,i){e(n.exports,"CSSAngleSwatch",(()=>c));var s=t("kpUjp"),o=t("dS5IF"),r=t("hyUma"),a=t("9rj5e");const{render:l,html:h}=o,d=o.Directives.styleMap;class c extends HTMLElement{static litTagName=o.literal`devtools-css-angle-swatch`;shadow=this.attachShadow({mode:"open"});angle={value:0,unit:"rad"};connectedCallback(){this.shadow.adoptedStyleSheets=[r.default]}set data(e){this.angle=e.angle,this.render()}render(){const{translateX:e,translateY:t}=(0,a.get2DTranslationsForAngle)(this.angle,2.75),n={transform:`translate(${e}px, ${t}px) rotate(${this.angle.value}${this.angle.unit})`};l(h`
      <div class="swatch">
        <span class="mini-hand" style=${d(n)}></span>
      </div>
    `,this.shadow,{host:this})}}s.CustomElements.defineComponent("devtools-css-angle-swatch",c)})),t.register("hyUma",(function(t,n){e(t.exports,"default",(()=>s));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.swatch {\n  position: relative;\n  display: inline-block;\n  margin-bottom: -2px;\n  width: 1em;\n  height: 1em;\n  border: 1px solid var(--legacy-selection-inactive-fg-color);\n  border-radius: 50%;\n  overflow: hidden;\n  cursor: pointer;\n  background-color: var(--color-background-elevation-1);\n}\n\n.mini-hand {\n  position: absolute;\n  margin: auto;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  height: 55%;\n  width: 2px;\n  background-color: var(--legacy-accent-fg-color);\n  border-radius: 5px;\n}\n\n/*# sourceURL=cssAngleSwatch.css */\n");var s=i})),t.register("conuI",(function(n,i){e(n.exports,"DraggingFinishedEvent",(()=>c)),e(n.exports,"CSSLength",(()=>p));var s=t("kpUjp"),o=t("dS5IF"),r=t("7XsmE"),a=t("lmBV6"),l=t("5LeVJ");const{render:h,html:d}=o;class c extends Event{static eventName="draggingfinished";constructor(){super(c.eventName,{})}}const u={value:0,unit:"px"};class p extends HTMLElement{static litTagName=o.literal`devtools-css-length`;shadow=this.attachShadow({mode:"open"});onDraggingValue=this.dragValue.bind(this);length=u;isEditingSlot=!1;isDraggingValue=!1;currentMouseClientX=0;set data(e){const t=(0,a.parseText)(e.lengthText);t&&(this.length=t,this.render())}connectedCallback(){this.shadow.adoptedStyleSheets=[r.default]}onUnitChange(e){this.length.unit=e.target.value,this.dispatchEvent(new(0,l.ValueChangedEvent)(`${this.length.value}${this.length.unit}`)),this.dispatchEvent(new c),this.render()}dragValue(e){e.preventDefault(),e.stopPropagation(),this.isDraggingValue=!0;let t=e.clientX-this.currentMouseClientX;this.currentMouseClientX=e.clientX,e.shiftKey&&(t*=10),e.altKey&&(t*=.1),this.length.value=this.length.value+t,this.dispatchEvent(new(0,l.ValueChangedEvent)(`${this.length.value}${this.length.unit}`)),this.render()}onValueMousedown(e){if(0!==e.button)return;this.currentMouseClientX=e.clientX;const t=e.target instanceof Node&&e.target.ownerDocument;t&&(t.addEventListener("mousemove",this.onDraggingValue,{capture:!0}),t.addEventListener("mouseup",(e=>{t.removeEventListener("mousemove",this.onDraggingValue,{capture:!0}),this.isDraggingValue&&(e.preventDefault(),e.stopPropagation(),this.isDraggingValue=!1,this.dispatchEvent(new c))}),{once:!0,capture:!0}))}onValueMouseup(){this.isDraggingValue||(this.isEditingSlot=!0,this.render())}onUnitMouseup(e){e.preventDefault(),e.stopPropagation()}render(){h(d`
      <div class="css-length">
        ${this.renderContent()}
      </div>
    `,this.shadow,{host:this})}renderContent(){if(this.isEditingSlot)return d`<slot></slot>`;const e=a.LENGTH_UNITS.map((e=>d`
          <option value=${e} .selected=${this.length.unit===e}>${e}</option>
        `));return d`
        <span class="value"
          @mousedown=${this.onValueMousedown}
          @mouseup=${this.onValueMouseup}
        >${this.length.value}</span><span class="unit">${this.length.unit}</span><div class="unit-dropdown">
          <span class="icon"></span>
          <select @mouseup=${this.onUnitMouseup} @change=${this.onUnitChange}>
            ${e}
          </select>
        </div>
      `}}s.CustomElements.defineComponent("devtools-css-length",p)})),t.register("7XsmE",(function(t,n){e(t.exports,"default",(()=>s));const i=new CSSStyleSheet;i.replaceSync('/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.css-length {\n  display: inline-block;\n  position: relative;\n  outline: none;\n}\n\n.value {\n  cursor: ew-resize;\n}\n\n.unit-dropdown {\n  display: none;\n}\n\n.unit-dropdown select {\n  all: unset;\n  cursor: pointer;\n  opacity: 0%;\n  width: 0.8em;\n  background-color: var(--color-background);\n}\n\n.icon {\n  position: absolute;\n  display: inline-block;\n  -webkit-mask-image: var(--image-file-chevrons);\n  -webkit-mask-position: -12px 4px;\n  -webkit-mask-size: 19px 6px;\n  -webkit-mask-repeat: no-repeat;\n  background-color: var(--color-text-primary);\n  content: "";\n  height: 1em;\n  width: 1em;\n}\n\n:host(:not(:last-child)) {\n  margin-right: 0.1em;\n}\n\n:host(:not(:last-child)) .unit-dropdown {\n  position: absolute;\n}\n\n.css-length:hover .unit-dropdown {\n  display: inline-block;\n}\n\n:host(:last-child) .unit-dropdown select {\n  width: 0.6em;\n}\n\n/*# sourceURL=cssLength.css */\n');var s=i})),t.register("lmBV6",(function(t,n){e(t.exports,"LENGTH_UNITS",(()=>i)),e(t.exports,"CSSLengthRegex",(()=>s)),e(t.exports,"parseText",(()=>o));const i=["px","cm","mm","in","pc","pt","ch","em","rem","vh","vw","vmin","vmax"],s=new RegExp(`(?<value>[+-]?\\d*\\.?\\d+)(?<unit>${i.join("|")})`),o=e=>{const t=e.match(s);return t&&t.groups?{value:Number(t.groups.value),unit:t.groups.unit}:null}})),t.register("95or5",(function(n,i){e(n.exports,"CSSShadowEditor",(()=>m)),e(n.exports,"Events",(()=>v));var s=t("koSS8"),o=t("ixFnt"),r=t("lz7WY"),a=t("9z2ZV"),l=t("3ep6O"),h=t("1yF8h");const d={type:"Type",xOffset:"X offset",yOffset:"Y offset",blur:"Blur",spread:"Spread"},c=o.i18n.registerUIStrings("ui/legacy/components/inline_editor/CSSShadowEditor.ts",d),u=o.i18n.getLocalizedString.bind(void 0,c),p=20,g="px";class m extends(s.ObjectWrapper.eventMixin(a.Widget.VBox)){typeField;outsetButton;insetButton;xInput;yInput;xySlider;halfCanvasSize;innerCanvasSize;blurInput;blurSlider;spreadField;spreadInput;spreadSlider;model;canvasOrigin;changedElement;constructor(){super(!0),this.contentElement.tabIndex=0,this.setDefaultFocusedElement(this.contentElement),this.typeField=this.contentElement.createChild("div","shadow-editor-field shadow-editor-flex-field"),this.typeField.createChild("label","shadow-editor-label").textContent=u(d.type),this.outsetButton=this.typeField.createChild("button","shadow-editor-button-left"),this.outsetButton.textContent=o.i18n.lockedString("Outset"),this.outsetButton.addEventListener("click",this.onButtonClick.bind(this),!1),this.insetButton=this.typeField.createChild("button","shadow-editor-button-right"),this.insetButton.textContent=o.i18n.lockedString("Inset"),this.insetButton.addEventListener("click",this.onButtonClick.bind(this),!1);const e=this.contentElement.createChild("div","shadow-editor-field");this.xInput=this.createTextInput(e,u(d.xOffset));const t=this.contentElement.createChild("div","shadow-editor-field");this.yInput=this.createTextInput(t,u(d.yOffset)),this.xySlider=e.createChild("canvas","shadow-editor-2D-slider"),this.xySlider.width=88,this.xySlider.height=88,this.xySlider.tabIndex=-1,this.halfCanvasSize=44,this.innerCanvasSize=this.halfCanvasSize-6,a.UIUtils.installDragHandle(this.xySlider,this.dragStart.bind(this),this.dragMove.bind(this),null,"default"),this.xySlider.addEventListener("keydown",this.onCanvasArrowKey.bind(this),!1),this.xySlider.addEventListener("blur",this.onCanvasBlur.bind(this),!1);const n=this.contentElement.createChild("div","shadow-editor-field shadow-editor-flex-field shadow-editor-blur-field");this.blurInput=this.createTextInput(n,u(d.blur)),this.blurSlider=this.createSlider(n),this.spreadField=this.contentElement.createChild("div","shadow-editor-field shadow-editor-flex-field"),this.spreadInput=this.createTextInput(this.spreadField,u(d.spread)),this.spreadSlider=this.createSlider(this.spreadField)}createTextInput(e,t){const n=e.createChild("label","shadow-editor-label");n.textContent=t,n.setAttribute("for",t);const i=a.UIUtils.createInput("shadow-editor-text-input","text");return e.appendChild(i),i.id=t,i.addEventListener("keydown",this.handleValueModification.bind(this),!1),i.addEventListener("wheel",this.handleValueModification.bind(this),!1),i.addEventListener("input",this.onTextInput.bind(this),!1),i.addEventListener("blur",this.onTextBlur.bind(this),!1),i}createSlider(e){const t=a.UIUtils.createSlider(0,p,-1);return t.addEventListener("input",this.onSliderInput.bind(this),!1),e.appendChild(t),t}wasShown(){this.registerCSSFiles([h.default]),this.updateUI()}setModel(e){this.model=e,this.typeField.classList.toggle("hidden",!e.isBoxShadow()),this.spreadField.classList.toggle("hidden",!e.isBoxShadow()),this.updateUI()}updateUI(){this.updateButtons(),this.xInput.value=this.model.offsetX().asCSSText(),this.yInput.value=this.model.offsetY().asCSSText(),this.blurInput.value=this.model.blurRadius().asCSSText(),this.spreadInput.value=this.model.spreadRadius().asCSSText(),this.blurSlider.value=this.model.blurRadius().amount.toString(),this.spreadSlider.value=this.model.spreadRadius().amount.toString(),this.updateCanvas(!1)}updateButtons(){this.insetButton.classList.toggle("enabled",this.model.inset()),this.outsetButton.classList.toggle("enabled",!this.model.inset())}updateCanvas(e){const t=this.xySlider.getContext("2d");if(!t)throw new Error("Unable to obtain canvas context");t.clearRect(0,0,this.xySlider.width,this.xySlider.height),t.save(),t.setLineDash([1,1]),t.strokeStyle="rgba(210, 210, 210, 0.8)",t.beginPath(),t.moveTo(this.halfCanvasSize,0),t.lineTo(this.halfCanvasSize,88),t.moveTo(0,this.halfCanvasSize),t.lineTo(88,this.halfCanvasSize),t.stroke(),t.restore();const n=this.sliderThumbPosition();t.save(),t.translate(this.halfCanvasSize,this.halfCanvasSize),t.lineWidth=2,t.strokeStyle="rgba(130, 130, 130, 0.75)",t.beginPath(),t.moveTo(0,0),t.lineTo(n.x,n.y),t.stroke(),e&&(t.beginPath(),t.fillStyle="rgba(66, 133, 244, 0.4)",t.arc(n.x,n.y,8,0,2*Math.PI),t.fill()),t.beginPath(),t.fillStyle="#4285F4",t.arc(n.x,n.y,6,0,2*Math.PI),t.fill(),t.restore()}onButtonClick(e){const t=e.currentTarget===this.insetButton;t&&this.model.inset()||!t&&!this.model.inset()||(this.model.setInset(t),this.updateButtons(),this.dispatchEventToListeners(v.ShadowChanged,this.model))}handleValueModification(e){const t=e.currentTarget,n=a.UIUtils.createReplacementString(t.value,e,(function(e,t,n){n.length||(n=g);return e+t+n}));if(!n)return;const i=l.CSSLength.parse(n);i&&(e.currentTarget===this.blurInput&&i.amount<0&&(i.amount=0),t.value=i.asCSSText(),t.selectionStart=0,t.selectionEnd=t.value.length,this.onTextInput(e),e.consume(!0))}onTextInput(e){const t=e.currentTarget;this.changedElement=t,this.changedElement.classList.remove("invalid");const n=l.CSSLength.parse(t.value);!n||t===this.blurInput&&n.amount<0||(t===this.xInput?(this.model.setOffsetX(n),this.updateCanvas(!1)):t===this.yInput?(this.model.setOffsetY(n),this.updateCanvas(!1)):t===this.blurInput?(this.model.setBlurRadius(n),this.blurSlider.value=n.amount.toString()):t===this.spreadInput&&(this.model.setSpreadRadius(n),this.spreadSlider.value=n.amount.toString()),this.dispatchEventToListeners(v.ShadowChanged,this.model))}onTextBlur(){if(!this.changedElement)return;let e=this.changedElement.value.trim()?l.CSSLength.parse(this.changedElement.value):l.CSSLength.zero();if(e||(e=l.CSSLength.parse(this.changedElement.value+g)),!e)return this.changedElement.classList.add("invalid"),void(this.changedElement=null);this.changedElement===this.xInput?(this.model.setOffsetX(e),this.xInput.value=e.asCSSText(),this.updateCanvas(!1)):this.changedElement===this.yInput?(this.model.setOffsetY(e),this.yInput.value=e.asCSSText(),this.updateCanvas(!1)):this.changedElement===this.blurInput?(e.amount<0&&(e=l.CSSLength.zero()),this.model.setBlurRadius(e),this.blurInput.value=e.asCSSText(),this.blurSlider.value=e.amount.toString()):this.changedElement===this.spreadInput&&(this.model.setSpreadRadius(e),this.spreadInput.value=e.asCSSText(),this.spreadSlider.value=e.amount.toString()),this.changedElement=null,this.dispatchEventToListeners(v.ShadowChanged,this.model)}onSliderInput(e){e.currentTarget===this.blurSlider?(this.model.setBlurRadius(new(0,l.CSSLength)(Number(this.blurSlider.value),this.model.blurRadius().unit||g)),this.blurInput.value=this.model.blurRadius().asCSSText(),this.blurInput.classList.remove("invalid")):e.currentTarget===this.spreadSlider&&(this.model.setSpreadRadius(new(0,l.CSSLength)(Number(this.spreadSlider.value),this.model.spreadRadius().unit||g)),this.spreadInput.value=this.model.spreadRadius().asCSSText(),this.spreadInput.classList.remove("invalid")),this.dispatchEventToListeners(v.ShadowChanged,this.model)}dragStart(e){this.xySlider.focus(),this.updateCanvas(!0),this.canvasOrigin=new a.Geometry.Point(this.xySlider.totalOffsetLeft()+this.halfCanvasSize,this.xySlider.totalOffsetTop()+this.halfCanvasSize);const t=new a.Geometry.Point(e.x-this.canvasOrigin.x,e.y-this.canvasOrigin.y),n=this.sliderThumbPosition();return t.distanceTo(n)>=6&&this.dragMove(e),!0}dragMove(e){let t=new a.Geometry.Point(e.x-this.canvasOrigin.x,e.y-this.canvasOrigin.y);e.shiftKey&&(t=this.snapToClosestDirection(t));const n=this.constrainPoint(t,this.innerCanvasSize),i=Math.round(n.x/this.innerCanvasSize*p),s=Math.round(n.y/this.innerCanvasSize*p);e.shiftKey?(this.model.setOffsetX(new(0,l.CSSLength)(i,this.model.offsetX().unit||g)),this.model.setOffsetY(new(0,l.CSSLength)(s,this.model.offsetY().unit||g))):(e.altKey||this.model.setOffsetX(new(0,l.CSSLength)(i,this.model.offsetX().unit||g)),a.KeyboardShortcut.KeyboardShortcut.eventHasCtrlEquivalentKey(e)||this.model.setOffsetY(new(0,l.CSSLength)(s,this.model.offsetY().unit||g))),this.xInput.value=this.model.offsetX().asCSSText(),this.yInput.value=this.model.offsetY().asCSSText(),this.xInput.classList.remove("invalid"),this.yInput.classList.remove("invalid"),this.updateCanvas(!0),this.dispatchEventToListeners(v.ShadowChanged,this.model)}onCanvasBlur(){this.updateCanvas(!1)}onCanvasArrowKey(e){const t=e;let n=0,i=0;if("ArrowRight"===t.key?n=1:"ArrowLeft"===t.key?n=-1:"ArrowUp"===t.key?i=-1:"ArrowDown"===t.key&&(i=1),n||i){if(e.consume(!0),n){const e=this.model.offsetX(),t=r.NumberUtilities.clamp(e.amount+n,-20,p);if(t===e.amount)return;this.model.setOffsetX(new(0,l.CSSLength)(t,e.unit||g)),this.xInput.value=this.model.offsetX().asCSSText(),this.xInput.classList.remove("invalid")}if(i){const e=this.model.offsetY(),t=r.NumberUtilities.clamp(e.amount+i,-20,p);if(t===e.amount)return;this.model.setOffsetY(new(0,l.CSSLength)(t,e.unit||g)),this.yInput.value=this.model.offsetY().asCSSText(),this.yInput.classList.remove("invalid")}this.updateCanvas(!0),this.dispatchEventToListeners(v.ShadowChanged,this.model)}}constrainPoint(e,t){return Math.abs(e.x)<=t&&Math.abs(e.y)<=t?new a.Geometry.Point(e.x,e.y):e.scale(t/Math.max(Math.abs(e.x),Math.abs(e.y)))}snapToClosestDirection(e){let t=Number.MAX_VALUE,n=e;const i=[new a.Geometry.Point(0,-1),new a.Geometry.Point(1,-1),new a.Geometry.Point(1,0),new a.Geometry.Point(1,1)];for(const s of i){const i=e.projectOn(s),o=e.distanceTo(i);o<t&&(t=o,n=i)}return n}sliderThumbPosition(){const e=this.model.offsetX().amount/p*this.innerCanvasSize,t=this.model.offsetY().amount/p*this.innerCanvasSize;return this.constrainPoint(new a.Geometry.Point(e,t),this.innerCanvasSize)}}var v;(v||(v={})).ShadowChanged="ShadowChanged"})),t.register("3ep6O",(function(n,i){e(n.exports,"CSSShadowModel",(()=>r)),e(n.exports,"CSSLength",(()=>a));var s=t("koSS8"),o=t("7f6zc");class r{isBoxShadowInternal;insetInternal;offsetXInternal;offsetYInternal;blurRadiusInternal;spreadRadiusInternal;colorInternal;format;important;constructor(e){this.isBoxShadowInternal=e,this.insetInternal=!1,this.offsetXInternal=a.zero(),this.offsetYInternal=a.zero(),this.blurRadiusInternal=a.zero(),this.spreadRadiusInternal=a.zero(),this.colorInternal=s.Color.Color.parse("black"),this.format=["X","Y"],this.important=!1}static parseTextShadow(e){return r.parseShadow(e,!1)}static parseBoxShadow(e){return r.parseShadow(e,!0)}static parseShadow(e,t){const n=[],i=o.TextUtils.Utils.splitStringByRegexes(e,[s.Color.Regex,/,/g]);let l=0;for(let t=0;t<i.length;t++)if(1===i[t].regexIndex){const s=i[t];n.push(e.substring(l,s.position)),l=s.position+1}n.push(e.substring(l,e.length));const h=[];for(let e=0;e<n.length;e++){const i=new r(t);i.format=[];let l=!0;const c=[/!important/gi,/inset/gi,s.Color.Regex,a.Regex],u=o.TextUtils.Utils.splitStringByRegexes(n[e],c);for(let e=0;e<u.length;e++){const t=u[e];if(-1===t.regexIndex){if(/\S/.test(t.value))return[];l=!0}else{if(!l)return[];if(l=!1,0===t.regexIndex)i.important=!0,i.format.push("M");else if(1===t.regexIndex)i.insetInternal=!0,i.format.push("I");else if(2===t.regexIndex){const e=s.Color.Color.parse(t.value);if(!e)return[];i.colorInternal=e,i.format.push("C")}else if(3===t.regexIndex){const e=a.parse(t.value);if(!e)return[];const n=i.format.length>0?i.format[i.format.length-1]:"";"X"===n?(i.offsetYInternal=e,i.format.push("Y")):"Y"===n?(i.blurRadiusInternal=e,i.format.push("B")):"B"===n?(i.spreadRadiusInternal=e,i.format.push("S")):(i.offsetXInternal=e,i.format.push("X"))}}}if(d(i,"X",1,1)||d(i,"Y",1,1)||d(i,"C",0,1)||d(i,"B",0,1)||d(i,"I",0,t?1:0)||d(i,"S",0,t?1:0)||d(i,"M",0,1))return[];h.push(i)}return h;function d(e,t,n,i){let s=0;for(let n=0;n<e.format.length;n++)e.format[n]===t&&s++;return s<n||s>i}}setInset(e){this.insetInternal=e,-1===this.format.indexOf("I")&&this.format.unshift("I")}setOffsetX(e){this.offsetXInternal=e}setOffsetY(e){this.offsetYInternal=e}setBlurRadius(e){if(this.blurRadiusInternal=e,-1===this.format.indexOf("B")){const e=this.format.indexOf("Y");this.format.splice(e+1,0,"B")}}setSpreadRadius(e){if(this.spreadRadiusInternal=e,-1===this.format.indexOf("S")){this.setBlurRadius(this.blurRadiusInternal);const e=this.format.indexOf("B");this.format.splice(e+1,0,"S")}}setColor(e){this.colorInternal=e,-1===this.format.indexOf("C")&&this.format.push("C")}isBoxShadow(){return this.isBoxShadowInternal}inset(){return this.insetInternal}offsetX(){return this.offsetXInternal}offsetY(){return this.offsetYInternal}blurRadius(){return this.blurRadiusInternal}spreadRadius(){return this.spreadRadiusInternal}color(){return this.colorInternal}asCSSText(){const e=[];for(let t=0;t<this.format.length;t++){const n=this.format[t];"I"===n&&this.insetInternal?e.push("inset"):"X"===n?e.push(this.offsetXInternal.asCSSText()):"Y"===n?e.push(this.offsetYInternal.asCSSText()):"B"===n?e.push(this.blurRadiusInternal.asCSSText()):"S"===n?e.push(this.spreadRadiusInternal.asCSSText()):"C"===n?e.push(this.colorInternal.asString(this.colorInternal.format())):"M"===n&&this.important&&e.push("!important")}return e.join(" ")}}class a{amount;unit;constructor(e,t){this.amount=e,this.unit=t}static parse(e){const t=new RegExp("^(?:"+a.Regex.source+")$","i"),n=e.match(t);return n?n.length>2&&n[2]?new a(parseFloat(n[1]),n[2]):a.zero():null}static zero(){return new a(0,"")}asCSSText(){return this.amount+this.unit}static Regex=function(){return new RegExp("([+-]?(?:[0-9]*[.])?[0-9]+(?:[eE][+-]?[0-9]+)?)(ch|cm|em|ex|in|mm|pc|pt|px|rem|vh|vmax|vmin|vw)|[+-]?(?:0*[.])?0+(?:[eE][+-]?[0-9]+)?","gi")}()}})),t.register("1yF8h",(function(t,n){e(t.exports,"default",(()=>s));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2016 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  user-select: none;\n  padding: 4px 12px 12px;\n  border: 1px solid transparent;\n}\n\n.shadow-editor-field {\n  height: 24px;\n  margin-top: 8px;\n  font-size: 12px;\n  flex-shrink: 0;\n}\n\n.shadow-editor-field:last-of-type {\n  margin-bottom: 8px;\n}\n\n.shadow-editor-flex-field {\n  display: flex;\n  align-items: center;\n  flex-direction: row;\n}\n\n.shadow-editor-field.shadow-editor-blur-field {\n  margin-top: 40px;\n}\n\n.shadow-editor-2D-slider {\n  position: absolute;\n  height: 88px;\n  width: 88px;\n  border: 1px solid var(--divider-line);\n  border-radius: 2px;\n}\n\n.shadow-editor-label {\n  display: inline-block;\n  width: 52px;\n  height: 24px;\n  line-height: 24px;\n  margin-right: 8px;\n  text-align: right;\n}\n\n.shadow-editor-button-left,\n.shadow-editor-button-right {\n  width: 74px;\n  height: 24px;\n  padding: 3px 7px;\n  line-height: 16px;\n  border: 1px solid var(--divider-line);\n  color: var(--color-text-primary);\n  background-color: var(--color-background);\n  text-align: center;\n  font-weight: 500;\n}\n\n.shadow-editor-button-left {\n  border-radius: 2px 0 0 2px;\n}\n\n.shadow-editor-button-right {\n  border-radius: 0 2px 2px 0;\n  border-left-width: 0;\n}\n\n.shadow-editor-button-left:hover,\n.shadow-editor-button-right:hover {\n  box-shadow: 0 1px 1px var(--color-background-elevation-1);\n}\n\n.shadow-editor-button-left:focus,\n.shadow-editor-button-right:focus {\n  background-color: var(--color-background-elevation-1);\n}\n\n.shadow-editor-button-left.enabled,\n.shadow-editor-button-right.enabled {\n  --override-button-text-color: #fff;\n\n  background-color: var(--color-primary);\n  color: var(--override-button-text-color);\n}\n\n.shadow-editor-button-left.enabled:focus,\n.shadow-editor-button-right.enabled:focus {\n  background-color: var(--color-primary-variant);\n}\n\n.shadow-editor-text-input {\n  width: 52px;\n  margin-right: 8px;\n  text-align: right;\n  box-shadow: var(--legacy-focus-ring-inactive-shadow);\n}\n\n@media (forced-colors: active) {\n  .shadow-editor-button-left:hover,\n  .shadow-editor-button-left.enabled:focus,\n  .shadow-editor-button-right:hover .shadow-editor-button-left.enabled,\n  .shadow-editor-button-right.enabled,\n  .shadow-editor-button-right.enabled:focus {\n    forced-color-adjust: none;\n    background-color: Highlight;\n    color: HighlightText;\n  }\n}\n\n/*# sourceURL=cssShadowEditor.css */\n");var s=i})),t.register("3jpYY",(function(n,i){e(n.exports,"CSSVarSwatch",(()=>m));var s=t("ixFnt"),o=t("kpUjp"),r=t("dS5IF"),a=t("503sa");const l={sIsNotDefined:"{PH1} is not defined"},h=s.i18n.registerUIStrings("ui/legacy/components/inline_editor/CSSVarSwatch.ts",l),d=s.i18n.getLocalizedString.bind(void 0,h),{render:c,html:u,Directives:p}=r,g=/(var\()(\s*--[^,)]+)(.*)/;class m extends HTMLElement{static litTagName=r.literal`devtools-css-var-swatch`;shadow=this.attachShadow({mode:"open"});text="";computedValue=null;fromFallback=!1;onLinkActivate=()=>{};constructor(){super(),this.tabIndex=-1,this.addEventListener("focus",(()=>{const e=this.shadow.querySelector('[role="link"]');e&&e.focus()}))}connectedCallback(){this.shadow.adoptedStyleSheets=[a.default]}set data(e){this.text=e.text,this.computedValue=e.computedValue,this.fromFallback=e.fromFallback,this.onLinkActivate=(t,n)=>{n instanceof MouseEvent&&0!==n.button||n instanceof KeyboardEvent&&!isEnterOrSpaceKey(n)||(e.onLinkActivate(t),n.consume(!0))},this.render()}parseVariableFunctionParts(){const e=this.text.replace(/\s{2,}/g," ").match(g);return e?{pre:e[1],name:e[2],post:e[3]}:null}get variableName(){const e=this.text.match(/--[^,)]+/);return e?e[0]:""}renderLink(e){const t=this.computedValue&&!this.fromFallback,n=p.classMap({"css-var-link":!0,undefined:!t}),i=t?this.computedValue:d(l.sIsNotDefined,{PH1:e}),s=t?this.onLinkActivate.bind(this,this.variableName.trim()):null;return u`<span class=${n} title=${i} @mousedown=${s} @keydown=${s} role="link" tabindex="-1">${e}</span>`}render(){const e=this.parseVariableFunctionParts();if(!e)return void c("",this.shadow,{host:this});const t=this.renderLink(e.name);c(u`<span title=${this.computedValue||""}>${e.pre}${t}${e.post}</span>`,this.shadow,{host:this})}}o.CustomElements.defineComponent("devtools-css-var-swatch",m)})),t.register("503sa",(function(t,n){e(t.exports,"default",(()=>s));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.css-var-link:not(.undefined) {\n  cursor: pointer;\n  text-underline-offset: 2px;\n  color: var(--color-link);\n}\n\n.css-var-link:hover:not(.undefined) {\n  text-decoration: underline;\n}\n\n.css-var-link:focus:not(:focus-visible) {\n  outline: none;\n}\n\n.css-var-link.undefined {\n  --override-css-var-link-undefined-color: rgb(102 102 102);\n\n  color: var(--override-css-var-link-undefined-color);\n}\n\n/*# sourceURL=cssVarSwatch.css */\n");var s=i})),t.register("7uOxM",(function(n,i){e(n.exports,"FontEditor",(()=>m)),e(n.exports,"Events",(()=>v));var s=t("koSS8"),o=t("ixFnt"),r=t("eQFvP"),a=t("cY3yZ"),l=t("9z2ZV"),h=t("hucFQ"),d=t("gdQYz"),c=t("5nbJO");const u={fontFamily:"Font Family",cssProperties:"CSS Properties",fontSize:"Font Size",lineHeight:"Line Height",fontWeight:"Font Weight",spacing:"Spacing",fallbackS:"Fallback {PH1}",thereIsNoValueToDeleteAtIndexS:"There is no value to delete at index: {PH1}",fontSelectorDeletedAtIndexS:"Font Selector deleted at index: {PH1}",deleteS:"Delete {PH1}",PleaseEnterAValidValueForSText:"* Please enter a valid value for {PH1} text input",thisPropertyIsSetToContainUnits:"This property is set to contain units but does not have a defined corresponding unitsArray: {PH1}",sSliderInput:"{PH1} Slider Input",sTextInput:"{PH1} Text Input",units:"Units",sUnitInput:"{PH1} Unit Input",sKeyValueSelector:"{PH1} Key Value Selector",sToggleInputType:"{PH1} toggle input type",selectorInputMode:"Selector Input Mode",sliderInputMode:"Slider Input Mode"},p=o.i18n.registerUIStrings("ui/legacy/components/inline_editor/FontEditor.ts",u),g=o.i18n.getLocalizedString.bind(void 0,p);class m extends(s.ObjectWrapper.eventMixin(l.Widget.VBox)){selectedNode;propertyMap;fontSelectorSection;fontSelectors;fontsList;constructor(e){super(!0),this.selectedNode=l.Context.Context.instance().flavor(r.DOMModel.DOMNode),this.propertyMap=e,this.contentElement.tabIndex=0,this.setDefaultFocusedElement(this.contentElement),this.fontSelectorSection=this.contentElement.createChild("div","font-selector-section"),this.fontSelectorSection.createChild("h2","font-section-header").textContent=g(u.fontFamily),this.fontSelectors=[],this.fontsList=null;const t=this.propertyMap.get("font-family");this.createFontSelectorSection(t);const n=this.contentElement.createChild("div","font-section");n.createChild("h2","font-section-header").textContent=g(u.cssProperties);const i=this.getPropertyInfo("font-size",c.FontSizeStaticParams.regex),s=this.getPropertyInfo("line-height",c.LineHeightStaticParams.regex),o=this.getPropertyInfo("font-weight",c.FontWeightStaticParams.regex),a=this.getPropertyInfo("letter-spacing",c.LetterSpacingStaticParams.regex);new x("font-size",g(u.fontSize),n,i,c.FontSizeStaticParams,this.updatePropertyValue.bind(this),this.resizePopout.bind(this),!0),new x("line-height",g(u.lineHeight),n,s,c.LineHeightStaticParams,this.updatePropertyValue.bind(this),this.resizePopout.bind(this),!0),new x("font-weight",g(u.fontWeight),n,o,c.FontWeightStaticParams,this.updatePropertyValue.bind(this),this.resizePopout.bind(this),!1),new x("letter-spacing",g(u.spacing),n,a,c.LetterSpacingStaticParams,this.updatePropertyValue.bind(this),this.resizePopout.bind(this),!0)}wasShown(){this.registerCSSFiles([h.default])}async createFontSelectorSection(e){if(e){const t=e.split(",");if(await this.createFontSelector(t[0],!0),!c.GlobalValues.includes(t[0]))for(let e=1;e<t.length+1;e++)this.createFontSelector(t[e])}else this.createFontSelector("",!0);this.resizePopout()}async createFontsList(){const e=await c.generateComputedFontArray(),t=new Map,n=this.splitComputedFontArray(e);t.set("Computed Fonts",n);const i=new Map;i.set("System Fonts",c.SystemFonts),i.set("Generic Families",c.GenericFonts);const s=[];return s.push(t),s.push(i),s}splitComputedFontArray(e){const t=[];for(const n of e)if(n.includes(",")){n.split(",").forEach((e=>{-1===t.findIndex((t=>t.toLowerCase()===e.trim().toLowerCase().replace(/"/g,"'")))&&t.push(e.trim().replace(/"/g,""))}))}else-1===t.findIndex((e=>e.toLowerCase()===n.toLowerCase().replace('"',"'")))&&t.push(n.replace(/"/g,""));return t}async createFontSelector(e,t){if(e=e?e.trim():""){const t=e.charAt(0);"'"===t?e=e.replace(/'/g,""):'"'===t&&(e=e.replace(/"/g,""))}const n=this.fontSelectorSection.createChild("div","shadow-editor-field shadow-editor-flex-field");let i;if(this.fontsList||(this.fontsList=await this.createFontsList()),t){i=g(u.fontFamily);const t=new Map([["Global Values",c.GlobalValues]]),s=[...this.fontsList];s.push(t),this.createSelector(n,i,s,e.trim())}else i=g(u.fallbackS,{PH1:this.fontSelectors.length}),this.createSelector(n,i,this.fontsList,e.trim())}deleteFontSelector(e,t){let n=this.fontSelectors[e];const i=0===e;if(""===n.input.value&&!t)return void l.ARIAUtils.alert(g(u.thereIsNoValueToDeleteAtIndexS,{PH1:e}));if(i){const t=this.fontSelectors[1];let i="";t&&(i=t.input.value,n=t);this.fontSelectors[0].input.value=i,e=1}if(n.input.parentNode){const t=this.fontSelectors.length>1;if(!i||t){const t=n.input.parentElement;t&&(t.remove(),this.fontSelectors.splice(e,1),this.updateFontSelectorList())}l.ARIAUtils.alert(g(u.fontSelectorDeletedAtIndexS,{PH1:e}))}this.onFontSelectorChanged(),this.resizePopout();const s=i?0:e-1;this.fontSelectors[s].input.focus()}updateFontSelectorList(){for(let e=0;e<this.fontSelectors.length;e++){const t=this.fontSelectors[e];let n;n=0===e?g(u.fontFamily):g(u.fallbackS,{PH1:e}),t.label.textContent=n,l.ARIAUtils.setAccessibleName(t.input,n),t.deleteButton.setTitle(g(u.deleteS,{PH1:n})),t.index=e}}getPropertyInfo(e,t){const n=this.propertyMap.get(e);if(n){const e=n,i=e.match(t);if(i){return{value:"+"===i[1].charAt(0)?i[1].substr(1):i[1],units:i[2]?i[2]:""}}return{value:e,units:null}}return{value:null,units:null}}createSelector(e,t,n,i){const s=this.fontSelectors.length,o=l.UIUtils.createSelect(t,n);o.value=i;const r=l.UIUtils.createLabel(t,"shadow-editor-label",o);o.addEventListener("input",this.onFontSelectorChanged.bind(this),!1),o.addEventListener("keydown",(e=>{"Enter"===e.key&&e.consume()}),!1),e.appendChild(r),e.appendChild(o);const a=new l.Toolbar.Toolbar("",e),h=new l.Toolbar.ToolbarButton(g(u.deleteS,{PH1:t}),"largeicon-trash-bin");a.appendToolbarItem(h);const d={label:r,input:o,deleteButton:h,index:s};h.addEventListener(l.Toolbar.ToolbarButton.Events.Click,(()=>{this.deleteFontSelector(d.index)})),h.element.addEventListener("keydown",(e=>{isEnterOrSpaceKey(e)&&(this.deleteFontSelector(d.index),e.consume())}),!1),this.fontSelectors.push(d)}onFontSelectorChanged(){let e="";const t=c.GlobalValues.includes(this.fontSelectors[0].input.value);if(t)for(let e=1;e<this.fontSelectors.length;e++)this.deleteFontSelector(e,!0);for(const t of this.fontSelectors){const n=t.input;""!==n.value&&(""===e?e=this.fontSelectors[0].input.value:e+=", "+n.value)}""!==this.fontSelectors[this.fontSelectors.length-1].input.value&&!t&&this.fontSelectors.length<10&&(this.createFontSelector(""),this.resizePopout()),this.updatePropertyValue("font-family",e)}updatePropertyValue(e,t){this.dispatchEventToListeners(v.FontChanged,{propertyName:e,value:t})}resizePopout(){this.dispatchEventToListeners(v.FontEditorResized)}}var v,f;(f=v||(v={})).FontChanged="FontChanged",f.FontEditorResized="FontEditorResized";class x{showSliderMode;errorText;propertyInfo;propertyName;staticParams;hasUnits;units;addedUnit;initialRange;boundUpdateCallback;boundResizeCallback;selectedNode;sliderInput;textBoxInput;unitInput;selectorInput;applyNextInput;constructor(e,t,n,i,s,o,a,h){this.showSliderMode=!0;const d=n.createChild("div","shadow-editor-field shadow-editor-flex-field");if(this.errorText=n.createChild("div","error-text"),this.errorText.textContent=g(u.PleaseEnterAValidValueForSText,{PH1:e}),this.errorText.hidden=!0,l.ARIAUtils.markAsAlert(this.errorText),this.propertyInfo=i,this.propertyName=e,this.staticParams=s,this.hasUnits=h,this.hasUnits&&this.staticParams.units&&null!==this.staticParams.defaultUnit){const e=this.staticParams.defaultUnit;this.units=null!==i.units?i.units:e,this.addedUnit=!this.staticParams.units.has(this.units)}else{if(this.hasUnits)throw new Error(g(u.thisPropertyIsSetToContainUnits,{PH1:e}));this.units=""}this.initialRange=this.getUnitRange(),this.boundUpdateCallback=o,this.boundResizeCallback=a,this.selectedNode=l.Context.Context.instance().flavor(r.DOMModel.DOMNode);const c=l.UIUtils.createLabel(t,"shadow-editor-label");d.append(c),this.sliderInput=this.createSliderInput(d,t),this.textBoxInput=this.createTextBoxInput(d),l.ARIAUtils.bindLabelToControl(c,this.textBoxInput),this.unitInput=this.createUnitInput(d),this.selectorInput=this.createSelectorInput(d),this.createTypeToggle(d),this.checkSelectorValueAndToggle(),this.applyNextInput=!1}setInvalidTextBoxInput(e){e?this.errorText.hidden&&(this.errorText.hidden=!1,this.textBoxInput.classList.add("error-input"),this.boundResizeCallback()):this.errorText.hidden||(this.errorText.hidden=!0,this.textBoxInput.classList.remove("error-input"),this.boundResizeCallback())}checkSelectorValueAndToggle(){return!(!this.staticParams.keyValues||null===this.propertyInfo.value||!this.staticParams.keyValues.has(this.propertyInfo.value))&&(this.toggleInputType(),!0)}getUnitRange(){let e=0,t=100,n=1;if(null!==this.propertyInfo.value&&/\d/.test(this.propertyInfo.value))if(this.staticParams.rangeMap.get(this.units)){const i=this.staticParams.rangeMap.get(this.units);i&&(e=Math.min(i.min,parseFloat(this.propertyInfo.value)),t=Math.max(i.max,parseFloat(this.propertyInfo.value)),n=i.step)}else{const i=this.staticParams.rangeMap.get("px");i&&(e=Math.min(i.min,parseFloat(this.propertyInfo.value)),t=Math.max(i.max,parseFloat(this.propertyInfo.value)),n=i.step)}else{const i=this.staticParams.rangeMap.get(this.units);i&&(e=i.min,t=i.max,n=i.step)}return{min:e,max:t,step:n}}createSliderInput(e,t){const n=this.initialRange.min,i=this.initialRange.max,s=this.initialRange.step,o=l.UIUtils.createSlider(n,i,-1);if(o.sliderElement.step=s.toString(),o.sliderElement.tabIndex=0,this.propertyInfo.value)o.value=parseFloat(this.propertyInfo.value);else{const e=(n+i)/2;o.value=e}return o.addEventListener("input",(e=>{this.onSliderInput(e,!1)})),o.addEventListener("mouseup",(e=>{this.onSliderInput(e,!0)})),o.addEventListener("keydown",(e=>{"ArrowUp"!==e.key&&"ArrowDown"!==e.key&&"ArrowLeft"!==e.key&&"ArrowRight"!==e.key||(this.applyNextInput=!0)})),e.appendChild(o),l.ARIAUtils.setAccessibleName(o.sliderElement,g(u.sSliderInput,{PH1:this.propertyName})),o}createTextBoxInput(e){const t=l.UIUtils.createInput("shadow-editor-text-input","number");return t.step=this.initialRange.step.toString(),t.classList.add("font-editor-text-input"),null!==this.propertyInfo.value&&("+"===this.propertyInfo.value.charAt(0)&&(this.propertyInfo.value=this.propertyInfo.value.substr(1)),t.value=this.propertyInfo.value),t.step="any",t.addEventListener("input",this.onTextBoxInput.bind(this),!1),e.appendChild(t),l.ARIAUtils.setAccessibleName(t,g(u.sTextInput,{PH1:this.propertyName})),t}createUnitInput(e){let t;if(this.hasUnits&&this.staticParams.units){const e=this.propertyInfo.units,n=this.staticParams.units;t=l.UIUtils.createSelect(g(u.units),n),t.classList.add("font-editor-select"),this.addedUnit&&e&&t.add(new Option(e,e)),e&&(t.value=e),t.addEventListener("change",this.onUnitInput.bind(this),!1)}else t=l.UIUtils.createSelect(g(u.units),[]),t.classList.add("font-editor-select"),t.disabled=!0;return t.addEventListener("keydown",(e=>{"Enter"===e.key&&e.consume()}),!1),e.appendChild(t),l.ARIAUtils.setAccessibleName(t,g(u.sUnitInput,{PH1:this.propertyName})),t}createSelectorInput(e){const t=l.UIUtils.createSelect(g(u.sKeyValueSelector,{PH1:this.propertyName}),this.staticParams.keyValues);return t.classList.add("font-selector-input"),this.propertyInfo.value&&(t.value=this.propertyInfo.value),t.addEventListener("input",this.onSelectorInput.bind(this),!1),t.addEventListener("keydown",(e=>{"Enter"===e.key&&e.consume()}),!1),e.appendChild(t),t.hidden=!0,t}onSelectorInput(e){if(e.currentTarget){const t=e.currentTarget.value;this.textBoxInput.value="";const n=(parseFloat(this.sliderInput.sliderElement.min)+parseFloat(this.sliderInput.sliderElement.max))/2;this.sliderInput.value=n,this.setInvalidTextBoxInput(!1),this.boundUpdateCallback(this.propertyName,t)}}onSliderInput(e,t){const n=e.currentTarget;if(n){const e=n.value;this.textBoxInput.value=e,this.selectorInput.value="";const i=this.hasUnits?e+this.unitInput.value:e.toString();this.setInvalidTextBoxInput(!1),(t||this.applyNextInput)&&(this.boundUpdateCallback(this.propertyName,i),this.applyNextInput=!1)}}onTextBoxInput(e){const t=e.currentTarget;if(t){const e=t.value,n=e+(""===e?"":this.unitInput.value);this.staticParams.regex.test(n)||""===e&&!t.validationMessage.length?(parseFloat(e)>parseFloat(this.sliderInput.sliderElement.max)?this.sliderInput.sliderElement.max=e:parseFloat(e)<parseFloat(this.sliderInput.sliderElement.min)&&(this.sliderInput.sliderElement.min=e),this.sliderInput.value=parseFloat(e),this.selectorInput.value="",this.setInvalidTextBoxInput(!1),this.boundUpdateCallback(this.propertyName,n)):this.setInvalidTextBoxInput(!0)}}async onUnitInput(e){const t=e.currentTarget,n=t.hasFocus(),i=t.value;t.disabled=!0;const s=this.units,o=await d.getUnitConversionMultiplier(s,i,"font-size"===this.propertyName);this.setInputUnits(o,i),this.textBoxInput.value&&this.boundUpdateCallback(this.propertyName,this.textBoxInput.value+i),this.units=i,t.disabled=!1,n&&t.focus()}createTypeToggle(e){const t=e.createChild("div","spectrum-switcher"),n=new a.Icon.Icon;n.data={iconName:"switcherIcon",color:"var(--color-text-primary)",width:"16px",height:"16px"},t.appendChild(n),l.UIUtils.setTitle(t,g(u.sToggleInputType,{PH1:this.propertyName})),t.tabIndex=0,self.onInvokeElement(t,this.toggleInputType.bind(this)),l.ARIAUtils.markAsButton(t)}toggleInputType(e){e&&"Enter"===e.key&&e.consume(),this.showSliderMode?(this.sliderInput.hidden=!0,this.textBoxInput.hidden=!0,this.unitInput.hidden=!0,this.selectorInput.hidden=!1,this.showSliderMode=!1,l.ARIAUtils.alert(g(u.selectorInputMode))):(this.sliderInput.hidden=!1,this.textBoxInput.hidden=!1,this.unitInput.hidden=!1,this.selectorInput.hidden=!0,this.showSliderMode=!0,l.ARIAUtils.alert(g(u.sliderInputMode)))}setInputUnits(e,t){const n=this.staticParams.rangeMap.get(t);let i,s,o;n?(i=n.min,s=n.max,o=n.step):(i=0,s=100,o=1);let r=!1;const a=c.getRoundingPrecision(o);let l=(i+s)/2;this.textBoxInput.value&&(r=!0,l=parseFloat((parseFloat(this.textBoxInput.value)*e).toFixed(a))),this.sliderInput.sliderElement.min=Math.min(l,i).toString(),this.sliderInput.sliderElement.max=Math.max(l,s).toString(),this.sliderInput.sliderElement.step=o.toString(),this.textBoxInput.step=o.toString(),r&&(this.textBoxInput.value=l.toString()),this.sliderInput.value=l}}})),t.register("hucFQ",(function(t,n){e(t.exports,"default",(()=>s));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2020 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  user-select: none;\n  padding: 4px 12px 12px;\n}\n\n.error-input {\n  box-shadow: 0 0 0 1px var(--color-accent-red);\n}\n\n.error-text {\n  color: var(--color-accent-red);\n  padding: 6px 0;\n}\n\n.warning-input {\n  --override-warning-input-color: #ffdd9e;\n\n  box-shadow: 0 0 0 1px var(--override-warning-input-color);\n}\n\n.-theme-with-dark-background .warning-input,\n:host-context(.-theme-with-dark-background) .warning-input {\n  --override-warning-input-color: rgb(97 63 0);\n}\n\n.hide-warning {\n  display: none;\n}\n\n.font-section-header {\n  font-weight: normal;\n  font-size: 17px;\n  text-align: left;\n}\n\n.font-section-subheader {\n  font-size: 12px;\n  text-align: left;\n  font-weight: bold;\n}\n\n.font-selector-section {\n  overflow-y: auto;\n  padding-bottom: 10px;\n}\n\n.font-selector-input {\n  width: 204px;\n  text-align-last: center;\n}\n\n.font-reset-button {\n  width: 100%;\n  margin-top: 10px;\n}\n\n.font-section {\n  border-top: 1px solid var(--color-details-hairline);\n}\n\n.chrome-select.font-editor-select {\n  min-width: 50px;\n  min-height: 27px;\n}\n\ninput::-webkit-outer-spin-button,\ninput::-webkit-inner-spin-button {\n  display: none;\n  margin: 0;\n}\n\n.preview-text {\n  max-width: 300px;\n  word-break: break-word;\n  display: block;\n}\n\n.rendered-font-list-label {\n  font-weight: bold;\n  font-size: 12px;\n}\n\n.rendered-font-list {\n  padding: 5px 0;\n}\n\n.shadow-editor-field {\n  height: 24px;\n  margin-top: 8px;\n  font-size: 12px;\n  flex-shrink: 0;\n}\n\n.shadow-editor-field:last-of-type {\n  margin-bottom: 8px;\n}\n\n.shadow-editor-flex-field {\n  display: flex;\n  align-items: center;\n  flex-direction: row;\n}\n\n.shadow-editor-field.shadow-editor-blur-field {\n  margin-top: 40px;\n}\n\n.shadow-editor-2D-slider {\n  position: absolute;\n  height: 88px;\n  width: 88px;\n  border: 1px solid var(--divider-line);\n  border-radius: 2px;\n}\n\n.shadow-editor-label {\n  display: inline-block;\n  width: 70px;\n  height: 24px;\n  line-height: 24px;\n  margin-right: 8px;\n  text-align: left;\n}\n\n.shadow-editor-button-left,\n.shadow-editor-button-right {\n  width: 74px;\n  height: 24px;\n  padding: 3px 7px;\n  line-height: 16px;\n  border: 1px solid var(--divider-line);\n  color: var(--color-text-primary);\n  background-color: var(--color-background);\n  text-align: center;\n  font-weight: 500;\n}\n\n.shadow-editor-button-left {\n  border-radius: 2px 0 0 2px;\n}\n\n.shadow-editor-button-right {\n  border-radius: 0 2px 2px 0;\n  border-left-width: 0;\n}\n\n.shadow-editor-button-left:hover,\n.shadow-editor-button-right:hover {\n  box-shadow: 0 1px 1px var(--divider-line);\n}\n\n.shadow-editor-button-left:focus,\n.shadow-editor-button-right:focus {\n  background-color: var(--color-background-elevation-1);\n}\n\n.shadow-editor-text-input {\n  width: 50px;\n  margin: 8px;\n  text-align: center;\n  box-shadow: var(--legacy-focus-ring-inactive-shadow);\n}\n\n.spectrum-switcher {\n  border-radius: 2px;\n  height: 20px;\n  width: 20px;\n  padding: 2px;\n  margin-left: 5px;\n}\n\n.spectrum-switcher:hover {\n  background-color: var(--color-background-elevation-1);\n}\n\n.spectrum-switcher:focus-visible {\n  background-color: var(--legacy-focus-bg-color);\n}\n\n/*# sourceURL=fontEditor.css */\n");var s=i})),t.register("gdQYz",(function(n,i){e(n.exports,"getUnitConversionMultiplier",(()=>p));var s=t("eQFvP"),o=t("dYxX8"),r=t("9z2ZV");async function a(e){const t=r.Context.Context.instance().flavor(s.DOMModel.DOMNode);let n;if(t&&t.parentNode&&"HTML"!==t.nodeName()){const[i]=s.TargetManager.TargetManager.instance().models(o.CSSOverviewModel.CSSOverviewModel),r=e?t.parentNode.id:t.id,a=(await i.getComputedStyleForNode(r).then(l)).replace(/[a-z]/g,"");n=parseFloat(a)}else n=16;return n}function l(e){const t=e.computedStyle;let n=6;if(t[n].name&&"font-size"!==t[n].name)for(let e=0;e<t.length;e++)if("font-size"===t[e].name){n=e;break}return t[n].value}const h={expression:"window.innerWidth",objectGroup:void 0,includeCommandLineAPI:!1,silent:!0,contextId:void 0,returnByValue:!1,generatePreview:!1,userGesture:!1,awaitPromise:!0,throwOnSideEffect:!1,timeout:void 0,disableBreaks:!0,replMode:!1,allowUnsafeEvalBlockedByCSP:!1},d={expression:"window.innerHeight",objectGroup:void 0,includeCommandLineAPI:!1,silent:!0,contextId:void 0,returnByValue:!1,generatePreview:!1,userGesture:!1,awaitPromise:!0,throwOnSideEffect:!1,timeout:void 0,disableBreaks:!0,replMode:!1,allowUnsafeEvalBlockedByCSP:!1};async function c(){const e=r.Context.Context.instance().flavor(s.RuntimeModel.ExecutionContext);let t,n;if(e){const i=await e.evaluate(h,!1,!1),s=await e.evaluate(d,!1,!1);if("error"in i||"error"in s)return null;i.object&&(t=i.object.value),s.object&&(n=s.object.value)}if(void 0===t||void 0===n){const e=r.Context.Context.instance().flavor(s.DOMModel.DOMNode);if(!e)return null;const i=await e.domModel().target().pageAgent().invoke_getLayoutMetrics(),o=i.visualViewport.zoom?i.visualViewport.zoom:1;n=i.visualViewport.clientHeight/o,t=i.visualViewport.clientWidth/o}return{width:t,height:n}}const u=new Map;async function p(e,t,n){let i,s;""===e&&(e="em"),""===t&&(t="em");const o=u.get(e),r=u.get(t);return o&&r?(i="em"===e||"%"===e?await o(n):await o(),s="em"===t||"%"===t?await r(n):await r(),i/s):1}u.set("px",(function(){return 1})),u.set("em",a),u.set("rem",(async function(){const e=function(e){let t=e;for(;t&&"HTML"!==t.nodeName()&&t.parentNode;)t=t.parentNode;return t}(r.Context.Context.instance().flavor(s.DOMModel.DOMNode));if(!e||!e.id)return 16;const[t]=s.TargetManager.TargetManager.instance().models(o.CSSOverviewModel.CSSOverviewModel),n=(await t.getComputedStyleForNode(e.id).then(l)).replace(/[a-z]/g,"");return parseFloat(n)})),u.set("%",(async function(e){return await a(e)/100})),u.set("vh",(async function(){const e=await c();return e?e.height/100:1})),u.set("vw",(async function(){const e=await c();return e?e.width/100:1})),u.set("vmin",(async function(){const e=await c();if(!e)return 1;const t=e.width,n=e.height;return Math.min(t,n)/100})),u.set("vmax",(async function(){const e=await c();if(!e)return 1;const t=e.width,n=e.height;return Math.max(t,n)/100})),u.set("cm",(function(){return 37.795})),u.set("mm",(function(){return 3.7795})),u.set("in",(function(){return 96})),u.set("pt",(function(){return 4/3})),u.set("pc",(function(){return 16}))})),t.register("5nbJO",(function(n,i){e(n.exports,"FontPropertiesRegex",(()=>r)),e(n.exports,"FontFamilyRegex",(()=>a)),e(n.exports,"GlobalValues",(()=>m)),e(n.exports,"FontSizeStaticParams",(()=>I)),e(n.exports,"LineHeightStaticParams",(()=>k)),e(n.exports,"FontWeightStaticParams",(()=>E)),e(n.exports,"LetterSpacingStaticParams",(()=>z)),e(n.exports,"SystemFonts",(()=>P)),e(n.exports,"GenericFonts",(()=>T)),e(n.exports,"generateComputedFontArray",(()=>A)),e(n.exports,"getRoundingPrecision",(()=>L));var s=t("eQFvP"),o=t("dYxX8");const r=/^[^- ][a-zA-Z-]+|-?\+?(?:[0-9]+\.[0-9]+|\.[0-9]+|[0-9]+)[a-zA-Z%]{0,4}/,a=/(?:"[\w \,-]+",? ?|'[\w \,-]+',? ?|[\w \,-]+,? ?)+/,l=new Set(["px","em","rem","%","vh","vw"]),h=new Set(["","px","em","%"]),d=new Set(["em","rem","px"]),c=["","xx-small","x-small","smaller","small","medium","large","larger","x-large","xx-large"],u=["","normal"],p=["","lighter","normal","bold","bolder"],g=["","normal"],m=["inherit","initial","unset"];c.push(...m),u.push(...m),p.push(...m),g.push(...m);const v=new Set(c),f=new Set(u),x=new Set(p),b=new Set(g),S=new Map([["px",{min:0,max:72,step:1}],["em",{min:0,max:4.5,step:.1}],["rem",{min:0,max:4.5,step:.1}],["%",{min:0,max:450,step:1}],["vh",{min:0,max:10,step:.1}],["vw",{min:0,max:10,step:.1}],["vmin",{min:0,max:10,step:.1}],["vmax",{min:0,max:10,step:.1}],["cm",{min:0,max:2,step:.1}],["mm",{min:0,max:20,step:.1}],["in",{min:0,max:1,step:.01}],["pt",{min:0,max:54,step:1}],["pc",{min:0,max:4.5,step:.1}]]),w=new Map([["",{min:0,max:2,step:.1}],["em",{min:0,max:2,step:.1}],["%",{min:0,max:200,step:1}],["px",{min:0,max:32,step:1}],["rem",{min:0,max:2,step:.1}],["vh",{min:0,max:4.5,step:.1}],["vw",{min:0,max:4.5,step:.1}],["vmin",{min:0,max:4.5,step:.1}],["vmax",{min:0,max:4.5,step:.1}],["cm",{min:0,max:1,step:.1}],["mm",{min:0,max:8.5,step:.1}],["in",{min:0,max:.5,step:.1}],["pt",{min:0,max:24,step:1}],["pc",{min:0,max:2,step:.1}]]),y=new Map([["",{min:100,max:700,step:100}]]),C=new Map([["px",{min:-10,max:10,step:.01}],["em",{min:-.625,max:.625,step:.001}],["rem",{min:-.625,max:.625,step:.001}],["%",{min:-62.5,max:62.5,step:.1}],["vh",{min:-1.5,max:1.5,step:.01}],["vw",{min:-1.5,max:1.5,step:.01}],["vmin",{min:-1.5,max:1.5,step:.01}],["vmax",{min:-1.5,max:1.5,step:.01}],["cm",{min:-.25,max:.025,step:.001}],["mm",{min:-2.5,max:2.5,step:.01}],["in",{min:-.1,max:.1,step:.001}],["pt",{min:-7.5,max:7.5,step:.01}],["pc",{min:-.625,max:.625,step:.001}]]),I={regex:/(^[\+\d\.]+)([a-zA-Z%]+)/,units:l,keyValues:v,rangeMap:S,defaultUnit:"px"},k={regex:/(^[\+\d\.]+)([a-zA-Z%]*)/,units:h,keyValues:f,rangeMap:w,defaultUnit:""},E={regex:/(^[\+\d\.]+)/,units:null,keyValues:x,rangeMap:y,defaultUnit:null},z={regex:/([\+-0-9\.]+)([a-zA-Z%]+)/,units:d,keyValues:b,rangeMap:C,defaultUnit:"em"},P=["Arial","Bookman","Candara","Comic Sans MS","Courier New","Garamond","Georgia","Helvetica","Impact","Palatino","Roboto","Times New Roman","Verdana"],T=["serif","sans-serif","monspace","cursive","fantasy","system-ui","ui-serif","ui-sans-serif","ui-monospace","ui-rounded","emoji","math","fangsong"];async function A(){const e=s.TargetManager.TargetManager.instance().models(o.CSSOverviewModel.CSSOverviewModel);if(e){const t=e[0];if(t){const{fontInfo:e}=await t.getNodeStyleStats();return Array.from(e.keys())}}return[]}function L(e){switch(e){case 1:default:return 0;case.1:return 1;case.01:return 2;case.001:return 3}}})),t.register("1NVFN",(function(n,i){e(n.exports,"BezierSwatch",(()=>d)),e(n.exports,"CSSShadowSwatch",(()=>c));var s=t("koSS8"),o=t("7f6zc"),r=t("9z2ZV"),a=t("ePWMq"),l=t("hU9Yk"),h=t("6Zd5X");class d extends HTMLSpanElement{iconElementInternal;textElement;constructor(){super();const e=r.Utils.createShadowRootWithCoreStyles(this,{cssFile:[l.default],delegatesFocus:void 0});this.iconElementInternal=r.Icon.Icon.create("smallicon-bezier","bezier-swatch-icon"),e.appendChild(this.iconElementInternal),this.textElement=this.createChild("span"),e.createChild("slot")}static create(){let e=d.constructorInternal;return e||(e=r.Utils.registerCustomElement("span","bezier-swatch",d),d.constructorInternal=e),e()}bezierText(){return this.textElement.textContent||""}setBezierText(e){this.textElement.textContent=e}hideText(e){this.textElement.hidden=e}iconElement(){return this.iconElementInternal}static constructorInternal=null}class c extends HTMLSpanElement{iconElementInternal;contentElement;colorSwatchInternal;modelInternal;constructor(){super();const e=r.Utils.createShadowRootWithCoreStyles(this,{cssFile:[h.default],delegatesFocus:void 0});this.iconElementInternal=r.Icon.Icon.create("smallicon-shadow","shadow-swatch-icon"),e.appendChild(this.iconElementInternal),e.createChild("slot"),this.contentElement=this.createChild("span")}static create(){let e=c.constructorInternal;return e||(e=r.Utils.registerCustomElement("span","css-shadow-swatch",c),c.constructorInternal=e),e()}model(){return this.modelInternal}setCSSShadow(e){this.modelInternal=e,this.contentElement.removeChildren();const t=o.TextUtils.Utils.splitStringByRegexes(e.asCSSText(),[/!important/g,/inset/g,s.Color.Regex]);for(let n=0;n<t.length;n++){const i=t[n];if(2===i.regexIndex){if(!this.colorSwatchInternal){this.colorSwatchInternal=new(0,a.ColorSwatch);const e=this.colorSwatchInternal.createChild("span");this.colorSwatchInternal.addEventListener(a.FormatChangedEvent.eventName,(t=>{e.textContent=t.data.text}))}this.colorSwatchInternal.renderColor(e.color());const t=this.colorSwatchInternal.querySelector("span");t&&(t.textContent=e.color().asString()),this.contentElement.appendChild(this.colorSwatchInternal)}else this.contentElement.appendChild(document.createTextNode(i.value))}}hideText(e){this.contentElement.hidden=e}iconElement(){return this.iconElementInternal}colorSwatch(){return this.colorSwatchInternal}static constructorInternal=null}})),t.register("hU9Yk",(function(t,n){e(t.exports,"default",(()=>s));const i=new CSSStyleSheet;i.replaceSync('/*\n * Copyright 2016 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  white-space: nowrap;\n\n  --override-bezier-icon-purple: #9c27b0;\n  --override-bezier-icon-purple-hover: #800080;\n}\n\n[is="ui-icon"].icon-mask.bezier-swatch-icon {\n  position: relative;\n  margin-left: 1px;\n  margin-right: 2px;\n  top: 1px;\n  user-select: none;\n  line-height: 10px;\n  background-color: var(--override-bezier-icon-purple);\n  cursor: default;\n}\n\n[is="ui-icon"].icon-mask.bezier-swatch-icon:hover {\n  background-color: var(--override-bezier-icon-purple-hover);\n}\n\n/*# sourceURL=bezierSwatch.css */\n');var s=i})),t.register("6Zd5X",(function(t,n){e(t.exports,"default",(()=>s));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2016 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  white-space: nowrap;\n}\n\n.shadow-swatch-icon {\n  --override-swatch-background-color: #9c27b0;\n\n  position: relative;\n  margin-left: 1px;\n  margin-right: 2px;\n  top: 1px;\n  user-select: none;\n  line-height: 10px;\n  background-color: var(--override-swatch-background-color);\n}\n\n.shadow-swatch-icon:hover {\n  --override-swatch-background-color: #800080;\n}\n\n.-theme-with-dark-background .shadow-swatch-icon,\n:host-context(.-theme-with-dark-background) .shadow-swatch-icon {\n  --override-swatch-background-color: rgb(196 79 216);\n}\n\n.-theme-with-dark-background .shadow-swatch-icon:hover,\n:host-context(.-theme-with-dark-background) .shadow-swatch-icon:hover {\n  --override-swatch-background-color: rgb(255 127 255);\n}\n\n/*# sourceURL=cssShadowSwatch.css */\n");var s=i})),t.register("kJnGS",(function(n,i){e(n.exports,"SwatchPopoverHelper",(()=>d)),e(n.exports,"Events",(()=>s));var s,o=t("koSS8"),r=t("lz7WY"),a=t("9z2ZV"),l=t("ePWMq"),h=t("3Gzrv");class d extends o.ObjectWrapper.ObjectWrapper{popover;hideProxy;boundOnKeyDown;boundFocusOut;isHidden;anchorElement;view;hiddenCallback;focusRestorer;constructor(){super(),this.popover=new a.GlassPane.GlassPane,this.popover.setSizeBehavior("MeasureContent"),this.popover.setMarginBehavior("Arrow"),this.popover.element.addEventListener("mousedown",(e=>e.consume()),!1),this.hideProxy=this.hide.bind(this,!0),this.boundOnKeyDown=this.onKeyDown.bind(this),this.boundFocusOut=this.onFocusOut.bind(this),this.isHidden=!0,this.anchorElement=null}onFocusOut(e){const t=e.relatedTarget;!this.isHidden&&t&&this.view&&!t.isSelfOrDescendant(this.view.contentElement)&&this.hideProxy()}setAnchorElement(e){this.anchorElement=e}isShowing(e){return this.popover.isShowing()&&(e&&this.view===e||!e)}show(e,t,n){if(this.popover.isShowing()){if(this.anchorElement===t)return;this.hide(!0)}this.popover.registerCSSFiles([h.default]),this.dispatchEventToListeners(s.WillShowPopover),this.isHidden=!1,this.anchorElement=t,this.view=e,this.hiddenCallback=n,this.reposition(),e.focus();const i=this.popover.element.ownerDocument;i.addEventListener("mousedown",this.hideProxy,!1),i.defaultView&&i.defaultView.addEventListener("resize",this.hideProxy,!1),this.view.contentElement.addEventListener("keydown",this.boundOnKeyDown,!1)}reposition(){if(!this.isHidden&&this.view){if(this.view.contentElement.removeEventListener("focusout",this.boundFocusOut,!1),this.view.show(this.popover.contentElement),this.anchorElement){let e=this.anchorElement.boxInWindow();if(l.ColorSwatch.isColorSwatch(this.anchorElement)){const t=this.anchorElement;if(!t.anchorBox)return;e=t.anchorBox}this.popover.setContentAnchorBox(e),this.popover.show(this.anchorElement.ownerDocument)}this.view.contentElement.addEventListener("focusout",this.boundFocusOut,!1),this.focusRestorer||(this.focusRestorer=new a.Widget.WidgetFocusRestorer(this.view))}}hide(e){if(this.isHidden)return;const t=this.popover.element.ownerDocument;this.isHidden=!0,this.popover.hide(),t.removeEventListener("mousedown",this.hideProxy,!1),t.defaultView&&t.defaultView.removeEventListener("resize",this.hideProxy,!1),this.hiddenCallback&&this.hiddenCallback.call(null,Boolean(e)),this.focusRestorer&&this.focusRestorer.restore(),this.anchorElement=null,this.view&&(this.view.detach(),this.view.contentElement.removeEventListener("keydown",this.boundOnKeyDown,!1),this.view.contentElement.removeEventListener("focusout",this.boundFocusOut,!1),delete this.view)}onKeyDown(e){if("Enter"===e.key)return this.hide(!0),void e.consume(!0);e.key===r.KeyboardUtilities.ESCAPE_KEY&&(this.hide(!1),e.consume(!0))}}(s||(s={})).WillShowPopover="WillShowPopover"})),t.register("3Gzrv",(function(t,n){e(t.exports,"default",(()=>s));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2017 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.widget {\n  display: flex;\n  background: var(--color-background);\n  box-shadow: var(--drop-shadow);\n  border-radius: 2px;\n  overflow: auto;\n  user-select: text;\n  line-height: 11px;\n}\n\n/*# sourceURL=swatchPopover.css */\n");var s=i}));
//# sourceMappingURL=sources.175f111a.js.map
