("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}).parcelRequire20b5.register("9F6kV",(function(e,s){e.exports=JSON.parse('{"core/common/ResourceType.ts | cspviolationreport":{"message":"CSPViolationReport"},"core/common/ResourceType.ts | css":{"message":"CSS"},"core/common/ResourceType.ts | doc":{"message":"Doc"},"core/common/ResourceType.ts | document":{"message":"Document"},"core/common/ResourceType.ts | documents":{"message":"Documents"},"core/common/ResourceType.ts | eventsource":{"message":"EventSource"},"core/common/ResourceType.ts | fetch":{"message":"Fetch"},"core/common/ResourceType.ts | font":{"message":"Font"},"core/common/ResourceType.ts | fonts":{"message":"Fonts"},"core/common/ResourceType.ts | image":{"message":"Image"},"core/common/ResourceType.ts | images":{"message":"Images"},"core/common/ResourceType.ts | img":{"message":"Img"},"core/common/ResourceType.ts | js":{"message":"JS"},"core/common/ResourceType.ts | manifest":{"message":"Manifest"},"core/common/ResourceType.ts | media":{"message":"Media"},"core/common/ResourceType.ts | other":{"message":"Other"},"core/common/ResourceType.ts | ping":{"message":"Ping"},"core/common/ResourceType.ts | preflight":{"message":"Preflight"},"core/common/ResourceType.ts | script":{"message":"Script"},"core/common/ResourceType.ts | scripts":{"message":"Scripts"},"core/common/ResourceType.ts | signedexchange":{"message":"SignedExchange"},"core/common/ResourceType.ts | stylesheet":{"message":"Stylesheet"},"core/common/ResourceType.ts | stylesheets":{"message":"Stylesheets"},"core/common/ResourceType.ts | texttrack":{"message":"TextTrack"},"core/common/ResourceType.ts | wasm":{"message":"Wasm"},"core/common/ResourceType.ts | webassembly":{"message":"WebAssembly"},"core/common/ResourceType.ts | webbundle":{"message":"WebBundle"},"core/common/ResourceType.ts | websocket":{"message":"WebSocket"},"core/common/ResourceType.ts | websockets":{"message":"WebSockets"},"core/common/ResourceType.ts | webtransport":{"message":"WebTransport"},"core/common/ResourceType.ts | ws":{"message":"WS"},"core/common/ResourceType.ts | xhrAndFetch":{"message":"XHR and Fetch"},"core/common/Revealer.ts | applicationPanel":{"message":"Application panel"},"core/common/Revealer.ts | changesDrawer":{"message":"Changes drawer"},"core/common/Revealer.ts | elementsPanel":{"message":"Elements panel"},"core/common/Revealer.ts | issuesView":{"message":"Issues view"},"core/common/Revealer.ts | networkPanel":{"message":"Network panel"},"core/common/Revealer.ts | sourcesPanel":{"message":"Sources panel"},"core/common/Revealer.ts | stylesSidebar":{"message":"styles sidebar"},"core/common/SettingRegistration.ts | adorner":{"message":"Adorner"},"core/common/SettingRegistration.ts | appearance":{"message":"Appearance"},"core/common/SettingRegistration.ts | console":{"message":"Console"},"core/common/SettingRegistration.ts | debugger":{"message":"Debugger"},"core/common/SettingRegistration.ts | elements":{"message":"Elements"},"core/common/SettingRegistration.ts | extension":{"message":"Extension"},"core/common/SettingRegistration.ts | global":{"message":"Global"},"core/common/SettingRegistration.ts | grid":{"message":"Grid"},"core/common/SettingRegistration.ts | memory":{"message":"Memory"},"core/common/SettingRegistration.ts | mobile":{"message":"Mobile"},"core/common/SettingRegistration.ts | network":{"message":"Network"},"core/common/SettingRegistration.ts | performance":{"message":"Performance"},"core/common/SettingRegistration.ts | persistence":{"message":"Persistence"},"core/common/SettingRegistration.ts | rendering":{"message":"Rendering"},"core/common/SettingRegistration.ts | sources":{"message":"Sources"},"core/common/SettingRegistration.ts | sync":{"message":"Sync"},"core/host/InspectorFrontendHost.ts | devtoolsS":{"message":"DevTools - {PH1}"},"core/host/ResourceLoader.ts | cacheError":{"message":"Cache error"},"core/host/ResourceLoader.ts | certificateError":{"message":"Certificate error"},"core/host/ResourceLoader.ts | certificateManagerError":{"message":"Certificate manager error"},"core/host/ResourceLoader.ts | connectionError":{"message":"Connection error"},"core/host/ResourceLoader.ts | decodingDataUrlFailed":{"message":"Decoding Data URL failed"},"core/host/ResourceLoader.ts | dnsResolverError":{"message":"DNS resolver error"},"core/host/ResourceLoader.ts | ftpError":{"message":"FTP error"},"core/host/ResourceLoader.ts | httpError":{"message":"HTTP error"},"core/host/ResourceLoader.ts | httpErrorStatusCodeSS":{"message":"HTTP error: status code {PH1}, {PH2}"},"core/host/ResourceLoader.ts | invalidUrl":{"message":"Invalid URL"},"core/host/ResourceLoader.ts | signedExchangeError":{"message":"Signed Exchange error"},"core/host/ResourceLoader.ts | systemError":{"message":"System error"},"core/host/ResourceLoader.ts | unknownError":{"message":"Unknown error"},"core/i18n/time-utilities.ts | fdays":{"message":"{PH1} days"},"core/i18n/time-utilities.ts | fhrs":{"message":"{PH1} hrs"},"core/i18n/time-utilities.ts | fmin":{"message":"{PH1} min"},"core/i18n/time-utilities.ts | fmms":{"message":"{PH1} μs"},"core/i18n/time-utilities.ts | fms":{"message":"{PH1} ms"},"core/i18n/time-utilities.ts | fs":{"message":"{PH1} s"},"core/sdk/CompilerSourceMappingContentProvider.ts | couldNotLoadContentForSS":{"message":"Could not load content for {PH1} ({PH2})"},"core/sdk/ConsoleModel.ts | failedToSaveToTempVariable":{"message":"Failed to save to temp variable."},"core/sdk/ConsoleModel.ts | navigatedToS":{"message":"Navigated to {PH1}"},"core/sdk/ConsoleModel.ts | profileSFinished":{"message":"Profile \'\'{PH1}\'\' finished."},"core/sdk/ConsoleModel.ts | profileSStarted":{"message":"Profile \'\'{PH1}\'\' started."},"core/sdk/CPUProfileDataModel.ts | devtoolsCpuProfileParserIsFixing":{"message":"DevTools: CPU profile parser is fixing {PH1} missing samples."},"core/sdk/CPUProfilerModel.ts | profileD":{"message":"Profile {PH1}"},"core/sdk/CSSStyleSheetHeader.ts | couldNotFindTheOriginalStyle":{"message":"Could not find the original style sheet."},"core/sdk/CSSStyleSheetHeader.ts | thereWasAnErrorRetrievingThe":{"message":"There was an error retrieving the source styles."},"core/sdk/DebuggerModel.ts | block":{"message":"Block"},"core/sdk/DebuggerModel.ts | closure":{"message":"Closure"},"core/sdk/DebuggerModel.ts | expression":{"message":"Expression"},"core/sdk/DebuggerModel.ts | global":{"message":"Global"},"core/sdk/DebuggerModel.ts | local":{"message":"Local"},"core/sdk/DebuggerModel.ts | module":{"message":"Module"},"core/sdk/DebuggerModel.ts | script":{"message":"Script"},"core/sdk/DebuggerModel.ts | withBlock":{"message":"With Block"},"core/sdk/DOMDebuggerModel.ts | animation":{"message":"Animation"},"core/sdk/DOMDebuggerModel.ts | animationFrameFired":{"message":"Animation Frame Fired"},"core/sdk/DOMDebuggerModel.ts | cancelAnimationFrame":{"message":"Cancel Animation Frame"},"core/sdk/DOMDebuggerModel.ts | canvas":{"message":"Canvas"},"core/sdk/DOMDebuggerModel.ts | clipboard":{"message":"Clipboard"},"core/sdk/DOMDebuggerModel.ts | closeAudiocontext":{"message":"Close AudioContext"},"core/sdk/DOMDebuggerModel.ts | control":{"message":"Control"},"core/sdk/DOMDebuggerModel.ts | createAudiocontext":{"message":"Create AudioContext"},"core/sdk/DOMDebuggerModel.ts | createCanvasContext":{"message":"Create canvas context"},"core/sdk/DOMDebuggerModel.ts | device":{"message":"Device"},"core/sdk/DOMDebuggerModel.ts | domMutation":{"message":"DOM Mutation"},"core/sdk/DOMDebuggerModel.ts | dragDrop":{"message":"Drag / drop"},"core/sdk/DOMDebuggerModel.ts | geolocation":{"message":"Geolocation"},"core/sdk/DOMDebuggerModel.ts | keyboard":{"message":"Keyboard"},"core/sdk/DOMDebuggerModel.ts | load":{"message":"Load"},"core/sdk/DOMDebuggerModel.ts | media":{"message":"Media"},"core/sdk/DOMDebuggerModel.ts | mouse":{"message":"Mouse"},"core/sdk/DOMDebuggerModel.ts | notification":{"message":"Notification"},"core/sdk/DOMDebuggerModel.ts | parse":{"message":"Parse"},"core/sdk/DOMDebuggerModel.ts | pictureinpicture":{"message":"Picture-in-Picture"},"core/sdk/DOMDebuggerModel.ts | pointer":{"message":"Pointer"},"core/sdk/DOMDebuggerModel.ts | policyViolations":{"message":"Policy Violations"},"core/sdk/DOMDebuggerModel.ts | requestAnimationFrame":{"message":"Request Animation Frame"},"core/sdk/DOMDebuggerModel.ts | resumeAudiocontext":{"message":"Resume AudioContext"},"core/sdk/DOMDebuggerModel.ts | script":{"message":"Script"},"core/sdk/DOMDebuggerModel.ts | scriptBlockedByContentSecurity":{"message":"Script Blocked by Content Security Policy"},"core/sdk/DOMDebuggerModel.ts | scriptBlockedDueToContent":{"message":"Script blocked due to Content Security Policy directive: {PH1}"},"core/sdk/DOMDebuggerModel.ts | scriptFirstStatement":{"message":"Script First Statement"},"core/sdk/DOMDebuggerModel.ts | setInnerhtml":{"message":"Set innerHTML"},"core/sdk/DOMDebuggerModel.ts | setTimeoutOrIntervalFired":{"message":"{PH1} fired"},"core/sdk/DOMDebuggerModel.ts | sinkViolations":{"message":"Sink Violations"},"core/sdk/DOMDebuggerModel.ts | suspendAudiocontext":{"message":"Suspend AudioContext"},"core/sdk/DOMDebuggerModel.ts | timer":{"message":"Timer"},"core/sdk/DOMDebuggerModel.ts | touch":{"message":"Touch"},"core/sdk/DOMDebuggerModel.ts | trustedTypeViolations":{"message":"Trusted Type Violations"},"core/sdk/DOMDebuggerModel.ts | webaudio":{"message":"WebAudio"},"core/sdk/DOMDebuggerModel.ts | webglErrorFired":{"message":"WebGL Error Fired"},"core/sdk/DOMDebuggerModel.ts | webglErrorFiredS":{"message":"WebGL Error Fired ({PH1})"},"core/sdk/DOMDebuggerModel.ts | webglWarningFired":{"message":"WebGL Warning Fired"},"core/sdk/DOMDebuggerModel.ts | window":{"message":"Window"},"core/sdk/DOMDebuggerModel.ts | worker":{"message":"Worker"},"core/sdk/DOMDebuggerModel.ts | xhr":{"message":"XHR"},"core/sdk/EventBreakpointsModel.ts | auctionWorklet":{"message":"Ad Auction Worklet"},"core/sdk/EventBreakpointsModel.ts | beforeBidderWorkletBiddingStart":{"message":"Bidder Bidding Phase Start"},"core/sdk/EventBreakpointsModel.ts | beforeBidderWorkletReportingStart":{"message":"Bidder Reporting Phase Start"},"core/sdk/EventBreakpointsModel.ts | beforeSellerWorkletReportingStart":{"message":"Seller Reporting Phase Start"},"core/sdk/EventBreakpointsModel.ts | beforeSellerWorkletScoringStart":{"message":"Seller Scoring Phase Start"},"core/sdk/NetworkManager.ts | crossoriginReadBlockingCorb":{"message":"Cross-Origin Read Blocking (CORB) blocked cross-origin response {PH1} with MIME type {PH2}. See https://www.chromestatus.com/feature/5629709824032768 for more details."},"core/sdk/NetworkManager.ts | fastG":{"message":"Fast 3G"},"core/sdk/NetworkManager.ts | noContentForPreflight":{"message":"No content available for preflight request"},"core/sdk/NetworkManager.ts | noContentForRedirect":{"message":"No content available because this request was redirected"},"core/sdk/NetworkManager.ts | noContentForWebSocket":{"message":"Content for WebSockets is currently not supported"},"core/sdk/NetworkManager.ts | noThrottling":{"message":"No throttling"},"core/sdk/NetworkManager.ts | offline":{"message":"Offline"},"core/sdk/NetworkManager.ts | requestWasBlockedByDevtoolsS":{"message":"Request was blocked by DevTools: \\"{PH1}\\""},"core/sdk/NetworkManager.ts | sFailedLoadingSS":{"message":"{PH1} failed loading: {PH2} \\"{PH3}\\"."},"core/sdk/NetworkManager.ts | sFinishedLoadingSS":{"message":"{PH1} finished loading: {PH2} \\"{PH3}\\"."},"core/sdk/NetworkManager.ts | slowG":{"message":"Slow 3G"},"core/sdk/NetworkRequest.ts | anUnknownErrorWasEncounteredWhenTrying":{"message":"An unknown error was encountered when trying to store this cookie."},"core/sdk/NetworkRequest.ts | binary":{"message":"(binary)"},"core/sdk/NetworkRequest.ts | blockedReasonInvalidDomain":{"message":"This attempt to set a cookie via a Set-Cookie header was blocked because its Domain attribute was invalid with regards to the current host url."},"core/sdk/NetworkRequest.ts | blockedReasonInvalidPrefix":{"message":"This attempt to set a cookie via a Set-Cookie header was blocked because it used the \\"__Secure-\\" or \\"__Host-\\" prefix in its name and broke the additional rules applied to cookies with these prefixes as defined in https://tools.ietf.org/html/draft-west-cookie-prefixes-05."},"core/sdk/NetworkRequest.ts | blockedReasonOverwriteSecure":{"message":"This attempt to set a cookie via a Set-Cookie header was blocked because it was not sent over a secure connection and would have overwritten a cookie with the Secure attribute."},"core/sdk/NetworkRequest.ts | blockedReasonSameSiteNoneInsecure":{"message":"This attempt to set a cookie via a Set-Cookie header was blocked because it had the \\"SameSite=None\\" attribute but did not have the \\"Secure\\" attribute, which is required in order to use \\"SameSite=None\\"."},"core/sdk/NetworkRequest.ts | blockedReasonSameSiteStrictLax":{"message":"This attempt to set a cookie via a Set-Cookie header was blocked because it had the \\"{PH1}\\" attribute but came from a cross-site response which was not the response to a top-level navigation."},"core/sdk/NetworkRequest.ts | blockedReasonSameSiteUnspecifiedTreatedAsLax":{"message":"This Set-Cookie header didn\'t specify a \\"SameSite\\" attribute and was defaulted to \\"SameSite=Lax,\\" and was blocked because it came from a cross-site response which was not the response to a top-level navigation. The Set-Cookie had to have been set with \\"SameSite=None\\" to enable cross-site usage."},"core/sdk/NetworkRequest.ts | blockedReasonSecureOnly":{"message":"This attempt to set a cookie via a Set-Cookie header was blocked because it had the \\"Secure\\" attribute but was not received over a secure connection."},"core/sdk/NetworkRequest.ts | domainMismatch":{"message":"This cookie was blocked because neither did the request URL\'s domain exactly match the cookie\'s domain, nor was the request URL\'s domain a subdomain of the cookie\'s Domain attribute value."},"core/sdk/NetworkRequest.ts | nameValuePairExceedsMaxSize":{"message":"This cookie was blocked because it was too large. The combined size of the name and value must be less than or equal to 4096 characters."},"core/sdk/NetworkRequest.ts | notOnPath":{"message":"This cookie was blocked because its path was not an exact match for or a superdirectory of the request url\'s path."},"core/sdk/NetworkRequest.ts | samePartyFromCrossPartyContext":{"message":"This cookie was blocked because it had the \\"SameParty\\" attribute but the request was cross-party. The request was considered cross-party because the domain of the resource\'s URL and the domains of the resource\'s enclosing frames/documents are neither owners nor members in the same First-Party Set."},"core/sdk/NetworkRequest.ts | sameSiteLax":{"message":"This cookie was blocked because it had the \\"SameSite=Lax\\" attribute and the request was made from a different site and was not initiated by a top-level navigation."},"core/sdk/NetworkRequest.ts | sameSiteNoneInsecure":{"message":"This cookie was blocked because it had the \\"SameSite=None\\" attribute but was not marked \\"Secure\\". Cookies without SameSite restrictions must be marked \\"Secure\\" and sent over a secure connection."},"core/sdk/NetworkRequest.ts | sameSiteStrict":{"message":"This cookie was blocked because it had the \\"SameSite=Strict\\" attribute and the request was made from a different site. This includes top-level navigation requests initiated by other sites."},"core/sdk/NetworkRequest.ts | sameSiteUnspecifiedTreatedAsLax":{"message":"This cookie didn\'t specify a \\"SameSite\\" attribute when it was stored and was defaulted to \\"SameSite=Lax,\\" and was blocked because the request was made from a different site and was not initiated by a top-level navigation. The cookie had to have been set with \\"SameSite=None\\" to enable cross-site usage."},"core/sdk/NetworkRequest.ts | schemefulSameSiteLax":{"message":"This cookie was blocked because it had the \\"SameSite=Lax\\" attribute but the request was cross-site and was not initiated by a top-level navigation. This request is considered cross-site because the URL has a different scheme than the current site."},"core/sdk/NetworkRequest.ts | schemefulSameSiteStrict":{"message":"This cookie was blocked because it had the \\"SameSite=Strict\\" attribute but the request was cross-site. This includes top-level navigation requests initiated by other sites. This request is considered cross-site because the URL has a different scheme than the current site."},"core/sdk/NetworkRequest.ts | schemefulSameSiteUnspecifiedTreatedAsLax":{"message":"This cookie didn\'t specify a \\"SameSite\\" attribute when it was stored, was defaulted to \\"SameSite=Lax\\", and was blocked because the request was cross-site and was not initiated by a top-level navigation. This request is considered cross-site because the URL has a different scheme than the current site."},"core/sdk/NetworkRequest.ts | secureOnly":{"message":"This cookie was blocked because it had the \\"Secure\\" attribute and the connection was not secure."},"core/sdk/NetworkRequest.ts | setcookieHeaderIsIgnoredIn":{"message":"Set-Cookie header is ignored in response from url: {PH1}. The combined size of the name and value must be less than or equal to 4096 characters."},"core/sdk/NetworkRequest.ts | theSchemeOfThisConnectionIsNot":{"message":"The scheme of this connection is not allowed to store cookies."},"core/sdk/NetworkRequest.ts | thisSetcookieDidntSpecifyASamesite":{"message":"This Set-Cookie header didn\'t specify a \\"SameSite\\" attribute, was defaulted to \\"SameSite=Lax\\", and was blocked because it came from a cross-site response which was not the response to a top-level navigation. This response is considered cross-site because the URL has a different scheme than the current site."},"core/sdk/NetworkRequest.ts | thisSetcookieHadInvalidSyntax":{"message":"This Set-Cookie header had invalid syntax."},"core/sdk/NetworkRequest.ts | thisSetcookieWasBlockedBecauseItHadTheSameparty":{"message":"This attempt to set a cookie via a Set-Cookie header was blocked because it had the \\"SameParty\\" attribute but the request was cross-party. The request was considered cross-party because the domain of the resource\'s URL and the domains of the resource\'s enclosing frames/documents are neither owners nor members in the same First-Party Set."},"core/sdk/NetworkRequest.ts | thisSetcookieWasBlockedBecauseItHadTheSamepartyAttribute":{"message":"This attempt to set a cookie via a Set-Cookie header was blocked because it had the \\"SameParty\\" attribute but also had other conflicting attributes. Chrome requires cookies that use the \\"SameParty\\" attribute to also have the \\"Secure\\" attribute, and to not be restricted to \\"SameSite=Strict\\"."},"core/sdk/NetworkRequest.ts | thisSetcookieWasBlockedBecauseItHadTheSamesiteStrictLax":{"message":"This attempt to set a cookie via a Set-Cookie header was blocked because it had the \\"{PH1}\\" attribute but came from a cross-site response which was not the response to a top-level navigation. This response is considered cross-site because the URL has a different scheme than the current site."},"core/sdk/NetworkRequest.ts | thisSetcookieWasBlockedBecauseTheNameValuePairExceedsMaxSize":{"message":"This attempt to set a cookie via a Set-Cookie header was blocked because the cookie was too large. The combined size of the name and value must be less than or equal to 4096 characters."},"core/sdk/NetworkRequest.ts | thisSetcookieWasBlockedDueToUser":{"message":"This attempt to set a cookie via a Set-Cookie header was blocked due to user preferences."},"core/sdk/NetworkRequest.ts | unknownError":{"message":"An unknown error was encountered when trying to send this cookie."},"core/sdk/NetworkRequest.ts | userPreferences":{"message":"This cookie was blocked due to user preferences."},"core/sdk/OverlayModel.ts | pausedInDebugger":{"message":"Paused in debugger"},"core/sdk/PageResourceLoader.ts | loadCanceledDueToLoadTimeout":{"message":"Load canceled due to load timeout"},"core/sdk/PageResourceLoader.ts | loadCanceledDueToReloadOf":{"message":"Load canceled due to reload of inspected page"},"core/sdk/Script.ts | scriptRemovedOrDeleted":{"message":"Script removed or deleted."},"core/sdk/Script.ts | unableToFetchScriptSource":{"message":"Unable to fetch script source."},"core/sdk/sdk-meta.ts | achromatopsia":{"message":"Achromatopsia"},"core/sdk/sdk-meta.ts | blurredVision":{"message":"Blurred vision"},"core/sdk/sdk-meta.ts | captureAsyncStackTraces":{"message":"Capture async stack traces"},"core/sdk/sdk-meta.ts | deuteranopia":{"message":"Deuteranopia"},"core/sdk/sdk-meta.ts | disableAsyncStackTraces":{"message":"Disable async stack traces"},"core/sdk/sdk-meta.ts | disableAvifFormat":{"message":"Disable AVIF format"},"core/sdk/sdk-meta.ts | disableCache":{"message":"Disable cache (while DevTools is open)"},"core/sdk/sdk-meta.ts | disableJavascript":{"message":"Disable JavaScript"},"core/sdk/sdk-meta.ts | disableJpegXlFormat":{"message":"Disable JPEG XL format"},"core/sdk/sdk-meta.ts | disableLocalFonts":{"message":"Disable local fonts"},"core/sdk/sdk-meta.ts | disableNetworkRequestBlocking":{"message":"Disable network request blocking"},"core/sdk/sdk-meta.ts | disableWebpFormat":{"message":"Disable WebP format"},"core/sdk/sdk-meta.ts | doNotCaptureAsyncStackTraces":{"message":"Do not capture async stack traces"},"core/sdk/sdk-meta.ts | doNotEmulateAFocusedPage":{"message":"Do not emulate a focused page"},"core/sdk/sdk-meta.ts | doNotEmulateAnyVisionDeficiency":{"message":"Do not emulate any vision deficiency"},"core/sdk/sdk-meta.ts | doNotEmulateCss":{"message":"Do not emulate CSS {PH1}"},"core/sdk/sdk-meta.ts | doNotEmulateCssMediaType":{"message":"Do not emulate CSS media type"},"core/sdk/sdk-meta.ts | doNotExtendGridLines":{"message":"Do not extend grid lines"},"core/sdk/sdk-meta.ts | doNotHighlightAdFrames":{"message":"Do not highlight ad frames"},"core/sdk/sdk-meta.ts | doNotPauseOnExceptions":{"message":"Do not pause on exceptions"},"core/sdk/sdk-meta.ts | doNotPreserveLogUponNavigation":{"message":"Do not preserve log upon navigation"},"core/sdk/sdk-meta.ts | doNotShowGridNamedAreas":{"message":"Do not show grid named areas"},"core/sdk/sdk-meta.ts | doNotShowGridTrackSizes":{"message":"Do not show grid track sizes"},"core/sdk/sdk-meta.ts | doNotShowRulersOnHover":{"message":"Do not show rulers on hover"},"core/sdk/sdk-meta.ts | emulateAchromatopsia":{"message":"Emulate achromatopsia"},"core/sdk/sdk-meta.ts | emulateAFocusedPage":{"message":"Emulate a focused page"},"core/sdk/sdk-meta.ts | emulateAutoDarkMode":{"message":"Emulate auto dark mode"},"core/sdk/sdk-meta.ts | emulateBlurredVision":{"message":"Emulate blurred vision"},"core/sdk/sdk-meta.ts | emulateCss":{"message":"Emulate CSS {PH1}"},"core/sdk/sdk-meta.ts | emulateCssMediaFeature":{"message":"Emulate CSS media feature {PH1}"},"core/sdk/sdk-meta.ts | emulateCssMediaType":{"message":"Emulate CSS media type"},"core/sdk/sdk-meta.ts | emulateCssPrintMediaType":{"message":"Emulate CSS print media type"},"core/sdk/sdk-meta.ts | emulateCssScreenMediaType":{"message":"Emulate CSS screen media type"},"core/sdk/sdk-meta.ts | emulateDeuteranopia":{"message":"Emulate deuteranopia"},"core/sdk/sdk-meta.ts | emulateProtanopia":{"message":"Emulate protanopia"},"core/sdk/sdk-meta.ts | emulateTritanopia":{"message":"Emulate tritanopia"},"core/sdk/sdk-meta.ts | emulateVisionDeficiencies":{"message":"Emulate vision deficiencies"},"core/sdk/sdk-meta.ts | enableAvifFormat":{"message":"Enable AVIF format"},"core/sdk/sdk-meta.ts | enableCache":{"message":"Enable cache"},"core/sdk/sdk-meta.ts | enableCustomFormatters":{"message":"Enable custom formatters"},"core/sdk/sdk-meta.ts | enableJavascript":{"message":"Enable JavaScript"},"core/sdk/sdk-meta.ts | enableJpegXlFormat":{"message":"Enable JPEG XL format"},"core/sdk/sdk-meta.ts | enableLocalFonts":{"message":"Enable local fonts"},"core/sdk/sdk-meta.ts | enableNetworkRequestBlocking":{"message":"Enable network request blocking"},"core/sdk/sdk-meta.ts | enableWebpFormat":{"message":"Enable WebP format"},"core/sdk/sdk-meta.ts | extendGridLines":{"message":"Extend grid lines"},"core/sdk/sdk-meta.ts | hideCoreWebVitalsOverlay":{"message":"Hide Core Web Vitals overlay"},"core/sdk/sdk-meta.ts | hideFramesPerSecondFpsMeter":{"message":"Hide frames per second (FPS) meter"},"core/sdk/sdk-meta.ts | hideLayerBorders":{"message":"Hide layer borders"},"core/sdk/sdk-meta.ts | hideLayoutShiftRegions":{"message":"Hide layout shift regions"},"core/sdk/sdk-meta.ts | hideLineLabels":{"message":"Hide line labels"},"core/sdk/sdk-meta.ts | hidePaintFlashingRectangles":{"message":"Hide paint flashing rectangles"},"core/sdk/sdk-meta.ts | hideScrollPerformanceBottlenecks":{"message":"Hide scroll performance bottlenecks"},"core/sdk/sdk-meta.ts | highlightAdFrames":{"message":"Highlight ad frames"},"core/sdk/sdk-meta.ts | noEmulation":{"message":"No emulation"},"core/sdk/sdk-meta.ts | pauseOnExceptions":{"message":"Pause on exceptions"},"core/sdk/sdk-meta.ts | preserveLogUponNavigation":{"message":"Preserve log upon navigation"},"core/sdk/sdk-meta.ts | print":{"message":"print"},"core/sdk/sdk-meta.ts | protanopia":{"message":"Protanopia"},"core/sdk/sdk-meta.ts | query":{"message":"query"},"core/sdk/sdk-meta.ts | screen":{"message":"screen"},"core/sdk/sdk-meta.ts | showAreaNames":{"message":"Show area names"},"core/sdk/sdk-meta.ts | showCoreWebVitalsOverlay":{"message":"Show Core Web Vitals overlay"},"core/sdk/sdk-meta.ts | showFramesPerSecondFpsMeter":{"message":"Show frames per second (FPS) meter"},"core/sdk/sdk-meta.ts | showGridNamedAreas":{"message":"Show grid named areas"},"core/sdk/sdk-meta.ts | showGridTrackSizes":{"message":"Show grid track sizes"},"core/sdk/sdk-meta.ts | showLayerBorders":{"message":"Show layer borders"},"core/sdk/sdk-meta.ts | showLayoutShiftRegions":{"message":"Show layout shift regions"},"core/sdk/sdk-meta.ts | showLineLabels":{"message":"Show line labels"},"core/sdk/sdk-meta.ts | showLineNames":{"message":"Show line names"},"core/sdk/sdk-meta.ts | showLineNumbers":{"message":"Show line numbers"},"core/sdk/sdk-meta.ts | showPaintFlashingRectangles":{"message":"Show paint flashing rectangles"},"core/sdk/sdk-meta.ts | showRulersOnHover":{"message":"Show rulers on hover"},"core/sdk/sdk-meta.ts | showScrollPerformanceBottlenecks":{"message":"Show scroll performance bottlenecks"},"core/sdk/sdk-meta.ts | showTrackSizes":{"message":"Show track sizes"},"core/sdk/sdk-meta.ts | tritanopia":{"message":"Tritanopia"},"core/sdk/ServerTiming.ts | deprecatedSyntaxFoundPleaseUse":{"message":"Deprecated syntax found. Please use: <name>;dur=<duration>;desc=<description>"},"core/sdk/ServerTiming.ts | duplicateParameterSIgnored":{"message":"Duplicate parameter \\"{PH1}\\" ignored."},"core/sdk/ServerTiming.ts | extraneousTrailingCharacters":{"message":"Extraneous trailing characters."},"core/sdk/ServerTiming.ts | noValueFoundForParameterS":{"message":"No value found for parameter \\"{PH1}\\"."},"core/sdk/ServerTiming.ts | unableToParseSValueS":{"message":"Unable to parse \\"{PH1}\\" value \\"{PH2}\\"."},"core/sdk/ServerTiming.ts | unrecognizedParameterS":{"message":"Unrecognized parameter \\"{PH1}\\"."},"core/sdk/ServiceWorkerCacheModel.ts | serviceworkercacheagentError":{"message":"ServiceWorkerCacheAgent error deleting cache entry {PH1} in cache: {PH2}"},"core/sdk/ServiceWorkerManager.ts | activated":{"message":"activated"},"core/sdk/ServiceWorkerManager.ts | activating":{"message":"activating"},"core/sdk/ServiceWorkerManager.ts | installed":{"message":"installed"},"core/sdk/ServiceWorkerManager.ts | installing":{"message":"installing"},"core/sdk/ServiceWorkerManager.ts | new":{"message":"new"},"core/sdk/ServiceWorkerManager.ts | redundant":{"message":"redundant"},"core/sdk/ServiceWorkerManager.ts | running":{"message":"running"},"core/sdk/ServiceWorkerManager.ts | sSS":{"message":"{PH1} #{PH2} ({PH3})"},"core/sdk/ServiceWorkerManager.ts | starting":{"message":"starting"},"core/sdk/ServiceWorkerManager.ts | stopped":{"message":"stopped"},"core/sdk/ServiceWorkerManager.ts | stopping":{"message":"stopping"},"core/sdk/SourceMap.ts | couldNotLoadContentForSS":{"message":"Could not load content for {PH1}: {PH2}"},"core/sdk/SourceMap.ts | couldNotParseContentForSS":{"message":"Could not parse content for {PH1}: {PH2}"},"core/sdk/SourceMapManager.ts | devtoolsFailedToLoadSourcemapS":{"message":"DevTools failed to load source map: {PH1}"},"entrypoints/inspector_main/inspector_main-meta.ts | autoOpenDevTools":{"message":"Auto-open DevTools for popups"},"entrypoints/inspector_main/inspector_main-meta.ts | blockAds":{"message":"Block ads on this site"},"entrypoints/inspector_main/inspector_main-meta.ts | colorVisionDeficiency":{"message":"color vision deficiency"},"entrypoints/inspector_main/inspector_main-meta.ts | cssMediaFeature":{"message":"CSS media feature"},"entrypoints/inspector_main/inspector_main-meta.ts | cssMediaType":{"message":"CSS media type"},"entrypoints/inspector_main/inspector_main-meta.ts | disablePaused":{"message":"Disable paused state overlay"},"entrypoints/inspector_main/inspector_main-meta.ts | doNotAutoOpen":{"message":"Do not auto-open DevTools for popups"},"entrypoints/inspector_main/inspector_main-meta.ts | forceAdBlocking":{"message":"Force ad blocking on this site"},"entrypoints/inspector_main/inspector_main-meta.ts | fps":{"message":"fps"},"entrypoints/inspector_main/inspector_main-meta.ts | hardReloadPage":{"message":"Hard reload page"},"entrypoints/inspector_main/inspector_main-meta.ts | layout":{"message":"layout"},"entrypoints/inspector_main/inspector_main-meta.ts | paint":{"message":"paint"},"entrypoints/inspector_main/inspector_main-meta.ts | reloadPage":{"message":"Reload page"},"entrypoints/inspector_main/inspector_main-meta.ts | rendering":{"message":"Rendering"},"entrypoints/inspector_main/inspector_main-meta.ts | showAds":{"message":"Show ads on this site, if allowed"},"entrypoints/inspector_main/inspector_main-meta.ts | showRendering":{"message":"Show Rendering"},"entrypoints/inspector_main/inspector_main-meta.ts | visionDeficiency":{"message":"vision deficiency"},"entrypoints/inspector_main/InspectorMain.ts | javascriptIsDisabled":{"message":"JavaScript is disabled"},"entrypoints/inspector_main/InspectorMain.ts | main":{"message":"Main"},"entrypoints/inspector_main/InspectorMain.ts | openDedicatedTools":{"message":"Open dedicated DevTools for Node.js"},"entrypoints/inspector_main/RenderingOptions.ts | coreWebVitals":{"message":"Core Web Vitals"},"entrypoints/inspector_main/RenderingOptions.ts | disableAvifImageFormat":{"message":"Disable AVIF image format"},"entrypoints/inspector_main/RenderingOptions.ts | disableJpegXlImageFormat":{"message":"Disable JPEG XL image format"},"entrypoints/inspector_main/RenderingOptions.ts | disableLocalFonts":{"message":"Disable local fonts"},"entrypoints/inspector_main/RenderingOptions.ts | disablesLocalSourcesInFontface":{"message":"Disables local() sources in @font-face rules. Requires a page reload to apply."},"entrypoints/inspector_main/RenderingOptions.ts | disableWebpImageFormat":{"message":"Disable WebP image format"},"entrypoints/inspector_main/RenderingOptions.ts | emulateAFocusedPage":{"message":"Emulate a focused page"},"entrypoints/inspector_main/RenderingOptions.ts | emulateAutoDarkMode":{"message":"Enable automatic dark mode"},"entrypoints/inspector_main/RenderingOptions.ts | emulatesAFocusedPage":{"message":"Emulates a focused page."},"entrypoints/inspector_main/RenderingOptions.ts | emulatesAutoDarkMode":{"message":"Enables automatic dark mode and sets prefers-color-scheme to dark."},"entrypoints/inspector_main/RenderingOptions.ts | forcesCssColorgamutMediaFeature":{"message":"Forces CSS color-gamut media feature"},"entrypoints/inspector_main/RenderingOptions.ts | forcesCssForcedColors":{"message":"Forces CSS forced-colors media feature"},"entrypoints/inspector_main/RenderingOptions.ts | forcesCssPreferscolorschemeMedia":{"message":"Forces CSS prefers-color-scheme media feature"},"entrypoints/inspector_main/RenderingOptions.ts | forcesCssPreferscontrastMedia":{"message":"Forces CSS prefers-contrast media feature"},"entrypoints/inspector_main/RenderingOptions.ts | forcesCssPrefersreduceddataMedia":{"message":"Forces CSS prefers-reduced-data media feature"},"entrypoints/inspector_main/RenderingOptions.ts | forcesCssPrefersreducedmotion":{"message":"Forces CSS prefers-reduced-motion media feature"},"entrypoints/inspector_main/RenderingOptions.ts | forcesMediaTypeForTestingPrint":{"message":"Forces media type for testing print and screen styles"},"entrypoints/inspector_main/RenderingOptions.ts | forcesVisionDeficiencyEmulation":{"message":"Forces vision deficiency emulation"},"entrypoints/inspector_main/RenderingOptions.ts | frameRenderingStats":{"message":"Frame Rendering Stats"},"entrypoints/inspector_main/RenderingOptions.ts | highlightAdFrames":{"message":"Highlight ad frames"},"entrypoints/inspector_main/RenderingOptions.ts | highlightsAreasOfThePageBlueThat":{"message":"Highlights areas of the page (blue) that were shifted. May not be suitable for people prone to photosensitive epilepsy."},"entrypoints/inspector_main/RenderingOptions.ts | highlightsAreasOfThePageGreen":{"message":"Highlights areas of the page (green) that need to be repainted. May not be suitable for people prone to photosensitive epilepsy."},"entrypoints/inspector_main/RenderingOptions.ts | highlightsElementsTealThatCan":{"message":"Highlights elements (teal) that can slow down scrolling, including touch & wheel event handlers and other main-thread scrolling situations."},"entrypoints/inspector_main/RenderingOptions.ts | highlightsFramesRedDetectedToBe":{"message":"Highlights frames (red) detected to be ads."},"entrypoints/inspector_main/RenderingOptions.ts | layerBorders":{"message":"Layer borders"},"entrypoints/inspector_main/RenderingOptions.ts | layoutShiftRegions":{"message":"Layout Shift Regions"},"entrypoints/inspector_main/RenderingOptions.ts | paintFlashing":{"message":"Paint flashing"},"entrypoints/inspector_main/RenderingOptions.ts | plotsFrameThroughputDropped":{"message":"Plots frame throughput, dropped frames distribution, and GPU memory."},"entrypoints/inspector_main/RenderingOptions.ts | requiresAPageReloadToApplyAnd":{"message":"Requires a page reload to apply and disables caching for image requests."},"entrypoints/inspector_main/RenderingOptions.ts | scrollingPerformanceIssues":{"message":"Scrolling performance issues"},"entrypoints/inspector_main/RenderingOptions.ts | showsAnOverlayWithCoreWebVitals":{"message":"Shows an overlay with Core Web Vitals."},"entrypoints/inspector_main/RenderingOptions.ts | showsLayerBordersOrangeoliveAnd":{"message":"Shows layer borders (orange/olive) and tiles (cyan)."},"entrypoints/js_app/js_app.ts | main":{"message":"Main"},"entrypoints/main/main-meta.ts | asAuthored":{"message":"As authored"},"entrypoints/main/main-meta.ts | auto":{"message":"auto"},"entrypoints/main/main-meta.ts | bottom":{"message":"Bottom"},"entrypoints/main/main-meta.ts | browserLanguage":{"message":"Browser UI language"},"entrypoints/main/main-meta.ts | cancelSearch":{"message":"Cancel search"},"entrypoints/main/main-meta.ts | colorFormat":{"message":"Color format:"},"entrypoints/main/main-meta.ts | darkCapital":{"message":"Dark"},"entrypoints/main/main-meta.ts | darkLower":{"message":"dark"},"entrypoints/main/main-meta.ts | devtoolsDefault":{"message":"DevTools (Default)"},"entrypoints/main/main-meta.ts | dockToBottom":{"message":"Dock to bottom"},"entrypoints/main/main-meta.ts | dockToLeft":{"message":"Dock to left"},"entrypoints/main/main-meta.ts | dockToRight":{"message":"Dock to right"},"entrypoints/main/main-meta.ts | enableCtrlShortcutToSwitchPanels":{"message":"Enable Ctrl + 1-9 shortcut to switch panels"},"entrypoints/main/main-meta.ts | enableShortcutToSwitchPanels":{"message":"Enable ⌘ + 1-9 shortcut to switch panels"},"entrypoints/main/main-meta.ts | enableSync":{"message":"Enable settings sync"},"entrypoints/main/main-meta.ts | findNextResult":{"message":"Find next result"},"entrypoints/main/main-meta.ts | findPreviousResult":{"message":"Find previous result"},"entrypoints/main/main-meta.ts | focusDebuggee":{"message":"Focus debuggee"},"entrypoints/main/main-meta.ts | horizontal":{"message":"horizontal"},"entrypoints/main/main-meta.ts | language":{"message":"Language:"},"entrypoints/main/main-meta.ts | left":{"message":"Left"},"entrypoints/main/main-meta.ts | lightCapital":{"message":"Light"},"entrypoints/main/main-meta.ts | lightLower":{"message":"light"},"entrypoints/main/main-meta.ts | nextPanel":{"message":"Next panel"},"entrypoints/main/main-meta.ts | panelLayout":{"message":"Panel layout:"},"entrypoints/main/main-meta.ts | previousPanel":{"message":"Previous panel"},"entrypoints/main/main-meta.ts | reloadDevtools":{"message":"Reload DevTools"},"entrypoints/main/main-meta.ts | resetZoomLevel":{"message":"Reset zoom level"},"entrypoints/main/main-meta.ts | restoreLastDockPosition":{"message":"Restore last dock position"},"entrypoints/main/main-meta.ts | right":{"message":"Right"},"entrypoints/main/main-meta.ts | searchInPanel":{"message":"Search in panel"},"entrypoints/main/main-meta.ts | setColorFormatAsAuthored":{"message":"Set color format as authored"},"entrypoints/main/main-meta.ts | setColorFormatToHex":{"message":"Set color format to HEX"},"entrypoints/main/main-meta.ts | setColorFormatToHsl":{"message":"Set color format to HSL"},"entrypoints/main/main-meta.ts | setColorFormatToRgb":{"message":"Set color format to RGB"},"entrypoints/main/main-meta.ts | switchToDarkTheme":{"message":"Switch to dark theme"},"entrypoints/main/main-meta.ts | switchToLightTheme":{"message":"Switch to light theme"},"entrypoints/main/main-meta.ts | switchToSystemPreferredColor":{"message":"Switch to system preferred color theme"},"entrypoints/main/main-meta.ts | systemPreference":{"message":"System preference"},"entrypoints/main/main-meta.ts | theme":{"message":"Theme:"},"entrypoints/main/main-meta.ts | toggleDrawer":{"message":"Toggle drawer"},"entrypoints/main/main-meta.ts | undocked":{"message":"Undocked"},"entrypoints/main/main-meta.ts | undockIntoSeparateWindow":{"message":"Undock into separate window"},"entrypoints/main/main-meta.ts | useAutomaticPanelLayout":{"message":"Use automatic panel layout"},"entrypoints/main/main-meta.ts | useHorizontalPanelLayout":{"message":"Use horizontal panel layout"},"entrypoints/main/main-meta.ts | useVerticalPanelLayout":{"message":"Use vertical panel layout"},"entrypoints/main/main-meta.ts | vertical":{"message":"vertical"},"entrypoints/main/main-meta.ts | zoomIn":{"message":"Zoom in"},"entrypoints/main/main-meta.ts | zoomOut":{"message":"Zoom out"},"entrypoints/main/MainImpl.ts | customizeAndControlDevtools":{"message":"Customize and control DevTools"},"entrypoints/main/MainImpl.ts | dockSide":{"message":"Dock side"},"entrypoints/main/MainImpl.ts | dockToBottom":{"message":"Dock to bottom"},"entrypoints/main/MainImpl.ts | dockToLeft":{"message":"Dock to left"},"entrypoints/main/MainImpl.ts | dockToRight":{"message":"Dock to right"},"entrypoints/main/MainImpl.ts | focusDebuggee":{"message":"Focus debuggee"},"entrypoints/main/MainImpl.ts | help":{"message":"Help"},"entrypoints/main/MainImpl.ts | hideConsoleDrawer":{"message":"Hide console drawer"},"entrypoints/main/MainImpl.ts | moreTools":{"message":"More tools"},"entrypoints/main/MainImpl.ts | placementOfDevtoolsRelativeToThe":{"message":"Placement of DevTools relative to the page. ({PH1} to restore last position)"},"entrypoints/main/MainImpl.ts | showConsoleDrawer":{"message":"Show console drawer"},"entrypoints/main/MainImpl.ts | undockIntoSeparateWindow":{"message":"Undock into separate window"},"entrypoints/node_app/node_app.ts | connection":{"message":"Connection"},"entrypoints/node_app/node_app.ts | networkTitle":{"message":"Node"},"entrypoints/node_app/node_app.ts | node":{"message":"node"},"entrypoints/node_app/node_app.ts | showConnection":{"message":"Show Connection"},"entrypoints/node_app/node_app.ts | showNode":{"message":"Node"},"entrypoints/node_app/NodeConnectionsPanel.ts | addConnection":{"message":"Add connection"},"entrypoints/node_app/NodeConnectionsPanel.ts | networkAddressEgLocalhost":{"message":"Network address (e.g. localhost:9229)"},"entrypoints/node_app/NodeConnectionsPanel.ts | noConnectionsSpecified":{"message":"No connections specified"},"entrypoints/node_app/NodeConnectionsPanel.ts | nodejsDebuggingGuide":{"message":"Node.js debugging guide"},"entrypoints/node_app/NodeConnectionsPanel.ts | specifyNetworkEndpointAnd":{"message":"Specify network endpoint and DevTools will connect to it automatically. Read {PH1} to learn more."},"entrypoints/node_app/NodeMain.ts | main":{"message":"Main"},"entrypoints/node_app/NodeMain.ts | nodejsS":{"message":"Node.js: {PH1}"},"entrypoints/worker_app/WorkerMain.ts | main":{"message":"Main"},"models/bindings/ContentProviderBasedProject.ts | unknownErrorLoadingFile":{"message":"Unknown error loading file"},"models/bindings/DebuggerLanguagePlugins.ts | errorInDebuggerLanguagePlugin":{"message":"Error in debugger language plugin: {PH1}"},"models/bindings/DebuggerLanguagePlugins.ts | failedToLoadDebugSymbolsFor":{"message":"[{PH1}] Failed to load debug symbols for {PH2} ({PH3})"},"models/bindings/DebuggerLanguagePlugins.ts | failedToLoadDebugSymbolsForFunction":{"message":"Missing debug symbols for function \\"{PH1}\\""},"models/bindings/DebuggerLanguagePlugins.ts | loadedDebugSymbolsForButDidnt":{"message":"[{PH1}] Loaded debug symbols for {PH2}, but didn\'t find any source files"},"models/bindings/DebuggerLanguagePlugins.ts | loadedDebugSymbolsForFound":{"message":"[{PH1}] Loaded debug symbols for {PH2}, found {PH3} source file(s)"},"models/bindings/DebuggerLanguagePlugins.ts | loadingDebugSymbolsFor":{"message":"[{PH1}] Loading debug symbols for {PH2}..."},"models/bindings/DebuggerLanguagePlugins.ts | loadingDebugSymbolsForVia":{"message":"[{PH1}] Loading debug symbols for {PH2} (via {PH3})..."},"models/bindings/DebuggerLanguagePlugins.ts | symbolFileNotFound":{"message":"Symbol file \\"{PH1}\\" not found"},"models/bindings/ResourceScriptMapping.ts | liveEditCompileFailed":{"message":"LiveEdit compile failed: {PH1}"},"models/bindings/ResourceScriptMapping.ts | liveEditFailed":{"message":"LiveEdit failed: {PH1}"},"models/emulation/DeviceModeModel.ts | devicePixelRatioMustBeANumberOr":{"message":"Device pixel ratio must be a number or blank."},"models/emulation/DeviceModeModel.ts | devicePixelRatioMustBeGreater":{"message":"Device pixel ratio must be greater than or equal to {PH1}."},"models/emulation/DeviceModeModel.ts | devicePixelRatioMustBeLessThanOr":{"message":"Device pixel ratio must be less than or equal to {PH1}."},"models/emulation/DeviceModeModel.ts | heightMustBeANumber":{"message":"Height must be a number."},"models/emulation/DeviceModeModel.ts | heightMustBeGreaterThanOrEqualTo":{"message":"Height must be greater than or equal to {PH1}."},"models/emulation/DeviceModeModel.ts | heightMustBeLessThanOrEqualToS":{"message":"Height must be less than or equal to {PH1}."},"models/emulation/DeviceModeModel.ts | widthMustBeANumber":{"message":"Width must be a number."},"models/emulation/DeviceModeModel.ts | widthMustBeGreaterThanOrEqualToS":{"message":"Width must be greater than or equal to {PH1}."},"models/emulation/DeviceModeModel.ts | widthMustBeLessThanOrEqualToS":{"message":"Width must be less than or equal to {PH1}."},"models/emulation/EmulatedDevices.ts | laptopWithHiDPIScreen":{"message":"Laptop with HiDPI screen"},"models/emulation/EmulatedDevices.ts | laptopWithMDPIScreen":{"message":"Laptop with MDPI screen"},"models/emulation/EmulatedDevices.ts | laptopWithTouch":{"message":"Laptop with touch"},"models/har/Writer.ts | collectingContent":{"message":"Collecting content…"},"models/har/Writer.ts | writingFile":{"message":"Writing file…"},"models/issues_manager/ClientHintIssue.ts | clientHintsInfrastructure":{"message":"Client Hints Infrastructure"},"models/issues_manager/ContentSecurityPolicyIssue.ts | contentSecurityPolicyEval":{"message":"Content Security Policy - Eval"},"models/issues_manager/ContentSecurityPolicyIssue.ts | contentSecurityPolicyInlineCode":{"message":"Content Security Policy - Inline Code"},"models/issues_manager/ContentSecurityPolicyIssue.ts | contentSecurityPolicySource":{"message":"Content Security Policy - Source Allowlists"},"models/issues_manager/ContentSecurityPolicyIssue.ts | trustedTypesFixViolations":{"message":"Trusted Types - Fix violations"},"models/issues_manager/ContentSecurityPolicyIssue.ts | trustedTypesPolicyViolation":{"message":"Trusted Types - Policy violation"},"models/issues_manager/CookieIssue.ts | anInsecure":{"message":"an insecure"},"models/issues_manager/CookieIssue.ts | aSecure":{"message":"a secure"},"models/issues_manager/CookieIssue.ts | firstPartySetsExplained":{"message":"First-Party Sets and the SameParty attribute"},"models/issues_manager/CookieIssue.ts | howSchemefulSamesiteWorks":{"message":"How Schemeful Same-Site Works"},"models/issues_manager/CookieIssue.ts | samesiteCookiesExplained":{"message":"SameSite cookies explained"},"models/issues_manager/CorsIssue.ts | CORS":{"message":"Cross-Origin Resource Sharing (CORS)"},"models/issues_manager/CorsIssue.ts | corsPrivateNetworkAccess":{"message":"Private Network Access"},"models/issues_manager/CrossOriginEmbedderPolicyIssue.ts | coopAndCoep":{"message":"COOP and COEP"},"models/issues_manager/CrossOriginEmbedderPolicyIssue.ts | samesiteAndSameorigin":{"message":"Same-Site and Same-Origin"},"models/issues_manager/FederatedAuthRequestIssue.ts | fedCm":{"message":"Federated Credential Management API"},"models/issues_manager/GenericIssue.ts | crossOriginPortalPostMessage":{"message":"Portals - Same-origin communication channels"},"models/issues_manager/HeavyAdIssue.ts | handlingHeavyAdInterventions":{"message":"Handling Heavy Ad Interventions"},"models/issues_manager/Issue.ts | breakingChangeIssue":{"message":"A breaking change issue: the page may stop working in an upcoming version of Chrome"},"models/issues_manager/Issue.ts | breakingChanges":{"message":"Breaking Changes"},"models/issues_manager/Issue.ts | improvementIssue":{"message":"An improvement issue: there is an opportunity to improve the page"},"models/issues_manager/Issue.ts | improvements":{"message":"Improvements"},"models/issues_manager/Issue.ts | pageErrorIssue":{"message":"A page error issue: the page is not working correctly"},"models/issues_manager/Issue.ts | pageErrors":{"message":"Page Errors"},"models/issues_manager/LowTextContrastIssue.ts | colorAndContrastAccessibility":{"message":"Color and contrast accessibility"},"models/issues_manager/MixedContentIssue.ts | preventingMixedContent":{"message":"Preventing mixed content"},"models/issues_manager/NavigatorUserAgentIssue.ts | userAgentReduction":{"message":"User-Agent String Reduction"},"models/issues_manager/QuirksModeIssue.ts | documentCompatibilityMode":{"message":"Document compatibility mode"},"models/issues_manager/SharedArrayBufferIssue.ts | enablingSharedArrayBuffer":{"message":"Enabling SharedArrayBuffer"},"models/issues_manager/TrustedWebActivityIssue.ts | changesToQualityCriteriaForPwas":{"message":"Changes to quality criteria for PWAs using Trusted Web Activity"},"models/logs/logs-meta.ts | clear":{"message":"clear"},"models/logs/logs-meta.ts | doNotPreserveLogOnPageReload":{"message":"Do not preserve log on page reload / navigation"},"models/logs/logs-meta.ts | preserve":{"message":"preserve"},"models/logs/logs-meta.ts | preserveLog":{"message":"Preserve log"},"models/logs/logs-meta.ts | preserveLogOnPageReload":{"message":"Preserve log on page reload / navigation"},"models/logs/logs-meta.ts | recordNetworkLog":{"message":"Record network log"},"models/logs/logs-meta.ts | reset":{"message":"reset"},"models/logs/NetworkLog.ts | anonymous":{"message":"<anonymous>"},"models/persistence/EditFileSystemView.ts | add":{"message":"Add"},"models/persistence/EditFileSystemView.ts | enterAPath":{"message":"Enter a path"},"models/persistence/EditFileSystemView.ts | enterAUniquePath":{"message":"Enter a unique path"},"models/persistence/EditFileSystemView.ts | excludedFolders":{"message":"Excluded folders"},"models/persistence/EditFileSystemView.ts | folderPath":{"message":"Folder path"},"models/persistence/EditFileSystemView.ts | none":{"message":"None"},"models/persistence/EditFileSystemView.ts | sViaDevtools":{"message":"{PH1} (via .devtools)"},"models/persistence/IsolatedFileSystem.ts | blobCouldNotBeLoaded":{"message":"Blob could not be loaded."},"models/persistence/IsolatedFileSystem.ts | cantReadFileSS":{"message":"Can\'t read file: {PH1}: {PH2}"},"models/persistence/IsolatedFileSystem.ts | fileSystemErrorS":{"message":"File system error: {PH1}"},"models/persistence/IsolatedFileSystem.ts | linkedToS":{"message":"Linked to {PH1}"},"models/persistence/IsolatedFileSystem.ts | unknownErrorReadingFileS":{"message":"Unknown error reading file: {PH1}"},"models/persistence/IsolatedFileSystemManager.ts | unableToAddFilesystemS":{"message":"Unable to add filesystem: {PH1}"},"models/persistence/persistence-meta.ts | disableOverrideNetworkRequests":{"message":"Disable override network requests"},"models/persistence/persistence-meta.ts | enableLocalOverrides":{"message":"Enable Local Overrides"},"models/persistence/persistence-meta.ts | enableOverrideNetworkRequests":{"message":"Enable override network requests"},"models/persistence/persistence-meta.ts | interception":{"message":"interception"},"models/persistence/persistence-meta.ts | network":{"message":"network"},"models/persistence/persistence-meta.ts | override":{"message":"override"},"models/persistence/persistence-meta.ts | request":{"message":"request"},"models/persistence/persistence-meta.ts | rewrite":{"message":"rewrite"},"models/persistence/persistence-meta.ts | showWorkspace":{"message":"Show Workspace"},"models/persistence/persistence-meta.ts | workspace":{"message":"Workspace"},"models/persistence/PersistenceActions.ts | openInContainingFolder":{"message":"Open in containing folder"},"models/persistence/PersistenceActions.ts | saveAs":{"message":"Save as..."},"models/persistence/PersistenceActions.ts | saveForOverrides":{"message":"Save for overrides"},"models/persistence/PersistenceActions.ts | saveImage":{"message":"Save image"},"models/persistence/PersistenceUtils.ts | linkedToS":{"message":"Linked to {PH1}"},"models/persistence/PersistenceUtils.ts | linkedToSourceMapS":{"message":"Linked to source map: {PH1}"},"models/persistence/PlatformFileSystem.ts | unableToReadFilesWithThis":{"message":"PlatformFileSystem cannot read files."},"models/persistence/WorkspaceSettingsTab.ts | addFolder":{"message":"Add folder…"},"models/persistence/WorkspaceSettingsTab.ts | folderExcludePattern":{"message":"Folder exclude pattern"},"models/persistence/WorkspaceSettingsTab.ts | mappingsAreInferredAutomatically":{"message":"Mappings are inferred automatically."},"models/persistence/WorkspaceSettingsTab.ts | remove":{"message":"Remove"},"models/persistence/WorkspaceSettingsTab.ts | workspace":{"message":"Workspace"},"models/timeline_model/TimelineIRModel.ts | twoFlingsAtTheSameTimeSVsS":{"message":"Two flings at the same time? {PH1} vs {PH2}"},"models/timeline_model/TimelineIRModel.ts | twoTouchesAtTheSameTimeSVsS":{"message":"Two touches at the same time? {PH1} vs {PH2}"},"models/timeline_model/TimelineJSProfile.ts | threadS":{"message":"Thread {PH1}"},"models/timeline_model/TimelineModel.ts | dedicatedWorker":{"message":"Dedicated Worker"},"models/timeline_model/TimelineModel.ts | threadS":{"message":"Thread {PH1}"},"models/timeline_model/TimelineModel.ts | workerS":{"message":"Worker — {PH1}"},"models/timeline_model/TimelineModel.ts | workerSS":{"message":"Worker: {PH1} — {PH2}"},"models/workspace/UISourceCode.ts | index":{"message":"(index)"},"models/workspace/UISourceCode.ts | thisFileWasChangedExternally":{"message":"This file was changed externally. Would you like to reload it?"},"panels/accessibility/accessibility-meta.ts | accessibility":{"message":"Accessibility"},"panels/accessibility/accessibility-meta.ts | shoAccessibility":{"message":"Show Accessibility"},"panels/accessibility/AccessibilityNodeView.ts | accessibilityNodeNotExposed":{"message":"Accessibility node not exposed"},"panels/accessibility/AccessibilityNodeView.ts | ancestorChildrenAreAll":{"message":"Ancestor\'s children are all presentational: "},"panels/accessibility/AccessibilityNodeView.ts | computedProperties":{"message":"Computed Properties"},"panels/accessibility/AccessibilityNodeView.ts | elementHasEmptyAltText":{"message":"Element has empty alt text."},"panels/accessibility/AccessibilityNodeView.ts | elementHasPlaceholder":{"message":"Element has {PH1}."},"panels/accessibility/AccessibilityNodeView.ts | elementIsHiddenBy":{"message":"Element is hidden by active modal dialog: "},"panels/accessibility/AccessibilityNodeView.ts | elementIsInAnInertSubTree":{"message":"Element is in an inert subtree from "},"panels/accessibility/AccessibilityNodeView.ts | elementIsInert":{"message":"Element is inert."},"panels/accessibility/AccessibilityNodeView.ts | elementIsNotRendered":{"message":"Element is not rendered."},"panels/accessibility/AccessibilityNodeView.ts | elementIsNotVisible":{"message":"Element is not visible."},"panels/accessibility/AccessibilityNodeView.ts | elementIsPlaceholder":{"message":"Element is {PH1}."},"panels/accessibility/AccessibilityNodeView.ts | elementIsPresentational":{"message":"Element is presentational."},"panels/accessibility/AccessibilityNodeView.ts | elementNotInteresting":{"message":"Element not interesting for accessibility."},"panels/accessibility/AccessibilityNodeView.ts | elementsInheritsPresentational":{"message":"Element inherits presentational role from "},"panels/accessibility/AccessibilityNodeView.ts | invalidSource":{"message":"Invalid source."},"panels/accessibility/AccessibilityNodeView.ts | labelFor":{"message":"Label for "},"panels/accessibility/AccessibilityNodeView.ts | noAccessibilityNode":{"message":"No accessibility node"},"panels/accessibility/AccessibilityNodeView.ts | noNodeWithThisId":{"message":"No node with this ID."},"panels/accessibility/AccessibilityNodeView.ts | noTextContent":{"message":"No text content."},"panels/accessibility/AccessibilityNodeView.ts | notSpecified":{"message":"Not specified"},"panels/accessibility/AccessibilityNodeView.ts | partOfLabelElement":{"message":"Part of label element: "},"panels/accessibility/AccessibilityNodeView.ts | placeholderIsPlaceholderOnAncestor":{"message":"{PH1} is {PH2} on ancestor: "},"panels/accessibility/AccessibilityStrings.ts | activeDescendant":{"message":"Active descendant"},"panels/accessibility/AccessibilityStrings.ts | aHumanreadableVersionOfTheValue":{"message":"A human-readable version of the value of a range widget (where necessary)."},"panels/accessibility/AccessibilityStrings.ts | atomicLiveRegions":{"message":"Atomic (live regions)"},"panels/accessibility/AccessibilityStrings.ts | busyLiveRegions":{"message":"Busy (live regions)"},"panels/accessibility/AccessibilityStrings.ts | canSetValue":{"message":"Can set value"},"panels/accessibility/AccessibilityStrings.ts | checked":{"message":"Checked"},"panels/accessibility/AccessibilityStrings.ts | contents":{"message":"Contents"},"panels/accessibility/AccessibilityStrings.ts | controls":{"message":"Controls"},"panels/accessibility/AccessibilityStrings.ts | describedBy":{"message":"Described by"},"panels/accessibility/AccessibilityStrings.ts | description":{"message":"Description"},"panels/accessibility/AccessibilityStrings.ts | disabled":{"message":"Disabled"},"panels/accessibility/AccessibilityStrings.ts | editable":{"message":"Editable"},"panels/accessibility/AccessibilityStrings.ts | elementOrElementsWhichFormThe":{"message":"Element or elements which form the description of this element."},"panels/accessibility/AccessibilityStrings.ts | elementOrElementsWhichMayFormThe":{"message":"Element or elements which may form the name of this element."},"panels/accessibility/AccessibilityStrings.ts | elementOrElementsWhichShouldBe":{"message":"Element or elements which should be considered descendants of this element, despite not being descendants in the DOM."},"panels/accessibility/AccessibilityStrings.ts | elementOrElementsWhoseContentOr":{"message":"Element or elements whose content or presence is/are controlled by this widget."},"panels/accessibility/AccessibilityStrings.ts | elementToWhichTheUserMayChooseTo":{"message":"Element to which the user may choose to navigate after this one, instead of the next element in the DOM order."},"panels/accessibility/AccessibilityStrings.ts | expanded":{"message":"Expanded"},"panels/accessibility/AccessibilityStrings.ts | focusable":{"message":"Focusable"},"panels/accessibility/AccessibilityStrings.ts | focused":{"message":"Focused"},"panels/accessibility/AccessibilityStrings.ts | forARangeWidgetTheMaximumAllowed":{"message":"For a range widget, the maximum allowed value."},"panels/accessibility/AccessibilityStrings.ts | forARangeWidgetTheMinimumAllowed":{"message":"For a range widget, the minimum allowed value."},"panels/accessibility/AccessibilityStrings.ts | fromAttribute":{"message":"From attribute"},"panels/accessibility/AccessibilityStrings.ts | fromCaption":{"message":"From caption"},"panels/accessibility/AccessibilityStrings.ts | fromDescription":{"message":"From description"},"panels/accessibility/AccessibilityStrings.ts | fromLabel":{"message":"From label"},"panels/accessibility/AccessibilityStrings.ts | fromLabelFor":{"message":"From label (for= attribute)"},"panels/accessibility/AccessibilityStrings.ts | fromLabelWrapped":{"message":"From label (wrapped)"},"panels/accessibility/AccessibilityStrings.ts | fromLegend":{"message":"From legend"},"panels/accessibility/AccessibilityStrings.ts | fromNativeHtml":{"message":"From native HTML"},"panels/accessibility/AccessibilityStrings.ts | fromPlaceholderAttribute":{"message":"From placeholder attribute"},"panels/accessibility/AccessibilityStrings.ts | fromRubyAnnotation":{"message":"From ruby annotation"},"panels/accessibility/AccessibilityStrings.ts | fromStyle":{"message":"From style"},"panels/accessibility/AccessibilityStrings.ts | fromTitle":{"message":"From title"},"panels/accessibility/AccessibilityStrings.ts | hasAutocomplete":{"message":"Has autocomplete"},"panels/accessibility/AccessibilityStrings.ts | hasPopup":{"message":"Has popup"},"panels/accessibility/AccessibilityStrings.ts | help":{"message":"Help"},"panels/accessibility/AccessibilityStrings.ts | ifAndHowThisElementCanBeEdited":{"message":"If and how this element can be edited."},"panels/accessibility/AccessibilityStrings.ts | ifThisElementMayReceiveLive":{"message":"If this element may receive live updates, whether the entire live region should be presented to the user on changes, or only changed nodes."},"panels/accessibility/AccessibilityStrings.ts | ifThisElementMayReceiveLiveUpdates":{"message":"If this element may receive live updates, what type of updates should trigger a notification."},"panels/accessibility/AccessibilityStrings.ts | ifThisElementMayReceiveLiveUpdatesThe":{"message":"If this element may receive live updates, the root element of the containing live region."},"panels/accessibility/AccessibilityStrings.ts | ifTrueThisElementCanReceiveFocus":{"message":"If true, this element can receive focus."},"panels/accessibility/AccessibilityStrings.ts | ifTrueThisElementCurrentlyCannot":{"message":"If true, this element currently cannot be interacted with."},"panels/accessibility/AccessibilityStrings.ts | ifTrueThisElementCurrentlyHas":{"message":"If true, this element currently has focus."},"panels/accessibility/AccessibilityStrings.ts | ifTrueThisElementMayBeInteracted":{"message":"If true, this element may be interacted with, but its value cannot be changed."},"panels/accessibility/AccessibilityStrings.ts | ifTrueThisElementsUserentered":{"message":"If true, this element\'s user-entered value does not conform to validation requirement."},"panels/accessibility/AccessibilityStrings.ts | implicit":{"message":"Implicit"},"panels/accessibility/AccessibilityStrings.ts | implicitValue":{"message":"Implicit value."},"panels/accessibility/AccessibilityStrings.ts | indicatesThePurposeOfThisElement":{"message":"Indicates the purpose of this element, such as a user interface idiom for a widget, or structural role within a document."},"panels/accessibility/AccessibilityStrings.ts | invalidUserEntry":{"message":"Invalid user entry"},"panels/accessibility/AccessibilityStrings.ts | labeledBy":{"message":"Labeled by"},"panels/accessibility/AccessibilityStrings.ts | level":{"message":"Level"},"panels/accessibility/AccessibilityStrings.ts | liveRegion":{"message":"Live region"},"panels/accessibility/AccessibilityStrings.ts | liveRegionRoot":{"message":"Live region root"},"panels/accessibility/AccessibilityStrings.ts | maximumValue":{"message":"Maximum value"},"panels/accessibility/AccessibilityStrings.ts | minimumValue":{"message":"Minimum value"},"panels/accessibility/AccessibilityStrings.ts | multiline":{"message":"Multi-line"},"panels/accessibility/AccessibilityStrings.ts | multiselectable":{"message":"Multi-selectable"},"panels/accessibility/AccessibilityStrings.ts | orientation":{"message":"Orientation"},"panels/accessibility/AccessibilityStrings.ts | pressed":{"message":"Pressed"},"panels/accessibility/AccessibilityStrings.ts | readonlyString":{"message":"Read-only"},"panels/accessibility/AccessibilityStrings.ts | relatedElement":{"message":"Related element"},"panels/accessibility/AccessibilityStrings.ts | relevantLiveRegions":{"message":"Relevant (live regions)"},"panels/accessibility/AccessibilityStrings.ts | requiredString":{"message":"Required"},"panels/accessibility/AccessibilityStrings.ts | role":{"message":"Role"},"panels/accessibility/AccessibilityStrings.ts | selectedString":{"message":"Selected"},"panels/accessibility/AccessibilityStrings.ts | theAccessibleDescriptionForThis":{"message":"The accessible description for this element."},"panels/accessibility/AccessibilityStrings.ts | theComputedHelpTextForThis":{"message":"The computed help text for this element."},"panels/accessibility/AccessibilityStrings.ts | theComputedNameOfThisElement":{"message":"The computed name of this element."},"panels/accessibility/AccessibilityStrings.ts | theDescendantOfThisElementWhich":{"message":"The descendant of this element which is active; i.e. the element to which focus should be delegated."},"panels/accessibility/AccessibilityStrings.ts | theHierarchicalLevelOfThis":{"message":"The hierarchical level of this element."},"panels/accessibility/AccessibilityStrings.ts | theValueOfThisElementThisMayBe":{"message":"The value of this element; this may be user-provided or developer-provided, depending on the element."},"panels/accessibility/AccessibilityStrings.ts | value":{"message":"Value"},"panels/accessibility/AccessibilityStrings.ts | valueDescription":{"message":"Value description"},"panels/accessibility/AccessibilityStrings.ts | valueFromAttribute":{"message":"Value from attribute."},"panels/accessibility/AccessibilityStrings.ts | valueFromDescriptionElement":{"message":"Value from description element."},"panels/accessibility/AccessibilityStrings.ts | valueFromElementContents":{"message":"Value from element contents."},"panels/accessibility/AccessibilityStrings.ts | valueFromFigcaptionElement":{"message":"Value from figcaption element."},"panels/accessibility/AccessibilityStrings.ts | valueFromLabelElement":{"message":"Value from label element."},"panels/accessibility/AccessibilityStrings.ts | valueFromLabelElementWithFor":{"message":"Value from label element with for= attribute."},"panels/accessibility/AccessibilityStrings.ts | valueFromLabelElementWrapped":{"message":"Value from a wrapping label element."},"panels/accessibility/AccessibilityStrings.ts | valueFromLegendElement":{"message":"Value from legend element."},"panels/accessibility/AccessibilityStrings.ts | valueFromNativeHtmlRuby":{"message":"Value from plain HTML ruby annotation."},"panels/accessibility/AccessibilityStrings.ts | valueFromNativeHtmlUnknownSource":{"message":"Value from native HTML (unknown source)."},"panels/accessibility/AccessibilityStrings.ts | valueFromPlaceholderAttribute":{"message":"Value from placeholder attribute."},"panels/accessibility/AccessibilityStrings.ts | valueFromRelatedElement":{"message":"Value from related element."},"panels/accessibility/AccessibilityStrings.ts | valueFromStyle":{"message":"Value from style."},"panels/accessibility/AccessibilityStrings.ts | valueFromTableCaption":{"message":"Value from table caption."},"panels/accessibility/AccessibilityStrings.ts | valueFromTitleAttribute":{"message":"Value from title attribute."},"panels/accessibility/AccessibilityStrings.ts | whetherAndWhatPriorityOfLive":{"message":"Whether and what priority of live updates may be expected for this element."},"panels/accessibility/AccessibilityStrings.ts | whetherAndWhatTypeOfAutocomplete":{"message":"Whether and what type of autocomplete suggestions are currently provided by this element."},"panels/accessibility/AccessibilityStrings.ts | whetherAUserMaySelectMoreThanOne":{"message":"Whether a user may select more than one option from this widget."},"panels/accessibility/AccessibilityStrings.ts | whetherTheOptionRepresentedBy":{"message":"Whether the option represented by this element is currently selected."},"panels/accessibility/AccessibilityStrings.ts | whetherTheValueOfThisElementCan":{"message":"Whether the value of this element can be set."},"panels/accessibility/AccessibilityStrings.ts | whetherThisCheckboxRadioButtonOr":{"message":"Whether this checkbox, radio button or tree item is checked, unchecked, or mixed (e.g. has both checked and un-checked children)."},"panels/accessibility/AccessibilityStrings.ts | whetherThisElementHasCausedSome":{"message":"Whether this element has caused some kind of pop-up (such as a menu) to appear."},"panels/accessibility/AccessibilityStrings.ts | whetherThisElementIsARequired":{"message":"Whether this element is a required field in a form."},"panels/accessibility/AccessibilityStrings.ts | whetherThisElementOrAnother":{"message":"Whether this element, or another grouping element it controls, is expanded."},"panels/accessibility/AccessibilityStrings.ts | whetherThisElementOrItsSubtree":{"message":"Whether this element or its subtree are currently being updated (and thus may be in an inconsistent state)."},"panels/accessibility/AccessibilityStrings.ts | whetherThisLinearElements":{"message":"Whether this linear element\'s orientation is horizontal or vertical."},"panels/accessibility/AccessibilityStrings.ts | whetherThisTextBoxMayHaveMore":{"message":"Whether this text box may have more than one line."},"panels/accessibility/AccessibilityStrings.ts | whetherThisToggleButtonIs":{"message":"Whether this toggle button is currently in a pressed state."},"panels/accessibility/ARIAAttributesView.ts | ariaAttributes":{"message":"ARIA Attributes"},"panels/accessibility/ARIAAttributesView.ts | noAriaAttributes":{"message":"No ARIA attributes"},"panels/accessibility/AXBreadcrumbsPane.ts | accessibilityTree":{"message":"Accessibility Tree"},"panels/accessibility/AXBreadcrumbsPane.ts | fullTreeExperimentDescription":{"message":"The accessibility tree moved to the top right corner of the DOM tree."},"panels/accessibility/AXBreadcrumbsPane.ts | fullTreeExperimentName":{"message":"Enable full-page accessibility tree"},"panels/accessibility/AXBreadcrumbsPane.ts | ignored":{"message":"Ignored"},"panels/accessibility/AXBreadcrumbsPane.ts | reloadRequired":{"message":"Reload required before the change takes effect."},"panels/accessibility/AXBreadcrumbsPane.ts | scrollIntoView":{"message":"Scroll into view"},"panels/accessibility/SourceOrderView.ts | noSourceOrderInformation":{"message":"No source order information available"},"panels/accessibility/SourceOrderView.ts | showSourceOrder":{"message":"Show source order"},"panels/accessibility/SourceOrderView.ts | sourceOrderViewer":{"message":"Source Order Viewer"},"panels/accessibility/SourceOrderView.ts | thereMayBeADelayInDisplaying":{"message":"There may be a delay in displaying source order for elements with many children"},"panels/animation/animation-meta.ts | animations":{"message":"Animations"},"panels/animation/animation-meta.ts | showAnimations":{"message":"Show Animations"},"panels/animation/AnimationTimeline.ts | animationPreviews":{"message":"Animation previews"},"panels/animation/AnimationTimeline.ts | animationPreviewS":{"message":"Animation Preview {PH1}"},"panels/animation/AnimationTimeline.ts | clearAll":{"message":"Clear all"},"panels/animation/AnimationTimeline.ts | pause":{"message":"Pause"},"panels/animation/AnimationTimeline.ts | pauseAll":{"message":"Pause all"},"panels/animation/AnimationTimeline.ts | pauseTimeline":{"message":"Pause timeline"},"panels/animation/AnimationTimeline.ts | playbackRatePlaceholder":{"message":"{PH1}%"},"panels/animation/AnimationTimeline.ts | playbackRates":{"message":"Playback rates"},"panels/animation/AnimationTimeline.ts | playTimeline":{"message":"Play timeline"},"panels/animation/AnimationTimeline.ts | replayTimeline":{"message":"Replay timeline"},"panels/animation/AnimationTimeline.ts | resumeAll":{"message":"Resume all"},"panels/animation/AnimationTimeline.ts | selectAnEffectAboveToInspectAnd":{"message":"Select an effect above to inspect and modify."},"panels/animation/AnimationTimeline.ts | setSpeedToS":{"message":"Set speed to {PH1}"},"panels/animation/AnimationTimeline.ts | waitingForAnimations":{"message":"Waiting for animations..."},"panels/animation/AnimationUI.ts | animationEndpointSlider":{"message":"Animation Endpoint slider"},"panels/animation/AnimationUI.ts | animationKeyframeSlider":{"message":"Animation Keyframe slider"},"panels/animation/AnimationUI.ts | sSlider":{"message":"{PH1} slider"},"panels/application/application-meta.ts | application":{"message":"Application"},"panels/application/application-meta.ts | clearSiteData":{"message":"Clear site data"},"panels/application/application-meta.ts | clearSiteDataIncludingThirdparty":{"message":"Clear site data (including third-party cookies)"},"panels/application/application-meta.ts | pwa":{"message":"pwa"},"panels/application/application-meta.ts | showApplication":{"message":"Show Application"},"panels/application/application-meta.ts | startRecordingEvents":{"message":"Start recording events"},"panels/application/application-meta.ts | stopRecordingEvents":{"message":"Stop recording events"},"panels/application/ApplicationPanelCacheSection.ts | backForwardCache":{"message":"Back/forward cache"},"panels/application/ApplicationPanelCacheSection.ts | cacheStorage":{"message":"Cache Storage"},"panels/application/ApplicationPanelCacheSection.ts | delete":{"message":"Delete"},"panels/application/ApplicationPanelCacheSection.ts | refreshCaches":{"message":"Refresh Caches"},"panels/application/ApplicationPanelSidebar.ts | application":{"message":"Application"},"panels/application/ApplicationPanelSidebar.ts | backgroundServices":{"message":"Background Services"},"panels/application/ApplicationPanelSidebar.ts | cache":{"message":"Cache"},"panels/application/ApplicationPanelSidebar.ts | clear":{"message":"Clear"},"panels/application/ApplicationPanelSidebar.ts | cookies":{"message":"Cookies"},"panels/application/ApplicationPanelSidebar.ts | cookiesUsedByFramesFromS":{"message":"Cookies used by frames from {PH1}"},"panels/application/ApplicationPanelSidebar.ts | documentNotAvailable":{"message":"Document not available"},"panels/application/ApplicationPanelSidebar.ts | frames":{"message":"Frames"},"panels/application/ApplicationPanelSidebar.ts | indexeddb":{"message":"IndexedDB"},"panels/application/ApplicationPanelSidebar.ts | keyPathS":{"message":"Key path: {PH1}"},"panels/application/ApplicationPanelSidebar.ts | localFiles":{"message":"Local Files"},"panels/application/ApplicationPanelSidebar.ts | localStorage":{"message":"Local Storage"},"panels/application/ApplicationPanelSidebar.ts | manifest":{"message":"Manifest"},"panels/application/ApplicationPanelSidebar.ts | openedWindows":{"message":"Opened Windows"},"panels/application/ApplicationPanelSidebar.ts | refreshIndexeddb":{"message":"Refresh IndexedDB"},"panels/application/ApplicationPanelSidebar.ts | sessionStorage":{"message":"Session Storage"},"panels/application/ApplicationPanelSidebar.ts | storage":{"message":"Storage"},"panels/application/ApplicationPanelSidebar.ts | theContentOfThisDocumentHasBeen":{"message":"The content of this document has been generated dynamically via \'document.write()\'."},"panels/application/ApplicationPanelSidebar.ts | versionS":{"message":"Version: {PH1}"},"panels/application/ApplicationPanelSidebar.ts | versionSEmpty":{"message":"Version: {PH1} (empty)"},"panels/application/ApplicationPanelSidebar.ts | webSql":{"message":"Web SQL"},"panels/application/ApplicationPanelSidebar.ts | webWorkers":{"message":"Web Workers"},"panels/application/ApplicationPanelSidebar.ts | windowWithoutTitle":{"message":"Window without title"},"panels/application/ApplicationPanelSidebar.ts | worker":{"message":"worker"},"panels/application/AppManifestView.ts | actualHeightSpxOfSSDoesNotMatch":{"message":"Actual height ({PH1}px) of {PH2} {PH3} does not match specified height ({PH4}px)"},"panels/application/AppManifestView.ts | actualSizeSspxOfSSDoesNotMatch":{"message":"Actual size ({PH1}×{PH2})px of {PH3} {PH4} does not match specified size ({PH5}×{PH6}px)"},"panels/application/AppManifestView.ts | actualWidthSpxOfSSDoesNotMatch":{"message":"Actual width ({PH1}px) of {PH2} {PH3} does not match specified width ({PH4}px)"},"panels/application/AppManifestView.ts | appIdExplainer":{"message":"This is used by the browser to know whether the manifest should be updating an existing application, or whether it refers to a new web app that can be installed."},"panels/application/AppManifestView.ts | appIdNote":{"message":"{PH1} {PH2} is not specified in the manifest, {PH3} is used instead. To specify an App Id that matches the current identity, set the {PH4} field to {PH5} {PH6}."},"panels/application/AppManifestView.ts | appManifest":{"message":"App Manifest"},"panels/application/AppManifestView.ts | aUrlInTheManifestContainsA":{"message":"A URL in the manifest contains a username, password, or port"},"panels/application/AppManifestView.ts | avoidPurposeAnyAndMaskable":{"message":"Declaring an icon with purpose: \\"any maskable\\" is discouraged. It is likely to look incorrect on some platforms due to too much or too little padding."},"panels/application/AppManifestView.ts | backgroundColor":{"message":"Background color"},"panels/application/AppManifestView.ts | computedAppId":{"message":"Computed App Id"},"panels/application/AppManifestView.ts | copyToClipboard":{"message":"Copy to clipboard"},"panels/application/AppManifestView.ts | couldNotCheckServiceWorker":{"message":"Could not check service worker without a \'start_url\' field in the manifest"},"panels/application/AppManifestView.ts | couldNotDownloadARequiredIcon":{"message":"Could not download a required icon from the manifest"},"panels/application/AppManifestView.ts | darkBackgroundColor":{"message":"Dark background color"},"panels/application/AppManifestView.ts | darkThemeColor":{"message":"Dark theme color"},"panels/application/AppManifestView.ts | description":{"message":"Description"},"panels/application/AppManifestView.ts | descriptionMayBeTruncated":{"message":"Description may be truncated."},"panels/application/AppManifestView.ts | display":{"message":"Display"},"panels/application/AppManifestView.ts | documentationOnMaskableIcons":{"message":"documentation on maskable icons"},"panels/application/AppManifestView.ts | downloadedIconWasEmptyOr":{"message":"Downloaded icon was empty or corrupted"},"panels/application/AppManifestView.ts | errorsAndWarnings":{"message":"Errors and warnings"},"panels/application/AppManifestView.ts | icon":{"message":"Icon"},"panels/application/AppManifestView.ts | icons":{"message":"Icons"},"panels/application/AppManifestView.ts | identity":{"message":"Identity"},"panels/application/AppManifestView.ts | imageFromS":{"message":"Image from {PH1}"},"panels/application/AppManifestView.ts | installability":{"message":"Installability"},"panels/application/AppManifestView.ts | learnMore":{"message":"Learn more"},"panels/application/AppManifestView.ts | manifestContainsDisplayoverride":{"message":"Manifest contains \'display_override\' field, and the first supported display mode must be one of \'standalone\', \'fullscreen\', or \'minimal-ui\'"},"panels/application/AppManifestView.ts | manifestCouldNotBeFetchedIsEmpty":{"message":"Manifest could not be fetched, is empty, or could not be parsed"},"panels/application/AppManifestView.ts | manifestDisplayPropertyMustBeOne":{"message":"Manifest \'display\' property must be one of \'standalone\', \'fullscreen\', or \'minimal-ui\'"},"panels/application/AppManifestView.ts | manifestDoesNotContainANameOr":{"message":"Manifest does not contain a \'name\' or \'short_name\' field"},"panels/application/AppManifestView.ts | manifestDoesNotContainASuitable":{"message":"Manifest does not contain a suitable icon - PNG, SVG or WebP format of at least {PH1}px is required, the sizes attribute must be set, and the purpose attribute, if set, must include \\"any\\"."},"panels/application/AppManifestView.ts | manifestSpecifies":{"message":"Manifest specifies prefer_related_applications: true"},"panels/application/AppManifestView.ts | manifestStartUrlIsNotValid":{"message":"Manifest start URL is not valid"},"panels/application/AppManifestView.ts | name":{"message":"Name"},"panels/application/AppManifestView.ts | needHelpReadOurS":{"message":"Need help? Read {PH1}."},"panels/application/AppManifestView.ts | newNoteUrl":{"message":"New note URL"},"panels/application/AppManifestView.ts | noManifestDetected":{"message":"No manifest detected"},"panels/application/AppManifestView.ts | noMatchingServiceWorkerDetected":{"message":"No matching service worker detected. You may need to reload the page, or check that the scope of the service worker for the current page encloses the scope and start URL from the manifest."},"panels/application/AppManifestView.ts | noPlayStoreIdProvided":{"message":"No Play store ID provided"},"panels/application/AppManifestView.ts | noSuppliedIconIsAtLeastSpxSquare":{"message":"No supplied icon is at least {PH1} pixels square in PNG, SVG or WebP format, with the purpose attribute unset or set to \\"any\\"."},"panels/application/AppManifestView.ts | note":{"message":"Note:"},"panels/application/AppManifestView.ts | orientation":{"message":"Orientation"},"panels/application/AppManifestView.ts | pageDoesNotWorkOffline":{"message":"Page does not work offline"},"panels/application/AppManifestView.ts | pageDoesNotWorkOfflineThePage":{"message":"Page does not work offline. Starting in Chrome 93, the installability criteria are changing, and this site will not be installable. See {PH1} for more information."},"panels/application/AppManifestView.ts | pageHasNoManifestLinkUrl":{"message":"Page has no manifest <link> URL"},"panels/application/AppManifestView.ts | pageIsLoadedInAnIncognitoWindow":{"message":"Page is loaded in an incognito window"},"panels/application/AppManifestView.ts | pageIsNotLoadedInTheMainFrame":{"message":"Page is not loaded in the main frame"},"panels/application/AppManifestView.ts | pageIsNotServedFromASecureOrigin":{"message":"Page is not served from a secure origin"},"panels/application/AppManifestView.ts | preferrelatedapplicationsIsOnly":{"message":"prefer_related_applications is only supported on Chrome Beta and Stable channels on Android."},"panels/application/AppManifestView.ts | presentation":{"message":"Presentation"},"panels/application/AppManifestView.ts | primaryIconasUsedByChrome":{"message":"Primary icon as used by Chrome"},"panels/application/AppManifestView.ts | primaryManifestIconFromS":{"message":"Primary manifest icon from {PH1}"},"panels/application/AppManifestView.ts | screenshot":{"message":"Screenshot"},"panels/application/AppManifestView.ts | screenshotPixelSize":{"message":"Screenshot {url} should specify a pixel size [width]x[height] instead of \\"any\\" as first size."},"panels/application/AppManifestView.ts | screenshotS":{"message":"Screenshot #{PH1}"},"panels/application/AppManifestView.ts | shortcutS":{"message":"Shortcut #{PH1}"},"panels/application/AppManifestView.ts | shortcutSShouldIncludeAXPixel":{"message":"Shortcut #{PH1} should include a 96x96 pixel icon"},"panels/application/AppManifestView.ts | shortName":{"message":"Short name"},"panels/application/AppManifestView.ts | showOnlyTheMinimumSafeAreaFor":{"message":"Show only the minimum safe area for maskable icons"},"panels/application/AppManifestView.ts | sSDoesNotSpecifyItsSizeInThe":{"message":"{PH1} {PH2} does not specify its size in the manifest"},"panels/application/AppManifestView.ts | sSFailedToLoad":{"message":"{PH1} {PH2} failed to load"},"panels/application/AppManifestView.ts | sSHeightDoesNotComplyWithRatioRequirement":{"message":"{PH1} {PH2} height can\'t be more than 2.3 times as long as the width"},"panels/application/AppManifestView.ts | sSrcIsNotSet":{"message":"{PH1} src is not set"},"panels/application/AppManifestView.ts | sSShouldHaveSquareIcon":{"message":"Most operating systems require square icons. Please include at least one square icon in the array."},"panels/application/AppManifestView.ts | sSShouldSpecifyItsSizeAs":{"message":"{PH1} {PH2} should specify its size as [width]x[height]"},"panels/application/AppManifestView.ts | sSSizeShouldBeAtLeast320":{"message":"{PH1} {PH2} size should be at least 320×320"},"panels/application/AppManifestView.ts | sSSizeShouldBeAtMost3840":{"message":"{PH1} {PH2} size should be at most 3840×3840"},"panels/application/AppManifestView.ts | sSWidthDoesNotComplyWithRatioRequirement":{"message":"{PH1} {PH2} width can\'t be more than 2.3 times as long as the height"},"panels/application/AppManifestView.ts | startUrl":{"message":"Start URL"},"panels/application/AppManifestView.ts | sUrlSFailedToParse":{"message":"{PH1} URL \'\'{PH2}\'\' failed to parse"},"panels/application/AppManifestView.ts | theAppIsAlreadyInstalled":{"message":"The app is already installed"},"panels/application/AppManifestView.ts | themeColor":{"message":"Theme color"},"panels/application/AppManifestView.ts | thePlayStoreAppUrlAndPlayStoreId":{"message":"The Play Store app URL and Play Store ID do not match"},"panels/application/AppManifestView.ts | theSpecifiedApplicationPlatform":{"message":"The specified application platform is not supported on Android"},"panels/application/BackgroundServiceView.ts | backgroundFetch":{"message":"Background Fetch"},"panels/application/BackgroundServiceView.ts | backgroundServices":{"message":"Background Services"},"panels/application/BackgroundServiceView.ts | backgroundSync":{"message":"Background Sync"},"panels/application/BackgroundServiceView.ts | clear":{"message":"Clear"},"panels/application/BackgroundServiceView.ts | clickTheRecordButtonSOrHitSTo":{"message":"Click the record button {PH1} or hit {PH2} to start recording."},"panels/application/BackgroundServiceView.ts | devtoolsWillRecordAllSActivity":{"message":"DevTools will record all {PH1} activity for up to 3 days, even when closed."},"panels/application/BackgroundServiceView.ts | empty":{"message":"empty"},"panels/application/BackgroundServiceView.ts | event":{"message":"Event"},"panels/application/BackgroundServiceView.ts | instanceId":{"message":"Instance ID"},"panels/application/BackgroundServiceView.ts | learnMore":{"message":"Learn more"},"panels/application/BackgroundServiceView.ts | noMetadataForThisEvent":{"message":"No metadata for this event"},"panels/application/BackgroundServiceView.ts | notifications":{"message":"Notifications"},"panels/application/BackgroundServiceView.ts | origin":{"message":"Origin"},"panels/application/BackgroundServiceView.ts | paymentHandler":{"message":"Payment Handler"},"panels/application/BackgroundServiceView.ts | periodicBackgroundSync":{"message":"Periodic Background Sync"},"panels/application/BackgroundServiceView.ts | pushMessaging":{"message":"Push Messaging"},"panels/application/BackgroundServiceView.ts | recordingSActivity":{"message":"Recording {PH1} activity..."},"panels/application/BackgroundServiceView.ts | saveEvents":{"message":"Save events"},"panels/application/BackgroundServiceView.ts | selectAnEntryToViewMetadata":{"message":"Select an entry to view metadata"},"panels/application/BackgroundServiceView.ts | showEventsFromOtherDomains":{"message":"Show events from other domains"},"panels/application/BackgroundServiceView.ts | startRecordingEvents":{"message":"Start recording events"},"panels/application/BackgroundServiceView.ts | stopRecordingEvents":{"message":"Stop recording events"},"panels/application/BackgroundServiceView.ts | swScope":{"message":"Service Worker Scope"},"panels/application/BackgroundServiceView.ts | timestamp":{"message":"Timestamp"},"panels/application/components/BackForwardCacheStrings.ts | appBanner":{"message":"Pages that requested an AppBanner are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabled":{"message":"Back/forward cache is disabled by flags. Visit chrome://flags/#back-forward-cache to enable it locally on this device."},"panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByCommandLine":{"message":"Back/forward cache is disabled by the command line."},"panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledByLowMemory":{"message":"Back/forward cache is disabled due to insufficient memory."},"panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForDelegate":{"message":"Back/forward cache is not supported by delegate."},"panels/application/components/BackForwardCacheStrings.ts | backForwardCacheDisabledForPrerender":{"message":"Back/forward cache is disabled for prerenderer."},"panels/application/components/BackForwardCacheStrings.ts | broadcastChannel":{"message":"The page cannot be cached because it has a BroadcastChannel instance with registered listeners."},"panels/application/components/BackForwardCacheStrings.ts | cacheControlNoStore":{"message":"Pages with cache-control:no-store header cannot enter back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | cacheFlushed":{"message":"The cache was intentionally cleared."},"panels/application/components/BackForwardCacheStrings.ts | cacheLimit":{"message":"The page was evicted from the cache to allow another page to be cached."},"panels/application/components/BackForwardCacheStrings.ts | containsPlugins":{"message":"Pages containing plugins are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | contentFileChooser":{"message":"Pages that use FileChooser API are not eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | contentFileSystemAccess":{"message":"Pages that use File System Access API are not eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | contentMediaDevicesDispatcherHost":{"message":"Pages that use Media Device Dispatcher are not eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | contentMediaPlay":{"message":"A media player was playing upon navigating away."},"panels/application/components/BackForwardCacheStrings.ts | contentMediaSession":{"message":"Pages that use MediaSession API and set a playback state are not eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | contentMediaSessionService":{"message":"Pages that use MediaSession API and set action handlers are not eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | contentScreenReader":{"message":"Back/forward cache is disabled due to screen reader."},"panels/application/components/BackForwardCacheStrings.ts | contentSecurityHandler":{"message":"Pages that use SecurityHandler are not eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | contentSerial":{"message":"Pages that use Serial API are not eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | contentWebAuthenticationAPI":{"message":"Pages that use WebAuthetication API are not eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | contentWebBluetooth":{"message":"Pages that use WebBluetooth API are not eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | contentWebUSB":{"message":"Pages that use WebUSB API are not eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | dedicatedWorkerOrWorklet":{"message":"Pages that use a dedicated worker or worklet are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | documentLoaded":{"message":"The document did not finish loading before navigating away."},"panels/application/components/BackForwardCacheStrings.ts | embedderAppBannerManager":{"message":"App Banner was present upon navigating away."},"panels/application/components/BackForwardCacheStrings.ts | embedderChromePasswordManagerClientBindCredentialManager":{"message":"Chrome Password Manager was present upon navigating away."},"panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerSelfDeletingRequestDelegate":{"message":"DOM distillation was in progress upon navigating away."},"panels/application/components/BackForwardCacheStrings.ts | embedderDomDistillerViewerSource":{"message":"DOM Distiller Viewer was present upon navigating away."},"panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessaging":{"message":"Back/forward cache is disabled due to extensions using messaging API."},"panels/application/components/BackForwardCacheStrings.ts | embedderExtensionMessagingForOpenPort":{"message":"Extensions with long-lived connection should close the connection before entering back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | embedderExtensions":{"message":"Back/forward cache is disabled due to extensions."},"panels/application/components/BackForwardCacheStrings.ts | embedderExtensionSentMessageToCachedFrame":{"message":"Extensions with long-lived connection attempted to send messages to frames in back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | embedderModalDialog":{"message":"Modal dialog such as form resubmission or http password dialog was shown for the page upon navigating away."},"panels/application/components/BackForwardCacheStrings.ts | embedderOfflinePage":{"message":"The offline page was shown upon navigating away."},"panels/application/components/BackForwardCacheStrings.ts | embedderOomInterventionTabHelper":{"message":"Out-Of-Memory Intervention bar was present upon navigating away."},"panels/application/components/BackForwardCacheStrings.ts | embedderPermissionRequestManager":{"message":"There were permission requests upon navigating away."},"panels/application/components/BackForwardCacheStrings.ts | embedderPopupBlockerTabHelper":{"message":"Popup blocker was present upon navigating away."},"panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingThreatDetails":{"message":"Safe Browsing details were shown upon navigating away."},"panels/application/components/BackForwardCacheStrings.ts | embedderSafeBrowsingTriggeredPopupBlocker":{"message":"Safe Browsing considered this page to be abusive and blocked popup."},"panels/application/components/BackForwardCacheStrings.ts | enteredBackForwardCacheBeforeServiceWorkerHostAdded":{"message":"A service worker was activated while the page was in back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | foregroundCacheLimit":{"message":"The page was evicted from the cache to allow another page to be cached."},"panels/application/components/BackForwardCacheStrings.ts | grantedMediaStreamAccess":{"message":"Pages that have granted media stream access are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | haveInnerContents":{"message":"Pages that use portals are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | HTTPMethodNotGET":{"message":"Only pages loaded via a GET request are eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | HTTPStatusNotOK":{"message":"Only pages with a status code of 2XX can be cached."},"panels/application/components/BackForwardCacheStrings.ts | idleManager":{"message":"Pages that use IdleManager are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | indexedDBConnection":{"message":"Pages that have an open IndexedDB connection are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | ineligibleAPI":{"message":"Ineligible APIs were used."},"panels/application/components/BackForwardCacheStrings.ts | injectedJavascript":{"message":"IPages that JavaScript is injected into by extensions are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | injectedStyleSheet":{"message":"Pages that StyleSheet is injected into by extensions are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | internalError":{"message":"Internal error."},"panels/application/components/BackForwardCacheStrings.ts | JavaScriptExecution":{"message":"Chrome detected an attempt to execute JavaScript while in the cache."},"panels/application/components/BackForwardCacheStrings.ts | keyboardLock":{"message":"Pages that use Keyboard lock are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | loading":{"message":"The page did not finish loading before navigating away."},"panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoCache":{"message":"Pages whose main resource has cache-control:no-cache cannot enter back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | mainResourceHasCacheControlNoStore":{"message":"Pages whose main resource has cache-control:no-store cannot enter back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | navigationCancelledWhileRestoring":{"message":"Navigation was cancelled before the page could be restored from back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | networkExceedsBufferLimit":{"message":"The page was evicted from the cache because an active network connection received too much data. Chrome limits the amount of data that a page may receive while cached."},"panels/application/components/BackForwardCacheStrings.ts | networkRequestDatapipeDrainedAsBytesConsumer":{"message":"Pages that have inflight fetch() or XHR are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | networkRequestRedirected":{"message":"The page was evicted from back/forward cache because an active network request involved a redirect."},"panels/application/components/BackForwardCacheStrings.ts | networkRequestTimeout":{"message":"The page was evicted from the cache because a network connection was open too long. Chrome limits the amount of time that a page may receive data while cached."},"panels/application/components/BackForwardCacheStrings.ts | noResponseHead":{"message":"Pages that do not have a valid response head cannot enter back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | notMainFrame":{"message":"Navigation happened in a frame other than the main frame."},"panels/application/components/BackForwardCacheStrings.ts | outstandingIndexedDBTransaction":{"message":"Page with ongoing indexed DB transactions are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestDirectSocket":{"message":"Pages with an in-flight network request are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestFetch":{"message":"Pages with an in-flight fetch network request are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestOthers":{"message":"Pages with an in-flight network request are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | outstandingNetworkRequestXHR":{"message":"Pages with an in-flight XHR network request are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | paymentManager":{"message":"Pages that use PaymentManager are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | pictureInPicture":{"message":"Pages that use Picture-in-Picture are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | portal":{"message":"Pages that use portals are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | printing":{"message":"Pages that show Printing UI are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | relatedActiveContentsExist":{"message":"The page was opened using \'window.open()\' and another tab has a reference to it, or the page opened a window."},"panels/application/components/BackForwardCacheStrings.ts | rendererProcessCrashed":{"message":"The renderer process for the page in back/forward cache crashed."},"panels/application/components/BackForwardCacheStrings.ts | rendererProcessKilled":{"message":"The renderer process for the page in back/forward cache was killed."},"panels/application/components/BackForwardCacheStrings.ts | requestedAudioCapturePermission":{"message":"Pages that have requested audio capture permissions are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | requestedBackForwardCacheBlockedSensors":{"message":"Pages that have requested sensor permissions are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | requestedBackgroundWorkPermission":{"message":"Pages that have requested background sync or fetch permissions are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | requestedMIDIPermission":{"message":"Pages that have requested MIDI permissions are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | requestedNotificationsPermission":{"message":"Pages that have requested notifications permissions are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | requestedStorageAccessGrant":{"message":"Pages that have requested storage access are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | requestedVideoCapturePermission":{"message":"Pages that have requested video capture permissions are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | schemeNotHTTPOrHTTPS":{"message":"Only pages whose URL scheme is HTTP / HTTPS can be cached."},"panels/application/components/BackForwardCacheStrings.ts | serviceWorkerClaim":{"message":"The page was claimed by a service worker while it is in back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | serviceWorkerPostMessage":{"message":"A service worker attempted to send the page in back/forward cache a MessageEvent."},"panels/application/components/BackForwardCacheStrings.ts | serviceWorkerUnregistration":{"message":"ServiceWorker was unregistered while a page was in back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | serviceWorkerVersionActivation":{"message":"The page was evicted from back/forward cache due to a service worker activation."},"panels/application/components/BackForwardCacheStrings.ts | sessionRestored":{"message":"Chrome restarted and cleared the back/forward cache entries."},"panels/application/components/BackForwardCacheStrings.ts | sharedWorker":{"message":"Pages that use SharedWorker are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | speechRecognizer":{"message":"Pages that use SpeechRecognizer are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | speechSynthesis":{"message":"Pages that use SpeechSynthesis are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | subframeIsNavigating":{"message":"An iframe on the page started a navigation that did not complete."},"panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoCache":{"message":"Pages whose subresource has cache-control:no-cache cannot enter back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | subresourceHasCacheControlNoStore":{"message":"Pages whose subresource has cache-control:no-store cannot enter back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | timeout":{"message":"The page exceeded the maximum time in back/forward cache and was expired."},"panels/application/components/BackForwardCacheStrings.ts | timeoutPuttingInCache":{"message":"The page timed out entering back/forward cache (likely due to long-running pagehide handlers)."},"panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInMainFrame":{"message":"The page has an unload handler in the main frame."},"panels/application/components/BackForwardCacheStrings.ts | unloadHandlerExistsInSubFrame":{"message":"The page has an unload handler in a sub frame."},"panels/application/components/BackForwardCacheStrings.ts | userAgentOverrideDiffers":{"message":"Browser has changed the user agent override header."},"panels/application/components/BackForwardCacheStrings.ts | wasGrantedMediaAccess":{"message":"Pages that have granted access to record video or audio are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | webDatabase":{"message":"Pages that use WebDatabase are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | webHID":{"message":"Pages that use WebHID are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | webLocks":{"message":"Pages that use WebLocks are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | webNfc":{"message":"Pages that use WebNfc are not currently eligible for back/forwad cache."},"panels/application/components/BackForwardCacheStrings.ts | webOTPService":{"message":"Pages that use WebOTPService are not currently eligible for bfcache."},"panels/application/components/BackForwardCacheStrings.ts | webRTC":{"message":"Pages with WebRTC cannot enter back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | webShare":{"message":"Pages that use WebShare are not currently eligible for back/forwad cache."},"panels/application/components/BackForwardCacheStrings.ts | webSocket":{"message":"Pages with WebSocket cannot enter back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | webTransport":{"message":"Pages with WebTransport cannot enter back/forward cache."},"panels/application/components/BackForwardCacheStrings.ts | webXR":{"message":"Pages that use WebXR are not currently eligible for back/forward cache."},"panels/application/components/BackForwardCacheView.ts | backForwardCacheTitle":{"message":"Back/forward cache"},"panels/application/components/BackForwardCacheView.ts | blockingExtensionId":{"message":"Extension id: "},"panels/application/components/BackForwardCacheView.ts | circumstantial":{"message":"Not Actionable"},"panels/application/components/BackForwardCacheView.ts | circumstantialExplanation":{"message":"These reasons are not actionable i.e. caching was prevented by something outside of the direct control of the page."},"panels/application/components/BackForwardCacheView.ts | learnMore":{"message":"Learn more: back/forward cache eligibility"},"panels/application/components/BackForwardCacheView.ts | mainFrame":{"message":"Main Frame"},"panels/application/components/BackForwardCacheView.ts | normalNavigation":{"message":"Not served from back/forward cache: to trigger back/forward cache, use Chrome\'s back/forward buttons, or use the test button below to automatically navigate away and back."},"panels/application/components/BackForwardCacheView.ts | pageSupportNeeded":{"message":"Actionable"},"panels/application/components/BackForwardCacheView.ts | pageSupportNeededExplanation":{"message":"These reasons are actionable i.e. they can be cleaned up to make the page eligible for back/forward cache."},"panels/application/components/BackForwardCacheView.ts | restoredFromBFCache":{"message":"Successfully served from back/forward cache."},"panels/application/components/BackForwardCacheView.ts | runningTest":{"message":"Running test"},"panels/application/components/BackForwardCacheView.ts | runTest":{"message":"Test back/forward cache"},"panels/application/components/BackForwardCacheView.ts | supportPending":{"message":"Pending Support"},"panels/application/components/BackForwardCacheView.ts | supportPendingExplanation":{"message":"Chrome support for these reasons is pending i.e. they will not prevent the page from being eligible for back/forward cache in a future version of Chrome."},"panels/application/components/BackForwardCacheView.ts | unavailable":{"message":"unavailable"},"panels/application/components/BackForwardCacheView.ts | unknown":{"message":"Unknown Status"},"panels/application/components/BackForwardCacheView.ts | url":{"message":"URL:"},"panels/application/components/EndpointsGrid.ts | noEndpointsToDisplay":{"message":"No endpoints to display"},"panels/application/components/FrameDetailsView.ts | additionalInformation":{"message":"Additional Information"},"panels/application/components/FrameDetailsView.ts | adStatus":{"message":"Ad Status"},"panels/application/components/FrameDetailsView.ts | aFrameAncestorIsAnInsecure":{"message":"A frame ancestor is an insecure context"},"panels/application/components/FrameDetailsView.ts | apiAvailability":{"message":"API availability"},"panels/application/components/FrameDetailsView.ts | availabilityOfCertainApisDepends":{"message":"Availability of certain APIs depends on the document being cross-origin isolated."},"panels/application/components/FrameDetailsView.ts | available":{"message":"available"},"panels/application/components/FrameDetailsView.ts | availableNotTransferable":{"message":"available, not transferable"},"panels/application/components/FrameDetailsView.ts | availableTransferable":{"message":"available, transferable"},"panels/application/components/FrameDetailsView.ts | child":{"message":"child"},"panels/application/components/FrameDetailsView.ts | childDescription":{"message":"This frame has been identified as a child frame of an ad"},"panels/application/components/FrameDetailsView.ts | clickToRevealInElementsPanel":{"message":"Click to reveal in Elements panel"},"panels/application/components/FrameDetailsView.ts | clickToRevealInNetworkPanel":{"message":"Click to reveal in Network panel"},"panels/application/components/FrameDetailsView.ts | clickToRevealInNetworkPanelMight":{"message":"Click to reveal in Network panel (might require page reload)"},"panels/application/components/FrameDetailsView.ts | clickToRevealInSourcesPanel":{"message":"Click to reveal in Sources panel"},"panels/application/components/FrameDetailsView.ts | createdByAdScriptExplanation":{"message":"There was an ad script in the (async) stack when this frame was created. Examining the creation stack trace of this frame might provide more insight."},"panels/application/components/FrameDetailsView.ts | creationStackTrace":{"message":"Frame Creation Stack Trace"},"panels/application/components/FrameDetailsView.ts | creationStackTraceExplanation":{"message":"This frame was created programmatically. The stack trace shows where this happened."},"panels/application/components/FrameDetailsView.ts | crossoriginIsolated":{"message":"Cross-Origin Isolated"},"panels/application/components/FrameDetailsView.ts | document":{"message":"Document"},"panels/application/components/FrameDetailsView.ts | frameId":{"message":"Frame ID"},"panels/application/components/FrameDetailsView.ts | learnMore":{"message":"Learn more"},"panels/application/components/FrameDetailsView.ts | localhostIsAlwaysASecureContext":{"message":"Localhost is always a secure context"},"panels/application/components/FrameDetailsView.ts | matchedBlockingRuleExplanation":{"message":"This frame is considered an ad frame because its current (or previous) main document is an ad resource."},"panels/application/components/FrameDetailsView.ts | measureMemory":{"message":"Measure Memory"},"panels/application/components/FrameDetailsView.ts | no":{"message":"No"},"panels/application/components/FrameDetailsView.ts | origin":{"message":"Origin"},"panels/application/components/FrameDetailsView.ts | ownerElement":{"message":"Owner Element"},"panels/application/components/FrameDetailsView.ts | parentIsAdExplanation":{"message":"This frame is considered an ad frame because its parent frame is an ad frame."},"panels/application/components/FrameDetailsView.ts | refresh":{"message":"Refresh"},"panels/application/components/FrameDetailsView.ts | reportingTo":{"message":"reporting to"},"panels/application/components/FrameDetailsView.ts | requiresCrossoriginIsolated":{"message":"requires cross-origin isolated context"},"panels/application/components/FrameDetailsView.ts | root":{"message":"root"},"panels/application/components/FrameDetailsView.ts | rootDescription":{"message":"This frame has been identified as the root frame of an ad"},"panels/application/components/FrameDetailsView.ts | secureContext":{"message":"Secure Context"},"panels/application/components/FrameDetailsView.ts | securityIsolation":{"message":"Security & Isolation"},"panels/application/components/FrameDetailsView.ts | sharedarraybufferConstructorIs":{"message":"SharedArrayBuffer constructor is available and SABs can be transferred via postMessage"},"panels/application/components/FrameDetailsView.ts | sharedarraybufferConstructorIsAvailable":{"message":"SharedArrayBuffer constructor is available but SABs cannot be transferred via postMessage"},"panels/application/components/FrameDetailsView.ts | theFramesSchemeIsInsecure":{"message":"The frame\'s scheme is insecure"},"panels/application/components/FrameDetailsView.ts | thePerformanceAPI":{"message":"The performance.measureUserAgentSpecificMemory() API is available"},"panels/application/components/FrameDetailsView.ts | thePerformancemeasureuseragentspecificmemory":{"message":"The performance.measureUserAgentSpecificMemory() API is not available"},"panels/application/components/FrameDetailsView.ts | thisAdditionalDebugging":{"message":"This additional (debugging) information is shown because the \'Protocol Monitor\' experiment is enabled."},"panels/application/components/FrameDetailsView.ts | transferRequiresCrossoriginIsolatedPermission":{"message":"SharedArrayBuffer transfer requires enabling the permission policy:"},"panels/application/components/FrameDetailsView.ts | unavailable":{"message":"unavailable"},"panels/application/components/FrameDetailsView.ts | unreachableUrl":{"message":"Unreachable URL"},"panels/application/components/FrameDetailsView.ts | url":{"message":"URL"},"panels/application/components/FrameDetailsView.ts | willRequireCrossoriginIsolated":{"message":"⚠️ will require cross-origin isolated context in the future"},"panels/application/components/FrameDetailsView.ts | yes":{"message":"Yes"},"panels/application/components/InterestGroupAccessGrid.ts | allInterestGroupStorageEvents":{"message":"All interest group storage events."},"panels/application/components/InterestGroupAccessGrid.ts | eventTime":{"message":"Event Time"},"panels/application/components/InterestGroupAccessGrid.ts | eventType":{"message":"Access Type"},"panels/application/components/InterestGroupAccessGrid.ts | groupName":{"message":"Name"},"panels/application/components/InterestGroupAccessGrid.ts | groupOwner":{"message":"Owner"},"panels/application/components/InterestGroupAccessGrid.ts | noEvents":{"message":"No interest group events recorded."},"panels/application/components/OriginTrialTreeView.ts | expiryTime":{"message":"Expiry Time"},"panels/application/components/OriginTrialTreeView.ts | isThirdParty":{"message":"Third Party"},"panels/application/components/OriginTrialTreeView.ts | matchSubDomains":{"message":"Subdomain Matching"},"panels/application/components/OriginTrialTreeView.ts | origin":{"message":"Origin"},"panels/application/components/OriginTrialTreeView.ts | rawTokenText":{"message":"Raw Token"},"panels/application/components/OriginTrialTreeView.ts | status":{"message":"Token Status"},"panels/application/components/OriginTrialTreeView.ts | token":{"message":"Token"},"panels/application/components/OriginTrialTreeView.ts | tokens":{"message":"{PH1} tokens"},"panels/application/components/OriginTrialTreeView.ts | trialName":{"message":"Trial Name"},"panels/application/components/OriginTrialTreeView.ts | usageRestriction":{"message":"Usage Restriction"},"panels/application/components/PermissionsPolicySection.ts | allowedFeatures":{"message":"Allowed Features"},"panels/application/components/PermissionsPolicySection.ts | clickToShowHeader":{"message":"Click to reveal the request whose \\"Permissions-Policy\\" HTTP header disables this feature."},"panels/application/components/PermissionsPolicySection.ts | clickToShowIframe":{"message":"Click to reveal the top-most iframe which does not allow this feature in the elements panel."},"panels/application/components/PermissionsPolicySection.ts | disabledByFencedFrame":{"message":"disabled inside a fencedframe"},"panels/application/components/PermissionsPolicySection.ts | disabledByHeader":{"message":"disabled by \\"Permissions-Policy\\" header"},"panels/application/components/PermissionsPolicySection.ts | disabledByIframe":{"message":"missing in iframe \\"allow\\" attribute"},"panels/application/components/PermissionsPolicySection.ts | disabledFeatures":{"message":"Disabled Features"},"panels/application/components/PermissionsPolicySection.ts | hideDetails":{"message":"Hide details"},"panels/application/components/PermissionsPolicySection.ts | showDetails":{"message":"Show details"},"panels/application/components/ReportsGrid.ts | destination":{"message":"Destination"},"panels/application/components/ReportsGrid.ts | generatedAt":{"message":"Generated at"},"panels/application/components/ReportsGrid.ts | noReportsToDisplay":{"message":"No reports to display"},"panels/application/components/ReportsGrid.ts | status":{"message":"Status"},"panels/application/components/StackTrace.ts | cannotRenderStackTrace":{"message":"Cannot render stack trace"},"panels/application/components/StackTrace.ts | showSMoreFrames":{"message":"{n, plural, =1 {Show # more frame} other {Show # more frames}}"},"panels/application/components/TrustTokensView.ts | allStoredTrustTokensAvailableIn":{"message":"All stored Trust Tokens available in this browser instance."},"panels/application/components/TrustTokensView.ts | deleteTrustTokens":{"message":"Delete all stored Trust Tokens issued by {PH1}."},"panels/application/components/TrustTokensView.ts | issuer":{"message":"Issuer"},"panels/application/components/TrustTokensView.ts | noTrustTokensStored":{"message":"No Trust Tokens are currently stored."},"panels/application/components/TrustTokensView.ts | storedTokenCount":{"message":"Stored token count"},"panels/application/CookieItemsView.ts | clearAllCookies":{"message":"Clear all cookies"},"panels/application/CookieItemsView.ts | clearFilteredCookies":{"message":"Clear filtered cookies"},"panels/application/CookieItemsView.ts | cookies":{"message":"Cookies"},"panels/application/CookieItemsView.ts | numberOfCookiesShownInTableS":{"message":"Number of cookies shown in table: {PH1}"},"panels/application/CookieItemsView.ts | onlyShowCookiesWhichHaveAn":{"message":"Only show cookies which have an associated issue"},"panels/application/CookieItemsView.ts | onlyShowCookiesWithAnIssue":{"message":"Only show cookies with an issue"},"panels/application/CookieItemsView.ts | selectACookieToPreviewItsValue":{"message":"Select a cookie to preview its value"},"panels/application/CookieItemsView.ts | showUrlDecoded":{"message":"Show URL decoded"},"panels/application/DatabaseModel.ts | anUnexpectedErrorSOccurred":{"message":"An unexpected error {PH1} occurred."},"panels/application/DatabaseModel.ts | databaseNoLongerHasExpected":{"message":"Database no longer has expected version."},"panels/application/DatabaseQueryView.ts | databaseQuery":{"message":"Database Query"},"panels/application/DatabaseQueryView.ts | queryS":{"message":"Query: {PH1}"},"panels/application/DatabaseTableView.ts | anErrorOccurredTryingToreadTheS":{"message":"An error occurred trying to read the \\"{PH1}\\" table."},"panels/application/DatabaseTableView.ts | database":{"message":"Database"},"panels/application/DatabaseTableView.ts | refresh":{"message":"Refresh"},"panels/application/DatabaseTableView.ts | theStableIsEmpty":{"message":"The \\"{PH1}\\" table is empty."},"panels/application/DatabaseTableView.ts | visibleColumns":{"message":"Visible columns"},"panels/application/DOMStorageItemsView.ts | domStorage":{"message":"DOM Storage"},"panels/application/DOMStorageItemsView.ts | domStorageItemDeleted":{"message":"The storage item was deleted."},"panels/application/DOMStorageItemsView.ts | domStorageItems":{"message":"DOM Storage Items"},"panels/application/DOMStorageItemsView.ts | domStorageNumberEntries":{"message":"Number of entries shown in table: {PH1}"},"panels/application/DOMStorageItemsView.ts | key":{"message":"Key"},"panels/application/DOMStorageItemsView.ts | selectAValueToPreview":{"message":"Select a value to preview"},"panels/application/DOMStorageItemsView.ts | value":{"message":"Value"},"panels/application/IndexedDBViews.ts | clearObjectStore":{"message":"Clear object store"},"panels/application/IndexedDBViews.ts | collapse":{"message":"Collapse"},"panels/application/IndexedDBViews.ts | dataMayBeStale":{"message":"Data may be stale"},"panels/application/IndexedDBViews.ts | deleteDatabase":{"message":"Delete database"},"panels/application/IndexedDBViews.ts | deleteSelected":{"message":"Delete selected"},"panels/application/IndexedDBViews.ts | expandRecursively":{"message":"Expand Recursively"},"panels/application/IndexedDBViews.ts | idb":{"message":"IDB"},"panels/application/IndexedDBViews.ts | indexedDb":{"message":"Indexed DB"},"panels/application/IndexedDBViews.ts | keyGeneratorValueS":{"message":"Key generator value: {PH1}"},"panels/application/IndexedDBViews.ts | keyPath":{"message":"Key path: "},"panels/application/IndexedDBViews.ts | keyString":{"message":"Key"},"panels/application/IndexedDBViews.ts | loading":{"message":"Loading…"},"panels/application/IndexedDBViews.ts | objectStores":{"message":"Object stores"},"panels/application/IndexedDBViews.ts | pleaseConfirmDeleteOfSDatabase":{"message":"Please confirm delete of \\"{PH1}\\" database."},"panels/application/IndexedDBViews.ts | primaryKey":{"message":"Primary key"},"panels/application/IndexedDBViews.ts | refresh":{"message":"Refresh"},"panels/application/IndexedDBViews.ts | refreshDatabase":{"message":"Refresh database"},"panels/application/IndexedDBViews.ts | securityOrigin":{"message":"Security origin"},"panels/application/IndexedDBViews.ts | showNextPage":{"message":"Show next page"},"panels/application/IndexedDBViews.ts | showPreviousPage":{"message":"Show previous page"},"panels/application/IndexedDBViews.ts | someEntriesMayHaveBeenModified":{"message":"Some entries may have been modified"},"panels/application/IndexedDBViews.ts | startFromKey":{"message":"Start from key"},"panels/application/IndexedDBViews.ts | totalEntriesS":{"message":"Total entries: {PH1}"},"panels/application/IndexedDBViews.ts | valueString":{"message":"Value"},"panels/application/IndexedDBViews.ts | version":{"message":"Version"},"panels/application/InterestGroupStorageView.ts | clickToDisplayBody":{"message":"Click on any interest group event to display the group\'s current state"},"panels/application/InterestGroupStorageView.ts | noDataAvailable":{"message":"No details available for the selected interest group. The browser may have left the group."},"panels/application/InterestGroupTreeElement.ts | interestGroups":{"message":"Interest Groups"},"panels/application/OpenedWindowDetailsView.ts | accessToOpener":{"message":"Access to opener"},"panels/application/OpenedWindowDetailsView.ts | clickToRevealInElementsPanel":{"message":"Click to reveal in Elements panel"},"panels/application/OpenedWindowDetailsView.ts | closed":{"message":"closed"},"panels/application/OpenedWindowDetailsView.ts | crossoriginEmbedderPolicy":{"message":"Cross-Origin Embedder Policy"},"panels/application/OpenedWindowDetailsView.ts | document":{"message":"Document"},"panels/application/OpenedWindowDetailsView.ts | no":{"message":"No"},"panels/application/OpenedWindowDetailsView.ts | openerFrame":{"message":"Opener Frame"},"panels/application/OpenedWindowDetailsView.ts | reportingTo":{"message":"reporting to"},"panels/application/OpenedWindowDetailsView.ts | security":{"message":"Security"},"panels/application/OpenedWindowDetailsView.ts | securityIsolation":{"message":"Security & Isolation"},"panels/application/OpenedWindowDetailsView.ts | showsWhetherTheOpenedWindowIs":{"message":"Shows whether the opened window is able to access its opener and vice versa"},"panels/application/OpenedWindowDetailsView.ts | type":{"message":"Type"},"panels/application/OpenedWindowDetailsView.ts | unknown":{"message":"Unknown"},"panels/application/OpenedWindowDetailsView.ts | url":{"message":"URL"},"panels/application/OpenedWindowDetailsView.ts | webWorker":{"message":"Web Worker"},"panels/application/OpenedWindowDetailsView.ts | windowWithoutTitle":{"message":"Window without title"},"panels/application/OpenedWindowDetailsView.ts | worker":{"message":"worker"},"panels/application/OpenedWindowDetailsView.ts | yes":{"message":"Yes"},"panels/application/ReportingApiReportsView.ts | clickToDisplayBody":{"message":"Click on any report to display its body"},"panels/application/ReportingApiTreeElement.ts | reportingApi":{"message":"Reporting API"},"panels/application/ServiceWorkerCacheViews.ts | cache":{"message":"Cache"},"panels/application/ServiceWorkerCacheViews.ts | deleteSelected":{"message":"Delete Selected"},"panels/application/ServiceWorkerCacheViews.ts | filterByPath":{"message":"Filter by Path"},"panels/application/ServiceWorkerCacheViews.ts | headers":{"message":"Headers"},"panels/application/ServiceWorkerCacheViews.ts | matchingEntriesS":{"message":"Matching entries: {PH1}"},"panels/application/ServiceWorkerCacheViews.ts | name":{"message":"Name"},"panels/application/ServiceWorkerCacheViews.ts | preview":{"message":"Preview"},"panels/application/ServiceWorkerCacheViews.ts | refresh":{"message":"Refresh"},"panels/application/ServiceWorkerCacheViews.ts | selectACacheEntryAboveToPreview":{"message":"Select a cache entry above to preview"},"panels/application/ServiceWorkerCacheViews.ts | serviceWorkerCache":{"message":"Service Worker Cache"},"panels/application/ServiceWorkerCacheViews.ts | timeCached":{"message":"Time Cached"},"panels/application/ServiceWorkerCacheViews.ts | totalEntriesS":{"message":"Total entries: {PH1}"},"panels/application/ServiceWorkerCacheViews.ts | varyHeaderWarning":{"message":"⚠️ Set ignoreVary to true when matching this entry"},"panels/application/ServiceWorkersView.ts | bypassForNetwork":{"message":"Bypass for network"},"panels/application/ServiceWorkersView.ts | bypassTheServiceWorkerAndLoad":{"message":"Bypass the service worker and load resources from the network"},"panels/application/ServiceWorkersView.ts | clients":{"message":"Clients"},"panels/application/ServiceWorkersView.ts | focus":{"message":"focus"},"panels/application/ServiceWorkersView.ts | inspect":{"message":"inspect"},"panels/application/ServiceWorkersView.ts | networkRequests":{"message":"Network requests"},"panels/application/ServiceWorkersView.ts | onPageReloadForceTheService":{"message":"On page reload, force the service worker to update, and activate it"},"panels/application/ServiceWorkersView.ts | periodicSync":{"message":"Periodic Sync"},"panels/application/ServiceWorkersView.ts | periodicSyncTag":{"message":"Periodic Sync tag"},"panels/application/ServiceWorkersView.ts | pushData":{"message":"Push data"},"panels/application/ServiceWorkersView.ts | pushString":{"message":"Push"},"panels/application/ServiceWorkersView.ts | receivedS":{"message":"Received {PH1}"},"panels/application/ServiceWorkersView.ts | sActivatedAndIsS":{"message":"#{PH1} activated and is {PH2}"},"panels/application/ServiceWorkersView.ts | sDeleted":{"message":"{PH1} - deleted"},"panels/application/ServiceWorkersView.ts | seeAllRegistrations":{"message":"See all registrations"},"panels/application/ServiceWorkersView.ts | serviceWorkerForS":{"message":"Service worker for {PH1}"},"panels/application/ServiceWorkersView.ts | serviceWorkersFromOtherOrigins":{"message":"Service workers from other origins"},"panels/application/ServiceWorkersView.ts | sIsRedundant":{"message":"#{PH1} is redundant"},"panels/application/ServiceWorkersView.ts | source":{"message":"Source"},"panels/application/ServiceWorkersView.ts | sRegistrationErrors":{"message":"{PH1} registration errors"},"panels/application/ServiceWorkersView.ts | startString":{"message":"start"},"panels/application/ServiceWorkersView.ts | status":{"message":"Status"},"panels/application/ServiceWorkersView.ts | stopString":{"message":"stop"},"panels/application/ServiceWorkersView.ts | sTryingToInstall":{"message":"#{PH1} trying to install"},"panels/application/ServiceWorkersView.ts | sWaitingToActivate":{"message":"#{PH1} waiting to activate"},"panels/application/ServiceWorkersView.ts | syncString":{"message":"Sync"},"panels/application/ServiceWorkersView.ts | syncTag":{"message":"Sync tag"},"panels/application/ServiceWorkersView.ts | testPushMessageFromDevtools":{"message":"Test push message from DevTools."},"panels/application/ServiceWorkersView.ts | unregister":{"message":"Unregister"},"panels/application/ServiceWorkersView.ts | unregisterServiceWorker":{"message":"Unregister service worker"},"panels/application/ServiceWorkersView.ts | update":{"message":"Update"},"panels/application/ServiceWorkersView.ts | updateCycle":{"message":"Update Cycle"},"panels/application/ServiceWorkersView.ts | updateOnReload":{"message":"Update on reload"},"panels/application/ServiceWorkersView.ts | workerS":{"message":"Worker: {PH1}"},"panels/application/ServiceWorkerUpdateCycleView.ts | endTimeS":{"message":"End time: {PH1}"},"panels/application/ServiceWorkerUpdateCycleView.ts | startTimeS":{"message":"Start time: {PH1}"},"panels/application/ServiceWorkerUpdateCycleView.ts | timeline":{"message":"Timeline"},"panels/application/ServiceWorkerUpdateCycleView.ts | updateActivity":{"message":"Update Activity"},"panels/application/ServiceWorkerUpdateCycleView.ts | version":{"message":"Version"},"panels/application/StorageItemsView.ts | clearAll":{"message":"Clear All"},"panels/application/StorageItemsView.ts | deleteSelected":{"message":"Delete Selected"},"panels/application/StorageItemsView.ts | filter":{"message":"Filter"},"panels/application/StorageItemsView.ts | refresh":{"message":"Refresh"},"panels/application/StorageItemsView.ts | refreshedStatus":{"message":"Table refreshed"},"panels/application/StorageView.ts | application":{"message":"Application"},"panels/application/StorageView.ts | cache":{"message":"Cache"},"panels/application/StorageView.ts | cacheStorage":{"message":"Cache storage"},"panels/application/StorageView.ts | clearing":{"message":"Clearing..."},"panels/application/StorageView.ts | clearSiteData":{"message":"Clear site data"},"panels/application/StorageView.ts | cookies":{"message":"Cookies"},"panels/application/StorageView.ts | fileSystem":{"message":"File System"},"panels/application/StorageView.ts | includingThirdPartyCookies":{"message":"including third-party cookies"},"panels/application/StorageView.ts | indexDB":{"message":"IndexedDB"},"panels/application/StorageView.ts | internalError":{"message":"Internal error"},"panels/application/StorageView.ts | learnMore":{"message":"Learn more"},"panels/application/StorageView.ts | localAndSessionStorage":{"message":"Local and session storage"},"panels/application/StorageView.ts | mb":{"message":"MB"},"panels/application/StorageView.ts | numberMustBeNonNegative":{"message":"Number must be non-negative"},"panels/application/StorageView.ts | other":{"message":"Other"},"panels/application/StorageView.ts | pleaseEnterANumber":{"message":"Please enter a number"},"panels/application/StorageView.ts | serviceWorkers":{"message":"Service Workers"},"panels/application/StorageView.ts | sFailedToLoad":{"message":"{PH1} (failed to load)"},"panels/application/StorageView.ts | simulateCustomStorage":{"message":"Simulate custom storage quota"},"panels/application/StorageView.ts | storageQuotaIsLimitedIn":{"message":"Storage quota is limited in Incognito mode"},"panels/application/StorageView.ts | storageQuotaUsed":{"message":"{PH1} used out of {PH2} storage quota"},"panels/application/StorageView.ts | storageQuotaUsedWithBytes":{"message":"{PH1} bytes used out of {PH2} bytes storage quota"},"panels/application/StorageView.ts | storageTitle":{"message":"Storage"},"panels/application/StorageView.ts | storageUsage":{"message":"Storage usage"},"panels/application/StorageView.ts | storageWithCustomMarker":{"message":"{PH1} (custom)"},"panels/application/StorageView.ts | unregisterServiceWorker":{"message":"Unregister service workers"},"panels/application/StorageView.ts | usage":{"message":"Usage"},"panels/application/StorageView.ts | webSql":{"message":"Web SQL"},"panels/application/TrustTokensTreeElement.ts | trustTokens":{"message":"Trust Tokens"},"panels/browser_debugger/browser_debugger-meta.ts | contentScripts":{"message":"Content scripts"},"panels/browser_debugger/browser_debugger-meta.ts | cspViolationBreakpoints":{"message":"CSP Violation Breakpoints"},"panels/browser_debugger/browser_debugger-meta.ts | domBreakpoints":{"message":"DOM Breakpoints"},"panels/browser_debugger/browser_debugger-meta.ts | eventListenerBreakpoints":{"message":"Event Listener Breakpoints"},"panels/browser_debugger/browser_debugger-meta.ts | globalListeners":{"message":"Global Listeners"},"panels/browser_debugger/browser_debugger-meta.ts | overrides":{"message":"Overrides"},"panels/browser_debugger/browser_debugger-meta.ts | page":{"message":"Page"},"panels/browser_debugger/browser_debugger-meta.ts | showContentScripts":{"message":"Show Content scripts"},"panels/browser_debugger/browser_debugger-meta.ts | showCspViolationBreakpoints":{"message":"Show CSP Violation Breakpoints"},"panels/browser_debugger/browser_debugger-meta.ts | showDomBreakpoints":{"message":"Show DOM Breakpoints"},"panels/browser_debugger/browser_debugger-meta.ts | showEventListenerBreakpoints":{"message":"Show Event Listener Breakpoints"},"panels/browser_debugger/browser_debugger-meta.ts | showGlobalListeners":{"message":"Show Global Listeners"},"panels/browser_debugger/browser_debugger-meta.ts | showOverrides":{"message":"Show Overrides"},"panels/browser_debugger/browser_debugger-meta.ts | showPage":{"message":"Show Page"},"panels/browser_debugger/browser_debugger-meta.ts | showXhrfetchBreakpoints":{"message":"Show XHR/fetch Breakpoints"},"panels/browser_debugger/browser_debugger-meta.ts | xhrfetchBreakpoints":{"message":"XHR/fetch Breakpoints"},"panels/browser_debugger/CategorizedBreakpointsSidebarPane.ts | breakpointHit":{"message":"breakpoint hit"},"panels/browser_debugger/DOMBreakpointsSidebarPane.ts | attributeModified":{"message":"Attribute modified"},"panels/browser_debugger/DOMBreakpointsSidebarPane.ts | breakOn":{"message":"Break on"},"panels/browser_debugger/DOMBreakpointsSidebarPane.ts | breakpointHit":{"message":"breakpoint hit"},"panels/browser_debugger/DOMBreakpointsSidebarPane.ts | breakpointRemoved":{"message":"Breakpoint removed"},"panels/browser_debugger/DOMBreakpointsSidebarPane.ts | breakpointSet":{"message":"Breakpoint set"},"panels/browser_debugger/DOMBreakpointsSidebarPane.ts | checked":{"message":"checked"},"panels/browser_debugger/DOMBreakpointsSidebarPane.ts | domBreakpointsList":{"message":"DOM Breakpoints list"},"panels/browser_debugger/DOMBreakpointsSidebarPane.ts | noBreakpoints":{"message":"No breakpoints"},"panels/browser_debugger/DOMBreakpointsSidebarPane.ts | nodeRemoved":{"message":"Node removed"},"panels/browser_debugger/DOMBreakpointsSidebarPane.ts | removeAllDomBreakpoints":{"message":"Remove all DOM breakpoints"},"panels/browser_debugger/DOMBreakpointsSidebarPane.ts | removeBreakpoint":{"message":"Remove breakpoint"},"panels/browser_debugger/DOMBreakpointsSidebarPane.ts | revealDomNodeInElementsPanel":{"message":"Reveal DOM node in Elements panel"},"panels/browser_debugger/DOMBreakpointsSidebarPane.ts | sBreakpointHit":{"message":"{PH1} breakpoint hit"},"panels/browser_debugger/DOMBreakpointsSidebarPane.ts | sS":{"message":"{PH1}: {PH2}"},"panels/browser_debugger/DOMBreakpointsSidebarPane.ts | subtreeModified":{"message":"Subtree modified"},"panels/browser_debugger/DOMBreakpointsSidebarPane.ts | unchecked":{"message":"unchecked"},"panels/browser_debugger/ObjectEventListenersSidebarPane.ts | refreshGlobalListeners":{"message":"Refresh global listeners"},"panels/browser_debugger/XHRBreakpointsSidebarPane.ts | addBreakpoint":{"message":"Add breakpoint"},"panels/browser_debugger/XHRBreakpointsSidebarPane.ts | addXhrfetchBreakpoint":{"message":"Add XHR/fetch breakpoint"},"panels/browser_debugger/XHRBreakpointsSidebarPane.ts | anyXhrOrFetch":{"message":"Any XHR or fetch"},"panels/browser_debugger/XHRBreakpointsSidebarPane.ts | breakpointHit":{"message":"breakpoint hit"},"panels/browser_debugger/XHRBreakpointsSidebarPane.ts | breakWhenUrlContains":{"message":"Break when URL contains:"},"panels/browser_debugger/XHRBreakpointsSidebarPane.ts | noBreakpoints":{"message":"No breakpoints"},"panels/browser_debugger/XHRBreakpointsSidebarPane.ts | removeAllBreakpoints":{"message":"Remove all breakpoints"},"panels/browser_debugger/XHRBreakpointsSidebarPane.ts | removeBreakpoint":{"message":"Remove breakpoint"},"panels/browser_debugger/XHRBreakpointsSidebarPane.ts | urlBreakpoint":{"message":"URL Breakpoint"},"panels/browser_debugger/XHRBreakpointsSidebarPane.ts | urlContainsS":{"message":"URL contains \\"{PH1}\\""},"panels/browser_debugger/XHRBreakpointsSidebarPane.ts | xhrfetchBreakpoints":{"message":"XHR/fetch Breakpoints"},"panels/changes/changes-meta.ts | changes":{"message":"Changes"},"panels/changes/changes-meta.ts | showChanges":{"message":"Show Changes"},"panels/changes/ChangesSidebar.ts | sFromSourceMap":{"message":"{PH1} (from source map)"},"panels/changes/ChangesView.ts | binaryData":{"message":"Binary data"},"panels/changes/ChangesView.ts | noChanges":{"message":"No changes"},"panels/changes/ChangesView.ts | revertAllChangesToCurrentFile":{"message":"Revert all changes to current file"},"panels/changes/ChangesView.ts | sDeletions":{"message":"{n, plural, =1 {# deletion (-)} other {# deletions (-)}}"},"panels/changes/ChangesView.ts | sInsertions":{"message":"{n, plural, =1 {# insertion (+)} other {# insertions (+)}}"},"panels/console_counters/WarningErrorCounter.ts | openConsoleToViewS":{"message":"Open Console to view {PH1}"},"panels/console_counters/WarningErrorCounter.ts | openIssuesToView":{"message":"{n, plural, =1 {Open Issues to view # issue:} other {Open Issues to view # issues:}}"},"panels/console_counters/WarningErrorCounter.ts | sErrors":{"message":"{n, plural, =1 {# error} other {# errors}}"},"panels/console_counters/WarningErrorCounter.ts | sWarnings":{"message":"{n, plural, =1 {# warning} other {# warnings}}"},"panels/console/console-meta.ts | autocompleteFromHistory":{"message":"Autocomplete from history"},"panels/console/console-meta.ts | clearConsole":{"message":"Clear console"},"panels/console/console-meta.ts | clearConsoleHistory":{"message":"Clear console history"},"panels/console/console-meta.ts | console":{"message":"Console"},"panels/console/console-meta.ts | createLiveExpression":{"message":"Create live expression"},"panels/console/console-meta.ts | doNotAutocompleteFromHistory":{"message":"Do not autocomplete from history"},"panels/console/console-meta.ts | doNotEagerlyEvaluateConsole":{"message":"Do not eagerly evaluate console prompt text"},"panels/console/console-meta.ts | doNotGroupSimilarMessagesIn":{"message":"Do not group similar messages in console"},"panels/console/console-meta.ts | doNotShowCorsErrorsIn":{"message":"Do not show CORS errors in console"},"panels/console/console-meta.ts | doNotTreatEvaluationAsUser":{"message":"Do not treat evaluation as user activation"},"panels/console/console-meta.ts | eagerEvaluation":{"message":"Eager evaluation"},"panels/console/console-meta.ts | eagerlyEvaluateConsolePromptText":{"message":"Eagerly evaluate console prompt text"},"panels/console/console-meta.ts | evaluateTriggersUserActivation":{"message":"Evaluate triggers user activation"},"panels/console/console-meta.ts | groupSimilarMessagesInConsole":{"message":"Group similar messages in console"},"panels/console/console-meta.ts | hideNetworkMessages":{"message":"Hide network messages"},"panels/console/console-meta.ts | hideTimestamps":{"message":"Hide timestamps"},"panels/console/console-meta.ts | logXmlhttprequests":{"message":"Log XMLHttpRequests"},"panels/console/console-meta.ts | onlyShowMessagesFromTheCurrent":{"message":"Only show messages from the current context (top, iframe, worker, extension)"},"panels/console/console-meta.ts | selectedContextOnly":{"message":"Selected context only"},"panels/console/console-meta.ts | showConsole":{"message":"Show Console"},"panels/console/console-meta.ts | showCorsErrorsInConsole":{"message":"Show CORS errors in console"},"panels/console/console-meta.ts | showMessagesFromAllContexts":{"message":"Show messages from all contexts"},"panels/console/console-meta.ts | showNetworkMessages":{"message":"Show network messages"},"panels/console/console-meta.ts | showTimestamps":{"message":"Show timestamps"},"panels/console/console-meta.ts | treatEvaluationAsUserActivation":{"message":"Treat evaluation as user activation"},"panels/console/ConsoleContextSelector.ts | extension":{"message":"Extension"},"panels/console/ConsoleContextSelector.ts | javascriptContextNotSelected":{"message":"JavaScript context: Not selected"},"panels/console/ConsoleContextSelector.ts | javascriptContextS":{"message":"JavaScript context: {PH1}"},"panels/console/ConsolePinPane.ts | evaluateAllowingSideEffects":{"message":"Evaluate, allowing side effects"},"panels/console/ConsolePinPane.ts | expression":{"message":"Expression"},"panels/console/ConsolePinPane.ts | liveExpressionEditor":{"message":"Live expression editor"},"panels/console/ConsolePinPane.ts | notAvailable":{"message":"not available"},"panels/console/ConsolePinPane.ts | removeAllExpressions":{"message":"Remove all expressions"},"panels/console/ConsolePinPane.ts | removeBlankExpression":{"message":"Remove blank expression"},"panels/console/ConsolePinPane.ts | removeExpression":{"message":"Remove expression"},"panels/console/ConsolePinPane.ts | removeExpressionS":{"message":"Remove expression: {PH1}"},"panels/console/ConsolePrompt.ts | consolePrompt":{"message":"Console prompt"},"panels/console/ConsoleSidebar.ts | dErrors":{"message":"{n, plural, =0 {No errors} =1 {# error} other {# errors}}"},"panels/console/ConsoleSidebar.ts | dInfo":{"message":"{n, plural, =0 {No info} =1 {# info} other {# info}}"},"panels/console/ConsoleSidebar.ts | dMessages":{"message":"{n, plural, =0 {No messages} =1 {# message} other {# messages}}"},"panels/console/ConsoleSidebar.ts | dUserMessages":{"message":"{n, plural, =0 {No user messages} =1 {# user message} other {# user messages}}"},"panels/console/ConsoleSidebar.ts | dVerbose":{"message":"{n, plural, =0 {No verbose} =1 {# verbose} other {# verbose}}"},"panels/console/ConsoleSidebar.ts | dWarnings":{"message":"{n, plural, =0 {No warnings} =1 {# warning} other {# warnings}}"},"panels/console/ConsoleSidebar.ts | other":{"message":"<other>"},"panels/console/ConsoleView.ts | allLevels":{"message":"All levels"},"panels/console/ConsoleView.ts | consoleCleared":{"message":"Console cleared"},"panels/console/ConsoleView.ts | consoleSettings":{"message":"Console settings"},"panels/console/ConsoleView.ts | consoleSidebarHidden":{"message":"Console sidebar hidden"},"panels/console/ConsoleView.ts | consoleSidebarShown":{"message":"Console sidebar shown"},"panels/console/ConsoleView.ts | copyVisibleStyledSelection":{"message":"Copy visible styled selection"},"panels/console/ConsoleView.ts | customLevels":{"message":"Custom levels"},"panels/console/ConsoleView.ts | default":{"message":"Default"},"panels/console/ConsoleView.ts | defaultLevels":{"message":"Default levels"},"panels/console/ConsoleView.ts | doNotClearLogOnPageReload":{"message":"Do not clear log on page reload / navigation"},"panels/console/ConsoleView.ts | eagerlyEvaluateTextInThePrompt":{"message":"Eagerly evaluate text in the prompt"},"panels/console/ConsoleView.ts | egEventdCdnUrlacom":{"message":"e.g. /eventd/ -cdn url:a.com"},"panels/console/ConsoleView.ts | errors":{"message":"Errors"},"panels/console/ConsoleView.ts | filter":{"message":"Filter"},"panels/console/ConsoleView.ts | filteredMessagesInConsole":{"message":"{PH1} messages in console"},"panels/console/ConsoleView.ts | findStringInLogs":{"message":"Find string in logs"},"panels/console/ConsoleView.ts | groupSimilarMessagesInConsole":{"message":"Group similar messages in console"},"panels/console/ConsoleView.ts | hideAll":{"message":"Hide all"},"panels/console/ConsoleView.ts | hideConsoleSidebar":{"message":"Hide console sidebar"},"panels/console/ConsoleView.ts | hideMessagesFromS":{"message":"Hide messages from {PH1}"},"panels/console/ConsoleView.ts | hideNetwork":{"message":"Hide network"},"panels/console/ConsoleView.ts | info":{"message":"Info"},"panels/console/ConsoleView.ts | issuesWithColon":{"message":"{n, plural, =0 {No Issues} =1 {# Issue:} other {# Issues:}}"},"panels/console/ConsoleView.ts | issueToolbarClickToGoToTheIssuesTab":{"message":"Click to go to the issues tab"},"panels/console/ConsoleView.ts | issueToolbarClickToView":{"message":"Click to view {issueEnumeration}"},"panels/console/ConsoleView.ts | issueToolbarTooltipGeneral":{"message":"Some problems no longer generate console messages, but are surfaced in the issues tab."},"panels/console/ConsoleView.ts | logLevels":{"message":"Log levels"},"panels/console/ConsoleView.ts | logLevelS":{"message":"Log level: {PH1}"},"panels/console/ConsoleView.ts | onlyShowMessagesFromTheCurrentContext":{"message":"Only show messages from the current context (top, iframe, worker, extension)"},"panels/console/ConsoleView.ts | overriddenByFilterSidebar":{"message":"Overridden by filter sidebar"},"panels/console/ConsoleView.ts | preserveLog":{"message":"Preserve log"},"panels/console/ConsoleView.ts | replayXhr":{"message":"Replay XHR"},"panels/console/ConsoleView.ts | saveAs":{"message":"Save as..."},"panels/console/ConsoleView.ts | searching":{"message":"Searching…"},"panels/console/ConsoleView.ts | selectedContextOnly":{"message":"Selected context only"},"panels/console/ConsoleView.ts | sHidden":{"message":"{n, plural, =1 {# hidden} other {# hidden}}"},"panels/console/ConsoleView.ts | showConsoleSidebar":{"message":"Show console sidebar"},"panels/console/ConsoleView.ts | showCorsErrorsInConsole":{"message":"Show CORS errors in console"},"panels/console/ConsoleView.ts | sOnly":{"message":"{PH1} only"},"panels/console/ConsoleView.ts | verbose":{"message":"Verbose"},"panels/console/ConsoleView.ts | warnings":{"message":"Warnings"},"panels/console/ConsoleView.ts | writingFile":{"message":"Writing file…"},"panels/console/ConsoleViewMessage.ts | assertionFailed":{"message":"Assertion failed: "},"panels/console/ConsoleViewMessage.ts | attribute":{"message":"<attribute>"},"panels/console/ConsoleViewMessage.ts | clearAllMessagesWithS":{"message":"Clear all messages with {PH1}"},"panels/console/ConsoleViewMessage.ts | console":{"message":"Console"},"panels/console/ConsoleViewMessage.ts | consoleclearWasPreventedDueTo":{"message":"console.clear() was prevented due to \'Preserve log\'"},"panels/console/ConsoleViewMessage.ts | consoleWasCleared":{"message":"Console was cleared"},"panels/console/ConsoleViewMessage.ts | deprecationS":{"message":"[Deprecation] {PH1}"},"panels/console/ConsoleViewMessage.ts | error":{"message":"Error"},"panels/console/ConsoleViewMessage.ts | errorS":{"message":"{n, plural, =1 {Error, Repeated # time} other {Error, Repeated # times}}"},"panels/console/ConsoleViewMessage.ts | exception":{"message":"<exception>"},"panels/console/ConsoleViewMessage.ts | functionWasResolvedFromBound":{"message":"Function was resolved from bound function."},"panels/console/ConsoleViewMessage.ts | index":{"message":"(index)"},"panels/console/ConsoleViewMessage.ts | interventionS":{"message":"[Intervention] {PH1}"},"panels/console/ConsoleViewMessage.ts | Mxx":{"message":" M<XX>"},"panels/console/ConsoleViewMessage.ts | repeatS":{"message":"{n, plural, =1 {Repeated # time} other {Repeated # times}}"},"panels/console/ConsoleViewMessage.ts | someEvent":{"message":"<some> event"},"panels/console/ConsoleViewMessage.ts | stackMessageCollapsed":{"message":"Stack table collapsed"},"panels/console/ConsoleViewMessage.ts | stackMessageExpanded":{"message":"Stack table expanded"},"panels/console/ConsoleViewMessage.ts | thisValueWasEvaluatedUponFirst":{"message":"This value was evaluated upon first expanding. It may have changed since then."},"panels/console/ConsoleViewMessage.ts | thisValueWillNotBeCollectedUntil":{"message":"This value will not be collected until console is cleared."},"panels/console/ConsoleViewMessage.ts | tookNms":{"message":"took <N>ms"},"panels/console/ConsoleViewMessage.ts | url":{"message":"<URL>"},"panels/console/ConsoleViewMessage.ts | value":{"message":"Value"},"panels/console/ConsoleViewMessage.ts | violationS":{"message":"[Violation] {PH1}"},"panels/console/ConsoleViewMessage.ts | warning":{"message":"Warning"},"panels/console/ConsoleViewMessage.ts | warningS":{"message":"{n, plural, =1 {Warning, Repeated # time} other {Warning, Repeated # times}}"},"panels/coverage/coverage-meta.ts | coverage":{"message":"Coverage"},"panels/coverage/coverage-meta.ts | instrumentCoverage":{"message":"Instrument coverage"},"panels/coverage/coverage-meta.ts | showCoverage":{"message":"Show Coverage"},"panels/coverage/coverage-meta.ts | startInstrumentingCoverageAnd":{"message":"Start instrumenting coverage and reload page"},"panels/coverage/coverage-meta.ts | stopInstrumentingCoverageAndShow":{"message":"Stop instrumenting coverage and show results"},"panels/coverage/CoverageListView.ts | codeCoverage":{"message":"Code Coverage"},"panels/coverage/CoverageListView.ts | css":{"message":"CSS"},"panels/coverage/CoverageListView.ts | jsCoverageWithPerBlock":{"message":"JS coverage with per block granularity: Once a block of JavaScript was executed, that block is marked as covered."},"panels/coverage/CoverageListView.ts | jsCoverageWithPerFunction":{"message":"JS coverage with per function granularity: Once a function was executed, the whole function is marked as covered."},"panels/coverage/CoverageListView.ts | jsPerBlock":{"message":"JS (per block)"},"panels/coverage/CoverageListView.ts | jsPerFunction":{"message":"JS (per function)"},"panels/coverage/CoverageListView.ts | sBytes":{"message":"{n, plural, =1 {# byte} other {# bytes}}"},"panels/coverage/CoverageListView.ts | sBytesS":{"message":"{n, plural, =1 {# byte, {percentage}} other {# bytes, {percentage}}}"},"panels/coverage/CoverageListView.ts | sBytesSBelongToBlocksOf":{"message":"{PH1} bytes ({PH2}) belong to blocks of JavaScript that have not (yet) been executed."},"panels/coverage/CoverageListView.ts | sBytesSBelongToBlocksOfJavascript":{"message":"{PH1} bytes ({PH2}) belong to blocks of JavaScript that have executed at least once."},"panels/coverage/CoverageListView.ts | sBytesSBelongToFunctionsThatHave":{"message":"{PH1} bytes ({PH2}) belong to functions that have not (yet) been executed."},"panels/coverage/CoverageListView.ts | sBytesSBelongToFunctionsThatHaveExecuted":{"message":"{PH1} bytes ({PH2}) belong to functions that have executed at least once."},"panels/coverage/CoverageListView.ts | sOfFileUnusedSOfFileUsed":{"message":"{PH1} % of file unused, {PH2} % of file used"},"panels/coverage/CoverageListView.ts | totalBytes":{"message":"Total Bytes"},"panels/coverage/CoverageListView.ts | type":{"message":"Type"},"panels/coverage/CoverageListView.ts | unusedBytes":{"message":"Unused Bytes"},"panels/coverage/CoverageListView.ts | url":{"message":"URL"},"panels/coverage/CoverageListView.ts | usageVisualization":{"message":"Usage Visualization"},"panels/coverage/CoverageView.ts | all":{"message":"All"},"panels/coverage/CoverageView.ts | chooseCoverageGranularityPer":{"message":"Choose coverage granularity: Per function has low overhead, per block has significant overhead."},"panels/coverage/CoverageView.ts | clearAll":{"message":"Clear all"},"panels/coverage/CoverageView.ts | clickTheRecordButtonSToStart":{"message":"Click the record button {PH1} to start capturing coverage."},"panels/coverage/CoverageView.ts | clickTheReloadButtonSToReloadAnd":{"message":"Click the reload button {PH1} to reload and start capturing coverage."},"panels/coverage/CoverageView.ts | contentScripts":{"message":"Content scripts"},"panels/coverage/CoverageView.ts | css":{"message":"CSS"},"panels/coverage/CoverageView.ts | export":{"message":"Export..."},"panels/coverage/CoverageView.ts | filterCoverageByType":{"message":"Filter coverage by type"},"panels/coverage/CoverageView.ts | filteredSTotalS":{"message":"Filtered: {PH1}  Total: {PH2}"},"panels/coverage/CoverageView.ts | includeExtensionContentScripts":{"message":"Include extension content scripts"},"panels/coverage/CoverageView.ts | javascript":{"message":"JavaScript"},"panels/coverage/CoverageView.ts | perBlock":{"message":"Per block"},"panels/coverage/CoverageView.ts | perFunction":{"message":"Per function"},"panels/coverage/CoverageView.ts | sOfSSUsedSoFarSUnused":{"message":"{PH1} of {PH2} ({PH3}%) used so far, {PH4} unused."},"panels/coverage/CoverageView.ts | urlFilter":{"message":"URL filter"},"panels/css_overview/components/CSSOverviewStartView.ts | captureOverview":{"message":"Capture overview"},"panels/css_overview/components/CSSOverviewStartView.ts | capturePageCSSOverview":{"message":"Capture an overview of your page’s CSS"},"panels/css_overview/components/CSSOverviewStartView.ts | identifyCSSImprovements":{"message":"Identify potential CSS improvements"},"panels/css_overview/components/CSSOverviewStartView.ts | identifyCSSImprovementsWithExampleIssues":{"message":"Identify potential CSS improvements (e.g. low contrast issues, unused declarations, color or font mismatches)"},"panels/css_overview/components/CSSOverviewStartView.ts | locateAffectedElements":{"message":"Locate the affected elements in the Elements panel"},"panels/css_overview/components/CSSOverviewStartView.ts | quickStartWithCSSOverview":{"message":"Quick start: get started with the new CSS Overview panel"},"panels/css_overview/css_overview-meta.ts | cssOverview":{"message":"CSS Overview"},"panels/css_overview/css_overview-meta.ts | showCssOverview":{"message":"Show CSS Overview"},"panels/css_overview/CSSOverviewCompletedView.ts | aa":{"message":"AA"},"panels/css_overview/CSSOverviewCompletedView.ts | aaa":{"message":"AAA"},"panels/css_overview/CSSOverviewCompletedView.ts | apca":{"message":"APCA"},"panels/css_overview/CSSOverviewCompletedView.ts | attributeSelectors":{"message":"Attribute selectors"},"panels/css_overview/CSSOverviewCompletedView.ts | backgroundColorsS":{"message":"Background colors: {PH1}"},"panels/css_overview/CSSOverviewCompletedView.ts | borderColorsS":{"message":"Border colors: {PH1}"},"panels/css_overview/CSSOverviewCompletedView.ts | classSelectors":{"message":"Class selectors"},"panels/css_overview/CSSOverviewCompletedView.ts | colors":{"message":"Colors"},"panels/css_overview/CSSOverviewCompletedView.ts | contrastIssues":{"message":"Contrast issues"},"panels/css_overview/CSSOverviewCompletedView.ts | contrastIssuesS":{"message":"Contrast issues: {PH1}"},"panels/css_overview/CSSOverviewCompletedView.ts | contrastRatio":{"message":"Contrast ratio"},"panels/css_overview/CSSOverviewCompletedView.ts | cssOverviewElements":{"message":"CSS Overview Elements"},"panels/css_overview/CSSOverviewCompletedView.ts | declaration":{"message":"Declaration"},"panels/css_overview/CSSOverviewCompletedView.ts | element":{"message":"Element"},"panels/css_overview/CSSOverviewCompletedView.ts | elements":{"message":"Elements"},"panels/css_overview/CSSOverviewCompletedView.ts | externalStylesheets":{"message":"External stylesheets"},"panels/css_overview/CSSOverviewCompletedView.ts | fillColorsS":{"message":"Fill colors: {PH1}"},"panels/css_overview/CSSOverviewCompletedView.ts | fontInfo":{"message":"Font info"},"panels/css_overview/CSSOverviewCompletedView.ts | idSelectors":{"message":"ID selectors"},"panels/css_overview/CSSOverviewCompletedView.ts | inlineStyleElements":{"message":"Inline style elements"},"panels/css_overview/CSSOverviewCompletedView.ts | mediaQueries":{"message":"Media queries"},"panels/css_overview/CSSOverviewCompletedView.ts | nOccurrences":{"message":"{n, plural, =1 {# occurrence} other {# occurrences}}"},"panels/css_overview/CSSOverviewCompletedView.ts | nonsimpleSelectors":{"message":"Non-simple selectors"},"panels/css_overview/CSSOverviewCompletedView.ts | overviewSummary":{"message":"Overview summary"},"panels/css_overview/CSSOverviewCompletedView.ts | showElement":{"message":"Show element"},"panels/css_overview/CSSOverviewCompletedView.ts | source":{"message":"Source"},"panels/css_overview/CSSOverviewCompletedView.ts | styleRules":{"message":"Style rules"},"panels/css_overview/CSSOverviewCompletedView.ts | textColorSOverSBackgroundResults":{"message":"Text color {PH1} over {PH2} background results in low contrast for {PH3} elements"},"panels/css_overview/CSSOverviewCompletedView.ts | textColorsS":{"message":"Text colors: {PH1}"},"panels/css_overview/CSSOverviewCompletedView.ts | thereAreNoFonts":{"message":"There are no fonts."},"panels/css_overview/CSSOverviewCompletedView.ts | thereAreNoMediaQueries":{"message":"There are no media queries."},"panels/css_overview/CSSOverviewCompletedView.ts | thereAreNoUnusedDeclarations":{"message":"There are no unused declarations."},"panels/css_overview/CSSOverviewCompletedView.ts | typeSelectors":{"message":"Type selectors"},"panels/css_overview/CSSOverviewCompletedView.ts | universalSelectors":{"message":"Universal selectors"},"panels/css_overview/CSSOverviewCompletedView.ts | unusedDeclarations":{"message":"Unused declarations"},"panels/css_overview/CSSOverviewProcessingView.ts | cancel":{"message":"Cancel"},"panels/css_overview/CSSOverviewSidebarPanel.ts | clearOverview":{"message":"Clear overview"},"panels/css_overview/CSSOverviewUnusedDeclarations.ts | bottomAppliedToAStatically":{"message":"Bottom applied to a statically positioned element"},"panels/css_overview/CSSOverviewUnusedDeclarations.ts | heightAppliedToAnInlineElement":{"message":"Height applied to an inline element"},"panels/css_overview/CSSOverviewUnusedDeclarations.ts | leftAppliedToAStatically":{"message":"Left applied to a statically positioned element"},"panels/css_overview/CSSOverviewUnusedDeclarations.ts | rightAppliedToAStatically":{"message":"Right applied to a statically positioned element"},"panels/css_overview/CSSOverviewUnusedDeclarations.ts | topAppliedToAStatically":{"message":"Top applied to a statically positioned element"},"panels/css_overview/CSSOverviewUnusedDeclarations.ts | verticalAlignmentAppliedTo":{"message":"Vertical alignment applied to element which is neither inline nor table-cell"},"panels/css_overview/CSSOverviewUnusedDeclarations.ts | widthAppliedToAnInlineElement":{"message":"Width applied to an inline element"},"panels/developer_resources/developer_resources-meta.ts | developerResources":{"message":"Developer Resources"},"panels/developer_resources/developer_resources-meta.ts | showDeveloperResources":{"message":"Show Developer Resources"},"panels/developer_resources/DeveloperResourcesListView.ts | copyInitiatorUrl":{"message":"Copy initiator URL"},"panels/developer_resources/DeveloperResourcesListView.ts | copyUrl":{"message":"Copy URL"},"panels/developer_resources/DeveloperResourcesListView.ts | developerResources":{"message":"Developer Resources"},"panels/developer_resources/DeveloperResourcesListView.ts | error":{"message":"Error"},"panels/developer_resources/DeveloperResourcesListView.ts | failure":{"message":"failure"},"panels/developer_resources/DeveloperResourcesListView.ts | initiator":{"message":"Initiator"},"panels/developer_resources/DeveloperResourcesListView.ts | pending":{"message":"pending"},"panels/developer_resources/DeveloperResourcesListView.ts | sBytes":{"message":"{n, plural, =1 {# byte} other {# bytes}}"},"panels/developer_resources/DeveloperResourcesListView.ts | status":{"message":"Status"},"panels/developer_resources/DeveloperResourcesListView.ts | success":{"message":"success"},"panels/developer_resources/DeveloperResourcesListView.ts | totalBytes":{"message":"Total Bytes"},"panels/developer_resources/DeveloperResourcesListView.ts | url":{"message":"URL"},"panels/developer_resources/DeveloperResourcesView.ts | enableLoadingThroughTarget":{"message":"Enable loading through target"},"panels/developer_resources/DeveloperResourcesView.ts | enterTextToSearchTheUrlAndError":{"message":"Enter text to search the URL and Error columns"},"panels/developer_resources/DeveloperResourcesView.ts | loadHttpsDeveloperResources":{"message":"Load HTTP(S) developer resources through the inspected target"},"panels/developer_resources/DeveloperResourcesView.ts | resources":{"message":"{n, plural, =1 {# resource} other {# resources}}"},"panels/developer_resources/DeveloperResourcesView.ts | resourcesCurrentlyLoading":{"message":"{PH1} resources, {PH2} currently loading"},"panels/elements/ClassesPaneWidget.ts | addNewClass":{"message":"Add new class"},"panels/elements/ClassesPaneWidget.ts | classesSAdded":{"message":"Classes {PH1} added"},"panels/elements/ClassesPaneWidget.ts | classSAdded":{"message":"Class {PH1} added"},"panels/elements/ClassesPaneWidget.ts | elementClasses":{"message":"Element Classes"},"panels/elements/ColorSwatchPopoverIcon.ts | openCubicBezierEditor":{"message":"Open cubic bezier editor"},"panels/elements/ColorSwatchPopoverIcon.ts | openShadowEditor":{"message":"Open shadow editor"},"panels/elements/components/AccessibilityTreeNode.ts | ignored":{"message":"Ignored"},"panels/elements/components/AdornerSettingsPane.ts | closeButton":{"message":"Close"},"panels/elements/components/AdornerSettingsPane.ts | settingsTitle":{"message":"Show badges"},"panels/elements/components/ElementsBreadcrumbs.ts | breadcrumbs":{"message":"DOM tree breadcrumbs"},"panels/elements/components/ElementsBreadcrumbsUtils.ts | text":{"message":"(text)"},"panels/elements/components/LayoutPane.ts | chooseElementOverlayColor":{"message":"Choose the overlay color for this element"},"panels/elements/components/LayoutPane.ts | colorPickerOpened":{"message":"Color picker opened."},"panels/elements/components/LayoutPane.ts | flexbox":{"message":"Flexbox"},"panels/elements/components/LayoutPane.ts | flexboxOverlays":{"message":"Flexbox overlays"},"panels/elements/components/LayoutPane.ts | grid":{"message":"Grid"},"panels/elements/components/LayoutPane.ts | gridOverlays":{"message":"Grid overlays"},"panels/elements/components/LayoutPane.ts | noFlexboxLayoutsFoundOnThisPage":{"message":"No flexbox layouts found on this page"},"panels/elements/components/LayoutPane.ts | noGridLayoutsFoundOnThisPage":{"message":"No grid layouts found on this page"},"panels/elements/components/LayoutPane.ts | overlayDisplaySettings":{"message":"Overlay display settings"},"panels/elements/components/LayoutPane.ts | showElementInTheElementsPanel":{"message":"Show element in the Elements panel"},"panels/elements/components/StylePropertyEditor.ts | deselectButton":{"message":"Remove {propertyName}: {propertyValue}"},"panels/elements/components/StylePropertyEditor.ts | selectButton":{"message":"Add {propertyName}: {propertyValue}"},"panels/elements/ComputedStyleWidget.ts | filter":{"message":"Filter"},"panels/elements/ComputedStyleWidget.ts | filterComputedStyles":{"message":"Filter Computed Styles"},"panels/elements/ComputedStyleWidget.ts | group":{"message":"Group"},"panels/elements/ComputedStyleWidget.ts | navigateToSelectorSource":{"message":"Navigate to selector source"},"panels/elements/ComputedStyleWidget.ts | navigateToStyle":{"message":"Navigate to style"},"panels/elements/ComputedStyleWidget.ts | noMatchingProperty":{"message":"No matching property"},"panels/elements/ComputedStyleWidget.ts | showAll":{"message":"Show all"},"panels/elements/DOMLinkifier.ts | node":{"message":"<node>"},"panels/elements/elements-meta.ts | captureAreaScreenshot":{"message":"Capture area screenshot"},"panels/elements/elements-meta.ts | copyStyles":{"message":"Copy styles"},"panels/elements/elements-meta.ts | disableDomWordWrap":{"message":"Disable DOM word wrap"},"panels/elements/elements-meta.ts | duplicateElement":{"message":"Duplicate element"},"panels/elements/elements-meta.ts | editAsHtml":{"message":"Edit as HTML"},"panels/elements/elements-meta.ts | elements":{"message":"Elements"},"panels/elements/elements-meta.ts | enableDomWordWrap":{"message":"Enable DOM word wrap"},"panels/elements/elements-meta.ts | eventListeners":{"message":"Event Listeners"},"panels/elements/elements-meta.ts | hideElement":{"message":"Hide element"},"panels/elements/elements-meta.ts | hideHtmlComments":{"message":"Hide HTML comments"},"panels/elements/elements-meta.ts | layout":{"message":"Layout"},"panels/elements/elements-meta.ts | properties":{"message":"Properties"},"panels/elements/elements-meta.ts | redo":{"message":"Redo"},"panels/elements/elements-meta.ts | revealDomNodeOnHover":{"message":"Reveal DOM node on hover"},"panels/elements/elements-meta.ts | selectAnElementInThePageTo":{"message":"Select an element in the page to inspect it"},"panels/elements/elements-meta.ts | showDetailedInspectTooltip":{"message":"Show detailed inspect tooltip"},"panels/elements/elements-meta.ts | showElements":{"message":"Show Elements"},"panels/elements/elements-meta.ts | showEventListeners":{"message":"Show Event Listeners"},"panels/elements/elements-meta.ts | showHtmlComments":{"message":"Show HTML comments"},"panels/elements/elements-meta.ts | showLayout":{"message":"Show Layout"},"panels/elements/elements-meta.ts | showProperties":{"message":"Show Properties"},"panels/elements/elements-meta.ts | showStackTrace":{"message":"Show Stack Trace"},"panels/elements/elements-meta.ts | showUserAgentShadowDOM":{"message":"Show user agent shadow DOM"},"panels/elements/elements-meta.ts | stackTrace":{"message":"Stack Trace"},"panels/elements/elements-meta.ts | undo":{"message":"Undo"},"panels/elements/elements-meta.ts | wordWrap":{"message":"Word wrap"},"panels/elements/ElementsPanel.ts | computed":{"message":"Computed"},"panels/elements/ElementsPanel.ts | computedStylesHidden":{"message":"Computed Styles sidebar hidden"},"panels/elements/ElementsPanel.ts | computedStylesShown":{"message":"Computed Styles sidebar shown"},"panels/elements/ElementsPanel.ts | domTreeExplorer":{"message":"DOM tree explorer"},"panels/elements/ElementsPanel.ts | elementStateS":{"message":"Element state: {PH1}"},"panels/elements/ElementsPanel.ts | findByStringSelectorOrXpath":{"message":"Find by string, selector, or XPath"},"panels/elements/ElementsPanel.ts | frame":{"message":"Frame"},"panels/elements/ElementsPanel.ts | hideComputedStylesSidebar":{"message":"Hide Computed Styles sidebar"},"panels/elements/ElementsPanel.ts | nodeCannotBeFoundInTheCurrent":{"message":"Node cannot be found in the current page."},"panels/elements/ElementsPanel.ts | revealInElementsPanel":{"message":"Reveal in Elements panel"},"panels/elements/ElementsPanel.ts | showComputedStylesSidebar":{"message":"Show Computed Styles sidebar"},"panels/elements/ElementsPanel.ts | sidePanelContent":{"message":"Side panel content"},"panels/elements/ElementsPanel.ts | sidePanelToolbar":{"message":"Side panel toolbar"},"panels/elements/ElementsPanel.ts | styles":{"message":"Styles"},"panels/elements/ElementsPanel.ts | switchToAccessibilityTreeView":{"message":"Switch to Accessibility Tree view"},"panels/elements/ElementsPanel.ts | switchToDomTreeView":{"message":"Switch to DOM Tree view"},"panels/elements/ElementsPanel.ts | theDeferredDomNodeCouldNotBe":{"message":"The deferred DOM Node could not be resolved to a valid node."},"panels/elements/ElementsPanel.ts | theRemoteObjectCouldNotBe":{"message":"The remote object could not be resolved to a valid node."},"panels/elements/ElementStatePaneWidget.ts | forceElementState":{"message":"Force element state"},"panels/elements/ElementStatePaneWidget.ts | toggleElementState":{"message":"Toggle Element State"},"panels/elements/ElementsTreeElement.ts | addAttribute":{"message":"Add attribute"},"panels/elements/ElementsTreeElement.ts | captureNodeScreenshot":{"message":"Capture node screenshot"},"panels/elements/ElementsTreeElement.ts | children":{"message":"Children:"},"panels/elements/ElementsTreeElement.ts | collapseChildren":{"message":"Collapse children"},"panels/elements/ElementsTreeElement.ts | copy":{"message":"Copy"},"panels/elements/ElementsTreeElement.ts | copyElement":{"message":"Copy element"},"panels/elements/ElementsTreeElement.ts | copyFullXpath":{"message":"Copy full XPath"},"panels/elements/ElementsTreeElement.ts | copyJsPath":{"message":"Copy JS path"},"panels/elements/ElementsTreeElement.ts | copyOuterhtml":{"message":"Copy outerHTML"},"panels/elements/ElementsTreeElement.ts | copySelector":{"message":"Copy selector"},"panels/elements/ElementsTreeElement.ts | copyStyles":{"message":"Copy styles"},"panels/elements/ElementsTreeElement.ts | copyXpath":{"message":"Copy XPath"},"panels/elements/ElementsTreeElement.ts | cut":{"message":"Cut"},"panels/elements/ElementsTreeElement.ts | deleteElement":{"message":"Delete element"},"panels/elements/ElementsTreeElement.ts | disableFlexMode":{"message":"Disable flex mode"},"panels/elements/ElementsTreeElement.ts | disableGridMode":{"message":"Disable grid mode"},"panels/elements/ElementsTreeElement.ts | disableScrollSnap":{"message":"Disable scroll-snap overlay"},"panels/elements/ElementsTreeElement.ts | duplicateElement":{"message":"Duplicate element"},"panels/elements/ElementsTreeElement.ts | editAsHtml":{"message":"Edit as HTML"},"panels/elements/ElementsTreeElement.ts | editAttribute":{"message":"Edit attribute"},"panels/elements/ElementsTreeElement.ts | editText":{"message":"Edit text"},"panels/elements/ElementsTreeElement.ts | enableFlexMode":{"message":"Enable flex mode"},"panels/elements/ElementsTreeElement.ts | enableGridMode":{"message":"Enable grid mode"},"panels/elements/ElementsTreeElement.ts | enableScrollSnap":{"message":"Enable scroll-snap overlay"},"panels/elements/ElementsTreeElement.ts | enterIsolationMode":{"message":"Enter Isolation Mode"},"panels/elements/ElementsTreeElement.ts | exitIsolationMode":{"message":"Exit Isolation Mode"},"panels/elements/ElementsTreeElement.ts | expandRecursively":{"message":"Expand recursively"},"panels/elements/ElementsTreeElement.ts | focus":{"message":"Focus"},"panels/elements/ElementsTreeElement.ts | forceState":{"message":"Force state"},"panels/elements/ElementsTreeElement.ts | hideElement":{"message":"Hide element"},"panels/elements/ElementsTreeElement.ts | paste":{"message":"Paste"},"panels/elements/ElementsTreeElement.ts | scrollIntoView":{"message":"Scroll into view"},"panels/elements/ElementsTreeElement.ts | showFrameDetails":{"message":"Show iframe details"},"panels/elements/ElementsTreeElement.ts | thisFrameWasIdentifiedAsAnAd":{"message":"This frame was identified as an ad frame"},"panels/elements/ElementsTreeElement.ts | useSInTheConsoleToReferToThis":{"message":"Use {PH1} in the console to refer to this element."},"panels/elements/ElementsTreeElement.ts | valueIsTooLargeToEdit":{"message":"<value is too large to edit>"},"panels/elements/ElementsTreeOutline.ts | adornerSettings":{"message":"Badge settings…"},"panels/elements/ElementsTreeOutline.ts | pageDom":{"message":"Page DOM"},"panels/elements/ElementsTreeOutline.ts | reveal":{"message":"reveal"},"panels/elements/ElementsTreeOutline.ts | showAllNodesDMore":{"message":"Show All Nodes ({PH1} More)"},"panels/elements/ElementsTreeOutline.ts | storeAsGlobalVariable":{"message":"Store as global variable"},"panels/elements/EventListenersWidget.ts | all":{"message":"All"},"panels/elements/EventListenersWidget.ts | ancestors":{"message":"Ancestors"},"panels/elements/EventListenersWidget.ts | blocking":{"message":"Blocking"},"panels/elements/EventListenersWidget.ts | eventListenersCategory":{"message":"Event listeners category"},"panels/elements/EventListenersWidget.ts | frameworkListeners":{"message":"Framework listeners"},"panels/elements/EventListenersWidget.ts | passive":{"message":"Passive"},"panels/elements/EventListenersWidget.ts | refresh":{"message":"Refresh"},"panels/elements/EventListenersWidget.ts | resolveEventListenersBoundWith":{"message":"Resolve event listeners bound with framework"},"panels/elements/EventListenersWidget.ts | showListenersOnTheAncestors":{"message":"Show listeners on the ancestors"},"panels/elements/LayersWidget.ts | cssLayersTitle":{"message":"CSS layers"},"panels/elements/LayersWidget.ts | toggleCSSLayers":{"message":"Toggle CSS Layers view"},"panels/elements/MarkerDecorator.ts | domBreakpoint":{"message":"DOM Breakpoint"},"panels/elements/MarkerDecorator.ts | elementIsHidden":{"message":"Element is hidden"},"panels/elements/NodeStackTraceWidget.ts | noStackTraceAvailable":{"message":"No stack trace available"},"panels/elements/PlatformFontsWidget.ts | dGlyphs":{"message":"{n, plural, =1 {(# glyph)} other {(# glyphs)}}"},"panels/elements/PlatformFontsWidget.ts | localFile":{"message":"Local file"},"panels/elements/PlatformFontsWidget.ts | networkResource":{"message":"Network resource"},"panels/elements/PlatformFontsWidget.ts | renderedFonts":{"message":"Rendered Fonts"},"panels/elements/PropertiesWidget.ts | filter":{"message":"Filter"},"panels/elements/PropertiesWidget.ts | filterProperties":{"message":"Filter Properties"},"panels/elements/PropertiesWidget.ts | noMatchingProperty":{"message":"No matching property"},"panels/elements/PropertiesWidget.ts | showAll":{"message":"Show all"},"panels/elements/PropertiesWidget.ts | showAllTooltip":{"message":"When unchecked, only properties whose values are neither null nor undefined will be shown"},"panels/elements/StylePropertyTreeElement.ts | copyAllCssDeclarationsAsJs":{"message":"Copy all declarations as JS"},"panels/elements/StylePropertyTreeElement.ts | copyAllDeclarations":{"message":"Copy all declarations"},"panels/elements/StylePropertyTreeElement.ts | copyCssDeclarationAsJs":{"message":"Copy declaration as JS"},"panels/elements/StylePropertyTreeElement.ts | copyDeclaration":{"message":"Copy declaration"},"panels/elements/StylePropertyTreeElement.ts | copyProperty":{"message":"Copy property"},"panels/elements/StylePropertyTreeElement.ts | copyRule":{"message":"Copy rule"},"panels/elements/StylePropertyTreeElement.ts | copyValue":{"message":"Copy value"},"panels/elements/StylePropertyTreeElement.ts | flexboxEditorButton":{"message":"Open flexbox editor"},"panels/elements/StylePropertyTreeElement.ts | gridEditorButton":{"message":"Open grid editor"},"panels/elements/StylePropertyTreeElement.ts | openColorPickerS":{"message":"Open color picker. {PH1}"},"panels/elements/StylePropertyTreeElement.ts | revealInSourcesPanel":{"message":"Reveal in Sources panel"},"panels/elements/StylePropertyTreeElement.ts | shiftClickToChangeColorFormat":{"message":"Shift + Click to change color format."},"panels/elements/StylePropertyTreeElement.ts | togglePropertyAndContinueEditing":{"message":"Toggle property and continue editing"},"panels/elements/StylePropertyTreeElement.ts | valueForSettingSSIsOutsideThe":{"message":"Value for setting “{PH1}” {PH2} is outside the supported range [{PH3}, {PH4}] for font-family “{PH5}”."},"panels/elements/StylePropertyTreeElement.ts | viewComputedValue":{"message":"View computed value"},"panels/elements/StylesSidebarPane.ts | clickToRevealLayer":{"message":"Click to reveal layer in layer tree"},"panels/elements/StylesSidebarPane.ts | constructedStylesheet":{"message":"constructed stylesheet"},"panels/elements/StylesSidebarPane.ts | copiedToClipboard":{"message":"Copied to clipboard"},"panels/elements/StylesSidebarPane.ts | copyAllCSSChanges":{"message":"Copy all the CSS changes"},"panels/elements/StylesSidebarPane.ts | copyAllDeclarations":{"message":"Copy all declarations"},"panels/elements/StylesSidebarPane.ts | copyRule":{"message":"Copy rule"},"panels/elements/StylesSidebarPane.ts | copySelector":{"message":"Copy selector"},"panels/elements/StylesSidebarPane.ts | cssPropertyName":{"message":"CSS property name: {PH1}"},"panels/elements/StylesSidebarPane.ts | cssPropertyValue":{"message":"CSS property value: {PH1}"},"panels/elements/StylesSidebarPane.ts | cssSelector":{"message":"CSS selector"},"panels/elements/StylesSidebarPane.ts | filter":{"message":"Filter"},"panels/elements/StylesSidebarPane.ts | filterStyles":{"message":"Filter Styles"},"panels/elements/StylesSidebarPane.ts | incrementdecrementWithMousewheelHundred":{"message":"Increment/decrement with mousewheel or up/down keys. {PH1}: ±100, Shift: ±10, Alt: ±0.1"},"panels/elements/StylesSidebarPane.ts | incrementdecrementWithMousewheelOne":{"message":"Increment/decrement with mousewheel or up/down keys. {PH1}: R ±1, Shift: G ±1, Alt: B ±1"},"panels/elements/StylesSidebarPane.ts | inheritedFroms":{"message":"Inherited from "},"panels/elements/StylesSidebarPane.ts | injectedStylesheet":{"message":"injected stylesheet"},"panels/elements/StylesSidebarPane.ts | insertStyleRuleBelow":{"message":"Insert Style Rule Below"},"panels/elements/StylesSidebarPane.ts | invalidPropertyValue":{"message":"Invalid property value"},"panels/elements/StylesSidebarPane.ts | invalidString":{"message":"{PH1}, property name: {PH2}, property value: {PH3}"},"panels/elements/StylesSidebarPane.ts | layer":{"message":"Layer"},"panels/elements/StylesSidebarPane.ts | newStyleRule":{"message":"New Style Rule"},"panels/elements/StylesSidebarPane.ts | noMatchingSelectorOrStyle":{"message":"No matching selector or style"},"panels/elements/StylesSidebarPane.ts | pseudoSElement":{"message":"Pseudo ::{PH1} element"},"panels/elements/StylesSidebarPane.ts | sattributesStyle":{"message":"{PH1}[Attributes Style]"},"panels/elements/StylesSidebarPane.ts | showAllPropertiesSMore":{"message":"Show All Properties ({PH1} more)"},"panels/elements/StylesSidebarPane.ts | styleAttribute":{"message":"style attribute"},"panels/elements/StylesSidebarPane.ts | unknownPropertyName":{"message":"Unknown property name"},"panels/elements/StylesSidebarPane.ts | userAgentStylesheet":{"message":"user agent stylesheet"},"panels/elements/StylesSidebarPane.ts | viaInspector":{"message":"via inspector"},"panels/emulation/DeviceModeToolbar.ts | addDevicePixelRatio":{"message":"Add device pixel ratio"},"panels/emulation/DeviceModeToolbar.ts | addDeviceType":{"message":"Add device type"},"panels/emulation/DeviceModeToolbar.ts | autoadjustZoom":{"message":"Auto-adjust zoom"},"panels/emulation/DeviceModeToolbar.ts | closeDevtools":{"message":"Close DevTools"},"panels/emulation/DeviceModeToolbar.ts | defaultF":{"message":"Default: {PH1}"},"panels/emulation/DeviceModeToolbar.ts | devicePixelRatio":{"message":"Device pixel ratio"},"panels/emulation/DeviceModeToolbar.ts | deviceType":{"message":"Device type"},"panels/emulation/DeviceModeToolbar.ts | dimensions":{"message":"Dimensions"},"panels/emulation/DeviceModeToolbar.ts | edit":{"message":"Edit…"},"panels/emulation/DeviceModeToolbar.ts | experimentalWebPlatformFeature":{"message":"\\"Experimental Web Platform Feature\\" flag is enabled. Click to disable it."},"panels/emulation/DeviceModeToolbar.ts | experimentalWebPlatformFeatureFlag":{"message":"\\"Experimental Web Platform Feature\\" flag is disabled. Click to enable it."},"panels/emulation/DeviceModeToolbar.ts | fitToWindowF":{"message":"Fit to window ({PH1}%)"},"panels/emulation/DeviceModeToolbar.ts | heightLeaveEmptyForFull":{"message":"Height (leave empty for full)"},"panels/emulation/DeviceModeToolbar.ts | hideDeviceFrame":{"message":"Hide device frame"},"panels/emulation/DeviceModeToolbar.ts | hideMediaQueries":{"message":"Hide media queries"},"panels/emulation/DeviceModeToolbar.ts | hideRulers":{"message":"Hide rulers"},"panels/emulation/DeviceModeToolbar.ts | landscape":{"message":"Landscape"},"panels/emulation/DeviceModeToolbar.ts | moreOptions":{"message":"More options"},"panels/emulation/DeviceModeToolbar.ts | none":{"message":"None"},"panels/emulation/DeviceModeToolbar.ts | portrait":{"message":"Portrait"},"panels/emulation/DeviceModeToolbar.ts | removeDevicePixelRatio":{"message":"Remove device pixel ratio"},"panels/emulation/DeviceModeToolbar.ts | removeDeviceType":{"message":"Remove device type"},"panels/emulation/DeviceModeToolbar.ts | resetToDefaults":{"message":"Reset to defaults"},"panels/emulation/DeviceModeToolbar.ts | responsive":{"message":"Responsive"},"panels/emulation/DeviceModeToolbar.ts | rotate":{"message":"Rotate"},"panels/emulation/DeviceModeToolbar.ts | screenOrientationOptions":{"message":"Screen orientation options"},"panels/emulation/DeviceModeToolbar.ts | showDeviceFrame":{"message":"Show device frame"},"panels/emulation/DeviceModeToolbar.ts | showMediaQueries":{"message":"Show media queries"},"panels/emulation/DeviceModeToolbar.ts | showRulers":{"message":"Show rulers"},"panels/emulation/DeviceModeToolbar.ts | toggleDualscreenMode":{"message":"Toggle dual-screen mode"},"panels/emulation/DeviceModeToolbar.ts | width":{"message":"Width"},"panels/emulation/DeviceModeToolbar.ts | zoom":{"message":"Zoom"},"panels/emulation/DeviceModeView.ts | doubleclickForFullHeight":{"message":"Double-click for full height"},"panels/emulation/DeviceModeView.ts | laptop":{"message":"Laptop"},"panels/emulation/DeviceModeView.ts | laptopL":{"message":"Laptop L"},"panels/emulation/DeviceModeView.ts | mobileL":{"message":"Mobile L"},"panels/emulation/DeviceModeView.ts | mobileM":{"message":"Mobile M"},"panels/emulation/DeviceModeView.ts | mobileS":{"message":"Mobile S"},"panels/emulation/DeviceModeView.ts | tablet":{"message":"Tablet"},"panels/emulation/emulation-meta.ts | captureFullSizeScreenshot":{"message":"Capture full size screenshot"},"panels/emulation/emulation-meta.ts | captureNodeScreenshot":{"message":"Capture node screenshot"},"panels/emulation/emulation-meta.ts | captureScreenshot":{"message":"Capture screenshot"},"panels/emulation/emulation-meta.ts | device":{"message":"device"},"panels/emulation/emulation-meta.ts | hideDeviceFrame":{"message":"Hide device frame"},"panels/emulation/emulation-meta.ts | hideMediaQueries":{"message":"Hide media queries"},"panels/emulation/emulation-meta.ts | hideRulers":{"message":"Hide rulers in the Device Mode toolbar"},"panels/emulation/emulation-meta.ts | showDeviceFrame":{"message":"Show device frame"},"panels/emulation/emulation-meta.ts | showMediaQueries":{"message":"Show media queries"},"panels/emulation/emulation-meta.ts | showRulers":{"message":"Show rulers in the Device Mode toolbar"},"panels/emulation/emulation-meta.ts | toggleDeviceToolbar":{"message":"Toggle device toolbar"},"panels/emulation/MediaQueryInspector.ts | revealInSourceCode":{"message":"Reveal in source code"},"panels/event_listeners/EventListenersView.ts | deleteEventListener":{"message":"Delete event listener"},"panels/event_listeners/EventListenersView.ts | noEventListeners":{"message":"No event listeners"},"panels/event_listeners/EventListenersView.ts | passive":{"message":"Passive"},"panels/event_listeners/EventListenersView.ts | remove":{"message":"Remove"},"panels/event_listeners/EventListenersView.ts | revealInElementsPanel":{"message":"Reveal in Elements panel"},"panels/event_listeners/EventListenersView.ts | togglePassive":{"message":"Toggle Passive"},"panels/event_listeners/EventListenersView.ts | toggleWhetherEventListenerIs":{"message":"Toggle whether event listener is passive or blocking"},"panels/input/input-meta.ts | inputs":{"message":"Inputs"},"panels/input/input-meta.ts | pause":{"message":"Pause"},"panels/input/input-meta.ts | resume":{"message":"Resume"},"panels/input/input-meta.ts | showInputs":{"message":"Show Inputs"},"panels/input/input-meta.ts | startRecording":{"message":"Start recording"},"panels/input/input-meta.ts | startReplaying":{"message":"Start replaying"},"panels/input/input-meta.ts | stopRecording":{"message":"Stop recording"},"panels/input/InputTimeline.ts | clearAll":{"message":"Clear all"},"panels/input/InputTimeline.ts | loadProfile":{"message":"Load profile…"},"panels/input/InputTimeline.ts | saveProfile":{"message":"Save profile…"},"panels/issues/AffectedBlockedByResponseView.ts | blockedResource":{"message":"Blocked Resource"},"panels/issues/AffectedBlockedByResponseView.ts | nRequests":{"message":"{n, plural, =1 {# request} other {# requests}}"},"panels/issues/AffectedBlockedByResponseView.ts | parentFrame":{"message":"Parent Frame"},"panels/issues/AffectedBlockedByResponseView.ts | requestC":{"message":"Request"},"panels/issues/AffectedCookiesView.ts | domain":{"message":"Domain"},"panels/issues/AffectedCookiesView.ts | filterSetCookieTitle":{"message":"Show network requests that include this Set-Cookie header in the network panel"},"panels/issues/AffectedCookiesView.ts | name":{"message":"Name"},"panels/issues/AffectedCookiesView.ts | nCookies":{"message":"{n, plural, =1 {# cookie} other {# cookies}}"},"panels/issues/AffectedCookiesView.ts | nRawCookieLines":{"message":"{n, plural, =1 {1 Raw Set-Cookie header} other {# Raw Set-Cookie headers}}"},"panels/issues/AffectedCookiesView.ts | path":{"message":"Path"},"panels/issues/AffectedDirectivesView.ts | blocked":{"message":"blocked"},"panels/issues/AffectedDirectivesView.ts | clickToRevealTheViolatingDomNode":{"message":"Click to reveal the violating DOM node in the Elements panel"},"panels/issues/AffectedDirectivesView.ts | directiveC":{"message":"Directive"},"panels/issues/AffectedDirectivesView.ts | element":{"message":"Element"},"panels/issues/AffectedDirectivesView.ts | nDirectives":{"message":"{n, plural, =1 {# directive} other {# directives}}"},"panels/issues/AffectedDirectivesView.ts | reportonly":{"message":"report-only"},"panels/issues/AffectedDirectivesView.ts | resourceC":{"message":"Resource"},"panels/issues/AffectedDirectivesView.ts | sourceLocation":{"message":"Source Location"},"panels/issues/AffectedDirectivesView.ts | status":{"message":"Status"},"panels/issues/AffectedDocumentsInQuirksModeView.ts | documentInTheDOMTree":{"message":"Document in the DOM tree"},"panels/issues/AffectedDocumentsInQuirksModeView.ts | mode":{"message":"Mode"},"panels/issues/AffectedDocumentsInQuirksModeView.ts | nDocuments":{"message":"{n, plural, =1 { document} other { documents}}"},"panels/issues/AffectedDocumentsInQuirksModeView.ts | url":{"message":"URL"},"panels/issues/AffectedElementsView.ts | nElements":{"message":"{n, plural, =1 {# element} other {# elements}}"},"panels/issues/AffectedElementsWithLowContrastView.ts | contrastRatio":{"message":"Contrast ratio"},"panels/issues/AffectedElementsWithLowContrastView.ts | element":{"message":"Element"},"panels/issues/AffectedElementsWithLowContrastView.ts | minimumAA":{"message":"Minimum AA ratio"},"panels/issues/AffectedElementsWithLowContrastView.ts | minimumAAA":{"message":"Minimum AAA ratio"},"panels/issues/AffectedElementsWithLowContrastView.ts | textSize":{"message":"Text size"},"panels/issues/AffectedElementsWithLowContrastView.ts | textWeight":{"message":"Text weight"},"panels/issues/AffectedHeavyAdView.ts | cpuPeakLimit":{"message":"CPU peak limit"},"panels/issues/AffectedHeavyAdView.ts | cpuTotalLimit":{"message":"CPU total limit"},"panels/issues/AffectedHeavyAdView.ts | frameUrl":{"message":"Frame URL"},"panels/issues/AffectedHeavyAdView.ts | limitExceeded":{"message":"Limit exceeded"},"panels/issues/AffectedHeavyAdView.ts | networkLimit":{"message":"Network limit"},"panels/issues/AffectedHeavyAdView.ts | nResources":{"message":"{n, plural, =1 {# resource} other {# resources}}"},"panels/issues/AffectedHeavyAdView.ts | removed":{"message":"Removed"},"panels/issues/AffectedHeavyAdView.ts | resolutionStatus":{"message":"Resolution Status"},"panels/issues/AffectedHeavyAdView.ts | warned":{"message":"Warned"},"panels/issues/AffectedResourcesView.ts | clickToRevealTheFramesDomNodeIn":{"message":"Click to reveal the frame\'s DOM node in the Elements panel"},"panels/issues/AffectedResourcesView.ts | unavailable":{"message":"unavailable"},"panels/issues/AffectedResourcesView.ts | unknown":{"message":"unknown"},"panels/issues/AffectedSharedArrayBufferIssueDetailsView.ts | aSharedarraybufferWas":{"message":"A SharedArrayBuffer was instantiated in a context that is not cross-origin isolated"},"panels/issues/AffectedSharedArrayBufferIssueDetailsView.ts | blocked":{"message":"blocked"},"panels/issues/AffectedSharedArrayBufferIssueDetailsView.ts | instantiation":{"message":"Instantiation"},"panels/issues/AffectedSharedArrayBufferIssueDetailsView.ts | nViolations":{"message":"{n, plural, =1 {# violation} other {# violations}}"},"panels/issues/AffectedSharedArrayBufferIssueDetailsView.ts | sharedarraybufferWasTransferedTo":{"message":"SharedArrayBuffer was transfered to a context that is not cross-origin isolated"},"panels/issues/AffectedSharedArrayBufferIssueDetailsView.ts | sourceLocation":{"message":"Source Location"},"panels/issues/AffectedSharedArrayBufferIssueDetailsView.ts | status":{"message":"Status"},"panels/issues/AffectedSharedArrayBufferIssueDetailsView.ts | transfer":{"message":"Transfer"},"panels/issues/AffectedSharedArrayBufferIssueDetailsView.ts | trigger":{"message":"Trigger"},"panels/issues/AffectedSharedArrayBufferIssueDetailsView.ts | warning":{"message":"warning"},"panels/issues/AffectedSourcesView.ts | nSources":{"message":"{n, plural, =1 {# source} other {# sources}}"},"panels/issues/AffectedTrustedWebActivityIssueDetailsView.ts | nResources":{"message":"{n, plural, =1 {# resource} other {# resources}}"},"panels/issues/AffectedTrustedWebActivityIssueDetailsView.ts | packageName":{"message":"Package name"},"panels/issues/AffectedTrustedWebActivityIssueDetailsView.ts | packageSignature":{"message":"Package signature"},"panels/issues/AffectedTrustedWebActivityIssueDetailsView.ts | statusCode":{"message":"Status code"},"panels/issues/AffectedTrustedWebActivityIssueDetailsView.ts | url":{"message":"Url"},"panels/issues/AttributionReportingIssueDetailsView.ts | element":{"message":"Element"},"panels/issues/AttributionReportingIssueDetailsView.ts | frame":{"message":"Frame"},"panels/issues/AttributionReportingIssueDetailsView.ts | invalidEventSourceTriggerData":{"message":"Invalid event-source-trigger-data"},"panels/issues/AttributionReportingIssueDetailsView.ts | invalidSourceEventId":{"message":"Invalid attributionsourceeventid"},"panels/issues/AttributionReportingIssueDetailsView.ts | invalidSourceExpiry":{"message":"Invalid attributionexpiry"},"panels/issues/AttributionReportingIssueDetailsView.ts | invalidSourcePriority":{"message":"Invalid attributionsourcepriority"},"panels/issues/AttributionReportingIssueDetailsView.ts | invalidTriggerData":{"message":"Invalid trigger-data"},"panels/issues/AttributionReportingIssueDetailsView.ts | invalidTriggerDedupKey":{"message":"Invalid dedup-key"},"panels/issues/AttributionReportingIssueDetailsView.ts | invalidTriggerPriority":{"message":"Invalid priority"},"panels/issues/AttributionReportingIssueDetailsView.ts | nViolations":{"message":"{n, plural, =1 {# violation} other {# violations}}"},"panels/issues/AttributionReportingIssueDetailsView.ts | request":{"message":"Request"},"panels/issues/AttributionReportingIssueDetailsView.ts | untrustworthyOrigin":{"message":"Untrustworthy origin"},"panels/issues/components/HideIssuesMenu.ts | tooltipTitle":{"message":"Hide issues"},"panels/issues/CorsIssueDetailsView.ts | allowCredentialsValueFromHeader":{"message":"Access-Control-Allow-Credentials Header Value"},"panels/issues/CorsIssueDetailsView.ts | allowedOrigin":{"message":"Allowed Origin (from header)"},"panels/issues/CorsIssueDetailsView.ts | blocked":{"message":"blocked"},"panels/issues/CorsIssueDetailsView.ts | disallowedRequestHeader":{"message":"Disallowed Request Header"},"panels/issues/CorsIssueDetailsView.ts | disallowedRequestMethod":{"message":"Disallowed Request Method"},"panels/issues/CorsIssueDetailsView.ts | failedRequest":{"message":"Failed Request"},"panels/issues/CorsIssueDetailsView.ts | header":{"message":"Header"},"panels/issues/CorsIssueDetailsView.ts | initiatorAddressSpace":{"message":"Initiator Address"},"panels/issues/CorsIssueDetailsView.ts | initiatorContext":{"message":"Initiator Context"},"panels/issues/CorsIssueDetailsView.ts | insecure":{"message":"insecure"},"panels/issues/CorsIssueDetailsView.ts | invalidValue":{"message":"Invalid Value (if available)"},"panels/issues/CorsIssueDetailsView.ts | nRequests":{"message":"{n, plural, =1 {# request} other {# requests}}"},"panels/issues/CorsIssueDetailsView.ts | preflightDisallowedRedirect":{"message":"Response to preflight was a redirect"},"panels/issues/CorsIssueDetailsView.ts | preflightInvalidStatus":{"message":"HTTP status of preflight request didn\'t indicate success"},"panels/issues/CorsIssueDetailsView.ts | preflightRequest":{"message":"Preflight Request"},"panels/issues/CorsIssueDetailsView.ts | preflightRequestIfProblematic":{"message":"Preflight Request (if problematic)"},"panels/issues/CorsIssueDetailsView.ts | problem":{"message":"Problem"},"panels/issues/CorsIssueDetailsView.ts | problemInvalidValue":{"message":"Invalid Value"},"panels/issues/CorsIssueDetailsView.ts | problemMissingHeader":{"message":"Missing Header"},"panels/issues/CorsIssueDetailsView.ts | problemMultipleValues":{"message":"Multiple Values"},"panels/issues/CorsIssueDetailsView.ts | request":{"message":"Request"},"panels/issues/CorsIssueDetailsView.ts | resourceAddressSpace":{"message":"Resource Address"},"panels/issues/CorsIssueDetailsView.ts | secure":{"message":"secure"},"panels/issues/CorsIssueDetailsView.ts | sourceLocation":{"message":"Source Location"},"panels/issues/CorsIssueDetailsView.ts | status":{"message":"Status"},"panels/issues/CorsIssueDetailsView.ts | unsupportedScheme":{"message":"Unsupported Scheme"},"panels/issues/CorsIssueDetailsView.ts | warning":{"message":"warning"},"panels/issues/CSPViolationsView.ts | filter":{"message":"Filter"},"panels/issues/GenericIssueDetailsView.ts | frameId":{"message":"Frame"},"panels/issues/GenericIssueDetailsView.ts | nResources":{"message":"{n, plural, =1 {# resource} other {# resources}}"},"panels/issues/HiddenIssuesRow.ts | hiddenIssues":{"message":"Hidden issues"},"panels/issues/HiddenIssuesRow.ts | unhideAll":{"message":"Unhide all"},"panels/issues/IssueKindView.ts | hideAllCurrentBreakingChanges":{"message":"Hide all current Breaking Changes"},"panels/issues/IssueKindView.ts | hideAllCurrentImprovements":{"message":"Hide all current Improvements"},"panels/issues/IssueKindView.ts | hideAllCurrentPageErrors":{"message":"Hide all current Page Errors"},"panels/issues/issues-meta.ts | cspViolations":{"message":"CSP Violations"},"panels/issues/issues-meta.ts | issues":{"message":"Issues"},"panels/issues/issues-meta.ts | showCspViolations":{"message":"Show CSP Violations"},"panels/issues/issues-meta.ts | showIssues":{"message":"Show Issues"},"panels/issues/IssuesPane.ts | attributionReporting":{"message":"Attribution Reporting API"},"panels/issues/IssuesPane.ts | contentSecurityPolicy":{"message":"Content Security Policy"},"panels/issues/IssuesPane.ts | cors":{"message":"Cross Origin Resource Sharing"},"panels/issues/IssuesPane.ts | crossOriginEmbedderPolicy":{"message":"Cross Origin Embedder Policy"},"panels/issues/IssuesPane.ts | generic":{"message":"Generic"},"panels/issues/IssuesPane.ts | groupByCategory":{"message":"Group by category"},"panels/issues/IssuesPane.ts | groupByKind":{"message":"Group by kind"},"panels/issues/IssuesPane.ts | groupDisplayedIssuesUnder":{"message":"Group displayed issues under associated categories"},"panels/issues/IssuesPane.ts | groupDisplayedIssuesUnderKind":{"message":"Group displayed issues as Page errors, Breaking changes and Improvements"},"panels/issues/IssuesPane.ts | heavyAds":{"message":"Heavy Ads"},"panels/issues/IssuesPane.ts | includeCookieIssuesCausedBy":{"message":"Include cookie Issues caused by third-party sites"},"panels/issues/IssuesPane.ts | includeThirdpartyCookieIssues":{"message":"Include third-party cookie issues"},"panels/issues/IssuesPane.ts | lowTextContrast":{"message":"Low Text Contrast"},"panels/issues/IssuesPane.ts | mixedContent":{"message":"Mixed Content"},"panels/issues/IssuesPane.ts | noIssuesDetectedSoFar":{"message":"No issues detected so far"},"panels/issues/IssuesPane.ts | onlyThirdpartyCookieIssues":{"message":"Only third-party cookie issues detected so far"},"panels/issues/IssuesPane.ts | other":{"message":"Other"},"panels/issues/IssuesPane.ts | quirksMode":{"message":"Quirks Mode"},"panels/issues/IssuesPane.ts | samesiteCookie":{"message":"SameSite Cookie"},"panels/issues/IssuesPane.ts | trustedWebActivity":{"message":"Trusted Web Activity"},"panels/issues/IssueView.ts | affectedResources":{"message":"Affected Resources"},"panels/issues/IssueView.ts | automaticallyUpgraded":{"message":"automatically upgraded"},"panels/issues/IssueView.ts | blocked":{"message":"blocked"},"panels/issues/IssueView.ts | hideIssuesLikeThis":{"message":"Hide issues like this"},"panels/issues/IssueView.ts | learnMoreS":{"message":"Learn more: {PH1}"},"panels/issues/IssueView.ts | name":{"message":"Name"},"panels/issues/IssueView.ts | nRequests":{"message":"{n, plural, =1 {# request} other {# requests}}"},"panels/issues/IssueView.ts | nResources":{"message":"{n, plural, =1 {# resource} other {# resources}}"},"panels/issues/IssueView.ts | restrictionStatus":{"message":"Restriction Status"},"panels/issues/IssueView.ts | unhideIssuesLikeThis":{"message":"Unhide issues like this"},"panels/issues/IssueView.ts | warned":{"message":"Warned"},"panels/js_profiler/js_profiler-meta.ts | profiler":{"message":"Profiler"},"panels/js_profiler/js_profiler-meta.ts | showProfiler":{"message":"Show Profiler"},"panels/js_profiler/js_profiler-meta.ts | startStopRecording":{"message":"Start/stop recording"},"panels/layer_viewer/layer_viewer-meta.ts | panOrRotateDown":{"message":"Pan or rotate down"},"panels/layer_viewer/layer_viewer-meta.ts | panOrRotateLeft":{"message":"Pan or rotate left"},"panels/layer_viewer/layer_viewer-meta.ts | panOrRotateRight":{"message":"Pan or rotate right"},"panels/layer_viewer/layer_viewer-meta.ts | panOrRotateUp":{"message":"Pan or rotate up"},"panels/layer_viewer/layer_viewer-meta.ts | resetView":{"message":"Reset view"},"panels/layer_viewer/layer_viewer-meta.ts | switchToPanMode":{"message":"Switch to pan mode"},"panels/layer_viewer/layer_viewer-meta.ts | switchToRotateMode":{"message":"Switch to rotate mode"},"panels/layer_viewer/layer_viewer-meta.ts | zoomIn":{"message":"Zoom in"},"panels/layer_viewer/layer_viewer-meta.ts | zoomOut":{"message":"Zoom out"},"panels/layer_viewer/LayerDetailsView.ts | compositingReasons":{"message":"Compositing Reasons"},"panels/layer_viewer/LayerDetailsView.ts | containingBlocRectangleDimensions":{"message":"Containing Block {PH1} × {PH2} (at {PH3}, {PH4})"},"panels/layer_viewer/LayerDetailsView.ts | mainThreadScrollingReason":{"message":"Main thread scrolling reason"},"panels/layer_viewer/LayerDetailsView.ts | memoryEstimate":{"message":"Memory estimate"},"panels/layer_viewer/LayerDetailsView.ts | nearestLayerShiftingContaining":{"message":"Nearest Layer Shifting Containing Block"},"panels/layer_viewer/LayerDetailsView.ts | nearestLayerShiftingStickyBox":{"message":"Nearest Layer Shifting Sticky Box"},"panels/layer_viewer/LayerDetailsView.ts | nonFastScrollable":{"message":"Non fast scrollable"},"panels/layer_viewer/LayerDetailsView.ts | paintCount":{"message":"Paint count"},"panels/layer_viewer/LayerDetailsView.ts | paintProfiler":{"message":"Paint Profiler"},"panels/layer_viewer/LayerDetailsView.ts | repaintsOnScroll":{"message":"Repaints on scroll"},"panels/layer_viewer/LayerDetailsView.ts | scrollRectangleDimensions":{"message":"{PH1} {PH2} × {PH3} (at {PH4}, {PH5})"},"panels/layer_viewer/LayerDetailsView.ts | selectALayerToSeeItsDetails":{"message":"Select a layer to see its details"},"panels/layer_viewer/LayerDetailsView.ts | size":{"message":"Size"},"panels/layer_viewer/LayerDetailsView.ts | slowScrollRegions":{"message":"Slow scroll regions"},"panels/layer_viewer/LayerDetailsView.ts | stickyAncenstorLayersS":{"message":"{PH1}: {PH2} ({PH3})"},"panels/layer_viewer/LayerDetailsView.ts | stickyBoxRectangleDimensions":{"message":"Sticky Box {PH1} × {PH2} (at {PH3}, {PH4})"},"panels/layer_viewer/LayerDetailsView.ts | stickyPositionConstraint":{"message":"Sticky position constraint"},"panels/layer_viewer/LayerDetailsView.ts | touchEventHandler":{"message":"Touch event handler"},"panels/layer_viewer/LayerDetailsView.ts | unnamed":{"message":"<unnamed>"},"panels/layer_viewer/LayerDetailsView.ts | updateRectangleDimensions":{"message":"{PH1} × {PH2} (at {PH3}, {PH4})"},"panels/layer_viewer/LayerDetailsView.ts | wheelEventHandler":{"message":"Wheel event handler"},"panels/layer_viewer/Layers3DView.ts | cantDisplayLayers":{"message":"Can\'t display layers,"},"panels/layer_viewer/Layers3DView.ts | checkSForPossibleReasons":{"message":"Check {PH1} for possible reasons."},"panels/layer_viewer/Layers3DView.ts | dLayersView":{"message":"3D Layers View"},"panels/layer_viewer/Layers3DView.ts | layerInformationIsNotYet":{"message":"Layer information is not yet available."},"panels/layer_viewer/Layers3DView.ts | paints":{"message":"Paints"},"panels/layer_viewer/Layers3DView.ts | resetView":{"message":"Reset View"},"panels/layer_viewer/Layers3DView.ts | showPaintProfiler":{"message":"Show Paint Profiler"},"panels/layer_viewer/Layers3DView.ts | slowScrollRects":{"message":"Slow scroll rects"},"panels/layer_viewer/Layers3DView.ts | webglSupportIsDisabledInYour":{"message":"WebGL support is disabled in your browser."},"panels/layer_viewer/LayerTreeOutline.ts | layersTreePane":{"message":"Layers Tree Pane"},"panels/layer_viewer/LayerTreeOutline.ts | showPaintProfiler":{"message":"Show Paint Profiler"},"panels/layer_viewer/LayerTreeOutline.ts | updateChildDimension":{"message":" ({PH1} × {PH2})"},"panels/layer_viewer/LayerViewHost.ts | showInternalLayers":{"message":"Show internal layers"},"panels/layer_viewer/PaintProfilerView.ts | bitmap":{"message":"Bitmap"},"panels/layer_viewer/PaintProfilerView.ts | commandLog":{"message":"Command Log"},"panels/layer_viewer/PaintProfilerView.ts | misc":{"message":"Misc"},"panels/layer_viewer/PaintProfilerView.ts | profiling":{"message":"Profiling…"},"panels/layer_viewer/PaintProfilerView.ts | profilingResults":{"message":"Profiling results"},"panels/layer_viewer/PaintProfilerView.ts | shapes":{"message":"Shapes"},"panels/layer_viewer/PaintProfilerView.ts | text":{"message":"Text"},"panels/layer_viewer/TransformController.ts | panModeX":{"message":"Pan mode (X)"},"panels/layer_viewer/TransformController.ts | resetTransform":{"message":"Reset transform (0)"},"panels/layer_viewer/TransformController.ts | rotateModeV":{"message":"Rotate mode (V)"},"panels/layers/layers-meta.ts | layers":{"message":"Layers"},"panels/layers/layers-meta.ts | showLayers":{"message":"Show Layers"},"panels/layers/LayersPanel.ts | details":{"message":"Details"},"panels/layers/LayersPanel.ts | profiler":{"message":"Profiler"},"panels/lighthouse/lighthouse-meta.ts | showLighthouse":{"message":"Show Lighthouse"},"panels/lighthouse/LighthouseController.ts | accessibility":{"message":"Accessibility"},"panels/lighthouse/LighthouseController.ts | applyMobileEmulation":{"message":"Apply mobile emulation"},"panels/lighthouse/LighthouseController.ts | applyMobileEmulationDuring":{"message":"Apply mobile emulation during auditing"},"panels/lighthouse/LighthouseController.ts | atLeastOneCategoryMustBeSelected":{"message":"At least one category must be selected."},"panels/lighthouse/LighthouseController.ts | bestPractices":{"message":"Best practices"},"panels/lighthouse/LighthouseController.ts | canOnlyAuditHttphttpsPagesAnd":{"message":"Can only audit HTTP/HTTPS pages and Chrome extensions. Navigate to a different page to start an audit."},"panels/lighthouse/LighthouseController.ts | clearStorage":{"message":"Clear storage"},"panels/lighthouse/LighthouseController.ts | desktop":{"message":"Desktop"},"panels/lighthouse/LighthouseController.ts | doesThisPageFollowBestPractices":{"message":"Does this page follow best practices for modern web development"},"panels/lighthouse/LighthouseController.ts | doesThisPageMeetTheStandardOfA":{"message":"Does this page meet the standard of a Progressive Web App"},"panels/lighthouse/LighthouseController.ts | howLongDoesThisAppTakeToShow":{"message":"How long does this app take to show content and become usable"},"panels/lighthouse/LighthouseController.ts | indexeddb":{"message":"IndexedDB"},"panels/lighthouse/LighthouseController.ts | isThisPageOptimizedForAdSpeedAnd":{"message":"Is this page optimized for ad speed and quality"},"panels/lighthouse/LighthouseController.ts | isThisPageOptimizedForSearch":{"message":"Is this page optimized for search engine results ranking"},"panels/lighthouse/LighthouseController.ts | isThisPageUsableByPeopleWith":{"message":"Is this page usable by people with disabilities or impairments"},"panels/lighthouse/LighthouseController.ts | legacyNavigation":{"message":"Legacy navigation"},"panels/lighthouse/LighthouseController.ts | lighthouseMode":{"message":"Lighthouse mode"},"panels/lighthouse/LighthouseController.ts | localStorage":{"message":"Local Storage"},"panels/lighthouse/LighthouseController.ts | mobile":{"message":"Mobile"},"panels/lighthouse/LighthouseController.ts | multipleTabsAreBeingControlledBy":{"message":"Multiple tabs are being controlled by the same service worker. Close your other tabs on the same origin to audit this page."},"panels/lighthouse/LighthouseController.ts | navigation":{"message":"Navigation"},"panels/lighthouse/LighthouseController.ts | performance":{"message":"Performance"},"panels/lighthouse/LighthouseController.ts | progressiveWebApp":{"message":"Progressive Web App"},"panels/lighthouse/LighthouseController.ts | publisherAds":{"message":"Publisher Ads"},"panels/lighthouse/LighthouseController.ts | resetStorageLocalstorage":{"message":"Reset storage (cache, service workers, etc) before auditing. (Good for performance & PWA testing)"},"panels/lighthouse/LighthouseController.ts | runLighthouseInMode":{"message":"Run Lighthouse in navigation, timespan, or snapshot mode"},"panels/lighthouse/LighthouseController.ts | seo":{"message":"SEO"},"panels/lighthouse/LighthouseController.ts | simulateASlowerPageLoadBasedOn":{"message":"Simulate a slower page load, based on data from an initial unthrottled load. If disabled, the page is actually slowed with applied throttling."},"panels/lighthouse/LighthouseController.ts | simulatedThrottling":{"message":"Simulated throttling"},"panels/lighthouse/LighthouseController.ts | snapshot":{"message":"Snapshot"},"panels/lighthouse/LighthouseController.ts | thereMayBeStoredDataAffectingLoadingPlural":{"message":"There may be stored data affecting loading performance in these locations: {PH1}. Audit this page in an incognito window to prevent those resources from affecting your scores."},"panels/lighthouse/LighthouseController.ts | thereMayBeStoredDataAffectingSingular":{"message":"There may be stored data affecting loading performance in this location: {PH1}. Audit this page in an incognito window to prevent those resources from affecting your scores."},"panels/lighthouse/LighthouseController.ts | timespan":{"message":"Timespan"},"panels/lighthouse/LighthouseController.ts | useLegacyNavigation":{"message":"Analyze the page using classic Lighthouse when in navigation mode."},"panels/lighthouse/LighthouseController.ts | webSql":{"message":"Web SQL"},"panels/lighthouse/LighthousePanel.ts | cancelling":{"message":"Cancelling"},"panels/lighthouse/LighthousePanel.ts | clearAll":{"message":"Clear all"},"panels/lighthouse/LighthousePanel.ts | dropLighthouseJsonHere":{"message":"Drop Lighthouse JSON here"},"panels/lighthouse/LighthousePanel.ts | lighthouseSettings":{"message":"Lighthouse settings"},"panels/lighthouse/LighthousePanel.ts | performAnAudit":{"message":"Perform an audit…"},"panels/lighthouse/LighthousePanel.ts | printing":{"message":"Printing"},"panels/lighthouse/LighthousePanel.ts | thePrintPopupWindowIsOpenPlease":{"message":"The print popup window is open. Please close it to continue."},"panels/lighthouse/LighthouseReportRenderer.ts | thePerformanceMetricsAboveAre":{"message":"The performance metrics above are simulated and won\'t match the timings found in this trace. Disable simulated throttling in \\"Lighthouse Settings\\" if you want the timings to match."},"panels/lighthouse/LighthouseReportRenderer.ts | viewOriginalTrace":{"message":"View Original Trace"},"panels/lighthouse/LighthouseReportRenderer.ts | viewTrace":{"message":"View Trace"},"panels/lighthouse/LighthouseReportSelector.ts | newReport":{"message":"(new report)"},"panels/lighthouse/LighthouseReportSelector.ts | reports":{"message":"Reports"},"panels/lighthouse/LighthouseStartView.ts | categories":{"message":"Categories"},"panels/lighthouse/LighthouseStartView.ts | communityPluginsBeta":{"message":"Community Plugins (beta)"},"panels/lighthouse/LighthouseStartView.ts | device":{"message":"Device"},"panels/lighthouse/LighthouseStartView.ts | generateReport":{"message":"Generate report"},"panels/lighthouse/LighthouseStartView.ts | identifyAndFixCommonProblemsThat":{"message":"Identify and fix common problems that affect your site\'s performance, accessibility, and user experience."},"panels/lighthouse/LighthouseStartView.ts | learnMore":{"message":"Learn more"},"panels/lighthouse/LighthouseStartViewFR.ts | analyzeNavigation":{"message":"Analyze navigation"},"panels/lighthouse/LighthouseStartViewFR.ts | analyzeSnapshot":{"message":"Analyze snapshot"},"panels/lighthouse/LighthouseStartViewFR.ts | mode":{"message":"Mode"},"panels/lighthouse/LighthouseStartViewFR.ts | startTimespan":{"message":"Start timespan"},"panels/lighthouse/LighthouseStatusView.ts | ahSorryWeRanIntoAnError":{"message":"Ah, sorry! We ran into an error."},"panels/lighthouse/LighthouseStatusView.ts | almostThereLighthouseIsNow":{"message":"Almost there! Lighthouse is now generating your report."},"panels/lighthouse/LighthouseStatusView.ts | asPageLoadTimeIncreasesFromOne":{"message":"As page load time increases from one second to seven seconds, the probability of a mobile site visitor bouncing increases 113%. [Source: Think with Google]"},"panels/lighthouse/LighthouseStatusView.ts | asTheNumberOfElementsOnAPage":{"message":"As the number of elements on a page increases from 400 to 6,000, the probability of conversion drops 95%. [Source: Think with Google]"},"panels/lighthouse/LighthouseStatusView.ts | auditingS":{"message":"Auditing {PH1}"},"panels/lighthouse/LighthouseStatusView.ts | auditingYourWebPage":{"message":"Auditing your web page"},"panels/lighthouse/LighthouseStatusView.ts | byReducingTheResponseSizeOfJson":{"message":"By reducing the response size of JSON needed for displaying comments, Instagram saw increased impressions [Source: WPO Stats]"},"panels/lighthouse/LighthouseStatusView.ts | cancel":{"message":"Cancel"},"panels/lighthouse/LighthouseStatusView.ts | cancelling":{"message":"Cancelling…"},"panels/lighthouse/LighthouseStatusView.ts | fastFactMessageWithPlaceholder":{"message":"💡 {PH1}"},"panels/lighthouse/LighthouseStatusView.ts | ifASiteTakesSecondToBecome":{"message":"If a site takes >1 second to become interactive, users lose attention, and their perception of completing the page task is broken [Source: Google Developers Blog]"},"panels/lighthouse/LighthouseStatusView.ts | ifThisIssueIsReproduciblePlease":{"message":"If this issue is reproducible, please report it at the Lighthouse GitHub repo."},"panels/lighthouse/LighthouseStatusView.ts | lighthouseIsGatheringInformation":{"message":"Lighthouse is gathering information about the page to compute your score."},"panels/lighthouse/LighthouseStatusView.ts | lighthouseIsLoadingThePage":{"message":"Lighthouse is loading the page."},"panels/lighthouse/LighthouseStatusView.ts | lighthouseIsLoadingYourPage":{"message":"Lighthouse is loading your page"},"panels/lighthouse/LighthouseStatusView.ts | lighthouseIsLoadingYourPageWith":{"message":"Lighthouse is loading your page with throttling to measure performance on a mobile device on 3G."},"panels/lighthouse/LighthouseStatusView.ts | lighthouseIsLoadingYourPageWithMobile":{"message":"Lighthouse is loading your page with mobile emulation."},"panels/lighthouse/LighthouseStatusView.ts | lighthouseIsLoadingYourPageWithThrottling":{"message":"Lighthouse is loading your page with throttling to measure performance on a slow desktop on 3G."},"panels/lighthouse/LighthouseStatusView.ts | lighthouseIsWarmingUp":{"message":"Lighthouse is warming up…"},"panels/lighthouse/LighthouseStatusView.ts | lighthouseOnlySimulatesMobile":{"message":"Lighthouse only simulates mobile performance; to measure performance on a real device, try WebPageTest.org [Source: Lighthouse team]"},"panels/lighthouse/LighthouseStatusView.ts | loading":{"message":"Loading…"},"panels/lighthouse/LighthouseStatusView.ts | mbTakesAMinimumOfSecondsTo":{"message":"1MB takes a minimum of 5 seconds to download on a typical 3G connection [Source: WebPageTest and DevTools 3G definition]."},"panels/lighthouse/LighthouseStatusView.ts | OfGlobalMobileUsersInWereOnGOrG":{"message":"75% of global mobile users in 2016 were on 2G or 3G [Source: GSMA Mobile]"},"panels/lighthouse/LighthouseStatusView.ts | OfMobilePagesTakeNearlySeconds":{"message":"70% of mobile pages take nearly 7 seconds for the visual content above the fold to display on the screen. [Source: Think with Google]"},"panels/lighthouse/LighthouseStatusView.ts | rebuildingPinterestPagesFor":{"message":"Rebuilding Pinterest pages for performance increased conversion rates by 15% [Source: WPO Stats]"},"panels/lighthouse/LighthouseStatusView.ts | SecondsIsTheAverageTimeAMobile":{"message":"19 seconds is the average time a mobile web page takes to load on a 3G connection [Source: Google DoubleClick blog]"},"panels/lighthouse/LighthouseStatusView.ts | theAverageUserDeviceCostsLess":{"message":"The average user device costs less than 200 USD. [Source: International Data Corporation]"},"panels/lighthouse/LighthouseStatusView.ts | tryToNavigateToTheUrlInAFresh":{"message":"Try to navigate to the URL in a fresh Chrome profile without any other tabs or extensions open and try again."},"panels/lighthouse/LighthouseStatusView.ts | walmartSawAIncreaseInRevenueFor":{"message":"Walmart saw a 1% increase in revenue for every 100ms improvement in page load [Source: WPO Stats]"},"panels/lighthouse/LighthouseTimespanView.ts | cancel":{"message":"Cancel"},"panels/lighthouse/LighthouseTimespanView.ts | endTimespan":{"message":"End timespan"},"panels/lighthouse/LighthouseTimespanView.ts | timespanStarted":{"message":"Timespan started, interact with the page"},"panels/lighthouse/LighthouseTimespanView.ts | timespanStarting":{"message":"Timespan starting…"},"panels/media/EventDisplayTable.ts | eventDisplay":{"message":"Event display"},"panels/media/EventDisplayTable.ts | eventName":{"message":"Event name"},"panels/media/EventDisplayTable.ts | timestamp":{"message":"Timestamp"},"panels/media/EventDisplayTable.ts | value":{"message":"Value"},"panels/media/EventTimelineView.ts | bufferingStatus":{"message":"Buffering Status"},"panels/media/EventTimelineView.ts | playbackStatus":{"message":"Playback Status"},"panels/media/media-meta.ts | media":{"message":"Media"},"panels/media/media-meta.ts | showMedia":{"message":"Show Media"},"panels/media/media-meta.ts | video":{"message":"video"},"panels/media/PlayerDetailView.ts | events":{"message":"Events"},"panels/media/PlayerDetailView.ts | messages":{"message":"Messages"},"panels/media/PlayerDetailView.ts | playerEvents":{"message":"Player events"},"panels/media/PlayerDetailView.ts | playerMessages":{"message":"Player messages"},"panels/media/PlayerDetailView.ts | playerProperties":{"message":"Player properties"},"panels/media/PlayerDetailView.ts | playerTimeline":{"message":"Player timeline"},"panels/media/PlayerDetailView.ts | properties":{"message":"Properties"},"panels/media/PlayerDetailView.ts | timeline":{"message":"Timeline"},"panels/media/PlayerListView.ts | hideAllOthers":{"message":"Hide all others"},"panels/media/PlayerListView.ts | hidePlayer":{"message":"Hide player"},"panels/media/PlayerListView.ts | players":{"message":"Players"},"panels/media/PlayerListView.ts | savePlayerInfo":{"message":"Save player info"},"panels/media/PlayerMessagesView.ts | all":{"message":"All"},"panels/media/PlayerMessagesView.ts | custom":{"message":"Custom"},"panels/media/PlayerMessagesView.ts | debug":{"message":"Debug"},"panels/media/PlayerMessagesView.ts | default":{"message":"Default"},"panels/media/PlayerMessagesView.ts | error":{"message":"Error"},"panels/media/PlayerMessagesView.ts | filterLogMessages":{"message":"Filter log messages"},"panels/media/PlayerMessagesView.ts | info":{"message":"Info"},"panels/media/PlayerMessagesView.ts | logLevel":{"message":"Log level:"},"panels/media/PlayerMessagesView.ts | warning":{"message":"Warning"},"panels/media/PlayerPropertiesView.ts | audio":{"message":"Audio"},"panels/media/PlayerPropertiesView.ts | bitrate":{"message":"Bitrate"},"panels/media/PlayerPropertiesView.ts | decoder":{"message":"Decoder"},"panels/media/PlayerPropertiesView.ts | decoderName":{"message":"Decoder name"},"panels/media/PlayerPropertiesView.ts | decryptingDemuxer":{"message":"Decrypting demuxer"},"panels/media/PlayerPropertiesView.ts | duration":{"message":"Duration"},"panels/media/PlayerPropertiesView.ts | encoderName":{"message":"Encoder name"},"panels/media/PlayerPropertiesView.ts | fileSize":{"message":"File size"},"panels/media/PlayerPropertiesView.ts | frameRate":{"message":"Frame rate"},"panels/media/PlayerPropertiesView.ts | hardwareDecoder":{"message":"Hardware decoder"},"panels/media/PlayerPropertiesView.ts | hardwareEncoder":{"message":"Hardware encoder"},"panels/media/PlayerPropertiesView.ts | noDecoder":{"message":"No decoder"},"panels/media/PlayerPropertiesView.ts | noEncoder":{"message":"No encoder"},"panels/media/PlayerPropertiesView.ts | noTextTracks":{"message":"No text tracks"},"panels/media/PlayerPropertiesView.ts | playbackFrameTitle":{"message":"Playback frame title"},"panels/media/PlayerPropertiesView.ts | playbackFrameUrl":{"message":"Playback frame URL"},"panels/media/PlayerPropertiesView.ts | properties":{"message":"Properties"},"panels/media/PlayerPropertiesView.ts | rangeHeaderSupport":{"message":"Range header support"},"panels/media/PlayerPropertiesView.ts | rendererName":{"message":"Renderer name"},"panels/media/PlayerPropertiesView.ts | resolution":{"message":"Resolution"},"panels/media/PlayerPropertiesView.ts | singleoriginPlayback":{"message":"Single-origin playback"},"panels/media/PlayerPropertiesView.ts | startTime":{"message":"Start time"},"panels/media/PlayerPropertiesView.ts | streaming":{"message":"Streaming"},"panels/media/PlayerPropertiesView.ts | textTrack":{"message":"Text track"},"panels/media/PlayerPropertiesView.ts | track":{"message":"Track"},"panels/media/PlayerPropertiesView.ts | video":{"message":"Video"},"panels/media/PlayerPropertiesView.ts | videoFreezingScore":{"message":"Video freezing score"},"panels/media/PlayerPropertiesView.ts | videoPlaybackRoughness":{"message":"Video playback roughness"},"panels/mobile_throttling/mobile_throttling-meta.ts | device":{"message":"device"},"panels/mobile_throttling/mobile_throttling-meta.ts | enableFastGThrottling":{"message":"Enable fast 3G throttling"},"panels/mobile_throttling/mobile_throttling-meta.ts | enableSlowGThrottling":{"message":"Enable slow 3G throttling"},"panels/mobile_throttling/mobile_throttling-meta.ts | goOffline":{"message":"Go offline"},"panels/mobile_throttling/mobile_throttling-meta.ts | goOnline":{"message":"Go online"},"panels/mobile_throttling/mobile_throttling-meta.ts | showThrottling":{"message":"Show Throttling"},"panels/mobile_throttling/mobile_throttling-meta.ts | throttling":{"message":"Throttling"},"panels/mobile_throttling/mobile_throttling-meta.ts | throttlingTag":{"message":"throttling"},"panels/mobile_throttling/MobileThrottlingSelector.ts | advanced":{"message":"Advanced"},"panels/mobile_throttling/MobileThrottlingSelector.ts | disabled":{"message":"Disabled"},"panels/mobile_throttling/MobileThrottlingSelector.ts | presets":{"message":"Presets"},"panels/mobile_throttling/NetworkPanelIndicator.ts | acceptedEncodingOverrideSet":{"message":"The set of accepted Content-Encoding headers has been modified by DevTools. See the Network Conditions panel."},"panels/mobile_throttling/NetworkPanelIndicator.ts | networkThrottlingIsEnabled":{"message":"Network throttling is enabled"},"panels/mobile_throttling/NetworkPanelIndicator.ts | requestsMayBeBlocked":{"message":"Requests may be blocked"},"panels/mobile_throttling/NetworkPanelIndicator.ts | requestsMayBeRewrittenByLocal":{"message":"Requests may be rewritten by local overrides"},"panels/mobile_throttling/NetworkThrottlingSelector.ts | custom":{"message":"Custom"},"panels/mobile_throttling/NetworkThrottlingSelector.ts | disabled":{"message":"Disabled"},"panels/mobile_throttling/NetworkThrottlingSelector.ts | presets":{"message":"Presets"},"panels/mobile_throttling/ThrottlingManager.ts | add":{"message":"Add…"},"panels/mobile_throttling/ThrottlingManager.ts | addS":{"message":"Add {PH1}"},"panels/mobile_throttling/ThrottlingManager.ts | cpuThrottling":{"message":"CPU throttling"},"panels/mobile_throttling/ThrottlingManager.ts | cpuThrottlingIsEnabled":{"message":"CPU throttling is enabled"},"panels/mobile_throttling/ThrottlingManager.ts | dSlowdown":{"message":"{PH1}× slowdown"},"panels/mobile_throttling/ThrottlingManager.ts | forceDisconnectedFromNetwork":{"message":"Force disconnected from network"},"panels/mobile_throttling/ThrottlingManager.ts | noThrottling":{"message":"No throttling"},"panels/mobile_throttling/ThrottlingManager.ts | offline":{"message":"Offline"},"panels/mobile_throttling/ThrottlingManager.ts | sS":{"message":"{PH1}: {PH2}"},"panels/mobile_throttling/ThrottlingManager.ts | throttling":{"message":"Throttling"},"panels/mobile_throttling/ThrottlingPresets.ts | checkNetworkAndPerformancePanels":{"message":"Check Network and Performance panels"},"panels/mobile_throttling/ThrottlingPresets.ts | custom":{"message":"Custom"},"panels/mobile_throttling/ThrottlingPresets.ts | fastGXCpuSlowdown":{"message":"Fast 3G & 4x CPU slowdown"},"panels/mobile_throttling/ThrottlingPresets.ts | lowendMobile":{"message":"Low-end mobile"},"panels/mobile_throttling/ThrottlingPresets.ts | midtierMobile":{"message":"Mid-tier mobile"},"panels/mobile_throttling/ThrottlingPresets.ts | noInternetConnectivity":{"message":"No internet connectivity"},"panels/mobile_throttling/ThrottlingPresets.ts | noThrottling":{"message":"No throttling"},"panels/mobile_throttling/ThrottlingPresets.ts | slowGXCpuSlowdown":{"message":"Slow 3G & 6x CPU slowdown"},"panels/mobile_throttling/ThrottlingSettingsTab.ts | addCustomProfile":{"message":"Add custom profile..."},"panels/mobile_throttling/ThrottlingSettingsTab.ts | dms":{"message":"{PH1} ms"},"panels/mobile_throttling/ThrottlingSettingsTab.ts | download":{"message":"Download"},"panels/mobile_throttling/ThrottlingSettingsTab.ts | dskbits":{"message":"{PH1} kbit/s"},"panels/mobile_throttling/ThrottlingSettingsTab.ts | fsmbits":{"message":"{PH1} Mbit/s"},"panels/mobile_throttling/ThrottlingSettingsTab.ts | latency":{"message":"Latency"},"panels/mobile_throttling/ThrottlingSettingsTab.ts | latencyMustBeAnIntegerBetweenSms":{"message":"Latency must be an integer between {PH1} ms to {PH2} ms inclusive"},"panels/mobile_throttling/ThrottlingSettingsTab.ts | networkThrottlingProfiles":{"message":"Network Throttling Profiles"},"panels/mobile_throttling/ThrottlingSettingsTab.ts | optional":{"message":"optional"},"panels/mobile_throttling/ThrottlingSettingsTab.ts | profileName":{"message":"Profile Name"},"panels/mobile_throttling/ThrottlingSettingsTab.ts | profileNameCharactersLengthMust":{"message":"Profile Name characters length must be between 1 to {PH1} inclusive"},"panels/mobile_throttling/ThrottlingSettingsTab.ts | sMustBeANumberBetweenSkbsToSkbs":{"message":"{PH1} must be a number between {PH2} kbit/s to {PH3} kbit/s inclusive"},"panels/mobile_throttling/ThrottlingSettingsTab.ts | upload":{"message":"Upload"},"panels/network/BinaryResourceView.ts | binaryViewType":{"message":"Binary view type"},"panels/network/BinaryResourceView.ts | copiedAsBase":{"message":"Copied as Base64"},"panels/network/BinaryResourceView.ts | copiedAsHex":{"message":"Copied as Hex"},"panels/network/BinaryResourceView.ts | copiedAsUtf":{"message":"Copied as UTF-8"},"panels/network/BinaryResourceView.ts | copyAsBase":{"message":"Copy as Base64"},"panels/network/BinaryResourceView.ts | copyAsHex":{"message":"Copy as Hex"},"panels/network/BinaryResourceView.ts | copyAsUtf":{"message":"Copy as UTF-8"},"panels/network/BinaryResourceView.ts | copyToClipboard":{"message":"Copy to clipboard"},"panels/network/BinaryResourceView.ts | hexViewer":{"message":"Hex Viewer"},"panels/network/BlockedURLsPane.ts | addNetworkRequestBlockingPattern":{"message":"Add network request blocking pattern"},"panels/network/BlockedURLsPane.ts | addPattern":{"message":"Add pattern"},"panels/network/BlockedURLsPane.ts | dBlocked":{"message":"{PH1} blocked"},"panels/network/BlockedURLsPane.ts | enableNetworkRequestBlocking":{"message":"Enable network request blocking"},"panels/network/BlockedURLsPane.ts | itemDeleted":{"message":"Item successfully deleted"},"panels/network/BlockedURLsPane.ts | networkRequestsAreNotBlockedS":{"message":"Network requests are not blocked. {PH1}"},"panels/network/BlockedURLsPane.ts | patternAlreadyExists":{"message":"Pattern already exists."},"panels/network/BlockedURLsPane.ts | patternInputCannotBeEmpty":{"message":"Pattern input cannot be empty."},"panels/network/BlockedURLsPane.ts | removeAllPatterns":{"message":"Remove all patterns"},"panels/network/BlockedURLsPane.ts | textPatternToBlockMatching":{"message":"Text pattern to block matching requests; use * for wildcard"},"panels/network/components/RequestTrustTokensView.ts | aClientprovidedArgumentWas":{"message":"A client-provided argument was malformed or otherwise invalid."},"panels/network/components/RequestTrustTokensView.ts | eitherNoInputsForThisOperation":{"message":"Either no inputs for this operation are available or the output exceeds the operations quota."},"panels/network/components/RequestTrustTokensView.ts | failure":{"message":"Failure"},"panels/network/components/RequestTrustTokensView.ts | issuer":{"message":"Issuer"},"panels/network/components/RequestTrustTokensView.ts | issuers":{"message":"Issuers"},"panels/network/components/RequestTrustTokensView.ts | numberOfIssuedTokens":{"message":"Number of issued tokens"},"panels/network/components/RequestTrustTokensView.ts | parameters":{"message":"Parameters"},"panels/network/components/RequestTrustTokensView.ts | refreshPolicy":{"message":"Refresh policy"},"panels/network/components/RequestTrustTokensView.ts | result":{"message":"Result"},"panels/network/components/RequestTrustTokensView.ts | status":{"message":"Status"},"panels/network/components/RequestTrustTokensView.ts | success":{"message":"Success"},"panels/network/components/RequestTrustTokensView.ts | theOperationFailedForAnUnknown":{"message":"The operation failed for an unknown reason."},"panels/network/components/RequestTrustTokensView.ts | theOperationsResultWasServedFrom":{"message":"The operations result was served from cache."},"panels/network/components/RequestTrustTokensView.ts | theOperationWasFulfilledLocally":{"message":"The operation was fulfilled locally, no request was sent."},"panels/network/components/RequestTrustTokensView.ts | theServersResponseWasMalformedOr":{"message":"The servers response was malformed or otherwise invalid."},"panels/network/components/RequestTrustTokensView.ts | topLevelOrigin":{"message":"Top level origin"},"panels/network/components/RequestTrustTokensView.ts | type":{"message":"Type"},"panels/network/components/WebBundleInfoView.ts | bundledResource":{"message":"Bundled resource"},"panels/network/EventSourceMessagesView.ts | copyMessage":{"message":"Copy message"},"panels/network/EventSourceMessagesView.ts | data":{"message":"Data"},"panels/network/EventSourceMessagesView.ts | eventSource":{"message":"Event Source"},"panels/network/EventSourceMessagesView.ts | id":{"message":"Id"},"panels/network/EventSourceMessagesView.ts | time":{"message":"Time"},"panels/network/EventSourceMessagesView.ts | type":{"message":"Type"},"panels/network/network-meta.ts | colorCode":{"message":"color code"},"panels/network/network-meta.ts | colorCodeByResourceType":{"message":"Color code by resource type"},"panels/network/network-meta.ts | colorcodeResourceTypes":{"message":"Color-code resource types"},"panels/network/network-meta.ts | diskCache":{"message":"disk cache"},"panels/network/network-meta.ts | dontGroupNetworkLogItemsByFrame":{"message":"Don\'t group network log items by frame"},"panels/network/network-meta.ts | frame":{"message":"frame"},"panels/network/network-meta.ts | group":{"message":"group"},"panels/network/network-meta.ts | groupNetworkLogByFrame":{"message":"Group network log by frame"},"panels/network/network-meta.ts | groupNetworkLogItemsByFrame":{"message":"Group network log items by frame"},"panels/network/network-meta.ts | hideRequestDetails":{"message":"Hide request details"},"panels/network/network-meta.ts | network":{"message":"Network"},"panels/network/network-meta.ts | netWork":{"message":"network"},"panels/network/network-meta.ts | networkConditions":{"message":"Network conditions"},"panels/network/network-meta.ts | networkRequestBlocking":{"message":"Network request blocking"},"panels/network/network-meta.ts | networkThrottling":{"message":"network throttling"},"panels/network/network-meta.ts | recordNetworkLog":{"message":"Record network log"},"panels/network/network-meta.ts | resourceType":{"message":"resource type"},"panels/network/network-meta.ts | search":{"message":"Search"},"panels/network/network-meta.ts | showNetwork":{"message":"Show Network"},"panels/network/network-meta.ts | showNetworkConditions":{"message":"Show Network conditions"},"panels/network/network-meta.ts | showNetworkRequestBlocking":{"message":"Show Network request blocking"},"panels/network/network-meta.ts | showSearch":{"message":"Show Search"},"panels/network/network-meta.ts | stopRecordingNetworkLog":{"message":"Stop recording network log"},"panels/network/network-meta.ts | useDefaultColors":{"message":"Use default colors"},"panels/network/NetworkConfigView.ts | acceptedEncoding":{"message":"Accepted Content-Encodings"},"panels/network/NetworkConfigView.ts | caching":{"message":"Caching"},"panels/network/NetworkConfigView.ts | clientHintsStatusText":{"message":"User agent updated."},"panels/network/NetworkConfigView.ts | custom":{"message":"Custom..."},"panels/network/NetworkConfigView.ts | customUserAgentFieldIsRequired":{"message":"Custom user agent field is required"},"panels/network/NetworkConfigView.ts | disableCache":{"message":"Disable cache"},"panels/network/NetworkConfigView.ts | enterACustomUserAgent":{"message":"Enter a custom user agent"},"panels/network/NetworkConfigView.ts | networkThrottling":{"message":"Network throttling"},"panels/network/NetworkConfigView.ts | selectAutomatically":{"message":"Use browser default"},"panels/network/NetworkConfigView.ts | userAgent":{"message":"User agent"},"panels/network/NetworkDataGridNode.ts | blockeds":{"message":"(blocked:{PH1})"},"panels/network/NetworkDataGridNode.ts | blockedTooltip":{"message":"This request was blocked due to misconfigured response headers, click to view the headers"},"panels/network/NetworkDataGridNode.ts | canceled":{"message":"(canceled)"},"panels/network/NetworkDataGridNode.ts | corsError":{"message":"CORS error"},"panels/network/NetworkDataGridNode.ts | crossoriginResourceSharingErrorS":{"message":"Cross-Origin Resource Sharing error: {PH1}"},"panels/network/NetworkDataGridNode.ts | csp":{"message":"csp"},"panels/network/NetworkDataGridNode.ts | data":{"message":"(data)"},"panels/network/NetworkDataGridNode.ts | devtools":{"message":"devtools"},"panels/network/NetworkDataGridNode.ts | diskCache":{"message":"(disk cache)"},"panels/network/NetworkDataGridNode.ts | failed":{"message":"(failed)"},"panels/network/NetworkDataGridNode.ts | finished":{"message":"Finished"},"panels/network/NetworkDataGridNode.ts | level":{"message":"level 1"},"panels/network/NetworkDataGridNode.ts | memoryCache":{"message":"(memory cache)"},"panels/network/NetworkDataGridNode.ts | origin":{"message":"origin"},"panels/network/NetworkDataGridNode.ts | other":{"message":"other"},"panels/network/NetworkDataGridNode.ts | otherC":{"message":"Other"},"panels/network/NetworkDataGridNode.ts | parser":{"message":"Parser"},"panels/network/NetworkDataGridNode.ts | pending":{"message":"Pending"},"panels/network/NetworkDataGridNode.ts | pendingq":{"message":"(pending)"},"panels/network/NetworkDataGridNode.ts | prefetchCache":{"message":"(prefetch cache)"},"panels/network/NetworkDataGridNode.ts | preflight":{"message":"Preflight"},"panels/network/NetworkDataGridNode.ts | preload":{"message":"Preload"},"panels/network/NetworkDataGridNode.ts | push":{"message":"Push / "},"panels/network/NetworkDataGridNode.ts | redirect":{"message":"Redirect"},"panels/network/NetworkDataGridNode.ts | script":{"message":"Script"},"panels/network/NetworkDataGridNode.ts | selectPreflightRequest":{"message":"Select preflight request"},"panels/network/NetworkDataGridNode.ts | selectTheRequestThatTriggered":{"message":"Select the request that triggered this preflight"},"panels/network/NetworkDataGridNode.ts | servedFromDiskCacheResourceSizeS":{"message":"Served from disk cache, resource size: {PH1}"},"panels/network/NetworkDataGridNode.ts | servedFromMemoryCacheResource":{"message":"Served from memory cache, resource size: {PH1}"},"panels/network/NetworkDataGridNode.ts | servedFromPrefetchCacheResource":{"message":"Served from prefetch cache, resource size: {PH1}"},"panels/network/NetworkDataGridNode.ts | servedFromServiceworkerResource":{"message":"Served from ServiceWorker, resource size: {PH1}"},"panels/network/NetworkDataGridNode.ts | servedFromSignedHttpExchange":{"message":"Served from Signed HTTP Exchange, resource size: {PH1}"},"panels/network/NetworkDataGridNode.ts | servedFromWebBundle":{"message":"Served from Web Bundle, resource size: {PH1}"},"panels/network/NetworkDataGridNode.ts | serviceworker":{"message":"(ServiceWorker)"},"panels/network/NetworkDataGridNode.ts | signedexchange":{"message":"signed-exchange"},"panels/network/NetworkDataGridNode.ts | sPreflight":{"message":"{PH1} + Preflight"},"panels/network/NetworkDataGridNode.ts | timeSubtitleTooltipText":{"message":"Latency (response received time - start time)"},"panels/network/NetworkDataGridNode.ts | unknown":{"message":"(unknown)"},"panels/network/NetworkDataGridNode.ts | unknownExplanation":{"message":"The request status cannot be shown here because the page that issued it unloaded while the request was in flight. You can use chrome://net-export to capture a network log and see all request details."},"panels/network/NetworkDataGridNode.ts | webBundle":{"message":"(Web Bundle)"},"panels/network/NetworkDataGridNode.ts | webBundleError":{"message":"Web Bundle error"},"panels/network/NetworkDataGridNode.ts | webBundleInnerRequest":{"message":"Served from Web Bundle"},"panels/network/NetworkItemView.ts | cookies":{"message":"Cookies"},"panels/network/NetworkItemView.ts | eventstream":{"message":"EventStream"},"panels/network/NetworkItemView.ts | headers":{"message":"Headers"},"panels/network/NetworkItemView.ts | initiator":{"message":"Initiator"},"panels/network/NetworkItemView.ts | messages":{"message":"Messages"},"panels/network/NetworkItemView.ts | payload":{"message":"Payload"},"panels/network/NetworkItemView.ts | preview":{"message":"Preview"},"panels/network/NetworkItemView.ts | rawResponseData":{"message":"Raw response data"},"panels/network/NetworkItemView.ts | requestAndResponseCookies":{"message":"Request and response cookies"},"panels/network/NetworkItemView.ts | requestAndResponseTimeline":{"message":"Request and response timeline"},"panels/network/NetworkItemView.ts | requestInitiatorCallStack":{"message":"Request initiator call stack"},"panels/network/NetworkItemView.ts | response":{"message":"Response"},"panels/network/NetworkItemView.ts | responsePreview":{"message":"Response preview"},"panels/network/NetworkItemView.ts | signedexchangeError":{"message":"SignedExchange error"},"panels/network/NetworkItemView.ts | timing":{"message":"Timing"},"panels/network/NetworkItemView.ts | trustTokenOperationDetails":{"message":"Trust Token operation details"},"panels/network/NetworkItemView.ts | trustTokens":{"message":"Trust Tokens"},"panels/network/NetworkItemView.ts | websocketMessages":{"message":"WebSocket messages"},"panels/network/NetworkLogView.ts | areYouSureYouWantToClearBrowser":{"message":"Are you sure you want to clear browser cache?"},"panels/network/NetworkLogView.ts | areYouSureYouWantToClearBrowserCookies":{"message":"Are you sure you want to clear browser cookies?"},"panels/network/NetworkLogView.ts | blockedRequests":{"message":"Blocked Requests"},"panels/network/NetworkLogView.ts | blockRequestDomain":{"message":"Block request domain"},"panels/network/NetworkLogView.ts | blockRequestUrl":{"message":"Block request URL"},"panels/network/NetworkLogView.ts | clearBrowserCache":{"message":"Clear browser cache"},"panels/network/NetworkLogView.ts | clearBrowserCookies":{"message":"Clear browser cookies"},"panels/network/NetworkLogView.ts | copy":{"message":"Copy"},"panels/network/NetworkLogView.ts | copyAllAsCurl":{"message":"Copy all as cURL"},"panels/network/NetworkLogView.ts | copyAllAsCurlBash":{"message":"Copy all as cURL (bash)"},"panels/network/NetworkLogView.ts | copyAllAsCurlCmd":{"message":"Copy all as cURL (cmd)"},"panels/network/NetworkLogView.ts | copyAllAsFetch":{"message":"Copy all as fetch"},"panels/network/NetworkLogView.ts | copyAllAsHar":{"message":"Copy all as HAR"},"panels/network/NetworkLogView.ts | copyAllAsNodejsFetch":{"message":"Copy all as Node.js fetch"},"panels/network/NetworkLogView.ts | copyAllAsPowershell":{"message":"Copy all as PowerShell"},"panels/network/NetworkLogView.ts | copyAsCurl":{"message":"Copy as cURL"},"panels/network/NetworkLogView.ts | copyAsCurlBash":{"message":"Copy as cURL (bash)"},"panels/network/NetworkLogView.ts | copyAsCurlCmd":{"message":"Copy as cURL (cmd)"},"panels/network/NetworkLogView.ts | copyAsFetch":{"message":"Copy as fetch"},"panels/network/NetworkLogView.ts | copyAsNodejsFetch":{"message":"Copy as Node.js fetch"},"panels/network/NetworkLogView.ts | copyAsPowershell":{"message":"Copy as PowerShell"},"panels/network/NetworkLogView.ts | copyRequestHeaders":{"message":"Copy request headers"},"panels/network/NetworkLogView.ts | copyResponse":{"message":"Copy response"},"panels/network/NetworkLogView.ts | copyResponseHeaders":{"message":"Copy response headers"},"panels/network/NetworkLogView.ts | copyStacktrace":{"message":"Copy stack trace"},"panels/network/NetworkLogView.ts | domcontentloadedS":{"message":"DOMContentLoaded: {PH1}"},"panels/network/NetworkLogView.ts | dropHarFilesHere":{"message":"Drop HAR files here"},"panels/network/NetworkLogView.ts | finishS":{"message":"Finish: {PH1}"},"panels/network/NetworkLogView.ts | hasBlockedCookies":{"message":"Has blocked cookies"},"panels/network/NetworkLogView.ts | hideDataUrls":{"message":"Hide data URLs"},"panels/network/NetworkLogView.ts | hidesDataAndBlobUrls":{"message":"Hides data: and blob: URLs"},"panels/network/NetworkLogView.ts | invertFilter":{"message":"Invert"},"panels/network/NetworkLogView.ts | invertsFilter":{"message":"Inverts the search filter"},"panels/network/NetworkLogView.ts | learnMore":{"message":"Learn more"},"panels/network/NetworkLogView.ts | loadS":{"message":"Load: {PH1}"},"panels/network/NetworkLogView.ts | networkDataAvailable":{"message":"Network Data Available"},"panels/network/NetworkLogView.ts | onlyShowBlockedRequests":{"message":"Only show blocked requests"},"panels/network/NetworkLogView.ts | onlyShowRequestsWithBlocked":{"message":"Only show requests with blocked response cookies"},"panels/network/NetworkLogView.ts | onlyShowThirdPartyRequests":{"message":"Shows only requests with origin different from page origin"},"panels/network/NetworkLogView.ts | performARequestOrHitSToRecordThe":{"message":"Perform a request or hit {PH1} to record the reload."},"panels/network/NetworkLogView.ts | recordingNetworkActivity":{"message":"Recording network activity…"},"panels/network/NetworkLogView.ts | recordToDisplayNetworkActivity":{"message":"Record network log ({PH1}) to display network activity."},"panels/network/NetworkLogView.ts | replayXhr":{"message":"Replay XHR"},"panels/network/NetworkLogView.ts | resourceTypesToInclude":{"message":"Resource types to include"},"panels/network/NetworkLogView.ts | saveAllAsHarWithContent":{"message":"Save all as HAR with content"},"panels/network/NetworkLogView.ts | sBResourcesLoadedByThePage":{"message":"{PH1} B resources loaded by the page"},"panels/network/NetworkLogView.ts | sBSBResourcesLoadedByThePage":{"message":"{PH1} B / {PH2} B resources loaded by the page"},"panels/network/NetworkLogView.ts | sBSBTransferredOverNetwork":{"message":"{PH1} B / {PH2} B transferred over network"},"panels/network/NetworkLogView.ts | sBTransferredOverNetwork":{"message":"{PH1} B transferred over network"},"panels/network/NetworkLogView.ts | sRequests":{"message":"{PH1} requests"},"panels/network/NetworkLogView.ts | sResources":{"message":"{PH1} resources"},"panels/network/NetworkLogView.ts | sSRequests":{"message":"{PH1} / {PH2} requests"},"panels/network/NetworkLogView.ts | sSResources":{"message":"{PH1} / {PH2} resources"},"panels/network/NetworkLogView.ts | sSTransferred":{"message":"{PH1} / {PH2} transferred"},"panels/network/NetworkLogView.ts | sTransferred":{"message":"{PH1} transferred"},"panels/network/NetworkLogView.ts | thirdParty":{"message":"3rd-party requests"},"panels/network/NetworkLogView.ts | unblockS":{"message":"Unblock {PH1}"},"panels/network/NetworkLogViewColumns.ts | connectionId":{"message":"Connection ID"},"panels/network/NetworkLogViewColumns.ts | content":{"message":"Content"},"panels/network/NetworkLogViewColumns.ts | cookies":{"message":"Cookies"},"panels/network/NetworkLogViewColumns.ts | domain":{"message":"Domain"},"panels/network/NetworkLogViewColumns.ts | endTime":{"message":"End Time"},"panels/network/NetworkLogViewColumns.ts | initiator":{"message":"Initiator"},"panels/network/NetworkLogViewColumns.ts | initiatorAddressSpace":{"message":"Initiator Address Space"},"panels/network/NetworkLogViewColumns.ts | latency":{"message":"Latency"},"panels/network/NetworkLogViewColumns.ts | manageHeaderColumns":{"message":"Manage Header Columns…"},"panels/network/NetworkLogViewColumns.ts | method":{"message":"Method"},"panels/network/NetworkLogViewColumns.ts | name":{"message":"Name"},"panels/network/NetworkLogViewColumns.ts | networkLog":{"message":"Network Log"},"panels/network/NetworkLogViewColumns.ts | path":{"message":"Path"},"panels/network/NetworkLogViewColumns.ts | priority":{"message":"Priority"},"panels/network/NetworkLogViewColumns.ts | protocol":{"message":"Protocol"},"panels/network/NetworkLogViewColumns.ts | remoteAddress":{"message":"Remote Address"},"panels/network/NetworkLogViewColumns.ts | remoteAddressSpace":{"message":"Remote Address Space"},"panels/network/NetworkLogViewColumns.ts | responseHeaders":{"message":"Response Headers"},"panels/network/NetworkLogViewColumns.ts | responseTime":{"message":"Response Time"},"panels/network/NetworkLogViewColumns.ts | scheme":{"message":"Scheme"},"panels/network/NetworkLogViewColumns.ts | setCookies":{"message":"Set Cookies"},"panels/network/NetworkLogViewColumns.ts | size":{"message":"Size"},"panels/network/NetworkLogViewColumns.ts | startTime":{"message":"Start Time"},"panels/network/NetworkLogViewColumns.ts | status":{"message":"Status"},"panels/network/NetworkLogViewColumns.ts | text":{"message":"Text"},"panels/network/NetworkLogViewColumns.ts | time":{"message":"Time"},"panels/network/NetworkLogViewColumns.ts | totalDuration":{"message":"Total Duration"},"panels/network/NetworkLogViewColumns.ts | type":{"message":"Type"},"panels/network/NetworkLogViewColumns.ts | url":{"message":"Url"},"panels/network/NetworkLogViewColumns.ts | waterfall":{"message":"Waterfall"},"panels/network/NetworkManageCustomHeadersView.ts | addCustomHeader":{"message":"Add custom header…"},"panels/network/NetworkManageCustomHeadersView.ts | headerName":{"message":"Header Name"},"panels/network/NetworkManageCustomHeadersView.ts | manageHeaderColumns":{"message":"Manage Header Columns"},"panels/network/NetworkManageCustomHeadersView.ts | noCustomHeaders":{"message":"No custom headers"},"panels/network/NetworkPanel.ts | captureScreenshots":{"message":"Capture screenshots"},"panels/network/NetworkPanel.ts | captureScreenshotsWhenLoadingA":{"message":"Capture screenshots when loading a page"},"panels/network/NetworkPanel.ts | clear":{"message":"Clear"},"panels/network/NetworkPanel.ts | close":{"message":"Close"},"panels/network/NetworkPanel.ts | disableCache":{"message":"Disable cache"},"panels/network/NetworkPanel.ts | disableCacheWhileDevtoolsIsOpen":{"message":"Disable cache (while DevTools is open)"},"panels/network/NetworkPanel.ts | doNotClearLogOnPageReload":{"message":"Do not clear log on page reload / navigation"},"panels/network/NetworkPanel.ts | exportHar":{"message":"Export HAR..."},"panels/network/NetworkPanel.ts | fetchingFrames":{"message":"Fetching frames..."},"panels/network/NetworkPanel.ts | groupByFrame":{"message":"Group by frame"},"panels/network/NetworkPanel.ts | groupRequestsByTopLevelRequest":{"message":"Group requests by top level request frame"},"panels/network/NetworkPanel.ts | hitSToReloadAndCaptureFilmstrip":{"message":"Hit {PH1} to reload and capture filmstrip."},"panels/network/NetworkPanel.ts | importHarFile":{"message":"Import HAR file..."},"panels/network/NetworkPanel.ts | moreNetworkConditions":{"message":"More network conditions…"},"panels/network/NetworkPanel.ts | networkSettings":{"message":"Network settings"},"panels/network/NetworkPanel.ts | preserveLog":{"message":"Preserve log"},"panels/network/NetworkPanel.ts | recordingFrames":{"message":"Recording frames..."},"panels/network/NetworkPanel.ts | revealInNetworkPanel":{"message":"Reveal in Network panel"},"panels/network/NetworkPanel.ts | search":{"message":"Search"},"panels/network/NetworkPanel.ts | showMoreInformationInRequestRows":{"message":"Show more information in request rows"},"panels/network/NetworkPanel.ts | showOverview":{"message":"Show overview"},"panels/network/NetworkPanel.ts | showOverviewOfNetworkRequests":{"message":"Show overview of network requests"},"panels/network/NetworkPanel.ts | throttling":{"message":"Throttling"},"panels/network/NetworkPanel.ts | useLargeRequestRows":{"message":"Use large request rows"},"panels/network/NetworkSearchScope.ts | url":{"message":"URL"},"panels/network/NetworkTimeCalculator.ts | sDownload":{"message":"{PH1} download"},"panels/network/NetworkTimeCalculator.ts | sFromCache":{"message":"{PH1} (from cache)"},"panels/network/NetworkTimeCalculator.ts | sFromServiceworker":{"message":"{PH1} (from ServiceWorker)"},"panels/network/NetworkTimeCalculator.ts | sLatency":{"message":"{PH1} latency"},"panels/network/NetworkTimeCalculator.ts | sLatencySDownloadSTotal":{"message":"{PH1} latency, {PH2} download ({PH3} total)"},"panels/network/RequestCookiesView.ts | cookiesThatWereReceivedFromThe":{"message":"Cookies that were received from the server in the \'set-cookie\' header of the response"},"panels/network/RequestCookiesView.ts | cookiesThatWereReceivedFromTheServer":{"message":"Cookies that were received from the server in the \'set-cookie\' header of the response but were malformed"},"panels/network/RequestCookiesView.ts | cookiesThatWereSentToTheServerIn":{"message":"Cookies that were sent to the server in the \'cookie\' header of the request"},"panels/network/RequestCookiesView.ts | malformedResponseCookies":{"message":"Malformed Response Cookies"},"panels/network/RequestCookiesView.ts | noRequestCookiesWereSent":{"message":"No request cookies were sent."},"panels/network/RequestCookiesView.ts | requestCookies":{"message":"Request Cookies"},"panels/network/RequestCookiesView.ts | responseCookies":{"message":"Response Cookies"},"panels/network/RequestCookiesView.ts | showFilteredOutRequestCookies":{"message":"show filtered out request cookies"},"panels/network/RequestCookiesView.ts | thisRequestHasNoCookies":{"message":"This request has no cookies."},"panels/network/RequestHeadersView.ts | activeClientExperimentVariation":{"message":"Active client experiment variation IDs."},"panels/network/RequestHeadersView.ts | activeClientExperimentVariationIds":{"message":"Active client experiment variation IDs that trigger server-side behavior."},"panels/network/RequestHeadersView.ts | chooseThisOptionIfTheResourceAnd":{"message":"Choose this option if the resource and the document are served from the same site."},"panels/network/RequestHeadersView.ts | copyValue":{"message":"Copy value"},"panels/network/RequestHeadersView.ts | decoded":{"message":"Decoded:"},"panels/network/RequestHeadersView.ts | fromDiskCache":{"message":"(from disk cache)"},"panels/network/RequestHeadersView.ts | fromMemoryCache":{"message":"(from memory cache)"},"panels/network/RequestHeadersView.ts | fromPrefetchCache":{"message":"(from prefetch cache)"},"panels/network/RequestHeadersView.ts | fromServiceWorker":{"message":"(from service worker)"},"panels/network/RequestHeadersView.ts | fromSignedexchange":{"message":"(from signed-exchange)"},"panels/network/RequestHeadersView.ts | fromWebBundle":{"message":"(from Web Bundle)"},"panels/network/RequestHeadersView.ts | general":{"message":"General"},"panels/network/RequestHeadersView.ts | learnMore":{"message":"Learn more"},"panels/network/RequestHeadersView.ts | learnMoreInTheIssuesTab":{"message":"Learn more in the issues tab"},"panels/network/RequestHeadersView.ts | onlyChooseThisOptionIfAn":{"message":"Only choose this option if an arbitrary website including this resource does not impose a security risk."},"panels/network/RequestHeadersView.ts | onlyProvisionalHeadersAre":{"message":"Only provisional headers are available because this request was not sent over the network and instead was served from a local cache, which doesn’t store the original request headers. Disable cache to see full request headers."},"panels/network/RequestHeadersView.ts | provisionalHeadersAreShown":{"message":"Provisional headers are shown"},"panels/network/RequestHeadersView.ts | provisionalHeadersAreShownS":{"message":"Provisional headers are shown. Disable cache to see full headers."},"panels/network/RequestHeadersView.ts | recordedAttribution":{"message":"Recorded attribution with trigger-data: {PH1}"},"panels/network/RequestHeadersView.ts | referrerPolicy":{"message":"Referrer Policy"},"panels/network/RequestHeadersView.ts | remoteAddress":{"message":"Remote Address"},"panels/network/RequestHeadersView.ts | requestHeaders":{"message":"Request Headers"},"panels/network/RequestHeadersView.ts | requestMethod":{"message":"Request Method"},"panels/network/RequestHeadersView.ts | requestUrl":{"message":"Request URL"},"panels/network/RequestHeadersView.ts | responseHeaders":{"message":"Response Headers"},"panels/network/RequestHeadersView.ts | showMore":{"message":"Show more"},"panels/network/RequestHeadersView.ts | statusCode":{"message":"Status Code"},"panels/network/RequestHeadersView.ts | thisDocumentWasBlockedFrom":{"message":"This document was blocked from loading in an iframe with a sandbox attribute because this document specified a cross-origin opener policy."},"panels/network/RequestHeadersView.ts | toEmbedThisFrameInYourDocument":{"message":"To embed this frame in your document, the response needs to enable the cross-origin embedder policy by specifying the following response header:"},"panels/network/RequestHeadersView.ts | toUseThisResourceFromADifferent":{"message":"To use this resource from a different origin, the server needs to specify a cross-origin resource policy in the response headers:"},"panels/network/RequestHeadersView.ts | toUseThisResourceFromADifferentOrigin":{"message":"To use this resource from a different origin, the server may relax the cross-origin resource policy response header:"},"panels/network/RequestHeadersView.ts | toUseThisResourceFromADifferentSite":{"message":"To use this resource from a different site, the server may relax the cross-origin resource policy response header:"},"panels/network/RequestHeadersView.ts | viewParsed":{"message":"View parsed"},"panels/network/RequestHeadersView.ts | viewSource":{"message":"View source"},"panels/network/RequestInitiatorView.ts | requestCallStack":{"message":"Request call stack"},"panels/network/RequestInitiatorView.ts | requestInitiatorChain":{"message":"Request initiator chain"},"panels/network/RequestInitiatorView.ts | thisRequestHasNoInitiatorData":{"message":"This request has no initiator data."},"panels/network/RequestPayloadView.ts | copyValue":{"message":"Copy value"},"panels/network/RequestPayloadView.ts | empty":{"message":"(empty)"},"panels/network/RequestPayloadView.ts | formData":{"message":"Form Data"},"panels/network/RequestPayloadView.ts | queryStringParameters":{"message":"Query String Parameters"},"panels/network/RequestPayloadView.ts | requestPayload":{"message":"Request Payload"},"panels/network/RequestPayloadView.ts | showMore":{"message":"Show more"},"panels/network/RequestPayloadView.ts | unableToDecodeValue":{"message":"(unable to decode value)"},"panels/network/RequestPayloadView.ts | viewDecoded":{"message":"View decoded"},"panels/network/RequestPayloadView.ts | viewDecodedL":{"message":"view decoded"},"panels/network/RequestPayloadView.ts | viewParsed":{"message":"View parsed"},"panels/network/RequestPayloadView.ts | viewParsedL":{"message":"view parsed"},"panels/network/RequestPayloadView.ts | viewSource":{"message":"View source"},"panels/network/RequestPayloadView.ts | viewSourceL":{"message":"view source"},"panels/network/RequestPayloadView.ts | viewUrlEncoded":{"message":"View URL-encoded"},"panels/network/RequestPayloadView.ts | viewUrlEncodedL":{"message":"view URL-encoded"},"panels/network/RequestPreviewView.ts | failedToLoadResponseData":{"message":"Failed to load response data"},"panels/network/RequestPreviewView.ts | previewNotAvailable":{"message":"Preview not available"},"panels/network/RequestResponseView.ts | failedToLoadResponseData":{"message":"Failed to load response data"},"panels/network/RequestResponseView.ts | thisRequestHasNoResponseData":{"message":"This request has no response data available."},"panels/network/RequestTimingView.ts | cacheStorageCacheNameS":{"message":"Cache storage cache name: {PH1}"},"panels/network/RequestTimingView.ts | cacheStorageCacheNameUnknown":{"message":"Cache storage cache name: Unknown"},"panels/network/RequestTimingView.ts | cautionRequestIsNotFinishedYet":{"message":"CAUTION: request is not finished yet!"},"panels/network/RequestTimingView.ts | connectionStart":{"message":"Connection Start"},"panels/network/RequestTimingView.ts | contentDownload":{"message":"Content Download"},"panels/network/RequestTimingView.ts | dnsLookup":{"message":"DNS Lookup"},"panels/network/RequestTimingView.ts | duration":{"message":"Duration"},"panels/network/RequestTimingView.ts | durationC":{"message":"DURATION"},"panels/network/RequestTimingView.ts | duringDevelopmentYouCanUseSToAdd":{"message":"During development, you can use {PH1} to add insights into the server-side timing of this request."},"panels/network/RequestTimingView.ts | explanation":{"message":"Explanation"},"panels/network/RequestTimingView.ts | fallbackCode":{"message":"Fallback code"},"panels/network/RequestTimingView.ts | fromHttpCache":{"message":"From HTTP cache"},"panels/network/RequestTimingView.ts | initialConnection":{"message":"Initial connection"},"panels/network/RequestTimingView.ts | label":{"message":"Label"},"panels/network/RequestTimingView.ts | networkFetch":{"message":"Network fetch"},"panels/network/RequestTimingView.ts | originalRequest":{"message":"Original Request"},"panels/network/RequestTimingView.ts | proxyNegotiation":{"message":"Proxy negotiation"},"panels/network/RequestTimingView.ts | queuedAtS":{"message":"Queued at {PH1}"},"panels/network/RequestTimingView.ts | queueing":{"message":"Queueing"},"panels/network/RequestTimingView.ts | readingPush":{"message":"Reading Push"},"panels/network/RequestTimingView.ts | receivingPush":{"message":"Receiving Push"},"panels/network/RequestTimingView.ts | requestresponse":{"message":"Request/Response"},"panels/network/RequestTimingView.ts | requestSent":{"message":"Request sent"},"panels/network/RequestTimingView.ts | requestToServiceworker":{"message":"Request to ServiceWorker"},"panels/network/RequestTimingView.ts | resourceScheduling":{"message":"Resource Scheduling"},"panels/network/RequestTimingView.ts | respondwith":{"message":"respondWith"},"panels/network/RequestTimingView.ts | responseReceived":{"message":"Response Received"},"panels/network/RequestTimingView.ts | retrievalTimeS":{"message":"Retrieval Time: {PH1}"},"panels/network/RequestTimingView.ts | serverPush":{"message":"Server Push"},"panels/network/RequestTimingView.ts | serverTiming":{"message":"Server Timing"},"panels/network/RequestTimingView.ts | serviceworkerCacheStorage":{"message":"ServiceWorker cache storage"},"panels/network/RequestTimingView.ts | sourceOfResponseS":{"message":"Source of response: {PH1}"},"panels/network/RequestTimingView.ts | ssl":{"message":"SSL"},"panels/network/RequestTimingView.ts | stalled":{"message":"Stalled"},"panels/network/RequestTimingView.ts | startedAtS":{"message":"Started at {PH1}"},"panels/network/RequestTimingView.ts | startup":{"message":"Startup"},"panels/network/RequestTimingView.ts | theServerTimingApi":{"message":"the Server Timing API"},"panels/network/RequestTimingView.ts | time":{"message":"TIME"},"panels/network/RequestTimingView.ts | total":{"message":"Total"},"panels/network/RequestTimingView.ts | unknown":{"message":"Unknown"},"panels/network/RequestTimingView.ts | waitingTtfb":{"message":"Waiting (TTFB)"},"panels/network/RequestTimingView.ts | waterfall":{"message":"Waterfall"},"panels/network/ResourceWebSocketFrameView.ts | all":{"message":"All"},"panels/network/ResourceWebSocketFrameView.ts | binaryMessage":{"message":"Binary Message"},"panels/network/ResourceWebSocketFrameView.ts | clearAll":{"message":"Clear All"},"panels/network/ResourceWebSocketFrameView.ts | clearAllL":{"message":"Clear all"},"panels/network/ResourceWebSocketFrameView.ts | connectionCloseMessage":{"message":"Connection Close Message"},"panels/network/ResourceWebSocketFrameView.ts | continuationFrame":{"message":"Continuation Frame"},"panels/network/ResourceWebSocketFrameView.ts | copyMessage":{"message":"Copy message"},"panels/network/ResourceWebSocketFrameView.ts | copyMessageD":{"message":"Copy message..."},"panels/network/ResourceWebSocketFrameView.ts | data":{"message":"Data"},"panels/network/ResourceWebSocketFrameView.ts | enterRegex":{"message":"Enter regex, for example: (web)?socket"},"panels/network/ResourceWebSocketFrameView.ts | filter":{"message":"Filter"},"panels/network/ResourceWebSocketFrameView.ts | length":{"message":"Length"},"panels/network/ResourceWebSocketFrameView.ts | na":{"message":"N/A"},"panels/network/ResourceWebSocketFrameView.ts | pingMessage":{"message":"Ping Message"},"panels/network/ResourceWebSocketFrameView.ts | pongMessage":{"message":"Pong Message"},"panels/network/ResourceWebSocketFrameView.ts | receive":{"message":"Receive"},"panels/network/ResourceWebSocketFrameView.ts | selectMessageToBrowseItsContent":{"message":"Select message to browse its content."},"panels/network/ResourceWebSocketFrameView.ts | send":{"message":"Send"},"panels/network/ResourceWebSocketFrameView.ts | sOpcodeS":{"message":"{PH1} (Opcode {PH2})"},"panels/network/ResourceWebSocketFrameView.ts | sOpcodeSMask":{"message":"{PH1} (Opcode {PH2}, mask)"},"panels/network/ResourceWebSocketFrameView.ts | textMessage":{"message":"Text Message"},"panels/network/ResourceWebSocketFrameView.ts | time":{"message":"Time"},"panels/network/ResourceWebSocketFrameView.ts | webSocketFrame":{"message":"Web Socket Frame"},"panels/network/SignedExchangeInfoView.ts | certificate":{"message":"Certificate"},"panels/network/SignedExchangeInfoView.ts | certificateSha":{"message":"Certificate SHA256"},"panels/network/SignedExchangeInfoView.ts | certificateUrl":{"message":"Certificate URL"},"panels/network/SignedExchangeInfoView.ts | date":{"message":"Date"},"panels/network/SignedExchangeInfoView.ts | errors":{"message":"Errors"},"panels/network/SignedExchangeInfoView.ts | expires":{"message":"Expires"},"panels/network/SignedExchangeInfoView.ts | headerIntegrityHash":{"message":"Header integrity hash"},"panels/network/SignedExchangeInfoView.ts | integrity":{"message":"Integrity"},"panels/network/SignedExchangeInfoView.ts | issuer":{"message":"Issuer"},"panels/network/SignedExchangeInfoView.ts | label":{"message":"Label"},"panels/network/SignedExchangeInfoView.ts | learnmore":{"message":"Learn more"},"panels/network/SignedExchangeInfoView.ts | requestUrl":{"message":"Request URL"},"panels/network/SignedExchangeInfoView.ts | responseCode":{"message":"Response code"},"panels/network/SignedExchangeInfoView.ts | responseHeaders":{"message":"Response headers"},"panels/network/SignedExchangeInfoView.ts | signature":{"message":"Signature"},"panels/network/SignedExchangeInfoView.ts | signedHttpExchange":{"message":"Signed HTTP exchange"},"panels/network/SignedExchangeInfoView.ts | subject":{"message":"Subject"},"panels/network/SignedExchangeInfoView.ts | validFrom":{"message":"Valid from"},"panels/network/SignedExchangeInfoView.ts | validityUrl":{"message":"Validity URL"},"panels/network/SignedExchangeInfoView.ts | validUntil":{"message":"Valid until"},"panels/network/SignedExchangeInfoView.ts | viewCertificate":{"message":"View certificate"},"panels/performance_monitor/performance_monitor-meta.ts | activity":{"message":"activity"},"panels/performance_monitor/performance_monitor-meta.ts | metrics":{"message":"metrics"},"panels/performance_monitor/performance_monitor-meta.ts | monitor":{"message":"monitor"},"panels/performance_monitor/performance_monitor-meta.ts | performance":{"message":"performance"},"panels/performance_monitor/performance_monitor-meta.ts | performanceMonitor":{"message":"Performance monitor"},"panels/performance_monitor/performance_monitor-meta.ts | showPerformanceMonitor":{"message":"Show Performance monitor"},"panels/performance_monitor/performance_monitor-meta.ts | systemMonitor":{"message":"system monitor"},"panels/performance_monitor/PerformanceMonitor.ts | cpuUsage":{"message":"CPU usage"},"panels/performance_monitor/PerformanceMonitor.ts | documentFrames":{"message":"Document Frames"},"panels/performance_monitor/PerformanceMonitor.ts | documents":{"message":"Documents"},"panels/performance_monitor/PerformanceMonitor.ts | domNodes":{"message":"DOM Nodes"},"panels/performance_monitor/PerformanceMonitor.ts | graphsDisplayingARealtimeViewOf":{"message":"Graphs displaying a real-time view of performance metrics"},"panels/performance_monitor/PerformanceMonitor.ts | jsEventListeners":{"message":"JS event listeners"},"panels/performance_monitor/PerformanceMonitor.ts | jsHeapSize":{"message":"JS heap size"},"panels/performance_monitor/PerformanceMonitor.ts | layoutsSec":{"message":"Layouts / sec"},"panels/performance_monitor/PerformanceMonitor.ts | paused":{"message":"Paused"},"panels/performance_monitor/PerformanceMonitor.ts | styleRecalcsSec":{"message":"Style recalcs / sec"},"panels/profiler/CPUProfileView.ts | aggregatedSelfTime":{"message":"Aggregated self time"},"panels/profiler/CPUProfileView.ts | aggregatedTotalTime":{"message":"Aggregated total time"},"panels/profiler/CPUProfileView.ts | cpuProfiles":{"message":"CPU PROFILES"},"panels/profiler/CPUProfileView.ts | cpuProfilesShow":{"message":"CPU profiles show where the execution time is spent in your page\'s JavaScript functions."},"panels/profiler/CPUProfileView.ts | fms":{"message":"{PH1} ms"},"panels/profiler/CPUProfileView.ts | formatPercent":{"message":"{PH1} %"},"panels/profiler/CPUProfileView.ts | name":{"message":"Name"},"panels/profiler/CPUProfileView.ts | notOptimized":{"message":"Not optimized"},"panels/profiler/CPUProfileView.ts | recording":{"message":"Recording…"},"panels/profiler/CPUProfileView.ts | recordJavascriptCpuProfile":{"message":"Record JavaScript CPU Profile"},"panels/profiler/CPUProfileView.ts | selfTime":{"message":"Self Time"},"panels/profiler/CPUProfileView.ts | startCpuProfiling":{"message":"Start CPU profiling"},"panels/profiler/CPUProfileView.ts | stopCpuProfiling":{"message":"Stop CPU profiling"},"panels/profiler/CPUProfileView.ts | totalTime":{"message":"Total Time"},"panels/profiler/CPUProfileView.ts | url":{"message":"URL"},"panels/profiler/HeapProfilerPanel.ts | revealInSummaryView":{"message":"Reveal in Summary view"},"panels/profiler/HeapProfileView.ts | allocationSampling":{"message":"Allocation sampling"},"panels/profiler/HeapProfileView.ts | formatPercent":{"message":"{PH1} %"},"panels/profiler/HeapProfileView.ts | heapProfilerIsRecording":{"message":"Heap profiler is recording"},"panels/profiler/HeapProfileView.ts | itProvidesGoodApproximation":{"message":"It provides good approximation of allocations broken down by JavaScript execution stack."},"panels/profiler/HeapProfileView.ts | name":{"message":"Name"},"panels/profiler/HeapProfileView.ts | profileD":{"message":"Profile {PH1}"},"panels/profiler/HeapProfileView.ts | recording":{"message":"Recording…"},"panels/profiler/HeapProfileView.ts | recordMemoryAllocations":{"message":"Record memory allocations using sampling method."},"panels/profiler/HeapProfileView.ts | samplingProfiles":{"message":"SAMPLING PROFILES"},"panels/profiler/HeapProfileView.ts | sBytes":{"message":"{PH1} bytes"},"panels/profiler/HeapProfileView.ts | selectedSizeS":{"message":"Selected size: {PH1}"},"panels/profiler/HeapProfileView.ts | selfSize":{"message":"Self size"},"panels/profiler/HeapProfileView.ts | selfSizeBytes":{"message":"Self Size (bytes)"},"panels/profiler/HeapProfileView.ts | skb":{"message":"{PH1} kB"},"panels/profiler/HeapProfileView.ts | startHeapProfiling":{"message":"Start heap profiling"},"panels/profiler/HeapProfileView.ts | stopHeapProfiling":{"message":"Stop heap profiling"},"panels/profiler/HeapProfileView.ts | stopping":{"message":"Stopping…"},"panels/profiler/HeapProfileView.ts | thisProfileTypeHasMinimal":{"message":"This profile type has minimal performance overhead and can be used for long running operations."},"panels/profiler/HeapProfileView.ts | totalSize":{"message":"Total size"},"panels/profiler/HeapProfileView.ts | totalSizeBytes":{"message":"Total Size (bytes)"},"panels/profiler/HeapProfileView.ts | url":{"message":"URL"},"panels/profiler/HeapSnapshotDataGrids.ts | allocation":{"message":"Allocation"},"panels/profiler/HeapSnapshotDataGrids.ts | allocSize":{"message":"Alloc. Size"},"panels/profiler/HeapSnapshotDataGrids.ts | constructorString":{"message":"Constructor"},"panels/profiler/HeapSnapshotDataGrids.ts | count":{"message":"Count"},"panels/profiler/HeapSnapshotDataGrids.ts | Deleted":{"message":"# Deleted"},"panels/profiler/HeapSnapshotDataGrids.ts | Delta":{"message":"# Delta"},"panels/profiler/HeapSnapshotDataGrids.ts | distance":{"message":"Distance"},"panels/profiler/HeapSnapshotDataGrids.ts | distanceFromWindowObject":{"message":"Distance from window object"},"panels/profiler/HeapSnapshotDataGrids.ts | freedSize":{"message":"Freed Size"},"panels/profiler/HeapSnapshotDataGrids.ts | function":{"message":"Function"},"panels/profiler/HeapSnapshotDataGrids.ts | heapSnapshotConstructors":{"message":"Heap Snapshot Constructors"},"panels/profiler/HeapSnapshotDataGrids.ts | heapSnapshotDiff":{"message":"Heap Snapshot Diff"},"panels/profiler/HeapSnapshotDataGrids.ts | heapSnapshotRetainment":{"message":"Heap Snapshot Retainment"},"panels/profiler/HeapSnapshotDataGrids.ts | liveCount":{"message":"Live Count"},"panels/profiler/HeapSnapshotDataGrids.ts | liveSize":{"message":"Live Size"},"panels/profiler/HeapSnapshotDataGrids.ts | New":{"message":"# New"},"panels/profiler/HeapSnapshotDataGrids.ts | object":{"message":"Object"},"panels/profiler/HeapSnapshotDataGrids.ts | retainedSize":{"message":"Retained Size"},"panels/profiler/HeapSnapshotDataGrids.ts | shallowSize":{"message":"Shallow Size"},"panels/profiler/HeapSnapshotDataGrids.ts | size":{"message":"Size"},"panels/profiler/HeapSnapshotDataGrids.ts | sizeDelta":{"message":"Size Delta"},"panels/profiler/HeapSnapshotDataGrids.ts | sizeOfTheObjectItselfInBytes":{"message":"Size of the object itself in bytes"},"panels/profiler/HeapSnapshotDataGrids.ts | sizeOfTheObjectPlusTheGraphIt":{"message":"Size of the object plus the graph it retains in bytes"},"panels/profiler/HeapSnapshotGridNodes.ts | detachedFromDomTree":{"message":"Detached from DOM tree"},"panels/profiler/HeapSnapshotGridNodes.ts | genericStringsTwoPlaceholders":{"message":"{PH1}, {PH2}"},"panels/profiler/HeapSnapshotGridNodes.ts | inElement":{"message":"in"},"panels/profiler/HeapSnapshotGridNodes.ts | internalArray":{"message":"(internal array)[]"},"panels/profiler/HeapSnapshotGridNodes.ts | previewIsNotAvailable":{"message":"Preview is not available"},"panels/profiler/HeapSnapshotGridNodes.ts | revealInSummaryView":{"message":"Reveal in Summary view"},"panels/profiler/HeapSnapshotGridNodes.ts | revealObjectSWithIdSInSummary":{"message":"Reveal object \'\'{PH1}\'\' with id @{PH2} in Summary view"},"panels/profiler/HeapSnapshotGridNodes.ts | storeAsGlobalVariable":{"message":"Store as global variable"},"panels/profiler/HeapSnapshotGridNodes.ts | summary":{"message":"Summary"},"panels/profiler/HeapSnapshotGridNodes.ts | userObjectReachableFromWindow":{"message":"User object reachable from window"},"panels/profiler/HeapSnapshotProxy.ts | anErrorOccurredWhenACallToMethod":{"message":"An error occurred when a call to method \'\'{PH1}\'\' was requested"},"panels/profiler/HeapSnapshotView.ts | allObjects":{"message":"All objects"},"panels/profiler/HeapSnapshotView.ts | allocation":{"message":"Allocation"},"panels/profiler/HeapSnapshotView.ts | allocationInstrumentationOn":{"message":"Allocation instrumentation on timeline"},"panels/profiler/HeapSnapshotView.ts | allocationStack":{"message":"Allocation stack"},"panels/profiler/HeapSnapshotView.ts | allocationTimelines":{"message":"ALLOCATION TIMELINES"},"panels/profiler/HeapSnapshotView.ts | AllocationTimelinesShowInstrumented":{"message":"Allocation timelines show instrumented JavaScript memory allocations over time. Once profile is recorded you can select a time interval to see objects that were allocated within it and still alive by the end of recording. Use this profile type to isolate memory leaks."},"panels/profiler/HeapSnapshotView.ts | baseSnapshot":{"message":"Base snapshot"},"panels/profiler/HeapSnapshotView.ts | captureNumericValue":{"message":"Include numerical values in capture"},"panels/profiler/HeapSnapshotView.ts | classFilter":{"message":"Class filter"},"panels/profiler/HeapSnapshotView.ts | code":{"message":"Code"},"panels/profiler/HeapSnapshotView.ts | comparison":{"message":"Comparison"},"panels/profiler/HeapSnapshotView.ts | containment":{"message":"Containment"},"panels/profiler/HeapSnapshotView.ts | filter":{"message":"Filter"},"panels/profiler/HeapSnapshotView.ts | find":{"message":"Find"},"panels/profiler/HeapSnapshotView.ts | heapMemoryUsage":{"message":"Heap memory usage"},"panels/profiler/HeapSnapshotView.ts | heapSnapshot":{"message":"Heap snapshot"},"panels/profiler/HeapSnapshotView.ts | heapSnapshotProfilesShowMemory":{"message":"Heap snapshot profiles show memory distribution among your page\'s JavaScript objects and related DOM nodes."},"panels/profiler/HeapSnapshotView.ts | heapSnapshots":{"message":"HEAP SNAPSHOTS"},"panels/profiler/HeapSnapshotView.ts | jsArrays":{"message":"JS arrays"},"panels/profiler/HeapSnapshotView.ts | liveObjects":{"message":"Live objects"},"panels/profiler/HeapSnapshotView.ts | loading":{"message":"Loading…"},"panels/profiler/HeapSnapshotView.ts | objectsAllocatedBeforeS":{"message":"Objects allocated before {PH1}"},"panels/profiler/HeapSnapshotView.ts | objectsAllocatedBetweenSAndS":{"message":"Objects allocated between {PH1} and {PH2}"},"panels/profiler/HeapSnapshotView.ts | percentagePlaceholder":{"message":"{PH1}%"},"panels/profiler/HeapSnapshotView.ts | perspective":{"message":"Perspective"},"panels/profiler/HeapSnapshotView.ts | recordAllocationStacksExtra":{"message":"Record stack traces of allocations (extra performance overhead)"},"panels/profiler/HeapSnapshotView.ts | recording":{"message":"Recording…"},"panels/profiler/HeapSnapshotView.ts | retainers":{"message":"Retainers"},"panels/profiler/HeapSnapshotView.ts | savingD":{"message":"Saving… {PH1}%"},"panels/profiler/HeapSnapshotView.ts | selectedSizeS":{"message":"Selected size: {PH1}"},"panels/profiler/HeapSnapshotView.ts | sKb":{"message":"{PH1} kB"},"panels/profiler/HeapSnapshotView.ts | snapshotD":{"message":"Snapshot {PH1}"},"panels/profiler/HeapSnapshotView.ts | snapshotting":{"message":"Snapshotting…"},"panels/profiler/HeapSnapshotView.ts | stackWasNotRecordedForThisObject":{"message":"Stack was not recorded for this object because it had been allocated before this profile recording started."},"panels/profiler/HeapSnapshotView.ts | startRecordingHeapProfile":{"message":"Start recording heap profile"},"panels/profiler/HeapSnapshotView.ts | statistics":{"message":"Statistics"},"panels/profiler/HeapSnapshotView.ts | stopRecordingHeapProfile":{"message":"Stop recording heap profile"},"panels/profiler/HeapSnapshotView.ts | strings":{"message":"Strings"},"panels/profiler/HeapSnapshotView.ts | summary":{"message":"Summary"},"panels/profiler/HeapSnapshotView.ts | systemObjects":{"message":"System objects"},"panels/profiler/HeapSnapshotView.ts | takeHeapSnapshot":{"message":"Take heap snapshot"},"panels/profiler/HeapSnapshotView.ts | treatGlobalObjectsAsRoots":{"message":"Treat global objects as roots (recommended, unchecking this exposes internal nodes and introduces excessive detail, but might help debugging cycles in retaining paths)"},"panels/profiler/HeapSnapshotView.ts | typedArrays":{"message":"Typed arrays"},"panels/profiler/IsolateSelector.ts | changeRate":{"message":"{PH1}/s"},"panels/profiler/IsolateSelector.ts | decreasingBySPerSecond":{"message":"decreasing by {PH1} per second"},"panels/profiler/IsolateSelector.ts | empty":{"message":"(empty)"},"panels/profiler/IsolateSelector.ts | heapSizeChangeTrendOverTheLastS":{"message":"Heap size change trend over the last {PH1} minutes."},"panels/profiler/IsolateSelector.ts | heapSizeInUseByLiveJsObjects":{"message":"Heap size in use by live JS objects."},"panels/profiler/IsolateSelector.ts | increasingBySPerSecond":{"message":"increasing by {PH1} per second"},"panels/profiler/IsolateSelector.ts | javascriptVmInstances":{"message":"JavaScript VM instances"},"panels/profiler/IsolateSelector.ts | totalJsHeapSize":{"message":"Total JS heap size"},"panels/profiler/IsolateSelector.ts | totalPageJsHeapSizeAcrossAllVm":{"message":"Total page JS heap size across all VM instances."},"panels/profiler/IsolateSelector.ts | totalPageJsHeapSizeChangeTrend":{"message":"Total page JS heap size change trend over the last {PH1} minutes."},"panels/profiler/LiveHeapProfileView.ts | allocatedJsHeapSizeCurrentlyIn":{"message":"Allocated JS heap size currently in use"},"panels/profiler/LiveHeapProfileView.ts | anonymousScriptS":{"message":"(Anonymous Script {PH1})"},"panels/profiler/LiveHeapProfileView.ts | heapProfile":{"message":"Heap Profile"},"panels/profiler/LiveHeapProfileView.ts | jsHeap":{"message":"JS Heap"},"panels/profiler/LiveHeapProfileView.ts | kb":{"message":"kB"},"panels/profiler/LiveHeapProfileView.ts | numberOfVmsSharingTheSameScript":{"message":"Number of VMs sharing the same script source"},"panels/profiler/LiveHeapProfileView.ts | scriptUrl":{"message":"Script URL"},"panels/profiler/LiveHeapProfileView.ts | urlOfTheScriptSource":{"message":"URL of the script source"},"panels/profiler/LiveHeapProfileView.ts | vms":{"message":"VMs"},"panels/profiler/ModuleUIStrings.ts | buildingAllocationStatistics":{"message":"Building allocation statistics…"},"panels/profiler/ModuleUIStrings.ts | buildingDominatedNodes":{"message":"Building dominated nodes…"},"panels/profiler/ModuleUIStrings.ts | buildingDominatorTree":{"message":"Building dominator tree…"},"panels/profiler/ModuleUIStrings.ts | buildingEdgeIndexes":{"message":"Building edge indexes…"},"panels/profiler/ModuleUIStrings.ts | buildingLocations":{"message":"Building locations…"},"panels/profiler/ModuleUIStrings.ts | buildingPostorderIndex":{"message":"Building postorder index…"},"panels/profiler/ModuleUIStrings.ts | buildingRetainers":{"message":"Building retainers…"},"panels/profiler/ModuleUIStrings.ts | calculatingDistances":{"message":"Calculating distances…"},"panels/profiler/ModuleUIStrings.ts | calculatingNodeFlags":{"message":"Calculating node flags…"},"panels/profiler/ModuleUIStrings.ts | calculatingRetainedSizes":{"message":"Calculating retained sizes…"},"panels/profiler/ModuleUIStrings.ts | calculatingSamples":{"message":"Calculating samples…"},"panels/profiler/ModuleUIStrings.ts | calculatingStatistics":{"message":"Calculating statistics…"},"panels/profiler/ModuleUIStrings.ts | done":{"message":"Done"},"panels/profiler/ModuleUIStrings.ts | finishedProcessing":{"message":"Finished processing."},"panels/profiler/ModuleUIStrings.ts | loadingAllocationTracesD":{"message":"Loading allocation traces… {PH1}%"},"panels/profiler/ModuleUIStrings.ts | loadingEdgesD":{"message":"Loading edges… {PH1}%"},"panels/profiler/ModuleUIStrings.ts | loadingLocations":{"message":"Loading locations…"},"panels/profiler/ModuleUIStrings.ts | loadingNodesD":{"message":"Loading nodes… {PH1}%"},"panels/profiler/ModuleUIStrings.ts | loadingSamples":{"message":"Loading samples…"},"panels/profiler/ModuleUIStrings.ts | loadingSnapshotInfo":{"message":"Loading snapshot info…"},"panels/profiler/ModuleUIStrings.ts | loadingStrings":{"message":"Loading strings…"},"panels/profiler/ModuleUIStrings.ts | parsingStrings":{"message":"Parsing strings…"},"panels/profiler/ModuleUIStrings.ts | processingSnapshot":{"message":"Processing snapshot…"},"panels/profiler/ModuleUIStrings.ts | propagatingDomState":{"message":"Propagating DOM state…"},"panels/profiler/ProfileDataGrid.ts | genericTextTwoPlaceholders":{"message":"{PH1}, {PH2}"},"panels/profiler/ProfileDataGrid.ts | notOptimizedS":{"message":"Not optimized: {PH1}"},"panels/profiler/ProfileLauncherView.ts | load":{"message":"Load"},"panels/profiler/ProfileLauncherView.ts | selectJavascriptVmInstance":{"message":"Select JavaScript VM instance"},"panels/profiler/ProfileLauncherView.ts | selectProfilingType":{"message":"Select profiling type"},"panels/profiler/ProfileLauncherView.ts | start":{"message":"Start"},"panels/profiler/ProfileLauncherView.ts | stop":{"message":"Stop"},"panels/profiler/ProfileLauncherView.ts | takeSnapshot":{"message":"Take snapshot"},"panels/profiler/profiler-meta.ts | liveHeapProfile":{"message":"Live Heap Profile"},"panels/profiler/profiler-meta.ts | memory":{"message":"Memory"},"panels/profiler/profiler-meta.ts | showLiveHeapProfile":{"message":"Show Live Heap Profile"},"panels/profiler/profiler-meta.ts | showMemory":{"message":"Show Memory"},"panels/profiler/profiler-meta.ts | showNativeFunctions":{"message":"Show native functions in JS Profile"},"panels/profiler/profiler-meta.ts | startRecordingHeapAllocations":{"message":"Start recording heap allocations"},"panels/profiler/profiler-meta.ts | startRecordingHeapAllocationsAndReload":{"message":"Start recording heap allocations and reload the page"},"panels/profiler/profiler-meta.ts | startStopRecording":{"message":"Start/stop recording"},"panels/profiler/profiler-meta.ts | stopRecordingHeapAllocations":{"message":"Stop recording heap allocations"},"panels/profiler/ProfileSidebarTreeElement.ts | delete":{"message":"Delete"},"panels/profiler/ProfileSidebarTreeElement.ts | load":{"message":"Load…"},"panels/profiler/ProfileSidebarTreeElement.ts | save":{"message":"Save"},"panels/profiler/ProfileSidebarTreeElement.ts | saveWithEllipsis":{"message":"Save…"},"panels/profiler/ProfilesPanel.ts | cantLoadFileSupportedFile":{"message":"Can’t load file. Supported file extensions: \'\'{PH1}\'\'."},"panels/profiler/ProfilesPanel.ts | cantLoadProfileWhileAnother":{"message":"Can’t load profile while another profile is being recorded."},"panels/profiler/ProfilesPanel.ts | clearAllProfiles":{"message":"Clear all profiles"},"panels/profiler/ProfilesPanel.ts | load":{"message":"Load…"},"panels/profiler/ProfilesPanel.ts | profileLoadingFailedS":{"message":"Profile loading failed: {PH1}."},"panels/profiler/ProfilesPanel.ts | profiles":{"message":"Profiles"},"panels/profiler/ProfilesPanel.ts | runD":{"message":"Run {PH1}"},"panels/profiler/ProfileView.ts | chart":{"message":"Chart"},"panels/profiler/ProfileView.ts | excludeSelectedFunction":{"message":"Exclude selected function"},"panels/profiler/ProfileView.ts | failedToReadFile":{"message":"Failed to read file"},"panels/profiler/ProfileView.ts | fileSReadErrorS":{"message":"File \'\'{PH1}\'\' read error: {PH2}"},"panels/profiler/ProfileView.ts | findByCostMsNameOrFile":{"message":"Find by cost (>50ms), name or file"},"panels/profiler/ProfileView.ts | focusSelectedFunction":{"message":"Focus selected function"},"panels/profiler/ProfileView.ts | function":{"message":"Function"},"panels/profiler/ProfileView.ts | heavyBottomUp":{"message":"Heavy (Bottom Up)"},"panels/profiler/ProfileView.ts | loaded":{"message":"Loaded"},"panels/profiler/ProfileView.ts | loading":{"message":"Loading…"},"panels/profiler/ProfileView.ts | loadingD":{"message":"Loading… {PH1}%"},"panels/profiler/ProfileView.ts | parsing":{"message":"Parsing…"},"panels/profiler/ProfileView.ts | profile":{"message":"Profile"},"panels/profiler/ProfileView.ts | profileD":{"message":"Profile {PH1}"},"panels/profiler/ProfileView.ts | profiler":{"message":"Profiler"},"panels/profiler/ProfileView.ts | profileViewMode":{"message":"Profile view mode"},"panels/profiler/ProfileView.ts | restoreAllFunctions":{"message":"Restore all functions"},"panels/profiler/ProfileView.ts | treeTopDown":{"message":"Tree (Top Down)"},"panels/protocol_monitor/protocol_monitor-meta.ts | protocolMonitor":{"message":"Protocol monitor"},"panels/protocol_monitor/protocol_monitor-meta.ts | showProtocolMonitor":{"message":"Show Protocol monitor"},"panels/protocol_monitor/ProtocolMonitor.ts | clearAll":{"message":"Clear all"},"panels/protocol_monitor/ProtocolMonitor.ts | documentation":{"message":"Documentation"},"panels/protocol_monitor/ProtocolMonitor.ts | filter":{"message":"Filter"},"panels/protocol_monitor/ProtocolMonitor.ts | method":{"message":"Method"},"panels/protocol_monitor/ProtocolMonitor.ts | noMessageSelected":{"message":"No message selected"},"panels/protocol_monitor/ProtocolMonitor.ts | record":{"message":"Record"},"panels/protocol_monitor/ProtocolMonitor.ts | request":{"message":"Request"},"panels/protocol_monitor/ProtocolMonitor.ts | response":{"message":"Response"},"panels/protocol_monitor/ProtocolMonitor.ts | save":{"message":"Save"},"panels/protocol_monitor/ProtocolMonitor.ts | sendRawCDPCommand":{"message":"Send a raw CDP command"},"panels/protocol_monitor/ProtocolMonitor.ts | session":{"message":"Session"},"panels/protocol_monitor/ProtocolMonitor.ts | sMs":{"message":"{PH1} ms"},"panels/protocol_monitor/ProtocolMonitor.ts | target":{"message":"Target"},"panels/protocol_monitor/ProtocolMonitor.ts | timestamp":{"message":"Timestamp"},"panels/protocol_monitor/ProtocolMonitor.ts | type":{"message":"Type"},"panels/screencast/ScreencastApp.ts | toggleScreencast":{"message":"Toggle screencast"},"panels/screencast/ScreencastView.ts | addressBar":{"message":"Address bar"},"panels/screencast/ScreencastView.ts | back":{"message":"back"},"panels/screencast/ScreencastView.ts | forward":{"message":"forward"},"panels/screencast/ScreencastView.ts | profilingInProgress":{"message":"Profiling in progress"},"panels/screencast/ScreencastView.ts | reload":{"message":"reload"},"panels/screencast/ScreencastView.ts | screencastViewOfDebugTarget":{"message":"Screencast view of debug target"},"panels/screencast/ScreencastView.ts | theTabIsInactive":{"message":"The tab is inactive"},"panels/search/SearchResultsPane.ts | lineS":{"message":"Line {PH1}"},"panels/search/SearchResultsPane.ts | matchesCountS":{"message":"Matches Count {PH1}"},"panels/search/SearchResultsPane.ts | showDMore":{"message":"Show {PH1} more"},"panels/search/SearchView.ts | clear":{"message":"Clear"},"panels/search/SearchView.ts | foundDMatchingLinesInDFiles":{"message":"Found {PH1} matching lines in {PH2} files."},"panels/search/SearchView.ts | foundDMatchingLinesInFile":{"message":"Found {PH1} matching lines in 1 file."},"panels/search/SearchView.ts | foundMatchingLineInFile":{"message":"Found 1 matching line in 1 file."},"panels/search/SearchView.ts | indexing":{"message":"Indexing…"},"panels/search/SearchView.ts | indexingInterrupted":{"message":"Indexing interrupted."},"panels/search/SearchView.ts | matchCase":{"message":"Match Case"},"panels/search/SearchView.ts | noMatchesFound":{"message":"No matches found."},"panels/search/SearchView.ts | refresh":{"message":"Refresh"},"panels/search/SearchView.ts | search":{"message":"Search"},"panels/search/SearchView.ts | searchFinished":{"message":"Search finished."},"panels/search/SearchView.ts | searching":{"message":"Searching…"},"panels/search/SearchView.ts | searchInterrupted":{"message":"Search interrupted."},"panels/search/SearchView.ts | searchQuery":{"message":"Search Query"},"panels/search/SearchView.ts | useRegularExpression":{"message":"Use Regular Expression"},"panels/security/security-meta.ts | security":{"message":"Security"},"panels/security/security-meta.ts | showSecurity":{"message":"Show Security"},"panels/security/SecurityModel.ts | cipherWithMAC":{"message":"{PH1} with {PH2}"},"panels/security/SecurityModel.ts | keyExchangeWithGroup":{"message":"{PH1} with {PH2}"},"panels/security/SecurityModel.ts | theSecurityOfThisPageIsUnknown":{"message":"The security of this page is unknown."},"panels/security/SecurityModel.ts | thisPageIsNotSecure":{"message":"This page is not secure."},"panels/security/SecurityModel.ts | thisPageIsNotSecureBrokenHttps":{"message":"This page is not secure (broken HTTPS)."},"panels/security/SecurityModel.ts | thisPageIsSecureValidHttps":{"message":"This page is secure (valid HTTPS)."},"panels/security/SecurityPanel.ts | activeContentWithCertificate":{"message":"active content with certificate errors"},"panels/security/SecurityPanel.ts | activeMixedContent":{"message":"active mixed content"},"panels/security/SecurityPanel.ts | allResourcesOnThisPageAreServed":{"message":"All resources on this page are served securely."},"panels/security/SecurityPanel.ts | allServedSecurely":{"message":"all served securely"},"panels/security/SecurityPanel.ts | blockedMixedContent":{"message":"Blocked mixed content"},"panels/security/SecurityPanel.ts | certificate":{"message":"Certificate"},"panels/security/SecurityPanel.ts | certificateExpiresSoon":{"message":"Certificate expires soon"},"panels/security/SecurityPanel.ts | certificateTransparency":{"message":"Certificate Transparency"},"panels/security/SecurityPanel.ts | chromeHasDeterminedThatThisSiteS":{"message":"Chrome has determined that this site could be fake or fraudulent."},"panels/security/SecurityPanel.ts | cipher":{"message":"Cipher"},"panels/security/SecurityPanel.ts | connection":{"message":"Connection"},"panels/security/SecurityPanel.ts | contentWithCertificateErrors":{"message":"content with certificate errors"},"panels/security/SecurityPanel.ts | flaggedByGoogleSafeBrowsing":{"message":"Flagged by Google Safe Browsing"},"panels/security/SecurityPanel.ts | hashAlgorithm":{"message":"Hash algorithm"},"panels/security/SecurityPanel.ts | hideFullDetails":{"message":"Hide full details"},"panels/security/SecurityPanel.ts | ifYouBelieveThisIsShownIn":{"message":"If you believe this is shown in error please visit https://bugs.chromium.org/p/chromium/issues/entry?template=Safety+Tips+Appeals."},"panels/security/SecurityPanel.ts | ifYouBelieveThisIsShownInErrorSafety":{"message":"If you believe this is shown in error please visit https://bugs.chromium.org/p/chromium/issues/entry?template=Safety+Tips+Appeals."},"panels/security/SecurityPanel.ts | info":{"message":"Info"},"panels/security/SecurityPanel.ts | insecureSha":{"message":"insecure (SHA-1)"},"panels/security/SecurityPanel.ts | issuedAt":{"message":"Issued at"},"panels/security/SecurityPanel.ts | issuer":{"message":"Issuer"},"panels/security/SecurityPanel.ts | keyExchange":{"message":"Key exchange"},"panels/security/SecurityPanel.ts | keyExchangeGroup":{"message":"Key exchange group"},"panels/security/SecurityPanel.ts | logId":{"message":"Log ID"},"panels/security/SecurityPanel.ts | logName":{"message":"Log name"},"panels/security/SecurityPanel.ts | mainOrigin":{"message":"Main origin"},"panels/security/SecurityPanel.ts | mainOriginNonsecure":{"message":"Main origin (non-secure)"},"panels/security/SecurityPanel.ts | mainOriginSecure":{"message":"Main origin (secure)"},"panels/security/SecurityPanel.ts | missing":{"message":"missing"},"panels/security/SecurityPanel.ts | mixedContent":{"message":"mixed content"},"panels/security/SecurityPanel.ts | na":{"message":"(n/a)"},"panels/security/SecurityPanel.ts | nonsecureForm":{"message":"non-secure form"},"panels/security/SecurityPanel.ts | nonsecureOrigins":{"message":"Non-secure origins"},"panels/security/SecurityPanel.ts | noSecurityDetailsAreAvailableFor":{"message":"No security details are available for this origin."},"panels/security/SecurityPanel.ts | noSecurityInformation":{"message":"No security information"},"panels/security/SecurityPanel.ts | notSecure":{"message":"Not secure"},"panels/security/SecurityPanel.ts | notSecureBroken":{"message":"Not secure (broken)"},"panels/security/SecurityPanel.ts | obsoleteConnectionSettings":{"message":"obsolete connection settings"},"panels/security/SecurityPanel.ts | openFullCertificateDetails":{"message":"Open full certificate details"},"panels/security/SecurityPanel.ts | origin":{"message":"Origin"},"panels/security/SecurityPanel.ts | overview":{"message":"Overview"},"panels/security/SecurityPanel.ts | possibleSpoofingUrl":{"message":"Possible spoofing URL"},"panels/security/SecurityPanel.ts | protocol":{"message":"Protocol"},"panels/security/SecurityPanel.ts | publickeypinningBypassed":{"message":"Public-Key-Pinning bypassed"},"panels/security/SecurityPanel.ts | publickeypinningWasBypassedByA":{"message":"Public-Key-Pinning was bypassed by a local root certificate."},"panels/security/SecurityPanel.ts | reloadThePageToRecordRequestsFor":{"message":"Reload the page to record requests for HTTP resources."},"panels/security/SecurityPanel.ts | reloadToViewDetails":{"message":"Reload to view details"},"panels/security/SecurityPanel.ts | resources":{"message":"Resources"},"panels/security/SecurityPanel.ts | rsaKeyExchangeIsObsoleteEnableAn":{"message":"RSA key exchange is obsolete. Enable an ECDHE-based cipher suite."},"panels/security/SecurityPanel.ts | sct":{"message":"SCT"},"panels/security/SecurityPanel.ts | secure":{"message":"Secure"},"panels/security/SecurityPanel.ts | secureConnectionSettings":{"message":"secure connection settings"},"panels/security/SecurityPanel.ts | secureOrigins":{"message":"Secure origins"},"panels/security/SecurityPanel.ts | securityOverview":{"message":"Security overview"},"panels/security/SecurityPanel.ts | showFullDetails":{"message":"Show full details"},"panels/security/SecurityPanel.ts | showLess":{"message":"Show less"},"panels/security/SecurityPanel.ts | showMoreSTotal":{"message":"Show more ({PH1} total)"},"panels/security/SecurityPanel.ts | signatureAlgorithm":{"message":"Signature algorithm"},"panels/security/SecurityPanel.ts | signatureData":{"message":"Signature data"},"panels/security/SecurityPanel.ts | sIsObsoleteEnableAnAesgcmbased":{"message":"{PH1} is obsolete. Enable an AES-GCM-based cipher suite."},"panels/security/SecurityPanel.ts | sIsObsoleteEnableTlsOrLater":{"message":"{PH1} is obsolete. Enable TLS 1.2 or later."},"panels/security/SecurityPanel.ts | source":{"message":"Source"},"panels/security/SecurityPanel.ts | subject":{"message":"Subject"},"panels/security/SecurityPanel.ts | subjectAlternativeNameMissing":{"message":"Subject Alternative Name missing"},"panels/security/SecurityPanel.ts | theCertificateChainForThisSite":{"message":"The certificate chain for this site contains a certificate signed using SHA-1."},"panels/security/SecurityPanel.ts | theCertificateForThisSiteDoesNot":{"message":"The certificate for this site does not contain a Subject Alternative Name extension containing a domain name or IP address."},"panels/security/SecurityPanel.ts | theCertificateForThisSiteExpires":{"message":"The certificate for this site expires in less than 48 hours and needs to be renewed."},"panels/security/SecurityPanel.ts | theConnectionToThisSiteIs":{"message":"The connection to this site is encrypted and authenticated using {PH1}, {PH2}, and {PH3}."},"panels/security/SecurityPanel.ts | theConnectionToThisSiteIsUsingA":{"message":"The connection to this site is using a valid, trusted server certificate issued by {PH1}."},"panels/security/SecurityPanel.ts | theSecurityDetailsAboveAreFrom":{"message":"The security details above are from the first inspected response."},"panels/security/SecurityPanel.ts | theServerSignatureUsesShaWhichIs":{"message":"The server signature uses SHA-1, which is obsolete. Enable a SHA-2 signature algorithm instead. (Note this is different from the signature in the certificate.)"},"panels/security/SecurityPanel.ts | thisIsAnErrorPage":{"message":"This is an error page."},"panels/security/SecurityPanel.ts | thisOriginIsANonhttpsSecure":{"message":"This origin is a non-HTTPS secure origin."},"panels/security/SecurityPanel.ts | thisPageHasANonhttpsSecureOrigin":{"message":"This page has a non-HTTPS secure origin."},"panels/security/SecurityPanel.ts | thisPageIncludesAFormWithA":{"message":"This page includes a form with a non-secure \\"action\\" attribute."},"panels/security/SecurityPanel.ts | thisPageIncludesHttpResources":{"message":"This page includes HTTP resources."},"panels/security/SecurityPanel.ts | thisPageIncludesResourcesThat":{"message":"This page includes resources that were loaded with certificate errors."},"panels/security/SecurityPanel.ts | thisPageIsDangerousFlaggedBy":{"message":"This page is dangerous (flagged by Google Safe Browsing)."},"panels/security/SecurityPanel.ts | thisPageIsInsecureUnencrypted":{"message":"This page is insecure (unencrypted HTTP)."},"panels/security/SecurityPanel.ts | thisPageIsSuspicious":{"message":"This page is suspicious"},"panels/security/SecurityPanel.ts | thisPageIsSuspiciousFlaggedBy":{"message":"This page is suspicious (flagged by Chrome)."},"panels/security/SecurityPanel.ts | thisRequestCompliesWithChromes":{"message":"This request complies with Chrome\'s Certificate Transparency policy."},"panels/security/SecurityPanel.ts | thisRequestDoesNotComplyWith":{"message":"This request does not comply with Chrome\'s Certificate Transparency policy."},"panels/security/SecurityPanel.ts | thisResponseWasLoadedFromCache":{"message":"This response was loaded from cache. Some security details might be missing."},"panels/security/SecurityPanel.ts | thisSiteIsMissingAValidTrusted":{"message":"This site is missing a valid, trusted certificate ({PH1})."},"panels/security/SecurityPanel.ts | thisSitesHostnameLooksSimilarToP":{"message":"This site\'s hostname looks similar to {PH1}. Attackers sometimes mimic sites by making small, hard-to-see changes to the domain name."},"panels/security/SecurityPanel.ts | toCheckThisPagesStatusVisit":{"message":"To check this page\'s status, visit g.co/safebrowsingstatus."},"panels/security/SecurityPanel.ts | unknownCanceled":{"message":"Unknown / canceled"},"panels/security/SecurityPanel.ts | validAndTrusted":{"message":"valid and trusted"},"panels/security/SecurityPanel.ts | validationStatus":{"message":"Validation status"},"panels/security/SecurityPanel.ts | validFrom":{"message":"Valid from"},"panels/security/SecurityPanel.ts | validUntil":{"message":"Valid until"},"panels/security/SecurityPanel.ts | viewCertificate":{"message":"View certificate"},"panels/security/SecurityPanel.ts | viewDRequestsInNetworkPanel":{"message":"{n, plural, =1 {View # request in Network Panel} other {View # requests in Network Panel}}"},"panels/security/SecurityPanel.ts | viewRequestsInNetworkPanel":{"message":"View requests in Network Panel"},"panels/security/SecurityPanel.ts | youHaveRecentlyAllowedContent":{"message":"You have recently allowed content loaded with certificate errors (such as scripts or iframes) to run on this site."},"panels/security/SecurityPanel.ts | youHaveRecentlyAllowedNonsecure":{"message":"You have recently allowed non-secure content (such as scripts or iframes) to run on this site."},"panels/security/SecurityPanel.ts | yourConnectionToThisOriginIsNot":{"message":"Your connection to this origin is not secure."},"panels/security/SecurityPanel.ts | yourPageRequestedNonsecure":{"message":"Your page requested non-secure resources that were blocked."},"panels/sensors/LocationsSettingsTab.ts | addLocation":{"message":"Add location..."},"panels/sensors/LocationsSettingsTab.ts | customLocations":{"message":"Custom locations"},"panels/sensors/LocationsSettingsTab.ts | lat":{"message":"Lat"},"panels/sensors/LocationsSettingsTab.ts | latitude":{"message":"Latitude"},"panels/sensors/LocationsSettingsTab.ts | latitudeMustBeANumber":{"message":"Latitude must be a number"},"panels/sensors/LocationsSettingsTab.ts | latitudeMustBeGreaterThanOrEqual":{"message":"Latitude must be greater than or equal to {PH1}"},"panels/sensors/LocationsSettingsTab.ts | latitudeMustBeLessThanOrEqualToS":{"message":"Latitude must be less than or equal to {PH1}"},"panels/sensors/LocationsSettingsTab.ts | locale":{"message":"Locale"},"panels/sensors/LocationsSettingsTab.ts | localeMustContainAlphabetic":{"message":"Locale must contain alphabetic characters"},"panels/sensors/LocationsSettingsTab.ts | locationName":{"message":"Location name"},"panels/sensors/LocationsSettingsTab.ts | locationNameCannotBeEmpty":{"message":"Location name cannot be empty"},"panels/sensors/LocationsSettingsTab.ts | locationNameMustBeLessThanS":{"message":"Location name must be less than {PH1} characters"},"panels/sensors/LocationsSettingsTab.ts | long":{"message":"Long"},"panels/sensors/LocationsSettingsTab.ts | longitude":{"message":"Longitude"},"panels/sensors/LocationsSettingsTab.ts | longitudeMustBeANumber":{"message":"Longitude must be a number"},"panels/sensors/LocationsSettingsTab.ts | longitudeMustBeGreaterThanOr":{"message":"Longitude must be greater than or equal to {PH1}"},"panels/sensors/LocationsSettingsTab.ts | longitudeMustBeLessThanOrEqualTo":{"message":"Longitude must be less than or equal to {PH1}"},"panels/sensors/LocationsSettingsTab.ts | timezoneId":{"message":"Timezone ID"},"panels/sensors/LocationsSettingsTab.ts | timezoneIdMustContainAlphabetic":{"message":"Timezone ID must contain alphabetic characters"},"panels/sensors/sensors-meta.ts | accelerometer":{"message":"accelerometer"},"panels/sensors/sensors-meta.ts | devicebased":{"message":"Device-based"},"panels/sensors/sensors-meta.ts | deviceOrientation":{"message":"device orientation"},"panels/sensors/sensors-meta.ts | emulateIdleDetectorState":{"message":"Emulate Idle Detector state"},"panels/sensors/sensors-meta.ts | forceEnabled":{"message":"Force enabled"},"panels/sensors/sensors-meta.ts | geolocation":{"message":"geolocation"},"panels/sensors/sensors-meta.ts | locale":{"message":"locale"},"panels/sensors/sensors-meta.ts | locales":{"message":"locales"},"panels/sensors/sensors-meta.ts | locations":{"message":"Locations"},"panels/sensors/sensors-meta.ts | noIdleEmulation":{"message":"No idle emulation"},"panels/sensors/sensors-meta.ts | sensors":{"message":"Sensors"},"panels/sensors/sensors-meta.ts | showLocations":{"message":"Show Locations"},"panels/sensors/sensors-meta.ts | showSensors":{"message":"Show Sensors"},"panels/sensors/sensors-meta.ts | timezones":{"message":"timezones"},"panels/sensors/sensors-meta.ts | touch":{"message":"Touch"},"panels/sensors/sensors-meta.ts | userActiveScreenLocked":{"message":"User active, screen locked"},"panels/sensors/sensors-meta.ts | userActiveScreenUnlocked":{"message":"User active, screen unlocked"},"panels/sensors/sensors-meta.ts | userIdleScreenLocked":{"message":"User idle, screen locked"},"panels/sensors/sensors-meta.ts | userIdleScreenUnlocked":{"message":"User idle, screen unlocked"},"panels/sensors/SensorsView.ts | adjustWithMousewheelOrUpdownKeys":{"message":"Adjust with mousewheel or up/down keys. {PH1}: ±10, Shift: ±1, Alt: ±0.01"},"panels/sensors/SensorsView.ts | alpha":{"message":"α (alpha)"},"panels/sensors/SensorsView.ts | beta":{"message":"β (beta)"},"panels/sensors/SensorsView.ts | customOrientation":{"message":"Custom orientation"},"panels/sensors/SensorsView.ts | deviceOrientationSetToAlphaSBeta":{"message":"Device orientation set to alpha: {PH1}, beta: {PH2}, gamma: {PH3}"},"panels/sensors/SensorsView.ts | displayDown":{"message":"Display down"},"panels/sensors/SensorsView.ts | displayUp":{"message":"Display up"},"panels/sensors/SensorsView.ts | enableOrientationToRotate":{"message":"Enable orientation to rotate"},"panels/sensors/SensorsView.ts | error":{"message":"Error"},"panels/sensors/SensorsView.ts | forcesSelectedIdleStateEmulation":{"message":"Forces selected idle state emulation"},"panels/sensors/SensorsView.ts | forcesTouchInsteadOfClick":{"message":"Forces touch instead of click"},"panels/sensors/SensorsView.ts | gamma":{"message":"γ (gamma)"},"panels/sensors/SensorsView.ts | landscapeLeft":{"message":"Landscape left"},"panels/sensors/SensorsView.ts | landscapeRight":{"message":"Landscape right"},"panels/sensors/SensorsView.ts | latitude":{"message":"Latitude"},"panels/sensors/SensorsView.ts | locale":{"message":"Locale"},"panels/sensors/SensorsView.ts | location":{"message":"Location"},"panels/sensors/SensorsView.ts | locationUnavailable":{"message":"Location unavailable"},"panels/sensors/SensorsView.ts | longitude":{"message":"Longitude"},"panels/sensors/SensorsView.ts | manage":{"message":"Manage"},"panels/sensors/SensorsView.ts | manageTheListOfLocations":{"message":"Manage the list of locations"},"panels/sensors/SensorsView.ts | noOverride":{"message":"No override"},"panels/sensors/SensorsView.ts | off":{"message":"Off"},"panels/sensors/SensorsView.ts | orientation":{"message":"Orientation"},"panels/sensors/SensorsView.ts | other":{"message":"Other…"},"panels/sensors/SensorsView.ts | overrides":{"message":"Overrides"},"panels/sensors/SensorsView.ts | portrait":{"message":"Portrait"},"panels/sensors/SensorsView.ts | portraitUpsideDown":{"message":"Portrait upside down"},"panels/sensors/SensorsView.ts | presets":{"message":"Presets"},"panels/sensors/SensorsView.ts | reset":{"message":"Reset"},"panels/sensors/SensorsView.ts | resetDeviceOrientation":{"message":"Reset device orientation"},"panels/sensors/SensorsView.ts | shiftdragHorizontallyToRotate":{"message":"Shift+drag horizontally to rotate around the y-axis"},"panels/sensors/SensorsView.ts | timezoneId":{"message":"Timezone ID"},"panels/settings/components/SyncSection.ts | preferencesSyncDisabled":{"message":"To turn this setting on, you must first enable settings sync in Chrome."},"panels/settings/components/SyncSection.ts | settings":{"message":"Go to Settings"},"panels/settings/components/SyncSection.ts | signedIn":{"message":"Signed into Chrome as:"},"panels/settings/components/SyncSection.ts | syncDisabled":{"message":"To turn this setting on, you must enable Chrome sync."},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | addBrand":{"message":"Add Brand"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | addedBrand":{"message":"Added brand row"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | architecture":{"message":"Architecture"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | architecturePlaceholder":{"message":"Architecture (e.g. x86)"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | brandDeleteAriaLabel":{"message":"Delete {PH1}"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | brandName":{"message":"Brand"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | brandNameAriaLabel":{"message":"Brand {PH1}"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | brandProperties":{"message":"Brand properties"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | brands":{"message":"Brands"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | brandVersionAriaLabel":{"message":"Version {PH1}"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | deletedBrand":{"message":"Deleted brand row"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | deleteTooltip":{"message":"Delete"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | deviceModel":{"message":"Device model"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | deviceProperties":{"message":"Device properties"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | fullBrowserVersion":{"message":"Full browser version"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | fullBrowserVersionPlaceholder":{"message":"Full browser version (e.g. 87.0.4280.88)"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | mobileCheckboxLabel":{"message":"Mobile"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | notRepresentable":{"message":"Not representable as structured headers string."},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | platformLabel":{"message":"Platform"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | platformPlaceholder":{"message":"Platform (e.g. Android)"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | platformProperties":{"message":"Platform properties"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | platformVersion":{"message":"Platform version"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | title":{"message":"User agent client hints"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | update":{"message":"Update"},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | userAgentClientHintsInfo":{"message":"User agent client hints are an alternative to the user agent string that identify the browser and the device in a more structured way with better privacy accounting. Click the button to learn more."},"panels/settings/emulation/components/UserAgentClientHintsForm.ts | version":{"message":"Version"},"panels/settings/emulation/DevicesSettingsTab.ts | addCustomDevice":{"message":"Add custom device..."},"panels/settings/emulation/DevicesSettingsTab.ts | device":{"message":"Device"},"panels/settings/emulation/DevicesSettingsTab.ts | deviceAddedOrUpdated":{"message":"Device {PH1} successfully added/updated."},"panels/settings/emulation/DevicesSettingsTab.ts | deviceName":{"message":"Device Name"},"panels/settings/emulation/DevicesSettingsTab.ts | deviceNameCannotBeEmpty":{"message":"Device name cannot be empty."},"panels/settings/emulation/DevicesSettingsTab.ts | deviceNameMustBeLessThanS":{"message":"Device name must be less than {PH1} characters."},"panels/settings/emulation/DevicesSettingsTab.ts | devicePixelRatio":{"message":"Device pixel ratio"},"panels/settings/emulation/DevicesSettingsTab.ts | emulatedDevices":{"message":"Emulated Devices"},"panels/settings/emulation/DevicesSettingsTab.ts | height":{"message":"Height"},"panels/settings/emulation/DevicesSettingsTab.ts | userAgentString":{"message":"User agent string"},"panels/settings/emulation/DevicesSettingsTab.ts | userAgentType":{"message":"User agent type"},"panels/settings/emulation/DevicesSettingsTab.ts | width":{"message":"Width"},"panels/settings/emulation/emulation-meta.ts | devices":{"message":"Devices"},"panels/settings/emulation/emulation-meta.ts | showDevices":{"message":"Show Devices"},"panels/settings/FrameworkIgnoreListSettingsTab.ts | addFilenamePattern":{"message":"Add filename pattern"},"panels/settings/FrameworkIgnoreListSettingsTab.ts | addPattern":{"message":"Add pattern..."},"panels/settings/FrameworkIgnoreListSettingsTab.ts | behavior":{"message":"Behavior"},"panels/settings/FrameworkIgnoreListSettingsTab.ts | debuggerWillSkipThroughThe":{"message":"Debugger will skip through the scripts and will not stop on exceptions thrown by them."},"panels/settings/FrameworkIgnoreListSettingsTab.ts | disabled":{"message":"Disabled"},"panels/settings/FrameworkIgnoreListSettingsTab.ts | frameworkIgnoreList":{"message":"Framework Ignore List"},"panels/settings/FrameworkIgnoreListSettingsTab.ts | ignoreList":{"message":"Ignore List"},"panels/settings/FrameworkIgnoreListSettingsTab.ts | ignoreListContentScripts":{"message":"Add content scripts to ignore list"},"panels/settings/FrameworkIgnoreListSettingsTab.ts | ignoreListContentScriptsExtension":{"message":"Add content scripts to ignore list (extension scripts in the page)"},"panels/settings/FrameworkIgnoreListSettingsTab.ts | ignoreScriptsWhoseNamesMatchS":{"message":"Ignore scripts whose names match \'\'{PH1}\'\'"},"panels/settings/FrameworkIgnoreListSettingsTab.ts | noIgnoreListPatterns":{"message":"No ignore list patterns"},"panels/settings/FrameworkIgnoreListSettingsTab.ts | pattern":{"message":"Pattern"},"panels/settings/FrameworkIgnoreListSettingsTab.ts | patternAlreadyExists":{"message":"Pattern already exists"},"panels/settings/FrameworkIgnoreListSettingsTab.ts | patternCannotBeEmpty":{"message":"Pattern cannot be empty"},"panels/settings/FrameworkIgnoreListSettingsTab.ts | patternMustBeAValidRegular":{"message":"Pattern must be a valid regular expression"},"panels/settings/KeybindsSettingsTab.ts | addAShortcut":{"message":"Add a shortcut"},"panels/settings/KeybindsSettingsTab.ts | confirmChanges":{"message":"Confirm changes"},"panels/settings/KeybindsSettingsTab.ts | discardChanges":{"message":"Discard changes"},"panels/settings/KeybindsSettingsTab.ts | editShortcut":{"message":"Edit shortcut"},"panels/settings/KeybindsSettingsTab.ts | FullListOfDevtoolsKeyboard":{"message":"Full list of DevTools keyboard shortcuts and gestures"},"panels/settings/KeybindsSettingsTab.ts | keyboardShortcutsList":{"message":"Keyboard shortcuts list"},"panels/settings/KeybindsSettingsTab.ts | matchShortcutsFromPreset":{"message":"Match shortcuts from preset"},"panels/settings/KeybindsSettingsTab.ts | noShortcutForAction":{"message":"No shortcut for action"},"panels/settings/KeybindsSettingsTab.ts | removeShortcut":{"message":"Remove shortcut"},"panels/settings/KeybindsSettingsTab.ts | ResetShortcutsForAction":{"message":"Reset shortcuts for action"},"panels/settings/KeybindsSettingsTab.ts | RestoreDefaultShortcuts":{"message":"Restore default shortcuts"},"panels/settings/KeybindsSettingsTab.ts | shortcutModified":{"message":"Shortcut modified"},"panels/settings/KeybindsSettingsTab.ts | shortcuts":{"message":"Shortcuts"},"panels/settings/KeybindsSettingsTab.ts | shortcutsCannotContainOnly":{"message":"Shortcuts cannot contain only modifier keys."},"panels/settings/KeybindsSettingsTab.ts | thisShortcutIsInUseByS":{"message":"This shortcut is in use by {PH1}: {PH2}."},"panels/settings/settings-meta.ts | documentation":{"message":"Documentation"},"panels/settings/settings-meta.ts | experiments":{"message":"Experiments"},"panels/settings/settings-meta.ts | ignoreList":{"message":"Ignore List"},"panels/settings/settings-meta.ts | preferences":{"message":"Preferences"},"panels/settings/settings-meta.ts | settings":{"message":"Settings"},"panels/settings/settings-meta.ts | shortcuts":{"message":"Shortcuts"},"panels/settings/settings-meta.ts | showExperiments":{"message":"Show Experiments"},"panels/settings/settings-meta.ts | showIgnoreList":{"message":"Show Ignore List"},"panels/settings/settings-meta.ts | showPreferences":{"message":"Show Preferences"},"panels/settings/settings-meta.ts | showShortcuts":{"message":"Show Shortcuts"},"panels/settings/SettingsScreen.ts | experiments":{"message":"Experiments"},"panels/settings/SettingsScreen.ts | filterExperimentsLabel":{"message":"Filter"},"panels/settings/SettingsScreen.ts | learnMore":{"message":"Learn more"},"panels/settings/SettingsScreen.ts | noResults":{"message":"No experiments match the filter"},"panels/settings/SettingsScreen.ts | oneOrMoreSettingsHaveChanged":{"message":"One or more settings have changed which requires a reload to take effect."},"panels/settings/SettingsScreen.ts | preferences":{"message":"Preferences"},"panels/settings/SettingsScreen.ts | restoreDefaultsAndReload":{"message":"Restore defaults and reload"},"panels/settings/SettingsScreen.ts | settings":{"message":"Settings"},"panels/settings/SettingsScreen.ts | shortcuts":{"message":"Shortcuts"},"panels/settings/SettingsScreen.ts | theseExperimentsAreParticularly":{"message":"These experiments are particularly unstable. Enable at your own risk."},"panels/settings/SettingsScreen.ts | theseExperimentsCouldBeUnstable":{"message":"These experiments could be unstable or unreliable and may require you to restart DevTools."},"panels/settings/SettingsScreen.ts | warning":{"message":"WARNING:"},"panels/snippets/ScriptSnippetFileSystem.ts | linkedTo":{"message":"Linked to {PH1}"},"panels/snippets/ScriptSnippetFileSystem.ts | scriptSnippet":{"message":"Script snippet #{PH1}"},"panels/snippets/SnippetsQuickOpen.ts | noSnippetsFound":{"message":"No snippets found."},"panels/snippets/SnippetsQuickOpen.ts | run":{"message":"Run"},"panels/snippets/SnippetsQuickOpen.ts | snippet":{"message":"Snippet"},"panels/sources/AddSourceMapURLDialog.ts | add":{"message":"Add"},"panels/sources/AddSourceMapURLDialog.ts | sourceMapUrl":{"message":"Source map URL: "},"panels/sources/BreakpointEditDialog.ts | breakpoint":{"message":"Breakpoint"},"panels/sources/BreakpointEditDialog.ts | breakpointType":{"message":"Breakpoint type"},"panels/sources/BreakpointEditDialog.ts | conditionalBreakpoint":{"message":"Conditional breakpoint"},"panels/sources/BreakpointEditDialog.ts | expressionToCheckBeforePausingEg":{"message":"Expression to check before pausing, e.g. x > 5"},"panels/sources/BreakpointEditDialog.ts | logAMessageToConsoleDoNotBreak":{"message":"Log a message to Console, do not break"},"panels/sources/BreakpointEditDialog.ts | logMessageEgXIsX":{"message":"Log message, e.g. \'x is\', x"},"panels/sources/BreakpointEditDialog.ts | logpoint":{"message":"Logpoint"},"panels/sources/BreakpointEditDialog.ts | pauseOnlyWhenTheConditionIsTrue":{"message":"Pause only when the condition is true"},"panels/sources/CallStackSidebarPane.ts | addAllContentScriptsToIgnoreList":{"message":"Add all content scripts to ignore list"},"panels/sources/CallStackSidebarPane.ts | addScriptToIgnoreList":{"message":"Add script to ignore list"},"panels/sources/CallStackSidebarPane.ts | callFrameWarnings":{"message":"Some call frames have warnings"},"panels/sources/CallStackSidebarPane.ts | callStack":{"message":"Call Stack"},"panels/sources/CallStackSidebarPane.ts | copyStackTrace":{"message":"Copy stack trace"},"panels/sources/CallStackSidebarPane.ts | notPaused":{"message":"Not paused"},"panels/sources/CallStackSidebarPane.ts | onIgnoreList":{"message":"on ignore list"},"panels/sources/CallStackSidebarPane.ts | removeAllContentScriptsFrom":{"message":"Remove all content scripts from ignore list"},"panels/sources/CallStackSidebarPane.ts | removeFromIgnoreList":{"message":"Remove from ignore list"},"panels/sources/CallStackSidebarPane.ts | showIgnorelistedFrames":{"message":"Show ignore-listed frames"},"panels/sources/CallStackSidebarPane.ts | showMore":{"message":"Show more"},"panels/sources/components/HeadersView.ts | addHeader":{"message":"Add a header"},"panels/sources/components/HeadersView.ts | addHeaderOverride":{"message":"Add header override"},"panels/sources/components/HeadersView.ts | errorWhenParsing":{"message":"Error when parsing \'\'{PH1}\'\'."},"panels/sources/components/HeadersView.ts | parsingErrorExplainer":{"message":"This is most likely due to a syntax error in \'\'{PH1}\'\'. Try opening this file in an external editor to fix the error or delete the file and re-create the override."},"panels/sources/components/HeadersView.ts | removeBlock":{"message":"Remove this \'ApplyTo\'-section"},"panels/sources/components/HeadersView.ts | removeHeader":{"message":"Remove this header"},"panels/sources/CoveragePlugin.ts | clickToShowCoveragePanel":{"message":"Click to show Coverage Panel"},"panels/sources/CoveragePlugin.ts | coverageNa":{"message":"Coverage: n/a"},"panels/sources/CoveragePlugin.ts | coverageS":{"message":"Coverage: {PH1}"},"panels/sources/CoveragePlugin.ts | showDetails":{"message":"Show Details"},"panels/sources/CSSPlugin.ts | openColorPicker":{"message":"Open color picker."},"panels/sources/CSSPlugin.ts | openCubicBezierEditor":{"message":"Open cubic bezier editor."},"panels/sources/DebuggerPausedMessage.ts | attributeModifications":{"message":"attribute modifications"},"panels/sources/DebuggerPausedMessage.ts | childSAdded":{"message":"Child {PH1} added"},"panels/sources/DebuggerPausedMessage.ts | debuggerPaused":{"message":"Debugger paused"},"panels/sources/DebuggerPausedMessage.ts | descendantSAdded":{"message":"Descendant {PH1} added"},"panels/sources/DebuggerPausedMessage.ts | descendantSRemoved":{"message":"Descendant {PH1} removed"},"panels/sources/DebuggerPausedMessage.ts | nodeRemoval":{"message":"node removal"},"panels/sources/DebuggerPausedMessage.ts | pausedBeforePotentialOutofmemory":{"message":"Paused before potential out-of-memory crash"},"panels/sources/DebuggerPausedMessage.ts | pausedOnAssertion":{"message":"Paused on assertion"},"panels/sources/DebuggerPausedMessage.ts | pausedOnBreakpoint":{"message":"Paused on breakpoint"},"panels/sources/DebuggerPausedMessage.ts | pausedOnCspViolation":{"message":"Paused on CSP violation"},"panels/sources/DebuggerPausedMessage.ts | pausedOnDebuggedFunction":{"message":"Paused on debugged function"},"panels/sources/DebuggerPausedMessage.ts | pausedOnEventListener":{"message":"Paused on event listener"},"panels/sources/DebuggerPausedMessage.ts | pausedOnException":{"message":"Paused on exception"},"panels/sources/DebuggerPausedMessage.ts | pausedOnPromiseRejection":{"message":"Paused on promise rejection"},"panels/sources/DebuggerPausedMessage.ts | pausedOnS":{"message":"Paused on {PH1}"},"panels/sources/DebuggerPausedMessage.ts | pausedOnXhrOrFetch":{"message":"Paused on XHR or fetch"},"panels/sources/DebuggerPausedMessage.ts | subtreeModifications":{"message":"subtree modifications"},"panels/sources/DebuggerPausedMessage.ts | trustedTypePolicyViolation":{"message":"Trusted Type Policy Violation"},"panels/sources/DebuggerPausedMessage.ts | trustedTypeSinkViolation":{"message":"Trusted Type Sink Violation"},"panels/sources/DebuggerPlugin.ts | addBreakpoint":{"message":"Add breakpoint"},"panels/sources/DebuggerPlugin.ts | addConditionalBreakpoint":{"message":"Add conditional breakpoint…"},"panels/sources/DebuggerPlugin.ts | addLogpoint":{"message":"Add logpoint…"},"panels/sources/DebuggerPlugin.ts | addSourceMap":{"message":"Add source map…"},"panels/sources/DebuggerPlugin.ts | associatedFilesAreAvailable":{"message":"Associated files are available via file tree or {PH1}."},"panels/sources/DebuggerPlugin.ts | associatedFilesShouldBeAdded":{"message":"Associated files should be added to the file tree. You can debug these resolved source files as regular JavaScript files."},"panels/sources/DebuggerPlugin.ts | configure":{"message":"Configure"},"panels/sources/DebuggerPlugin.ts | disableBreakpoint":{"message":"{n, plural, =1 {Disable breakpoint} other {Disable all breakpoints in line}}"},"panels/sources/DebuggerPlugin.ts | editBreakpoint":{"message":"Edit breakpoint…"},"panels/sources/DebuggerPlugin.ts | enableBreakpoint":{"message":"{n, plural, =1 {Enable breakpoint} other {Enable all breakpoints in line}}"},"panels/sources/DebuggerPlugin.ts | neverPauseHere":{"message":"Never pause here"},"panels/sources/DebuggerPlugin.ts | prettyprint":{"message":"Pretty-print"},"panels/sources/DebuggerPlugin.ts | prettyprintingWillFormatThisFile":{"message":"Pretty-printing will format this file in a new tab where you can continue debugging. You can also pretty-print this file by clicking the {PH1} button on the bottom status bar."},"panels/sources/DebuggerPlugin.ts | prettyprintThisMinifiedFile":{"message":"Pretty-print this minified file?"},"panels/sources/DebuggerPlugin.ts | removeBreakpoint":{"message":"{n, plural, =1 {Remove breakpoint} other {Remove all breakpoints in line}}"},"panels/sources/DebuggerPlugin.ts | removeFromIgnoreList":{"message":"Remove from ignore list"},"panels/sources/DebuggerPlugin.ts | sourceMapDetected":{"message":"Source map detected."},"panels/sources/DebuggerPlugin.ts | sourceMapFoundButIgnoredForFile":{"message":"Source map found, but ignored for file on ignore list."},"panels/sources/DebuggerPlugin.ts | theDebuggerWillSkipStepping":{"message":"The debugger will skip stepping through this script, and will not stop on exceptions."},"panels/sources/DebuggerPlugin.ts | thisScriptIsOnTheDebuggersIgnore":{"message":"This script is on the debugger\'s ignore list"},"panels/sources/FilteredUISourceCodeListProvider.ts | noFilesFound":{"message":"No files found"},"panels/sources/GoToLineQuickOpen.ts | currentLineSTypeALineNumber":{"message":"Current line: {PH1}. Type a line number between 1 and {PH2} to navigate to."},"panels/sources/GoToLineQuickOpen.ts | currentPositionXsTypeAnOffset":{"message":"Current position: 0x{PH1}. Type an offset between 0x{PH2} and 0x{PH3} to navigate to."},"panels/sources/GoToLineQuickOpen.ts | goToLineS":{"message":"Go to line {PH1}."},"panels/sources/GoToLineQuickOpen.ts | goToLineSAndColumnS":{"message":"Go to line {PH1} and column {PH2}."},"panels/sources/GoToLineQuickOpen.ts | goToOffsetXs":{"message":"Go to offset 0x{PH1}."},"panels/sources/GoToLineQuickOpen.ts | noFileSelected":{"message":"No file selected."},"panels/sources/GoToLineQuickOpen.ts | noResultsFound":{"message":"No results found"},"panels/sources/GoToLineQuickOpen.ts | typeANumberToGoToThatLine":{"message":"Type a number to go to that line."},"panels/sources/InplaceFormatterEditorAction.ts | format":{"message":"Format"},"panels/sources/InplaceFormatterEditorAction.ts | formatS":{"message":"Format {PH1}"},"panels/sources/JavaScriptBreakpointsSidebarPane.ts | activateBreakpoints":{"message":"Activate breakpoints"},"panels/sources/JavaScriptBreakpointsSidebarPane.ts | checked":{"message":"checked"},"panels/sources/JavaScriptBreakpointsSidebarPane.ts | deactivateBreakpoints":{"message":"Deactivate breakpoints"},"panels/sources/JavaScriptBreakpointsSidebarPane.ts | disableAllBreakpoints":{"message":"Disable all breakpoints"},"panels/sources/JavaScriptBreakpointsSidebarPane.ts | disableBreakpointsInFile":{"message":"Disable breakpoints in file"},"panels/sources/JavaScriptBreakpointsSidebarPane.ts | enableAllBreakpoints":{"message":"Enable all breakpoints"},"panels/sources/JavaScriptBreakpointsSidebarPane.ts | enableBreakpointsInFile":{"message":"Enable breakpoints in file"},"panels/sources/JavaScriptBreakpointsSidebarPane.ts | mixed":{"message":"mixed"},"panels/sources/JavaScriptBreakpointsSidebarPane.ts | noBreakpoints":{"message":"No breakpoints"},"panels/sources/JavaScriptBreakpointsSidebarPane.ts | removeAllBreakpoints":{"message":"Remove all breakpoints"},"panels/sources/JavaScriptBreakpointsSidebarPane.ts | removeAllBreakpointsInLine":{"message":"Remove all breakpoints in line"},"panels/sources/JavaScriptBreakpointsSidebarPane.ts | removeBreakpoint":{"message":"Remove breakpoint"},"panels/sources/JavaScriptBreakpointsSidebarPane.ts | removeOtherBreakpoints":{"message":"Remove other breakpoints"},"panels/sources/JavaScriptBreakpointsSidebarPane.ts | revealLocation":{"message":"Reveal location"},"panels/sources/JavaScriptBreakpointsSidebarPane.ts | sBreakpointHit":{"message":"{PH1} breakpoint hit"},"panels/sources/JavaScriptBreakpointsSidebarPane.ts | unchecked":{"message":"unchecked"},"panels/sources/NavigatorView.ts | areYouSureYouWantToDeleteAll":{"message":"Are you sure you want to delete all overrides contained in this folder?"},"panels/sources/NavigatorView.ts | areYouSureYouWantToDeleteThis":{"message":"Are you sure you want to delete this file?"},"panels/sources/NavigatorView.ts | areYouSureYouWantToExcludeThis":{"message":"Are you sure you want to exclude this folder?"},"panels/sources/NavigatorView.ts | areYouSureYouWantToRemoveThis":{"message":"Are you sure you want to remove this folder?"},"panels/sources/NavigatorView.ts | delete":{"message":"Delete"},"panels/sources/NavigatorView.ts | deleteAllOverrides":{"message":"Delete all overrides"},"panels/sources/NavigatorView.ts | excludeFolder":{"message":"Exclude folder"},"panels/sources/NavigatorView.ts | makeACopy":{"message":"Make a copy…"},"panels/sources/NavigatorView.ts | newFile":{"message":"New file"},"panels/sources/NavigatorView.ts | noDomain":{"message":"(no domain)"},"panels/sources/NavigatorView.ts | openFolder":{"message":"Open folder"},"panels/sources/NavigatorView.ts | removeFolderFromWorkspace":{"message":"Remove folder from workspace"},"panels/sources/NavigatorView.ts | rename":{"message":"Rename…"},"panels/sources/NavigatorView.ts | searchInAllFiles":{"message":"Search in all files"},"panels/sources/NavigatorView.ts | searchInFolder":{"message":"Search in folder"},"panels/sources/NavigatorView.ts | sFromSourceMap":{"message":"{PH1} (from source map)"},"panels/sources/OutlineQuickOpen.ts | noFileSelected":{"message":"No file selected."},"panels/sources/OutlineQuickOpen.ts | noResultsFound":{"message":"No results found"},"panels/sources/OutlineQuickOpen.ts | openAJavascriptOrCssFileToSee":{"message":"Open a JavaScript or CSS file to see symbols"},"panels/sources/ProfilePlugin.ts | kb":{"message":"kB"},"panels/sources/ProfilePlugin.ts | mb":{"message":"MB"},"panels/sources/ProfilePlugin.ts | ms":{"message":"ms"},"panels/sources/ScopeChainSidebarPane.ts | closure":{"message":"Closure"},"panels/sources/ScopeChainSidebarPane.ts | closureS":{"message":"Closure ({PH1})"},"panels/sources/ScopeChainSidebarPane.ts | couldNotOpenLinearMemory":{"message":"Could not open linear memory inspector: failed locating buffer."},"panels/sources/ScopeChainSidebarPane.ts | exception":{"message":"Exception"},"panels/sources/ScopeChainSidebarPane.ts | loading":{"message":"Loading..."},"panels/sources/ScopeChainSidebarPane.ts | notPaused":{"message":"Not paused"},"panels/sources/ScopeChainSidebarPane.ts | noVariables":{"message":"No variables"},"panels/sources/ScopeChainSidebarPane.ts | returnValue":{"message":"Return value"},"panels/sources/ScopeChainSidebarPane.ts | revealInMemoryInspectorPanel":{"message":"Reveal in Memory Inspector panel"},"panels/sources/ScriptFormatterEditorAction.ts | prettyPrint":{"message":"Pretty print"},"panels/sources/ScriptFormatterEditorAction.ts | prettyPrintS":{"message":"Pretty print {PH1}"},"panels/sources/ScriptOriginPlugin.ts | providedViaDebugInfoByS":{"message":"(provided via debug info by {PH1})"},"panels/sources/ScriptOriginPlugin.ts | sourceMappedFromS":{"message":"(source mapped from {PH1})"},"panels/sources/SnippetsPlugin.ts | ctrlenter":{"message":"Ctrl+Enter"},"panels/sources/SnippetsPlugin.ts | enter":{"message":"⌘+Enter"},"panels/sources/sources-meta.ts | activateBreakpoints":{"message":"Activate breakpoints"},"panels/sources/sources-meta.ts | addFolderToWorkspace":{"message":"Add folder to workspace"},"panels/sources/sources-meta.ts | addSelectedTextToWatches":{"message":"Add selected text to watches"},"panels/sources/sources-meta.ts | all":{"message":"All"},"panels/sources/sources-meta.ts | allowScrollingPastEndOfFile":{"message":"Allow scrolling past end of file"},"panels/sources/sources-meta.ts | autocompletion":{"message":"Autocompletion"},"panels/sources/sources-meta.ts | automaticallyRevealFilesIn":{"message":"Automatically reveal files in sidebar"},"panels/sources/sources-meta.ts | bracketMatching":{"message":"Bracket matching"},"panels/sources/sources-meta.ts | breakpoints":{"message":"Breakpoints"},"panels/sources/sources-meta.ts | closeAll":{"message":"Close All"},"panels/sources/sources-meta.ts | closeTheActiveTab":{"message":"Close the active tab"},"panels/sources/sources-meta.ts | codeFolding":{"message":"Code folding"},"panels/sources/sources-meta.ts | createNewSnippet":{"message":"Create new snippet"},"panels/sources/sources-meta.ts | deactivateBreakpoints":{"message":"Deactivate breakpoints"},"panels/sources/sources-meta.ts | decrementCssUnitBy":{"message":"Decrement CSS unit by {PH1}"},"panels/sources/sources-meta.ts | detectIndentation":{"message":"Detect indentation"},"panels/sources/sources-meta.ts | disableAutocompletion":{"message":"Disable autocompletion"},"panels/sources/sources-meta.ts | disableAutoFocusOnDebuggerPaused":{"message":"Do not focus Sources panel when triggering a breakpoint"},"panels/sources/sources-meta.ts | disableBracketMatching":{"message":"Disable bracket matching"},"panels/sources/sources-meta.ts | disableCodeFolding":{"message":"Disable code folding"},"panels/sources/sources-meta.ts | disableCssSourceMaps":{"message":"Disable CSS source maps"},"panels/sources/sources-meta.ts | disableJavascriptSourceMaps":{"message":"Disable JavaScript source maps"},"panels/sources/sources-meta.ts | disableTabMovesFocus":{"message":"Disable tab moves focus"},"panels/sources/sources-meta.ts | disallowScrollingPastEndOfFile":{"message":"Disallow scrolling past end of file"},"panels/sources/sources-meta.ts | displayVariableValuesInlineWhile":{"message":"Display variable values inline while debugging"},"panels/sources/sources-meta.ts | doNotAutomaticallyRevealFilesIn":{"message":"Do not automatically reveal files in sidebar"},"panels/sources/sources-meta.ts | doNotDetectIndentation":{"message":"Do not detect indentation"},"panels/sources/sources-meta.ts | doNotDisplayVariableValuesInline":{"message":"Do not display variable values inline while debugging"},"panels/sources/sources-meta.ts | doNotSearchInAnonymousAndContent":{"message":"Do not search in anonymous and content scripts"},"panels/sources/sources-meta.ts | doNotShowWhitespaceCharacters":{"message":"Do not show whitespace characters"},"panels/sources/sources-meta.ts | enableAutocompletion":{"message":"Enable autocompletion"},"panels/sources/sources-meta.ts | enableAutoFocusOnDebuggerPaused":{"message":"Focus Sources panel when triggering a breakpoint"},"panels/sources/sources-meta.ts | enableBracketMatching":{"message":"Enable bracket matching"},"panels/sources/sources-meta.ts | enableCodeFolding":{"message":"Enable code folding"},"panels/sources/sources-meta.ts | enableCssSourceMaps":{"message":"Enable CSS source maps"},"panels/sources/sources-meta.ts | enableJavascriptSourceMaps":{"message":"Enable JavaScript source maps"},"panels/sources/sources-meta.ts | enableTabMovesFocus":{"message":"Enable tab moves focus"},"panels/sources/sources-meta.ts | evaluateSelectedTextInConsole":{"message":"Evaluate selected text in console"},"panels/sources/sources-meta.ts | file":{"message":"File"},"panels/sources/sources-meta.ts | filesystem":{"message":"Filesystem"},"panels/sources/sources-meta.ts | goTo":{"message":"Go to"},"panels/sources/sources-meta.ts | goToAFunctionDeclarationruleSet":{"message":"Go to a function declaration/rule set"},"panels/sources/sources-meta.ts | goToLine":{"message":"Go to line"},"panels/sources/sources-meta.ts | incrementCssUnitBy":{"message":"Increment CSS unit by {PH1}"},"panels/sources/sources-meta.ts | jumpToNextEditingLocation":{"message":"Jump to next editing location"},"panels/sources/sources-meta.ts | jumpToPreviousEditingLocation":{"message":"Jump to previous editing location"},"panels/sources/sources-meta.ts | line":{"message":"Line"},"panels/sources/sources-meta.ts | nextCallFrame":{"message":"Next call frame"},"panels/sources/sources-meta.ts | none":{"message":"None"},"panels/sources/sources-meta.ts | open":{"message":"Open"},"panels/sources/sources-meta.ts | pauseScriptExecution":{"message":"Pause script execution"},"panels/sources/sources-meta.ts | previousCallFrame":{"message":"Previous call frame"},"panels/sources/sources-meta.ts | quickSource":{"message":"Quick source"},"panels/sources/sources-meta.ts | rename":{"message":"Rename"},"panels/sources/sources-meta.ts | resumeScriptExecution":{"message":"Resume script execution"},"panels/sources/sources-meta.ts | runSnippet":{"message":"Run snippet"},"panels/sources/sources-meta.ts | save":{"message":"Save"},"panels/sources/sources-meta.ts | saveAll":{"message":"Save all"},"panels/sources/sources-meta.ts | scope":{"message":"Scope"},"panels/sources/sources-meta.ts | search":{"message":"Search"},"panels/sources/sources-meta.ts | searchInAnonymousAndContent":{"message":"Search in anonymous and content scripts"},"panels/sources/sources-meta.ts | showAllWhitespaceCharacters":{"message":"Show all whitespace characters"},"panels/sources/sources-meta.ts | showBreakpoints":{"message":"Show Breakpoints"},"panels/sources/sources-meta.ts | showFilesystem":{"message":"Show Filesystem"},"panels/sources/sources-meta.ts | showQuickSource":{"message":"Show Quick source"},"panels/sources/sources-meta.ts | showScope":{"message":"Show Scope"},"panels/sources/sources-meta.ts | showSearch":{"message":"Show Search"},"panels/sources/sources-meta.ts | showSnippets":{"message":"Show Snippets"},"panels/sources/sources-meta.ts | showSources":{"message":"Show Sources"},"panels/sources/sources-meta.ts | showThreads":{"message":"Show Threads"},"panels/sources/sources-meta.ts | showTrailingWhitespaceCharacters":{"message":"Show trailing whitespace characters"},"panels/sources/sources-meta.ts | showWatch":{"message":"Show Watch"},"panels/sources/sources-meta.ts | showWhitespaceCharacters":{"message":"Show whitespace characters:"},"panels/sources/sources-meta.ts | snippets":{"message":"Snippets"},"panels/sources/sources-meta.ts | sources":{"message":"Sources"},"panels/sources/sources-meta.ts | step":{"message":"Step"},"panels/sources/sources-meta.ts | stepIntoNextFunctionCall":{"message":"Step into next function call"},"panels/sources/sources-meta.ts | stepOutOfCurrentFunction":{"message":"Step out of current function"},"panels/sources/sources-meta.ts | stepOverNextFunctionCall":{"message":"Step over next function call"},"panels/sources/sources-meta.ts | switchFile":{"message":"Switch file"},"panels/sources/sources-meta.ts | symbol":{"message":"Symbol"},"panels/sources/sources-meta.ts | threads":{"message":"Threads"},"panels/sources/sources-meta.ts | toggleBreakpoint":{"message":"Toggle breakpoint"},"panels/sources/sources-meta.ts | toggleBreakpointEnabled":{"message":"Toggle breakpoint enabled"},"panels/sources/sources-meta.ts | toggleBreakpointInputWindow":{"message":"Toggle breakpoint input window"},"panels/sources/sources-meta.ts | trailing":{"message":"Trailing"},"panels/sources/sources-meta.ts | watch":{"message":"Watch"},"panels/sources/SourcesNavigator.ts | clearConfiguration":{"message":"Clear configuration"},"panels/sources/SourcesNavigator.ts | contentScriptsServedByExtensions":{"message":"Content scripts served by extensions appear here"},"panels/sources/SourcesNavigator.ts | createAndSaveCodeSnippetsFor":{"message":"Create and save code snippets for later reuse"},"panels/sources/SourcesNavigator.ts | createNewSnippet":{"message":"Create new snippet"},"panels/sources/SourcesNavigator.ts | learnMore":{"message":"Learn more"},"panels/sources/SourcesNavigator.ts | learnMoreAboutWorkspaces":{"message":"Learn more about Workspaces"},"panels/sources/SourcesNavigator.ts | newSnippet":{"message":"New snippet"},"panels/sources/SourcesNavigator.ts | overridePageAssetsWithFilesFromA":{"message":"Override page assets with files from a local folder"},"panels/sources/SourcesNavigator.ts | remove":{"message":"Remove"},"panels/sources/SourcesNavigator.ts | rename":{"message":"Rename…"},"panels/sources/SourcesNavigator.ts | run":{"message":"Run"},"panels/sources/SourcesNavigator.ts | saveAs":{"message":"Save as..."},"panels/sources/SourcesNavigator.ts | selectFolderForOverrides":{"message":"Select folder for overrides"},"panels/sources/SourcesNavigator.ts | syncChangesInDevtoolsWithThe":{"message":"Sync changes in DevTools with the local filesystem"},"panels/sources/SourcesPanel.ts | continueToHere":{"message":"Continue to here"},"panels/sources/SourcesPanel.ts | copyS":{"message":"Copy {PH1}"},"panels/sources/SourcesPanel.ts | copyStringAsJSLiteral":{"message":"Copy string as JavaScript literal"},"panels/sources/SourcesPanel.ts | copyStringAsJSONLiteral":{"message":"Copy string as JSON literal"},"panels/sources/SourcesPanel.ts | copyStringContents":{"message":"Copy string contents"},"panels/sources/SourcesPanel.ts | debuggerHidden":{"message":"Debugger sidebar hidden"},"panels/sources/SourcesPanel.ts | debuggerShown":{"message":"Debugger sidebar shown"},"panels/sources/SourcesPanel.ts | dontPauseOnExceptions":{"message":"Don\'t pause on exceptions"},"panels/sources/SourcesPanel.ts | dropWorkspaceFolderHere":{"message":"Drop workspace folder here"},"panels/sources/SourcesPanel.ts | groupByFolder":{"message":"Group by folder"},"panels/sources/SourcesPanel.ts | hideDebugger":{"message":"Hide debugger"},"panels/sources/SourcesPanel.ts | hideNavigator":{"message":"Hide navigator"},"panels/sources/SourcesPanel.ts | moreOptions":{"message":"More options"},"panels/sources/SourcesPanel.ts | navigatorHidden":{"message":"Navigator sidebar hidden"},"panels/sources/SourcesPanel.ts | navigatorShown":{"message":"Navigator sidebar shown"},"panels/sources/SourcesPanel.ts | openInSourcesPanel":{"message":"Open in Sources panel"},"panels/sources/SourcesPanel.ts | pauseOnCaughtExceptions":{"message":"Pause on caught exceptions"},"panels/sources/SourcesPanel.ts | pauseOnExceptions":{"message":"Pause on exceptions"},"panels/sources/SourcesPanel.ts | resumeWithAllPausesBlockedForMs":{"message":"Resume with all pauses blocked for 500 ms"},"panels/sources/SourcesPanel.ts | revealInSidebar":{"message":"Reveal in sidebar"},"panels/sources/SourcesPanel.ts | showDebugger":{"message":"Show debugger"},"panels/sources/SourcesPanel.ts | showFunctionDefinition":{"message":"Show function definition"},"panels/sources/SourcesPanel.ts | showNavigator":{"message":"Show navigator"},"panels/sources/SourcesPanel.ts | storeSAsGlobalVariable":{"message":"Store {PH1} as global variable"},"panels/sources/SourcesPanel.ts | terminateCurrentJavascriptCall":{"message":"Terminate current JavaScript call"},"panels/sources/SourcesView.ts | dropInAFolderToAddToWorkspace":{"message":"Drop in a folder to add to workspace"},"panels/sources/SourcesView.ts | openFile":{"message":"Open file"},"panels/sources/SourcesView.ts | runCommand":{"message":"Run command"},"panels/sources/SourcesView.ts | sourceViewActions":{"message":"Source View Actions"},"panels/sources/TabbedEditorContainer.ts | areYouSureYouWantToCloseUnsaved":{"message":"Are you sure you want to close unsaved file: {PH1}?"},"panels/sources/TabbedEditorContainer.ts | changesToThisFileWereNotSavedTo":{"message":"Changes to this file were not saved to file system."},"panels/sources/TabbedEditorContainer.ts | unableToLoadThisContent":{"message":"Unable to load this content."},"panels/sources/ThreadsSidebarPane.ts | paused":{"message":"paused"},"panels/sources/WatchExpressionsSidebarPane.ts | addPropertyPathToWatch":{"message":"Add property path to watch"},"panels/sources/WatchExpressionsSidebarPane.ts | addWatchExpression":{"message":"Add watch expression"},"panels/sources/WatchExpressionsSidebarPane.ts | copyValue":{"message":"Copy value"},"panels/sources/WatchExpressionsSidebarPane.ts | deleteAllWatchExpressions":{"message":"Delete all watch expressions"},"panels/sources/WatchExpressionsSidebarPane.ts | deleteWatchExpression":{"message":"Delete watch expression"},"panels/sources/WatchExpressionsSidebarPane.ts | notAvailable":{"message":"<not available>"},"panels/sources/WatchExpressionsSidebarPane.ts | noWatchExpressions":{"message":"No watch expressions"},"panels/sources/WatchExpressionsSidebarPane.ts | refreshWatchExpressions":{"message":"Refresh watch expressions"},"panels/timeline/components/WebVitalsTimeline.ts | fcp":{"message":"FCP"},"panels/timeline/components/WebVitalsTimeline.ts | firstContentfulPaint":{"message":"First Contentful Paint"},"panels/timeline/components/WebVitalsTimeline.ts | good":{"message":"Good"},"panels/timeline/components/WebVitalsTimeline.ts | largestContentfulPaint":{"message":"Largest Contentful Paint"},"panels/timeline/components/WebVitalsTimeline.ts | lcp":{"message":"LCP"},"panels/timeline/components/WebVitalsTimeline.ts | longTask":{"message":"Long Task"},"panels/timeline/components/WebVitalsTimeline.ts | longTasks":{"message":"Long Tasks"},"panels/timeline/components/WebVitalsTimeline.ts | ls":{"message":"LS"},"panels/timeline/components/WebVitalsTimeline.ts | needsImprovement":{"message":"Needs improvement"},"panels/timeline/components/WebVitalsTimeline.ts | poor":{"message":"Poor"},"panels/timeline/CountersGraph.ts | documents":{"message":"Documents"},"panels/timeline/CountersGraph.ts | gpuMemory":{"message":"GPU Memory"},"panels/timeline/CountersGraph.ts | jsHeap":{"message":"JS Heap"},"panels/timeline/CountersGraph.ts | listeners":{"message":"Listeners"},"panels/timeline/CountersGraph.ts | nodes":{"message":"Nodes"},"panels/timeline/CountersGraph.ts | ss":{"message":"[{PH1} – {PH2}]"},"panels/timeline/EventsTimelineTreeView.ts | all":{"message":"All"},"panels/timeline/EventsTimelineTreeView.ts | Dms":{"message":"{PH1} ms"},"panels/timeline/EventsTimelineTreeView.ts | durationFilter":{"message":"Duration filter"},"panels/timeline/EventsTimelineTreeView.ts | filterEventLog":{"message":"Filter event log"},"panels/timeline/EventsTimelineTreeView.ts | startTime":{"message":"Start Time"},"panels/timeline/timeline-meta.ts | hideChromeFrameInLayersView":{"message":"Hide chrome frame in Layers view"},"panels/timeline/timeline-meta.ts | javascriptProfiler":{"message":"JavaScript Profiler"},"panels/timeline/timeline-meta.ts | loadProfile":{"message":"Load profile…"},"panels/timeline/timeline-meta.ts | nextFrame":{"message":"Next frame"},"panels/timeline/timeline-meta.ts | nextRecording":{"message":"Next recording"},"panels/timeline/timeline-meta.ts | performance":{"message":"Performance"},"panels/timeline/timeline-meta.ts | previousFrame":{"message":"Previous frame"},"panels/timeline/timeline-meta.ts | previousRecording":{"message":"Previous recording"},"panels/timeline/timeline-meta.ts | record":{"message":"Record"},"panels/timeline/timeline-meta.ts | saveProfile":{"message":"Save profile…"},"panels/timeline/timeline-meta.ts | showJavascriptProfiler":{"message":"Show JavaScript Profiler"},"panels/timeline/timeline-meta.ts | showPerformance":{"message":"Show Performance"},"panels/timeline/timeline-meta.ts | showRecentTimelineSessions":{"message":"Show recent timeline sessions"},"panels/timeline/timeline-meta.ts | startProfilingAndReloadPage":{"message":"Start profiling and reload page"},"panels/timeline/timeline-meta.ts | startStopRecording":{"message":"Start/stop recording"},"panels/timeline/timeline-meta.ts | stop":{"message":"Stop"},"panels/timeline/TimelineController.ts | cpuProfileForATargetIsNot":{"message":"CPU profile for a target is not available."},"panels/timeline/TimelineController.ts | tracingNotSupported":{"message":"Performance trace recording not supported for this type of target"},"panels/timeline/TimelineDetailsView.ts | bottomup":{"message":"Bottom-Up"},"panels/timeline/TimelineDetailsView.ts | callTree":{"message":"Call Tree"},"panels/timeline/TimelineDetailsView.ts | estimated":{"message":"estimated"},"panels/timeline/TimelineDetailsView.ts | eventLog":{"message":"Event Log"},"panels/timeline/TimelineDetailsView.ts | layers":{"message":"Layers"},"panels/timeline/TimelineDetailsView.ts | learnMore":{"message":"Learn more"},"panels/timeline/TimelineDetailsView.ts | paintProfiler":{"message":"Paint Profiler"},"panels/timeline/TimelineDetailsView.ts | rangeSS":{"message":"Range:  {PH1} – {PH2}"},"panels/timeline/TimelineDetailsView.ts | summary":{"message":"Summary"},"panels/timeline/TimelineDetailsView.ts | totalBlockingTimeSmss":{"message":"Total blocking time: {PH1}ms{PH2}"},"panels/timeline/TimelineEventOverview.ts | coverage":{"message":"COVERAGE"},"panels/timeline/TimelineEventOverview.ts | cpu":{"message":"CPU"},"panels/timeline/TimelineEventOverview.ts | fps":{"message":"FPS"},"panels/timeline/TimelineEventOverview.ts | heap":{"message":"HEAP"},"panels/timeline/TimelineEventOverview.ts | net":{"message":"NET"},"panels/timeline/TimelineEventOverview.ts | sSDash":{"message":"{PH1} – {PH2}"},"panels/timeline/TimelineFlameChartDataProvider.ts | animation":{"message":"Animation"},"panels/timeline/TimelineFlameChartDataProvider.ts | console":{"message":"Console"},"panels/timeline/TimelineFlameChartDataProvider.ts | droppedFrame":{"message":"Dropped Frame"},"panels/timeline/TimelineFlameChartDataProvider.ts | experience":{"message":"Experience"},"panels/timeline/TimelineFlameChartDataProvider.ts | frame":{"message":"Frame"},"panels/timeline/TimelineFlameChartDataProvider.ts | frames":{"message":"Frames"},"panels/timeline/TimelineFlameChartDataProvider.ts | frameS":{"message":"Frame — {PH1}"},"panels/timeline/TimelineFlameChartDataProvider.ts | gpu":{"message":"GPU"},"panels/timeline/TimelineFlameChartDataProvider.ts | idleFrame":{"message":"Idle Frame"},"panels/timeline/TimelineFlameChartDataProvider.ts | input":{"message":"Input"},"panels/timeline/TimelineFlameChartDataProvider.ts | interactions":{"message":"Interactions"},"panels/timeline/TimelineFlameChartDataProvider.ts | longFrame":{"message":"Long frame"},"panels/timeline/TimelineFlameChartDataProvider.ts | main":{"message":"Main"},"panels/timeline/TimelineFlameChartDataProvider.ts | mainS":{"message":"Main — {PH1}"},"panels/timeline/TimelineFlameChartDataProvider.ts | occurrencesS":{"message":"Occurrences: {PH1}"},"panels/timeline/TimelineFlameChartDataProvider.ts | onIgnoreList":{"message":"On ignore list"},"panels/timeline/TimelineFlameChartDataProvider.ts | partiallyPresentedFrame":{"message":"Partially Presented Frame"},"panels/timeline/TimelineFlameChartDataProvider.ts | raster":{"message":"Raster"},"panels/timeline/TimelineFlameChartDataProvider.ts | rasterizerThreadS":{"message":"Rasterizer Thread {PH1}"},"panels/timeline/TimelineFlameChartDataProvider.ts | sFfps":{"message":"{PH1} ~ {PH2} fps"},"panels/timeline/TimelineFlameChartDataProvider.ts | sSelfS":{"message":"{PH1} (self {PH2})"},"panels/timeline/TimelineFlameChartDataProvider.ts | subframe":{"message":"Subframe"},"panels/timeline/TimelineFlameChartDataProvider.ts | thread":{"message":"Thread"},"panels/timeline/TimelineFlameChartDataProvider.ts | threadS":{"message":"Thread {PH1}"},"panels/timeline/TimelineFlameChartDataProvider.ts | timings":{"message":"Timings"},"panels/timeline/TimelineFlameChartNetworkDataProvider.ts | network":{"message":"Network"},"panels/timeline/TimelineFlameChartView.ts | sAtS":{"message":"{PH1} at {PH2}"},"panels/timeline/TimelineHistoryManager.ts | currentSessionSS":{"message":"Current Session: {PH1}. {PH2}"},"panels/timeline/TimelineHistoryManager.ts | moments":{"message":"moments"},"panels/timeline/TimelineHistoryManager.ts | noRecordings":{"message":"(no recordings)"},"panels/timeline/TimelineHistoryManager.ts | sAgo":{"message":"({PH1} ago)"},"panels/timeline/TimelineHistoryManager.ts | sD":{"message":"{PH1} #{PH2}"},"panels/timeline/TimelineHistoryManager.ts | selectTimelineSession":{"message":"Select Timeline Session"},"panels/timeline/TimelineHistoryManager.ts | sH":{"message":"{PH1} h"},"panels/timeline/TimelineHistoryManager.ts | sM":{"message":"{PH1} m"},"panels/timeline/TimelineLoader.ts | legacyTimelineFormatIsNot":{"message":"Legacy Timeline format is not supported."},"panels/timeline/TimelineLoader.ts | malformedCpuProfileFormat":{"message":"Malformed CPU profile format"},"panels/timeline/TimelineLoader.ts | malformedTimelineDataS":{"message":"Malformed timeline data: {PH1}"},"panels/timeline/TimelineLoader.ts | malformedTimelineDataUnknownJson":{"message":"Malformed timeline data: Unknown JSON format"},"panels/timeline/TimelineLoader.ts | malformedTimelineInputWrongJson":{"message":"Malformed timeline input, wrong JSON brackets balance"},"panels/timeline/TimelinePanel.ts | afterRecordingSelectAnAreaOf":{"message":"After recording, select an area of interest in the overview by dragging. Then, zoom and pan the timeline with the mousewheel or {PH1} keys. {PH2}"},"panels/timeline/TimelinePanel.ts | bufferUsage":{"message":"Buffer usage"},"panels/timeline/TimelinePanel.ts | capturesAdvancedPaint":{"message":"Captures advanced paint instrumentation, introduces significant performance overhead"},"panels/timeline/TimelinePanel.ts | captureScreenshots":{"message":"Capture screenshots"},"panels/timeline/TimelinePanel.ts | captureSettings":{"message":"Capture settings"},"panels/timeline/TimelinePanel.ts | clear":{"message":"Clear"},"panels/timeline/TimelinePanel.ts | clickTheRecordButtonSOrHitSTo":{"message":"Click the record button {PH1} or hit {PH2} to start a new recording."},"panels/timeline/TimelinePanel.ts | clickTheReloadButtonSOrHitSTo":{"message":"Click the reload button {PH1} or hit {PH2} to record the page load."},"panels/timeline/TimelinePanel.ts | close":{"message":"Close"},"panels/timeline/TimelinePanel.ts | coverage":{"message":"Coverage"},"panels/timeline/TimelinePanel.ts | cpu":{"message":"CPU:"},"panels/timeline/TimelinePanel.ts | CpuThrottlingIsEnabled":{"message":"- CPU throttling is enabled"},"panels/timeline/TimelinePanel.ts | description":{"message":"Description"},"panels/timeline/TimelinePanel.ts | disableJavascriptSamples":{"message":"Disable JavaScript samples"},"panels/timeline/TimelinePanel.ts | disablesJavascriptSampling":{"message":"Disables JavaScript sampling, reduces overhead when running against mobile devices"},"panels/timeline/TimelinePanel.ts | dropTimelineFileOrUrlHere":{"message":"Drop timeline file or URL here"},"panels/timeline/TimelinePanel.ts | enableAdvancedPaint":{"message":"Enable advanced paint instrumentation (slow)"},"panels/timeline/TimelinePanel.ts | failedToSaveTimelineSSS":{"message":"Failed to save timeline: {PH1} ({PH2}, {PH3})"},"panels/timeline/TimelinePanel.ts | initializingProfiler":{"message":"Initializing profiler…"},"panels/timeline/TimelinePanel.ts | JavascriptSamplingIsDisabled":{"message":"- JavaScript sampling is disabled"},"panels/timeline/TimelinePanel.ts | learnmore":{"message":"Learn more"},"panels/timeline/TimelinePanel.ts | loadingProfile":{"message":"Loading profile…"},"panels/timeline/TimelinePanel.ts | loadProfile":{"message":"Load profile…"},"panels/timeline/TimelinePanel.ts | memory":{"message":"Memory"},"panels/timeline/TimelinePanel.ts | network":{"message":"Network:"},"panels/timeline/TimelinePanel.ts | networkConditions":{"message":"Network conditions"},"panels/timeline/TimelinePanel.ts | NetworkThrottlingIsEnabled":{"message":"- Network throttling is enabled"},"panels/timeline/TimelinePanel.ts | processingProfile":{"message":"Processing profile…"},"panels/timeline/TimelinePanel.ts | profiling":{"message":"Profiling…"},"panels/timeline/TimelinePanel.ts | received":{"message":"Received"},"panels/timeline/TimelinePanel.ts | recordCoverageWithPerformance":{"message":"Record coverage with performance trace"},"panels/timeline/TimelinePanel.ts | recordingFailed":{"message":"Recording failed"},"panels/timeline/TimelinePanel.ts | saveProfile":{"message":"Save profile…"},"panels/timeline/TimelinePanel.ts | screenshots":{"message":"Screenshots"},"panels/timeline/TimelinePanel.ts | showMemoryTimeline":{"message":"Show memory timeline"},"panels/timeline/TimelinePanel.ts | showWebVitals":{"message":"Show Web Vitals"},"panels/timeline/TimelinePanel.ts | SignificantOverheadDueToPaint":{"message":"- Significant overhead due to paint instrumentation"},"panels/timeline/TimelinePanel.ts | ssec":{"message":"{PH1} sec"},"panels/timeline/TimelinePanel.ts | status":{"message":"Status"},"panels/timeline/TimelinePanel.ts | stop":{"message":"Stop"},"panels/timeline/TimelinePanel.ts | stoppingTimeline":{"message":"Stopping timeline…"},"panels/timeline/TimelinePanel.ts | time":{"message":"Time"},"panels/timeline/TimelinePanel.ts | wasd":{"message":"WASD"},"panels/timeline/TimelinePanel.ts | webVitals":{"message":"Web Vitals"},"panels/timeline/TimelineTreeView.ts | activity":{"message":"Activity"},"panels/timeline/TimelineTreeView.ts | chromeExtensionsOverhead":{"message":"[Chrome extensions overhead]"},"panels/timeline/TimelineTreeView.ts | filter":{"message":"Filter"},"panels/timeline/TimelineTreeView.ts | filterBottomup":{"message":"Filter bottom-up"},"panels/timeline/TimelineTreeView.ts | filterCallTree":{"message":"Filter call tree"},"panels/timeline/TimelineTreeView.ts | fms":{"message":"{PH1} ms"},"panels/timeline/TimelineTreeView.ts | groupBy":{"message":"Group by"},"panels/timeline/TimelineTreeView.ts | groupByActivity":{"message":"Group by Activity"},"panels/timeline/TimelineTreeView.ts | groupByCategory":{"message":"Group by Category"},"panels/timeline/TimelineTreeView.ts | groupByDomain":{"message":"Group by Domain"},"panels/timeline/TimelineTreeView.ts | groupByFrame":{"message":"Group by Frame"},"panels/timeline/TimelineTreeView.ts | groupBySubdomain":{"message":"Group by Subdomain"},"panels/timeline/TimelineTreeView.ts | groupByUrl":{"message":"Group by URL"},"panels/timeline/TimelineTreeView.ts | heaviestStack":{"message":"Heaviest stack"},"panels/timeline/TimelineTreeView.ts | heaviestStackHidden":{"message":"Heaviest stack sidebar hidden"},"panels/timeline/TimelineTreeView.ts | heaviestStackShown":{"message":"Heaviest stack sidebar shown"},"panels/timeline/TimelineTreeView.ts | hideHeaviestStack":{"message":"Hide Heaviest stack"},"panels/timeline/TimelineTreeView.ts | javascript":{"message":"JavaScript"},"panels/timeline/TimelineTreeView.ts | noGrouping":{"message":"No Grouping"},"panels/timeline/TimelineTreeView.ts | notOptimizedS":{"message":"Not optimized: {PH1}"},"panels/timeline/TimelineTreeView.ts | page":{"message":"Page"},"panels/timeline/TimelineTreeView.ts | percentPlaceholder":{"message":"{PH1} %"},"panels/timeline/TimelineTreeView.ts | performance":{"message":"Performance"},"panels/timeline/TimelineTreeView.ts | selectItemForDetails":{"message":"Select item for details."},"panels/timeline/TimelineTreeView.ts | selfTime":{"message":"Self Time"},"panels/timeline/TimelineTreeView.ts | showHeaviestStack":{"message":"Show Heaviest stack"},"panels/timeline/TimelineTreeView.ts | timelineStack":{"message":"Timeline Stack"},"panels/timeline/TimelineTreeView.ts | totalTime":{"message":"Total Time"},"panels/timeline/TimelineTreeView.ts | unattributed":{"message":"[unattributed]"},"panels/timeline/TimelineTreeView.ts | vRuntime":{"message":"[V8 Runtime]"},"panels/timeline/TimelineUIUtils.ts | aggregatedTime":{"message":"Aggregated Time"},"panels/timeline/TimelineUIUtils.ts | allottedTime":{"message":"Allotted Time"},"panels/timeline/TimelineUIUtils.ts | animation":{"message":"Animation"},"panels/timeline/TimelineUIUtils.ts | animationFrameFired":{"message":"Animation Frame Fired"},"panels/timeline/TimelineUIUtils.ts | animationFrameRequested":{"message":"Animation Frame Requested"},"panels/timeline/TimelineUIUtils.ts | async":{"message":"Async"},"panels/timeline/TimelineUIUtils.ts | asyncTask":{"message":"Async Task"},"panels/timeline/TimelineUIUtils.ts | cachedWasmModule":{"message":"Cached Wasm Module"},"panels/timeline/TimelineUIUtils.ts | cacheModule":{"message":"Cache Module Code"},"panels/timeline/TimelineUIUtils.ts | cacheScript":{"message":"Cache Script Code"},"panels/timeline/TimelineUIUtils.ts | callbackFunction":{"message":"Callback Function"},"panels/timeline/TimelineUIUtils.ts | callbackId":{"message":"Callback ID"},"panels/timeline/TimelineUIUtils.ts | callStacks":{"message":"Call Stacks"},"panels/timeline/TimelineUIUtils.ts | cancelAnimationFrame":{"message":"Cancel Animation Frame"},"panels/timeline/TimelineUIUtils.ts | cancelIdleCallback":{"message":"Cancel Idle Callback"},"panels/timeline/TimelineUIUtils.ts | changedAttributeToSs":{"message":"(changed attribute to \\"{PH1}\\"{PH2})"},"panels/timeline/TimelineUIUtils.ts | changedClassToSs":{"message":"(changed class to \\"{PH1}\\"{PH2})"},"panels/timeline/TimelineUIUtils.ts | changedIdToSs":{"message":"(changed id to \\"{PH1}\\"{PH2})"},"panels/timeline/TimelineUIUtils.ts | changedPesudoToSs":{"message":"(changed pseudo to \\"{PH1}\\"{PH2})"},"panels/timeline/TimelineUIUtils.ts | changedSs":{"message":"(changed \\"{PH1}\\"{PH2})"},"panels/timeline/TimelineUIUtils.ts | click":{"message":"Click"},"panels/timeline/TimelineUIUtils.ts | collected":{"message":"Collected"},"panels/timeline/TimelineUIUtils.ts | compilationCacheSize":{"message":"Compilation cache size"},"panels/timeline/TimelineUIUtils.ts | compilationCacheStatus":{"message":"Compilation cache status"},"panels/timeline/TimelineUIUtils.ts | compile":{"message":"Compile"},"panels/timeline/TimelineUIUtils.ts | compileCode":{"message":"Compile Code"},"panels/timeline/TimelineUIUtils.ts | compiledWasmModule":{"message":"Compiled Wasm Module"},"panels/timeline/TimelineUIUtils.ts | compileModule":{"message":"Compile Module"},"panels/timeline/TimelineUIUtils.ts | compileScript":{"message":"Compile Script"},"panels/timeline/TimelineUIUtils.ts | compositeLayers":{"message":"Composite Layers"},"panels/timeline/TimelineUIUtils.ts | computeIntersections":{"message":"Compute Intersections"},"panels/timeline/TimelineUIUtils.ts | consoleTime":{"message":"Console Time"},"panels/timeline/TimelineUIUtils.ts | consumedCacheSize":{"message":"Consumed Cache Size"},"panels/timeline/TimelineUIUtils.ts | contextMenu":{"message":"Context Menu"},"panels/timeline/TimelineUIUtils.ts | cpuTime":{"message":"CPU time"},"panels/timeline/TimelineUIUtils.ts | createWebsocket":{"message":"Create WebSocket"},"panels/timeline/TimelineUIUtils.ts | cumulativeLayoutShifts":{"message":"Cumulative Layout Shifts"},"panels/timeline/TimelineUIUtils.ts | cumulativeScore":{"message":"Cumulative Score"},"panels/timeline/TimelineUIUtils.ts | currentClusterId":{"message":"Current Cluster ID"},"panels/timeline/TimelineUIUtils.ts | currentClusterScore":{"message":"Current Cluster Score"},"panels/timeline/TimelineUIUtils.ts | decodedBody":{"message":"Decoded Body"},"panels/timeline/TimelineUIUtils.ts | decrypt":{"message":"Decrypt"},"panels/timeline/TimelineUIUtils.ts | decryptReply":{"message":"Decrypt Reply"},"panels/timeline/TimelineUIUtils.ts | destroyWebsocket":{"message":"Destroy WebSocket"},"panels/timeline/TimelineUIUtils.ts | details":{"message":"Details"},"panels/timeline/TimelineUIUtils.ts | digest":{"message":"Digest"},"panels/timeline/TimelineUIUtils.ts | digestReply":{"message":"Digest Reply"},"panels/timeline/TimelineUIUtils.ts | dimensions":{"message":"Dimensions"},"panels/timeline/TimelineUIUtils.ts | domcontentloadedEvent":{"message":"DOMContentLoaded Event"},"panels/timeline/TimelineUIUtils.ts | domGc":{"message":"DOM GC"},"panels/timeline/TimelineUIUtils.ts | drag":{"message":"Drag"},"panels/timeline/TimelineUIUtils.ts | drawFrame":{"message":"Draw Frame"},"panels/timeline/TimelineUIUtils.ts | duration":{"message":"Duration"},"panels/timeline/TimelineUIUtils.ts | eagerCompile":{"message":"Compiling all functions eagerly"},"panels/timeline/TimelineUIUtils.ts | elementsAffected":{"message":"Elements Affected"},"panels/timeline/TimelineUIUtils.ts | embedderCallback":{"message":"Embedder Callback"},"panels/timeline/TimelineUIUtils.ts | emptyPlaceholder":{"message":"{PH1}"},"panels/timeline/TimelineUIUtils.ts | emptyPlaceholderColon":{"message":": {PH1}"},"panels/timeline/TimelineUIUtils.ts | encodedData":{"message":"Encoded Data"},"panels/timeline/TimelineUIUtils.ts | encrypt":{"message":"Encrypt"},"panels/timeline/TimelineUIUtils.ts | encryptReply":{"message":"Encrypt Reply"},"panels/timeline/TimelineUIUtils.ts | evaluateModule":{"message":"Evaluate Module"},"panels/timeline/TimelineUIUtils.ts | evaluateScript":{"message":"Evaluate Script"},"panels/timeline/TimelineUIUtils.ts | event":{"message":"Event"},"panels/timeline/TimelineUIUtils.ts | evolvedClsLink":{"message":"evolved"},"panels/timeline/TimelineUIUtils.ts | experience":{"message":"Experience"},"panels/timeline/TimelineUIUtils.ts | failedToLoadScriptFromCache":{"message":"failed to load script from cache"},"panels/timeline/TimelineUIUtils.ts | finishLoading":{"message":"Finish Loading"},"panels/timeline/TimelineUIUtils.ts | fireIdleCallback":{"message":"Fire Idle Callback"},"panels/timeline/TimelineUIUtils.ts | firstContentfulPaint":{"message":"First Contentful Paint"},"panels/timeline/TimelineUIUtils.ts | firstInvalidated":{"message":"First Invalidated"},"panels/timeline/TimelineUIUtils.ts | firstLayoutInvalidation":{"message":"First Layout Invalidation"},"panels/timeline/TimelineUIUtils.ts | firstPaint":{"message":"First Paint"},"panels/timeline/TimelineUIUtils.ts | fling":{"message":"Fling"},"panels/timeline/TimelineUIUtils.ts | flingHalt":{"message":"Fling Halt"},"panels/timeline/TimelineUIUtils.ts | flingStart":{"message":"Fling Start"},"panels/timeline/TimelineUIUtils.ts | forcedReflow":{"message":"Forced reflow"},"panels/timeline/TimelineUIUtils.ts | fps":{"message":"FPS"},"panels/timeline/TimelineUIUtils.ts | frame":{"message":"Frame"},"panels/timeline/TimelineUIUtils.ts | frameStart":{"message":"Frame Start"},"panels/timeline/TimelineUIUtils.ts | frameStartedLoading":{"message":"Frame Started Loading"},"panels/timeline/TimelineUIUtils.ts | frameStartMainThread":{"message":"Frame Start (main thread)"},"panels/timeline/TimelineUIUtils.ts | FromCache":{"message":" (from cache)"},"panels/timeline/TimelineUIUtils.ts | FromMemoryCache":{"message":" (from memory cache)"},"panels/timeline/TimelineUIUtils.ts | FromPush":{"message":" (from push)"},"panels/timeline/TimelineUIUtils.ts | FromServiceWorker":{"message":" (from service worker)"},"panels/timeline/TimelineUIUtils.ts | function":{"message":"Function"},"panels/timeline/TimelineUIUtils.ts | functionCall":{"message":"Function Call"},"panels/timeline/TimelineUIUtils.ts | gcEvent":{"message":"GC Event"},"panels/timeline/TimelineUIUtils.ts | gpu":{"message":"GPU"},"panels/timeline/TimelineUIUtils.ts | hadRecentInput":{"message":"Had recent input"},"panels/timeline/TimelineUIUtils.ts | handlerTookS":{"message":"Handler took {PH1}"},"panels/timeline/TimelineUIUtils.ts | hitTest":{"message":"Hit Test"},"panels/timeline/TimelineUIUtils.ts | idle":{"message":"Idle"},"panels/timeline/TimelineUIUtils.ts | idleCallbackExecutionExtended":{"message":"Idle callback execution extended beyond deadline by {PH1}"},"panels/timeline/TimelineUIUtils.ts | idleCallbackRequested":{"message":"Idle Callback Requested"},"panels/timeline/TimelineUIUtils.ts | imageDecode":{"message":"Image Decode"},"panels/timeline/TimelineUIUtils.ts | imageResize":{"message":"Image Resize"},"panels/timeline/TimelineUIUtils.ts | imageUrl":{"message":"Image URL"},"panels/timeline/TimelineUIUtils.ts | initiator":{"message":"Initiator"},"panels/timeline/TimelineUIUtils.ts | inputLatency":{"message":"Input Latency"},"panels/timeline/TimelineUIUtils.ts | installTimer":{"message":"Install Timer"},"panels/timeline/TimelineUIUtils.ts | invalidateLayout":{"message":"Invalidate Layout"},"panels/timeline/TimelineUIUtils.ts | invalidations":{"message":"Invalidations"},"panels/timeline/TimelineUIUtils.ts | invokedByTimeout":{"message":"Invoked by Timeout"},"panels/timeline/TimelineUIUtils.ts | jank":{"message":"jank"},"panels/timeline/TimelineUIUtils.ts | jsFrame":{"message":"JS Frame"},"panels/timeline/TimelineUIUtils.ts | keyCharacter":{"message":"Key — Character"},"panels/timeline/TimelineUIUtils.ts | keyDown":{"message":"Key Down"},"panels/timeline/TimelineUIUtils.ts | keyUp":{"message":"Key Up"},"panels/timeline/TimelineUIUtils.ts | largestContentfulPaint":{"message":"Largest Contentful Paint"},"panels/timeline/TimelineUIUtils.ts | layerRoot":{"message":"Layer Root"},"panels/timeline/TimelineUIUtils.ts | layerTree":{"message":"Layer tree"},"panels/timeline/TimelineUIUtils.ts | layout":{"message":"Layout"},"panels/timeline/TimelineUIUtils.ts | layoutForced":{"message":"Layout Forced"},"panels/timeline/TimelineUIUtils.ts | layoutInvalidations":{"message":"Layout Invalidations"},"panels/timeline/TimelineUIUtils.ts | layoutRoot":{"message":"Layout root"},"panels/timeline/TimelineUIUtils.ts | layoutShift":{"message":"Layout Shift"},"panels/timeline/TimelineUIUtils.ts | learnMore":{"message":"Learn more"},"panels/timeline/TimelineUIUtils.ts | loadFromCache":{"message":"load from cache"},"panels/timeline/TimelineUIUtils.ts | loading":{"message":"Loading"},"panels/timeline/TimelineUIUtils.ts | location":{"message":"Location"},"panels/timeline/TimelineUIUtils.ts | longTask":{"message":"Long task"},"panels/timeline/TimelineUIUtils.ts | majorGc":{"message":"Major GC"},"panels/timeline/TimelineUIUtils.ts | message":{"message":"Message"},"panels/timeline/TimelineUIUtils.ts | mimeType":{"message":"Mime Type"},"panels/timeline/TimelineUIUtils.ts | mimeTypeCaps":{"message":"MIME Type"},"panels/timeline/TimelineUIUtils.ts | minorGc":{"message":"Minor GC"},"panels/timeline/TimelineUIUtils.ts | module":{"message":"Module"},"panels/timeline/TimelineUIUtils.ts | mouseDown":{"message":"Mouse Down"},"panels/timeline/TimelineUIUtils.ts | mouseMove":{"message":"Mouse Move"},"panels/timeline/TimelineUIUtils.ts | mouseUp":{"message":"Mouse Up"},"panels/timeline/TimelineUIUtils.ts | mouseWheel":{"message":"Mouse Wheel"},"panels/timeline/TimelineUIUtils.ts | movedFrom":{"message":"Moved from"},"panels/timeline/TimelineUIUtils.ts | movedTo":{"message":"Moved to"},"panels/timeline/TimelineUIUtils.ts | networkRequest":{"message":"Network request"},"panels/timeline/TimelineUIUtils.ts | networkTransfer":{"message":"network transfer"},"panels/timeline/TimelineUIUtils.ts | no":{"message":"No"},"panels/timeline/TimelineUIUtils.ts | node":{"message":"Node:"},"panels/timeline/TimelineUIUtils.ts | nodes":{"message":"Nodes:"},"panels/timeline/TimelineUIUtils.ts | nodesThatNeedLayout":{"message":"Nodes That Need Layout"},"panels/timeline/TimelineUIUtils.ts | notOptimized":{"message":"Not optimized"},"panels/timeline/TimelineUIUtils.ts | onloadEvent":{"message":"Onload Event"},"panels/timeline/TimelineUIUtils.ts | optimizeCode":{"message":"Optimize Code"},"panels/timeline/TimelineUIUtils.ts | other":{"message":"Other"},"panels/timeline/TimelineUIUtils.ts | otherInvalidations":{"message":"Other Invalidations"},"panels/timeline/TimelineUIUtils.ts | ownerElement":{"message":"Owner Element"},"panels/timeline/TimelineUIUtils.ts | paint":{"message":"Paint"},"panels/timeline/TimelineUIUtils.ts | paintImage":{"message":"Paint Image"},"panels/timeline/TimelineUIUtils.ts | painting":{"message":"Painting"},"panels/timeline/TimelineUIUtils.ts | paintProfiler":{"message":"Paint Profiler"},"panels/timeline/TimelineUIUtils.ts | paintSetup":{"message":"Paint Setup"},"panels/timeline/TimelineUIUtils.ts | parse":{"message":"Parse"},"panels/timeline/TimelineUIUtils.ts | parseAndCompile":{"message":"Parse and Compile"},"panels/timeline/TimelineUIUtils.ts | parseHtml":{"message":"Parse HTML"},"panels/timeline/TimelineUIUtils.ts | parseStylesheet":{"message":"Parse Stylesheet"},"panels/timeline/TimelineUIUtils.ts | pendingFor":{"message":"Pending for"},"panels/timeline/TimelineUIUtils.ts | pinchBegin":{"message":"Pinch Begin"},"panels/timeline/TimelineUIUtils.ts | pinchEnd":{"message":"Pinch End"},"panels/timeline/TimelineUIUtils.ts | pinchUpdate":{"message":"Pinch Update"},"panels/timeline/TimelineUIUtils.ts | prePaint":{"message":"Pre-Paint"},"panels/timeline/TimelineUIUtils.ts | preview":{"message":"Preview"},"panels/timeline/TimelineUIUtils.ts | priority":{"message":"Priority"},"panels/timeline/TimelineUIUtils.ts | producedCacheSize":{"message":"Produced Cache Size"},"panels/timeline/TimelineUIUtils.ts | range":{"message":"Range"},"panels/timeline/TimelineUIUtils.ts | rasterizePaint":{"message":"Rasterize Paint"},"panels/timeline/TimelineUIUtils.ts | recalculateStyle":{"message":"Recalculate Style"},"panels/timeline/TimelineUIUtils.ts | recalculationForced":{"message":"Recalculation Forced"},"panels/timeline/TimelineUIUtils.ts | receiveData":{"message":"Receive Data"},"panels/timeline/TimelineUIUtils.ts | receiveResponse":{"message":"Receive Response"},"panels/timeline/TimelineUIUtils.ts | receiveWebsocketHandshake":{"message":"Receive WebSocket Handshake"},"panels/timeline/TimelineUIUtils.ts | recurringHandlerTookS":{"message":"Recurring handler took {PH1}"},"panels/timeline/TimelineUIUtils.ts | relatedNode":{"message":"Related Node"},"panels/timeline/TimelineUIUtils.ts | removeTimer":{"message":"Remove Timer"},"panels/timeline/TimelineUIUtils.ts | rendering":{"message":"Rendering"},"panels/timeline/TimelineUIUtils.ts | repeats":{"message":"Repeats"},"panels/timeline/TimelineUIUtils.ts | requestAnimationFrame":{"message":"Request Animation Frame"},"panels/timeline/TimelineUIUtils.ts | requestIdleCallback":{"message":"Request Idle Callback"},"panels/timeline/TimelineUIUtils.ts | requestMainThreadFrame":{"message":"Request Main Thread Frame"},"panels/timeline/TimelineUIUtils.ts | requestMethod":{"message":"Request Method"},"panels/timeline/TimelineUIUtils.ts | resource":{"message":"Resource"},"panels/timeline/TimelineUIUtils.ts | response":{"message":"Response"},"panels/timeline/TimelineUIUtils.ts | reveal":{"message":"Reveal"},"panels/timeline/TimelineUIUtils.ts | runMicrotasks":{"message":"Run Microtasks"},"panels/timeline/TimelineUIUtils.ts | sAndS":{"message":"{PH1} and {PH2}"},"panels/timeline/TimelineUIUtils.ts | sAndSOther":{"message":"{PH1}, {PH2}, and 1 other"},"panels/timeline/TimelineUIUtils.ts | sAtS":{"message":"{PH1} at {PH2}"},"panels/timeline/TimelineUIUtils.ts | sAtSParentheses":{"message":"{PH1} (at {PH2})"},"panels/timeline/TimelineUIUtils.ts | sBytes":{"message":"{n, plural, =1 {# Byte} other {# Bytes}}"},"panels/timeline/TimelineUIUtils.ts | scheduleStyleRecalculation":{"message":"Schedule Style Recalculation"},"panels/timeline/TimelineUIUtils.ts | sChildren":{"message":"{PH1} (children)"},"panels/timeline/TimelineUIUtils.ts | sCLSInformation":{"message":"{PH1} can result in poor user experiences. It has recently {PH2}."},"panels/timeline/TimelineUIUtils.ts | sCollected":{"message":"{PH1} collected"},"panels/timeline/TimelineUIUtils.ts | score":{"message":"Score"},"panels/timeline/TimelineUIUtils.ts | script":{"message":"Script"},"panels/timeline/TimelineUIUtils.ts | scripting":{"message":"Scripting"},"panels/timeline/TimelineUIUtils.ts | scriptLoadedFromCache":{"message":"script loaded from cache"},"panels/timeline/TimelineUIUtils.ts | scriptNotEligible":{"message":"script not eligible"},"panels/timeline/TimelineUIUtils.ts | scroll":{"message":"Scroll"},"panels/timeline/TimelineUIUtils.ts | scrollBegin":{"message":"Scroll Begin"},"panels/timeline/TimelineUIUtils.ts | scrollEnd":{"message":"Scroll End"},"panels/timeline/TimelineUIUtils.ts | scrollUpdate":{"message":"Scroll Update"},"panels/timeline/TimelineUIUtils.ts | selfTime":{"message":"Self Time"},"panels/timeline/TimelineUIUtils.ts | sendRequest":{"message":"Send Request"},"panels/timeline/TimelineUIUtils.ts | sendWebsocketHandshake":{"message":"Send WebSocket Handshake"},"panels/timeline/TimelineUIUtils.ts | sForS":{"message":"{PH1} for {PH2}"},"panels/timeline/TimelineUIUtils.ts | show":{"message":"Show"},"panels/timeline/TimelineUIUtils.ts | sign":{"message":"Sign"},"panels/timeline/TimelineUIUtils.ts | signReply":{"message":"Sign Reply"},"panels/timeline/TimelineUIUtils.ts | sIsALikelyPerformanceBottleneck":{"message":"{PH1} is a likely performance bottleneck."},"panels/timeline/TimelineUIUtils.ts | size":{"message":"Size"},"panels/timeline/TimelineUIUtils.ts | sLongFrameTimesAreAnIndicationOf":{"message":"{PH1}. Long frame times are an indication of {PH2}"},"panels/timeline/TimelineUIUtils.ts | sOfS":{"message":"{PH1} of {PH2}"},"panels/timeline/TimelineUIUtils.ts | sS":{"message":"{PH1}: {PH2}"},"panels/timeline/TimelineUIUtils.ts | sSAndSOthers":{"message":"{PH1}, {PH2}, and {PH3} others"},"panels/timeline/TimelineUIUtils.ts | sSCurlyBrackets":{"message":"({PH1}, {PH2})"},"panels/timeline/TimelineUIUtils.ts | sSDimensions":{"message":"{PH1} × {PH2}"},"panels/timeline/TimelineUIUtils.ts | sSDot":{"message":"{PH1}. {PH2}"},"panels/timeline/TimelineUIUtils.ts | sSelf":{"message":"{PH1} (self)"},"panels/timeline/TimelineUIUtils.ts | sSs":{"message":"{PH1} [{PH2}…{PH3}]"},"panels/timeline/TimelineUIUtils.ts | sSSquareBrackets":{"message":"{PH1} [{PH2}…]"},"panels/timeline/TimelineUIUtils.ts | SSSResourceLoading":{"message":" ({PH1} {PH2} + {PH3} resource loading)"},"panels/timeline/TimelineUIUtils.ts | stackTrace":{"message":"Stack Trace"},"panels/timeline/TimelineUIUtils.ts | stackTraceColon":{"message":"Stack trace:"},"panels/timeline/TimelineUIUtils.ts | state":{"message":"State"},"panels/timeline/TimelineUIUtils.ts | statusCode":{"message":"Status Code"},"panels/timeline/TimelineUIUtils.ts | sTookS":{"message":"{PH1} took {PH2}."},"panels/timeline/TimelineUIUtils.ts | streamed":{"message":"Streamed"},"panels/timeline/TimelineUIUtils.ts | streamingCompileTask":{"message":"Streaming Compile Task"},"panels/timeline/TimelineUIUtils.ts | streamingWasmResponse":{"message":"Streaming Wasm Response"},"panels/timeline/TimelineUIUtils.ts | styleInvalidations":{"message":"Style Invalidations"},"panels/timeline/TimelineUIUtils.ts | stylesheetUrl":{"message":"Stylesheet URL"},"panels/timeline/TimelineUIUtils.ts | system":{"message":"System"},"panels/timeline/TimelineUIUtils.ts | tap":{"message":"Tap"},"panels/timeline/TimelineUIUtils.ts | tapBegin":{"message":"Tap Begin"},"panels/timeline/TimelineUIUtils.ts | tapDown":{"message":"Tap Down"},"panels/timeline/TimelineUIUtils.ts | tapHalt":{"message":"Tap Halt"},"panels/timeline/TimelineUIUtils.ts | task":{"message":"Task"},"panels/timeline/TimelineUIUtils.ts | timeout":{"message":"Timeout"},"panels/timeline/TimelineUIUtils.ts | timerFired":{"message":"Timer Fired"},"panels/timeline/TimelineUIUtils.ts | timerId":{"message":"Timer ID"},"panels/timeline/TimelineUIUtils.ts | timerInstalled":{"message":"Timer Installed"},"panels/timeline/TimelineUIUtils.ts | timeSpentInRendering":{"message":"Time spent in rendering"},"panels/timeline/TimelineUIUtils.ts | timestamp":{"message":"Timestamp"},"panels/timeline/TimelineUIUtils.ts | timeWaitingForMainThread":{"message":"Time Waiting for Main Thread"},"panels/timeline/TimelineUIUtils.ts | totalTime":{"message":"Total Time"},"panels/timeline/TimelineUIUtils.ts | touchCancel":{"message":"Touch Cancel"},"panels/timeline/TimelineUIUtils.ts | touchEnd":{"message":"Touch End"},"panels/timeline/TimelineUIUtils.ts | touchMove":{"message":"Touch Move"},"panels/timeline/TimelineUIUtils.ts | touchStart":{"message":"Touch Start"},"panels/timeline/TimelineUIUtils.ts | type":{"message":"Type"},"panels/timeline/TimelineUIUtils.ts | uncategorized":{"message":"Uncategorized"},"panels/timeline/TimelineUIUtils.ts | unknown":{"message":"unknown"},"panels/timeline/TimelineUIUtils.ts | unknownCause":{"message":"Unknown cause"},"panels/timeline/TimelineUIUtils.ts | UnknownNode":{"message":"[ unknown node ]"},"panels/timeline/TimelineUIUtils.ts | updateLayer":{"message":"Update Layer"},"panels/timeline/TimelineUIUtils.ts | updateLayerTree":{"message":"Update Layer Tree"},"panels/timeline/TimelineUIUtils.ts | url":{"message":"Url"},"panels/timeline/TimelineUIUtils.ts | userTiming":{"message":"User Timing"},"panels/timeline/TimelineUIUtils.ts | verify":{"message":"Verify"},"panels/timeline/TimelineUIUtils.ts | verifyReply":{"message":"Verify Reply"},"panels/timeline/TimelineUIUtils.ts | waitingForNetwork":{"message":"Waiting for Network"},"panels/timeline/TimelineUIUtils.ts | warning":{"message":"Warning"},"panels/timeline/TimelineUIUtils.ts | wasmModuleCacheHit":{"message":"Wasm Module Cache Hit"},"panels/timeline/TimelineUIUtils.ts | wasmModuleCacheInvalid":{"message":"Wasm Module Cache Invalid"},"panels/timeline/TimelineUIUtils.ts | websocketProtocol":{"message":"WebSocket Protocol"},"panels/timeline/TimelineUIUtils.ts | willSendRequest":{"message":"Will Send Request"},"panels/timeline/TimelineUIUtils.ts | xhrLoad":{"message":"XHR Load"},"panels/timeline/TimelineUIUtils.ts | xhrReadyStateChange":{"message":"XHR Ready State Change"},"panels/timeline/TimelineUIUtils.ts | yes":{"message":"Yes"},"panels/timeline/UIDevtoolsUtils.ts | drawFrame":{"message":"Draw Frame"},"panels/timeline/UIDevtoolsUtils.ts | drawing":{"message":"Drawing"},"panels/timeline/UIDevtoolsUtils.ts | frameStart":{"message":"Frame Start"},"panels/timeline/UIDevtoolsUtils.ts | idle":{"message":"Idle"},"panels/timeline/UIDevtoolsUtils.ts | layout":{"message":"Layout"},"panels/timeline/UIDevtoolsUtils.ts | painting":{"message":"Painting"},"panels/timeline/UIDevtoolsUtils.ts | rasterizing":{"message":"Rasterizing"},"panels/timeline/UIDevtoolsUtils.ts | system":{"message":"System"},"panels/web_audio/AudioContextContentBuilder.ts | callbackBufferSize":{"message":"Callback Buffer Size"},"panels/web_audio/AudioContextContentBuilder.ts | callbackInterval":{"message":"Callback Interval"},"panels/web_audio/AudioContextContentBuilder.ts | currentTime":{"message":"Current Time"},"panels/web_audio/AudioContextContentBuilder.ts | maxOutputChannels":{"message":"Max Output Channels"},"panels/web_audio/AudioContextContentBuilder.ts | renderCapacity":{"message":"Render Capacity"},"panels/web_audio/AudioContextContentBuilder.ts | sampleRate":{"message":"Sample Rate"},"panels/web_audio/AudioContextContentBuilder.ts | state":{"message":"State"},"panels/web_audio/AudioContextSelector.ts | audioContextS":{"message":"Audio context: {PH1}"},"panels/web_audio/AudioContextSelector.ts | noRecordings":{"message":"(no recordings)"},"panels/web_audio/web_audio-meta.ts | audio":{"message":"audio"},"panels/web_audio/web_audio-meta.ts | showWebaudio":{"message":"Show WebAudio"},"panels/web_audio/web_audio-meta.ts | webaudio":{"message":"WebAudio"},"panels/web_audio/WebAudioView.ts | openAPageThatUsesWebAudioApiTo":{"message":"Open a page that uses Web Audio API to start monitoring."},"panels/webauthn/webauthn-meta.ts | showWebauthn":{"message":"Show WebAuthn"},"panels/webauthn/webauthn-meta.ts | webauthn":{"message":"WebAuthn"},"panels/webauthn/WebauthnPane.ts | actions":{"message":"Actions"},"panels/webauthn/WebauthnPane.ts | active":{"message":"Active"},"panels/webauthn/WebauthnPane.ts | add":{"message":"Add"},"panels/webauthn/WebauthnPane.ts | addAuthenticator":{"message":"Add authenticator"},"panels/webauthn/WebauthnPane.ts | authenticatorS":{"message":"Authenticator {PH1}"},"panels/webauthn/WebauthnPane.ts | credentials":{"message":"Credentials"},"panels/webauthn/WebauthnPane.ts | editName":{"message":"Edit name"},"panels/webauthn/WebauthnPane.ts | enableVirtualAuthenticator":{"message":"Enable virtual authenticator environment"},"panels/webauthn/WebauthnPane.ts | export":{"message":"Export"},"panels/webauthn/WebauthnPane.ts | id":{"message":"ID"},"panels/webauthn/WebauthnPane.ts | isResident":{"message":"Is Resident"},"panels/webauthn/WebauthnPane.ts | learnMore":{"message":"Learn more"},"panels/webauthn/WebauthnPane.ts | newAuthenticator":{"message":"New authenticator"},"panels/webauthn/WebauthnPane.ts | no":{"message":"No"},"panels/webauthn/WebauthnPane.ts | noCredentialsTryCallingSFromYour":{"message":"No credentials. Try calling {PH1} from your website."},"panels/webauthn/WebauthnPane.ts | privateKeypem":{"message":"Private key.pem"},"panels/webauthn/WebauthnPane.ts | protocol":{"message":"Protocol"},"panels/webauthn/WebauthnPane.ts | remove":{"message":"Remove"},"panels/webauthn/WebauthnPane.ts | rpId":{"message":"RP ID"},"panels/webauthn/WebauthnPane.ts | saveName":{"message":"Save name"},"panels/webauthn/WebauthnPane.ts | setSAsTheActiveAuthenticator":{"message":"Set {PH1} as the active authenticator"},"panels/webauthn/WebauthnPane.ts | signCount":{"message":"Signature Count"},"panels/webauthn/WebauthnPane.ts | supportsResidentKeys":{"message":"Supports resident keys"},"panels/webauthn/WebauthnPane.ts | supportsUserVerification":{"message":"Supports user verification"},"panels/webauthn/WebauthnPane.ts | transport":{"message":"Transport"},"panels/webauthn/WebauthnPane.ts | userHandle":{"message":"User Handle"},"panels/webauthn/WebauthnPane.ts | useWebauthnForPhishingresistant":{"message":"Use WebAuthn for phishing-resistant authentication"},"panels/webauthn/WebauthnPane.ts | uuid":{"message":"UUID"},"panels/webauthn/WebauthnPane.ts | yes":{"message":"Yes"},"ui/components/data_grid/DataGrid.ts | headerOptions":{"message":"Header Options"},"ui/components/data_grid/DataGrid.ts | resetColumns":{"message":"Reset Columns"},"ui/components/data_grid/DataGrid.ts | sortBy":{"message":"Sort By"},"ui/components/diff_view/DiffView.ts | additions":{"message":"Addition:"},"ui/components/diff_view/DiffView.ts | changesDiffViewer":{"message":"Changes diff viewer"},"ui/components/diff_view/DiffView.ts | deletions":{"message":"Deletion:"},"ui/components/diff_view/DiffView.ts | SkippingDMatchingLines":{"message":"( … Skipping {PH1} matching lines … )"},"ui/components/issue_counter/IssueCounter.ts | breakingChanges":{"message":"{issueCount, plural, =1 {# breaking change} other {# breaking changes}}"},"ui/components/issue_counter/IssueCounter.ts | pageErrors":{"message":"{issueCount, plural, =1 {# page error} other {# page errors}}"},"ui/components/issue_counter/IssueCounter.ts | possibleImprovements":{"message":"{issueCount, plural, =1 {# possible improvement} other {# possible improvements}}"},"ui/components/issue_counter/IssueLinkIcon.ts | clickToShowIssue":{"message":"Click to show issue in the issues tab"},"ui/components/issue_counter/IssueLinkIcon.ts | clickToShowIssueWithTitle":{"message":"Click to open the issue tab and show issue: {title}"},"ui/components/issue_counter/IssueLinkIcon.ts | issueUnavailable":{"message":"Issue unavailable at this time"},"ui/components/linear_memory_inspector/linear_memory_inspector-meta.ts | memoryInspector":{"message":"Memory Inspector"},"ui/components/linear_memory_inspector/linear_memory_inspector-meta.ts | showMemoryInspector":{"message":"Show Memory Inspector"},"ui/components/linear_memory_inspector/LinearMemoryInspector.ts | addressHasToBeANumberBetweenSAnd":{"message":"Address has to be a number between {PH1} and {PH2}"},"ui/components/linear_memory_inspector/LinearMemoryInspectorPane.ts | noOpenInspections":{"message":"No open inspections"},"ui/components/linear_memory_inspector/LinearMemoryNavigator.ts | enterAddress":{"message":"Enter address"},"ui/components/linear_memory_inspector/LinearMemoryNavigator.ts | goBackInAddressHistory":{"message":"Go back in address history"},"ui/components/linear_memory_inspector/LinearMemoryNavigator.ts | goForwardInAddressHistory":{"message":"Go forward in address history"},"ui/components/linear_memory_inspector/LinearMemoryNavigator.ts | nextPage":{"message":"Next page"},"ui/components/linear_memory_inspector/LinearMemoryNavigator.ts | previousPage":{"message":"Previous page"},"ui/components/linear_memory_inspector/LinearMemoryNavigator.ts | refresh":{"message":"Refresh"},"ui/components/linear_memory_inspector/LinearMemoryValueInterpreter.ts | changeEndianness":{"message":"Change Endianness"},"ui/components/linear_memory_inspector/LinearMemoryValueInterpreter.ts | toggleValueTypeSettings":{"message":"Toggle value type settings"},"ui/components/linear_memory_inspector/ValueInterpreterDisplay.ts | addressOutOfRange":{"message":"Address out of memory range"},"ui/components/linear_memory_inspector/ValueInterpreterDisplay.ts | changeValueTypeMode":{"message":"Change mode"},"ui/components/linear_memory_inspector/ValueInterpreterDisplay.ts | jumpToPointer":{"message":"Jump to address"},"ui/components/linear_memory_inspector/ValueInterpreterDisplay.ts | signedValue":{"message":"Signed value"},"ui/components/linear_memory_inspector/ValueInterpreterDisplay.ts | unsignedValue":{"message":"Unsigned value"},"ui/components/linear_memory_inspector/ValueInterpreterDisplayUtils.ts | notApplicable":{"message":"N/A"},"ui/components/linear_memory_inspector/ValueInterpreterSettings.ts | otherGroup":{"message":"Other"},"ui/components/panel_feedback/FeedbackButton.ts | feedback":{"message":"Feedback"},"ui/components/panel_feedback/PanelFeedback.ts | previewFeature":{"message":"Preview feature"},"ui/components/panel_feedback/PanelFeedback.ts | previewText":{"message":"Our team is actively working on this feature and we would love to know what you think."},"ui/components/panel_feedback/PanelFeedback.ts | previewTextFeedbackLink":{"message":"Send us your feedback."},"ui/components/panel_feedback/PanelFeedback.ts | videoAndDocumentation":{"message":"Video and documentation"},"ui/components/panel_feedback/PreviewToggle.ts | previewTextFeedbackLink":{"message":"Send us your feedback."},"ui/components/request_link_icon/RequestLinkIcon.ts | clickToShowRequestInTheNetwork":{"message":"Click to open the network panel and show request for URL: {url}"},"ui/components/request_link_icon/RequestLinkIcon.ts | requestUnavailableInTheNetwork":{"message":"Request unavailable in the network panel, try reloading the inspected page"},"ui/components/request_link_icon/RequestLinkIcon.ts | shortenedURL":{"message":"Shortened URL"},"ui/components/survey_link/SurveyLink.ts | anErrorOccurredWithTheSurvey":{"message":"An error occurred with the survey"},"ui/components/survey_link/SurveyLink.ts | openingSurvey":{"message":"Opening survey …"},"ui/components/survey_link/SurveyLink.ts | thankYouForYourFeedback":{"message":"Thank you for your feedback"},"ui/components/text_editor/config.ts | codeEditor":{"message":"Code editor"},"ui/legacy/components/color_picker/ContrastDetails.ts | aa":{"message":"AA"},"ui/legacy/components/color_picker/ContrastDetails.ts | aaa":{"message":"AAA"},"ui/legacy/components/color_picker/ContrastDetails.ts | apca":{"message":"APCA"},"ui/legacy/components/color_picker/ContrastDetails.ts | contrastRatio":{"message":"Contrast ratio"},"ui/legacy/components/color_picker/ContrastDetails.ts | noContrastInformationAvailable":{"message":"No contrast information available"},"ui/legacy/components/color_picker/ContrastDetails.ts | pickBackgroundColor":{"message":"Pick background color"},"ui/legacy/components/color_picker/ContrastDetails.ts | placeholderWithColon":{"message":": {PH1}"},"ui/legacy/components/color_picker/ContrastDetails.ts | showLess":{"message":"Show less"},"ui/legacy/components/color_picker/ContrastDetails.ts | showMore":{"message":"Show more"},"ui/legacy/components/color_picker/ContrastDetails.ts | toggleBackgroundColorPicker":{"message":"Toggle background color picker"},"ui/legacy/components/color_picker/ContrastDetails.ts | useSuggestedColorStoFixLow":{"message":"Use suggested color {PH1}to fix low contrast"},"ui/legacy/components/color_picker/Spectrum.ts | addToPalette":{"message":"Add to palette"},"ui/legacy/components/color_picker/Spectrum.ts | changeAlpha":{"message":"Change alpha"},"ui/legacy/components/color_picker/Spectrum.ts | changeColorFormat":{"message":"Change color format"},"ui/legacy/components/color_picker/Spectrum.ts | changeHue":{"message":"Change hue"},"ui/legacy/components/color_picker/Spectrum.ts | clearPalette":{"message":"Clear palette"},"ui/legacy/components/color_picker/Spectrum.ts | colorPalettes":{"message":"Color Palettes"},"ui/legacy/components/color_picker/Spectrum.ts | colorS":{"message":"Color {PH1}"},"ui/legacy/components/color_picker/Spectrum.ts | copyColorToClipboard":{"message":"Copy color to clipboard"},"ui/legacy/components/color_picker/Spectrum.ts | hex":{"message":"HEX"},"ui/legacy/components/color_picker/Spectrum.ts | longclickOrLongpressSpaceToShow":{"message":"Long-click or long-press space to show alternate shades of {PH1}"},"ui/legacy/components/color_picker/Spectrum.ts | pressArrowKeysMessage":{"message":"Press arrow keys with or without modifiers to move swatch position. Arrow key with Shift key moves position largely, with Ctrl key it is less and with Alt key it is even less"},"ui/legacy/components/color_picker/Spectrum.ts | previewPalettes":{"message":"Preview palettes"},"ui/legacy/components/color_picker/Spectrum.ts | removeAllToTheRight":{"message":"Remove all to the right"},"ui/legacy/components/color_picker/Spectrum.ts | removeColor":{"message":"Remove color"},"ui/legacy/components/color_picker/Spectrum.ts | returnToColorPicker":{"message":"Return to color picker"},"ui/legacy/components/color_picker/Spectrum.ts | sInS":{"message":"{PH1} in {PH2}"},"ui/legacy/components/color_picker/Spectrum.ts | toggleColorPicker":{"message":"Toggle color picker"},"ui/legacy/components/cookie_table/CookiesTable.ts | cookies":{"message":"Cookies"},"ui/legacy/components/cookie_table/CookiesTable.ts | editableCookies":{"message":"Editable Cookies"},"ui/legacy/components/cookie_table/CookiesTable.ts | na":{"message":"N/A"},"ui/legacy/components/cookie_table/CookiesTable.ts | name":{"message":"Name"},"ui/legacy/components/cookie_table/CookiesTable.ts | opaquePartitionKey":{"message":"(opaque)"},"ui/legacy/components/cookie_table/CookiesTable.ts | session":{"message":"Session"},"ui/legacy/components/cookie_table/CookiesTable.ts | showIssueAssociatedWithThis":{"message":"Show issue associated with this cookie"},"ui/legacy/components/cookie_table/CookiesTable.ts | showRequestsWithThisCookie":{"message":"Show Requests With This Cookie"},"ui/legacy/components/cookie_table/CookiesTable.ts | size":{"message":"Size"},"ui/legacy/components/cookie_table/CookiesTable.ts | sourcePortTooltip":{"message":"Shows the source port (range 1-65535) the cookie was set on. If the port is unknown, this shows -1."},"ui/legacy/components/cookie_table/CookiesTable.ts | sourceSchemeTooltip":{"message":"Shows the source scheme (Secure, NonSecure) the cookie was set on. If the scheme is unknown, this shows Unset."},"ui/legacy/components/cookie_table/CookiesTable.ts | timeAfter":{"message":"after {date}"},"ui/legacy/components/cookie_table/CookiesTable.ts | timeAfterTooltip":{"message":"The expiration timestamp is {seconds}, which corresponds to a date after {date}"},"ui/legacy/components/cookie_table/CookiesTable.ts | value":{"message":"Value"},"ui/legacy/components/data_grid/DataGrid.ts | addNew":{"message":"Add new"},"ui/legacy/components/data_grid/DataGrid.ts | checked":{"message":"checked"},"ui/legacy/components/data_grid/DataGrid.ts | collapsed":{"message":"collapsed"},"ui/legacy/components/data_grid/DataGrid.ts | delete":{"message":"Delete"},"ui/legacy/components/data_grid/DataGrid.ts | editS":{"message":"Edit \\"{PH1}\\""},"ui/legacy/components/data_grid/DataGrid.ts | emptyRowCreated":{"message":"An empty table row has been created. You may double click or use context menu to edit."},"ui/legacy/components/data_grid/DataGrid.ts | expanded":{"message":"expanded"},"ui/legacy/components/data_grid/DataGrid.ts | headerOptions":{"message":"Header Options"},"ui/legacy/components/data_grid/DataGrid.ts | levelS":{"message":"level {PH1}"},"ui/legacy/components/data_grid/DataGrid.ts | refresh":{"message":"Refresh"},"ui/legacy/components/data_grid/DataGrid.ts | resetColumns":{"message":"Reset Columns"},"ui/legacy/components/data_grid/DataGrid.ts | rowsS":{"message":"Rows: {PH1}"},"ui/legacy/components/data_grid/DataGrid.ts | sortByString":{"message":"Sort By"},"ui/legacy/components/data_grid/DataGrid.ts | sRowS":{"message":"{PH1} Row {PH2}"},"ui/legacy/components/data_grid/DataGrid.ts | sSUseTheUpAndDownArrowKeysTo":{"message":"{PH1} {PH2}, use the up and down arrow keys to navigate and interact with the rows of the table; Use browse mode to read cell by cell."},"ui/legacy/components/data_grid/ShowMoreDataGridNode.ts | showAllD":{"message":"Show all {PH1}"},"ui/legacy/components/data_grid/ShowMoreDataGridNode.ts | showDAfter":{"message":"Show {PH1} after"},"ui/legacy/components/data_grid/ShowMoreDataGridNode.ts | showDBefore":{"message":"Show {PH1} before"},"ui/legacy/components/data_grid/ViewportDataGrid.ts | collapsed":{"message":"collapsed"},"ui/legacy/components/inline_editor/ColorSwatch.ts | shiftclickToChangeColorFormat":{"message":"Shift-click to change color format"},"ui/legacy/components/inline_editor/CSSShadowEditor.ts | blur":{"message":"Blur"},"ui/legacy/components/inline_editor/CSSShadowEditor.ts | spread":{"message":"Spread"},"ui/legacy/components/inline_editor/CSSShadowEditor.ts | type":{"message":"Type"},"ui/legacy/components/inline_editor/CSSShadowEditor.ts | xOffset":{"message":"X offset"},"ui/legacy/components/inline_editor/CSSShadowEditor.ts | yOffset":{"message":"Y offset"},"ui/legacy/components/inline_editor/CSSVarSwatch.ts | sIsNotDefined":{"message":"{PH1} is not defined"},"ui/legacy/components/inline_editor/FontEditor.ts | cssProperties":{"message":"CSS Properties"},"ui/legacy/components/inline_editor/FontEditor.ts | deleteS":{"message":"Delete {PH1}"},"ui/legacy/components/inline_editor/FontEditor.ts | fallbackS":{"message":"Fallback {PH1}"},"ui/legacy/components/inline_editor/FontEditor.ts | fontFamily":{"message":"Font Family"},"ui/legacy/components/inline_editor/FontEditor.ts | fontSelectorDeletedAtIndexS":{"message":"Font Selector deleted at index: {PH1}"},"ui/legacy/components/inline_editor/FontEditor.ts | fontSize":{"message":"Font Size"},"ui/legacy/components/inline_editor/FontEditor.ts | fontWeight":{"message":"Font Weight"},"ui/legacy/components/inline_editor/FontEditor.ts | lineHeight":{"message":"Line Height"},"ui/legacy/components/inline_editor/FontEditor.ts | PleaseEnterAValidValueForSText":{"message":"* Please enter a valid value for {PH1} text input"},"ui/legacy/components/inline_editor/FontEditor.ts | selectorInputMode":{"message":"Selector Input Mode"},"ui/legacy/components/inline_editor/FontEditor.ts | sKeyValueSelector":{"message":"{PH1} Key Value Selector"},"ui/legacy/components/inline_editor/FontEditor.ts | sliderInputMode":{"message":"Slider Input Mode"},"ui/legacy/components/inline_editor/FontEditor.ts | spacing":{"message":"Spacing"},"ui/legacy/components/inline_editor/FontEditor.ts | sSliderInput":{"message":"{PH1} Slider Input"},"ui/legacy/components/inline_editor/FontEditor.ts | sTextInput":{"message":"{PH1} Text Input"},"ui/legacy/components/inline_editor/FontEditor.ts | sToggleInputType":{"message":"{PH1} toggle input type"},"ui/legacy/components/inline_editor/FontEditor.ts | sUnitInput":{"message":"{PH1} Unit Input"},"ui/legacy/components/inline_editor/FontEditor.ts | thereIsNoValueToDeleteAtIndexS":{"message":"There is no value to delete at index: {PH1}"},"ui/legacy/components/inline_editor/FontEditor.ts | thisPropertyIsSetToContainUnits":{"message":"This property is set to contain units but does not have a defined corresponding unitsArray: {PH1}"},"ui/legacy/components/inline_editor/FontEditor.ts | units":{"message":"Units"},"ui/legacy/components/object_ui/CustomPreviewComponent.ts | showAsJavascriptObject":{"message":"Show as JavaScript object"},"ui/legacy/components/object_ui/ObjectPropertiesSection.ts | collapseChildren":{"message":"Collapse children"},"ui/legacy/components/object_ui/ObjectPropertiesSection.ts | copy":{"message":"Copy"},"ui/legacy/components/object_ui/ObjectPropertiesSection.ts | copyPropertyPath":{"message":"Copy property path"},"ui/legacy/components/object_ui/ObjectPropertiesSection.ts | copyValue":{"message":"Copy value"},"ui/legacy/components/object_ui/ObjectPropertiesSection.ts | dots":{"message":"(...)"},"ui/legacy/components/object_ui/ObjectPropertiesSection.ts | exceptionS":{"message":"[Exception: {PH1}]"},"ui/legacy/components/object_ui/ObjectPropertiesSection.ts | expandRecursively":{"message":"Expand recursively"},"ui/legacy/components/object_ui/ObjectPropertiesSection.ts | invokePropertyGetter":{"message":"Invoke property getter"},"ui/legacy/components/object_ui/ObjectPropertiesSection.ts | longTextWasTruncatedS":{"message":"long text was truncated ({PH1})"},"ui/legacy/components/object_ui/ObjectPropertiesSection.ts | noProperties":{"message":"No properties"},"ui/legacy/components/object_ui/ObjectPropertiesSection.ts | noPropertyGetter":{"message":"No property getter"},"ui/legacy/components/object_ui/ObjectPropertiesSection.ts | showAllD":{"message":"Show all {PH1}"},"ui/legacy/components/object_ui/ObjectPropertiesSection.ts | showMoreS":{"message":"Show more ({PH1})"},"ui/legacy/components/object_ui/ObjectPropertiesSection.ts | stringIsTooLargeToEdit":{"message":"<string is too large to edit>"},"ui/legacy/components/object_ui/ObjectPropertiesSection.ts | unknown":{"message":"unknown"},"ui/legacy/components/object_ui/ObjectPropertiesSection.ts | unreadable":{"message":"<unreadable>"},"ui/legacy/components/object_ui/RemoteObjectPreviewFormatter.ts | empty":{"message":"empty"},"ui/legacy/components/object_ui/RemoteObjectPreviewFormatter.ts | emptyD":{"message":"empty × {PH1}"},"ui/legacy/components/object_ui/RemoteObjectPreviewFormatter.ts | thePropertyIsComputedWithAGetter":{"message":"The property is computed with a getter"},"ui/legacy/components/perf_ui/FilmStripView.ts | doubleclickToZoomImageClickTo":{"message":"Doubleclick to zoom image. Click to view preceding requests."},"ui/legacy/components/perf_ui/FilmStripView.ts | nextFrame":{"message":"Next frame"},"ui/legacy/components/perf_ui/FilmStripView.ts | previousFrame":{"message":"Previous frame"},"ui/legacy/components/perf_ui/FilmStripView.ts | screenshot":{"message":"Screenshot"},"ui/legacy/components/perf_ui/FilmStripView.ts | screenshotForSSelectToView":{"message":"Screenshot for {PH1} - select to view preceding requests."},"ui/legacy/components/perf_ui/FlameChart.ts | flameChart":{"message":"Flame Chart"},"ui/legacy/components/perf_ui/FlameChart.ts | sCollapsed":{"message":"{PH1} collapsed"},"ui/legacy/components/perf_ui/FlameChart.ts | sExpanded":{"message":"{PH1} expanded"},"ui/legacy/components/perf_ui/FlameChart.ts | sHovered":{"message":"{PH1} hovered"},"ui/legacy/components/perf_ui/FlameChart.ts | sSelected":{"message":"{PH1} selected"},"ui/legacy/components/perf_ui/NetworkPriorities.ts | high":{"message":"High"},"ui/legacy/components/perf_ui/NetworkPriorities.ts | highest":{"message":"Highest"},"ui/legacy/components/perf_ui/NetworkPriorities.ts | low":{"message":"Low"},"ui/legacy/components/perf_ui/NetworkPriorities.ts | lowest":{"message":"Lowest"},"ui/legacy/components/perf_ui/NetworkPriorities.ts | medium":{"message":"Medium"},"ui/legacy/components/perf_ui/OverviewGrid.ts | leftResizer":{"message":"Left Resizer"},"ui/legacy/components/perf_ui/OverviewGrid.ts | overviewGridWindow":{"message":"Overview grid window"},"ui/legacy/components/perf_ui/OverviewGrid.ts | rightResizer":{"message":"Right Resizer"},"ui/legacy/components/perf_ui/perf_ui-meta.ts | collectGarbage":{"message":"Collect garbage"},"ui/legacy/components/perf_ui/perf_ui-meta.ts | flamechartMouseWheelAction":{"message":"Flamechart mouse wheel action:"},"ui/legacy/components/perf_ui/perf_ui-meta.ts | hideLiveMemoryAllocation":{"message":"Hide live memory allocation annotations"},"ui/legacy/components/perf_ui/perf_ui-meta.ts | liveMemoryAllocationAnnotations":{"message":"Live memory allocation annotations"},"ui/legacy/components/perf_ui/perf_ui-meta.ts | scroll":{"message":"Scroll"},"ui/legacy/components/perf_ui/perf_ui-meta.ts | showLiveMemoryAllocation":{"message":"Show live memory allocation annotations"},"ui/legacy/components/perf_ui/perf_ui-meta.ts | zoom":{"message":"Zoom"},"ui/legacy/components/perf_ui/PieChart.ts | total":{"message":"Total"},"ui/legacy/components/quick_open/CommandMenu.ts | command":{"message":"Command"},"ui/legacy/components/quick_open/CommandMenu.ts | noCommandsFound":{"message":"No commands found"},"ui/legacy/components/quick_open/CommandMenu.ts | oneOrMoreSettingsHaveChanged":{"message":"One or more settings have changed which requires a reload to take effect."},"ui/legacy/components/quick_open/CommandMenu.ts | run":{"message":"Run"},"ui/legacy/components/quick_open/FilteredListWidget.ts | noResultsFound":{"message":"No results found"},"ui/legacy/components/quick_open/FilteredListWidget.ts | quickOpen":{"message":"Quick open"},"ui/legacy/components/quick_open/FilteredListWidget.ts | quickOpenPrompt":{"message":"Quick open prompt"},"ui/legacy/components/quick_open/quick_open-meta.ts | openFile":{"message":"Open file"},"ui/legacy/components/quick_open/quick_open-meta.ts | runCommand":{"message":"Run command"},"ui/legacy/components/quick_open/QuickInput.ts | pressEnterToConfirmOrEscapeTo":{"message":"{PH1} (Press \'Enter\' to confirm or \'Escape\' to cancel.)"},"ui/legacy/components/quick_open/QuickOpen.ts | typeToSeeAvailableCommands":{"message":"Type \'?\' to see available commands"},"ui/legacy/components/source_frame/FontView.ts | font":{"message":"Font"},"ui/legacy/components/source_frame/FontView.ts | previewOfFontFromS":{"message":"Preview of font from {PH1}"},"ui/legacy/components/source_frame/ImageView.ts | copyImageAsDataUri":{"message":"Copy image as data URI"},"ui/legacy/components/source_frame/ImageView.ts | copyImageUrl":{"message":"Copy image URL"},"ui/legacy/components/source_frame/ImageView.ts | dD":{"message":"{PH1} × {PH2}"},"ui/legacy/components/source_frame/ImageView.ts | download":{"message":"download"},"ui/legacy/components/source_frame/ImageView.ts | dropImageFileHere":{"message":"Drop image file here"},"ui/legacy/components/source_frame/ImageView.ts | image":{"message":"Image"},"ui/legacy/components/source_frame/ImageView.ts | imageFromS":{"message":"Image from {PH1}"},"ui/legacy/components/source_frame/ImageView.ts | openImageInNewTab":{"message":"Open image in new tab"},"ui/legacy/components/source_frame/ImageView.ts | saveImageAs":{"message":"Save image as..."},"ui/legacy/components/source_frame/JSONView.ts | find":{"message":"Find"},"ui/legacy/components/source_frame/PreviewFactory.ts | nothingToPreview":{"message":"Nothing to preview"},"ui/legacy/components/source_frame/ResourceSourceFrame.ts | find":{"message":"Find"},"ui/legacy/components/source_frame/source_frame-meta.ts | defaultIndentation":{"message":"Default indentation:"},"ui/legacy/components/source_frame/source_frame-meta.ts | eSpaces":{"message":"8 spaces"},"ui/legacy/components/source_frame/source_frame-meta.ts | fSpaces":{"message":"4 spaces"},"ui/legacy/components/source_frame/source_frame-meta.ts | setIndentationToESpaces":{"message":"Set indentation to 8 spaces"},"ui/legacy/components/source_frame/source_frame-meta.ts | setIndentationToFSpaces":{"message":"Set indentation to 4 spaces"},"ui/legacy/components/source_frame/source_frame-meta.ts | setIndentationToSpaces":{"message":"Set indentation to 2 spaces"},"ui/legacy/components/source_frame/source_frame-meta.ts | setIndentationToTabCharacter":{"message":"Set indentation to tab character"},"ui/legacy/components/source_frame/source_frame-meta.ts | Spaces":{"message":"2 spaces"},"ui/legacy/components/source_frame/source_frame-meta.ts | tabCharacter":{"message":"Tab character"},"ui/legacy/components/source_frame/SourceFrame.ts | bytecodePositionXs":{"message":"Bytecode position 0x{PH1}"},"ui/legacy/components/source_frame/SourceFrame.ts | dCharactersSelected":{"message":"{PH1} characters selected"},"ui/legacy/components/source_frame/SourceFrame.ts | dLinesDCharactersSelected":{"message":"{PH1} lines, {PH2} characters selected"},"ui/legacy/components/source_frame/SourceFrame.ts | dSelectionRegions":{"message":"{PH1} selection regions"},"ui/legacy/components/source_frame/SourceFrame.ts | lineSColumnS":{"message":"Line {PH1}, Column {PH2}"},"ui/legacy/components/source_frame/SourceFrame.ts | loading":{"message":"Loading…"},"ui/legacy/components/source_frame/SourceFrame.ts | prettyPrint":{"message":"Pretty print"},"ui/legacy/components/source_frame/SourceFrame.ts | source":{"message":"Source"},"ui/legacy/components/source_frame/XMLView.ts | find":{"message":"Find"},"ui/legacy/components/utils/ImagePreview.ts | currentSource":{"message":"Current source:"},"ui/legacy/components/utils/ImagePreview.ts | fileSize":{"message":"File size:"},"ui/legacy/components/utils/ImagePreview.ts | imageFromS":{"message":"Image from {PH1}"},"ui/legacy/components/utils/ImagePreview.ts | intrinsicAspectRatio":{"message":"Intrinsic aspect ratio:"},"ui/legacy/components/utils/ImagePreview.ts | intrinsicSize":{"message":"Intrinsic size:"},"ui/legacy/components/utils/ImagePreview.ts | renderedAspectRatio":{"message":"Rendered aspect ratio:"},"ui/legacy/components/utils/ImagePreview.ts | renderedSize":{"message":"Rendered size:"},"ui/legacy/components/utils/ImagePreview.ts | unknownSource":{"message":"unknown source"},"ui/legacy/components/utils/JSPresentationUtils.ts | addToIgnore":{"message":"Add script to ignore list"},"ui/legacy/components/utils/JSPresentationUtils.ts | removeFromIgnore":{"message":"Remove from ignore list"},"ui/legacy/components/utils/JSPresentationUtils.ts | showSMoreFrames":{"message":"{n, plural, =1 {Show # more frame} other {Show # more frames}}"},"ui/legacy/components/utils/JSPresentationUtils.ts | unknownSource":{"message":"unknown"},"ui/legacy/components/utils/Linkifier.ts | auto":{"message":"auto"},"ui/legacy/components/utils/Linkifier.ts | linkHandling":{"message":"Link handling:"},"ui/legacy/components/utils/Linkifier.ts | openUsingS":{"message":"Open using {PH1}"},"ui/legacy/components/utils/Linkifier.ts | reveal":{"message":"Reveal"},"ui/legacy/components/utils/Linkifier.ts | revealInS":{"message":"Reveal in {PH1}"},"ui/legacy/components/utils/Linkifier.ts | unknown":{"message":"(unknown)"},"ui/legacy/components/utils/TargetDetachedDialog.ts | websocketDisconnected":{"message":"WebSocket disconnected"},"ui/legacy/DockController.ts | close":{"message":"Close"},"ui/legacy/DockController.ts | dockToBottom":{"message":"Dock to bottom"},"ui/legacy/DockController.ts | dockToLeft":{"message":"Dock to left"},"ui/legacy/DockController.ts | dockToRight":{"message":"Dock to right"},"ui/legacy/DockController.ts | undockIntoSeparateWindow":{"message":"Undock into separate window"},"ui/legacy/EmptyWidget.ts | learnMore":{"message":"Learn more"},"ui/legacy/FilterBar.ts | allStrings":{"message":"All"},"ui/legacy/FilterBar.ts | clearFilter":{"message":"Clear input"},"ui/legacy/FilterBar.ts | egSmalldUrlacomb":{"message":"e.g. /small[d]+/ url:a.com/b"},"ui/legacy/FilterBar.ts | filter":{"message":"Filter"},"ui/legacy/FilterBar.ts | sclickToSelectMultipleTypes":{"message":"{PH1}Click to select multiple types"},"ui/legacy/Infobar.ts | close":{"message":"Close"},"ui/legacy/Infobar.ts | dontShowAgain":{"message":"Don\'t show again"},"ui/legacy/Infobar.ts | learnMore":{"message":"Learn more"},"ui/legacy/InspectorView.ts | closeDrawer":{"message":"Close drawer"},"ui/legacy/InspectorView.ts | devToolsLanguageMissmatch":{"message":"DevTools is now available in {PH1}!"},"ui/legacy/InspectorView.ts | drawer":{"message":"Tool drawer"},"ui/legacy/InspectorView.ts | drawerHidden":{"message":"Drawer hidden"},"ui/legacy/InspectorView.ts | drawerShown":{"message":"Drawer shown"},"ui/legacy/InspectorView.ts | mainToolbar":{"message":"Main toolbar"},"ui/legacy/InspectorView.ts | moreTools":{"message":"More Tools"},"ui/legacy/InspectorView.ts | moveToBottom":{"message":"Move to bottom"},"ui/legacy/InspectorView.ts | moveToTop":{"message":"Move to top"},"ui/legacy/InspectorView.ts | panels":{"message":"Panels"},"ui/legacy/InspectorView.ts | reloadDevtools":{"message":"Reload DevTools"},"ui/legacy/InspectorView.ts | setToBrowserLanguage":{"message":"Always match Chrome\'s language"},"ui/legacy/InspectorView.ts | setToSpecificLanguage":{"message":"Switch DevTools to {PH1}"},"ui/legacy/ListWidget.ts | addString":{"message":"Add"},"ui/legacy/ListWidget.ts | cancelString":{"message":"Cancel"},"ui/legacy/ListWidget.ts | editString":{"message":"Edit"},"ui/legacy/ListWidget.ts | removeString":{"message":"Remove"},"ui/legacy/ListWidget.ts | saveString":{"message":"Save"},"ui/legacy/RemoteDebuggingTerminatedScreen.ts | debuggingConnectionWasClosed":{"message":"Debugging connection was closed. Reason: "},"ui/legacy/RemoteDebuggingTerminatedScreen.ts | reconnectDevtools":{"message":"Reconnect DevTools"},"ui/legacy/RemoteDebuggingTerminatedScreen.ts | reconnectWhenReadyByReopening":{"message":"Reconnect when ready by reopening DevTools."},"ui/legacy/SearchableView.ts | cancel":{"message":"Cancel"},"ui/legacy/SearchableView.ts | dMatches":{"message":"{PH1} matches"},"ui/legacy/SearchableView.ts | dOfD":{"message":"{PH1} of {PH2}"},"ui/legacy/SearchableView.ts | findString":{"message":"Find"},"ui/legacy/SearchableView.ts | matchCase":{"message":"Match Case"},"ui/legacy/SearchableView.ts | matchString":{"message":"1 match"},"ui/legacy/SearchableView.ts | replace":{"message":"Replace"},"ui/legacy/SearchableView.ts | replaceAll":{"message":"Replace all"},"ui/legacy/SearchableView.ts | searchNext":{"message":"Search next"},"ui/legacy/SearchableView.ts | searchPrevious":{"message":"Search previous"},"ui/legacy/SearchableView.ts | useRegularExpression":{"message":"Use Regular Expression"},"ui/legacy/SettingsUI.ts | oneOrMoreSettingsHaveChanged":{"message":"One or more settings have changed which requires a reload to take effect."},"ui/legacy/SettingsUI.ts | srequiresReload":{"message":"*Requires reload"},"ui/legacy/SoftContextMenu.ts | checked":{"message":"checked"},"ui/legacy/SoftContextMenu.ts | sS":{"message":"{PH1}, {PH2}"},"ui/legacy/SoftContextMenu.ts | sSS":{"message":"{PH1}, {PH2}, {PH3}"},"ui/legacy/SoftContextMenu.ts | unchecked":{"message":"unchecked"},"ui/legacy/SoftDropDown.ts | noItemSelected":{"message":"(no item selected)"},"ui/legacy/SuggestBox.ts | sSuggestionSOfS":{"message":"{PH1}, suggestion {PH2} of {PH3}"},"ui/legacy/SuggestBox.ts | sSuggestionSSelected":{"message":"{PH1}, suggestion selected"},"ui/legacy/TabbedPane.ts | close":{"message":"Close"},"ui/legacy/TabbedPane.ts | closeAll":{"message":"Close all"},"ui/legacy/TabbedPane.ts | closeOthers":{"message":"Close others"},"ui/legacy/TabbedPane.ts | closeS":{"message":"Close {PH1}"},"ui/legacy/TabbedPane.ts | closeTabsToTheRight":{"message":"Close tabs to the right"},"ui/legacy/TabbedPane.ts | moreTabs":{"message":"More tabs"},"ui/legacy/TabbedPane.ts | previewFeature":{"message":"Preview feature"},"ui/legacy/TargetCrashedScreen.ts | devtoolsWasDisconnectedFromThe":{"message":"DevTools was disconnected from the page."},"ui/legacy/TargetCrashedScreen.ts | oncePageIsReloadedDevtoolsWill":{"message":"Once page is reloaded, DevTools will automatically reconnect."},"ui/legacy/Toolbar.ts | notPressed":{"message":"not pressed"},"ui/legacy/Toolbar.ts | pressed":{"message":"pressed"},"ui/legacy/UIUtils.ts | anonymous":{"message":"(anonymous)"},"ui/legacy/UIUtils.ts | anotherProfilerIsAlreadyActive":{"message":"Another profiler is already active"},"ui/legacy/UIUtils.ts | asyncCall":{"message":"Async Call"},"ui/legacy/UIUtils.ts | cancel":{"message":"Cancel"},"ui/legacy/UIUtils.ts | close":{"message":"Close"},"ui/legacy/UIUtils.ts | copyFileName":{"message":"Copy file name"},"ui/legacy/UIUtils.ts | copyLinkAddress":{"message":"Copy link address"},"ui/legacy/UIUtils.ts | ok":{"message":"OK"},"ui/legacy/UIUtils.ts | openInNewTab":{"message":"Open in new tab"},"ui/legacy/UIUtils.ts | promiseRejectedAsync":{"message":"Promise rejected (async)"},"ui/legacy/UIUtils.ts | promiseResolvedAsync":{"message":"Promise resolved (async)"},"ui/legacy/UIUtils.ts | sAsync":{"message":"{PH1} (async)"},"ui/legacy/ViewManager.ts | sPanel":{"message":"{PH1} panel"}}')}));
//# sourceMappingURL=en-US.28c3c6bb.js.map
