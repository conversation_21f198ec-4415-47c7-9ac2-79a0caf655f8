function e(e,t,r,i){Object.defineProperty(e,t,{get:r,set:i,enumerable:!0,configurable:!0})}function t(e){return e&&e.__esModule?e.default:e}var r=("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}).parcelRequire20b5;r.register("1vowh",(function(t,i){e(t.exports,"BinaryResourceView",(()=>r("kYHDV"))),e(t.exports,"BlockedURLsPane",(()=>r("eptiJ"))),e(t.exports,"EventSourceMessagesView",(()=>r("6zKhV"))),e(t.exports,"NetworkConfigView",(()=>r("6ebTa"))),e(t.exports,"NetworkDataGridNode",(()=>r("9kyN0"))),e(t.exports,"NetworkFrameGrouper",(()=>r("k5iKk"))),e(t.exports,"NetworkItemView",(()=>r("l7XL4"))),e(t.exports,"NetworkLogView",(()=>r("h5pc5"))),e(t.exports,"NetworkLogViewColumns",(()=>r("dRcPb"))),e(t.exports,"NetworkManageCustomHeadersView",(()=>r("gGxKN"))),e(t.exports,"NetworkOverview",(()=>r("3ZLtp"))),e(t.exports,"NetworkPanel",(()=>r("4yJHA"))),e(t.exports,"NetworkSearchScope",(()=>r("3juS1"))),e(t.exports,"NetworkTimeCalculator",(()=>r("kBxCb"))),e(t.exports,"NetworkWaterfallColumn",(()=>r("km5qV"))),e(t.exports,"RequestCookiesView",(()=>r("fCddz"))),e(t.exports,"RequestHeadersView",(()=>r("kmtft"))),e(t.exports,"RequestHTMLView",(()=>r("gwA6q"))),e(t.exports,"RequestInitiatorView",(()=>r("ej1uS"))),e(t.exports,"RequestPayloadView",(()=>r("amGvi"))),e(t.exports,"RequestPreviewView",(()=>r("dDrZy"))),e(t.exports,"RequestResponseView",(()=>r("kt9Ih"))),e(t.exports,"RequestTimingView",(()=>r("gpw7U"))),e(t.exports,"ResourceWebSocketFrameView",(()=>r("g1wr2"))),e(t.exports,"SignedExchangeInfoView",(()=>r("7XVA7")));r("kYHDV"),r("eptiJ"),r("6zKhV"),r("6ebTa"),r("9kyN0"),r("l7XL4"),r("kBxCb"),r("h5pc5"),r("dRcPb"),r("k5iKk"),r("gGxKN"),r("3juS1"),r("km5qV"),r("fCddz"),r("kmtft"),r("gwA6q"),r("ej1uS"),r("kt9Ih"),r("dDrZy"),r("gpw7U"),r("g1wr2"),r("7XVA7"),r("3ZLtp"),r("4yJHA"),r("kYHDV"),r("eptiJ"),r("6zKhV"),r("6ebTa"),r("9kyN0"),r("k5iKk"),r("l7XL4"),r("h5pc5"),r("dRcPb"),r("gGxKN"),r("3ZLtp"),r("4yJHA"),r("3juS1"),r("kBxCb"),r("km5qV"),r("fCddz"),r("kmtft"),r("gwA6q"),r("ej1uS"),r("amGvi"),r("dDrZy"),r("kt9Ih"),r("gpw7U"),r("g1wr2"),r("7XVA7")})),r.register("kYHDV",(function(t,i){e(t.exports,"BinaryResourceView",(()=>p)),e(t.exports,"BinaryViewObject",(()=>g));var n=r("koSS8"),o=r("e7bLS"),s=r("ixFnt"),a=r("3Efl2"),l=r("5adRV"),d=r("9z2ZV");const c={copiedAsBase:"Copied as `Base64`",hexViewer:"`Hex` Viewer",copiedAsHex:"Copied as `Hex`",copiedAsUtf:"Copied as `UTF-8`",binaryViewType:"Binary view type",copyToClipboard:"Copy to clipboard",copyAsBase:"Copy as `Base64`",copyAsHex:"Copy as `Hex`",copyAsUtf:"Copy as `UTF-8`"},h=s.i18n.registerUIStrings("panels/network/BinaryResourceView.ts",c),u=s.i18n.getLocalizedString.bind(void 0,h);class p extends d.Widget.VBox{binaryResourceViewFactory;toolbar;binaryViewObjects;binaryViewTypeSetting;binaryViewTypeCombobox;copiedText;addFadeoutSettimeoutId;lastView;constructor(e,t,r){super(),this.binaryResourceViewFactory=new l.BinaryResourceViewFactory.BinaryResourceViewFactory(e,t,r),this.toolbar=new d.Toolbar.Toolbar("binary-view-toolbar",this.element),this.binaryViewObjects=[new g("base64",s.i18n.lockedString("Base64"),u(c.copiedAsBase),this.binaryResourceViewFactory.createBase64View.bind(this.binaryResourceViewFactory),this.binaryResourceViewFactory.base64.bind(this.binaryResourceViewFactory)),new g("hex",u(c.hexViewer),u(c.copiedAsHex),this.binaryResourceViewFactory.createHexView.bind(this.binaryResourceViewFactory),this.binaryResourceViewFactory.hex.bind(this.binaryResourceViewFactory)),new g("utf8",s.i18n.lockedString("UTF-8"),u(c.copiedAsUtf),this.binaryResourceViewFactory.createUtf8View.bind(this.binaryResourceViewFactory),this.binaryResourceViewFactory.utf8.bind(this.binaryResourceViewFactory))],this.binaryViewTypeSetting=n.Settings.Settings.instance().createSetting("binaryViewType","hex"),this.binaryViewTypeCombobox=new d.Toolbar.ToolbarComboBox(this.binaryViewTypeChanged.bind(this),u(c.binaryViewType));for(const e of this.binaryViewObjects)this.binaryViewTypeCombobox.addOption(this.binaryViewTypeCombobox.createOption(e.label,e.type));this.toolbar.appendToolbarItem(this.binaryViewTypeCombobox);const i=new d.Toolbar.ToolbarButton(u(c.copyToClipboard),"largeicon-copy");i.addEventListener(d.Toolbar.ToolbarButton.Events.Click,(e=>{this.copySelectedViewToClipboard()}),this),this.toolbar.appendToolbarItem(i),this.copiedText=new d.Toolbar.ToolbarText,this.copiedText.element.classList.add("binary-view-copied-text"),this.toolbar.element.appendChild(this.copiedText.element),this.addFadeoutSettimeoutId=null,this.lastView=null,this.updateView()}getCurrentViewObject(){const e=this.binaryViewObjects.find((e=>e.type===this.binaryViewTypeSetting.get()));return console.assert(Boolean(e),`No binary view found for binary view type found in setting 'binaryViewType': ${this.binaryViewTypeSetting.get()}`),e||null}async copySelectedViewToClipboard(){const e=this.getCurrentViewObject();e&&(o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText((await e.content()).content),this.copiedText.setText(e.copiedMessage),this.copiedText.element.classList.remove("fadeout"),this.addFadeoutSettimeoutId&&(clearTimeout(this.addFadeoutSettimeoutId),this.addFadeoutSettimeoutId=null),this.addFadeoutSettimeoutId=window.setTimeout(function(){this.copiedText.element.classList.add("fadeout")}.bind(this),2e3))}wasShown(){this.updateView(),this.registerCSSFiles([a.default])}updateView(){const e=this.getCurrentViewObject();if(!e)return;const t=e.getView();t!==this.lastView&&(this.lastView&&this.lastView.detach(),this.lastView=t,t.show(this.element,this.toolbar.element),this.binaryViewTypeCombobox.selectElement().value=this.binaryViewTypeSetting.get())}binaryViewTypeChanged(){const e=this.binaryViewTypeCombobox.selectedOption();if(!e)return;const t=e.value;this.binaryViewTypeSetting.get()!==t&&(this.binaryViewTypeSetting.set(t),this.updateView())}addCopyToContextMenu(e,t){const r=e.clipboardSection().appendSubMenuItem(t).footerSection();r.appendItem(u(c.copyAsBase),(async()=>{const e=await this.binaryResourceViewFactory.base64();o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e.content)})),r.appendItem(u(c.copyAsHex),(async()=>{const e=await this.binaryResourceViewFactory.hex();o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e.content)})),r.appendItem(u(c.copyAsUtf),(async()=>{const e=await this.binaryResourceViewFactory.utf8();o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e.content)}))}}class g{type;label;copiedMessage;content;createViewFn;view;constructor(e,t,r,i,n){this.type=e,this.label=t,this.copiedMessage=r,this.content=n,this.createViewFn=i,this.view=null}getView(){return this.view||(this.view=this.createViewFn()),this.view}}})),r.register("3Efl2",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2019 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.panel.network .toolbar.binary-view-toolbar {\n  border-top: 1px solid var(--color-details-hairline);\n  border-bottom: 0;\n  padding-left: 5px;\n}\n\n.binary-view-copied-text {\n  opacity: 100%;\n}\n\n.binary-view-copied-text.fadeout {\n  opacity: 0%;\n  transition: opacity 1s;\n}\n\n/*# sourceURL=binaryResourceView.css */\n");var n=i})),r.register("eptiJ",(function(t,i){e(t.exports,"blockedURLsPaneInstance",(()=>u)),e(t.exports,"BlockedURLsPane",(()=>p));var n=r("koSS8"),o=r("ixFnt"),s=r("eQFvP"),a=r("9z2ZV"),l=r("acxU2");const d={enableNetworkRequestBlocking:"Enable network request blocking",addPattern:"Add pattern",removeAllPatterns:"Remove all patterns",addNetworkRequestBlockingPattern:"Add network request blocking pattern",networkRequestsAreNotBlockedS:"Network requests are not blocked. {PH1}",dBlocked:"{PH1} blocked",textPatternToBlockMatching:"Text pattern to block matching requests; use * for wildcard",patternInputCannotBeEmpty:"Pattern input cannot be empty.",patternAlreadyExists:"Pattern already exists.",itemDeleted:"Item successfully deleted"},c=o.i18n.registerUIStrings("panels/network/BlockedURLsPane.ts",d),h=o.i18n.getLocalizedString.bind(void 0,c);let u=null;class p extends a.Widget.VBox{manager;toolbar;enabledCheckbox;list;editor;blockedCountForUrl;updateThrottler;constructor(){super(!0),this.manager=s.NetworkManager.MultitargetNetworkManager.instance(),this.manager.addEventListener(s.NetworkManager.MultitargetNetworkManager.Events.BlockedPatternsChanged,(()=>{this.update()}),this),this.toolbar=new a.Toolbar.Toolbar("",this.contentElement),this.enabledCheckbox=new a.Toolbar.ToolbarCheckbox(h(d.enableNetworkRequestBlocking),void 0,this.toggleEnabled.bind(this)),this.toolbar.appendToolbarItem(this.enabledCheckbox),this.toolbar.appendSeparator();const e=new a.Toolbar.ToolbarButton(h(d.addPattern),"largeicon-add");e.addEventListener(a.Toolbar.ToolbarButton.Events.Click,this.addButtonClicked,this),this.toolbar.appendToolbarItem(e);const t=new a.Toolbar.ToolbarButton(h(d.removeAllPatterns),"largeicon-clear");t.addEventListener(a.Toolbar.ToolbarButton.Events.Click,this.removeAll,this),this.toolbar.appendToolbarItem(t),this.list=new a.ListWidget.ListWidget(this),this.list.element.classList.add("blocked-urls"),this.list.setEmptyPlaceholder(this.createEmptyPlaceholder()),this.list.show(this.contentElement),this.editor=null,this.blockedCountForUrl=new Map,s.TargetManager.TargetManager.instance().addModelListener(s.NetworkManager.NetworkManager,s.NetworkManager.Events.RequestFinished,this.onRequestFinished,this),this.updateThrottler=new n.Throttler.Throttler(200),this.update()}static instance(e={forceNew:null}){const{forceNew:t}=e;return u&&!t||(u=new p),u}createEmptyPlaceholder(){const e=this.contentElement.createChild("div","no-blocked-urls"),t=a.UIUtils.createTextButton(h(d.addPattern),this.addButtonClicked.bind(this),"add-button");return a.ARIAUtils.setAccessibleName(t,h(d.addNetworkRequestBlockingPattern)),e.appendChild(o.i18n.getFormatLocalizedString(c,d.networkRequestsAreNotBlockedS,{PH1:t})),e}static reset(){u&&u.reset()}addButtonClicked(){this.manager.setBlockingEnabled(!0),this.list.addNewItem(0,{url:"",enabled:!0})}renderItem(e,t){const r=this.blockedRequestsCount(e.url),i=document.createElement("div");i.classList.add("blocked-url");const n=i.createChild("input","blocked-url-checkbox");return n.type="checkbox",n.checked=e.enabled,n.disabled=!t,i.createChild("div","blocked-url-label").textContent=e.url,i.createChild("div","blocked-url-count").textContent=h(d.dBlocked,{PH1:r}),t&&(i.addEventListener("click",(t=>this.togglePattern(e,t))),n.addEventListener("click",(t=>this.togglePattern(e,t)))),i}togglePattern(e,t){t.consume(!0);const r=this.manager.blockedPatterns();r.splice(r.indexOf(e),1,{enabled:!e.enabled,url:e.url}),this.manager.setBlockedPatterns(r)}toggleEnabled(){this.manager.setBlockingEnabled(!this.manager.blockingEnabled()),this.update()}removeItemRequested(e,t){const r=this.manager.blockedPatterns();r.splice(t,1),this.manager.setBlockedPatterns(r),a.ARIAUtils.alert(d.itemDeleted)}beginEdit(e){return this.editor=this.createEditor(),this.editor.control("url").value=e.url,this.editor}commitEdit(e,t,r){const i=t.control("url").value,n=this.manager.blockedPatterns();r?n.push({enabled:!0,url:i}):n.splice(n.indexOf(e),1,{enabled:!0,url:i}),this.manager.setBlockedPatterns(n)}createEditor(){if(this.editor)return this.editor;const e=new a.ListWidget.Editor,t=e.contentElement();t.createChild("div","blocked-url-edit-row").createChild("div").textContent=h(d.textPatternToBlockMatching);const r=t.createChild("div","blocked-url-edit-row"),i=e.createInput("url","text","",((e,t,r)=>{let i,n=!0;return r.value?this.manager.blockedPatterns().find((e=>e.url===r.value))&&(i=h(d.patternAlreadyExists),n=!1):(i=h(d.patternInputCannotBeEmpty),n=!1),{valid:n,errorMessage:i}}));return r.createChild("div","blocked-url-edit-value").appendChild(i),e}removeAll(){this.manager.setBlockedPatterns([])}update(){const e=this.manager.blockingEnabled();this.list.element.classList.toggle("blocking-disabled",!e&&Boolean(this.manager.blockedPatterns().length)),this.enabledCheckbox.setChecked(e),this.list.clear();for(const t of this.manager.blockedPatterns())this.list.appendItem(t,e);return Promise.resolve()}blockedRequestsCount(e){if(!e)return 0;let t=0;for(const r of this.blockedCountForUrl.keys())this.matches(e,r)&&(t+=this.blockedCountForUrl.get(r));return t}matches(e,t){let r=0;const i=e.split("*");for(let e=0;e<i.length;e++){const n=i[e];if(n.length){if(r=t.indexOf(n,r),-1===r)return!1;r+=n.length}}return!0}reset(){this.blockedCountForUrl.clear(),this.updateThrottler.schedule(this.update.bind(this))}onRequestFinished(e){const t=e.data;if(t.wasBlocked()){const e=this.blockedCountForUrl.get(t.url())||0;this.blockedCountForUrl.set(t.url(),e+1),this.updateThrottler.schedule(this.update.bind(this))}}wasShown(){super.wasShown(),this.list.registerCSSFiles([l.default]),this.registerCSSFiles([l.default])}}})),r.register("acxU2",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright (c) 2015 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.list {\n  border: none !important; /* stylelint-disable-line declaration-no-important */\n  border-top: 1px solid var(--color-details-hairline) !important; /* stylelint-disable-line declaration-no-important */\n}\n\n.blocking-disabled {\n  opacity: 80%;\n}\n\n.editor-container {\n  padding: 0 4px;\n}\n\n.no-blocked-urls,\n.blocked-urls {\n  overflow-x: hidden;\n  overflow-y: auto;\n}\n\n.no-blocked-urls {\n  display: flex;\n  justify-content: center;\n  padding: 10px;\n}\n\n.no-blocked-urls > span {\n  white-space: pre;\n}\n\n.blocked-url {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  flex: auto;\n}\n\n.blocked-url-count {\n  flex: none;\n  padding-right: 9px;\n}\n\n.blocked-url-checkbox {\n  margin-left: 8px;\n  flex: none;\n}\n\n.blocked-url-checkbox:focus {\n  outline: auto 5px -webkit-focus-ring-color;\n}\n\n.blocked-url-label {\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  flex: auto;\n  padding: 0 3px;\n}\n\n.blocked-url-edit-row {\n  flex: none;\n  display: flex;\n  flex-direction: row;\n  margin: 7px 5px 0;\n  align-items: center;\n}\n\n.blocked-url-edit-value {\n  user-select: none;\n  flex: 1 1 0;\n}\n\n.blocked-url-edit-row input {\n  width: 100%;\n  text-align: inherit;\n  height: 22px;\n}\n\n/*# sourceURL=blockedURLsPane.css */\n");var n=i})),r.register("6zKhV",(function(t,i){e(t.exports,"EventSourceMessagesView",(()=>p)),e(t.exports,"EventSourceMessageNode",(()=>g)),e(t.exports,"Comparators",(()=>f)),e(t.exports,"EventSourceMessageNodeComparator",(()=>m));var n=r("8Jvgy"),o=r("e7bLS"),s=r("ixFnt"),a=r("eQFvP"),l=r("cObcK"),d=r("9z2ZV");const c={id:"Id",type:"Type",data:"Data",time:"Time",eventSource:"Event Source",copyMessage:"Copy message"},h=s.i18n.registerUIStrings("panels/network/EventSourceMessagesView.ts",c),u=s.i18n.getLocalizedString.bind(void 0,h);class p extends d.Widget.VBox{request;dataGrid;constructor(e){super(),this.element.classList.add("event-source-messages-view"),this.request=e;const t=[{id:"id",title:u(c.id),sortable:!0,weight:8},{id:"type",title:u(c.type),sortable:!0,weight:8},{id:"data",title:u(c.data),sortable:!1,weight:88},{id:"time",title:u(c.time),sortable:!0,weight:8}];this.dataGrid=new l.SortableDataGrid.SortableDataGrid({displayName:u(c.eventSource),columns:t,editCallback:void 0,deleteCallback:void 0,refreshCallback:void 0}),this.dataGrid.setStriped(!0),this.dataGrid.setStickToBottom(!0),this.dataGrid.setRowContextMenuCallback(this.onRowContextMenu.bind(this)),this.dataGrid.markColumnAsSortedBy("time",l.DataGrid.Order.Ascending),this.sortItems(),this.dataGrid.addEventListener(l.DataGrid.Events.SortingChanged,this.sortItems,this),this.dataGrid.setName("EventSourceMessagesView"),this.dataGrid.asWidget().show(this.element)}wasShown(){this.dataGrid.rootNode().removeChildren(),this.registerCSSFiles([n.default]);const e=this.request.eventSourceMessages();for(let t=0;t<e.length;++t)this.dataGrid.insertChild(new g(e[t]));this.request.addEventListener(a.NetworkRequest.Events.EventSourceMessageAdded,this.messageAdded,this)}willHide(){this.request.removeEventListener(a.NetworkRequest.Events.EventSourceMessageAdded,this.messageAdded,this)}messageAdded(e){const t=e.data;this.dataGrid.insertChild(new g(t))}sortItems(){const e=this.dataGrid.sortColumnId();if(!e)return;const t=f[e];t&&this.dataGrid.sortNodes(t,!this.dataGrid.isSortOrderAscending())}onRowContextMenu(e,t){e.clipboardSection().appendItem(u(c.copyMessage),o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText.bind(o.InspectorFrontendHost.InspectorFrontendHostInstance,t.data.data))}}class g extends l.SortableDataGrid.SortableDataGridNode{message;constructor(e){const t=new Date(1e3*e.time),r=("0"+t.getHours()).substr(-2)+":"+("0"+t.getMinutes()).substr(-2)+":"+("0"+t.getSeconds()).substr(-2)+"."+("00"+t.getMilliseconds()).substr(-3),i=document.createElement("div");d.UIUtils.createTextChild(i,r),d.Tooltip.Tooltip.install(i,t.toLocaleString()),super({id:e.eventId,type:e.eventName,data:e.data,time:i}),this.message=e}}function m(e,t,r){const i=e(t.message),n=e(r.message);return i<n?-1:i>n?1:0}const f={id:m.bind(null,(e=>e.eventId)),type:m.bind(null,(e=>e.eventName)),time:m.bind(null,(e=>e.time))}})),r.register("8Jvgy",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright (c) 2014 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.event-source-messages-view .data-grid {\n  flex: auto;\n  border: none;\n}\n\n/*# sourceURL=eventSourceMessagesView.css */\n");var n=i})),r.register("6ebTa",(function(t,i){e(t.exports,"NetworkConfigView",(()=>m)),e(t.exports,"userAgentGroups",(()=>w));var n=r("koSS8"),o=r("ixFnt"),s=r("eQFvP"),a=r("9z2ZV"),l=r("8fwyZ");r("i3ieh");var d=r("87tJd"),c=r("iDTky");const h={custom:"Custom...",enterACustomUserAgent:"Enter a custom user agent",customUserAgentFieldIsRequired:"Custom user agent field is required",caching:"Caching",disableCache:"Disable cache",networkThrottling:"Network throttling",userAgent:"User agent",selectAutomatically:"Use browser default",acceptedEncoding:"Accepted `Content-Encoding`s",clientHintsStatusText:"User agent updated."},u=o.i18n.registerUIStrings("panels/network/NetworkConfigView.ts",h),p=o.i18n.getLocalizedString.bind(void 0,u);let g;class m extends a.Widget.VBox{constructor(){super(!0),this.contentElement.classList.add("network-config"),this.createCacheSection(),this.contentElement.createChild("div").classList.add("panel-section-separator"),this.createNetworkThrottlingSection(),this.contentElement.createChild("div").classList.add("panel-section-separator"),this.createUserAgentSection(),this.contentElement.createChild("div").classList.add("panel-section-separator"),this.createAcceptedEncodingSection()}static instance(e={forceNew:null}){const{forceNew:t}=e;return g&&!t||(g=new m),g}static createUserAgentSelectAndInput(e){const t=n.Settings.Settings.instance().createSetting("customUserAgent",""),r=n.Settings.Settings.instance().createSetting("customUserAgentMetadata",null),i=document.createElement("select");a.ARIAUtils.setAccessibleName(i,e);const o={title:p(h.custom),value:"custom"};i.appendChild(new Option(o.title,o.value));for(const e of w){const t=i.createChild("optgroup");t.label=e.title;for(const r of e.values){const e=s.NetworkManager.MultitargetNetworkManager.patchUserAgentWithChromeVersion(r.value);t.appendChild(new Option(r.title,e))}}i.selectedIndex=0;const l=a.UIUtils.createInput("","text");l.value=t.get(),a.Tooltip.Tooltip.install(l,t.get()),l.placeholder=p(h.enterACustomUserAgent),l.required=!0,a.ARIAUtils.setAccessibleName(l,l.placeholder);const d=document.createElement("div");function c(){const e=t.get(),r=i.options;let n=!1;for(let t=0;t<r.length;++t)if(r[t].value===e){i.selectedIndex=t,n=!0;break}n||(i.selectedIndex=0)}return d.classList.add("network-config-input-validation-error"),a.ARIAUtils.markAsAlert(d),l.value||(d.textContent=p(h.customUserAgentFieldIsRequired)),c(),i.addEventListener("change",(function(){const e=i.options[i.selectedIndex].value;if(e!==o.value){t.set(e),l.value=e,a.Tooltip.Tooltip.install(l,e);const i=f(e);r.set(i),s.NetworkManager.MultitargetNetworkManager.instance().setCustomUserAgentOverride(e,i)}else r.set(null),l.select();d.textContent="";const n=new CustomEvent("user-agent-change",{detail:{value:e}});i.dispatchEvent(n)}),!1),l.addEventListener("input",(function(){t.get()!==l.value&&(l.value?d.textContent="":d.textContent=p(h.customUserAgentFieldIsRequired),t.set(l.value),a.Tooltip.Tooltip.install(l,l.value),c())}),!1),{select:i,input:l,error:d}}createSection(e,t){const r=this.contentElement.createChild("section","network-config-group");return t&&r.classList.add(t),r.createChild("div","network-config-title").textContent=e,r.createChild("div","network-config-fields")}createCacheSection(){this.createSection(p(h.caching),"network-config-disable-cache").appendChild(a.SettingsUI.createSettingCheckbox(p(h.disableCache),n.Settings.Settings.instance().moduleSetting("cacheDisabled"),!0))}createNetworkThrottlingSection(){const e=p(h.networkThrottling),t=this.createSection(e,"network-config-throttling").createChild("select","chrome-select");l.ThrottlingManager.throttlingManager().decorateSelectWithNetworkThrottling(t),a.ARIAUtils.setAccessibleName(t,e)}createUserAgentSection(){const e=p(h.userAgent),t=this.createSection(e,"network-config-ua"),r=a.UIUtils.CheckboxLabel.create(p(h.selectAutomatically),!0);t.appendChild(r);const i=r.checkboxElement,o=n.Settings.Settings.instance().createSetting("customUserAgentMetadata",null),l=n.Settings.Settings.instance().createSetting("customUserAgent","");l.addChangeListener((()=>{if(i.checked)return;const e=l.get(),t=f(e);s.NetworkManager.MultitargetNetworkManager.instance().setCustomUserAgentOverride(e,t)}));const c=t.createChild("div","network-config-ua-custom");i.addEventListener("change",C);const u=m.createUserAgentSelectAndInput(e);u.select.classList.add("chrome-select"),c.appendChild(u.select),c.appendChild(u.input),c.appendChild(u.error);const g=c.createChild("div","client-hints-form"),w=new d.UserAgentClientHintsForm,b=o.get(),k=f(u.select.value);w.value={showMobileCheckbox:!0,showSubmitButton:!0,metaData:b||k||void 0},g.appendChild(w),u.select.addEventListener("user-agent-change",(e=>{const t=e.detail.value,r=t?f(t):null;w.value={metaData:r||void 0,showMobileCheckbox:!0,showSubmitButton:!0},v.textContent=""})),w.addEventListener("clienthintschange",(()=>{u.select.value="custom",v.textContent=""})),w.addEventListener("clienthintssubmit",(e=>{const t=e.detail.value,r=l.get();o.set(t),s.NetworkManager.MultitargetNetworkManager.instance().setCustomUserAgentOverride(r,t),v.textContent=p(h.clientHintsStatusText)}));const v=t.createChild("span","status-text");function C(){const e=!i.checked;c.classList.toggle("checked",e),u.select.disabled=!e,u.input.disabled=!e,u.error.hidden=!e,w.disabled=!e;const t=e?l.get():"",r=e?f(t):null;s.NetworkManager.MultitargetNetworkManager.instance().setCustomUserAgentOverride(t,r)}v.textContent="",C()}createAcceptedEncodingSection(){const e=p(h.acceptedEncoding),t=this.createSection(e,"network-config-accepted-encoding"),r=a.UIUtils.CheckboxLabel.create(p(h.selectAutomatically),!0);t.appendChild(r);const i=r.checkboxElement,o=n.Settings.Settings.instance().createSetting("useCustomAcceptedEncodings",!1),l=n.Settings.Settings.instance().createSetting("customAcceptedEncodings","gzip,br,deflate");function d(){o.get()?s.NetworkManager.MultitargetNetworkManager.instance().setCustomAcceptedEncodingsOverride(""===l.get()?[]:l.get().split(",")):s.NetworkManager.MultitargetNetworkManager.instance().clearCustomAcceptedEncodingsOverride()}l.addChangeListener(d),o.addChangeListener(d);const c=t.createChild("div","network-config-accepted-encoding-custom");i.checked=!o.get(),i.addEventListener("change",m);const u=new Map,g={Deflate:"deflate",Gzip:"gzip",Br:"br"};for(const e of Object.values(g)){const t=a.UIUtils.CheckboxLabel.create(e,!0);c.appendChild(t),u.set(e,t.checkboxElement)}for(const[e,t]of u)t.checked=l.get().includes(e),t.addEventListener("change",m);function m(){o.set(!i.checked);const e=[];for(const[t,r]of u)r.disabled=i.checked,r.checked&&e.push(t);l.set(e.join(","))}m()}wasShown(){super.wasShown(),this.registerCSSFiles([c.default])}}function f(e){for(const t of w)for(const r of t.values)if(e===s.NetworkManager.MultitargetNetworkManager.patchUserAgentWithChromeVersion(r.value))return r.metadata?(s.NetworkManager.MultitargetNetworkManager.patchUserAgentMetadataWithChromeVersion(r.metadata),r.metadata):null;return null}const w=[{title:"Android",values:[{title:"Android (4.0.2) Browser — Galaxy Nexus",value:"Mozilla/5.0 (Linux; U; Android 4.0.2; en-us; Galaxy Nexus Build/ICL53F) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Google Chrome",version:"%s"}],fullVersion:"%s",platform:"Android",platformVersion:"4.0.2",architecture:"",model:"Galaxy Nexus",mobile:!0}},{title:"Android (2.3) Browser — Nexus S",value:"Mozilla/5.0 (Linux; U; Android 2.3.6; en-us; Nexus S Build/GRK39F) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Google Chrome",version:"%s"}],fullVersion:"%s",platform:"Android",platformVersion:"2.3.6",architecture:"",model:"Nexus S",mobile:!0}}]},{title:"BlackBerry",values:[{title:"BlackBerry — BB10",value:"Mozilla/5.0 (BB10; Touch) AppleWebKit/537.1+ (KHTML, like Gecko) Version/10.0.0.1337 Mobile Safari/537.1+",metadata:null},{title:"BlackBerry — PlayBook 2.1",value:"Mozilla/5.0 (PlayBook; U; RIM Tablet OS 2.1.0; en-US) AppleWebKit/536.2+ (KHTML, like Gecko) Version/7.2.1.0 Safari/536.2+",metadata:null},{title:"BlackBerry — 9900",value:"Mozilla/5.0 (BlackBerry; U; BlackBerry 9900; en-US) AppleWebKit/534.11+ (KHTML, like Gecko) Version/7.0.0.187 Mobile Safari/534.11+",metadata:null}]},{title:"Chrome",values:[{title:"Chrome — Android Mobile",value:"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Mobile Safari/537.36",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Google Chrome",version:"%s"}],fullVersion:"%s",platform:"Android",platformVersion:"6.0",architecture:"",model:"Nexus 5",mobile:!0}},{title:"Chrome — Android Mobile (high-end)",value:"Mozilla/5.0 (Linux; Android 10; Pixel 4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Mobile Safari/537.36",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Google Chrome",version:"%s"}],fullVersion:"%s",platform:"Android",platformVersion:"10",architecture:"",model:"Pixel 4",mobile:!0}},{title:"Chrome — Android Tablet",value:"Mozilla/5.0 (Linux; Android 4.3; Nexus 7 Build/JSS15Q) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Safari/537.36",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Google Chrome",version:"%s"}],fullVersion:"%s",platform:"Android",platformVersion:"4.3",architecture:"",model:"Nexus 7",mobile:!0}},{title:"Chrome — iPhone",value:"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/%s Mobile/15E148 Safari/604.1",metadata:null},{title:"Chrome — iPad",value:"Mozilla/5.0 (iPad; CPU OS 13_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/%s Mobile/15E148 Safari/604.1",metadata:null},{title:"Chrome — Chrome OS",value:"Mozilla/5.0 (X11; CrOS x86_64 10066.0.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Safari/537.36",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Google Chrome",version:"%s"}],fullVersion:"%s",platform:"Chrome OS",platformVersion:"10066.0.0",architecture:"x86",model:"",mobile:!1}},{title:"Chrome — Mac",value:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Safari/537.36",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Google Chrome",version:"%s"}],fullVersion:"%s",platform:"macOS",platformVersion:"10_14_6",architecture:"x86",model:"",mobile:!1}},{title:"Chrome — Windows",value:"Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Safari/537.36",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Google Chrome",version:"%s"}],fullVersion:"%s",platform:"Windows",platformVersion:"10.0",architecture:"x86",model:"",mobile:!1}}]},{title:"Firefox",values:[{title:"Firefox — Android Mobile",value:"Mozilla/5.0 (Android 4.4; Mobile; rv:70.0) Gecko/70.0 Firefox/70.0",metadata:null},{title:"Firefox — Android Tablet",value:"Mozilla/5.0 (Android 4.4; Tablet; rv:70.0) Gecko/70.0 Firefox/70.0",metadata:null},{title:"Firefox — iPhone",value:"Mozilla/5.0 (iPhone; CPU iPhone OS 8_3 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) FxiOS/1.0 Mobile/12F69 Safari/600.1.4",metadata:null},{title:"Firefox — iPad",value:"Mozilla/5.0 (iPad; CPU iPhone OS 8_3 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) FxiOS/1.0 Mobile/12F69 Safari/600.1.4",metadata:null},{title:"Firefox — Mac",value:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10.14; rv:70.0) Gecko/20100101 Firefox/70.0",metadata:null},{title:"Firefox — Windows",value:"Mozilla/5.0 (Windows NT 10.0; WOW64; rv:70.0) Gecko/20100101 Firefox/70.0",metadata:null}]},{title:"Googlebot",values:[{title:"Googlebot",value:"Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)",metadata:null},{title:"Googlebot Desktop",value:"Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko; compatible; Googlebot/2.1; +http://www.google.com/bot.html) Chrome/%s Safari/537.36",metadata:null},{title:"Googlebot Smartphone",value:"Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)",metadata:null}]},{title:"Internet Explorer",values:[{title:"Internet Explorer 11",value:"Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko",metadata:null},{title:"Internet Explorer 10",value:"Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; WOW64; Trident/6.0)",metadata:null},{title:"Internet Explorer 9",value:"Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0)",metadata:null},{title:"Internet Explorer 8",value:"Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.0; Trident/4.0)",metadata:null},{title:"Internet Explorer 7",value:"Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0)",metadata:null}]},{title:"Microsoft Edge",values:[{title:"Microsoft Edge (Chromium) — Windows",value:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/%s Safari/537.36 Edg/%s",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Microsoft Edge",version:"%s"}],fullVersion:"%s",platform:"Windows",platformVersion:"10.0",architecture:"x86",model:"",mobile:!1}},{title:"Microsoft Edge (Chromium) — Mac",value:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Chrome/%s Safari/604.1 Edg/%s",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Microsoft Edge",version:"%s"}],fullVersion:"%s",platform:"macOS",platformVersion:"10_14_6",architecture:"x86",model:"",mobile:!1}},{title:"Microsoft Edge — iPhone",value:"Mozilla/5.0 (iPhone; CPU iPhone OS 12_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.1 EdgiOS/********* Mobile/15E148 Safari/604.1",metadata:null},{title:"Microsoft Edge — iPad",value:"Mozilla/5.0 (iPad; CPU OS 12_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 EdgiOS/44.5.2 Mobile/15E148 Safari/605.1.15",metadata:null},{title:"Microsoft Edge — Android Mobile",value:"Mozilla/5.0 (Linux; Android 8.1.0; Pixel Build/OPM4.171019.021.D1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.109 Mobile Safari/537.36 EdgA/42.0.0.2057",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Microsoft Edge",version:"%s"}],fullVersion:"%s",platform:"Android",platformVersion:"8.1.0",architecture:"",model:"Pixel",mobile:!0}},{title:"Microsoft Edge — Android Tablet",value:"Mozilla/5.0 (Linux; Android 6.0.1; Nexus 7 Build/MOB30X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.109 Safari/537.36 EdgA/42.0.0.2057",metadata:{brands:[{brand:"Not A;Brand",version:"99"},{brand:"Chromium",version:"%s"},{brand:"Microsoft Edge",version:"%s"}],fullVersion:"%s",platform:"Android",platformVersion:"6.0.1",architecture:"",model:"Nexus 7",mobile:!0}},{title:"Microsoft Edge (EdgeHTML) — Windows",value:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 Safari/537.36 Edge/18.19042",metadata:null},{title:"Microsoft Edge (EdgeHTML) — XBox",value:"Mozilla/5.0 (Windows NT 10.0; Win64; x64; Xbox; Xbox One) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.102 Safari/537.36 Edge/18.19041",metadata:null}]},{title:"Opera",values:[{title:"Opera — Mac",value:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.97 Safari/537.36 OPR/65.0.3467.48",metadata:null},{title:"Opera — Windows",value:"Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.97 Safari/537.36 OPR/65.0.3467.48",metadata:null},{title:"Opera (Presto) — Mac",value:"Opera/9.80 (Macintosh; Intel Mac OS X 10.9.1) Presto/2.12.388 Version/12.16",metadata:null},{title:"Opera (Presto) — Windows",value:"Opera/9.80 (Windows NT 6.1) Presto/2.12.388 Version/12.16",metadata:null},{title:"Opera Mobile — Android Mobile",value:"Opera/12.02 (Android 4.1; Linux; Opera Mobi/ADR-1111101157; U; en-US) Presto/2.9.201 Version/12.02",metadata:null},{title:"Opera Mini — iOS",value:"Opera/9.80 (iPhone; Opera Mini/8.0.0/34.2336; U; en) Presto/2.8.119 Version/11.10",metadata:null}]},{title:"Safari",values:[{title:"Safari — iPad iOS 13.2",value:"Mozilla/5.0 (iPad; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1",metadata:null},{title:"Safari — iPhone iOS 13.2",value:"Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1",metadata:null},{title:"Safari — Mac",value:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Safari/605.1.15",metadata:null}]},{title:"UC Browser",values:[{title:"UC Browser — Android Mobile",value:"Mozilla/5.0 (Linux; U; Android 8.1.0; en-US; Nexus 6P Build/OPM7.181205.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.2987.108 UCBrowser/12.11.1.1197 Mobile Safari/537.36",metadata:null},{title:"UC Browser — iOS",value:"Mozilla/5.0 (iPhone; CPU iPhone OS 12_1 like Mac OS X; zh-CN) AppleWebKit/537.51.1 (KHTML, like Gecko) Mobile/16B92 UCBrowser/12.1.7.1109 Mobile AliApp(TUnionSDK/********)",metadata:null},{title:"UC Browser — Windows Phone",value:"Mozilla/5.0 (compatible; MSIE 10.0; Windows Phone 8.0; Trident/6.0; IEMobile/10.0; ARM; Touch; NOKIA; Lumia 920) UCBrowser/10.1.0.563 Mobile",metadata:null}]}]})),r.register("i3ieh",(function(t,i){e(t.exports,"UserAgentClientHintsForm",(()=>r("87tJd")));r("87tJd")})),r.register("87tJd",(function(t,i){e(t.exports,"ClientHintsChangeEvent",(()=>m)),e(t.exports,"ClientHintsSubmitEvent",(()=>f)),e(t.exports,"UserAgentClientHintsForm",(()=>b));var n=r("ixFnt"),o=r("9apcb"),s=r("kpUjp"),a=r("dS5IF"),l=r("dvY0l"),d=r("lzcO5"),c=r("cY3yZ");r("gTogI");var h=r("3XcxI");const u={title:"User agent client hints",brands:"Brands",brandProperties:"Brand properties",brandName:"Brand",brandNameAriaLabel:"Brand {PH1}",version:"Version",brandVersionAriaLabel:"Version {PH1}",addBrand:"Add Brand",deleteTooltip:"Delete",brandDeleteAriaLabel:"Delete {PH1}",fullBrowserVersion:"Full browser version",fullBrowserVersionPlaceholder:"Full browser version (e.g. 87.0.4280.88)",platformLabel:"Platform",platformProperties:"Platform properties",platformVersion:"Platform version",platformPlaceholder:"Platform (e.g. Android)",architecture:"Architecture",architecturePlaceholder:"Architecture (e.g. x86)",deviceProperties:"Device properties",deviceModel:"Device model",mobileCheckboxLabel:"Mobile",update:"Update",notRepresentable:"Not representable as structured headers string.",userAgentClientHintsInfo:"User agent client hints are an alternative to the user agent string that identify the browser and the device in a more structured way with better privacy accounting. Click the button to learn more.",addedBrand:"Added brand row",deletedBrand:"Deleted brand row"},p=n.i18n.registerUIStrings("panels/settings/emulation/components/UserAgentClientHintsForm.ts",u),g=n.i18n.getLocalizedString.bind(void 0,p);class m extends Event{static eventName="clienthintschange";constructor(){super(m.eventName)}}class f extends Event{static eventName="clienthintssubmit";detail;constructor(e){super(f.eventName),this.detail={value:e}}}const w={brands:[{brand:"",version:""}],fullVersion:"",platform:"",platformVersion:"",architecture:"",model:"",mobile:!1};class b extends HTMLElement{static litTagName=a.literal`devtools-user-agent-client-hints-form`;#e=this.attachShadow({mode:"open"});#t=!1;#r=!1;#i=w;#n=!1;#o=!1;#s="";connectedCallback(){this.#e.adoptedStyleSheets=[d.checkboxStyles,l.default]}set value(e){const{metaData:t=w,showMobileCheckbox:r=!1,showSubmitButton:i=!1}=e;this.#i={...this.#i,...t},this.#n=r,this.#o=i,this.#a()}get value(){return{metaData:this.#i}}set disabled(e){this.#r=e,this.#t=!1,this.#a()}get disabled(){return this.#r}#l=e=>{"Space"!==e.code&&"Enter"!==e.code&&"ArrowLeft"!==e.code&&"ArrowRight"!==e.code||(e.stopPropagation(),this.#d(e.code))};#d=e=>{this.#r||"ArrowLeft"===e&&!this.#t||"ArrowRight"===e&&this.#t||(this.#t=!this.#t,this.#a())};#c=(e,t,r)=>{const i=this.#i.brands?.map(((i,n)=>{if(n===t){const{brand:t,version:n}=i;return"brandName"===r?{brand:e,version:n}:{brand:t,version:e}}return i}));this.#i={...this.#i,brands:i},this.dispatchEvent(new m),this.#a()};#h=e=>{const{brands:t=[]}=this.#i;t.splice(e,1),this.#i={...this.#i,brands:t},this.dispatchEvent(new m),this.#s=g(u.deletedBrand),this.#a();let r=this.shadowRoot?.getElementById(`brand-${e+1}-input`);r||(r=this.shadowRoot?.getElementById("add-brand-button")),r?.focus()};#u=()=>{const{brands:e}=this.#i;this.#i={...this.#i,brands:[...Array.isArray(e)?e:[],{brand:"",version:""}]},this.dispatchEvent(new m),this.#s=g(u.addedBrand),this.#a();const t=this.shadowRoot?.querySelectorAll(".brand-name-input");if(t){const e=Array.from(t).pop();e&&e.focus()}};#p=e=>{"Space"!==e.code&&"Enter"!==e.code||(e.preventDefault(),this.#u())};#g=(e,t)=>{e in this.#i&&(this.#i={...this.#i,[e]:t},this.#a()),this.dispatchEvent(new m)};#m=e=>{"Space"!==e.code&&"Enter"!==e.code||(e.preventDefault(),e.target.click())};#f=e=>{e.preventDefault(),this.#o&&(this.dispatchEvent(new f(this.#i)),this.#a())};#w(e,t,r,i){return a.html`
      <label class="full-row label input-field-label-container">
        ${e}
        <input
          class="input-field"
          type="text"
          @input=${e=>{const t=e.target.value;this.#g(i,t)}}
          .value=${r}
          placeholder=${t}
        />
      </label>
    `}#b(){const{platform:e,platformVersion:t}=this.#i;return a.html`
      <span class="full-row label">${g(u.platformLabel)}</span>
      <div class="full-row brand-row" aria-label=${g(u.platformProperties)} role="group">
        <input
          class="input-field half-row"
          type="text"
          @input=${e=>{const t=e.target.value;this.#g("platform",t)}}
          .value=${e}
          placeholder=${g(u.platformPlaceholder)}
          aria-label=${g(u.platformLabel)}
        />
        <input
          class="input-field half-row"
          type="text"
          @input=${e=>{const t=e.target.value;this.#g("platformVersion",t)}}
          .value=${t}
          placeholder=${g(u.platformVersion)}
          aria-label=${g(u.platformVersion)}
        />
      </div>
    `}#k(){const{model:e,mobile:t}=this.#i,r=this.#n?a.html`
      <label class="mobile-checkbox-container">
        <input type="checkbox" @input=${e=>{const t=e.target.checked;this.#g("mobile",t)}} .checked=${t} />
        ${g(u.mobileCheckboxLabel)}
      </label>
    `:a.html``;return a.html`
      <span class="full-row label">${g(u.deviceModel)}</span>
      <div class="full-row brand-row" aria-label=${g(u.deviceProperties)} role="group">
        <input
          class="input-field ${this.#n?"device-model-input":"full-row"}"
          type="text"
          @input=${e=>{const t=e.target.value;this.#g("model",t)}}
          .value=${e}
          placeholder=${g(u.deviceModel)}
        />
        ${r}
      </div>
    `}#v(){const{brands:e=[{brand:"",version:""}]}=this.#i,t=e.map(((e,t)=>{const{brand:r,version:i}=e,n=()=>{this.#h(t)};return a.html`
        <div class="full-row brand-row" aria-label=${g(u.brandProperties)} role="group">
          <input
            class="input-field brand-name-input"
            type="text"
            @input=${e=>{const r=e.target.value;this.#c(r,t,"brandName")}}
            .value=${r}
            id="brand-${t+1}-input"
            placeholder=${g(u.brandName)}
            aria-label=${g(u.brandNameAriaLabel,{PH1:t+1})}
          />
          <input
            class="input-field"
            type="text"
            @input=${e=>{const r=e.target.value;this.#c(r,t,"brandVersion")}}
            .value=${i}
            placeholder=${g(u.version)}
            aria-label=${g(u.brandVersionAriaLabel,{PH1:t+1})}
          />
          <${c.Icon.Icon.litTagName}
            .data=${{color:"var(--client-hints-form-icon-color)",iconName:"trash_bin_icon",width:"10px",height:"14px"}}
            title=${g(u.deleteTooltip)}
            class="delete-icon"
            tabindex="0"
            role="button"
            @click=${n}
            @keypress=${e=>{"Space"!==e.code&&"Enter"!==e.code||(e.preventDefault(),n())}}
            aria-label=${g(u.brandDeleteAriaLabel,{PH1:t+1})}
          >
          </${c.Icon.Icon.litTagName}>
        </div>
      `}));return a.html`
      <span class="full-row label">${g(u.brands)}</span>
      ${t}
      <div
        class="add-container full-row"
        role="button"
        tabindex="0"
        id="add-brand-button"
        aria-label=${g(u.addBrand)}
        @click=${this.#u}
        @keypress=${this.#p}
      >
        <${c.Icon.Icon.litTagName}
          aria-hidden="true"
          .data=${{color:"var(--client-hints-form-icon-color)",iconName:"add-icon",width:"10px"}}
        >
        </${c.Icon.Icon.litTagName}>
        ${g(u.addBrand)}
      </div>
    `}#a(){const{fullVersion:e,architecture:t}=this.#i,r=this.#v(),i=this.#w(g(u.fullBrowserVersion),g(u.fullBrowserVersionPlaceholder),e||"","fullVersion"),n=this.#b(),s=this.#w(g(u.architecture),g(u.architecturePlaceholder),t,"architecture"),l=this.#k(),d=this.#o?a.html`
      <${o.Button.Button.litTagName}
        .variant=${"secondary"}
        .type=${"submit"}
      >
        ${g(u.update)}
      </${o.Button.Button.litTagName}>
    `:a.html``,h=a.html`
      <section class="root">
        <div
          class="tree-title"
          role="button"
          @click=${this.#d}
          tabindex="0"
          @keydown=${this.#l}
          aria-expanded=${this.#t}
          aria-controls="form-container"
          @disabled=${this.#r}
          aria-disabled=${this.#r}
          aria-label=${g(u.title)}
        >
          <${c.Icon.Icon.litTagName}
            class=${this.#t?"":"rotate-icon"}
            .data=${{color:"var(--client-hints-form-icon-color)",iconName:"chromeSelect",width:"20px"}}
          >
          </${c.Icon.Icon.litTagName}>
          ${g(u.title)}
          <x-link
           tabindex="0"
           href="https://web.dev/user-agent-client-hints/"
           target="_blank"
           class="info-link"
           @keypress=${this.#m}
           aria-label=${g(u.userAgentClientHintsInfo)}
          >
            <${c.Icon.Icon.litTagName}
              .data=${{color:"var(--client-hints-form-icon-color)",iconName:"ic_info_black_18dp",width:"14px"}}
            >
            </${c.Icon.Icon.litTagName}>
          </x-link>
        </div>
        <form
          id="form-container"
          class="form-container ${this.#t?"":"hide-container"}"
          @submit=${this.#f}
        >
          ${r}
          ${i}
          ${n}
          ${s}
          ${l}
          ${d}
        </form>
        <div aria-live="polite" aria-label=${this.#s}></div>
      </section>
    `;a.render(h,this.#e,{host:this})}validate=()=>{for(const[e,t]of Object.entries(this.#i))if("brands"===e){const e=this.#i.brands?.every((({brand:e,version:t})=>{const r=h.validateAsStructuredHeadersString(e,g(u.notRepresentable)),i=h.validateAsStructuredHeadersString(t,g(u.notRepresentable));return r.valid&&i.valid}));if(!e)return{valid:!1,errorMessage:g(u.notRepresentable)}}else{const e=h.validateAsStructuredHeadersString(t,g(u.notRepresentable));if(!e.valid)return e}return{valid:!0}}}s.CustomElements.defineComponent("devtools-user-agent-client-hints-form",b)})),r.register("dvY0l",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync('/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  --client-hints-form-icon-color: var(--color-text-primary);\n}\n\n.root {\n  color: var(--color-text-primary);\n  width: 100%;\n}\n\n.tree-title {\n  font-weight: 700;\n  display: flex;\n  align-items: center;\n}\n\n.rotate-icon {\n  transform: rotate(-90deg);\n}\n\n.form-container {\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr auto;\n  align-items: center;\n  column-gap: 10px;\n  row-gap: 8px;\n  padding: 0 10px;\n}\n\n.full-row {\n  grid-column: 1 / 5;\n}\n\n.half-row {\n  grid-column: span 2;\n}\n\n.mobile-checkbox-container {\n  display: flex;\n}\n\n.device-model-input {\n  grid-column: 1 / 4;\n}\n\n.input-field {\n  color: var(--color-text-primary);\n  padding: 3px 6px;\n  border: none;\n  border-radius: 2px;\n  box-shadow: var(--legacy-focus-ring-inactive-shadow);\n  background-color: var(--color-background);\n  font-size: inherit;\n  height: 18px;\n}\n\n.input-field:focus {\n  box-shadow: var(--legacy-focus-ring-active-shadow);\n  outline-width: 0;\n}\n\n.add-container {\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n}\n\n.add-icon {\n  margin-right: 5px;\n}\n\n.brand-row {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  justify-content: space-between;\n}\n\n.brand-row > input {\n  width: 100%;\n}\n\n.info-link {\n  display: flex;\n  align-items: center;\n  margin-left: 5px;\n}\n\n.hide-container {\n  display: none;\n}\n\n.input-field-label-container {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n@media (forced-colors: active) {\n  :host {\n    --client-hints-form-icon-color: fieldtext;\n  }\n\n  .input-field {\n    border: 1px solid;\n  }\n\n  .tree-title[aria-disabled="true"] {\n    color: GrayText;\n  }\n}\n\n/*# sourceURL=userAgentClientHintsForm.css */\n');var n=i})),r.register("gTogI",(function(t,i){e(t.exports,"UserAgentMetadata",(()=>r("3XcxI")));r("1hk9g"),r("3XcxI")})),r.register("1hk9g",(function(t,r){e(t.exports,"parseList",(()=>D)),e(t.exports,"serializeItem",(()=>W)),e(t.exports,"serializeList",(()=>G));const i="-".charCodeAt(0),n="0".charCodeAt(0),o="9".charCodeAt(0),s="A".charCodeAt(0),a="Z".charCodeAt(0),l="a".charCodeAt(0),d="z".charCodeAt(0),c='"'.charCodeAt(0),h=":".charCodeAt(0),u="?".charCodeAt(0),p="*".charCodeAt(0),g="_".charCodeAt(0),m=".".charCodeAt(0),f="\\".charCodeAt(0),w="/".charCodeAt(0),b="+".charCodeAt(0),k="=".charCodeAt(0),v="!".charCodeAt(0),C="#".charCodeAt(0),S="$".charCodeAt(0),y="%".charCodeAt(0),T="&".charCodeAt(0),R="'".charCodeAt(0),x="^".charCodeAt(0),I="`".charCodeAt(0),q="|".charCodeAt(0),L="~".charCodeAt(0),F=126;function E(e){return void 0!==e&&(e>=n&&e<=o)}function A(e){return void 0!==e&&(e>=s&&e<=a||e>=l&&e<=d)}function N(e){return void 0!==e&&(e>=l&&e<=d)}function P(e){if(void 0===e)return!1;if(E(e)||A(e))return!0;switch(e){case v:case C:case S:case y:case T:case R:case p:case b:case i:case m:case x:case g:case I:case q:case L:return!0;default:return!1}}class B{data;pos;constructor(e){this.data=e,this.pos=0,this.skipSP()}peek(){return this.data[this.pos]}peekCharCode(){return this.pos<this.data.length?this.data.charCodeAt(this.pos):void 0}eat(){++this.pos}skipSP(){for(;" "===this.data[this.pos];)++this.pos}skipOWS(){for(;" "===this.data[this.pos]||"\t"===this.data[this.pos];)++this.pos}atEnd(){return this.pos===this.data.length}allParsed(){return this.skipSP(),this.pos===this.data.length}}function U(e){return"("===e.peek()?function(e){if("("!==e.peek())return{kind:0};e.eat();const t=[];for(;!e.atEnd();){if(e.skipSP(),")"===e.peek()){e.eat();const r=V(e);return 0===r.kind?r:{kind:12,items:t,parameters:r}}const r=H(e);if(0===r.kind)return r;if(t.push(r)," "!==e.peek()&&")"!==e.peek())return{kind:0}}return{kind:0}}(e):H(e)}function H(e){const t=M(e);if(0===t.kind)return t;const r=V(e);return 0===r.kind?r:{kind:4,value:t,parameters:r}}function M(e){const t=e.peekCharCode();return t===i||E(t)?function(e){let t=5,r=1,i="";"-"===e.peek()&&(e.eat(),r=-1);if(!E(e.peekCharCode()))return{kind:0};for(;!e.atEnd();){const r=e.peekCharCode();if(void 0!==r&&E(r))e.eat(),i+=String.fromCodePoint(r);else{if(r!==m||5!==t)break;if(e.eat(),i.length>12)return{kind:0};i+=".",t=6}if(5===t&&i.length>15)return{kind:0};if(6===t&&i.length>16)return{kind:0}}if(5===t){const e=r*Number.parseInt(i,10);return e<-999999999999999||e>999999999999999?{kind:0}:{kind:5,value:e}}const n=i.length-1-i.indexOf(".");return n>3||0===n?{kind:0}:{kind:6,value:r*Number.parseFloat(i)}}(e):t===c?function(e){let t="";if('"'!==e.peek())return{kind:0};e.eat();for(;!e.atEnd();){const r=e.peekCharCode();if(void 0===r)return{kind:0};if(e.eat(),r===f){if(e.atEnd())return{kind:0};const r=e.peekCharCode();if(e.eat(),r!==f&&r!==c)return{kind:0};t+=String.fromCodePoint(r)}else{if(r===c)return{kind:7,value:t};if(r<32||r>126)return{kind:0};t+=String.fromCodePoint(r)}}return{kind:0}}(e):t===h?function(e){let t="";if(":"!==e.peek())return{kind:0};e.eat();for(;!e.atEnd();){const r=e.peekCharCode();if(void 0===r)return{kind:0};if(e.eat(),r===h)return{kind:9,value:t};if(!E(r)&&!A(r)&&r!==b&&r!==w&&r!==k)return{kind:0};t+=String.fromCodePoint(r)}return{kind:0}}(e):t===u?function(e){if("?"!==e.peek())return{kind:0};if(e.eat(),"0"===e.peek())return e.eat(),{kind:10,value:!1};if("1"===e.peek())return e.eat(),{kind:10,value:!0};return{kind:0}}(e):t===p||A(t)?function(e){const t=e.peekCharCode();if(t!==p&&!A(t))return{kind:0};let r="";for(;!e.atEnd();){const t=e.peekCharCode();if(void 0===t||!P(t)&&t!==h&&t!==w)break;e.eat(),r+=String.fromCodePoint(t)}return{kind:8,value:r}}(e):{kind:0}}function V(e){const t=new Map;for(;!e.atEnd()&&";"===e.peek();){e.eat(),e.skipSP();const r=O(e);if(0===r.kind)return r;let i={kind:10,value:!0};if("="===e.peek()){e.eat();const t=M(e);if(0===t.kind)return t;i=t}t.has(r.value)&&t.delete(r.value),t.set(r.value,{kind:2,name:r,value:i})}return{kind:3,items:[...t.values()]}}function O(e){let t="";const r=e.peekCharCode();if(r!==p&&!N(r))return{kind:0};for(;!e.atEnd();){const r=e.peekCharCode();if(!N(r)&&!E(r)&&r!==g&&r!==i&&r!==m&&r!==p)break;t+=e.peek(),e.eat()}return{kind:1,value:t}}function D(e){return function(e){const t={kind:11,items:[]};for(;!e.atEnd();){const r=U(e);if(0===r.kind)return r;if(t.items.push(r),e.skipOWS(),e.atEnd())return t;if(","!==e.peek())return{kind:0};if(e.eat(),e.skipOWS(),e.atEnd())return{kind:0}}return t}(new B(e))}function W(e){const t=j(e.value);if(0===t.kind)return t;const r=$(e.parameters);return 0===r.kind?r:{kind:13,value:t.value+r.value}}function G(e){const t=[];for(let r=0;r<e.items.length;++r){const i=e.items[r];if(12===i.kind){const e=z(i);if(0===e.kind)return e;t.push(e.value)}else{const e=W(i);if(0===e.kind)return e;t.push(e.value)}}return{kind:13,value:t.join(", ")}}function z(e){const t=[];for(let r=0;r<e.items.length;++r){const i=W(e.items[r]);if(0===i.kind)return i;t.push(i.value)}let r="("+t.join(" ")+")";const i=$(e.parameters);return 0===i.kind?i:(r+=i.value,{kind:13,value:r})}function $(e){let t="";for(const r of e.items){t+=";";const e=K(r.name);if(0===e.kind)return e;t+=e.value;const i=r.value;if(10!==i.kind||!i.value){t+="=";const e=j(i);if(0===e.kind)return e;t+=e.value}}return{kind:13,value:t}}function K(e){if(0===e.value.length)return{kind:0};const t=e.value.charCodeAt(0);if(!N(t)&&t!==p)return{kind:0};for(let t=1;t<e.value.length;++t){const r=e.value.charCodeAt(t);if(!N(r)&&!E(r)&&r!==g&&r!==i&&r!==m&&r!==p)return{kind:0}}return{kind:13,value:e.value}}function j(e){return 5===e.kind?function(e){return e.value<-999999999999999||e.value>999999999999999||!Number.isInteger(e.value)?{kind:0}:{kind:13,value:e.value.toString(10)}}(e):6===e.kind?function(e){throw"Unimplemented"}():7===e.kind?function(e){for(let t=0;t<e.value.length;++t){const r=e.value.charCodeAt(t);if(r<32||r>F)return{kind:0}}let t='"';for(let r=0;r<e.value.length;++r){const i=e.value[r];'"'!==i&&"\\"!==i||(t+="\\"),t+=i}return t+='"',{kind:13,value:t}}(e):8===e.kind?function(e){if(0===e.value.length)return{kind:0};const t=e.value.charCodeAt(0);if(!A(t)&&t!==p)return{kind:0};for(let t=1;t<e.value.length;++t){const r=e.value.charCodeAt(t);if(!P(r)&&r!==h&&r!==w)return{kind:0}}return{kind:13,value:e.value}}(e):10===e.kind?function(e){return{kind:13,value:e.value?"?1":"?0"}}(e):9===e.kind?function(e){throw"Unimplemented"}():{kind:0}}})),r.register("3XcxI",(function(t,i){e(t.exports,"parseBrandsList",(()=>o)),e(t.exports,"serializeBrandsList",(()=>s)),e(t.exports,"validateAsStructuredHeadersString",(()=>a));var n=r("1hk9g");function o(e,t,r){const i=[],o=(0,n.parseList)(e);if(0===o.kind)return t;for(const e of o.items){if(4!==e.kind)return r;const t=e.value;if(7!==t.kind)return r;if(1!==e.parameters.items.length)return r;const n=e.parameters.items[0];if("v"!==n.name.value)return r;const o=n.value;if(7!==o.kind)return r;i.push({brand:t.value,version:o.value})}return i}function s(e){const t={kind:11,items:[]},r={kind:1,value:"v"};for(const i of e){const e={kind:4,value:{kind:7,value:i.brand},parameters:{kind:3,items:[{kind:2,name:r,value:{kind:7,value:i.version}}]}};t.items.push(e)}const i=(0,n.serializeList)(t);return 0===i.kind?"":i.value}function a(e,t){return 0===(0,n.serializeItem)({kind:4,value:{kind:7,value:e},parameters:{kind:3,items:[]}}).kind?{valid:!1,errorMessage:t}:{valid:!0,errorMessage:void 0}}})),r.register("iDTky",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync('/*\n * Copyright (c) 2015 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.network-config {\n  padding: 12px;\n  display: block;\n}\n\n.network-config-group {\n  display: flex;\n  margin-bottom: 10px;\n  flex-wrap: wrap;\n  flex: 0 0 auto;\n  min-height: 30px;\n}\n\n.network-config-title {\n  margin-right: 16px;\n  width: 130px;\n}\n\n.network-config-fields {\n  flex: 2 0 200px;\n}\n\n.panel-section-separator {\n  height: 1px;\n  margin-bottom: 10px;\n  background: var(--color-details-hairline);\n}\n/* Disable cache */\n\n.network-config-disable-cache {\n  line-height: 28px;\n  border-top: none;\n  padding-top: 0;\n}\n\n.network-config-input-validation-error {\n  color: var(--legacy-input-validation-error);\n  margin: 5px 0;\n}\n\n.network-config-input-validation-error:empty {\n  display: none;\n}\n/* Network throttling */\n\n.network-config-throttling .chrome-select {\n  width: 100%;\n  max-width: 250px;\n}\n\n.network-config-throttling > .network-config-title {\n  line-height: 24px;\n}\n/* User agent */\n\n.network-config-ua > .network-config-title {\n  line-height: 20px;\n}\n\n.network-config-ua span[is="dt-radio"].checked > * {\n  display: none;\n}\n\n.network-config-ua input {\n  display: block;\n  width: calc(100% - 20px);\n}\n\n.network-config-ua input[type="text"],\n.network-config-ua .chrome-select {\n  margin-top: 8px;\n}\n\n.network-config-ua .chrome-select {\n  width: calc(100% - 20px);\n  max-width: 250px;\n}\n\n.network-config-ua span[is="dt-radio"] {\n  display: block;\n}\n\n.network-config-ua-custom {\n  opacity: 50%;\n  padding-bottom: 8px;\n}\n\n.network-config-ua-custom.checked {\n  opacity: 100%;\n}\n\n.client-hints-form {\n  margin-top: 14px;\n  width: min(100%, 400px);\n}\n\n.status-text {\n  padding: 10px;\n  color: var(--color-accent-green);\n}\n\n/*# sourceURL=networkConfigView.css */\n');var n=i})),r.register("9kyN0",(function(i,n){e(i.exports,"Events",(()=>y)),e(i.exports,"NetworkNode",(()=>R)),e(i.exports,"_backgroundColors",(()=>x)),e(i.exports,"NetworkRequestNode",(()=>I)),e(i.exports,"NetworkGroupNode",(()=>q));var o=r("koSS8"),s=r("e7bLS"),a=r("ixFnt"),l=r("lz7WY"),d=r("eQFvP"),c=r("fMswD"),h=r("g4rSN"),u=r("4gsXY"),p=r("hE0P3"),g=r("cObcK"),m=r("af4Nd"),f=r("a3yig"),w=r("9z2ZV"),b=r("fG15b"),k=r("fxJGO");const v={redirect:"Redirect",sPreflight:"{PH1} + Preflight",preflight:"Preflight",selectPreflightRequest:"Select preflight request",failed:"(failed)",data:"(data)",canceled:"(canceled)",other:"other",csp:"csp",origin:"origin",devtools:"devtools",blockeds:"(blocked:{PH1})",blockedTooltip:"This request was blocked due to misconfigured response headers, click to view the headers",corsError:"CORS error",crossoriginResourceSharingErrorS:"Cross-Origin Resource Sharing error: {PH1}",finished:"Finished",pendingq:"(pending)",unknown:"(unknown)",unknownExplanation:"The request status cannot be shown here because the page that issued it unloaded while the request was in flight. You can use chrome://net-export to capture a network log and see all request details.",push:"Push / ",parser:"Parser",script:"Script",preload:"Preload",signedexchange:"signed-exchange",selectTheRequestThatTriggered:"Select the request that triggered this preflight",otherC:"Other",memoryCache:"(memory cache)",servedFromMemoryCacheResource:"Served from memory cache, resource size: {PH1}",serviceworker:"(`ServiceWorker`)",servedFromServiceworkerResource:"Served from `ServiceWorker`, resource size: {PH1}",servedFromSignedHttpExchange:"Served from Signed HTTP Exchange, resource size: {PH1}",servedFromWebBundle:"Served from Web Bundle, resource size: {PH1}",prefetchCache:"(prefetch cache)",servedFromPrefetchCacheResource:"Served from prefetch cache, resource size: {PH1}",diskCache:"(disk cache)",servedFromDiskCacheResourceSizeS:"Served from disk cache, resource size: {PH1}",pending:"Pending",level:"level 1",webBundleError:"Web Bundle error",webBundleInnerRequest:"Served from Web Bundle",webBundle:"(Web Bundle)",timeSubtitleTooltipText:"Latency (response received time - start time)"},C=a.i18n.registerUIStrings("panels/network/NetworkDataGridNode.ts",v),S=a.i18n.getLocalizedString.bind(void 0,C);var y,T;(T=y||(y={})).RequestSelected="RequestSelected",T.RequestActivated="RequestActivated";class R extends g.SortableDataGrid.SortableDataGridNode{parentViewInternal;isHovered;showingInitiatorChainInternal;requestOrFirstKnownChildRequestInternal;constructor(e){super({}),this.parentViewInternal=e,this.isHovered=!1,this.showingInitiatorChainInternal=!1,this.requestOrFirstKnownChildRequestInternal=null}displayName(){return""}displayType(){return""}createCell(e){const t=this.createTD(e);return this.renderCell(t,e),t}renderCell(e,t){}isFailed(){return!1}backgroundColor(){const e=x,t=document.hasFocus(),r=this.dataGrid&&this.dataGrid.element===document.activeElement,i=this.isFailed();return this.selected&&t&&r&&i?e.FocusSelectedHasError:this.selected&&t&&r?e.FocusSelected:this.selected?e.Selected:this.hovered()?e.Hovered:this.isOnInitiatorPath()?e.InitiatorPath:this.isOnInitiatedPath()?e.InitiatedPath:this.isStriped()?e.Stripe:e.Default}updateBackgroundColor(){const e=this.existingElement();e&&(e.style.backgroundColor=`var(${this.backgroundColor()})`,this.parentViewInternal.stylesChanged())}setStriped(e){super.setStriped(e),this.updateBackgroundColor()}select(e){super.select(e),this.updateBackgroundColor(),this.parentViewInternal.updateNodeSelectedClass(!0)}deselect(e){super.deselect(e),this.updateBackgroundColor(),this.parentViewInternal.updateNodeSelectedClass(!1)}parentView(){return this.parentViewInternal}hovered(){return this.isHovered}showingInitiatorChain(){return this.showingInitiatorChainInternal}nodeSelfHeight(){return this.parentViewInternal.rowHeight()}setHovered(e,t){this.isHovered===e&&this.showingInitiatorChainInternal===t||(this.isHovered!==e&&(this.isHovered=e,this.attached()&&this.element().classList.toggle("hover",e)),this.showingInitiatorChainInternal!==t&&(this.showingInitiatorChainInternal=t,this.showingInitiatorChainChanged()),this.parentViewInternal.stylesChanged(),this.updateBackgroundColor())}showingInitiatorChainChanged(){}isOnInitiatorPath(){return!1}isOnInitiatedPath(){return!1}request(){return null}isNavigationRequest(){return!1}clearFlatNodes(){super.clearFlatNodes(),this.requestOrFirstKnownChildRequestInternal=null}requestOrFirstKnownChildRequest(){if(this.requestOrFirstKnownChildRequestInternal)return this.requestOrFirstKnownChildRequestInternal;let e=this.request();if(e||!this.hasChildren())return this.requestOrFirstKnownChildRequestInternal=e,this.requestOrFirstKnownChildRequestInternal;let t=null;const r=this.flatChildren();for(let i=0;i<r.length;i++)e=r[i].request(),(!t||e&&e.issueTime()<t.issueTime())&&(t=e);return this.requestOrFirstKnownChildRequestInternal=t,this.requestOrFirstKnownChildRequestInternal}}const x={Default:"--network-grid-default-color",Stripe:"--network-grid-stripe-color",Navigation:"--network-grid-navigation-color",Hovered:"--network-grid-hovered-color",InitiatorPath:"--network-grid-initiator-path-color",InitiatedPath:"--network-grid-initiated-path-color",Selected:"--network-grid-selected-color",FocusSelected:"--network-grid-focus-selected-color",FocusSelectedHasError:"--network-grid-focus-selected-color-has-error",FromFrame:"--network-grid-from-frame-color"};class I extends R{nameCell;initiatorCell;requestInternal;isNavigationRequestInternal;selectable;isOnInitiatorPathInternal;isOnInitiatedPathInternal;linkifiedInitiatorAnchor;constructor(e,t){super(e),this.nameCell=null,this.initiatorCell=null,this.requestInternal=t,this.isNavigationRequestInternal=!1,this.selectable=!0,this.isOnInitiatorPathInternal=!1,this.isOnInitiatedPathInternal=!1}static NameComparator(e,t){const r=e.displayName().toLowerCase(),i=t.displayName().toLowerCase();if(r===i){const r=e.requestOrFirstKnownChildRequest(),i=t.requestOrFirstKnownChildRequest();return r&&i?r.identityCompare(i):r?-1:1}return r<i?-1:1}static RemoteAddressComparator(e,t){const r=e.requestOrFirstKnownChildRequest(),i=t.requestOrFirstKnownChildRequest();if(!r||!i)return r?1:-1;const n=r.remoteAddress(),o=i.remoteAddress();return n>o?1:o>n?-1:r.identityCompare(i)}static SizeComparator(e,t){const r=e.requestOrFirstKnownChildRequest(),i=t.requestOrFirstKnownChildRequest();return r&&i?i.cached()&&!r.cached()?1:r.cached()&&!i.cached()?-1:r.transferSize-i.transferSize||r.resourceSize-i.resourceSize||r.identityCompare(i):r?1:-1}static TypeComparator(e,t){const r=e.requestOrFirstKnownChildRequest(),i=t.requestOrFirstKnownChildRequest();if(!r||!i)return r?1:-1;const n=e.displayType(),o=t.displayType();return n>o?1:o>n?-1:r.identityCompare(i)}static InitiatorComparator(e,t){const r=e.requestOrFirstKnownChildRequest(),i=t.requestOrFirstKnownChildRequest();if(!r||!i)return r?1:-1;const n=e instanceof I&&e.initiatorCell,o=t instanceof I&&t.initiatorCell;if(!n||!o)return n?1:-1;const s=e,a=t,l=s.linkifiedInitiatorAnchor?s.linkifiedInitiatorAnchor.textContent||"":s.initiatorCell.title,d=a.linkifiedInitiatorAnchor?a.linkifiedInitiatorAnchor.textContent||"":a.initiatorCell.title;return l.localeCompare(d)}static InitiatorAddressSpaceComparator(e,t){const r=e.requestOrFirstKnownChildRequest(),i=t.requestOrFirstKnownChildRequest();if(!r||!i)return r?1:-1;const n=r.clientSecurityState(),o=i.clientSecurityState();return n&&o?n.initiatorIPAddressSpace.localeCompare(o.initiatorIPAddressSpace):n?1:-1}static RemoteAddressSpaceComparator(e,t){const r=e.requestOrFirstKnownChildRequest(),i=t.requestOrFirstKnownChildRequest();return r&&i?r.remoteAddressSpace().localeCompare(i.remoteAddressSpace()):r?1:-1}static RequestCookiesCountComparator(e,t){const r=e.requestOrFirstKnownChildRequest(),i=t.requestOrFirstKnownChildRequest();if(!r||!i)return r?1:-1;return r.includedRequestCookies().length-i.includedRequestCookies().length||r.identityCompare(i)}static ResponseCookiesCountComparator(e,t){const r=e.requestOrFirstKnownChildRequest(),i=t.requestOrFirstKnownChildRequest();if(!r||!i)return r?1:-1;return(r.responseCookies?r.responseCookies.length:0)-(i.responseCookies?i.responseCookies.length:0)||r.identityCompare(i)}static PriorityComparator(e,t){const r=e.requestOrFirstKnownChildRequest(),i=t.requestOrFirstKnownChildRequest();if(!r||!i)return r?1:-1;const n=r.priority();let o=n?m.NetworkPriorities.networkPriorityWeight(n):0;o=o||0;const s=i.priority();let a=s?m.NetworkPriorities.networkPriorityWeight(s):0;return a=a||0,o-a||r.identityCompare(i)}static RequestPropertyComparator(e,t,r){const i=t.requestOrFirstKnownChildRequest(),n=r.requestOrFirstKnownChildRequest();if(!i||!n)return i?1:-1;const o=i[e],s=n[e];return o===s?i.identityCompare(n):o>s?1:-1}static RequestURLComparator(e,t){const r=e.requestOrFirstKnownChildRequest(),i=t.requestOrFirstKnownChildRequest();if(!r||!i)return r?1:-1;const n=r.url(),o=i.url();return n===o?r.identityCompare(i):n>o?1:-1}static ResponseHeaderStringComparator(e,t,r){const i=t.requestOrFirstKnownChildRequest(),n=r.requestOrFirstKnownChildRequest();if(!i||!n)return i?1:-1;const o=String(i.responseHeaderValue(e)||""),s=String(n.responseHeaderValue(e)||"");return o.localeCompare(s)||i.identityCompare(n)}static ResponseHeaderNumberComparator(e,t,r){const i=t.requestOrFirstKnownChildRequest(),n=r.requestOrFirstKnownChildRequest();if(!i||!n)return i?1:-1;const o=i.responseHeaderValue(e),s=void 0!==o?parseFloat(o):-1/0,a=n.responseHeaderValue(e),l=void 0!==a?parseFloat(a):-1/0;return s===l?i.identityCompare(n):s>l?1:-1}static ResponseHeaderDateComparator(e,t,r){const i=t.requestOrFirstKnownChildRequest(),n=r.requestOrFirstKnownChildRequest();if(!i||!n)return i?1:-1;const o=i.responseHeaderValue(e),s=n.responseHeaderValue(e),a=o?new Date(o).getTime():-1/0,l=s?new Date(s).getTime():-1/0;return a===l?i.identityCompare(n):a>l?1:-1}showingInitiatorChainChanged(){const e=this.showingInitiatorChain(),t=h.NetworkLog.NetworkLog.instance().initiatorGraphForRequest(this.requestInternal);for(const r of t.initiators){if(r===this.requestInternal)continue;const t=this.parentView().nodeForRequest(r);t&&t.setIsOnInitiatorPath(e)}for(const r of t.initiated.keys()){if(r===this.requestInternal)continue;const t=this.parentView().nodeForRequest(r);t&&t.setIsOnInitiatedPath(e)}}setIsOnInitiatorPath(e){this.isOnInitiatorPathInternal!==e&&this.attached()&&(this.isOnInitiatorPathInternal=e,this.updateBackgroundColor())}isOnInitiatorPath(){return this.isOnInitiatorPathInternal}setIsOnInitiatedPath(e){this.isOnInitiatedPathInternal!==e&&this.attached()&&(this.isOnInitiatedPathInternal=e,this.updateBackgroundColor())}isOnInitiatedPath(){return this.isOnInitiatedPathInternal}displayType(){const e=this.requestInternal.mimeType||this.requestInternal.requestContentType()||"",t=this.requestInternal.resourceType();let r=t.name();return t!==o.ResourceType.resourceTypes.Other&&t!==o.ResourceType.resourceTypes.Image||(r=e.replace(/^(application|image)\//,"")),this.requestInternal.isRedirect()&&(r+=" / "+S(v.redirect)),r}displayName(){return this.requestInternal.name()}request(){return this.requestInternal}isNavigationRequest(){const e=d.PageLoad.PageLoad.forRequest(this.requestInternal);return!!e&&e.mainRequest===this.requestInternal}nodeSelfHeight(){return this.parentView().rowHeight()}createCells(e){this.nameCell=null,this.initiatorCell=null,e.classList.toggle("network-error-row",this.isFailed()),e.classList.toggle("network-navigation-row",this.isNavigationRequestInternal),super.createCells(e),this.updateBackgroundColor()}setTextAndTitle(e,t,r){w.UIUtils.createTextChild(e,t),w.Tooltip.Tooltip.install(e,r||t)}setTextAndTitleAsLink(e,t,r,i){const n=document.createElement("span");n.classList.add("devtools-link"),n.textContent=t,n.addEventListener("click",i),e.appendChild(n),w.Tooltip.Tooltip.install(e,r)}renderCell(e,t){const r=e;switch(t){case"name":this.renderPrimaryCell(r,t);break;case"path":this.renderPrimaryCell(r,t,this.requestInternal.pathname);break;case"url":this.renderPrimaryCell(r,t,this.requestInternal.url());break;case"method":{const e=this.requestInternal.preflightRequest();e?(this.setTextAndTitle(r,`${this.requestInternal.requestMethod} + `,S(v.sPreflight,{PH1:this.requestInternal.requestMethod})),r.appendChild(f.Linkifier.Linkifier.linkifyRevealable(e,S(v.preflight),void 0,S(v.selectPreflightRequest)))):this.setTextAndTitle(r,this.requestInternal.requestMethod);break}case"status":this.renderStatusCell(r);break;case"protocol":this.setTextAndTitle(r,this.requestInternal.protocol);break;case"scheme":this.setTextAndTitle(r,this.requestInternal.scheme);break;case"domain":this.setTextAndTitle(r,this.requestInternal.domain);break;case"remoteaddress":this.setTextAndTitle(r,this.requestInternal.remoteAddress());break;case"remoteaddress-space":this.renderAddressSpaceCell(r,this.requestInternal.remoteAddressSpace());break;case"cookies":this.setTextAndTitle(r,this.arrayLength(this.requestInternal.includedRequestCookies()));break;case"setcookies":this.setTextAndTitle(r,this.arrayLength(this.requestInternal.responseCookies));break;case"priority":{const e=this.requestInternal.priority();this.setTextAndTitle(r,e?m.NetworkPriorities.uiLabelForNetworkPriority(e):"");break}case"connectionid":this.setTextAndTitle(r,"0"===this.requestInternal.connectionId?"":this.requestInternal.connectionId);break;case"type":this.setTextAndTitle(r,this.displayType());break;case"initiator":this.renderInitiatorCell(r);break;case"initiator-address-space":{const e=this.requestInternal.clientSecurityState();this.renderAddressSpaceCell(r,e?e.initiatorIPAddressSpace:"Unknown");break}case"size":this.renderSizeCell(r);break;case"time":this.renderTimeCell(r);break;case"timeline":this.setTextAndTitle(r,"");break;default:this.setTextAndTitle(r,this.requestInternal.responseHeaderValue(t)||"")}}arrayLength(e){return e?String(e.length):""}select(e){super.select(e),this.parentView().dispatchEventToListeners(y.RequestSelected,this.requestInternal)}highlightMatchedSubstring(e){if(!e||!this.nameCell||null===this.nameCell.textContent)return[];this.element();const t=[],r=this.nameCell.textContent.match(e);return r&&w.UIUtils.highlightSearchResult(this.nameCell,r.index||0,r[0].length,t),t}openInNewTab(){s.InspectorFrontendHost.InspectorFrontendHostInstance.openInNewTab(this.requestInternal.url())}isFailed(){if(this.requestInternal.failed&&!this.requestInternal.statusCode)return!0;if(this.requestInternal.statusCode>=400)return!0;const e=this.requestInternal.signedExchangeInfo();return!(null===e||!Boolean(e.errors))||(!(!this.requestInternal.webBundleInfo()?.errorMessage&&!this.requestInternal.webBundleInnerRequestInfo()?.errorMessage)||!!this.requestInternal.corsErrorStatus())}renderPrimaryCell(e,i,n){if(0===this.dataGrid.indexOfVisibleColumn(i)){const r=this.leftPadding?this.leftPadding+"px":"";let i;if(e.style.setProperty("padding-left",r),this.nameCell=e,e.addEventListener("dblclick",this.openInNewTab.bind(this),!1),e.addEventListener("mousedown",(()=>{this.select(),this.parentView().dispatchEventToListeners(y.RequestActivated,{showPanel:!0})})),this.requestInternal.resourceType()===o.ResourceType.resourceTypes.Image){const e=document.createElement("img");e.classList.add("image-network-icon-preview"),e.alt=this.requestInternal.resourceType().title(),this.requestInternal.populateImageSource(e),i=document.createElement("div"),i.classList.add("image"),i.appendChild(e)}else i=document.createElement("img"),i.alt=this.requestInternal.resourceType().title(),i.src=t(b)[(0,k.imageNameForResourceType)(this.requestInternal.resourceType())];i.classList.add("icon"),e.appendChild(i)}if("name"===i){const t=this.requestInternal.webBundleInnerRequestInfo();if(t){const i=document.createElement("img");i.classList.add("icon"),i.alt=S(v.webBundleInnerRequest),i.src="Images/ic_file_webbundle_inner_request.svg",new URL(r("98e0U")).toString();const n=d.NetworkManager.NetworkManager.forRequest(this.requestInternal);t.bundleRequestId&&n?e.appendChild(f.Linkifier.Linkifier.linkifyRevealable(new p.NetworkRequestId.NetworkRequestId(t.bundleRequestId,n),i)):e.appendChild(i)}const i=l.StringUtilities.trimMiddle(this.requestInternal.name(),100),n=d.NetworkManager.NetworkManager.forRequest(this.requestInternal);w.UIUtils.createTextChild(e,n?n.target().decorateLabel(i):i),this.appendSubtitle(e,this.requestInternal.path())}else n&&w.UIUtils.createTextChild(e,n)}renderStatusCell(e){e.classList.toggle("network-dim-cell",!this.isFailed()&&(this.requestInternal.cached()||!this.requestInternal.statusCode));const t=this.requestInternal.corsErrorStatus(),r=this.requestInternal.webBundleInfo()?.errorMessage||this.requestInternal.webBundleInnerRequestInfo()?.errorMessage;if(r)this.setTextAndTitle(e,S(v.webBundleError),r);else if(!this.requestInternal.failed||this.requestInternal.canceled||this.requestInternal.wasBlocked()||t)if(this.requestInternal.statusCode&&this.requestInternal.statusCode>=400)w.UIUtils.createTextChild(e,String(this.requestInternal.statusCode)),this.appendSubtitle(e,this.requestInternal.statusText),w.Tooltip.Tooltip.install(e,this.requestInternal.statusCode+" "+this.requestInternal.statusText);else if(!this.requestInternal.statusCode&&this.requestInternal.parsedURL.isDataURL())this.setTextAndTitle(e,S(v.data));else if(!this.requestInternal.statusCode&&this.requestInternal.canceled)this.setTextAndTitle(e,S(v.canceled));else if(this.requestInternal.wasBlocked()){let t=S(v.other),r=!1;switch(this.requestInternal.blockedReason()){case"other":t=S(v.other);break;case"csp":t=S(v.csp);break;case"mixed-content":t=a.i18n.lockedString("mixed-content");break;case"origin":t=S(v.origin);break;case"inspector":t=S(v.devtools);break;case"subresource-filter":t=a.i18n.lockedString("subresource-filter");break;case"content-type":t=a.i18n.lockedString("content-type");break;case"coep-frame-resource-needs-coep-header":r=!0,t=a.i18n.lockedString("CoepFrameResourceNeedsCoepHeader");break;case"coop-sandboxed-iframe-cannot-navigate-to-coop-page":r=!0,t=a.i18n.lockedString("CoopSandboxedIframeCannotNavigateToCoopPage");break;case"corp-not-same-origin":r=!0,t=a.i18n.lockedString("NotSameOrigin");break;case"corp-not-same-site":r=!0,t=a.i18n.lockedString("NotSameSite");break;case"corp-not-same-origin-after-defaulted-to-same-origin-by-coep":r=!0,t=a.i18n.lockedString("NotSameOriginAfterDefaultedToSameOriginByCoep")}r?this.setTextAndTitleAsLink(e,S(v.blockeds,{PH1:t}),S(v.blockedTooltip),(()=>{this.parentView().dispatchEventToListeners(y.RequestActivated,{showPanel:!0,tab:p.UIRequestLocation.UIRequestTabs.Headers})})):this.setTextAndTitle(e,S(v.blockeds,{PH1:t}))}else t?this.setTextAndTitle(e,S(v.corsError),S(v.crossoriginResourceSharingErrorS,{PH1:t.corsError})):this.requestInternal.statusCode?(w.UIUtils.createTextChild(e,String(this.requestInternal.statusCode)),this.appendSubtitle(e,this.requestInternal.statusText),w.Tooltip.Tooltip.install(e,this.requestInternal.statusCode+" "+this.requestInternal.statusText)):this.requestInternal.finished?this.setTextAndTitle(e,S(v.finished)):this.requestInternal.preserved?this.setTextAndTitle(e,S(v.unknown),S(v.unknownExplanation)):this.setTextAndTitle(e,S(v.pendingq));else{const t=S(v.failed);this.requestInternal.localizedFailDescription?(w.UIUtils.createTextChild(e,t),this.appendSubtitle(e,this.requestInternal.localizedFailDescription,!0),w.Tooltip.Tooltip.install(e,t+" "+this.requestInternal.localizedFailDescription)):this.setTextAndTitle(e,t)}}renderInitiatorCell(e){this.initiatorCell=e;const t=this.requestInternal,r=h.NetworkLog.NetworkLog.instance().initiatorInfoForRequest(t),i=t.timing;switch(i&&i.pushStart&&e.appendChild(document.createTextNode(S(v.push))),r.type){case d.NetworkRequest.InitiatorType.Parser:{w.Tooltip.Tooltip.install(e,r.url+":"+(r.lineNumber+1));const t=u.Workspace.WorkspaceImpl.instance().uiSourceCodeForURL(r.url);e.appendChild(f.Linkifier.Linkifier.linkifyURL(r.url,{text:t?t.displayName():void 0,lineNumber:r.lineNumber,columnNumber:r.columnNumber})),this.appendSubtitle(e,S(v.parser));break}case d.NetworkRequest.InitiatorType.Redirect:{w.Tooltip.Tooltip.install(e,r.url);const i=t.redirectSource();console.assert(null!==i),this.parentView().nodeForRequest(i)?e.appendChild(f.Linkifier.Linkifier.linkifyRevealable(i,c.ResourceUtils.displayNameForURL(i.url()))):e.appendChild(f.Linkifier.Linkifier.linkifyURL(i.url())),this.appendSubtitle(e,S(v.redirect));break}case d.NetworkRequest.InitiatorType.Script:{const i=d.NetworkManager.NetworkManager.forRequest(t);if(!i)return;const n=this.parentView().linkifier();r.stack?this.linkifiedInitiatorAnchor=n.linkifyStackTraceTopFrame(i.target(),r.stack):this.linkifiedInitiatorAnchor=n.linkifyScriptLocation(i.target(),r.scriptId,r.url,r.lineNumber,{columnNumber:r.columnNumber,inlineFrameIndex:0}),w.Tooltip.Tooltip.install(this.linkifiedInitiatorAnchor,""),e.appendChild(this.linkifiedInitiatorAnchor),this.appendSubtitle(e,S(v.script)),e.classList.add("network-script-initiated");break}case d.NetworkRequest.InitiatorType.Preload:w.Tooltip.Tooltip.install(e,S(v.preload)),e.classList.add("network-dim-cell"),e.appendChild(document.createTextNode(S(v.preload)));break;case d.NetworkRequest.InitiatorType.SignedExchange:e.appendChild(f.Linkifier.Linkifier.linkifyURL(r.url)),this.appendSubtitle(e,S(v.signedexchange));break;case d.NetworkRequest.InitiatorType.Preflight:if(e.appendChild(document.createTextNode(S(v.preflight))),r.initiatorRequest){const t=w.Icon.Icon.create("mediumicon-network-panel"),i=f.Linkifier.Linkifier.linkifyRevealable(r.initiatorRequest,t,void 0,S(v.selectTheRequestThatTriggered),"trailing-link-icon");w.ARIAUtils.setAccessibleName(i,S(v.selectTheRequestThatTriggered)),e.appendChild(i)}break;default:w.Tooltip.Tooltip.install(e,S(v.otherC)),e.classList.add("network-dim-cell"),e.appendChild(document.createTextNode(S(v.otherC)))}}renderAddressSpaceCell(e,t){"Unknown"!==t&&w.UIUtils.createTextChild(e,t)}renderSizeCell(e){const t=l.NumberUtilities.bytesToString(this.requestInternal.resourceSize);if(this.requestInternal.cachedInMemory())w.UIUtils.createTextChild(e,S(v.memoryCache)),w.Tooltip.Tooltip.install(e,S(v.servedFromMemoryCacheResource,{PH1:t})),e.classList.add("network-dim-cell");else if(this.requestInternal.fetchedViaServiceWorker)w.UIUtils.createTextChild(e,S(v.serviceworker)),w.Tooltip.Tooltip.install(e,S(v.servedFromServiceworkerResource,{PH1:t})),e.classList.add("network-dim-cell");else if(this.requestInternal.redirectSourceSignedExchangeInfoHasNoErrors())w.UIUtils.createTextChild(e,a.i18n.lockedString("(signed-exchange)")),w.Tooltip.Tooltip.install(e,S(v.servedFromSignedHttpExchange,{PH1:t})),e.classList.add("network-dim-cell");else if(this.requestInternal.webBundleInnerRequestInfo())w.UIUtils.createTextChild(e,S(v.webBundle)),w.Tooltip.Tooltip.install(e,S(v.servedFromWebBundle,{PH1:t})),e.classList.add("network-dim-cell");else if(this.requestInternal.fromPrefetchCache())w.UIUtils.createTextChild(e,S(v.prefetchCache)),w.Tooltip.Tooltip.install(e,S(v.servedFromPrefetchCacheResource,{PH1:t})),e.classList.add("network-dim-cell");else if(this.requestInternal.cached())w.UIUtils.createTextChild(e,S(v.diskCache)),w.Tooltip.Tooltip.install(e,S(v.servedFromDiskCacheResourceSizeS,{PH1:t})),e.classList.add("network-dim-cell");else{const r=l.NumberUtilities.bytesToString(this.requestInternal.transferSize);w.UIUtils.createTextChild(e,r),w.Tooltip.Tooltip.install(e,`${r} transferred over network, resource size: ${t}`)}this.appendSubtitle(e,t)}renderTimeCell(e){this.requestInternal.duration>0?(this.setTextAndTitle(e,a.TimeUtilities.secondsToString(this.requestInternal.duration)),this.appendSubtitle(e,a.TimeUtilities.secondsToString(this.requestInternal.latency),!1,S(v.timeSubtitleTooltipText))):this.requestInternal.preserved?this.setTextAndTitle(e,S(v.unknown),S(v.unknownExplanation)):(e.classList.add("network-dim-cell"),this.setTextAndTitle(e,S(v.pending)))}appendSubtitle(e,t,r=!1,i=""){const n=document.createElement("div");n.classList.add("network-cell-subtitle"),r&&n.classList.add("network-cell-subtitle-show-inline-when-selected"),n.textContent=t,i&&w.Tooltip.Tooltip.install(n,i),e.appendChild(n)}}class q extends R{createCells(e){super.createCells(e);const t=this.dataGrid.visibleColumnsArray[0],r=`${t.title}`,i=S(v.level);this.nodeAccessibleText=`${i} ${r}: ${this.cellAccessibleTextMap.get(t.id)}`}renderCell(e,t){if(0===this.dataGrid.indexOfVisibleColumn(t)){const r=e,i=this.leftPadding?this.leftPadding+"px":"";r.style.setProperty("padding-left",i),r.classList.add("disclosure"),this.setCellAccessibleName(r.textContent||"",r,t)}}select(e){super.select(e);const t=this.traverseNextNode(!1,void 0,!0)?.request();t&&this.parentView().dispatchEventToListeners(y.RequestSelected,t)}}})),r.register("98e0U",(function(e,t){e.exports=new URL(r("27Lyk").resolve("83oIN"),import.meta.url).toString()})),r.register("l7XL4",(function(t,i){e(t.exports,"NetworkItemView",(()=>S));var n=r("koSS8"),o=r("ixFnt"),s=r("eQFvP"),a=r("hE0P3"),l=r("9z2ZV");r("iGAa1");var d=r("2uaHZ"),c=r("6zKhV"),h=r("fCddz"),u=r("kmtft"),p=r("amGvi"),g=r("ej1uS"),m=r("dDrZy"),f=r("kt9Ih"),w=r("gpw7U"),b=r("g1wr2");const k={headers:"Headers",payload:"Payload",messages:"Messages",websocketMessages:"WebSocket messages",eventstream:"EventStream",preview:"Preview",responsePreview:"Response preview",signedexchangeError:"SignedExchange error",response:"Response",rawResponseData:"Raw response data",initiator:"Initiator",requestInitiatorCallStack:"Request initiator call stack",timing:"Timing",requestAndResponseTimeline:"Request and response timeline",trustTokens:"Trust Tokens",trustTokenOperationDetails:"Trust Token operation details",cookies:"Cookies",requestAndResponseCookies:"Request and response cookies"},v=o.i18n.registerUIStrings("panels/network/NetworkItemView.ts",k),C=o.i18n.getLocalizedString.bind(void 0,v);class S extends l.TabbedPane.TabbedPane{requestInternal;resourceViewTabSetting;headersView;payloadView;responseView;cookiesView;initialTab;constructor(e,t,r){if(super(),this.requestInternal=e,this.element.classList.add("network-item-view"),this.resourceViewTabSetting=n.Settings.Settings.instance().createSetting("resourceViewTab",a.UIRequestLocation.UIRequestTabs.Preview),this.headersView=new(0,u.RequestHeadersView)(e),this.appendTab(a.UIRequestLocation.UIRequestTabs.Headers,C(k.headers),this.headersView,C(k.headers)),this.payloadView=null,this.maybeAppendPayloadPanel(),this.addEventListener(l.TabbedPane.Events.TabSelected,this.tabSelected,this),e.resourceType()===n.ResourceType.resourceTypes.WebSocket){const t=new(0,b.ResourceWebSocketFrameView)(e);this.appendTab(a.UIRequestLocation.UIRequestTabs.WsFrames,C(k.messages),t,C(k.websocketMessages))}else if(e.mimeType===s.NetworkRequest.MIME_TYPE.EVENTSTREAM)this.appendTab(a.UIRequestLocation.UIRequestTabs.EventSource,C(k.eventstream),new(0,c.EventSourceMessagesView)(e));else{this.responseView=new(0,f.RequestResponseView)(e);const t=new(0,m.RequestPreviewView)(e);this.appendTab(a.UIRequestLocation.UIRequestTabs.Preview,C(k.preview),t,C(k.responsePreview));const r=e.signedExchangeInfo();if(r&&r.errors&&r.errors.length){const e=l.Icon.Icon.create("smallicon-error");l.Tooltip.Tooltip.install(e,C(k.signedexchangeError)),this.setTabIcon(a.UIRequestLocation.UIRequestTabs.Preview,e)}this.appendTab(a.UIRequestLocation.UIRequestTabs.Response,C(k.response),this.responseView,C(k.rawResponseData))}this.appendTab(a.UIRequestLocation.UIRequestTabs.Initiator,C(k.initiator),new(0,g.RequestInitiatorView)(e),C(k.requestInitiatorCallStack)),this.appendTab(a.UIRequestLocation.UIRequestTabs.Timing,C(k.timing),new(0,w.RequestTimingView)(e,t),C(k.requestAndResponseTimeline)),e.trustTokenParams()&&this.appendTab(a.UIRequestLocation.UIRequestTabs.TrustTokens,C(k.trustTokens),new d.RequestTrustTokensView(e),C(k.trustTokenOperationDetails)),this.cookiesView=null,this.initialTab=r||this.resourceViewTabSetting.get(),this.setAutoSelectFirstItemOnShow(!1)}wasShown(){super.wasShown(),this.requestInternal.addEventListener(s.NetworkRequest.Events.RequestHeadersChanged,this.requestHeadersChanged,this),this.requestInternal.addEventListener(s.NetworkRequest.Events.ResponseHeadersChanged,this.maybeAppendCookiesPanel,this),this.requestInternal.addEventListener(s.NetworkRequest.Events.TrustTokenResultAdded,this.maybeShowErrorIconInTrustTokenTabHeader,this),this.maybeAppendCookiesPanel(),this.maybeShowErrorIconInTrustTokenTabHeader(),this.initialTab&&(this.selectTabInternal(this.initialTab),this.initialTab=void 0)}willHide(){this.requestInternal.removeEventListener(s.NetworkRequest.Events.RequestHeadersChanged,this.requestHeadersChanged,this),this.requestInternal.removeEventListener(s.NetworkRequest.Events.ResponseHeadersChanged,this.maybeAppendCookiesPanel,this),this.requestInternal.removeEventListener(s.NetworkRequest.Events.TrustTokenResultAdded,this.maybeShowErrorIconInTrustTokenTabHeader,this)}async requestHeadersChanged(){this.maybeAppendCookiesPanel(),this.maybeAppendPayloadPanel()}maybeAppendCookiesPanel(){const e=this.requestInternal.hasRequestCookies()||this.requestInternal.responseCookies.length>0;console.assert(e||!this.cookiesView,"Cookies were introduced in headers and then removed!"),e&&!this.cookiesView&&(this.cookiesView=new(0,h.RequestCookiesView)(this.requestInternal),this.appendTab(a.UIRequestLocation.UIRequestTabs.Cookies,C(k.cookies),this.cookiesView,C(k.requestAndResponseCookies)))}async maybeAppendPayloadPanel(){this.hasTab("payload")||(this.requestInternal.queryParameters||await this.requestInternal.requestFormData())&&(this.payloadView=new(0,p.RequestPayloadView)(this.requestInternal),this.appendTab(a.UIRequestLocation.UIRequestTabs.Payload,C(k.payload),this.payloadView,C(k.payload),void 0,void 0,void 0,1))}maybeShowErrorIconInTrustTokenTabHeader(){const e=this.requestInternal.trustTokenOperationDoneEvent();e&&!d.statusConsideredSuccess(e.status)&&this.setTabIcon(a.UIRequestLocation.UIRequestTabs.TrustTokens,l.Icon.Icon.create("smallicon-error"))}selectTabInternal(e){this.selectTab(e)||window.setTimeout((()=>{this.selectTab(e)||this.selectTab("headers")}),0)}tabSelected(e){e.data.isUserGesture&&this.resourceViewTabSetting.set(e.data.tabId)}request(){return this.requestInternal}async revealResponseBody(e){this.selectTabInternal(a.UIRequestLocation.UIRequestTabs.Response),this.responseView&&"number"==typeof e&&await this.responseView.revealLine(e)}revealHeader(e,t){this.selectTabInternal(a.UIRequestLocation.UIRequestTabs.Headers),this.headersView.revealHeader(e,t)}}})),r.register("iGAa1",(function(t,i){e(t.exports,"RequestTrustTokensView",(()=>r("2uaHZ")));r("2uaHZ")})),r.register("2uaHZ",(function(t,i){e(t.exports,"RequestTrustTokensView",(()=>m)),e(t.exports,"RequestTrustTokensReport",(()=>f)),e(t.exports,"statusConsideredSuccess",(()=>k));var n=r("ixFnt"),o=r("eQFvP"),s=r("kpUjp"),a=r("cY3yZ");r("dpR4s");var l=r("5En9r"),d=r("9z2ZV"),c=r("dS5IF"),h=r("dhDUn");const u={parameters:"Parameters",type:"Type",refreshPolicy:"Refresh policy",issuers:"Issuers",topLevelOrigin:"Top level origin",issuer:"Issuer",result:"Result",status:"Status",numberOfIssuedTokens:"Number of issued tokens",success:"Success",failure:"Failure",theOperationsResultWasServedFrom:"The operations result was served from cache.",theOperationWasFulfilledLocally:"The operation was fulfilled locally, no request was sent.",aClientprovidedArgumentWas:"A client-provided argument was malformed or otherwise invalid.",eitherNoInputsForThisOperation:"Either no inputs for this operation are available or the output exceeds the operations quota.",theServersResponseWasMalformedOr:"The servers response was malformed or otherwise invalid.",theOperationFailedForAnUnknown:"The operation failed for an unknown reason."},p=n.i18n.registerUIStrings("panels/network/components/RequestTrustTokensView.ts",u),g=n.i18n.getLocalizedString.bind(void 0,p);class m extends d.Widget.VBox{#C=new f;#S;constructor(e){super(),this.#S=e,this.contentElement.appendChild(this.#C)}wasShown(){this.#S.addEventListener(o.NetworkRequest.Events.TrustTokenResultAdded,this.#y,this),this.#y()}willHide(){this.#S.removeEventListener(o.NetworkRequest.Events.TrustTokenResultAdded,this.#y,this)}#y(){this.#C.data={params:this.#S.trustTokenParams(),result:this.#S.trustTokenOperationDoneEvent()}}}class f extends HTMLElement{static litTagName=c.literal`devtools-trust-token-report`;#e=this.attachShadow({mode:"open"});#T;set data(e){this.#T=e,this.#a()}connectedCallback(){this.#e.adoptedStyleSheets=[h.default]}#a(){if(!this.#T)throw new Error("Trying to render a Trust Token report without providing data");c.render(c.html`<${l.Report.litTagName}>
        ${this.#R()}
        ${this.#x()}
      </${l.Report.litTagName}>
    `,this.#e,{host:this})}#R(){return this.#T&&this.#T.params?c.html`
      <${l.ReportSectionHeader.litTagName}>${g(u.parameters)}</${l.ReportSectionHeader.litTagName}>
      ${C(g(u.type),this.#T.params.type.toString())}
      ${this.#I(this.#T.params)}
      ${this.#q(this.#T.params)}
      ${this.#L()}
      <${l.ReportSectionDivider.litTagName}></${l.ReportSectionDivider.litTagName}>
    `:c.nothing}#I(e){return"Redemption"!==e.type?c.nothing:C(g(u.refreshPolicy),e.refreshPolicy.toString())}#q(e){return e.issuers&&0!==e.issuers.length?c.html`
      <${l.ReportKey.litTagName}>${g(u.issuers)}</${l.ReportKey.litTagName}>
      <${l.ReportValue.litTagName}>
        <ul class="issuers-list">
          ${e.issuers.map((e=>c.html`<li>${e}</li>`))}
        </ul>
      </${l.ReportValue.litTagName}>
    `:c.nothing}#L(){return this.#T&&this.#T.result?c.html`
      ${v(g(u.topLevelOrigin),this.#T.result.topLevelOrigin)}
      ${v(g(u.issuer),this.#T.result.issuerOrigin)}`:c.nothing}#x(){return this.#T&&this.#T.result?c.html`
      <${l.ReportSectionHeader.litTagName}>${g(u.result)}</${l.ReportSectionHeader.litTagName}>
      <${l.ReportKey.litTagName}>${g(u.status)}</${l.ReportKey.litTagName}>
      <${l.ReportValue.litTagName}>
        <span>
          <${a.Icon.Icon.litTagName} class="status-icon"
            .data=${e=this.#T.result.status,k(e)?w:b}>
          </${a.Icon.Icon.litTagName}>
          <strong>${function(e){return k(e)?g(u.success):g(u.failure)}(this.#T.result.status)}</strong>
          ${function(e){switch(e){case"Ok":return null;case"AlreadyExists":return g(u.theOperationsResultWasServedFrom);case"FulfilledLocally":return g(u.theOperationWasFulfilledLocally);case"InvalidArgument":return g(u.aClientprovidedArgumentWas);case"ResourceExhausted":return g(u.eitherNoInputsForThisOperation);case"BadResponse":return g(u.theServersResponseWasMalformedOr);case"FailedPrecondition":case"Unavailable":case"InternalError":case"UnknownError":return g(u.theOperationFailedForAnUnknown)}}(this.#T.result.status)}
        </span>
      </${l.ReportValue.litTagName}>
      ${this.#F(this.#T.result)}
      <${l.ReportSectionDivider.litTagName}></${l.ReportSectionDivider.litTagName}>
      `:c.nothing;var e}#F(e){return"Issuance"!==e.type?c.nothing:v(g(u.numberOfIssuedTokens),e.issuedTokenCount)}}const w={color:"rgb(12, 164, 12)",iconName:"ic_checkmark_16x16",width:"12px"},b={color:"",iconName:"error_icon",width:"12px"};function k(e){return"Ok"===e||"AlreadyExists"===e||"FulfilledLocally"===e}function v(e,t){return void 0===t?c.nothing:c.html`
    <${l.ReportKey.litTagName}>${e}</${l.ReportKey.litTagName}>
    <${l.ReportValue.litTagName}>${t}</${l.ReportValue.litTagName}>
  `}function C(e,t){return c.html`
    <${l.ReportKey.litTagName}>${e}</${l.ReportKey.litTagName}>
    <${l.ReportValue.litTagName} class="code">${t}</${l.ReportValue.litTagName}>
  `}s.CustomElements.defineComponent("devtools-trust-token-report",f)})),r.register("dpR4s",(function(t,i){e(t.exports,"ReportView",(()=>r("5En9r")));r("5En9r")})),r.register("5En9r",(function(t,i){e(t.exports,"Report",(()=>u)),e(t.exports,"ReportSection",(()=>p)),e(t.exports,"ReportSectionHeader",(()=>g)),e(t.exports,"ReportSectionDivider",(()=>m)),e(t.exports,"ReportKey",(()=>f)),e(t.exports,"ReportValue",(()=>w));var n=r("kpUjp"),o=r("dS5IF"),s=r("X5nHD"),a=r("9UN20"),l=r("a7vBa"),d=r("k3rVg"),c=r("ewzK5"),h=r("3JUvK");class u extends HTMLElement{static litTagName=o.literal`devtools-report`;#e=this.attachShadow({mode:"open"});#E="";set data({reportTitle:e}){this.#E=e,this.#a()}connectedCallback(){this.#e.adoptedStyleSheets=[s.default],this.#a()}#a(){o.render(o.html`
      <div class="content">
        ${this.#E?o.html`<div class="report-title">${this.#E}</div>`:o.nothing}
        <slot></slot>
      </div>
    `,this.#e,{host:this})}}class p extends HTMLElement{static litTagName=o.literal`devtools-report-section`;#e=this.attachShadow({mode:"open"});connectedCallback(){this.#e.adoptedStyleSheets=[l.default],this.#a()}#a(){o.render(o.html`
      <div class="section">
        <slot></slot>
      </div>
    `,this.#e,{host:this})}}class g extends HTMLElement{static litTagName=o.literal`devtools-report-section-header`;#e=this.attachShadow({mode:"open"});connectedCallback(){this.#e.adoptedStyleSheets=[c.default],this.#a()}#a(){o.render(o.html`
      <div class="section-header">
        <slot></slot>
      </div>
    `,this.#e,{host:this})}}class m extends HTMLElement{static litTagName=o.literal`devtools-report-divider`;#e=this.attachShadow({mode:"open"});connectedCallback(){this.#e.adoptedStyleSheets=[d.default],this.#a()}#a(){o.render(o.html`
      <div class="section-divider">
      </div>
    `,this.#e,{host:this})}}class f extends HTMLElement{static litTagName=o.literal`devtools-report-key`;#e=this.attachShadow({mode:"open"});connectedCallback(){this.#e.adoptedStyleSheets=[a.default],this.#a()}#a(){o.render(o.html`
      <div class="key"><slot></slot></div>
    `,this.#e,{host:this})}}class w extends HTMLElement{static litTagName=o.literal`devtools-report-value`;#e=this.attachShadow({mode:"open"});connectedCallback(){this.#e.adoptedStyleSheets=[h.default],this.#a()}#a(){o.render(o.html`
      <div class="value"><slot></slot></div>
    `,this.#e,{host:this})}}n.CustomElements.defineComponent("devtools-report",u),n.CustomElements.defineComponent("devtools-report-section",p),n.CustomElements.defineComponent("devtools-report-section-header",g),n.CustomElements.defineComponent("devtools-report-key",f),n.CustomElements.defineComponent("devtools-report-value",w),n.CustomElements.defineComponent("devtools-report-divider",m)})),r.register("X5nHD",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  display: block;\n}\n\n.content {\n  background-color: var(--color-background);\n  display: grid;\n  grid-template-columns: min-content 1fr;\n  user-select: text;\n}\n\n.report-title {\n  padding: 12px 24px;\n  font-size: 15px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  border-bottom: 1px solid var(--color-details-hairline);\n  color: var(--color-text-primary);\n  background-color: var(--color-background);\n  grid-column-start: span 2;\n}\n\n/*# sourceURL=report.css */\n");var n=i})),r.register("9UN20",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  line-height: 28px;\n  margin: 0 0 8px;\n}\n\n.key {\n  color: var(--color-text-secondary);\n  padding: 0 6px;\n  text-align: right;\n  white-space: pre;\n}\n\n/*# sourceURL=reportKey.css */\n");var n=i})),r.register("a7vBa",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  grid-column-start: span 2;\n}\n\n.section {\n  padding: 12px;\n  margin-left: 18px;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  flex: auto;\n  overflow-wrap: break-word;\n  overflow: hidden;\n}\n\n/*# sourceURL=reportSection.css */\n");var n=i})),r.register("k3rVg",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  grid-column-start: span 2;\n}\n\n.section-divider {\n  border-bottom: 1px solid var(--color-details-hairline);\n}\n\n/*# sourceURL=reportSectionDivider.css */\n");var n=i})),r.register("ewzK5",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  grid-column-start: span 2;\n}\n\n.section-header {\n  padding: 12px;\n  margin-left: 18px;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  flex: auto;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  font-weight: bold;\n  color: var(--color-text-primary);\n}\n\n/*# sourceURL=reportSectionHeader.css */\n");var n=i})),r.register("3JUvK",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  line-height: 28px;\n  margin: 0 0 8px;\n  min-width: 150px;\n}\n\n.value {\n  color: var(--color-text-primary);\n  margin-inline-start: 0;\n  padding: 0 6px;\n  overflow-wrap: break-word;\n}\n\n/*# sourceURL=reportValue.css */\n");var n=i})),r.register("dhDUn",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n.code {\n  font-family: var(--monospace-font-family);\n  font-size: var(--monospace-font-size);\n}\n\n.issuers-list {\n  display: flex;\n  flex-direction: column;\n  list-style-type: none;\n  padding: 0;\n  margin: 0;\n}\n\n.status-icon {\n  margin: 0 0.3em 2px 0;\n  vertical-align: middle;\n}\n\n/*# sourceURL=RequestTrustTokensView.css */\n");var n=i})),r.register("fCddz",(function(t,i){e(t.exports,"RequestCookiesView",(()=>p));var n=r("koSS8"),o=r("ixFnt"),s=r("eQFvP");r("24XFe");var a=r("fTlF2"),l=r("9z2ZV"),d=r("6zqMx");const c={thisRequestHasNoCookies:"This request has no cookies.",requestCookies:"Request Cookies",cookiesThatWereSentToTheServerIn:"Cookies that were sent to the server in the 'cookie' header of the request",showFilteredOutRequestCookies:"show filtered out request cookies",noRequestCookiesWereSent:"No request cookies were sent.",responseCookies:"Response Cookies",cookiesThatWereReceivedFromThe:"Cookies that were received from the server in the '`set-cookie`' header of the response",malformedResponseCookies:"Malformed Response Cookies",cookiesThatWereReceivedFromTheServer:"Cookies that were received from the server in the '`set-cookie`' header of the response but were malformed"},h=o.i18n.registerUIStrings("panels/network/RequestCookiesView.ts",c),u=o.i18n.getLocalizedString.bind(void 0,h);class p extends l.Widget.Widget{request;showFilteredOutCookiesSetting;emptyWidget;requestCookiesTitle;requestCookiesEmpty;requestCookiesTable;responseCookiesTitle;responseCookiesTable;malformedResponseCookiesTitle;malformedResponseCookiesList;constructor(e){super(),this.element.classList.add("request-cookies-view"),this.request=e,this.showFilteredOutCookiesSetting=n.Settings.Settings.instance().createSetting("show-filtered-out-request-cookies",!1),this.emptyWidget=new l.EmptyWidget.EmptyWidget(u(c.thisRequestHasNoCookies)),this.emptyWidget.show(this.element),this.requestCookiesTitle=this.element.createChild("div");const t=this.requestCookiesTitle.createChild("span","request-cookies-title");t.textContent=u(c.requestCookies),l.Tooltip.Tooltip.install(t,u(c.cookiesThatWereSentToTheServerIn));const r=l.SettingsUI.createSettingCheckbox(u(c.showFilteredOutRequestCookies),this.showFilteredOutCookiesSetting,!0);r.checkboxElement.addEventListener("change",(()=>{this.refreshRequestCookiesView()})),this.requestCookiesTitle.appendChild(r),this.requestCookiesEmpty=this.element.createChild("div","cookies-panel-item"),this.requestCookiesEmpty.textContent=u(c.noRequestCookiesWereSent),this.requestCookiesTable=new a.CookiesTable(!0),this.requestCookiesTable.contentElement.classList.add("cookie-table","cookies-panel-item"),this.requestCookiesTable.show(this.element),this.responseCookiesTitle=this.element.createChild("div","request-cookies-title"),this.responseCookiesTitle.textContent=u(c.responseCookies),this.responseCookiesTitle.title=u(c.cookiesThatWereReceivedFromThe),this.responseCookiesTable=new a.CookiesTable(!0),this.responseCookiesTable.contentElement.classList.add("cookie-table","cookies-panel-item"),this.responseCookiesTable.show(this.element),this.malformedResponseCookiesTitle=this.element.createChild("div","request-cookies-title"),this.malformedResponseCookiesTitle.textContent=u(c.malformedResponseCookies),l.Tooltip.Tooltip.install(this.malformedResponseCookiesTitle,u(c.cookiesThatWereReceivedFromTheServer)),this.malformedResponseCookiesList=this.element.createChild("div")}getRequestCookies(){const e=new Map,t=this.request.includedRequestCookies().slice();if(this.showFilteredOutCookiesSetting.get())for(const r of this.request.blockedRequestCookies())e.set(r.cookie,r.blockedReasons.map((e=>({attribute:s.NetworkRequest.cookieBlockedReasonToAttribute(e),uiString:s.NetworkRequest.cookieBlockedReasonToUiString(e)})))),t.push(r.cookie);return{requestCookies:t,requestCookieToBlockedReasons:e}}getResponseCookies(){let e=[];const t=new Map,r=[];if(this.request.responseCookies.length){const i=this.request.blockedResponseCookies().map((e=>e.cookieLine));e=this.request.responseCookies.filter((e=>{const t=i.indexOf(e.getCookieLine());return-1===t||(i[t]=null,!1)}));for(const i of this.request.blockedResponseCookies()){const n=s.CookieParser.CookieParser.parseSetCookie(i.cookieLine);if(n&&!n.length||i.blockedReasons.includes("SyntaxError")||i.blockedReasons.includes("NameValuePairExceedsMaxSize")){r.push(i);continue}let o=i.cookie;!o&&n&&(o=n[0]),o&&(t.set(o,i.blockedReasons.map((e=>({attribute:s.NetworkRequest.setCookieBlockedReasonToAttribute(e),uiString:s.NetworkRequest.setCookieBlockedReasonToUiString(e)})))),e.push(o))}}return{responseCookies:e,responseCookieToBlockedReasons:t,malformedResponseCookies:r}}refreshRequestCookiesView(){if(!this.isShowing())return;this.request.hasRequestCookies()||this.request.responseCookies.length?this.emptyWidget.hideWidget():this.emptyWidget.showWidget();const{requestCookies:e,requestCookieToBlockedReasons:t}=this.getRequestCookies(),{responseCookies:r,responseCookieToBlockedReasons:i,malformedResponseCookies:n}=this.getResponseCookies();if(e.length?(this.requestCookiesTitle.classList.remove("hidden"),this.requestCookiesEmpty.classList.add("hidden"),this.requestCookiesTable.showWidget(),this.requestCookiesTable.setCookies(e,t)):this.request.blockedRequestCookies().length?(this.requestCookiesTitle.classList.remove("hidden"),this.requestCookiesEmpty.classList.remove("hidden"),this.requestCookiesTable.hideWidget()):(this.requestCookiesTitle.classList.add("hidden"),this.requestCookiesEmpty.classList.add("hidden"),this.requestCookiesTable.hideWidget()),r.length?(this.responseCookiesTitle.classList.remove("hidden"),this.responseCookiesTable.showWidget(),this.responseCookiesTable.setCookies(r,i)):(this.responseCookiesTitle.classList.add("hidden"),this.responseCookiesTable.hideWidget()),n.length){this.malformedResponseCookiesTitle.classList.remove("hidden"),this.malformedResponseCookiesList.classList.remove("hidden"),this.malformedResponseCookiesList.removeChildren();for(const e of n){const t=this.malformedResponseCookiesList.createChild("span","cookie-line source-code"),r=l.Icon.Icon.create("smallicon-error","cookie-warning-icon");t.appendChild(r),l.UIUtils.createTextChild(t,e.cookieLine),e.blockedReasons.includes("NameValuePairExceedsMaxSize")?t.title=s.NetworkRequest.setCookieBlockedReasonToUiString("NameValuePairExceedsMaxSize"):t.title=s.NetworkRequest.setCookieBlockedReasonToUiString("SyntaxError")}}else this.malformedResponseCookiesTitle.classList.add("hidden"),this.malformedResponseCookiesList.classList.add("hidden")}wasShown(){super.wasShown(),this.registerCSSFiles([d.default]),this.request.addEventListener(s.NetworkRequest.Events.RequestHeadersChanged,this.refreshRequestCookiesView,this),this.request.addEventListener(s.NetworkRequest.Events.ResponseHeadersChanged,this.refreshRequestCookiesView,this),this.refreshRequestCookiesView()}willHide(){this.request.removeEventListener(s.NetworkRequest.Events.RequestHeadersChanged,this.refreshRequestCookiesView,this),this.request.removeEventListener(s.NetworkRequest.Events.ResponseHeadersChanged,this.refreshRequestCookiesView,this)}}})),r.register("24XFe",(function(t,i){e(t.exports,"CookiesTable",(()=>r("fTlF2")));r("fTlF2")})),r.register("fTlF2",(function(t,i){e(t.exports,"CookiesTable",(()=>b)),e(t.exports,"DataGridNode",(()=>k));var n=r("koSS8"),o=r("ixFnt"),s=r("lz7WY"),a=r("9X2mn"),l=r("eQFvP"),d=r("fm4u4"),c=r("hE0P3"),h=r("9z2ZV"),u=r("cObcK"),p=r("kGHj8");const g={session:"Session",name:"Name",value:"Value",size:"Size",editableCookies:"Editable Cookies",cookies:"Cookies",na:"N/A",showRequestsWithThisCookie:"Show Requests With This Cookie",showIssueAssociatedWithThis:"Show issue associated with this cookie",sourcePortTooltip:"Shows the source port (range 1-65535) the cookie was set on. If the port is unknown, this shows -1.",sourceSchemeTooltip:"Shows the source scheme (`Secure`, `NonSecure`) the cookie was set on. If the scheme is unknown, this shows `Unset`.",timeAfter:"after {date}",timeAfterTooltip:"The expiration timestamp is {seconds}, which corresponds to a date after {date}",opaquePartitionKey:"(opaque)"},m=o.i18n.registerUIStrings("ui/legacy/components/cookie_table/CookiesTable.ts",g),f=o.i18n.getLocalizedString.bind(void 0,m),w=o.i18n.getLazilyComputedLocalizedString.bind(void 0,m)(g.session);class b extends h.Widget.VBox{saveCallback;refreshCallback;deleteCallback;dataGrid;lastEditedColumnId;data;cookieDomain;cookieToBlockedReasons;constructor(e,t,r,i,n){super(),this.element.classList.add("cookies-table"),this.saveCallback=t,this.refreshCallback=r,this.deleteCallback=n;const o=Boolean(t),s=[{id:l.Cookie.Attributes.Name,title:f(g.name),sortable:!0,disclosure:o,sort:u.DataGrid.Order.Ascending,longText:!0,weight:24,editable:o},{id:l.Cookie.Attributes.Value,title:f(g.value),sortable:!0,longText:!0,weight:34,editable:o},{id:l.Cookie.Attributes.Domain,title:"Domain",sortable:!0,weight:7,editable:o},{id:l.Cookie.Attributes.Path,title:"Path",sortable:!0,weight:7,editable:o},{id:l.Cookie.Attributes.Expires,title:"Expires / Max-Age",sortable:!0,weight:7,editable:o},{id:l.Cookie.Attributes.Size,title:f(g.size),sortable:!0,align:u.DataGrid.Align.Right,weight:7},{id:l.Cookie.Attributes.HttpOnly,title:"HttpOnly",sortable:!0,align:u.DataGrid.Align.Center,weight:7,dataType:u.DataGrid.DataType.Boolean,editable:o},{id:l.Cookie.Attributes.Secure,title:"Secure",sortable:!0,align:u.DataGrid.Align.Center,weight:7,dataType:u.DataGrid.DataType.Boolean,editable:o},{id:l.Cookie.Attributes.SameSite,title:"SameSite",sortable:!0,weight:7,editable:o},{id:l.Cookie.Attributes.SameParty,title:"SameParty",sortable:!0,align:u.DataGrid.Align.Center,weight:7,dataType:u.DataGrid.DataType.Boolean,editable:o},{id:l.Cookie.Attributes.PartitionKey,title:"Partition Key",sortable:!0,weight:7,editable:o},{id:l.Cookie.Attributes.Priority,title:"Priority",sortable:!0,sort:u.DataGrid.Order.Descending,weight:7,editable:o}];if(a.Runtime.experiments.isEnabled("experimentalCookieFeatures")){const e=[{id:l.Cookie.Attributes.SourceScheme,title:"SourceScheme",sortable:!0,align:u.DataGrid.Align.Center,weight:7,editable:o},{id:l.Cookie.Attributes.SourcePort,title:"SourcePort",sortable:!0,align:u.DataGrid.Align.Center,weight:7,editable:o}];s.push(...e)}this.dataGrid=o?new u.DataGrid.DataGridImpl({displayName:f(g.editableCookies),columns:s,editCallback:this.onUpdateCookie.bind(this),deleteCallback:this.onDeleteCookie.bind(this),refreshCallback:r}):new u.DataGrid.DataGridImpl({displayName:f(g.cookies),columns:s,editCallback:void 0,deleteCallback:void 0,refreshCallback:void 0}),this.dataGrid.setStriped(!0),this.dataGrid.setName("cookiesTable"),this.dataGrid.addEventListener(u.DataGrid.Events.SortingChanged,this.rebuildTable,this),this.dataGrid.setRowContextMenuCallback(this.populateContextMenu.bind(this)),e&&this.dataGrid.renderInline(),i&&this.dataGrid.addEventListener(u.DataGrid.Events.SelectedNode,i,this),this.lastEditedColumnId=null,this.dataGrid.asWidget().show(this.element),this.data=[],this.cookieDomain="",this.cookieToBlockedReasons=null}wasShown(){this.registerCSSFiles([p.default])}setCookies(e,t){this.setCookieFolders([{cookies:e,folderName:null}],t)}setCookieFolders(e,t){this.data=e,this.cookieToBlockedReasons=t||null,this.rebuildTable()}setCookieDomain(e){this.cookieDomain=e}selectedCookie(){const e=this.dataGrid.selectedNode;return e?e.cookie:null}getSelectionCookies(){const e=this.dataGrid.selectedNode,t=e&&e.traverseNextNode(!0),r=e&&e.traversePreviousNode(!0);return{current:e&&e.cookie,neighbor:t&&t.cookie||r&&r.cookie}}willHide(){this.lastEditedColumnId=null}findSelectedCookie(e,t){if(!t)return null;const r=e.current,i=t.find((e=>this.isSameCookie(e,r)));if(i)return i;const n=e.neighbor,o=t.find((e=>this.isSameCookie(e,n)));return o||null}isSameCookie(e,t){return null!=t&&t.name()===e.name()&&t.domain()===e.domain()&&t.path()===e.path()}rebuildTable(){const e=this.getSelectionCookies(),t=this.lastEditedColumnId;this.lastEditedColumnId=null,this.dataGrid.rootNode().removeChildren();for(let r=0;r<this.data.length;++r){const i=this.data[r],n=this.findSelectedCookie(e,i.cookies);if(i.folderName){const e={};e[l.Cookie.Attributes.Name]=i.folderName,e[l.Cookie.Attributes.Value]="",e[l.Cookie.Attributes.Size]=this.totalSize(i.cookies),e[l.Cookie.Attributes.Domain]="",e[l.Cookie.Attributes.Path]="",e[l.Cookie.Attributes.Expires]="",e[l.Cookie.Attributes.HttpOnly]="",e[l.Cookie.Attributes.Secure]="",e[l.Cookie.Attributes.SameSite]="",e[l.Cookie.Attributes.SameParty]="",e[l.Cookie.Attributes.SourcePort]="",e[l.Cookie.Attributes.SourceScheme]="",e[l.Cookie.Attributes.Priority]="";const r=new u.DataGrid.DataGridNode(e);r.selectable=!0,this.dataGrid.rootNode().appendChild(r),r.element().classList.add("row-group"),this.populateNode(r,i.cookies,n,t),r.expand()}else this.populateNode(this.dataGrid.rootNode(),i.cookies,n,t)}e.current&&t&&!this.dataGrid.selectedNode&&this.addInactiveNode(this.dataGrid.rootNode(),e.current,t),this.saveCallback&&this.dataGrid.addCreationNode(!1)}populateNode(e,t,r,i){if(e.removeChildren(),t){this.sortCookies(t);for(let n=0;n<t.length;++n){const o=t[n],s=this.createGridNode(o);e.appendChild(s),this.isSameCookie(o,r)&&(s.select(),null!==i&&this.dataGrid.startEditingNextEditableColumnOfDataGridNode(s,i))}}}addInactiveNode(e,t,r){const i=this.createGridNode(t);e.appendChild(i),i.select(),i.setInactive(!0),null!==r&&this.dataGrid.startEditingNextEditableColumnOfDataGridNode(i,r)}totalSize(e){let t=0;for(let r=0;e&&r<e.length;++r)t+=e[r].size();return t}sortCookies(e){const t=this.dataGrid.isSortOrderAscending()?1:-1;function r(e,t){switch(t){case l.Cookie.Attributes.Name:return String(e.name());case l.Cookie.Attributes.Value:return String(e.value());case l.Cookie.Attributes.Domain:return String(e.domain());case l.Cookie.Attributes.Path:return String(e.path());case l.Cookie.Attributes.HttpOnly:return String(e.httpOnly());case l.Cookie.Attributes.Secure:return String(e.secure());case l.Cookie.Attributes.SameSite:return String(e.sameSite());case l.Cookie.Attributes.SameParty:return String(e.sameParty());case l.Cookie.Attributes.PartitionKey:return e.partitionKeyOpaque()?f(g.opaquePartitionKey):String(e.partitionKey());case l.Cookie.Attributes.SourceScheme:return String(e.sourceScheme());default:return String(e.name())}}function i(e,r,i){return t*(e(r)-e(i))}let n;const o=this.dataGrid.sortColumnId()||l.Cookie.Attributes.Name;n=o===l.Cookie.Attributes.Expires?function(e,r){return e.session()!==r.session()?t*(e.session()?1:-1):e.session()?0:e.maxAge()&&r.maxAge()?t*(e.maxAge()-r.maxAge()):e.expires()&&r.expires()?t*(e.expires()-r.expires()):t*(e.expires()?1:-1)}:o===l.Cookie.Attributes.Size?i.bind(null,(e=>e.size())):o===l.Cookie.Attributes.SourcePort?i.bind(null,(e=>e.sourcePort())):o===l.Cookie.Attributes.Priority?function(e,r){const i=["Low","Medium","High"],n=i.indexOf(e.priority()),o=i.indexOf(r.priority());return t*(n-o)}:function(e,i,n){return t*s.StringUtilities.compare(r(i,e),r(n,e))}.bind(null,o),e.sort(n)}createGridNode(e){const t={};let r;if(t[l.Cookie.Attributes.Name]=e.name(),t[l.Cookie.Attributes.Value]=e.value(),e.type()===l.Cookie.Type.Request?(t[l.Cookie.Attributes.Domain]=e.domain()?e.domain():f(g.na),t[l.Cookie.Attributes.Path]=e.path()?e.path():f(g.na)):(t[l.Cookie.Attributes.Domain]=e.domain()||"",t[l.Cookie.Attributes.Path]=e.path()||""),e.maxAge())t[l.Cookie.Attributes.Expires]=o.TimeUtilities.secondsToString(Math.floor(e.maxAge()));else if(e.expires()){const i=e.expires();if(i<0)t[l.Cookie.Attributes.Expires]=w();else{const e=864e13;if(i>e){const n=new Date(e).toISOString();t[l.Cookie.Attributes.Expires]=f(g.timeAfter,{date:n}),r=f(g.timeAfterTooltip,{seconds:i,date:n})}else t[l.Cookie.Attributes.Expires]=new Date(i).toISOString()}}else t[l.Cookie.Attributes.Expires]=e.type()===l.Cookie.Type.Request?f(g.na):w();t[l.Cookie.Attributes.Size]=e.size(),t[l.Cookie.Attributes.HttpOnly]=e.httpOnly(),t[l.Cookie.Attributes.Secure]=e.secure(),t[l.Cookie.Attributes.SameSite]=e.sameSite()||"",t[l.Cookie.Attributes.SameParty]=e.sameParty(),t[l.Cookie.Attributes.SourcePort]=e.sourcePort(),t[l.Cookie.Attributes.SourceScheme]=e.sourceScheme(),t[l.Cookie.Attributes.Priority]=e.priority()||"",t[l.Cookie.Attributes.PartitionKey]=e.partitionKey()||"";const i=this.cookieToBlockedReasons?.get(e),n=new k(t,e,i||null);return r&&n.setExpiresTooltip(r),n.selectable=!0,n}onDeleteCookie(e){e.cookie&&this.deleteCallback&&this.deleteCallback(e.cookie,(()=>this.refresh()))}onUpdateCookie(e,t,r,i){this.lastEditedColumnId=t,this.setDefaults(e),this.isValidCookieData(e.data)?this.saveNode(e):e.setDirty(!0)}setDefaults(e){null===e.data[l.Cookie.Attributes.Name]&&(e.data[l.Cookie.Attributes.Name]=""),null===e.data[l.Cookie.Attributes.Value]&&(e.data[l.Cookie.Attributes.Value]=""),null===e.data[l.Cookie.Attributes.Domain]&&(e.data[l.Cookie.Attributes.Domain]=this.cookieDomain),null===e.data[l.Cookie.Attributes.Path]&&(e.data[l.Cookie.Attributes.Path]="/"),null===e.data[l.Cookie.Attributes.Expires]&&(e.data[l.Cookie.Attributes.Expires]=w()),null===e.data[l.Cookie.Attributes.PartitionKey]&&(e.data[l.Cookie.Attributes.PartitionKey]="")}saveNode(e){const t=e.cookie,r=this.createCookieFromData(e.data);e.cookie=r,this.saveCallback&&this.saveCallback(r,t).then((t=>{t?this.refresh():e.setDirty(!0)}))}createCookieFromData(e){const t=new l.Cookie.Cookie(e[l.Cookie.Attributes.Name],e[l.Cookie.Attributes.Value],null,e[l.Cookie.Attributes.Priority]);return t.addAttribute(l.Cookie.Attributes.Domain,e[l.Cookie.Attributes.Domain]),t.addAttribute(l.Cookie.Attributes.Path,e[l.Cookie.Attributes.Path]),e.expires&&e.expires!==w()&&t.addAttribute(l.Cookie.Attributes.Expires,new Date(e[l.Cookie.Attributes.Expires]).toUTCString()),e[l.Cookie.Attributes.HttpOnly]&&t.addAttribute(l.Cookie.Attributes.HttpOnly),e[l.Cookie.Attributes.Secure]&&t.addAttribute(l.Cookie.Attributes.Secure),e[l.Cookie.Attributes.SameSite]&&t.addAttribute(l.Cookie.Attributes.SameSite,e[l.Cookie.Attributes.SameSite]),e[l.Cookie.Attributes.SameParty]&&t.addAttribute(l.Cookie.Attributes.SameParty),l.Cookie.Attributes.SourceScheme in e&&t.addAttribute(l.Cookie.Attributes.SourceScheme,e[l.Cookie.Attributes.SourceScheme]),l.Cookie.Attributes.SourcePort in e&&t.addAttribute(l.Cookie.Attributes.SourcePort,Number.parseInt(e[l.Cookie.Attributes.SourcePort],10)||void 0),e[l.Cookie.Attributes.PartitionKey]&&t.addAttribute(l.Cookie.Attributes.PartitionKey,e[l.Cookie.Attributes.PartitionKey]),t.setSize(e[l.Cookie.Attributes.Name].length+e[l.Cookie.Attributes.Value].length),t}isValidCookieData(e){return(Boolean(e.name)||Boolean(e.value))&&this.isValidDomain(e.domain)&&this.isValidPath(e.path)&&this.isValidDate(e.expires)}isValidDomain(e){if(!e)return!0;const t=n.ParsedURL.ParsedURL.fromString("http://"+e);return null!==t&&t.domain()===e}isValidPath(e){const t=n.ParsedURL.ParsedURL.fromString("http://example.com"+e);return null!==t&&t.path===e}isValidDate(e){return""===e||e===w()||!isNaN(Date.parse(e))}refresh(){this.refreshCallback&&this.refreshCallback()}populateContextMenu(e,t){const r=t.cookie;if(!r)return;const i=r;e.revealSection().appendItem(f(g.showRequestsWithThisCookie),(()=>{const e=c.UIFilter.UIRequestFilter.filters([{filterType:c.UIFilter.FilterType.CookieDomain,filterValue:i.domain()},{filterType:c.UIFilter.FilterType.CookieName,filterValue:i.name()}]);n.Revealer.reveal(e)})),d.RelatedIssue.hasIssues(i)&&e.revealSection().appendItem(f(g.showIssueAssociatedWithThis),(()=>{d.RelatedIssue.reveal(i)}))}}class k extends u.DataGrid.DataGridNode{cookie;blockedReasons;expiresTooltip;constructor(e,t,r){super(e),this.cookie=t,this.blockedReasons=r}createCells(e){super.createCells(e),this.blockedReasons&&this.blockedReasons.length&&e.classList.add("flagged-cookie-attribute-row")}setExpiresTooltip(e){this.expiresTooltip=e}createCell(e){const t=super.createCell(e);e===l.Cookie.Attributes.SourcePort?h.Tooltip.Tooltip.install(t,f(g.sourcePortTooltip)):e===l.Cookie.Attributes.SourceScheme?h.Tooltip.Tooltip.install(t,f(g.sourceSchemeTooltip)):e===l.Cookie.Attributes.Expires&&this.expiresTooltip?h.Tooltip.Tooltip.install(t,this.expiresTooltip):h.Tooltip.Tooltip.install(t,t.textContent||"");let r="";if(this.blockedReasons)for(const t of this.blockedReasons){const i=t.attribute===e,n=!t.attribute&&e===l.Cookie.Attributes.Name;(i||n)&&(r&&(r+="\n"),r+=t.uiString)}if(r){const e=h.Icon.Icon.create("smallicon-info","cookie-warning-icon");h.Tooltip.Tooltip.install(e,r),t.insertBefore(e,t.firstChild),t.classList.add("flagged-cookie-attribute-cell")}return t}}})),r.register("kGHj8",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright (c) 2019 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.cookies-table {\n  --override-flagged-row-background-color-odd: rgb(247 234 161);\n  --override-flagged-row-background-color-even: rgb(255 240 155);\n}\n\n.-theme-with-dark-background .cookies-table,\n:host-context(.-theme-with-dark-background) .cookies-table {\n  --override-flagged-row-background-color-odd: rgb(94 81 8);\n  --override-flagged-row-background-color-even: rgb(121 103 0);\n}\n\n.cookies-table .cookie-warning-icon {\n  margin-right: 4px;\n}\n\n.cookies-table td.flagged-cookie-attribute-cell .cookie-warning-icon {\n  filter: grayscale();\n}\n\n.cookies-table tr.revealed.data-grid-data-grid-node.flagged-cookie-attribute-row:not(.selected):nth-child(odd) {\n  background-color: var(--override-flagged-row-background-color-odd);\n}\n\n.cookies-table tr.revealed.data-grid-data-grid-node.flagged-cookie-attribute-row:not(.selected):nth-child(even) {\n  background-color: var(--override-flagged-row-background-color-odd);\n}\n\n/*# sourceURL=cookiesTable.css */\n");var n=i})),r.register("6zqMx",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright (c) 2014 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.request-cookies-view {\n  overflow: auto;\n  padding: 12px;\n  height: 100%;\n  background-color: var(--color-background);\n}\n\n.request-cookies-view .request-cookies-title {\n  font-size: 12px;\n  font-weight: bold;\n  margin-right: 30px;\n  color: var(--color-text-primary);\n}\n\n.request-cookies-view .cookie-line {\n  margin-top: 6px;\n  display: inline-block;\n}\n\n.request-cookies-view .cookies-panel-item {\n  margin-top: 6px;\n  margin-bottom: 16px;\n  flex: none;\n}\n\n@media (forced-colors: active) {\n  td.flagged-cookie-attribute-cell .cookie-warning-icon {\n    forced-color-adjust: none;\n    filter: grayscale();\n  }\n}\n\n/*# sourceURL=requestCookiesView.css */\n");var n=i})),r.register("kmtft",(function(t,i){e(t.exports,"RequestHeadersView",(()=>C)),e(t.exports,"Category",(()=>y));var n=r("koSS8"),o=r("e7bLS"),s=r("ixFnt"),a=r("lz7WY"),l=r("eQFvP"),d=r("fm4u4"),c=r("hE0P3");r("RCzYD");var h=r("buS3j"),u=r("f2hYB"),p=r("eKcu2"),g=r("9z2ZV"),m=r("jHyi6"),f=r("bO0Qf");const w={general:"General",copyValue:"Copy value",learnMoreInTheIssuesTab:"Learn more in the issues tab",learnMore:"Learn more",requestUrl:"Request URL",showMore:"Show more",viewParsed:"View parsed",viewSource:"View source",requestHeaders:"Request Headers",responseHeaders:"Response Headers",statusCode:"Status Code",requestMethod:"Request Method",fromMemoryCache:"(from memory cache)",fromServiceWorker:"(from `service worker`)",fromSignedexchange:"(from signed-exchange)",fromPrefetchCache:"(from prefetch cache)",fromDiskCache:"(from disk cache)",fromWebBundle:"(from Web Bundle)",provisionalHeadersAreShownS:"Provisional headers are shown. Disable cache to see full headers.",onlyProvisionalHeadersAre:"Only provisional headers are available because this request was not sent over the network and instead was served from a local cache, which doesn’t store the original request headers. Disable cache to see full request headers.",provisionalHeadersAreShown:"Provisional headers are shown",activeClientExperimentVariation:"Active `client experiment variation IDs`.",activeClientExperimentVariationIds:"Active `client experiment variation IDs` that trigger server-side behavior.",decoded:"Decoded:",remoteAddress:"Remote Address",referrerPolicy:"Referrer Policy",toEmbedThisFrameInYourDocument:"To embed this frame in your document, the response needs to enable the cross-origin embedder policy by specifying the following response header:",toUseThisResourceFromADifferent:"To use this resource from a different origin, the server needs to specify a cross-origin resource policy in the response headers:",chooseThisOptionIfTheResourceAnd:"Choose this option if the resource and the document are served from the same site.",onlyChooseThisOptionIfAn:"Only choose this option if an arbitrary website including this resource does not impose a security risk.",thisDocumentWasBlockedFrom:"This document was blocked from loading in an `iframe` with a `sandbox` attribute because this document specified a cross-origin opener policy.",toUseThisResourceFromADifferentSite:"To use this resource from a different site, the server may relax the cross-origin resource policy response header:",toUseThisResourceFromADifferentOrigin:"To use this resource from a different origin, the server may relax the cross-origin resource policy response header:",recordedAttribution:"Recorded attribution with `trigger-data`: {PH1}"},b=s.i18n.registerUIStrings("panels/network/RequestHeadersView.ts",w),k=s.i18n.getLocalizedString.bind(void 0,b),v=s.i18n.getLazilyComputedLocalizedString.bind(void 0,b);class C extends g.Widget.VBox{request;showRequestHeadersText;showResponseHeadersText;highlightedElement;root;urlItem;requestMethodItem;statusCodeItem;remoteAddressItem;referrerPolicyItem;responseHeadersCategory;requestHeadersCategory;constructor(e){super(),this.element.classList.add("request-headers-view"),this.request=e,this.showRequestHeadersText=!1,this.showResponseHeadersText=!1,this.highlightedElement=null;const t=new g.TreeOutline.TreeOutlineInShadow;t.registerCSSFiles([p.default,u.default,m.default]),t.element.classList.add("request-headers-tree"),t.makeDense(),t.setUseLightSelectionColor(!0),this.element.appendChild(t.element);const r=new y(t,"general",k(w.general));r.hidden=!1,this.root=r,this.setDefaultFocusedElement(this.root.listItemElement),this.urlItem=r.createLeaf(),this.requestMethodItem=r.createLeaf(),S.set(this.requestMethodItem,"Request-Method"),this.statusCodeItem=r.createLeaf(),S.set(this.statusCodeItem,"Status-Code"),this.remoteAddressItem=r.createLeaf(),this.remoteAddressItem.hidden=!0,this.referrerPolicyItem=r.createLeaf(),this.referrerPolicyItem.hidden=!0,this.responseHeadersCategory=new y(t,"responseHeaders",""),this.requestHeadersCategory=new y(t,"requestHeaders","")}wasShown(){this.clearHighlight(),this.registerCSSFiles([f.default]),this.request.addEventListener(l.NetworkRequest.Events.RemoteAddressChanged,this.refreshRemoteAddress,this),this.request.addEventListener(l.NetworkRequest.Events.RequestHeadersChanged,this.refreshRequestHeaders,this),this.request.addEventListener(l.NetworkRequest.Events.ResponseHeadersChanged,this.refreshResponseHeaders,this),this.request.addEventListener(l.NetworkRequest.Events.FinishedLoading,this.refreshHTTPInformation,this),this.refreshURL(),this.refreshRequestHeaders(),this.refreshResponseHeaders(),this.refreshHTTPInformation(),this.refreshRemoteAddress(),this.refreshReferrerPolicy(),this.root.select(!0,!1)}willHide(){this.request.removeEventListener(l.NetworkRequest.Events.RemoteAddressChanged,this.refreshRemoteAddress,this),this.request.removeEventListener(l.NetworkRequest.Events.RequestHeadersChanged,this.refreshRequestHeaders,this),this.request.removeEventListener(l.NetworkRequest.Events.ResponseHeadersChanged,this.refreshResponseHeaders,this),this.request.removeEventListener(l.NetworkRequest.Events.FinishedLoading,this.refreshHTTPInformation,this)}addEntryContextMenuHandler(e,t){e.listItemElement.addEventListener("contextmenu",(e=>{e.consume(!0);const r=new g.ContextMenu.ContextMenu(e),i=decodeURIComponent(t);r.clipboardSection().appendItem(k(w.copyValue),(()=>{o.userMetrics.actionTaken(o.UserMetrics.Action.NetworkPanelCopyValue),o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(i)})),r.show()}))}formatHeader(e,t){const r=document.createDocumentFragment();return r.createChild("div","header-name").textContent=e+": ",r.createChild("span","header-separator"),r.createChild("div","header-value source-code").textContent=t,r}formatHeaderObject(e){const t=document.createDocumentFragment();if(e.headerNotSet&&(t.createChild("div","header-badge header-badge-error header-badge-text").textContent="not-set"),"location"===e.name.toLowerCase()&&this.request.canceled){const r=function(e){return"/.well-known/attribution-reporting/trigger-attribution"===e.pathname&&e.searchParams.has("trigger-data")?e.searchParams.get("trigger-data"):null}(new URL(e.value?.toString()||"",this.request.parsedURL.securityOrigin()));r&&(t.createChild("div","header-badge header-badge-success header-badge-text").textContent="Attribution Reporting API",e.details={explanation:()=>k(w.recordedAttribution,{PH1:r}),examples:[],link:null})}const r=e.value?": ":"";if(t.createChild("div","header-name").textContent=e.name+r,t.createChild("span","header-separator"),e.value&&(e.headerValueIncorrect?t.createChild("div","header-value source-code header-warning").textContent=e.value.toString():t.createChild("div","header-value source-code").textContent=e.value.toString()),e.details){const r=t.createChild("div","header-details").createChild("div","call-to-action").createChild("div","call-to-action-body");r.createChild("div","explanation").textContent=e.details.explanation();for(const t of e.details.examples){const e=r.createChild("div","example");e.createChild("code").textContent=t.codeSnippet,t.comment&&(e.createChild("span","comment").textContent=t.comment())}if(d.RelatedIssue.hasIssueOfCategory(this.request,d.Issue.IssueCategory.CrossOriginEmbedderPolicy)){const e=document.createElement("div");e.classList.add("devtools-link"),e.onclick=()=>{o.userMetrics.issuesPanelOpenedFrom(o.UserMetrics.IssueOpener.LearnMoreLinkCOEP),d.RelatedIssue.reveal(this.request,d.Issue.IssueCategory.CrossOriginEmbedderPolicy)};const t=document.createElement("span");t.classList.add("devtools-link"),t.textContent=k(w.learnMoreInTheIssuesTab),e.appendChild(t),e.prepend(g.Icon.Icon.create("largeicon-breaking-change","icon")),r.appendChild(e)}else if(e.details.link){const t=g.XLink.XLink.create(e.details.link.url,k(w.learnMore),"link");t.prepend(g.Icon.Icon.create("largeicon-link")),r.appendChild(t)}}return t}refreshURL(){const e=this.request.url();this.urlItem.title=this.formatHeader(k(w.requestUrl),e),this.addEntryContextMenuHandler(this.urlItem,e)}populateTreeElementWithSourceText(e,t){const r=(t||"").trim(),i=r.length>3e3,n=document.createElement("span");n.classList.add("header-value"),n.classList.add("source-code"),n.textContent=i?r.substr(0,3e3):r;const o=new g.TreeOutline.TreeElement(n);if(e.removeChildren(),e.appendChild(o),!i)return;const s=document.createElement("button");function a(){s.remove(),n.textContent=r,o.listItemElement.removeEventListener("contextmenu",l)}function l(e){const t=new g.ContextMenu.ContextMenu(e);t.newSection().appendItem(k(w.showMore),a),t.show()}s.classList.add("request-headers-show-more-button"),s.textContent=k(w.showMore),s.addEventListener("click",a),o.listItemElement.addEventListener("contextmenu",l),n.appendChild(s)}refreshRequestHeaders(){const e=this.requestHeadersCategory,t=this.request.requestHeaders().slice();t.sort((function(e,t){return a.StringUtilities.compare(e.name.toLowerCase(),t.name.toLowerCase())}));const r=this.request.requestHeadersText();if(this.showRequestHeadersText&&r?this.refreshHeadersText(k(w.requestHeaders),t.length,r,e):this.refreshHeaders(k(w.requestHeaders),t,e,void 0===r),r){const t=this.createHeadersToggleButton(this.showRequestHeadersText);t.addEventListener("click",this.toggleRequestHeadersText.bind(this),!1),e.listItemElement.appendChild(t)}}refreshResponseHeaders(){const e=this.responseHeadersCategory,t=this.request.sortedResponseHeaders.slice(),r=this.request.responseHeadersText;if(this.showResponseHeadersText)this.refreshHeadersText(k(w.responseHeaders),t.length,r,e);else{const r=[];if(this.request.wasBlocked()){const e=T.get(this.request.blockedReason());e&&r.push(e)}this.refreshHeaders(k(w.responseHeaders),function(e,t){let r=0,i=0;const n=[];for(;r<e.length||i<t.length;)r<e.length&&(i>=t.length||e[r].name<t[i].name)?n.push({...e[r++],headerNotSet:!1}):i<t.length&&(r>=e.length||e[r].name>t[i].name)?n.push({...t[i++],headerNotSet:!0}):r<e.length&&i<t.length&&e[r].name===t[i].name&&n.push({...t[i++],...e[r++],headerNotSet:!1});return n}(t,r),e,!1,this.request.blockedResponseCookies())}if(r){const t=this.createHeadersToggleButton(this.showResponseHeadersText);t.addEventListener("click",this.toggleResponseHeadersText.bind(this),!1),e.listItemElement.appendChild(t)}}refreshHTTPInformation(){const e=this.requestMethodItem;e.hidden=!this.request.statusCode;const t=this.statusCodeItem;if(t.hidden=!this.request.statusCode,this.request.statusCode){const r=document.createDocumentFragment();r.createChild("div","header-name").textContent=k(w.statusCode)+": ",r.createChild("span","header-separator");const i=r.createChild("span","resource-status-image","dt-icon-label");g.Tooltip.Tooltip.install(i,this.request.statusCode+" "+this.request.statusText),this.request.statusCode<300||304===this.request.statusCode?i.type="smallicon-green-ball":this.request.statusCode<400?i.type="smallicon-orange-ball":i.type="smallicon-red-ball",e.title=this.formatHeader(k(w.requestMethod),this.request.requestMethod);const n=r.createChild("div","header-value source-code");let o=this.request.statusCode+" "+this.request.statusText;this.request.cachedInMemory()?(o+=" "+k(w.fromMemoryCache),n.classList.add("status-from-cache")):this.request.fetchedViaServiceWorker?(o+=" "+k(w.fromServiceWorker),n.classList.add("status-from-cache")):this.request.redirectSourceSignedExchangeInfoHasNoErrors()?(o+=" "+k(w.fromSignedexchange),n.classList.add("status-from-cache")):this.request.webBundleInnerRequestInfo()?(o+=" "+k(w.fromWebBundle),n.classList.add("status-from-cache")):this.request.fromPrefetchCache()?(o+=" "+k(w.fromPrefetchCache),n.classList.add("status-from-cache")):this.request.cached()&&(o+=" "+k(w.fromDiskCache),n.classList.add("status-from-cache")),n.textContent=o,t.title=r}}refreshHeadersTitle(e,t,r){t.listItemElement.removeChildren(),t.listItemElement.createChild("div","selection fill"),g.UIUtils.createTextChild(t.listItemElement,e);const i=` (${r})`;t.listItemElement.createChild("span","header-count").textContent=i}refreshHeaders(e,t,r,i,n){r.removeChildren();const o=t.length;if(this.refreshHeadersTitle(e,r,o),i){let e,t="";this.request.cachedInMemory()||this.request.cached()?(e=k(w.provisionalHeadersAreShownS),t=k(w.onlyProvisionalHeadersAre)):e=k(w.provisionalHeadersAreShown);const i=document.createElement("div");i.classList.add("request-headers-caution"),g.Tooltip.Tooltip.install(i,t),i.createChild("span","","dt-icon-label").type="smallicon-warning",i.createChild("div","caution").textContent=e;const n=new g.TreeOutline.TreeElement(i);i.createChild("div","learn-more").appendChild(g.XLink.XLink.create("https://developer.chrome.com/docs/devtools/network/reference/#provisional-headers",k(w.learnMore))),r.appendChild(n)}const s=new Map;n&&n.forEach((e=>{s.set(e.cookieLine,e.blockedReasons)})),r.hidden=!o&&!i;for(const e of t){const t=new g.TreeOutline.TreeElement(this.formatHeaderObject(e));S.set(t,e.name);const i=e.name.toLowerCase();if("set-cookie"===i){const r=s.get(e.value);if(r){const e=g.Icon.Icon.create("smallicon-warning","");t.listItemElement.appendChild(e);let i="";for(const e of r)i&&(i+="\n"),i+=l.NetworkRequest.setCookieBlockedReasonToUiString(e);g.Tooltip.Tooltip.install(e,i)}}if(this.addEntryContextMenuHandler(t,e.value),r.appendChild(t),"x-client-data"===i){const r=h.parseClientVariations(e.value),i=h.formatClientVariations(r,k(w.activeClientExperimentVariation),k(w.activeClientExperimentVariationIds)),n=document.createElement("div");n.classList.add("x-client-data-details"),g.UIUtils.createTextChild(n,k(w.decoded));const o=n.createChild("div");o.classList.add("source-code"),o.textContent=i,t.listItemElement.appendChild(n)}}}refreshHeadersText(e,t,r,i){this.populateTreeElementWithSourceText(i,r),this.refreshHeadersTitle(e,i,t)}refreshRemoteAddress(){const e=this.request.remoteAddress(),t=this.remoteAddressItem;t.hidden=!e,e&&(t.title=this.formatHeader(k(w.remoteAddress),e))}refreshReferrerPolicy(){const e=this.request.referrerPolicy(),t=this.referrerPolicyItem;t.hidden=!e,e&&(t.title=this.formatHeader(k(w.referrerPolicy),e))}toggleRequestHeadersText(e){this.showRequestHeadersText=!this.showRequestHeadersText,this.refreshRequestHeaders(),e.consume()}toggleResponseHeadersText(e){this.showResponseHeadersText=!this.showResponseHeadersText,this.refreshResponseHeaders(),e.consume()}createToggleButton(e){const t=document.createElement("span");return t.classList.add("header-toggle"),t.textContent=e,t}createHeadersToggleButton(e){const t=k(e?w.viewParsed:w.viewSource);return this.createToggleButton(t)}clearHighlight(){this.highlightedElement&&this.highlightedElement.listItemElement.classList.remove("header-highlight"),this.highlightedElement=null}revealAndHighlight(e,t){if(this.clearHighlight(),e){if(t)for(const r of e.children())if(S.get(r)?.toUpperCase()===t.toUpperCase())return this.highlightedElement=r,r.reveal(),void r.listItemElement.classList.add("header-highlight");e.childCount()>0&&e.childAt(0)?.reveal()}}getCategoryForSection(e){switch(e){case c.UIRequestLocation.UIHeaderSection.General:return this.root;case c.UIRequestLocation.UIHeaderSection.Request:return this.requestHeadersCategory;case c.UIRequestLocation.UIHeaderSection.Response:return this.responseHeadersCategory}}revealHeader(e,t){this.revealAndHighlight(this.getCategoryForSection(e),t)}}const S=new WeakMap;class y extends g.TreeOutline.TreeElement{toggleOnClick;expandedSetting;expanded;constructor(e,t,r){super(r||"",!0),this.toggleOnClick=!0,this.hidden=!0,this.expandedSetting=n.Settings.Settings.instance().createSetting("request-info-"+t+"-category-expanded",!0),this.expanded=this.expandedSetting.get(),e.appendChild(this)}createLeaf(){const e=new g.TreeOutline.TreeElement;return this.appendChild(e),e}onexpand(){this.expandedSetting.set(!0)}oncollapse(){this.expandedSetting.set(!1)}}const T=new Map([["coep-frame-resource-needs-coep-header",{name:"cross-origin-embedder-policy",value:null,headerValueIncorrect:null,details:{explanation:v(w.toEmbedThisFrameInYourDocument),examples:[{codeSnippet:"Cross-Origin-Embedder-Policy: require-corp",comment:void 0}],link:{url:"https://web.dev/coop-coep/"}},headerNotSet:null}],["corp-not-same-origin-after-defaulted-to-same-origin-by-coep",{name:"cross-origin-resource-policy",value:null,headerValueIncorrect:null,details:{explanation:v(w.toUseThisResourceFromADifferent),examples:[{codeSnippet:"Cross-Origin-Resource-Policy: same-site",comment:v(w.chooseThisOptionIfTheResourceAnd)},{codeSnippet:"Cross-Origin-Resource-Policy: cross-origin",comment:v(w.onlyChooseThisOptionIfAn)}],link:{url:"https://web.dev/coop-coep/"}},headerNotSet:null}],["coop-sandboxed-iframe-cannot-navigate-to-coop-page",{name:"cross-origin-opener-policy",value:null,headerValueIncorrect:!1,details:{explanation:v(w.thisDocumentWasBlockedFrom),examples:[],link:{url:"https://web.dev/coop-coep/"}},headerNotSet:null}],["corp-not-same-site",{name:"cross-origin-resource-policy",value:null,headerValueIncorrect:!0,details:{explanation:v(w.toUseThisResourceFromADifferentSite),examples:[{codeSnippet:"Cross-Origin-Resource-Policy: cross-origin",comment:v(w.onlyChooseThisOptionIfAn)}],link:null},headerNotSet:null}],["corp-not-same-origin",{name:"cross-origin-resource-policy",value:null,headerValueIncorrect:!0,details:{explanation:v(w.toUseThisResourceFromADifferentOrigin),examples:[{codeSnippet:"Cross-Origin-Resource-Policy: same-site",comment:v(w.chooseThisOptionIfTheResourceAnd)},{codeSnippet:"Cross-Origin-Resource-Policy: cross-origin",comment:v(w.onlyChooseThisOptionIfAn)}],link:null},headerNotSet:null}]])})),r.register("RCzYD",(function(t,i){e(t.exports,"parseClientVariations",(()=>r("buS3j").parseClientVariations)),e(t.exports,"formatClientVariations",(()=>r("buS3j").formatClientVariations)),r("buS3j")})),r.register("buS3j",(function(t,i){e(t.exports,"parseClientVariations",(()=>s)),e(t.exports,"formatClientVariations",(()=>a));var n=r("6ZWSX").Buffer;const o={};function s(e){return o.parseClientVariations(e)}function a(e){return o.formatClientVariations(e)}(function(){var e=this||self;function t(t,r){t=t.split(".");var i,n=e;t[0]in n||void 0===n.execScript||n.execScript("var "+t[0]);for(;t.length&&(i=t.shift());)t.length||void 0===r?n=n[i]&&n[i]!==Object.prototype[i]?n[i]:n[i]={}:n[i]=r}function r(e,t){function r(){}r.prototype=t.prototype,e.m=t.prototype,e.prototype=new r,e.prototype.constructor=e,e.base=function(e,r,i){for(var n=Array(arguments.length-2),o=2;o<arguments.length;o++)n[o-2]=arguments[o];return t.prototype[r].apply(e,n)}}function i(e){if(Error.captureStackTrace)Error.captureStackTrace(this,i);else{const e=Error().stack;e&&(this.stack=e)}e&&(this.message=String(e))}function o(e,t){for(var r="",n=(e=e.split("%s")).length-1,o=0;o<n;o++)r+=e[o]+(o<t.length?t[o]:"%s");i.call(this,r+e[n])}function s(e,t){throw new o("Failure"+(e?": "+e:""),Array.prototype.slice.call(arguments,1))}function a(){this.a=""}function l(){this.l=""}function d(){this.j=""}function c(){this.a=""}r(i,Error),i.prototype.name="CustomError",r(o,i),o.prototype.name="AssertionError",a.prototype.toString=function(){return"SafeScript{"+this.a+"}"},a.prototype.g=function(e){this.a=e},(new a).g(""),l.prototype.toString=function(){return"SafeStyle{"+this.l+"}"},l.prototype.g=function(e){this.l=e},(new l).g(""),d.prototype.toString=function(){return"SafeStyleSheet{"+this.j+"}"},d.prototype.g=function(e){this.j=e},(new d).g(""),c.prototype.toString=function(){return"SafeHtml{"+this.a+"}"},c.prototype.g=function(e){this.a=e},(new c).g("<!DOCTYPE html>"),(new c).g(""),(new c).g("<br>");var h=null;function u(e){var t=e.length,r=3*t/4;r%3?r=Math.floor(r):-1!="=.".indexOf(e[t-1])&&(r=-1!="=.".indexOf(e[t-2])?r-2:r-1);var i=new Uint8Array(r),n=0;return function(e,t){function r(t){for(;i<e.length;){var r=e.charAt(i++),n=h[r];if(null!=n)return n;if(!/^[\s\xa0]*$/.test(r))throw Error("Unknown base64 encoding at char: "+r)}return t}!function(){if(!h){h={};for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),t=["+/=","+/","-_=","-_.","-_"],r=0;5>r;r++)for(var i=e.concat(t[r].split("")),n=0;n<i.length;n++){var o=i[n];void 0===h[o]&&(h[o]=n)}}}();for(var i=0;;){var n=r(-1),o=r(0),s=r(64),a=r(64);if(64===a&&-1===n)break;t(n<<2|o>>4),64!=s&&(t(o<<4&240|s>>2),64!=a&&t(s<<6&192|a))}}(e,(function(e){i[n++]=e})),i.subarray(0,n)}function p(e){this.b=null,this.a=this.h=this.i=0,e&&m(this,e)}var g=[];function m(e,t){e.b=function(e){return e.constructor===Uint8Array?e:e.constructor===ArrayBuffer||void 0!==n&&e.constructor===n||e.constructor===Array?new Uint8Array(e):e.constructor===String?u(e):(s("Type not convertible to Uint8Array."),new Uint8Array(0))}(t),e.i=0,e.h=e.b.length,e.a=e.i}function f(e){if(g.length){var t=g.pop();e&&m(t,e),e=t}else e=new p(e);this.a=e,this.h=this.a.a,this.b=this.c=-1,this.f=!1}function w(e){var t=e.a;if(t.a==t.h)return!1;if((t=e.f)||(t=0>(t=e.a).a||t.a>t.h),t)return s("Decoder hit an error"),!1;e.h=e.a.a;var r=e.a.f();return t=r>>>3,0!=(r&=7)&&5!=r&&1!=r&&2!=r&&3!=r&&4!=r?(s("Invalid wire type: %s (at position %s)",r,e.h),e.f=!0,!1):(e.c=t,e.b=r,!0)}function b(e){switch(e.b){case 0:if(0!=e.b)s("Invalid wire type for skipVarintField"),b(e);else{for(e=e.a;128&e.b[e.a];)e.a++;e.a++}break;case 1:1!=e.b?(s("Invalid wire type for skipFixed64Field"),b(e)):(e=e.a).a+=8;break;case 2:if(2!=e.b)s("Invalid wire type for skipDelimitedField"),b(e);else{var t=e.a.f();(e=e.a).a+=t}break;case 5:5!=e.b?(s("Invalid wire type for skipFixed32Field"),b(e)):(e=e.a).a+=4;break;case 3:for(t=e.c;;){if(!w(e)){s("Unmatched start-group tag: stream EOF"),e.f=!0;break}if(4==e.b){e.c!=t&&(s("Unmatched end-group tag"),e.f=!0);break}b(e)}break;default:s("Invalid wire encoding for field.")}}function k(e,t){var r=e.a.f();r=e.a.a+r;for(var i=[];e.a.a<r;)i.push(t.call(e.a));return i}function v(){}p.prototype.reset=function(){this.a=this.i},p.prototype.f=function(){var e=this.b,t=e[this.a],r=127&t;return 128>t?(this.a+=1,r):(r|=(127&(t=e[this.a+1]))<<7,128>t?(this.a+=2,r):(r|=(127&(t=e[this.a+2]))<<14,128>t?(this.a+=3,r):(r|=(127&(t=e[this.a+3]))<<21,128>t?(this.a+=4,r):(r|=(15&(t=e[this.a+4]))<<28,128>t?(this.a+=5,r>>>0):(this.a+=5,128<=e[this.a++]&&128<=e[this.a++]&&128<=e[this.a++]&&128<=e[this.a++]&&this.a++,r)))))},p.prototype.c=p.prototype.f,f.prototype.reset=function(){this.a.reset(),this.b=this.c=-1};var C="function"==typeof Uint8Array,S=Object.freeze?Object.freeze([]):[];function y(e,t){if(t<e.c){t+=e.f;var r=e.a[t];return r===S?e.a[t]=[]:r}if(e.b)return(r=e.b[t])===S?e.b[t]=[]:r}function T(e){var t=e;e=R,t||(t=[]),this.f=-1,this.a=t;e:{if(t=this.a.length){--t;var r=this.a[t];if(!(null===r||"object"!=typeof r||Array.isArray(r)||C&&r instanceof Uint8Array)){this.c=t- -1,this.b=r;break e}}this.c=Number.MAX_VALUE}if(e)for(t=0;t<e.length;t++)if((r=e[t])<this.c)r+=-1,this.a[r]=this.a[r]||S;else{var i=this.c+-1;this.a[i]||(this.b=this.a[i]={}),this.b[r]=this.b[r]||S}}v.prototype.toString=function(){return this.a.toString()},r(T,v);var R=[1,3];function x(e){e=new f(e);for(var t=new T;w(e)&&4!=e.b;)switch(e.c){case 1:for(var r=2==e.b?k(e,e.a.c):[e.a.c()],i=0;i<r.length;i++){var n=r[i];y(t,1).push(n)}break;case 3:for(r=2==e.b?k(e,e.a.c):[e.a.c()],i=0;i<r.length;i++)n=r[i],y(t,3).push(n);break;default:b(e)}return t}t("parseClientVariations",(function(e){var t="";try{t=atob(e)}catch(e){}e=[];for(let r=0;r<t.length;r++)e.push(t.charCodeAt(r));t=null;try{t=x(e)}catch(e){t=x([])}return{variationIds:y(t,1),triggerVariationIds:y(t,3)}})),t("formatClientVariations",(function(e,t="Active client experiment variation IDs.",r="Active client experiment variation IDs that trigger server-side behavior."){const i=e.variationIds;e=e.triggerVariationIds;const n=["message ClientVariations {"];return i.length&&n.push(`  // ${t}`,`  repeated int32 variation_id = [${i.join(", ")}];`),e.length&&n.push(`  // ${r}`,`  repeated int32 trigger_variation_id = [${e.join(", ")}];`),n.push("}"),n.join("\n")}))}).call(o)})),r.register("6ZWSX",(function(t,i){var n,o;e(t.exports,"Buffer",(()=>n),(e=>n=e)),e(t.exports,"INSPECT_MAX_BYTES",(()=>o),(e=>o=e));var s=r("kuxul"),a=r("9NvM5");const l="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;n=h,o=50;const d=2147483647;function c(e){if(e>d)throw new RangeError('The value "'+e+'" is invalid for option "size"');const t=new Uint8Array(e);return Object.setPrototypeOf(t,h.prototype),t}function h(e,t,r){if("number"==typeof e){if("string"==typeof t)throw new TypeError('The "string" argument must be of type string. Received type number');return g(e)}return u(e,t,r)}function u(e,t,r){if("string"==typeof e)return function(e,t){"string"==typeof t&&""!==t||(t="utf8");if(!h.isEncoding(t))throw new TypeError("Unknown encoding: "+t);const r=0|b(e,t);let i=c(r);const n=i.write(e,t);n!==r&&(i=i.slice(0,n));return i}(e,t);if(ArrayBuffer.isView(e))return function(e){if(Q(e,Uint8Array)){const t=new Uint8Array(e);return f(t.buffer,t.byteOffset,t.byteLength)}return m(e)}(e);if(null==e)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(Q(e,ArrayBuffer)||e&&Q(e.buffer,ArrayBuffer))return f(e,t,r);if("undefined"!=typeof SharedArrayBuffer&&(Q(e,SharedArrayBuffer)||e&&Q(e.buffer,SharedArrayBuffer)))return f(e,t,r);if("number"==typeof e)throw new TypeError('The "value" argument must not be of type number. Received type number');const i=e.valueOf&&e.valueOf();if(null!=i&&i!==e)return h.from(i,t,r);const n=function(e){if(h.isBuffer(e)){const t=0|w(e.length),r=c(t);return 0===r.length||e.copy(r,0,0,t),r}if(void 0!==e.length)return"number"!=typeof e.length||J(e.length)?c(0):m(e);if("Buffer"===e.type&&Array.isArray(e.data))return m(e.data)}(e);if(n)return n;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return h.from(e[Symbol.toPrimitive]("string"),t,r);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function p(e){if("number"!=typeof e)throw new TypeError('"size" argument must be of type number');if(e<0)throw new RangeError('The value "'+e+'" is invalid for option "size"')}function g(e){return p(e),c(e<0?0:0|w(e))}function m(e){const t=e.length<0?0:0|w(e.length),r=c(t);for(let i=0;i<t;i+=1)r[i]=255&e[i];return r}function f(e,t,r){if(t<0||e.byteLength<t)throw new RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw new RangeError('"length" is outside of buffer bounds');let i;return i=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),Object.setPrototypeOf(i,h.prototype),i}function w(e){if(e>=d)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+d.toString(16)+" bytes");return 0|e}function b(e,t){if(h.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||Q(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);const r=e.length,i=arguments.length>2&&!0===arguments[2];if(!i&&0===r)return 0;let n=!1;for(;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return Y(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return X(e).length;default:if(n)return i?-1:Y(e).length;t=(""+t).toLowerCase(),n=!0}}function k(e,t,r){let i=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return N(this,t,r);case"utf8":case"utf-8":return L(this,t,r);case"ascii":return E(this,t,r);case"latin1":case"binary":return A(this,t,r);case"base64":return q(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return P(this,t,r);default:if(i)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),i=!0}}function v(e,t,r){const i=e[t];e[t]=e[r],e[r]=i}function C(e,t,r,i,n){if(0===e.length)return-1;if("string"==typeof r?(i=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),J(r=+r)&&(r=n?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(n)return-1;r=e.length-1}else if(r<0){if(!n)return-1;r=0}if("string"==typeof t&&(t=h.from(t,i)),h.isBuffer(t))return 0===t.length?-1:S(e,t,r,i,n);if("number"==typeof t)return t&=255,"function"==typeof Uint8Array.prototype.indexOf?n?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):S(e,[t],r,i,n);throw new TypeError("val must be string, number or Buffer")}function S(e,t,r,i,n){let o,s=1,a=e.length,l=t.length;if(void 0!==i&&("ucs2"===(i=String(i).toLowerCase())||"ucs-2"===i||"utf16le"===i||"utf-16le"===i)){if(e.length<2||t.length<2)return-1;s=2,a/=2,l/=2,r/=2}function d(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}if(n){let i=-1;for(o=r;o<a;o++)if(d(e,o)===d(t,-1===i?0:o-i)){if(-1===i&&(i=o),o-i+1===l)return i*s}else-1!==i&&(o-=o-i),i=-1}else for(r+l>a&&(r=a-l),o=r;o>=0;o--){let r=!0;for(let i=0;i<l;i++)if(d(e,o+i)!==d(t,i)){r=!1;break}if(r)return o}return-1}function y(e,t,r,i){r=Number(r)||0;const n=e.length-r;i?(i=Number(i))>n&&(i=n):i=n;const o=t.length;let s;for(i>o/2&&(i=o/2),s=0;s<i;++s){const i=parseInt(t.substr(2*s,2),16);if(J(i))return s;e[r+s]=i}return s}function T(e,t,r,i){return Z(Y(t,e.length-r),e,r,i)}function R(e,t,r,i){return Z(function(e){const t=[];for(let r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,i)}function x(e,t,r,i){return Z(X(t),e,r,i)}function I(e,t,r,i){return Z(function(e,t){let r,i,n;const o=[];for(let s=0;s<e.length&&!((t-=2)<0);++s)r=e.charCodeAt(s),i=r>>8,n=r%256,o.push(n),o.push(i);return o}(t,e.length-r),e,r,i)}function q(e,t,r){return 0===t&&r===e.length?s.fromByteArray(e):s.fromByteArray(e.slice(t,r))}function L(e,t,r){r=Math.min(e.length,r);const i=[];let n=t;for(;n<r;){const t=e[n];let o=null,s=t>239?4:t>223?3:t>191?2:1;if(n+s<=r){let r,i,a,l;switch(s){case 1:t<128&&(o=t);break;case 2:r=e[n+1],128==(192&r)&&(l=(31&t)<<6|63&r,l>127&&(o=l));break;case 3:r=e[n+1],i=e[n+2],128==(192&r)&&128==(192&i)&&(l=(15&t)<<12|(63&r)<<6|63&i,l>2047&&(l<55296||l>57343)&&(o=l));break;case 4:r=e[n+1],i=e[n+2],a=e[n+3],128==(192&r)&&128==(192&i)&&128==(192&a)&&(l=(15&t)<<18|(63&r)<<12|(63&i)<<6|63&a,l>65535&&l<1114112&&(o=l))}}null===o?(o=65533,s=1):o>65535&&(o-=65536,i.push(o>>>10&1023|55296),o=56320|1023&o),i.push(o),n+=s}return function(e){const t=e.length;if(t<=F)return String.fromCharCode.apply(String,e);let r="",i=0;for(;i<t;)r+=String.fromCharCode.apply(String,e.slice(i,i+=F));return r}(i)}h.TYPED_ARRAY_SUPPORT=function(){try{const e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),h.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(h.prototype,"parent",{enumerable:!0,get:function(){if(h.isBuffer(this))return this.buffer}}),Object.defineProperty(h.prototype,"offset",{enumerable:!0,get:function(){if(h.isBuffer(this))return this.byteOffset}}),h.poolSize=8192,h.from=function(e,t,r){return u(e,t,r)},Object.setPrototypeOf(h.prototype,Uint8Array.prototype),Object.setPrototypeOf(h,Uint8Array),h.alloc=function(e,t,r){return function(e,t,r){return p(e),e<=0?c(e):void 0!==t?"string"==typeof r?c(e).fill(t,r):c(e).fill(t):c(e)}(e,t,r)},h.allocUnsafe=function(e){return g(e)},h.allocUnsafeSlow=function(e){return g(e)},h.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==h.prototype},h.compare=function(e,t){if(Q(e,Uint8Array)&&(e=h.from(e,e.offset,e.byteLength)),Q(t,Uint8Array)&&(t=h.from(t,t.offset,t.byteLength)),!h.isBuffer(e)||!h.isBuffer(t))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;let r=e.length,i=t.length;for(let n=0,o=Math.min(r,i);n<o;++n)if(e[n]!==t[n]){r=e[n],i=t[n];break}return r<i?-1:i<r?1:0},h.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},h.concat=function(e,t){if(!Array.isArray(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return h.alloc(0);let r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;const i=h.allocUnsafe(t);let n=0;for(r=0;r<e.length;++r){let t=e[r];if(Q(t,Uint8Array))n+t.length>i.length?(h.isBuffer(t)||(t=h.from(t)),t.copy(i,n)):Uint8Array.prototype.set.call(i,t,n);else{if(!h.isBuffer(t))throw new TypeError('"list" argument must be an Array of Buffers');t.copy(i,n)}n+=t.length}return i},h.byteLength=b,h.prototype._isBuffer=!0,h.prototype.swap16=function(){const e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let t=0;t<e;t+=2)v(this,t,t+1);return this},h.prototype.swap32=function(){const e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let t=0;t<e;t+=4)v(this,t,t+3),v(this,t+1,t+2);return this},h.prototype.swap64=function(){const e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let t=0;t<e;t+=8)v(this,t,t+7),v(this,t+1,t+6),v(this,t+2,t+5),v(this,t+3,t+4);return this},h.prototype.toString=function(){const e=this.length;return 0===e?"":0===arguments.length?L(this,0,e):k.apply(this,arguments)},h.prototype.toLocaleString=h.prototype.toString,h.prototype.equals=function(e){if(!h.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===h.compare(this,e)},h.prototype.inspect=function(){let e="";const t=o;return e=this.toString("hex",0,t).replace(/(.{2})/g,"$1 ").trim(),this.length>t&&(e+=" ... "),"<Buffer "+e+">"},l&&(h.prototype[l]=h.prototype.inspect),h.prototype.compare=function(e,t,r,i,n){if(Q(e,Uint8Array)&&(e=h.from(e,e.offset,e.byteLength)),!h.isBuffer(e))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===i&&(i=0),void 0===n&&(n=this.length),t<0||r>e.length||i<0||n>this.length)throw new RangeError("out of range index");if(i>=n&&t>=r)return 0;if(i>=n)return-1;if(t>=r)return 1;if(this===e)return 0;let o=(n>>>=0)-(i>>>=0),s=(r>>>=0)-(t>>>=0);const a=Math.min(o,s),l=this.slice(i,n),d=e.slice(t,r);for(let e=0;e<a;++e)if(l[e]!==d[e]){o=l[e],s=d[e];break}return o<s?-1:s<o?1:0},h.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},h.prototype.indexOf=function(e,t,r){return C(this,e,t,r,!0)},h.prototype.lastIndexOf=function(e,t,r){return C(this,e,t,r,!1)},h.prototype.write=function(e,t,r,i){if(void 0===t)i="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)i=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t>>>=0,isFinite(r)?(r>>>=0,void 0===i&&(i="utf8")):(i=r,r=void 0)}const n=this.length-t;if((void 0===r||r>n)&&(r=n),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");i||(i="utf8");let o=!1;for(;;)switch(i){case"hex":return y(this,e,t,r);case"utf8":case"utf-8":return T(this,e,t,r);case"ascii":case"latin1":case"binary":return R(this,e,t,r);case"base64":return x(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return I(this,e,t,r);default:if(o)throw new TypeError("Unknown encoding: "+i);i=(""+i).toLowerCase(),o=!0}},h.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};const F=4096;function E(e,t,r){let i="";r=Math.min(e.length,r);for(let n=t;n<r;++n)i+=String.fromCharCode(127&e[n]);return i}function A(e,t,r){let i="";r=Math.min(e.length,r);for(let n=t;n<r;++n)i+=String.fromCharCode(e[n]);return i}function N(e,t,r){const i=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>i)&&(r=i);let n="";for(let i=t;i<r;++i)n+=ee[e[i]];return n}function P(e,t,r){const i=e.slice(t,r);let n="";for(let e=0;e<i.length-1;e+=2)n+=String.fromCharCode(i[e]+256*i[e+1]);return n}function B(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function U(e,t,r,i,n,o){if(!h.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>n||t<o)throw new RangeError('"value" argument is out of bounds');if(r+i>e.length)throw new RangeError("Index out of range")}function H(e,t,r,i,n){$(t,i,n,e,r,7);let o=Number(t&BigInt(4294967295));e[r++]=o,o>>=8,e[r++]=o,o>>=8,e[r++]=o,o>>=8,e[r++]=o;let s=Number(t>>BigInt(32)&BigInt(4294967295));return e[r++]=s,s>>=8,e[r++]=s,s>>=8,e[r++]=s,s>>=8,e[r++]=s,r}function M(e,t,r,i,n){$(t,i,n,e,r,7);let o=Number(t&BigInt(4294967295));e[r+7]=o,o>>=8,e[r+6]=o,o>>=8,e[r+5]=o,o>>=8,e[r+4]=o;let s=Number(t>>BigInt(32)&BigInt(4294967295));return e[r+3]=s,s>>=8,e[r+2]=s,s>>=8,e[r+1]=s,s>>=8,e[r]=s,r+8}function V(e,t,r,i,n,o){if(r+i>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function O(e,t,r,i,n){return t=+t,r>>>=0,n||V(e,0,r,4),a.write(e,t,r,i,23,4),r+4}function D(e,t,r,i,n){return t=+t,r>>>=0,n||V(e,0,r,8),a.write(e,t,r,i,52,8),r+8}h.prototype.slice=function(e,t){const r=this.length;(e=~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),(t=void 0===t?r:~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);const i=this.subarray(e,t);return Object.setPrototypeOf(i,h.prototype),i},h.prototype.readUintLE=h.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||B(e,t,this.length);let i=this[e],n=1,o=0;for(;++o<t&&(n*=256);)i+=this[e+o]*n;return i},h.prototype.readUintBE=h.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||B(e,t,this.length);let i=this[e+--t],n=1;for(;t>0&&(n*=256);)i+=this[e+--t]*n;return i},h.prototype.readUint8=h.prototype.readUInt8=function(e,t){return e>>>=0,t||B(e,1,this.length),this[e]},h.prototype.readUint16LE=h.prototype.readUInt16LE=function(e,t){return e>>>=0,t||B(e,2,this.length),this[e]|this[e+1]<<8},h.prototype.readUint16BE=h.prototype.readUInt16BE=function(e,t){return e>>>=0,t||B(e,2,this.length),this[e]<<8|this[e+1]},h.prototype.readUint32LE=h.prototype.readUInt32LE=function(e,t){return e>>>=0,t||B(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},h.prototype.readUint32BE=h.prototype.readUInt32BE=function(e,t){return e>>>=0,t||B(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},h.prototype.readBigUInt64LE=te((function(e){K(e>>>=0,"offset");const t=this[e],r=this[e+7];void 0!==t&&void 0!==r||j(e,this.length-8);const i=t+256*this[++e]+65536*this[++e]+this[++e]*2**24,n=this[++e]+256*this[++e]+65536*this[++e]+r*2**24;return BigInt(i)+(BigInt(n)<<BigInt(32))})),h.prototype.readBigUInt64BE=te((function(e){K(e>>>=0,"offset");const t=this[e],r=this[e+7];void 0!==t&&void 0!==r||j(e,this.length-8);const i=t*2**24+65536*this[++e]+256*this[++e]+this[++e],n=this[++e]*2**24+65536*this[++e]+256*this[++e]+r;return(BigInt(i)<<BigInt(32))+BigInt(n)})),h.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||B(e,t,this.length);let i=this[e],n=1,o=0;for(;++o<t&&(n*=256);)i+=this[e+o]*n;return n*=128,i>=n&&(i-=Math.pow(2,8*t)),i},h.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||B(e,t,this.length);let i=t,n=1,o=this[e+--i];for(;i>0&&(n*=256);)o+=this[e+--i]*n;return n*=128,o>=n&&(o-=Math.pow(2,8*t)),o},h.prototype.readInt8=function(e,t){return e>>>=0,t||B(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},h.prototype.readInt16LE=function(e,t){e>>>=0,t||B(e,2,this.length);const r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},h.prototype.readInt16BE=function(e,t){e>>>=0,t||B(e,2,this.length);const r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},h.prototype.readInt32LE=function(e,t){return e>>>=0,t||B(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},h.prototype.readInt32BE=function(e,t){return e>>>=0,t||B(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},h.prototype.readBigInt64LE=te((function(e){K(e>>>=0,"offset");const t=this[e],r=this[e+7];void 0!==t&&void 0!==r||j(e,this.length-8);const i=this[e+4]+256*this[e+5]+65536*this[e+6]+(r<<24);return(BigInt(i)<<BigInt(32))+BigInt(t+256*this[++e]+65536*this[++e]+this[++e]*2**24)})),h.prototype.readBigInt64BE=te((function(e){K(e>>>=0,"offset");const t=this[e],r=this[e+7];void 0!==t&&void 0!==r||j(e,this.length-8);const i=(t<<24)+65536*this[++e]+256*this[++e]+this[++e];return(BigInt(i)<<BigInt(32))+BigInt(this[++e]*2**24+65536*this[++e]+256*this[++e]+r)})),h.prototype.readFloatLE=function(e,t){return e>>>=0,t||B(e,4,this.length),a.read(this,e,!0,23,4)},h.prototype.readFloatBE=function(e,t){return e>>>=0,t||B(e,4,this.length),a.read(this,e,!1,23,4)},h.prototype.readDoubleLE=function(e,t){return e>>>=0,t||B(e,8,this.length),a.read(this,e,!0,52,8)},h.prototype.readDoubleBE=function(e,t){return e>>>=0,t||B(e,8,this.length),a.read(this,e,!1,52,8)},h.prototype.writeUintLE=h.prototype.writeUIntLE=function(e,t,r,i){if(e=+e,t>>>=0,r>>>=0,!i){U(this,e,t,r,Math.pow(2,8*r)-1,0)}let n=1,o=0;for(this[t]=255&e;++o<r&&(n*=256);)this[t+o]=e/n&255;return t+r},h.prototype.writeUintBE=h.prototype.writeUIntBE=function(e,t,r,i){if(e=+e,t>>>=0,r>>>=0,!i){U(this,e,t,r,Math.pow(2,8*r)-1,0)}let n=r-1,o=1;for(this[t+n]=255&e;--n>=0&&(o*=256);)this[t+n]=e/o&255;return t+r},h.prototype.writeUint8=h.prototype.writeUInt8=function(e,t,r){return e=+e,t>>>=0,r||U(this,e,t,1,255,0),this[t]=255&e,t+1},h.prototype.writeUint16LE=h.prototype.writeUInt16LE=function(e,t,r){return e=+e,t>>>=0,r||U(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},h.prototype.writeUint16BE=h.prototype.writeUInt16BE=function(e,t,r){return e=+e,t>>>=0,r||U(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},h.prototype.writeUint32LE=h.prototype.writeUInt32LE=function(e,t,r){return e=+e,t>>>=0,r||U(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},h.prototype.writeUint32BE=h.prototype.writeUInt32BE=function(e,t,r){return e=+e,t>>>=0,r||U(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},h.prototype.writeBigUInt64LE=te((function(e,t=0){return H(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))})),h.prototype.writeBigUInt64BE=te((function(e,t=0){return M(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))})),h.prototype.writeIntLE=function(e,t,r,i){if(e=+e,t>>>=0,!i){const i=Math.pow(2,8*r-1);U(this,e,t,r,i-1,-i)}let n=0,o=1,s=0;for(this[t]=255&e;++n<r&&(o*=256);)e<0&&0===s&&0!==this[t+n-1]&&(s=1),this[t+n]=(e/o>>0)-s&255;return t+r},h.prototype.writeIntBE=function(e,t,r,i){if(e=+e,t>>>=0,!i){const i=Math.pow(2,8*r-1);U(this,e,t,r,i-1,-i)}let n=r-1,o=1,s=0;for(this[t+n]=255&e;--n>=0&&(o*=256);)e<0&&0===s&&0!==this[t+n+1]&&(s=1),this[t+n]=(e/o>>0)-s&255;return t+r},h.prototype.writeInt8=function(e,t,r){return e=+e,t>>>=0,r||U(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},h.prototype.writeInt16LE=function(e,t,r){return e=+e,t>>>=0,r||U(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},h.prototype.writeInt16BE=function(e,t,r){return e=+e,t>>>=0,r||U(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},h.prototype.writeInt32LE=function(e,t,r){return e=+e,t>>>=0,r||U(this,e,t,4,2147483647,-2147483648),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},h.prototype.writeInt32BE=function(e,t,r){return e=+e,t>>>=0,r||U(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},h.prototype.writeBigInt64LE=te((function(e,t=0){return H(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),h.prototype.writeBigInt64BE=te((function(e,t=0){return M(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),h.prototype.writeFloatLE=function(e,t,r){return O(this,e,t,!0,r)},h.prototype.writeFloatBE=function(e,t,r){return O(this,e,t,!1,r)},h.prototype.writeDoubleLE=function(e,t,r){return D(this,e,t,!0,r)},h.prototype.writeDoubleBE=function(e,t,r){return D(this,e,t,!1,r)},h.prototype.copy=function(e,t,r,i){if(!h.isBuffer(e))throw new TypeError("argument should be a Buffer");if(r||(r=0),i||0===i||(i=this.length),t>=e.length&&(t=e.length),t||(t=0),i>0&&i<r&&(i=r),i===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("Index out of range");if(i<0)throw new RangeError("sourceEnd out of bounds");i>this.length&&(i=this.length),e.length-t<i-r&&(i=e.length-t+r);const n=i-r;return this===e&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(t,r,i):Uint8Array.prototype.set.call(e,this.subarray(r,i),t),n},h.prototype.fill=function(e,t,r,i){if("string"==typeof e){if("string"==typeof t?(i=t,t=0,r=this.length):"string"==typeof r&&(i=r,r=this.length),void 0!==i&&"string"!=typeof i)throw new TypeError("encoding must be a string");if("string"==typeof i&&!h.isEncoding(i))throw new TypeError("Unknown encoding: "+i);if(1===e.length){const t=e.charCodeAt(0);("utf8"===i&&t<128||"latin1"===i)&&(e=t)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;let n;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(n=t;n<r;++n)this[n]=e;else{const o=h.isBuffer(e)?e:h.from(e,i),s=o.length;if(0===s)throw new TypeError('The value "'+e+'" is invalid for argument "value"');for(n=0;n<r-t;++n)this[n+t]=o[n%s]}return this};const W={};function G(e,t,r){W[e]=class extends r{constructor(){super(),Object.defineProperty(this,"message",{value:t.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${e}]`,this.stack,delete this.name}get code(){return e}set code(e){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:e,writable:!0})}toString(){return`${this.name} [${e}]: ${this.message}`}}}function z(e){let t="",r=e.length;const i="-"===e[0]?1:0;for(;r>=i+4;r-=3)t=`_${e.slice(r-3,r)}${t}`;return`${e.slice(0,r)}${t}`}function $(e,t,r,i,n,o){if(e>r||e<t){const i="bigint"==typeof t?"n":"";let n;throw n=o>3?0===t||t===BigInt(0)?`>= 0${i} and < 2${i} ** ${8*(o+1)}${i}`:`>= -(2${i} ** ${8*(o+1)-1}${i}) and < 2 ** ${8*(o+1)-1}${i}`:`>= ${t}${i} and <= ${r}${i}`,new W.ERR_OUT_OF_RANGE("value",n,e)}!function(e,t,r){K(t,"offset"),void 0!==e[t]&&void 0!==e[t+r]||j(t,e.length-(r+1))}(i,n,o)}function K(e,t){if("number"!=typeof e)throw new W.ERR_INVALID_ARG_TYPE(t,"number",e)}function j(e,t,r){if(Math.floor(e)!==e)throw K(e,r),new W.ERR_OUT_OF_RANGE(r||"offset","an integer",e);if(t<0)throw new W.ERR_BUFFER_OUT_OF_BOUNDS;throw new W.ERR_OUT_OF_RANGE(r||"offset",`>= ${r?1:0} and <= ${t}`,e)}G("ERR_BUFFER_OUT_OF_BOUNDS",(function(e){return e?`${e} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"}),RangeError),G("ERR_INVALID_ARG_TYPE",(function(e,t){return`The "${e}" argument must be of type number. Received type ${typeof t}`}),TypeError),G("ERR_OUT_OF_RANGE",(function(e,t,r){let i=`The value of "${e}" is out of range.`,n=r;return Number.isInteger(r)&&Math.abs(r)>2**32?n=z(String(r)):"bigint"==typeof r&&(n=String(r),(r>BigInt(2)**BigInt(32)||r<-(BigInt(2)**BigInt(32)))&&(n=z(n)),n+="n"),i+=` It must be ${t}. Received ${n}`,i}),RangeError);const _=/[^+/0-9A-Za-z-_]/g;function Y(e,t){let r;t=t||1/0;const i=e.length;let n=null;const o=[];for(let s=0;s<i;++s){if(r=e.charCodeAt(s),r>55295&&r<57344){if(!n){if(r>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(s+1===i){(t-=3)>-1&&o.push(239,191,189);continue}n=r;continue}if(r<56320){(t-=3)>-1&&o.push(239,191,189),n=r;continue}r=65536+(n-55296<<10|r-56320)}else n&&(t-=3)>-1&&o.push(239,191,189);if(n=null,r<128){if((t-=1)<0)break;o.push(r)}else if(r<2048){if((t-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function X(e){return s.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(_,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function Z(e,t,r,i){let n;for(n=0;n<i&&!(n+r>=t.length||n>=e.length);++n)t[n+r]=e[n];return n}function Q(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}function J(e){return e!=e}const ee=function(){const e="0123456789abcdef",t=new Array(256);for(let r=0;r<16;++r){const i=16*r;for(let n=0;n<16;++n)t[i+n]=e[r]+e[n]}return t}();function te(e){return"undefined"==typeof BigInt?re:e}function re(){throw new Error("BigInt not supported")}})),r.register("kuxul",(function(t,r){var i,n;e(t.exports,"toByteArray",(()=>i),(e=>i=e)),e(t.exports,"fromByteArray",(()=>n),(e=>n=e)),i=function(e){var t,r,i=h(e),n=i[0],o=i[1],l=new a(function(e,t,r){return 3*(t+r)/4-r}(0,n,o)),d=0,c=o>0?n-4:n;for(r=0;r<c;r+=4)t=s[e.charCodeAt(r)]<<18|s[e.charCodeAt(r+1)]<<12|s[e.charCodeAt(r+2)]<<6|s[e.charCodeAt(r+3)],l[d++]=t>>16&255,l[d++]=t>>8&255,l[d++]=255&t;2===o&&(t=s[e.charCodeAt(r)]<<2|s[e.charCodeAt(r+1)]>>4,l[d++]=255&t);1===o&&(t=s[e.charCodeAt(r)]<<10|s[e.charCodeAt(r+1)]<<4|s[e.charCodeAt(r+2)]>>2,l[d++]=t>>8&255,l[d++]=255&t);return l},n=function(e){for(var t,r=e.length,i=r%3,n=[],s=16383,a=0,l=r-i;a<l;a+=s)n.push(u(e,a,a+s>l?l:a+s));1===i?(t=e[r-1],n.push(o[t>>2]+o[t<<4&63]+"==")):2===i&&(t=(e[r-2]<<8)+e[r-1],n.push(o[t>>10]+o[t>>4&63]+o[t<<2&63]+"="));return n.join("")};for(var o=[],s=[],a="undefined"!=typeof Uint8Array?Uint8Array:Array,l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",d=0,c=l.length;d<c;++d)o[d]=l[d],s[l.charCodeAt(d)]=d;function h(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");return-1===r&&(r=t),[r,r===t?0:4-r%4]}function u(e,t,r){for(var i,n,s=[],a=t;a<r;a+=3)i=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),s.push(o[(n=i)>>18&63]+o[n>>12&63]+o[n>>6&63]+o[63&n]);return s.join("")}s["-".charCodeAt(0)]=62,s["_".charCodeAt(0)]=63})),r.register("9NvM5",(function(t,r){
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */var i,n;e(t.exports,"read",(()=>i),(e=>i=e)),e(t.exports,"write",(()=>n),(e=>n=e)),i=function(e,t,r,i,n){var o,s,a=8*n-i-1,l=(1<<a)-1,d=l>>1,c=-7,h=r?n-1:0,u=r?-1:1,p=e[t+h];for(h+=u,o=p&(1<<-c)-1,p>>=-c,c+=a;c>0;o=256*o+e[t+h],h+=u,c-=8);for(s=o&(1<<-c)-1,o>>=-c,c+=i;c>0;s=256*s+e[t+h],h+=u,c-=8);if(0===o)o=1-d;else{if(o===l)return s?NaN:1/0*(p?-1:1);s+=Math.pow(2,i),o-=d}return(p?-1:1)*s*Math.pow(2,o-i)},n=function(e,t,r,i,n,o){var s,a,l,d=8*o-n-1,c=(1<<d)-1,h=c>>1,u=23===n?Math.pow(2,-24)-Math.pow(2,-77):0,p=i?0:o-1,g=i?1:-1,m=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,s=c):(s=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-s))<1&&(s--,l*=2),(t+=s+h>=1?u/l:u*Math.pow(2,1-h))*l>=2&&(s++,l/=2),s+h>=c?(a=0,s=c):s+h>=1?(a=(t*l-1)*Math.pow(2,n),s+=h):(a=t*Math.pow(2,h-1)*Math.pow(2,n),s=0));n>=8;e[r+p]=255&a,p+=g,a/=256,n-=8);for(s=s<<n|a,d+=n;d>0;e[r+p]=255&s,p+=g,s/=256,d-=8);e[r+p-g]|=128*m}})),r.register("jHyi6",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync('/*\n * Copyright 2016 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.tree-outline {\n  padding-left: 0;\n\n  --override-error-background-color: #d93025;\n  --override-error-text-color: #fff;\n  --override-call-to-action-border-color: #f29900;\n  --override-header-highlight-background-color: #ffff78;\n}\n\n.-theme-with-dark-background .tree-outline,\n:host-context(.-theme-with-dark-background) .tree-outline {\n  --override-error-text-color: #000;\n  --override-call-to-action-border-color: rgb(255 166 13);\n  --override-header-highlight-background-color: rgb(135 135 0);\n}\n\n.tree-outline > ol {\n  padding-bottom: 5px;\n  border-bottom: solid 1px var(--color-details-hairline);\n}\n\n.tree-outline > .parent {\n  user-select: none;\n  font-weight: bold;\n  color: var(--color-text-primary);\n  margin-top: -1px;\n  display: flex;\n  align-items: center;\n  height: 26px;\n}\n\n.tree-outline li {\n  display: block;\n  padding-left: 5px;\n  line-height: 20px;\n}\n\n.tree-outline li:not(.parent) {\n  margin-left: 10px;\n}\n\n.tree-outline li:not(.parent)::before {\n  display: none;\n}\n\n.tree-outline .caution {\n  margin-left: 4px;\n  display: inline-block;\n  font-weight: bold;\n}\n\n.tree-outline li.expanded .header-count {\n  display: none;\n}\n\n.tree-outline li .header-toggle {\n  display: none;\n}\n\n.tree-outline li .status-from-cache {\n  color: var(--color-text-secondary);\n}\n\n.tree-outline li.expanded .header-toggle {\n  display: inline;\n  margin-left: 30px;\n  font-weight: normal;\n  color: var(--color-text-primary);\n}\n\n.tree-outline li .header-toggle:hover {\n  color: var(--color-text-secondary);\n  cursor: pointer;\n}\n\n.tree-outline .header-name {\n  color: var(--color-text-secondary);\n  display: inline-block;\n  margin-right: 0.25em;\n  font-weight: bold;\n  vertical-align: top;\n  white-space: pre-wrap;\n}\n\n.tree-outline .header-separator {\n  user-select: none;\n}\n\n.tree-outline .header-badge-text {\n  font-variant: small-caps;\n  font-weight: 500;\n  white-space: pre-wrap;\n  word-break: break-all;\n}\n\n.tree-outline .header-warning {\n  color: var(--override-error-background-color);\n}\n\n.tree-outline .header-badge {\n  display: inline;\n  margin-right: 0.75em;\n  color: var(--override-error-text-color);\n  border-radius: 100vh;\n  padding-left: 6px;\n  padding-right: 6px;\n}\n\n.tree-outline .header-badge-error {\n  background-color: var(--override-error-background-color);\n}\n\n.tree-outline .header-badge-success {\n  background-color: var(--color-accent-green);\n  text-transform: uppercase;\n}\n\n.tree-outline .header-value {\n  display: inline;\n  margin-right: 1em;\n  white-space: pre-wrap;\n  word-break: break-all;\n  margin-top: 1px;\n}\n\n.tree-outline .call-to-action {\n  background-color: var(--color-background-elevation-1);\n  padding: 8px;\n  border-radius: 2px;\n}\n\n.tree-outline .selected .call-to-action {\n  background-color: transparent;\n  padding: 8px;\n  border-radius: 2px;\n}\n\n.tree-outline .call-to-action-body {\n  padding: 6px 0;\n  margin-left: 9.5px;\n  border-left: 2px solid var(--override-call-to-action-border-color);\n  padding-left: 18px;\n}\n\n.tree-outline .call-to-action .explanation {\n  font-weight: bold;\n}\n\n.tree-outline .call-to-action code {\n  font-size: 90%;\n}\n\n.tree-outline .call-to-action .example .comment::before {\n  content: " — ";\n}\n\n.tree-outline .empty-request-header {\n  color: var(--color-text-disabled);\n}\n\n.request-headers-show-more-button {\n  border: none;\n  border-radius: 3px;\n  display: inline-block;\n  font-size: 12px;\n  font-family: sans-serif;\n  cursor: pointer;\n  margin: 0 4px;\n  padding: 2px 4px;\n}\n\n.request-headers-caution {\n  display: flex;\n  gap: 6px;\n}\n\n.header-highlight {\n  background-color: var(--override-header-highlight-background-color);\n}\n\n.header-highlight:focus {\n  background-color: var(--legacy-selection-bg-color);\n}\n\n.x-client-data-details {\n  padding-left: 10px;\n}\n\n@media (forced-colors: active) {\n  :host-context(.request-headers-tree) ol.tree-outline:not(.hide-selection-when-blurred) li.selected:focus {\n    background: Highlight;\n  }\n\n  :host-context(.request-headers-tree) ol.tree-outline:not(.hide-selection-when-blurred) li::before {\n    background-color: ButtonText;\n  }\n\n  :host-context(.request-headers-tree) ol.tree-outline:not(.hide-selection-when-blurred) li.selected.parent::before {\n    background-color: HighlightText;\n  }\n\n  :host-context(.request-headers-tree) ol.tree-outline:not(.hide-selection-when-blurred) li.selected *,\n  :host-context(.request-headers-tree) ol.tree-outline:not(.hide-selection-when-blurred) li.selected.parent,\n  :host-context(.request-headers-tree) ol.tree-outline:not(.hide-selection-when-blurred) li.selected.parent span,\n  :host-context(.request-headers-tree) ol.tree-outline:not(.hide-selection-when-blurred) li.selected:focus .status-from-cache {\n    color: HighlightText;\n  }\n}\n\n.header-decode-error {\n  color: var(--color-accent-red);\n}\n\n/*# sourceURL=requestHeadersTree.css */\n');var n=i})),r.register("bO0Qf",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright (c) 2014 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.request-headers-view {\n  user-select: text;\n  overflow: auto;\n}\n\n.resource-status-image {\n  margin-top: -2px;\n  margin-right: 3px;\n}\n\n.request-headers-tree {\n  flex-grow: 1;\n  overflow-y: auto;\n  margin: 0;\n  background-color: var(--color-background);\n}\n\n/*# sourceURL=requestHeadersView.css */\n");var n=i})),r.register("amGvi",(function(t,i){e(t.exports,"RequestPayloadView",(()=>w)),e(t.exports,"Category",(()=>k));var n=r("koSS8"),o=r("e7bLS"),s=r("ixFnt"),a=r("eQFvP"),l=r("R6KC8"),d=r("f2hYB"),c=r("eKcu2"),h=r("9z2ZV"),u=r("2NZfS"),p=r("cOofm");const g={copyValue:"Copy value",requestPayload:"Request Payload",unableToDecodeValue:"(unable to decode value)",queryStringParameters:"Query String Parameters",formData:"Form Data",showMore:"Show more",viewParsed:"View parsed",empty:"(empty)",viewSource:"View source",viewUrlEncoded:"View URL-encoded",viewDecoded:"View decoded",viewUrlEncodedL:"view URL-encoded",viewDecodedL:"view decoded",viewParsedL:"view parsed",viewSourceL:"view source"},m=s.i18n.registerUIStrings("panels/network/RequestPayloadView.ts",g),f=s.i18n.getLocalizedString.bind(void 0,m);class w extends h.Widget.VBox{request;decodeRequestParameters;queryStringCategory;formDataCategory;requestPayloadCategory;constructor(e){super(),this.element.classList.add("request-payload-view"),this.request=e,this.decodeRequestParameters=!0;const t=e.requestContentType();t&&(this.decodeRequestParameters=Boolean(t.match(/^application\/x-www-form-urlencoded\s*(;.*)?$/i)));const r=new h.TreeOutline.TreeOutlineInShadow;r.registerCSSFiles([c.default,d.default,u.default]),r.element.classList.add("request-payload-tree"),r.makeDense(),this.element.appendChild(r.element),this.queryStringCategory=new k(r,"queryString",""),this.formDataCategory=new k(r,"formData",""),this.requestPayloadCategory=new k(r,"requestPayload",f(g.requestPayload))}wasShown(){this.registerCSSFiles([p.default]),this.request.addEventListener(a.NetworkRequest.Events.RequestHeadersChanged,this.refreshFormData,this),this.refreshQueryString(),this.refreshFormData()}willHide(){this.request.removeEventListener(a.NetworkRequest.Events.RequestHeadersChanged,this.refreshFormData,this)}addEntryContextMenuHandler(e,t){e.listItemElement.addEventListener("contextmenu",(e=>{e.consume(!0);const r=new h.ContextMenu.ContextMenu(e),i=decodeURIComponent(t);r.clipboardSection().appendItem(f(g.copyValue),(()=>{o.userMetrics.actionTaken(o.UserMetrics.Action.NetworkPanelCopyValue),o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(i)})),r.show()}))}formatParameter(e,t,r){let i=!1;if(r&&(e=e.replace(/\+/g," ")).indexOf("%")>=0)try{e=decodeURIComponent(e)}catch(e){i=!0}const n=document.createElement("div");return t&&(n.className=t),""===e&&n.classList.add("empty-value"),i?n.createChild("span","payload-decode-error").textContent=f(g.unableToDecodeValue):n.textContent=e,n}refreshQueryString(){const e=this.request.queryString(),t=this.request.queryParameters;this.queryStringCategory.hidden=!t,t&&this.refreshParams(f(g.queryStringParameters),t,e,this.queryStringCategory)}async refreshFormData(){const e=await this.request.requestFormData();if(!e)return this.formDataCategory.hidden=!0,void(this.requestPayloadCategory.hidden=!0);const t=await this.request.formParameters();if(t)this.formDataCategory.hidden=!1,this.requestPayloadCategory.hidden=!0,this.refreshParams(f(g.formData),t,e,this.formDataCategory);else{this.requestPayloadCategory.hidden=!1,this.formDataCategory.hidden=!0;try{const t=JSON.parse(e);this.refreshRequestJSONPayload(t,e)}catch(t){this.populateTreeElementWithSourceText(this.requestPayloadCategory,e)}}}populateTreeElementWithSourceText(e,t){const r=(t||"").trim(),i=r.length>3e3,n=document.createElement("span");n.classList.add("payload-value"),n.classList.add("source-code"),n.textContent=i?r.substr(0,3e3):r;const o=new h.TreeOutline.TreeElement(n);if(e.removeChildren(),e.appendChild(o),!i)return;const s=document.createElement("button");function a(){s.remove(),n.textContent=r,o.listItemElement.removeEventListener("contextmenu",l)}function l(e){const t=new h.ContextMenu.ContextMenu(e);t.newSection().appendItem(f(g.showMore),a),t.show()}s.classList.add("request-payload-show-more-button"),s.textContent=f(g.showMore),s.addEventListener("click",a),o.listItemElement.addEventListener("contextmenu",l),n.appendChild(s)}refreshParams(e,t,r,i){i.removeChildren(),i.listItemElement.removeChildren(),i.listItemElement.createChild("div","selection fill"),h.UIUtils.createTextChild(i.listItemElement,e);const n=document.createElement("span");n.classList.add("payload-count");const o=t?t.length:0;n.textContent=` (${o})`,i.listItemElement.appendChild(n);b.has(i)?this.appendParamsSource(e,t,r,i):this.appendParamsParsed(e,t,r,i)}appendParamsSource(e,t,r,i){this.populateTreeElementWithSourceText(i,r);const n=i.listItemElement,o=function(o){n.removeEventListener("contextmenu",s),b.delete(i),this.refreshParams(e,t,r,i),o.consume()},s=e=>{if(!i.expanded)return;const t=new h.ContextMenu.ContextMenu(e);t.newSection().appendItem(f(g.viewParsed),o.bind(this,e)),t.show()},a=this.createViewSourceToggle(!0,o.bind(this));n.appendChild(a),n.addEventListener("contextmenu",s)}appendParamsParsed(e,t,r,i){for(const e of t||[]){const t=document.createDocumentFragment();if(""!==e.name){const r=this.formatParameter(e.name+": ","payload-name",this.decodeRequestParameters),i=this.formatParameter(e.value,"payload-value source-code",this.decodeRequestParameters);t.appendChild(r),t.createChild("span","payload-separator"),t.appendChild(i)}else t.appendChild(this.formatParameter(f(g.empty),"empty-request-payload",this.decodeRequestParameters));const r=new h.TreeOutline.TreeElement(t);this.addEntryContextMenuHandler(r,e.value),i.appendChild(r)}const n=i.listItemElement,o=function(o){n.removeEventListener("contextmenu",a),b.add(i),this.refreshParams(e,t,r,i),o.consume()},s=function(e){n.removeEventListener("contextmenu",a),this.toggleURLDecoding(e)},a=e=>{if(!i.expanded)return;const t=new h.ContextMenu.ContextMenu(e),r=t.newSection();r.appendItem(f(g.viewSource),o.bind(this,e));const n=this.decodeRequestParameters?f(g.viewUrlEncoded):f(g.viewDecoded);r.appendItem(n,s.bind(this,e)),t.show()},l=this.createViewSourceToggle(!1,o.bind(this));n.appendChild(l);const d=this.decodeRequestParameters?f(g.viewUrlEncodedL):f(g.viewDecodedL),c=this.createToggleButton(d);c.addEventListener("click",s.bind(this),!1),n.appendChild(c),n.addEventListener("contextmenu",a)}refreshRequestJSONPayload(e,t){const r=this.requestPayloadCategory;r.removeChildren();const i=r.listItemElement;i.removeChildren(),i.createChild("div","selection fill"),h.UIUtils.createTextChild(i,this.requestPayloadCategory.title.toString()),b.has(r)?this.appendJSONPayloadSource(r,e,t):this.appendJSONPayloadParsed(r,e,t)}appendJSONPayloadSource(e,t,r){const i=e.listItemElement;this.populateTreeElementWithSourceText(e,r);const n=function(n){i.removeEventListener("contextmenu",s),b.delete(e),this.refreshRequestJSONPayload(t,r),n.consume()},o=this.createViewSourceToggle(!0,n.bind(this));i.appendChild(o);const s=t=>{if(!e.expanded)return;const r=new h.ContextMenu.ContextMenu(t);r.newSection().appendItem(f(g.viewParsed),n.bind(this,t)),r.show()};i.addEventListener("contextmenu",s)}appendJSONPayloadParsed(e,t,r){const i=a.RemoteObject.RemoteObject.fromLocalObject(t),n=new l.ObjectPropertiesSection.RootElement(i);n.title=i.description,n.expand(),n.editable=!1,e.childrenListElement.classList.add("source-code","object-properties-section"),e.appendChild(n);const o=e.listItemElement,s=function(i){o.removeEventListener("contextmenu",d),b.add(e),this.refreshRequestJSONPayload(t,r),i.consume()},d=t=>{if(!e.expanded)return;const r=new h.ContextMenu.ContextMenu(t);r.newSection().appendItem(f(g.viewSource),s.bind(this,t)),r.show()},c=this.createViewSourceToggle(!1,s.bind(this));o.appendChild(c),o.addEventListener("contextmenu",d)}createViewSourceToggle(e,t){const r=f(e?g.viewParsedL:g.viewSourceL),i=this.createToggleButton(r);return i.addEventListener("click",t,!1),i}toggleURLDecoding(e){this.decodeRequestParameters=!this.decodeRequestParameters,this.refreshQueryString(),this.refreshFormData(),e.consume()}createToggleButton(e){const t=document.createElement("span");return t.classList.add("payload-toggle"),t.tabIndex=0,t.setAttribute("role","button"),t.textContent=e,t}}const b=new WeakSet;class k extends h.TreeOutline.TreeElement{toggleOnClick;expandedSetting;expanded;constructor(e,t,r){super(r||"",!0),this.toggleOnClick=!0,this.hidden=!0,this.expandedSetting=n.Settings.Settings.instance().createSetting("request-info-"+t+"-category-expanded",!0),this.expanded=this.expandedSetting.get(),e.appendChild(this)}createLeaf(){const e=new h.TreeOutline.TreeElement;return this.appendChild(e),e}onexpand(){this.expandedSetting.set(!0)}oncollapse(){this.expandedSetting.set(!1)}}})),r.register("2NZfS",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2016 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.tree-outline {\n  padding-left: 0;\n\n  --override-error-background-color: #d93025;\n  --override-error-text-color: #fff;\n  --override-call-to-action-border-color: #f29900;\n  --override-header-highlight-background-color: #ffff78;\n}\n\n.-theme-with-dark-background .tree-outline,\n:host-context(.-theme-with-dark-background) .tree-outline {\n  --override-error-text-color: #000;\n  --override-call-to-action-border-color: rgb(255 166 13);\n  --override-header-highlight-background-color: rgb(135 135 0);\n}\n\n.tree-outline > ol {\n  padding-bottom: 5px;\n  border-bottom: solid 1px var(--color-details-hairline);\n}\n\n.tree-outline > .parent {\n  user-select: none;\n  font-weight: bold;\n  color: var(--color-text-primary);\n  margin-top: -1px;\n  display: flex;\n  align-items: center;\n  height: 26px;\n}\n\n.tree-outline li {\n  display: block;\n  padding-left: 5px;\n  line-height: 20px;\n}\n\n.tree-outline li:not(.parent) {\n  margin-left: 10px;\n}\n\n.tree-outline li:not(.parent)::before {\n  display: none;\n}\n\n.tree-outline li.expanded .payload-count {\n  display: none;\n}\n\n.tree-outline li .payload-toggle {\n  display: none;\n}\n\n.tree-outline li.expanded .payload-toggle {\n  display: inline;\n  margin-left: 30px;\n  font-weight: normal;\n  color: var(--color-text-primary);\n}\n\n.tree-outline li.expanded .payload-toggle:focus-visible {\n  border: 2px solid var(--color-button-outline-focus);\n  border-radius: 5px;\n}\n\n.tree-outline li .header-toggle:hover {\n  color: var(--color-text-secondary);\n}\n\n.tree-outline .payload-name {\n  color: var(--color-text-secondary);\n  display: inline-block;\n  margin-right: 0.25em;\n  font-weight: bold;\n  vertical-align: top;\n  white-space: pre-wrap;\n}\n\n.tree-outline .payload-separator {\n  user-select: none;\n}\n\n.tree-outline .payload-value {\n  display: inline;\n  margin-right: 1em;\n  white-space: pre-wrap;\n  word-break: break-all;\n  margin-top: 1px;\n}\n\n.tree-outline .empty-request-payload {\n  color: var(--color-text-disabled);\n}\n\n.request-payload-show-more-button {\n  border: none;\n  border-radius: 3px;\n  display: inline-block;\n  font-size: 12px;\n  font-family: sans-serif;\n  margin: 0 4px;\n  padding: 2px 4px;\n}\n\n@media (forced-colors: active) {\n  :host-context(.request-payload-tree) ol.tree-outline:not(.hide-selection-when-blurred) li.selected:focus {\n    background: Highlight;\n  }\n\n  :host-context(.request-payload-tree) ol.tree-outline:not(.hide-selection-when-blurred) li::before {\n    background-color: ButtonText;\n  }\n\n  :host-context(.request-payload-tree) ol.tree-outline:not(.hide-selection-when-blurred) li.selected.parent::before {\n    background-color: HighlightText;\n  }\n\n  :host-context(.request-payload-tree) ol.tree-outline:not(.hide-selection-when-blurred) li.selected *,\n  :host-context(.request-payload-tree) ol.tree-outline:not(.hide-selection-when-blurred) li.selected.parent,\n  :host-context(.request-payload-tree) ol.tree-outline:not(.hide-selection-when-blurred) li.selected.parent span {\n    color: HighlightText;\n  }\n}\n\n.payload-decode-error {\n  color: var(--color-accent-red);\n}\n\n/*# sourceURL=requestPayloadTree.css */\n");var n=i})),r.register("cOofm",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright (c) 2014 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.request-payload-view {\n  user-select: text;\n  overflow: auto;\n}\n\n.request-payload-tree {\n  flex-grow: 1;\n  overflow-y: auto;\n  margin: 0;\n}\n\n/*# sourceURL=requestPayloadView.css */\n");var n=i})),r.register("ej1uS",(function(t,i){e(t.exports,"RequestInitiatorView",(()=>g));var n=r("ixFnt"),o=r("eQFvP"),s=r("g4rSN"),a=r("a3yig"),l=r("9z2ZV"),d=r("1qAIZ"),c=r("fL8Y6");const h={thisRequestHasNoInitiatorData:"This request has no initiator data.",requestCallStack:"Request call stack",requestInitiatorChain:"Request initiator chain"},u=n.i18n.registerUIStrings("panels/network/RequestInitiatorView.ts",h),p=n.i18n.getLocalizedString.bind(void 0,u);class g extends l.Widget.VBox{linkifier;request;emptyWidget;hasShown;constructor(e){super(),this.element.classList.add("request-initiator-view"),this.linkifier=new a.Linkifier.Linkifier,this.request=e,this.emptyWidget=new l.EmptyWidget.EmptyWidget(p(h.thisRequestHasNoInitiatorData)),this.emptyWidget.show(this.element),this.hasShown=!1}static createStackTracePreview(e,t,r){const i=e.initiator();if(!i||!i.stack)return null;const n=o.NetworkManager.NetworkManager.forRequest(e),s=n?n.target():null;return a.JSPresentationUtils.buildStackTracePreviewContents(s,t,{stackTrace:i.stack,tabStops:r})}createTree(){const e=new l.TreeOutline.TreeOutlineInShadow;return e.registerCSSFiles([c.default]),e.contentElement.classList.add("request-initiator-view-tree"),e}buildRequestChainTree(e,t,r){const i=new l.TreeOutline.TreeElement(t);r.appendChild(i),i.titleElement instanceof HTMLElement&&i.titleElement.classList.add("request-initiator-view-section-title");const n=e.initiators;let o=i;for(const e of Array.from(n).reverse()){const t=new l.TreeOutline.TreeElement(e.url());o.appendChild(t),o.expand(),o=t}i.expand(),o.select();const s=o.titleElement;s instanceof HTMLElement&&(s.style.fontWeight="bold");const a=e.initiated;return this.depthFirstSearchTreeBuilder(a,o,this.request),i}depthFirstSearchTreeBuilder(e,t,r){const i=new Set;i.add(this.request);for(const n of e.keys())if(e.get(n)===r){const r=new l.TreeOutline.TreeElement(n.url());t.appendChild(r),t.expand(),i.has(n)||(i.add(n),this.depthFirstSearchTreeBuilder(e,r,n))}}buildStackTraceSection(e,t,r){const i=new l.TreeOutline.TreeElement(t);r.appendChild(i),i.titleElement instanceof HTMLElement&&i.titleElement.classList.add("request-initiator-view-section-title");const n=new l.TreeOutline.TreeElement(e,!1);n.selectable=!1,i.appendChild(n),i.expand()}wasShown(){if(this.hasShown)return;this.registerCSSFiles([d.default]);let e=!1;const t=this.createTree(),r=g.createStackTracePreview(this.request,this.linkifier,!0);r&&(e=!0,this.buildStackTraceSection(r.element,p(h.requestCallStack),t));const i=s.NetworkLog.NetworkLog.instance().initiatorGraphForRequest(this.request);(i.initiators.size>1||i.initiated.size>1)&&(e=!0,this.buildRequestChainTree(i,p(h.requestInitiatorChain),t));const n=t.firstChild();n&&n.select(!0),e&&(this.element.appendChild(t.element),this.emptyWidget.hideWidget()),this.hasShown=!0}}})),r.register("1qAIZ",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright (c) 2019 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.request-initiator-view {\n  display: block;\n  margin: 6px;\n}\n\n/*# sourceURL=requestInitiatorView.css */\n");var n=i})),r.register("fL8Y6",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright (c) 2019 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.request-initiator-view-tree .fill {\n  right: -6px; /* Same as the margin in .request-initiator-view but negative. */\n}\n\n.request-initiator-view-section-title {\n  font-weight: bold;\n  padding: 4px;\n}\n\n.request-initiator-view-section-title:focus-visible {\n  background-color: var(--color-background-elevation-2);\n}\n\n@media (forced-colors: active) {\n  .request-initiator-view-section-title:focus-visible {\n    forced-color-adjust: none;\n    background-color: Highlight;\n    color: HighlightText;\n  }\n}\n\n/*# sourceURL=requestInitiatorViewTree.css */\n");var n=i})),r.register("dDrZy",(function(t,i){e(t.exports,"RequestPreviewView",(()=>m));var n=r("ixFnt"),o=r("7f6zc"),s=r("5adRV"),a=r("9z2ZV"),l=r("gwA6q"),d=r("kt9Ih"),c=r("7XVA7"),h=r("5pSyA");const u={failedToLoadResponseData:"Failed to load response data",previewNotAvailable:"Preview not available"},p=n.i18n.registerUIStrings("panels/network/RequestPreviewView.ts",u),g=n.i18n.getLocalizedString.bind(void 0,p);class m extends d.RequestResponseView{constructor(e){super(e)}async showPreview(){const e=await super.showPreview();if(!(e instanceof a.View.SimpleView))return e;const t=new a.Toolbar.Toolbar("network-item-preview-toolbar",this.element);return e.toolbarItems().then((e=>{e.map((e=>t.appendToolbarItem(e)))})),e}async htmlPreview(){const e=await this.request.contentData();if(e.error)return new a.EmptyWidget.EmptyWidget(g(u.failedToLoadResponseData)+": "+e.error);if(!new Set(["text/html","text/plain","application/xhtml+xml"]).has(this.request.mimeType))return null;const t=e.encoded?window.atob(e.content):e.content,r=await s.JSONView.JSONView.createView(t);if(r)return r;const i=o.ContentProvider.contentAsDataURL(e.content,this.request.mimeType,e.encoded,this.request.charset());return i?new(0,l.RequestHTMLView)(i):null}async createPreview(){if(this.request.signedExchangeInfo())return new(0,c.SignedExchangeInfoView)(this.request);if(this.request.webBundleInfo())return new(0,h.WebBundleInfoView)(this.request);const e=await this.htmlPreview();if(e)return e;const t=await s.PreviewFactory.PreviewFactory.createPreview(this.request,this.request.mimeType);return t||new a.EmptyWidget.EmptyWidget(g(u.previewNotAvailable))}}})),r.register("gwA6q",(function(t,i){e(t.exports,"RequestHTMLView",(()=>s));var n=r("9z2ZV"),o=r("54yWf");class s extends n.Widget.VBox{dataURL;constructor(e){super(!0),this.dataURL=encodeURI(e).replace(/#/g,"%23"),this.contentElement.classList.add("html","request-view")}wasShown(){this.createIFrame(),this.registerCSSFiles([o.default])}willHide(){this.contentElement.removeChildren()}createIFrame(){this.contentElement.removeChildren();const e=document.createElement("iframe");e.className="html-preview-frame",e.setAttribute("sandbox",""),e.setAttribute("src",this.dataURL),e.tabIndex=-1,n.ARIAUtils.markAsPresentation(e),this.contentElement.appendChild(e)}}})),r.register("54yWf",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright (c) 2018 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.html-preview-frame {\n  /* We always want a white background, even in dark mode */\n  --override-preview-frame-background: #fff;\n\n  box-shadow: var(--drop-shadow);\n  background: var(--override-preview-frame-background);\n  flex-grow: 1;\n  margin: 20px;\n}\n\n/*# sourceURL=requestHTMLView.css */\n");var n=i})),r.register("kt9Ih",(function(t,i){e(t.exports,"RequestResponseView",(()=>h));var n=r("koSS8"),o=r("ixFnt"),s=r("5adRV"),a=r("9z2ZV");const l={thisRequestHasNoResponseData:"This request has no response data available.",failedToLoadResponseData:"Failed to load response data"},d=o.i18n.registerUIStrings("panels/network/RequestResponseView.ts",l),c=o.i18n.getLocalizedString.bind(void 0,d);class h extends a.Widget.VBox{request;contentViewPromise;constructor(e){super(),this.element.classList.add("request-view"),this.request=e,this.contentViewPromise=null}static hasTextContent(e,t){const r=e.mimeType||"";let i=n.ResourceType.ResourceType.fromMimeType(r);return i===n.ResourceType.resourceTypes.Other&&(i=e.contentType()),i===n.ResourceType.resourceTypes.Image?r.startsWith("image/svg"):!!i.isTextType()||!t.error&&(i===n.ResourceType.resourceTypes.Other&&(Boolean(t.content)&&!t.encoded))}static async sourceViewForRequest(e){let t=u.get(e);if(void 0!==t)return t;const r=await e.contentData();if(!h.hasTextContent(e,r))return u.delete(e),null;const i=e.resourceType().canonicalMimeType()||e.mimeType;return t=s.ResourceSourceFrame.ResourceSourceFrame.createSearchableView(e,i),u.set(e,t),t}wasShown(){this.doShowPreview()}doShowPreview(){return this.contentViewPromise||(this.contentViewPromise=this.showPreview()),this.contentViewPromise}async showPreview(){const e=await this.createPreview();return e.show(this.element),e}async createPreview(){const e=await this.request.contentData(),t=await h.sourceViewForRequest(this.request);return e.content&&t||e.error?e.content&&t?t:e.error?new a.EmptyWidget.EmptyWidget(c(l.failedToLoadResponseData)+": "+e.error):204===this.request.statusCode?new a.EmptyWidget.EmptyWidget(c(l.thisRequestHasNoResponseData)):new a.EmptyWidget.EmptyWidget(c(l.failedToLoadResponseData)):new a.EmptyWidget.EmptyWidget(c(l.thisRequestHasNoResponseData))}async revealLine(e){const t=await this.doShowPreview();t instanceof s.ResourceSourceFrame.SearchableContainer&&t.revealPosition(e)}}const u=new WeakMap})),r.register("7XVA7",(function(t,i){e(t.exports,"SignedExchangeInfoView",(()=>p)),e(t.exports,"Category",(()=>g));var n=r("e7bLS"),o=r("ixFnt"),s=r("47tV8"),a=r("18Hhm"),l=r("a3yig"),d=r("9z2ZV");const c={errors:"Errors",signedHttpExchange:"Signed HTTP exchange",learnmore:"Learn more",requestUrl:"Request URL",responseCode:"Response code",headerIntegrityHash:"Header integrity hash",responseHeaders:"Response headers",signature:"Signature",label:"Label",certificateUrl:"Certificate URL",viewCertificate:"View certificate",integrity:"Integrity",certificateSha:"Certificate SHA256",validityUrl:"Validity URL",date:"Date",expires:"Expires",certificate:"Certificate",subject:"Subject",validFrom:"Valid from",validUntil:"Valid until",issuer:"Issuer"},h=o.i18n.registerUIStrings("panels/network/SignedExchangeInfoView.ts",c),u=o.i18n.getLocalizedString.bind(void 0,h);class p extends d.Widget.VBox{responseHeadersItem;constructor(e){super(),console.assert(null!==e.signedExchangeInfo());const t=e.signedExchangeInfo();this.element.classList.add("signed-exchange-info-view");const r=new d.TreeOutline.TreeOutlineInShadow;r.registerCSSFiles([s.default]),r.element.classList.add("signed-exchange-info-tree"),r.setFocusable(!1),r.makeDense(),r.expandTreeElementsWhenArrowing=!0,this.element.appendChild(r.element);const i=new Map;if(t.errors&&t.errors.length){const e=new g(r,u(c.errors));for(const r of t.errors){const t=document.createDocumentFragment();if(t.appendChild(d.Icon.Icon.create("smallicon-error","prompt-icon")),t.createChild("div","error-log").textContent=r.message,e.createLeaf(t),r.errorField){let e=i.get(r.signatureIndex);e||(e=new Set,i.set(r.signatureIndex,e)),e.add(r.errorField)}}}const o=document.createDocumentFragment();o.createChild("div","header-name").textContent=u(c.signedHttpExchange);const a=d.XLink.XLink.create("https://github.com/WICG/webpackage",u(c.learnmore),"header-toggle");o.appendChild(a);const h=new g(r,o);if(t.header){const o=t.header,s=e.redirectDestination(),a=this.formatHeader(u(c.requestUrl),o.requestUrl);if(s){const e=l.Linkifier.Linkifier.linkifyRevealable(s,"View request");e.classList.add("header-toggle"),a.appendChild(e)}h.createLeaf(a),h.createLeaf(this.formatHeader(u(c.responseCode),String(o.responseCode))),h.createLeaf(this.formatHeader(u(c.headerIntegrityHash),o.headerIntegrity)),this.responseHeadersItem=h.createLeaf(this.formatHeader(u(c.responseHeaders),""));const p=o.responseHeaders;for(const e in p){const t=new d.TreeOutline.TreeElement(this.formatHeader(e,p[e]));t.selectable=!1,this.responseHeadersItem.appendChild(t)}this.responseHeadersItem.expand();for(let e=0;e<o.signatures.length;++e){const t=i.get(e)||new Set,s=o.signatures[e],a=new g(r,u(c.signature));if(a.createLeaf(this.formatHeader(u(c.label),s.label)),a.createLeaf(this.formatHeaderForHexData(u(c.signature),s.signature,t.has("signatureSig"))),s.certUrl){const e=this.formatHeader(u(c.certificateUrl),s.certUrl,t.has("signatureCertUrl"));if(s.certificates){const t=e.createChild("span","devtools-link header-toggle");t.textContent=u(c.viewCertificate),t.addEventListener("click",n.InspectorFrontendHost.InspectorFrontendHostInstance.showCertificateViewer.bind(null,s.certificates),!1)}a.createLeaf(e)}a.createLeaf(this.formatHeader(u(c.integrity),s.integrity,t.has("signatureIntegrity"))),s.certSha256&&a.createLeaf(this.formatHeaderForHexData(u(c.certificateSha),s.certSha256,t.has("signatureCertSha256"))),a.createLeaf(this.formatHeader(u(c.validityUrl),s.validityUrl,t.has("signatureValidityUrl"))),a.createLeaf().title=this.formatHeader(u(c.date),new Date(1e3*s.date).toUTCString(),t.has("signatureTimestamps")),a.createLeaf().title=this.formatHeader(u(c.expires),new Date(1e3*s.expires).toUTCString(),t.has("signatureTimestamps"))}}if(t.securityDetails){const e=t.securityDetails,i=new g(r,u(c.certificate));i.createLeaf(this.formatHeader(u(c.subject),e.subjectName)),i.createLeaf(this.formatHeader(u(c.validFrom),new Date(1e3*e.validFrom).toUTCString())),i.createLeaf(this.formatHeader(u(c.validUntil),new Date(1e3*e.validTo).toUTCString())),i.createLeaf(this.formatHeader(u(c.issuer),e.issuer))}}formatHeader(e,t,r){const i=document.createDocumentFragment(),n=i.createChild("div","header-name");n.textContent=e+": ",i.createChild("span","header-separator");const o=i.createChild("div","header-value source-code");return o.textContent=t,r&&(n.classList.add("error-field"),o.classList.add("error-field")),i}formatHeaderForHexData(e,t,r){const i=document.createDocumentFragment(),n=i.createChild("div","header-name");n.textContent=e+": ",i.createChild("span","header-separator");const o=i.createChild("div","header-value source-code hex-data");return o.textContent=t.replace(/(.{2})/g,"$1 "),r&&(n.classList.add("error-field"),o.classList.add("error-field")),i}wasShown(){super.wasShown(),this.registerCSSFiles([a.default])}}class g extends d.TreeOutline.TreeElement{toggleOnClick;expanded;constructor(e,t){super(t,!0),this.selectable=!1,this.toggleOnClick=!0,this.expanded=!0,e.appendChild(this)}createLeaf(e){const t=new d.TreeOutline.TreeElement(e);return t.selectable=!1,this.appendChild(t),t}}})),r.register("47tV8",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2018 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.tree-outline {\n  padding-left: 0;\n}\n\n.tree-outline > ol {\n  padding-bottom: 5px;\n  border-bottom: solid 1px var(--color-details-hairline);\n}\n\n.tree-outline > .parent {\n  user-select: none;\n  font-weight: bold;\n  color: var(--color-text-primary);\n  margin-top: -1px;\n  display: flex;\n  align-items: center;\n  height: 26px;\n}\n\n.tree-outline li {\n  padding-left: 5px;\n  line-height: 20px;\n}\n\n.tree-outline li:not(.parent) {\n  display: block;\n  margin-left: 10px;\n}\n\n.tree-outline li:not(.parent)::before {\n  display: none;\n}\n\n.tree-outline .header-name {\n  color: var(--color-text-secondary);\n  display: inline-block;\n  margin-right: 0.25em;\n  font-weight: bold;\n  vertical-align: top;\n  white-space: pre-wrap;\n}\n\n.tree-outline .header-separator {\n  user-select: none;\n}\n\n.tree-outline .header-value {\n  display: inline;\n  margin-right: 1em;\n  white-space: pre-wrap;\n  word-break: break-all;\n  margin-top: 1px;\n}\n\n.tree-outline .header-toggle {\n  display: inline;\n  margin-left: 30px;\n  font-weight: normal;\n  color: var(--color-text-disabled);\n}\n\n.tree-outline .header-toggle:hover {\n  --override-header-hover-color: rgb(20% 20% 45%);\n\n  color: var(--override-header-hover-color);\n}\n\n.-theme-with-dark-background .tree-outline .header-toggle:hover,\n:host-context(.-theme-with-dark-background) .tree-outline .header-toggle:hover {\n  --override-header-hover-color: rgb(140 140 204);\n}\n\n.tree-outline .error-log {\n  color: var(--color-red);\n  display: inline-block;\n  margin-right: 0.25em;\n  margin-left: 0.25em;\n  font-weight: bold;\n  vertical-align: top;\n  white-space: pre-wrap;\n}\n\n.tree-outline .hex-data {\n  display: block;\n  word-break: break-word;\n  margin-left: 20px;\n}\n\n.tree-outline .error-field {\n  color: var(--color-red);\n}\n\n/*# sourceURL=signedExchangeInfoTree.css */\n");var n=i})),r.register("18Hhm",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright (c) 2018 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.signed-exchange-info-view {\n  user-select: text;\n  overflow: auto;\n}\n\n.signed-exchange-info-tree {\n  flex-grow: 1;\n  overflow-y: auto;\n  margin: 0;\n}\n\n/*# sourceURL=signedExchangeInfoView.css */\n");var n=i})),r.register("5pSyA",(function(t,i){e(t.exports,"WebBundleInfoView",(()=>b));var n=r("koSS8"),o=r("ixFnt"),s=r("9z2ZV"),a=r("dS5IF"),l=r("kpUjp"),d=r("cY3yZ"),c=r("1uT81"),h=r("fxJGO"),u=r("kdQiu");const{render:p,html:g}=a,m={bundledResource:"Bundled resource"},f=o.i18n.registerUIStrings("panels/network/components/WebBundleInfoView.ts",m),w=o.i18n.getLocalizedString.bind(void 0,f);class b extends s.Widget.VBox{constructor(e){super();const t=e.webBundleInfo();if(!t)throw new Error("Trying to render a Web Bundle info without providing data");const r=new k(t,e.parsedURL.lastPathComponent);this.contentElement.appendChild(r),r.render()}}class k extends HTMLElement{static litTagName=a.literal`devtools-web-bundle-info`;#e=this.attachShadow({mode:"open"});#A;#N;constructor(e,t){super(),this.#A=e,this.#N=t}connectedCallback(){this.#e.adoptedStyleSheets=[u.default]}render(){const e=this.#A.resourceUrls?.map((e=>{const t=n.ResourceType.ResourceType.mimeFromURL(e)||null,r=n.ResourceType.ResourceType.fromMimeTypeOverride(t)||n.ResourceType.ResourceType.fromMimeType(t),i=(0,h.imageNameForResourceType)(r);return{cells:[{columnId:"url",value:null,renderer:()=>g`
                <div style="display: flex;">
                  <${d.Icon.Icon.litTagName} class="icon"
                    .data=${{color:"",iconName:i,width:"18px"}}>
                  </${d.Icon.Icon.litTagName}>
                  <span>${e}</span>
                </div>`}]}}));p(g`
      <div class="header">
        <${d.Icon.Icon.litTagName} class="icon"
          .data=${{color:"",iconName:"resourceWebBundle",width:"16px"}}>
        </${d.Icon.Icon.litTagName}>
        <span>${this.#N}</span>
        <x-link href="https://web.dev/web-bundles/#explaining-web-bundles">
          <${d.Icon.Icon.litTagName} class="icon"
            .data=${{color:"var(--color-text-secondary)",iconName:"help_outline",width:"16px"}}>
          </${d.Icon.Icon.litTagName}>
        </x-link>
      </div>
      <div>
        <${c.DataGrid.DataGrid.litTagName}
          .data=${{columns:[{id:"url",title:w(m.bundledResource),widthWeighting:1,visible:!0,hideable:!1}],rows:e,activeSort:null}}>
        </${c.DataGrid.DataGrid.litTagName}>
      </div>`,this.#e,{host:this})}}l.CustomElements.defineComponent("devtools-web-bundle-info",k)})),r.register("kdQiu",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n:host {\n  --icon-padding: 4px;\n}\n\n.header {\n  display: flex;\n  font-weight: bold;\n  padding: calc(2 * var(--icon-padding)) var(--icon-padding);\n}\n\n.icon {\n  margin: 0 var(--icon-padding);\n}\n\n/*# sourceURL=WebBundleInfoView.css */\n");var n=i})),r.register("gpw7U",(function(t,i){e(t.exports,"RequestTimingView",(()=>f)),e(t.exports,"RequestTimeRangeNames",(()=>w)),e(t.exports,"ConnectionSetupRangeNames",(()=>v)),e(t.exports,"ServiceWorkerRangeNames",(()=>k));var n=r("koSS8"),o=r("e7bLS"),s=r("ixFnt"),a=r("eQFvP"),l=r("g4rSN"),d=r("R6KC8"),c=r("9z2ZV"),h=r("kBxCb"),u=r("4dQmF");const p={receivingPush:"Receiving `Push`",queueing:"Queueing",stalled:"Stalled",initialConnection:"Initial connection",dnsLookup:"DNS Lookup",proxyNegotiation:"Proxy negotiation",readingPush:"Reading `Push`",contentDownload:"Content Download",requestSent:"Request sent",requestToServiceworker:"Request to `ServiceWorker`",startup:"Startup",respondwith:"respondWith",ssl:"SSL",total:"Total",waitingTtfb:"Waiting (TTFB)",label:"Label",waterfall:"Waterfall",duration:"Duration",queuedAtS:"Queued at {PH1}",startedAtS:"Started at {PH1}",serverPush:"Server Push",resourceScheduling:"Resource Scheduling",connectionStart:"Connection Start",requestresponse:"Request/Response",cautionRequestIsNotFinishedYet:"CAUTION: request is not finished yet!",explanation:"Explanation",serverTiming:"Server Timing",time:"TIME",theServerTimingApi:"the Server Timing API",duringDevelopmentYouCanUseSToAdd:"During development, you can use {PH1} to add insights into the server-side timing of this request.",durationC:"DURATION",originalRequest:"Original Request",responseReceived:"Response Received",unknown:"Unknown",sourceOfResponseS:"Source of response: {PH1}",cacheStorageCacheNameS:"Cache storage cache name: {PH1}",cacheStorageCacheNameUnknown:"Cache storage cache name: Unknown",retrievalTimeS:"Retrieval Time: {PH1}",serviceworkerCacheStorage:"`ServiceWorker` cache storage",fromHttpCache:"From HTTP cache",networkFetch:"Network fetch",fallbackCode:"Fallback code"},g=s.i18n.registerUIStrings("panels/network/RequestTimingView.ts",p),m=s.i18n.getLocalizedString.bind(void 0,g);class f extends c.Widget.VBox{request;calculator;lastMinimumBoundary;tableElement;constructor(e,t){super(),this.element.classList.add("resource-timing-view"),this.request=e,this.calculator=t,this.lastMinimumBoundary=-1}static timeRangeTitle(e){switch(e){case w.Push:return m(p.receivingPush);case w.Queueing:return m(p.queueing);case w.Blocking:return m(p.stalled);case w.Connecting:return m(p.initialConnection);case w.DNS:return m(p.dnsLookup);case w.Proxy:return m(p.proxyNegotiation);case w.ReceivingPush:return m(p.readingPush);case w.Receiving:return m(p.contentDownload);case w.Sending:return m(p.requestSent);case w.ServiceWorker:return m(p.requestToServiceworker);case w.ServiceWorkerPreparation:return m(p.startup);case w.ServiceWorkerRespondWith:return m(p.respondwith);case w.SSL:return m(p.ssl);case w.Total:return m(p.total);case w.Waiting:return m(p.waitingTtfb);default:return e}}static calculateRequestTimeRanges(e,t){const r=[];function i(e,t,i){t<Number.MAX_VALUE&&t<=i&&r.push({name:e,start:t,end:i})}function n(e){for(let t=0;t<e.length;++t)if(e[t]>0)return e[t]}function o(e,t,r){t>=0&&r>=0&&i(e,l+t/1e3,l+r/1e3)}const s=e.timing;if(!s){const t=-1!==e.issueTime()?e.issueTime():-1!==e.startTime?e.startTime:0,n=-1!==e.issueTime()&&-1!==e.startTime&&e.issueTime()!==e.startTime,o=-1===e.responseReceivedTime?n?e.startTime:Number.MAX_VALUE:e.responseReceivedTime,s=-1===e.endTime?Number.MAX_VALUE:e.endTime;i(w.Total,t,s),i(w.Blocking,t,o);return i(-1===e.responseReceivedTime?w.Connecting:w.Receiving,o,s),r}const a=e.issueTime(),l=s.requestTime,d=n([e.endTime,e.responseReceivedTime])||l;if(i(w.Total,a<l?a:l,d),s.pushStart){const e=s.pushEnd||d;e>t&&i(w.Push,Math.max(s.pushStart,t),e)}a<l&&i(w.Queueing,a,l);const c=1e3*(e.responseReceivedTime-l);if(e.fetchedViaServiceWorker)o(w.Blocking,0,s.workerStart),o(w.ServiceWorkerPreparation,s.workerStart,s.workerReady),o(w.ServiceWorkerRespondWith,s.workerFetchStart,s.workerRespondWithSettled),o(w.ServiceWorker,s.workerReady,s.sendEnd),o(w.Waiting,s.sendEnd,c);else if(!s.pushStart){const e=n([s.dnsStart,s.connectStart,s.sendStart,c])||0;o(w.Blocking,0,e),o(w.Proxy,s.proxyStart,s.proxyEnd),o(w.DNS,s.dnsStart,s.dnsEnd),o(w.Connecting,s.connectStart,s.connectEnd),o(w.SSL,s.sslStart,s.sslEnd),o(w.Sending,s.sendStart,s.sendEnd),o(w.Waiting,Math.max(s.sendEnd,s.connectEnd,s.dnsEnd,s.proxyEnd,e),c)}return-1!==e.endTime&&i(s.pushStart?w.ReceivingPush:w.Receiving,e.responseReceivedTime,d),r}static createTimingTable(e,t){const r=document.createElement("table");r.classList.add("network-timing-table");const i=r.createChild("colgroup");i.createChild("col","labels"),i.createChild("col","bars"),i.createChild("col","duration");const o=f.calculateRequestTimeRanges(e,t.minimumBoundary()),a=o.map((e=>e.start)).reduce(((e,t)=>Math.min(e,t))),l=o.map((e=>e.end)).reduce(((e,t)=>Math.max(e,t))),d=100/(l-a);let h,u,b,C,S=0;const y=r.createChild("thead","network-timing-start"),T=y.createChild("tr"),R=T.createChild("th");R.createChild("span","network-timing-hidden-header").textContent=m(p.label),R.scope="col";const x=T.createChild("th");x.createChild("span","network-timing-hidden-header").textContent=m(p.waterfall),x.scope="col";const I=T.createChild("th");I.createChild("span","network-timing-hidden-header").textContent=m(p.duration),I.scope="col";const q=y.createChild("tr").createChild("td"),L=y.createChild("tr").createChild("td");let F;q.colSpan=L.colSpan=3,c.UIUtils.createTextChild(q,m(p.queuedAtS,{PH1:t.formatValue(e.issueTime(),2)})),c.UIUtils.createTextChild(L,m(p.startedAtS,{PH1:t.formatValue(e.startTime,2)}));for(let e=0;e<o.length;++e){const i=o[e],n=i.name;if(n===w.Total){S=i.end-i.start;continue}n===w.Push?M(m(p.serverPush)):n===w.Queueing?C||(C=M(m(p.resourceScheduling))):v.has(n)?h||(h=M(m(p.connectionStart))):k.has(n)?u||(u=M("Service Worker")):b||(b=M(m(p.requestresponse)));const g=d*(i.start-a);F=d*(l-i.end);const y=i.end-i.start,T=r.createChild("tr"),R=T.createChild("td");c.UIUtils.createTextChild(R,f.timeRangeTitle(n));const x=T.createChild("td").createChild("div","network-timing-row"),I=x.createChild("span","network-timing-bar "+n);I.style.left=g+"%",I.style.right=F+"%",I.textContent="​",c.ARIAUtils.setAccessibleName(x,m(p.startedAtS,{PH1:t.formatValue(i.start,2)}));T.createChild("td").createChild("div","network-timing-bar-title").textContent=s.TimeUtilities.secondsToString(y,!0),"serviceworker-respondwith"===i.name&&(R.classList.add("network-fetch-timing-bar-clickable"),r.createChild("tr","network-fetch-timing-bar-details"),R.setAttribute("tabindex","0"),R.setAttribute("role","switch"),c.ARIAUtils.setChecked(R,!1))}if(!e.finished&&!e.preserved){const e=r.createChild("tr").createChild("td","caution");e.colSpan=3,c.UIUtils.createTextChild(e,m(p.cautionRequestIsNotFinishedYet))}const E=r.createChild("tr","network-timing-footer"),A=E.createChild("td");A.colSpan=1,A.appendChild(c.XLink.XLink.create("https://developer.chrome.com/docs/devtools/network/reference#timing-explanation",m(p.explanation))),E.createChild("td"),c.UIUtils.createTextChild(E.createChild("td"),s.TimeUtilities.secondsToString(S,!0));const N=e.serverTimings,P=void 0===F?100:F,B=r.createChild("tr","network-timing-table-header").createChild("td");B.colSpan=3,B.createChild("hr","break");const U=r.createChild("tr","network-timing-table-header");if(c.UIUtils.createTextChild(U.createChild("td"),m(p.serverTiming)),U.createChild("td"),c.UIUtils.createTextChild(U.createChild("td"),m(p.time)),!N){const e=r.createChild("tr").createChild("td");e.colSpan=3;const t=c.XLink.XLink.create("https://web.dev/custom-metrics/#server-timing-api",m(p.theServerTimingApi));return e.appendChild(s.i18n.getFormatLocalizedString(g,p.duringDevelopmentYouCanUseSToAdd,{PH1:t})),r}return N.filter((e=>"total"!==e.metric.toLowerCase())).forEach((e=>H(e,P))),N.filter((e=>"total"===e.metric.toLowerCase())).forEach((e=>H(e,P))),r;function H(e,t){const i=new n.Color.Generator({min:0,max:360,count:36},{min:50,max:80,count:void 0},80),o="total"===e.metric.toLowerCase(),h=r.createChild("tr",o?"network-timing-footer":"server-timing-row"),u=h.createChild("td","network-timing-metric"),p=e.description||e.metric;c.UIUtils.createTextChild(u,p),c.Tooltip.Tooltip.install(u,p);const g=h.createChild("td").createChild("div","network-timing-row");if(null===e.value)return;const m=d*(l-a-e.value/1e3);if(m>=0){const r=g.createChild("span","network-timing-bar server-timing");r.style.left=m+"%",r.style.right=t+"%",r.textContent="​",o||(r.style.backgroundColor=i.colorForID(e.metric))}h.createChild("td").createChild("div","network-timing-bar-title").textContent=s.TimeUtilities.millisToString(e.value,!0)}function M(e){const t=r.createChild("tr","network-timing-table-header"),i=t.createChild("td");return c.UIUtils.createTextChild(i,e),c.ARIAUtils.markAsHeading(i,2),c.UIUtils.createTextChild(t.createChild("td"),""),c.UIUtils.createTextChild(t.createChild("td"),m(p.durationC)),t}}constructFetchDetailsView(){if(!this.tableElement)return;const e=this.tableElement.ownerDocument,t=e.querySelector(".network-fetch-timing-bar-details");if(!t)return;t.classList.add("network-fetch-timing-bar-details-collapsed"),self.onInvokeElement(this.tableElement,this.onToggleFetchDetails.bind(this,t));const r=new c.TreeOutline.TreeOutlineInShadow;t.appendChild(r.element);const i=l.NetworkLog.NetworkLog.instance().originalRequestForURL(this.request.url());if(i){const e=a.RemoteObject.RemoteObject.fromLocalObject(i),t=new d.ObjectPropertiesSection.RootElement(e);t.title=m(p.originalRequest),r.appendChild(t)}const n=l.NetworkLog.NetworkLog.instance().originalResponseForURL(this.request.url());if(n){const e=a.RemoteObject.RemoteObject.fromLocalObject(n),t=new d.ObjectPropertiesSection.RootElement(e);t.title=m(p.responseReceived),r.appendChild(t)}const o=e.createElement("div");o.classList.add("network-fetch-details-treeitem");let s=m(p.unknown);const h=this.request.serviceWorkerResponseSource();h&&(s=this.getLocalizedResponseSourceForCode(h)),o.textContent=m(p.sourceOfResponseS,{PH1:s});const u=new c.TreeOutline.TreeElement(o);r.appendChild(u);const g=e.createElement("div");g.classList.add("network-fetch-details-treeitem");const f=this.request.getResponseCacheStorageCacheName();g.textContent=f?m(p.cacheStorageCacheNameS,{PH1:f}):m(p.cacheStorageCacheNameUnknown);const w=new c.TreeOutline.TreeElement(g);r.appendChild(w);const b=this.request.getResponseRetrievalTime();if(b){const t=e.createElement("div");t.classList.add("network-fetch-details-treeitem"),t.textContent=m(p.retrievalTimeS,{PH1:b.toString()});const i=new c.TreeOutline.TreeElement(t);r.appendChild(i)}}getLocalizedResponseSourceForCode(e){switch(e){case"cache-storage":return m(p.serviceworkerCacheStorage);case"http-cache":return m(p.fromHttpCache);case"network":return m(p.networkFetch);default:return m(p.fallbackCode)}}onToggleFetchDetails(e,t){if(!t.target)return;const r=t.target;if(r.classList.contains("network-fetch-timing-bar-clickable")){e.classList.contains("network-fetch-timing-bar-details-collapsed")&&o.userMetrics.actionTaken(o.UserMetrics.Action.NetworkPanelServiceWorkerRespondWith);const t="true"===r.getAttribute("aria-checked");r.setAttribute("aria-checked",String(!t)),e.classList.toggle("network-fetch-timing-bar-details-collapsed"),e.classList.toggle("network-fetch-timing-bar-details-expanded")}}wasShown(){this.request.addEventListener(a.NetworkRequest.Events.TimingChanged,this.refresh,this),this.request.addEventListener(a.NetworkRequest.Events.FinishedLoading,this.refresh,this),this.calculator.addEventListener(h.Events.BoundariesChanged,this.boundaryChanged,this),this.registerCSSFiles([u.default]),this.refresh()}willHide(){this.request.removeEventListener(a.NetworkRequest.Events.TimingChanged,this.refresh,this),this.request.removeEventListener(a.NetworkRequest.Events.FinishedLoading,this.refresh,this),this.calculator.removeEventListener(h.Events.BoundariesChanged,this.boundaryChanged,this)}refresh(){this.tableElement&&this.tableElement.remove(),this.tableElement=f.createTimingTable(this.request,this.calculator),this.tableElement.classList.add("resource-timing-table"),this.element.appendChild(this.tableElement),this.request.fetchedViaServiceWorker&&this.constructFetchDetailsView()}boundaryChanged(){const e=this.calculator.minimumBoundary();e!==this.lastMinimumBoundary&&(this.lastMinimumBoundary=e,this.refresh())}}var w,b;(b=w||(w={})).Push="push",b.Queueing="queueing",b.Blocking="blocking",b.Connecting="connecting",b.DNS="dns",b.Proxy="proxy",b.Receiving="receiving",b.ReceivingPush="receiving-push",b.Sending="sending",b.ServiceWorker="serviceworker",b.ServiceWorkerPreparation="serviceworker-preparation",b.ServiceWorkerRespondWith="serviceworker-respondwith",b.SSL="ssl",b.Total="total",b.Waiting="waiting";const k=new Set([w.ServiceWorker,w.ServiceWorkerPreparation,w.ServiceWorkerRespondWith]),v=new Set([w.Queueing,w.Blocking,w.Connecting,w.DNS,w.Proxy,w.SSL])})),r.register("kBxCb",(function(t,i){e(t.exports,"NetworkTimeBoundary",(()=>d)),e(t.exports,"NetworkTimeCalculator",(()=>c)),e(t.exports,"Events",(()=>u)),e(t.exports,"_minimumSpread",(()=>h)),e(t.exports,"NetworkTransferTimeCalculator",(()=>p)),e(t.exports,"NetworkTransferDurationCalculator",(()=>g));var n=r("koSS8"),o=r("ixFnt");const s={sLatencySDownloadSTotal:"{PH1} latency, {PH2} download ({PH3} total)",sLatency:"{PH1} latency",sDownload:"{PH1} download",sFromServiceworker:"{PH1} (from `ServiceWorker`)",sFromCache:"{PH1} (from cache)"},a=o.i18n.registerUIStrings("panels/network/NetworkTimeCalculator.ts",s),l=o.i18n.getLocalizedString.bind(void 0,a);class d{minimum;maximum;constructor(e,t){this.minimum=e,this.maximum=t}equals(e){return this.minimum===e.minimum&&this.maximum===e.maximum}}class c extends n.ObjectWrapper.ObjectWrapper{startAtZero;minimumBoundaryInternal;maximumBoundaryInternal;boundryChangedEventThrottler;window;workingArea;constructor(e){super(),this.startAtZero=e,this.minimumBoundaryInternal=-1,this.maximumBoundaryInternal=-1,this.boundryChangedEventThrottler=new n.Throttler.Throttler(0),this.window=null}setWindow(e){this.window=e,this.boundaryChanged()}setInitialUserFriendlyBoundaries(){this.minimumBoundaryInternal=0,this.maximumBoundaryInternal=1}computePosition(e){return(e-this.minimumBoundary())/this.boundarySpan()*(this.workingArea||0)}formatValue(e,t){return o.TimeUtilities.secondsToString(e,Boolean(t))}minimumBoundary(){return this.window?this.window.minimum:this.minimumBoundaryInternal}zeroTime(){return this.minimumBoundaryInternal}maximumBoundary(){return this.window?this.window.maximum:this.maximumBoundaryInternal}boundary(){return new d(this.minimumBoundary(),this.maximumBoundary())}boundarySpan(){return this.maximumBoundary()-this.minimumBoundary()}reset(){this.minimumBoundaryInternal=-1,this.maximumBoundaryInternal=-1,this.boundaryChanged()}value(){return 0}setDisplayWidth(e){this.workingArea=e}computeBarGraphPercentages(e){let t,r,i;return t=-1!==e.startTime?(e.startTime-this.minimumBoundary())/this.boundarySpan()*100:0,r=-1!==e.responseReceivedTime?(e.responseReceivedTime-this.minimumBoundary())/this.boundarySpan()*100:this.startAtZero?t:100,i=-1!==e.endTime?(e.endTime-this.minimumBoundary())/this.boundarySpan()*100:this.startAtZero?r:100,this.startAtZero&&(i-=t,r-=t,t=0),{start:t,middle:r,end:i}}computePercentageFromEventTime(e){return-1===e||this.startAtZero?0:(e-this.minimumBoundary())/this.boundarySpan()*100}percentageToTime(e){return e*this.boundarySpan()/100+this.minimumBoundary()}boundaryChanged(){this.boundryChangedEventThrottler.schedule((async()=>{this.dispatchEventToListeners(u.BoundariesChanged)}))}updateBoundariesForEventTime(e){-1===e||this.startAtZero||(void 0===this.maximumBoundaryInternal||e>this.maximumBoundaryInternal)&&(this.maximumBoundaryInternal=e,this.boundaryChanged())}computeBarGraphLabels(e){let t="";-1!==e.responseReceivedTime&&-1!==e.endTime&&(t=o.TimeUtilities.secondsToString(e.endTime-e.responseReceivedTime));const r=e.latency>0,i=r?o.TimeUtilities.secondsToString(e.latency):t;if(e.timing)return{left:i,right:t,tooltip:void 0};let n;if(r&&t){const r=o.TimeUtilities.secondsToString(e.duration);n=l(s.sLatencySDownloadSTotal,{PH1:i,PH2:t,PH3:r})}else r?n=l(s.sLatency,{PH1:i}):t&&(n=l(s.sDownload,{PH1:t}));return e.fetchedViaServiceWorker?n=l(s.sFromServiceworker,{PH1:String(n)}):e.cached()&&(n=l(s.sFromCache,{PH1:String(n)})),{left:i,right:t,tooltip:n}}updateBoundaries(e){const t=this.lowerBound(e),r=this.upperBound(e);let i=!1;(-1!==t||this.startAtZero)&&(i=this.extendBoundariesToIncludeTimestamp(this.startAtZero?0:t)),-1!==r&&(i=this.extendBoundariesToIncludeTimestamp(r)||i),i&&this.boundaryChanged()}extendBoundariesToIncludeTimestamp(e){const t=this.minimumBoundaryInternal,r=this.maximumBoundaryInternal,i=h;return-1===this.minimumBoundaryInternal||-1===this.maximumBoundaryInternal?(this.minimumBoundaryInternal=e,this.maximumBoundaryInternal=e+i):(this.minimumBoundaryInternal=Math.min(e,this.minimumBoundaryInternal),this.maximumBoundaryInternal=Math.max(e,this.minimumBoundaryInternal+i,this.maximumBoundaryInternal)),t!==this.minimumBoundaryInternal||r!==this.maximumBoundaryInternal}lowerBound(e){return 0}upperBound(e){return 0}}const h=.1;var u;(u||(u={})).BoundariesChanged="BoundariesChanged";class p extends c{constructor(){super(!1)}formatValue(e,t){return o.TimeUtilities.secondsToString(e-this.zeroTime(),Boolean(t))}lowerBound(e){return e.issueTime()}upperBound(e){return e.endTime}}class g extends c{constructor(){super(!0)}formatValue(e,t){return o.TimeUtilities.secondsToString(e,Boolean(t))}upperBound(e){return e.duration}}})),r.register("4dQmF",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync('/*\n * Copyright 2017 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.network-timing-table {\n  width: 380px;\n  border-spacing: 0;\n  padding-left: 10px;\n  padding-right: 10px;\n  line-height: initial;\n  table-layout: fixed;\n}\n\n.network-timing-start {\n  border-top: 5px solid transparent;\n}\n\n.network-timing-start th span.network-timing-hidden-header {\n  height: 1px;\n  width: 1px;\n  position: absolute;\n  overflow: hidden;\n}\n\n.network-timing-table-header td,\n.network-timing-footer td {\n  border-top: 10px solid transparent;\n}\n\n.network-timing-table-header td {\n  color: var(--color-text-secondary);\n}\n\n.network-timing-table td {\n  padding: 4px 0;\n}\n\n.network-timing-table-header td:last-child {\n  text-align: right;\n}\n\n.network-timing-footer td:last-child {\n  font-weight: bold;\n  text-align: right;\n}\n\ntable.network-timing-table > tr:not(.network-timing-table-header):not(.network-timing-footer) > td:first-child {\n  padding-left: 12px;\n}\n\n.network-timing-table col.labels {\n  width: 156px;\n}\n\n.network-timing-table col.duration {\n  width: 80px;\n}\n\n.network-timing-table td.caution {\n  font-weight: bold;\n  color: var(--issue-color-yellow);\n  padding: 2px 0;\n}\n\n.network-timing-table hr.break {\n  border: 0;\n  height: 1px;\n  background-image: linear-gradient(to right, var(--color-background-elevation-0), var(--color-text-secondary), var(--color-background-elevation-0));\n}\n\n.network-timing-row {\n  position: relative;\n  height: 15px;\n}\n\n.network-timing-bar {\n  position: absolute;\n  min-width: 1px;\n  top: 0;\n  bottom: 0;\n}\n\n.network-timing-bar-title {\n  color: var(--color-text-primary);\n  white-space: nowrap;\n  text-align: right;\n}\n\n.network-timing-bar.queueing,\n.network-timing-bar.total {\n  border: 1px solid var(--color-text-secondary);\n}\n\n.network-timing-bar.blocking,\n.-theme-preserve {\n  background-color: var(--override-network-overview-blocking);\n}\n\n.network-timing-bar.proxy,\n.-theme-preserve {\n  background-color: var(--override-network-overview-proxy);\n}\n\n.network-timing-bar.dns,\n.-theme-preserve {\n  background-color: var(--override-network-overview-dns);\n}\n\n.network-timing-bar.connecting,\n.network-timing-bar.serviceworker,\n.network-timing-bar.serviceworker-preparation,\n.-theme-preserve {\n  background-color: var(--override-network-overview-service-worker);\n}\n\n.network-timing-bar.ssl,\n.-theme-preserve {\n  background-color: var(--override-network-overview-ssl);\n}\n\n.network-timing-bar.serviceworker-respondwith,\n.-theme-preserve {\n  background-color: var(--override-network-overview-service-worker-respond-with);\n}\n\n.network-fetch-timing-bar-clickable::before {\n  user-select: none;\n  -webkit-mask-image: var(--image-file-treeoutlineTriangles);\n  -webkit-mask-position: 0 0;\n  -webkit-mask-size: 32px 24px;\n  float: left;\n  width: 8px;\n  height: 12px;\n  margin-right: 2px;\n  content: "";\n  position: relative;\n  top: 3px;\n  background-color: var(--color-text-secondary);\n}\n\n.network-fetch-timing-bar-clickable {\n  position: relative;\n  left: -12px;\n}\n\n.network-fetch-timing-bar-clickable:focus-visible {\n  background-color: var(--color-background-elevation-1);\n}\n\n.network-fetch-timing-bar-clickable[aria-checked="true"]::before {\n  -webkit-mask-position: -16px 0;\n}\n\n.network-fetch-timing-bar-details-collapsed {\n  display: none;\n}\n\n.network-fetch-timing-bar-details-expanded {\n  display: block;\n}\n\n.network-fetch-timing-bar-details {\n  padding-left: 11px;\n  width: fit-content;\n}\n\n.network-fetch-details-treeitem {\n  width: max-content;\n}\n\n.network-timing-bar.sending,\n.-theme-preserve {\n  background-color: var(--override-network-overview-sending);\n}\n\n.network-timing-bar.waiting,\n.-theme-preserve {\n  background-color: var(--override-network-overview-waiting);\n}\n\n.network-timing-bar.receiving,\n.network-timing-bar.receiving-push,\n.-theme-preserve {\n  background-color: var(--override-network-overview-receiving);\n}\n\n.network-timing-bar.push,\n.-theme-preserve {\n  background-color: var(--override-network-overview-push);\n}\n\n.server-timing-row:nth-child(even) {\n  background: var(--color-background-elevation-0);\n}\n\n.network-timing-bar.server-timing,\n.-theme-preserve {\n  background-color: var(--color-background-elevation-2);\n}\n\n.network-timing-table td.network-timing-metric {\n  white-space: nowrap;\n  max-width: 150px;\n  overflow-x: hidden;\n  text-overflow: ellipsis;\n}\n\n.network-timing-bar.proxy,\n.network-timing-bar.dns,\n.network-timing-bar.ssl,\n.network-timing-bar.connecting,\n.network-timing-bar.blocking {\n  height: 10px;\n  margin: auto;\n}\n\n@media (forced-colors: active) {\n  .network-timing-bar.blocking,\n  .network-timing-bar.proxy,\n  .network-timing-bar.dns,\n  .network-timing-bar.connecting,\n  .network-timing-bar.serviceworker,\n  .network-timing-bar.serviceworker-preparation,\n  .network-timing-bar.ssl,\n  .network-timing-bar.sending,\n  .network-timing-bar.waiting,\n  .network-timing-bar.receiving,\n  .network-timing-bar.receiving-push,\n  .network-timing-bar.push,\n  .network-timing-bar.server-timing,\n  .-theme-preserve {\n    forced-color-adjust: none;\n  }\n\n  .network-timing-table-header td,\n  .network-timing-footer td {\n    forced-color-adjust: none;\n    color: ButtonText;\n  }\n}\n\n/*# sourceURL=networkTimingTable.css */\n');var n=i})),r.register("g1wr2",(function(t,i){e(t.exports,"ResourceWebSocketFrameView",(()=>k)),e(t.exports,"ResourceWebSocketFrameNodeTimeComparator",(()=>R)),e(t.exports,"_filterTypes",(()=>y)),e(t.exports,"opCodeDescriptions",(()=>S)),e(t.exports,"ResourceWebSocketFrameNode",(()=>T)),e(t.exports,"OpCodes",(()=>v));var n=r("koSS8"),o=r("e7bLS"),s=r("ixFnt"),a=r("lz7WY"),l=r("eQFvP"),d=r("7f6zc"),c=r("cObcK"),h=r("5adRV"),u=r("9z2ZV"),p=r("kYHDV"),g=r("gRynw");const m={data:"Data",length:"Length",time:"Time",webSocketFrame:"Web Socket Frame",clearAll:"Clear All",filter:"Filter",selectMessageToBrowseItsContent:"Select message to browse its content.",copyMessageD:"Copy message...",copyMessage:"Copy message",clearAllL:"Clear all",sOpcodeSMask:"{PH1} (Opcode {PH2}, mask)",sOpcodeS:"{PH1} (Opcode {PH2})",continuationFrame:"Continuation Frame",textMessage:"Text Message",binaryMessage:"Binary Message",connectionCloseMessage:"Connection Close Message",pingMessage:"Ping Message",pongMessage:"Pong Message",all:"All",send:"Send",receive:"Receive",na:"N/A",enterRegex:"Enter regex, for example: (web)?socket"},f=s.i18n.registerUIStrings("panels/network/ResourceWebSocketFrameView.ts",m),w=s.i18n.getLocalizedString.bind(void 0,f),b=s.i18n.getLazilyComputedLocalizedString.bind(void 0,f);class k extends u.Widget.VBox{request;splitWidget;dataGrid;timeComparator;mainToolbar;clearAllButton;filterTypeCombobox;filterType;filterTextInput;filterRegex;frameEmptyWidget;selectedNode;currentSelectedNode;messageFilterSetting=n.Settings.Settings.instance().createSetting("networkWebSocketMessageFilter","");constructor(e){super(),this.element.classList.add("websocket-frame-view"),this.request=e,this.splitWidget=new u.SplitWidget.SplitWidget(!1,!0,"resourceWebSocketFrameSplitViewState"),this.splitWidget.show(this.element);const t=[{id:"data",title:w(m.data),sortable:!1,weight:88},{id:"length",title:w(m.length),sortable:!1,align:c.DataGrid.Align.Right,weight:5},{id:"time",title:w(m.time),sortable:!0,weight:7}];this.dataGrid=new c.SortableDataGrid.SortableDataGrid({displayName:w(m.webSocketFrame),columns:t,editCallback:void 0,deleteCallback:void 0,refreshCallback:void 0}),this.dataGrid.setRowContextMenuCallback(function(e,t){const r=t,i=r.binaryView();i?i.addCopyToContextMenu(e,w(m.copyMessageD)):e.clipboardSection().appendItem(w(m.copyMessage),o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText.bind(o.InspectorFrontendHost.InspectorFrontendHostInstance,r.data.data));e.footerSection().appendItem(w(m.clearAllL),this.clearFrames.bind(this))}.bind(this)),this.dataGrid.setStickToBottom(!0),this.dataGrid.setCellClass("websocket-frame-view-td"),this.timeComparator=R,this.dataGrid.sortNodes(this.timeComparator,!1),this.dataGrid.markColumnAsSortedBy("time",c.DataGrid.Order.Ascending),this.dataGrid.addEventListener(c.DataGrid.Events.SortingChanged,this.sortItems,this),this.dataGrid.setName("ResourceWebSocketFrameView"),this.dataGrid.addEventListener(c.DataGrid.Events.SelectedNode,(e=>{this.onFrameSelected(e)}),this),this.dataGrid.addEventListener(c.DataGrid.Events.DeselectedNode,this.onFrameDeselected,this),this.mainToolbar=new u.Toolbar.Toolbar(""),this.clearAllButton=new u.Toolbar.ToolbarButton(w(m.clearAll),"largeicon-clear"),this.clearAllButton.addEventListener(u.Toolbar.ToolbarButton.Events.Click,this.clearFrames,this),this.mainToolbar.appendToolbarItem(this.clearAllButton),this.filterTypeCombobox=new u.Toolbar.ToolbarComboBox(this.updateFilterSetting.bind(this),w(m.filter));for(const e of y){const t=this.filterTypeCombobox.createOption(e.label(),e.name);this.filterTypeCombobox.addOption(t)}this.mainToolbar.appendToolbarItem(this.filterTypeCombobox),this.filterType=null;const r=w(m.enterRegex);this.filterTextInput=new u.Toolbar.ToolbarInput(r,"",.4),this.filterTextInput.addEventListener(u.Toolbar.ToolbarInput.Event.TextChanged,this.updateFilterSetting,this);const i=this.messageFilterSetting.get();i&&this.filterTextInput.setValue(i),this.filterRegex=null,this.mainToolbar.appendToolbarItem(this.filterTextInput);const n=new u.Widget.VBox;n.element.appendChild(this.mainToolbar.element),this.dataGrid.asWidget().show(n.element),n.setMinimumSize(0,72),this.splitWidget.setMainWidget(n),this.frameEmptyWidget=new u.EmptyWidget.EmptyWidget(w(m.selectMessageToBrowseItsContent)),this.splitWidget.setSidebarWidget(this.frameEmptyWidget),this.selectedNode=null,i&&this.applyFilter(i)}static opCodeDescription(e,t){const r=S[e]||(()=>"");return w(t?m.sOpcodeSMask:m.sOpcodeS,{PH1:r(),PH2:e})}wasShown(){this.refresh(),this.registerCSSFiles([g.default]),this.request.addEventListener(l.NetworkRequest.Events.WebsocketFrameAdded,this.frameAdded,this)}willHide(){this.request.removeEventListener(l.NetworkRequest.Events.WebsocketFrameAdded,this.frameAdded,this)}frameAdded(e){const t=e.data;this.frameFilter(t)&&this.dataGrid.insertChild(new T(this.request.url(),t))}frameFilter(e){return(!this.filterType||e.type===this.filterType)&&(!this.filterRegex||this.filterRegex.test(e.text))}clearFrames(){x.set(this.request,this.request.frames().length),this.refresh()}updateFilterSetting(){const e=this.filterTextInput.value();this.messageFilterSetting.set(e),this.applyFilter(e)}applyFilter(e){const t=this.filterTypeCombobox.selectedOption().value;this.filterRegex=e?new RegExp(e,"i"):null,this.filterType="all"===t?null:t,this.refresh()}async onFrameSelected(e){this.currentSelectedNode=e.data;const t=this.currentSelectedNode.dataText(),r=this.currentSelectedNode.binaryView();if(r)return void this.splitWidget.setSidebarWidget(r);const i=await h.JSONView.JSONView.createView(t);i?this.splitWidget.setSidebarWidget(i):this.splitWidget.setSidebarWidget(new h.ResourceSourceFrame.ResourceSourceFrame(d.StaticContentProvider.StaticContentProvider.fromString(this.request.url(),n.ResourceType.resourceTypes.WebSocket,t),""))}onFrameDeselected(){this.currentSelectedNode=null,this.splitWidget.setSidebarWidget(this.frameEmptyWidget)}refresh(){this.dataGrid.rootNode().removeChildren();const e=this.request.url();let t=this.request.frames();const r=x.get(this.request)||0;t=t.slice(r),t=t.filter(this.frameFilter.bind(this)),t.forEach((t=>this.dataGrid.insertChild(new T(e,t))))}sortItems(){this.dataGrid.sortNodes(this.timeComparator,!this.dataGrid.isSortOrderAscending())}}var v,C;(C=v||(v={}))[C.ContinuationFrame=0]="ContinuationFrame",C[C.TextFrame=1]="TextFrame",C[C.BinaryFrame=2]="BinaryFrame",C[C.ConnectionCloseFrame=8]="ConnectionCloseFrame",C[C.PingFrame=9]="PingFrame",C[C.PongFrame=10]="PongFrame";const S=function(){const e=v,t=[];return t[e.ContinuationFrame]=b(m.continuationFrame),t[e.TextFrame]=b(m.textMessage),t[e.BinaryFrame]=b(m.binaryMessage),t[e.ConnectionCloseFrame]=b(m.connectionCloseMessage),t[e.PingFrame]=b(m.pingMessage),t[e.PongFrame]=b(m.pongMessage),t}(),y=[{name:"all",label:b(m.all),title:void 0},{name:"send",label:b(m.send),title:void 0},{name:"receive",label:b(m.receive),title:void 0}];class T extends c.SortableDataGrid.SortableDataGridNode{url;frame;isTextFrame;dataTextInternal;binaryViewInternal;constructor(e,t){let r=String(t.text.length);const i=new Date(1e3*t.time),n=("0"+i.getHours()).substr(-2)+":"+("0"+i.getMinutes()).substr(-2)+":"+("0"+i.getSeconds()).substr(-2)+"."+("00"+i.getMilliseconds()).substr(-3),o=document.createElement("div");u.UIUtils.createTextChild(o,n),u.Tooltip.Tooltip.install(o,i.toLocaleString());let s=t.text,d=k.opCodeDescription(t.opCode,t.mask);const c=t.opCode===v.TextFrame;t.type===l.NetworkRequest.WebSocketFrameType.Error?(d=s,r=w(m.na)):c?d=s:t.opCode===v.BinaryFrame?(r=a.NumberUtilities.bytesToString(a.StringUtilities.base64ToSize(t.text)),d=S[t.opCode]()):s=d,super({data:d,length:r,time:o}),this.url=e,this.frame=t,this.isTextFrame=c,this.dataTextInternal=s,this.binaryViewInternal=null}createCells(e){e.classList.toggle("websocket-frame-view-row-error",this.frame.type===l.NetworkRequest.WebSocketFrameType.Error),e.classList.toggle("websocket-frame-view-row-send",this.frame.type===l.NetworkRequest.WebSocketFrameType.Send),e.classList.toggle("websocket-frame-view-row-receive",this.frame.type===l.NetworkRequest.WebSocketFrameType.Receive),super.createCells(e)}nodeSelfHeight(){return 21}dataText(){return this.dataTextInternal}opCode(){return this.frame.opCode}binaryView(){return this.isTextFrame||this.frame.type===l.NetworkRequest.WebSocketFrameType.Error?null:(this.binaryViewInternal||this.dataTextInternal.length>0&&(this.binaryViewInternal=new(0,p.BinaryResourceView)(this.dataTextInternal,a.DevToolsPath.EmptyUrlString,n.ResourceType.resourceTypes.WebSocket)),this.binaryViewInternal)}}function R(e,t){return e.frame.time-t.frame.time}const x=new WeakMap})),r.register("gRynw",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync('/*\n * Copyright (c) 2014 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.websocket-frame-view {\n  --override-icon-color-when-row-selected: #fff;\n  --override-send-row-background-color: rgb(226 247 218);\n  --override-error-row-background-color: rgb(255 237 237);\n\n  user-select: text;\n}\n\n.-theme-with-dark-background .websocket-frame-view,\n:host-context(.-theme-with-dark-background) .websocket-frame-view {\n  --override-icon-color-when-row-selected: #000;\n  --override-send-row-background-color: rgb(16 37 8);\n  --override-error-row-background-color: rgb(18 0 0);\n}\n\n.websocket-frame-view .data-grid {\n  flex: auto;\n  border: none;\n}\n\n.websocket-frame-view .data-grid .data {\n  background-image: none;\n}\n\n.websocket-frame-view-td {\n  border-bottom: 1px solid var(--color-details-hairline);\n}\n\n.websocket-frame-view .data-grid td,\n.websocket-frame-view .data-grid th {\n  border-left-color: var(--color-details-hairline);\n}\n\n.websocket-frame-view-row-send td:first-child::before {\n  content: "\\2B06";\n  color: var(--color-green);\n  padding-right: 4px;\n}\n\n.websocket-frame-view-row-receive td:first-child::before {\n  content: "\\2B07";\n  color: var(--color-red);\n  padding-right: 4px;\n}\n\n.data-grid:focus .websocket-frame-view-row-send.selected td:first-child::before,\n.data-grid:focus .websocket-frame-view-row-receive.selected td:first-child::before {\n  color: var(--override-icon-color-when-row-selected);\n}\n\n.websocket-frame-view-row-send {\n  background-color: var(--override-send-row-background-color);\n}\n\n.websocket-frame-view-row-error {\n  background-color: var(--override-error-row-background-color);\n  color: var(--color-accent-red);\n}\n\n.websocket-frame-view .toolbar {\n  border-bottom: var(--legacy-divider-border);\n}\n\n/*# sourceURL=webSocketFrameView.css */\n');var n=i})),r.register("h5pc5",(function(t,i){e(t.exports,"NetworkLogView",(()=>q)),e(t.exports,"HTTPSchemas",(()=>N)),e(t.exports,"computeStackTraceText",(()=>L)),e(t.exports,"isRequestFilteredOut",(()=>A));var n=r("koSS8"),o=r("e7bLS"),s=r("ixFnt"),a=r("lz7WY"),l=r("eQFvP"),d=r("fMswD"),c=r("8lVqV"),h=r("fm4u4"),u=r("g4rSN"),p=r("7f6zc"),g=r("hE0P3"),m=r("cObcK"),f=r("af4Nd"),w=r("a3yig"),b=r("9z2ZV"),k=r("5887T"),v=r("fPfDn"),C=r("9kyN0"),S=r("k5iKk"),y=r("dRcPb"),T=r("kBxCb");const R={invertFilter:"Invert",invertsFilter:"Inverts the search filter",hideDataUrls:"Hide data URLs",hidesDataAndBlobUrls:"Hides data: and blob: URLs",resourceTypesToInclude:"Resource types to include",hasBlockedCookies:"Has blocked cookies",onlyShowRequestsWithBlocked:"Only show requests with blocked response cookies",blockedRequests:"Blocked Requests",onlyShowBlockedRequests:"Only show blocked requests",thirdParty:"3rd-party requests",onlyShowThirdPartyRequests:"Shows only requests with origin different from page origin",dropHarFilesHere:"Drop HAR files here",recordingNetworkActivity:"Recording network activity…",performARequestOrHitSToRecordThe:"Perform a request or hit {PH1} to record the reload.",recordToDisplayNetworkActivity:"Record network log ({PH1}) to display network activity.",learnMore:"Learn more",networkDataAvailable:"Network Data Available",sSRequests:"{PH1} / {PH2} requests",sSTransferred:"{PH1} / {PH2} transferred",sBSBTransferredOverNetwork:"{PH1} B / {PH2} B transferred over network",sSResources:"{PH1} / {PH2} resources",sBSBResourcesLoadedByThePage:"{PH1} B / {PH2} B resources loaded by the page",sRequests:"{PH1} requests",sTransferred:"{PH1} transferred",sBTransferredOverNetwork:"{PH1} B transferred over network",sResources:"{PH1} resources",sBResourcesLoadedByThePage:"{PH1} B resources loaded by the page",finishS:"Finish: {PH1}",domcontentloadedS:"DOMContentLoaded: {PH1}",loadS:"Load: {PH1}",copy:"Copy",copyRequestHeaders:"Copy request headers",copyResponseHeaders:"Copy response headers",copyResponse:"Copy response",copyStacktrace:"Copy stack trace",copyAsPowershell:"Copy as `PowerShell`",copyAsFetch:"Copy as `fetch`",copyAsNodejsFetch:"Copy as `Node.js` `fetch`",copyAsCurlCmd:"Copy as `cURL` (`cmd`)",copyAsCurlBash:"Copy as `cURL` (`bash`)",copyAllAsPowershell:"Copy all as `PowerShell`",copyAllAsFetch:"Copy all as `fetch`",copyAllAsNodejsFetch:"Copy all as `Node.js` `fetch`",copyAllAsCurlCmd:"Copy all as `cURL` (`cmd`)",copyAllAsCurlBash:"Copy all as `cURL` (`bash`)",copyAsCurl:"Copy as `cURL`",copyAllAsCurl:"Copy all as `cURL`",copyAllAsHar:"Copy all as `HAR`",saveAllAsHarWithContent:"Save all as `HAR` with content",clearBrowserCache:"Clear browser cache",clearBrowserCookies:"Clear browser cookies",blockRequestUrl:"Block request URL",unblockS:"Unblock {PH1}",blockRequestDomain:"Block request domain",replayXhr:"Replay XHR",areYouSureYouWantToClearBrowser:"Are you sure you want to clear browser cache?",areYouSureYouWantToClearBrowserCookies:"Are you sure you want to clear browser cookies?"},x=s.i18n.registerUIStrings("panels/network/NetworkLogView.ts",R),I=s.i18n.getLocalizedString.bind(void 0,x);class q extends(n.ObjectWrapper.eventMixin(b.Widget.VBox)){networkInvertFilterSetting;networkHideDataURLSetting;networkShowIssuesOnlySetting;networkOnlyBlockedRequestsSetting;networkOnlyThirdPartySetting;networkResourceTypeFiltersSetting;rawRowHeight;progressBarContainer;networkLogLargeRowsSetting;rowHeightInternal;timeCalculatorInternal;durationCalculator;calculatorInternal;columns;staleRequests;mainRequestLoadTime;mainRequestDOMContentLoadedTime;filters;timeFilter;hoveredNodeInternal;recordingHint;refreshRequestId;highlightedNode;linkifierInternal;recording;needsRefresh;headerHeightInternal;groupLookups;activeGroupLookup;textFilterUI;invertFilterUI;dataURLFilterUI;resourceCategoryFilterUI;onlyIssuesFilterUI;onlyBlockedRequestsUI;onlyThirdPartyFilterUI;filterParser;suggestionBuilder;dataGrid;summaryToolbar;filterBar;textFilterSetting;constructor(e,t,r){function i(){this.rawRowHeight=Boolean(this.networkLogLargeRowsSetting.get())?41:21,this.rowHeightInternal=this.computeRowHeight()}super(),this.setMinimumSize(50,64),this.element.id="network-container",this.element.classList.add("no-node-selected"),this.networkInvertFilterSetting=n.Settings.Settings.instance().createSetting("networkInvertFilter",!1),this.networkHideDataURLSetting=n.Settings.Settings.instance().createSetting("networkHideDataURL",!1),this.networkShowIssuesOnlySetting=n.Settings.Settings.instance().createSetting("networkShowIssuesOnly",!1),this.networkOnlyBlockedRequestsSetting=n.Settings.Settings.instance().createSetting("networkOnlyBlockedRequests",!1),this.networkOnlyThirdPartySetting=n.Settings.Settings.instance().createSetting("networkOnlyThirdPartySetting",!1),this.networkResourceTypeFiltersSetting=n.Settings.Settings.instance().createSetting("networkResourceTypeFilters",{}),this.rawRowHeight=0,this.progressBarContainer=t,this.networkLogLargeRowsSetting=r,this.networkLogLargeRowsSetting.addChangeListener(i.bind(this),this),this.rawRowHeight=0,this.rowHeightInternal=0,i.call(this),this.timeCalculatorInternal=new(0,T.NetworkTransferTimeCalculator),this.durationCalculator=new(0,T.NetworkTransferDurationCalculator),this.calculatorInternal=this.timeCalculatorInternal,this.columns=new(0,y.NetworkLogViewColumns)(this,this.timeCalculatorInternal,this.durationCalculator,r),this.columns.show(this.element),this.staleRequests=new Set,this.mainRequestLoadTime=-1,this.mainRequestDOMContentLoadedTime=-1,this.filters=[],this.timeFilter=null,this.hoveredNodeInternal=null,this.recordingHint=null,this.refreshRequestId=null,this.highlightedNode=null,this.linkifierInternal=new w.Linkifier.Linkifier,this.recording=!1,this.needsRefresh=!1,this.headerHeightInternal=0,this.groupLookups=new Map,this.groupLookups.set("Frame",new(0,S.NetworkFrameGrouper)(this)),this.activeGroupLookup=null,this.textFilterUI=new b.FilterBar.TextFilterUI,this.textFilterUI.addEventListener("FilterChanged",this.filterChanged,this),e.addFilter(this.textFilterUI),this.invertFilterUI=new b.FilterBar.CheckboxFilterUI("invert-filter",I(R.invertFilter),!0,this.networkInvertFilterSetting),this.invertFilterUI.addEventListener("FilterChanged",this.filterChanged.bind(this),this),b.Tooltip.Tooltip.install(this.invertFilterUI.element(),I(R.invertsFilter)),e.addFilter(this.invertFilterUI),this.dataURLFilterUI=new b.FilterBar.CheckboxFilterUI("hide-data-url",I(R.hideDataUrls),!0,this.networkHideDataURLSetting),this.dataURLFilterUI.addEventListener("FilterChanged",this.filterChanged.bind(this),this),b.Tooltip.Tooltip.install(this.dataURLFilterUI.element(),I(R.hidesDataAndBlobUrls)),e.addFilter(this.dataURLFilterUI);const o=Object.values(n.ResourceType.resourceCategories).map((e=>({name:e.title(),label:()=>e.shortTitle(),title:e.title()})));this.resourceCategoryFilterUI=new b.FilterBar.NamedBitSetFilterUI(o,this.networkResourceTypeFiltersSetting),b.ARIAUtils.setAccessibleName(this.resourceCategoryFilterUI.element(),I(R.resourceTypesToInclude)),this.resourceCategoryFilterUI.addEventListener("FilterChanged",this.filterChanged.bind(this),this),e.addFilter(this.resourceCategoryFilterUI),this.onlyIssuesFilterUI=new b.FilterBar.CheckboxFilterUI("only-show-issues",I(R.hasBlockedCookies),!0,this.networkShowIssuesOnlySetting),this.onlyIssuesFilterUI.addEventListener("FilterChanged",this.filterChanged.bind(this),this),b.Tooltip.Tooltip.install(this.onlyIssuesFilterUI.element(),I(R.onlyShowRequestsWithBlocked)),e.addFilter(this.onlyIssuesFilterUI),this.onlyBlockedRequestsUI=new b.FilterBar.CheckboxFilterUI("only-show-blocked-requests",I(R.blockedRequests),!0,this.networkOnlyBlockedRequestsSetting),this.onlyBlockedRequestsUI.addEventListener("FilterChanged",this.filterChanged.bind(this),this),b.Tooltip.Tooltip.install(this.onlyBlockedRequestsUI.element(),I(R.onlyShowBlockedRequests)),e.addFilter(this.onlyBlockedRequestsUI),this.onlyThirdPartyFilterUI=new b.FilterBar.CheckboxFilterUI("only-show-third-party",I(R.thirdParty),!0,this.networkOnlyThirdPartySetting),this.onlyThirdPartyFilterUI.addEventListener("FilterChanged",this.filterChanged.bind(this),this),b.Tooltip.Tooltip.install(this.onlyThirdPartyFilterUI.element(),I(R.onlyShowThirdPartyRequests)),e.addFilter(this.onlyThirdPartyFilterUI),this.filterParser=new p.TextUtils.FilterParser(P),this.suggestionBuilder=new b.FilterSuggestionBuilder.FilterSuggestionBuilder(P,q.sortSearchValues),this.resetSuggestionBuilder(),this.dataGrid=this.columns.dataGrid(),this.setupDataGrid(),this.columns.sortByCurrentColumn(),e.filterButton().addEventListener(b.Toolbar.ToolbarButton.Events.Click,this.dataGrid.scheduleUpdate.bind(this.dataGrid,!0)),this.summaryToolbar=new b.Toolbar.Toolbar("network-summary-bar",this.element),this.summaryToolbar.element.setAttribute("role","status"),new b.DropTarget.DropTarget(this.element,[b.DropTarget.Type.File],I(R.dropHarFilesHere),this.handleDrop.bind(this)),n.Settings.Settings.instance().moduleSetting("networkColorCodeResourceTypes").addChangeListener(this.invalidateAllItems.bind(this,!1),this),l.TargetManager.TargetManager.instance().observeModels(l.NetworkManager.NetworkManager,this),u.NetworkLog.NetworkLog.instance().addEventListener(u.NetworkLog.Events.RequestAdded,this.onRequestUpdated,this),u.NetworkLog.NetworkLog.instance().addEventListener(u.NetworkLog.Events.RequestUpdated,this.onRequestUpdated,this),u.NetworkLog.NetworkLog.instance().addEventListener(u.NetworkLog.Events.Reset,this.reset,this),this.updateGroupByFrame(),n.Settings.Settings.instance().moduleSetting("network.group-by-frame").addChangeListener((()=>this.updateGroupByFrame())),this.filterBar=e,this.textFilterSetting=n.Settings.Settings.instance().createSetting("networkTextFilter",""),this.textFilterSetting.get()&&this.textFilterUI.setValue(this.textFilterSetting.get())}updateGroupByFrame(){const e=n.Settings.Settings.instance().moduleSetting("network.group-by-frame").get();this.setGrouping(e?"Frame":null)}static sortSearchValues(e,t){e===g.UIFilter.FilterType.Priority?t.sort(((e,t)=>{const r=f.NetworkPriorities.uiLabelToNetworkPriority(e),i=f.NetworkPriorities.uiLabelToNetworkPriority(t);return f.NetworkPriorities.networkPriorityWeight(r)-f.NetworkPriorities.networkPriorityWeight(i)})):t.sort()}static negativeFilter(e,t){return!e(t)}static requestPathFilter(e,t){return!!e&&e.test(t.path()+"/"+t.name())}static subdomains(e){const t=[e];let r=e.indexOf(".");for(;-1!==r;)t.push("*"+e.substring(r)),r=e.indexOf(".",r+1);return t}static createRequestDomainFilter(e){const t=e.split("*").map(a.StringUtilities.escapeForRegExp).join(".*");return q.requestDomainFilter.bind(null,new RegExp("^"+t+"$","i"))}static requestDomainFilter(e,t){return e.test(t.domain)}static runningRequestFilter(e){return!e.finished}static fromCacheRequestFilter(e){return e.cached()}static interceptedByServiceWorkerFilter(e){return e.fetchedViaServiceWorker}static initiatedByServiceWorkerFilter(e){return e.initiatedByServiceWorker()}static requestResponseHeaderFilter(e,t){return void 0!==t.responseHeaderValue(e)}static requestResponseHeaderSetCookieFilter(e,t){return Boolean(t.responseHeaderValue("Set-Cookie")?.includes(e))}static requestMethodFilter(e,t){return t.requestMethod===e}static requestPriorityFilter(e,t){return t.priority()===e}static requestMimeTypeFilter(e,t){return t.mimeType===e}static requestMixedContentFilter(e,t){return e===g.UIFilter.MixedContentFilterValues.Displayed?"optionally-blockable"===t.mixedContentType:e===g.UIFilter.MixedContentFilterValues.Blocked?"blockable"===t.mixedContentType&&t.wasBlocked():e===g.UIFilter.MixedContentFilterValues.BlockOverridden?"blockable"===t.mixedContentType&&!t.wasBlocked():e===g.UIFilter.MixedContentFilterValues.All&&"none"!==t.mixedContentType}static requestSchemeFilter(e,t){return t.scheme===e}static requestCookieDomainFilter(e,t){return t.allCookiesIncludingBlockedOnes().some((t=>t.domain()===e))}static requestCookieNameFilter(e,t){return t.allCookiesIncludingBlockedOnes().some((t=>t.name()===e))}static requestCookiePathFilter(e,t){return t.allCookiesIncludingBlockedOnes().some((t=>t.path()===e))}static requestCookieValueFilter(e,t){return t.allCookiesIncludingBlockedOnes().some((t=>t.value()===e))}static requestSetCookieDomainFilter(e,t){return t.responseCookies.some((t=>t.domain()===e))}static requestSetCookieNameFilter(e,t){return t.responseCookies.some((t=>t.name()===e))}static requestSetCookieValueFilter(e,t){return t.responseCookies.some((t=>t.value()===e))}static requestSizeLargerThanFilter(e,t){return t.transferSize>=e}static statusCodeFilter(e,t){return String(t.statusCode)===e}static getHTTPRequestsFilter(e){return e.parsedURL.isValid&&e.scheme in N}static resourceTypeFilter(e,t){return t.resourceType().name()===e}static requestUrlFilter(e,t){return new RegExp(a.StringUtilities.escapeForRegExp(e),"i").test(t.url())}static requestTimeFilter(e,t,r){return!(r.issueTime()>t)&&!(-1!==r.endTime&&r.endTime<e)}static copyRequestHeaders(e){o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e.requestHeadersText())}static copyResponseHeaders(e){o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e.responseHeadersText)}static async copyResponse(e){const t=await e.contentData();let r=t.content||"";e.contentType().isTextType()?t.encoded&&r&&(r=window.atob(r)):r=p.ContentProvider.contentAsDataURL(r,e.mimeType,t.encoded),o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(r)}handleDrop(e){const t=e.items;if(!t.length)return;const r=t[0].getAsFile();r&&this.onLoadFromFile(r)}async onLoadFromFile(e){const t=new n.StringOutputStream.StringOutputStream,r=new d.FileUtils.ChunkedFileReader(e,1e7);if(!await r.read(t)){const e=r.error();return void(e&&this.harLoadFailed(e.message))}let i;try{i=new c.HARFormat.HARRoot(JSON.parse(t.data()))}catch(e){return void this.harLoadFailed(e)}u.NetworkLog.NetworkLog.instance().importRequests(c.Importer.Importer.requestsFromHARLog(i.log))}harLoadFailed(e){n.Console.Console.instance().error("Failed to load HAR file with following error: "+e)}setGrouping(e){this.activeGroupLookup&&this.activeGroupLookup.reset();const t=e&&this.groupLookups.get(e)||null;this.activeGroupLookup=t,this.invalidateAllItems()}computeRowHeight(){return Math.round(this.rawRowHeight*window.devicePixelRatio)/window.devicePixelRatio}nodeForRequest(e){return E.get(e)||null}headerHeight(){return this.headerHeightInternal}setRecording(e){this.recording=e,this.updateSummaryBar()}modelAdded(e){if(e.target().parentTarget())return;const t=e.target().model(l.ResourceTreeModel.ResourceTreeModel);t&&(t.addEventListener(l.ResourceTreeModel.Events.Load,this.loadEventFired,this),t.addEventListener(l.ResourceTreeModel.Events.DOMContentLoaded,this.domContentLoadedEventFired,this))}modelRemoved(e){if(!e.target().parentTarget()){const t=e.target().model(l.ResourceTreeModel.ResourceTreeModel);t&&(t.removeEventListener(l.ResourceTreeModel.Events.Load,this.loadEventFired,this),t.removeEventListener(l.ResourceTreeModel.Events.DOMContentLoaded,this.domContentLoadedEventFired,this))}}linkifier(){return this.linkifierInternal}setWindow(e,t){e||t?(this.timeFilter=q.requestTimeFilter.bind(null,e,t),this.timeCalculatorInternal.setWindow(new(0,T.NetworkTimeBoundary)(e,t))):(this.timeFilter=null,this.timeCalculatorInternal.setWindow(null)),this.filterRequests()}resetFocus(){this.dataGrid.element.focus()}resetSuggestionBuilder(){this.suggestionBuilder.clear(),this.suggestionBuilder.addItem(g.UIFilter.FilterType.Is,g.UIFilter.IsFilterType.Running),this.suggestionBuilder.addItem(g.UIFilter.FilterType.Is,g.UIFilter.IsFilterType.FromCache),this.suggestionBuilder.addItem(g.UIFilter.FilterType.Is,g.UIFilter.IsFilterType.ServiceWorkerIntercepted),this.suggestionBuilder.addItem(g.UIFilter.FilterType.Is,g.UIFilter.IsFilterType.ServiceWorkerInitiated),this.suggestionBuilder.addItem(g.UIFilter.FilterType.LargerThan,"100"),this.suggestionBuilder.addItem(g.UIFilter.FilterType.LargerThan,"10k"),this.suggestionBuilder.addItem(g.UIFilter.FilterType.LargerThan,"1M"),this.textFilterUI.setSuggestionProvider(this.suggestionBuilder.completions.bind(this.suggestionBuilder))}filterChanged(){this.removeAllNodeHighlights(),this.parseFilterQuery(this.textFilterUI.value(),this.invertFilterUI.checked()),this.filterRequests(),this.textFilterSetting.set(this.textFilterUI.value())}async resetFilter(){this.textFilterUI.clear()}showRecordingHint(){this.hideRecordingHint(),this.recordingHint=this.element.createChild("div","network-status-pane fill");const e=this.recordingHint.createChild("div","recording-hint");if(this.recording){let t=null;const r=b.ShortcutRegistry.ShortcutRegistry.instance().shortcutsForAction("inspector_main.reload")[0];r&&(t=this.recordingHint.createChild("b"),t.textContent=r.title());e.createChild("span").textContent=I(R.recordingNetworkActivity),t&&(e.createChild("br"),e.appendChild(s.i18n.getFormatLocalizedString(x,R.performARequestOrHitSToRecordThe,{PH1:t})))}else{const t=e.createChild("b");t.textContent=b.ShortcutRegistry.ShortcutRegistry.instance().shortcutTitleForAction("network.toggle-recording")||"",e.appendChild(s.i18n.getFormatLocalizedString(x,R.recordToDisplayNetworkActivity,{PH1:t}))}e.createChild("br"),e.appendChild(b.XLink.XLink.create("https://developer.chrome.com/docs/devtools/network/?utm_source=devtools&utm_campaign=2019Q1",I(R.learnMore))),this.setHidden(!0)}hideRecordingHint(){this.setHidden(!1),this.recordingHint&&this.recordingHint.remove(),b.ARIAUtils.alert(I(R.networkDataAvailable)),this.recordingHint=null}setHidden(e){this.columns.setHidden(e),b.ARIAUtils.setHidden(this.summaryToolbar.element,e)}elementsToRestoreScrollPositionsFor(){return this.dataGrid?[this.dataGrid.scrollContainer]:[]}columnExtensionResolved(){this.invalidateAllItems(!0)}setupDataGrid(){return this.dataGrid.setRowContextMenuCallback(((e,t)=>{const r=t.request();r&&this.handleContextMenuForRequest(e,r)})),this.dataGrid.setStickToBottom(!0),this.dataGrid.setName("networkLog"),this.dataGrid.setResizeMethod(m.DataGrid.ResizeMethod.Last),this.dataGrid.element.classList.add("network-log-grid"),this.dataGrid.element.addEventListener("mousedown",this.dataGridMouseDown.bind(this),!0),this.dataGrid.element.addEventListener("mousemove",this.dataGridMouseMove.bind(this),!0),this.dataGrid.element.addEventListener("mouseleave",(()=>this.setHoveredNode(null)),!0),this.dataGrid.element.addEventListener("keydown",(e=>{if("ArrowRight"===e.key&&this.dataGrid.selectedNode){const e=this.dataGrid.selectedNode.element().querySelector("span.devtools-link");e&&e.focus()}isEnterOrSpaceKey(e)&&(this.dispatchEventToListeners(C.Events.RequestActivated,{showPanel:!0,takeFocus:!0}),e.consume(!0))})),this.dataGrid.element.addEventListener("keyup",(e=>{if(("r"===e.key||"R"===e.key)&&this.dataGrid.selectedNode){const e=this.dataGrid.selectedNode.request();if(!e)return;l.NetworkManager.NetworkManager.canReplayRequest(e)&&l.NetworkManager.NetworkManager.replayRequest(e)}})),this.dataGrid.element.addEventListener("focus",this.onDataGridFocus.bind(this),!0),this.dataGrid.element.addEventListener("blur",this.onDataGridBlur.bind(this),!0),this.dataGrid}dataGridMouseMove(e){const t=e,r=this.dataGrid.dataGridNodeFromNode(t.target),i=t.shiftKey;this.setHoveredNode(r,i)}hoveredNode(){return this.hoveredNodeInternal}setHoveredNode(e,t){this.hoveredNodeInternal&&this.hoveredNodeInternal.setHovered(!1,!1),this.hoveredNodeInternal=e,this.hoveredNodeInternal&&this.hoveredNodeInternal.setHovered(!0,Boolean(t))}dataGridMouseDown(e){const t=e;!this.dataGrid.selectedNode&&t.button&&t.consume()}updateSummaryBar(){this.hideRecordingHint();let e=0,t=0,r=0,i=0,o=0,d=-1,c=-1,h=0;for(const s of u.NetworkLog.NetworkLog.instance().requests()){const a=E.get(s);if(!a)continue;h++;const u=s.transferSize;e+=u;const p=s.resourceSize;t+=p,F.has(a)||(r++,i+=u,o+=p);const g=l.NetworkManager.NetworkManager.forRequest(s);g&&s.url()===g.target().inspectedURL()&&s.resourceType()===n.ResourceType.resourceTypes.Document&&!g.target().parentTarget()&&(d=s.startTime),s.endTime>c&&(c=s.endTime)}if(!h)return void this.showRecordingHint();this.summaryToolbar.removeToolbarItems();const p=(e,t)=>{const r=new b.Toolbar.ToolbarText(e);return r.setTitle(t||e),this.summaryToolbar.appendToolbarItem(r),r.element};if(r!==h?(p(I(R.sSRequests,{PH1:r,PH2:h})),this.summaryToolbar.appendSeparator(),p(I(R.sSTransferred,{PH1:a.NumberUtilities.bytesToString(i),PH2:a.NumberUtilities.bytesToString(e)}),I(R.sBSBTransferredOverNetwork,{PH1:i,PH2:e})),this.summaryToolbar.appendSeparator(),p(I(R.sSResources,{PH1:a.NumberUtilities.bytesToString(o),PH2:a.NumberUtilities.bytesToString(t)}),I(R.sBSBResourcesLoadedByThePage,{PH1:o,PH2:t}))):(p(I(R.sRequests,{PH1:h})),this.summaryToolbar.appendSeparator(),p(I(R.sTransferred,{PH1:a.NumberUtilities.bytesToString(e)}),I(R.sBTransferredOverNetwork,{PH1:e})),this.summaryToolbar.appendSeparator(),p(I(R.sResources,{PH1:a.NumberUtilities.bytesToString(t)}),I(R.sBResourcesLoadedByThePage,{PH1:t}))),-1!==d&&-1!==c){if(this.summaryToolbar.appendSeparator(),p(I(R.finishS,{PH1:s.TimeUtilities.secondsToString(c-d)})),-1!==this.mainRequestDOMContentLoadedTime&&this.mainRequestDOMContentLoadedTime>d){this.summaryToolbar.appendSeparator();p(I(R.domcontentloadedS,{PH1:s.TimeUtilities.secondsToString(this.mainRequestDOMContentLoadedTime-d)})).style.color=q.getDCLEventColor()}if(-1!==this.mainRequestLoadTime){this.summaryToolbar.appendSeparator();p(I(R.loadS,{PH1:s.TimeUtilities.secondsToString(this.mainRequestLoadTime-d)})).style.color=q.getLoadEventColor()}}}scheduleRefresh(){this.needsRefresh||(this.needsRefresh=!0,this.isShowing()&&!this.refreshRequestId&&(this.refreshRequestId=this.element.window().requestAnimationFrame(this.refresh.bind(this))))}addFilmStripFrames(e){this.columns.addEventDividers(e,"network-frame-divider")}selectFilmStripFrame(e){this.columns.selectFilmStripFrame(e)}clearFilmStripFrame(){this.columns.clearFilmStripFrame()}refreshIfNeeded(){this.needsRefresh&&this.refresh()}invalidateAllItems(e){this.staleRequests=new Set(u.NetworkLog.NetworkLog.instance().requests()),e?this.scheduleRefresh():this.refresh()}timeCalculator(){return this.timeCalculatorInternal}calculator(){return this.calculatorInternal}setCalculator(e){e&&this.calculatorInternal!==e&&(this.calculatorInternal!==e&&(this.calculatorInternal=e,this.columns.setCalculator(this.calculatorInternal)),this.calculatorInternal.reset(),this.calculatorInternal.startAtZero?this.columns.hideEventDividers():this.columns.showEventDividers(),this.invalidateAllItems())}loadEventFired(e){if(!this.recording)return;const t=e.data.loadTime;t&&(this.mainRequestLoadTime=t,this.columns.addEventDividers([t],"network-load-divider"))}domContentLoadedEventFired(e){if(!this.recording)return;const{data:t}=e;t&&(this.mainRequestDOMContentLoadedTime=t,this.columns.addEventDividers([t],"network-dcl-divider"))}wasShown(){this.refreshIfNeeded(),this.registerCSSFiles([v.default]),this.columns.wasShown()}willHide(){this.columns.willHide()}onResize(){this.rowHeightInternal=this.computeRowHeight()}flatNodesList(){return this.dataGrid.rootNode().flatChildren()}onDataGridFocus(){this.dataGrid.element.matches(":focus-visible")&&this.element.classList.add("grid-focused"),this.updateNodeBackground()}onDataGridBlur(){this.element.classList.remove("grid-focused"),this.updateNodeBackground()}updateNodeBackground(){this.dataGrid.selectedNode&&this.dataGrid.selectedNode.updateBackgroundColor()}updateNodeSelectedClass(e){e?this.element.classList.remove("no-node-selected"):this.element.classList.add("no-node-selected")}stylesChanged(){this.columns.scheduleRefresh()}refresh(){this.needsRefresh=!1,this.refreshRequestId&&(this.element.window().cancelAnimationFrame(this.refreshRequestId),this.refreshRequestId=null),this.removeAllNodeHighlights(),this.timeCalculatorInternal.updateBoundariesForEventTime(this.mainRequestLoadTime),this.durationCalculator.updateBoundariesForEventTime(this.mainRequestLoadTime),this.timeCalculatorInternal.updateBoundariesForEventTime(this.mainRequestDOMContentLoadedTime),this.durationCalculator.updateBoundariesForEventTime(this.mainRequestDOMContentLoadedTime);const e=new Map,t=[],r=new Set;for(;this.staleRequests.size;){const e=this.staleRequests.values().next().value;this.staleRequests.delete(e);let t=E.get(e);t||(t=this.createNodeForRequest(e)),r.add(t)}for(const i of r){const r=!this.applyFilter(i);r&&i===this.hoveredNodeInternal&&this.setHoveredNode(null),r||t.push(i);const n=i.request();this.timeCalculatorInternal.updateBoundaries(n),this.durationCalculator.updateBoundaries(n);const o=this.parentNodeForInsert(i);if(F.has(i)===r&&i.parent===o)continue;r?F.add(i):F.delete(i);if(i.parent&&(r||i.parent!==o)){let e=i.parent;if(!e)continue;for(e.removeChild(i);e&&!e.hasChildren()&&e.dataGrid&&e.dataGrid.rootNode()!==e;){const t=e.parent;t.removeChild(e),e=t}}o&&!r&&(o.dataGrid||e.has(o)||(e.set(o,this.dataGrid.rootNode()),t.push(o)),e.set(i,o))}for(const t of e.keys())e.get(t).appendChild(t);for(const e of t)e.refresh();this.updateSummaryBar(),e.size&&this.columns.sortByCurrentColumn(),this.dataGrid.updateInstantly(),this.didRefreshForTest()}didRefreshForTest(){}parentNodeForInsert(e){if(!this.activeGroupLookup)return this.dataGrid.rootNode();const t=this.activeGroupLookup.groupNodeForRequest(e.request());return t||this.dataGrid.rootNode()}reset(){this.dispatchEventToListeners(C.Events.RequestActivated,{showPanel:!1}),this.setHoveredNode(null),this.columns.reset(),this.timeFilter=null,this.calculatorInternal.reset(),this.timeCalculatorInternal.setWindow(null),this.linkifierInternal.reset(),this.activeGroupLookup&&this.activeGroupLookup.reset(),this.staleRequests.clear(),this.resetSuggestionBuilder(),this.mainRequestLoadTime=-1,this.mainRequestDOMContentLoadedTime=-1,this.dataGrid.rootNode().removeChildren(),this.updateSummaryBar(),this.dataGrid.setStickToBottom(!0),this.scheduleRefresh()}setTextFilterValue(e){this.textFilterUI.setValue(e),this.dataURLFilterUI.setChecked(!1),this.onlyIssuesFilterUI.setChecked(!1),this.onlyBlockedRequestsUI.setChecked(!1),this.resourceCategoryFilterUI.reset()}createNodeForRequest(e){const t=new(0,C.NetworkRequestNode)(this,e);E.set(e,t),F.add(t);for(let t=e.redirectSource();t;t=t.redirectSource())this.refreshRequest(t);return t}onRequestUpdated(e){const t=e.data;this.refreshRequest(t)}refreshRequest(e){q.subdomains(e.domain).forEach(this.suggestionBuilder.addItem.bind(this.suggestionBuilder,g.UIFilter.FilterType.Domain)),this.suggestionBuilder.addItem(g.UIFilter.FilterType.Method,e.requestMethod),this.suggestionBuilder.addItem(g.UIFilter.FilterType.MimeType,e.mimeType),this.suggestionBuilder.addItem(g.UIFilter.FilterType.Scheme,String(e.scheme)),this.suggestionBuilder.addItem(g.UIFilter.FilterType.StatusCode,String(e.statusCode)),this.suggestionBuilder.addItem(g.UIFilter.FilterType.ResourceType,e.resourceType().name()),this.suggestionBuilder.addItem(g.UIFilter.FilterType.Url,e.securityOrigin());const t=e.priority();if(t&&this.suggestionBuilder.addItem(g.UIFilter.FilterType.Priority,f.NetworkPriorities.uiLabelForNetworkPriority(t)),"none"!==e.mixedContentType&&this.suggestionBuilder.addItem(g.UIFilter.FilterType.MixedContent,g.UIFilter.MixedContentFilterValues.All),"optionally-blockable"===e.mixedContentType&&this.suggestionBuilder.addItem(g.UIFilter.FilterType.MixedContent,g.UIFilter.MixedContentFilterValues.Displayed),"blockable"===e.mixedContentType){const t=e.wasBlocked()?g.UIFilter.MixedContentFilterValues.Blocked:g.UIFilter.MixedContentFilterValues.BlockOverridden;this.suggestionBuilder.addItem(g.UIFilter.FilterType.MixedContent,t)}const r=e.responseHeaders;for(const e of r)this.suggestionBuilder.addItem(g.UIFilter.FilterType.HasResponseHeader,e.name),"Set-Cookie"===e.name&&this.suggestionBuilder.addItem(g.UIFilter.FilterType.ResponseHeaderValueSetCookie);for(const t of e.responseCookies)this.suggestionBuilder.addItem(g.UIFilter.FilterType.SetCookieDomain,t.domain()),this.suggestionBuilder.addItem(g.UIFilter.FilterType.SetCookieName,t.name()),this.suggestionBuilder.addItem(g.UIFilter.FilterType.SetCookieValue,t.value());for(const t of e.allCookiesIncludingBlockedOnes())this.suggestionBuilder.addItem(g.UIFilter.FilterType.CookieDomain,t.domain()),this.suggestionBuilder.addItem(g.UIFilter.FilterType.CookieName,t.name()),this.suggestionBuilder.addItem(g.UIFilter.FilterType.CookiePath,t.path()),this.suggestionBuilder.addItem(g.UIFilter.FilterType.CookieValue,t.value());this.staleRequests.add(e),this.scheduleRefresh()}rowHeight(){return this.rowHeightInternal}switchViewMode(e){this.columns.switchViewMode(e)}handleContextMenuForRequest(e,t){e.appendApplicableItems(t);let r=e.clipboardSection().appendSubMenuItem(I(R.copy));const i=r.footerSection();if(t){r.defaultSection().appendItem(b.UIUtils.copyLinkAddressLabel(),o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText.bind(o.InspectorFrontendHost.InspectorFrontendHostInstance,t.contentURL())),t.requestHeadersText()&&r.defaultSection().appendItem(I(R.copyRequestHeaders),q.copyRequestHeaders.bind(null,t)),t.responseHeadersText&&r.defaultSection().appendItem(I(R.copyResponseHeaders),q.copyResponseHeaders.bind(null,t)),t.finished&&r.defaultSection().appendItem(I(R.copyResponse),q.copyResponse.bind(null,t));const n=t.initiator();if(n){const d=n.stack;if(d){const c=L(d);""!==c&&r.defaultSection().appendItem(I(R.copyStacktrace),(()=>{o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(c)}))}}const s=t.isBlobRequest();o.Platform.isWin()?(i.appendItem(I(R.copyAsPowershell),this.copyPowerShellCommand.bind(this,t),s),i.appendItem(I(R.copyAsFetch),this.copyFetchCall.bind(this,t,0),s),i.appendItem(I(R.copyAsNodejsFetch),this.copyFetchCall.bind(this,t,1),s),i.appendItem(I(R.copyAsCurlCmd),this.copyCurlCommand.bind(this,t,"win"),s),i.appendItem(I(R.copyAsCurlBash),this.copyCurlCommand.bind(this,t,"unix"),s),i.appendItem(I(R.copyAllAsPowershell),this.copyAllPowerShellCommand.bind(this)),i.appendItem(I(R.copyAllAsFetch),this.copyAllFetchCall.bind(this,0)),i.appendItem(I(R.copyAllAsNodejsFetch),this.copyAllFetchCall.bind(this,1)),i.appendItem(I(R.copyAllAsCurlCmd),this.copyAllCurlCommand.bind(this,"win")),i.appendItem(I(R.copyAllAsCurlBash),this.copyAllCurlCommand.bind(this,"unix"))):(i.appendItem(I(R.copyAsPowershell),this.copyPowerShellCommand.bind(this,t),s),i.appendItem(I(R.copyAsFetch),this.copyFetchCall.bind(this,t,0),s),i.appendItem(I(R.copyAsNodejsFetch),this.copyFetchCall.bind(this,t,1),s),i.appendItem(I(R.copyAsCurl),this.copyCurlCommand.bind(this,t,"unix"),s),i.appendItem(I(R.copyAllAsPowershell),this.copyAllPowerShellCommand.bind(this)),i.appendItem(I(R.copyAllAsFetch),this.copyAllFetchCall.bind(this,0)),i.appendItem(I(R.copyAllAsNodejsFetch),this.copyAllFetchCall.bind(this,1)),i.appendItem(I(R.copyAllAsCurl),this.copyAllCurlCommand.bind(this,"unix")))}else r=e.clipboardSection().appendSubMenuItem(I(R.copy));if(i.appendItem(I(R.copyAllAsHar),this.copyAll.bind(this)),e.saveSection().appendItem(I(R.saveAllAsHarWithContent),this.exportAll.bind(this)),e.editSection().appendItem(I(R.clearBrowserCache),this.clearBrowserCache.bind(this)),e.editSection().appendItem(I(R.clearBrowserCookies),this.clearBrowserCookies.bind(this)),t){const h=20,u=l.NetworkManager.MultitargetNetworkManager.instance();let p=u.blockedPatterns();function g(e){p.push({enabled:!0,url:e}),u.setBlockedPatterns(p),u.setBlockingEnabled(!0),b.ViewManager.ViewManager.instance().showView("network.blocked-urls")}function m(e){p=p.filter((t=>t.url!==e)),u.setBlockedPatterns(p),b.ViewManager.ViewManager.instance().showView("network.blocked-urls")}const f=t.parsedURL.urlWithoutScheme();if(f&&!p.find((e=>e.url===f)))e.debugSection().appendItem(I(R.blockRequestUrl),g.bind(null,f));else if(f){const k=a.StringUtilities.trimMiddle(f,h);e.debugSection().appendItem(I(R.unblockS,{PH1:k}),m.bind(null,f))}const w=t.parsedURL.domain();if(w&&!p.find((e=>e.url===w)))e.debugSection().appendItem(I(R.blockRequestDomain),g.bind(null,w));else if(w){const v=a.StringUtilities.trimMiddle(w,h);e.debugSection().appendItem(I(R.unblockS,{PH1:v}),m.bind(null,w))}l.NetworkManager.NetworkManager.canReplayRequest(t)&&e.debugSection().appendItem(I(R.replayXhr),l.NetworkManager.NetworkManager.replayRequest.bind(null,t))}}harRequests(){return u.NetworkLog.NetworkLog.instance().requests().filter(q.getHTTPRequestsFilter).filter((e=>e.finished||e.resourceType()===n.ResourceType.resourceTypes.WebSocket&&e.responseReceivedTime))}async copyAll(){const e={log:await c.Log.Log.build(this.harRequests())};o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(JSON.stringify(e,null,2))}async copyCurlCommand(e,t){const r=await this.generateCurlCommand(e,t);o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(r)}async copyAllCurlCommand(e){const t=await this.generateAllCurlCommand(u.NetworkLog.NetworkLog.instance().requests(),e);o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(t)}async copyFetchCall(e,t){const r=await this.generateFetchCall(e,t);o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(r)}async copyAllFetchCall(e){const t=await this.generateAllFetchCall(u.NetworkLog.NetworkLog.instance().requests(),e);o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(t)}async copyPowerShellCommand(e){const t=await this.generatePowerShellCommand(e);o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(t)}async copyAllPowerShellCommand(){const e=await this.generateAllPowerShellCommand(u.NetworkLog.NetworkLog.instance().requests());o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e)}async exportAll(){const e=l.TargetManager.TargetManager.instance().mainTarget();if(!e)return;const t=e.inspectedURL(),r=n.ParsedURL.ParsedURL.fromString(t),i=r?r.host:"network-log",o=new d.FileUtils.FileOutputStream;if(!await o.open(n.ParsedURL.ParsedURL.concatenate(i,".har")))return;const s=new b.ProgressIndicator.ProgressIndicator;this.progressBarContainer.appendChild(s.element),await c.Writer.Writer.write(o,this.harRequests(),s),s.done(),o.close()}clearBrowserCache(){confirm(I(R.areYouSureYouWantToClearBrowser))&&l.NetworkManager.MultitargetNetworkManager.instance().clearBrowserCache()}clearBrowserCookies(){confirm(I(R.areYouSureYouWantToClearBrowserCookies))&&l.NetworkManager.MultitargetNetworkManager.instance().clearBrowserCookies()}removeAllHighlights(){this.removeAllNodeHighlights()}applyFilter(e){const t=e.request();if(this.timeFilter&&!this.timeFilter(t))return!1;const r=t.resourceType().category().title();if(!this.resourceCategoryFilterUI.accept(r))return!1;if(this.dataURLFilterUI.checked()&&(t.parsedURL.isDataURL()||t.parsedURL.isBlobURL()))return!1;if(this.onlyIssuesFilterUI.checked()&&!h.RelatedIssue.hasIssueOfCategory(t,h.Issue.IssueCategory.Cookie))return!1;if(this.onlyBlockedRequestsUI.checked()&&!t.wasBlocked()&&!t.corsErrorStatus())return!1;if(this.onlyThirdPartyFilterUI.checked()&&t.isSameSite())return!1;for(let e=0;e<this.filters.length;++e)if(!this.filters[e](t))return!1;return!0}isValidUrl(e){try{return new URL(e),!0}catch(e){return!1}}parseFilterQuery(e,t){const r=this.filterParser.parse(e);this.filters=r.map((e=>{const r=e.key,i=e.text||"",n=e.regex;let o;if(r){const e=a.StringUtilities.escapeForRegExp(r+":"+i);o=this.createSpecialFilter(r,i)||q.requestPathFilter.bind(null,new RegExp(e,"i"))}else o=e.regex?q.requestPathFilter.bind(null,n):this.isValidUrl(i)?q.requestUrlFilter.bind(null,i):q.requestPathFilter.bind(null,new RegExp(a.StringUtilities.escapeForRegExp(i),"i"));return e.negative&&!t||!e.negative&&t?q.negativeFilter.bind(null,o):o}))}createSpecialFilter(e,t){switch(e){case g.UIFilter.FilterType.Domain:return q.createRequestDomainFilter(t);case g.UIFilter.FilterType.HasResponseHeader:return q.requestResponseHeaderFilter.bind(null,t);case g.UIFilter.FilterType.ResponseHeaderValueSetCookie:return q.requestResponseHeaderSetCookieFilter.bind(null,t);case g.UIFilter.FilterType.Is:if(t.toLowerCase()===g.UIFilter.IsFilterType.Running)return q.runningRequestFilter;if(t.toLowerCase()===g.UIFilter.IsFilterType.FromCache)return q.fromCacheRequestFilter;if(t.toLowerCase()===g.UIFilter.IsFilterType.ServiceWorkerIntercepted)return q.interceptedByServiceWorkerFilter;if(t.toLowerCase()===g.UIFilter.IsFilterType.ServiceWorkerInitiated)return q.initiatedByServiceWorkerFilter;break;case g.UIFilter.FilterType.LargerThan:return this.createSizeFilter(t.toLowerCase());case g.UIFilter.FilterType.Method:return q.requestMethodFilter.bind(null,t);case g.UIFilter.FilterType.MimeType:return q.requestMimeTypeFilter.bind(null,t);case g.UIFilter.FilterType.MixedContent:return q.requestMixedContentFilter.bind(null,t);case g.UIFilter.FilterType.Scheme:return q.requestSchemeFilter.bind(null,t);case g.UIFilter.FilterType.SetCookieDomain:return q.requestSetCookieDomainFilter.bind(null,t);case g.UIFilter.FilterType.SetCookieName:return q.requestSetCookieNameFilter.bind(null,t);case g.UIFilter.FilterType.SetCookieValue:return q.requestSetCookieValueFilter.bind(null,t);case g.UIFilter.FilterType.CookieDomain:return q.requestCookieDomainFilter.bind(null,t);case g.UIFilter.FilterType.CookieName:return q.requestCookieNameFilter.bind(null,t);case g.UIFilter.FilterType.CookiePath:return q.requestCookiePathFilter.bind(null,t);case g.UIFilter.FilterType.CookieValue:return q.requestCookieValueFilter.bind(null,t);case g.UIFilter.FilterType.Priority:return q.requestPriorityFilter.bind(null,f.NetworkPriorities.uiLabelToNetworkPriority(t));case g.UIFilter.FilterType.StatusCode:return q.statusCodeFilter.bind(null,t);case g.UIFilter.FilterType.ResourceType:return q.resourceTypeFilter.bind(null,t);case g.UIFilter.FilterType.Url:return q.requestUrlFilter.bind(null,t)}return null}createSizeFilter(e){let t=1;e.endsWith("k")?(t=1e3,e=e.substring(0,e.length-1)):e.endsWith("m")&&(t=1e6,e=e.substring(0,e.length-1));const r=Number(e);return isNaN(r)?null:q.requestSizeLargerThanFilter.bind(null,r*t)}filterRequests(){this.removeAllHighlights(),this.invalidateAllItems()}reveal(e){this.removeAllNodeHighlights();const t=E.get(e);return t&&t.dataGrid?(t.parent&&t.parent instanceof C.NetworkGroupNode&&(t.parent.reveal(),t.parent.expand()),t.reveal(),t):null}revealAndHighlightRequest(e){const t=this.reveal(e);t&&this.highlightNode(t)}revealAndHighlightRequestWithId(e){const t=u.NetworkLog.NetworkLog.instance().requestByManagerAndId(e.manager,e.requestId);t&&this.revealAndHighlightRequest(t)}selectRequest(e,t){const{clearFilter:r}=t||{clearFilter:!0};r&&this.setTextFilterValue("");const i=this.reveal(e);i&&i.select()}removeAllNodeHighlights(){this.highlightedNode&&(this.highlightedNode.element().classList.remove("highlighted-row"),this.highlightedNode=null)}highlightNode(e){b.UIUtils.runCSSAnimationOnce(e.element(),"highlighted-row"),this.highlightedNode=e}filterOutBlobRequests(e){return e.filter((e=>!e.isBlobRequest()))}async generateFetchCall(e,t){const r=new Set(["method","path","scheme","version","accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","cookie","cookie2","date","dnt","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","via","user-agent"]),i=new Set(["cookie","authorization"]),n=JSON.stringify(e.url()),o=e.requestHeaders(),s=o.reduce(((e,t)=>{const i=t.name;return r.has(i.toLowerCase())||i.includes(":")||e.append(i,t.value),e}),new Headers),a={};for(const e of s)a[e[0]]=e[1];const l=e.includedRequestCookies().length||o.some((({name:e})=>i.has(e.toLowerCase())))?"include":"omit",d=o.find((({name:e})=>"referer"===e.toLowerCase())),c=d?d.value:void 0,h=e.referrerPolicy()||void 0,u=await e.requestFormData(),p={headers:Object.keys(a).length?a:void 0,referrer:c,referrerPolicy:h,body:u,method:e.requestMethod,mode:"cors"};if(1===t){const e=o.find((e=>"cookie"===e.name.toLowerCase())),t={};delete p.mode,e&&(t.cookie=e.value),c&&(delete p.referrer,t.Referer=c),c&&(delete p.referrerPolicy,t["Referrer-Policy"]=h),Object.keys(t).length&&(p.headers={...a,...t})}else p.credentials=l;return`fetch(${n}, ${JSON.stringify(p,null,2)});`}async generateAllFetchCall(e,t){const r=this.filterOutBlobRequests(e);return(await Promise.all(r.map((e=>this.generateFetchCall(e,t))))).join(" ;\n")}async generateCurlCommand(e,t){let r=[];const i=new Set(["accept-encoding","host","method","path","scheme","version"]);const n="win"===t?function(e){const t=/[\r\n]/.test(e)?'^"':'"';return t+e.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/[^a-zA-Z0-9\s_\-:=+~'\/.',?;()*`&]/g,"^$&").replace(/%(?=[a-zA-Z0-9_])/g,"%^").replace(/\r?\n/g,"^\n\n")+t}:function(e){return/[\0-\x1F\x7F-\x9F!]|\'/.test(e)?"$'"+e.replace(/\\/g,"\\\\").replace(/\'/g,"\\'").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\0-\x1F\x7F-\x9F!]/g,(function(e){let t=e.charCodeAt(0).toString(16);for(;t.length<4;)t="0"+t;return"\\u"+t}))+"'":"'"+e+"'"};r.push(n(e.url()).replace(/[[{}\]]/g,"\\$&"));let o="GET";const s=[],a=await e.requestFormData();a&&(s.push("--data-raw "+n(a)),i.add("content-length"),o="POST"),e.requestMethod!==o&&r.push("-X "+n(e.requestMethod));const l=e.requestHeaders();for(let e=0;e<l.length;e++){const t=l[e],o=t.name.replace(/^:/,"");i.has(o.toLowerCase())||r.push("-H "+n(o+": "+t.value))}return r=r.concat(s),r.push("--compressed"),"insecure"===e.securityState()&&r.push("--insecure"),"curl "+r.join(r.length>=3?"win"===t?" ^\n  ":" \\\n  ":" ")}async generateAllCurlCommand(e,t){const r=this.filterOutBlobRequests(e),i=await Promise.all(r.map((e=>this.generateCurlCommand(e,t))));return"win"===t?i.join(" &\r\n"):i.join(" ;\n")}async generatePowerShellCommand(e){const t=[],r=new Set(["host","connection","proxy-connection","content-length","expect","range","content-type","user-agent","cookie"]);function i(e){return'"'+e.replace(/[`\$"]/g,"`$&").replace(/[^\x20-\x7E]/g,(e=>"$([char]"+e.charCodeAt(0)+")"))+'"'}t.push("-Uri "+i(e.url())),"GET"!==e.requestMethod&&t.push("-Method "+i(e.requestMethod));const n=function(e){const t=[],r=e.requestHeaders().find((({name:e})=>"user-agent"===e.toLowerCase()));r&&t.push(`$session.UserAgent = ${i(r.value)}`);for(const r of e.includedRequestCookies()){const e=i(r.name()),n=i(r.value()),o=i(r.domain());t.push(`$session.Cookies.Add((New-Object System.Net.Cookie(${e}, ${n}, "/", ${o})))`)}return t.length?"$session = New-Object Microsoft.PowerShell.Commands.WebRequestSession\n"+t.join("\n")+"\n":null}(e);n&&t.push("-WebSession $session");const o=e.requestHeaders(),s=[];for(const e of o){const t=e.name.replace(/^:/,"");r.has(t.toLowerCase())||s.push(i(t)+"="+i(e.value))}s.length&&t.push("-Headers @{\n"+s.join("\n  ")+"\n}");const a=o.find((({name:e})=>"content-type"===e.toLowerCase()));a&&t.push("-ContentType "+i(a.value));const l=await e.requestFormData();if(l){const e=i(l);/[^\x20-\x7E]/.test(l)?t.push("-Body ([System.Text.Encoding]::UTF8.GetBytes("+e+"))"):t.push("-Body "+e)}return(n||"")+"Invoke-WebRequest -UseBasicParsing "+t.join(t.length>=3?" `\n":" ")}async generateAllPowerShellCommand(e){const t=this.filterOutBlobRequests(e);return(await Promise.all(t.map((e=>this.generatePowerShellCommand(e))))).join(";\r\n")}static getDCLEventColor(){return k.ThemeSupport.instance().getComputedValue("--color-syntax-3")}static getLoadEventColor(){return k.ThemeSupport.instance().getComputedValue("--color-syntax-1")}}function L(e){let t="";for(const r of e.callFrames){t+=`${b.UIUtils.beautifyFunctionName(r.functionName)} @ ${r.url}:${r.lineNumber+1}\n`}return e.parent&&(t+=L(e.parent)),t}const F=new WeakSet,E=new WeakMap;function A(e){return F.has(e)}const N={http:!0,https:!0,ws:!0,wss:!0},P=Object.values(g.UIFilter.FilterType)})),r.register("fPfDn",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync('/*\n * Copyright (C) 2013 Google Inc. All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are\n * met:\n *\n *     * Redistributions of source code must retain the above copyright\n * notice, this list of conditions and the following disclaimer.\n *     * Redistributions in binary form must reproduce the above\n * copyright notice, this list of conditions and the following disclaimer\n * in the documentation and/or other materials provided with the\n * distribution.\n *     * Neither the name of Google Inc. nor the names of its\n * contributors may be used to endorse or promote products derived from\n * this software without specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\n.network-log-grid.data-grid {\n  border: none !important; /* stylelint-disable-line declaration-no-important */\n  flex: auto;\n}\n\n.network-log-grid.data-grid.no-selection:focus-visible {\n  border: none !important; /* stylelint-disable-line declaration-no-important */\n}\n\n#network-container {\n  border: 1px solid var(--color-details-hairline);\n  overflow: hidden;\n}\n\n#network-container.grid-focused.no-node-selected:focus-within {\n  border: 1px solid var(--legacy-accent-color);\n}\n\n.network-summary-bar {\n  flex: 0 0 27px;\n  line-height: 27px;\n  padding-left: 5px;\n  background-color: var(--color-background-elevation-1);\n  border-top: 1px solid var(--color-details-hairline);\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  user-select: text;\n}\n\n.panel.network .toolbar.network-summary-bar {\n  border-bottom: 0;\n}\n\n.network-summary-bar span[is="dt-icon-label"] {\n  margin-right: 6px;\n}\n\n.network-summary-bar > * {\n  flex: none;\n}\n\n.network-log-grid.data-grid table.data {\n  background: transparent;\n}\n\n.network-log-grid.data-grid td {\n  height: 41px;\n  border-left: 1px solid var(--color-details-hairline);\n  vertical-align: middle;\n}\n\n.network-log-grid.data-grid .corner {\n  display: none;\n}\n\n.network-log-grid.data-grid.small td {\n  height: 21px;\n}\n\n.network-waterfall-header,\n.network-log-grid.data-grid th {\n  border-bottom: 1px solid var(--color-details-hairline);\n  border-left: 1px solid var(--color-details-hairline);\n}\n\n.network-log-grid.data-grid table.data th {\n  border-bottom: none;\n}\n\n.network-waterfall-header,\n.network-log-grid.data-grid .header-container {\n  height: 31px;\n  background-color: var(--color-background-elevation-1);\n}\n\n.network-log-grid.data-grid .data-container {\n  top: 31px;\n}\n\n.network-waterfall-header.small,\n.network-log-grid.data-grid.small .header-container {\n  height: 27px;\n}\n\n.network-log-grid.data-grid.small .data-container {\n  top: 27px;\n}\n\n.network-log-grid.data-grid select {\n  appearance: none;\n  border: none;\n  width: 100%;\n  color: inherit;\n}\n\n.network-log-grid.data-grid .waterfall-column {\n  padding: 1px 0;\n}\n\n.network-log-grid.data-grid .waterfall-column .sort-order-icon-container {\n  right: 15px;\n  pointer-events: none;\n}\n\n.network-log-grid.data-grid th.sortable:active {\n  background-image: none !important; /* stylelint-disable-line declaration-no-important */\n}\n\n.network-cell-subtitle {\n  font-weight: normal;\n  color: var(--color-text-secondary);\n}\n\n.network-badge {\n  margin-right: 4px;\n}\n\n.status-column .devtools-link {\n  color: inherit;\n}\n\n.initiator-column .devtools-link {\n  color: inherit;\n}\n\n.network-error-row,\n.network-error-row .network-cell-subtitle {\n  /* stylelint-disable-next-line declaration-no-important */\n  color: var(--color-red) !important;\n}\n\n.network-log-grid.data-grid tr.selected.network-error-row,\n.network-log-grid.data-grid tr.selected.network-error-row .network-cell-subtitle,\n.network-log-grid.data-grid tr.selected.network-error-row .network-dim-cell,\n.network-log-grid.data-grid:focus tr.selected.network-error-row .devtools-link,\n.network-log-grid.data-grid:focus tr.selected.network-error-row,\n.network-log-grid.data-grid:focus tr.selected.network-error-row .network-cell-subtitle,\n.network-log-grid.data-grid:focus tr.selected.network-error-row .network-dim-cell {\n  color: var(--color-accent-red);\n}\n\n/* We are using a multitude of different selector specificity rules here, which\n   is incompatible with what stylelint requires as ordering of the rules. */\n/* stylelint-disable no-descending-specificity */\n\n.network-log-grid.data-grid tr.selected,\n.network-log-grid.data-grid tr.selected .network-cell-subtitle,\n.network-log-grid.data-grid tr.selected .network-dim-cell {\n  color: inherit;\n}\n\n.network-log-grid.data-grid:focus tr.selected,\n.network-log-grid.data-grid:focus tr.selected .network-cell-subtitle,\n.network-log-grid.data-grid:focus tr.selected .network-dim-cell {\n  color: var(--legacy-selection-fg-color);\n}\n\n.network-header-subtitle {\n  color: var(--color-text-secondary);\n}\n\n.network-log-grid.data-grid.small .network-cell-subtitle,\n.network-log-grid.data-grid.small .network-header-subtitle {\n  display: none;\n}\n\n.network-log-grid.data-grid.small tr.selected .network-cell-subtitle-show-inline-when-selected {\n  display: inline;\n  margin-left: 4px;\n}\n\n.network-log-grid tr.highlighted-row {\n  --override-highlight-fade-from: rgb(255 255 120 / 100%);\n  --override-highlight-fade-to: rgb(255 255 120 / 0%);\n\n  animation: network-row-highlight-fadeout 2s 0s;\n}\n/* See comment above why the rules were disabled */\n/* stylelint-enable no-descending-specificity */\n\n@keyframes network-row-highlight-fadeout {\n  from {\n    background-color: var(--override-highlight-fade-from);\n  }\n\n  to {\n    background-color: var(--override-highlight-fade-to);\n  }\n}\n\n/* Resource preview icons */\n/* These rules are grouped by type and therefore do not adhere to the ordering of stylelint */\n/* stylelint-disable no-descending-specificity, no-duplicate-selectors */\n\n.network-log-grid.data-grid .icon.image {\n  position: relative;\n}\n\n.network-log-grid.data-grid .icon {\n  float: left;\n  width: 32px;\n  height: 32px;\n  margin-top: 1px;\n  margin-right: 3px;\n}\n\n.network-log-grid.data-grid:focus .data-grid-data-grid-node.selected img.icon {\n  filter: brightness(0) invert(1);\n}\n\n.network-log-grid.data-grid:focus .data-grid-data-grid-node.selected [is="ui-icon"].icon-mask {\n  --network-grid-selected-color: #dadce0;\n  --override-icon-mask-background-color: var(--network-grid-selected-color);\n}\n\n.network-log-grid.data-grid .network-error-row.data-grid-data-grid-node img.icon,\n.network-log-grid.data-grid .network-error-row.data-grid-data-grid-node.selected img.icon {\n  /* This is generated with https://codepen.io/sosuke/pen/Pjoqqp to target var(--color-red) */\n  filter: brightness(0) saturate(100%) invert(35%) sepia(76%) saturate(1413%) hue-rotate(338deg) brightness(92%) contrast(103%);\n}\n\n.network-log-grid.data-grid.small .icon {\n  margin-top: 0;\n  width: 18px;\n  height: 18px;\n}\n\n.image-network-icon-preview {\n  background: var(--color-image-preview-background);\n  border: 1px solid var(--color-details-hairline);\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  overflow: hidden;\n  right: 0;\n  top: 0;\n}\n\n.network-log-grid.data-grid .image-network-icon-preview {\n  position: absolute;\n  max-width: 18px;\n  max-height: 21px;\n  min-width: 1px;\n  min-height: 1px;\n}\n\n.network-log-grid.data-grid.small .image-network-icon-preview {\n  left: 2px;\n  right: 2px;\n  max-width: 10px;\n  max-height: 12px;\n}\n\n.network-log-grid.data-grid .trailing-link-icon {\n  padding-left: 0.5ex;\n}\n/* stylelint-enable no-descending-specificity, no-duplicate-selectors */\n/* This is part of the large color block declared above, but should not be\n   extracted out. */\n/* stylelint-disable-next-line no-descending-specificity */\n.network-dim-cell {\n  color: var(--color-text-secondary);\n}\n\n.network-frame-divider {\n  --override-frame-divider: #fccc49;\n\n  width: 2px;\n  background-color: var(--override-frame-divider);\n  z-index: 10;\n  visibility: hidden;\n}\n\n.-theme-with-dark-background .network-frame-divider,\n:host-context(.-theme-with-dark-background) .network-frame-divider {\n  --override-frame-divider: rgb(182 134 3);\n}\n\n#network-container:not(.brief-mode) .data-container {\n  overflow: hidden;\n}\n\n.network-log-grid.data-grid .resources-dividers {\n  z-index: 0;\n}\n\n.network-log-grid.data-grid .resources-dividers-label-bar {\n  background-color: transparent;\n  border: none;\n  height: 30px;\n  pointer-events: none;\n}\n\n.network-log-grid.data-grid span.separator-in-cell {\n  user-select: none;\n  min-width: 1ex;\n  display: inline-block;\n}\n\n.network-status-pane {\n  color: var(--color-text-secondary);\n  background-color: var(--color-background);\n  z-index: 500;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  text-align: center;\n  padding: 0 20px;\n  overflow: auto;\n}\n\n.network-status-pane > .recording-hint {\n  font-size: 14px;\n  text-align: center;\n  line-height: 28px;\n}\n\n.network-waterfall-header {\n  position: absolute;\n  border-left: 0;\n  width: 100%;\n  display: table;\n  z-index: 200;\n}\n\n.network-waterfall-header:hover {\n  background-color: var(--color-background-highlight);\n}\n\n.network-waterfall-header div {\n  display: table-cell;\n  line-height: 14px;\n  margin: auto 0;\n  vertical-align: middle;\n  text-align: left;\n  font-weight: normal;\n  padding: 0 4px;\n}\n/* All network-waterfall-header rules are defined here instead of above */\n/* stylelint-disable-next-line no-descending-specificity */\n.network-waterfall-header .sort-order-icon-container {\n  position: absolute;\n  top: 1px;\n  right: 0;\n  bottom: 1px;\n  display: flex;\n  align-items: center;\n}\n\n.network-waterfall-header .sort-order-icon {\n  align-items: center;\n  margin-right: 4px;\n  margin-bottom: -2px;\n}\n\n.network-frame-group-icon {\n  display: inline-block;\n  margin: -8px -2px;\n}\n\n.network-frame-group-badge {\n  margin-right: 4px;\n}\n\n@media (forced-colors: active) {\n  .network-status-pane > .recording-hint {\n    color: canvastext;\n  }\n\n  .initiator-column .devtools-link {\n    color: linktext;\n  }\n\n  /* This is part of the large color block declared above, but should not be\n   extracted out. */\n  /* stylelint-disable no-descending-specificity */\n  .network-log-grid.data-grid table.data tr.revealed.selected,\n  .network-log-grid.data-grid:focus table.data tr.revealed.selected,\n  .network-log-grid.data-grid:focus tr.selected .network-dim-cell,\n  .network-log-grid.data-grid tr.selected .network-dim-cell,\n  .network-log-grid.data-grid:focus tr.selected .initiator-column .devtools-link,\n  .network-log-grid.data-grid tr.selected .initiator-column .devtools-link,\n  .network-waterfall-header:hover * {\n    color: HighlightText;\n  }\n  /* stylelint-enable no-descending-specificity */\n\n  .network-log-grid {\n    --network-grid-default-color: canvas;\n    --network-grid-stripe-color: canvas;\n    --network-grid-hovered-color: Highlight;\n    --network-grid-selected-color: ButtonText;\n    --network-grid-focus-selected-color: Highlight;\n  }\n\n  #network-container.no-node-selected:focus-within,\n  .network-status-pane {\n    forced-color-adjust: none;\n    border-color: Highlight;\n    background-color: canvas !important; /* stylelint-disable-line declaration-no-important */\n  }\n\n  .network-waterfall-header:hover {\n    forced-color-adjust: none;\n    background-color: Highlight;\n  }\n\n  .network-waterfall-header.small,\n  .network-log-grid.data-grid.small .header-container .network-waterfall-header,\n  .network-log-grid.data-grid .header-container {\n    background-color: canvas;\n  }\n\n  .network-waterfall-header:hover .sort-order-icon-container [is="ui-icon"].icon-mask {\n    background-color: HighlightText;\n  }\n}\n\n/*# sourceURL=networkLogView.css */\n');var n=i})),r.register("k5iKk",(function(t,i){e(t.exports,"NetworkFrameGrouper",(()=>l)),e(t.exports,"FrameGroupNode",(()=>d));var n=r("koSS8"),o=r("eQFvP"),s=r("9z2ZV"),a=r("9kyN0");class l{parentView;activeGroups;constructor(e){this.parentView=e,this.activeGroups=new Map}groupNodeForRequest(e){const t=o.ResourceTreeModel.ResourceTreeModel.frameForRequest(e);if(!t||t.isTopFrame())return null;let r=this.activeGroups.get(t);return r||(r=new d(this.parentView,t),this.activeGroups.set(t,r),r)}reset(){this.activeGroups.clear()}}class d extends a.NetworkGroupNode{frame;constructor(e,t){super(e),this.frame=t}displayName(){return new n.ParsedURL.ParsedURL(this.frame.url).domain()||this.frame.name||"<iframe>"}renderCell(e,t){super.renderCell(e,t);if(0===this.dataGrid.indexOfVisibleColumn(t)){const r=this.displayName();e.appendChild(s.Icon.Icon.create("largeicon-navigator-frame","network-frame-group-icon")),s.UIUtils.createTextChild(e,r),s.Tooltip.Tooltip.install(e,r),this.setCellAccessibleName(e.textContent||"",e,t)}}}})),r.register("dRcPb",(function(t,i){e(t.exports,"NetworkLogViewColumns",(()=>b)),e(t.exports,"_calculatorTypes",(()=>v)),e(t.exports,"_defaultColumnConfig",(()=>S)),e(t.exports,"WaterfallSortIds",(()=>R)),e(t.exports,"_initialSortColumn",(()=>k)),e(t.exports,"_filmStripDividerColor",(()=>T));var n=r("koSS8"),o=r("ixFnt"),s=r("cObcK"),a=r("a3yig"),l=r("9z2ZV"),d=r("5887T"),c=r("9kyN0"),h=r("gGxKN"),u=r("km5qV"),p=r("ej1uS");const g={networkLog:"Network Log",waterfall:"Waterfall",responseHeaders:"Response Headers",manageHeaderColumns:"Manage Header Columns…",startTime:"Start Time",responseTime:"Response Time",endTime:"End Time",totalDuration:"Total Duration",latency:"Latency",name:"Name",path:"Path",url:"Url",method:"Method",status:"Status",text:"Text",protocol:"Protocol",scheme:"Scheme",domain:"Domain",remoteAddress:"Remote Address",type:"Type",initiator:"Initiator",initiatorAddressSpace:"Initiator Address Space",cookies:"Cookies",setCookies:"Set Cookies",size:"Size",content:"Content",time:"Time",priority:"Priority",connectionId:"Connection ID",remoteAddressSpace:"Remote Address Space"},m=o.i18n.registerUIStrings("panels/network/NetworkLogViewColumns.ts",g),f=o.i18n.getLocalizedString.bind(void 0,m),w=o.i18n.getLazilyComputedLocalizedString.bind(void 0,m);class b{networkLogView;persistantSettings;networkLogLargeRowsSetting;eventDividers;eventDividersShown;gridMode;columns;waterfallRequestsAreStale;waterfallScrollerWidthIsStale;popupLinkifier;calculatorsMap;lastWheelTime;dataGridInternal;splitWidget;waterfallColumn;activeScroller;dataGridScroller;waterfallScroller;waterfallScrollerContent;waterfallHeaderElement;waterfallColumnSortIcon;activeWaterfallSortId;popoverHelper;hasScrollerTouchStarted;scrollerTouchStartPos;constructor(e,t,r,i){this.networkLogView=e,this.persistantSettings=n.Settings.Settings.instance().createSetting("networkLogColumns",{}),this.networkLogLargeRowsSetting=i,this.networkLogLargeRowsSetting.addChangeListener(this.updateRowsSize,this),this.eventDividers=new Map,this.eventDividersShown=!1,this.gridMode=!0,this.columns=[],this.waterfallRequestsAreStale=!1,this.waterfallScrollerWidthIsStale=!0,this.popupLinkifier=new a.Linkifier.Linkifier,this.calculatorsMap=new Map,this.calculatorsMap.set(v.Time,t),this.calculatorsMap.set(v.Duration,r),this.lastWheelTime=0,this.setupDataGrid(),this.setupWaterfall(),d.ThemeSupport.instance().addEventListener(d.ThemeChangeEvent.eventName,(()=>{this.scheduleRefresh()}))}static convertToDataGridDescriptor(e){const t=e.title instanceof Function?e.title():e.title;return{id:e.id,title:t,sortable:e.sortable,align:e.align,nonSelectable:e.nonSelectable,weight:e.weight,allowInSortByEvenWhenHidden:e.allowInSortByEvenWhenHidden}}wasShown(){this.updateRowsSize()}willHide(){this.popoverHelper&&this.popoverHelper.hidePopover()}reset(){this.popoverHelper&&this.popoverHelper.hidePopover(),this.eventDividers.clear()}setupDataGrid(){const e=y,t=S;this.columns=[];for(const r of e){const e=Object.assign({},t,r);if(e.id=e.id,e.subtitle){const t=e.title instanceof Function?e.title():e.title,r=e.subtitle instanceof Function?e.subtitle():e.subtitle;e.titleDOMFragment=this.makeHeaderFragment(t,r)}this.columns.push(e)}this.loadCustomColumnsAndSettings(),this.popoverHelper=new l.PopoverHelper.PopoverHelper(this.networkLogView.element,this.getPopoverRequest.bind(this)),this.popoverHelper.setHasPadding(!0),this.popoverHelper.setTimeout(300,300),this.dataGridInternal=new s.SortableDataGrid.SortableDataGrid({displayName:f(g.networkLog),columns:this.columns.map(b.convertToDataGridDescriptor),editCallback:void 0,deleteCallback:void 0,refreshCallback:void 0}),this.dataGridInternal.element.addEventListener("mousedown",(e=>{!this.dataGridInternal.selectedNode&&e.button&&e.consume()}),!0),this.dataGridScroller=this.dataGridInternal.scrollContainer,this.updateColumns(),this.dataGridInternal.addEventListener(s.DataGrid.Events.SortingChanged,this.sortHandler,this),this.dataGridInternal.setHeaderContextMenuCallback(this.innerHeaderContextMenu.bind(this)),this.activeWaterfallSortId=R.StartTime,this.dataGridInternal.markColumnAsSortedBy(k,s.DataGrid.Order.Ascending),this.splitWidget=new l.SplitWidget.SplitWidget(!0,!0,"networkPanelSplitViewWaterfall",200);const r=this.dataGridInternal.asWidget();r.setMinimumSize(150,0),this.splitWidget.setMainWidget(r)}setupWaterfall(){this.waterfallColumn=new(0,u.NetworkWaterfallColumn)(this.networkLogView.calculator()),this.waterfallColumn.element.addEventListener("contextmenu",function(e){const t=e,r=this.waterfallColumn.getNodeFromPoint(t.offsetX,t.offsetY);if(!r)return;const i=r.request();if(!i)return;const n=new l.ContextMenu.ContextMenu(t);this.networkLogView.handleContextMenuForRequest(n,i),n.show()}.bind(this)),this.waterfallColumn.element.addEventListener("wheel",this.onMouseWheel.bind(this,!1),{passive:!0}),this.waterfallColumn.element.addEventListener("touchstart",this.onTouchStart.bind(this)),this.waterfallColumn.element.addEventListener("touchmove",this.onTouchMove.bind(this)),this.waterfallColumn.element.addEventListener("touchend",this.onTouchEnd.bind(this)),this.dataGridScroller.addEventListener("wheel",this.onMouseWheel.bind(this,!0),!0),this.dataGridScroller.addEventListener("touchstart",this.onTouchStart.bind(this)),this.dataGridScroller.addEventListener("touchmove",this.onTouchMove.bind(this)),this.dataGridScroller.addEventListener("touchend",this.onTouchEnd.bind(this)),this.waterfallScroller=this.waterfallColumn.contentElement.createChild("div","network-waterfall-v-scroll"),this.waterfallScrollerContent=this.waterfallScroller.createChild("div","network-waterfall-v-scroll-content"),this.dataGridInternal.addEventListener(s.DataGrid.Events.PaddingChanged,(()=>{this.waterfallScrollerWidthIsStale=!0,this.syncScrollers()})),this.dataGridInternal.addEventListener(s.ViewportDataGrid.Events.ViewportCalculated,this.redrawWaterfallColumn.bind(this)),this.createWaterfallHeader(),this.waterfallColumn.contentElement.classList.add("network-waterfall-view"),this.waterfallColumn.setMinimumSize(100,0),this.splitWidget.setSidebarWidget(this.waterfallColumn),this.switchViewMode(!1)}onMouseWheel(e,t){e&&t.consume(!0);const r=t,i=Date.now()-this.lastWheelTime<80;this.activeScroller.scrollBy({top:r.deltaY,behavior:i?"auto":"smooth"}),this.syncScrollers(),this.lastWheelTime=Date.now()}onTouchStart(e){const t=e;this.hasScrollerTouchStarted=!0,this.scrollerTouchStartPos=t.changedTouches[0].pageY}onTouchMove(e){if(!this.hasScrollerTouchStarted)return;const t=e.changedTouches[0].pageY,r=this.scrollerTouchStartPos-t;this.activeScroller.scrollBy({top:r,behavior:"auto"}),this.syncScrollers(),this.scrollerTouchStartPos=t}onTouchEnd(){this.hasScrollerTouchStarted=!1}syncScrollers(){this.waterfallColumn.isShowing()&&(this.waterfallScrollerContent.style.height=this.dataGridScroller.scrollHeight+"px",this.updateScrollerWidthIfNeeded(),this.dataGridScroller.scrollTop=this.waterfallScroller.scrollTop)}updateScrollerWidthIfNeeded(){this.waterfallScrollerWidthIsStale&&(this.waterfallScrollerWidthIsStale=!1,this.waterfallColumn.setRightPadding(this.waterfallScroller.offsetWidth-this.waterfallScrollerContent.offsetWidth))}redrawWaterfallColumn(){if(!this.waterfallRequestsAreStale)return this.updateScrollerWidthIfNeeded(),void this.waterfallColumn.update(this.activeScroller.scrollTop,this.eventDividersShown?this.eventDividers:void 0);this.syncScrollers();const e=this.networkLogView.flatNodesList();this.waterfallColumn.update(this.activeScroller.scrollTop,this.eventDividers,e)}createWaterfallHeader(){this.waterfallHeaderElement=this.waterfallColumn.contentElement.createChild("div","network-waterfall-header"),this.waterfallHeaderElement.addEventListener("click",function(){const e=s.DataGrid.Order,t="waterfall"===this.dataGridInternal.sortColumnId(),r=this.dataGridInternal.isSortOrderAscending(),i=t&&r?e.Descending:e.Ascending;this.dataGridInternal.markColumnAsSortedBy("waterfall",i),this.sortHandler()}.bind(this)),this.waterfallHeaderElement.addEventListener("contextmenu",(e=>this.innerHeaderContextMenu(new l.ContextMenu.ContextMenu(e))));this.waterfallHeaderElement.createChild("div").textContent=f(g.waterfall),this.waterfallColumnSortIcon=l.Icon.Icon.create("","sort-order-icon"),this.waterfallHeaderElement.createChild("div","sort-order-icon-container").appendChild(this.waterfallColumnSortIcon)}setCalculator(e){this.waterfallColumn.setCalculator(e)}scheduleRefresh(){this.waterfallColumn.scheduleDraw()}updateRowsSize(){const e=Boolean(this.networkLogLargeRowsSetting.get());this.dataGridInternal.element.classList.toggle("small",!e),this.dataGridInternal.scheduleUpdate(),this.waterfallScrollerWidthIsStale=!0,this.waterfallColumn.setRowHeight(e?41:21),this.waterfallScroller.classList.toggle("small",!e),this.waterfallHeaderElement.classList.toggle("small",!e),window.requestAnimationFrame((()=>{this.waterfallColumn.setHeaderHeight(this.waterfallScroller.offsetTop),this.waterfallColumn.scheduleDraw()}))}show(e){this.splitWidget.show(e)}setHidden(e){l.ARIAUtils.setHidden(this.splitWidget.element,e)}dataGrid(){return this.dataGridInternal}sortByCurrentColumn(){this.sortHandler()}sortHandler(){const e=this.dataGridInternal.sortColumnId();if(this.networkLogView.removeAllNodeHighlights(),this.waterfallRequestsAreStale=!0,"waterfall"===e){this.dataGridInternal.sortOrder()===s.DataGrid.Order.Ascending?this.waterfallColumnSortIcon.setIconType("smallicon-triangle-up"):this.waterfallColumnSortIcon.setIconType("smallicon-triangle-down");const e=c.NetworkRequestNode.RequestPropertyComparator.bind(null,this.activeWaterfallSortId);return this.dataGridInternal.sortNodes(e,!this.dataGridInternal.isSortOrderAscending()),void this.dataGridSortedForTest()}this.waterfallColumnSortIcon.setIconType("");const t=this.columns.find((t=>t.id===e));if(!t||!t.sortingFunction)return;const r=t.sortingFunction;r&&(this.dataGridInternal.sortNodes(r,!this.dataGridInternal.isSortOrderAscending()),this.dataGridSortedForTest())}dataGridSortedForTest(){}updateColumns(){if(!this.dataGridInternal)return;const e=new Set;if(this.gridMode)for(const t of this.columns)t.visible&&e.add(t.id);else{const t=this.columns.find((e=>"path"===e.hideableGroup&&e.visible));t?e.add(t.id):e.add("name")}this.dataGridInternal.setColumnsVisiblity(e)}switchViewMode(e){this.gridMode!==e&&(this.gridMode=e,e?(this.splitWidget.showBoth(),this.activeScroller=this.waterfallScroller,this.waterfallScroller.scrollTop=this.dataGridScroller.scrollTop,this.dataGridInternal.setScrollContainer(this.waterfallScroller)):(this.networkLogView.removeAllNodeHighlights(),this.splitWidget.hideSidebar(),this.activeScroller=this.dataGridScroller,this.dataGridInternal.setScrollContainer(this.dataGridScroller)),this.networkLogView.element.classList.toggle("brief-mode",!e),this.updateColumns(),this.updateRowsSize())}toggleColumnVisibility(e){this.loadCustomColumnsAndSettings(),e.visible=!e.visible,this.saveColumnsSettings(),this.updateColumns()}saveColumnsSettings(){const e={};for(const t of this.columns)e[t.id]={visible:t.visible,title:t.title};this.persistantSettings.set(e)}loadCustomColumnsAndSettings(){const e=this.persistantSettings.get(),t=Object.keys(e);for(const r of t){const t=e[r];let i=this.columns.find((e=>e.id===r));i||(i=this.addCustomHeader(t.title,r)||void 0),i&&i.hideable&&"boolean"==typeof t.visible&&(i.visible=Boolean(t.visible)),i&&"string"==typeof t.title&&(i.title=t.title)}}makeHeaderFragment(e,t){const r=document.createDocumentFragment();l.UIUtils.createTextChild(r,e);const i=r.createChild("div","network-header-subtitle");return l.UIUtils.createTextChild(i,t),r}innerHeaderContextMenu(e){const t=this.columns.filter((e=>e.hideable)),r=t.filter((e=>!e.isResponseHeader)),i=new Map,n=[];for(const e of r)if(e.hideableGroup){const t=e.hideableGroup;let r=i.get(t);r||(r=[],i.set(t,r)),r.push(e)}else n.push(e);for(const t of i.values()){const r=t.filter((e=>e.visible));for(const i of t){const t=1===r.length&&r[0]===i,n=i.title instanceof Function?i.title():i.title;e.headerSection().appendCheckboxItem(n,this.toggleColumnVisibility.bind(this,i),i.visible,t)}e.headerSection().appendSeparator()}for(const t of n){const r=t.title instanceof Function?t.title():t.title;e.headerSection().appendCheckboxItem(r,this.toggleColumnVisibility.bind(this,t),t.visible)}const o=e.footerSection().appendSubMenuItem(f(g.responseHeaders)),a=t.filter((e=>e.isResponseHeader));for(const e of a){const t=e.title instanceof Function?e.title():e.title;o.defaultSection().appendCheckboxItem(t,this.toggleColumnVisibility.bind(this,e),e.visible)}o.footerSection().appendItem(f(g.manageHeaderColumns),this.manageCustomHeaderDialog.bind(this));const l=R,d=e.footerSection().appendSubMenuItem(f(g.waterfall));function c(e){let t=this.calculatorsMap.get(v.Time);const r=R;e!==r.Duration&&e!==r.Latency||(t=this.calculatorsMap.get(v.Duration)),this.networkLogView.setCalculator(t),this.activeWaterfallSortId=e,this.dataGridInternal.markColumnAsSortedBy("waterfall",s.DataGrid.Order.Ascending),this.sortHandler()}d.defaultSection().appendCheckboxItem(f(g.startTime),c.bind(this,l.StartTime),this.activeWaterfallSortId===l.StartTime),d.defaultSection().appendCheckboxItem(f(g.responseTime),c.bind(this,l.ResponseTime),this.activeWaterfallSortId===l.ResponseTime),d.defaultSection().appendCheckboxItem(f(g.endTime),c.bind(this,l.EndTime),this.activeWaterfallSortId===l.EndTime),d.defaultSection().appendCheckboxItem(f(g.totalDuration),c.bind(this,l.Duration),this.activeWaterfallSortId===l.Duration),d.defaultSection().appendCheckboxItem(f(g.latency),c.bind(this,l.Latency),this.activeWaterfallSortId===l.Latency)}manageCustomHeaderDialog(){const e=[];for(const t of this.columns){const r=t.title instanceof Function?t.title():t.title;t.isResponseHeader&&e.push({title:r,editable:t.isCustomHeader})}const t=new(0,h.NetworkManageCustomHeadersView)(e,(e=>Boolean(this.addCustomHeader(e))),this.changeCustomHeader.bind(this),this.removeCustomHeader.bind(this)),r=new l.Dialog.Dialog;t.show(r.contentElement),r.setSizeBehavior("MeasureContent"),r.show(this.networkLogView.element)}removeCustomHeader(e){e=e.toLowerCase();const t=this.columns.findIndex((t=>t.id===e));return-1!==t&&(this.columns.splice(t,1),this.dataGridInternal.removeColumn(e),this.saveColumnsSettings(),this.updateColumns(),!0)}addCustomHeader(e,t,r){t||(t=e.toLowerCase()),void 0===r&&(r=this.columns.length-1);if(this.columns.find((e=>e.id===t)))return null;const i=Object.assign({},S,{id:t,title:e,isResponseHeader:!0,isCustomHeader:!0,visible:!0,sortingFunction:c.NetworkRequestNode.ResponseHeaderStringComparator.bind(null,t)});return this.columns.splice(r,0,i),this.dataGridInternal&&this.dataGridInternal.addColumn(b.convertToDataGridDescriptor(i),r),this.saveColumnsSettings(),this.updateColumns(),i}changeCustomHeader(e,t,r){r||(r=t.toLowerCase()),e=e.toLowerCase();const i=this.columns.findIndex((t=>t.id===e)),n=this.columns[i],o=this.columns.find((e=>e.id===r));return!(!n||o&&e!==r)&&(this.removeCustomHeader(e),this.addCustomHeader(t,r,i),!0)}getPopoverRequest(e){if(!this.gridMode)return null;const t=this.networkLogView.hoveredNode();if(!t||!e.target)return null;const r=e.target.enclosingNodeOrSelfWithClass("network-script-initiated");if(!r)return null;const i=t.request();return i?{box:r.boxInWindow(),show:async e=>{this.popupLinkifier.setLiveLocationUpdateCallback((()=>{e.setSizeBehavior("MeasureContent")}));const t=p.RequestInitiatorView.createStackTracePreview(i,this.popupLinkifier,!1);return!!t&&(e.contentElement.appendChild(t.element),!0)},hide:this.popupLinkifier.reset.bind(this.popupLinkifier)}:null}addEventDividers(e,t){let r="transparent";switch(t){case"network-dcl-divider":r="#0867CB";break;case"network-load-divider":r="#B31412";break;default:return}const i=this.eventDividers.get(r)||[];this.eventDividers.set(r,i.concat(e)),this.networkLogView.scheduleRefresh()}hideEventDividers(){this.eventDividersShown=!0,this.redrawWaterfallColumn()}showEventDividers(){this.eventDividersShown=!1,this.redrawWaterfallColumn()}selectFilmStripFrame(e){this.eventDividers.set(T,[e]),this.redrawWaterfallColumn()}clearFilmStripFrame(){this.eventDividers.delete(T),this.redrawWaterfallColumn()}}const k="waterfall";var v,C;(C=v||(v={})).Duration="Duration",C.Time="Time";const S={subtitle:null,visible:!1,weight:6,sortable:!0,hideable:!0,hideableGroup:null,nonSelectable:!1,isResponseHeader:!1,isCustomHeader:!1,allowInSortByEvenWhenHidden:!1},y=[{id:"name",title:w(g.name),subtitle:w(g.path),visible:!0,weight:20,hideable:!0,hideableGroup:"path",sortingFunction:c.NetworkRequestNode.NameComparator},{id:"path",title:w(g.path),hideable:!0,hideableGroup:"path",sortingFunction:c.NetworkRequestNode.RequestPropertyComparator.bind(null,"pathname")},{id:"url",title:w(g.url),hideable:!0,hideableGroup:"path",sortingFunction:c.NetworkRequestNode.RequestURLComparator},{id:"method",title:w(g.method),sortingFunction:c.NetworkRequestNode.RequestPropertyComparator.bind(null,"requestMethod")},{id:"status",title:w(g.status),visible:!0,subtitle:w(g.text),sortingFunction:c.NetworkRequestNode.RequestPropertyComparator.bind(null,"statusCode")},{id:"protocol",title:w(g.protocol),sortingFunction:c.NetworkRequestNode.RequestPropertyComparator.bind(null,"protocol")},{id:"scheme",title:w(g.scheme),sortingFunction:c.NetworkRequestNode.RequestPropertyComparator.bind(null,"scheme")},{id:"domain",title:w(g.domain),sortingFunction:c.NetworkRequestNode.RequestPropertyComparator.bind(null,"domain")},{id:"remoteaddress",title:w(g.remoteAddress),weight:10,align:s.DataGrid.Align.Right,sortingFunction:c.NetworkRequestNode.RemoteAddressComparator},{id:"remoteaddress-space",title:w(g.remoteAddressSpace),visible:!1,weight:10,sortingFunction:c.NetworkRequestNode.RemoteAddressSpaceComparator},{id:"type",title:w(g.type),visible:!0,sortingFunction:c.NetworkRequestNode.TypeComparator},{id:"initiator",title:w(g.initiator),visible:!0,weight:10,sortingFunction:c.NetworkRequestNode.InitiatorComparator},{id:"initiator-address-space",title:w(g.initiatorAddressSpace),visible:!1,weight:10,sortingFunction:c.NetworkRequestNode.InitiatorAddressSpaceComparator},{id:"cookies",title:w(g.cookies),align:s.DataGrid.Align.Right,sortingFunction:c.NetworkRequestNode.RequestCookiesCountComparator},{id:"setcookies",title:w(g.setCookies),align:s.DataGrid.Align.Right,sortingFunction:c.NetworkRequestNode.ResponseCookiesCountComparator},{id:"size",title:w(g.size),visible:!0,subtitle:w(g.content),align:s.DataGrid.Align.Right,sortingFunction:c.NetworkRequestNode.SizeComparator},{id:"time",title:w(g.time),visible:!0,subtitle:w(g.latency),align:s.DataGrid.Align.Right,sortingFunction:c.NetworkRequestNode.RequestPropertyComparator.bind(null,"duration")},{id:"priority",title:w(g.priority),sortingFunction:c.NetworkRequestNode.PriorityComparator},{id:"connectionid",title:w(g.connectionId),sortingFunction:c.NetworkRequestNode.RequestPropertyComparator.bind(null,"connectionId")},{id:"cache-control",isResponseHeader:!0,title:o.i18n.lockedLazyString("Cache-Control"),sortingFunction:c.NetworkRequestNode.ResponseHeaderStringComparator.bind(null,"cache-control")},{id:"connection",isResponseHeader:!0,title:o.i18n.lockedLazyString("Connection"),sortingFunction:c.NetworkRequestNode.ResponseHeaderStringComparator.bind(null,"connection")},{id:"content-encoding",isResponseHeader:!0,title:o.i18n.lockedLazyString("Content-Encoding"),sortingFunction:c.NetworkRequestNode.ResponseHeaderStringComparator.bind(null,"content-encoding")},{id:"content-length",isResponseHeader:!0,title:o.i18n.lockedLazyString("Content-Length"),align:s.DataGrid.Align.Right,sortingFunction:c.NetworkRequestNode.ResponseHeaderNumberComparator.bind(null,"content-length")},{id:"etag",isResponseHeader:!0,title:o.i18n.lockedLazyString("ETag"),sortingFunction:c.NetworkRequestNode.ResponseHeaderStringComparator.bind(null,"etag")},{id:"keep-alive",isResponseHeader:!0,title:o.i18n.lockedLazyString("Keep-Alive"),sortingFunction:c.NetworkRequestNode.ResponseHeaderStringComparator.bind(null,"keep-alive")},{id:"last-modified",isResponseHeader:!0,title:o.i18n.lockedLazyString("Last-Modified"),sortingFunction:c.NetworkRequestNode.ResponseHeaderDateComparator.bind(null,"last-modified")},{id:"server",isResponseHeader:!0,title:o.i18n.lockedLazyString("Server"),sortingFunction:c.NetworkRequestNode.ResponseHeaderStringComparator.bind(null,"server")},{id:"vary",isResponseHeader:!0,title:o.i18n.lockedLazyString("Vary"),sortingFunction:c.NetworkRequestNode.ResponseHeaderStringComparator.bind(null,"vary")},{id:"waterfall",title:w(g.waterfall),visible:!1,hideable:!1,allowInSortByEvenWhenHidden:!0}],T="#fccc49";var R,x;(x=R||(R={})).StartTime="startTime",x.ResponseTime="responseReceivedTime",x.EndTime="endTime",x.Duration="duration",x.Latency="latency"})),r.register("gGxKN",(function(t,i){e(t.exports,"NetworkManageCustomHeadersView",(()=>c));var n=r("ixFnt"),o=r("9z2ZV"),s=r("22lEA");const a={manageHeaderColumns:"Manage Header Columns",noCustomHeaders:"No custom headers",addCustomHeader:"Add custom header…",headerName:"Header Name"},l=n.i18n.registerUIStrings("panels/network/NetworkManageCustomHeadersView.ts",a),d=n.i18n.getLocalizedString.bind(void 0,l);class c extends o.Widget.VBox{list;columnConfigs;addHeaderColumnCallback;changeHeaderColumnCallback;removeHeaderColumnCallback;editor;constructor(e,t,r,i){super(!0),this.contentElement.classList.add("custom-headers-wrapper"),this.contentElement.createChild("div","header").textContent=d(a.manageHeaderColumns),this.list=new o.ListWidget.ListWidget(this),this.list.element.classList.add("custom-headers-list");const n=document.createElement("div");n.classList.add("custom-headers-list-list-empty"),n.textContent=d(a.noCustomHeaders),this.list.setEmptyPlaceholder(n),this.list.show(this.contentElement),this.contentElement.appendChild(o.UIUtils.createTextButton(d(a.addCustomHeader),this.addButtonClicked.bind(this),"add-button")),this.columnConfigs=new Map,e.forEach((e=>this.columnConfigs.set(e.title.toLowerCase(),e))),this.addHeaderColumnCallback=t,this.changeHeaderColumnCallback=r,this.removeHeaderColumnCallback=i,this.contentElement.tabIndex=0}wasShown(){this.headersUpdated(),this.list.registerCSSFiles([s.default]),this.registerCSSFiles([s.default])}headersUpdated(){this.list.clear(),this.columnConfigs.forEach((e=>this.list.appendItem({header:e.title},e.editable)))}addButtonClicked(){this.list.addNewItem(this.columnConfigs.size,{header:""})}renderItem(e,t){const r=document.createElement("div");r.classList.add("custom-headers-list-item");const i=r.createChild("div","custom-header-name");return i.textContent=e.header,o.Tooltip.Tooltip.install(i,e.header),r}removeItemRequested(e,t){this.removeHeaderColumnCallback(e.header),this.columnConfigs.delete(e.header.toLowerCase()),this.headersUpdated()}commitEdit(e,t,r){const i=t.control("header").value.trim();let n;n=r?this.addHeaderColumnCallback(i):this.changeHeaderColumnCallback(e.header,i),n&&!r&&this.columnConfigs.delete(e.header.toLowerCase()),n&&this.columnConfigs.set(i.toLowerCase(),{title:i,editable:!0}),this.headersUpdated()}beginEdit(e){const t=this.createEditor();return t.control("header").value=e.header,t}createEditor(){if(this.editor)return this.editor;const e=new o.ListWidget.Editor;this.editor=e;const t=e.contentElement();t.createChild("div","custom-headers-edit-row").createChild("div","custom-headers-header").textContent=d(a.headerName);return t.createChild("div","custom-headers-edit-row").createChild("div","custom-headers-header").appendChild(e.createInput("header","text","x-custom-header",function(t,r,i){let n=!0;const o=e.control("header").value.trim().toLowerCase();this.columnConfigs.has(o)&&t.header!==o&&(n=!1);return{valid:n,errorMessage:void 0}}.bind(this))),e}}})),r.register("22lEA",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2016 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.custom-headers-list {\n  height: 272px;\n  width: 250px;\n}\n\n.custom-headers-wrapper {\n  margin: 10px;\n}\n\n.header {\n  padding: 0 0 6px;\n  font-size: 18px;\n  font-weight: normal;\n  flex: none;\n}\n\n.custom-headers-header {\n  padding: 2px;\n}\n\n.custom-headers-list-item {\n  padding-left: 5px;\n}\n\n.editor-container {\n  padding: 5px 0 0 5px;\n}\n\n.add-button {\n  width: 150px;\n  margin: auto;\n  margin-top: 5px;\n}\n\n/*# sourceURL=networkManageCustomHeadersView.css */\n");var n=i})),r.register("km5qV",(function(t,i){e(t.exports,"NetworkWaterfallColumn",(()=>u));var n=r("koSS8"),o=r("8Mint"),s=r("af4Nd"),a=r("9z2ZV"),l=r("5887T"),d=r("3ZLtp"),c=r("gpw7U"),h=r("4dQmF");class u extends a.Widget.VBox{canvas;canvasPosition;leftPadding;fontSize;rightPadding;scrollTop;headerHeight;calculator;rawRowHeight;rowHeight;offsetWidth;offsetHeight;startTime;endTime;popoverHelper;nodes;hoveredNode;eventDividers;updateRequestID;styleForTimeRangeName;styleForWaitingResourceType;styleForDownloadingResourceType;wiskerStyle;hoverDetailsStyle;pathForStyle;textLayers;constructor(e){super(!1),this.canvas=this.contentElement.createChild("canvas"),this.canvas.tabIndex=-1,this.setDefaultFocusedElement(this.canvas),this.canvasPosition=this.canvas.getBoundingClientRect(),this.leftPadding=5,this.fontSize=10,this.rightPadding=0,this.scrollTop=0,this.headerHeight=0,this.calculator=e,this.rawRowHeight=0,this.rowHeight=0,this.offsetWidth=0,this.offsetHeight=0,this.startTime=this.calculator.minimumBoundary(),this.endTime=this.calculator.maximumBoundary(),this.popoverHelper=new a.PopoverHelper.PopoverHelper(this.element,this.getPopoverRequest.bind(this)),this.popoverHelper.setHasPadding(!0),this.popoverHelper.setTimeout(300,300),this.nodes=[],this.hoveredNode=null,this.eventDividers=new Map,this.element.addEventListener("mousemove",this.onMouseMove.bind(this),!0),this.element.addEventListener("mouseleave",(e=>this.setHoveredNode(null,!1)),!0),this.element.addEventListener("click",this.onClick.bind(this),!0),this.styleForTimeRangeName=u.buildRequestTimeRangeStyle();const t=u.buildResourceTypeStyle();this.styleForWaitingResourceType=t[0],this.styleForDownloadingResourceType=t[1];const r=l.ThemeSupport.instance().getComputedValue("--color-text-disabled");this.wiskerStyle={borderColor:r,lineWidth:1,fillStyle:void 0},this.hoverDetailsStyle={fillStyle:r,lineWidth:1,borderColor:r},this.pathForStyle=new Map,this.textLayers=[]}static buildRequestTimeRangeStyle(){const e=c.RequestTimeRangeNames,t=new Map;return t.set(e.Connecting,{fillStyle:d.RequestTimeRangeNameToColor[e.Connecting]}),t.set(e.SSL,{fillStyle:d.RequestTimeRangeNameToColor[e.SSL]}),t.set(e.DNS,{fillStyle:d.RequestTimeRangeNameToColor[e.DNS]}),t.set(e.Proxy,{fillStyle:d.RequestTimeRangeNameToColor[e.Proxy]}),t.set(e.Blocking,{fillStyle:d.RequestTimeRangeNameToColor[e.Blocking]}),t.set(e.Push,{fillStyle:d.RequestTimeRangeNameToColor[e.Push]}),t.set(e.Queueing,{fillStyle:d.RequestTimeRangeNameToColor[e.Queueing],lineWidth:2,borderColor:"lightgrey"}),t.set(e.Receiving,{fillStyle:d.RequestTimeRangeNameToColor[e.Receiving],lineWidth:2,borderColor:"#03A9F4"}),t.set(e.Waiting,{fillStyle:d.RequestTimeRangeNameToColor[e.Waiting]}),t.set(e.ReceivingPush,{fillStyle:d.RequestTimeRangeNameToColor[e.ReceivingPush]}),t.set(e.ServiceWorker,{fillStyle:d.RequestTimeRangeNameToColor[e.ServiceWorker]}),t.set(e.ServiceWorkerPreparation,{fillStyle:d.RequestTimeRangeNameToColor[e.ServiceWorkerPreparation]}),t.set(e.ServiceWorkerRespondWith,{fillStyle:d.RequestTimeRangeNameToColor[e.ServiceWorkerRespondWith]}),t}static buildResourceTypeStyle(){const e=new Map([["document","hsl(215, 100%, 80%)"],["font","hsl(8, 100%, 80%)"],["media","hsl(90, 50%, 80%)"],["image","hsl(90, 50%, 80%)"],["script","hsl(31, 100%, 80%)"],["stylesheet","hsl(272, 64%, 80%)"],["texttrack","hsl(8, 100%, 80%)"],["websocket","hsl(0, 0%, 95%)"],["xhr","hsl(53, 100%, 80%)"],["fetch","hsl(53, 100%, 80%)"],["other","hsl(0, 0%, 95%)"]]),t=new Map,r=new Map;for(const s of Object.values(n.ResourceType.resourceTypes)){let n=e.get(s.name());n||(n=e.get("other"));const a=i(n);t.set(s,{fillStyle:o(n),lineWidth:1,borderColor:a}),r.set(s,{fillStyle:n,lineWidth:1,borderColor:a})}return[t,r];function i(e){const t=n.Color.Color.parse(e);if(!t)return"";const r=t.hsla();return r[1]/=2,r[2]-=Math.min(r[2],.2),t.asString(null)}function o(e){const t=n.Color.Color.parse(e);if(!t)return"";return t.hsla()[2]*=1.1,t.asString(null)}}resetPaths(){this.pathForStyle.clear(),this.pathForStyle.set(this.wiskerStyle,new Path2D),this.styleForTimeRangeName.forEach((e=>this.pathForStyle.set(e,new Path2D))),this.styleForWaitingResourceType.forEach((e=>this.pathForStyle.set(e,new Path2D))),this.styleForDownloadingResourceType.forEach((e=>this.pathForStyle.set(e,new Path2D))),this.pathForStyle.set(this.hoverDetailsStyle,new Path2D)}willHide(){this.popoverHelper.hidePopover()}wasShown(){this.update(),this.registerCSSFiles([o.default])}onMouseMove(e){this.setHoveredNode(this.getNodeFromPoint(e.offsetX,e.offsetY),e.shiftKey)}onClick(e){this.setSelectedNode(this.getNodeFromPoint(e.offsetX,e.offsetY))&&e.consume(!0)}getPopoverRequest(e){if(!this.hoveredNode)return null;const t=this.hoveredNode.request();if(!t)return null;let r,i,o;if(!n.Settings.Settings.instance().moduleSetting("networkColorCodeResourceTypes").get()&&!this.calculator.startAtZero?(r=c.RequestTimingView.calculateRequestTimeRanges(t,0).find((e=>e.name===c.RequestTimeRangeNames.Total)),i=this.timeToPosition(r.start),o=this.timeToPosition(r.end)):(r=this.getSimplifiedBarRange(t,0),i=r.start,o=r.end),o-i<50){const e=(o-i)/2;i=i+e-25,o=o-e+25}if(e.clientX<this.canvasPosition.left+i||e.clientX>this.canvasPosition.left+o)return null;const s=this.nodes.findIndex((e=>e.hovered())),a=this.getBarHeight(r.name),l=this.headerHeight+(this.rowHeight*s-this.scrollTop)+(this.rowHeight-a)/2;if(e.clientY<this.canvasPosition.top+l||e.clientY>this.canvasPosition.top+l+a)return null;const d=this.element.boxInWindow();return d.x+=i,d.y+=l,d.width=o-i,d.height=a,{box:d,show:e=>{const r=c.RequestTimingView.createTimingTable(t,this.calculator);return e.registerCSSFiles([h.default]),e.contentElement.appendChild(r),Promise.resolve(!0)},hide:void 0}}setHoveredNode(e,t){this.hoveredNode&&this.hoveredNode.setHovered(!1,!1),this.hoveredNode=e,this.hoveredNode&&this.hoveredNode.setHovered(!0,t)}setSelectedNode(e){return!(!e||!e.dataGrid)&&(e.select(),e.dataGrid.element.focus(),!0)}setRowHeight(e){this.rawRowHeight=e,this.updateRowHeight()}updateRowHeight(){this.rowHeight=Math.round(this.rawRowHeight*window.devicePixelRatio)/window.devicePixelRatio}setHeaderHeight(e){this.headerHeight=e}setRightPadding(e){this.rightPadding=e,this.calculateCanvasSize()}setCalculator(e){this.calculator=e}getNodeFromPoint(e,t){return t<=this.headerHeight?null:this.nodes[Math.floor((this.scrollTop+t-this.headerHeight)/this.rowHeight)]}scheduleDraw(){this.updateRequestID||(this.updateRequestID=this.element.window().requestAnimationFrame((()=>this.update())))}update(e,t,r){void 0!==e&&this.scrollTop!==e&&(this.popoverHelper.hidePopover(),this.scrollTop=e),r&&(this.nodes=r,this.calculateCanvasSize()),void 0!==t&&(this.eventDividers=t),this.updateRequestID&&(this.element.window().cancelAnimationFrame(this.updateRequestID),delete this.updateRequestID),this.startTime=this.calculator.minimumBoundary(),this.endTime=this.calculator.maximumBoundary(),this.resetCanvas(),this.resetPaths(),this.textLayers=[],this.draw()}resetCanvas(){const e=window.devicePixelRatio;this.canvas.width=this.offsetWidth*e,this.canvas.height=this.offsetHeight*e,this.canvas.style.width=this.offsetWidth+"px",this.canvas.style.height=this.offsetHeight+"px"}onResize(){super.onResize(),this.updateRowHeight(),this.calculateCanvasSize(),this.scheduleDraw()}calculateCanvasSize(){this.offsetWidth=this.contentElement.offsetWidth-this.rightPadding,this.offsetHeight=this.contentElement.offsetHeight,this.calculator.setDisplayWidth(this.offsetWidth),this.canvasPosition=this.canvas.getBoundingClientRect()}timeToPosition(e){const t=(this.offsetWidth-this.leftPadding)/(this.endTime-this.startTime);return Math.floor(this.leftPadding+(e-this.startTime)*t)}didDrawForTest(){}draw(){const e=!n.Settings.Settings.instance().moduleSetting("networkColorCodeResourceTypes").get()&&!this.calculator.startAtZero,t=this.nodes,r=this.canvas.getContext("2d");if(!r)return;r.save(),r.scale(window.devicePixelRatio,window.devicePixelRatio),r.translate(0,this.headerHeight),r.rect(0,0,this.offsetWidth,this.offsetHeight),r.clip();const i=Math.floor(this.scrollTop/this.rowHeight),o=Math.min(t.length,i+Math.ceil(this.offsetHeight/this.rowHeight));for(let n=i;n<o;n++){const i=this.rowHeight*n,o=t[n];this.decorateRow(r,o,i-this.scrollTop);let s=[];o.hasChildren()&&!o.expanded&&(s=o.flatChildren()),s.push(o);for(const t of s)e?this.buildTimingBarLayers(t,i-this.scrollTop):this.buildSimplifiedBarLayers(r,t,i-this.scrollTop)}this.drawLayers(r,e),r.save(),r.fillStyle=l.ThemeSupport.instance().getComputedValue("--color-text-disabled");for(const e of this.textLayers)r.fillText(e.text,e.x,e.y);r.restore(),this.drawEventDividers(r),r.restore();const a=s.TimelineGrid.TimelineGrid.calculateGridOffsets(this.calculator);s.TimelineGrid.TimelineGrid.drawCanvasGrid(r,a),s.TimelineGrid.TimelineGrid.drawCanvasHeaders(r,a,(e=>this.calculator.formatValue(e,a.precision)),this.fontSize,this.headerHeight,75),r.save(),r.scale(window.devicePixelRatio,window.devicePixelRatio),r.clearRect(this.offsetWidth-18,0,18,this.headerHeight),r.restore(),this.didDrawForTest()}drawLayers(e,t){for(const r of this.pathForStyle){const i=r[0],n=r[1];e.save(),e.beginPath(),i.lineWidth&&(e.lineWidth=i.lineWidth,i.borderColor&&(e.strokeStyle=i.borderColor),e.stroke(n)),i.fillStyle&&(e.fillStyle=t?l.ThemeSupport.instance().getComputedValue(i.fillStyle):i.fillStyle,e.fill(n)),e.restore()}}drawEventDividers(e){e.save(),e.lineWidth=1;for(const t of this.eventDividers.keys()){e.strokeStyle=t;for(const r of this.eventDividers.get(t)||[]){e.beginPath();const t=this.timeToPosition(r);e.moveTo(t,0),e.lineTo(t,this.offsetHeight)}e.stroke()}e.restore()}getBarHeight(e){const t=c.RequestTimeRangeNames;switch(e){case t.Connecting:case t.SSL:case t.DNS:case t.Proxy:case t.Blocking:case t.Push:case t.Queueing:return 7;default:return 13}}getSimplifiedBarRange(e,t){const r=this.offsetWidth-this.leftPadding,i=this.calculator.computeBarGraphPercentages(e);return{start:this.leftPadding+Math.floor(i.start/100*r)+t,mid:this.leftPadding+Math.floor(i.middle/100*r)+t,end:this.leftPadding+Math.floor(i.end/100*r)+t}}buildSimplifiedBarLayers(e,t,r){const i=t.request();if(!i)return;const n=.5,o=this.getSimplifiedBarRange(i,n),s=this.getBarHeight();r+=Math.floor(this.rowHeight/2-s/2+1)-.5;const a=this.styleForWaitingResourceType.get(i.resourceType());this.pathForStyle.get(a).rect(o.start,r,o.mid-o.start,s-1);const l=Math.max(2,o.end-o.mid),d=this.styleForDownloadingResourceType.get(i.resourceType());this.pathForStyle.get(d).rect(o.mid,r,l,s-1);let h=null;if(t.hovered()){h=this.calculator.computeBarGraphLabels(i);const t=10,a=e.measureText(h.left).width,d=e.measureText(h.right).width,c=this.pathForStyle.get(this.hoverDetailsStyle);if(a<o.mid-o.start){const e=o.start+(o.mid-o.start-a)/2;this.textLayers.push({text:h.left,x:e,y:r+this.fontSize})}else t+a+this.leftPadding<o.start&&(this.textLayers.push({text:h.left,x:o.start-a-t-1,y:r+this.fontSize}),c.moveTo(o.start-t,r+Math.floor(s/2)),c.arc(o.start,r+Math.floor(s/2),2,0,2*Math.PI),c.moveTo(o.start-t,r+Math.floor(s/2)),c.lineTo(o.start,r+Math.floor(s/2)));const u=o.mid+l+n;if(d<u-o.mid){const e=o.mid+(u-o.mid-d)/2;this.textLayers.push({text:h.right,x:e,y:r+this.fontSize})}else u+t+d<this.offsetWidth-this.leftPadding&&(this.textLayers.push({text:h.right,x:u+t+1,y:r+this.fontSize}),c.moveTo(u,r+Math.floor(s/2)),c.arc(u,r+Math.floor(s/2),2,0,2*Math.PI),c.moveTo(u,r+Math.floor(s/2)),c.lineTo(u+t,r+Math.floor(s/2)))}if(!this.calculator.startAtZero){const t=c.RequestTimingView.calculateRequestTimeRanges(i,0).find((e=>e.name===c.RequestTimeRangeNames.Total)),a=h?e.measureText(h.left).width:0,l=a<o.mid-o.start,d=13,u=h&&!l?a+d:0,p=this.timeToPosition(t.start);if(o.start-u>p){const e=this.pathForStyle.get(this.wiskerStyle);e.moveTo(p,r+Math.floor(s/2)),e.lineTo(o.start-u,r+Math.floor(s/2));const t=s/2;e.moveTo(p+n,r+t/2),e.lineTo(p+n,r+s-t/2-1)}}}buildTimingBarLayers(e,t){const r=e.request();if(!r)return;const i=c.RequestTimingView.calculateRequestTimeRanges(r,0);let n=0;for(const e of i){if(e.name===c.RequestTimeRangeNames.Total||e.name===c.RequestTimeRangeNames.Sending||e.end-e.start==0)continue;const r=this.styleForTimeRangeName.get(e.name),i=this.pathForStyle.get(r),o=r.lineWidth||0,s=this.getBarHeight(e.name),a=t+Math.floor(this.rowHeight/2-s/2)+o/2,l=this.timeToPosition(e.start),d=this.timeToPosition(e.end);i.rect(l+1*n,a,d-l,s-o),n++}}decorateRow(e,t,r){const i=t.backgroundColor();e.save(),e.beginPath(),e.fillStyle=l.ThemeSupport.instance().getComputedValue(i),e.rect(0,r,this.offsetWidth,this.rowHeight),e.fill(),e.restore()}}})),r.register("8Mint",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2016 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.network-waterfall-v-scroll {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  overflow-x: hidden;\n  margin-top: 31px;\n  z-index: 200;\n}\n\n.-theme-with-dark-background .network-waterfall-v-scroll {\n  /**\n  * Waterfall scrollbars are implemented as overflowing elements on top of the\n  * scrollable content. The actual content is a viewport without scrollbars.\n  * When using a dark theme, we should inform Blink that the content is dark,\n  * so that the native scrollbars are colored accordingly.\n  */\n  background: rgb(0 0 0 / 1%);\n}\n\n.network-waterfall-v-scroll.small {\n  margin-top: 27px;\n}\n\n.network-waterfall-v-scroll-content {\n  width: 15px;\n  pointer-events: none;\n}\n\n/*# sourceURL=networkWaterfallColumn.css */\n");var n=i})),r.register("3ZLtp",(function(t,i){e(t.exports,"NetworkOverview",(()=>c)),e(t.exports,"_padding",(()=>p)),e(t.exports,"_bandHeight",(()=>u)),e(t.exports,"RequestTimeRangeNameToColor",(()=>h));var n=r("eQFvP"),o=r("af4Nd"),s=r("5887T"),a=r("h5pc5"),l=r("kBxCb"),d=r("gpw7U");class c extends o.TimelineOverviewPane.TimelineOverviewBase{selectedFilmStripTime;numBands;updateScheduled;highlightedRequest;loadEvents;domContentLoadedEvents;nextBand;bandMap;requestsList;requestsSet;span;filmStripModel;lastBoundary;constructor(){super(),this.selectedFilmStripTime=-1,this.element.classList.add("network-overview"),this.numBands=1,this.updateScheduled=!1,this.highlightedRequest=null,n.TargetManager.TargetManager.instance().addModelListener(n.ResourceTreeModel.ResourceTreeModel,n.ResourceTreeModel.Events.Load,this.loadEventFired,this),n.TargetManager.TargetManager.instance().addModelListener(n.ResourceTreeModel.ResourceTreeModel,n.ResourceTreeModel.Events.DOMContentLoaded,this.domContentLoadedEventFired,this),this.reset()}setHighlightedRequest(e){this.highlightedRequest=e,this.scheduleUpdate()}setFilmStripModel(e){this.filmStripModel=e,this.scheduleUpdate()}selectFilmStripFrame(e){this.selectedFilmStripTime=e,this.scheduleUpdate()}clearFilmStripFrame(){this.selectedFilmStripTime=-1,this.scheduleUpdate()}loadEventFired(e){const t=e.data.loadTime;t&&this.loadEvents.push(1e3*t),this.scheduleUpdate()}domContentLoadedEventFired(e){const{data:t}=e;t&&this.domContentLoadedEvents.push(1e3*t),this.scheduleUpdate()}bandId(e){if(!e||"0"===e)return-1;if(this.bandMap.has(e))return this.bandMap.get(e);const t=this.nextBand++;return this.bandMap.set(e,t),t}updateRequest(e){this.requestsSet.has(e)||(this.requestsSet.add(e),this.requestsList.push(e)),this.scheduleUpdate()}wasShown(){this.onResize()}calculator(){return super.calculator()}onResize(){const e=this.element.offsetWidth,t=this.element.offsetHeight;this.calculator().setDisplayWidth(e),this.resetCanvas();const r=(t-p-1)/u-1|0;this.numBands=r>0?r:1,this.scheduleUpdate()}reset(){this.filmStripModel=null,this.span=1,this.lastBoundary=null,this.nextBand=0,this.bandMap=new Map,this.requestsList=[],this.requestsSet=new Set,this.loadEvents=[],this.domContentLoadedEvents=[],this.resetCanvas()}scheduleUpdate(){!this.updateScheduled&&this.isShowing()&&(this.updateScheduled=!0,this.element.window().requestAnimationFrame(this.update.bind(this)))}update(){this.updateScheduled=!1;const e=this.calculator(),t=new(0,l.NetworkTimeBoundary)(e.minimumBoundary(),e.maximumBoundary());if(!this.lastBoundary||!t.equals(this.lastBoundary)){const t=e.boundarySpan();for(;this.span<t;)this.span*=1.25;e.setBounds(e.minimumBoundary(),e.minimumBoundary()+this.span),this.lastBoundary=new(0,l.NetworkTimeBoundary)(e.minimumBoundary(),e.maximumBoundary())}const r=this.context(),i=new Map,n=p;function o(t){const o=i.get(t);if(!o)return;const a=o.length;r.beginPath(),r.strokeStyle=s.ThemeSupport.instance().getComputedValue("--color-background-opacity-80"),r.lineWidth=g,r.fillStyle=s.ThemeSupport.instance().getComputedValue(h[t]);for(let t=0;t<a;){const i=o[t++]*u+n,s=o[t++];let a=o[t++];a===Number.MAX_VALUE&&(a=e.maximumBoundary());const l=e.computePosition(s),d=e.computePosition(a)+1;r.fillRect(l,i,d-l,u),r.strokeRect(l,i,d-l,u)}}function c(e,t,r,n){let o=i.get(e);o||(o=[],i.set(e,o)),o.push(t,r,n)}const m=this.requestsList,f=m.length;for(let e=0;e<f;++e){const t=m[e],r=this.bandId(t.connectionId),i=-1===r?0:r%this.numBands+1,n=d.RequestTimingView.calculateRequestTimeRanges(t,this.calculator().minimumBoundary());for(let e=0;e<n.length;++e){const t=n[e].name;-1===r&&t!==d.RequestTimeRangeNames.Total||c(t,i,1e3*n[e].start,1e3*n[e].end)}}if(r.clearRect(0,0,this.width(),this.height()),r.save(),r.scale(window.devicePixelRatio,window.devicePixelRatio),r.lineWidth=2,o(d.RequestTimeRangeNames.Total),o(d.RequestTimeRangeNames.Blocking),o(d.RequestTimeRangeNames.Connecting),o(d.RequestTimeRangeNames.ServiceWorker),o(d.RequestTimeRangeNames.ServiceWorkerPreparation),o(d.RequestTimeRangeNames.ServiceWorkerRespondWith),o(d.RequestTimeRangeNames.Push),o(d.RequestTimeRangeNames.Proxy),o(d.RequestTimeRangeNames.DNS),o(d.RequestTimeRangeNames.SSL),o(d.RequestTimeRangeNames.Sending),o(d.RequestTimeRangeNames.Waiting),o(d.RequestTimeRangeNames.Receiving),this.highlightedRequest){const t=5,i=2,o=this.highlightedRequest,a=this.bandId(o.connectionId),l=(-1===a?0:a%this.numBands+1)*u+n,c=d.RequestTimingView.calculateRequestTimeRanges(o,this.calculator().minimumBoundary());r.fillStyle=s.ThemeSupport.instance().getComputedValue("--legacy-selection-bg-color");const p=1e3*c[0].start,g=1e3*c[0].end;r.fillRect(e.computePosition(p)-i,l-t/2-i,e.computePosition(g)-e.computePosition(p)+1+2*i,t*i);for(let i=0;i<c.length;++i){const n=c[i].name;if(-1!==a||n===d.RequestTimeRangeNames.Total){r.beginPath(),r.strokeStyle=s.ThemeSupport.instance().getComputedValue(h[n]),r.lineWidth=t;const o=1e3*c[i].start,a=1e3*c[i].end;r.moveTo(e.computePosition(o)-0,l),r.lineTo(e.computePosition(a)+1,l),r.stroke()}}}const w=this.element.offsetHeight;r.lineWidth=1,r.beginPath(),r.strokeStyle=a.NetworkLogView.getDCLEventColor();for(let t=this.domContentLoadedEvents.length-1;t>=0;--t){const i=Math.round(e.computePosition(this.domContentLoadedEvents[t]))+.5;r.moveTo(i,0),r.lineTo(i,w)}r.stroke(),r.beginPath(),r.strokeStyle=a.NetworkLogView.getLoadEventColor();for(let t=this.loadEvents.length-1;t>=0;--t){const i=Math.round(e.computePosition(this.loadEvents[t]))+.5;r.moveTo(i,0),r.lineTo(i,w)}if(r.stroke(),-1!==this.selectedFilmStripTime){r.lineWidth=2,r.beginPath(),r.strokeStyle=s.ThemeSupport.instance().getComputedValue("--network-frame-divider-color");const t=Math.round(e.computePosition(this.selectedFilmStripTime));r.moveTo(t,0),r.lineTo(t,w),r.stroke()}r.restore()}}const h={[d.RequestTimeRangeNames.Total]:"--override-network-overview-total",[d.RequestTimeRangeNames.Blocking]:"--override-network-overview-blocking",[d.RequestTimeRangeNames.Connecting]:"--override-network-overview-connecting",[d.RequestTimeRangeNames.ServiceWorker]:"--override-network-overview-service-worker",[d.RequestTimeRangeNames.ServiceWorkerPreparation]:"--override-network-overview-service-worker",[d.RequestTimeRangeNames.ServiceWorkerRespondWith]:"--override-network-overview-service-worker-respond-with",[d.RequestTimeRangeNames.Push]:"--override-network-overview-push",[d.RequestTimeRangeNames.Proxy]:"--override-network-overview-proxy",[d.RequestTimeRangeNames.DNS]:"--override-network-overview-dns",[d.RequestTimeRangeNames.SSL]:"--override-network-overview-ssl",[d.RequestTimeRangeNames.Sending]:"--override-network-overview-sending",[d.RequestTimeRangeNames.Waiting]:"--override-network-overview-waiting",[d.RequestTimeRangeNames.Receiving]:"--override-network-overview-receiving",[d.RequestTimeRangeNames.Queueing]:"--override-network-overview-queueing"},u=3,p=5,g=1})),r.register("3juS1",(function(t,i){e(t.exports,"NetworkSearchScope",(()=>h)),e(t.exports,"NetworkSearchResult",(()=>u));var n=r("ixFnt"),o=r("lz7WY"),s=r("g4rSN"),a=r("hE0P3");const l={url:"URL"},d=n.i18n.registerUIStrings("panels/network/NetworkSearchScope.ts",l),c=n.i18n.getLocalizedString.bind(void 0,d);class h{performIndexing(e){queueMicrotask((()=>{e.done()}))}async performSearch(e,t,r,i){const n=[],o=s.NetworkLog.NetworkLog.instance().requests().filter((t=>e.filePathMatchesFileQuery(t.url())));t.setTotalWork(o.length);for(const r of o){const i=this.searchRequest(e,r,t);n.push(i)}const a=(await Promise.all(n)).filter((e=>null!==e));if(t.isCanceled())i(!1);else{for(const e of a.sort(((e,t)=>e.label().localeCompare(t.label()))))e.matchesCount()>0&&r(e);t.done(),i(!0)}}async searchRequest(e,t,r){let i=[];if(t.contentType().isTextType()&&(i=await t.searchInContent(e.query(),!e.ignoreCase(),e.isRegex())),r.isCanceled())return null;const n=[];l(t.url())&&n.push(a.UIRequestLocation.UIRequestLocation.urlMatch(t));for(const e of t.requestHeaders())s(e)&&n.push(a.UIRequestLocation.UIRequestLocation.requestHeaderMatch(t,e));for(const e of t.responseHeaders)s(e)&&n.push(a.UIRequestLocation.UIRequestLocation.responseHeaderMatch(t,e));for(const e of i)n.push(a.UIRequestLocation.UIRequestLocation.bodyMatch(t,e));return r.incrementWorked(),new u(t,n);function s(e){return l(`${e.name}: ${e.value}`)}function l(t){const r=e.ignoreCase()?"i":"",i=e.queries().map((e=>new RegExp(o.StringUtilities.escapeForRegExp(e),r)));let n=0;for(const e of i){const r=t.substr(n).match(e);if(!r||!r.index)return!1;n+=r.index+r[0].length}return!0}}stopSearch(){}}class u{request;locations;constructor(e,t){this.request=e,this.locations=t}matchesCount(){return this.locations.length}label(){return this.request.displayName}description(){const e=this.request.parsedURL;return e?e.urlWithoutScheme():this.request.url()}matchLineContent(e){const t=this.locations[e];if(t.isUrlMatch)return this.request.url();const r=t?.header?.header;return r?r.value:t.searchMatch.lineContent}matchRevealable(e){return this.locations[e]}matchLabel(e){const t=this.locations[e];if(t.isUrlMatch)return c(l.url);const r=t?.header?.header;return r?`${r.name}:`:t.searchMatch.lineNumber+1}}})),r.register("4yJHA",(function(t,i){e(t.exports,"NetworkPanel",(()=>F)),e(t.exports,"displayScreenshotDelay",(()=>E)),e(t.exports,"FilmStripRecorder",(()=>G)),e(t.exports,"ContextMenuProvider",(()=>V)),e(t.exports,"RequestRevealer",(()=>O)),e(t.exports,"RequestIdRevealer",(()=>D)),e(t.exports,"NetworkLogWithFilterRevealer",(()=>W)),e(t.exports,"ActionDelegate",(()=>z)),e(t.exports,"SearchNetworkView",(()=>K)),e(t.exports,"RequestLocationRevealer",(()=>$));var n=r("koSS8"),o=r("e7bLS"),s=r("ixFnt"),a=r("lz7WY"),l=r("eQFvP"),d=r("fMswD"),c=r("g4rSN"),h=r("4gsXY"),u=r("hE0P3"),p=r("cY3yZ"),g=r("af4Nd"),m=r("9z2ZV"),f=r("8fwyZ"),w=r("ehgAU"),b=r("eptiJ"),k=r("9kyN0"),v=r("l7XL4"),C=r("h5pc5"),S=r("3ZLtp"),y=r("95PjZ"),T=r("3juS1"),R=r("kBxCb");const x={close:"Close",search:"Search",clear:"Clear",doNotClearLogOnPageReload:"Do not clear log on page reload / navigation",preserveLog:"Preserve log",disableCacheWhileDevtoolsIsOpen:"Disable cache (while DevTools is open)",disableCache:"Disable cache",networkSettings:"Network settings",showMoreInformationInRequestRows:"Show more information in request rows",useLargeRequestRows:"Use large request rows",showOverviewOfNetworkRequests:"Show overview of network requests",showOverview:"Show overview",groupRequestsByTopLevelRequest:"Group requests by top level request frame",groupByFrame:"Group by frame",captureScreenshotsWhenLoadingA:"Capture screenshots when loading a page",captureScreenshots:"Capture screenshots",importHarFile:"Import `HAR` file...",exportHar:"Export `HAR`...",throttling:"Throttling",hitSToReloadAndCaptureFilmstrip:"Hit {PH1} to reload and capture filmstrip.",revealInNetworkPanel:"Reveal in Network panel",recordingFrames:"Recording frames...",fetchingFrames:"Fetching frames...",moreNetworkConditions:"More network conditions…"},I=s.i18n.registerUIStrings("panels/network/NetworkPanel.ts",x),q=s.i18n.getLocalizedString.bind(void 0,I);let L;class F extends m.Panel.Panel{networkLogShowOverviewSetting;networkLogLargeRowsSetting;networkRecordFilmStripSetting;toggleRecordAction;pendingStopTimer;networkItemView;filmStripView;filmStripRecorder;currentRequest;panelToolbar;rightToolbar;filterBar;settingsPane;showSettingsPaneSetting;filmStripPlaceholderElement;overviewPane;networkOverview;overviewPlaceholderElement;calculator;splitWidget;sidebarLocation;progressBarContainer;networkLogView;fileSelectorElement;detailsWidget;closeButtonElement;preserveLogSetting;recordLogSetting;throttlingSelect;constructor(){super("network"),this.networkLogShowOverviewSetting=n.Settings.Settings.instance().createSetting("networkLogShowOverview",!0),this.networkLogLargeRowsSetting=n.Settings.Settings.instance().createSetting("networkLogLargeRows",!1),this.networkRecordFilmStripSetting=n.Settings.Settings.instance().createSetting("networkRecordFilmStripSetting",!1),this.toggleRecordAction=m.ActionRegistry.ActionRegistry.instance().action("network.toggle-recording"),this.networkItemView=null,this.filmStripView=null,this.filmStripRecorder=null,this.currentRequest=null;const e=new m.Widget.VBox,t=e.contentElement.createChild("div","network-toolbar-container");this.panelToolbar=new m.Toolbar.Toolbar("",t),this.panelToolbar.makeWrappable(!0),this.rightToolbar=new m.Toolbar.Toolbar("",t),this.filterBar=new m.FilterBar.FilterBar("networkPanel",!0),this.filterBar.show(e.contentElement),this.filterBar.addEventListener("Changed",this.handleFilterChanged.bind(this)),this.settingsPane=new m.Widget.HBox,this.settingsPane.element.classList.add("network-settings-pane"),this.settingsPane.show(e.contentElement),this.showSettingsPaneSetting=n.Settings.Settings.instance().createSetting("networkShowSettingsToolbar",!1),this.showSettingsPaneSetting.addChangeListener(this.updateSettingsPaneVisibility.bind(this)),this.updateSettingsPaneVisibility(),this.filmStripPlaceholderElement=e.contentElement.createChild("div","network-film-strip-placeholder"),this.overviewPane=new g.TimelineOverviewPane.TimelineOverviewPane("network"),this.overviewPane.addEventListener(g.TimelineOverviewPane.Events.WindowChanged,this.onWindowChanged.bind(this)),this.overviewPane.element.id="network-overview-panel",this.networkOverview=new(0,S.NetworkOverview),this.overviewPane.setOverviewControls([this.networkOverview]),this.overviewPlaceholderElement=e.contentElement.createChild("div"),this.calculator=new(0,R.NetworkTransferTimeCalculator),this.splitWidget=new m.SplitWidget.SplitWidget(!0,!1,"networkPanelSplitViewState"),this.splitWidget.hideMain(),this.splitWidget.show(e.contentElement),e.setDefaultFocusedChild(this.filterBar);const r=new m.SplitWidget.SplitWidget(!0,!1,"networkPanelSidebarState",225);r.hideSidebar(),r.enableShowModeSaving(),r.show(this.element),this.sidebarLocation=m.ViewManager.ViewManager.instance().createTabbedLocation((async()=>{m.ViewManager.ViewManager.instance().showView("network"),r.showBoth()}),"network-sidebar",!0);const i=this.sidebarLocation.tabbedPane();i.setMinimumSize(100,25),i.element.classList.add("network-tabbed-pane"),i.element.addEventListener("keydown",(e=>{e.key===a.KeyboardUtilities.ESCAPE_KEY&&(r.hideSidebar(),e.consume())}));const o=new m.Toolbar.ToolbarButton(q(x.close),"largeicon-delete");o.addEventListener(m.Toolbar.ToolbarButton.Events.Click,(()=>r.hideSidebar())),i.rightToolbar().appendToolbarItem(o),r.setSidebarWidget(i),r.setMainWidget(e),r.setDefaultFocusedChild(e),this.setDefaultFocusedChild(r),this.progressBarContainer=document.createElement("div"),this.networkLogView=new(0,C.NetworkLogView)(this.filterBar,this.progressBarContainer,this.networkLogLargeRowsSetting),this.splitWidget.setSidebarWidget(this.networkLogView),this.fileSelectorElement=m.UIUtils.createFileSelectorElement(this.networkLogView.onLoadFromFile.bind(this.networkLogView)),e.element.appendChild(this.fileSelectorElement),this.detailsWidget=new m.Widget.VBox,this.detailsWidget.element.classList.add("network-details-view"),this.splitWidget.setMainWidget(this.detailsWidget),this.closeButtonElement=document.createElement("div",{is:"dt-close-button"}),this.closeButtonElement.addEventListener("click",(async()=>{const e=m.ActionRegistry.ActionRegistry.instance().action("network.hide-request-details");e&&await e.execute()}),!1),this.closeButtonElement.style.margin="0 5px",this.networkLogShowOverviewSetting.addChangeListener(this.toggleShowOverview,this),this.networkLogLargeRowsSetting.addChangeListener(this.toggleLargerRequests,this),this.networkRecordFilmStripSetting.addChangeListener(this.toggleRecordFilmStrip,this),this.preserveLogSetting=n.Settings.Settings.instance().moduleSetting("network_log.preserve-log"),this.recordLogSetting=n.Settings.Settings.instance().moduleSetting("network_log.record-log"),this.recordLogSetting.addChangeListener((({data:e})=>this.toggleRecord(e))),this.throttlingSelect=this.createThrottlingConditionsSelect(),this.setupToolbarButtons(r),this.toggleRecord(this.recordLogSetting.get()),this.toggleShowOverview(),this.toggleLargerRequests(),this.toggleRecordFilmStrip(),this.updateUI(),l.TargetManager.TargetManager.instance().addModelListener(l.ResourceTreeModel.ResourceTreeModel,l.ResourceTreeModel.Events.WillReloadPage,this.willReloadPage,this),l.TargetManager.TargetManager.instance().addModelListener(l.ResourceTreeModel.ResourceTreeModel,l.ResourceTreeModel.Events.Load,this.load,this),this.networkLogView.addEventListener(k.Events.RequestSelected,this.onRequestSelected,this),this.networkLogView.addEventListener(k.Events.RequestActivated,this.onRequestActivated,this),c.NetworkLog.NetworkLog.instance().addEventListener(c.NetworkLog.Events.RequestAdded,this.onUpdateRequest,this),c.NetworkLog.NetworkLog.instance().addEventListener(c.NetworkLog.Events.RequestUpdated,this.onUpdateRequest,this),c.NetworkLog.NetworkLog.instance().addEventListener(c.NetworkLog.Events.Reset,this.onNetworkLogReset,this)}static instance(e={forceNew:null}){const{forceNew:t}=e;return L&&!t||(L=new F),L}static revealAndFilter(e){const t=F.instance();let r="";for(const t of e)t.filterType?r+=`${t.filterType}:${t.filterValue} `:r+=`${t.filterValue} `;return t.networkLogView.setTextFilterValue(r),m.ViewManager.ViewManager.instance().showView("network")}static async selectAndShowRequest(e,t,r){const i=F.instance();await i.selectAndActivateRequest(e,t,r)}throttlingSelectForTest(){return this.throttlingSelect}onWindowChanged(e){const t=Math.max(this.calculator.minimumBoundary(),e.data.startTime/1e3),r=Math.min(this.calculator.maximumBoundary(),e.data.endTime/1e3);this.networkLogView.setWindow(t,r)}async searchToggleClick(){const e=m.ActionRegistry.ActionRegistry.instance().action("network.search");e&&await e.execute()}setupToolbarButtons(e){const t=new m.Toolbar.ToolbarToggle(q(x.search),"largeicon-search");function r(){const r=e.showMode()!==m.SplitWidget.ShowMode.OnlyMain;t.setToggled(r),r||t.element.focus()}this.panelToolbar.appendToolbarItem(m.Toolbar.Toolbar.createActionButton(this.toggleRecordAction));const i=new m.Toolbar.ToolbarButton(q(x.clear),"largeicon-clear");i.addEventListener(m.Toolbar.ToolbarButton.Events.Click,(()=>c.NetworkLog.NetworkLog.instance().reset(!0)),this),this.panelToolbar.appendToolbarItem(i),this.panelToolbar.appendSeparator(),this.panelToolbar.appendToolbarItem(this.filterBar.filterButton()),r(),e.addEventListener(m.SplitWidget.Events.ShowModeChanged,r),t.addEventListener(m.Toolbar.ToolbarButton.Events.Click,(()=>{this.searchToggleClick()})),this.panelToolbar.appendToolbarItem(t),this.panelToolbar.appendSeparator(),this.panelToolbar.appendToolbarItem(new m.Toolbar.ToolbarSettingCheckbox(this.preserveLogSetting,q(x.doNotClearLogOnPageReload),q(x.preserveLog))),this.panelToolbar.appendSeparator();const o=new m.Toolbar.ToolbarSettingCheckbox(n.Settings.Settings.instance().moduleSetting("cacheDisabled"),q(x.disableCacheWhileDevtoolsIsOpen),q(x.disableCache));this.panelToolbar.appendToolbarItem(o),this.panelToolbar.appendToolbarItem(this.throttlingSelect);const s=new p.Icon.Icon;s.data={iconName:"network_conditions_icon",color:"rgb(110 110 110)",width:"18px",height:"18px"};const a=new m.Toolbar.ToolbarButton(q(x.moreNetworkConditions),s);a.addEventListener(m.Toolbar.ToolbarButton.Events.Click,(()=>{m.ViewManager.ViewManager.instance().showView("network.config")}),this),this.panelToolbar.appendToolbarItem(a),this.rightToolbar.appendToolbarItem(new m.Toolbar.ToolbarItem(this.progressBarContainer)),this.rightToolbar.appendSeparator(),this.rightToolbar.appendToolbarItem(new m.Toolbar.ToolbarSettingToggle(this.showSettingsPaneSetting,"largeicon-settings-gear",q(x.networkSettings)));const l=new m.Toolbar.Toolbar("",this.settingsPane.element);l.makeVertical(),l.appendToolbarItem(new m.Toolbar.ToolbarSettingCheckbox(this.networkLogLargeRowsSetting,q(x.showMoreInformationInRequestRows),q(x.useLargeRequestRows))),l.appendToolbarItem(new m.Toolbar.ToolbarSettingCheckbox(this.networkLogShowOverviewSetting,q(x.showOverviewOfNetworkRequests),q(x.showOverview)));const d=new m.Toolbar.Toolbar("",this.settingsPane.element);d.makeVertical(),d.appendToolbarItem(new m.Toolbar.ToolbarSettingCheckbox(n.Settings.Settings.instance().moduleSetting("network.group-by-frame"),q(x.groupRequestsByTopLevelRequest),q(x.groupByFrame))),d.appendToolbarItem(new m.Toolbar.ToolbarSettingCheckbox(this.networkRecordFilmStripSetting,q(x.captureScreenshotsWhenLoadingA),q(x.captureScreenshots))),this.panelToolbar.appendSeparator();const h=new m.Toolbar.ToolbarButton(q(x.importHarFile),"largeicon-load");h.addEventListener(m.Toolbar.ToolbarButton.Events.Click,(()=>this.fileSelectorElement.click()),this),this.panelToolbar.appendToolbarItem(h);const u=new m.Toolbar.ToolbarButton(q(x.exportHar),"largeicon-download");u.addEventListener(m.Toolbar.ToolbarButton.Events.Click,(e=>{this.networkLogView.exportAll()}),this),this.panelToolbar.appendToolbarItem(u)}updateSettingsPaneVisibility(){this.settingsPane.element.classList.toggle("hidden",!this.showSettingsPaneSetting.get())}createThrottlingConditionsSelect(){const e=new m.Toolbar.ToolbarComboBox(null,q(x.throttling));return e.setMaxWidth(160),f.ThrottlingManager.throttlingManager().decorateSelectWithNetworkThrottling(e.selectElement()),e}toggleRecord(e){this.toggleRecordAction.setToggled(e),this.recordLogSetting.get()!==e&&this.recordLogSetting.set(e),this.networkLogView.setRecording(e),!e&&this.filmStripRecorder&&this.filmStripRecorder.stopRecording(this.filmStripAvailable.bind(this))}filmStripAvailable(e){if(!e)return;const t=this.networkLogView.timeCalculator();this.filmStripView&&this.filmStripView.setModel(e,1e3*t.minimumBoundary(),1e3*t.boundarySpan()),this.networkOverview.setFilmStripModel(e);const r=e.frames().map((function(e){return e.timestamp/1e3}));this.networkLogView.addFilmStripFrames(r)}onNetworkLogReset(e){const{clearIfPreserved:t}=e.data;b.BlockedURLsPane.reset(),this.preserveLogSetting.get()&&!t||(this.calculator.reset(),this.overviewPane.reset()),this.filmStripView&&this.resetFilmStripView()}willReloadPage(){this.pendingStopTimer&&(clearTimeout(this.pendingStopTimer),delete this.pendingStopTimer),this.isShowing()&&this.filmStripRecorder&&this.filmStripRecorder.startRecording()}load(){this.filmStripRecorder&&this.filmStripRecorder.isRecording()&&(this.pendingStopTimer=window.setTimeout(this.stopFilmStripRecording.bind(this),E))}stopFilmStripRecording(){this.filmStripRecorder&&this.filmStripRecorder.stopRecording(this.filmStripAvailable.bind(this)),delete this.pendingStopTimer}toggleLargerRequests(){this.updateUI()}toggleShowOverview(){this.networkLogShowOverviewSetting.get()?this.overviewPane.show(this.overviewPlaceholderElement):this.overviewPane.detach(),this.doResize()}toggleRecordFilmStrip(){const e=this.networkRecordFilmStripSetting.get();e&&!this.filmStripRecorder&&(this.filmStripView=new g.FilmStripView.FilmStripView,this.filmStripView.setMode(g.FilmStripView.Modes.FrameBased),this.filmStripView.element.classList.add("network-film-strip"),this.filmStripRecorder=new G(this.networkLogView.timeCalculator(),this.filmStripView),this.filmStripView.show(this.filmStripPlaceholderElement),this.filmStripView.addEventListener(g.FilmStripView.Events.FrameSelected,this.onFilmFrameSelected,this),this.filmStripView.addEventListener(g.FilmStripView.Events.FrameEnter,this.onFilmFrameEnter,this),this.filmStripView.addEventListener(g.FilmStripView.Events.FrameExit,this.onFilmFrameExit,this),this.resetFilmStripView()),!e&&this.filmStripRecorder&&(this.filmStripView&&this.filmStripView.detach(),this.filmStripView=null,this.filmStripRecorder=null)}resetFilmStripView(){const e=m.ShortcutRegistry.ShortcutRegistry.instance().shortcutsForAction("inspector_main.reload")[0];this.filmStripView&&(this.filmStripView.reset(),e&&this.filmStripView.setStatusText(q(x.hitSToReloadAndCaptureFilmstrip,{PH1:e.title()})))}elementsToRestoreScrollPositionsFor(){return this.networkLogView.elementsToRestoreScrollPositionsFor()}wasShown(){m.Context.Context.instance().setFlavor(F,this),this.registerCSSFiles([y.default]),o.userMetrics.panelLoaded("network","DevTools.Launch.Network")}willHide(){m.Context.Context.instance().setFlavor(F,null)}revealAndHighlightRequest(e){this.hideRequestPanel(),e&&this.networkLogView.revealAndHighlightRequest(e)}revealAndHighlightRequestWithId(e){this.hideRequestPanel(),e&&this.networkLogView.revealAndHighlightRequestWithId(e)}async selectAndActivateRequest(e,t,r){return await m.ViewManager.ViewManager.instance().showView("network"),this.networkLogView.selectRequest(e,r),this.showRequestPanel(t),this.networkItemView}handleFilterChanged(){this.hideRequestPanel()}onRowSizeChanged(){this.updateUI()}onRequestSelected(e){const t=e.data;this.currentRequest=t,this.networkOverview.setHighlightedRequest(t),this.updateNetworkItemView()}onRequestActivated(e){const{showPanel:t,tab:r,takeFocus:i}=e.data;t?this.showRequestPanel(r,i):this.hideRequestPanel()}showRequestPanel(e,t){if(this.splitWidget.showMode()!==m.SplitWidget.ShowMode.Both||e||t){if(this.clearNetworkItemView(),this.currentRequest){const r=this.createNetworkItemView(e);r&&t&&r.focus()}this.updateUI()}}hideRequestPanel(){this.clearNetworkItemView(),this.splitWidget.hideMain(),this.updateUI()}updateNetworkItemView(){this.splitWidget.showMode()===m.SplitWidget.ShowMode.Both&&(this.clearNetworkItemView(),this.createNetworkItemView(),this.updateUI())}clearNetworkItemView(){this.networkItemView&&(this.networkItemView.detach(),this.networkItemView=null)}createNetworkItemView(e){if(this.currentRequest)return this.networkItemView=new(0,v.NetworkItemView)(this.currentRequest,this.networkLogView.timeCalculator(),e),this.networkItemView.leftToolbar().appendToolbarItem(new m.Toolbar.ToolbarItem(this.closeButtonElement)),this.networkItemView.show(this.detailsWidget.element),this.splitWidget.showBoth(),this.networkItemView}updateUI(){this.detailsWidget&&this.detailsWidget.element.classList.toggle("network-details-view-tall-header",this.networkLogLargeRowsSetting.get()),this.networkLogView&&this.networkLogView.switchViewMode(!this.splitWidget.isResizable())}appendApplicableItems(e,t,r){function i(e){m.ViewManager.ViewManager.instance().showView("network").then(this.networkLogView.resetFilter.bind(this.networkLogView)).then(this.revealAndHighlightRequest.bind(this,e))}function n(e){t.revealSection().appendItem(q(x.revealInNetworkPanel),i.bind(this,e))}if(e.target.isSelfOrDescendant(this.element))return;if(r instanceof l.Resource.Resource){const e=r;return void(e.request&&n.call(this,e.request))}if(r instanceof h.UISourceCode.UISourceCode){const e=r,t=d.ResourceUtils.resourceForURL(e.url());return void(t&&t.request&&n.call(this,t.request))}if(!(r instanceof l.NetworkRequest.NetworkRequest))return;const o=r;this.networkItemView&&this.networkItemView.isShowing()&&this.networkItemView.request()===o||n.call(this,o)}onFilmFrameSelected(e){const t=e.data;this.overviewPane.setWindowTimes(0,t)}onFilmFrameEnter(e){const t=e.data;this.networkOverview.selectFilmStripFrame(t),this.networkLogView.selectFilmStripFrame(t/1e3)}onFilmFrameExit(){this.networkOverview.clearFilmStripFrame(),this.networkLogView.clearFilmStripFrame()}onUpdateRequest(e){const t=e.data;this.calculator.updateBoundaries(t),this.overviewPane.setBounds(1e3*this.calculator.minimumBoundary(),1e3*this.calculator.maximumBoundary()),this.networkOverview.updateRequest(t),this.overviewPane.scheduleUpdate()}resolveLocation(e){return"network-sidebar"===e?this.sidebarLocation:null}}const E=1e3;let A,N,P,B,U,H,M;class V{static instance(e={forceNew:null}){const{forceNew:t}=e;return A&&!t||(A=new V),A}appendApplicableItems(e,t,r){F.instance().appendApplicableItems(e,t,r)}}class O{static instance(e={forceNew:null}){const{forceNew:t}=e;return N&&!t||(N=new O),N}reveal(e){if(!(e instanceof l.NetworkRequest.NetworkRequest))return Promise.reject(new Error("Internal error: not a network request"));const t=F.instance();return m.ViewManager.ViewManager.instance().showView("network").then(t.revealAndHighlightRequest.bind(t,e))}}class D{static instance(e={forceNew:null}){const{forceNew:t}=e;return P&&!t||(P=new D),P}reveal(e){if(!(e instanceof u.NetworkRequestId.NetworkRequestId))return Promise.reject(new Error("Internal error: not a network request ID"));const t=F.instance();return m.ViewManager.ViewManager.instance().showView("network").then(t.revealAndHighlightRequestWithId.bind(t,e))}}class W{static instance(e={forceNew:null}){const{forceNew:t}=e;return B&&!t||(B=new W),B}reveal(e){return e instanceof u.UIFilter.UIRequestFilter?F.revealAndFilter(e.filters):Promise.reject(new Error("Internal error: not a UIRequestFilter"))}}class G{tracingManager;resourceTreeModel;timeCalculator;filmStripView;tracingModel;callback;constructor(e,t){this.tracingManager=null,this.resourceTreeModel=null,this.timeCalculator=e,this.filmStripView=t,this.tracingModel=null,this.callback=null}traceEventsCollected(e){this.tracingModel&&this.tracingModel.addEvents(e)}tracingComplete(){this.tracingModel&&this.tracingManager&&(this.tracingModel.tracingComplete(),this.tracingManager=null,this.callback&&this.callback(new l.FilmStripModel.FilmStripModel(this.tracingModel,1e3*this.timeCalculator.minimumBoundary())),this.callback=null,this.resourceTreeModel&&this.resourceTreeModel.resumeReload(),this.resourceTreeModel=null)}tracingBufferUsage(){}eventsRetrievalProgress(e){}startRecording(){this.filmStripView.reset(),this.filmStripView.setStatusText(q(x.recordingFrames));const e=l.TargetManager.TargetManager.instance().models(l.TracingManager.TracingManager);!this.tracingManager&&e.length&&(this.tracingManager=e[0],this.tracingManager&&(this.resourceTreeModel=this.tracingManager.target().model(l.ResourceTreeModel.ResourceTreeModel),this.tracingModel&&this.tracingModel.dispose(),this.tracingModel=new l.TracingModel.TracingModel(new d.TempFile.TempFileBackingStorage),this.tracingManager.start(this,"-*,disabled-by-default-devtools.screenshot",""),o.userMetrics.actionTaken(o.UserMetrics.Action.FilmStripStartedRecording)))}isRecording(){return Boolean(this.tracingManager)}stopRecording(e){this.tracingManager&&(this.tracingManager.stop(),this.resourceTreeModel&&this.resourceTreeModel.suspendReload(),this.callback=e,this.filmStripView.setStatusText(q(x.fetchingFrames)))}}class z{static instance(e={forceNew:null}){const{forceNew:t}=e;return U&&!t||(U=new z),U}handleAction(e,t){const r=m.Context.Context.instance().flavor(F);if(console.assert(Boolean(r&&r instanceof F)),!r)return!1;switch(t){case"network.toggle-recording":return r.toggleRecord(!r.recordLogSetting.get()),!0;case"network.hide-request-details":return!!r.networkItemView&&(r.hideRequestPanel(),r.networkLogView.resetFocus(),!0);case"network.search":{const e=m.InspectorView.InspectorView.instance().element.window().getSelection();if(e){let t="";return e.rangeCount&&(t=e.toString().replace(/\r?\n.*/,"")),K.openSearch(t),!0}}}return!1}}class ${static instance(e={forceNew:null}){const{forceNew:t}=e;return H&&!t||(H=new $),H}async reveal(e){const t=e,r=await F.instance().selectAndActivateRequest(t.request,t.tab,t.filterOptions);r&&(t.searchMatch&&await r.revealResponseBody(t.searchMatch.lineNumber),t.header&&r.revealHeader(t.header.section,t.header.header?.name))}}class K extends w.SearchView.SearchView{constructor(){super("network")}static instance(e={forceNew:null}){const{forceNew:t}=e;return M&&!t||(M=new K),M}static async openSearch(e,t){await m.ViewManager.ViewManager.instance().showView("network.search-network-tab");const r=K.instance();return r.toggle(e,Boolean(t)),r}createScope(){return new(0,T.NetworkSearchScope)}}})),r.register("95PjZ",(function(t,r){e(t.exports,"default",(()=>n));const i=new CSSStyleSheet;i.replaceSync('/*\n * Copyright (C) 2006, 2007, 2008 Apple Inc.  All rights reserved.\n * Copyright (C) 2009 Anthony Ricaud <<EMAIL>>\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions\n * are met:\n *\n * 1.  Redistributions of source code must retain the above copyright\n *     notice, this list of conditions and the following disclaimer.\n * 2.  Redistributions in binary form must reproduce the above copyright\n *     notice, this list of conditions and the following disclaimer in the\n *     documentation and/or other materials provided with the distribution.\n * 3.  Neither the name of Apple Computer, Inc. ("Apple") nor the names of\n *     its contributors may be used to endorse or promote products derived\n *     from this software without specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY APPLE AND ITS CONTRIBUTORS "AS IS" AND ANY\n * EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n * DISCLAIMED. IN NO EVENT SHALL APPLE OR ITS CONTRIBUTORS BE LIABLE FOR ANY\n * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\n * THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\n:root {\n  --override-network-overview-total: #919191;\n  --override-network-overview-blocking: #858585;\n  --override-network-overview-connecting: #d68100;\n  --override-network-overview-service-worker: #d68100;\n  --override-network-overview-service-worker-respond-with: #00a3a3;\n  --override-network-overview-push: #0099e0;\n  --override-network-overview-proxy: #a1887f;\n  --override-network-overview-dns: #009688;\n  --override-network-overview-ssl: #b52dcd;\n  --override-network-overview-sending: #74979a;\n  --override-network-overview-waiting: #00a846;\n  --override-network-overview-receiving: #0299de;\n  --override-network-overview-queueing: #fff;\n  --network-grid-default-color: rgb(255 255 255 / 100%);\n  --network-grid-stripe-color: rgb(245 245 245 / 100%);\n  --network-grid-navigation-color: rgb(221 238 255 / 100%);\n  --network-grid-hovered-color: rgb(235 242 252 / 70%);\n  --network-grid-initiator-path-color: rgb(58 217 58 / 40%);\n  --network-grid-initiated-path-color: rgb(217 58 58 / 40%);\n  --network-grid-selected-color: #dadce0;\n  --network-grid-focus-selected-color: var(--legacy-selection-bg-color);\n  --network-grid-focus-selected-color-has-error: #fad2cf;\n  --network-grid-from-frame-color: rgb(224 247 250 / 40%);\n  --network-grid-is-product-color: rgb(255 252 225 / 60%);\n  --network-frame-divider-color: #fccc49;\n}\n\n.-theme-with-dark-background {\n  --network-grid-default-color: rgb(36 36 36 / 100%);\n  --network-grid-stripe-color: rgb(41 41 41 / 100%);\n  --network-grid-navigation-color: rgb(221 238 255 / 100%);\n  --network-grid-hovered-color: rgb(20 37 63 / 70%);\n  --network-grid-initiator-path-color: rgb(58 217 58 / 40%);\n  --network-grid-initiated-path-color: rgb(217 58 58 / 40%);\n  --network-grid-selected-color: #454545;\n  --network-grid-focus-selected-color: var(--legacy-selection-bg-color);\n  --network-grid-focus-selected-color-has-error: #482422;\n  --network-grid-from-frame-color: rgb(224 247 250 / 40%);\n  --network-grid-is-product-color: rgb(255 252 225 / 60%);\n}\n\n.network-details-view {\n  background: rgb(203 203 203); /* stylelint-disable-line plugin/use_theme_colors */\n  /* See: crbug.com/1152736 for color variable migration. */\n}\n\n.network-details-view-tall-header {\n  border-top: 4px solid var(--color-background-elevation-1);\n}\n\n.network-item-view {\n  display: flex;\n  background: var(--color-background);\n}\n\n.network-item-preview-toolbar {\n  border-top: 1px solid var(--color-details-hairline);\n  background-color: var(--color-background-elevation-1);\n}\n\n.resource-timing-view {\n  display: block;\n  margin: 6px;\n  color: var(--color-text-primary);\n  overflow: auto;\n  background-color: var(--color-background);\n}\n\n.resource-timing-table {\n  width: 100% !important; /* stylelint-disable-line declaration-no-important */\n}\n\n#network-overview-panel {\n  flex: none;\n  position: relative;\n}\n\n#network-overview-container {\n  overflow: hidden;\n  flex: auto;\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  border-bottom: 1px solid var(--color-details-hairline);\n}\n\n#network-overview-container canvas {\n  width: 100%;\n  height: 100%;\n}\n\n.resources-dividers-label-bar {\n  background-color: var(--color-background);\n}\n\n#network-overview-grid .resources-dividers-label-bar {\n  pointer-events: auto;\n}\n\n.network .network-overview {\n  flex: 0 0 60px;\n}\n\n.network-overview .resources-dividers-label-bar .resources-divider {\n  background-color: transparent;\n}\n\n.network-overview .resources-dividers {\n  z-index: 250;\n}\n\n.request-view.html iframe {\n  width: 100%;\n  height: 100%;\n  position: absolute;\n}\n\n.network-film-strip {\n  border-bottom: solid 1px var(--color-details-hairline);\n  flex: none !important; /* stylelint-disable-line declaration-no-important */\n}\n\n.network-film-strip-placeholder {\n  flex-shrink: 0;\n}\n\n.network-tabbed-pane {\n  background-color: var(--color-background-elevation-1);\n}\n\n.network-settings-pane {\n  flex: none;\n  background-color: var(--color-background-elevation-1);\n}\n\n.network-settings-pane .toolbar {\n  flex: 1 1;\n}\n\n.network-toolbar-container {\n  display: flex;\n  flex: none;\n}\n\n.network-toolbar-container > :first-child {\n  flex: 1 1 auto;\n}\n\n.panel.network .toolbar {\n  background-color: var(--color-background-elevation-1);\n  border-bottom: var(--legacy-divider-border);\n}\n\n@media (forced-colors: active) {\n  .panel.network .toolbar {\n    background-color: canvas;\n  }\n}\n\n/*# sourceURL=networkPanel.css */\n');var n=i}));
//# sourceMappingURL=network.9bc08839.js.map
