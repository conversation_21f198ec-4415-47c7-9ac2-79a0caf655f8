function e(e,t,s,n){Object.defineProperty(e,t,{get:s,set:n,enumerable:!0,configurable:!0})}var t=("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}).parcelRequire20b5;t.register("jHa8d",(function(s,n){e(s.exports,"ConsoleContextSelector",(()=>t("bS6IU"))),e(s.exports,"ConsoleFilter",(()=>t("i1dmU"))),e(s.exports,"ConsoleFormat",(()=>t("9WUcz"))),e(s.exports,"ConsolePanel",(()=>t("2XTyv"))),e(s.exports,"ConsolePinPane",(()=>t("fhQNs"))),e(s.exports,"ConsolePrompt",(()=>t("lygHa"))),e(s.exports,"ConsoleSidebar",(()=>t("5hKKG"))),e(s.exports,"ConsoleView",(()=>t("i5ql7"))),e(s.exports,"ConsoleViewMessage",(()=>t("91fou"))),e(s.exports,"ConsoleViewport",(()=>t("cXqFI"))),e(s.exports,"ErrorStackParser",(()=>t("iibJW")));t("bS6IU"),t("i1dmU"),t("9WUcz"),t("fhQNs"),t("5hKKG"),t("cXqFI"),t("91fou"),t("lygHa"),t("i5ql7"),t("2XTyv"),t("bS6IU"),t("i1dmU"),t("9WUcz"),t("2XTyv"),t("fhQNs"),t("lygHa"),t("5hKKG"),t("i5ql7"),t("91fou"),t("cXqFI"),t("iibJW")})),t.register("bS6IU",(function(s,n){e(s.exports,"ConsoleContextSelector",(()=>m));var o=t("koSS8"),i=t("ixFnt"),r=t("lz7WY"),l=t("eQFvP"),a=t("9z2ZV"),c=t("hZ9H6");const h={javascriptContextNotSelected:"JavaScript context: Not selected",extension:"Extension",javascriptContextS:"JavaScript context: {PH1}"},d=i.i18n.registerUIStrings("panels/console/ConsoleContextSelector.ts",h),u=i.i18n.getLocalizedString.bind(void 0,d);class m{items;dropDown;toolbarItemInternal;constructor(){this.items=new a.ListModel.ListModel,this.dropDown=new a.SoftDropDown.SoftDropDown(this.items,this),this.dropDown.setRowHeight(36),this.toolbarItemInternal=new a.Toolbar.ToolbarItem(this.dropDown.element),this.toolbarItemInternal.setEnabled(!1),this.toolbarItemInternal.setTitle(u(h.javascriptContextNotSelected)),this.items.addEventListener(a.ListModel.Events.ItemsReplaced,(()=>this.toolbarItemInternal.setEnabled(Boolean(this.items.length)))),this.toolbarItemInternal.element.classList.add("toolbar-has-dropdown"),l.TargetManager.TargetManager.instance().addModelListener(l.RuntimeModel.RuntimeModel,l.RuntimeModel.Events.ExecutionContextCreated,this.onExecutionContextCreated,this),l.TargetManager.TargetManager.instance().addModelListener(l.RuntimeModel.RuntimeModel,l.RuntimeModel.Events.ExecutionContextChanged,this.onExecutionContextChanged,this),l.TargetManager.TargetManager.instance().addModelListener(l.RuntimeModel.RuntimeModel,l.RuntimeModel.Events.ExecutionContextDestroyed,this.onExecutionContextDestroyed,this),l.TargetManager.TargetManager.instance().addModelListener(l.ResourceTreeModel.ResourceTreeModel,l.ResourceTreeModel.Events.FrameNavigated,this.frameNavigated,this),a.Context.Context.instance().addFlavorChangeListener(l.RuntimeModel.ExecutionContext,this.executionContextChangedExternally,this),a.Context.Context.instance().addFlavorChangeListener(l.DebuggerModel.CallFrame,this.callFrameSelectedInUI,this),l.TargetManager.TargetManager.instance().observeModels(l.RuntimeModel.RuntimeModel,this),l.TargetManager.TargetManager.instance().addModelListener(l.DebuggerModel.DebuggerModel,l.DebuggerModel.Events.CallFrameSelected,this.callFrameSelectedInModel,this)}toolbarItem(){return this.toolbarItemInternal}highlightedItemChanged(e,t,s,n){if(l.OverlayModel.OverlayModel.hideDOMNodeHighlight(),t&&t.frameId){const e=l.FrameManager.FrameManager.instance().getFrame(t.frameId);e&&!e.isTopFrame()&&e.highlight()}s&&s.classList.remove("highlighted"),n&&n.classList.add("highlighted")}titleFor(e){const t=e.target(),s=e.label();let n=s?t.decorateLabel(s):"";if(e.frameId){const s=t.model(l.ResourceTreeModel.ResourceTreeModel),o=s&&s.frameForId(e.frameId);o&&(n=n||o.displayName())}return n=n||e.origin,n}depthFor(e){let t=e.target(),s=0;if(e.isDefault||s++,e.frameId){let n=l.FrameManager.FrameManager.instance().getFrame(e.frameId);for(;n;)n=n.parentFrame(),n&&(s++,t=n.resourceTreeModel().target())}let n=0,o=t.parentTarget();for(;o&&t.type()!==l.Target.Type.ServiceWorker;)n++,t=o,o=t.parentTarget();return s+=n,s}executionContextCreated(e){this.items.insertWithComparator(e,e.runtimeModel.executionContextComparator()),e===a.Context.Context.instance().flavor(l.RuntimeModel.ExecutionContext)&&this.dropDown.selectItem(e)}onExecutionContextCreated(e){const t=e.data;this.executionContextCreated(t)}onExecutionContextChanged(e){const t=e.data;-1!==this.items.indexOf(t)&&(this.executionContextDestroyed(t),this.executionContextCreated(t))}executionContextDestroyed(e){const t=this.items.indexOf(e);-1!==t&&this.items.remove(t)}onExecutionContextDestroyed(e){const t=e.data;this.executionContextDestroyed(t)}executionContextChangedExternally({data:e}){this.dropDown.selectItem(e)}isTopContext(e){if(!e||!e.isDefault)return!1;const t=e.target().model(l.ResourceTreeModel.ResourceTreeModel),s=e.frameId&&t&&t.frameForId(e.frameId);return!!s&&s.isTopFrame()}hasTopContext(){return this.items.some((e=>this.isTopContext(e)))}modelAdded(e){e.executionContexts().forEach(this.executionContextCreated,this)}modelRemoved(e){for(let t=this.items.length-1;t>=0;t--)this.items.at(t).runtimeModel===e&&this.executionContextDestroyed(this.items.at(t))}createElementForItem(e){const t=document.createElement("div"),s=a.Utils.createShadowRootWithCoreStyles(t,{cssFile:[c.default],delegatesFocus:void 0}),n=s.createChild("div","title");a.UIUtils.createTextChild(n,r.StringUtilities.trimEndWithMaxLength(this.titleFor(e),100));const o=s.createChild("div","subtitle");return a.UIUtils.createTextChild(o,this.subtitleFor(e)),t.style.paddingLeft=8+15*this.depthFor(e)+"px",t}subtitleFor(e){const t=e.target();let s=null;if(e.frameId){const n=t.model(l.ResourceTreeModel.ResourceTreeModel);s=n&&n.frameForId(e.frameId)}if(e.origin.startsWith("chrome-extension://"))return u(h.extension);const n=s&&s.sameTargetParentFrame();if(!s||!n||n.securityOrigin!==e.origin){const t=o.ParsedURL.ParsedURL.fromString(e.origin);if(t)return t.domain()}if(s&&s.securityOrigin){const e=new o.ParsedURL.ParsedURL(s.securityOrigin).domain();if(e)return e}return"IFrame"}isItemSelectable(e){const t=e.debuggerModel.selectedCallFrame(),s=t&&t.script.executionContext();return!s||e===s}itemSelected(e){this.toolbarItemInternal.element.classList.toggle("highlight",!this.isTopContext(e)&&this.hasTopContext());const t=e?u(h.javascriptContextS,{PH1:this.titleFor(e)}):u(h.javascriptContextNotSelected);this.toolbarItemInternal.setTitle(t),a.Context.Context.instance().setFlavor(l.RuntimeModel.ExecutionContext,e)}callFrameSelectedInUI(){const e=a.Context.Context.instance().flavor(l.DebuggerModel.CallFrame),t=e&&e.script.executionContext();t&&a.Context.Context.instance().setFlavor(l.RuntimeModel.ExecutionContext,t)}callFrameSelectedInModel(e){const t=e.data;for(const e of this.items)e.debuggerModel===t&&this.dropDown.refreshItem(e)}frameNavigated(e){const t=e.data,s=t.resourceTreeModel().target().model(l.RuntimeModel.RuntimeModel);if(s)for(const e of s.executionContexts())t.id===e.frameId&&this.dropDown.refreshItem(e)}}})),t.register("hZ9H6",(function(t,s){e(t.exports,"default",(()=>o));const n=new CSSStyleSheet;n.replaceSync("/*\n * Copyright 2017 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  padding: 2px 1px 2px 2px;\n  white-space: nowrap;\n  display: flex;\n  flex-direction: column;\n  height: 36px;\n  justify-content: center;\n  overflow-y: auto;\n}\n\n.title {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  flex-grow: 0;\n}\n\n.badge {\n  pointer-events: none;\n  margin-right: 4px;\n  display: inline-block;\n  height: 15px;\n}\n\n.subtitle {\n  color: var(--color-text-secondary);\n  margin-right: 3px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  flex-grow: 0;\n}\n\n:host(.highlighted) .subtitle {\n  color: inherit;\n}\n\n/*# sourceURL=consoleContextSelector.css */\n");var o=n})),t.register("i1dmU",(function(s,n){e(s.exports,"ConsoleFilter",(()=>a)),e(s.exports,"FilterType",(()=>o));var o,i,r=t("eQFvP"),l=t("7f6zc");class a{name;parsedFilters;executionContext;levelsMask;constructor(e,t,s,n){this.name=e,this.parsedFilters=t,this.executionContext=s,this.levelsMask=n||a.defaultLevelsFilterValue()}static allLevelsFilterValue(){const e={},t={Verbose:"verbose",Info:"info",Warning:"warning",Error:"error"};for(const s of Object.values(t))e[s]=!0;return e}static defaultLevelsFilterValue(){const e=a.allLevelsFilterValue();return e.verbose=!1,e}static singleLevelMask(e){const t={};return t[e]=!0,t}clone(){const e=this.parsedFilters.map(l.TextUtils.FilterParser.cloneFilter),t=Object.assign({},this.levelsMask);return new a(this.name,e,this.executionContext,t)}shouldBeVisible(e){const t=e.consoleMessage();return(!this.executionContext||this.executionContext.runtimeModel===t.runtimeModel()&&this.executionContext.id===t.getExecutionContextId())&&(t.type===r.ConsoleModel.FrontendMessageType.Command||t.type===r.ConsoleModel.FrontendMessageType.Result||"endGroup"===t.type||!(t.level&&!this.levelsMask[t.level])&&(this.applyFilter(e)||this.parentGroupHasMatch(e.consoleGroup())))}parentGroupHasMatch(e){return null!==e&&(this.applyFilter(e)||this.parentGroupHasMatch(e.consoleGroup()))}applyFilter(e){const t=e.consoleMessage();for(const n of this.parsedFilters)if(n.key)switch(n.key){case o.Context:if(!s(n,t.context,!1))return!1;break;case o.Source:if(!s(n,t.source?r.ConsoleModel.MessageSourceDisplayName.get(t.source):t.source,!0))return!1;break;case o.Url:if(!s(n,t.url,!1))return!1}else{if(n.regex&&e.matchesFilterRegex(n.regex)===n.negative)return!1;if(n.text&&e.matchesFilterText(n.text)===n.negative)return!1}return!0;function s(e,t,s){if(!e.text)return Boolean(t)===e.negative;if(!t)return!e.text==!e.negative;const n=e.text.toLowerCase(),o=t.toLowerCase();return(!s||o===n!==e.negative)&&!(!s&&o.includes(n)===e.negative)}}}(i=o||(o={})).Context="context",i.Source="source",i.Url="url"})),t.register("9WUcz",(function(t,s){e(t.exports,"format",(()=>i)),e(t.exports,"updateStyle",(()=>r));const n=["black","red","green","yellow","blue","magenta","cyan","gray"],o=["darkgray","lightred","lightgreen","lightyellow","lightblue","lightmagenta","lightcyan","white"],i=(e,t)=>{const s=[],i=new Map;function r(e){const t=i.get("text-decoration")??"";t.includes(e)||i.set("text-decoration",`${t} ${e}`)}function l(e){const t=i.get("text-decoration")?.replace(` ${e}`,"");t?i.set("text-decoration",t):i.delete("text-decoration")}function a(e){e&&(s.length&&"string"===s[s.length-1].type?s[s.length-1].value+=e:s.push({type:"string",value:e}))}let c=0;const h=/%([%_Oocsdfi])|\x1B\[([\d;]*)m/;for(let d=h.exec(e);null!==d;d=h.exec(e)){let h;a(d.input.substring(0,d.index));const u=d[1];switch(u){case"%":a("%"),h="";break;case"s":if(c<t.length){const{description:e}=t[c++];h=e??""}break;case"c":if(c<t.length){const e="style",n=t[c++].description??"";s.push({type:e,value:n}),h=""}break;case"o":case"O":if(c<t.length){const e="O"===u?"generic":"optimal",n=t[c++];s.push({type:e,value:n}),h=""}break;case"_":c<t.length&&(c++,h="");break;case"d":case"f":case"i":if(c<t.length){const{value:e}=t[c++];h="number"!=typeof e?NaN:e,"f"!==u&&(h=Math.floor(h))}break;case void 0:{const e=(d[2]||"0").split(";").map((e=>e?parseInt(e,10):0));for(;e.length;){const t=e.shift();switch(t){case 0:i.clear();break;case 1:i.set("font-weight","bold");break;case 2:i.set("font-weight","lighter");break;case 3:i.set("font-style","italic");break;case 4:r("underline");break;case 9:r("line-through");break;case 22:i.delete("font-weight");break;case 23:i.delete("font-style");break;case 24:l("underline");break;case 29:l("line-through");break;case 38:case 48:if(2===e.shift()){const s=e.shift()??0,n=e.shift()??0,o=e.shift()??0;i.set(38===t?"color":"background",`rgb(${s},${n},${o})`)}break;case 39:case 49:i.delete(39===t?"color":"background");break;case 53:r("overline");break;case 55:l("overline");break;default:{const e=n[t-30]??o[t-90];if(void 0!==e)i.set("color",`var(--console-color-${e})`);else{const e=n[t-40]??o[t-100];void 0!==e&&i.set("background-color",`var(--console-color-${e})`)}break}}}const t=[...i.entries()].map((([e,t])=>`${e}:${t.trimStart()}`)).join(";"),a="style";s.push({type:a,value:t}),h="";break}}void 0===h&&(a(d[0]),h=""),e=h+d.input.substring(d.index+d[0].length)}return a(e),{tokens:s,args:t.slice(c)}},r=(e,t)=>{const s=["background","border","color","font","line","margin","padding","text"],n=["chrome","resource","about","app","http","https","ftp","file"];e.clear();const o=document.createElement("span");o.setAttribute("style",t);for(const t of o.style){if(!s.some((e=>t.startsWith(e)||t.startsWith(`-webkit-${e}`))))continue;const i=o.style.getPropertyValue(t);n.some((e=>i.includes(e+":")))||e.set(t,{value:i,priority:o.style.getPropertyPriority(t)})}}})),t.register("fhQNs",(function(s,n){e(s.exports,"ConsolePinPane",(()=>v)),e(s.exports,"ConsolePin",(()=>b));var o=t("koSS8"),i=t("ixFnt"),r=t("eQFvP"),l=t("39160"),a=t("hi0Sq"),c=t("R6KC8"),h=t("eKcu2"),d=t("9z2ZV"),u=t("dRNdB");const m={removeExpression:"Remove expression",removeAllExpressions:"Remove all expressions",removeExpressionS:"Remove expression: {PH1}",removeBlankExpression:"Remove blank expression",liveExpressionEditor:"Live expression editor",expression:"Expression",evaluateAllowingSideEffects:"Evaluate, allowing side effects",notAvailable:"not available"},p=i.i18n.registerUIStrings("panels/console/ConsolePinPane.ts",m),g=i.i18n.getLocalizedString.bind(void 0,p),f=new WeakMap;class v extends d.ThrottledWidget.ThrottledWidget{liveExpressionButton;focusOut;pins;pinsSetting;constructor(e,t){super(!0,250),this.liveExpressionButton=e,this.focusOut=t,this.contentElement.classList.add("console-pins","monospace"),this.contentElement.addEventListener("contextmenu",this.contextMenuEventFired.bind(this),!1),this.pins=new Set,this.pinsSetting=o.Settings.Settings.instance().createLocalSetting("consolePins",[]);for(const e of this.pinsSetting.get())this.addPin(e)}wasShown(){super.wasShown(),this.registerCSSFiles([u.default,h.default])}willHide(){for(const e of this.pins)e.setHovered(!1)}savePins(){const e=Array.from(this.pins).map((e=>e.expression()));this.pinsSetting.set(e)}contextMenuEventFired(e){const t=new d.ContextMenu.ContextMenu(e),s=d.UIUtils.deepElementFromEvent(e);if(s){const e=s.enclosingNodeOrSelfWithClass("console-pin");if(e){const s=f.get(e);s&&(t.editSection().appendItem(g(m.removeExpression),this.removePin.bind(this,s)),s.appendToContextMenu(t))}}t.editSection().appendItem(g(m.removeAllExpressions),this.removeAllPins.bind(this)),t.show()}removeAllPins(){for(const e of this.pins)this.removePin(e)}removePin(e){e.element().remove();const t=this.focusedPinAfterDeletion(e);this.pins.delete(e),this.savePins(),t?t.focus():this.liveExpressionButton.focus()}addPin(e,t){const s=new b(e,this,this.focusOut);this.contentElement.appendChild(s.element()),this.pins.add(s),this.savePins(),t&&s.focus(),this.update()}focusedPinAfterDeletion(e){const t=Array.from(this.pins);for(let s=0;s<t.length;s++)if(t[s]===e)return 1===t.length?null:s===t.length-1?t[s-1]:t[s+1];return null}async doUpdate(){if(!this.pins.size||!this.isShowing())return;this.isShowing()&&this.update();const e=Array.from(this.pins,(e=>e.updatePreview()));await Promise.all(e),this.updatedForTest()}updatedForTest(){}}class b{pinPane;focusOut;pinElement;pinPreview;lastResult;lastExecutionContext;editor;committedExpression;hovered;lastNode;deletePinIcon;constructor(e,t,s){this.pinPane=t,this.focusOut=s,this.deletePinIcon=document.createElement("div",{is:"dt-close-button"}),this.deletePinIcon.gray=!0,this.deletePinIcon.classList.add("close-button"),this.deletePinIcon.setTabbable(!0),e.length?this.deletePinIcon.setAccessibleName(g(m.removeExpressionS,{PH1:e})):this.deletePinIcon.setAccessibleName(g(m.removeBlankExpression)),self.onInvokeElement(this.deletePinIcon,(e=>{t.removePin(this),e.consume(!0)}));const n=d.Fragment.Fragment.build`
  <div class='console-pin'>
  ${this.deletePinIcon}
  <div class='console-pin-name' $='name'></div>
  <div class='console-pin-preview' $='preview'></div>
  </div>`;this.pinElement=n.element(),this.pinPreview=n.$("preview");const i=n.$("name");d.Tooltip.Tooltip.install(i,e),f.set(this.pinElement,this),this.lastResult=null,this.lastExecutionContext=null,this.committedExpression=e,this.hovered=!1,this.lastNode=null,this.editor=this.createEditor(e,i),this.pinPreview.addEventListener("mouseenter",this.setHovered.bind(this,!0),!1),this.pinPreview.addEventListener("mouseleave",this.setHovered.bind(this,!1),!1),this.pinPreview.addEventListener("click",(e=>{this.lastNode&&(o.Revealer.reveal(this.lastNode),e.consume())}),!1),i.addEventListener("keydown",(e=>{"Escape"===e.key&&e.consume()}))}createEditor(e,t){const s=new a.TextEditor.TextEditor(l.EditorState.create({doc:e,extensions:[l.EditorView.contentAttributes.of({"aria-label":g(m.liveExpressionEditor)}),l.EditorView.lineWrapping,l.javascript.javascriptLanguage,a.JavaScript.completion(),a.Config.showCompletionHint,l.placeholder(g(m.expression)),l.keymap.of([{key:"Escape",run:e=>(e.dispatch({changes:{from:0,to:e.state.doc.length,insert:this.committedExpression}}),this.focusOut(),!0)},{key:"Mod-Enter",run:()=>(this.focusOut(),!0)}]),l.EditorView.domEventHandlers({blur:(e,t)=>this.onBlur(t)}),a.Config.baseConfiguration(e),a.Config.closeBrackets,a.Config.autocompletion]}));return t.appendChild(s),s}onBlur(e){const t=e.state.doc.toString(),s=t.trim();this.committedExpression=s,this.pinPane.savePins(),this.committedExpression.length?this.deletePinIcon.setAccessibleName(g(m.removeExpressionS,{PH1:this.committedExpression})):this.deletePinIcon.setAccessibleName(g(m.removeBlankExpression)),e.dispatch({selection:{anchor:s.length},changes:s!==t?{from:0,to:t.length,insert:s}:void 0})}setHovered(e){this.hovered!==e&&(this.hovered=e,!e&&this.lastNode&&r.OverlayModel.OverlayModel.hideDOMNodeHighlight())}expression(){return this.committedExpression}element(){return this.pinElement}async focus(){const e=this.editor;e.editor.focus(),e.dispatch({selection:{anchor:e.state.doc.length}})}appendToContextMenu(e){this.lastResult&&!("error"in this.lastResult)&&this.lastResult.object&&(e.appendApplicableItems(this.lastResult.object),this.lastResult=null)}async updatePreview(){if(!this.editor)return;const e=a.Config.contentIncludingHint(this.editor.editor),t=this.pinElement.hasFocus(),s=t&&e!==this.committedExpression,n=s?250:void 0,o=d.Context.Context.instance().flavor(r.RuntimeModel.ExecutionContext),i=c.JavaScriptREPL.JavaScriptREPL.preprocessExpression(e),{preview:l,result:h}=await c.JavaScriptREPL.JavaScriptREPL.evaluateAndBuildPreview(i,s,!1,n,!t,"console",!0);this.lastResult&&this.lastExecutionContext&&this.lastExecutionContext.runtimeModel.releaseEvaluationResult(this.lastResult),this.lastResult=h||null,this.lastExecutionContext=o||null;const u=l.deepTextContent();if(!u||u!==this.pinPreview.deepTextContent()){if(this.pinPreview.removeChildren(),h&&r.RuntimeModel.RuntimeModel.isSideEffectFailure(h)){const e=this.pinPreview.createChild("span","object-value-calculate-value-button");e.textContent="(…)",d.Tooltip.Tooltip.install(e,g(m.evaluateAllowingSideEffects))}else u?this.pinPreview.appendChild(l):t||d.UIUtils.createTextChild(this.pinPreview,g(m.notAvailable));d.Tooltip.Tooltip.install(this.pinPreview,u)}let p=null;h&&!("error"in h)&&"object"===h.object.type&&"node"===h.object.subtype&&(p=h.object),this.hovered&&(p?r.OverlayModel.OverlayModel.highlightObjectAsDOMNode(p):this.lastNode&&r.OverlayModel.OverlayModel.hideDOMNodeHighlight()),this.lastNode=p||null;const f=h&&!("error"in h)&&h.exceptionDetails&&!r.RuntimeModel.RuntimeModel.isSideEffectFailure(h);this.pinElement.classList.toggle("error-level",Boolean(f))}}})),t.register("dRNdB",(function(t,s){e(t.exports,"default",(()=>o));const n=new CSSStyleSheet;n.replaceSync("/*\n * Copyright 2018 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.close-button {\n  position: absolute;\n  top: 7px;\n  left: 5px;\n}\n\n.console-pins {\n  max-height: 200px;\n  overflow-y: auto;\n  background: var(--color-background-elevation-1);\n\n  --override-error-background-color: rgb(255 240 240);\n  --override-error-border-color: rgb(225 214 214);\n  --override-error-text-color: rgb(255 0 0);\n}\n\n.console-pins:not(:empty) {\n  border-bottom: 1px solid var(--color-details-hairline);\n}\n\n.-theme-with-dark-background .console-pins,\n:host-context(.-theme-with-dark-background) .console-pins {\n  --override-error-background-color: rgb(41 0 0);\n  --override-error-border-color: rgb(92 0 0);\n  --override-error-text-color: rgb(255 128 128);\n}\n\n.console-pin {\n  position: relative;\n  user-select: text;\n  flex: none;\n  padding: 2px 0 6px 24px;\n}\n\n.console-pin:not(:last-child) {\n  border-bottom: 1px solid var(--color-background-elevation-2);\n}\n\n.console-pin.error-level:not(:focus-within) {\n  background-color: var(--override-error-background-color);\n  color: var(--override-error-text-color);\n}\n\n.console-pin:not(:last-child).error-level:not(:focus-within) {\n  border-top: 1px solid var(--override-error-border-color);\n  border-bottom: 1px solid var(--override-error-border-color);\n  margin-top: -1px;\n}\n\n.console-pin-name {\n  margin-left: -4px;\n  margin-bottom: 1px;\n  height: auto;\n}\n\n.console-pin-name,\n.console-pin-preview {\n  width: 100%;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  min-height: 13px;\n}\n\n.console-pin-preview {\n  overflow: hidden;\n}\n\n.console-pin-name:focus-within {\n  background: var(--color-background);\n  box-shadow: var(--legacy-focus-ring-active-shadow) inset;\n}\n\n.console-pin:focus-within .console-pin-preview,\n.console-pin-name:not(:focus-within):not(:hover) {\n  opacity: 60%;\n}\n\n/*# sourceURL=consolePinPane.css */\n");var o=n})),t.register("5hKKG",(function(s,n){e(s.exports,"ConsoleSidebar",(()=>m)),e(s.exports,"FilterTreeElement",(()=>v)),e(s.exports,"URLGroupTreeElement",(()=>g));var o=t("koSS8"),i=t("ixFnt"),r=t("eQFvP"),l=t("9z2ZV"),a=t("i1dmU"),c=t("fbeO7");const h={other:"<other>",dUserMessages:"{n, plural, =0 {No user messages} =1 {# user message} other {# user messages}}",dMessages:"{n, plural, =0 {No messages} =1 {# message} other {# messages}}",dErrors:"{n, plural, =0 {No errors} =1 {# error} other {# errors}}",dWarnings:"{n, plural, =0 {No warnings} =1 {# warning} other {# warnings}}",dInfo:"{n, plural, =0 {No info} =1 {# info} other {# info}}",dVerbose:"{n, plural, =0 {No verbose} =1 {# verbose} other {# verbose}}"},d=i.i18n.registerUIStrings("panels/console/ConsoleSidebar.ts",h),u=i.i18n.getLocalizedString.bind(void 0,d);class m extends(o.ObjectWrapper.eventMixin(l.Widget.VBox)){tree;selectedTreeElement;treeElements;constructor(){super(!0),this.setMinimumSize(125,0),this.tree=new l.TreeOutline.TreeOutlineInShadow,this.tree.addEventListener(l.TreeOutline.Events.ElementSelected,this.selectionChanged.bind(this)),this.contentElement.appendChild(this.tree.element),this.selectedTreeElement=null,this.treeElements=[];const e=o.Settings.Settings.instance().createSetting("console.sidebarSelectedFilter",null),t=[{key:a.FilterType.Source,text:r.ConsoleModel.FrontendMessageSource.ConsoleAPI,negative:!1,regex:void 0}];this.appendGroup("message",[],a.ConsoleFilter.allLevelsFilterValue(),l.Icon.Icon.create("mediumicon-list"),e),this.appendGroup("user message",t,a.ConsoleFilter.allLevelsFilterValue(),l.Icon.Icon.create("mediumicon-account-circle"),e),this.appendGroup("error",[],a.ConsoleFilter.singleLevelMask("error"),l.Icon.Icon.create("mediumicon-error-circle"),e),this.appendGroup("warning",[],a.ConsoleFilter.singleLevelMask("warning"),l.Icon.Icon.create("mediumicon-warning-triangle"),e),this.appendGroup("info",[],a.ConsoleFilter.singleLevelMask("info"),l.Icon.Icon.create("mediumicon-info-circle"),e),this.appendGroup("verbose",[],a.ConsoleFilter.singleLevelMask("verbose"),l.Icon.Icon.create("mediumicon-bug"),e);const s=e.get();(this.treeElements.find((e=>e.name()===s))||this.treeElements[0]).select()}appendGroup(e,t,s,n,o){const i=new(0,a.ConsoleFilter)(e,t,null,s),r=new v(i,n,o);this.tree.appendChild(r),this.treeElements.push(r)}clear(){for(const e of this.treeElements)e.clear()}onMessageAdded(e){for(const t of this.treeElements)t.onMessageAdded(e)}shouldBeVisible(e){return!(this.selectedTreeElement instanceof p)||this.selectedTreeElement.filter().shouldBeVisible(e)}selectionChanged(e){this.selectedTreeElement=e.data,this.dispatchEventToListeners("FilterSelected")}wasShown(){super.wasShown(),this.tree.registerCSSFiles([c.default])}}class p extends l.TreeOutline.TreeElement{filterInternal;constructor(e,t){super(e),this.filterInternal=t}filter(){return this.filterInternal}}class g extends p{countElement;messageCount;constructor(e){super(e.name,e),this.countElement=this.listItemElement.createChild("span","count");const t=[l.Icon.Icon.create("largeicon-navigator-file")];this.setLeadingIcons(t),this.messageCount=0}incrementAndUpdateCounter(){this.messageCount++,this.countElement.textContent=`${this.messageCount}`}}const f=new Map([["user message",h.dUserMessages],["message",h.dMessages],["error",h.dErrors],["warning",h.dWarnings],["info",h.dInfo],["verbose",h.dVerbose]]);class v extends p{selectedFilterSetting;urlTreeElements;messageCount;uiStringForFilterCount;constructor(e,t,s){super(e.name,e),this.uiStringForFilterCount=f.get(e.name)||"",this.selectedFilterSetting=s,this.urlTreeElements=new Map,this.setLeadingIcons([t]),this.messageCount=0,this.updateCounter()}clear(){this.urlTreeElements.clear(),this.removeChildren(),this.messageCount=0,this.updateCounter()}name(){return this.filterInternal.name}onselect(e){return this.selectedFilterSetting.set(this.filterInternal.name),super.onselect(e)}updateCounter(){this.title=this.updateGroupTitle(this.messageCount),this.setExpandable(Boolean(this.childCount()))}updateGroupTitle(e){return this.uiStringForFilterCount?u(this.uiStringForFilterCount,{n:e}):""}onMessageAdded(e){const t=e.consoleMessage(),s=t.type!==r.ConsoleModel.FrontendMessageType.Command&&t.type!==r.ConsoleModel.FrontendMessageType.Result&&!t.isGroupMessage();if(!this.filterInternal.shouldBeVisible(e)||!s)return;this.childElement(t.url).incrementAndUpdateCounter(),this.messageCount++,this.updateCounter()}childElement(e){const t=e||null;let s=this.urlTreeElements.get(t);if(s)return s;const n=this.filterInternal.clone(),i=t?o.ParsedURL.ParsedURL.fromString(t):null;return n.name=t?i?i.displayName:t:u(h.other),n.parsedFilters.push({key:a.FilterType.Url,text:t,negative:!1,regex:void 0}),s=new g(n),t&&(s.tooltip=t),this.urlTreeElements.set(t,s),this.appendChild(s),s}}})),t.register("fbeO7",(function(t,s){e(t.exports,"default",(()=>o));const n=new CSSStyleSheet;n.replaceSync('/*\n * Copyright (c) 2017 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  overflow: auto;\n  background-color: var(--color-background-elevation-1);\n}\n\n.tree-outline-disclosure {\n  max-width: 100%;\n  padding-left: 6px;\n}\n\n.count {\n  flex: none;\n  margin: 0 8px;\n}\n\n[is="ui-icon"] {\n  margin: 0 5px;\n}\n\nli {\n  height: 24px;\n}\n\nli .largeicon-navigator-file {\n  margin: 0;\n}\n\nli .largeicon-navigator-folder {\n  margin: -3px -3px 0 -5px;\n}\n\n.tree-element-title {\n  flex-shrink: 100;\n  flex-grow: 1;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.tree-outline li:hover:not(.selected) .selection {\n  display: block;\n  background-color: var(--item-hover-color);\n}\n\n@media (forced-colors: active) {\n  [is="ui-icon"].icon-mask {\n    background-color: ButtonText;\n  }\n\n  .tree-outline li:hover:not(.selected) .selection {\n    forced-color-adjust: none;\n    background-color: Highlight;\n  }\n\n  .tree-outline li:hover .tree-element-title,\n  .tree-outline li.selected .tree-element-title,\n  .tree-outline li:hover .count,\n  .tree-outline li.selected .count {\n    forced-color-adjust: none;\n    color: HighlightText;\n  }\n\n  .tree-outline li:hover [is="ui-icon"].icon-mask,\n  .tree-outline li.selected [is="ui-icon"].icon-mask,\n  .tree-outline li.selected:focus .spritesheet-mediumicons:not(.icon-mask) {\n    background-color: HighlightText !important; /* stylelint-disable-line declaration-no-important */\n  }\n}\n\n/*# sourceURL=consoleSidebar.css */\n');var o=n})),t.register("cXqFI",(function(s,n){e(s.exports,"ConsoleViewport",(()=>l));var o=t("lz7WY"),i=t("a3yig"),r=t("9z2ZV");class l{element;topGapElement;topGapElementActive;contentElementInternal;bottomGapElement;bottomGapElementActive;provider;virtualSelectedIndex;firstActiveIndex;lastActiveIndex;renderedItems;anchorSelection;headSelection;itemCount;cumulativeHeights;muteCopyHandler;observer;observerConfig;stickToBottomInternal;selectionIsBackward;lastSelectedElement;cachedProviderElements;constructor(e){this.element=document.createElement("div"),this.element.style.overflow="auto",this.topGapElement=this.element.createChild("div"),this.topGapElement.style.height="0px",this.topGapElement.style.color="transparent",this.topGapElementActive=!1,this.contentElementInternal=this.element.createChild("div"),this.bottomGapElement=this.element.createChild("div"),this.bottomGapElement.style.height="0px",this.bottomGapElement.style.color="transparent",this.bottomGapElementActive=!1,this.topGapElement.textContent="\ufeff",this.bottomGapElement.textContent="\ufeff",r.ARIAUtils.markAsHidden(this.topGapElement),r.ARIAUtils.markAsHidden(this.bottomGapElement),this.provider=e,this.element.addEventListener("scroll",this.onScroll.bind(this),!1),this.element.addEventListener("copy",this.onCopy.bind(this),!1),this.element.addEventListener("dragstart",this.onDragStart.bind(this),!1),this.contentElementInternal.addEventListener("focusin",this.onFocusIn.bind(this),!1),this.contentElementInternal.addEventListener("focusout",this.onFocusOut.bind(this),!1),this.contentElementInternal.addEventListener("keydown",this.onKeyDown.bind(this),!1),this.virtualSelectedIndex=-1,this.contentElementInternal.tabIndex=-1,this.firstActiveIndex=-1,this.lastActiveIndex=-1,this.renderedItems=[],this.anchorSelection=null,this.headSelection=null,this.itemCount=0,this.cumulativeHeights=new Int32Array(0),this.muteCopyHandler=!1,this.observer=new MutationObserver(this.refresh.bind(this)),this.observerConfig={childList:!0,subtree:!0},this.stickToBottomInternal=!1,this.selectionIsBackward=!1}stickToBottom(){return this.stickToBottomInternal}setStickToBottom(e){this.stickToBottomInternal=e,this.stickToBottomInternal?this.observer.observe(this.contentElementInternal,this.observerConfig):this.observer.disconnect()}hasVirtualSelection(){return-1!==this.virtualSelectedIndex}copyWithStyles(){this.muteCopyHandler=!0,this.element.ownerDocument.execCommand("copy"),this.muteCopyHandler=!1}onCopy(e){if(this.muteCopyHandler)return;const t=this.selectedText();t&&(e.preventDefault(),this.selectionContainsTable()?this.copyWithStyles():e.clipboardData&&e.clipboardData.setData("text/plain",t))}onFocusIn(e){const t=this.renderedItems.findIndex((t=>t.element().isSelfOrAncestor(e.target)));-1!==t&&(this.virtualSelectedIndex=this.firstActiveIndex+t);let s=!1;-1===this.virtualSelectedIndex&&this.isOutsideViewport(e.relatedTarget)&&e.target===this.contentElementInternal&&this.itemCount&&(s=!0,this.virtualSelectedIndex=this.itemCount-1,this.refresh(),this.scrollItemIntoView(this.virtualSelectedIndex)),this.updateFocusedItem(s)}onFocusOut(e){this.isOutsideViewport(e.relatedTarget)&&(this.virtualSelectedIndex=-1),this.updateFocusedItem()}isOutsideViewport(e){return null!==e&&!e.isSelfOrDescendant(this.contentElementInternal)}onDragStart(e){const t=this.selectedText();return!!t&&(e.dataTransfer&&(e.dataTransfer.clearData(),e.dataTransfer.setData("text/plain",t),e.dataTransfer.effectAllowed="copy"),!0)}onKeyDown(e){if(r.UIUtils.isEditing()||!this.itemCount||e.shiftKey)return;let t=!1;switch(e.key){case"ArrowUp":if(!(this.virtualSelectedIndex>0))return;t=!0,this.virtualSelectedIndex--;break;case"ArrowDown":if(!(this.virtualSelectedIndex<this.itemCount-1))return;this.virtualSelectedIndex++;break;case"Home":this.virtualSelectedIndex=0;break;case"End":this.virtualSelectedIndex=this.itemCount-1;break;default:return}e.consume(!0),this.scrollItemIntoView(this.virtualSelectedIndex),this.updateFocusedItem(t)}updateFocusedItem(e){const t=this.renderedElementAt(this.virtualSelectedIndex),s=this.lastSelectedElement!==t,n=this.contentElementInternal===this.element.ownerDocument.deepActiveElement();this.lastSelectedElement&&s&&this.lastSelectedElement.classList.remove("console-selected"),t&&(e||s||n)&&this.element.hasFocus()&&(t.classList.add("console-selected"),e?(this.setStickToBottom(!1),this.renderedItems[this.virtualSelectedIndex-this.firstActiveIndex].focusLastChildOrSelf()):t.hasFocus()||t.focus({preventScroll:!0})),this.itemCount&&!this.contentElementInternal.hasFocus()?this.contentElementInternal.tabIndex=0:this.contentElementInternal.tabIndex=-1,this.lastSelectedElement=t}contentElement(){return this.contentElementInternal}invalidate(){delete this.cachedProviderElements,this.itemCount=this.provider.itemCount(),this.virtualSelectedIndex>this.itemCount-1&&(this.virtualSelectedIndex=this.itemCount-1),this.rebuildCumulativeHeights(),this.refresh()}providerElement(e){this.cachedProviderElements||(this.cachedProviderElements=new Array(this.itemCount));let t=this.cachedProviderElements[e];return t||(t=this.provider.itemElement(e),this.cachedProviderElements[e]=t),t}rebuildCumulativeHeights(){const e=this.firstActiveIndex,t=this.lastActiveIndex;let s=0;this.cumulativeHeights=new Int32Array(this.itemCount);for(let n=0;n<this.itemCount;++n)e<=n&&n-e<this.renderedItems.length&&n<=t?s+=this.renderedItems[n-e].element().offsetHeight:s+=this.provider.fastHeight(n),this.cumulativeHeights[n]=s}rebuildCumulativeHeightsIfNeeded(){let e=0,t=0;for(let s=0;s<this.renderedItems.length;++s){const n=this.cachedItemHeight(this.firstActiveIndex+s),o=this.renderedItems[s].element().offsetHeight;if(Math.abs(n-o)>1)return void this.rebuildCumulativeHeights();if(t+=o,e+=n,Math.abs(e-t)>1)return void this.rebuildCumulativeHeights()}}cachedItemHeight(e){return 0===e?this.cumulativeHeights[0]:this.cumulativeHeights[e]-this.cumulativeHeights[e-1]}isSelectionBackwards(e){if(!(e&&e.rangeCount&&e.anchorNode&&e.focusNode))return!1;const t=document.createRange();return t.setStart(e.anchorNode,e.anchorOffset),t.setEnd(e.focusNode,e.focusOffset),t.collapsed}createSelectionModel(e,t,s){return{item:e,node:t,offset:s}}updateSelectionModel(e){const t=e&&e.rangeCount?e.getRangeAt(0):null;if(!t||!e||e.isCollapsed||!this.element.hasSelection())return this.headSelection=null,this.anchorSelection=null,!1;let s=Number.MAX_VALUE,n=-1,o=!1;for(let e=0;e<this.renderedItems.length;++e)if(t.intersectsNode(this.renderedItems[e].element())){const t=e+this.firstActiveIndex;s=Math.min(s,t),n=Math.max(n,t),o=!0}const i=t.intersectsNode(this.topGapElement)&&this.topGapElementActive,r=t.intersectsNode(this.bottomGapElement)&&this.bottomGapElementActive;if(!i&&!r&&!o)return this.headSelection=null,this.anchorSelection=null,!1;this.anchorSelection&&this.headSelection||(this.anchorSelection=this.createSelectionModel(0,this.element,0),this.headSelection=this.createSelectionModel(this.itemCount-1,this.element,this.element.children.length),this.selectionIsBackward=!1);const l=this.isSelectionBackwards(e),a=this.selectionIsBackward?this.headSelection:this.anchorSelection,c=this.selectionIsBackward?this.anchorSelection:this.headSelection;let h=null,d=null;return o&&(h=this.createSelectionModel(s,t.startContainer,t.startOffset),d=this.createSelectionModel(n,t.endContainer,t.endOffset)),i&&r&&o?(h=h&&h.item<a.item?h:a,d=d&&d.item>c.item?d:c):o?i?h=l?this.headSelection:this.anchorSelection:r&&(d=l?this.anchorSelection:this.headSelection):(h=a,d=c),l?(this.anchorSelection=d,this.headSelection=h):(this.anchorSelection=h,this.headSelection=d),this.selectionIsBackward=l,!0}restoreSelection(e){if(!e||!this.anchorSelection||!this.headSelection)return;const t=(e,t)=>{if(this.firstActiveIndex<=e.item&&e.item<=this.lastActiveIndex)return{element:e.node,offset:e.offset};return{element:e.item<this.firstActiveIndex?this.topGapElement:this.bottomGapElement,offset:t?1:0}},{element:s,offset:n}=t(this.anchorSelection,Boolean(this.selectionIsBackward)),{element:o,offset:i}=t(this.headSelection,!this.selectionIsBackward);e.setBaseAndExtent(s,n,o,i)}selectionContainsTable(){if(!this.anchorSelection||!this.headSelection)return!1;const e=this.selectionIsBackward?this.headSelection.item:this.anchorSelection.item,t=this.selectionIsBackward?this.anchorSelection.item:this.headSelection.item;for(let s=e;s<=t;s++){const e=this.providerElement(s);if(e&&"table"===e.consoleMessage().type)return!0}return!1}refresh(){this.observer.disconnect(),this.innerRefresh(),this.stickToBottomInternal&&this.observer.observe(this.contentElementInternal,this.observerConfig)}innerRefresh(){if(!this.visibleHeight())return;if(!this.itemCount){for(let e=0;e<this.renderedItems.length;++e)this.renderedItems[e].willHide();return this.renderedItems=[],this.contentElementInternal.removeChildren(),this.topGapElement.style.height="0px",this.bottomGapElement.style.height="0px",this.firstActiveIndex=-1,this.lastActiveIndex=-1,void this.updateFocusedItem()}const e=this.element.getComponentSelection(),t=this.updateSelectionModel(e),s=this.element.scrollTop,n=this.visibleHeight(),i=2*n;this.rebuildCumulativeHeightsIfNeeded(),this.stickToBottomInternal?(this.firstActiveIndex=Math.max(this.itemCount-Math.ceil(i/this.provider.minimumRowHeight()),0),this.lastActiveIndex=this.itemCount-1):(this.firstActiveIndex=Math.max(o.ArrayUtilities.lowerBound(this.cumulativeHeights,s+1-(i-n)/2,o.ArrayUtilities.DEFAULT_COMPARATOR),0),this.lastActiveIndex=this.firstActiveIndex+Math.ceil(i/this.provider.minimumRowHeight())-1,this.lastActiveIndex=Math.min(this.lastActiveIndex,this.itemCount-1));const r=this.cumulativeHeights[this.firstActiveIndex-1]||0,l=this.cumulativeHeights[this.cumulativeHeights.length-1]-this.cumulativeHeights[this.lastActiveIndex];this.partialViewportUpdate(function(){this.topGapElement.style.height=r+"px",this.bottomGapElement.style.height=l+"px",this.topGapElementActive=Boolean(r),this.bottomGapElementActive=Boolean(l),this.contentElementInternal.style.setProperty("height","10000000px")}.bind(this)),this.contentElementInternal.style.removeProperty("height"),t&&this.restoreSelection(e),this.stickToBottomInternal&&(this.element.scrollTop=1e7)}partialViewportUpdate(e){const t=new Set;for(let e=this.firstActiveIndex;e<=this.lastActiveIndex;++e){const s=this.providerElement(e);console.assert(Boolean(s),"Expected provider element to be defined"),s&&t.add(s)}const s=this.renderedItems.filter((e=>!t.has(e)));for(let e=0;e<s.length;++e)s[e].willHide();e();let n=!1;for(let e=0;e<s.length;++e)n=n||s[e].element().hasFocus(),s[e].element().remove();const o=[];let i=this.contentElementInternal.firstChild;for(const e of t){const t=e.element();if(t!==i){!t.parentElement&&o.push(e),this.contentElementInternal.insertBefore(t,i)}else i=i.nextSibling}for(let e=0;e<o.length;++e)o[e].wasShown();this.renderedItems=Array.from(t),n&&this.contentElementInternal.focus(),this.updateFocusedItem()}selectedText(){if(this.updateSelectionModel(this.element.getComponentSelection()),!this.headSelection||!this.anchorSelection)return null;let e=null,t=null;this.selectionIsBackward?(e=this.headSelection,t=this.anchorSelection):(e=this.anchorSelection,t=this.headSelection);const s=[];for(let n=e.item;n<=t.item;++n){const e=this.providerElement(n);if(console.assert(Boolean(e)),!e)continue;const t=e.element().childTextNodes().map(i.Linkifier.Linkifier.untruncatedNodeText).join("");s.push(t)}const n=this.providerElement(t.item),o=n&&n.element();if(o&&t.node&&t.node.isSelfOrDescendant(o)){const e=this.textOffsetInNode(o,t.node,t.offset);s.length>0&&(s[s.length-1]=s[s.length-1].substring(0,e))}const r=this.providerElement(e.item),l=r&&r.element();if(l&&e.node&&e.node.isSelfOrDescendant(l)){const t=this.textOffsetInNode(l,e.node,e.offset);s[0]=s[0].substring(t)}return s.join("\n")}textOffsetInNode(e,t,s){const n=t.textContent?t.textContent.length:0;t.nodeType!==Node.TEXT_NODE&&(s<t.childNodes.length?(t=t.childNodes.item(s),s=0):s=n);let o=0,r=e;for(;(r=r.traverseNextNode(e))&&r!==t;)r.nodeType!==Node.TEXT_NODE||r.parentElement&&("STYLE"===r.parentElement.nodeName||"SCRIPT"===r.parentElement.nodeName)||(o+=i.Linkifier.Linkifier.untruncatedNodeText(r).length);const l=i.Linkifier.Linkifier.untruncatedNodeText(t).length;return s>0&&l!==n&&(s=l),o+s}onScroll(e){this.refresh()}firstVisibleIndex(){return this.cumulativeHeights.length?(this.rebuildCumulativeHeightsIfNeeded(),o.ArrayUtilities.lowerBound(this.cumulativeHeights,this.element.scrollTop+1,o.ArrayUtilities.DEFAULT_COMPARATOR)):-1}lastVisibleIndex(){if(!this.cumulativeHeights.length)return-1;this.rebuildCumulativeHeightsIfNeeded();const e=this.element.scrollTop+this.element.clientHeight,t=this.itemCount-1;return o.ArrayUtilities.lowerBound(this.cumulativeHeights,e,o.ArrayUtilities.DEFAULT_COMPARATOR,void 0,t)}renderedElementAt(e){return-1===e||e<this.firstActiveIndex||e>this.lastActiveIndex?null:this.renderedItems[e-this.firstActiveIndex].element()}scrollItemIntoView(e,t){const s=this.firstVisibleIndex(),n=this.lastVisibleIndex();e>s&&e<n||e===n&&this.cumulativeHeights[e]<=this.element.scrollTop+this.visibleHeight()||(t?this.forceScrollItemToBeLast(e):e<=s?this.forceScrollItemToBeFirst(e):e>=n&&this.forceScrollItemToBeLast(e))}forceScrollItemToBeFirst(e){console.assert(e>=0&&e<this.itemCount,"Cannot scroll item at invalid index"),this.setStickToBottom(!1),this.rebuildCumulativeHeightsIfNeeded(),this.element.scrollTop=e>0?this.cumulativeHeights[e-1]:0,r.UIUtils.isScrolledToBottom(this.element)&&this.setStickToBottom(!0),this.refresh();const t=this.renderedElementAt(e);t&&t.scrollIntoView(!0)}forceScrollItemToBeLast(e){console.assert(e>=0&&e<this.itemCount,"Cannot scroll item at invalid index"),this.setStickToBottom(!1),this.rebuildCumulativeHeightsIfNeeded(),this.element.scrollTop=this.cumulativeHeights[e]-this.visibleHeight(),r.UIUtils.isScrolledToBottom(this.element)&&this.setStickToBottom(!0),this.refresh();const t=this.renderedElementAt(e);t&&t.scrollIntoView(!1)}visibleHeight(){return this.element.offsetHeight}}})),t.register("91fou",(function(s,n){e(s.exports,"getMessageForElement",(()=>M)),e(s.exports,"ConsoleViewMessage",(()=>R)),e(s.exports,"getMaxTokenizableStringLength",(()=>G)),e(s.exports,"getLongStringVisibleLength",(()=>W)),e(s.exports,"ConsoleGroupViewMessage",(()=>P)),e(s.exports,"ConsoleCommand",(()=>A)),e(s.exports,"ConsoleCommandResult",(()=>U)),e(s.exports,"ConsoleTableMessageView",(()=>H)),e(s.exports,"MaxLengthForLinks",(()=>B)),e(s.exports,"setMaxTokenizableStringLength",(()=>D)),e(s.exports,"setLongStringVisibleLength",(()=>j));var o=t("koSS8"),i=t("ixFnt"),r=t("lz7WY"),l=t("eQFvP"),a=t("fMswD"),c=t("g4rSN"),h=t("7f6zc"),d=t("4gsXY"),u=t("3T3mV"),m=t("hzgLE");t("6gaMO");var p=t("cep8F"),g=t("cObcK"),f=t("R6KC8"),v=t("a3yig"),b=t("9z2ZV"),C=t("eKcu2"),x=t("9WUcz"),w=t("g0XHO"),S=t("iibJW");const I={consoleclearWasPreventedDueTo:"`console.clear()` was prevented due to 'Preserve log'",consoleWasCleared:"Console was cleared",clearAllMessagesWithS:"Clear all messages with {PH1}",assertionFailed:"Assertion failed: ",violationS:"`[Violation]` {PH1}",interventionS:"`[Intervention]` {PH1}",deprecationS:"`[Deprecation]` {PH1}",thisValueWillNotBeCollectedUntil:"This value will not be collected until console is cleared.",thisValueWasEvaluatedUponFirst:"This value was evaluated upon first expanding. It may have changed since then.",functionWasResolvedFromBound:"Function was resolved from bound function.",exception:"<exception>",warning:"Warning",error:"Error",repeatS:"{n, plural, =1 {Repeated # time} other {Repeated # times}}",warningS:"{n, plural, =1 {Warning, Repeated # time} other {Warning, Repeated # times}}",errorS:"{n, plural, =1 {Error, Repeated # time} other {Error, Repeated # times}}",url:"<URL>",tookNms:"took <N>ms",someEvent:"<some> event",Mxx:" M<XX>",attribute:"<attribute>",index:"(index)",value:"Value",console:"Console",stackMessageExpanded:"Stack table expanded",stackMessageCollapsed:"Stack table collapsed"},E=i.i18n.registerUIStrings("panels/console/ConsoleViewMessage.ts",I),T=i.i18n.getLocalizedString.bind(void 0,E),y=new WeakMap,M=e=>y.get(e),k=e=>t=>t instanceof l.RemoteObject.RemoteObject?t:e?"object"==typeof t?e.createRemoteObject(t):e.createRemoteObjectFromPrimitiveValue(t):l.RemoteObject.RemoteObject.fromLocalObject(t);class R{message;linkifier;repeatCountInternal;closeGroupDecorationCount;consoleGroupInternal;selectableChildren;messageResized;elementInternal;previewFormatter;searchRegexInternal;messageLevelIcon;traceExpanded;expandTrace;anchorElement;contentElementInternal;nestingLevelMarkers;searchHighlightNodes;searchHighlightNodeChanges;isVisibleInternal;cachedHeight;messagePrefix;timestampElement;inSimilarGroup;similarGroupMarker;lastInSimilarGroup;groupKeyInternal;repeatCountElement;requestResolver;issueResolver;#e=Promise.resolve();constructor(e,t,s,n,o){this.message=e,this.linkifier=t,this.requestResolver=s,this.issueResolver=n,this.repeatCountInternal=1,this.closeGroupDecorationCount=0,this.selectableChildren=[],this.messageResized=o,this.elementInternal=null,this.previewFormatter=new f.RemoteObjectPreviewFormatter.RemoteObjectPreviewFormatter,this.searchRegexInternal=null,this.messageLevelIcon=null,this.traceExpanded=!1,this.expandTrace=null,this.anchorElement=null,this.contentElementInternal=null,this.nestingLevelMarkers=null,this.searchHighlightNodes=[],this.searchHighlightNodeChanges=[],this.isVisibleInternal=!1,this.cachedHeight=0,this.messagePrefix="",this.timestampElement=null,this.inSimilarGroup=!1,this.similarGroupMarker=null,this.lastInSimilarGroup=!1,this.groupKeyInternal="",this.repeatCountElement=null,this.consoleGroupInternal=null}element(){return this.toMessageElement()}wasShown(){this.isVisibleInternal=!0}onResize(){}willHide(){this.isVisibleInternal=!1,this.cachedHeight=this.element().offsetHeight}isVisible(){return this.isVisibleInternal}fastHeight(){return this.cachedHeight?this.cachedHeight:this.approximateFastHeight()}approximateFastHeight(){return 19}consoleMessage(){return this.message}formatErrorStackPromiseForTest(){return this.#e}buildMessage(){let e,t=this.message.messageText;if(this.message.source===l.ConsoleModel.FrontendMessageSource.ConsoleAPI)switch(this.message.type){case"trace":e=this.format(this.message.parameters||["console.trace"]);break;case"clear":e=document.createElement("span"),e.classList.add("console-info"),o.Settings.Settings.instance().moduleSetting("preserveConsoleLog").get()?e.textContent=T(I.consoleclearWasPreventedDueTo):e.textContent=T(I.consoleWasCleared),b.Tooltip.Tooltip.install(e,T(I.clearAllMessagesWithS,{PH1:String(b.ShortcutRegistry.ShortcutRegistry.instance().shortcutTitleForAction("console.clear"))}));break;case"dir":{const t=["%O",this.message.parameters?this.message.parameters[0]:void 0];e=this.format(t);break}case"profile":case"profileEnd":e=this.format([t]);break;default:{if("assert"===this.message.type&&(this.messagePrefix=T(I.assertionFailed)),this.message.parameters&&1===this.message.parameters.length){const t=this.message.parameters[0];"string"!=typeof t&&"string"===t.type&&(e=this.tryFormatAsError(t.value))}const s=this.message.parameters||[t];e=e||this.format(s)}}else if("network"===this.message.source)e=this.formatAsNetworkRequest()||this.format([t]);else{const s=this.message.parameters&&t===this.message.parameters[0];"violation"===this.message.source?t=T(I.violationS,{PH1:t}):"intervention"===this.message.source?t=T(I.interventionS,{PH1:t}):"deprecation"===this.message.source&&(t=T(I.deprecationS,{PH1:t}));const n=this.message.parameters||[t];s&&(n[0]=t),e=this.format(n)}e.classList.add("console-message-text");const s=document.createElement("span");return s.classList.add("source-code"),this.anchorElement=this.buildMessageAnchor(),this.anchorElement&&s.appendChild(this.anchorElement),s.appendChild(e),s}formatAsNetworkRequest(){const e=c.NetworkLog.NetworkLog.requestForConsoleMessage(this.message);if(!e)return null;const t=document.createElement("span");if("error"===this.message.level){b.UIUtils.createTextChild(t,e.requestMethod+" ");const s=v.Linkifier.Linkifier.linkifyRevealable(e,e.url(),e.url());s.tabIndex=-1,this.selectableChildren.push({element:s,forceSelect:()=>s.focus()}),t.appendChild(s),e.failed&&b.UIUtils.createTextChildren(t," ",e.localizedFailDescription||""),0!==e.statusCode&&b.UIUtils.createTextChildren(t," ",String(e.statusCode)),e.statusText&&b.UIUtils.createTextChildren(t," (",e.statusText,")")}else{const s=this.message.messageText,n=this.linkifyWithCustomLinkifier(s,((t,s,n,o)=>{const i=s===e.url()?v.Linkifier.Linkifier.linkifyRevealable(e,s,e.url()):v.Linkifier.Linkifier.linkifyURL(s,{text:t,lineNumber:n,columnNumber:o});return i.tabIndex=-1,this.selectableChildren.push({element:i,forceSelect:()=>i.focus()}),i}));t.appendChild(n)}return t}createAffectedResourceLinks(){const e=[],t=this.message.getAffectedResources()?.requestId;if(t){const s=new p.RequestLinkIcon;s.classList.add("resource-links"),s.data={affectedRequest:{requestId:t},requestResolver:this.requestResolver,displayURL:!1},e.push(s)}const s=this.message.getAffectedResources()?.issueId;if(s){const t=new m.IssueLinkIcon.IssueLinkIcon;t.classList.add("resource-links"),t.data={issueId:s,issueResolver:this.issueResolver},e.push(t)}return e}buildMessageAnchor(){const e=(e=>e.scriptId?this.linkifyScriptId(e.scriptId,e.url||"",e.line,e.column):e.stackTrace&&e.stackTrace.callFrames.length?this.linkifyStackTraceTopFrame(e.stackTrace):e.url&&"undefined"!==e.url?this.linkifyLocation(e.url,e.line,e.column):null)(this.message);if(e){e.tabIndex=-1,this.selectableChildren.push({element:e,forceSelect:()=>e.focus()});const t=document.createElement("span");t.classList.add("console-message-anchor"),t.appendChild(e);for(const e of this.createAffectedResourceLinks())b.UIUtils.createTextChild(t," "),t.append(e);return b.UIUtils.createTextChild(t," "),t}return null}buildMessageWithStackTrace(e){const t=document.createElement("div");t.classList.add("console-message-stack-trace-toggle");const s=t.createChild("div","console-message-stack-trace-wrapper"),n=this.buildMessage(),o=b.Icon.Icon.create("smallicon-triangle-right","console-message-expand-icon"),i=s.createChild("div");b.ARIAUtils.setExpanded(i,!1),i.appendChild(o),i.tabIndex=-1,i.appendChild(n);const r=s.createChild("div"),l=v.JSPresentationUtils.buildStackTracePreviewContents(e.target(),this.linkifier,{stackTrace:this.message.stackTrace,tabStops:void 0});r.appendChild(l.element);for(const e of l.links)this.selectableChildren.push({element:e,forceSelect:()=>e.focus()});r.classList.add("hidden"),b.ARIAUtils.setAccessibleName(s,`${n.textContent} ${T(I.stackMessageCollapsed)}`),b.ARIAUtils.markAsGroup(r),this.expandTrace=e=>{o.setIconType(e?"smallicon-triangle-down":"smallicon-triangle-right"),r.classList.toggle("hidden",!e);const t=T(e?I.stackMessageExpanded:I.stackMessageCollapsed);b.ARIAUtils.setAccessibleName(s,`${n.textContent} ${t}`),b.ARIAUtils.alert(t),b.ARIAUtils.setExpanded(i,e),this.traceExpanded=e};return i.addEventListener("click",(e=>{b.UIUtils.isEditing()||s.hasSelection()||(this.expandTrace&&this.expandTrace(r.classList.contains("hidden")),e.consume())}),!1),"trace"===this.message.type&&this.expandTrace(!0),t._expandStackTraceForTest=this.expandTrace.bind(this,!0),t}linkifyLocation(e,t,s){const n=this.message.runtimeModel();return n?this.linkifier.linkifyScriptLocation(n.target(),null,e,t,{columnNumber:s,inlineFrameIndex:0}):null}linkifyStackTraceTopFrame(e){const t=this.message.runtimeModel();return t?this.linkifier.linkifyStackTraceTopFrame(t.target(),e):null}linkifyScriptId(e,t,s,n){const o=this.message.runtimeModel();return o?this.linkifier.linkifyScriptLocation(o.target(),e,t,s,{columnNumber:n,inlineFrameIndex:0}):null}format(e){const t=document.createElement("span");if(this.messagePrefix&&(t.createChild("span").textContent=this.messagePrefix),!e.length)return t;let s=e.map(k(this.message.runtimeModel()));const n="string"===l.RemoteObject.RemoteObject.type(s[0])&&(this.message.type!==l.ConsoleModel.FrontendMessageType.Result||"error"===this.message.level);n&&(s=this.formatWithSubstitutionString(s[0].description,s.slice(1),t),s.length&&b.UIUtils.createTextChild(t," "));for(let e=0;e<s.length;++e)n&&"string"===s[e].type?t.appendChild(this.linkifyStringAsFragment(s[e].description||"")):t.appendChild(this.formatParameter(s[e],!1,!0)),e<s.length-1&&b.UIUtils.createTextChild(t," ");return t}formatParameter(e,t,s){if(e.customPreview())return new f.CustomPreviewComponent.CustomPreviewComponent(e).element;const n=t?"object":e.subtype||e.type;let o;switch(n){case"error":o=this.formatParameterAsError(e);break;case"function":o=this.formatParameterAsFunction(e,s);break;case"array":case"arraybuffer":case"blob":case"dataview":case"generator":case"iterator":case"map":case"object":case"promise":case"proxy":case"set":case"typedarray":case"wasmvalue":case"weakmap":case"weakset":case"webassemblymemory":o=this.formatParameterAsObject(e,s);break;case"node":o=e.isNode()?this.formatParameterAsNode(e):this.formatParameterAsObject(e,!1);break;case"trustedtype":o=this.formatParameterAsObject(e,!1);break;case"string":o=this.formatParameterAsString(e);break;case"boolean":case"date":case"null":case"number":case"regexp":case"symbol":case"undefined":case"bigint":o=this.formatParameterAsValue(e);break;default:o=this.formatParameterAsValue(e),console.error(`Tried to format remote object of unknown type ${n}.`)}return o.classList.add(`object-value-${n}`),o.classList.add("source-code"),o}formatParameterAsValue(e){const t=document.createElement("span"),s=e.description||"";if(s.length>G()){const e=new f.ObjectPropertiesSection.ExpandableTextPropertyValue(document.createElement("span"),s,W());t.appendChild(e.element)}else b.UIUtils.createTextChild(t,s);return t.addEventListener("contextmenu",this.contextMenuEventFired.bind(this,e),!1),t}formatParameterAsTrustedType(e){const t=document.createElement("span"),s=document.createElement("span");return s.appendChild(this.formatParameterAsString(e)),s.classList.add("object-value-string"),b.UIUtils.createTextChild(t,`${e.className} `),t.appendChild(s),t}formatParameterAsObject(e,t){const s=document.createElement("span");if(s.classList.add("console-object"),t&&e.preview)s.classList.add("console-object-preview"),this.previewFormatter.appendObjectPreview(s,e.preview,!1);else if("function"===e.type){const t=s.createChild("span");f.ObjectPropertiesSection.ObjectPropertiesSection.formatObjectAsFunction(e,t,!1),s.classList.add("object-value-function")}else"trustedtype"===e.subtype?s.appendChild(this.formatParameterAsTrustedType(e)):b.UIUtils.createTextChild(s,e.description||"");if(!e.hasChildren||e.customPreview())return s;const n=s.createChild("span","object-state-note info-note");this.message.type===l.ConsoleModel.FrontendMessageType.QueryObjectResult?b.Tooltip.Tooltip.install(n,T(I.thisValueWillNotBeCollectedUntil)):b.Tooltip.Tooltip.install(n,T(I.thisValueWasEvaluatedUponFirst));const o=new f.ObjectPropertiesSection.ObjectPropertiesSection(e,s,this.linkifier);return o.element.classList.add("console-view-object-properties-section"),o.enableContextMenu(),o.setShowSelectionOnKeyboardFocus(!0,!0),this.selectableChildren.push(o),o.addEventListener(b.TreeOutline.Events.ElementAttached,this.messageResized),o.addEventListener(b.TreeOutline.Events.ElementExpanded,this.messageResized),o.addEventListener(b.TreeOutline.Events.ElementCollapsed,this.messageResized),o.element}formatParameterAsFunction(e,t){const s=document.createElement("span");return l.RemoteObject.RemoteFunction.objectAsFunction(e).targetFunction().then(function(n){const o=document.createElement("span"),i=f.ObjectPropertiesSection.ObjectPropertiesSection.formatObjectAsFunction(n,o,!0,t);if(s.appendChild(o),n!==e){const e=s.createChild("span","object-state-note info-note");b.Tooltip.Tooltip.install(e,T(I.functionWasResolvedFromBound))}s.addEventListener("contextmenu",this.contextMenuEventFired.bind(this,n),!1),i.then((()=>this.formattedParameterAsFunctionForTest()))}.bind(this)),s}formattedParameterAsFunctionForTest(){}contextMenuEventFired(e,t){const s=new b.ContextMenu.ContextMenu(t);s.appendApplicableItems(e),s.show()}renderPropertyPreviewOrAccessor(e,t,s){return"accessor"===t.type?this.formatAsAccessorProperty(e,s.map((e=>e.name.toString())),!1):this.previewFormatter.renderPropertyPreview(t.type,"subtype"in t?t.subtype:void 0,null,t.value)}formatParameterAsNode(e){const t=document.createElement("span"),s=e.runtimeModel().target().model(l.DOMModel.DOMModel);return s?(s.pushObjectAsNodeToFrontend(e).then((async s=>{if(!s)return void t.appendChild(this.formatParameterAsObject(e,!1));const n=await b.UIUtils.Renderer.render(s);n?(n.tree&&(this.selectableChildren.push(n.tree),n.tree.addEventListener(b.TreeOutline.Events.ElementAttached,this.messageResized),n.tree.addEventListener(b.TreeOutline.Events.ElementExpanded,this.messageResized),n.tree.addEventListener(b.TreeOutline.Events.ElementCollapsed,this.messageResized)),t.appendChild(n.node)):t.appendChild(this.formatParameterAsObject(e,!1)),this.formattedParameterAsNodeForTest()})),t):t}formattedParameterAsNodeForTest(){}formatParameterAsString(e){const t=e.description??"",s=r.StringUtilities.formatAsJSLiteral(t),n=document.createElement("span");return n.addEventListener("contextmenu",this.contextMenuEventFired.bind(this,e),!1),n.appendChild(this.linkifyStringAsFragment(s)),n}formatParameterAsError(e){const t=document.createElement("span"),s=e.description||"";return this.#e=this.retrieveExceptionDetails(e).then((e=>{const n=this.tryFormatAsError(s,e);t.appendChild(n??this.linkifyStringAsFragment(s))})),t}async retrieveExceptionDetails(e){const t=this.message.runtimeModel();if(t&&e.objectId)return t.getExceptionDetails(e.objectId)}formatAsArrayEntry(e){return this.previewFormatter.renderPropertyPreview(e.type,e.subtype,e.className,e.description)}formatAsAccessorProperty(e,t,s){const n=f.ObjectPropertiesSection.ObjectPropertyTreeElement.createRemoteObjectAccessorPropertySpan(e,t,function(e){const t=e.wasThrown,o=e.object;if(!o)return;if(n.removeChildren(),t){const e=n.createChild("span");e.textContent=T(I.exception),b.Tooltip.Tooltip.install(e,o.description)}else if(s)n.appendChild(this.formatAsArrayEntry(o));else{const e=100,t=o.type,s=o.subtype;let i="";"function"!==t&&o.description&&(i="string"===t||"regexp"===s||"trustedtype"===s?r.StringUtilities.trimMiddle(o.description,e):r.StringUtilities.trimEndWithMaxLength(o.description,e)),n.appendChild(this.previewFormatter.renderPropertyPreview(t,s,o.className,i))}}.bind(this));return n}formatWithSubstitutionString(e,t,s){const n=new Map,{tokens:o,args:i}=(0,x.format)(e,t);for(const e of o)switch(e.type){case"generic":s.append(this.formatParameter(e.value,!0,!1));break;case"optimal":s.append(this.formatParameter(e.value,!1,!0));break;case"string":if(0===n.size)s.append(this.linkifyStringAsFragment(e.value));else{const t=e.value.split("\n");for(let e=0;e<t.length;e++){e>0&&s.append(document.createElement("br"));const o=document.createElement("span");o.style.setProperty("contain","paint"),o.style.setProperty("display","inline-block"),o.style.setProperty("max-width","100%"),o.appendChild(this.linkifyStringAsFragment(t[e]));for(const[e,{value:t,priority:s}]of n)o.style.setProperty(e,t,s);s.append(o)}}break;case"style":(0,x.updateStyle)(n,e.value)}return i}matchesFilterRegex(e){e.lastIndex=0;const t=this.contentElement(),s=this.anchorElement?this.anchorElement.deepTextContent():"";return Boolean(s)&&e.test(s.trim())||e.test(t.deepTextContent().slice(s.length))}matchesFilterText(e){return this.contentElement().deepTextContent().toLowerCase().includes(e.toLowerCase())}updateTimestamp(){this.contentElementInternal&&(o.Settings.Settings.instance().moduleSetting("consoleTimestampsEnabled").get()?(this.timestampElement||(this.timestampElement=document.createElement("span"),this.timestampElement.classList.add("console-timestamp")),this.timestampElement.textContent=b.UIUtils.formatTimestamp(this.message.timestamp,!1)+" ",b.Tooltip.Tooltip.install(this.timestampElement,b.UIUtils.formatTimestamp(this.message.timestamp,!0)),this.contentElementInternal.insertBefore(this.timestampElement,this.contentElementInternal.firstChild)):this.timestampElement&&(this.timestampElement.remove(),this.timestampElement=null))}nestingLevel(){let e=0;for(let t=this.consoleGroup();null!==t;t=t.consoleGroup())e++;return e}setConsoleGroup(e){console.assert(null===this.consoleGroupInternal),this.consoleGroupInternal=e}clearConsoleGroup(){this.consoleGroupInternal=null}consoleGroup(){return this.consoleGroupInternal}setInSimilarGroup(e,t){this.inSimilarGroup=e,this.lastInSimilarGroup=e&&Boolean(t),this.similarGroupMarker&&!e?(this.similarGroupMarker.remove(),this.similarGroupMarker=null):this.elementInternal&&!this.similarGroupMarker&&e&&(this.similarGroupMarker=document.createElement("div"),this.similarGroupMarker.classList.add("nesting-level-marker"),this.elementInternal.insertBefore(this.similarGroupMarker,this.elementInternal.firstChild),this.similarGroupMarker.classList.toggle("group-closed",this.lastInSimilarGroup))}isLastInSimilarGroup(){return Boolean(this.inSimilarGroup)&&Boolean(this.lastInSimilarGroup)}resetCloseGroupDecorationCount(){this.closeGroupDecorationCount&&(this.closeGroupDecorationCount=0,this.updateCloseGroupDecorations())}incrementCloseGroupDecorationCount(){++this.closeGroupDecorationCount,this.updateCloseGroupDecorations()}updateCloseGroupDecorations(){if(this.nestingLevelMarkers)for(let e=0,t=this.nestingLevelMarkers.length;e<t;++e){this.nestingLevelMarkers[e].classList.toggle("group-closed",t-e<=this.closeGroupDecorationCount)}}focusedChildIndex(){return this.selectableChildren.length?this.selectableChildren.findIndex((e=>e.element.hasFocus())):-1}onKeyDown(e){!b.UIUtils.isEditing()&&this.elementInternal&&this.elementInternal.hasFocus()&&!this.elementInternal.hasSelection()&&this.maybeHandleOnKeyDown(e)&&e.consume(!0)}maybeHandleOnKeyDown(e){const t=this.focusedChildIndex(),s=-1===t;if(this.expandTrace&&s&&("ArrowLeft"===e.key&&this.traceExpanded||"ArrowRight"===e.key&&!this.traceExpanded))return this.expandTrace(!this.traceExpanded),!0;if(!this.selectableChildren.length)return!1;if("ArrowLeft"===e.key)return this.elementInternal&&this.elementInternal.focus(),!0;if("ArrowRight"===e.key&&s&&this.selectNearestVisibleChild(0))return!0;if("ArrowUp"===e.key){const e=this.nearestVisibleChild(0);if(this.selectableChildren[t]===e&&e)return this.elementInternal&&this.elementInternal.focus(),!0;if(this.selectNearestVisibleChild(t-1,!0))return!0}if("ArrowDown"===e.key){if(s&&this.selectNearestVisibleChild(0))return!0;if(!s&&this.selectNearestVisibleChild(t+1))return!0}return!1}selectNearestVisibleChild(e,t){const s=this.nearestVisibleChild(e,t);return!!s&&(s.forceSelect(),!0)}nearestVisibleChild(e,t){const s=this.selectableChildren.length;if(e<0||e>=s)return null;const n=t?-1:1;let o=e;for(;!this.selectableChildren[o].element.offsetParent;)if(o+=n,o<0||o>=s)return null;return this.selectableChildren[o]}focusLastChildOrSelf(){this.elementInternal&&!this.selectNearestVisibleChild(this.selectableChildren.length-1,!0)&&this.elementInternal.focus()}setContentElement(e){console.assert(!this.contentElementInternal,"Cannot set content element twice"),this.contentElementInternal=e}getContentElement(){return this.contentElementInternal}contentElement(){if(this.contentElementInternal)return this.contentElementInternal;const e=document.createElement("div");e.classList.add("console-message"),this.messageLevelIcon&&e.appendChild(this.messageLevelIcon),this.contentElementInternal=e;const t=this.message.runtimeModel();let s;const n=Boolean(this.message.stackTrace)&&("network"===this.message.source||"violation"===this.message.source||"error"===this.message.level||"warning"===this.message.level||"trace"===this.message.type);return s=t&&n?this.buildMessageWithStackTrace(t):this.buildMessage(),e.appendChild(s),this.updateTimestamp(),this.contentElementInternal}toMessageElement(){return this.elementInternal||(this.elementInternal=document.createElement("div"),this.elementInternal.tabIndex=-1,this.elementInternal.addEventListener("keydown",this.onKeyDown.bind(this)),this.updateMessageElement()),this.elementInternal}updateMessageElement(){if(this.elementInternal){this.elementInternal.className="console-message-wrapper",this.elementInternal.removeChildren(),this.message.isGroupStartMessage()&&this.elementInternal.classList.add("console-group-title"),this.message.source===l.ConsoleModel.FrontendMessageSource.ConsoleAPI&&this.elementInternal.classList.add("console-from-api"),this.inSimilarGroup&&(this.similarGroupMarker=this.elementInternal.createChild("div","nesting-level-marker"),this.similarGroupMarker.classList.toggle("group-closed",this.lastInSimilarGroup)),this.nestingLevelMarkers=[];for(let e=0;e<this.nestingLevel();++e)this.nestingLevelMarkers.push(this.elementInternal.createChild("div","nesting-level-marker"));switch(this.updateCloseGroupDecorations(),y.set(this.elementInternal,this),this.message.level){case"verbose":this.elementInternal.classList.add("console-verbose-level");break;case"info":this.elementInternal.classList.add("console-info-level"),this.message.type===l.ConsoleModel.FrontendMessageType.System&&this.elementInternal.classList.add("console-system-type");break;case"warning":this.elementInternal.classList.add("console-warning-level");break;case"error":this.elementInternal.classList.add("console-error-level")}this.updateMessageLevelIcon(),this.shouldRenderAsWarning()&&this.elementInternal.classList.add("console-warning-level"),this.elementInternal.appendChild(this.contentElement()),this.repeatCountInternal>1&&this.showRepeatCountElement()}}shouldRenderAsWarning(){return!("verbose"!==this.message.level&&"info"!==this.message.level||"violation"!==this.message.source&&"deprecation"!==this.message.source&&"intervention"!==this.message.source&&"recommendation"!==this.message.source)}updateMessageLevelIcon(){let e="",t="";if("warning"===this.message.level?(e="smallicon-warning",t=T(I.warning)):"error"===this.message.level&&(e="smallicon-error",t=T(I.error)),!this.messageLevelIcon){if(!e)return;this.messageLevelIcon=b.Icon.Icon.create("","message-level-icon"),this.contentElementInternal&&this.contentElementInternal.insertBefore(this.messageLevelIcon,this.contentElementInternal.firstChild)}this.messageLevelIcon.setIconType(e),b.ARIAUtils.setAccessibleName(this.messageLevelIcon,t)}repeatCount(){return this.repeatCountInternal||1}resetIncrementRepeatCount(){this.repeatCountInternal=1,this.repeatCountElement&&(this.repeatCountElement.remove(),this.contentElementInternal&&this.contentElementInternal.classList.remove("repeated-message"),this.repeatCountElement=null)}incrementRepeatCount(){this.repeatCountInternal++,this.showRepeatCountElement()}setRepeatCount(e){this.repeatCountInternal=e,this.showRepeatCountElement()}showRepeatCountElement(){if(!this.elementInternal)return;if(!this.repeatCountElement){switch(this.repeatCountElement=document.createElement("span",{is:"dt-small-bubble"}),this.repeatCountElement.classList.add("console-message-repeat-count"),this.message.level){case"warning":this.repeatCountElement.type="warning";break;case"error":this.repeatCountElement.type="error";break;case"verbose":this.repeatCountElement.type="verbose";break;default:this.repeatCountElement.type="info"}this.shouldRenderAsWarning()&&(this.repeatCountElement.type="warning"),this.elementInternal.insertBefore(this.repeatCountElement,this.contentElementInternal),this.contentElement().classList.add("repeated-message")}let e;this.repeatCountElement.textContent=`${this.repeatCountInternal}`,e="warning"===this.message.level?T(I.warningS,{n:this.repeatCountInternal}):"error"===this.message.level?T(I.errorS,{n:this.repeatCountInternal}):T(I.repeatS,{n:this.repeatCountInternal}),b.ARIAUtils.setAccessibleName(this.repeatCountElement,e)}get text(){return this.message.messageText}toExportString(){const e=[],t=this.contentElement().childTextNodes().map(v.Linkifier.Linkifier.untruncatedNodeText).join("");for(let s=0;s<this.repeatCount();++s)e.push(t);return e.join("\n")}setSearchRegex(e){if(this.searchHighlightNodeChanges&&this.searchHighlightNodeChanges.length&&b.UIUtils.revertDomChanges(this.searchHighlightNodeChanges),this.searchRegexInternal=e,this.searchHighlightNodes=[],this.searchHighlightNodeChanges=[],!this.searchRegexInternal)return;const t=this.contentElement().deepTextContent();let s;this.searchRegexInternal.lastIndex=0;const n=[];for(;(s=this.searchRegexInternal.exec(t))&&s[0];)n.push(new h.TextRange.SourceRange(s.index,s[0].length));n.length&&(this.searchHighlightNodes=b.UIUtils.highlightSearchResults(this.contentElement(),n,this.searchHighlightNodeChanges))}searchRegex(){return this.searchRegexInternal}searchCount(){return this.searchHighlightNodes.length}searchHighlightNode(e){return this.searchHighlightNodes[e]}async getInlineFrames(e,t,s,n){const o=a.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance();if(o.pluginManager){const i=d.Workspace.WorkspaceImpl.instance().projects().map((e=>e.uiSourceCodeForURL(t))).flat().filter((e=>Boolean(e))).map((e=>o.scriptsForUISourceCode(e))).flat();if(i.length){const t=new l.DebuggerModel.Location(e,i[0].scriptId,s||0,n);return await o.pluginManager.getFunctionInfo(i[0],t)??{frames:[]}}}return{frames:[]}}async expandInlineStackFrames(e,t,s,n,o,i,r,l){const{frames:a}=await this.getInlineFrames(e,n,o,i);if(!a.length)return!1;for(let c=0;c<a.length;++c){const{name:h}=a[c],d=document.createElement("span");d.appendChild(this.linkifyStringAsFragment(`${t} ${h} (`));const u=this.linkifier.linkifyScriptLocation(e.target(),null,n,o,{columnNumber:i,inlineFrameIndex:c});u.tabIndex=-1,this.selectableChildren.push({element:u,forceSelect:()=>u.focus()}),d.appendChild(u),d.appendChild(this.linkifyStringAsFragment(s)),r.insertBefore(d,l)}return!0}createScriptLocationLinkForSyntaxError(e,t){const{scriptId:s,lineNumber:n,columnNumber:o}=t;if(!s)return;const i=t.url||e.scriptForId(s)?.sourceURL;if(!i)return;const r=this.linkifier.linkifyScriptLocation(e.target(),t.scriptId||null,i,n,{columnNumber:o,inlineFrameIndex:0,showColumnNumber:!0});return r.tabIndex=-1,r}tryFormatAsError(e,t){const s=this.message.runtimeModel();if(!s)return null;const n=(0,S.parseSourcePositionsFromErrorStack)(s,e);if(!n?.length)return null;t?.stackTrace&&(0,S.augmentErrorStackWithScriptIds)(n,t.stackTrace);const o=s.debuggerModel(),i=document.createElement("span");for(let e=0;e<n.length;++e){const s=e<n.length-1?"\n":"",{line:r,link:l}=n[e];if(!l&&t&&r.startsWith("SyntaxError")){i.appendChild(this.linkifyStringAsFragment(r));const e=this.createScriptLocationLinkForSyntaxError(o,t);e&&(i.append(" (at "),i.appendChild(e),i.append(")")),i.append(s);continue}if(!l){i.appendChild(this.linkifyStringAsFragment(`${r}${s}`));continue}const a=document.createElement("span"),c=`${l.suffix}${s}`;a.appendChild(this.linkifyStringAsFragment(l.prefix));const h=this.linkifier.linkifyScriptLocation(o.target(),l.scriptId||null,l.url,l.lineNumber,{columnNumber:l.columnNumber,inlineFrameIndex:0,showColumnNumber:!0});if(h.tabIndex=-1,this.selectableChildren.push({element:h,forceSelect:()=>h.focus()}),a.appendChild(h),a.appendChild(this.linkifyStringAsFragment(c)),i.appendChild(a),!l.enclosedInBraces)continue;const d=l.prefix.substring(0,l.prefix.lastIndexOf(" ",l.prefix.length-3)),u=this.selectableChildren.length-1;this.expandInlineStackFrames(o,d,c,l.url,l.lineNumber,l.columnNumber,i,a).then((e=>{e&&(i.removeChild(a),this.selectableChildren.splice(u,1))}))}return i}linkifyWithCustomLinkifier(e,t){if(e.length>G()){const t=new f.ObjectPropertiesSection.ExpandableTextPropertyValue(document.createElement("span"),e,W()),s=document.createDocumentFragment();return s.appendChild(t.element),s}const s=document.createDocumentFragment(),n=R.tokenizeMessageText(e);let i=!1;for(const e of n)if(e.text)switch(i&&(e.text=`blob:${e.text}`,i=!i),"'blob:"===e.text&&e===n[0]&&(i=!0,e.text="'"),e.type){case"url":{const n=e.text.startsWith("www.")?"http://"+e.text:e.text,i=o.ParsedURL.ParsedURL.splitLineAndColumn(n),r=o.ParsedURL.ParsedURL.removeWasmFunctionInfoFromURL(i.url);let l;l=i?t(e.text,r,i.lineNumber,i.columnNumber):t(e.text,""),s.appendChild(l);break}default:s.appendChild(document.createTextNode(e.text))}return s}linkifyStringAsFragment(e){return this.linkifyWithCustomLinkifier(e,((e,t,s,n)=>{const o={text:e,lineNumber:s,columnNumber:n},i=v.Linkifier.Linkifier.linkifyURL(t,o);return i.tabIndex=-1,this.selectableChildren.push({element:i,forceSelect:()=>i.focus()}),i}))}static tokenizeMessageText(e){const{tokenizerRegexes:t,tokenizerTypes:s}=function(){if(!F||!L){const e="\\u0000-\\u0020\\u007f-\\u009f",t=new RegExp("(?:[a-zA-Z][a-zA-Z0-9+.-]{2,}:\\/\\/|data:|www\\.)[^\\s"+e+'"]{2,}[^\\s'+e+"\"')}\\],:;.!?]","u"),s=/(?:\/[\w\.-]*)+\:[\d]+/,n=/took [\d]+ms/,o=/'\w+' event/,i=/\sM[6-7]\d/,r=/\(suggested: \"[\w-]+\"\)/,l=new Map;return l.set(t,"url"),l.set(s,"url"),l.set(n,"time"),l.set(o,"event"),l.set(i,"milestone"),l.set(r,"autofill"),F=Array.from(l.keys()),L=Array.from(l.values()),{tokenizerRegexes:F,tokenizerTypes:L}}return{tokenizerRegexes:F,tokenizerTypes:L}}();if(e.length>G())return[{text:e,type:void 0}];return h.TextUtils.Utils.splitStringByRegexes(e,t).map((e=>({text:e.value,type:s[e.regexIndex]})))}groupKey(){return this.groupKeyInternal||(this.groupKeyInternal=this.message.groupCategoryKey()+":"+this.groupTitle()),this.groupKeyInternal}groupTitle(){return R.tokenizeMessageText(this.message.messageText).reduce(((e,t)=>{let s=t.text;return"url"===t.type?s=T(I.url):"time"===t.type?s=T(I.tookNms):"event"===t.type?s=T(I.someEvent):"milestone"===t.type?s=T(I.Mxx):"autofill"===t.type&&(s=T(I.attribute)),e+s}),"").replace(/[%]o/g,"")}}let F=null,L=null;class P extends R{collapsedInternal;expandGroupIcon;onToggle;groupEndMessageInternal;constructor(e,t,s,n,o,i){console.assert(e.isGroupStartMessage()),super(e,t,s,n,i),this.collapsedInternal="startGroupCollapsed"===e.type,this.expandGroupIcon=null,this.onToggle=o,this.groupEndMessageInternal=null}setCollapsed(e){this.collapsedInternal=e,this.expandGroupIcon&&this.expandGroupIcon.setIconType(this.collapsedInternal?"smallicon-triangle-right":"smallicon-triangle-down"),this.onToggle.call(null)}collapsed(){return this.collapsedInternal}maybeHandleOnKeyDown(e){return-1===this.focusedChildIndex()&&("ArrowLeft"===e.key&&!this.collapsedInternal||"ArrowRight"===e.key&&this.collapsedInternal)?(this.setCollapsed(!this.collapsedInternal),!0):super.maybeHandleOnKeyDown(e)}toMessageElement(){let e=this.elementInternal||null;if(!e){e=super.toMessageElement();const t=this.collapsedInternal?"smallicon-triangle-right":"smallicon-triangle-down";this.expandGroupIcon=b.Icon.Icon.create(t,"expand-group-icon"),this.contentElement().tabIndex=-1,this.repeatCountElement?this.repeatCountElement.insertBefore(this.expandGroupIcon,this.repeatCountElement.firstChild):e.insertBefore(this.expandGroupIcon,this.contentElementInternal),e.addEventListener("click",(()=>this.setCollapsed(!this.collapsedInternal)))}return e}showRepeatCountElement(){super.showRepeatCountElement(),this.repeatCountElement&&this.expandGroupIcon&&this.repeatCountElement.insertBefore(this.expandGroupIcon,this.repeatCountElement.firstChild)}messagesHidden(){if(this.collapsed())return!0;const e=this.consoleGroup();return Boolean(e&&e.messagesHidden())}setGroupEnd(e){if("endGroup"!==e.consoleMessage().type)throw new Error("Invalid console message as group end");if(null!==this.groupEndMessageInternal)throw new Error("Console group already has an end");this.groupEndMessageInternal=e}groupEnd(){return this.groupEndMessageInternal}}class A extends R{formattedCommand;constructor(e,t,s,n,o){super(e,t,s,n,o),this.formattedCommand=null}contentElement(){const e=this.getContentElement();if(e)return e;const t=document.createElement("div");this.setContentElement(t),t.classList.add("console-user-command");const s=b.Icon.Icon.create("smallicon-user-command","command-result-icon");return t.appendChild(s),y.set(t,this),this.formattedCommand=document.createElement("span"),this.formattedCommand.classList.add("source-code"),this.formattedCommand.textContent=r.StringUtilities.replaceControlCharacters(this.text),t.appendChild(this.formattedCommand),this.formattedCommand.textContent.length<O?u.CodeHighlighter.highlightNode(this.formattedCommand,"text/javascript").then(this.updateSearch.bind(this)):this.updateSearch(),this.updateTimestamp(),t}updateSearch(){this.setSearchRegex(this.searchRegex())}}class U extends R{contentElement(){const e=super.contentElement();if(!e.classList.contains("console-user-command-result")&&(e.classList.add("console-user-command-result"),"info"===this.consoleMessage().level)){const t=b.Icon.Icon.create("smallicon-command-result","command-result-icon");e.insertBefore(t,e.firstChild)}return e}}class H extends R{dataGrid;constructor(e,t,s,n,o){super(e,t,s,n,o),console.assert("table"===e.type),this.dataGrid=null}wasShown(){this.dataGrid&&this.dataGrid.updateWidths(),super.wasShown()}onResize(){this.isVisible()&&this.dataGrid&&this.dataGrid.onResize()}contentElement(){const e=this.getContentElement();if(e)return e;const t=document.createElement("div");return t.classList.add("console-message"),this.messageLevelIcon&&t.appendChild(this.messageLevelIcon),this.setContentElement(t),t.appendChild(this.buildTableMessage()),this.updateTimestamp(),t}buildTableMessage(){const e=document.createElement("span");e.classList.add("source-code"),this.anchorElement=this.buildMessageAnchor(),this.anchorElement&&e.appendChild(this.anchorElement);const t=this.message.parameters&&this.message.parameters.length?this.message.parameters[0]:null;if(!t)return this.buildMessage();const s=k(this.message.runtimeModel())(t);if(!s||!s.preview)return this.buildMessage();const n=Symbol("rawValueColumn"),o=[],i=s.preview,r=[];for(let e=0;e<i.properties.length;++e){const t=i.properties[e];let l;if(t.valuePreview&&t.valuePreview.properties.length)l=t.valuePreview.properties;else{if(!t.value)continue;l=[{name:n,type:t.type,value:t.value}]}const a=new Map,c=20;for(let e=0;e<l.length;++e){const n=l[e];let i=-1!==o.indexOf(n.name);if(!i){if(o.length===c)continue;i=!0,o.push(n.name)}if(i){const e=this.renderPropertyPreviewOrAccessor(s,n,[t,n]);e.classList.add("console-message-nowrap-below"),a.set(n.name,e)}}r.push({rowName:t.name,rowValue:a})}const l=[];for(const{rowName:e,rowValue:t}of r){l.push(e);for(let e=0;e<o.length;++e)l.push(t.get(o[e]))}o.unshift(T(I.index));const a=o.map((e=>e===n?T(I.value):e.toString()));if(l.length&&(this.dataGrid=g.SortableDataGrid.SortableDataGrid.create(a,l,T(I.console)),this.dataGrid)){this.dataGrid.setStriped(!0),this.dataGrid.setFocusable(!1);const t=document.createElement("span");t.classList.add("console-message-text");const n=t.createChild("div","console-message-formatted-table"),o=n.createChild("span");n.appendChild(this.formatParameter(s,!0,!1));const i=o.attachShadow({mode:"open"}),r=this.dataGrid.asWidget();r.markAsRoot(),r.show(i),r.registerCSSFiles([w.default,C.default]),e.appendChild(t),this.dataGrid.renderInline()}return e}approximateFastHeight(){const e=this.message.parameters&&this.message.parameters[0];return e&&"string"!=typeof e&&e.preview?19*e.preview.properties.length:19}}const O=1e4,B=40;let N=1e4,V=5e3;const G=()=>N,D=e=>{N=e},W=()=>V,j=e=>{V=e}})),t.register("6gaMO",(function(s,n){e(s.exports,"RequestLinkIcon",(()=>t("cep8F")));t("cep8F")})),t.register("cep8F",(function(s,n){e(s.exports,"extractShortPath",(()=>g)),e(s.exports,"RequestLinkIcon",(()=>v));var o=t("ixFnt"),i=t("koSS8"),r=t("hE0P3"),l=t("kpUjp"),a=t("cY3yZ"),c=t("dS5IF"),h=t("hveEP"),d=t("7xVdH");const u={clickToShowRequestInTheNetwork:"Click to open the network panel and show request for URL: {url}",requestUnavailableInTheNetwork:"Request unavailable in the network panel, try reloading the inspected page",shortenedURL:"Shortened URL"},m=o.i18n.registerUIStrings("ui/components/request_link_icon/RequestLinkIcon.ts",u),p=o.i18n.getLocalizedString.bind(void 0,m),g=e=>(/[^/]+$/.exec(e)||/[^/]+\/$/.exec(e)||[""])[0],f=h.RenderCoordinator.RenderCoordinator.instance();class v extends HTMLElement{static litTagName=c.literal`devtools-request-link-icon`;#t=this.attachShadow({mode:"open"});#s;#n;#o;#i;#r=!1;#l;#a;#c;#h=i.Revealer.reveal;#d=Promise.resolve(void 0);set data(e){this.#s=e.linkToPreflight,this.#n=e.request,e.affectedRequest&&(this.#a={...e.affectedRequest}),this.#o=e.highlightHeader,this.#l=e.networkTab,this.#i=e.requestResolver,this.#r=e.displayURL??!1,this.#c=e.additionalOnClickAction,e.revealOverride&&(this.#h=e.revealOverride),!this.#n&&e.affectedRequest&&(this.#d=this.#u(e.affectedRequest.requestId)),this.#m()}connectedCallback(){this.#t.adoptedStyleSheets=[d.default]}#u(e){if(!this.#i)throw new Error("A `RequestResolver` must be provided if an `affectedRequest` is provided.");return this.#i.waitFor(e).then((e=>{this.#n=e})).catch((()=>{this.#n=null}))}get data(){return{linkToPreflight:this.#s,request:this.#n,affectedRequest:this.#a,highlightHeader:this.#o,networkTab:this.#l,requestResolver:this.#i,displayURL:this.#r,additionalOnClickAction:this.#c,revealOverride:this.#h!==i.Revealer.reveal?this.#h:void 0}}#p(){return this.#n?"--color-link":"--issue-color-yellow"}iconData(){return{iconName:"network_panel_icon",color:`var(${this.#p()})`,width:"16px",height:"16px"}}handleClick(e){if(0!==e.button)return;const t=this.#s?this.#n?.preflightRequest():this.#n;if(t){if(this.#o){const e=r.UIRequestLocation.UIRequestLocation.header(t,this.#o.section,this.#o.name);this.#h(e)}else{const e=r.UIRequestLocation.UIRequestLocation.tab(t,this.#l??r.UIRequestLocation.UIRequestTabs.Headers);this.#h(e)}this.#c?.()}}#g(){return this.#n?p(u.clickToShowRequestInTheNetwork,{url:this.#n.url()}):p(u.requestUnavailableInTheNetwork)}#f(){return this.#n?this.#n.url():this.#a?.url}#v(){if(!this.#r)return c.nothing;const e=this.#f();if(!e)return c.nothing;const t=g(e);return c.html`<span aria-label=${p(u.shortenedURL)} title=${e}>${t}</span>`}#m(){return f.write((()=>{c.render(c.html`
        ${c.Directives.until(this.#d.then((()=>this.#b())),this.#b())}
      `,this.#t,{host:this})}))}#b(){return c.html`
      <span class=${c.Directives.classMap({link:Boolean(this.#n)})}
            tabindex="0"
            @click=${this.handleClick}>
        <${a.Icon.Icon.litTagName} .data=${this.iconData()}
          title=${this.#g()}></${a.Icon.Icon.litTagName}>
        ${this.#v()}
      </span>`}}l.CustomElements.defineComponent("devtools-request-link-icon",v)})),t.register("7xVdH",(function(t,s){e(t.exports,"default",(()=>o));const n=new CSSStyleSheet;n.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  display: inline-block;\n  white-space: nowrap;\n  color: inherit;\n  font-size: inherit;\n  font-family: inherit;\n}\n\ndevtools-icon {\n  vertical-align: middle;\n}\n\n.link {\n  cursor: pointer;\n}\n\n.link span {\n  color: var(--color-link);\n}\n\n/*# <AUTHOR> <EMAIL>\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions\n * are met:\n *\n * 1.  Redistributions of source code must retain the above copyright\n *     notice, this list of conditions and the following disclaimer.\n * 2.  Redistributions in binary form must reproduce the above copyright\n *     notice, this list of conditions and the following disclaimer in the\n *     documentation and/or other materials provided with the distribution.\n * 3.  Neither the name of Apple Computer, Inc. ("Apple") nor the names of\n *     its contributors may be used to endorse or promote products derived\n *     from this software without specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY APPLE AND ITS CONTRIBUTORS "AS IS" AND ANY\n * EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n * DISCLAIMED. IN NO EVENT SHALL APPLE OR ITS CONTRIBUTORS BE LIABLE FOR ANY\n * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\n * THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\n.console-view {\n  background-color: var(--color-background);\n  overflow: hidden;\n\n  --override-error-text-color: var(--color-error-text);\n  --override-console-error-background-color: var(--color-error-background);\n  --override-error-border-color: var(--color-error-border);\n  --override-message-border-color: rgb(240 240 240);\n  --override-warning-border-color: hsl(50deg 100% 88%);\n  --override-focused-message-border-color: hsl(214deg 67% 88%);\n  --override-focused-message-background-color: hsl(214deg 48% 95%);\n  --override-console-warning-background-color: hsl(50deg 100% 95%);\n  --override-console-warning-text-color: hsl(39deg 100% 18%);\n}\n\n.-theme-with-dark-background .console-view {\n  --override-message-border-color: rgb(58 58 58);\n  --override-warning-border-color: rgb(102 85 0);\n  --override-focused-message-border-color: hsl(214deg 47% 48%);\n  --override-focused-message-background-color: hsl(214deg 19% 20%);\n  --override-console-warning-background-color: hsl(50deg 100% 10%);\n  --override-console-warning-text-color: hsl(39deg 89% 55%);\n  --override-console-link-color: var(--color-background-inverted);\n}\n\n.console-toolbar-container {\n  display: flex;\n  flex: none;\n}\n\n.console-main-toolbar {\n  flex: 1 1 auto;\n}\n\n.console-toolbar-container > .toolbar {\n  background-color: var(--color-background-elevation-1);\n  border-bottom: var(--legacy-divider-border);\n}\n\n.console-view-fix-select-all {\n  height: 0;\n  overflow: hidden;\n}\n\n.console-settings-pane {\n  flex: none;\n  background-color: var(--color-background-elevation-1);\n  border-bottom: var(--legacy-divider-border);\n}\n\n.console-settings-pane .toolbar {\n  flex: 1 1;\n}\n\n#console-messages {\n  flex: 1 1;\n  overflow-y: auto;\n  word-wrap: break-word;\n  user-select: text;\n  transform: translateZ(0);\n  overflow-anchor: none;  /* Chrome-specific scroll-anchoring opt-out */\n  background-color: var(--color-background);\n}\n\n#console-prompt {\n  clear: right;\n  position: relative;\n  margin: 0 22px 0 20px;\n}\n\n.console-prompt-editor-container {\n  min-height: 21px;\n}\n\n.console-message,\n.console-user-command {\n  clear: right;\n  position: relative;\n  padding: 3px 22px 1px 0;\n  margin-left: 24px;\n  min-height: 17px;  /* Sync with ConsoleViewMessage.js */\n  flex: auto;\n  display: flex;\n}\n\n.console-message > * {\n  flex: auto;\n}\n\n.console-timestamp {\n  color: var(--color-text-secondary);\n  user-select: none;\n  flex: none;\n  margin-right: 5px;\n}\n\n.message-level-icon,\n.command-result-icon {\n  position: absolute;\n  left: -17px;\n  top: 4px;\n  user-select: none;\n}\n\n.console-message-repeat-count {\n  margin: 2px 0 0 10px;\n  flex: none;\n}\n\n.repeated-message {\n  margin-left: 4px;\n}\n\n.repeated-message .message-level-icon {\n  display: none;\n}\n\n.console-message-stack-trace-toggle {\n  display: flex;\n  flex-direction: row;\n  align-items: flex-start;\n}\n\n.repeated-message .console-message-stack-trace-toggle,\n.repeated-message > .console-message-text {\n  flex: 1;\n}\n\n.console-error-level .repeated-message,\n.console-warning-level .repeated-message,\n.console-verbose-level .repeated-message,\n.console-info-level .repeated-message {\n  display: flex;\n}\n\n.console-info {\n  color: var(--color-text-secondary);\n  font-style: italic;\n  padding-bottom: 2px;\n}\n\n.console-group .console-group > .console-group-messages {\n  margin-left: 16px;\n}\n\n.console-group-title.console-from-api {\n  font-weight: bold;\n}\n\n.console-group-title .console-message {\n  margin-left: 12px;\n}\n\n.expand-group-icon {\n  user-select: none;\n  flex: none;\n  position: relative;\n  left: 10px;\n  top: 5px;\n  margin-right: 2px;\n}\n\n.console-group-title .message-level-icon {\n  display: none;\n}\n\n.console-message-repeat-count .expand-group-icon {\n  left: 2px;\n  top: 2px;\n  background-color: var(--color-background);\n  margin-right: 4px;\n}\n\n.console-group {\n  position: relative;\n}\n\n.console-message-wrapper {\n  display: flex;\n  border-top: 1px solid var(--override-message-border-color);\n  border-bottom: 1px solid transparent;\n\n  /* Console ANSI color */\n  --console-color-black: #000;\n  --console-color-red: #a00;\n  --console-color-green: #0a0;\n  --console-color-yellow: #a50;\n  --console-color-blue: #00a;\n  --console-color-magenta: #a0a;\n  --console-color-cyan: #0aa;\n  --console-color-gray: #aaa;\n  --console-color-darkgray: #555;\n  --console-color-lightred: #f55;\n  --console-color-lightgreen: #5f5;\n  --console-color-lightyellow: #ff5;\n  --console-color-lightblue: #55f;\n  --console-color-ightmagenta: #f5f;\n  --console-color-lightcyan: #5ff;\n  --console-color-white: #fff;\n}\n\n.-theme-with-dark-background .console-message-wrapper {\n  /* Dark theme console ANSI color */\n  --console-color-red: rgb(237 78 76);\n  --console-color-green: rgb(1 200 1);\n  --console-color-yellow: rgb(210 192 87);\n  --console-color-blue: rgb(39 116 240);\n  --console-color-magenta: rgb(161 66 244);\n  --console-color-cyan: rgb(18 181 203);\n  --console-color-gray: rgb(207 208 208);\n  --console-color-darkgray: rgb(137 137 137);\n  --console-color-lightred: rgb(242 139 130);\n  --console-color-lightgreen: rgb(161 247 181);\n  --console-color-lightyellow: rgb(221 251 85);\n  --console-color-lightblue: rgb(102 157 246);\n  --console-color-lightmagenta: rgb(214 112 214);\n  --console-color-lightcyan: rgb(132 240 255);\n}\n\n.console-message-wrapper:first-of-type {\n  border-top-color: transparent;\n}\n\n.console-message-wrapper.console-adjacent-user-command-result:not(.console-error-level):not(.console-warning-level) {\n  border-top-width: 0;\n}\n\n.console-message-wrapper:last-of-type {\n  border-bottom-color: var(--override-message-border-color);\n}\n\n.console-message-wrapper:focus {\n  border-top-color: var(--override-focused-message-border-color);\n  border-bottom-color: var(--override-focused-message-border-color);\n  background-color: var(--override-focused-message-background-color);\n}\n\n.console-message-wrapper:focus + .console-message-wrapper {\n  border-top-color: transparent;\n}\n\n.console-message-wrapper.console-error-level,\n.console-message-wrapper.console-error-level:not(:focus) + .console-message-wrapper:not(.console-warning-level):not(:focus) {\n  border-top-color: var(--override-error-border-color);\n}\n\n.console-message-wrapper.console-warning-level,\n.console-message-wrapper.console-warning-level:not(:focus) + .console-message-wrapper:not(.console-error-level):not(:focus) {\n  border-top-color: var(--override-warning-border-color);\n}\n\n.console-message-wrapper.console-error-level:last-of-type {\n  border-bottom-color: var(--override-error-border-color);\n}\n\n.console-message-wrapper.console-warning-level:last-of-type {\n  border-bottom-color: var(--override-warning-border-color);\n}\n\n.console-message-wrapper.console-adjacent-user-command-result:not(.console-error-level):not(.console-warning-level):focus {\n  border-top-width: 1px;\n}\n\n.console-message-wrapper.console-adjacent-user-command-result:not(.console-error-level):not(.console-warning-level):focus .console-message {\n  padding-top: 2px;\n  min-height: 16px;\n}\n\n.console-message-wrapper.console-adjacent-user-command-result:not(.console-error-level):not(.console-warning-level):focus .command-result-icon {\n  top: 3px;\n}\n\n.console-message-wrapper.console-error-level:focus {\n  --override-error-text-color: rgb(200 0 0);\n}\n\n.-theme-with-dark-background .console-message-wrapper.console-error-level:focus {\n  --override-error-text-color: hsl(0deg 100% 75%);\n}\n\n.console-message-wrapper .nesting-level-marker {\n  width: 14px;\n  flex: 0 0 auto;\n  border-right: 1px solid var(--color-details-hairline);\n  position: relative;\n  margin-bottom: -1px;\n  margin-top: -1px;\n}\n\n.console-message-wrapper .nesting-level-marker::before {\n  border-bottom: 1px solid var(--color-details-hairline);\n  position: absolute;\n  top: 0;\n  left: 0;\n  margin-left: 100%;\n  width: 3px;\n  height: 100%;\n  box-sizing: border-box;\n}\n\n.console-message-wrapper:last-child .nesting-level-marker::before,\n.console-message-wrapper .nesting-level-marker.group-closed::before {\n  content: "";\n}\n\n.console-error-level {\n  background-color: var(--override-console-error-background-color);\n}\n\n.console-warning-level {\n  background-color: var(--override-console-warning-background-color);\n}\n\n.console-warning-level .console-message-text {\n  color: var(--override-console-warning-text-color);\n}\n\n.console-view-object-properties-section {\n  padding: 0;\n  position: relative;\n  vertical-align: baseline;\n  color: inherit;\n  display: inline-block;\n  overflow-wrap: break-word;\n  max-width: 100%;\n}\n\n.info-note {\n  background-color: var(--color-primary-variant);\n}\n\n.info-note::before {\n  content: "i";\n}\n\n.console-view-object-properties-section:not(.expanded) .info-note {\n  display: none;\n}\n\n.console-error-level .console-message-text,\n.console-error-level .console-view-object-properties-section {\n  color: var(--override-error-text-color) !important; /* stylelint-disable-line declaration-no-important */\n}\n\n.console-system-type.console-info-level {\n  color: #00f; /* stylelint-disable-line plugin/use_theme_colors */\n  /* See: crbug.com/1152736 for color variable migration. */\n}\n\n.-theme-with-dark-background .console-verbose-level:not(.console-warning-level) .console-message-text,\n.-theme-with-dark-background .console-system-type.console-info-level {\n  color: hsl(220deg 100% 65%) !important; /* stylelint-disable-line declaration-no-important */\n}\n\n#console-messages .link {\n  cursor: pointer;\n  text-decoration: underline;\n}\n\n#console-messages .link,\n#console-messages .devtools-link {\n  color: var(--color-text-secondary);\n  word-break: break-all;\n}\n\n#console-messages .resource-links {\n  vertical-align: bottom;\n}\n\n#console-messages .link:hover,\n#console-messages .devtools-link:hover {\n  color: var(--color-text-primary);\n}\n\n.console-object-preview {\n  white-space: normal;\n  word-wrap: break-word;\n  font-style: italic;\n}\n\n.console-object-preview .name {\n  flex-shrink: 0;\n}\n\n.console-message-text .object-value-node {\n  display: inline-block;\n}\n\n.console-message-text .object-value-string,\n.console-message-text .object-value-regexp,\n.console-message-text .object-value-symbol {\n  white-space: pre-wrap;\n  word-break: break-all;\n}\n\n.console-message-formatted-table {\n  clear: both;\n}\n\n.console-message .source-code {\n  line-height: 1.2;\n}\n\n.console-message-anchor {\n  float: right;\n  text-align: right;\n  max-width: 100%;\n  margin-left: 4px;\n}\n\n.console-message-badge {\n  float: right;\n  margin-left: 4px;\n}\n\n.console-message-nowrap-below,\n.console-message-nowrap-below div,\n.console-message-nowrap-below span {\n  white-space: nowrap !important; /* stylelint-disable-line declaration-no-important */\n}\n\n.object-state-note {\n  display: inline-block;\n  width: 11px;\n  height: 11px;\n  color: var(--color-background);\n  text-align: center;\n  border-radius: 3px;\n  line-height: 13px;\n  margin: 0 6px;\n  font-size: 9px;\n}\n\n.-theme-with-dark-background .object-state-note {\n  background-color: hsl(230deg 100% 80%);\n}\n\n.console-object {\n  white-space: pre-wrap;\n  word-break: break-all;\n}\n\n.console-message-stack-trace-wrapper {\n  flex: 1 1 auto;\n  display: flex;\n  flex-direction: column;\n  align-items: stretch;\n}\n\n.console-message-stack-trace-wrapper > * {\n  flex: none;\n}\n\n.console-message-expand-icon {\n  margin-bottom: -2px;\n}\n\n.console-searchable-view {\n  max-height: 100%;\n}\n\n.console-view-pinpane {\n  flex: none;\n  max-height: 50%;\n}\n\n/* We are setting width and height to 0px to essentially hide the html element on the UI but visible to the screen reader.\n This html element is used by screen readers when console messages are filtered, instead of screen readers reading\n contents of the filtered messages we only want the screen readers to read the count of filtered messages. */\n.message-count {\n  width: 0;\n  height: 0;\n}\n\n.-theme-with-dark-background #console-messages .console-message-text .devtools-link {\n  color: var(--override-console-link-color);\n}\n\n@media (forced-colors: active) {\n  .console-message-expand-icon,\n  .console-warning-level [is="ui-icon"].icon-mask.expand-group-icon {\n    forced-color-adjust: none;\n    background-color: ButtonText;\n  }\n\n  .console-message-wrapper:focus,\n  .console-message-wrapper:focus:last-of-type {\n    forced-color-adjust: none;\n    background-color: Highlight;\n    border-top-color: Highlight;\n    border-bottom-color: Highlight;\n  }\n\n  .console-message-wrapper:focus *,\n  .console-message-wrapper:focus:last-of-type *,\n  .console-message-wrapper:focus .devtools-link,\n  .console-message-wrapper:focus:last-of-type .devtools-link {\n    color: HighlightText !important; /* stylelint-disable-line declaration-no-important */\n  }\n\n  #console-messages .devtools-link,\n  #console-messages .devtools-link:hover {\n    color: linktext;\n  }\n\n  #console-messages .link:focus-visible,\n  #console-messages .devtools-link:focus-visible {\n    background: Highlight;\n    color: HighlightText;\n  }\n\n  .console-message-wrapper:focus [is="ui-icon"].icon-mask {\n    background-color: HighlightText;\n  }\n\n  .console-message-wrapper.console-error-level:focus,\n  .console-message-wrapper.console-error-level:focus:last-of-type {\n    --override-error-text-color: HighlightText;\n  }\n}\n\n/*# sourceURL=consoleView.css */\n');var o=n})),t.register("iibJW",(function(s,n){e(s.exports,"parseSourcePositionsFromErrorStack",(()=>i)),e(s.exports,"augmentErrorStackWithScriptIds",(()=>l));var o=t("koSS8");function i(e,t){if(!/^[\w.]*Error\b/.test(t))return null;const s=e.debuggerModel(),n=e.target().inspectedURL(),i=t.split("\n"),l=[];for(const e of i){const t=/^\s*at\s/.test(e);if(!t&&l.length&&l[l.length-1].link)return null;if(!t){l.push({line:e});continue}let i=-1,a=-1;const c=/\([^\)\(]+:\d+:\d+\)/g,h=/\([^\)\(]+\)/g;let d,u=null;for(;d=c.exec(e);)u=d;if(!u)for(;d=h.exec(e);)u=d;u&&(i=u.index,a=u.index+u[0].length-1);const m=-1!==i;let p=m?i+1:e.indexOf("at")+3;m||e.indexOf("async ")!==p||(p+=6);const g=m?a:e.length,f=e.substring(p,g),v=o.ParsedURL.ParsedURL.splitLineAndColumn(f);if("<anonymous>"===v.url){l.push({line:e});continue}let b=r(s,v.url);if(!b&&o.ParsedURL.ParsedURL.isRelativeURL(v.url)&&(b=r(s,o.ParsedURL.ParsedURL.completeURL(n,v.url))),!b)return null;l.push({line:e,link:{url:b,prefix:e.substring(0,p),suffix:e.substring(g),enclosedInBraces:m,lineNumber:v.lineNumber,columnNumber:v.columnNumber}})}return l}function r(e,t){if(!t)return null;const s=o.ParsedURL.ParsedURL.fromString(t);if(s)return s.url;if(e.scriptsForSourceURL(t).length)return t;const n=new URL(t,"file://");return e.scriptsForSourceURL(n.href).length?n.href:null}function l(e,t){for(const s of e){const e=t.callFrames.find((e=>a(s,e)));e&&s.link&&(s.link.scriptId=e.scriptId)}}function a(e,t){if(!e.link)return!1;const{url:s,lineNumber:n,columnNumber:o}=e.link;return s===t.url&&n===t.lineNumber&&o===t.columnNumber}})),t.register("lygHa",(function(s,n){e(s.exports,"ConsolePrompt",(()=>x)),e(s.exports,"ConsoleHistoryManager",(()=>w));var o=t("koSS8"),i=t("e7bLS"),r=t("ixFnt"),l=t("9X2mn"),a=t("eQFvP"),c=t("kaczT"),h=t("zPo9O"),d=t("39160"),u=t("hi0Sq"),m=t("R6KC8"),p=t("9z2ZV"),g=t("2XTyv"),f=t("5jm5f");const v={consolePrompt:"Console prompt"},b=r.i18n.registerUIStrings("panels/console/ConsolePrompt.ts",v),C=r.i18n.getLocalizedString.bind(void 0,b);class x extends(o.ObjectWrapper.eventMixin(p.Widget.Widget)){addCompletionsFromHistory;historyInternal;initialText;editor;eagerPreviewElement;textChangeThrottler;formatter;requestPreviewBound;requestPreviewCurrent=0;innerPreviewElement;promptIcon;iconThrottler;eagerEvalSetting;previewRequestForTest;highlightingNode;#C;constructor(){super(),this.addCompletionsFromHistory=!0,this.historyInternal=new w,this.initialText="",this.eagerPreviewElement=document.createElement("div"),this.eagerPreviewElement.classList.add("console-eager-preview"),this.textChangeThrottler=new o.Throttler.Throttler(150),this.formatter=new m.RemoteObjectPreviewFormatter.RemoteObjectPreviewFormatter,this.requestPreviewBound=this.requestPreview.bind(this),this.innerPreviewElement=this.eagerPreviewElement.createChild("div","console-eager-inner-preview"),this.eagerPreviewElement.appendChild(p.Icon.Icon.create("smallicon-command-result","preview-result-icon"));const e=this.element.createChild("div","console-prompt-editor-container");this.element.appendChild(this.eagerPreviewElement),this.promptIcon=p.Icon.Icon.create("smallicon-text-prompt","console-prompt-icon"),this.element.appendChild(this.promptIcon),this.iconThrottler=new o.Throttler.Throttler(0),this.eagerEvalSetting=o.Settings.Settings.instance().moduleSetting("consoleEagerEval"),this.eagerEvalSetting.addChangeListener(this.eagerSettingChanged.bind(this)),this.eagerPreviewElement.classList.toggle("hidden",!this.eagerEvalSetting.get()),this.element.tabIndex=0,this.previewRequestForTest=null,this.highlightingNode=!1;const t=u.JavaScript.argumentHints();this.#C=t[0];const s=d.EditorState.create({doc:this.initialText,extensions:[d.keymap.of(this.editorKeymap()),d.EditorView.updateListener.of((e=>this.editorUpdate(e))),t,u.JavaScript.completion(),u.Config.showCompletionHint,d.javascript.javascript(),u.Config.baseConfiguration(this.initialText),u.Config.autocompletion,d.javascript.javascriptLanguage.data.of({autocomplete:e=>this.historyCompletions(e)}),d.EditorView.contentAttributes.of({"aria-label":C(v.consolePrompt)}),d.EditorView.lineWrapping,d.autocompletion({aboveCursor:!0})]});this.editor=new u.TextEditor.TextEditor(s),this.editor.addEventListener("keydown",(e=>{e.defaultPrevented&&e.stopPropagation()})),e.appendChild(this.editor),this.hasFocus()&&this.focus(),this.element.removeAttribute("tabindex"),this.editorSetForTest(),i.userMetrics.panelLoaded("console","DevTools.Launch.Console")}eagerSettingChanged(){const e=this.eagerEvalSetting.get();this.eagerPreviewElement.classList.toggle("hidden",!e),e&&this.requestPreview()}belowEditorElement(){return this.eagerPreviewElement}onTextChanged(){if(this.eagerEvalSetting.get()){const e=!u.Config.contentIncludingHint(this.editor.editor);this.previewRequestForTest=this.textChangeThrottler.schedule(this.requestPreviewBound,e)}this.updatePromptIcon(),this.dispatchEventToListeners("TextChanged")}async requestPreview(){const e=++this.requestPreviewCurrent,t=u.Config.contentIncludingHint(this.editor.editor).trim(),s=p.Context.Context.instance().flavor(a.RuntimeModel.ExecutionContext),{preview:n,result:o}=await m.JavaScriptREPL.JavaScriptREPL.evaluateAndBuildPreview(t,!0,!0,500);this.requestPreviewCurrent===e&&(this.innerPreviewElement.removeChildren(),n.deepTextContent()!==u.Config.contentIncludingHint(this.editor.editor).trim()&&this.innerPreviewElement.appendChild(n),o&&"object"in o&&o.object&&"node"===o.object.subtype?(this.highlightingNode=!0,a.OverlayModel.OverlayModel.highlightObjectAsDOMNode(o.object)):this.highlightingNode&&(this.highlightingNode=!1,a.OverlayModel.OverlayModel.hideDOMNodeHighlight()),o&&s&&s.runtimeModel.releaseEvaluationResult(o))}wasShown(){super.wasShown(),this.registerCSSFiles([f.default])}willHide(){this.highlightingNode&&(this.highlightingNode=!1,a.OverlayModel.OverlayModel.hideDOMNodeHighlight())}history(){return this.historyInternal}clearAutocomplete(){d.closeCompletion(this.editor.editor)}isCaretAtEndOfPrompt(){return this.editor.state.selection.main.head===this.editor.state.doc.length}moveCaretToEndOfPrompt(){this.editor.dispatch({selection:d.EditorSelection.cursor(this.editor.state.doc.length)})}clear(){this.editor.dispatch({changes:{from:0,to:this.editor.state.doc.length}})}text(){return this.editor.state.doc.toString()}setAddCompletionsFromHistory(e){this.addCompletionsFromHistory=e}editorKeymap(){return[{key:"ArrowUp",run:()=>this.moveHistory(-1)},{key:"ArrowDown",run:()=>this.moveHistory(1)},{mac:"Ctrl-p",run:()=>this.moveHistory(-1,!0)},{mac:"Ctrl-n",run:()=>this.moveHistory(1,!0)},{key:"Escape",run:()=>u.JavaScript.closeArgumentsHintsTooltip(this.editor.editor,this.#C)},{key:"Enter",run:()=>(this.handleEnter(),!0),shift:d.insertNewlineAndIndent}]}moveHistory(e,t=!1){const{editor:s}=this.editor,{main:n}=s.state.selection;if(!t){if(!n.empty)return!1;const t=s.coordsAtPos(n.head),o=s.coordsAtPos(e<0?0:s.state.doc.length);if(t&&o&&(e<0?t.top>o.top+5:t.bottom<o.bottom-5))return!1}const o=this.historyInternal,i=e<0?o.previous(this.text()):o.next();if(void 0===i)return!1;const r=e<0?i.search(/\n|$/):i.length;return this.editor.dispatch({changes:{from:0,to:this.editor.state.doc.length,insert:i},selection:d.EditorSelection.cursor(r),scrollIntoView:!0}),!0}async enterWillEvaluate(){const{state:e}=this.editor;return e.doc.length>0&&await u.JavaScript.isExpressionComplete(e.doc.toString())}async handleEnter(){await this.enterWillEvaluate()?(this.appendCommand(this.text(),!0),u.JavaScript.closeArgumentsHintsTooltip(this.editor.editor,this.#C),this.editor.dispatch({changes:{from:0,to:this.editor.state.doc.length},scrollIntoView:!0})):this.editor.state.doc.length?d.insertNewlineAndIndent(this.editor.editor):this.editor.dispatch({scrollIntoView:!0})}updatePromptIcon(){this.iconThrottler.schedule((async()=>{this.promptIcon.classList.toggle("console-prompt-incomplete",!await this.enterWillEvaluate())}))}appendCommand(e,t){const s=p.Context.Context.instance().flavor(a.RuntimeModel.ExecutionContext);if(s){const n=s,o=a.ConsoleModel.ConsoleModel.instance().addCommandMessage(n,e),r=m.JavaScriptREPL.JavaScriptREPL.preprocessExpression(e);this.evaluateCommandInConsole(n,o,r,t),g.ConsolePanel.instance().isShowing()&&i.userMetrics.actionTaken(i.UserMetrics.Action.CommandEvaluatedInConsolePanel)}}async evaluateCommandInConsole(e,t,s,n){if(l.Runtime.experiments.isEnabled("evaluateExpressionsWithSourceMaps")){const t=e.debuggerModel.selectedCallFrame();if(t){const e=await h.NamesResolver.allVariablesInCallFrame(t);s=await this.substituteNames(s,e)}}await a.ConsoleModel.ConsoleModel.instance().evaluateCommandInConsole(e,t,s,n)}async substituteNames(e,t){try{return await c.FormatterWorkerPool.formatterWorkerPool().javaScriptSubstitute(e,t)}catch{return e}}editorUpdate(e){e.docChanged||d.selectedCompletion(e.state)!==d.selectedCompletion(e.startState)?this.onTextChanged():e.selectionSet&&this.updatePromptIcon()}historyCompletions(e){const t=this.text();if(!this.addCompletionsFromHistory||!this.isCaretAtEndOfPrompt()||!t.length&&!e.explicit)return null;const s=[],n=new Set,o=this.historyInternal.historyData();for(let e=o.length-1;e>=0&&s.length<50;--e){const i=o[e];i.startsWith(t)&&(n.has(i)||(n.add(i),s.push({label:i,type:"secondary",boost:-1e5})))}return s.length?{from:0,to:t.length,options:s}:null}focus(){this.editor.focus()}editorSetForTest(){}}class w{data;historyOffset;uncommittedIsTop;constructor(){this.data=[],this.historyOffset=1}historyData(){return this.data}setHistoryData(e){this.data=e.slice(),this.historyOffset=1}pushHistoryItem(e){this.uncommittedIsTop&&(this.data.pop(),delete this.uncommittedIsTop),this.historyOffset=1,e!==this.currentHistoryItem()&&this.data.push(e)}pushCurrentText(e){this.uncommittedIsTop&&this.data.pop(),this.uncommittedIsTop=!0,this.data.push(e)}previous(e){if(!(this.historyOffset>this.data.length))return 1===this.historyOffset&&this.pushCurrentText(e),++this.historyOffset,this.currentHistoryItem()}next(){if(1!==this.historyOffset)return--this.historyOffset,this.currentHistoryItem()}currentHistoryItem(){return this.data[this.data.length-this.historyOffset]}}})),t.register("2XTyv",(function(s,n){e(s.exports,"ConsolePanel",(()=>l)),e(s.exports,"WrapperView",(()=>h)),e(s.exports,"ConsoleRevealer",(()=>d));var o=t("9z2ZV"),i=t("i5ql7");let r;class l extends o.Panel.Panel{view;constructor(){super("console"),this.view=i.ConsoleView.instance()}static instance(e={forceNew:null}){const{forceNew:t}=e;return r&&!t||(r=new l),r}static updateContextFlavor(){const e=l.instance().view;o.Context.Context.instance().setFlavor(i.ConsoleView,e.isShowing()?e:null)}wasShown(){super.wasShown();c&&c.isShowing()&&o.InspectorView.InspectorView.instance().setDrawerMinimized(!0),this.view.show(this.element),l.updateContextFlavor()}willHide(){super.willHide(),o.InspectorView.InspectorView.instance().setDrawerMinimized(!1),c&&c.showViewInWrapper(),l.updateContextFlavor()}searchableView(){return i.ConsoleView.instance().searchableView()}}let a,c=null;class h extends o.Widget.VBox{view;constructor(){super(),this.view=i.ConsoleView.instance()}static instance(){return c||(c=new h),c}wasShown(){l.instance().isShowing()?o.InspectorView.InspectorView.instance().setDrawerMinimized(!0):this.showViewInWrapper(),l.updateContextFlavor()}willHide(){o.InspectorView.InspectorView.instance().setDrawerMinimized(!1),l.updateContextFlavor()}showViewInWrapper(){this.view.show(this.element)}}class d{static instance(e={forceNew:null}){const{forceNew:t}=e;return a&&!t||(a=new d),a}async reveal(e){const t=i.ConsoleView.instance();t.isShowing()?t.focus():await o.ViewManager.ViewManager.instance().showView("console-view")}}})),t.register("i5ql7",(function(s,n){e(s.exports,"ConsoleView",(()=>F)),e(s.exports,"ConsoleViewFilter",(()=>P)),e(s.exports,"ActionDelegate",(()=>U));var o=t("koSS8"),i=t("e7bLS"),r=t("ixFnt"),l=t("lz7WY"),a=t("eQFvP"),c=t("fMswD"),h=t("fm4u4"),d=t("g4rSN"),u=t("7f6zc"),m=t("3T3mV"),p=t("hzgLE"),g=t("eKcu2"),f=t("a3yig"),v=t("9z2ZV"),b=t("bS6IU"),C=t("g0XHO"),x=t("i1dmU"),w=t("fhQNs"),S=t("lygHa"),I=t("5hKKG"),E=t("91fou"),T=t("cXqFI");const y={issuesWithColon:"{n, plural, =0 {No Issues} =1 {# Issue:} other {# Issues:}}",issueToolbarTooltipGeneral:"Some problems no longer generate console messages, but are surfaced in the issues tab.",issueToolbarClickToView:"Click to view {issueEnumeration}",issueToolbarClickToGoToTheIssuesTab:"Click to go to the issues tab",findStringInLogs:"Find string in logs",consoleSettings:"Console settings",groupSimilarMessagesInConsole:"Group similar messages in console",showCorsErrorsInConsole:"Show `CORS` errors in console",showConsoleSidebar:"Show console sidebar",hideConsoleSidebar:"Hide console sidebar",consoleSidebarShown:"Console sidebar shown",consoleSidebarHidden:"Console sidebar hidden",doNotClearLogOnPageReload:"Do not clear log on page reload / navigation",preserveLog:"Preserve log",hideNetwork:"Hide network",onlyShowMessagesFromTheCurrentContext:"Only show messages from the current context (`top`, `iframe`, `worker`, extension)",selectedContextOnly:"Selected context only",eagerlyEvaluateTextInThePrompt:"Eagerly evaluate text in the prompt",sHidden:"{n, plural, =1 {# hidden} other {# hidden}}",consoleCleared:"Console cleared",hideMessagesFromS:"Hide messages from {PH1}",saveAs:"Save as...",copyVisibleStyledSelection:"Copy visible styled selection",replayXhr:"Replay XHR",writingFile:"Writing file…",searching:"Searching…",filter:"Filter",egEventdCdnUrlacom:"e.g. `/eventd/ -cdn url:a.com`",verbose:"Verbose",info:"Info",warnings:"Warnings",errors:"Errors",logLevels:"Log levels",overriddenByFilterSidebar:"Overridden by filter sidebar",customLevels:"Custom levels",sOnly:"{PH1} only",allLevels:"All levels",defaultLevels:"Default levels",hideAll:"Hide all",logLevelS:"Log level: {PH1}",default:"Default",filteredMessagesInConsole:"{PH1} messages in console"},M=r.i18n.registerUIStrings("panels/console/ConsoleView.ts",y),k=r.i18n.getLocalizedString.bind(void 0,M);let R;class F extends v.Widget.VBox{searchableViewInternal;sidebar;isSidebarOpen;filter;consoleToolbarContainer;splitWidget;contentsElement;visibleViewMessages;hiddenByFilterCount;shouldBeHiddenCache;lastShownHiddenByFilterCount;currentMatchRangeIndex;searchRegex;groupableMessages;groupableMessageTitle;shortcuts;regexMatchRanges;consoleContextSelector;filterStatusText;showSettingsPaneSetting;showSettingsPaneButton;progressToolbarItem;groupSimilarSetting;showCorsErrorsSetting;preserveLogCheckbox;hideNetworkMessagesCheckbox;timestampsSetting;consoleHistoryAutocompleteSetting;pinPane;viewport;messagesElement;messagesCountElement;viewportThrottler;pendingBatchResize;onMessageResizedBound;promptElement;linkifier;consoleMessages;consoleGroupStarts;consoleHistorySetting;prompt;immediatelyFilterMessagesForTest;maybeDirtyWhileMuted;scheduledRefreshPromiseForTest;needsFullUpdate;buildHiddenCacheTimeout;searchShouldJumpBackwards;searchProgressIndicator;innerSearchTimeoutId;muteViewportUpdates;waitForScrollTimeout;issueCounter;pendingSidebarMessages=[];userHasOpenedSidebarAtLeastOnce=!1;issueToolbarThrottle;requestResolver=new d.RequestResolver.RequestResolver;issueResolver=new h.IssueResolver.IssueResolver;constructor(){super(),this.setMinimumSize(0,35),this.searchableViewInternal=new v.SearchableView.SearchableView(this,null),this.searchableViewInternal.element.classList.add("console-searchable-view"),this.searchableViewInternal.setPlaceholder(k(y.findStringInLogs)),this.searchableViewInternal.setMinimalSearchQuerySize(0),this.sidebar=new(0,I.ConsoleSidebar),this.sidebar.addEventListener("FilterSelected",this.onFilterChanged.bind(this)),this.isSidebarOpen=!1,this.filter=new P(this.onFilterChanged.bind(this)),this.consoleToolbarContainer=this.element.createChild("div","console-toolbar-container"),this.splitWidget=new v.SplitWidget.SplitWidget(!0,!1,"console.sidebar.width",100),this.splitWidget.setMainWidget(this.searchableViewInternal),this.splitWidget.setSidebarWidget(this.sidebar),this.splitWidget.show(this.element),this.splitWidget.hideSidebar(),this.splitWidget.enableShowModeSaving(),this.isSidebarOpen=this.splitWidget.showMode()===v.SplitWidget.ShowMode.Both,this.filter.setLevelMenuOverridden(this.isSidebarOpen),this.splitWidget.addEventListener(v.SplitWidget.Events.ShowModeChanged,(e=>{this.isSidebarOpen=e.data===v.SplitWidget.ShowMode.Both,this.isSidebarOpen&&(this.userHasOpenedSidebarAtLeastOnce||(i.userMetrics.actionTaken(i.UserMetrics.Action.ConsoleSidebarOpened),this.userHasOpenedSidebarAtLeastOnce=!0),this.pendingSidebarMessages.forEach((e=>{this.sidebar.onMessageAdded(e)})),this.pendingSidebarMessages=[]),this.filter.setLevelMenuOverridden(this.isSidebarOpen),this.onFilterChanged()})),this.contentsElement=this.searchableViewInternal.element,this.element.classList.add("console-view"),this.visibleViewMessages=[],this.hiddenByFilterCount=0,this.shouldBeHiddenCache=new Set,this.groupableMessages=new Map,this.groupableMessageTitle=new Map,this.shortcuts=new Map,this.regexMatchRanges=[],this.consoleContextSelector=new(0,b.ConsoleContextSelector),this.filterStatusText=new v.Toolbar.ToolbarText,this.filterStatusText.element.classList.add("dimmed"),this.showSettingsPaneSetting=o.Settings.Settings.instance().createSetting("consoleShowSettingsToolbar",!1),this.showSettingsPaneButton=new v.Toolbar.ToolbarSettingToggle(this.showSettingsPaneSetting,"largeicon-settings-gear",k(y.consoleSettings)),this.progressToolbarItem=new v.Toolbar.ToolbarItem(document.createElement("div")),this.groupSimilarSetting=o.Settings.Settings.instance().moduleSetting("consoleGroupSimilar"),this.groupSimilarSetting.addChangeListener((()=>this.updateMessageList()));const e=new v.Toolbar.ToolbarSettingCheckbox(this.groupSimilarSetting,k(y.groupSimilarMessagesInConsole));this.showCorsErrorsSetting=o.Settings.Settings.instance().moduleSetting("consoleShowsCorsErrors"),this.showCorsErrorsSetting.addChangeListener((()=>{i.userMetrics.showCorsErrorsSettingChanged(this.showCorsErrorsSetting.get()),this.updateMessageList()}));const t=new v.Toolbar.ToolbarSettingCheckbox(this.showCorsErrorsSetting,k(y.showCorsErrorsInConsole)),s=new v.Toolbar.Toolbar("console-main-toolbar",this.consoleToolbarContainer);s.makeWrappable(!0);const n=new v.Toolbar.Toolbar("",this.consoleToolbarContainer);s.appendToolbarItem(this.splitWidget.createShowHideSidebarButton(k(y.showConsoleSidebar),k(y.hideConsoleSidebar),k(y.consoleSidebarShown),k(y.consoleSidebarHidden))),s.appendToolbarItem(v.Toolbar.Toolbar.createActionButton(v.ActionRegistry.ActionRegistry.instance().action("console.clear"))),s.appendSeparator(),s.appendToolbarItem(this.consoleContextSelector.toolbarItem()),s.appendSeparator();const r=v.Toolbar.Toolbar.createActionButton(v.ActionRegistry.ActionRegistry.instance().action("console.create-pin"));s.appendToolbarItem(r),s.appendSeparator(),s.appendToolbarItem(this.filter.textFilterUI),s.appendToolbarItem(this.filter.levelMenuButton),s.appendToolbarItem(this.progressToolbarItem),s.appendSeparator(),this.issueCounter=new p.IssueCounter.IssueCounter,this.issueCounter.id="console-issues-counter";const l=new v.Toolbar.ToolbarItem(this.issueCounter);this.issueCounter.data={clickHandler:()=>{i.userMetrics.issuesPanelOpenedFrom(i.UserMetrics.IssueOpener.StatusBarIssuesCounter),v.ViewManager.ViewManager.instance().showView("issues-pane")},issuesManager:h.IssuesManager.IssuesManager.instance(),accessibleName:k(y.issueToolbarTooltipGeneral),displayMode:"OmitEmpty"},s.appendToolbarItem(l),n.appendSeparator(),n.appendToolbarItem(this.filterStatusText),n.appendToolbarItem(this.showSettingsPaneButton),this.preserveLogCheckbox=new v.Toolbar.ToolbarSettingCheckbox(o.Settings.Settings.instance().moduleSetting("preserveConsoleLog"),k(y.doNotClearLogOnPageReload),k(y.preserveLog)),this.hideNetworkMessagesCheckbox=new v.Toolbar.ToolbarSettingCheckbox(this.filter.hideNetworkMessagesSetting,this.filter.hideNetworkMessagesSetting.title(),k(y.hideNetwork));const c=new v.Toolbar.ToolbarSettingCheckbox(this.filter.filterByExecutionContextSetting,k(y.onlyShowMessagesFromTheCurrentContext),k(y.selectedContextOnly)),d=o.Settings.Settings.instance().moduleSetting("monitoringXHREnabled");this.timestampsSetting=o.Settings.Settings.instance().moduleSetting("consoleTimestampsEnabled"),this.consoleHistoryAutocompleteSetting=o.Settings.Settings.instance().moduleSetting("consoleHistoryAutocomplete");const u=new v.Widget.HBox;u.show(this.contentsElement),u.element.classList.add("console-settings-pane"),v.ARIAUtils.setAccessibleName(u.element,k(y.consoleSettings)),v.ARIAUtils.markAsGroup(u.element);const m=new v.Toolbar.Toolbar("",u.element);m.makeVertical(),m.appendToolbarItem(this.hideNetworkMessagesCheckbox),m.appendToolbarItem(this.preserveLogCheckbox),m.appendToolbarItem(c),m.appendToolbarItem(e),m.appendToolbarItem(t);const g=new v.Toolbar.Toolbar("",u.element);g.makeVertical(),g.appendToolbarItem(new v.Toolbar.ToolbarSettingCheckbox(d));const C=new v.Toolbar.ToolbarSettingCheckbox(o.Settings.Settings.instance().moduleSetting("consoleEagerEval"),k(y.eagerlyEvaluateTextInThePrompt));g.appendToolbarItem(C),g.appendToolbarItem(new v.Toolbar.ToolbarSettingCheckbox(this.consoleHistoryAutocompleteSetting));const x=new v.Toolbar.ToolbarSettingCheckbox(o.Settings.Settings.instance().moduleSetting("consoleUserActivationEval"));g.appendToolbarItem(x),this.showSettingsPaneSetting.get()||u.element.classList.add("hidden"),this.showSettingsPaneSetting.addChangeListener((()=>u.element.classList.toggle("hidden",!this.showSettingsPaneSetting.get()))),this.pinPane=new(0,w.ConsolePinPane)(r,(()=>this.prompt.focus())),this.pinPane.element.classList.add("console-view-pinpane"),this.pinPane.show(this.contentsElement),this.viewport=new(0,T.ConsoleViewport)(this),this.viewport.setStickToBottom(!0),this.viewport.contentElement().classList.add("console-group","console-group-messages"),this.contentsElement.appendChild(this.viewport.element),this.messagesElement=this.viewport.element,this.messagesElement.id="console-messages",this.messagesElement.classList.add("monospace"),this.messagesElement.addEventListener("click",this.messagesClicked.bind(this),!1),this.messagesElement.addEventListener("paste",this.messagesPasted.bind(this),!0),this.messagesElement.addEventListener("clipboard-paste",this.messagesPasted.bind(this),!0),this.messagesCountElement=this.consoleToolbarContainer.createChild("div","message-count"),v.ARIAUtils.markAsPoliteLiveRegion(this.messagesCountElement,!1),this.viewportThrottler=new o.Throttler.Throttler(50),this.pendingBatchResize=!1,this.onMessageResizedBound=e=>{this.onMessageResized(e)},this.promptElement=this.messagesElement.createChild("div","source-code"),this.promptElement.id="console-prompt";const M=this.messagesElement.createChild("div","console-view-fix-select-all");M.textContent=".",v.ARIAUtils.markAsHidden(M),this.registerShortcuts(),this.messagesElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!1);const R=new o.Throttler.Throttler(100);this.linkifier=new f.Linkifier.Linkifier(E.MaxLengthForLinks,void 0,(()=>R.schedule((async()=>this.onFilterChanged())))),this.consoleMessages=[],this.consoleGroupStarts=[],this.consoleHistorySetting=o.Settings.Settings.instance().createLocalSetting("consoleHistory",[]),this.prompt=new(0,S.ConsolePrompt),this.prompt.show(this.promptElement),this.prompt.element.addEventListener("keydown",this.promptKeyDown.bind(this),!0),this.prompt.addEventListener("TextChanged",this.promptTextChanged,this),this.messagesElement.addEventListener("keydown",this.messagesKeyDown.bind(this),!1),this.prompt.element.addEventListener("focusin",(()=>{this.isScrolledToBottom()&&this.viewport.setStickToBottom(!0)})),this.consoleHistoryAutocompleteSetting.addChangeListener(this.consoleHistoryAutocompleteChanged,this);const F=this.consoleHistorySetting.get();this.prompt.history().setHistoryData(F),this.consoleHistoryAutocompleteChanged(),this.updateFilterStatus(),this.timestampsSetting.addChangeListener(this.consoleTimestampsSettingChanged,this),this.registerWithMessageSink(),v.Context.Context.instance().addFlavorChangeListener(a.RuntimeModel.ExecutionContext,this.executionContextChanged,this),this.messagesElement.addEventListener("mousedown",(e=>this.updateStickToBottomOnPointerDown(2===e.button)),!1),this.messagesElement.addEventListener("mouseup",this.updateStickToBottomOnPointerUp.bind(this),!1),this.messagesElement.addEventListener("mouseleave",this.updateStickToBottomOnPointerUp.bind(this),!1),this.messagesElement.addEventListener("wheel",this.updateStickToBottomOnWheel.bind(this),!1),this.messagesElement.addEventListener("touchstart",this.updateStickToBottomOnPointerDown.bind(this,!1),!1),this.messagesElement.addEventListener("touchend",this.updateStickToBottomOnPointerUp.bind(this),!1),this.messagesElement.addEventListener("touchcancel",this.updateStickToBottomOnPointerUp.bind(this),!1),a.ConsoleModel.ConsoleModel.instance().addEventListener(a.ConsoleModel.Events.ConsoleCleared,this.consoleCleared,this),a.ConsoleModel.ConsoleModel.instance().addEventListener(a.ConsoleModel.Events.MessageAdded,this.onConsoleMessageAdded,this),a.ConsoleModel.ConsoleModel.instance().addEventListener(a.ConsoleModel.Events.MessageUpdated,this.onConsoleMessageUpdated,this),a.ConsoleModel.ConsoleModel.instance().addEventListener(a.ConsoleModel.Events.CommandEvaluated,this.commandEvaluated,this),a.ConsoleModel.ConsoleModel.instance().messages().forEach(this.addConsoleMessage,this);const L=h.IssuesManager.IssuesManager.instance();this.issueToolbarThrottle=new o.Throttler.Throttler(100),L.addEventListener("IssuesCountUpdated",(()=>this.issueToolbarThrottle.schedule((async()=>this.updateIssuesToolbarItem()))),this)}static instance(){return R||(R=new F),R}static clearConsole(){a.ConsoleModel.ConsoleModel.instance().requestClearMessages()}onFilterChanged(){if(this.filter.currentFilter.levelsMask=this.isSidebarOpen?x.ConsoleFilter.allLevelsFilterValue():this.filter.messageLevelFiltersSetting.get(),this.cancelBuildHiddenCache(),this.immediatelyFilterMessagesForTest){for(const e of this.consoleMessages)this.computeShouldMessageBeVisible(e);this.updateMessageList()}else this.buildHiddenCache(0,this.consoleMessages.slice())}setImmediatelyFilterMessagesForTest(){this.immediatelyFilterMessagesForTest=!0}searchableView(){return this.searchableViewInternal}clearHistory(){this.consoleHistorySetting.set([]),this.prompt.history().setHistoryData([])}consoleHistoryAutocompleteChanged(){this.prompt.setAddCompletionsFromHistory(this.consoleHistoryAutocompleteSetting.get())}itemCount(){return this.visibleViewMessages.length}itemElement(e){return this.visibleViewMessages[e]}fastHeight(e){return this.visibleViewMessages[e].fastHeight()}minimumRowHeight(){return 16}registerWithMessageSink(){o.Console.Console.instance().messages().forEach(this.addSinkMessage,this),o.Console.Console.instance().addEventListener(o.Console.Events.MessageAdded,(({data:e})=>{this.addSinkMessage(e)}),this)}addSinkMessage(e){let t="verbose";switch(e.level){case o.Console.MessageLevel.Info:t="info";break;case o.Console.MessageLevel.Error:t="error";break;case o.Console.MessageLevel.Warning:t="warning"}const s=new a.ConsoleModel.ConsoleMessage(null,"other",t,e.text,{type:a.ConsoleModel.FrontendMessageType.System,timestamp:e.timestamp});this.addConsoleMessage(s)}consoleTimestampsSettingChanged(){this.updateMessageList(),this.consoleMessages.forEach((e=>e.updateTimestamp())),this.groupableMessageTitle.forEach((e=>e.updateTimestamp()))}executionContextChanged(){this.prompt.clearAutocomplete()}willHide(){this.hidePromptSuggestBox()}wasShown(){super.wasShown(),this.updateIssuesToolbarItem(),this.viewport.refresh(),this.registerCSSFiles([C.default,g.default,m.Style.default])}focus(){this.viewport.hasVirtualSelection()?this.viewport.contentElement().focus():this.focusPrompt()}focusPrompt(){if(!this.prompt.hasFocus()){const e=this.viewport.stickToBottom(),t=this.viewport.element.scrollTop;this.prompt.focus(),this.viewport.setStickToBottom(e),this.viewport.element.scrollTop=t}}restoreScrollPositions(){this.viewport.stickToBottom()?this.immediatelyScrollToBottom():super.restoreScrollPositions()}onResize(){this.scheduleViewportRefresh(),this.hidePromptSuggestBox(),this.viewport.stickToBottom()&&this.immediatelyScrollToBottom();for(let e=0;e<this.visibleViewMessages.length;++e)this.visibleViewMessages[e].onResize()}hidePromptSuggestBox(){this.prompt.clearAutocomplete()}async invalidateViewport(){this.updateIssuesToolbarItem(),this.muteViewportUpdates?this.maybeDirtyWhileMuted=!0:this.needsFullUpdate?(this.updateMessageList(),delete this.needsFullUpdate):this.viewport.invalidate()}updateIssuesToolbarItem(){const e=h.IssuesManager.IssuesManager.instance(),t=p.IssueCounter.getIssueCountsEnumeration(e),s=0===e.numberOfIssues()?k(y.issueToolbarClickToGoToTheIssuesTab):k(y.issueToolbarClickToView,{issueEnumeration:t}),n=`${k(y.issueToolbarTooltipGeneral)} ${s}`;v.Tooltip.Tooltip.install(this.issueCounter,n),this.issueCounter.data={...this.issueCounter.data,leadingText:k(y.issuesWithColon,{n:e.numberOfIssues()}),accessibleName:n}}scheduleViewportRefresh(){if(this.muteViewportUpdates)return this.maybeDirtyWhileMuted=!0,void this.scheduleViewportRefreshForTest(!0);this.scheduleViewportRefreshForTest(!1),this.scheduledRefreshPromiseForTest=this.viewportThrottler.schedule(this.invalidateViewport.bind(this))}scheduleViewportRefreshForTest(e){}immediatelyScrollToBottom(){this.viewport.setStickToBottom(!0),this.promptElement.scrollIntoView(!0)}updateFilterStatus(){this.hiddenByFilterCount!==this.lastShownHiddenByFilterCount&&(this.filterStatusText.setText(k(y.sHidden,{n:this.hiddenByFilterCount})),this.filterStatusText.setVisible(Boolean(this.hiddenByFilterCount)),this.lastShownHiddenByFilterCount=this.hiddenByFilterCount)}onConsoleMessageAdded(e){const t=e.data;this.addConsoleMessage(t)}addConsoleMessage(e){const t=this.createViewMessage(e);if(O.set(e,t),e.type===a.ConsoleModel.FrontendMessageType.Command||e.type===a.ConsoleModel.FrontendMessageType.Result){const e=this.consoleMessages[this.consoleMessages.length-1],s=e&&H.get(e)||0;H.set(t,s)}else H.set(t,t.consoleMessage().timestamp);let s;s=!this.consoleMessages.length||r(t,this.consoleMessages[this.consoleMessages.length-1])>0?this.consoleMessages.length:l.ArrayUtilities.upperBound(this.consoleMessages,t,r);const n=s<this.consoleMessages.length;if(this.consoleMessages.splice(s,0,t),e.type!==a.ConsoleModel.FrontendMessageType.Command&&e.type!==a.ConsoleModel.FrontendMessageType.Result){const n=l.ArrayUtilities.upperBound(this.consoleGroupStarts,t,r)-1;if(n>=0){!function e(t,s){const n=s.groupEnd();if(null!==n&&r(t,n)>0){const n=s.consoleGroup();if(null===n)return;return void e(t,n)}"endGroup"===t.consoleMessage().type?s.setGroupEnd(t):t.setConsoleGroup(s)}(t,this.consoleGroupStarts[n])}e.isGroupStartMessage()&&(s=l.ArrayUtilities.upperBound(this.consoleGroupStarts,t,r),this.consoleGroupStarts.splice(s,0,t))}this.filter.onMessageAdded(e),this.isSidebarOpen?this.sidebar.onMessageAdded(t):this.pendingSidebarMessages.push(t);let o=!1;const i=this.groupSimilarSetting.get();if(e.isGroupable()){const e=t.groupKey();o=i&&this.groupableMessages.has(e);let s=this.groupableMessages.get(e);s||(s=[],this.groupableMessages.set(e,s)),s.push(t)}function r(e,t){return(H.get(e)||0)-(H.get(t)||0)}this.computeShouldMessageBeVisible(t),o||n?this.needsFullUpdate=!0:(this.appendMessageToEnd(t,!i),this.updateFilterStatus(),this.searchableViewInternal.updateSearchMatchesCount(this.regexMatchRanges.length)),this.scheduleViewportRefresh(),this.consoleMessageAddedForTest(t)}onConsoleMessageUpdated(e){const t=e.data,s=O.get(t);s&&(s.updateMessageElement(),this.computeShouldMessageBeVisible(s),this.updateMessageList())}consoleMessageAddedForTest(e){}shouldMessageBeVisible(e){return!this.shouldBeHiddenCache.has(e)}computeShouldMessageBeVisible(e){!this.filter.shouldBeVisible(e)||this.isSidebarOpen&&!this.sidebar.shouldBeVisible(e)?this.shouldBeHiddenCache.add(e):this.shouldBeHiddenCache.delete(e)}appendMessageToEnd(e,t){if("cors"===e.consoleMessage().category&&!this.showCorsErrorsSetting.get())return;const s=this.visibleViewMessages[this.visibleViewMessages.length-1];if("endGroup"===e.consoleMessage().type){if(s){const e=s.consoleGroup();e&&!e.messagesHidden()&&s.incrementCloseGroupDecorationCount()}return}if(!this.shouldMessageBeVisible(e))return void this.hiddenByFilterCount++;if(!t&&this.tryToCollapseMessages(e,this.visibleViewMessages[this.visibleViewMessages.length-1]))return;const n=e.consoleGroup();if(!n||!n.messagesHidden()){const t=e.consoleMessage().originatingMessage();s&&t&&s.consoleMessage()===t&&e.toMessageElement().classList.add("console-adjacent-user-command-result"),function e(t,s){if(null===t)return;if(s.includes(t))return;const n=t.consoleGroup();n&&e(n,s);s.push(t)}(n,this.visibleViewMessages),this.visibleViewMessages.push(e),this.searchMessage(this.visibleViewMessages.length-1)}this.messageAppendedForTests()}messageAppendedForTests(){}createViewMessage(e){switch(e.type){case a.ConsoleModel.FrontendMessageType.Command:return new(0,E.ConsoleCommand)(e,this.linkifier,this.requestResolver,this.issueResolver,this.onMessageResizedBound);case a.ConsoleModel.FrontendMessageType.Result:return new(0,E.ConsoleCommandResult)(e,this.linkifier,this.requestResolver,this.issueResolver,this.onMessageResizedBound);case"startGroupCollapsed":case"startGroup":return new(0,E.ConsoleGroupViewMessage)(e,this.linkifier,this.requestResolver,this.issueResolver,this.updateMessageList.bind(this),this.onMessageResizedBound);case"table":return new(0,E.ConsoleTableMessageView)(e,this.linkifier,this.requestResolver,this.issueResolver,this.onMessageResizedBound);default:return new(0,E.ConsoleViewMessage)(e,this.linkifier,this.requestResolver,this.issueResolver,this.onMessageResizedBound)}}async onMessageResized(e){const t=e.data;if(this.pendingBatchResize||!t.treeOutline)return;this.pendingBatchResize=!0,await Promise.resolve();const s=t.treeOutline.element;this.viewport.setStickToBottom(this.isScrolledToBottom()),s.offsetHeight<=this.messagesElement.offsetHeight&&s.scrollIntoViewIfNeeded(),this.pendingBatchResize=!1}consoleCleared(){const e=this.viewport.element.hasFocus();this.cancelBuildHiddenCache(),this.currentMatchRangeIndex=-1,this.consoleMessages=[],this.groupableMessages.clear(),this.groupableMessageTitle.clear(),this.sidebar.clear(),this.updateMessageList(),this.hidePromptSuggestBox(),this.viewport.setStickToBottom(!0),this.linkifier.reset(),this.filter.clear(),this.requestResolver.clear(),this.consoleGroupStarts=[],e&&this.prompt.focus(),v.ARIAUtils.alert(k(y.consoleCleared))}handleContextMenuEvent(e){const t=new v.ContextMenu.ContextMenu(e),s=e.target;if(s.isSelfOrDescendant(this.promptElement))return void t.show();const n=s.enclosingNodeOrSelfWithClass("console-message-wrapper"),i=n&&(0,E.getMessageForElement)(n),r=i?i.consoleMessage():null;if(r&&r.url){const e=k(y.hideMessagesFromS,{PH1:new o.ParsedURL.ParsedURL(r.url).displayName});t.headerSection().appendItem(e,this.filter.addMessageURLFilter.bind(this.filter,r.url))}if(t.defaultSection().appendAction("console.clear"),t.defaultSection().appendAction("console.clear.history"),t.saveSection().appendItem(k(y.saveAs),this.saveConsole.bind(this)),this.element.hasSelection()&&t.clipboardSection().appendItem(k(y.copyVisibleStyledSelection),this.viewport.copyWithStyles.bind(this.viewport)),r){const e=d.NetworkLog.NetworkLog.requestForConsoleMessage(r);e&&a.NetworkManager.NetworkManager.canReplayRequest(e)&&t.debugSection().appendItem(k(y.replayXhr),a.NetworkManager.NetworkManager.replayRequest.bind(null,e))}t.show()}async saveConsole(){const e=a.TargetManager.TargetManager.instance().mainTarget().inspectedURL(),t=o.ParsedURL.ParsedURL.fromString(e),s=l.StringUtilities.sprintf("%s-%d.log",t?t.host:"console",Date.now()),n=new c.FileUtils.FileOutputStream,i=new v.ProgressIndicator.ProgressIndicator;i.setTitle(k(y.writingFile)),i.setTotalWork(this.itemCount());if(!await n.open(s))return;this.progressToolbarItem.element.appendChild(i.element);let r=0;for(;r<this.itemCount()&&!i.isCanceled();){const e=[];let t;for(t=0;t<350&&t+r<this.itemCount();++t){const s=this.itemElement(r+t);e.push(s.toExportString())}r+=t,await n.write(e.join("\n")+"\n"),i.setWorked(r)}n.close(),i.done()}tryToCollapseMessages(e,t){return!(this.timestampsSetting.get()||!t||e.consoleMessage().isGroupMessage()||e.consoleMessage().type===a.ConsoleModel.FrontendMessageType.Command||e.consoleMessage().type===a.ConsoleModel.FrontendMessageType.Result||!e.consoleMessage().isEqual(t.consoleMessage()))&&(t.incrementRepeatCount(),e.isLastInSimilarGroup()&&t.setInSimilarGroup(!0,!0),!0)}buildHiddenCache(e,t){const s=Date.now();let n;for(n=e;n<t.length&&(this.computeShouldMessageBeVisible(t[n]),!(n%10==0&&Date.now()-s>12));++n);n!==t.length?this.buildHiddenCacheTimeout=this.element.window().requestAnimationFrame(this.buildHiddenCache.bind(this,n+1,t)):this.updateMessageList()}cancelBuildHiddenCache(){this.shouldBeHiddenCache.clear(),this.buildHiddenCacheTimeout&&(this.element.window().cancelAnimationFrame(this.buildHiddenCacheTimeout),delete this.buildHiddenCacheTimeout)}updateMessageList(){this.regexMatchRanges=[],this.hiddenByFilterCount=0;for(const e of this.visibleViewMessages)e.resetCloseGroupDecorationCount(),e.resetIncrementRepeatCount();if(this.visibleViewMessages=[],this.groupSimilarSetting.get())this.addGroupableMessagesToEnd();else for(const e of this.consoleMessages)e.setInSimilarGroup(!1),e.consoleMessage().isGroupable()&&e.clearConsoleGroup(),this.appendMessageToEnd(e,!0);this.updateFilterStatus(),this.searchableViewInternal.updateSearchMatchesCount(this.regexMatchRanges.length),this.viewport.invalidate(),this.messagesCountElement.setAttribute("aria-label",k(y.filteredMessagesInConsole,{PH1:this.visibleViewMessages.length}))}addGroupableMessagesToEnd(){const e=new Set,t=new Set;for(const s of this.consoleMessages){const n=s.consoleMessage();if(e.has(n))continue;if(!n.isGroupable()){this.appendMessageToEnd(s),e.add(n);continue}const o=s.groupKey(),i=this.groupableMessages.get(o);if(!i||i.length<5){s.setInSimilarGroup(!1),this.appendMessageToEnd(s),e.add(n);continue}if(t.has(o))continue;if(!i.find((e=>this.shouldMessageBeVisible(e)))){l.SetUtilities.addAll(e,i),t.add(o);continue}let r=this.groupableMessageTitle.get(o);if(!r){const e=new a.ConsoleModel.ConsoleMessage(null,n.source,n.level,s.groupTitle(),{type:"startGroupCollapsed"});r=this.createViewMessage(e),this.groupableMessageTitle.set(o,r)}r.setRepeatCount(i.length),this.appendMessageToEnd(r);for(const t of i)t.setInSimilarGroup(!0,i[i.length-1]===t),t.setConsoleGroup(r),this.appendMessageToEnd(t,!0),e.add(t.consoleMessage());const c=new a.ConsoleModel.ConsoleMessage(null,n.source,n.level,n.messageText,{type:"endGroup"});this.appendMessageToEnd(this.createViewMessage(c))}}messagesClicked(e){const t=e.target;if(!this.messagesElement.hasSelection()){(t===this.messagesElement||this.prompt.belowEditorElement().isSelfOrAncestor(t))&&(this.prompt.moveCaretToEndOfPrompt(),this.focusPrompt())}}messagesKeyDown(e){const t=e;t.ctrlKey||t.altKey||t.metaKey||1!==t.key.length||v.UIUtils.isEditing()||this.messagesElement.hasSelection()||(this.prompt.moveCaretToEndOfPrompt(),this.focusPrompt())}messagesPasted(e){v.UIUtils.isEditing()||this.prompt.focus()}registerShortcuts(){this.shortcuts.set(v.KeyboardShortcut.KeyboardShortcut.makeKey("u",v.KeyboardShortcut.Modifiers.Ctrl),this.clearPromptBackwards.bind(this))}clearPromptBackwards(){this.prompt.clear()}promptKeyDown(e){const t=e;if("PageUp"===t.key)return void this.updateStickToBottomOnWheel();const s=v.KeyboardShortcut.KeyboardShortcut.makeKeyFromEvent(t),n=this.shortcuts.get(s);n&&(n(),t.preventDefault())}printResult(e,t,s){if(!e)return;const n=Boolean(s)?"error":"info";let o;o=s?a.ConsoleModel.ConsoleMessage.fromException(e.runtimeModel(),s,a.ConsoleModel.FrontendMessageType.Result,void 0,void 0):new a.ConsoleModel.ConsoleMessage(e.runtimeModel(),"javascript",n,"",{type:a.ConsoleModel.FrontendMessageType.Result,parameters:[e]}),o.setOriginatingMessage(t),a.ConsoleModel.ConsoleModel.instance().addMessage(o)}commandEvaluated(e){const{data:t}=e;this.prompt.history().pushHistoryItem(t.commandMessage.messageText),this.consoleHistorySetting.set(this.prompt.history().historyData().slice(-L)),this.printResult(t.result,t.commandMessage,t.exceptionDetails)}elementsToRestoreScrollPositionsFor(){return[this.messagesElement]}searchCanceled(){this.cleanupAfterSearch();for(const e of this.visibleViewMessages)e.setSearchRegex(null);this.currentMatchRangeIndex=-1,this.regexMatchRanges=[],this.searchRegex=null,this.viewport.refresh()}performSearch(e,t,s){this.searchCanceled(),this.searchableViewInternal.updateSearchMatchesCount(0),this.searchRegex=e.toSearchRegex(!0).regex,this.regexMatchRanges=[],this.currentMatchRangeIndex=-1,t&&(this.searchShouldJumpBackwards=Boolean(s)),this.searchProgressIndicator=new v.ProgressIndicator.ProgressIndicator,this.searchProgressIndicator.setTitle(k(y.searching)),this.searchProgressIndicator.setTotalWork(this.visibleViewMessages.length),this.progressToolbarItem.element.appendChild(this.searchProgressIndicator.element),this.innerSearch(0)}cleanupAfterSearch(){delete this.searchShouldJumpBackwards,this.innerSearchTimeoutId&&(clearTimeout(this.innerSearchTimeoutId),delete this.innerSearchTimeoutId),this.searchProgressIndicator&&(this.searchProgressIndicator.done(),delete this.searchProgressIndicator)}searchFinishedForTests(){}innerSearch(e){if(delete this.innerSearchTimeoutId,this.searchProgressIndicator&&this.searchProgressIndicator.isCanceled())return void this.cleanupAfterSearch();const t=Date.now();for(;e<this.visibleViewMessages.length&&Date.now()-t<100;++e)this.searchMessage(e);if(this.searchableViewInternal.updateSearchMatchesCount(this.regexMatchRanges.length),void 0!==this.searchShouldJumpBackwards&&this.regexMatchRanges.length&&(this.jumpToMatch(this.searchShouldJumpBackwards?-1:0),delete this.searchShouldJumpBackwards),e===this.visibleViewMessages.length)return this.cleanupAfterSearch(),void window.setTimeout(this.searchFinishedForTests.bind(this),0);this.innerSearchTimeoutId=window.setTimeout(this.innerSearch.bind(this,e),100),this.searchProgressIndicator&&this.searchProgressIndicator.setWorked(e)}searchMessage(e){const t=this.visibleViewMessages[e];t.setSearchRegex(this.searchRegex);for(let s=0;s<t.searchCount();++s)this.regexMatchRanges.push({messageIndex:e,matchIndex:s})}jumpToNextSearchResult(){this.jumpToMatch(this.currentMatchRangeIndex+1)}jumpToPreviousSearchResult(){this.jumpToMatch(this.currentMatchRangeIndex-1)}supportsCaseSensitiveSearch(){return!0}supportsRegexSearch(){return!0}jumpToMatch(e){if(!this.regexMatchRanges.length)return;let t;if(this.currentMatchRangeIndex>=0){t=this.regexMatchRanges[this.currentMatchRangeIndex];this.visibleViewMessages[t.messageIndex].searchHighlightNode(t.matchIndex).classList.remove(v.UIUtils.highlightedCurrentSearchResultClassName)}e=l.NumberUtilities.mod(e,this.regexMatchRanges.length),this.currentMatchRangeIndex=e,this.searchableViewInternal.updateCurrentMatchIndex(e),t=this.regexMatchRanges[e];const s=this.visibleViewMessages[t.messageIndex].searchHighlightNode(t.matchIndex);s.classList.add(v.UIUtils.highlightedCurrentSearchResultClassName),this.viewport.scrollItemIntoView(t.messageIndex),s.scrollIntoViewIfNeeded()}updateStickToBottomOnPointerDown(e){this.muteViewportUpdates=!e,this.viewport.setStickToBottom(!1),this.waitForScrollTimeout&&(clearTimeout(this.waitForScrollTimeout),delete this.waitForScrollTimeout)}updateStickToBottomOnPointerUp(){this.muteViewportUpdates&&(this.waitForScrollTimeout=window.setTimeout(function(){this.muteViewportUpdates=!1,this.isShowing()&&this.viewport.setStickToBottom(this.isScrolledToBottom());this.maybeDirtyWhileMuted&&(this.scheduleViewportRefresh(),delete this.maybeDirtyWhileMuted);delete this.waitForScrollTimeout,this.updateViewportStickinessForTest()}.bind(this),200))}updateViewportStickinessForTest(){}updateStickToBottomOnWheel(){this.updateStickToBottomOnPointerDown(),this.updateStickToBottomOnPointerUp()}promptTextChanged(){const e=this.viewport.stickToBottom(),t=this.isScrolledToBottom();this.viewport.setStickToBottom(t),t&&!e&&this.scheduleViewportRefresh(),this.promptTextChangedForTest()}promptTextChangedForTest(){}isScrolledToBottom(){return this.messagesElement.scrollHeight-this.messagesElement.scrollTop-this.messagesElement.clientHeight-this.prompt.belowEditorElement().offsetHeight<=2}}globalThis.Console=globalThis.Console||{},globalThis.Console.ConsoleView=F;const L=300;class P{filterChanged;messageLevelFiltersSetting;hideNetworkMessagesSetting;filterByExecutionContextSetting;suggestionBuilder;textFilterUI;textFilterSetting;filterParser;currentFilter;levelLabels;levelMenuButton;constructor(e){this.filterChanged=e,this.messageLevelFiltersSetting=P.levelFilterSetting(),this.hideNetworkMessagesSetting=o.Settings.Settings.instance().moduleSetting("hideNetworkMessages"),this.filterByExecutionContextSetting=o.Settings.Settings.instance().moduleSetting("selectedContextFilterEnabled"),this.messageLevelFiltersSetting.addChangeListener(this.onFilterChanged.bind(this)),this.hideNetworkMessagesSetting.addChangeListener(this.onFilterChanged.bind(this)),this.filterByExecutionContextSetting.addChangeListener(this.onFilterChanged.bind(this)),v.Context.Context.instance().addFlavorChangeListener(a.RuntimeModel.ExecutionContext,this.onFilterChanged,this);const t=Object.values(x.FilterType);this.suggestionBuilder=new v.FilterSuggestionBuilder.FilterSuggestionBuilder(t),this.textFilterUI=new v.Toolbar.ToolbarInput(k(y.filter),"",1,1,k(y.egEventdCdnUrlacom),this.suggestionBuilder.completions.bind(this.suggestionBuilder),!0),this.textFilterSetting=o.Settings.Settings.instance().createSetting("console.textFilter",""),this.textFilterSetting.get()&&this.textFilterUI.setValue(this.textFilterSetting.get()),this.textFilterUI.addEventListener(v.Toolbar.ToolbarInput.Event.TextChanged,(()=>{this.textFilterSetting.set(this.textFilterUI.value()),this.onFilterChanged()})),this.filterParser=new u.TextUtils.FilterParser(t),this.currentFilter=new(0,x.ConsoleFilter)("",[],null,this.messageLevelFiltersSetting.get()),this.updateCurrentFilter(),this.levelLabels=new Map([["verbose",k(y.verbose)],["info",k(y.info)],["warning",k(y.warnings)],["error",k(y.errors)]]),this.levelMenuButton=new v.Toolbar.ToolbarButton(k(y.logLevels)),this.levelMenuButton.turnIntoSelect(),this.levelMenuButton.addEventListener(v.Toolbar.ToolbarButton.Events.Click,this.showLevelContextMenu.bind(this)),v.ARIAUtils.markAsMenuButton(this.levelMenuButton.element),this.updateLevelMenuButtonText(),this.messageLevelFiltersSetting.addChangeListener(this.updateLevelMenuButtonText.bind(this))}onMessageAdded(e){e.type===a.ConsoleModel.FrontendMessageType.Command||e.type===a.ConsoleModel.FrontendMessageType.Result||e.isGroupMessage()||(e.context&&this.suggestionBuilder.addItem(x.FilterType.Context,e.context),e.source&&this.suggestionBuilder.addItem(x.FilterType.Source,e.source),e.url&&this.suggestionBuilder.addItem(x.FilterType.Url,e.url))}setLevelMenuOverridden(e){this.levelMenuButton.setEnabled(!e),e?this.levelMenuButton.setTitle(k(y.overriddenByFilterSidebar)):this.updateLevelMenuButtonText()}static levelFilterSetting(){return o.Settings.Settings.instance().createSetting("messageLevelFilters",x.ConsoleFilter.defaultLevelsFilterValue())}updateCurrentFilter(){const e=this.filterParser.parse(this.textFilterUI.value());this.hideNetworkMessagesSetting.get()&&e.push({key:x.FilterType.Source,text:"network",negative:!0,regex:void 0}),this.currentFilter.executionContext=this.filterByExecutionContextSetting.get()?v.Context.Context.instance().flavor(a.RuntimeModel.ExecutionContext):null,this.currentFilter.parsedFilters=e,this.currentFilter.levelsMask=this.messageLevelFiltersSetting.get()}onFilterChanged(){this.updateCurrentFilter(),this.filterChanged()}updateLevelMenuButtonText(){let e=!0,t=!0;const s=x.ConsoleFilter.allLevelsFilterValue(),n=x.ConsoleFilter.defaultLevelsFilterValue();let o=null;const i=this.messageLevelFiltersSetting.get(),r={Verbose:"verbose",Info:"info",Warning:"warning",Error:"error"};for(const l of Object.values(r))e=e&&i[l]===s[l],t=t&&i[l]===n[l],i[l]&&(o=o?k(y.customLevels):k(y.sOnly,{PH1:String(this.levelLabels.get(l))}));o=e?k(y.allLevels):t?k(y.defaultLevels):o||k(y.hideAll),this.levelMenuButton.element.classList.toggle("warning",!e&&!t),this.levelMenuButton.setText(o),this.levelMenuButton.setTitle(k(y.logLevelS,{PH1:o}))}showLevelContextMenu(e){const t=e.data,s=this.messageLevelFiltersSetting,n=s.get(),o=new v.ContextMenu.ContextMenu(t,{useSoftMenu:!0,x:this.levelMenuButton.element.totalOffsetLeft(),y:this.levelMenuButton.element.totalOffsetTop()+this.levelMenuButton.element.offsetHeight});o.headerSection().appendItem(k(y.default),(()=>s.set(x.ConsoleFilter.defaultLevelsFilterValue())));for(const[e,t]of this.levelLabels.entries())o.defaultSection().appendCheckboxItem(t,i.bind(null,e),n[e]);function i(e){n[e]=!n[e],s.set(n)}o.show()}addMessageURLFilter(e){if(!e)return;const t=this.textFilterUI.value()?` ${this.textFilterUI.value()}`:"";this.textFilterUI.setValue(`-url:${e}${t}`),this.textFilterSetting.set(this.textFilterUI.value()),this.onFilterChanged()}shouldBeVisible(e){return this.currentFilter.shouldBeVisible(e)}clear(){this.suggestionBuilder.clear()}reset(){this.messageLevelFiltersSetting.set(x.ConsoleFilter.defaultLevelsFilterValue()),this.filterByExecutionContextSetting.set(!1),this.hideNetworkMessagesSetting.set(!1),this.textFilterUI.setValue(""),this.onFilterChanged()}}let A;class U{handleAction(e,t){switch(t){case"console.show":return i.InspectorFrontendHost.InspectorFrontendHostInstance.bringToFront(),o.Console.Console.instance().show(),F.instance().focusPrompt(),!0;case"console.clear":return F.clearConsole(),!0;case"console.clear.history":return F.instance().clearHistory(),!0;case"console.create-pin":return F.instance().pinPane.addPin("",!0),!0}return!1}static instance(e={forceNew:null}){const{forceNew:t}=e;return A&&!t||(A=new U),A}}const H=new WeakMap,O=new WeakMap})),t.register("5jm5f",(function(t,s){e(t.exports,"default",(()=>o));const n=new CSSStyleSheet;n.replaceSync("/*\n * Copyright 2018 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n#console-prompt .CodeMirror {\n  padding: 3px 0 1px;\n}\n\n#console-prompt .CodeMirror-line {\n  padding-top: 0;\n}\n\n#console-prompt .CodeMirror-lines {\n  padding-top: 0;\n}\n\n#console-prompt .console-prompt-icon {\n  position: absolute;\n  left: -13px;\n  top: 5px;\n  user-select: none;\n}\n\n.console-eager-preview {\n  padding-bottom: 2px;\n  opacity: 60%;\n  position: relative;\n  height: 15px;\n}\n\n.console-eager-inner-preview {\n  text-overflow: ellipsis;\n  overflow: hidden;\n  margin-left: 4px;\n  height: 100%;\n  white-space: nowrap;\n}\n\n.preview-result-icon {\n  position: absolute;\n  left: -13px;\n  top: 1px;\n}\n\n.console-eager-inner-preview:empty,\n.console-eager-inner-preview:empty + .preview-result-icon {\n  opacity: 0%;\n}\n\n.console-prompt-icon.console-prompt-incomplete {\n  opacity: 65%;\n}\n\n/*# sourceURL=consolePrompt.css */\n");var o=n}));
//# sourceMappingURL=console.e90d3976.js.map
