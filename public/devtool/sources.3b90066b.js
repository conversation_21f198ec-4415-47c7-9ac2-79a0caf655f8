function e(e,s,i,t){Object.defineProperty(e,s,{get:i,set:t,enumerable:!0,configurable:!0})}var s=("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}).parcelRequire20b5;s.register("hzgLE",(function(i,t){e(i.exports,"IssueCounter",(()=>s("ggl3n"))),e(i.exports,"IssueLinkIcon",(()=>s("8PEby")));s("ggl3n"),s("8PEby")})),s.register("ggl3n",(function(i,t){e(i.exports,"getIssueKindIconData",(()=>m)),e(i.exports,"getIssueCountsEnumeration",(()=>I)),e(i.exports,"IssueCounter",(()=>v));var n=s("koSS8"),o=s("ixFnt"),r=s("fm4u4"),a=s("kpUjp"),l=s("dS5IF"),u=s("as4jY");const c={pageErrors:"{issueCount, plural, =1 {# page error} other {# page errors}}",breakingChanges:"{issueCount, plural, =1 {# breaking change} other {# breaking changes}}",possibleImprovements:"{issueCount, plural, =1 {# possible improvement} other {# possible improvements}}"},h=o.i18n.registerUIStrings("ui/components/issue_counter/IssueCounter.ts",c),d=o.i18n.getLocalizedString.bind(void 0,h);function m(e){switch(e){case r.Issue.IssueKind.PageError:return{iconName:"issue-cross-icon",color:"var(--issue-color-red)",width:"16px",height:"16px"};case r.Issue.IssueKind.BreakingChange:return{iconName:"issue-exclamation-icon",color:"var(--issue-color-yellow)",width:"16px",height:"16px"};case r.Issue.IssueKind.Improvement:return{iconName:"issue-text-icon",color:"var(--issue-color-blue)",width:"16px",height:"16px"}}}function p({iconName:e,color:s,width:i,height:t},n){return n?{iconName:e,iconColor:s,iconWidth:n,iconHeight:n}:{iconName:e,iconColor:s,iconWidth:i,iconHeight:t}}const g=new Intl.ListFormat(navigator.language,{type:"unit",style:"short"});function I(e,s=!0){const i=[e.numberOfIssues(r.Issue.IssueKind.PageError),e.numberOfIssues(r.Issue.IssueKind.BreakingChange),e.numberOfIssues(r.Issue.IssueKind.Improvement)],t=[d(c.pageErrors,{issueCount:i[0]}),d(c.breakingChanges,{issueCount:i[1]}),d(c.possibleImprovements,{issueCount:i[2]})];return g.format(t.filter(((e,t)=>!s||i[t]>0)))}class v extends HTMLElement{static litTagName=l.literal`issue-counter`;#e=this.attachShadow({mode:"open"});#s=void 0;#i=void 0;#t="";#n;#o=[0,0,0];#r="OmitEmpty";#a=void 0;#l=void 0;#u;#c=!1;scheduleUpdate(){this.#n?this.#n.schedule((async()=>this.#h())):this.#h()}connectedCallback(){this.#e.adoptedStyleSheets=[u.default]}set data(e){this.#s=e.clickHandler,this.#t=e.leadingText??"",this.#i=e.tooltipCallback,this.#r=e.displayMode??"OmitEmpty",this.#l=e.accessibleName,this.#u=e.throttlerTimeout,this.#c=Boolean(e.compact),this.#a!==e.issuesManager&&(this.#a?.removeEventListener("IssuesCountUpdated",this.scheduleUpdate,this),this.#a=e.issuesManager,this.#a.addEventListener("IssuesCountUpdated",this.scheduleUpdate,this)),0!==e.throttlerTimeout?this.#n=new n.Throttler.Throttler(e.throttlerTimeout??100):this.#n=void 0,this.scheduleUpdate()}get data(){return{clickHandler:this.#s,leadingText:this.#t,tooltipCallback:this.#i,displayMode:this.#r,accessibleName:this.#l,throttlerTimeout:this.#u,compact:this.#c,issuesManager:this.#a}}#h(){if(!this.#a)return;this.#o=[this.#a.numberOfIssues(r.Issue.IssueKind.PageError),this.#a.numberOfIssues(r.Issue.IssueKind.BreakingChange),this.#a.numberOfIssues(r.Issue.IssueKind.Improvement)];const e=[r.Issue.IssueKind.PageError,r.Issue.IssueKind.BreakingChange,r.Issue.IssueKind.Improvement][this.#o.findIndex((e=>e>0))??2],s=(s,i)=>{switch(this.#r){case"OmitEmpty":return i>0?`${i}`:void 0;case"ShowAlways":return`${i}`;case"OnlyMostImportant":return s===e?`${i}`:void 0}},i="2ex",t={groups:[{...p(m(r.Issue.IssueKind.PageError),i),text:s(r.Issue.IssueKind.PageError,this.#o[0])},{...p(m(r.Issue.IssueKind.BreakingChange),i),text:s(r.Issue.IssueKind.BreakingChange,this.#o[1])},{...p(m(r.Issue.IssueKind.Improvement),i),text:s(r.Issue.IssueKind.Improvement,this.#o[2])}],clickHandler:this.#s,leadingText:this.#t,accessibleName:this.#l,compact:this.#c};l.render(l.html`
        <icon-button .data=${t} .accessibleName=${this.#l}></icon-button>
        `,this.#e,{host:this}),this.#i?.()}}a.CustomElements.defineComponent("issue-counter",v)})),s.register("as4jY",(function(s,i){e(s.exports,"default",(()=>n));const t=new CSSStyleSheet;t.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  white-space: normal;\n  display: inline-block;\n}\n\n/*# sourceURL=issueCounter.css */\n");var n=t})),s.register("8PEby",(function(i,t){e(i.exports,"extractShortPath",(()=>I)),e(i.exports,"IssueLinkIcon",(()=>b));var n=s("ixFnt"),o=s("koSS8"),r=s("kpUjp"),a=s("cY3yZ"),l=s("dS5IF"),u=s("fm4u4"),c=s("hveEP"),h=s("dVpmi"),d=s("ggl3n");const m={clickToShowIssue:"Click to show issue in the issues tab",clickToShowIssueWithTitle:"Click to open the issue tab and show issue: {title}",issueUnavailable:"Issue unavailable at this time"},p=n.i18n.registerUIStrings("ui/components/issue_counter/IssueLinkIcon.ts",m),g=n.i18n.getLocalizedString.bind(void 0,p),I=e=>(/[^/]+$/.exec(e)||/[^/]+\/$/.exec(e)||[""])[0],v=c.RenderCoordinator.RenderCoordinator.instance();class b extends HTMLElement{static litTagName=l.literal`devtools-issue-link-icon`;#e=this.attachShadow({mode:"open"});#d;#m=null;#p=Promise.resolve(void 0);#g;#I;#v;#b=o.Revealer.reveal;#k=Promise.resolve(void 0);set data(e){if(this.#d=e.issue,this.#g=e.issueId,!this.#d&&!this.#g)throw new Error("Either `issue` or `issueId` must be provided");this.#I=e.issueResolver,this.#v=e.additionalOnClickAction,e.revealOverride&&(this.#b=e.revealOverride),!this.#d&&this.#g?(this.#k=this.#C(this.#g),this.#p=this.#k.then((()=>this.#f()))):this.#p=this.#f(),this.#h()}async#f(){const e=this.#d?.getDescription();if(!e)return;const s=await u.MarkdownIssueDescription.getIssueTitleFromMarkdownDescription(e);s&&(this.#m=s)}connectedCallback(){this.#e.adoptedStyleSheets=[h.default]}#C(e){if(!this.#I)throw new Error("An `IssueResolver` must be provided if an `issueId` is provided.");return this.#I.waitFor(e).then((e=>{this.#d=e})).catch((()=>{this.#d=null}))}get data(){return{issue:this.#d,issueId:this.#g,issueResolver:this.#I,additionalOnClickAction:this.#v,revealOverride:this.#b!==o.Revealer.reveal?this.#b:void 0}}iconData(){return this.#d?(0,d.getIssueKindIconData)(this.#d.getKind()):{iconName:"issue-questionmark-icon",color:"var(--color-text-secondary)",width:"16px",height:"16px"}}handleClick(e){0===e.button&&(this.#d&&this.#b(this.#d),this.#v?.())}#T(){return this.#m?g(m.clickToShowIssueWithTitle,{title:this.#m}):this.#d?g(m.clickToShowIssue):g(m.issueUnavailable)}#h(){return v.write((()=>{l.render(l.html`
        ${l.Directives.until(this.#p.then((()=>this.#w())),this.#k.then((()=>this.#w())),this.#w())}
      `,this.#e,{host:this})}))}#w(){return l.html`
      <span class=${l.Directives.classMap({link:Boolean(this.#d)})}
            tabindex="0"
            @click=${this.handleClick}>
        <${a.Icon.Icon.litTagName} .data=${this.iconData()}
          title=${this.#T()}></${a.Icon.Icon.litTagName}>
      </span>`}}r.CustomElements.defineComponent("devtools-issue-link-icon",b)})),s.register("dVpmi",(function(s,i){e(s.exports,"default",(()=>n));const t=new CSSStyleSheet;t.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  display: inline-block;\n  white-space: nowrap;\n  color: inherit;\n  font-size: inherit;\n  font-family: inherit;\n}\n\ndevtools-icon {\n  vertical-align: middle;\n}\n\n.link {\n  cursor: pointer;\n}\n\n.link span {\n  color: var(--color-link);\n}\n\n/*# sourceURL=issueLinkIcon.css */\n");var n=t}));
//# sourceMappingURL=sources.3b90066b.js.map
