function e(e,t,n,i){Object.defineProperty(e,t,{get:n,set:i,enumerable:!0,configurable:!0})}var t=("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}).parcelRequire20b5;t.register("gYNA5",(function(n,i){e(n.exports,"ClassesPaneWidget",(()=>t("e1GCw"))),e(n.exports,"ColorSwatchPopoverIcon",(()=>t("hqdDa"))),e(n.exports,"ComputedStyleModel",(()=>t("3LXVO"))),e(n.exports,"ComputedStyleWidget",(()=>t("aAkQj"))),e(n.exports,"DOMLinkifier",(()=>t("h7UuJ"))),e(n.exports,"DOMPath",(()=>t("iADGB"))),e(n.exports,"ElementsPanel",(()=>t("1r4or"))),e(n.exports,"ElementsSidebarPane",(()=>t("eVQ8w"))),e(n.exports,"ElementStatePaneWidget",(()=>t("eYScD"))),e(n.exports,"ElementsTreeElement",(()=>t("4o2MM"))),e(n.exports,"ElementsTreeElementHighlighter",(()=>t("hnsgt"))),e(n.exports,"ElementsTreeOutline",(()=>t("Vxucs"))),e(n.exports,"EventListenersWidget",(()=>t("5jpv3"))),e(n.exports,"InspectElementModeController",(()=>t("3yYcy"))),e(n.exports,"LayersWidget",(()=>t("1DdG1"))),e(n.exports,"LayoutSidebarPane",(()=>t("l294p"))),e(n.exports,"MarkerDecorator",(()=>t("bVu26"))),e(n.exports,"MetricsSidebarPane",(()=>t("KoYQ7"))),e(n.exports,"NodeStackTraceWidget",(()=>t("g4JbB"))),e(n.exports,"PlatformFontsWidget",(()=>t("lyNC2"))),e(n.exports,"PropertiesWidget",(()=>t("iMvSZ"))),e(n.exports,"StyleEditorWidget",(()=>t("3yaAT"))),e(n.exports,"StylePropertyHighlighter",(()=>t("41s9g"))),e(n.exports,"StylePropertyTreeElement",(()=>t("8V7BL"))),e(n.exports,"StylePropertyUtils",(()=>t("dqP5n"))),e(n.exports,"StylesSidebarPane",(()=>t("3OgVH")));t("3yYcy"),t("hqdDa"),t("3LXVO"),t("h7UuJ"),t("iADGB"),t("eVQ8w"),t("4o2MM"),t("Vxucs"),t("5jpv3"),t("bVu26"),t("KoYQ7"),t("lyNC2"),t("iMvSZ"),t("g4JbB"),t("41s9g"),t("3OgVH"),t("8V7BL"),t("aAkQj"),t("1r4or"),t("e1GCw"),t("eYScD"),t("hnsgt"),t("e1GCw"),t("hqdDa"),t("3LXVO"),t("aAkQj"),t("h7UuJ"),t("iADGB"),t("1r4or"),t("eVQ8w"),t("eYScD"),t("4o2MM"),t("hnsgt"),t("Vxucs"),t("5jpv3"),t("3yYcy"),t("1DdG1"),t("l294p"),t("bVu26"),t("KoYQ7"),t("g4JbB"),t("lyNC2"),t("iMvSZ"),t("3yaAT"),t("41s9g"),t("8V7BL"),t("dqP5n"),t("3OgVH")})),t.register("3yYcy",(function(n,i){e(n.exports,"InspectElementModeController",(()=>h)),e(n.exports,"ToggleSearchActionDelegate",(()=>p));var o=t("koSS8"),s=t("9X2mn"),r=t("eQFvP"),l=t("9z2ZV"),a=t("1r4or");let d,c;class h{toggleSearchAction;mode;showDetailedInspectTooltipSetting;constructor(){this.toggleSearchAction=l.ActionRegistry.ActionRegistry.instance().action("elements.toggle-element-search"),this.mode="none",r.TargetManager.TargetManager.instance().addEventListener(r.TargetManager.Events.SuspendStateChanged,this.suspendStateChanged,this),r.TargetManager.TargetManager.instance().addModelListener(r.OverlayModel.OverlayModel,r.OverlayModel.Events.ExitedInspectMode,(()=>this.setMode("none"))),r.OverlayModel.OverlayModel.setInspectNodeHandler(this.inspectNode.bind(this)),r.TargetManager.TargetManager.instance().observeModels(r.OverlayModel.OverlayModel,this),this.showDetailedInspectTooltipSetting=o.Settings.Settings.instance().moduleSetting("showDetailedInspectTooltip"),this.showDetailedInspectTooltipSetting.addChangeListener(this.showDetailedInspectTooltipChanged.bind(this)),document.addEventListener("keydown",(e=>{e.keyCode===l.KeyboardShortcut.Keys.Esc.code&&this.isInInspectElementMode()&&(this.setMode("none"),e.consume(!0))}),!0)}static instance({forceNew:e}={forceNew:!1}){return d&&!e||(d=new h),d}modelAdded(e){"none"!==this.mode&&e.setInspectMode(this.mode,this.showDetailedInspectTooltipSetting.get())}modelRemoved(e){}isInInspectElementMode(){return"none"!==this.mode}toggleInspectMode(){let e;e=this.isInInspectElementMode()?"none":o.Settings.Settings.instance().moduleSetting("showUAShadowDOM").get()?"searchForUAShadowDOM":"searchForNode",this.setMode(e)}captureScreenshotMode(){this.setMode("captureAreaScreenshot")}setMode(e){if(!r.TargetManager.TargetManager.instance().allTargetsSuspended()){this.mode=e;for(const t of r.TargetManager.TargetManager.instance().models(r.OverlayModel.OverlayModel))t.setInspectMode(e,this.showDetailedInspectTooltipSetting.get());this.toggleSearchAction&&this.toggleSearchAction.setToggled(this.isInInspectElementMode())}}suspendStateChanged(){r.TargetManager.TargetManager.instance().allTargetsSuspended()&&(this.mode="none",this.toggleSearchAction&&this.toggleSearchAction.setToggled(!1))}inspectNode(e){a.ElementsPanel.instance().revealAndSelectNode(e,!0,!0)}showDetailedInspectTooltipChanged(){this.setMode(this.mode)}}class p{handleAction(e,t){return!s.Runtime.Runtime.queryParam("isSharedWorker")&&(d=h.instance(),!!d&&("elements.toggle-element-search"===t?d.toggleInspectMode():"elements.capture-area-screenshot"===t&&d.captureScreenshotMode(),!0))}static instance(e={forceNew:null}){const{forceNew:t}=e;return c&&!t||(c=new p),c}}})),t.register("1r4or",(function(n,i){e(n.exports,"ElementsPanel",(()=>O)),e(n.exports,"ContextMenuProvider",(()=>U)),e(n.exports,"DOMNodeRevealer",(()=>B)),e(n.exports,"CSSPropertyRevealer",(()=>H)),e(n.exports,"ElementsActionDelegate",(()=>V)),e(n.exports,"PseudoStateMarkerDecorator",(()=>W));var o=t("koSS8"),s=t("e7bLS"),r=t("ixFnt"),l=t("lz7WY"),a=t("9X2mn"),d=t("eQFvP"),c=t("hluvd"),h=t("3gapz"),p=t("9apcb"),u=t("a3yig"),m=t("9z2ZV"),g=t("RJDor");t("Px6PZ");var f=t("lcU9C"),y=t("au5Rx"),v=t("6ICvI"),b=t("7HOJI"),S=t("aAkQj"),E=t("hnsgt"),C=t("Vxucs"),x=t("KoYQ7"),w=t("3OgVH");const T={findByStringSelectorOrXpath:"Find by string, selector, or `XPath`",switchToAccessibilityTreeView:"Switch to Accessibility Tree view",switchToDomTreeView:"Switch to DOM Tree view",frame:"Frame",showComputedStylesSidebar:"Show Computed Styles sidebar",hideComputedStylesSidebar:"Hide Computed Styles sidebar",computedStylesShown:"Computed Styles sidebar shown",computedStylesHidden:"Computed Styles sidebar hidden",computed:"Computed",styles:"Styles",revealInElementsPanel:"Reveal in Elements panel",nodeCannotBeFoundInTheCurrent:"Node cannot be found in the current page.",theRemoteObjectCouldNotBe:"The remote object could not be resolved to a valid node.",theDeferredDomNodeCouldNotBe:"The deferred `DOM` Node could not be resolved to a valid node.",elementStateS:"Element state: {PH1}",sidePanelToolbar:"Side panel toolbar",sidePanelContent:"Side panel content",domTreeExplorer:"DOM tree explorer"},M=r.i18n.registerUIStrings("panels/elements/ElementsPanel.ts",T),N=r.i18n.getLocalizedString.bind(void 0,M),I=e=>{const n=new p.Button.Button,i=N(e?T.switchToDomTreeView:T.switchToAccessibilityTreeView);return n.data={active:e,variant:"toolbar",iconUrl:new URL(t("d6d0Q")).toString(),title:i},n.tabIndex=0,n.classList.add("axtree-button"),e&&n.classList.add("active"),n};let P;class O extends m.Panel.Panel{splitWidget;searchableViewInternal;mainContainer;domTreeContainer;splitMode;accessibilityTreeView;breadcrumbs;stylesWidget;computedStyleWidget;metricsWidget;treeOutlines=new Set;treeOutlineHeaders=new Map;searchResults;currentSearchResultIndex;pendingNodeReveal;adornerManager;adornerSettingsPane;adornersByName;accessibilityTreeButton;domTreeButton;selectedNodeOnReset;hasNonDefaultSelectedNode;searchConfig;omitDefaultSelection;notFirstInspectElement;sidebarPaneView;stylesViewToReveal;cssStyleTrackerByCSSModel;constructor(){super("elements"),this.splitWidget=new m.SplitWidget.SplitWidget(!0,!0,"elementsPanelSplitViewState",325,325),this.splitWidget.addEventListener(m.SplitWidget.Events.SidebarSizeChanged,this.updateTreeOutlineVisibleWidth.bind(this)),this.splitWidget.show(this.element),this.searchableViewInternal=new m.SearchableView.SearchableView(this,null),this.searchableViewInternal.setMinimumSize(25,28),this.searchableViewInternal.setPlaceholder(N(T.findByStringSelectorOrXpath));const e=this.searchableViewInternal.element;this.mainContainer=document.createElement("div"),this.domTreeContainer=document.createElement("div");const t=document.createElement("div");a.Runtime.experiments.isEnabled("fullAccessibilityTree")&&this.initializeFullAccessibilityTreeView(),this.mainContainer.appendChild(this.domTreeContainer),e.appendChild(this.mainContainer),e.appendChild(t),m.ARIAUtils.markAsMain(this.domTreeContainer),m.ARIAUtils.setAccessibleName(this.domTreeContainer,N(T.domTreeExplorer)),this.splitWidget.setMainWidget(this.searchableViewInternal),this.splitMode=null,this.mainContainer.id="main-content",this.domTreeContainer.id="elements-content",o.Settings.Settings.instance().moduleSetting("domWordWrap").get()&&this.domTreeContainer.classList.add("elements-wrap"),o.Settings.Settings.instance().moduleSetting("domWordWrap").addChangeListener(this.domWordWrapSettingChanged.bind(this)),t.id="elements-crumbs",this.domTreeButton&&(this.accessibilityTreeView=new(0,g.AccessibilityTreeView)(this.domTreeButton)),this.breadcrumbs=new f.ElementsBreadcrumbs,this.breadcrumbs.addEventListener("breadcrumbsnodeselected",(e=>{this.crumbNodeSelected(e)})),t.appendChild(this.breadcrumbs),this.stylesWidget=w.StylesSidebarPane.instance(),this.computedStyleWidget=new(0,S.ComputedStyleWidget),this.metricsWidget=new(0,x.MetricsSidebarPane),o.Settings.Settings.instance().moduleSetting("sidebarPosition").addChangeListener(this.updateSidebarPosition.bind(this)),this.updateSidebarPosition(),this.cssStyleTrackerByCSSModel=new Map,d.TargetManager.TargetManager.instance().observeModels(d.DOMModel.DOMModel,this),d.TargetManager.TargetManager.instance().addEventListener(d.TargetManager.Events.NameChanged,(e=>this.targetNameChanged(e.data))),o.Settings.Settings.instance().moduleSetting("showUAShadowDOM").addChangeListener(this.showUAShadowDOMChanged.bind(this)),d.TargetManager.TargetManager.instance().addModelListener(d.DOMModel.DOMModel,d.DOMModel.Events.DocumentUpdated,this.documentUpdatedEvent,this),c.ExtensionServer.ExtensionServer.instance().addEventListener(c.ExtensionServer.Events.SidebarPaneAdded,this.extensionSidebarPaneAdded,this),this.currentSearchResultIndex=-1,this.pendingNodeReveal=!1,this.adornerManager=new y.AdornerManager(o.Settings.Settings.instance().moduleSetting("adornerSettings")),this.adornerSettingsPane=null,this.adornersByName=new Map}initializeFullAccessibilityTreeView(){this.accessibilityTreeButton=I(!1),this.accessibilityTreeButton.addEventListener("click",this.showAccessibilityTree.bind(this)),this.domTreeButton=I(!0),this.domTreeButton.addEventListener("click",this.showDOMTree.bind(this)),this.mainContainer.appendChild(this.accessibilityTreeButton)}showAccessibilityTree(){this.accessibilityTreeView&&this.splitWidget.setMainWidget(this.accessibilityTreeView)}showDOMTree(){this.splitWidget.setMainWidget(this.searchableViewInternal);const e=this.selectedDOMNode();if(!e)return;const t=this.treeElementForNode(e);t&&t.select()}static instance(e={forceNew:null}){const{forceNew:t}=e;return P&&!t||(P=new O),P}revealProperty(e){return this.sidebarPaneView&&this.stylesViewToReveal?this.sidebarPaneView.showView(this.stylesViewToReveal).then((()=>{this.stylesWidget.revealProperty(e)})):Promise.resolve()}resolveLocation(e){return this.sidebarPaneView||null}showToolbarPane(e,t){this.stylesWidget.showToolbarPane(e,t)}modelAdded(e){const t=e.parentModel();let n=t?C.ElementsTreeOutline.forDOMModel(t):null;if(!n&&(n=new(0,C.ElementsTreeOutline)(!0,!0),n.setWordWrap(o.Settings.Settings.instance().moduleSetting("domWordWrap").get()),n.addEventListener(C.ElementsTreeOutline.Events.SelectedNodeChanged,this.selectedNodeChanged,this),n.addEventListener(C.ElementsTreeOutline.Events.ElementsTreeUpdated,this.updateBreadcrumbIfNeeded,this),new(0,E.ElementsTreeElementHighlighter)(n),this.treeOutlines.add(n),e.target().parentTarget())){const t=document.createElement("div");t.classList.add("elements-tree-header"),this.treeOutlineHeaders.set(n,t),this.targetNameChanged(e.target())}n.wireToDOMModel(e),this.setupStyleTracking(e.cssModel()),this.isShowing()&&this.wasShown()}modelRemoved(e){const t=C.ElementsTreeOutline.forDOMModel(e);if(!t)return;if(t.unwireFromDOMModel(e),e.parentModel())return;this.treeOutlines.delete(t);const n=this.treeOutlineHeaders.get(t);n&&n.remove(),this.treeOutlineHeaders.delete(t),t.element.remove(),this.removeStyleTracking(e.cssModel())}targetNameChanged(e){const t=e.model(d.DOMModel.DOMModel);if(!t)return;const n=C.ElementsTreeOutline.forDOMModel(t);if(!n)return;const i=this.treeOutlineHeaders.get(n);i&&(i.removeChildren(),i.createChild("div","elements-tree-header-frame").textContent=N(T.frame),i.appendChild(u.Linkifier.Linkifier.linkifyURL(e.inspectedURL(),{text:e.name()})))}updateTreeOutlineVisibleWidth(){if(!this.treeOutlines.size)return;let e=this.splitWidget.element.offsetWidth;this.splitWidget.isVertical()&&(e-=this.splitWidget.sidebarSize());for(const t of this.treeOutlines)t.setVisibleWidth(e)}focus(){this.treeOutlines.size&&this.treeOutlines.values().next().value.focus()}searchableView(){return this.searchableViewInternal}wasShown(){super.wasShown(),m.Context.Context.instance().setFlavor(O,this),this.registerCSSFiles([h.default]);for(const e of this.treeOutlines)if(e.element.parentElement!==this.domTreeContainer){const t=this.treeOutlineHeaders.get(e);t&&this.domTreeContainer.appendChild(t),this.domTreeContainer.appendChild(e.element)}const e=d.TargetManager.TargetManager.instance().models(d.DOMModel.DOMModel);for(const t of e){if(t.parentModel())continue;const e=C.ElementsTreeOutline.forDOMModel(t);e&&(e.setVisible(!0),e.rootDOMNode||(t.existingDocument()?(e.rootDOMNode=t.existingDocument(),this.documentUpdated(t)):t.requestDocument()))}}willHide(){d.OverlayModel.OverlayModel.hideDOMNodeHighlight();for(const e of this.treeOutlines){e.setVisible(!1),this.domTreeContainer.removeChild(e.element);const t=this.treeOutlineHeaders.get(e);t&&this.domTreeContainer.removeChild(t)}super.willHide(),m.Context.Context.instance().setFlavor(O,null)}onResize(){this.element.window().requestAnimationFrame(this.updateSidebarPosition.bind(this)),this.updateTreeOutlineVisibleWidth()}selectedNodeChanged(e){let t=e.data.node;t&&t.pseudoType()&&!t.parentNode&&(t=null);const{focus:n}=e.data;for(const e of this.treeOutlines)t&&C.ElementsTreeOutline.forDOMModel(t.domModel())===e||e.selectDOMNode(null);if(t){const e=[v.legacyNodeToElementsComponentsNode(t)];for(let n=t.parentNode;n;n=n.parentNode)e.push(v.legacyNodeToElementsComponentsNode(n));this.breadcrumbs.data={crumbs:e,selectedNode:v.legacyNodeToElementsComponentsNode(t)},this.accessibilityTreeView&&this.accessibilityTreeView.selectedNodeChanged(t)}else this.breadcrumbs.data={crumbs:[],selectedNode:null};if(m.Context.Context.instance().setFlavor(d.DOMModel.DOMNode,t),!t)return;t.setAsInspectedNode(),n&&(this.selectedNodeOnReset=t,this.hasNonDefaultSelectedNode=!0);const i=t.domModel().runtimeModel().executionContexts(),o=t.frameId();for(const e of i)if(e.frameId===o){m.Context.Context.instance().setFlavor(d.RuntimeModel.ExecutionContext,e);break}}documentUpdatedEvent(e){const t=e.data;this.documentUpdated(t),this.removeStyleTracking(t.cssModel()),this.setupStyleTracking(t.cssModel())}documentUpdated(e){if(this.searchableViewInternal.resetSearch(),!e.existingDocument())return void(this.isShowing()&&e.requestDocument());if(this.hasNonDefaultSelectedNode=!1,this.omitDefaultSelection)return;const t=this.selectedNodeOnReset;(async function(e,n){const i=n?n.path():null,o=i?await e.pushNodeByPathToFrontend(i):null;if(t!==this.selectedNodeOnReset)return;let s=o?e.nodeForId(o):null;if(!s){const t=e.existingDocument();s=t?t.body||t.documentElement:null}s&&(this.setDefaultSelectedNode(s),this.lastSelectedNodeSelectedForTest())}).call(this,e,this.selectedNodeOnReset||null)}lastSelectedNodeSelectedForTest(){}setDefaultSelectedNode(e){if(!e||this.hasNonDefaultSelectedNode||this.pendingNodeReveal)return;const t=C.ElementsTreeOutline.forDOMModel(e.domModel());t&&(this.selectDOMNode(e),t.selectedTreeElement&&t.selectedTreeElement.expand())}searchCanceled(){this.searchConfig=void 0,this.hideSearchHighlights(),this.searchableViewInternal.updateSearchMatchesCount(0),this.currentSearchResultIndex=-1,delete this.searchResults,d.DOMModel.DOMModel.cancelSearch()}performSearch(e,t,n){const i=e.query,s=i.trim();if(!s.length)return;this.searchConfig&&this.searchConfig.query===i?this.hideSearchHighlights():this.searchCanceled(),this.searchConfig=e;const r=o.Settings.Settings.instance().moduleSetting("showUAShadowDOM").get(),l=d.TargetManager.TargetManager.instance().models(d.DOMModel.DOMModel),a=l.map((e=>e.performSearch(s,r)));Promise.all(a).then((e=>{this.searchResults=[];for(let t=0;t<e.length;++t){const n=e[t];for(let e=0;e<n;++e)this.searchResults.push({domModel:l[t],index:e,node:void 0})}if(this.searchableViewInternal.updateSearchMatchesCount(this.searchResults.length),!this.searchResults.length)return;this.currentSearchResultIndex>=this.searchResults.length&&(this.currentSearchResultIndex=-1);let i=this.currentSearchResultIndex;t&&(i=-1===this.currentSearchResultIndex?n?-1:0:n?i-1:i+1,this.jumpToSearchResult(i))}))}domWordWrapSettingChanged(e){this.domTreeContainer.classList.toggle("elements-wrap",e.data);for(const t of this.treeOutlines)t.setWordWrap(e.data)}switchToAndFocus(e){this.searchableViewInternal.cancelSearch(),m.ViewManager.ViewManager.instance().showView("elements").then((()=>this.selectDOMNode(e,!0)))}jumpToSearchResult(e){this.searchResults&&(this.currentSearchResultIndex=(e+this.searchResults.length)%this.searchResults.length,this.highlightCurrentSearchResult())}jumpToNextSearchResult(){this.searchResults&&this.searchConfig&&this.performSearch(this.searchConfig,!0)}jumpToPreviousSearchResult(){this.searchResults&&this.searchConfig&&this.performSearch(this.searchConfig,!0,!0)}supportsCaseSensitiveSearch(){return!1}supportsRegexSearch(){return!1}highlightCurrentSearchResult(){const e=this.currentSearchResultIndex,t=this.searchResults;if(!t)return;const n=t[e];if(this.searchableViewInternal.updateCurrentMatchIndex(e),null===n.node)return;if(void 0===n.node)return void n.domModel.searchResult(n.index).then((e=>{n.node=e;this.searchConfig&&this.searchResults&&-1!==this.currentSearchResultIndex&&this.highlightCurrentSearchResult()}));const i=this.treeElementForNode(n.node);if(n.node.scrollIntoView(),i){this.searchConfig&&i.highlightSearchResults(this.searchConfig.query),i.reveal();const e=i.listItemElement.getElementsByClassName(m.UIUtils.highlightedSearchResultClassName);e.length&&e[0].scrollIntoViewIfNeeded(!1)}}hideSearchHighlights(){if(!this.searchResults||!this.searchResults.length||-1===this.currentSearchResultIndex)return;const e=this.searchResults[this.currentSearchResultIndex];if(!e.node)return;const t=this.treeElementForNode(e.node);t&&t.hideSearchHighlights()}selectedDOMNode(){for(const e of this.treeOutlines)if(e.selectedDOMNode())return e.selectedDOMNode();return null}selectDOMNode(e,t){for(const n of this.treeOutlines){C.ElementsTreeOutline.forDOMModel(e.domModel())===n?n.selectDOMNode(e,t):n.selectDOMNode(null)}}updateBreadcrumbIfNeeded(e){const t=e.data,n=this.selectedDOMNode();if(!n)return void(this.breadcrumbs.data={crumbs:[],selectedNode:null});const i=v.legacyNodeToElementsComponentsNode(n),o=[i];for(let e=n.parentNode;e;e=e.parentNode)o.push(v.legacyNodeToElementsComponentsNode(e));const s=t.map(v.legacyNodeToElementsComponentsNode),r=new Map;s.forEach((e=>r.set(e.id,e)));const l=o.map((e=>r.get(e.id)||e));this.breadcrumbs.data={crumbs:l,selectedNode:i}}crumbNodeSelected(e){this.selectDOMNode(e.legacyDomNode,!0)}treeOutlineForNode(e){return e?C.ElementsTreeOutline.forDOMModel(e.domModel()):null}treeElementForNode(e){const t=this.treeOutlineForNode(e);return t?t.findTreeElement(e):null}leaveUserAgentShadowDOM(e){let t;for(;(t=e.ancestorUserAgentShadowRoot())&&t.parentNode;)e=t.parentNode;return e}async revealAndSelectNode(e,t,n){this.omitDefaultSelection=!0,e=o.Settings.Settings.instance().moduleSetting("showUAShadowDOM").get()?e:this.leaveUserAgentShadowDOM(e),n||e.highlightForTwoSeconds(),this.accessibilityTreeView&&this.accessibilityTreeView.revealAndSelectNode(e),await m.ViewManager.ViewManager.instance().showView("elements",!1,!t),this.selectDOMNode(e,t),delete this.omitDefaultSelection,this.notFirstInspectElement||(O.firstInspectElementNodeNameForTest=e.nodeName(),O.firstInspectElementCompletedForTest(),s.InspectorFrontendHost.InspectorFrontendHostInstance.inspectElementCompleted()),this.notFirstInspectElement=!0}showUAShadowDOMChanged(){for(const e of this.treeOutlines)e.update()}setupTextSelectionHack(e){const t=i.bind(this),n=e=>{0===e.buttons&&i.call(this)};function i(){this.splitWidget.element.classList.remove("disable-resizer-for-elements-hack"),e.style.removeProperty("left"),e.style.removeProperty("padding-left"),e.style.removeProperty("width"),e.style.removeProperty("position"),e.window().removeEventListener("blur",t),e.window().removeEventListener("contextmenu",t,!0),e.window().removeEventListener("dragstart",t,!0),e.window().removeEventListener("mousemove",n,!0),e.window().removeEventListener("mouseup",t,!0),e.window().removeEventListener("visibilitychange",t)}e.addEventListener("mousedown",(i=>{if(0!==i.button)return;this.splitWidget.element.classList.add("disable-resizer-for-elements-hack"),e.style.setProperty("height",`${e.offsetHeight}px`);e.style.setProperty("left","-1000000px"),e.style.setProperty("padding-left","1000000px"),e.style.setProperty("width","calc(100% + 1000000px)"),e.style.setProperty("position","fixed"),e.window().addEventListener("blur",t),e.window().addEventListener("contextmenu",t,!0),e.window().addEventListener("dragstart",t,!0),e.window().addEventListener("mousemove",n,!0),e.window().addEventListener("mouseup",t,!0),e.window().addEventListener("visibilitychange",t)}),!0)}initializeSidebarPanes(e){this.splitWidget.setVertical("Vertical"===e),this.showToolbarPane(null,null);const t=new m.Widget.VBox;t.element.classList.add("style-panes-wrapper"),this.stylesWidget.show(t.element),this.setupTextSelectionHack(t.element);const n=new m.Widget.VBox;n.element.classList.add("style-panes-wrapper"),this.computedStyleWidget.show(n.element);const i=new m.SplitWidget.SplitWidget(!0,!0,"elements.styles.sidebar.width",100);i.setMainWidget(t),i.hideSidebar(),i.enableShowModeSaving(),i.addEventListener(m.SplitWidget.Events.ShowModeChanged,(()=>{r()})),this.stylesWidget.addEventListener("InitialUpdateCompleted",(()=>{this.stylesWidget.appendToolbarItem(i.createShowHideSidebarButton(N(T.showComputedStylesSidebar),N(T.hideComputedStylesSidebar),N(T.computedStylesShown),N(T.computedStylesHidden)))}));const o=()=>{this.metricsWidget.show(n.element,this.computedStyleWidget.element),this.metricsWidget.toggleVisibility(!0),this.stylesWidget.removeEventListener("StylesUpdateCompleted",a)},r=()=>{i.showMode()===m.SplitWidget.ShowMode.Both?o():(this.metricsWidget.show(t.element),this.stylesWidget.hasMatchedStyles||this.metricsWidget.toggleVisibility(!1),this.stylesWidget.addEventListener("StylesUpdateCompleted",a))};let l=!1;const a=e=>{this.metricsWidget.toggleVisibility(e.data.hasMatchedStyles)};this.sidebarPaneView=m.ViewManager.ViewManager.instance().createTabbedLocation((()=>m.ViewManager.ViewManager.instance().showView("elements")));const d=this.sidebarPaneView.tabbedPane();"Vertical"!==this.splitMode&&this.splitWidget.installResizer(d.headerElement());const h=d.headerElement();m.ARIAUtils.markAsNavigation(h),m.ARIAUtils.setAccessibleName(h,N(T.sidePanelToolbar));const p=d.tabbedPaneContentElement();m.ARIAUtils.markAsComplementary(p),m.ARIAUtils.setAccessibleName(p,N(T.sidePanelContent));const u=new m.View.SimpleView(N(T.styles));this.sidebarPaneView.appendView(u),u.element.classList.add("flex-auto"),i.show(u.element);const g=new m.View.SimpleView(N(T.computed));g.element.classList.add("composite","fill"),d.addEventListener(m.TabbedPane.Events.TabSelected,(e=>{const{tabId:t}=e.data;t===N(T.computed)?(n.show(g.element),o()):t===N(T.styles)&&(i.setSidebarWidget(n),r()),l?s.userMetrics.sidebarPaneShown(t):l=!0}),this),this.sidebarPaneView.appendView(g),this.stylesViewToReveal=u,this.sidebarPaneView.appendApplicableItems("elements-sidebar");const f=c.ExtensionServer.ExtensionServer.instance().sidebarPanes();for(let e=0;e<f.length;++e)this.addExtensionSidebarPane(f[e]);this.splitWidget.setSidebarWidget(this.sidebarPaneView.tabbedPane())}updateSidebarPosition(){if(this.sidebarPaneView&&this.sidebarPaneView.tabbedPane().shouldHideOnDetach())return;const e=o.Settings.Settings.instance().moduleSetting("sidebarPosition").get();let t="Horizontal";if(("right"===e||"auto"===e&&m.InspectorView.InspectorView.instance().element.offsetWidth>680)&&(t="Vertical"),!this.sidebarPaneView)return void this.initializeSidebarPanes(t);if(t===this.splitMode)return;this.splitMode=t;const n=this.sidebarPaneView.tabbedPane();this.splitWidget.uninstallResizer(n.headerElement()),this.splitWidget.setVertical("Vertical"===this.splitMode),this.showToolbarPane(null,null),"Vertical"!==this.splitMode&&this.splitWidget.installResizer(n.headerElement())}extensionSidebarPaneAdded(e){this.addExtensionSidebarPane(e.data)}addExtensionSidebarPane(e){this.sidebarPaneView&&e.panelName()===this.name&&this.sidebarPaneView.appendView(e)}getComputedStyleWidget(){return this.computedStyleWidget}setupStyleTracking(e){const t=e.createCSSPropertyTracker(k);t.start(),this.cssStyleTrackerByCSSModel.set(e,t),t.addEventListener(d.CSSModel.CSSPropertyTrackerEvents.TrackedCSSPropertiesUpdated,this.trackedCSSPropertiesUpdated,this)}removeStyleTracking(e){const t=this.cssStyleTrackerByCSSModel.get(e);t&&(t.stop(),this.cssStyleTrackerByCSSModel.delete(e),t.removeEventListener(d.CSSModel.CSSPropertyTrackerEvents.TrackedCSSPropertiesUpdated,this.trackedCSSPropertiesUpdated,this))}trackedCSSPropertiesUpdated({data:e}){for(const t of e){if(!t)continue;const e=this.treeElementForNode(t);e&&e.updateStyleAdorners()}}showAdornerSettingsPane(){this.adornerSettingsPane||(this.adornerSettingsPane=new b.AdornerSettingsPane,this.adornerSettingsPane.addEventListener("adornersettingupdated",(e=>{const{adornerName:t,isEnabledNow:n,newSettings:i}=e.data,o=this.adornersByName.get(t);if(o)for(const e of o)n?e.show():e.hide();this.adornerManager.updateSettings(i)})),this.searchableViewInternal.element.prepend(this.adornerSettingsPane));const e=this.adornerManager.getSettings();this.adornerSettingsPane.data={settings:e},this.adornerSettingsPane.show()}isAdornerEnabled(e){return this.adornerManager.isAdornerEnabled(e)}registerAdorner(e){let t=this.adornersByName.get(e.name);t||(t=new Set,this.adornersByName.set(e.name,t)),t.add(e),this.isAdornerEnabled(e.name)||e.hide()}deregisterAdorner(e){const t=this.adornersByName.get(e.name);t&&t.delete(e)}static firstInspectElementCompletedForTest=function(){};static firstInspectElementNodeNameForTest=""}globalThis.Elements=globalThis.Elements||{},globalThis.Elements.ElementsPanel=O;const k=[{name:"display",value:"grid"},{name:"display",value:"inline-grid"},{name:"display",value:"flex"},{name:"display",value:"inline-flex"},{name:"container-type",value:"inline-size"},{name:"container-type",value:"block-size"},{name:"container-type",value:"size"}];let L,D,A,R,F;class U{appendApplicableItems(e,t,n){if(!(n instanceof d.RemoteObject.RemoteObject&&n.isNode()||n instanceof d.DOMModel.DOMNode||n instanceof d.DOMModel.DeferredDOMNode))return;if(O.instance().element.isAncestor(e.target))return;const i=o.Revealer.reveal.bind(o.Revealer.Revealer,n);t.revealSection().appendItem(N(T.revealInElementsPanel),i)}static instance(){return L||(L=new U),L}}class B{static instance(e={forceNew:null}){const{forceNew:t}=e;return D&&!t||(D=new B),D}reveal(e,t){const n=O.instance();return n.pendingNodeReveal=!0,new Promise((function(i,o){if(e instanceof d.DOMModel.DOMNode)s(e);else if(e instanceof d.DOMModel.DeferredDOMNode)e.resolve((function(e){if(!e){const e=N(T.theDeferredDomNodeCouldNotBe);return void o(new l.UserVisibleError.UserVisibleError(e))}s(e)}));else if(e instanceof d.RemoteObject.RemoteObject){const t=e.runtimeModel().target().model(d.DOMModel.DOMModel);if(t)t.pushObjectAsNodeToFrontend(e).then((function(e){if(!e){const e=N(T.theRemoteObjectCouldNotBe);return void o(new l.UserVisibleError.UserVisibleError(e))}s(e)}));else{const e=N(T.nodeCannotBeFoundInTheCurrent);o(new l.UserVisibleError.UserVisibleError(e))}}else{const e=N(T.theRemoteObjectCouldNotBe);o(new l.UserVisibleError.UserVisibleError(e)),n.pendingNodeReveal=!1}function s(s){n.pendingNodeReveal=!1;let r=s;for(;r.parentNode;)r=r.parentNode;const a=!(r instanceof d.DOMModel.DOMDocument);if(!(e instanceof d.DOMModel.DOMDocument)&&a){const e=N(T.nodeCannotBeFoundInTheCurrent);return void o(new l.UserVisibleError.UserVisibleError(e))}if(s)return void n.revealAndSelectNode(s,!t).then(i);const c=N(T.nodeCannotBeFoundInTheCurrent);o(new l.UserVisibleError.UserVisibleError(c))}})).catch((e=>{let t;throw t=l.UserVisibleError.isUserVisibleError(e)?e.message:N(T.nodeCannotBeFoundInTheCurrent),o.Console.Console.instance().warn(t),e}))}}class H{static instance(e={forceNew:null}){const{forceNew:t}=e;return A&&!t||(A=new H),A}reveal(e){return O.instance().revealProperty(e)}}class V{handleAction(e,t){const n=m.Context.Context.instance().flavor(d.DOMModel.DOMNode);if(!n)return!0;const i=C.ElementsTreeOutline.forDOMModel(n.domModel());if(!i)return!0;switch(t){case"elements.hide-element":return i.toggleHideElement(n),!0;case"elements.edit-as-html":return i.toggleEditAsHTML(n),!0;case"elements.duplicate-element":return i.duplicateNode(n),!0;case"elements.copy-styles":return i.findTreeElement(n)?.copyStyles(),!0;case"elements.undo":return d.DOMModel.DOMModelUndoStack.instance().undo(),O.instance().stylesWidget.forceUpdate(),!0;case"elements.redo":return d.DOMModel.DOMModelUndoStack.instance().redo(),O.instance().stylesWidget.forceUpdate(),!0}return!1}static instance(e={forceNew:null}){const{forceNew:t}=e;return R&&!t||(R=new V),R}}class W{static instance(e={forceNew:null}){const{forceNew:t}=e;return F&&!t||(F=new W),F}decorate(e){const t=e.domModel().cssModel().pseudoState(e);return t?{color:"orange",title:N(T.elementStateS,{PH1:":"+t.join(", :")})}:null}}})),t.register("3gapz",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync('/*\n * Copyright (C) 2006, 2007, 2008 Apple Inc.  All rights reserved.\n * Copyright (C) 2009 Anthony Ricaud <<EMAIL>>\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions\n * are met:\n *\n * 1.  Redistributions of source code must retain the above copyright\n *     notice, this list of conditions and the following disclaimer.\n * 2.  Redistributions in binary form must reproduce the above copyright\n *     notice, this list of conditions and the following disclaimer in the\n *     documentation and/or other materials provided with the distribution.\n * 3.  Neither the name of Apple Computer, Inc. ("Apple") nor the names of\n *     its contributors may be used to endorse or promote products derived\n *     from this software without specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY APPLE AND ITS CONTRIBUTORS "AS IS" AND ANY\n * EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n * DISCLAIMED. IN NO EVENT SHALL APPLE OR ITS CONTRIBUTORS BE LIABLE FOR ANY\n * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\n * THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\n#main-content {\n  position: relative;\n  flex: 1 1;\n}\n\n#elements-content {\n  overflow: auto;\n  padding: 2px 0 0;\n  height: 100%;\n}\n\n.style-panes-wrapper {\n  overflow: hidden scroll;\n  background-color: var(--color-background);\n}\n\n.style-panes-wrapper > div:not(:last-child) {\n  border-bottom: 1px solid var(--color-details-hairline);\n}\n\n#elements-content:not(.elements-wrap) > div {\n  display: inline-block;\n  min-width: 100%;\n}\n\n#elements-crumbs {\n  background-color: var(--color-background);\n  border-top: 1px solid var(--color-details-hairline);\n  overflow: hidden;\n  width: 100%;\n}\n\n.elements-tree-header {\n  height: 24px;\n  border-top: 1px solid var(--color-details-hairline);\n  border-bottom: 1px solid var(--color-details-hairline);\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n}\n\n.elements-tree-header-frame {\n  margin-left: 6px;\n  margin-right: 6px;\n  flex: none;\n}\n\ndevtools-adorner-settings-pane {\n  margin-bottom: 10px;\n  border-bottom: 1px solid var(--color-details-hairline);\n  overflow: auto;\n}\n\ndevtools-tree-outline {\n  overflow: auto;\n}\n\n.axtree-button {\n  position: absolute;\n  top: 16px;\n  right: 20px;\n  background-color: var(--color-background-elevation-1);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1;\n  border: 1px solid var(--color-details-hairline);\n  border-radius: 3px;\n}\n\n/*# sourceURL=elementsPanel.css */\n');var o=i})),t.register("RJDor",(function(n,i){e(n.exports,"AccessibilityTreeView",(()=>d));var o=t("eQFvP");t("b78cS");var s=t("2V4uM"),r=t("9z2ZV"),l=t("3x7Ll"),a=t("1r4or");class d extends r.Widget.VBox{accessibilityTreeComponent=new s.TreeOutline;toggleButton;inspectedDOMNode=null;root=null;constructor(e){super(),this.toggleButton=e,this.contentElement.appendChild(this.toggleButton),this.contentElement.appendChild(this.accessibilityTreeComponent),o.TargetManager.TargetManager.instance().observeModels(o.AccessibilityModel.AccessibilityModel,this),this.accessibilityTreeComponent.addEventListener("itemselected",(e=>{const t=e.data.node.treeNodeData;if(!t.isDOMNode())return;const n=t.deferredDOMNode();n&&n.resolve((e=>{e&&(this.inspectedDOMNode=e,a.ElementsPanel.instance().revealAndSelectNode(e,!0,!1))}))})),this.accessibilityTreeComponent.addEventListener("itemmouseover",(e=>{e.data.node.treeNodeData.highlightDOMNode()})),this.accessibilityTreeComponent.addEventListener("itemmouseout",(()=>{o.OverlayModel.OverlayModel.hideDOMNodeHighlight()}))}async wasShown(){await this.refreshAccessibilityTree(),this.inspectedDOMNode&&await this.loadSubTreeIntoAccessibilityModel(this.inspectedDOMNode)}async refreshAccessibilityTree(){if(!this.root){const e=o.FrameManager.FrameManager.instance().getTopFrame()?.id;if(!e)throw Error("No top frame");if(this.root=await l.getRootNode(e),!this.root)throw Error("No root")}await this.renderTree(),await this.accessibilityTreeComponent.expandRecursively(1)}async renderTree(){if(!this.root)return;const e=await l.sdkNodeToAXTreeNodes(this.root);this.accessibilityTreeComponent.data={defaultRenderer:l.accessibilityNodeRenderer,tree:e,filter:e=>e.ignored()||"generic"===e.role()?.value&&!e.name()?.value?"FLATTEN":"SHOW"}}async loadSubTreeIntoAccessibilityModel(e){const t=await l.getNodeAndAncestorsFromDOMNode(e),n=t.find((t=>t.backendDOMNodeId()===e.backendNodeId()));n&&(await this.accessibilityTreeComponent.expandNodeIds(t.map((e=>e.getFrameId()+"#"+e.id()))),await this.accessibilityTreeComponent.focusNodeId(l.getNodeId(n)))}async revealAndSelectNode(e){e!==this.inspectedDOMNode&&(this.inspectedDOMNode=e,this.isShowing()&&await this.loadSubTreeIntoAccessibilityModel(e))}async selectedNodeChanged(e){this.isShowing()||e===this.inspectedDOMNode||(!e.ownerDocument||"HTML"!==e.nodeName()&&"BODY"!==e.nodeName()?this.inspectedDOMNode=e:this.inspectedDOMNode=e.ownerDocument)}treeUpdated({data:e}){if(!e.root)return void this.renderTree();const t=o.FrameManager.FrameManager.instance().getTopFrame()?.id;e.root?.getFrameId()===t?(this.root=e.root,this.accessibilityTreeComponent.collapseAllNodes(),this.refreshAccessibilityTree()):this.renderTree()}modelAdded(e){e.addEventListener(o.AccessibilityModel.Events.TreeUpdated,this.treeUpdated,this)}modelRemoved(e){e.removeEventListener(o.AccessibilityModel.Events.TreeUpdated,this.treeUpdated,this)}}})),t.register("b78cS",(function(n,i){e(n.exports,"TreeOutline",(()=>t("2V4uM")));t("2V4uM"),t("2P8Jk")})),t.register("2V4uM",(function(n,i){e(n.exports,"defaultRenderer",(()=>p)),e(n.exports,"ItemSelectedEvent",(()=>u)),e(n.exports,"ItemMouseOverEvent",(()=>m)),e(n.exports,"ItemMouseOutEvent",(()=>g)),e(n.exports,"TreeOutline",(()=>f));var o=t("lz7WY"),s=t("dS5IF"),r=t("3T3mV"),l=t("kpUjp"),a=t("hveEP"),d=t("X0IwP"),c=t("2P8Jk");const h=a.RenderCoordinator.RenderCoordinator.instance();function p(e){return s.html`${e.treeNodeData}`}class u extends Event{static eventName="itemselected";data;constructor(e){super(u.eventName,{bubbles:!0,composed:!0}),this.data={node:e}}}class m extends Event{static eventName="itemmouseover";data;constructor(e){super(m.eventName,{bubbles:!0,composed:!0}),this.data={node:e}}}class g extends Event{static eventName="itemmouseout";data;constructor(e){super(g.eventName,{bubbles:!0,composed:!0}),this.data={node:e}}}class f extends HTMLElement{static litTagName=s.literal`devtools-tree-outline`;#e=this.attachShadow({mode:"open"});#t=[];#n=new Map;#i=new WeakMap;#o=!1;#s=null;#r=null;#l=(e,t)=>("string"!=typeof e.treeNodeData&&console.warn(`The default TreeOutline renderer simply stringifies its given value. You passed in ${JSON.stringify(e.treeNodeData,null,2)}. Consider providing a different defaultRenderer that can handle nodes of this type.`),s.html`${String(e.treeNodeData)}`);#a;#d=!1;#c=!1;static get observedAttributes(){return["nowrap","toplevelbordercolor"]}attributeChangedCallback(e,t,n){switch(e){case"nowrap":this.#h(n);break;case"toplevelbordercolor":this.#p(n)}}connectedCallback(){this.#p(this.getAttribute("toplevelbordercolor")),this.#h(this.getAttribute("nowrap")),this.#e.adoptedStyleSheets=[d.default,r.Style.default]}get data(){return{tree:this.#t,defaultRenderer:this.#l}}set data(e){this.#l=e.defaultRenderer,this.#t=e.tree,this.#a=e.filter,this.#o||(this.#r=this.#t[0]),this.#u()}async expandRecursively(e=2){await Promise.all(this.#t.map((t=>this.#m(t,0,e)))),await this.#u()}async collapseAllNodes(){this.#n.clear(),await this.#u()}async expandToAndSelectTreeNode(e){return this.expandToAndSelectTreeNodeId(e.id)}async expandToAndSelectTreeNodeId(e){const t=await(0,c.getPathToTreeNode)(this.#t,e);if(null===t)throw new Error(`Could not find node with id ${e} in the tree.`);t.forEach(((e,n)=>{n<t.length-1&&this.#g(e,!0)})),this.#s=e,await this.#u()}expandNodeIds(e){return e.forEach((e=>this.#n.set(e,!0))),this.#u()}focusNodeId(e){return this.#s=e,this.#u()}async collapseChildrenOfNode(e){const t=this.#i.get(e);t&&(await this.#f(t),await this.#u())}#h(e){l.SetCSSProperty.set(this,"--override-key-whitespace-wrapping",null!==e?"nowrap":"initial")}#p(e){l.SetCSSProperty.set(this,"--override-top-node-border",e?`1px solid ${e}`:"")}async#f(e){if(!(0,c.isExpandableNode)(e)||!this.#y(e))return;const t=await this.#v(e),n=Promise.all(t.map((e=>this.#f(e))));await n,this.#g(e,!1)}#b(){if(!this.#r)throw new Error("getSelectedNode was called but selectedTreeNode is null");return this.#r}async#v(e){const t=await(0,c.getNodeChildren)(e);if(!this.#a)return t;const n=[];for(const e of t){const t=this.#a(e.treeNodeData);if("SHOW"===t||this.#S(e)||e.id===this.#s)n.push(e);else if("FLATTEN"===t&&(0,c.isExpandableNode)(e)){const t=await this.#v(e);n.push(...t)}}return n}#g(e,t){this.#n.set(e.id,t)}#y(e){return this.#n.get(e.id)||!1}async#m(e,t,n){if(!(0,c.isExpandableNode)(e))return;if(this.#g(e,!0),t===n||!(0,c.isExpandableNode)(e))return;const i=await this.#v(e);await Promise.all(i.map((e=>this.#m(e,t+1,n))))}#E(e){return t=>{t.stopPropagation(),(0,c.isExpandableNode)(e)&&(this.#g(e,!this.#y(e)),this.#u())}}#C(e){e.stopPropagation();const t=null!==this.getAttribute("clickabletitle"),n=e.currentTarget,i=this.#i.get(n);t&&i&&(0,c.isExpandableNode)(i)&&this.#g(i,!this.#y(i)),this.#x(n)}async#x(e){const t=this.#i.get(e);t&&(this.#r=t,await this.#u(),this.dispatchEvent(new u(t)),h.write("DOMNode focus",(()=>{e.focus()})))}#w(e){if("Home"===e){const e=this.#e.querySelector('ul[role="tree"] > li[role="treeitem"]');e&&this.#x(e)}else if("End"===e){const e=this.#e.querySelectorAll('li[role="treeitem"]'),t=e[e.length-1];t&&this.#x(t)}}async#T(e,t){const n=this.#i.get(t);if(!n)return;const i=(0,c.findNextNodeForTreeOutlineKeyboardNavigation)({currentDOMNode:t,currentTreeNode:n,direction:e,setNodeExpandedState:(e,t)=>this.#g(e,t)});await this.#x(i)}#M(e){const t=this.#i.get(e);if(t&&(0,c.isExpandableNode)(t)){const e=this.#y(t);this.#g(t,!e),this.#u()}}async#N(e){if(!(e.target instanceof HTMLLIElement))throw new Error("event.target was not an <li> element");"Home"===e.key||"End"===e.key?(e.preventDefault(),this.#w(e.key)):o.KeyboardUtilities.keyIsArrowKey(e.key)?(e.preventDefault(),await this.#T(e.key,e.target)):"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),this.#M(e.target))}#I(e){this.#s=null,this.#x(e)}#S(e){return!!this.#r&&e.id===this.#r.id}#P(e,{depth:t,setSize:n,positionInSet:i}){let o;const r=this.#y(e);if((0,c.isExpandableNode)(e)&&r){const n=this.#v(e).then((e=>e.map(((n,i)=>this.#P(n,{depth:t+1,setSize:e.length,positionInSet:i})))));o=s.html`<ul role="group">${s.Directives.until(n)}</ul>`}else o=s.nothing;const a=this.#b()===e?0:-1,d=s.Directives.classMap({expanded:(0,c.isExpandableNode)(e)&&r,parent:(0,c.isExpandableNode)(e),selected:this.#S(e),"is-top-level":0===t}),h=s.Directives.ifDefined((0,c.isExpandableNode)(e)?String(r):void 0);let p;return p=e.renderer?e.renderer(e,{isExpanded:r}):this.#l(e,{isExpanded:r}),s.html`
      <li role="treeitem"
        tabindex=${a}
        aria-setsize=${n}
        aria-expanded=${h}
        aria-level=${t+1}
        aria-posinset=${i+1}
        class=${d}
        @click=${this.#C}
        track-dom-node-to-tree-node=${(0,c.trackDOMNodeToTreeNode)(this.#i,e)}
        on-render=${l.Directives.nodeRenderedCallback((t=>{t instanceof HTMLLIElement&&this.#s&&e.id===this.#s&&this.#I(t)}))}
      >
        <span class="arrow-and-key-wrapper"
          @mouseover=${()=>{this.dispatchEvent(new m(e))}}
          @mouseout=${()=>{this.dispatchEvent(new g(e))}}
        >
          <span class="arrow-icon" @click=${this.#E(e)}>
          </span>
          <span class="tree-node-key" data-node-key=${e.treeNodeData}>${p}</span>
        </span>
        ${o}
      </li>
    `}async#u(){if(!this.#d)return this.#d=!0,await h.write("TreeOutline render",(()=>{s.render(s.html`
      <div class="wrapping-container">
      <ul role="tree" @keydown=${this.#N}>
        ${this.#t.map(((e,t)=>this.#P(e,{depth:0,setSize:this.#t.length,positionInSet:t})))}
      </ul>
      </div>
      `,this.#e,{host:this})})),this.#o=!0,this.#d=!1,this.#c?(this.#c=!1,this.#u()):void 0;this.#c=!0}}l.CustomElements.defineComponent("devtools-tree-outline",f)})),t.register("X0IwP",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync('/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  --list-group-padding: 16px;\n}\n\nli {\n  list-style: none;\n  text-overflow: ellipsis;\n  min-height: 12px;\n}\n\n.tree-node-key {\n  white-space: var(--override-key-whitespace-wrapping);\n  /* Override the default |min-width: auto| to avoid overflows of flex items */\n  min-width: 0;\n}\n\n.arrow-icon {\n  display: block;\n  user-select: none;\n  -webkit-mask-image: var(--image-file-treeoutlineTriangles);\n  -webkit-mask-size: 32px 24px;\n  -webkit-mask-position: 0 0;\n  background-color: var(--color-text-primary);\n  content: "";\n  text-shadow: none;\n  height: 12px;\n  width: 13px;\n  overflow: hidden;\n}\n\nul {\n  margin: 0;\n  padding: 0;\n}\n\nul[role="group"] {\n  padding-left: var(--list-group-padding);\n}\n\nli:not(.parent) > .arrow-and-key-wrapper > .arrow-icon {\n  -webkit-mask-size: 0;\n}\n\nli.parent.expanded > .arrow-and-key-wrapper > .arrow-icon {\n  -webkit-mask-position: -16px 0;\n}\n\nli.is-top-level {\n  border-top: var(--override-top-node-border);\n}\n\nli.is-top-level:last-child {\n  border-bottom: var(--override-top-node-border);\n}\n\n:host([animated]) li:not(.is-top-level) {\n  animation-name: slideIn;\n  animation-duration: 150ms;\n  animation-timing-function: cubic-bezier(0, 0, 0.3, 1);\n  animation-fill-mode: forwards;\n}\n\n@keyframes slideIn {\n  from {\n    transform: translateY(-5px);\n    opacity: 0%;\n  }\n\n  to {\n    transform: none;\n    opacity: 100%;\n  }\n}\n\n.arrow-and-key-wrapper {\n  border: 2px solid transparent;\n  display: flex;\n  align-content: center;\n  align-items: center;\n}\n\n[role="treeitem"]:focus {\n  outline: 0;\n}\n\n[role="treeitem"].selected > .arrow-and-key-wrapper {\n  /* stylelint-disable-next-line color-named */\n  background-color: var(--legacy-item-selection-bg-color);\n}\n\n/*# sourceURL=treeOutline.css */\n');var o=i})),t.register("2P8Jk",(function(n,i){e(n.exports,"isExpandableNode",(()=>s)),e(n.exports,"trackDOMNodeToTreeNode",(()=>l)),e(n.exports,"getNodeChildren",(()=>g)),e(n.exports,"getPathToTreeNode",(()=>f)),e(n.exports,"findNextNodeForTreeOutlineKeyboardNavigation",(()=>v)),t("lz7WY");var o=t("dS5IF");function s(e){return"children"in e}class r extends o.Directive.Directive{constructor(e){if(super(e),e.type!==o.Directive.PartType.ATTRIBUTE)throw new Error("TrackDOMNodeToTreeNode directive must be used as an attribute.")}update(e,[t,n]){const i=e.element;if(!(i instanceof HTMLLIElement))throw new Error("trackTreeNodeToDOMNode must be used on <li> elements.");t.set(i,n)}render(e,t){}}const l=o.Directive.directive(r),a=e=>{const t=e.parentElement?.parentElement;if(t&&t instanceof HTMLLIElement){const e=t.nextElementSibling;return e&&e instanceof HTMLLIElement?e:a(t)}return null},d=e=>{const t=e.querySelector(':scope > [role="group"] > [role="treeitem"]:first-child');if(!t)throw new Error("Could not find child of expanded node.");return t},c=e=>null!==e.getAttribute("aria-expanded"),h=e=>c(e)&&"true"===e.getAttribute("aria-expanded"),p=e=>{const t=e.querySelector(':scope > [role="group"] > [role="treeitem"]:last-child');if(!t)throw new Error("Could not find child of expanded node.");return h(t)?p(t):t},u=e=>{let t=e.parentElement;if(!t)return null;for(;t&&"treeitem"!==t.getAttribute("role")&&t instanceof HTMLLIElement==!1;)t=t.parentElement;return t},m=new WeakMap,g=async e=>{if(!e.children)throw new Error("Asked for children of node that does not have any children.");const t=m.get(e);if(t)return t;const n=await e.children();return m.set(e,n),n},f=async(e,t)=>{for(const n of e){const e=await y(n,t,[n]);if(null!==e)return e}return null},y=async(e,t,n)=>{if(e.id===t)return n;if(e.children){const i=await g(e);for(const e of i){const i=await y(e,t,[...n,e]);if(null!==i)return i}}return null},v=e=>{const{currentDOMNode:t,currentTreeNode:n,direction:i,setNodeExpandedState:o}=e;if(!n)return t;if("ArrowDown"===i){if(h(t))return d(t);const e=(e=>{const t=e.nextElementSibling;return t&&t instanceof HTMLLIElement?t:null})(t);if(e)return e;const n=a(t);if(n)return n}else{if("ArrowRight"===i)return c(t)?h(t)?d(t):(o(n,!0),t):t;if("ArrowUp"===i){const e=(e=>{const t=e.previousElementSibling;return t&&t instanceof HTMLLIElement?t:null})(t);if(e)return h(e)?p(e):e;const n=u(t);if(n&&n instanceof HTMLLIElement)return n}else if("ArrowLeft"===i){if(h(t))return o(n,!1),t;const e=u(t);if(e&&e instanceof HTMLLIElement)return e}}return t}})),t.register("3x7Ll",(function(n,i){e(n.exports,"getRootNode",(()=>a)),e(n.exports,"getNodeAndAncestorsFromDOMNode",(()=>c)),e(n.exports,"sdkNodeToAXTreeNodes",(()=>h)),e(n.exports,"getNodeId",(()=>u)),e(n.exports,"accessibilityNodeRenderer",(()=>p));var o=t("eQFvP");t("Px6PZ");var s=t("iiZAl"),r=t("dS5IF");function l(e){const t=o.FrameManager.FrameManager.instance().getFrame(e)?.resourceTreeModel().target().model(o.AccessibilityModel.AccessibilityModel);if(!t)throw Error("Could not instantiate model for frameId");return t}async function a(e){const t=l(e),n=await t.requestRootNode(e);if(!n)throw Error("No accessibility root for frame");return n}function d(e){let t;if(t=e instanceof o.DOMModel.DOMDocument?e.body?.frameId():e.frameId(),!t)throw Error("No frameId for DOM node");return t}async function c(e){let t=d(e);const n=l(t),i=await n.requestAndLoadSubTreeToNode(e);if(!i)throw Error("Could not retrieve accessibility node for inspected DOM node");const s=o.FrameManager.FrameManager.instance().getTopFrame()?.id;if(!s)return i;for(;t!==s;){const e=await(o.FrameManager.FrameManager.instance().getFrame(t)?.getOwnerDOMNodeOrDocument());if(!e)break;t=d(e);const n=l(t),s=await n.requestAndLoadSubTreeToNode(e);i.push(...s||[])}return i}async function h(e){const t=e;return 0===(n=e).numChildren()&&"Iframe"!==n.role()?.value?[{treeNodeData:t,id:u(e)}]:[{treeNodeData:t,children:async()=>{const t=await async function(e){if("Iframe"===e.role()?.value){const t=await(e.deferredDOMNode()?.resolvePromise());if(!t)throw new Error("Could not find corresponding DOMNode");const n=t.frameOwnerFrameId();if(!n)throw Error("No owner frameId on iframe node");return[await a(n)]}return e.accessibilityModel().requestAXChildren(e.id(),e.getFrameId()||void 0)}(e);return(await Promise.all(t.map((e=>h(e))))).flat(1)},id:u(e)}];var n}function p(e){const t=s.AccessibilityTreeNode.litTagName,n=e.treeNodeData,i=n.name()?.value||"",o=n.role()?.value||"",l=n.properties()||[],a=n.ignored();return r.html`<${t} .data=${{name:i,role:o,ignored:a,properties:l}}></${t}>`}function u(e){return e.getFrameId()+"#"+e.id()}})),t.register("Px6PZ",(function(n,i){e(n.exports,"AccessibilityTreeNode",(()=>t("iiZAl"))),e(n.exports,"AdornerManager",(()=>t("au5Rx"))),e(n.exports,"AdornerSettingsPane",(()=>t("7HOJI"))),e(n.exports,"ComputedStyleProperty",(()=>t("6JdOX"))),e(n.exports,"ComputedStyleTrace",(()=>t("79cvW"))),e(n.exports,"CSSPropertyIconResolver",(()=>t("acCr5"))),e(n.exports,"CSSQuery",(()=>t("3g5FT"))),e(n.exports,"ElementsBreadcrumbs",(()=>t("lcU9C"))),e(n.exports,"Helper",(()=>t("6ICvI"))),e(n.exports,"LayoutPane",(()=>t("73QJC"))),e(n.exports,"QueryContainer",(()=>t("cMoBo"))),e(n.exports,"StylePropertyEditor",(()=>t("2p7Mx")));t("iiZAl"),t("au5Rx"),t("7HOJI"),t("6JdOX"),t("79cvW"),t("acCr5"),t("3g5FT"),t("lcU9C"),t("5gaNj"),t("4vjJn"),t("6ICvI"),t("73QJC"),t("60ifE"),t("5371F"),t("cMoBo"),t("2p7Mx")})),t.register("iiZAl",(function(n,i){e(n.exports,"AccessibilityTreeNode",(()=>u));var o=t("ixFnt"),s=t("lz7WY"),r=t("kpUjp"),l=t("hveEP"),a=t("dS5IF"),d=t("aZbBk");const c={ignored:"Ignored"},h=o.i18n.registerUIStrings("panels/elements/components/AccessibilityTreeNode.ts",c),p=o.i18n.getLocalizedString.bind(void 0,h);class u extends HTMLElement{static litTagName=a.literal`devtools-accessibility-tree-node`;#e=this.attachShadow({mode:"open"});#O=!0;#k="";#L="";#D=[];set data(e){this.#O=e.ignored,this.#k=e.name,this.#L=e.role,this.#D=e.properties,this.#u()}connectedCallback(){this.#e.adoptedStyleSheets=[d.default]}async#u(){const e=a.html`<span class='role-value'>${t=this.#L,t.length>1e4?s.StringUtilities.trimMiddle(t,1e4):t}</span>`;var t;const n=a.html`"<span class='attribute-value'>${this.#k}</span>"`,i=this.#D.map((({name:e,value:t})=>function(e){switch(e){case"boolean":case"booleanOrUndefined":case"string":case"number":return!0;default:return!1}}(t.type)?a.html`&nbsp<span class='attribute-name'>${e}</span>:&nbsp<span class='attribute-value'>${t.value}</span>`:a.nothing));await l.RenderCoordinator.RenderCoordinator.instance().write("Accessibility node render",(()=>{a.render(this.#O?a.html`<span>${p(c.ignored)}</span>`:a.html`${e}&nbsp${n}${i}`,this.#e,{host:this})}))}}r.CustomElements.defineComponent("devtools-accessibility-tree-node",u)})),t.register("aZbBk",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\nspan {\n  color: var(--color-syntax-8);\n  font-family: var(--monospace-font-family);\n  font-size: var(--monospace-font-size);\n}\n\n.role-value {\n  color: var(--color-syntax-2);\n}\n\n.attribute-name {\n  color: var(--color-syntax-4);\n}\n\n.attribute-value {\n  color: var(--color-syntax-3);\n}\n\n/*# sourceURL=accessibilityTreeNode.css */\n");var o=i})),t.register("au5Rx",(function(t,n){var i,o;function s(e){switch(e){case i.GRID:return{name:"grid",category:"Layout",enabledByDefault:!0};case i.FLEX:return{name:"flex",category:"Layout",enabledByDefault:!0};case i.AD:return{name:"ad",category:"Security",enabledByDefault:!0};case i.SCROLL_SNAP:return{name:"scroll-snap",category:"Layout",enabledByDefault:!0};case i.CONTAINER:return{name:"container",category:"Layout",enabledByDefault:!0}}}let r;function l(e){if(!r){r=new Map;for(const{name:e,category:t}of Object.values(i).map(s))r.set(e,t)}return r.get(e)||"Default"}e(t.exports,"RegisteredAdorners",(()=>i)),e(t.exports,"getRegisteredAdorner",(()=>s)),e(t.exports,"DefaultAdornerSettings",(()=>a)),e(t.exports,"AdornerManager",(()=>d)),e(t.exports,"AdornerCategoryOrder",(()=>c)),e(t.exports,"compareAdornerNamesByCategory",(()=>h)),(o=i||(i={})).GRID="grid",o.FLEX="flex",o.AD="ad",o.SCROLL_SNAP="scroll-snap",o.CONTAINER="container";const a=Object.values(i).map(s).map((({name:e,enabledByDefault:t})=>({adorner:e,isEnabled:t})));class d{#A=new Map;#R;constructor(e){this.#R=e,this.#F()}updateSettings(e){this.#A=e,this.#U()}getSettings(){return this.#A}isAdornerEnabled(e){return this.#A.get(e)||!1}#U(){const e=[];for(const[t,n]of this.#A)e.push({adorner:t,isEnabled:n});this.#R.set(e)}#B(){const e=this.#R.get();for(const t of e)this.#A.set(t.adorner,t.isEnabled)}#F(){this.#B();const e=new Set(this.#A.keys());for(const{adorner:t,isEnabled:n}of a)e.delete(t),this.#A.has(t)||this.#A.set(t,n);for(const t of e)this.#A.delete(t);this.#U()}}const c=new Map(["Security","Layout","Default"].map(((e,t)=>[e,t+1])));function h(e,t){return(c.get(l(e))||Number.POSITIVE_INFINITY)-(c.get(l(t))||Number.POSITIVE_INFINITY)}})),t.register("7HOJI",(function(n,i){e(n.exports,"AdornerSettingUpdatedEvent",(()=>m)),e(n.exports,"AdornerSettingsPane",(()=>g));var o=t("ixFnt"),s=t("kpUjp"),r=t("lzcO5"),l=t("dS5IF"),a=t("k4jR8");const d={settingsTitle:"Show badges",closeButton:"Close"},c=o.i18n.registerUIStrings("panels/elements/components/AdornerSettingsPane.ts",d),h=o.i18n.getLocalizedString.bind(void 0,c),{render:p,html:u}=l;class m extends Event{static eventName="adornersettingupdated";data;constructor(e,t,n){super(m.eventName,{}),this.data={adornerName:e,isEnabledNow:t,newSettings:n}}}class g extends HTMLElement{static litTagName=l.literal`devtools-adorner-settings-pane`;#e=this.attachShadow({mode:"open"});#H=new Map;connectedCallback(){this.#e.adoptedStyleSheets=[r.checkboxStyles,a.default]}set data(e){this.#H=new Map(e.settings.entries()),this.#u()}show(){this.classList.remove("hidden");const e=this.#e.querySelector(".adorner-settings-pane");e&&e.focus()}hide(){this.classList.add("hidden")}#V(e){const t=e.target,n=t.dataset.adorner;if(void 0===n)return;const i=t.checked;this.#H.set(n,i),this.dispatchEvent(new m(n,i,this.#H)),this.#u()}#u(){const e=[];for(const[t,n]of this.#H)e.push(u`
        <label class="setting" title=${t}>
          <input
            class="adorner-status"
            type="checkbox" name=${t}
            .checked=${n}
            data-adorner=${t}>
          <span class="adorner-name">${t}</span>
        </label>
      `);p(u`
      <div class="adorner-settings-pane" tabindex="-1">
        <div class="settings-title">${h(d.settingsTitle)}</div>
        <div class="setting-list" @change=${this.#V}>
          ${e}
        </div>
        <button class="close" @click=${this.hide} aria-label=${h(d.closeButton)}></button>
      </div>
    `,this.#e,{host:this})}}s.CustomElements.defineComponent("devtools-adorner-settings-pane",g)})),t.register("k4jR8",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync('/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.adorner-settings-pane {\n  display: flex;\n  height: 2.67em;\n  padding: 0 12px;\n  color: var(--color-text-primary);\n  font-size: 12px;\n  align-items: center;\n}\n\n.settings-title {\n  font-weight: 500;\n}\n\n.setting {\n  margin-left: 1em;\n}\n\n.adorner-status {\n  margin: auto 0.4em auto 0;\n}\n\n.adorner-status,\n.adorner-name {\n  vertical-align: middle;\n}\n\n.close {\n  position: relative;\n  margin-left: auto;\n  font-size: 1em;\n  width: 1.5em;\n  height: 1.5em;\n  border: none;\n  border-radius: 50%;\n  background-color: var(--color-background-elevation-1);\n}\n\n.close::before,\n.close::after {\n  content: "";\n  display: inline-block;\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  margin: auto;\n  width: 1em;\n  height: 0.2em;\n  background-color: var(--color-text-secondary);\n  border-radius: 2px;\n}\n\n.close::before {\n  transform: rotate(45deg);\n}\n\n.close::after {\n  transform: rotate(-45deg);\n}\n\n/*# sourceURL=adornerSettingsPane.css */\n');var o=i})),t.register("6JdOX",(function(n,i){e(n.exports,"ComputedStyleProperty",(()=>d));var o=t("kpUjp"),s=t("dS5IF"),r=t("bjKZj");const{render:l,html:a}=s;class d extends HTMLElement{static litTagName=s.literal`devtools-computed-style-property`;#e=this.attachShadow({mode:"open"});#W=!1;#z=!1;#$=()=>{};connectedCallback(){this.#e.adoptedStyleSheets=[r.default]}set data(e){this.#W=e.inherited,this.#z=e.traceable,this.#$=e.onNavigateToSource,this.#u()}#u(){l(a`
      <div class="computed-style-property ${this.#W?"inherited":""}">
        <slot name="property-name"></slot>
        <span class="hidden" aria-hidden="false">: </span>
        ${this.#z?a`<span class="goto" @click=${this.#$}></span>`:null}
        <slot name="property-value"></slot>
        <span class="hidden" aria-hidden="false">;</span>
      </div>
    `,this.#e,{host:this})}}o.CustomElements.defineComponent("devtools-computed-style-property",d)})),t.register("bjKZj",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync('/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  position: relative;\n  overflow: hidden;\n  flex: auto;\n  text-overflow: ellipsis;\n}\n\n.computed-style-property {\n  --goto-size: 16px;\n\n  min-height: 16px;\n  box-sizing: border-box;\n  padding-top: 2px;\n  white-space: nowrap;\n}\n\n.computed-style-property.inherited {\n  opacity: 50%;\n}\n\nslot[name="property-name"],\nslot[name="property-value"] {\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\nslot[name="property-name"] {\n  width: 16em;\n  max-width: 52%;\n  margin-right: calc(var(--goto-size) / 2);\n  display: inline-block;\n  vertical-align: text-top;\n}\n\nslot[name="property-value"] {\n  margin-left: 2em;\n}\n\n.goto {\n  display: none;\n  cursor: pointer;\n  position: absolute;\n  width: var(--goto-size);\n  height: var(--goto-size);\n  margin: -1px 0 0 calc(-1 * var(--goto-size));\n  -webkit-mask-image: var(--image-file-mediumIcons);\n  -webkit-mask-position: -32px 48px;\n  background-color: var(--legacy-active-control-bg-color);\n}\n\n.computed-style-property:hover .goto {\n  display: inline-block;\n}\n\n.hidden {\n  display: none;\n}\n/* narrowed styles */\n:host-context(.computed-narrow) .computed-style-property {\n  white-space: normal;\n}\n\n:host-context(.computed-narrow) slot[name="property-name"],\n:host-context(.computed-narrow) slot[name="property-value"] {\n  display: inline-block;\n  width: 100%;\n  max-width: 100%;\n  margin-left: 0;\n  white-space: nowrap;\n}\n\n:host-context(.computed-narrow) .goto {\n  display: none;\n}\n/* high-contrast styles */\n@media (forced-colors: active) {\n  .computed-style-property.inherited {\n    opacity: 100%;\n  }\n\n  :host-context(.monospace.computed-properties) .computed-style-property:hover {\n    forced-color-adjust: none;\n    background-color: Highlight;\n  }\n\n  :host-context(.monospace.computed-properties) .computed-style-property:hover * {\n    color: HighlightText;\n  }\n\n  :host-context(.monospace.computed-properties) .goto {\n    background-color: HighlightText;\n  }\n}\n\n/*# sourceURL=computedStyleProperty.css */\n');var o=i})),t.register("79cvW",(function(n,i){e(n.exports,"ComputedStyleTrace",(()=>d));var o=t("kpUjp"),s=t("dS5IF"),r=t("7iOWI");const{render:l,html:a}=s;class d extends HTMLElement{static litTagName=s.literal`devtools-computed-style-trace`;#e=this.attachShadow({mode:"open"});#_="";#j=!1;#$=()=>{};connectedCallback(){this.#e.adoptedStyleSheets=[r.default]}set data(e){this.#_=e.selector,this.#j=e.active,this.#$=e.onNavigateToSource,this.#u()}#u(){l(a`
      <div class="computed-style-trace ${this.#j?"active":"inactive"}">
        <span class="goto" @click=${this.#$}></span>
        <slot name="trace-value" @click=${this.#$}></slot>
        <span class="trace-selector">${this.#_}</span>
        <slot name="trace-link"></slot>
      </div>
    `,this.#e,{host:this})}}o.CustomElements.defineComponent("devtools-computed-style-trace",d)})),t.register("7iOWI",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync('/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  text-overflow: ellipsis;\n  overflow: hidden;\n  flex-grow: 1;\n}\n\n.computed-style-trace {\n  margin-left: 16px;\n}\n\n.computed-style-trace:hover {\n  background-color: var(--legacy-focus-bg-color);\n  cursor: text;\n}\n\n.goto {\n  /* TODO: reuse with ComputedStyleProperty */\n  --size: 16px;\n\n  display: none;\n  cursor: pointer;\n  position: absolute;\n  width: var(--size);\n  height: var(--size);\n  margin: -1px 0 0 calc(-1 * var(--size));\n  -webkit-mask-image: var(--image-file-mediumIcons);\n  -webkit-mask-position: -32px 48px;\n  background-color: var(--legacy-active-control-bg-color);\n}\n\n.computed-style-trace:hover .goto {\n  display: inline-block;\n}\n\n.trace-value {\n  margin-left: 16px;\n}\n\n.computed-style-trace.inactive slot[name="trace-value"] {\n  text-decoration: line-through;\n}\n\n.trace-selector {\n  --override-trace-selector-color: rgb(128 128 128);\n\n  color: var(--override-trace-selector-color);\n  padding-left: 2em;\n}\n\n::slotted([slot="trace-link"]) {\n  user-select: none;\n  float: right;\n  padding-left: 1em;\n  position: relative;\n  z-index: 1;\n}\n/* high-contrast styles */\n@media (forced-colors: active) {\n  :host-context(.monospace.computed-properties) .computed-style-trace:hover {\n    forced-color-adjust: none;\n    background-color: Highlight;\n  }\n\n  :host-context(.monospace.computed-properties) .goto {\n    background-color: HighlightText;\n  }\n\n  :host-context(.monospace.computed-properties) .computed-style-trace:hover * {\n    color: HighlightText;\n  }\n}\n\n/*# sourceURL=computedStyleTrace.css */\n');var o=i})),t.register("acCr5",(function(t,n){e(t.exports,"PhysicalDirection",(()=>o)),e(t.exports,"reverseDirection",(()=>r)),e(t.exports,"getPhysicalDirections",(()=>a)),e(t.exports,"rotateFlexDirectionIcon",(()=>d)),e(t.exports,"rotateAlignContentIcon",(()=>c)),e(t.exports,"rotateJustifyContentIcon",(()=>h)),e(t.exports,"rotateJustifyItemsIcon",(()=>p)),e(t.exports,"rotateAlignItemsIcon",(()=>u)),e(t.exports,"roateFlexWrapIcon",(()=>T)),e(t.exports,"findIcon",(()=>D)),e(t.exports,"findFlexContainerIcon",(()=>A)),e(t.exports,"findFlexItemIcon",(()=>R)),e(t.exports,"findGridContainerIcon",(()=>F)),e(t.exports,"findGridItemIcon",(()=>U));const i=new Set(["tb","tb-rl","vertical-lr","vertical-rl"]);var o,s;function r(e){if(e===o.LEFT_TO_RIGHT)return o.RIGHT_TO_LEFT;if(e===o.RIGHT_TO_LEFT)return o.LEFT_TO_RIGHT;if(e===o.TOP_TO_BOTTOM)return o.BOTTOM_TO_TOP;if(e===o.BOTTOM_TO_TOP)return o.TOP_TO_BOTTOM;throw new Error("Unknown PhysicalFlexDirection")}function l(e){return{...e,"row-reverse":r(e.row),"column-reverse":r(e.column)}}function a(e){const t="rtl"===e.get("direction"),n=e.get("writing-mode");return l(n&&i.has(n)?{row:t?o.BOTTOM_TO_TOP:o.TOP_TO_BOTTOM,column:"vertical-lr"===n?o.LEFT_TO_RIGHT:o.RIGHT_TO_LEFT}:{row:t?o.RIGHT_TO_LEFT:o.LEFT_TO_RIGHT,column:o.TOP_TO_BOTTOM})}function d(e){let t=!0,n=!1,i=-90;return e===o.RIGHT_TO_LEFT?(i=90,n=!1,t=!1):e===o.TOP_TO_BOTTOM?(i=0,t=!1,n=!1):e===o.BOTTOM_TO_TOP&&(i=0,t=!1,n=!0),{iconName:"flex-direction-icon",rotate:i,scaleX:t?-1:1,scaleY:n?-1:1}}function c(e,t){return{iconName:e,rotate:t===o.RIGHT_TO_LEFT?90:t===o.LEFT_TO_RIGHT?-90:0,scaleX:1,scaleY:1}}function h(e,t){return{iconName:e,rotate:t===o.TOP_TO_BOTTOM?90:t===o.BOTTOM_TO_TOP?-90:0,scaleX:t===o.RIGHT_TO_LEFT?-1:1,scaleY:1}}function p(e,t){return{iconName:e,rotate:t===o.TOP_TO_BOTTOM?90:t===o.BOTTOM_TO_TOP?-90:0,scaleX:t===o.RIGHT_TO_LEFT?-1:1,scaleY:1}}function u(e,t){return{iconName:e,rotate:t===o.RIGHT_TO_LEFT?90:t===o.LEFT_TO_RIGHT?-90:0,scaleX:1,scaleY:1}}function m(e){return function(t){return d(a(t)[e])}}function g(e){return function(t){const n=a(t),i=new Map([["column",n.row],["row",n.column],["column-reverse",n.row],["row-reverse",n.column]]),o=t.get("flex-direction")||"row",s=i.get(o);if(!s)throw new Error("Unknown direction for flex-align icon");return c(e,s)}}function f(e){return function(t){const n=a(t);return c(e,n.column)}}function y(e){return function(t){const n=a(t);return h(e,n[t.get("flex-direction")||"row"])}}function v(e){return function(t){const n=a(t);return h(e,n.row)}}function b(e){return function(t){const n=a(t);return p(e,n.row)}}function S(e){return function(t){const n=a(t),i=new Map([["column",n.row],["row",n.column],["column-reverse",n.row],["row-reverse",n.column]]),o=t.get("flex-direction")||"row",s=i.get(o);if(!s)throw new Error("Unknown direction for flex-align icon");return u(e,s)}}function E(e){return function(t){const n=a(t);return u(e,n.column)}}function C(){return{iconName:"baseline-icon",rotate:0,scaleX:1,scaleY:1}}function x(e){return function(t,n){return S(e)(n)}}function w(e){return function(t,n){return E(e)(n)}}function T(e,t){return{iconName:e,rotate:t===o.BOTTOM_TO_TOP||t===o.TOP_TO_BOTTOM?90:0,scaleX:1,scaleY:1}}function M(e){return function(t){const n=a(t),i=t.get("flex-direction")||"row";return T(e,n[i])}}(s=o||(o={})).LEFT_TO_RIGHT="left-to-right",s.RIGHT_TO_LEFT="right-to-left",s.BOTTOM_TO_TOP="bottom-to-top",s.TOP_TO_BOTTOM="top-to-bottom";const N=new Map([["flex-direction: row",m("row")],["flex-direction: column",m("column")],["flex-direction: column-reverse",m("column-reverse")],["flex-direction: row-reverse",m("row-reverse")],["flex-direction: initial",m("row")],["flex-direction: unset",m("row")],["flex-direction: revert",m("row")],["align-content: center",g("align-content-center-icon")],["align-content: space-around",g("align-content-space-around-icon")],["align-content: space-between",g("align-content-space-between-icon")],["align-content: stretch",g("align-content-stretch-icon")],["align-content: space-evenly",g("align-content-space-evenly-icon")],["align-content: flex-end",g("align-content-end-icon")],["align-content: flex-start",g("align-content-start-icon")],["align-content: normal",g("align-content-stretch-icon")],["align-content: revert",g("align-content-stretch-icon")],["align-content: unset",g("align-content-stretch-icon")],["align-content: initial",g("align-content-stretch-icon")],["justify-content: center",y("justify-content-center-icon")],["justify-content: space-around",y("justify-content-space-around-icon")],["justify-content: space-between",y("justify-content-space-between-icon")],["justify-content: space-evenly",y("justify-content-space-evenly-icon")],["justify-content: flex-end",y("justify-content-flex-end-icon")],["justify-content: flex-start",y("justify-content-flex-start-icon")],["align-items: stretch",S("align-items-stretch-icon")],["align-items: flex-end",S("align-items-flex-end-icon")],["align-items: flex-start",S("align-items-flex-start-icon")],["align-items: center",S("align-items-center-icon")],["align-items: baseline",C],["align-content: baseline",C],["flex-wrap: wrap",M("flex-wrap-icon")],["flex-wrap: nowrap",M("flex-nowrap-icon")]]),I=new Map([["align-self: baseline",C],["align-self: center",x("align-self-center-icon")],["align-self: flex-start",x("align-self-flex-start-icon")],["align-self: flex-end",x("align-self-flex-end-icon")],["align-self: stretch",x("align-self-stretch-icon")]]),P=new Map([["align-content: center",f("align-content-center-icon")],["align-content: space-around",f("align-content-space-around-icon")],["align-content: space-between",f("align-content-space-between-icon")],["align-content: stretch",f("align-content-stretch-icon")],["align-content: space-evenly",f("align-content-space-evenly-icon")],["align-content: end",f("align-content-end-icon")],["align-content: start",f("align-content-start-icon")],["align-content: baseline",C],["justify-content: center",v("justify-content-center-icon")],["justify-content: space-around",v("justify-content-space-around-icon")],["justify-content: space-between",v("justify-content-space-between-icon")],["justify-content: space-evenly",v("justify-content-space-evenly-icon")],["justify-content: end",v("justify-content-flex-end-icon")],["justify-content: start",v("justify-content-flex-start-icon")],["align-items: stretch",E("align-items-stretch-icon")],["align-items: end",E("align-items-flex-end-icon")],["align-items: start",E("align-items-flex-start-icon")],["align-items: center",E("align-items-center-icon")],["align-items: baseline",C],["justify-items: center",b("justify-items-center-icon")],["justify-items: stretch",b("justify-items-stretch-icon")],["justify-items: end",b("justify-items-end-icon")],["justify-items: start",b("justify-items-start-icon")],["justify-items: baseline",C]]),O=new Map([["align-self: baseline",C],["align-self: center",w("align-self-center-icon")],["align-self: start",w("align-self-flex-start-icon")],["align-self: end",w("align-self-flex-end-icon")],["align-self: stretch",w("align-self-stretch-icon")]]),k=e=>{const t=e?.get("display");return"flex"===t||"inline-flex"===t},L=e=>{const t=e?.get("display");return"grid"===t||"inline-grid"===t};function D(e,t,n){if(k(t)){const n=A(e,t);if(n)return n}if(k(n)){const i=R(e,t,n);if(i)return i}if(L(t)){const n=F(e,t);if(n)return n}if(L(n)){const i=U(e,t,n);if(i)return i}return null}function A(e,t){const n=N.get(e);return n?n(t||new Map):null}function R(e,t,n){const i=I.get(e);return i?i(t||new Map,n||new Map):null}function F(e,t){const n=P.get(e);return n?n(t||new Map):null}function U(e,t,n){const i=O.get(e);return i?i(t||new Map,n||new Map):null}})),t.register("3g5FT",(function(n,i){e(n.exports,"CSSQuery",(()=>c));var o=t("kpUjp"),s=t("80baR"),r=t("dS5IF"),l=t("f9jOI");const{render:a,html:d}=r;class c extends HTMLElement{static litTagName=r.literal`devtools-css-query`;#e=this.attachShadow({mode:"open"});#q="";#G;#K="";#Q;set data(e){this.#q=e.queryPrefix,this.#G=e.queryName,this.#K=e.queryText,this.#Q=e.onQueryTextClick,this.#u()}connectedCallback(){this.#e.adoptedStyleSheets=[l.default,s.default]}#u(){const e=r.Directives.classMap({query:!0,editable:Boolean(this.#Q)}),t=d`
      <span class="query-text" @click=${this.#Q}>${this.#K}</span>
    `;a(d`
      <div class=${e}>
        ${this.#q?d`<span>${this.#q+" "}</span>`:r.nothing}${this.#G?d`<span>${this.#G+" "}</span>`:r.nothing}${t}
      </div>
    `,this.#e,{host:this})}}o.CustomElements.defineComponent("devtools-css-query",c)})),t.register("f9jOI",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.query:not(.editing-query) {\n  overflow: hidden;\n}\n\n.editable .query-text {\n  color: var(--color-text-primary);\n}\n\n.editable .query-text:hover {\n  text-decoration: var(--override-styles-section-text-hover-text-decoration);\n  cursor: var(--override-styles-section-text-hover-cursor);\n}\n\n/*# sourceURL=cssQuery.css */\n");var o=i})),t.register("lcU9C",(function(n,i){e(n.exports,"NodeSelectedEvent",(()=>m)),e(n.exports,"ElementsBreadcrumbs",(()=>f));var o=t("ixFnt"),s=t("kpUjp"),r=t("hveEP"),l=t("dS5IF"),a=t("eAlhq"),d=t("5gaNj"),c=t("5371F");const h={breadcrumbs:"DOM tree breadcrumbs"},p=o.i18n.registerUIStrings("panels/elements/components/ElementsBreadcrumbs.ts",h),u=o.i18n.getLocalizedString.bind(void 0,p);class m extends Event{static eventName="breadcrumbsnodeselected";legacyDomNode;constructor(e){super(m.eventName,{}),this.legacyDomNode=e.legacyDomNode}}const g=r.RenderCoordinator.RenderCoordinator.instance();class f extends HTMLElement{static litTagName=l.literal`devtools-elements-breadcrumbs`;#e=this.attachShadow({mode:"open"});#X=new ResizeObserver((()=>this.#Y()));#J=[];#Z=null;#ee=!1;#te="start";#ne=!1;#ie=!1;connectedCallback(){this.#e.adoptedStyleSheets=[a.default]}set data(e){this.#Z=e.selectedNode,this.#J=e.crumbs,this.#ie=!1,this.#oe()}disconnectedCallback(){this.#ne=!1,this.#X.disconnect()}#se(e){return t=>{t.preventDefault(),this.dispatchEvent(new m(e))}}async#Y(){const e=this.#e.querySelector(".crumbs"),t=this.#e.querySelector(".crumbs-scroll-container");if(!e||!t)return;const n=await g.read((()=>e.clientWidth)),i=await g.read((()=>t.clientWidth));i>=n&&!1===this.#ee?(this.#ee=!0,this.#te="start",this.#u()):i<n&&!0===this.#ee&&(this.#ee=!1,this.#te="start",this.#u())}async#oe(){await this.#u(),this.#re(),this.#le()}#ae(e){return()=>e.highlightNode()}#de(e){return()=>e.clearHighlight()}#ce(e){return()=>e.highlightNode()}#he(e){return()=>e.clearHighlight()}#re(){if(!this.#X||!0===this.#ne)return;const e=this.#e.querySelector(".crumbs");e&&(this.#X.observe(e),this.#ne=!0)}async#pe(){const e=this.#e.querySelector(".crumbs-scroll-container"),t=this.#e.querySelector(".crumbs-window");if(!e||!t)return;const n=await g.read((()=>t.clientWidth));await g.read((()=>e.clientWidth))<n-20?this.#ee&&(this.#ee=!1,this.#u()):this.#ee||(this.#ee=!0,this.#u())}#ue(e){if(!e.target)return;const t=e.target;this.#me(t)}#me(e){const t=e.scrollWidth-e.clientWidth,n=e.scrollLeft;this.#te=n<10?"start":n>=t-10?"end":"middle",this.#u()}#ge(e){return()=>{this.#ie=!0;const t=this.#e.querySelector(".crumbs-window");if(!t)return;const n=t.clientWidth/2,i="left"===e?Math.max(Math.floor(t.scrollLeft-n),0):t.scrollLeft+n;t.scrollTo({behavior:"smooth",left:i})}}#fe(e,t){const n=l.Directives.classMap({overflow:!0,[e]:!0,hidden:!1===this.#ee});return l.html`
      <button
        class=${n}
        @click=${this.#ge(e)}
        ?disabled=${t}
        aria-label="Scroll ${e}"
      >&hellip;</button>
      `}async#u(){const e=(0,d.crumbsToRender)(this.#J,this.#Z);await g.write("Breadcrumbs render",(()=>{l.render(l.html`
        <nav class="crumbs" aria-label=${u(h.breadcrumbs)}>
          ${this.#fe("left","start"===this.#te)}

          <div class="crumbs-window" @scroll=${this.#ue}>
            <ul class="crumbs-scroll-container">
              ${e.map((e=>{const t={crumb:!0,selected:e.selected};return l.html`
                  <li class=${l.Directives.classMap(t)}
                    data-node-id=${e.node.id}
                    data-crumb="true"
                  >
                    <a href="#"
                      draggable=false
                      class="crumb-link"
                      @click=${this.#se(e.node)}
                      @mousemove=${this.#ae(e.node)}
                      @mouseleave=${this.#de(e.node)}
                      @focus=${this.#ce(e.node)}
                      @blur=${this.#he(e.node)}
                    ><${c.NodeText.litTagName} data-node-title=${e.title.main} .data=${{nodeTitle:e.title.main,nodeId:e.title.extras.id,nodeClasses:e.title.extras.classes}}></${c.NodeText.litTagName}></a>
                  </li>`}))}
            </ul>
          </div>
          ${this.#fe("right","end"===this.#te)}
        </nav>
      `,this.#e,{host:this})})),this.#pe()}async#le(){if(!this.#Z||!this.#e||!this.#ee||this.#ie)return;const e=this.#Z.id,t=this.#e.querySelector(`.crumb[data-node-id="${e}"]`);t&&await g.scroll((()=>{t.scrollIntoView({behavior:"smooth"})}))}}s.CustomElements.defineComponent("devtools-elements-breadcrumbs",f)})),t.register("eAlhq",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  --override-node-text-label-color: var(--color-syntax-2);\n  --override-node-text-class-color: var(--color-syntax-4);\n  --override-node-text-id-color: var(--color-syntax-4);\n  --override-node-text-multiple-descriptors-id: var(--color-syntax-7);\n  --override-node-text-multiple-descriptors-class: var(--color-syntax-4);\n}\n\n.crumbs {\n  display: inline-flex;\n  align-items: stretch;\n  width: 100%;\n  overflow: hidden;\n  pointer-events: auto;\n  cursor: default;\n  white-space: nowrap;\n  position: relative;\n  background: var(--color-background);\n  font-size: inherit;\n  font-family: inherit;\n}\n\n.crumbs-window {\n  flex-grow: 2;\n  overflow: hidden;\n}\n\n.crumbs-scroll-container {\n  display: inline-flex;\n  margin: 0;\n  padding: 0;\n}\n\n.crumb {\n  display: block;\n  padding: 0 7px;\n  line-height: 23px;\n  white-space: nowrap;\n}\n\n.overflow {\n  padding: 0 7px;\n  font-weight: bold;\n  display: block;\n  border: none;\n  flex-grow: 0;\n  flex-shrink: 0;\n  text-align: center;\n  background-color: var(--color-background-elevation-1);\n  color: var(--color-text-secondary);\n  margin: 1px;\n  outline: var(--color-background-elevation-1) solid 1px;\n}\n\n.overflow.hidden {\n  display: none;\n}\n\n.overflow:disabled {\n  opacity: 50%;\n}\n\n.overflow:focus {\n  outline-color: var(--color-primary);\n}\n\n.overflow:not(:disabled):hover {\n  background-color: var(--color-background-elevation-2);\n  color: var(--color-text-primary);\n}\n\n.crumb-link {\n  text-decoration: none;\n  color: inherit;\n}\n\n.crumb:hover {\n  background: var(--color-background-elevation-2);\n}\n\n.crumb.selected {\n  background: var(--color-background-elevation-1);\n}\n\n.crumb:focus {\n  outline: var(--color-primary) auto 1px;\n}\n\n/*# sourceURL=elementsBreadcrumbs.css */\n");var o=i})),t.register("5gaNj",(function(n,i){e(n.exports,"crumbsToRender",(()=>a));var o=t("ixFnt");const s={text:"(text)"},r=o.i18n.registerUIStrings("panels/elements/components/ElementsBreadcrumbsUtils.ts",s),l=o.i18n.getLocalizedString.bind(void 0,r),a=(e,t)=>t?e.filter((e=>e.nodeType!==Node.DOCUMENT_NODE)).map((e=>({title:c(e),selected:e.id===t.id,node:e,originalNode:e.legacyDomNode}))).reverse():[],d=(e,t={})=>({main:e,extras:t}),c=e=>{switch(e.nodeType){case Node.ELEMENT_NODE:{if(e.pseudoType)return d("::"+e.pseudoType);const t=d(e.nodeNameNicelyCased),n=e.getAttribute("id");n&&(t.extras.id=n);const i=e.getAttribute("class");if(i){const e=new Set(i.split(/\s+/));t.extras.classes=Array.from(e)}return t}case Node.TEXT_NODE:return d(l(s.text));case Node.COMMENT_NODE:return d("\x3c!--\x3e");case Node.DOCUMENT_TYPE_NODE:return d("<!doctype>");case Node.DOCUMENT_FRAGMENT_NODE:return d(e.shadowRootType?"#shadow-root":e.nodeNameNicelyCased);default:return d(e.nodeNameNicelyCased)}}})),t.register("5371F",(function(n,i){e(n.exports,"NodeText",(()=>d));var o=t("kpUjp"),s=t("dS5IF"),r=t("6wSDI");const{render:l,html:a}=s;class d extends HTMLElement{static litTagName=s.literal`devtools-node-text`;#e=this.attachShadow({mode:"open"});#ye="";#ve="";#be=[];connectedCallback(){this.#e.adoptedStyleSheets=[r.default]}set data(e){this.#ye=e.nodeTitle,this.#ve=e.nodeId,this.#be=e.nodeClasses,this.#u()}#u(){const e=Boolean(this.#ve),t=Boolean(this.#be&&this.#be.length>0),n=[a`<span class="node-label-name">${this.#ye}</span>`];if(this.#ve){const e=s.Directives.classMap({"node-label-id":!0,"node-multiple-descriptors":t});n.push(a`<span class=${e}>#${CSS.escape(this.#ve)}</span>`)}if(this.#be&&this.#be.length>0){const t=this.#be.map((e=>`.${CSS.escape(e)}`)).join(""),i=s.Directives.classMap({"node-label-class":!0,"node-multiple-descriptors":e});n.push(a`<span class=${i}>${t}</span>`)}l(a`
      ${n}
    `,this.#e,{host:this})}}o.CustomElements.defineComponent("devtools-node-text",d)})),t.register("6wSDI",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n/* See: https://crbug.com/1227651 for details on changing these to --override pattern. */\n\n.node-label-name {\n  color: var(--override-node-text-label-color, --color-token-tag);\n}\n\n.node-label-class {\n  color: var(--override-node-text-class-color, --color-token-attribute);\n}\n\n.node-label-id {\n  color: var(--override-node-text-id-color);\n}\n\n.node-label-class.node-multiple-descriptors {\n  color: var(--override-node-text-multiple-descriptors-class, var(--override-node-text-class-color, --color-token-attribute));\n}\n\n.node-label-id.node-multiple-descriptors {\n  color: var(--override-node-text-multiple-descriptors-id, var(--override-node-text-id-color, --color-token-attribute));\n}\n\n/*# sourceURL=nodeText.css */\n");var o=i})),t.register("4vjJn",(function(e,n){var i=t("kpUjp"),o=t("dS5IF"),s=t("fAQAz");class r extends HTMLElement{static litTagName=o.literal`devtools-elements-panel-link`;#e=this.attachShadow({mode:"open"});#Se=()=>{};#Ee=()=>{};#Ce=()=>{};set data(e){this.#Se=e.onElementRevealIconClick,this.#Ee=e.onElementRevealIconMouseEnter,this.#Ce=e.onElementRevealIconMouseLeave,this.#oe()}#oe(){this.#u()}connectedCallback(){this.#e.adoptedStyleSheets=[s.default]}#u(){o.render(o.html`
      <span
        class="element-reveal-icon"
        @click=${this.#Se}
        @mouseenter=${this.#Ee}
        @mouseleave=${this.#Ce}></span>
      `,this.#e,{host:this})}}i.CustomElements.defineComponent("devtools-elements-panel-link",r)})),t.register("fAQAz",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.element-reveal-icon {\n  --override-element-reveal-icon-background: rgb(110 110 110);\n\n  display: inline-block;\n  width: 28px;\n  height: 24px;\n  -webkit-mask-position: -140px 96px;\n  -webkit-mask-image: var(--image-file-largeIcons);\n  background-color: var(--override-element-reveal-icon-background);\n}\n\n/*# sourceURL=elementsPanelLink.css */\n");var o=i})),t.register("6ICvI",(function(n,i){e(n.exports,"legacyNodeToElementsComponentsNode",(()=>s));var o=t("eQFvP");const s=e=>({parentNode:e.parentNode?s(e.parentNode):null,id:e.id,nodeType:e.nodeType(),pseudoType:e.pseudoType(),shadowRootType:e.shadowRootType(),nodeName:e.nodeName(),nodeNameNicelyCased:e.nodeNameInCorrectCase(),legacyDomNode:e,highlightNode:t=>e.highlight(t),clearHighlight:()=>o.OverlayModel.OverlayModel.hideDOMNodeHighlight(),getAttribute:e.getAttribute.bind(e)})})),t.register("73QJC",(function(n,i){e(n.exports,"SettingChangedEvent",(()=>v)),e(n.exports,"LayoutPane",(()=>E));var o=t("koSS8"),s=t("kpUjp"),r=t("9z2ZV"),l=t("dS5IF"),a=t("5371F"),d=t("9NgNS"),c=t("lzcO5"),h=t("80baR"),p=t("ixFnt");const u={chooseElementOverlayColor:"Choose the overlay color for this element",showElementInTheElementsPanel:"Show element in the Elements panel",grid:"Grid",overlayDisplaySettings:"Overlay display settings",gridOverlays:"Grid overlays",noGridLayoutsFoundOnThisPage:"No grid layouts found on this page",flexbox:"Flexbox",flexboxOverlays:"Flexbox overlays",noFlexboxLayoutsFoundOnThisPage:"No flexbox layouts found on this page",colorPickerOpened:"Color picker opened."},m=p.i18n.registerUIStrings("panels/elements/components/LayoutPane.ts",u),g=p.i18n.getLocalizedString.bind(void 0,m),{render:f,html:y}=l;class v extends Event{static eventName="settingchanged";data;constructor(e,t){super(v.eventName,{}),this.data={setting:e,value:t}}}function b(e){return e.type===o.Settings.SettingType.ENUM}function S(e){return e.type===o.Settings.SettingType.BOOLEAN}class E extends HTMLElement{static litTagName=l.literal`devtools-layout-pane`;#e=this.attachShadow({mode:"open"});#H=[];#xe=[];#we=[];constructor(){super(),this.#e.adoptedStyleSheets=[c.checkboxStyles,d.default,h.default]}set data(e){this.#H=e.settings,this.#xe=e.gridElements,this.#we=e.flexContainerElements,this.#u()}#Te(e){if(!e.target)return;const t=e.target.parentElement;if(!t)throw new Error("<details> element is not found for a <summary> element");switch(e.key){case"ArrowLeft":t.open=!1;break;case"ArrowRight":t.open=!0}}#u(){f(y`
      <details open>
        <summary class="header" @keydown=${this.#Te}>
          ${g(u.grid)}
        </summary>
        <div class="content-section">
          <h3 class="content-section-title">${g(u.overlayDisplaySettings)}</h3>
          <div class="select-settings">
            ${this.#Me().map((e=>this.#Ne(e)))}
          </div>
          <div class="checkbox-settings">
            ${this.#Ie().map((e=>this.#Pe(e)))}
          </div>
        </div>
        ${this.#xe?y`<div class="content-section">
            <h3 class="content-section-title">
              ${this.#xe.length?g(u.gridOverlays):g(u.noGridLayoutsFoundOnThisPage)}
            </h3>
            ${this.#xe.length?y`<div class="elements">
                ${this.#xe.map((e=>this.#Oe(e)))}
              </div>`:""}
          </div>`:""}
      </details>
      ${void 0!==this.#we?y`
        <details open>
          <summary class="header" @keydown=${this.#Te}>
            ${g(u.flexbox)}
          </summary>
          ${this.#we?y`<div class="content-section">
              <h3 class="content-section-title">
                ${this.#we.length?g(u.flexboxOverlays):g(u.noFlexboxLayoutsFoundOnThisPage)}
              </h3>
              ${this.#we.length?y`<div class="elements">
                  ${this.#we.map((e=>this.#Oe(e)))}
                </div>`:""}
            </div>`:""}
        </details>
        `:""}
    `,this.#e,{host:this})}#Me(){return this.#H.filter(b)}#Ie(){return this.#H.filter(S)}#ke(e,t){t.preventDefault(),this.dispatchEvent(new v(e.name,t.target.checked))}#Le(e,t){t.preventDefault(),this.dispatchEvent(new v(e.name,t.target.value))}#De(e,t){t.preventDefault(),e.toggle(t.target.checked)}#Ae(e,t){t.preventDefault(),e.reveal()}#Re(e,t){t.preventDefault(),e.setColor(t.target.value),this.#u()}#Fe(e,t){t.preventDefault(),e.highlight()}#Ue(e,t){t.preventDefault(),e.hideHighlight()}#Oe(e){const t=this.#De.bind(this,e),n=this.#Ae.bind(this,e),i=this.#Re.bind(this,e),o=this.#Fe.bind(this,e),s=this.#Ue.bind(this,e);return y`<div class="element">
      <label data-element="true" class="checkbox-label">
        <input data-input="true" type="checkbox" .checked=${e.enabled} @change=${t} />
        <span class="node-text-container" data-label="true" @mouseenter=${o} @mouseleave=${s}>
          <${a.NodeText.litTagName} .data=${{nodeId:e.domId,nodeTitle:e.name,nodeClasses:e.domClasses}}></${a.NodeText.litTagName}>
        </span>
      </label>
      <label @keyup=${e=>{if("Enter"!==e.key&&" "!==e.key)return;e.target.querySelector("input").click(),r.ARIAUtils.alert(g(u.colorPickerOpened)),e.preventDefault()}} @keydown=${e=>{" "===e.key&&e.preventDefault()}} tabindex="0" title=${g(u.chooseElementOverlayColor)} class="color-picker-label" style="background: ${e.color};">
        <input @change=${i} @input=${i} tabindex="-1" class="color-picker" type="color" value=${e.color} />
      </label>
      <button tabindex="0" @click=${n} title=${g(u.showElementInTheElementsPanel)} class="show-element"></button>
    </div>`}#Pe(e){const t=this.#ke.bind(this,e);return y`<label data-boolean-setting="true" class="checkbox-label" title=${e.title}>
      <input data-input="true" type="checkbox" .checked=${e.value} @change=${t} />
      <span data-label="true">${e.title}</span>
    </label>`}#Ne(e){const t=this.#Le.bind(this,e);return y`<label data-enum-setting="true" class="select-label" title=${e.title}>
      <select class="chrome-select" data-input="true" @change=${t}>
        ${e.options.map((t=>y`<option value=${t.value} .selected=${e.value===t.value}>${t.title}</option>`))}
      </select>
    </label>`}}s.CustomElements.defineComponent("devtools-layout-pane",E)})),t.register("9NgNS",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync('/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n* {\n  box-sizing: border-box;\n  font-size: 12px;\n}\n\n.header {\n  background-color: var(--color-background-elevation-1);\n  border-bottom: var(--legacy-divider-border);\n  line-height: 1.6;\n  overflow: hidden;\n  padding: 0 5px;\n  white-space: nowrap;\n}\n\n.header::marker {\n  color: rgb(110 110 110); /* stylelint-disable-line plugin/use_theme_colors */\n  /* See: crbug.com/1152736 for color variable migration. */\n  font-size: 11px;\n  line-height: 1;\n}\n\n.header:focus {\n  background-color: var(--legacy-focus-bg-color);\n}\n\n.content-section {\n  padding: 16px;\n  border-bottom: var(--legacy-divider-border);\n  overflow-x: hidden;\n}\n\n.content-section-title {\n  font-size: 12px;\n  font-weight: 500;\n  line-height: 1.1;\n  margin: 0;\n  padding: 0;\n}\n\n.checkbox-settings {\n  margin-top: 8px;\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 5px;\n}\n\n.checkbox-label {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  min-width: 40px;\n}\n\n.checkbox-settings .checkbox-label {\n  margin-bottom: 8px;\n}\n\n.checkbox-settings .checkbox-label:last-child {\n  margin-bottom: 0;\n}\n\n.checkbox-label input {\n  margin: 0 6px 0 0;\n  padding: 0;\n  flex: none;\n}\n\n.checkbox-label input:focus {\n  outline: auto 5px -webkit-focus-ring-color;\n}\n\n.checkbox-label > span {\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  overflow: hidden;\n}\n\n.select-settings {\n  margin-top: 16px;\n}\n\n.select-label {\n  display: flex;\n  flex-direction: column;\n}\n\n.select-label span {\n  margin-bottom: 4px;\n}\n\n.elements {\n  margin-top: 12px;\n  color: var(--color-token-tag);\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(min(250px, 100%), 1fr));\n  gap: 8px;\n}\n\n.element {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n}\n\n.show-element {\n  margin: 0 0 0 8px;\n  padding: 0;\n  background: none;\n  border: none;\n  -webkit-mask-image: var(--image-file-ic_show_node_16x16);\n  background-color: #333; /* stylelint-disable-line plugin/use_theme_colors */\n  /* See: crbug.com/1152736 for color variable migration. */\n  width: 16px;\n  height: 16px;\n  display: block;\n  cursor: pointer;\n  flex: none;\n}\n\n.show-element:focus,\n.show-element:hover {\n  background-color: #6e6e6e; /* stylelint-disable-line plugin/use_theme_colors */\n  /* See: crbug.com/1152736 for color variable migration. */\n}\n\n.chrome-select {\n  min-width: 0;\n  max-width: 150px;\n}\n\n:host-context(.-theme-with-dark-background) .show-element {\n  background-color: rgb(204 204 204);\n}\n\n:host-context(.-theme-with-dark-background) .show-element:focus,\n:host-context(.-theme-with-dark-background) .show-element:hover {\n  background-color: #6e6e6e;\n}\n\n.color-picker {\n  opacity: 0%;\n}\n\n.color-picker-label {\n  border: 1px solid rgb(128 128 128 / 60%); /* stylelint-disable-line plugin/use_theme_colors */\n  /* See: crbug.com/1152736 for color variable migration. */\n  cursor: default;\n  display: inline-block;\n  flex: none;\n  height: 10px;\n  margin: 0 0 0 8px;\n  width: 10px;\n  position: relative;\n}\n/* We set dimensions for the invisible input to support quick highlight a11y feature\nthat uses the dimensions to draw an outline around the element. */\n.color-picker-label input[type="color"] {\n  width: 100%;\n  height: 100%;\n  position: absolute;\n}\n\n.color-picker-label:hover,\n.color-picker-label:focus {\n  border: 1px solid var(--legacy-accent-color-hover);\n  transform: scale(1.2);\n}\n\n.node-text-container {\n  line-height: 16px;\n  padding: 0 0.5ex;\n  border-radius: 5px;\n}\n\n.node-text-container:hover {\n  background-color: var(--item-hover-color);\n}\n\n/*# sourceURL=layoutPane.css */\n');var o=i})),t.register("60ifE",(function(e,t){})),t.register("cMoBo",(function(n,i){e(n.exports,"QueriedSizeRequestedEvent",(()=>m)),e(n.exports,"QueryContainer",(()=>g));var o=t("eQFvP"),s=t("kpUjp"),r=t("cY3yZ"),l=t("dS5IF"),a=t("5371F"),d=t("3ivZb");const{render:c,html:h}=l,{PhysicalAxis:p,QueryAxis:u}=o.CSSContainerQuery;class m extends Event{static eventName="queriedsizerequested";constructor(){super(m.eventName,{})}}class g extends HTMLElement{static litTagName=l.literal`devtools-query-container`;#e=this.attachShadow({mode:"open"});#G;#Be;#He;#Ve=!1;#We;set data(e){this.#G=e.queryName,this.#Be=e.container,this.#He=e.onContainerLinkClick,this.#u()}connectedCallback(){this.#e.adoptedStyleSheets=[d.default]}updateContainerQueriedSizeDetails(e){this.#We=e,this.#u()}async#ze(){this.#Be?.highlightNode("container-outline"),this.#Ve=!0,this.dispatchEvent(new m)}#$e(){this.#Be?.clearHighlight(),this.#Ve=!1,this.#u()}#u(){if(!this.#Be)return;let e,t;this.#G||(e=this.#Be.getAttribute("id"),t=this.#Be.getAttribute("class")?.split(/\s+/).filter(Boolean));const n=this.#G||this.#Be.nodeNameNicelyCased;c(h`
      →
      <a href="#"
        draggable=false
        class="container-link"
        @click=${this.#He}
        @mouseenter=${this.#ze}
        @mouseleave=${this.#$e}
      ><${a.NodeText.litTagName}
          data-node-title=${n}
          .data=${{nodeTitle:n,nodeId:e,nodeClasses:t}}></${a.NodeText.litTagName}></a>
      ${this.#Ve?this.#_e():l.nothing}
    `,this.#e,{host:this})}#_e(){if(!this.#We||""===this.#We.queryAxis)return l.nothing;const e="size"===this.#We.queryAxis,t=l.Directives.classMap({"axis-icon":!0,hidden:e,vertical:"Vertical"===this.#We.physicalAxis});return h`
      <span class="queried-size-details">
        (${this.#We.queryAxis}<${r.Icon.Icon.litTagName}
          class=${t} .data=${{iconName:"ic_dimension_single",color:"var(--color-text-primary)"}}></${r.Icon.Icon.litTagName}>)
        ${e&&this.#We.width?"width:":l.nothing}
        ${this.#We.width||l.nothing}
        ${e&&this.#We.height?"height:":l.nothing}
        ${this.#We.height||l.nothing}
      </span>
    `}}s.CustomElements.defineComponent("devtools-query-container",g)})),t.register("3ivZb",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.container-link {\n  display: inline-block;\n  color: var(--color-text-disabled);\n}\n\n.container-link:hover {\n  color: var(--color-link);\n}\n\n.queried-size-details {\n  color: var(--color-text-primary);\n}\n\n.axis-icon {\n  margin-left: 0.4em;\n  width: 1em;\n  height: 1em;\n  vertical-align: text-top;\n}\n\n.axis-icon.hidden {\n  display: none;\n}\n\n.axis-icon.vertical {\n  transform: rotate(90deg);\n}\n\n/*# sourceURL=queryContainer.css */\n");var o=i})),t.register("2p7Mx",(function(n,i){e(n.exports,"PropertySelectedEvent",(()=>f)),e(n.exports,"PropertyDeselectedEvent",(()=>y)),e(n.exports,"StylePropertyEditor",(()=>v)),e(n.exports,"FlexboxEditor",(()=>b)),e(n.exports,"FlexboxEditableProperties",(()=>E)),e(n.exports,"GridEditor",(()=>S)),e(n.exports,"GridEditableProperties",(()=>C));var o=t("ixFnt"),s=t("kpUjp"),r=t("cY3yZ"),l=t("dS5IF"),a=t("aTcgA"),d=t("acCr5");const c={selectButton:"Add {propertyName}: {propertyValue}",deselectButton:"Remove {propertyName}: {propertyValue}"},h=o.i18n.registerUIStrings("panels/elements/components/StylePropertyEditor.ts",c),p=o.i18n.getLocalizedString.bind(void 0,h),{render:u,html:m,Directives:g}=l;class f extends Event{static eventName="propertyselected";data;constructor(e,t){super(f.eventName,{}),this.data={name:e,value:t}}}class y extends Event{static eventName="propertydeselected";data;constructor(e,t){super(y.eventName,{}),this.data={name:e,value:t}}}class v extends HTMLElement{#e=this.attachShadow({mode:"open"});#je=new Map;#qe=new Map;editableProperties=[];constructor(){super()}connectedCallback(){this.#e.adoptedStyleSheets=[a.default]}getEditableProperties(){return this.editableProperties}set data(e){this.#je=e.authoredProperties,this.#qe=e.computedProperties,this.#u()}#u(){u(m`
      <div class="container">
        ${this.editableProperties.map((e=>this.#Ge(e)))}
      </div>
    `,this.#e,{host:this})}#Ge(e){const t=this.#je.get(e.propertyName),n=!t,i=t||this.#qe.get(e.propertyName),o=g.classMap({"property-value":!0,"not-authored":n});return m`<div class="row">
      <div class="property">
        <span class="property-name">${e.propertyName}</span>: <span class=${o}>${i}</span>
      </div>
      <div class="buttons">
        ${e.propertyValues.map((n=>this.#Ke(n,e.propertyName,n===t)))}
      </div>
    </div>`}#Ke(e,t,n=!1){const i=`${t}: ${e}`,o=this.findIcon(i,this.#qe);if(!o)throw new Error(`Icon for ${i} is not found`);const s=`transform: rotate(${o.rotate}deg) scale(${o.scaleX}, ${o.scaleY})`,l=g.classMap({button:!0,selected:n}),a={propertyName:t,propertyValue:e},d=p(n?c.deselectButton:c.selectButton,a);return m`<button title=${d} class=${l} @click=${()=>this.#Qe(t,e,n)}>
       <${r.Icon.Icon.litTagName} style=${s} .data=${{iconName:o.iconName,color:"var(--icon-color)",width:"18px",height:"18px"}}></${r.Icon.Icon.litTagName}>
    </button>`}#Qe(e,t,n){n?this.dispatchEvent(new y(e,t)):this.dispatchEvent(new f(e,t))}findIcon(e,t){throw new Error("Not implemented")}}class b extends v{editableProperties=E;findIcon(e,t){return(0,d.findFlexContainerIcon)(e,t)}}s.CustomElements.defineComponent("devtools-flexbox-editor",b);class S extends v{editableProperties=C;findIcon(e,t){return(0,d.findGridContainerIcon)(e,t)}}s.CustomElements.defineComponent("devtools-grid-editor",S);const E=[{propertyName:"flex-direction",propertyValues:["row","column","row-reverse","column-reverse"]},{propertyName:"flex-wrap",propertyValues:["nowrap","wrap"]},{propertyName:"align-content",propertyValues:["center","flex-start","flex-end","space-around","space-between","stretch"]},{propertyName:"justify-content",propertyValues:["center","flex-start","flex-end","space-between","space-around","space-evenly"]},{propertyName:"align-items",propertyValues:["center","flex-start","flex-end","stretch","baseline"]}],C=[{propertyName:"align-content",propertyValues:["center","space-between","space-around","space-evenly","stretch"]},{propertyName:"justify-content",propertyValues:["center","start","end","space-between","space-around","space-evenly"]},{propertyName:"align-items",propertyValues:["center","start","end","stretch","baseline"]},{propertyName:"justify-items",propertyValues:["center","start","end","stretch"]}]})),t.register("aTcgA",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.container {\n  padding: 12px;\n  min-width: 170px;\n}\n\n.row {\n  padding: 0;\n  color: var(--color-text-primary);\n  padding-bottom: 16px;\n}\n\n.row:last-child {\n  padding-bottom: 0;\n}\n\n.property {\n  padding-bottom: 4px;\n  white-space: nowrap;\n}\n\n.property-name {\n  color: var(--color-syntax-1);\n}\n\n.property-value {\n  color: var(--color-text-primary);\n}\n\n.property-value.not-authored {\n  color: var(--color-text-disabled);\n}\n\n.buttons {\n  display: flex;\n  flex-direction: row;\n}\n\n.buttons > :first-child {\n  border-radius: 3px 0 0 3px;\n}\n\n.buttons > :last-child {\n  border-radius: 0 3px 3px 0;\n}\n\n.button {\n  border: 1px solid var(--color-background-elevation-2);\n  background-color: var(--color-background);\n  width: 24px;\n  height: 24px;\n  min-width: 24px;\n  min-height: 24px;\n  padding: 0;\n  margin: 0;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n}\n\n.button:focus-visible {\n  outline: auto 5px -webkit-focus-ring-color;\n}\n\n.button devtools-icon {\n  --icon-color: var(--color-text-secondary);\n}\n\n.button.selected {\n  background-color: var(--color-background-elevation-1);\n}\n\n.button.selected devtools-icon {\n  --icon-color: var(--color-primary);\n}\n\n/*# sourceURL=stylePropertyEditor.css */\n");var o=i})),t.register("aAkQj",(function(n,i){e(n.exports,"ComputedStyleWidget",(()=>N));var o=t("koSS8"),s=t("ixFnt"),r=t("lz7WY"),l=t("eQFvP"),a=t("fuChX"),d=t("a3yig"),c=t("9z2ZV");t("Px6PZ");var h=t("6JdOX"),p=t("79cvW"),u=t("5JlNB"),m=t("bJ4Bp"),g=t("3LXVO"),f=t("hJQty"),y=t("lyNC2"),v=t("lrOX8"),b=t("3OgVH");const S={filter:"Filter",filterComputedStyles:"Filter Computed Styles",showAll:"Show all",group:"Group",noMatchingProperty:"No matching property",navigateToSelectorSource:"Navigate to selector source",navigateToStyle:"Navigate to style"},E=s.i18n.registerUIStrings("panels/elements/ComputedStyleWidget.ts",S),C=s.i18n.getLocalizedString.bind(void 0,E),x=(e,t,n,i,o)=>{const s=new p.ComputedStyleTrace,r=new(0,b.StylesSidebarPropertyRenderer)(null,e,t.name,t.value);r.setColorHandler(w.bind(null,!0));const l=r.renderValue();l.slot="trace-value",s.appendChild(l);const a=t.ownerStyle.parentRule;if(a){const e=document.createElement("span");e.appendChild(b.StylePropertiesSection.createRuleOriginNode(i,o,a)),e.slot="trace-link",s.appendChild(e)}return s.data={selector:a?a.selectorText():"element.style",active:!n,onNavigateToSource:T.bind(null,t)},s},w=(e,t)=>{const n=new a.ColorSwatch.ColorSwatch;n.renderColor(t,e||o.Color.Format.RGB);const i=document.createElement("span");return i.textContent=t,n.append(i),n.addEventListener(a.ColorSwatch.FormatChangedEvent.eventName,(e=>{const{data:t}=e;i.textContent=t.text})),n},T=(e,t)=>{o.Revealer.reveal(e),t.consume(!0)},M=(e,t)=>{if(e.startsWith("--")!==t.startsWith("--"))return e.startsWith("--")?1:-1;if(e.startsWith("-webkit")!==t.startsWith("-webkit"))return e.startsWith("-webkit")?1:-1;const n=l.CSSMetadata.cssMetadata().canonicalPropertyName(e),i=l.CSSMetadata.cssMetadata().canonicalPropertyName(t);return r.StringUtilities.compare(n,i)};class N extends c.ThrottledWidget.ThrottledWidget{computedStyleModel;showInheritedComputedStylePropertiesSetting;groupComputedStylesSetting;input;filterRegex;noMatchesElement;propertiesOutline;propertyByTreeElement;categoryByTreeElement;expandedProperties;expandedGroups;linkifier;imagePreviewPopover;idleCallbackManager;constructor(){super(!0),this.computedStyleModel=new(0,g.ComputedStyleModel),this.computedStyleModel.addEventListener("ComputedStyleChanged",this.update,this),this.showInheritedComputedStylePropertiesSetting=o.Settings.Settings.instance().createSetting("showInheritedComputedStyleProperties",!1),this.showInheritedComputedStylePropertiesSetting.addChangeListener(this.update.bind(this)),this.groupComputedStylesSetting=o.Settings.Settings.instance().createSetting("groupComputedStyles",!1),this.groupComputedStylesSetting.addChangeListener((()=>{this.update()}));const e=this.contentElement.createChild("div","hbox styles-sidebar-pane-toolbar"),t=e.createChild("div","styles-sidebar-pane-filter-box"),n=b.StylesSidebarPane.createPropertyFilterElement(C(S.filter),e,this.filterComputedStyles.bind(this));c.ARIAUtils.setAccessibleName(n,C(S.filterComputedStyles)),t.appendChild(n),this.input=n,this.filterRegex=null;const i=new c.Toolbar.Toolbar("styles-pane-toolbar",e);i.appendToolbarItem(new c.Toolbar.ToolbarSettingCheckbox(this.showInheritedComputedStylePropertiesSetting,void 0,C(S.showAll))),i.appendToolbarItem(new c.Toolbar.ToolbarSettingCheckbox(this.groupComputedStylesSetting,void 0,C(S.group))),this.noMatchesElement=this.contentElement.createChild("div","gray-info-message"),this.noMatchesElement.textContent=C(S.noMatchingProperty),this.propertiesOutline=new c.TreeOutline.TreeOutlineInShadow,this.propertiesOutline.hideOverflow(),this.propertiesOutline.setShowSelectionOnKeyboardFocus(!0),this.propertiesOutline.setFocusable(!0),this.propertiesOutline.element.classList.add("monospace","computed-properties"),this.propertiesOutline.addEventListener(c.TreeOutline.Events.ElementExpanded,this.onTreeElementToggled,this),this.propertiesOutline.addEventListener(c.TreeOutline.Events.ElementCollapsed,this.onTreeElementToggled,this),this.contentElement.appendChild(this.propertiesOutline.element),this.propertyByTreeElement=new WeakMap,this.categoryByTreeElement=new WeakMap,this.expandedProperties=new Set,this.expandedGroups=new Set(v.DefaultCategoryOrder),this.linkifier=new d.Linkifier.Linkifier(I),this.imagePreviewPopover=new(0,f.ImagePreviewPopover)(this.contentElement,(e=>{const t=e.composedPath()[0];return t instanceof Element?t:null}),(()=>this.computedStyleModel.node()));new(0,y.PlatformFontsWidget)(this.computedStyleModel).show(this.contentElement),this.idleCallbackManager=new(0,b.IdleCallbackManager)}onResize(){const e=this.contentElement.offsetWidth<260;this.propertiesOutline.contentElement.classList.toggle("computed-narrow",e)}showInheritedComputedStyleChanged(){this.update()}update(){this.idleCallbackManager&&this.idleCallbackManager.discard(),this.idleCallbackManager=new(0,b.IdleCallbackManager),super.update()}wasShown(){super.wasShown(),this.registerCSSFiles([u.default]),this.propertiesOutline.registerCSSFiles([m.default])}async doUpdate(){const[e,t]=await Promise.all([this.computedStyleModel.fetchComputedStyle(),this.fetchMatchedCascade()]),n=this.groupComputedStylesSetting.get();this.propertiesOutline.contentElement.classList.toggle("grouped-list",n),this.propertiesOutline.contentElement.classList.toggle("alphabetical-list",!n),n?await this.rebuildGroupedList(e,t):await this.rebuildAlphabeticalList(e,t)}async fetchMatchedCascade(){const e=this.computedStyleModel.node();if(!e||!this.computedStyleModel.cssModel())return null;const t=this.computedStyleModel.cssModel();return t?t.cachedMatchedCascadeForNode(e).then(function(e){return e&&e.node()===this.computedStyleModel.node()?e:null}.bind(this)):null}async rebuildAlphabeticalList(e,t){const n=this.propertiesOutline.element.hasFocus();this.imagePreviewPopover.hide(),this.propertiesOutline.removeChildren(),this.linkifier.reset();const i=this.computedStyleModel.cssModel();if(!e||!t||!i)return void this.noMatchesElement.classList.remove("hidden");const o=[...e.computedStyle.keys()];o.sort(M);const s=e.node,r=this.computePropertyTraces(t),a=this.computeNonInheritedProperties(t),d=this.showInheritedComputedStylePropertiesSetting.get(),c=[];for(const t of o){const n=e.computedStyle.get(t)||"",i=l.CSSMetadata.cssMetadata().canonicalPropertyName(t),o=!a.has(i);(d||!o||P.has(t))&&(!d&&t.startsWith("--")||t!==i&&n===e.computedStyle.get(i)||c.push({propertyName:t,propertyValue:n,isInherited:o}))}this.propertiesOutline.contentElement.classList.add("render-flash");let h=100;for(;c.length>0;){const e=c.splice(0,20);this.idleCallbackManager.schedule((()=>{for(const{propertyName:i,propertyValue:o,isInherited:l}of e){const e=this.buildPropertyTreeElement(r,s,t,i,o,l,n);this.propertiesOutline.appendChild(e)}this.filterAlphabeticalList()}),h),h+=100}await this.idleCallbackManager.awaitDone(),this.propertiesOutline.contentElement.classList.remove("render-flash")}async rebuildGroupedList(e,t){const n=this.propertiesOutline.element.hasFocus();this.imagePreviewPopover.hide(),this.propertiesOutline.removeChildren(),this.linkifier.reset();const i=this.computedStyleModel.cssModel();if(!e||!t||!i)return void this.noMatchesElement.classList.remove("hidden");const o=e.node,s=this.computePropertyTraces(t),r=this.computeNonInheritedProperties(t),a=this.showInheritedComputedStylePropertiesSetting.get(),d=new Map;for(const[i,c]of e.computedStyle){const h=l.CSSMetadata.cssMetadata().canonicalPropertyName(i),p=!r.has(h);if(!a&&p&&!P.has(i))continue;if(!a&&i.startsWith("--"))continue;if(i!==h&&c===e.computedStyle.get(h))continue;const u=(0,v.categorizePropertyName)(i);for(const e of u){const r=this.buildPropertyTreeElement(s,o,t,i,c,p,n);d.has(e)||d.set(e,[]),d.get(e).push(r)}}for(const e of v.DefaultCategoryOrder){const t=d.get(e);if(t&&t.length>0){const n=document.createElement("h1");n.textContent=e;const i=new c.TreeOutline.TreeElement(n);i.listItemElement.classList.add("group-title"),i.toggleOnClick=!0;for(const e of t)i.appendChild(e);this.propertiesOutline.appendChild(i),this.expandedGroups.has(e)&&i.expand(),this.categoryByTreeElement.set(i,e)}}this.filterGroupLists()}onTreeElementToggled(e){const t=e.data,n=this.propertyByTreeElement.get(t);if(n)t.expanded?this.expandedProperties.add(n.name):this.expandedProperties.delete(n.name);else{const e=this.categoryByTreeElement.get(t);e&&(t.expanded?this.expandedGroups.add(e):this.expandedGroups.delete(e))}}buildPropertyTreeElement(e,t,n,i,o,s,r){const l=new c.TreeOutline.TreeElement,a=e.get(i);let d=()=>{};if(a){const e=this.renderPropertyTrace(n,t,l,a);l.setExpandable(!0),l.listItemElement.addEventListener("click",(e=>{l.expanded?l.collapse():l.expand(),e.consume()}),!1),d=T.bind(this,e)}const p=((e,t,n)=>{const i=new h.ComputedStyleProperty,o=new(0,b.StylesSidebarPropertyRenderer)(null,e,t,n);o.setColorHandler(w.bind(null,!1));const s=o.renderName();s.slot="property-name",i.appendChild(s);const r=o.renderValue();return r.slot="property-value",i.appendChild(r),i})(t,i,o);return p.data={traceable:e.has(i),inherited:s,onNavigateToSource:d},l.title=p,this.propertyByTreeElement.set(l,{name:i,value:o}),this.propertiesOutline.selectedTreeElement||l.select(!r),this.expandedProperties.has(i)&&l.expand(),l}renderPropertyTrace(e,t,n,i){let o=null;for(const s of i){const i=e.propertyState(s)===l.CSSMatchedStyles.PropertyState.Overloaded;i||(o=s,n.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this,e,s)));const r=x(t,s,i,e,this.linkifier),a=new c.TreeOutline.TreeElement;a.title=r,a.listItemElement.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this,e,s)),n.appendChild(a)}return o}handleContextMenuEvent(e,t,n){const i=new c.ContextMenu.ContextMenu(n),s=t.ownerStyle.parentRule;if(s){const t=s.styleSheetId?e.cssModel().styleSheetHeaderForId(s.styleSheetId):null;t&&!t.isAnonymousInlineStyleSheet()&&i.defaultSection().appendItem(C(S.navigateToSelectorSource),(()=>{b.StylePropertiesSection.tryNavigateToRuleLocation(e,s)}))}i.defaultSection().appendItem(C(S.navigateToStyle),(()=>o.Revealer.reveal(t))),i.show()}computePropertyTraces(e){const t=new Map;for(const n of e.nodeStyles()){const i=n.allProperties();for(const n of i)n.activeInStyle()&&e.propertyState(n)&&(t.has(n.name)||t.set(n.name,[]),t.get(n.name).push(n))}return t}computeNonInheritedProperties(e){const t=new Set;for(const n of e.nodeStyles())for(const i of n.allProperties())e.propertyState(i)&&t.add(l.CSSMetadata.cssMetadata().canonicalPropertyName(i.name));return t}filterComputedStyles(e){this.filterRegex=e,this.groupComputedStylesSetting.get()?this.filterGroupLists():this.filterAlphabeticalList()}filterAlphabeticalList(){const e=this.filterRegex,t=this.propertiesOutline.rootElement().children();let n=!1;for(const i of t){const t=this.propertyByTreeElement.get(i);if(!t)continue;const o=!e||e.test(t.name)||e.test(t.value);i.hidden=!o,n=n||o}this.noMatchesElement.classList.toggle("hidden",Boolean(n))}filterGroupLists(){const e=this.filterRegex,t=this.propertiesOutline.rootElement().children();let n=!1,i=!1;for(const o of t){let t=!1;const s=o.children();for(const i of s){const o=this.propertyByTreeElement.get(i);if(!o)continue;const s=!e||e.test(o.name)||e.test(o.value);i.hidden=!s,n=n||s,t=t||s}o.hidden=!t,o.listItemElement.classList.toggle("first-group",t&&!i),i=i||t}this.noMatchesElement.classList.toggle("hidden",n)}}const I=30,P=new Set(["display","height","width"])})),t.register("5JlNB",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright (c) 2015 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.computed-properties {\n  user-select: text;\n  flex-shrink: 0;\n}\n\n.styles-sidebar-pane-toolbar {\n  border-bottom: 1px solid var(--color-details-hairline-light);\n  flex-shrink: 0;\n}\n\n.styles-sidebar-pane-filter-box {\n  flex: auto;\n  display: flex;\n}\n\n.styles-sidebar-pane-filter-box > input {\n  outline: none !important; /* stylelint-disable-line declaration-no-important */\n  border: none;\n  width: 100%;\n  background: var(--color-background);\n  padding-left: 4px;\n  margin: 3px;\n}\n\n.styles-sidebar-pane-filter-box > input:focus,\n.styles-sidebar-pane-filter-box > input:not(:placeholder-shown) {\n  box-shadow: var(--legacy-focus-ring-active-shadow);\n}\n\n.styles-sidebar-pane-filter-box > input::placeholder {\n  color: var(--color-text-disabled);\n}\n\n@media (forced-colors: active) {\n  .styles-sidebar-pane-filter-box > input {\n    border: 1px solid ButtonText;\n  }\n}\n\n/*# sourceURL=computedStyleSidebarPane.css */\n");var o=i})),t.register("bJ4Bp",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright (c) 2017 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.alphabetical-list.render-flash {\n  min-height: 100vh;\n}\n\n.tree-outline,\n.tree-outline ol {\n  padding-left: 0;\n}\n\n.tree-outline li:hover {\n  background-color: var(--legacy-focus-bg-color);\n  cursor: text;\n}\n\n.tree-outline li::before {\n  margin: 0 -1px 0 4px;\n}\n\n.group-title {\n  /* tree-outline li::before is set to be 16px wide */\n  padding-right: 16px;\n}\n\n.tree-outline li.group-title:hover {\n  background-color: transparent;\n}\n\n.group-title > h1 {\n  margin: 1px 0 0;\n  padding: 1em 0;\n  width: 100%;\n  cursor: text;\n  color: var(--color-text-secondary);\n  font-size: 11px;\n  font-weight: 400;\n}\n\n.group-title:not(.first-group) > h1 {\n  border-top: 1px solid var(--color-details-hairline);\n}\n\n.group-title + ol.children {\n  margin-bottom: 1em;\n}\n\n@media (forced-colors: active) {\n  :host-context(.monospace.computed-properties) .tree-outline li:hover {\n    forced-color-adjust: none;\n    background-color: Highlight;\n  }\n\n  :host-context(.monospace.computed-properties) .tree-outline:not(.hide-selection-when-blurred) li.parent:hover.selected::before,\n  :host-context(.monospace.computed-properties) .tree-outline-disclosure li.parent:hover::before {\n    background-color: HighlightText;\n  }\n\n  :host-context(.monospace.computed-properties) .tree-outline li:hover * {\n    color: HighlightText;\n  }\n}\n\n/*# sourceURL=computedStyleWidgetTree.css */\n");var o=i})),t.register("3LXVO",(function(n,i){e(n.exports,"ComputedStyleModel",(()=>l)),e(n.exports,"ComputedStyle",(()=>a));var o=t("koSS8"),s=t("eQFvP"),r=t("9z2ZV");class l extends o.ObjectWrapper.ObjectWrapper{nodeInternal;cssModelInternal;eventListeners;frameResizedTimer;computedStylePromise;constructor(){super(),this.nodeInternal=r.Context.Context.instance().flavor(s.DOMModel.DOMNode),this.cssModelInternal=null,this.eventListeners=[],r.Context.Context.instance().addFlavorChangeListener(s.DOMModel.DOMNode,this.onNodeChanged,this)}node(){return this.nodeInternal}cssModel(){return this.cssModelInternal&&this.cssModelInternal.isEnabled()?this.cssModelInternal:null}onNodeChanged(e){this.nodeInternal=e.data,this.updateModel(this.nodeInternal?this.nodeInternal.domModel().cssModel():null),this.onComputedStyleChanged(null)}updateModel(e){if(this.cssModelInternal===e)return;o.EventTarget.removeEventListeners(this.eventListeners),this.cssModelInternal=e;const t=e?e.domModel():null,n=e?e.target().model(s.ResourceTreeModel.ResourceTreeModel):null;e&&t&&n&&(this.eventListeners=[e.addEventListener(s.CSSModel.Events.StyleSheetAdded,this.onComputedStyleChanged,this),e.addEventListener(s.CSSModel.Events.StyleSheetRemoved,this.onComputedStyleChanged,this),e.addEventListener(s.CSSModel.Events.StyleSheetChanged,this.onComputedStyleChanged,this),e.addEventListener(s.CSSModel.Events.FontsUpdated,this.onComputedStyleChanged,this),e.addEventListener(s.CSSModel.Events.MediaQueryResultChanged,this.onComputedStyleChanged,this),e.addEventListener(s.CSSModel.Events.PseudoStateForced,this.onComputedStyleChanged,this),e.addEventListener(s.CSSModel.Events.ModelWasEnabled,this.onComputedStyleChanged,this),t.addEventListener(s.DOMModel.Events.DOMMutated,this.onDOMModelChanged,this),n.addEventListener(s.ResourceTreeModel.Events.FrameResized,this.onFrameResized,this)])}onComputedStyleChanged(e){delete this.computedStylePromise,this.dispatchEventToListeners("ComputedStyleChanged",e?.data??null)}onDOMModelChanged(e){const t=e.data;this.nodeInternal&&(this.nodeInternal===t||t.parentNode===this.nodeInternal.parentNode||t.isAncestor(this.nodeInternal))&&this.onComputedStyleChanged(null)}onFrameResized(){this.frameResizedTimer&&clearTimeout(this.frameResizedTimer),this.frameResizedTimer=window.setTimeout(function(){this.onComputedStyleChanged(null),delete this.frameResizedTimer}.bind(this),100)}elementNode(){const e=this.node();return e?e.enclosingElementOrSelf():null}async fetchComputedStyle(){const e=this.elementNode(),t=this.cssModel();if(!e||!t)return null;const n=e.id;return n?(this.computedStylePromise||(this.computedStylePromise=t.getComputedStyle(n).then(function(e,t){return e===this.elementNode()&&t?new a(e,t):null}.bind(this,e))),this.computedStylePromise):null}}class a{node;computedStyle;constructor(e,t){this.node=e,this.computedStyle=t}}})),t.register("hJQty",(function(n,i){e(n.exports,"ImagePreviewPopover",(()=>r));var o=t("a3yig"),s=t("9z2ZV");class r{getLinkElement;getDOMNode;popover;constructor(e,t,n){this.getLinkElement=t,this.getDOMNode=n,this.popover=new s.PopoverHelper.PopoverHelper(e,this.handleRequest.bind(this)),this.popover.setHasPadding(!0),this.popover.setTimeout(0,100)}handleRequest(e){const t=this.getLinkElement(e);if(!t)return null;const n=l.get(t);return n?{box:t.boxInWindow(),hide:void 0,show:async e=>{const i=this.getDOMNode(t);if(!i)return!1;const s=await o.ImagePreview.ImagePreview.loadDimensionsForNode(i),r=await o.ImagePreview.ImagePreview.build(i.domModel().target(),n,!0,{imageAltText:void 0,precomputedFeatures:s});return r&&e.contentElement.appendChild(r),Boolean(r)}}:null}hide(){this.popover.hidePopover()}static setImageUrl(e,t){return l.set(e,t),e}static getImageURL(e){return l.get(e)}}const l=new WeakMap})),t.register("lyNC2",(function(n,i){e(n.exports,"PlatformFontsWidget",(()=>c));var o=t("ixFnt"),s=t("hQOAT"),r=t("9z2ZV");const l={renderedFonts:"Rendered Fonts",networkResource:"Network resource",localFile:"Local file",dGlyphs:"{n, plural, =1 {(# glyph)} other {(# glyphs)}}"},a=o.i18n.registerUIStrings("panels/elements/PlatformFontsWidget.ts",l),d=o.i18n.getLocalizedString.bind(void 0,a);class c extends r.ThrottledWidget.ThrottledWidget{sharedModel;sectionTitle;fontStatsSection;constructor(e){super(!0),this.sharedModel=e,this.sharedModel.addEventListener("ComputedStyleChanged",this.update,this),this.sectionTitle=document.createElement("div"),this.sectionTitle.classList.add("title"),this.contentElement.classList.add("platform-fonts"),this.contentElement.appendChild(this.sectionTitle),this.sectionTitle.textContent=d(l.renderedFonts),this.fontStatsSection=this.contentElement.createChild("div","stats-section")}doUpdate(){const e=this.sharedModel.cssModel(),t=this.sharedModel.node();return t&&e?e.getPlatformFonts(t.id).then(this.refreshUI.bind(this,t)):Promise.resolve()}refreshUI(e,t){if(this.sharedModel.node()!==e)return;this.fontStatsSection.removeChildren();const n=!t||!t.length;if(this.sectionTitle.classList.toggle("hidden",n),!n&&t){t.sort((function(e,t){return t.glyphCount-e.glyphCount}));for(let e=0;e<t.length;++e){const n=this.fontStatsSection.createChild("div","font-stats-item");n.createChild("span","font-name").textContent=t[e].familyName;n.createChild("span","font-delimeter").textContent="—";n.createChild("span").textContent=t[e].isCustomFont?d(l.networkResource):d(l.localFile);const i=n.createChild("span","font-usage"),o=t[e].glyphCount;i.textContent=d(l.dGlyphs,{n:o})}}}wasShown(){super.wasShown(),this.registerCSSFiles([s.default])}}})),t.register("hQOAT",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync("/**\n * Copyright 2016 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  user-select: text;\n}\n\n.platform-fonts {\n  flex-shrink: 0;\n}\n\n.font-name {\n  font-weight: bold;\n}\n\n.font-usage {\n  color: var(--color-text-secondary);\n  padding-left: 3px;\n}\n\n.title {\n  padding: 0 5px;\n  border-top: 1px solid;\n  border-bottom: 1px solid;\n  border-color: var(--color-background-elevation-2);\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  height: 24px;\n  background-color: var(--color-background-elevation-0);\n  display: flex;\n  align-items: center;\n}\n\n.stats-section {\n  margin: 5px 0;\n}\n\n.font-stats-item {\n  padding-left: 1em;\n}\n\n.font-stats-item .font-delimeter {\n  margin: 0 1ex;\n}\n\n/*# sourceURL=platformFontsWidget.css */\n");var o=i})),t.register("lrOX8",(function(n,i){e(n.exports,"DefaultCategoryOrder",(()=>s)),e(n.exports,"categorizePropertyName",(()=>d));var o=t("eQFvP");const s=["Layout","Text","Appearance","Animation","CSS Variables","Grid","Flex","Table","Generated Content","Other"],r=new Map([["Layout",["display","margin","padding","height","width","position","top","right","bottom","left","z-index","float","clear","overflow","resize","clip","visibility","box-sizing","align-content","align-items","align-self","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","justify-content","order","inline-size","block-size","min-inline-size","min-block-size","max-inline-size","max-block-size","min-width","max-width","min-height","max-height"]],["Text",["font","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","font-smoothing","direction","tab-size","text-align","text-align-last","text-decoration","text-decoration-color","text-decoration-line","text-decoration-style","text-indent","text-justify","text-overflow","text-shadow","text-transform","text-size-adjust","line-height","vertical-align","letter-spacing","word-spacing","white-space","word-break","word-wrap"]],["Appearance",["color","outline","outline-color","outline-offset","outline-style","Outline-width","border","border-image","background","cursor","box-shadow","≈","tap-highlight-color"]],["Animation",["animation","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","transition","transition-delay","transition-duration","transition-property","transition-timing-function"]],["Grid",["grid","grid-column","grid-row","order","place-items","place-content","place-self"]],["Flex",["flex","order","place-items","place-content","place-self"]],["Table",["border-collapse","border-spacing","caption-side","empty-cells","table-layout"]],["Generated Content",["content","quotes","counter-reset","counter-increment"]]]),l=new Map;for(const[e,t]of r)for(const n of t){l.has(n)||l.set(n,[]);l.get(n).push(e)}const a=e=>l.has(e)?l.get(e):e.startsWith("--")?["CSS Variables"]:[],d=e=>{const t=o.CSSMetadata.cssMetadata(),n=t.canonicalPropertyName(e),i=a(n);if(i.length>0)return i;const s=t.getShorthands(n);if(s)for(const e of s){const t=a(e);if(t.length>0)return t}return["Other"]}})),t.register("3OgVH",(function(n,i){e(n.exports,"StylesSidebarPane",(()=>z)),e(n.exports,"BlankStylePropertiesSection",(()=>Q)),e(n.exports,"IdleCallbackManager",(()=>G)),e(n.exports,"SectionBlock",(()=>q)),e(n.exports,"StylePropertiesSection",(()=>K)),e(n.exports,"KeyframePropertiesSection",(()=>X)),e(n.exports,"formatCSSChangesFromDiff",(()=>$)),e(n.exports,"escapeUrlAsCssComment",(()=>ee)),e(n.exports,"quoteFamilyName",(()=>Y)),e(n.exports,"CSSPropertyPrompt",(()=>J)),e(n.exports,"unescapeCssString",(()=>Z)),e(n.exports,"StylesSidebarPropertyRenderer",(()=>te)),e(n.exports,"ButtonProvider",(()=>ie));var o=t("koSS8"),s=t("e7bLS"),r=t("ixFnt"),l=t("lz7WY"),a=t("9X2mn"),d=t("eQFvP"),c=t("fMswD"),h=t("kaczT"),p=t("7f6zc"),u=t("4gsXY"),m=t("5Lynf"),g=t("cWO9r"),f=t("cY3yZ"),y=t("fuChX"),v=t("a3yig"),b=t("9z2ZV"),S=t("hqdDa");t("Px6PZ");var E=t("3g5FT"),C=t("cMoBo"),x=t("6ICvI"),w=t("acCr5"),T=t("3LXVO"),M=t("h7UuJ"),N=t("1r4or"),I=t("eVQ8w"),P=t("hJQty"),O=t("3yaAT"),k=t("41s9g"),L=t("YxxPp"),D=t("LE9n4"),A=t("8V7BL"),R=t("1DdG1");const F={noMatchingSelectorOrStyle:"No matching selector or style",invalidPropertyValue:"Invalid property value",unknownPropertyName:"Unknown property name",filter:"Filter",filterStyles:"Filter Styles",pseudoSElement:"Pseudo ::{PH1} element",inheritedFroms:"Inherited from ",insertStyleRuleBelow:"Insert Style Rule Below",constructedStylesheet:"constructed stylesheet",userAgentStylesheet:"user agent stylesheet",injectedStylesheet:"injected stylesheet",viaInspector:"via inspector",styleAttribute:"`style` attribute",sattributesStyle:"{PH1}[Attributes Style]",showAllPropertiesSMore:"Show All Properties ({PH1} more)",copySelector:"Copy `selector`",copyRule:"Copy rule",copyAllDeclarations:"Copy all declarations",incrementdecrementWithMousewheelOne:"Increment/decrement with mousewheel or up/down keys. {PH1}: R ±1, Shift: G ±1, Alt: B ±1",incrementdecrementWithMousewheelHundred:"Increment/decrement with mousewheel or up/down keys. {PH1}: ±100, Shift: ±10, Alt: ±0.1",invalidString:"{PH1}, property name: {PH2}, property value: {PH3}",newStyleRule:"New Style Rule",cssPropertyName:"`CSS` property name: {PH1}",cssPropertyValue:"`CSS` property value: {PH1}",cssSelector:"`CSS` selector",copyAllCSSChanges:"Copy all the CSS changes",copiedToClipboard:"Copied to clipboard",layer:"Layer",clickToRevealLayer:"Click to reveal layer in layer tree"},U=r.i18n.registerUIStrings("panels/elements/StylesSidebarPane.ts",F),B=r.i18n.getLocalizedString.bind(void 0,U),H=[{mode:"padding",properties:["padding"]},{mode:"border",properties:["border"]},{mode:"margin",properties:["margin"]},{mode:"gap",properties:["gap","grid-gap"]},{mode:"column-gap",properties:["column-gap","grid-column-gap"]},{mode:"row-gap",properties:["row-gap","grid-row-gap"]},{mode:"grid-template-columns",properties:["grid-template-columns"]},{mode:"grid-template-rows",properties:["grid-template-rows"]},{mode:"grid-template-areas",properties:["grid-areas"]},{mode:"justify-content",properties:["justify-content"]},{mode:"align-content",properties:["align-content"]},{mode:"align-items",properties:["align-items"]},{mode:"flexibility",properties:["flex","flex-basis","flex-grow","flex-shrink"]}];let V;const W="<style>";class z extends(o.ObjectWrapper.eventMixin(I.ElementsSidebarPane)){currentToolbarPane;animatedToolbarPane;pendingWidget;pendingWidgetToggle;toolbar;toolbarPaneElement;noMatchesElement;sectionsContainer;sectionByElement;swatchPopoverHelperInternal;linkifier;decorator;lastRevealedProperty;userOperation;isEditingStyle;filterRegexInternal;isActivePropertyHighlighted;initialUpdateCompleted;hasMatchedStyles;sectionBlocks;idleCallbackManager;needsForceUpdate;resizeThrottler;imagePreviewPopover;activeCSSAngle;#Xe=new Map;static instance(){return V||(V=new z),V}constructor(){super(!0),this.setMinimumSize(96,26),this.registerCSSFiles([D.default]),o.Settings.Settings.instance().moduleSetting("colorFormat").addChangeListener(this.update.bind(this)),o.Settings.Settings.instance().moduleSetting("textEditorIndent").addChangeListener(this.update.bind(this)),this.currentToolbarPane=null,this.animatedToolbarPane=null,this.pendingWidget=null,this.pendingWidgetToggle=null,this.toolbar=null,this.toolbarPaneElement=this.createStylesSidebarToolbar(),this.computedStyleModelInternal=new(0,T.ComputedStyleModel),this.noMatchesElement=this.contentElement.createChild("div","gray-info-message hidden"),this.noMatchesElement.textContent=B(F.noMatchingSelectorOrStyle),this.sectionsContainer=this.contentElement.createChild("div"),b.ARIAUtils.markAsList(this.sectionsContainer),this.sectionsContainer.addEventListener("keydown",this.sectionsContainerKeyDown.bind(this),!1),this.sectionsContainer.addEventListener("focusin",this.sectionsContainerFocusChanged.bind(this),!1),this.sectionsContainer.addEventListener("focusout",this.sectionsContainerFocusChanged.bind(this),!1),this.sectionByElement=new WeakMap,this.swatchPopoverHelperInternal=new y.SwatchPopoverHelper.SwatchPopoverHelper,this.swatchPopoverHelperInternal.addEventListener(y.SwatchPopoverHelper.Events.WillShowPopover,this.hideAllPopovers,this),this.linkifier=new v.Linkifier.Linkifier(j,!0),this.decorator=new(0,k.StylePropertyHighlighter)(this),this.lastRevealedProperty=null,this.userOperation=!1,this.isEditingStyle=!1,this.filterRegexInternal=null,this.isActivePropertyHighlighted=!1,this.initialUpdateCompleted=!1,this.hasMatchedStyles=!1,this.contentElement.classList.add("styles-pane"),this.sectionBlocks=[],this.idleCallbackManager=null,this.needsForceUpdate=!1,V=this,b.Context.Context.instance().addFlavorChangeListener(d.DOMModel.DOMNode,this.forceUpdate,this),this.contentElement.addEventListener("copy",this.clipboardCopy.bind(this)),this.resizeThrottler=new o.Throttler.Throttler(100),this.imagePreviewPopover=new(0,P.ImagePreviewPopover)(this.contentElement,(e=>{const t=e.composedPath()[0];return t instanceof Element?t:null}),(()=>this.node())),this.activeCSSAngle=null}swatchPopoverHelper(){return this.swatchPopoverHelperInternal}setUserOperation(e){this.userOperation=e}static createExclamationMark(e,t){const n=document.createElement("span",{is:"dt-icon-label"});let i;n.className="exclamation-mark",z.ignoreErrorsForProperty(e)||(n.type="smallicon-warning"),t?(b.Tooltip.Tooltip.install(n,t),i=t):(i=d.CSSMetadata.cssMetadata().isCSSPropertyName(e.name)?B(F.invalidPropertyValue):B(F.unknownPropertyName),b.Tooltip.Tooltip.install(n,i));const o=B(F.invalidString,{PH1:i,PH2:e.name,PH3:e.value});return e.setDisplayedStringForInvalidProperty(o),n}static ignoreErrorsForProperty(e){function t(e){return!e.startsWith("-webkit-")&&/^[-_][\w\d]+-\w/.test(e)}const n=e.name.toLowerCase();if("_"===n.charAt(0))return!0;if("filter"===n)return!0;if(n.startsWith("scrollbar-"))return!0;if(t(n))return!0;const i=e.value.toLowerCase();return!!i.endsWith("\\9")||!!t(i)}static createPropertyFilterElement(e,t,n){const i=document.createElement("input");function o(){const e=i.value?new RegExp(l.StringUtilities.escapeForRegExp(i.value),"i"):null;n(e)}return i.type="search",i.classList.add("custom-search-input"),i.placeholder=e,i.addEventListener("input",o,!1),i.addEventListener("keydown",(function(e){const t=e;t.key===l.KeyboardUtilities.ESCAPE_KEY&&i.value&&(t.consume(!0),i.value="",o())}),!1),i}static formatLeadingProperties(e){const t=e.headerText(),n=o.Settings.Settings.instance().moduleSetting("textEditorIndent").get(),i=e.style(),s=[];for(const e of i.leadingProperties())e.disabled?s.push(`${n}/* ${e.name}: ${e.value}; */`):s.push(`${n}${e.name}: ${e.value};`);const r=s.join("\n");return{allDeclarationText:r,ruleText:`${t} {\n${r}\n}`}}revealProperty(e){this.decorator.highlightProperty(e),this.lastRevealedProperty=e,this.update()}jumpToProperty(e){this.decorator.findAndHighlightPropertyName(e)}forceUpdate(){this.needsForceUpdate=!0,this.swatchPopoverHelperInternal.hide(),this.resetCache(),this.update()}sectionsContainerKeyDown(e){const t=this.sectionsContainer.ownerDocument.deepActiveElement();if(!t)return;const n=this.sectionByElement.get(t);if(!n)return;let i=null,o=!1;switch(e.key){case"ArrowUp":case"ArrowLeft":i=n.previousSibling()||n.lastSibling(),o=!1;break;case"ArrowDown":case"ArrowRight":i=n.nextSibling()||n.firstSibling(),o=!0;break;case"Home":i=n.firstSibling(),o=!0;break;case"End":i=n.lastSibling(),o=!1}i&&this.filterRegexInternal&&(i=i.findCurrentOrNextVisible(o)),i&&(i.element.focus(),e.consume(!0))}sectionsContainerFocusChanged(){this.resetFocus()}resetFocus(){if(this.noMatchesElement.classList.contains("hidden")&&this.sectionBlocks[0]&&this.sectionBlocks[0].sections[0]){const e=this.sectionBlocks[0].sections[0].findCurrentOrNextVisible(!0);e&&(e.element.tabIndex=this.sectionsContainer.hasFocus()?-1:0)}}onAddButtonLongClick(e){const t=this.cssModel();if(!t)return;const n=t.styleSheetHeaders().filter((function(e){return!e.isViaInspector()&&!e.isInline&&Boolean(e.resourceURL())})),i=[];for(let e=0;e<n.length;++e){const t=n[e],o=this.createNewRuleInStyleSheet.bind(this,t);i.push({text:c.ResourceUtils.displayNameForURL(t.resourceURL()),handler:o})}i.sort((function(e,t){return l.StringUtilities.naturalOrderComparator(e.text,t.text)}));const o=new b.ContextMenu.ContextMenu(e);for(let e=0;e<i.length;++e){const t=i[e];o.defaultSection().appendItem(t.text,t.handler)}o.footerSection().appendItem("inspector-stylesheet",this.createNewRuleInViaInspectorStyleSheet.bind(this)),o.show()}onFilterChanged(e){this.filterRegexInternal=e,this.updateFilter(),this.resetFocus()}refreshUpdate(e,t){if(t)for(const e of this.allSections())e instanceof Q&&e.isBlank||e.updateVarFunctions(t);if(this.isEditingStyle)return;const n=this.node();if(n){for(const t of this.allSections())t instanceof Q&&t.isBlank||t.update(t===e);this.filterRegexInternal&&this.updateFilter(),this.swatchPopoverHelper().reposition(),this.nodeStylesUpdatedForTest(n,!1)}}async doUpdate(){this.initialUpdateCompleted||window.setTimeout((()=>{this.initialUpdateCompleted||this.sectionsContainer.createChild("span","spinner")}),200);const e=await this.fetchMatchedCascade();await this.innerRebuildUpdate(e),this.initialUpdateCompleted||(this.initialUpdateCompleted=!0,a.Runtime.experiments.isEnabled(a.Runtime.ExperimentName.STYLES_PANE_CSS_CHANGES)&&this.appendToolbarItem(this.createCopyAllChangesButton()),this.dispatchEventToListeners("InitialUpdateCompleted")),this.dispatchEventToListeners("StylesUpdateCompleted",{hasMatchedStyles:this.hasMatchedStyles})}onResize(){this.resizeThrottler.schedule(this.innerResize.bind(this))}innerResize(){const e=this.contentElement.getBoundingClientRect().width+"px";return this.allSections().forEach((t=>{t.propertiesTreeOutline.element.style.width=e})),Promise.resolve()}resetCache(){const e=this.cssModel();e&&e.discardCachedMatchedCascade()}fetchMatchedCascade(){const e=this.node();if(!e||!this.cssModel())return Promise.resolve(null);const t=this.cssModel();return t?t.cachedMatchedCascadeForNode(e).then(function(e){return e&&e.node()===this.node()?e:null}.bind(this)):Promise.resolve(null)}setEditingStyle(e,t){this.isEditingStyle!==e&&(this.contentElement.classList.toggle("is-editing-style",e),this.isEditingStyle=e,this.setActiveProperty(null))}setActiveProperty(e){if(this.isActivePropertyHighlighted&&d.OverlayModel.OverlayModel.hideDOMNodeHighlight(),this.isActivePropertyHighlighted=!1,!this.node())return;if(!e||e.overloaded()||e.inherited())return;const t=e.property.ownerStyle.parentRule,n=t instanceof d.CSSRule.CSSStyleRule?t.selectorText():void 0;for(const{properties:t,mode:i}of H){if(!t.includes(e.name))continue;const o=this.node();if(o){o.domModel().overlayModel().highlightInOverlay({node:this.node(),selectorList:n},i),this.isActivePropertyHighlighted=!0;break}}}onCSSModelChanged(e){const t=e?.data&&"edit"in e.data?e.data.edit:null;if(t)for(const e of this.allSections())e.styleSheetEdited(t);else this.userOperation||this.isEditingStyle||(this.resetCache(),this.update())}focusedSectionIndex(){let e=0;for(const t of this.sectionBlocks)for(const n of t.sections){if(n.element.hasFocus())return e;e++}return-1}continueEditingElement(e,t){const n=this.allSections()[e];if(n){const e=n.closestPropertyForEditing(t);if(!e)return void n.element.focus();e.startEditing()}}async innerRebuildUpdate(e){if(this.needsForceUpdate)this.needsForceUpdate=!1;else if(this.isEditingStyle||this.userOperation)return;const t=this.focusedSectionIndex();this.linkifier.reset();const n=this.sectionBlocks.map((e=>e.sections)).flat();this.sectionBlocks=[];const i=this.node();if(this.hasMatchedStyles=null!==e&&null!==i,!this.hasMatchedStyles)return this.sectionsContainer.removeChildren(),void this.noMatchesElement.classList.remove("hidden");this.sectionBlocks=await this.rebuildSectionsForMatchedStyleRules(e);const o=this.sectionBlocks.map((e=>e.sections)).flat(),r=O.StyleEditorWidget.instance(),l=r.getSection();if(l){r.unbindContext();for(const[e,t]of n.entries())l===t&&e<o.length&&r.bindContext(this,o[e])}this.sectionsContainer.removeChildren();const a=document.createDocumentFragment();let d=0,c=null;for(const e of this.sectionBlocks){const n=e.titleElement();n&&a.appendChild(n);for(const n of e.sections)a.appendChild(n.element),d===t&&(c=n.element),d++}this.sectionsContainer.appendChild(a),c&&c.focus(),t>=d&&this.sectionBlocks[0].sections[0].element.focus(),this.sectionsContainerFocusChanged(),this.filterRegexInternal?this.updateFilter():this.noMatchesElement.classList.toggle("hidden",this.sectionBlocks.length>0),this.nodeStylesUpdatedForTest(i,!0),this.lastRevealedProperty&&(this.decorator.highlightProperty(this.lastRevealedProperty),this.lastRevealedProperty=null),this.swatchPopoverHelper().reposition(),s.userMetrics.panelLoaded("elements","DevTools.Launch.Elements"),this.dispatchEventToListeners("StylesUpdateCompleted",{hasMatchedStyles:!1})}nodeStylesUpdatedForTest(e,t){}async rebuildSectionsForMatchedStyleRules(e){this.idleCallbackManager&&this.idleCallbackManager.discard(),this.idleCallbackManager=new G;const t=[new q(null)];let n=0,i=null,o=null,s=!1;const r=a.Runtime.experiments.isEnabled(a.Runtime.ExperimentName.CSS_LAYERS),l=e=>{if(!r)return;const n=e.parentRule;if(n instanceof d.CSSRule.CSSStyleRule){const e=n.layers;if((e.length||o)&&o!==e){const i=q.createLayerBlock(n);t.push(i),s=!0,o=e}}};R.ButtonProvider.instance().item().setVisible(!1);const c=new Set;for(const o of e.nodeStyles()){if(a.Runtime.experiments.isEnabled(a.Runtime.ExperimentName.STYLES_PANE_CSS_CHANGES)&&o.parentRule){const e=o.parentRule.resourceURL();e&&!c.has(e)&&(await this.trackURLForChanges(e),c.add(e))}const s=e.isInherited(o)?e.nodeForStyle(o):null;if(s&&s!==i){i=s;const e=await q.createInheritedNodeBlock(i);t.push(e)}l(o);const r=t[t.length-1];r&&this.idleCallbackManager.schedule((()=>{const t=new K(this,e,o,n);n++,r.sections.push(t)}))}let h=[];const p=e.pseudoTypes();p.delete("before")&&h.push("before"),h=h.concat([...p].sort());for(const i of h){t.push(q.createPseudoTypeBlock(i)),o=null;for(const o of e.pseudoStyles(i)){l(o);const i=t[t.length-1];this.idleCallbackManager.schedule((()=>{const t=new K(this,e,o,n);n++,i.sections.push(t)}))}}for(const i of e.keyframes()){const o=q.createKeyframesBlock(i.name().text);for(const t of i.keyframes())this.idleCallbackManager.schedule((()=>{o.sections.push(new X(this,e,t.style,n)),n++}));t.push(o)}return r&&(s?R.ButtonProvider.instance().item().setVisible(!0):R.LayersWidget.instance().isShowing()&&N.ElementsPanel.instance().showToolbarPane(null,R.ButtonProvider.instance().item())),await this.idleCallbackManager.awaitDone(),t}async createNewRuleInViaInspectorStyleSheet(){const e=this.cssModel(),t=this.node();if(!e||!t)return;this.setUserOperation(!0);const n=await e.requestViaInspectorStylesheet(t);this.setUserOperation(!1),await this.createNewRuleInStyleSheet(n)}async createNewRuleInStyleSheet(e){if(!e)return;const t=((await e.requestContent()).content||"").split("\n"),n=p.TextRange.TextRange.createFromLocation(t.length-1,t[t.length-1].length);this.sectionBlocks&&this.sectionBlocks.length>0&&this.addBlankSection(this.sectionBlocks[0].sections[0],e.id,n)}addBlankSection(e,t,n){const i=this.node(),o=new Q(this,e.matchedStyles,i?i.simpleSelector():"",t,n,e.style(),0);this.sectionsContainer.insertBefore(o.element,e.element.nextSibling);for(const t of this.sectionBlocks){const n=t.sections.indexOf(e);-1!==n&&(t.sections.splice(n+1,0,o),o.startEditingSelector())}let s=0;for(const e of this.sectionBlocks)for(const t of e.sections)t.setSectionIdx(s),s++}removeSection(e){for(const t of this.sectionBlocks){const n=t.sections.indexOf(e);-1!==n&&(t.sections.splice(n,1),e.element.remove())}}filterRegex(){return this.filterRegexInternal}updateFilter(){let e=!1;for(const t of this.sectionBlocks)e=t.updateFilter()||e;this.noMatchesElement.classList.toggle("hidden",Boolean(e))}willHide(){this.hideAllPopovers(),super.willHide()}hideAllPopovers(){this.swatchPopoverHelperInternal.hide(),this.imagePreviewPopover.hide(),this.activeCSSAngle&&(this.activeCSSAngle.minify(),this.activeCSSAngle=null)}allSections(){let e=[];for(const t of this.sectionBlocks)e=e.concat(t.sections);return e}async trackURLForChanges(e){const t=this.#Xe.get(e);t&&m.WorkspaceDiff.workspaceDiff().unsubscribeFromDiffChange(t.uiSourceCode,t.diffChangeCallback);const n=u.Workspace.WorkspaceImpl.instance().uiSourceCodeForURL(e);if(!n)return;const i=this.refreshChangedLines.bind(this,n);m.WorkspaceDiff.workspaceDiff().subscribeToDiffChange(n,i);const o={uiSourceCode:n,changedLines:new Set,diffChangeCallback:i};this.#Xe.set(e,o),await this.refreshChangedLines(o.uiSourceCode)}isPropertyChanged(e){const t=e.ownerStyle.parentRule?.resourceURL();if(!t)return!1;const n=this.#Xe.get(t);if(!n)return!1;const{changedLines:i,formattedCurrentMapping:o}=n,s=c.CSSWorkspaceBinding.CSSWorkspaceBinding.instance().propertyUILocation(e,!0);if(!s)return!1;if(!o)return i.has(s.lineNumber+1);const r=o.originalToFormatted(s.lineNumber,s.columnNumber)[0];return i.has(r+1)}async refreshChangedLines(e){const t=this.#Xe.get(e.url());if(!t)return;const n=await m.WorkspaceDiff.workspaceDiff().requestDiff(e,{shouldFormatDiff:!0}),i=new Set;if(t.changedLines=i,!n)return;const{diff:o,formattedCurrentMapping:s}=n,{rows:r}=g.DiffView.buildDiffRows(o);for(const e of r)"addition"===e.type&&i.add(e.currentLineNumber);t.formattedCurrentMapping=s}async getFormattedChanges(){let e="";for(const[t,{uiSourceCode:n}]of this.#Xe){const i=await m.WorkspaceDiff.workspaceDiff().requestDiff(n,{shouldFormatDiff:!0});if(!i||i?.diff.length<2)continue;const o=await $(i.diff);o.length>0&&(e+=`/* ${ee(t)} */\n\n${o}\n\n`)}return e}clipboardCopy(e){s.userMetrics.actionTaken(s.UserMetrics.Action.StyleRuleCopied)}createStylesSidebarToolbar(){const e=this.contentElement.createChild("div","styles-sidebar-pane-toolbar-container"),t=e.createChild("div","hbox styles-sidebar-pane-toolbar"),n=t.createChild("div","styles-sidebar-pane-filter-box"),i=z.createPropertyFilterElement(B(F.filter),t,this.onFilterChanged.bind(this));b.ARIAUtils.setAccessibleName(i,B(F.filterStyles)),n.appendChild(i);const o=new b.Toolbar.Toolbar("styles-pane-toolbar",t);o.makeToggledGray(),o.appendItemsAtLocation("styles-sidebarpane-toolbar"),this.toolbar=o;return e.createChild("div","styles-sidebar-toolbar-pane-container").createChild("div","styles-sidebar-toolbar-pane")}showToolbarPane(e,t){this.pendingWidgetToggle&&this.pendingWidgetToggle.setToggled(!1),this.pendingWidgetToggle=t,this.animatedToolbarPane?this.pendingWidget=e:this.startToolbarPaneAnimation(e),e&&t&&t.setToggled(!0)}appendToolbarItem(e){this.toolbar&&this.toolbar.appendToolbarItem(e)}startToolbarPaneAnimation(e){if(e===this.currentToolbarPane)return;if(e&&this.currentToolbarPane)return this.currentToolbarPane.detach(),e.show(this.toolbarPaneElement),this.currentToolbarPane=e,void this.currentToolbarPane.focus();this.animatedToolbarPane=e,this.currentToolbarPane?this.toolbarPaneElement.style.animationName="styles-element-state-pane-slideout":e&&(this.toolbarPaneElement.style.animationName="styles-element-state-pane-slidein"),e&&e.show(this.toolbarPaneElement);const t=function(){this.toolbarPaneElement.style.removeProperty("animation-name"),this.toolbarPaneElement.removeEventListener("animationend",t,!1),this.currentToolbarPane&&this.currentToolbarPane.detach();this.currentToolbarPane=this.animatedToolbarPane,this.currentToolbarPane&&this.currentToolbarPane.focus();this.animatedToolbarPane=null,this.pendingWidget&&(this.startToolbarPaneAnimation(this.pendingWidget),this.pendingWidget=null)}.bind(this);this.toolbarPaneElement.addEventListener("animationend",t,!1)}createCopyAllChangesButton(){const e=new f.Icon.Icon;e.data={iconName:"ic_changes",color:"var(--color-text-secondary)",width:"18px",height:"18px"};const t=new b.Toolbar.ToolbarButton(B(F.copyAllCSSChanges),e);let n;return t.element.setAttribute("data-content",B(F.copiedToClipboard)),t.addEventListener(b.Toolbar.ToolbarButton.Events.Click,(async()=>{const e=await this.getFormattedChanges();s.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e),s.userMetrics.styleTextCopied(s.UserMetrics.StyleTextCopied.AllChangesViaStylesPane),n&&(clearTimeout(n),n=void 0),t.element.classList.add("copied-to-clipboard"),n=window.setTimeout((()=>{t.element.classList.remove("copied-to-clipboard"),n=void 0}),2e3)})),t}}async function $(e){const{originalLines:t,currentLines:n,rows:i}=g.DiffView.buildDiffRows(e),{propertyToSelector:o,ruleToSelector:s}=await _(t.join("\n")),{propertyToSelector:r,ruleToSelector:l}=await _(n.join("\n"));let a,d,c="";for(const{currentLineNumber:e,originalLineNumber:h,type:p}of i){const i=e-1,u=h-1;switch(p){case"deletion":{const e=t[u].trim();if(s.has(u)){c+=`/* ${e} { */\n`,a=e;continue}const n=o.get(u);if(!n)continue;n!==a&&n!==d&&((a||d)&&(c+="}\n\n"),c+=`${n} {\n`),a=n,c+=`  /* ${e} */\n`;break}case"addition":{const e=n[i].trim();if(l.has(i)){c+=`${e} {\n`,d=e;continue}const t=r.get(i);if(!t)continue;t!==a&&t!==d&&((a||d)&&(c+="}\n\n"),c+=`${t} {\n`),d=t,c+=`  ${e}\n`;break}}}return c.length>0&&(c+="}"),c}async function _(e){const t=await new Promise((t=>{const n=[];h.FormatterWorkerPool.formatterWorkerPool().parseCSS(e,((e,i)=>{n.push(...i),e&&t(n)}))})),n=new Map,i=new Map;for(const e of t)if("styleRange"in e){const t=e.selectorText.split("\n").pop()?.trim();if(!t)continue;i.set(e.styleRange.startLine,t);for(const i of e.properties)n.set(i.range.startLine,t)}return{propertyToSelector:n,ruleToSelector:i}}const j=23;class q{titleElementInternal;sections;constructor(e){this.titleElementInternal=e,this.sections=[]}static createPseudoTypeBlock(e){const t=document.createElement("div");return t.className="sidebar-separator",t.textContent=B(F.pseudoSElement,{PH1:e}),new q(t)}static createKeyframesBlock(e){const t=document.createElement("div");return t.className="sidebar-separator",t.textContent=`@keyframes ${e}`,new q(t)}static async createInheritedNodeBlock(e){const t=document.createElement("div");t.className="sidebar-separator",b.UIUtils.createTextChild(t,B(F.inheritedFroms));const n=await o.Linkifier.Linkifier.linkify(e,{preventKeyboardFocus:!0,tooltip:void 0});return t.appendChild(n),new q(t)}static createLayerBlock(e){const t=document.createElement("div");t.className="sidebar-separator layer-separator",b.UIUtils.createTextChild(t.createChild("div"),B(F.layer));const n=e.layers;if(!n.length&&"user-agent"===e.origin){const n="user-agent"===e.origin?" user agent stylesheet":" implicit outer layer";return b.UIUtils.createTextChild(t.createChild("div"),n),new q(t)}const i=t.createChild("button");i.className="link",i.title=B(F.clickToRevealLayer);const o=n.map((e=>d.CSSModel.CSSModel.readableLayerName(e.text))).join(".");return i.textContent=o,i.onclick=()=>R.LayersWidget.instance().revealLayer(o),new q(t)}updateFilter(){let e=!1;for(const t of this.sections)e=t.updateFilter()||e;return this.titleElementInternal&&this.titleElementInternal.classList.toggle("hidden",!e),Boolean(e)}titleElement(){return this.titleElementInternal}}class G{discarded;promises;constructor(){this.discarded=!1,this.promises=[]}discard(){this.discarded=!0}schedule(e,t=100){this.discarded||this.promises.push(new Promise(((n,i)=>{window.requestIdleCallback((()=>{if(this.discarded)return n();(()=>{try{e(),n()}catch(e){i(e)}})()}),{timeout:t})})))}awaitDone(){return Promise.all(this.promises)}}class K{parentPane;styleInternal;matchedStyles;editable;hoverTimer;willCauseCancelEditing;forceShowAll;originalPropertiesCount;element;innerElement;titleElement;propertiesTreeOutline;showAllButton;selectorElement;newStyleRuleToolbar;fontEditorToolbar;fontEditorSectionManager;fontEditorButton;selectedSinceMouseDown;elementToSelectorIndex;navigable;selectorRefElement;selectorContainer;fontPopoverIcon;hoverableSelectorsMode;isHiddenInternal;queryListElement;nextEditorTriggerButtonIdx=1;sectionIdx=0;constructor(e,t,n,i){this.parentPane=e,this.sectionIdx=i,this.styleInternal=n,this.matchedStyles=t,this.editable=Boolean(n.styleSheetId&&n.range),this.hoverTimer=null,this.willCauseCancelEditing=!1,this.forceShowAll=!1,this.originalPropertiesCount=n.leadingProperties().length;const o=n.parentRule;this.element=document.createElement("div"),this.element.classList.add("styles-section"),this.element.classList.add("matched-styles"),this.element.classList.add("monospace"),b.ARIAUtils.setAccessibleName(this.element,`${this.headerText()}, css selector`),this.element.tabIndex=-1,b.ARIAUtils.markAsListitem(this.element),this.element.addEventListener("keydown",this.onKeyDown.bind(this),!1),e.sectionByElement.set(this.element,this),this.innerElement=this.element.createChild("div"),this.titleElement=this.innerElement.createChild("div","styles-section-title "+(o?"styles-selector":"")),this.propertiesTreeOutline=new b.TreeOutline.TreeOutlineInShadow,this.propertiesTreeOutline.setFocusable(!1),this.propertiesTreeOutline.registerCSSFiles([L.default]),this.propertiesTreeOutline.element.classList.add("style-properties","matched-styles","monospace"),this.propertiesTreeOutline.section=this,this.innerElement.appendChild(this.propertiesTreeOutline.element),this.showAllButton=b.UIUtils.createTextButton("",this.showAllItems.bind(this),"styles-show-all"),this.innerElement.appendChild(this.showAllButton);const s=document.createElement("div");this.selectorElement=document.createElement("span"),b.ARIAUtils.setAccessibleName(this.selectorElement,B(F.cssSelector)),this.selectorElement.classList.add("selector"),this.selectorElement.textContent=this.headerText(),s.appendChild(this.selectorElement),this.selectorElement.addEventListener("mouseenter",this.onMouseEnterSelector.bind(this),!1),this.selectorElement.addEventListener("mousemove",(e=>e.consume()),!1),this.selectorElement.addEventListener("mouseleave",this.onMouseOutSelector.bind(this),!1);s.createChild("span","sidebar-pane-open-brace").textContent=" {",s.addEventListener("mousedown",this.handleEmptySpaceMouseDown.bind(this),!1),s.addEventListener("click",this.handleSelectorContainerClick.bind(this),!1);if(this.innerElement.createChild("div","sidebar-pane-closing-brace").textContent="}",this.styleInternal.parentRule){const e=new b.Toolbar.ToolbarButton(B(F.insertStyleRuleBelow),"largeicon-add");e.addEventListener(b.Toolbar.ToolbarButton.Events.Click,this.onNewRuleClick,this),e.element.tabIndex=-1,this.newStyleRuleToolbar||(this.newStyleRuleToolbar=new b.Toolbar.Toolbar("sidebar-pane-section-toolbar new-rule-toolbar",this.innerElement)),this.newStyleRuleToolbar.appendToolbarItem(e),b.ARIAUtils.markAsHidden(this.newStyleRuleToolbar.element)}if(a.Runtime.experiments.isEnabled("fontEditor")&&this.editable&&(this.fontEditorToolbar=new b.Toolbar.Toolbar("sidebar-pane-section-toolbar",this.innerElement),this.fontEditorSectionManager=new(0,S.FontEditorSectionManager)(this.parentPane.swatchPopoverHelper(),this),this.fontEditorButton=new b.Toolbar.ToolbarButton("Font Editor","largeicon-font-editor"),this.fontEditorButton.addEventListener(b.Toolbar.ToolbarButton.Events.Click,(()=>{this.onFontEditorButtonClicked()}),this),this.fontEditorButton.element.addEventListener("keydown",(e=>{isEnterOrSpaceKey(e)&&(e.consume(!0),this.onFontEditorButtonClicked())}),!1),this.fontEditorToolbar.appendToolbarItem(this.fontEditorButton),this.styleInternal.type===d.CSSStyleDeclaration.Type.Inline?this.newStyleRuleToolbar&&this.newStyleRuleToolbar.element.classList.add("shifted-toolbar"):this.fontEditorToolbar.element.classList.add("font-toolbar-hidden")),this.selectorElement.addEventListener("click",this.handleSelectorClick.bind(this),!1),this.element.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this),!1),this.element.addEventListener("mousedown",this.handleEmptySpaceMouseDown.bind(this),!1),this.element.addEventListener("click",this.handleEmptySpaceClick.bind(this),!1),this.element.addEventListener("mousemove",this.onMouseMove.bind(this),!1),this.element.addEventListener("mouseleave",this.onMouseLeave.bind(this),!1),this.selectedSinceMouseDown=!1,this.elementToSelectorIndex=new WeakMap,o)if(o.isUserAgent()||o.isInjected())this.editable=!1;else if(o.styleSheetId){const e=o.cssModel().styleSheetHeaderForId(o.styleSheetId);this.navigable=e&&!e.isAnonymousInlineStyleSheet()}this.queryListElement=this.titleElement.createChild("div","query-list query-matches"),this.selectorRefElement=this.titleElement.createChild("div","styles-section-subtitle"),this.updateQueryList(),this.updateRuleOrigin(),this.titleElement.appendChild(s),this.selectorContainer=s,this.navigable&&this.element.classList.add("navigable"),this.editable||(this.element.classList.add("read-only"),this.propertiesTreeOutline.element.classList.add("read-only")),this.fontPopoverIcon=null,this.hoverableSelectorsMode=!1,this.isHiddenInternal=!1,this.markSelectorMatches(),this.onpopulate()}setSectionIdx(e){this.sectionIdx=e,this.onpopulate()}getSectionIdx(){return this.sectionIdx}registerFontProperty(e){this.fontEditorSectionManager&&this.fontEditorSectionManager.registerFontProperty(e),this.fontEditorToolbar&&(this.fontEditorToolbar.element.classList.remove("font-toolbar-hidden"),this.newStyleRuleToolbar&&this.newStyleRuleToolbar.element.classList.add("shifted-toolbar"))}resetToolbars(){this.parentPane.swatchPopoverHelper().isShowing()||this.styleInternal.type===d.CSSStyleDeclaration.Type.Inline||(this.fontEditorToolbar&&this.fontEditorToolbar.element.classList.add("font-toolbar-hidden"),this.newStyleRuleToolbar&&this.newStyleRuleToolbar.element.classList.remove("shifted-toolbar"))}static createRuleOriginNode(e,t,n){if(!n)return document.createTextNode("");const i=this.getRuleLocationFromCSSRule(n),o=n.styleSheetId?e.cssModel().styleSheetHeaderForId(n.styleSheetId):null;function s(){return n&&i&&n.styleSheetId&&o&&!o.isAnonymousInlineStyleSheet()?K.linkifyRuleLocation(e.cssModel(),t,n.styleSheetId,i):null}function r(e){if(o?.ownerNode){const t=(0,M.linkifyDeferredNodeReference)(o.ownerNode,{preventKeyboardFocus:!1,tooltip:void 0});return t.textContent=e,t}return null}if(o?.isMutable&&!o.isViaInspector()){const e=o.isConstructedByNew()?null:s();if(e)return e;const t=o.isConstructedByNew()?B(F.constructedStylesheet):W,n=r(t);return n||document.createTextNode(t)}const l=s();if(l)return l;if(n.isUserAgent())return document.createTextNode(B(F.userAgentStylesheet));if(n.isInjected())return document.createTextNode(B(F.injectedStylesheet));if(n.isViaInspector())return document.createTextNode(B(F.viaInspector));const a=r(W);return a||document.createTextNode("")}static getRuleLocationFromCSSRule(e){let t;return e instanceof d.CSSRule.CSSStyleRule?t=e.style.range:e instanceof d.CSSRule.CSSKeyframeRule&&(t=e.key().range),t}static tryNavigateToRuleLocation(e,t){if(!t)return;const n=this.getRuleLocationFromCSSRule(t),i=t.styleSheetId?e.cssModel().styleSheetHeaderForId(t.styleSheetId):null;if(n&&t.styleSheetId&&i&&!i.isAnonymousInlineStyleSheet()){const i=this.getCSSSelectorLocation(e.cssModel(),t.styleSheetId,n);this.revealSelectorSource(i,!0)}}static linkifyRuleLocation(e,t,n,i){const o=this.getCSSSelectorLocation(e,n,i);return t.linkifyCSSLocation(o)}static getCSSSelectorLocation(e,t,n){const i=e.styleSheetHeaderForId(t),o=i.lineNumberInSource(n.startLine),s=i.columnNumberInSource(n.startLine,n.startColumn);return new d.CSSModel.CSSLocation(i,o,s)}getFocused(){return this.propertiesTreeOutline.shadowRoot.activeElement||null}focusNext(e){const t=this.getFocused();t&&(t.tabIndex=-1),e.focus(),this.propertiesTreeOutline.shadowRoot.contains(e)&&(e.tabIndex=0)}ruleNavigation(e){if(e.altKey||e.ctrlKey||e.metaKey||e.shiftKey)return;const t=this.getFocused();let n=null;const i=Array.from(this.propertiesTreeOutline.shadowRoot.querySelectorAll("[tabindex]"));if(0===i.length)return;const o=t?i.indexOf(t):-1;if("ArrowLeft"===e.key)n=i[o-1]||this.element;else if("ArrowRight"===e.key)n=i[o+1]||this.element;else if("ArrowUp"===e.key||"ArrowDown"===e.key)return void this.focusNext(this.element);n&&(this.focusNext(n),e.consume(!0))}onKeyDown(e){const t=e;if(!(b.UIUtils.isEditing()||!this.editable||t.altKey||t.ctrlKey||t.metaKey))switch(t.key){case"Enter":case" ":this.startEditingAtFirstPosition(),t.consume(!0);break;case"ArrowLeft":case"ArrowRight":case"ArrowUp":case"ArrowDown":this.ruleNavigation(t);break;default:1===t.key.length&&this.addNewBlankProperty(0).startEditing()}}setSectionHovered(e){this.element.classList.toggle("styles-panel-hovered",e),this.propertiesTreeOutline.element.classList.toggle("styles-panel-hovered",e),this.hoverableSelectorsMode!==e&&(this.hoverableSelectorsMode=e,this.markSelectorMatches())}onMouseLeave(e){this.setSectionHovered(!1),this.parentPane.setActiveProperty(null)}onMouseMove(e){const t=b.KeyboardShortcut.KeyboardShortcut.eventHasCtrlEquivalentKey(e);this.setSectionHovered(t);const n=this.propertiesTreeOutline.treeElementFromEvent(e);n instanceof A.StylePropertyTreeElement?this.parentPane.setActiveProperty(n):this.parentPane.setActiveProperty(null);const i=this.element.getComponentSelection();!this.selectedSinceMouseDown&&i&&i.toString()&&(this.selectedSinceMouseDown=!0)}onFontEditorButtonClicked(){this.fontEditorSectionManager&&this.fontEditorButton&&this.fontEditorSectionManager.showPopover(this.fontEditorButton.element,this.parentPane)}style(){return this.styleInternal}headerText(){const e=this.matchedStyles.nodeForStyle(this.styleInternal);return this.styleInternal.type===d.CSSStyleDeclaration.Type.Inline?this.matchedStyles.isInherited(this.styleInternal)?B(F.styleAttribute):"element.style":e&&this.styleInternal.type===d.CSSStyleDeclaration.Type.Attributes?B(F.sattributesStyle,{PH1:e.nodeNameInCorrectCase()}):this.styleInternal.parentRule instanceof d.CSSRule.CSSStyleRule?this.styleInternal.parentRule.selectorText():""}onMouseOutSelector(){this.hoverTimer&&clearTimeout(this.hoverTimer),d.OverlayModel.OverlayModel.hideDOMNodeHighlight()}onMouseEnterSelector(){this.hoverTimer&&clearTimeout(this.hoverTimer),this.hoverTimer=window.setTimeout(this.highlight.bind(this),300)}highlight(e="all"){d.OverlayModel.OverlayModel.hideDOMNodeHighlight();const t=this.parentPane.node();if(!t)return;const n=this.styleInternal.parentRule&&this.styleInternal.parentRule instanceof d.CSSRule.CSSStyleRule?this.styleInternal.parentRule.selectorText():void 0;t.domModel().overlayModel().highlightInOverlay({node:t,selectorList:n},e)}firstSibling(){const e=this.element.parentElement;if(!e)return null;let t=e.firstChild;for(;t;){const e=this.parentPane.sectionByElement.get(t);if(e)return e;t=t.nextSibling}return null}findCurrentOrNextVisible(e,t){if(!this.isHidden())return this;if(this===t)return null;t||(t=this);let n=null;const i=e?this.nextSibling():this.previousSibling();if(i)n=i.findCurrentOrNextVisible(e,t);else{const i=e?this.firstSibling():this.lastSibling();i&&(n=i.findCurrentOrNextVisible(e,t))}return n}lastSibling(){const e=this.element.parentElement;if(!e)return null;let t=e.lastChild;for(;t;){const e=this.parentPane.sectionByElement.get(t);if(e)return e;t=t.previousSibling}return null}nextSibling(){let e=this.element;do{e=e.nextSibling}while(e&&!this.parentPane.sectionByElement.has(e));if(e)return this.parentPane.sectionByElement.get(e)}previousSibling(){let e=this.element;do{e=e.previousSibling}while(e&&!this.parentPane.sectionByElement.has(e));if(e)return this.parentPane.sectionByElement.get(e)}onNewRuleClick(e){e.data.consume();const t=this.styleInternal.parentRule;if(!t||!t.style.range||void 0===t.styleSheetId)return;const n=p.TextRange.TextRange.createFromLocation(t.style.range.endLine,t.style.range.endColumn+1);this.parentPane.addBlankSection(this,t.styleSheetId,n)}styleSheetEdited(e){const t=this.styleInternal.parentRule;t?t.rebase(e):this.styleInternal.rebase(e),this.updateQueryList(),this.updateRuleOrigin()}createAtRuleLists(e){this.createMediaList(e.media),this.createContainerQueryList(e.containerQueries),this.createSupportsList(e.supports)}createMediaList(e){for(let t=e.length-1;t>=0;--t){const n=e[t];if(!n.text||!n.text.includes("(")&&"print"!==n.text)continue;let i,o="",s="";switch(n.source){case d.CSSMedia.Source.LINKED_SHEET:case d.CSSMedia.Source.INLINE_SHEET:s=`media="${n.text}"`;break;case d.CSSMedia.Source.MEDIA_RULE:o="@media",s=n.text,n.styleSheetId&&(i=this.handleQueryRuleClick.bind(this,n));break;case d.CSSMedia.Source.IMPORT_RULE:s=`@import ${n.text}`}const r=new E.CSSQuery;r.data={queryPrefix:o,queryText:s,onQueryTextClick:i},this.queryListElement.append(r)}}createContainerQueryList(e){for(let t=e.length-1;t>=0;--t){const n=e[t];if(!n.text)continue;let i;n.styleSheetId&&(i=this.handleQueryRuleClick.bind(this,n));const o=new E.CSSQuery;o.data={queryPrefix:"@container",queryName:n.name,queryText:n.text,onQueryTextClick:i},this.queryListElement.append(o),this.addContainerForContainerQuery(n)}}createSupportsList(e){for(let t=e.length-1;t>=0;--t){const n=e[t];if(!n.text)continue;let i;n.styleSheetId&&(i=this.handleQueryRuleClick.bind(this,n));const o=new E.CSSQuery;o.data={queryPrefix:"@supports",queryText:n.text,onQueryTextClick:i},this.queryListElement.append(o)}}async addContainerForContainerQuery(e){const t=await e.getContainerForNode(this.matchedStyles.node().id);if(!t)return;const n=new C.QueryContainer;n.data={container:x.legacyNodeToElementsComponentsNode(t.containerNode),queryName:e.name,onContainerLinkClick:e=>{e.preventDefault(),N.ElementsPanel.instance().revealAndSelectNode(t.containerNode,!0,!0),t.containerNode.scrollIntoView()}},n.addEventListener("queriedsizerequested",(async()=>{const e=await t.getContainerSizeDetails();e&&n.updateContainerQueriedSizeDetails(e)})),this.queryListElement.prepend(n)}updateQueryList(){this.queryListElement.removeChildren(),this.styleInternal.parentRule&&this.styleInternal.parentRule instanceof d.CSSRule.CSSStyleRule&&this.createAtRuleLists(this.styleInternal.parentRule)}isPropertyInherited(e){return!!this.matchedStyles.isInherited(this.styleInternal)&&!d.CSSMetadata.cssMetadata().isPropertyInherited(e)}nextEditableSibling(){let e=this;do{e=e.nextSibling()}while(e&&!e.editable);if(!e)for(e=this.firstSibling();e&&!e.editable;)e=e.nextSibling();return e&&e.editable?e:null}previousEditableSibling(){let e=this;do{e=e.previousSibling()}while(e&&!e.editable);if(!e)for(e=this.lastSibling();e&&!e.editable;)e=e.previousSibling();return e&&e.editable?e:null}refreshUpdate(e){this.parentPane.refreshUpdate(this,e)}updateVarFunctions(e){let t=this.propertiesTreeOutline.firstChild();for(;t;)t!==e&&t instanceof A.StylePropertyTreeElement&&t.updateTitleIfComputedValueChanged(),t=t.traverseNextTreeElement(!1,null,!0)}update(e){if(this.selectorElement.textContent=this.headerText(),this.markSelectorMatches(),e)this.onpopulate();else{let e=this.propertiesTreeOutline.firstChild();for(;e&&e instanceof A.StylePropertyTreeElement;)e.setOverloaded(this.isPropertyOverloaded(e.property)),e=e.traverseNextTreeElement(!1,null,!0)}}showAllItems(e){e&&e.consume(),this.forceShowAll||(this.forceShowAll=!0,this.onpopulate())}onpopulate(){this.parentPane.setActiveProperty(null),this.nextEditorTriggerButtonIdx=1,this.propertiesTreeOutline.removeChildren();const e=this.styleInternal;let t=0;const n=e.leadingProperties(),i=K.MaxProperties+n.length-this.originalPropertiesCount;for(const o of n){if(!this.forceShowAll&&t>=i)break;t++;const n=Boolean(e.longhandProperties(o.name).length),s=this.isPropertyInherited(o.name),r=this.isPropertyOverloaded(o);if(e.parentRule&&e.parentRule.isUserAgent()&&s)continue;const l=new(0,A.StylePropertyTreeElement)(this.parentPane,this.matchedStyles,o,n,s,r,!1);this.propertiesTreeOutline.appendChild(l)}t<n.length?(this.showAllButton.classList.remove("hidden"),this.showAllButton.textContent=B(F.showAllPropertiesSMore,{PH1:n.length-t})):this.showAllButton.classList.add("hidden")}isPropertyOverloaded(e){return this.matchedStyles.propertyState(e)===d.CSSMatchedStyles.PropertyState.Overloaded}updateFilter(){let e=!1;this.showAllItems();for(const t of this.propertiesTreeOutline.rootElement().children())if(t instanceof A.StylePropertyTreeElement){const n=t.updateFilter();e=e||n}const t=this.parentPane.filterRegex(),n=!e&&null!==t&&!t.test(this.element.deepTextContent());return this.isHiddenInternal=n,this.element.classList.toggle("hidden",n),!n&&this.styleInternal.parentRule&&this.markSelectorHighlights(),!n}isHidden(){return this.isHiddenInternal}markSelectorMatches(){const e=this.styleInternal.parentRule;if(!(e&&e instanceof d.CSSRule.CSSStyleRule))return;this.queryListElement.classList.toggle("query-matches",this.matchedStyles.mediaMatches(this.styleInternal));const t=e.selectors.map((e=>e.text)),n=this.matchedStyles.getMatchingSelectors(e),i=new Array(t.length).fill(!1);for(const e of n)i[e]=!0;if(this.parentPane.isEditingStyle)return;const o=this.hoverableSelectorsMode?this.renderHoverableSelectors(t,i):this.renderSimplifiedSelectors(t,i);this.selectorElement.removeChildren(),this.selectorElement.appendChild(o),this.markSelectorHighlights()}renderHoverableSelectors(e,t){const n=document.createDocumentFragment();for(let i=0;i<e.length;++i)i&&b.UIUtils.createTextChild(n,", "),n.appendChild(this.createSelectorElement(e[i],t[i],i));return n}createSelectorElement(e,t,n){const i=document.createElement("span");return i.classList.add("simple-selector"),i.classList.toggle("selector-matches",t),"number"==typeof n&&this.elementToSelectorIndex.set(i,n),i.textContent=e,i}renderSimplifiedSelectors(e,t){const n=document.createDocumentFragment();let i=!1,o="";for(let s=0;s<e.length;++s)i!==t[s]&&o&&(n.appendChild(this.createSelectorElement(o,i)),o=""),i=t[s],o+=e[s]+(s===e.length-1?"":", ");return o&&n.appendChild(this.createSelectorElement(o,i)),n}markSelectorHighlights(){const e=this.selectorElement.getElementsByClassName("simple-selector"),t=this.parentPane.filterRegex();for(let n=0;n<e.length;++n){const i=null!==t&&t.test(e[n].textContent||"");e[n].classList.toggle("filter-match",i)}}checkWillCancelEditing(){const e=this.willCauseCancelEditing;return this.willCauseCancelEditing=!1,e}handleSelectorContainerClick(e){!this.checkWillCancelEditing()&&this.editable&&e.target===this.selectorContainer&&(this.addNewBlankProperty(0).startEditing(),e.consume(!0))}addNewBlankProperty(e=this.propertiesTreeOutline.rootElement().childCount()){const t=this.styleInternal.newBlankProperty(e),n=new(0,A.StylePropertyTreeElement)(this.parentPane,this.matchedStyles,t,!1,!1,!1,!0);return this.propertiesTreeOutline.insertChild(n,t.index),n}handleEmptySpaceMouseDown(){this.willCauseCancelEditing=this.parentPane.isEditingStyle,this.selectedSinceMouseDown=!1}handleEmptySpaceClick(e){if(!this.editable||this.element.hasSelection()||this.checkWillCancelEditing()||this.selectedSinceMouseDown)return;const t=e.target;if(t.classList.contains("header")||this.element.classList.contains("read-only")||t.enclosingNodeOrSelfWithClass("query"))return void e.consume();const n=b.UIUtils.deepElementFromEvent(e),i=n&&b.TreeOutline.TreeElement.getTreeElementBylistItemNode(n);i&&i instanceof A.StylePropertyTreeElement?this.addNewBlankProperty(i.property.index+1).startEditing():this.addNewBlankProperty().startEditing(),e.consume(!0)}handleQueryRuleClick(e,t){const n=t.currentTarget;if(b.UIUtils.isBeingEdited(n))return;if(b.KeyboardShortcut.KeyboardShortcut.eventHasCtrlEquivalentKey(t)&&this.navigable){const n=e.rawLocation();if(!n)return void t.consume(!0);const i=c.CSSWorkspaceBinding.CSSWorkspaceBinding.instance().rawLocationToUILocation(n);return i&&o.Revealer.reveal(i),void t.consume(!0)}if(!this.editable)return;const i=new b.InplaceEditor.Config(this.editingMediaCommitted.bind(this,e),this.editingMediaCancelled.bind(this,n),void 0,this.editingMediaBlurHandler.bind(this));b.InplaceEditor.InplaceEditor.startEditing(n,i);const s=n.getComponentSelection();s&&s.selectAllChildren(n),this.parentPane.setEditingStyle(!0);n.enclosingNodeOrSelfWithClass("query").classList.add("editing-query"),t.consume(!0)}editingMediaFinished(e){this.parentPane.setEditingStyle(!1);e.enclosingNodeOrSelfWithClass("query").classList.remove("editing-query")}editingMediaCancelled(e){this.editingMediaFinished(e),this.markSelectorMatches();const t=e.getComponentSelection();t&&t.collapse(e,0)}editingMediaBlurHandler(){return!0}async editingMediaCommitted(e,t,n,i,o,s){this.parentPane.setEditingStyle(!1),this.editingMediaFinished(t),n&&(n=n.trim()),this.parentPane.setUserOperation(!0);const r=this.parentPane.cssModel();if(r&&e.styleSheetId){const t=e.range;let i=!1;i=e instanceof d.CSSContainerQuery.CSSContainerQuery?await r.setContainerQueryText(e.styleSheetId,t,n):e instanceof d.CSSSupports.CSSSupports?await r.setSupportsText(e.styleSheetId,t,n):await r.setMediaText(e.styleSheetId,t,n),i&&(this.matchedStyles.resetActiveProperties(),this.parentPane.refreshUpdate(this)),this.parentPane.setUserOperation(!1),this.editingMediaTextCommittedForTest()}}editingMediaTextCommittedForTest(){}handleSelectorClick(e){const t=e.target;if(t){if(b.KeyboardShortcut.KeyboardShortcut.eventHasCtrlEquivalentKey(e)&&this.navigable&&t.classList.contains("simple-selector")){const n=this.elementToSelectorIndex.get(t);return n&&this.navigateToSelectorSource(n,!0),void e.consume(!0)}this.element.hasSelection()||(this.startEditingAtFirstPosition(),e.consume(!0))}}handleContextMenuEvent(e){if(!e.target)return;const t=new b.ContextMenu.ContextMenu(e);t.clipboardSection().appendItem(B(F.copySelector),(()=>{const e=this.headerText();s.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e),s.userMetrics.styleTextCopied(s.UserMetrics.StyleTextCopied.SelectorViaContextMenu)})),t.clipboardSection().appendItem(B(F.copyRule),(()=>{const e=z.formatLeadingProperties(this).ruleText;s.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e),s.userMetrics.styleTextCopied(s.UserMetrics.StyleTextCopied.RuleViaContextMenu)})),t.clipboardSection().appendItem(B(F.copyAllDeclarations),(()=>{const e=z.formatLeadingProperties(this).allDeclarationText;s.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e),s.userMetrics.styleTextCopied(s.UserMetrics.StyleTextCopied.AllDeclarationsViaContextMenu)})),t.show()}navigateToSelectorSource(e,t){const n=this.parentPane.cssModel();if(!n)return;const i=this.styleInternal.parentRule;if(!i||void 0===i.styleSheetId)return;const o=n.styleSheetHeaderForId(i.styleSheetId);if(!o)return;const s=new d.CSSModel.CSSLocation(o,i.lineNumberInSource(e),i.columnNumberInSource(e));K.revealSelectorSource(s,t)}static revealSelectorSource(e,t){const n=c.CSSWorkspaceBinding.CSSWorkspaceBinding.instance().rawLocationToUILocation(e);n&&o.Revealer.reveal(n,!t)}startEditingAtFirstPosition(){this.editable&&(this.styleInternal.parentRule?this.startEditingSelector():this.moveEditorFromSelector("forward"))}startEditingSelector(){const e=this.selectorElement;if(b.UIUtils.isBeingEdited(e))return;e.scrollIntoViewIfNeeded(!1);const t=e.textContent;null!==t&&(e.textContent=t.replace(/\s+/g," ").trim());const n=new b.InplaceEditor.Config(this.editingSelectorCommitted.bind(this),this.editingSelectorCancelled.bind(this));b.InplaceEditor.InplaceEditor.startEditing(this.selectorElement,n);const i=e.getComponentSelection();i&&i.selectAllChildren(e),this.parentPane.setEditingStyle(!0),e.classList.contains("simple-selector")&&this.navigateToSelectorSource(0,!1)}moveEditorFromSelector(e){if(this.markSelectorMatches(),e)if("forward"===e){let e=this.propertiesTreeOutline.firstChild();for(;e&&e.inherited();){const t=e.nextSibling;e=t instanceof A.StylePropertyTreeElement?t:null}e?e.startEditing(e.nameElement):this.addNewBlankProperty().startEditing()}else{const e=this.previousEditableSibling();if(!e)return;e.addNewBlankProperty().startEditing()}}editingSelectorCommitted(e,t,n,i,o){if(this.editingSelectorEnded(),t&&(t=t.trim()),t===n)return this.selectorElement.textContent=t,void this.moveEditorFromSelector(o);const s=this.styleInternal.parentRule;s&&(this.parentPane.setUserOperation(!0),this.setHeaderText(s,t).then(function(){this.parentPane.setUserOperation(!1),this.moveEditorFromSelector(o),this.editingSelectorCommittedForTest()}.bind(this)))}setHeaderText(e,t){function n(e){const t=this.matchedStyles.getMatchingSelectors(e).length>0;this.propertiesTreeOutline.element.classList.toggle("no-affect",!t),this.matchedStyles.resetActiveProperties(),this.parentPane.refreshUpdate(this)}if(!(e instanceof d.CSSRule.CSSStyleRule))return Promise.resolve();const i=e.selectorRange();return i?e.setSelectorText(t).then(function(e,t){return t?this.matchedStyles.recomputeMatchingSelectors(e).then(n.bind(this,e)):Promise.resolve()}.bind(this,e,Boolean(i))):Promise.resolve()}editingSelectorCommittedForTest(){}updateRuleOrigin(){this.selectorRefElement.removeChildren(),this.selectorRefElement.appendChild(K.createRuleOriginNode(this.matchedStyles,this.parentPane.linkifier,this.styleInternal.parentRule))}editingSelectorEnded(){this.parentPane.setEditingStyle(!1)}editingSelectorCancelled(){this.editingSelectorEnded(),this.markSelectorMatches()}closestPropertyForEditing(e){const t=this.propertiesTreeOutline.rootElement();return e>=t.childCount()?t.lastChild():t.childAt(e)}static MaxProperties=50}class Q extends K{normal;ruleLocation;styleSheetId;constructor(e,t,n,i,o,s,r){const l=e.cssModel();super(e,t,d.CSSRule.CSSStyleRule.createDummyRule(l,n).style,r),this.normal=!1,this.ruleLocation=o,this.styleSheetId=i,this.selectorRefElement.removeChildren(),this.selectorRefElement.appendChild(K.linkifyRuleLocation(l,this.parentPane.linkifier,i,this.actualRuleLocation())),s&&s.parentRule&&s.parentRule instanceof d.CSSRule.CSSStyleRule&&this.createAtRuleLists(s.parentRule),this.element.classList.add("blank-section")}actualRuleLocation(){const e=this.rulePrefix().split("\n"),t=e[e.length-1],n=new p.TextRange.TextRange(0,0,e.length-1,t?t.length:0);return this.ruleLocation.rebaseAfterTextEdit(p.TextRange.TextRange.createFromLocation(0,0),n)}rulePrefix(){return 0===this.ruleLocation.startLine&&0===this.ruleLocation.startColumn?"":"\n\n"}get isBlank(){return!this.normal}editingSelectorCommitted(e,t,n,i,o){if(!this.isBlank)return void super.editingSelectorCommitted(e,t,n,i,o);function s(e){const t=this.matchedStyles.getMatchingSelectors(e).length>0;this.makeNormal(e),t||this.propertiesTreeOutline.element.classList.add("no-affect"),this.updateRuleOrigin(),this.parentPane.setUserOperation(!1),this.editingSelectorEnded(),this.element.parentElement&&this.moveEditorFromSelector(o),this.markSelectorMatches(),this.editingSelectorCommittedForTest()}t&&(t=t.trim()),this.parentPane.setUserOperation(!0);const r=this.parentPane.cssModel(),l=this.rulePrefix()+t+" {}";r&&r.addRule(this.styleSheetId,l,this.ruleLocation).then(function(e){return e?this.matchedStyles.addNewRule(e,this.matchedStyles.node()).then(s.bind(this,e)):(this.editingSelectorCancelled(),this.editingSelectorCommittedForTest(),Promise.resolve())}.bind(this))}editingSelectorCancelled(){this.parentPane.setUserOperation(!1),this.isBlank?(this.editingSelectorEnded(),this.parentPane.removeSection(this)):super.editingSelectorCancelled()}makeNormal(e){this.element.classList.remove("blank-section"),this.styleInternal=e.style,this.normal=!0}}class X extends K{constructor(e,t,n,i){super(e,t,n,i),this.selectorElement.className="keyframe-key"}headerText(){return this.styleInternal.parentRule instanceof d.CSSRule.CSSKeyframeRule?this.styleInternal.parentRule.key().text:""}setHeaderText(e,t){if(!(e instanceof d.CSSRule.CSSKeyframeRule))return Promise.resolve();return e.key().range?e.setKeyText(t).then(function(e){e&&this.parentPane.refreshUpdate(this)}.bind(this)):Promise.resolve()}isPropertyInherited(e){return!1}isPropertyOverloaded(e){return!1}markSelectorHighlights(){}markSelectorMatches(){this.styleInternal.parentRule instanceof d.CSSRule.CSSKeyframeRule&&(this.selectorElement.textContent=this.styleInternal.parentRule.key().text)}highlight(){}}function Y(e){return`'${e.replaceAll("'","\\'")}'`}class J extends b.TextPrompt.TextPrompt{isColorAware;cssCompletions;selectedNodeComputedStyles;parentNodeComputedStyles;treeElement;isEditingName;cssVariables;constructor(e,t){super(),this.initialize(this.buildPropertyCompletions.bind(this),b.UIUtils.StyleValueDelimiters);const n=d.CSSMetadata.cssMetadata();this.isColorAware=d.CSSMetadata.cssMetadata().isColorAwareProperty(e.property.name),this.cssCompletions=[];const i=e.node();if(t)this.cssCompletions=n.allProperties(),i&&!i.isSVGNode()&&(this.cssCompletions=this.cssCompletions.filter((e=>!n.isSVGProperty(e))));else if(this.cssCompletions=n.getPropertyValues(e.property.name),i&&n.isFontFamilyProperty(e.property.name)){const e=i.domModel().cssModel().fontFaces().map((e=>Y(e.getFontFamily())));this.cssCompletions.unshift(...e)}if(this.selectedNodeComputedStyles=null,this.parentNodeComputedStyles=null,this.treeElement=e,this.isEditingName=t,this.cssVariables=e.matchedStyles().availableCSSVariables(e.property.ownerStyle),this.cssVariables.length<1e3?this.cssVariables.sort(l.StringUtilities.naturalOrderComparator):this.cssVariables.sort(),!t&&(this.disableDefaultSuggestionForEmptyInput(),e&&e.valueElement)){const t=e.valueElement.textContent,n=s.Platform.isMac()?"Cmd":"Ctrl";null!==t&&(t.match(/#[\da-f]{3,6}$/i)?this.setTitle(B(F.incrementdecrementWithMousewheelOne,{PH1:n})):t.match(/\d+/)&&this.setTitle(B(F.incrementdecrementWithMousewheelHundred,{PH1:n})))}}onKeyDown(e){const t=e;switch(t.key){case"ArrowUp":case"ArrowDown":case"PageUp":case"PageDown":if(!this.isSuggestBoxVisible()&&this.handleNameOrValueUpDown(t))return void t.preventDefault();break;case"Enter":if(t.shiftKey)return;return this.tabKeyPressed(),void t.preventDefault()}super.onKeyDown(t)}onMouseWheel(e){this.handleNameOrValueUpDown(e)?e.consume(!0):super.onMouseWheel(e)}tabKeyPressed(){return this.acceptAutoComplete(),!1}handleNameOrValueUpDown(e){return!(this.isEditingName||!this.treeElement.valueElement||!b.UIUtils.handleElementValueModifications(e,this.treeElement.valueElement,function(e,t){this.treeElement.nameElement&&this.treeElement.valueElement&&this.treeElement.applyStyleText(this.treeElement.nameElement.textContent+": "+this.treeElement.valueElement.textContent,!1)}.bind(this),this.isValueSuggestion.bind(this),function(e,t,n){return 0===t||n.length||!d.CSSMetadata.cssMetadata().isLengthProperty(this.treeElement.property.name)||this.treeElement.property.value.toLowerCase().startsWith("calc(")||(n="px"),e+t+n}.bind(this)))}isValueSuggestion(e){return!!e&&(e=e.toLowerCase(),-1!==this.cssCompletions.indexOf(e)||e.startsWith("--"))}async buildPropertyCompletions(e,t,n){const i=t.toLowerCase(),s=!this.isEditingName&&e.trim().endsWith("var(");if(!t&&!n&&!s&&(this.isEditingName||e))return Promise.resolve([]);const r=[],l=[];s||this.cssCompletions.forEach((e=>u.call(this,e,!1)));const a=this.treeElement.node();if(this.isEditingName&&a){d.CSSMetadata.cssMetadata().nameValuePresets(a.isSVGNode()).forEach((e=>u.call(this,e,!1,!0)))}(this.isEditingName||s)&&this.cssVariables.forEach((e=>u.call(this,e,!0)));const c=r.concat(l);!this.isEditingName&&!c.length&&t.length>1&&"!important".startsWith(i)&&c.push({text:"!important",title:void 0,subtitle:void 0,iconType:void 0,priority:void 0,isSecondary:void 0,subtitleRenderer:void 0,selectionRange:void 0,hideGhostText:void 0,iconElement:void 0});const h=t.replace("-","");if(h&&h===h.toUpperCase())for(let e=0;e<c.length;++e)c[e].text.startsWith("--")||(c[e].text=c[e].text.toUpperCase());for(const e of c){if(s){e.title=e.text,e.text+=")";continue}const t=d.CSSMetadata.cssMetadata().getValuePreset(this.treeElement.name,e.text);!this.isEditingName&&t&&(e.title=e.text,e.text=t.text,e.selectionRange={startColumn:t.startColumn,endColumn:t.endColumn})}const p=async()=>{if(!a||this.selectedNodeComputedStyles)return;this.selectedNodeComputedStyles=await a.domModel().cssModel().getComputedStyle(a.id);const e=a.parentNode;e&&(this.parentNodeComputedStyles=await e.domModel().cssModel().getComputedStyle(e.id))};for(const e of c){await p();const t=w.findIcon(this.isEditingName?e.text:`${this.treeElement.property.name}: ${e.text}`,this.selectedNodeComputedStyles,this.parentNodeComputedStyles);if(!t)continue;const n=new f.Icon.Icon,i="12.5px",o="12.5px";n.data={iconName:t.iconName,width:i,height:o,color:"black"},n.style.transform=`rotate(${t.rotate}deg) scale(${1.1*t.scaleX}, ${1.1*t.scaleY})`,n.style.maxHeight=o,n.style.maxWidth=i,e.iconElement=n}return this.isColorAware&&!this.isEditingName&&c.sort(((e,t)=>Boolean(e.subtitleRenderer)===Boolean(t.subtitleRenderer)?0:e.subtitleRenderer?-1:1)),Promise.resolve(c);function u(e,t,n){const s=e.toLowerCase().indexOf(i),a={text:e,title:void 0,subtitle:void 0,iconType:void 0,priority:void 0,isSecondary:void 0,subtitleRenderer:void 0,selectionRange:void 0,hideGhostText:void 0,iconElement:void 0};if(t){const t=this.treeElement.matchedStyles().computeCSSVariable(this.treeElement.property.ownerStyle,e);if(t){const e=o.Color.Color.parse(t);e&&(a.subtitleRenderer=m.bind(null,e))}}n&&(a.hideGhostText=!0),0===s?(a.priority=this.isEditingName?d.CSSMetadata.cssMetadata().propertyUsageWeight(e):1,r.push(a)):s>-1&&l.push(a)}function m(e){const t=new y.ColorSwatch.ColorSwatch;return t.renderColor(e),t.style.pointerEvents="none",t}}}function Z(e){return e.replace(/(?<!\\)\\(?:([a-fA-F0-9]{1,6})|(.))[\n\t\x20]?/gs,((e,t,n)=>{if(n)return n;const i=parseInt(t,16);return 55296<=i&&i<=57343||0===i||i>1114111?"�":String.fromCodePoint(i)}))}function ee(e){const t=new URL(e);return t.search?`${t.origin}${t.pathname}${t.search.replaceAll("*/","*%2F")}${t.hash}`:t.toString()}class te{rule;node;propertyName;propertyValue;colorHandler;bezierHandler;fontHandler;shadowHandler;gridHandler;varHandler;angleHandler;lengthHandler;constructor(e,t,n,i){this.rule=e,this.node=t,this.propertyName=n,this.propertyValue=i,this.colorHandler=null,this.bezierHandler=null,this.fontHandler=null,this.shadowHandler=null,this.gridHandler=null,this.varHandler=document.createTextNode.bind(document),this.angleHandler=null,this.lengthHandler=null}setColorHandler(e){this.colorHandler=e}setBezierHandler(e){this.bezierHandler=e}setFontHandler(e){this.fontHandler=e}setShadowHandler(e){this.shadowHandler=e}setGridHandler(e){this.gridHandler=e}setVarHandler(e){this.varHandler=e}setAngleHandler(e){this.angleHandler=e}setLengthHandler(e){this.lengthHandler=e}renderName(){const e=document.createElement("span");return b.ARIAUtils.setAccessibleName(e,B(F.cssPropertyName,{PH1:this.propertyName})),e.className="webkit-css-property",e.textContent=this.propertyName,e.normalize(),e}renderValue(){const e=document.createElement("span");if(b.ARIAUtils.setAccessibleName(e,B(F.cssPropertyValue,{PH1:this.propertyValue})),e.className="value",!this.propertyValue)return e;const t=d.CSSMetadata.cssMetadata();if(this.shadowHandler&&t.isShadowProperty(this.propertyName)&&!d.CSSMetadata.VariableRegex.test(this.propertyValue))return e.appendChild(this.shadowHandler(this.propertyValue,this.propertyName)),e.normalize(),e;if(this.gridHandler&&t.isGridAreaDefiningProperty(this.propertyName))return e.appendChild(this.gridHandler(this.propertyValue,this.propertyName)),e.normalize(),e;t.isStringProperty(this.propertyName)&&b.Tooltip.Tooltip.install(e,Z(this.propertyValue));const n=[d.CSSMetadata.VariableRegex,d.CSSMetadata.URLRegex],i=[this.varHandler,this.processURL.bind(this)];this.bezierHandler&&t.isBezierAwareProperty(this.propertyName)&&(n.push(b.Geometry.CubicBezier.Regex),i.push(this.bezierHandler)),this.colorHandler&&t.isColorAwareProperty(this.propertyName)&&(n.push(o.Color.Regex),i.push(this.colorHandler)),this.angleHandler&&t.isAngleAwareProperty(this.propertyName)&&(n.push(y.CSSAngleUtils.CSSAngleRegex),i.push(this.angleHandler)),this.fontHandler&&t.isFontAwareProperty(this.propertyName)&&("font-family"===this.propertyName?n.push(y.FontEditorUtils.FontFamilyRegex):n.push(y.FontEditorUtils.FontPropertiesRegex),i.push(this.fontHandler)),a.Runtime.experiments.isEnabled("cssTypeComponentLength")&&this.lengthHandler&&(n.push(y.CSSLengthUtils.CSSLengthRegex),i.push(this.lengthHandler));const s=p.TextUtils.Utils.splitStringByRegexes(this.propertyValue,n);for(let t=0;t<s.length;t++){const n=s[t],o=-1===n.regexIndex?document.createTextNode.bind(document):i[n.regexIndex];o&&e.appendChild(o(n.value))}return e.normalize(),e}processURL(e){let t=e.substring(4,e.length-1).trim();(/^'.*'$/s.test(t)||/^".*"$/s.test(t))&&(t=t.substring(1,t.length-1));const n=document.createDocumentFragment();b.UIUtils.createTextChild(n,"url(");let i=null;this.rule&&this.rule.resourceURL()?i=o.ParsedURL.ParsedURL.completeURL(this.rule.resourceURL(),t):this.node&&(i=this.node.resolveURL(t));const s=P.ImagePreviewPopover.setImageUrl(v.Linkifier.Linkifier.linkifyURL(i||t,{text:t,preventClick:!1,bypassURLTrimming:!0,showColumnNumber:!1,inlineFrameIndex:0}),i||t);return n.appendChild(s),b.UIUtils.createTextChild(n,")"),n}}let ne;class ie{button;constructor(){this.button=new b.Toolbar.ToolbarButton(B(F.newStyleRule),"largeicon-add"),this.button.addEventListener(b.Toolbar.ToolbarButton.Events.Click,this.clicked,this);const e=b.Icon.Icon.create("largeicon-longclick-triangle","long-click-glyph");function t(){let e=b.Context.Context.instance().flavor(d.DOMModel.DOMNode);e=e?e.enclosingElementOrSelf():null,this.button.setEnabled(Boolean(e))}this.button.element.appendChild(e),new b.UIUtils.LongClickController(this.button.element,this.longClicked.bind(this)),b.Context.Context.instance().addFlavorChangeListener(d.DOMModel.DOMNode,t.bind(this)),t.call(this)}static instance(e={forceNew:null}){const{forceNew:t}=e;return ne&&!t||(ne=new ie),ne}clicked(){z.instance().createNewRuleInViaInspectorStyleSheet()}longClicked(e){z.instance().onAddButtonLongClick(e)}item(){return this.button}}})),t.register("hqdDa",(function(n,i){e(n.exports,"BezierPopoverIcon",(()=>u)),e(n.exports,"ColorSwatchPopoverIcon",(()=>m)),e(n.exports,"ShadowSwatchPopoverHelper",(()=>g)),e(n.exports,"FontEditorSectionManager",(()=>f));var o=t("koSS8"),s=t("ixFnt"),r=t("fMswD"),l=t("XILzK"),a=t("fuChX"),d=t("9z2ZV");const c={openCubicBezierEditor:"Open cubic bezier editor",openShadowEditor:"Open shadow editor"},h=s.i18n.registerUIStrings("panels/elements/ColorSwatchPopoverIcon.ts",c),p=s.i18n.getLocalizedString.bind(void 0,h);class u{treeElement;swatchPopoverHelper;swatch;boundBezierChanged;boundOnScroll;bezierEditor;scrollerElement;originalPropertyText;constructor(e,t,n){this.treeElement=e,this.swatchPopoverHelper=t,this.swatch=n,d.Tooltip.Tooltip.install(this.swatch.iconElement(),p(c.openCubicBezierEditor)),this.swatch.iconElement().addEventListener("click",this.iconClick.bind(this),!1),this.swatch.iconElement().addEventListener("mousedown",(e=>e.consume()),!1),this.boundBezierChanged=this.bezierChanged.bind(this),this.boundOnScroll=this.onScroll.bind(this)}iconClick(e){if(e.consume(!0),this.swatchPopoverHelper.isShowing())return void this.swatchPopoverHelper.hide(!0);const t=d.Geometry.CubicBezier.parse(this.swatch.bezierText())||d.Geometry.CubicBezier.parse("linear");this.bezierEditor=new a.BezierEditor.BezierEditor(t),this.bezierEditor.setBezier(t),this.bezierEditor.addEventListener(a.BezierEditor.Events.BezierChanged,this.boundBezierChanged),this.swatchPopoverHelper.show(this.bezierEditor,this.swatch.iconElement(),this.onPopoverHidden.bind(this)),this.scrollerElement=this.swatch.enclosingNodeOrSelfWithClass("style-panes-wrapper"),this.scrollerElement&&this.scrollerElement.addEventListener("scroll",this.boundOnScroll,!1),this.originalPropertyText=this.treeElement.property.propertyText,this.treeElement.parentPane().setEditingStyle(!0);const n=r.CSSWorkspaceBinding.CSSWorkspaceBinding.instance().propertyUILocation(this.treeElement.property,!1);n&&o.Revealer.reveal(n,!0)}bezierChanged(e){this.swatch.setBezierText(e.data),this.treeElement.applyStyleText(this.treeElement.renderedPropertyText(),!1)}onScroll(e){this.swatchPopoverHelper.hide(!0)}onPopoverHidden(e){this.scrollerElement&&this.scrollerElement.removeEventListener("scroll",this.boundOnScroll,!1),this.bezierEditor&&this.bezierEditor.removeEventListener(a.BezierEditor.Events.BezierChanged,this.boundBezierChanged),this.bezierEditor=void 0;const t=e?this.treeElement.renderedPropertyText():this.originalPropertyText||"";this.treeElement.applyStyleText(t,!0),this.treeElement.parentPane().setEditingStyle(!1),delete this.originalPropertyText}}class m{treeElement;swatchPopoverHelper;swatch;contrastInfo;boundSpectrumChanged;boundOnScroll;spectrum;scrollerElement;originalPropertyText;constructor(e,t,n){this.treeElement=e,this.swatchPopoverHelper=t,this.swatch=n,this.swatch.addEventListener(a.ColorSwatch.ClickEvent.eventName,this.iconClick.bind(this)),this.contrastInfo=null,this.boundSpectrumChanged=this.spectrumChanged.bind(this),this.boundOnScroll=this.onScroll.bind(this)}generateCSSVariablesPalette(){const e=this.treeElement.matchedStyles(),t=this.treeElement.property.ownerStyle,n=e.availableCSSVariables(t),i=[],s=[];for(const r of n){if(r===this.treeElement.property.name)continue;const n=e.computeCSSVariable(t,r);if(!n)continue;o.Color.Color.parse(n)&&(i.push(n),s.push(r))}return{title:"CSS Variables",mutable:!1,matchUserFormat:!0,colors:i,colorNames:s}}setContrastInfo(e){this.contrastInfo=e}iconClick(e){e.consume(!0),this.showPopover()}showPopover(){if(this.swatchPopoverHelper.isShowing())return void this.swatchPopoverHelper.hide(!0);const e=this.swatch.getColor();let t=this.swatch.getFormat();if(!e||!t)return;t===o.Color.Format.Original&&(t=e.format()),this.spectrum=new l.Spectrum.Spectrum(this.contrastInfo),this.spectrum.setColor(e,t),this.spectrum.addPalette(this.generateCSSVariablesPalette()),this.spectrum.addEventListener(l.Spectrum.Events.SizeChanged,this.spectrumResized,this),this.spectrum.addEventListener(l.Spectrum.Events.ColorChanged,this.boundSpectrumChanged),this.swatchPopoverHelper.show(this.spectrum,this.swatch,this.onPopoverHidden.bind(this)),this.scrollerElement=this.swatch.enclosingNodeOrSelfWithClass("style-panes-wrapper"),this.scrollerElement&&this.scrollerElement.addEventListener("scroll",this.boundOnScroll,!1),this.originalPropertyText=this.treeElement.property.propertyText,this.treeElement.parentPane().setEditingStyle(!0);const n=r.CSSWorkspaceBinding.CSSWorkspaceBinding.instance().propertyUILocation(this.treeElement.property,!1);n&&o.Revealer.reveal(n,!0)}spectrumResized(){this.swatchPopoverHelper.reposition()}spectrumChanged(e){const t=o.Color.Color.parse(e.data);if(!t)return;const n=this.spectrum?this.spectrum.colorName():void 0,i=n&&n.startsWith("--")?`var(${n})`:t.asString();this.swatch.renderColor(t);const s=this.swatch.firstElementChild;s&&(s.remove(),this.swatch.createChild("span").textContent=i),this.treeElement.applyStyleText(this.treeElement.renderedPropertyText(),!1)}onScroll(e){this.swatchPopoverHelper.hide(!0)}onPopoverHidden(e){this.scrollerElement&&this.scrollerElement.removeEventListener("scroll",this.boundOnScroll,!1),this.spectrum&&this.spectrum.removeEventListener(l.Spectrum.Events.ColorChanged,this.boundSpectrumChanged),this.spectrum=void 0;const t=e?this.treeElement.renderedPropertyText():this.originalPropertyText||"";this.treeElement.applyStyleText(t,!0),this.treeElement.parentPane().setEditingStyle(!1),delete this.originalPropertyText}}class g{treeElement;swatchPopoverHelper;shadowSwatch;iconElement;boundShadowChanged;boundOnScroll;cssShadowEditor;scrollerElement;originalPropertyText;constructor(e,t,n){this.treeElement=e,this.swatchPopoverHelper=t,this.shadowSwatch=n,this.iconElement=n.iconElement(),d.Tooltip.Tooltip.install(this.iconElement,p(c.openShadowEditor)),this.iconElement.addEventListener("click",this.iconClick.bind(this),!1),this.iconElement.addEventListener("mousedown",(e=>e.consume()),!1),this.boundShadowChanged=this.shadowChanged.bind(this),this.boundOnScroll=this.onScroll.bind(this)}iconClick(e){e.consume(!0),this.showPopover()}showPopover(){if(this.swatchPopoverHelper.isShowing())return void this.swatchPopoverHelper.hide(!0);this.cssShadowEditor=new a.CSSShadowEditor.CSSShadowEditor,this.cssShadowEditor.setModel(this.shadowSwatch.model()),this.cssShadowEditor.addEventListener(a.CSSShadowEditor.Events.ShadowChanged,this.boundShadowChanged),this.swatchPopoverHelper.show(this.cssShadowEditor,this.iconElement,this.onPopoverHidden.bind(this)),this.scrollerElement=this.iconElement.enclosingNodeOrSelfWithClass("style-panes-wrapper"),this.scrollerElement&&this.scrollerElement.addEventListener("scroll",this.boundOnScroll,!1),this.originalPropertyText=this.treeElement.property.propertyText,this.treeElement.parentPane().setEditingStyle(!0);const e=r.CSSWorkspaceBinding.CSSWorkspaceBinding.instance().propertyUILocation(this.treeElement.property,!1);e&&o.Revealer.reveal(e,!0)}shadowChanged(e){this.shadowSwatch.setCSSShadow(e.data),this.treeElement.applyStyleText(this.treeElement.renderedPropertyText(),!1)}onScroll(e){this.swatchPopoverHelper.hide(!0)}onPopoverHidden(e){this.scrollerElement&&this.scrollerElement.removeEventListener("scroll",this.boundOnScroll,!1),this.cssShadowEditor&&this.cssShadowEditor.removeEventListener(a.CSSShadowEditor.Events.ShadowChanged,this.boundShadowChanged),this.cssShadowEditor=void 0;const t=e?this.treeElement.renderedPropertyText():this.originalPropertyText||"";this.treeElement.applyStyleText(t,!0),this.treeElement.parentPane().setEditingStyle(!1),delete this.originalPropertyText}}class f{treeElementMap;swatchPopoverHelper;section;parentPane;fontEditor;scrollerElement;boundFontChanged;boundOnScroll;boundResized;constructor(e,t){this.treeElementMap=new Map,this.swatchPopoverHelper=e,this.section=t,this.parentPane=null,this.fontEditor=null,this.scrollerElement=null,this.boundFontChanged=this.fontChanged.bind(this),this.boundOnScroll=this.onScroll.bind(this),this.boundResized=this.fontEditorResized.bind(this)}fontChanged(e){const{propertyName:t,value:n}=e.data,i=this.treeElementMap.get(t);this.updateFontProperty(t,n,i)}async updateFontProperty(e,t,n){if(n&&n.treeOutline&&n.valueElement&&n.property.parsedOk&&n.property.range){let e,i=!1;n.valueElement.textContent=t,n.property.value=t;const o=n.property.name;t.length?e=n.renderedPropertyText():(e="",i=!0,this.fixIndex(n.property.index)),this.treeElementMap.set(o,n),await n.applyStyleText(e,!0),i&&this.treeElementMap.delete(o)}else if(t.length){const n=this.section.addNewBlankProperty();n&&(n.property.name=e,n.property.value=t,n.updateTitle(),await n.applyStyleText(n.renderedPropertyText(),!0),this.treeElementMap.set(n.property.name,n))}this.section.onpopulate(),this.swatchPopoverHelper.reposition()}fontEditorResized(){this.swatchPopoverHelper.reposition()}fixIndex(e){for(const t of this.treeElementMap.values())t.property.index>e&&(t.property.index-=1)}createPropertyValueMap(){const e=new Map;for(const t of this.treeElementMap){const n=t[0],i=t[1];i.property.value.length?e.set(n,i.property.value):this.treeElementMap.delete(n)}return e}registerFontProperty(e){const t=e.property.name;if(this.treeElementMap.has(t)){const n=this.treeElementMap.get(t);(!e.overloaded()||n&&n.overloaded())&&this.treeElementMap.set(t,e)}else this.treeElementMap.set(t,e)}async showPopover(e,t){if(this.swatchPopoverHelper.isShowing())return void this.swatchPopoverHelper.hide(!0);this.parentPane=t;const n=this.createPropertyValueMap();this.fontEditor=new a.FontEditor.FontEditor(n),this.fontEditor.addEventListener(a.FontEditor.Events.FontChanged,this.boundFontChanged),this.fontEditor.addEventListener(a.FontEditor.Events.FontEditorResized,this.boundResized),this.swatchPopoverHelper.show(this.fontEditor,e,this.onPopoverHidden.bind(this)),this.scrollerElement=e.enclosingNodeOrSelfWithClass("style-panes-wrapper"),this.scrollerElement&&this.scrollerElement.addEventListener("scroll",this.boundOnScroll,!1),this.parentPane.setEditingStyle(!0)}onScroll(){this.swatchPopoverHelper.hide(!0)}onPopoverHidden(){this.scrollerElement&&this.scrollerElement.removeEventListener("scroll",this.boundOnScroll,!1),this.section.onpopulate(),this.fontEditor&&this.fontEditor.removeEventListener(a.FontEditor.Events.FontChanged,this.boundFontChanged),this.fontEditor=null,this.parentPane&&this.parentPane.setEditingStyle(!1),this.section.resetToolbars(),this.section.onpopulate()}static treeElementSymbol=Symbol("FontEditorSectionManager._treeElementSymbol")}})),t.register("h7UuJ",(function(n,i){e(n.exports,"decorateNodeLabel",(()=>p)),e(n.exports,"linkifyNodeReference",(()=>u)),e(n.exports,"linkifyDeferredNodeReference",(()=>m)),e(n.exports,"Linkifier",(()=>f));var o=t("koSS8"),s=t("ixFnt"),r=t("eQFvP"),l=t("9z2ZV"),a=t("2kPna");const d={node:"<node>"},c=s.i18n.registerUIStrings("panels/elements/DOMLinkifier.ts",d),h=s.i18n.getLocalizedString.bind(void 0,c),p=function(e,t,n){const i=e,o=e.nodeType()===Node.ELEMENT_NODE&&e.pseudoType();o&&e.parentNode&&(e=e.parentNode);let s=e.nodeNameInCorrectCase();const r=t.createChild("span","node-label-name");r.textContent=s;const a=e.getAttribute("id");if(a){const e=t.createChild("span","node-label-id"),n="#"+a;s+=n,l.UIUtils.createTextChild(e,n),r.classList.add("extra")}const d=e.getAttribute("class");if(d){const e=d.split(/\s+/);if(e.length){const n=new Set,i=t.createChild("span","extra node-label-class");for(let t=0;t<e.length;++t){const o=e[t];if(o&&!n.has(o)){const e="."+o;s+=e,l.UIUtils.createTextChild(i,e),n.add(o)}}}}if(o){const e=t.createChild("span","extra node-label-pseudo"),n="::"+i.pseudoType();l.UIUtils.createTextChild(e,n),s+=n}l.Tooltip.Tooltip.install(t,n||s)},u=function(e,t={tooltip:void 0,preventKeyboardFocus:void 0}){if(!e)return document.createTextNode(h(d.node));const n=document.createElement("span");n.classList.add("monospace");const i=l.Utils.createShadowRootWithCoreStyles(n,{cssFile:[a.default],delegatesFocus:void 0}).createChild("div","node-link");return p(e,i,t.tooltip),i.addEventListener("click",(()=>(o.Revealer.reveal(e,!1),!1)),!1),i.addEventListener("mouseover",e.highlight.bind(e,void 0),!1),i.addEventListener("mouseleave",(()=>r.OverlayModel.OverlayModel.hideDOMNodeHighlight()),!1),t.preventKeyboardFocus||(i.addEventListener("keydown",(t=>"Enter"===t.key&&o.Revealer.reveal(e,!1)&&!1)),i.tabIndex=0,l.ARIAUtils.markAsLink(i)),n},m=function(e,t={tooltip:void 0,preventKeyboardFocus:void 0}){const n=document.createElement("div"),i=l.Utils.createShadowRootWithCoreStyles(n,{cssFile:[a.default],delegatesFocus:void 0}).createChild("div","node-link");function s(e){o.Revealer.reveal(e)}return i.createChild("slot"),i.addEventListener("click",e.resolve.bind(e,s),!1),i.addEventListener("mousedown",(e=>e.consume()),!1),t.preventKeyboardFocus||(i.addEventListener("keydown",(t=>"Enter"===t.key&&e.resolve(s))),i.tabIndex=0,l.ARIAUtils.markAsLink(i)),n};let g;class f{static instance(e={forceNew:null}){const{forceNew:t}=e;return g&&!t||(g=new f),g}linkify(e,t){if(e instanceof r.DOMModel.DOMNode)return u(e,t);if(e instanceof r.DOMModel.DeferredDOMNode)return m(e,t);throw new Error("Can't linkify non-node")}}})),t.register("2kPna",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2018 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  display: inline;\n}\n\n.node-link {\n  cursor: pointer;\n  display: inline;\n  pointer-events: auto;\n}\n\n.node-link:focus-visible {\n  outline-width: unset;\n}\n\n.node-label-name {\n  color: var(--color-syntax-1);\n}\n\n.node-label-class,\n.node-label-pseudo {\n  color: var(--color-syntax-4);\n}\n\n/*# sourceURL=domLinkifier.css */\n");var o=i})),t.register("eVQ8w",(function(n,i){e(n.exports,"ElementsSidebarPane",(()=>l));var o=t("koSS8"),s=t("9z2ZV"),r=t("3LXVO");class l extends s.Widget.VBox{computedStyleModelInternal;updateThrottler;updateWhenVisible;constructor(e){super(!0,e),this.element.classList.add("flex-none"),this.computedStyleModelInternal=new(0,r.ComputedStyleModel),this.computedStyleModelInternal.addEventListener("ComputedStyleChanged",this.onCSSModelChanged,this),this.updateThrottler=new o.Throttler.Throttler(100),this.updateWhenVisible=!1}node(){return this.computedStyleModelInternal.node()}cssModel(){return this.computedStyleModelInternal.cssModel()}computedStyleModel(){return this.computedStyleModelInternal}async doUpdate(){}update(){this.updateWhenVisible=!this.isShowing(),this.updateWhenVisible||this.updateThrottler.schedule(function(){return this.isShowing()?this.doUpdate():Promise.resolve()}.bind(this))}wasShown(){super.wasShown(),this.updateWhenVisible&&this.update()}onCSSModelChanged(e){}}})),t.register("3yaAT",(function(n,i){e(n.exports,"StyleEditorWidget",(()=>a));var o=t("cY3yZ"),s=t("9z2ZV"),r=t("8V7BL");let l=null;class a extends s.Widget.VBox{editor;pane;section;editorContainer;#Ye;constructor(){super(!0),this.contentElement.tabIndex=0,this.setDefaultFocusedElement(this.contentElement),this.editorContainer=document.createElement("div"),this.contentElement.appendChild(this.editorContainer),this.onPropertySelected=this.onPropertySelected.bind(this),this.onPropertyDeselected=this.onPropertyDeselected.bind(this)}getSection(){return this.section}async onPropertySelected(e){if(!this.section)return;const t=d(this.section,e.data.name);t.property.value=e.data.value,t.updateTitle(),await t.applyStyleText(t.renderedPropertyText(),!1),await this.render()}async onPropertyDeselected(e){if(!this.section)return;const t=d(this.section,e.data.name);await t.applyStyleText("",!1),await this.render()}bindContext(e,t){this.pane=e,this.section=t,this.editor?.addEventListener("propertyselected",this.onPropertySelected),this.editor?.addEventListener("propertydeselected",this.onPropertyDeselected)}setTriggerKey(e){this.#Ye=e}getTriggerKey(){return this.#Ye}unbindContext(){this.pane=void 0,this.section=void 0,this.editor?.removeEventListener("propertyselected",this.onPropertySelected),this.editor?.removeEventListener("propertydeselected",this.onPropertyDeselected)}async render(){this.editor&&(this.editor.data={authoredProperties:this.section?h(this.section,this.editor.getEditableProperties()):new Map,computedProperties:this.pane?await c(this.pane):new Map})}static instance(){return l||(l=new a),l}setEditor(e){this.editor instanceof e||(this.contentElement.removeChildren(),this.editor=new e,this.contentElement.appendChild(this.editor))}static createTriggerButton(e,t,n,i,s){const r=function(e){const t=document.createElement("button");t.classList.add("styles-pane-button"),t.tabIndex=0,t.title=e,t.onmouseup=e=>{e.stopPropagation()};const n=new o.Icon.Icon;return n.data={iconName:"flex-wrap-icon",color:"var(--color-text-secondary)",width:"12px",height:"12px"},t.appendChild(n),t}(i);return r.onclick=async i=>{i.stopPropagation();const o=e.swatchPopoverHelper(),l=a.instance();l.setEditor(n),l.bindContext(e,t),l.setTriggerKey(s),await l.render();const d=r.enclosingNodeOrSelfWithClass("style-panes-wrapper"),c=()=>{o.hide(!0)};o.show(l,r,(()=>{l.unbindContext(),d&&d.removeEventListener("scroll",c)})),d&&d.addEventListener("scroll",c)},r}}function d(e,t){const n=e.propertiesTreeOutline.rootElement().children().find((e=>e instanceof r.StylePropertyTreeElement&&e.property.name===t));if(n)return n;const i=e.addNewBlankProperty();return i.property.name=t,i}async function c(e){const t=e.computedStyleModel(),n=await t.fetchComputedStyle();return n?n.computedStyle:new Map}function h(e,t){const n=new Map,i=new Set(t.map((e=>e.propertyName)));for(const t of e.style().leadingProperties())i.has(t.name)&&n.set(t.name,t.value);return n}})),t.register("8V7BL",(function(n,i){e(n.exports,"StylePropertyTreeElement",(()=>N));var o=t("koSS8"),s=t("e7bLS"),r=t("ixFnt"),l=t("lz7WY"),a=t("9X2mn"),d=t("eQFvP"),c=t("fMswD"),h=t("7f6zc"),p=t("XILzK"),u=t("fuChX"),m=t("9z2ZV"),g=t("hqdDa");t("Px6PZ");var f=t("2p7Mx"),y=t("1r4or"),v=t("3yaAT"),b=t("3OgVH"),S=t("dqP5n");const E=f.FlexboxEditor,C=f.GridEditor,x={shiftClickToChangeColorFormat:"Shift + Click to change color format.",openColorPickerS:"Open color picker. {PH1}",valueForSettingSSIsOutsideThe:"Value for setting “{PH1}” {PH2} is outside the supported range [{PH3}, {PH4}] for font-family “{PH5}”.",togglePropertyAndContinueEditing:"Toggle property and continue editing",revealInSourcesPanel:"Reveal in Sources panel",copyDeclaration:"Copy declaration",copyProperty:"Copy property",copyValue:"Copy value",copyRule:"Copy rule",copyAllDeclarations:"Copy all declarations",viewComputedValue:"View computed value",flexboxEditorButton:"Open `flexbox` editor",gridEditorButton:"Open `grid` editor",copyCssDeclarationAsJs:"Copy declaration as JS",copyAllCssDeclarationsAsJs:"Copy all declarations as JS"},w=r.i18n.registerUIStrings("panels/elements/StylePropertyTreeElement.ts",x),T=r.i18n.getLocalizedString.bind(void 0,w),M=new WeakMap;class N extends m.TreeOutline.TreeElement{style;matchedStylesInternal;property;inheritedInternal;overloadedInternal;parentPaneInternal;isShorthand;applyStyleThrottler;newProperty;expandedDueToFilter;valueElement;nameElement;expandElement;originalPropertyText;hasBeenEditedIncrementally;prompt;lastComputedValue;contextForTest;#Je;constructor(e,t,n,i,s,r,l){super("",i),this.style=n.ownerStyle,this.matchedStylesInternal=t,this.property=n,this.inheritedInternal=s,this.overloadedInternal=r,this.selectable=!1,this.parentPaneInternal=e,this.isShorthand=i,this.applyStyleThrottler=new o.Throttler.Throttler(0),this.newProperty=l,this.newProperty&&(this.listItemElement.textContent=""),this.expandedDueToFilter=!1,this.valueElement=null,this.nameElement=null,this.expandElement=null,this.originalPropertyText="",this.hasBeenEditedIncrementally=!1,this.prompt=null,this.lastComputedValue=null,this.#Je=n.propertyText||""}matchedStyles(){return this.matchedStylesInternal}editable(){return Boolean(this.style.styleSheetId&&this.style.range)}inherited(){return this.inheritedInternal}overloaded(){return this.overloadedInternal}setOverloaded(e){e!==this.overloadedInternal&&(this.overloadedInternal=e,this.updateState())}get name(){return this.property.name}get value(){return this.property.value}updateFilter(){const e=this.parentPaneInternal.filterRegex(),t=null!==e&&(e.test(this.property.name)||e.test(this.property.value));this.listItemElement.classList.toggle("filter-match",t),this.onpopulate();let n=!1;for(let e=0;e<this.childCount();++e){const t=this.childAt(e);!t||t&&!t.updateFilter()||(n=!0)}return e?n&&!this.expanded?(this.expand(),this.expandedDueToFilter=!0):!n&&this.expanded&&this.expandedDueToFilter&&(this.collapse(),this.expandedDueToFilter=!1):(this.expandedDueToFilter&&this.collapse(),this.expandedDueToFilter=!1),t}processColor(e,t){const n=this.editable(),i=T(x.shiftClickToChangeColorFormat),o=this.editable()?T(x.openColorPickerS,{PH1:i}):i,s=new u.ColorSwatch.ColorSwatch;if(s.renderColor(e,n,o),!t){t=s.createChild("span");const n=s.getColor();t.textContent=n?n.asString(s.getFormat()):e}s.appendChild(t);return s.addEventListener(u.ColorSwatch.FormatChangedEvent.eventName,(e=>{const{data:t}=e;s.firstElementChild&&s.firstElementChild.remove(),s.createChild("span").textContent=t.text})),this.editable()&&this.addColorContrastInfo(s),s}processVar(e){const t=this.matchedStylesInternal.computeSingleVariableValue(this.style,e);if(!t)return document.createTextNode(e);const{computedValue:n,fromFallback:i}=t,s=new u.CSSVarSwatch.CSSVarSwatch;return m.UIUtils.createTextChild(s,e),s.data={text:e,computedValue:n,fromFallback:i,onLinkActivate:this.handleVarDefinitionActivate.bind(this)},n&&o.Color.Color.parse(n)?this.processColor(n,s):s}handleVarDefinitionActivate(e){s.userMetrics.actionTaken(s.UserMetrics.Action.CustomPropertyLinkClicked),this.parentPaneInternal.jumpToProperty(e)}async addColorContrastInfo(e){const t=this.parentPaneInternal.swatchPopoverHelper(),n=new(0,g.ColorSwatchPopoverIcon)(this,t,e);if("color"!==this.property.name||!this.parentPaneInternal.cssModel()||!this.node())return;const i=this.parentPaneInternal.cssModel(),o=this.node();if(i&&o&&void 0!==o.id){const e=new p.ContrastInfo.ContrastInfo(await i.getBackgroundColors(o.id));n.setContrastInfo(e)}}renderedPropertyText(){return this.nameElement&&this.valueElement?this.nameElement.textContent+": "+this.valueElement.textContent:""}processBezier(e){if(!this.editable()||!m.Geometry.CubicBezier.parse(e))return document.createTextNode(e);const t=this.parentPaneInternal.swatchPopoverHelper(),n=u.Swatches.BezierSwatch.create();return n.setBezierText(e),new(0,g.BezierPopoverIcon)(this,t,n),n}processFont(e){const t=this.section();return t&&t.registerFontProperty(this),document.createTextNode(e)}processShadow(e,t){if(!this.editable())return document.createTextNode(e);let n;if(n="text-shadow"===t?u.CSSShadowModel.CSSShadowModel.parseTextShadow(e):u.CSSShadowModel.CSSShadowModel.parseBoxShadow(e),!n.length)return document.createTextNode(e);const i=document.createDocumentFragment(),o=this.parentPaneInternal.swatchPopoverHelper();for(let e=0;e<n.length;e++){0!==e&&i.appendChild(document.createTextNode(", "));const t=u.Swatches.CSSShadowSwatch.create();t.setCSSShadow(n[e]),new(0,g.ShadowSwatchPopoverHelper)(this,o,t);const s=t.colorSwatch();s&&new(0,g.ColorSwatchPopoverIcon)(this,o,s),i.appendChild(t)}return i}processGrid(e,t){const n=h.TextUtils.Utils.splitStringByRegexes(e,[d.CSSMetadata.GridAreaRowRegex]);if(n.length<=1)return document.createTextNode(e);const i=o.Settings.Settings.instance().moduleSetting("textEditorIndent").get(),s=document.createDocumentFragment();for(const e of n){const t=e.value.trim(),n=m.Fragment.html`<br /><span class='styles-clipboard-only'>${i.repeat(2)}</span>${t}`;s.appendChild(n)}return s}processAngle(e){if(!this.editable())return document.createTextNode(e);const t=new u.CSSAngle.CSSAngle,n=document.createElement("span");n.textContent=e;const i=this.matchedStylesInternal.computeValue(this.property.ownerStyle,this.property.value)||"";t.data={propertyName:this.property.name,propertyValue:i,angleText:e,containingPane:this.parentPaneInternal.element.enclosingNodeOrSelfWithClass("style-panes-wrapper")},t.append(n);return t.addEventListener("popovertoggled",(e=>{const n=this.section();if(!n)return;const{data:i}=e;i.open&&(this.parentPaneInternal.hideAllPopovers(),this.parentPaneInternal.activeCSSAngle=t),n.element.classList.toggle("has-open-popover",i.open),this.parentPaneInternal.setEditingStyle(i.open)})),t.addEventListener("valuechanged",(async e=>{const{data:i}=e;n.textContent=i.value,await this.applyStyleText(this.renderedPropertyText(),!1);const o=this.matchedStylesInternal.computeValue(this.property.ownerStyle,this.property.value)||"";t.updateProperty(this.property.name,o)})),t.addEventListener("unitchanged",(async e=>{const{data:t}=e;n.textContent=t.value})),t}processLength(e){if(!this.editable())return document.createTextNode(e);const t=new u.CSSLength.CSSLength,n=document.createElement("span");n.textContent=e,t.data={lengthText:e},t.append(n);return t.addEventListener("valuechanged",(e=>{const{data:t}=e;n.textContent=t.value,this.parentPaneInternal.setEditingStyle(!0),this.applyStyleText(this.renderedPropertyText(),!1)})),t.addEventListener("draggingfinished",(()=>{this.parentPaneInternal.setEditingStyle(!1)})),t}updateState(){if(!this.listItemElement)return;this.style.isPropertyImplicit(this.name)?this.listItemElement.classList.add("implicit"):this.listItemElement.classList.remove("implicit");!this.property.parsedOk&&b.StylesSidebarPane.ignoreErrorsForProperty(this.property)?this.listItemElement.classList.add("has-ignorable-error"):this.listItemElement.classList.remove("has-ignorable-error"),this.inherited()?this.listItemElement.classList.add("inherited"):this.listItemElement.classList.remove("inherited"),this.overloaded()?this.listItemElement.classList.add("overloaded"):this.listItemElement.classList.remove("overloaded"),this.property.disabled?this.listItemElement.classList.add("disabled"):this.listItemElement.classList.remove("disabled"),this.listItemElement.classList.toggle("changed",this.isPropertyChanged(this.property))}node(){return this.parentPaneInternal.node()}parentPane(){return this.parentPaneInternal}section(){return this.treeOutline?this.treeOutline.section:null}updatePane(){const e=this.section();e&&e.refreshUpdate(this)}async toggleDisabled(e){if(!this.style.range)return;this.parentPaneInternal.setUserOperation(!0);const t=await this.property.setDisabled(e);this.parentPaneInternal.setUserOperation(!1),t&&(this.matchedStylesInternal.resetActiveProperties(),this.updatePane(),this.styleTextAppliedForTest())}isPropertyChanged(e){return!!a.Runtime.experiments.isEnabled(a.Runtime.ExperimentName.STYLES_PANE_CSS_CHANGES)&&(this.#Je!==e.propertyText||this.parentPane().isPropertyChanged(e))}async onpopulate(){if(this.childCount()||!this.isShorthand)return;const e=this.style.longhandProperties(this.name),t=this.style.leadingProperties();for(let n=0;n<e.length;++n){const i=e[n].name;let o=!1,s=!1;const r=this.section();r&&(o=r.isPropertyInherited(i),s=this.matchedStylesInternal.propertyState(e[n])===d.CSSMatchedStyles.PropertyState.Overloaded);t.find((e=>e.name===i&&e.activeInStyle()))&&(s=!0);const l=new N(this.parentPaneInternal,this.matchedStylesInternal,e[n],!1,o,s,!1);this.appendChild(l)}}onattach(){this.updateTitle(),this.listItemElement.addEventListener("mousedown",(e=>{0===e.button&&M.set(this.parentPaneInternal,this)}),!1),this.listItemElement.addEventListener("mouseup",this.mouseUp.bind(this)),this.listItemElement.addEventListener("click",(e=>{if(!e.target)return;e.target.hasSelection()||e.target===this.listItemElement||e.consume(!0)})),this.listItemElement.addEventListener("contextmenu",this.handleCopyContextMenuEvent.bind(this))}onexpand(){this.updateExpandElement()}oncollapse(){this.updateExpandElement()}updateExpandElement(){this.expandElement&&(this.expanded?this.expandElement.setIconType("smallicon-triangle-down"):this.expandElement.setIconType("smallicon-triangle-right"))}updateTitleIfComputedValueChanged(){const e=this.matchedStylesInternal.computeValue(this.property.ownerStyle,this.property.value);e!==this.lastComputedValue&&(this.lastComputedValue=e,this.innerUpdateTitle())}updateTitle(){this.lastComputedValue=this.matchedStylesInternal.computeValue(this.property.ownerStyle,this.property.value),this.innerUpdateTitle()}innerUpdateTitle(){this.updateState(),this.isExpandable()?this.expandElement=m.Icon.Icon.create("smallicon-triangle-right","expand-icon"):this.expandElement=null;const e=new(0,b.StylesSidebarPropertyRenderer)(this.style.parentRule,this.node(),this.name,this.value);if(this.property.parsedOk&&(e.setVarHandler(this.processVar.bind(this)),e.setColorHandler(this.processColor.bind(this)),e.setBezierHandler(this.processBezier.bind(this)),e.setFontHandler(this.processFont.bind(this)),e.setShadowHandler(this.processShadow.bind(this)),e.setGridHandler(this.processGrid.bind(this)),e.setAngleHandler(this.processAngle.bind(this)),e.setLengthHandler(this.processLength.bind(this))),this.listItemElement.removeChildren(),this.nameElement=e.renderName(),this.property.name.startsWith("--")&&this.nameElement&&m.Tooltip.Tooltip.install(this.nameElement,this.matchedStylesInternal.computeCSSVariable(this.style,this.property.name)||""),this.valueElement=e.renderValue(),!this.treeOutline)return;const t=o.Settings.Settings.instance().moduleSetting("textEditorIndent").get();if(m.UIUtils.createTextChild(this.listItemElement.createChild("span","styles-clipboard-only"),t+(this.property.disabled?"/* ":"")),this.nameElement&&this.listItemElement.appendChild(this.nameElement),this.valueElement){const e=this.valueElement.firstElementChild&&"BR"===this.valueElement.firstElementChild.tagName?":":": ";this.listItemElement.createChild("span","styles-name-value-separator").textContent=e,this.expandElement&&this.listItemElement.appendChild(this.expandElement),this.listItemElement.appendChild(this.valueElement),m.UIUtils.createTextChild(this.listItemElement,";"),this.property.disabled&&m.UIUtils.createTextChild(this.listItemElement.createChild("span","styles-clipboard-only")," */")}const n=this.section();if(this.valueElement&&n&&n.editable&&"display"===this.property.name){const e=this.property.trimmedValueWithoutImportant(),t="flex"===e||"inline-flex"===e,i="grid"===e||"inline-grid"===e;if(t||i){const e=`${n.getSectionIdx()}_${n.nextEditorTriggerButtonIdx}`,i=v.StyleEditorWidget.createTriggerButton(this.parentPaneInternal,n,t?E:C,T(t?x.flexboxEditorButton:x.gridEditorButton),e);n.nextEditorTriggerButtonIdx++,this.listItemElement.appendChild(i);const o=this.parentPaneInternal.swatchPopoverHelper();o.isShowing(v.StyleEditorWidget.instance())&&v.StyleEditorWidget.instance().getTriggerKey()===e&&o.setAnchorElement(i)}}if(this.property.parsedOk?this.updateFontVariationSettingsWarning():(this.listItemElement.classList.add("not-parsed-ok"),this.listItemElement.insertBefore(b.StylesSidebarPane.createExclamationMark(this.property,null),this.listItemElement.firstChild)),this.property.activeInStyle()||this.listItemElement.classList.add("inactive"),this.updateFilter(),this.property.parsedOk&&this.section()&&this.parent&&this.parent.root){const e=document.createElement("input");e.className="enabled-button",e.type="checkbox",e.checked=!this.property.disabled,e.addEventListener("mousedown",(e=>e.consume()),!1),e.addEventListener("click",(e=>{this.toggleDisabled(!this.property.disabled),e.consume()}),!1),this.nameElement&&this.valueElement&&m.ARIAUtils.setAccessibleName(e,`${this.nameElement.textContent} ${this.valueElement.textContent}`);const t=m.Icon.Icon.create("largeicon-copy","copy");m.Tooltip.Tooltip.install(t,T(x.copyDeclaration)),t.addEventListener("click",(()=>{const e=`${this.property.name}: ${this.property.value};`;s.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e),s.userMetrics.styleTextCopied(s.UserMetrics.StyleTextCopied.DeclarationViaChangedLine)})),this.listItemElement.append(t),this.listItemElement.insertBefore(e,this.listItemElement.firstChild)}}async updateFontVariationSettingsWarning(){if("font-variation-settings"!==this.property.name)return;const e=this.property.value,t=this.parentPaneInternal.cssModel();if(!t)return;const n=this.parentPaneInternal.computedStyleModel(),i=await n.fetchComputedStyle();if(!i)return;const o=i.computedStyle.get("font-family");if(!o)return;const s=new Set(d.CSSPropertyParser.parseFontFamily(o)),r=t.fontFaces().filter((e=>s.has(e.getFontFamily()))),l=d.CSSPropertyParser.parseFontVariationSettings(e),a=[];for(const e of l)for(const t of r){const n=t.getVariationAxisByTag(e.tag);n&&((e.value<n.minValue||e.value>n.maxValue)&&a.push(T(x.valueForSettingSSIsOutsideThe,{PH1:e.tag,PH2:e.value,PH3:n.minValue,PH4:n.maxValue,PH5:t.getFontFamily()})))}a.length&&(this.listItemElement.classList.add("has-warning"),this.listItemElement.insertBefore(b.StylesSidebarPane.createExclamationMark(this.property,a.join(" ")),this.listItemElement.firstChild))}mouseUp(e){const t=M.get(this.parentPaneInternal);if(M.delete(this.parentPaneInternal),!t)return;if(this.listItemElement.hasSelection())return;if(m.UIUtils.isBeingEdited(e.target))return;if(e.consume(!0),e.target===this.listItemElement)return;const n=this.section();m.KeyboardShortcut.KeyboardShortcut.eventHasCtrlEquivalentKey(e)&&n&&n.navigable?this.navigateToSource(e.target):this.startEditing(e.target)}handleContextMenuEvent(e,t){const n=new m.ContextMenu.ContextMenu(t);if(this.property.parsedOk&&this.section()&&this.parent&&this.parent.root){const i=this.parentPaneInternal.focusedSectionIndex();n.defaultSection().appendCheckboxItem(T(x.togglePropertyAndContinueEditing),(async()=>{if(this.treeOutline){const n=this.treeOutline.rootElement().indexOfChild(this);this.editingCancelled(null,e),await this.toggleDisabled(!this.property.disabled),t.consume(),this.parentPaneInternal.continueEditingElement(i,n)}}),!this.property.disabled)}const i=this.navigateToSource.bind(this);n.defaultSection().appendItem(T(x.revealInSourcesPanel),i),n.show()}handleCopyContextMenuEvent(e){if(!e.target)return;const t=new m.ContextMenu.ContextMenu(e);t.clipboardSection().appendItem(T(x.copyDeclaration),(()=>{const e=`${this.property.name}: ${this.property.value};`;s.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e),s.userMetrics.styleTextCopied(s.UserMetrics.StyleTextCopied.DeclarationViaContextMenu)})),t.clipboardSection().appendItem(T(x.copyProperty),(()=>{s.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(this.property.name),s.userMetrics.styleTextCopied(s.UserMetrics.StyleTextCopied.PropertyViaContextMenu)})),t.clipboardSection().appendItem(T(x.copyValue),(()=>{s.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(this.property.value),s.userMetrics.styleTextCopied(s.UserMetrics.StyleTextCopied.ValueViaContextMenu)})),t.defaultSection().appendItem(T(x.copyRule),(()=>{const e=this.section(),t=b.StylesSidebarPane.formatLeadingProperties(e).ruleText;s.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(t),s.userMetrics.styleTextCopied(s.UserMetrics.StyleTextCopied.RuleViaContextMenu)})),t.defaultSection().appendItem(T(x.copyAllDeclarations),(()=>{const e=this.section(),t=b.StylesSidebarPane.formatLeadingProperties(e).allDeclarationText;s.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(t),s.userMetrics.styleTextCopied(s.UserMetrics.StyleTextCopied.AllDeclarationsViaContextMenu)})),t.defaultSection().appendItem(T(x.viewComputedValue),(()=>{this.viewComputedValue()})),t.clipboardSection().appendItem(T(x.copyCssDeclarationAsJs),this.copyCssDeclarationAsJs.bind(this)),t.defaultSection().appendItem(T(x.copyAllCssDeclarationsAsJs),this.copyAllCssDeclarationAsJs.bind(this)),t.show()}async viewComputedValue(){const e=y.ElementsPanel.instance().getComputedStyleWidget();e.isShowing()||await m.ViewManager.ViewManager.instance().showView("Computed");let t="";t=this.isShorthand?"^"+this.property.name+"-":"^"+this.property.name+"$";const n=new RegExp(t,"i");e.filterComputedStyles(n);const i=e.input;i.value=this.property.name,i.focus()}copyCssDeclarationAsJs(){const e=(0,S.getCssDeclarationAsJavascriptProperty)(this.property);s.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e),s.userMetrics.styleTextCopied(s.UserMetrics.StyleTextCopied.DeclarationAsJSViaContextMenu)}copyAllCssDeclarationAsJs(){const e=this.section().style().leadingProperties().filter((e=>!e.disabled)).map(S.getCssDeclarationAsJavascriptProperty);s.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e.join(",\n")),s.userMetrics.styleTextCopied(s.UserMetrics.StyleTextCopied.AllDeclarationsAsJSViaContextMenu)}navigateToSource(e,t){const n=this.section();if(!n||!n.navigable)return;const i=e===this.nameElement,s=c.CSSWorkspaceBinding.CSSWorkspaceBinding.instance().propertyUILocation(this.property,i);s&&o.Revealer.reveal(s,t)}startEditing(e){if(this.parent instanceof N&&this.parent.isShorthand)return;if(this.expandElement&&e===this.expandElement)return;const t=this.section();if(t&&!t.editable)return;if(e&&(e=e.enclosingNodeOrSelfWithClass("webkit-css-property")||e.enclosingNodeOrSelfWithClass("value")),e||(e=this.nameElement),m.UIUtils.isBeingEdited(e))return;const n=e===this.nameElement;var i;!n&&this.valueElement&&(d.CSSMetadata.cssMetadata().isGridAreaDefiningProperty(this.name)&&(this.valueElement.textContent=(i=this.value,h.TextUtils.Utils.splitStringByRegexes(i,[d.CSSMetadata.GridAreaRowRegex]).map((e=>e.value.trim())).join("\n"))),this.valueElement.textContent=function(e,t){const n=e.split(d.CSSMetadata.URLRegex);if(1===n.length)return e;const i=new RegExp(d.CSSMetadata.URLRegex);for(let e=1;e<n.length;e+=2){const o=i.exec(t);o&&(n[e]=o[0])}return n.join("")}(this.valueElement.textContent||"",this.value));const o=e&&e.textContent||"",s={expanded:this.expanded,hasChildren:this.isExpandable(),isEditingName:n,originalProperty:this.property,previousContent:o,originalName:void 0,originalValue:void 0};this.contextForTest=s,this.setExpandable(!1),e&&(e.parentElement&&e.parentElement.classList.add("child-editing"),e.textContent=e.textContent),this.originalPropertyText=this.property.propertyText||"",this.parentPaneInternal.setEditingStyle(!0,this),e&&e.parentElement&&e.parentElement.scrollIntoViewIfNeeded(!1),this.prompt=new(0,b.CSSPropertyPrompt)(this,n),this.prompt.setAutocompletionTimeout(0),this.prompt.addEventListener(m.TextPrompt.Events.TextChanged,(e=>{this.applyFreeFlowStyleTextEdit(s)}));const r=this.property.getInvalidStringForInvalidProperty();if(r&&e&&m.ARIAUtils.alert(r),e){const t=this.prompt.attachAndStartEditing(e,function(e,t){let n=t.target.textContent;e.isEditingName||(n=this.value||n),this.editingCommitted(n||"",e,"")}.bind(this,s));this.navigateToSource(e,!0),t.addEventListener("keydown",this.editingNameValueKeyDown.bind(this,s),!1),t.addEventListener("keypress",this.editingNameValueKeyPress.bind(this,s),!1),n&&(t.addEventListener("paste",function(e,t){const n=t.clipboardData;if(!n)return;const i=n.getData("Text");if(!i)return;const o=i.indexOf(":");if(o<0)return;const s=i.substring(0,o).trim(),r=i.substring(o+1).trim();t.preventDefault(),void 0===e.originalName&&(this.nameElement&&(e.originalName=this.nameElement.textContent||""),this.valueElement&&(e.originalValue=this.valueElement.textContent||"")),this.property.name=s,this.property.value=r,this.nameElement&&(this.nameElement.textContent=s,this.nameElement.normalize()),this.valueElement&&(this.valueElement.textContent=r,this.valueElement.normalize());const l=t.target;this.editingCommitted(l.textContent||"",e,"forward")}.bind(this,s),!1),t.addEventListener("contextmenu",this.handleContextMenuEvent.bind(this,s),!1));const i=e.getComponentSelection();i&&i.selectAllChildren(e)}}editingNameValueKeyDown(e,t){if(t.handled)return;const n=t,i=n.target;let o;if("Enter"!==n.key||n.shiftKey)if(n.keyCode===m.KeyboardShortcut.Keys.Esc.code||n.key===l.KeyboardUtilities.ESCAPE_KEY)o="cancel";else if(!e.isEditingName&&this.newProperty&&n.keyCode===m.KeyboardShortcut.Keys.Backspace.code){const e=i.getComponentSelection();e&&e.isCollapsed&&!e.focusOffset&&(t.preventDefault(),o="backward")}else"Tab"===n.key&&(o=n.shiftKey?"backward":"forward",t.preventDefault());else o="forward";if(o){switch(o){case"cancel":this.editingCancelled(null,e);break;case"forward":case"backward":this.editingCommitted(i.textContent||"",e,o)}t.consume()}else;}editingNameValueKeyPress(e,t){const n=t,i=n.target,o=String.fromCharCode(n.charCode),s=i.selectionLeftOffset();if(e.isEditingName?":"===o:";"===o&&null!==s&&function(e,t){let n="";for(let i=0;i<t;++i){const t=e[i];"\\"===t&&""!==n?++i:n||'"'!==t&&"'"!==t?n===t&&(n=""):n=t}return!n}(i.textContent||"",s))return t.consume(!0),void this.editingCommitted(i.textContent||"",e,"forward")}async applyFreeFlowStyleTextEdit(e){if(!this.prompt||!this.parentPaneInternal.node())return;const t=this.prompt.text();if(e.isEditingName&&t.includes(":"))return void this.editingCommitted(t,e,"forward");const n=this.prompt.textWithCurrentSuggestion();if(n.includes(";"))return;const i=this.parentPaneInternal.node();if(i){if(Boolean(i.pseudoType())){if("content"===this.name.toLowerCase())return;const e=n.trim().toLowerCase();if(e.startsWith("content:")||"display: none"===e)return}}e.isEditingName?n.includes(":")?await this.applyStyleText(n,!1):this.hasBeenEditedIncrementally&&await this.applyOriginalStyle(e):this.nameElement&&await this.applyStyleText(`${this.nameElement.textContent}: ${n}`,!1)}kickFreeFlowStyleEditForTest(){const e=this.contextForTest;return this.applyFreeFlowStyleTextEdit(e)}editingEnded(e){this.setExpandable(e.hasChildren),e.expanded&&this.expand();const t=e.isEditingName?this.nameElement:this.valueElement;t&&t.parentElement&&t.parentElement.classList.remove("child-editing"),this.parentPaneInternal.setEditingStyle(!1)}editingCancelled(e,t){this.removePrompt(),this.hasBeenEditedIncrementally?this.applyOriginalStyle(t):this.newProperty&&this.treeOutline&&this.treeOutline.removeChild(this),this.updateTitle(),this.editingEnded(t)}async applyOriginalStyle(e){await this.applyStyleText(this.originalPropertyText,!1,e.originalProperty)}findSibling(e){let t=this;do{const n="forward"===e?t.nextSibling:t.previousSibling;t=n instanceof N?n:null}while(t&&t.inherited());return t}async editingCommitted(e,t,n){this.removePrompt(),this.editingEnded(t);const i=t.isEditingName;if(!this.nameElement||!this.valueElement)return;const o=this.nameElement.textContent||"",s=i&&o.includes(":")||!this.property;let r=!1,a=!1;const d=void 0!==t.originalName,c=d&&(this.nameElement.textContent!==t.originalName||this.valueElement.textContent!==t.originalValue),h=d&&i&&this.valueElement.textContent!==t.originalValue;let p=this;const u=i!==("forward"===n),m=this.newProperty&&!e&&(u||i);("forward"===n&&(!i||h)||"backward"===n&&i)&&(p=p.findSibling(n),p||("forward"!==n||this.newProperty&&!e?"backward"===n&&(a=!0):r=!0));let g=-1;null!==p&&this.treeOutline&&(g=this.treeOutline.rootElement().indexOfChild(p));const f=l.StringUtilities.isWhitespace(e),y=this.newProperty&&(h||u||!n&&!i||i&&f||s),v=this.section();if((e!==t.previousContent||c)&&!this.newProperty||y){let t;t=s?this.nameElement.textContent:f||this.newProperty&&l.StringUtilities.isWhitespace(this.valueElement.textContent||"")?"":i?e+": "+this.property.value:this.property.name+": "+e,await this.applyStyleText(t||"",!0),b.call(this,this.newProperty,!f,v)}else i?this.property.name=e:this.property.value=e,d||this.newProperty||this.updateTitle(),b.call(this,this.newProperty,!1,v);function b(e,t,o){if(n)if(p&&p.parent)p.startEditing(i?p.valueElement:p.nameElement);else{if(p&&!p.parent){const t=o.propertiesTreeOutline.rootElement();if("forward"===n&&f&&!i&&--g,g>=t.childCount()&&!this.newProperty)r=!0;else{const o=g>=0?t.childAt(g):null;if(o){let t=!i||h?o.nameElement:o.valueElement;return e&&f&&(t="forward"===n?o.nameElement:o.valueElement),void o.startEditing(t)}e||(a=!0)}}if(r){if(e&&!t&&i!==("backward"===n))return;o.addNewBlankProperty().startEditing()}else if(m){p=this.findSibling(n);const e=p||"backward"===n?o:o.nextEditableSibling();e&&(e.style().parentRule?e.startEditingSelector():e.moveEditorFromSelector(n))}else a&&(o.style().parentRule?o.startEditingSelector():o.moveEditorFromSelector(n))}else this.parentPaneInternal.resetFocus()}}removePrompt(){this.prompt&&(this.prompt.detach(),this.prompt=null)}styleTextAppliedForTest(){}applyStyleText(e,t,n){return this.applyStyleThrottler.schedule(this.innerApplyStyleText.bind(this,e,t,n))}async innerApplyStyleText(e,t,n){if(!this.treeOutline||!this.property)return;if(!this.style.range)return;const i=this.hasBeenEditedIncrementally;if(!(e=e.replace(/[\xA0\t]/g," ").trim()).length&&t&&this.newProperty&&!i)return void(this.parent&&this.parent.removeChild(this));const o=this.parentPaneInternal.node();this.parentPaneInternal.setUserOperation(!0),(e+=l.StringUtilities.findUnclosedCssQuote(e)).length&&!/;\s*$/.test(e)&&(e+=";");const s=!this.newProperty||i;let r=await this.property.setText(e,t,s);i&&t&&!r&&(t=!1,r=await this.property.setText(this.originalPropertyText,t,s)),this.parentPaneInternal.setUserOperation(!1);const a=n||this.style.propertyAt(this.property.index),d=this.property.index<this.style.allProperties().length;if(!r||!a&&d)return t&&(this.newProperty?this.treeOutline.removeChild(this):this.updateTitle()),void this.styleTextAppliedForTest();a&&this.listItemElement.classList.toggle("changed",this.isPropertyChanged(a)),this.matchedStylesInternal.resetActiveProperties(),this.hasBeenEditedIncrementally=!0;const c=t&&!e.length,h=this.section();c&&h?h.resetToolbars():!c&&a&&(this.property=a),o===this.node()&&this.updatePane(),this.styleTextAppliedForTest()}ondblclick(){return!0}isEventWithinDisclosureTriangle(e){return e.target===this.expandElement}}})),t.register("dqP5n",(function(t,n){function i(e){const{name:t,value:n}=e;return`${t.startsWith("--")?`'${t}'`:t.replace(/-([a-z])/gi,((e,t)=>t.toUpperCase()))}: ${`'${n.replaceAll("'","\\'")}'`}`}e(t.exports,"getCssDeclarationAsJavascriptProperty",(()=>i))})),t.register("41s9g",(function(n,i){e(n.exports,"StylePropertyHighlighter",(()=>s));var o=t("8V7BL");class s{styleSidebarPane;constructor(e){this.styleSidebarPane=e}highlightProperty(e){for(const e of this.styleSidebarPane.allSections())for(let t=e.propertiesTreeOutline.firstChild();t;t=t.nextSibling)t.onpopulate();const{treeElement:t,section:n}=this.findTreeElementAndSection((t=>t.property===e));t&&(t.parent&&t.parent.expand(),this.scrollAndHighlightTreeElement(t),n&&n.element.focus())}findAndHighlightPropertyName(e){for(const t of this.styleSidebarPane.allSections()){if(!t.style().hasActiveProperty(e))continue;t.showAllItems();const n=this.findTreeElementFromSection((t=>t.property.name===e&&!t.overloaded()),t);if(n)return this.scrollAndHighlightTreeElement(n),void(t&&t.element.focus())}}findTreeElementAndSection(e){for(const t of this.styleSidebarPane.allSections()){const n=this.findTreeElementFromSection(e,t);if(n)return{treeElement:n,section:t}}return{treeElement:null,section:null}}findTreeElementFromSection(e,t){let n=t.propertiesTreeOutline.firstChild();for(;n&&n instanceof o.StylePropertyTreeElement;){if(e(n))return n;n=n.traverseNextTreeElement(!1,null,!0)}return null}scrollAndHighlightTreeElement(e){e.listItemElement.scrollIntoViewIfNeeded(),e.listItemElement.animate([{offset:0,backgroundColor:"rgba(255, 255, 0, 0.2)"},{offset:.1,backgroundColor:"rgba(255, 255, 0, 0.7)"},{offset:1,backgroundColor:"transparent"}],{duration:2e3,easing:"cubic-bezier(0, 0, 0.2, 1)"})}}})),t.register("YxxPp",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync('/*\n * Copyright 2016 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.tree-outline {\n  padding: 0;\n}\n\n.tree-outline li.has-warning,\n.tree-outline li.not-parsed-ok {\n  margin-left: 0;\n}\n\n.tree-outline li.filter-match {\n  background-color: var(--color-match-highlight);\n}\n\n.tree-outline li.has-warning .exclamation-mark,\n.tree-outline li.not-parsed-ok .exclamation-mark {\n  display: inline-block;\n  position: relative;\n  width: 11px;\n  height: 10px;\n  margin: 0 7px 0 0;\n  top: 1px;\n  left: -36px; /* outdent to compensate for the top-level property indent */\n  user-select: none;\n  cursor: default;\n  z-index: 1;\n}\n\n.tree-outline li {\n  margin-left: 12px;\n  padding-left: 22px;\n  white-space: normal;\n  text-overflow: ellipsis;\n  cursor: auto;\n  display: block;\n}\n\n.tree-outline li::before {\n  display: none;\n}\n\n.has-ignorable-error .webkit-css-property {\n  color: inherit;\n}\n\n.tree-outline li .webkit-css-property {\n  margin-left: -22px; /* outdent the first line of longhand properties (in an expanded shorthand) to compensate for the "padding-left" shift in .tree-outline li */\n}\n\n.tree-outline > li {\n  padding-left: 38px;\n  clear: both;\n  min-height: 14px;\n}\n\n.tree-outline > li .webkit-css-property {\n  margin-left: -38px; /* outdent the first line of the top-level properties to compensate for the "padding-left" shift in .tree-outline > li */\n}\n\n.tree-outline > li.child-editing {\n  padding-left: 8px;\n}\n\n.tree-outline > li.child-editing .text-prompt {\n  white-space: pre-wrap;\n}\n\n.tree-outline > li.child-editing .webkit-css-property {\n  margin-left: 0;\n}\n\n.tree-outline li.child-editing {\n  word-wrap: break-word !important; /* stylelint-disable-line declaration-no-important */\n  white-space: normal !important; /* stylelint-disable-line declaration-no-important */\n  padding-left: 0;\n}\n\nol:not(.tree-outline) {\n  display: none;\n  margin: 0;\n  padding-inline-start: 12px;\n  list-style: none;\n}\n\nol.expanded {\n  display: block;\n}\n\n.tree-outline li .info {\n  padding-top: 4px;\n  padding-bottom: 3px;\n}\n\n.enabled-button {\n  visibility: hidden;\n  float: left;\n  font-size: 10px;\n  margin: 0;\n  vertical-align: top;\n  position: relative;\n  z-index: 1;\n  width: 18px;\n  left: -40px; /* original -2px + (-38px) to compensate for the first line outdent */\n  top: 1px;\n  height: 13px;\n}\n\n.tree-outline li.editing .enabled-button {\n  display: none !important; /* stylelint-disable-line declaration-no-important */\n}\n\n.overloaded:not(.has-ignorable-error),\n.inactive,\n.disabled,\n.not-parsed-ok:not(.has-ignorable-error) {\n  text-decoration: line-through;\n}\n\n.implicit,\n.inherited {\n  opacity: 50%;\n}\n\n.changed::after {\n  content: "";\n  position: absolute;\n  left: -4px;\n  width: 2px;\n  height: 100%;\n  background-color: var(--color-accent-green);\n}\n\n.copy {\n  display: none;\n}\n\n.changed:hover {\n  background-color: var(--color-accent-green-background);\n}\n\n.changed:hover .copy {\n  position: absolute;\n  right: -4px;\n  top: 0;\n  bottom: 0;\n  margin: auto;\n  display: inline-block;\n  cursor: pointer;\n  transform: scale(0.9);\n}\n\n.has-ignorable-error {\n  color: var(--color-text-disabled);\n}\n\n.tree-outline li.editing {\n  margin-left: 10px;\n  text-overflow: clip;\n}\n\n.tree-outline li.editing-sub-part {\n  padding: 3px 6px 8px 18px;\n  margin: -1px -6px -8px;\n  text-overflow: clip;\n}\n\n:host-context(.no-affect) .tree-outline li {\n  opacity: 50%;\n}\n\n:host-context(.no-affect) .tree-outline li.editing {\n  opacity: 100%;\n}\n\n:host-context(.styles-panel-hovered:not(.read-only)) .webkit-css-property:hover,\n:host-context(.styles-panel-hovered:not(.read-only)) .value:hover {\n  text-decoration: underline;\n  cursor: default;\n}\n\n.styles-name-value-separator {\n  display: inline-block;\n  width: 14px;\n  text-decoration: inherit;\n  white-space: pre;\n}\n\n.styles-clipboard-only {\n  display: inline-block;\n  width: 0;\n  opacity: 0%;\n  pointer-events: none;\n  white-space: pre;\n}\n\n.styles-pane-button {\n  width: 15px;\n  height: 15px;\n  padding: 0;\n  border: 0;\n  margin: 0 0 0 6px;\n  position: absolute;\n  top: -1px;\n  background-color: var(--color-background-elevation-2);\n  border-radius: 3px;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n}\n\n.tree-outline li.child-editing .styles-clipboard-only {\n  display: none;\n}\n/* Matched styles */\n\n:host-context(.matched-styles) .tree-outline li {\n  margin-left: 0 !important; /* stylelint-disable-line declaration-no-important */\n}\n\n.expand-icon {\n  user-select: none;\n  margin-left: -6px;\n  margin-right: 2px;\n  margin-bottom: -2px;\n}\n\n.tree-outline li:not(.parent) .expand-icon {\n  display: none;\n}\n\n:host-context(.matched-styles:not(.read-only):hover) .enabled-button {\n  visibility: visible;\n}\n\n:host-context(.matched-styles:not(.read-only)) .tree-outline li.disabled .enabled-button {\n  visibility: visible;\n}\n\n:host-context(.matched-styles) ol.expanded {\n  margin-left: 16px;\n}\n\n.devtools-link-styled-trim {\n  display: inline-block;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  max-width: 80%;\n  vertical-align: bottom;\n}\n\ndevtools-css-angle,\ndevtools-css-length {\n  display: inline-block;\n}\n\n/*# sourceURL=stylesSectionTree.css */\n');var o=i})),t.register("LE9n4",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync("/**\n * Copyright 2017 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.styles-section {\n  min-height: 18px;\n  white-space: nowrap;\n  user-select: text;\n  border-bottom: 1px solid var(--color-details-hairline);\n  position: relative;\n  overflow: hidden;\n}\n\n.styles-section > div {\n  padding: 2px 2px 4px 4px;\n}\n\n.styles-section:last-child {\n  border-bottom: none;\n}\n\n.styles-section.read-only {\n  background-color: var(--color-background-opacity-50);\n  font-style: italic;\n}\n\n.styles-section.has-open-popover {\n  z-index: 1;\n}\n\n.styles-section:focus-visible {\n  background-color: var(--color-background-elevation-2);\n}\n\n.styles-section.read-only:focus-visible {\n  background-color: var(--color-background-elevation-2);\n}\n\n.styles-section .simple-selector.filter-match {\n  background-color: var(--color-match-highlight);\n  color: var(--color-text-primary);\n}\n\n.sidebar-pane-closing-brace {\n  clear: both;\n}\n\n.styles-section-title {\n  background-origin: padding;\n  background-clip: padding;\n  word-wrap: break-word;\n  white-space: normal;\n}\n\n.styles-section-title .query-list {\n  color: var(--color-text-disabled);\n}\n\n.styles-section-subtitle {\n  color: var(--color-text-secondary);\n  float: right;\n  padding-left: 15px;\n  max-width: 100%;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n  height: 15px;\n  margin-bottom: -1px;\n}\n\n.sidebar-pane-open-brace,\n.sidebar-pane-closing-brace {\n  color: var(--color-text-primary);\n}\n\n.styles-section .devtools-link {\n  user-select: none;\n}\n\n.styles-section .styles-section-subtitle .devtools-link {\n  color: var(--color-text-primary);\n  text-decoration-color: hsl(0deg 0% 60%);\n}\n\n.styles-section .selector {\n  color: var(--color-text-disabled);\n}\n\n.styles-section .simple-selector.selector-matches,\n.styles-section.keyframe-key {\n  color: var(--color-text-primary);\n}\n\n.styles-section .style-properties {\n  margin: 0;\n  padding: 2px 4px 0 0;\n  list-style: none;\n  clear: both;\n  display: flex;\n}\n\n.styles-section.matched-styles .style-properties {\n  padding-left: 0;\n}\n\n@keyframes styles-element-state-pane-slidein {\n  from {\n    margin-top: -60px;\n  }\n\n  to {\n    margin-top: 0;\n  }\n}\n\n@keyframes styles-element-state-pane-slideout {\n  from {\n    margin-top: 0;\n  }\n\n  to {\n    margin-top: -60px;\n  }\n}\n\n.styles-sidebar-toolbar-pane {\n  position: relative;\n  animation-duration: 0.1s;\n  animation-direction: normal;\n}\n\n.styles-sidebar-toolbar-pane-container {\n  position: relative;\n  overflow: hidden;\n  flex-shrink: 0;\n}\n\n.styles-selector {\n  cursor: text;\n}\n\n.styles-sidebar-pane-toolbar-container {\n  flex-shrink: 0;\n  overflow: hidden;\n  position: sticky;\n  top: 0;\n  background-color: var(--color-background-elevation-1);\n  z-index: 2;\n}\n\n.styles-sidebar-pane-toolbar {\n  border-bottom: 1px solid var(--color-details-hairline);\n  flex-shrink: 0;\n}\n\n.font-toolbar-hidden {\n  visibility: hidden;\n}\n\n.styles-sidebar-pane-filter-box {\n  flex: auto;\n  display: flex;\n}\n\n.styles-sidebar-pane-filter-box > input {\n  outline: none !important; /* stylelint-disable-line declaration-no-important */\n  border: none;\n  width: 100%;\n  background: var(--color-background);\n  padding-left: 4px;\n  margin: 3px;\n}\n\n.styles-sidebar-pane-filter-box > input:hover {\n  box-shadow: var(--legacy-focus-ring-inactive-shadow);\n}\n\n.styles-sidebar-pane-filter-box > input:focus,\n.styles-sidebar-pane-filter-box > input:not(:placeholder-shown) {\n  box-shadow: var(--legacy-focus-ring-active-shadow);\n}\n\n.styles-sidebar-pane-filter-box > input::placeholder {\n  color: var(--color-text-secondary);\n}\n\n.styles-section.styles-panel-hovered:not(.read-only),\n.styles-section.styles-panel-hovered:not(.read-only) devtools-css-query {\n  --override-styles-section-text-hover-text-decoration: underline;\n  --override-styles-section-text-hover-cursor: default;\n}\n\n.styles-section span.simple-selector:hover {\n  text-decoration: var(--override-styles-section-text-hover-text-decoration);\n  cursor: var(--override-styles-section-text-hover-cursor);\n}\n\n.sidebar-separator {\n  background-color: var(--color-background-elevation-1);\n  padding: 0 5px;\n  border-bottom: 1px solid var(--color-details-hairline);\n  color: var(--color-text-secondary);\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  line-height: 22px;\n}\n\n.sidebar-separator > span.monospace {\n  max-width: 180px;\n  display: inline-block;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  vertical-align: middle;\n  margin-left: 2px;\n}\n\n.sidebar-separator.layer-separator {\n  display: flex;\n}\n\n.sidebar-pane-section-toolbar {\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  z-index: 0;\n}\n\n.sidebar-pane-section-toolbar.new-rule-toolbar {\n  visibility: hidden;\n}\n\n.styles-pane:not(.is-editing-style) .styles-section.matched-styles:not(.read-only):hover .sidebar-pane-section-toolbar.new-rule-toolbar {\n  visibility: visible;\n}\n\n.sidebar-pane-section-toolbar.shifted-toolbar {\n  padding-right: 32px;\n}\n\n.styles-show-all {\n  margin-left: 16px;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  max-width: -webkit-fill-available;\n}\n\n@media (forced-colors: active) {\n  .styles-sidebar-pane-filter-box > input {\n    border: 1px solid ButtonText;\n    background: ButtonFace;\n  }\n\n  .styles-section:focus-visible,\n  .styles-section.read-only:focus-visible {\n    forced-color-adjust: none;\n    background-color: Highlight;\n  }\n\n  .styles-section .styles-section-subtitle .devtools-link {\n    color: linktext;\n    text-decoration-color: linktext;\n  }\n\n  .styles-section .styles-section-subtitle .devtools-link:focus-visible {\n    color: HighlightText;\n  }\n\n  .styles-section:focus-visible *,\n  .styles-section.read-only:focus-visible *,\n  .styles-section:focus-visible .styles-section-subtitle .devtools-link {\n    color: HighlightText;\n    text-decoration-color: HighlightText;\n  }\n\n  .sidebar-pane-section-toolbar {\n    forced-color-adjust: none;\n    border-color: 1px solid ButtonText;\n    background-color: ButtonFace;\n  }\n\n  .styles-section:focus-visible .sidebar-pane-section-toolbar {\n    background-color: ButtonFace;\n  }\n\n  .styles-section:focus-visible {\n    --webkit-css-property-color: HighlightText;\n  }\n}\n\n.spinner::before {\n  --dimension: 24px;\n\n  margin-top: 2em;\n  left: calc(50% - var(--dimension) / 2);\n}\n\n/*# sourceURL=stylesSidebarPane.css */\n");var o=i})),t.register("1DdG1",(function(n,i){e(n.exports,"LayersWidget",(()=>f)),e(n.exports,"ButtonProvider",(()=>y));var o=t("ixFnt"),s=t("eQFvP"),r=t("9z2ZV");t("b78cS");var l=t("2V4uM"),a=t("1r4or"),d=t("78X6w"),c=t("cY3yZ");const h={cssLayersTitle:"CSS layers",toggleCSSLayers:"Toggle CSS Layers view"},p=o.i18n.registerUIStrings("panels/elements/LayersWidget.ts",h),u=o.i18n.getLocalizedString.bind(void 0,p);let m,g;class f extends r.Widget.Widget{cssModel;layerTreeComponent=new l.TreeOutline;constructor(){super(!0),this.contentElement.className="styles-layers-pane",r.UIUtils.createTextChild(this.contentElement.createChild("div"),u(h.cssLayersTitle)),this.contentElement.appendChild(this.layerTreeComponent),r.Context.Context.instance().addFlavorChangeListener(s.DOMModel.DOMNode,this.update,this)}updateModel(e){this.cssModel!==e&&(this.cssModel&&this.cssModel.removeEventListener(s.CSSModel.Events.StyleSheetChanged,this.update,this),this.cssModel=e,this.cssModel&&this.cssModel.addEventListener(s.CSSModel.Events.StyleSheetChanged,this.update,this))}async wasShown(){return super.wasShown(),this.registerCSSFiles([d.default]),this.update()}async update(){if(!this.isShowing())return;let e=r.Context.Context.instance().flavor(s.DOMModel.DOMNode);if(e&&(e=e.enclosingElementOrSelf()),!e)return;if(this.updateModel(e.domModel().cssModel()),!this.cssModel)return;const t=e=>n=>{const i=n.subLayers,o=s.CSSModel.CSSModel.readableLayerName(n.name),r=n.order+": "+o,l=e?e+"."+o:o;return i?{treeNodeData:r,id:l,children:()=>Promise.resolve(i.sort(((e,t)=>e.order-t.order)).map(t(l)))}:{treeNodeData:r,id:l}},n=await this.cssModel.getRootLayer(e.id);this.layerTreeComponent.data={defaultRenderer:l.defaultRenderer,tree:[t("")(n)]},await this.layerTreeComponent.expandRecursively(5)}async revealLayer(e){return this.isShowing()||a.ElementsPanel.instance().showToolbarPane(this,y.instance().item()),await this.update(),this.layerTreeComponent.expandToAndSelectTreeNodeId("implicit outer layer."+e)}static instance(e={forceNew:null}){const{forceNew:t}=e;return m&&!t||(m=new f),m}}class y{button;constructor(){const e=new c.Icon.Icon;e.data={iconName:"ic_layers_16x16",color:"var(--color-text-secondary)",width:"13px",height:"13px"},this.button=new r.Toolbar.ToolbarToggle(u(h.toggleCSSLayers),e),this.button.setVisible(!1),this.button.addEventListener(r.Toolbar.ToolbarButton.Events.Click,this.clicked,this),this.button.element.classList.add("monospace")}static instance(e={forceNew:null}){const{forceNew:t}=e;return g&&!t||(g=new y),g}clicked(){const e=f.instance();a.ElementsPanel.instance().showToolbarPane(e.isShowing()?null:e,this.button)}item(){return this.button}}})),t.register("78X6w",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync("/**\n * Copyright 2022 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.styles-layers-pane {\n  overflow: hidden;\n  padding-left: 2px;\n  background-color: var(--color-background-elevation-1);\n  border-bottom: 1px solid var(--color-details-hairline);\n  margin-top: 0;\n  padding-bottom: 2px;\n}\n\n.styles-layers-pane > div {\n  font-weight: bold;\n  margin: 8px 4px 6px;\n}\n\n.styles-layers-pane > table {\n  width: 100%;\n  border-spacing: 0;\n}\n\n.styles-layers-pane td {\n  padding: 0;\n}\n\n/*# sourceURL=layersWidget.css */\n");var o=i})),t.register("hnsgt",(function(n,i){e(n.exports,"ElementsTreeElementHighlighter",(()=>d));var o=t("koSS8"),s=t("eQFvP"),r=t("9z2ZV"),l=t("4o2MM"),a=t("Vxucs");class d{throttler;treeOutline;currentHighlightedElement;alreadyExpandedParentElement;pendingHighlightNode;isModifyingTreeOutline;constructor(e){this.throttler=new o.Throttler.Throttler(100),this.treeOutline=e,this.treeOutline.addEventListener(r.TreeOutline.Events.ElementExpanded,this.clearState,this),this.treeOutline.addEventListener(r.TreeOutline.Events.ElementCollapsed,this.clearState,this),this.treeOutline.addEventListener(a.ElementsTreeOutline.Events.SelectedNodeChanged,this.clearState,this),s.TargetManager.TargetManager.instance().addModelListener(s.OverlayModel.OverlayModel,s.OverlayModel.Events.HighlightNodeRequested,this.highlightNode,this),s.TargetManager.TargetManager.instance().addModelListener(s.OverlayModel.OverlayModel,s.OverlayModel.Events.InspectModeWillBeToggled,this.clearState,this),this.currentHighlightedElement=null,this.alreadyExpandedParentElement=null,this.pendingHighlightNode=null,this.isModifyingTreeOutline=!1}highlightNode(e){if(!o.Settings.Settings.instance().moduleSetting("highlightNodeOnHoverInOverlay").get())return;const t=e.data;this.throttler.schedule((async()=>{this.highlightNodeInternal(this.pendingHighlightNode),this.pendingHighlightNode=null})),this.pendingHighlightNode=this.treeOutline===a.ElementsTreeOutline.forDOMModel(t.domModel())?t:null}highlightNodeInternal(e){this.isModifyingTreeOutline=!0;let t=null;if(this.currentHighlightedElement){let e=this.currentHighlightedElement;for(;e&&e!==this.alreadyExpandedParentElement;){e.expanded&&e.collapse();const t=e.parent;e=t instanceof l.ElementsTreeElement?t:null}}if(this.currentHighlightedElement=null,this.alreadyExpandedParentElement=null,e){let n=e;const i=this.treeOutline.treeElementByNode,o=e=>{const t=i.get(e);return!t||!t.expanded};for(;n&&o(n);)n=n.parentNode;this.alreadyExpandedParentElement=n?i.get(n):this.treeOutline.rootElement(),t=this.treeOutline.createTreeElementFor(e)}this.currentHighlightedElement=t,this.treeOutline.setHoverEffect(t),t&&t.reveal(!0),this.isModifyingTreeOutline=!1}clearState(){this.isModifyingTreeOutline||(this.currentHighlightedElement=null,this.alreadyExpandedParentElement=null,this.pendingHighlightNode=null)}}})),t.register("4o2MM",(function(n,i){e(n.exports,"ElementsTreeElement",(()=>M)),e(n.exports,"InitialChildrenLimit",(()=>N)),e(n.exports,"EditTagBlocklist",(()=>P)),e(n.exports,"ForbiddenClosingTagElements",(()=>I)),e(n.exports,"adornerComparator",(()=>O));var o=t("koSS8"),s=t("e7bLS"),r=t("ixFnt"),l=t("lz7WY"),a=t("eQFvP"),d=t("7f6zc"),c=t("39160");t("6pDhf");var h=t("1T18D"),p=t("3T3mV"),u=t("hi0Sq"),m=t("a3yig"),g=t("9z2ZV"),f=t("a1WJ2");t("Px6PZ");var y=t("au5Rx"),v=t("iADGB"),b=t("1r4or"),S=t("Vxucs"),E=t("hJQty"),C=t("bVu26");const x={thisFrameWasIdentifiedAsAnAd:"This frame was identified as an ad frame",forceState:"Force state",useSInTheConsoleToReferToThis:"Use {PH1} in the console to refer to this element.",addAttribute:"Add attribute",editAttribute:"Edit attribute",focus:"Focus",scrollIntoView:"Scroll into view",enterIsolationMode:"Enter Isolation Mode",exitIsolationMode:"Exit Isolation Mode",editText:"Edit text",editAsHtml:"Edit as HTML",cut:"Cut",copy:"Copy",paste:"Paste",copyOuterhtml:"Copy outerHTML",copySelector:"Copy `selector`",copyJsPath:"Copy JS path",copyStyles:"Copy styles",copyXpath:"Copy XPath",copyFullXpath:"Copy full XPath",copyElement:"Copy element",duplicateElement:"Duplicate element",hideElement:"Hide element",deleteElement:"Delete element",expandRecursively:"Expand recursively",collapseChildren:"Collapse children",captureNodeScreenshot:"Capture node screenshot",showFrameDetails:"Show `iframe` details",valueIsTooLargeToEdit:"<value is too large to edit>",children:"Children:",enableGridMode:"Enable grid mode",disableGridMode:"Disable grid mode",enableFlexMode:"Enable flex mode",disableFlexMode:"Disable flex mode",enableScrollSnap:"Enable scroll-snap overlay",disableScrollSnap:"Disable scroll-snap overlay"},w=r.i18n.registerUIStrings("panels/elements/ElementsTreeElement.ts",x),T=r.i18n.getLocalizedString.bind(void 0,w);class M extends g.TreeOutline.TreeElement{nodeInternal;treeOutline;gutterContainer;decorationsElement;isClosingTagInternal;canAddAttributes;searchQuery;expandedChildrenLimitInternal;decorationsThrottler;inClipboard;hoveredInternal;editing;highlightResult;adornerContainer;adorners;styleAdorners;adornersThrottler;htmlEditElement;expandAllButtonElement;searchHighlightsVisible;selectionElement;hintElement;contentElement;constructor(e,t){super(),this.nodeInternal=e,this.treeOutline=null,this.contentElement=this.listItemElement.createChild("div"),this.gutterContainer=this.contentElement.createChild("div","gutter-container"),this.gutterContainer.addEventListener("click",this.showContextMenu.bind(this));const n=g.Icon.Icon.create("largeicon-menu","gutter-menu-icon");if(this.gutterContainer.append(n),this.decorationsElement=this.gutterContainer.createChild("div","hidden"),this.isClosingTagInternal=t,this.nodeInternal.nodeType()!==Node.ELEMENT_NODE||t||(this.canAddAttributes=!0),this.searchQuery=null,this.expandedChildrenLimitInternal=N,this.decorationsThrottler=new o.Throttler.Throttler(100),this.inClipboard=!1,this.hoveredInternal=!1,this.editing=null,this.highlightResult=[],!t&&(this.adornerContainer=this.contentElement.createChild("div","adorner-container hidden"),this.adorners=[],this.styleAdorners=[],this.adornersThrottler=new o.Throttler.Throttler(100),this.updateStyleAdorners(),e.isAdFrameNode())){const e=y.getRegisteredAdorner(y.RegisteredAdorners.AD),t=this.adorn(e);g.Tooltip.Tooltip.install(t,T(x.thisFrameWasIdentifiedAsAnAd))}this.expandAllButtonElement=null}static animateOnDOMUpdate(e){const t=e.listItemElement.querySelector(".webkit-html-tag-name");g.UIUtils.runCSSAnimationOnce(t||e.listItemElement,"dom-update-highlight")}static visibleShadowRoots(e){let t=e.shadowRoots();return t.length&&!o.Settings.Settings.instance().moduleSetting("showUAShadowDOM").get()&&(t=t.filter((function(e){return e.shadowRootType()!==a.DOMModel.DOMNode.ShadowRootTypes.UserAgent}))),t}static canShowInlineText(e){if(e.contentDocument()||e.templateContent()||M.visibleShadowRoots(e).length||e.hasPseudoElements())return!1;if(e.nodeType()!==Node.ELEMENT_NODE)return!1;if(!e.firstChild||e.firstChild!==e.lastChild||e.firstChild.nodeType()!==Node.TEXT_NODE)return!1;return e.firstChild.nodeValue().length<80}static populateForcedPseudoStateItems(e,t){const n=["active","hover","focus","visited","focus-within","focus-visible"],i=t.domModel().cssModel().pseudoState(t),o=e.debugSection().appendSubMenuItem(T(x.forceState));for(const e of n){const t=!!i&&i.indexOf(e)>=0;o.defaultSection().appendCheckboxItem(":"+e,s.bind(null,e,!t),t,!1)}function s(e,n){t.domModel().cssModel().forcePseudoState(t,e,n)}}isClosingTag(){return Boolean(this.isClosingTagInternal)}node(){return this.nodeInternal}isEditing(){return Boolean(this.editing)}highlightSearchResults(e){this.searchQuery!==e&&this.hideSearchHighlight(),this.searchQuery=e,this.searchHighlightsVisible=!0,this.updateTitle(null,!0)}hideSearchHighlights(){delete this.searchHighlightsVisible,this.hideSearchHighlight()}hideSearchHighlight(){if(0!==this.highlightResult.length){for(let e=this.highlightResult.length-1;e>=0;--e){const t=this.highlightResult[e];switch(t.type){case"added":t.node.remove();break;case"changed":t.node.textContent=t.oldText||null}}this.highlightResult=[]}}setInClipboard(e){this.inClipboard!==e&&(this.inClipboard=e,this.listItemElement.classList.toggle("in-clipboard",e))}get hovered(){return this.hoveredInternal}set hovered(e){this.hoveredInternal!==e&&(this.hoveredInternal=e,this.listItemElement&&(e?(this.createSelection(),this.listItemElement.classList.add("hovered")):this.listItemElement.classList.remove("hovered")))}expandedChildrenLimit(){return this.expandedChildrenLimitInternal}setExpandedChildrenLimit(e){this.expandedChildrenLimitInternal=e}createSelection(){const e=this.contentElement;e&&(this.selectionElement||(this.selectionElement=document.createElement("div"),this.selectionElement.className="selection fill",this.selectionElement.style.setProperty("margin-left",-this.computeLeftIndent()+"px"),e.prepend(this.selectionElement)))}createHint(){if(this.contentElement&&!this.hintElement){this.hintElement=this.contentElement.createChild("span","selected-hint");const e="$0";g.Tooltip.Tooltip.install(this.hintElement,T(x.useSInTheConsoleToReferToThis,{PH1:e})),g.ARIAUtils.markAsHidden(this.hintElement)}}onbind(){this.treeOutline&&!this.isClosingTagInternal&&this.treeOutline.treeElementByNode.set(this.nodeInternal,this)}onunbind(){this.editing&&this.editing.cancel(),this.treeOutline&&this.treeOutline.treeElementByNode.get(this.nodeInternal)===this&&this.treeOutline.treeElementByNode.delete(this.nodeInternal)}onattach(){this.hoveredInternal&&(this.createSelection(),this.listItemElement.classList.add("hovered")),this.updateTitle(),this.listItemElement.draggable=!0}async onpopulate(){if(this.treeOutline)return this.treeOutline.populateTreeElement(this)}async expandRecursively(){await this.nodeInternal.getSubtree(-1,!0),await super.expandRecursively(Number.MAX_VALUE)}onexpand(){this.isClosingTagInternal||this.updateTitle()}oncollapse(){this.isClosingTagInternal||this.updateTitle()}select(e,t){return!this.editing&&super.select(e,t)}onselect(e){return!!this.treeOutline&&(this.treeOutline.suppressRevealAndSelect=!0,this.treeOutline.selectDOMNode(this.nodeInternal,e),e&&(this.nodeInternal.highlight(),s.userMetrics.actionTaken(s.UserMetrics.Action.ChangeInspectedNodeInElementsPanel)),this.createSelection(),this.createHint(),this.treeOutline.suppressRevealAndSelect=!1,!0)}ondelete(){if(!this.treeOutline)return!1;const e=this.treeOutline.findTreeElement(this.nodeInternal);return e?e.remove():this.remove(),!0}onenter(){return!this.editing&&(this.startEditing(),!0)}selectOnMouseDown(e){super.selectOnMouseDown(e),this.editing||e.detail>=2&&e.preventDefault()}ondblclick(e){return this.editing||this.isClosingTagInternal||this.startEditingTarget(e.target)||this.isExpandable()&&!this.expanded&&this.expand(),!1}hasEditableNode(){return!this.nodeInternal.isShadowRoot()&&!this.nodeInternal.ancestorUserAgentShadowRoot()}insertInLastAttributePosition(e,t){if(e.getElementsByClassName("webkit-html-attribute").length>0)e.insertBefore(t,e.lastChild);else if(null!==e.textContent){const n=e.textContent.match(/^<(.*?)>$/);if(!n)return;const i=n[1];e.textContent="",g.UIUtils.createTextChild(e,"<"+i),e.appendChild(t),g.UIUtils.createTextChild(e,">")}}startEditingTarget(e){if(!this.treeOutline||this.treeOutline.selectedDOMNode()!==this.nodeInternal)return!1;if(this.nodeInternal.nodeType()!==Node.ELEMENT_NODE&&this.nodeInternal.nodeType()!==Node.TEXT_NODE)return!1;const t=e.enclosingNodeOrSelfWithClass("webkit-html-text-node");if(t)return this.startEditingTextNode(t);const n=e.enclosingNodeOrSelfWithClass("webkit-html-attribute");if(n)return this.startEditingAttribute(n,e);const i=e.enclosingNodeOrSelfWithClass("webkit-html-tag-name");if(i)return this.startEditingTagName(i);return!!e.enclosingNodeOrSelfWithClass("add-attribute")&&this.addNewAttribute()}showContextMenu(e){this.treeOutline&&this.treeOutline.showContextMenu(this,e)}populateTagContextMenu(e,t){const n=this.isClosingTagInternal&&this.treeOutline?this.treeOutline.findTreeElement(this.nodeInternal):this;if(!n)return;e.editSection().appendItem(T(x.addAttribute),n.addNewAttribute.bind(n));const i=t.target,o=i.enclosingNodeOrSelfWithClass("webkit-html-attribute"),s=i.enclosingNodeOrSelfWithClass("add-attribute");o&&!s&&e.editSection().appendItem(T(x.editAttribute),this.startEditingAttribute.bind(this,o,i)),this.populateNodeContextMenu(e),M.populateForcedPseudoStateItems(e,n.node()),this.populateScrollIntoView(e),e.viewSection().appendItem(T(x.focus),(async()=>{await this.nodeInternal.focus()}));const r=this.nodeInternal.domModel().overlayModel();r.isHighlightedIsolatedElementInPersistentOverlay(this.nodeInternal.id)?e.viewSection().appendItem(T(x.exitIsolationMode),(()=>{r.hideIsolatedElementInPersistentOverlay(this.nodeInternal.id)})):e.viewSection().appendItem(T(x.enterIsolationMode),(()=>{r.highlightIsolatedElementInPersistentOverlay(this.nodeInternal.id)}))}populateScrollIntoView(e){e.viewSection().appendItem(T(x.scrollIntoView),(()=>this.nodeInternal.scrollIntoView()))}populateTextContextMenu(e,t){this.editing||e.editSection().appendItem(T(x.editText),this.startEditingTextNode.bind(this,t)),this.populateNodeContextMenu(e)}populateNodeContextMenu(e){const t=this.hasEditableNode();t&&!this.editing&&e.editSection().appendItem(T(x.editAsHtml),this.editAsHTML.bind(this));const n=this.nodeInternal.isShadowRoot(),i=g.KeyboardShortcut.KeyboardShortcut.shortcutToString.bind(null),s=g.KeyboardShortcut.Modifiers.CtrlOrMeta,r=this.treeOutline;if(!r)return;let l;l=e.clipboardSection().appendItem(T(x.cut),r.performCopyOrCut.bind(r,!0,this.nodeInternal),!this.hasEditableNode()),l.setShortcut(i("X",s));const d=e.clipboardSection().appendSubMenuItem(T(x.copy)),c=d.section();if(n||(l=c.appendItem(T(x.copyOuterhtml),r.performCopyOrCut.bind(r,!1,this.nodeInternal)),l.setShortcut(i("V",s))),this.nodeInternal.nodeType()===Node.ELEMENT_NODE&&(c.appendItem(T(x.copySelector),this.copyCSSPath.bind(this)),c.appendItem(T(x.copyJsPath),this.copyJSPath.bind(this),!(0,v.canGetJSPath)(this.nodeInternal)),c.appendItem(T(x.copyStyles),this.copyStyles.bind(this))),n||(c.appendItem(T(x.copyXpath),this.copyXPath.bind(this)),c.appendItem(T(x.copyFullXpath),this.copyFullXPath.bind(this))),!n){l=d.clipboardSection().appendItem(T(x.copyElement),r.performCopyOrCut.bind(r,!1,this.nodeInternal)),l.setShortcut(i("C",s));const t=!this.nodeInternal.parentNode||"#document"===this.nodeInternal.parentNode.nodeName();l=e.editSection().appendItem(T(x.duplicateElement),r.duplicateNode.bind(r,this.nodeInternal),this.nodeInternal.isInShadowTree()||t)}l=e.clipboardSection().appendItem(T(x.paste),r.pasteNode.bind(r,this.nodeInternal),!r.canPaste(this.nodeInternal)),l.setShortcut(i("V",s)),l=e.debugSection().appendCheckboxItem(T(x.hideElement),r.toggleHideElement.bind(r,this.nodeInternal),r.isToggledToHidden(this.nodeInternal)),l.setShortcut(g.ShortcutRegistry.ShortcutRegistry.instance().shortcutTitleForAction("elements.hide-element")||""),t&&e.editSection().appendItem(T(x.deleteElement),this.remove.bind(this)),e.viewSection().appendItem(T(x.expandRecursively),this.expandRecursively.bind(this)),e.viewSection().appendItem(T(x.collapseChildren),this.collapseChildren.bind(this));const h=new f.DeviceModeWrapper.ActionDelegate;e.viewSection().appendItem(T(x.captureNodeScreenshot),h.handleAction.bind(null,g.Context.Context.instance(),"emulation.capture-node-screenshot")),this.nodeInternal.frameOwnerFrameId()&&e.viewSection().appendItem(T(x.showFrameDetails),(()=>{const e=this.nodeInternal.frameOwnerFrameId();if(e){const t=a.FrameManager.FrameManager.instance().getFrame(e);o.Revealer.reveal(t)}}))}startEditing(){if(!this.treeOutline||this.treeOutline.selectedDOMNode()!==this.nodeInternal)return;const e=this.listItemElement;if(this.canAddAttributes){const t=e.getElementsByClassName("webkit-html-attribute")[0];return t?this.startEditingAttribute(t,t.getElementsByClassName("webkit-html-attribute-value")[0]):this.addNewAttribute()}if(this.nodeInternal.nodeType()===Node.TEXT_NODE){const t=e.getElementsByClassName("webkit-html-text-node")[0];if(t)return this.startEditingTextNode(t)}}addNewAttribute(){const e=document.createElement("span"),t=this.buildAttributeDOM(e," ","",null);t.style.marginLeft="2px",t.style.marginRight="2px";const n=this.listItemElement.getElementsByClassName("webkit-html-tag")[0];return this.insertInLastAttributePosition(n,t),t.scrollIntoViewIfNeeded(!0),this.startEditingAttribute(t,t)}triggerEditAttribute(e){const t=this.listItemElement.getElementsByClassName("webkit-html-attribute-name");for(let n=0,i=t.length;n<i;++n)if(t[n].textContent===e)for(let e=t[n].nextSibling;e;e=e.nextSibling)if(e.nodeType===Node.ELEMENT_NODE&&e.classList.contains("webkit-html-attribute-value"))return this.startEditingAttribute(e.parentElement,e)}startEditingAttribute(e,t){if(console.assert(this.listItemElement.isAncestor(e)),g.UIUtils.isBeingEdited(e))return!0;const n=e.getElementsByClassName("webkit-html-attribute-name")[0];if(!n)return!1;const i=n.textContent,s=e.getElementsByClassName("webkit-html-attribute-value")[0];t=s.isAncestor(t)?s:t;const r=i&&s?this.nodeInternal.getAttribute(i):void 0;void 0!==r&&s.setTextContentTruncatedIfNeeded(r,T(x.valueIsTooLargeToEdit)),function e(t){if(t.nodeType!==Node.TEXT_NODE){if(t.nodeType===Node.ELEMENT_NODE)for(let n=t.firstChild;n;n=n.nextSibling)e(n)}else t.nodeValue=t.nodeValue?t.nodeValue.replace(/\u200B/g,""):""}(e);const l=new g.InplaceEditor.Config(this.attributeEditingCommitted.bind(this),this.editingCancelled.bind(this),i||void 0);o.ParsedURL.ParsedURL.fromString(s.textContent||"")||l.setPostKeydownFinishHandler((function(t){return g.UIUtils.handleElementValueModifications(t,e),""})),this.updateEditorHandles(e,l);const a=this.listItemElement.getComponentSelection();return a&&a.selectAllChildren(t),!0}startEditingTextNode(e){if(g.UIUtils.isBeingEdited(e))return!0;let t=this.nodeInternal;t.nodeType()===Node.ELEMENT_NODE&&t.firstChild&&(t=t.firstChild);const n=e.enclosingNodeOrSelfWithClass("webkit-html-text-node");n&&(n.textContent=t.nodeValue());const i=new g.InplaceEditor.Config(this.textNodeEditingCommitted.bind(this,t),this.editingCancelled.bind(this));this.updateEditorHandles(e,i);const o=this.listItemElement.getComponentSelection();return o&&o.selectAllChildren(e),!0}startEditingTagName(e){if(!e&&!(e=this.listItemElement.getElementsByClassName("webkit-html-tag-name")[0]))return!1;const t=e.textContent;if(null!==t&&P.has(t.toLowerCase()))return!1;if(g.UIUtils.isBeingEdited(e))return!0;const n=this.distinctClosingTagElement();function i(){n&&e&&(n.textContent="</"+e.textContent+">")}const o=e=>{" "===e.key&&(this.editing&&this.editing.commit(),e.consume(!0))};e.addEventListener("keyup",i,!1),e.addEventListener("keydown",o,!1);const s=new g.InplaceEditor.Config(function(t,n,s,r,l){e&&(e.removeEventListener("keyup",i,!1),e.removeEventListener("keydown",o,!1),this.tagNameEditingCommitted(t,n,s,r,l))}.bind(this),function(t,n){e&&(e.removeEventListener("keyup",i,!1),e.removeEventListener("keydown",o,!1),this.editingCancelled(t,n))}.bind(this),t);this.updateEditorHandles(e,s);const r=this.listItemElement.getComponentSelection();return r&&r.selectAllChildren(e),!0}updateEditorHandles(e,t){const n=g.InplaceEditor.InplaceEditor.startEditing(e,t);this.editing=n?{commit:n.commit,cancel:n.cancel,editor:void 0,resize:()=>{}}:null}async startEditingAsHTML(e,t,n){if(null===n)return;if(this.editing)return;const i=this.convertWhitespaceToEntities(n).text;this.htmlEditElement=document.createElement("div"),this.htmlEditElement.className="source-code elements-tree-editor";let o=this.listItemElement.firstChild;for(;o;)o.style.display="none",o=o.nextSibling;this.childrenListElement&&(this.childrenListElement.style.display="none"),this.listItemElement.append(this.htmlEditElement),this.htmlEditElement.addEventListener("keydown",(e=>{"Escape"===e.key&&e.consume(!0)}));const s=new u.TextEditor.TextEditor(c.EditorState.create({doc:i,extensions:[c.keymap.of([{key:"Mod-Enter",run:()=>(this.editing?.commit(),!0)},{key:"Escape",run:()=>(this.editing?.cancel(),!0)}]),u.Config.baseConfiguration(i),u.Config.closeBrackets,u.Config.autocompletion,c.html.html(),u.Config.domWordWrap.instance(),c.EditorView.theme({"&.cm-editor":{maxHeight:"300px"},".cm-scroller":{overflowY:"auto"}}),c.EditorView.domEventHandlers({focusout:e=>{const t=e.relatedTarget;t&&!t.isSelfOrDescendant(s)&&this.editing&&this.editing.commit()}})]}));function r(){this.treeOutline&&this.htmlEditElement&&(this.htmlEditElement.style.width=this.treeOutline.visibleWidth()-this.computeLeftIndent()-30+"px")}function l(){if(!this.editing||!this.editing.editor)return;this.editing=null,this.htmlEditElement&&this.listItemElement.removeChild(this.htmlEditElement),this.htmlEditElement=void 0,this.childrenListElement&&this.childrenListElement.style.removeProperty("display");let e=this.listItemElement.firstChild;for(;e;)e.style.removeProperty("display"),e=e.nextSibling;this.treeOutline&&(this.treeOutline.setMultilineEditing(null),this.treeOutline.focus()),t()}this.editing={commit:function(){this.editing&&this.editing.editor&&e(i,this.editing.editor.state.doc.toString());l.call(this)}.bind(this),cancel:l.bind(this),editor:s,resize:r.bind(this)},r.call(this),this.htmlEditElement.appendChild(s),s.editor.focus(),this.treeOutline&&this.treeOutline.setMultilineEditing(this.editing)}attributeEditingCommitted(e,t,n,i,o){this.editing=null;const s=this.treeOutline;function r(n){if(n&&this.editingCancelled(e,i),!o)return;s&&(s.runPendingUpdates(),s.focus());const r=this.nodeInternal.attributes();for(let e=0;e<r.length;++e)if(r[e].name===i)return void("backward"===o?0===e?this.startEditingTagName():this.triggerEditAttribute(r[e-1].name):e===r.length-1?this.addNewAttribute():this.triggerEditAttribute(r[e+1].name));"backward"===o?" "===t?r.length>0&&this.triggerEditAttribute(r[r.length-1].name):r.length>1&&this.triggerEditAttribute(r[r.length-2].name):"forward"===o&&(l.StringUtilities.isWhitespace(t)?this.startEditingTagName():this.addNewAttribute())}!i.trim()&&!t.trim()||n===t?(this.updateTitle(),r.call(this)):this.nodeInternal.setAttribute(i,t,r.bind(this))}tagNameEditingCommitted(e,t,n,i,o){this.editing=null;const s=this;function r(){const t=s.distinctClosingTagElement();t&&(t.textContent="</"+i+">"),s.editingCancelled(e,i),l.call(s)}function l(){if("forward"!==o)return void this.addNewAttribute();const e=this.nodeInternal.attributes();e.length>0?this.triggerEditAttribute(e[0].name):this.addNewAttribute()}if((t=t.trim())===n)return void r();const a=this.treeOutline,d=this.expanded;this.nodeInternal.setNodeName(t,((e,t)=>{if(e||!t)return void r();if(!a)return;const n=a.selectNodeAfterEdit(d,e,t);l.call(n)}))}textNodeEditingCommitted(e,t,n){this.editing=null,e.setNodeValue(n,function(){this.updateTitle()}.bind(this))}editingCancelled(e,t){this.editing=null,this.updateTitle()}distinctClosingTagElement(){if(this.expanded){const e=this.childrenListElement.querySelectorAll(".close");return e[e.length-1]}const e=this.listItemElement.getElementsByClassName("webkit-html-tag");return 1===e.length?null:e[e.length-1]}updateTitle(e,t){if(!this.editing){if(t)this.hideSearchHighlight();else{const t=this.nodeTitleInfo(e||null);if(this.nodeInternal.nodeType()===Node.DOCUMENT_FRAGMENT_NODE&&this.nodeInternal.isInShadowTree()&&this.nodeInternal.shadowRootType()){this.childrenListElement.classList.add("shadow-root");let e=4;for(let t=this.nodeInternal;e&&t;t=t.parentNode)t.nodeType()===Node.DOCUMENT_FRAGMENT_NODE&&e--;e?this.childrenListElement.classList.add("shadow-root-depth-"+e):this.childrenListElement.classList.add("shadow-root-deep")}this.contentElement.removeChildren();this.contentElement.createChild("span","highlight").append(t),this.title=this.contentElement,this.updateDecorations(),this.contentElement.prepend(this.gutterContainer),!this.isClosingTagInternal&&this.adornerContainer&&this.contentElement.append(this.adornerContainer),this.highlightResult=[],delete this.selectionElement,delete this.hintElement,this.selected&&(this.createSelection(),this.createHint())}this.highlightSearchResultsInternal()}}computeLeftIndent(){let e=this.parent,t=0;for(;null!==e;)t++,e=e.parent;return 12*(t-2)+(this.isExpandable()&&this.isCollapsible()?1:12)}updateDecorations(){this.gutterContainer.style.left=-this.computeLeftIndent()+"px",this.isClosingTag()||this.nodeInternal.nodeType()===Node.ELEMENT_NODE&&this.decorationsThrottler.schedule(this.updateDecorationsInternal.bind(this))}updateDecorationsInternal(){if(!this.treeOutline)return Promise.resolve();const e=this.nodeInternal;this.treeOutline.decoratorExtensions||(this.treeOutline.decoratorExtensions=(0,C.getRegisteredDecorators)());const t=new Map;for(const e of this.treeOutline.decoratorExtensions)t.set(e.marker,e);const n=[],i=[],o=[];function s(t,n){const s=n.decorate(t);s&&(t===e?i:o).push(s)}return e.traverseMarkers((function(e,i){const o=t.get(i);if(!o)return;n.push(Promise.resolve(o.decorator()).then(s.bind(null,e)))})),Promise.all(n).then(function(){if(this.decorationsElement.removeChildren(),this.decorationsElement.classList.add("hidden"),this.gutterContainer.classList.toggle("has-decorations",Boolean(i.length||o.length)),g.ARIAUtils.setAccessibleName(this.decorationsElement,""),!i.length&&!o.length)return;const e=new Set,t=document.createElement("div");for(const n of i){t.createChild("div").textContent=n.title,e.add(n.color)}if(this.expanded&&!i.length)return;const n=new Set;if(o.length){let e=t.createChild("div");e.textContent=T(x.children);for(const i of o)e=t.createChild("div"),e.style.marginLeft="15px",e.textContent=i.title,n.add(i.color)}let s=0;r.call(this,e,"elements-gutter-decoration"),this.expanded||r.call(this,n,"elements-gutter-decoration elements-has-decorated-children");function r(e,t){for(const n of e){const e=this.decorationsElement.createChild("div",t);this.decorationsElement.classList.remove("hidden"),e.style.backgroundColor=n,e.style.borderColor=n,s&&(e.style.marginLeft=s+"px"),s+=3}}g.Tooltip.Tooltip.install(this.decorationsElement,t.textContent),g.ARIAUtils.setAccessibleName(this.decorationsElement,t.textContent||"")}.bind(this))}buildAttributeDOM(e,t,n,i,o,s){const r=/[\/;:\)\]\}]/g;let a=0,d=0,c=0;function h(e,t){const n=this.convertWhitespaceToEntities(t);for(d=n.entityRanges.length,t=n.text.replace(r,((e,t)=>{for(;a<d&&n.entityRanges[a].offset<t;)n.entityRanges[a].offset+=c,++a;return c+=1,e+"​"}));a<d;)n.entityRanges[a].offset+=c,++a;e.setTextContentTruncatedIfNeeded(t),g.UIUtils.highlightRangesWithStyleClass(e,n.entityRanges,"webkit-html-entity-value")}const p=o||n.length>0,u=e.createChild("span","webkit-html-attribute"),f=u.createChild("span","webkit-html-attribute-name");f.textContent=t,p&&g.UIUtils.createTextChild(u,'=​"');const y=u.createChild("span","webkit-html-attribute-value");function v(e){const t=s?s.resolveURL(e):null;if(null===t){const t=document.createElement("span");return h.call(this,t,e),t}(e=e.replace(r,"$&​")).startsWith("data:")&&(e=l.StringUtilities.trimMiddle(e,60));const n=s&&"a"===s.nodeName().toLowerCase()?g.XLink.XLink.create(t,e,"",!0):m.Linkifier.Linkifier.linkifyURL(t,{text:e,preventClick:!0,showColumnNumber:!1,inlineFrameIndex:0});return E.ImagePreviewPopover.setImageUrl(n,t)}i&&i.isAttributeModified(t)&&g.UIUtils.runCSSAnimationOnce(p?y:f,"dom-update-highlight");const b=s?s.nodeName().toLowerCase():"";function S(e){const t=document.createDocumentFragment();let n=0;for(;e.length;){n++>0&&g.UIUtils.createTextChild(t," ");let i="",o="";const s=(e=e.trim()).search(/\s/);if(-1===s)i=e;else if(s>0&&","===e[s-1])i=e.substring(0,s);else{i=e.substring(0,s);const t=e.indexOf(",",s);o=-1!==t?e.substring(s,t+1):e.substring(s)}i&&(i.endsWith(",")?(t.appendChild(v.call(this,i.substring(0,i.length-1))),g.UIUtils.createTextChild(t,",")):t.appendChild(v.call(this,i))),o&&g.UIUtils.createTextChild(t,o),e=e.substring(i.length+o.length)}return t}return!b||"src"!==t&&"href"!==t?("img"!==b&&"source"!==b||"srcset"!==t)&&("image"!==b||"xlink:href"!==t&&"href"!==t)?h.call(this,y,n):y.appendChild(S.call(this,n)):y.appendChild(v.call(this,n)),p&&g.UIUtils.createTextChild(u,'"'),u}buildPseudoElementDOM(e,t){e.createChild("span","webkit-html-pseudo-element").textContent="::"+t,g.UIUtils.createTextChild(e,"​")}buildTagDOM(e,t,n,i,o){const s=this.nodeInternal,r=["webkit-html-tag"];n&&i&&r.push("close");const l=e.createChild("span",r.join(" "));g.UIUtils.createTextChild(l,"<");const a=l.createChild("span",n?"webkit-html-close-tag-name":"webkit-html-tag-name");if(a.textContent=(n?"/":"")+t,!n){if(s.hasAttributes()){const e=s.attributes();for(let t=0;t<e.length;++t){const n=e[t];g.UIUtils.createTextChild(l," "),this.buildAttributeDOM(l,n.name,n.value,o,!1,s)}}if(o){let e=o.hasRemovedAttributes()||o.hasRemovedChildren();e=e||!this.expanded&&o.hasChangedChildren(),e&&g.UIUtils.runCSSAnimationOnce(a,"dom-update-highlight")}}g.UIUtils.createTextChild(l,">"),g.UIUtils.createTextChild(e,"​"),l.textContent&&g.ARIAUtils.setAccessibleName(l,l.textContent)}convertWhitespaceToEntities(e){let t="",n=0;const i=[],o=S.MappedCharToEntity;for(let s=0,r=e.length;s<r;++s){const r=e.charAt(s);if(o.has(r)){t+=e.substring(n,s);const l="&"+o.get(r)+";";i.push({offset:t.length,length:l.length}),t+=l,n=s+1}}return t&&(t+=e.substring(n)),{text:t||e,entityRanges:i}}nodeTitleInfo(e){const t=this.nodeInternal,n=document.createDocumentFragment(),i=()=>{this.highlightResult=[],this.highlightSearchResultsInternal()};switch(t.nodeType()){case Node.ATTRIBUTE_NODE:this.buildAttributeDOM(n,t.name,t.value,e,!0);break;case Node.ELEMENT_NODE:{const i=t.pseudoType();if(i){this.buildPseudoElementDOM(n,i);break}const o=t.nodeNameInCorrectCase();if(this.isClosingTagInternal){this.buildTagDOM(n,o,!0,!0,e);break}if(this.buildTagDOM(n,o,!1,!1,e),this.isExpandable()){if(!this.expanded){n.createChild("span","webkit-html-text-node bogus").textContent="…",g.UIUtils.createTextChild(n,"​"),this.buildTagDOM(n,o,!0,!1,e)}break}if(M.canShowInlineText(t)){const i=n.createChild("span","webkit-html-text-node"),s=t.firstChild;if(!s)throw new Error("ElementsTreeElement._nodeTitleInfo expects node.firstChild to be defined.");const r=this.convertWhitespaceToEntities(s.nodeValue());i.textContent=l.StringUtilities.collapseWhitespace(r.text),g.UIUtils.highlightRangesWithStyleClass(i,r.entityRanges,"webkit-html-entity-value"),g.UIUtils.createTextChild(n,"​"),this.buildTagDOM(n,o,!0,!1,e),e&&e.hasChangedChildren()&&g.UIUtils.runCSSAnimationOnce(i,"dom-update-highlight"),e&&e.isCharDataModified()&&g.UIUtils.runCSSAnimationOnce(i,"dom-update-highlight");break}(this.treeOutline&&this.treeOutline.isXMLMimeType||!I.has(o))&&this.buildTagDOM(n,o,!0,!1,e);break}case Node.TEXT_NODE:if(t.parentNode&&"script"===t.parentNode.nodeName().toLowerCase()){const e=n.createChild("span","webkit-html-text-node webkit-html-js-node"),o=t.nodeValue();e.textContent=o.replace(/^[\n\r]+|\s+$/g,""),p.CodeHighlighter.highlightNode(e,"text/javascript").then(i)}else if(t.parentNode&&"style"===t.parentNode.nodeName().toLowerCase()){const e=n.createChild("span","webkit-html-text-node webkit-html-css-node"),o=t.nodeValue();e.textContent=o.replace(/^[\n\r]+|\s+$/g,""),p.CodeHighlighter.highlightNode(e,"text/css").then(i)}else{g.UIUtils.createTextChild(n,'"');const i=n.createChild("span","webkit-html-text-node"),o=this.convertWhitespaceToEntities(t.nodeValue());i.textContent=l.StringUtilities.collapseWhitespace(o.text),g.UIUtils.highlightRangesWithStyleClass(i,o.entityRanges,"webkit-html-entity-value"),g.UIUtils.createTextChild(n,'"'),e&&e.isCharDataModified()&&g.UIUtils.runCSSAnimationOnce(i,"dom-update-highlight")}break;case Node.COMMENT_NODE:{const e=n.createChild("span","webkit-html-comment");g.UIUtils.createTextChild(e,"\x3c!--"+t.nodeValue()+"--\x3e");break}case Node.DOCUMENT_TYPE_NODE:{const e=n.createChild("span","webkit-html-doctype");g.UIUtils.createTextChild(e,"<!DOCTYPE "+t.nodeName()),t.publicId?(g.UIUtils.createTextChild(e,' PUBLIC "'+t.publicId+'"'),t.systemId&&g.UIUtils.createTextChild(e,' "'+t.systemId+'"')):t.systemId&&g.UIUtils.createTextChild(e,' SYSTEM "'+t.systemId+'"'),t.internalSubset&&g.UIUtils.createTextChild(e," ["+t.internalSubset+"]"),g.UIUtils.createTextChild(e,">");break}case Node.CDATA_SECTION_NODE:{const e=n.createChild("span","webkit-html-text-node");g.UIUtils.createTextChild(e,"<![CDATA["+t.nodeValue()+"]]>");break}case Node.DOCUMENT_FRAGMENT_NODE:n.createChild("span","webkit-html-fragment").textContent=l.StringUtilities.collapseWhitespace(t.nodeNameInCorrectCase());break;default:{const e=l.StringUtilities.collapseWhitespace(t.nodeNameInCorrectCase());g.UIUtils.createTextChild(n,e)}}return n}remove(){if(this.nodeInternal.pseudoType())return;this.parent&&this.nodeInternal.parentNode&&this.nodeInternal.parentNode.nodeType()!==Node.DOCUMENT_NODE&&this.nodeInternal.removeNode()}toggleEditAsHTML(e,t){if(this.editing&&this.htmlEditElement)return void this.editing.commit();if(!1===t)return;function n(t){e&&e(!t)}const i=this.nodeInternal;i.getOuterHTML().then(this.startEditingAsHTML.bind(this,(function(e,t){e!==t&&i.setOuterHTML(t,n)}),(function(){e&&e(!1)})))}copyCSSPath(){s.InspectorFrontendHost.InspectorFrontendHostInstance.copyText((0,v.cssPath)(this.nodeInternal,!0))}copyJSPath(){s.InspectorFrontendHost.InspectorFrontendHostInstance.copyText((0,v.jsPath)(this.nodeInternal,!0))}copyXPath(){s.InspectorFrontendHost.InspectorFrontendHostInstance.copyText((0,v.xPath)(this.nodeInternal,!0))}copyFullXPath(){s.InspectorFrontendHost.InspectorFrontendHostInstance.copyText((0,v.xPath)(this.nodeInternal,!1))}async copyStyles(){const e=this.nodeInternal,t=e.domModel().cssModel(),n=await t.cachedMatchedCascadeForNode(e);if(!n)return;const i=o.Settings.Settings.instance().moduleSetting("textEditorIndent").get(),r=[];for(const e of n.nodeStyles().reverse())for(const t of e.leadingProperties())t.parsedOk&&!t.disabled&&t.activeInStyle()&&!t.implicit&&(n.isInherited(e)&&!a.CSSMetadata.cssMetadata().isPropertyInherited(t.name)||e.parentRule&&e.parentRule.isUserAgent()||n.propertyState(t)===a.CSSMatchedStyles.PropertyState.Active&&r.push(`${i}${t.name}: ${t.value};`));s.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(r.join("\n"))}highlightSearchResultsInternal(){if(!this.searchQuery||!this.searchHighlightsVisible)return;this.hideSearchHighlight();const e=this.listItemElement.textContent||"",t=l.StringUtilities.createPlainTextSearchRegex(this.searchQuery,"gi");let n=t.exec(e);const i=[];for(;n;)i.push(new d.TextRange.SourceRange(n.index,n[0].length)),n=t.exec(e);i.length||i.push(new d.TextRange.SourceRange(0,e.length)),this.highlightResult=[],g.UIUtils.highlightSearchResults(this.listItemElement,i,this.highlightResult)}editAsHTML(){o.Revealer.reveal(this.node()).then((()=>{const e=g.ActionRegistry.ActionRegistry.instance().action("elements.edit-as-html");if(e)return e.execute()}))}adorn({name:e}){const t=document.createElement("span");t.textContent=e;const n=new h.Adorner;return n.data={name:e,content:t},this.adorners.push(n),b.ElementsPanel.instance().registerAdorner(n),this.updateAdorners(),n}removeAdorner(e){const t=this.adorners;b.ElementsPanel.instance().deregisterAdorner(e),e.remove();for(let n=0;n<t.length;++n)if(t[n]===e)return t.splice(n,1),void this.updateAdorners()}removeAllAdorners(){for(const e of this.adorners)b.ElementsPanel.instance().deregisterAdorner(e),e.remove();this.adorners=[],this.updateAdorners()}updateAdorners(){this.adornersThrottler.schedule(this.updateAdornersInternal.bind(this))}updateAdornersInternal(){const e=this.adornerContainer;if(!e)return Promise.resolve();const t=this.adorners;if(0===t.length)return e.classList.add("hidden"),Promise.resolve();t.sort(O),e.removeChildren();for(const n of t)e.appendChild(n);return e.classList.remove("hidden"),Promise.resolve()}async updateStyleAdorners(){if(this.isClosingTagInternal)return;const e=this.node(),t=e.id;if(e.nodeType()===Node.COMMENT_NODE||e.nodeType()===Node.DOCUMENT_FRAGMENT_NODE||e.nodeType()===Node.TEXT_NODE||void 0===t)return;const n=await e.domModel().cssModel().getComputedStyle(t);for(const e of this.styleAdorners)this.removeAdorner(e);if(this.styleAdorners=[],!n)return;const i=n.get("display"),o="grid"===i||"inline-grid"===i,s="flex"===i||"inline-flex"===i,r=n.get("container-type"),l=n.get("contain"),d=""!==a.CSSContainerQuery.getQueryAxis(`${r} ${l}`),c=e=>{e&&this.styleAdorners.push(e)};o&&c(this.createGridAdorner()),s&&c(this.createFlexAdorner()),n.get("scroll-snap-type")&&"none"!==n.get("scroll-snap-type")&&c(this.createScrollSnapAdorner()),d&&c(this.createContainerAdorner())}createGridAdorner(){const e=this.node(),t=e.id;if(!t)return null;const n=y.getRegisteredAdorner(y.RegisteredAdorners.GRID),i=this.adorn(n);i.classList.add("grid");return i.addInteraction((()=>{i.isActive()?e.domModel().overlayModel().highlightGridInPersistentOverlay(t):e.domModel().overlayModel().hideGridInPersistentOverlay(t)}),{isToggle:!0,shouldPropagateOnKeydown:!1,ariaLabelDefault:T(x.enableGridMode),ariaLabelActive:T(x.disableGridMode)}),e.domModel().overlayModel().addEventListener(a.OverlayModel.Events.PersistentGridOverlayStateChanged,(e=>{const{nodeId:n,enabled:o}=e.data;n===t&&i.toggle(o)})),i}createScrollSnapAdorner(){const e=this.node(),t=e.id;if(!t)return null;const n=y.getRegisteredAdorner(y.RegisteredAdorners.SCROLL_SNAP),i=this.adorn(n);i.classList.add("scroll-snap");return i.addInteraction((()=>{const n=e.domModel().overlayModel();i.isActive()?n.highlightScrollSnapInPersistentOverlay(t):n.hideScrollSnapInPersistentOverlay(t)}),{isToggle:!0,shouldPropagateOnKeydown:!1,ariaLabelDefault:T(x.enableScrollSnap),ariaLabelActive:T(x.disableScrollSnap)}),e.domModel().overlayModel().addEventListener(a.OverlayModel.Events.PersistentScrollSnapOverlayStateChanged,(e=>{const{nodeId:n,enabled:o}=e.data;n===t&&i.toggle(o)})),i}createFlexAdorner(){const e=this.node(),t=e.id;if(!t)return null;const n=y.getRegisteredAdorner(y.RegisteredAdorners.FLEX),i=this.adorn(n);i.classList.add("flex");return i.addInteraction((()=>{const n=e.domModel().overlayModel();i.isActive()?n.highlightFlexContainerInPersistentOverlay(t):n.hideFlexContainerInPersistentOverlay(t)}),{isToggle:!0,shouldPropagateOnKeydown:!1,ariaLabelDefault:T(x.enableFlexMode),ariaLabelActive:T(x.disableFlexMode)}),e.domModel().overlayModel().addEventListener(a.OverlayModel.Events.PersistentFlexContainerOverlayStateChanged,(e=>{const{nodeId:n,enabled:o}=e.data;n===t&&i.toggle(o)})),i}createContainerAdorner(){const e=this.node(),t=e.id;if(!t)return null;const n=y.getRegisteredAdorner(y.RegisteredAdorners.CONTAINER),i=this.adorn(n);i.classList.add("container");return i.addInteraction((()=>{const n=e.domModel().overlayModel();i.isActive()?n.highlightContainerQueryInPersistentOverlay(t):n.hideContainerQueryInPersistentOverlay(t)}),{isToggle:!0,shouldPropagateOnKeydown:!1,ariaLabelDefault:T(x.enableScrollSnap),ariaLabelActive:T(x.disableScrollSnap)}),e.domModel().overlayModel().addEventListener(a.OverlayModel.Events.PersistentContainerQueryOverlayStateChanged,(e=>{const{nodeId:n,enabled:o}=e.data;n===t&&i.toggle(o)})),i}}const N=500,I=new Set(["area","base","basefont","br","canvas","col","command","embed","frame","hr","img","input","keygen","link","menuitem","meta","param","source","track","wbr"]),P=new Set(["html","head","body"]);function O(e,t){const n=y.compareAdornerNamesByCategory(t.name,t.name);return 0===n?e.name.localeCompare(t.name):n}})),t.register("6pDhf",(function(n,i){e(n.exports,"Adorner",(()=>t("1T18D")));t("1T18D")})),t.register("1T18D",(function(n,i){e(n.exports,"Adorner",(()=>d));var o=t("kpUjp"),s=t("dS5IF"),r=t("hNk6R");const{render:l,html:a}=s;class d extends HTMLElement{static litTagName=s.literal`devtools-adorner`;name="";#e=this.attachShadow({mode:"open"});#Ze=!1;#et;#tt;#nt;set data(e){this.name=e.name,e.content.slot="content",this.#nt?.remove(),this.append(e.content),this.#nt=e.content,this.#u()}connectedCallback(){this.getAttribute("aria-label")||this.setAttribute("aria-label",this.name),this.#e.adoptedStyleSheets=[r.default]}isActive(){return"true"===this.getAttribute("aria-pressed")}toggle(e){if(!this.#Ze)return;const t=void 0===e?!this.isActive():e;this.setAttribute("aria-pressed",Boolean(t).toString()),this.setAttribute("aria-label",(t?this.#tt:this.#et)||this.name)}show(){this.classList.remove("hidden")}hide(){this.classList.add("hidden")}addInteraction(e,t){const{isToggle:n=!1,shouldPropagateOnKeydown:i=!1,ariaLabelDefault:o,ariaLabelActive:s}=t;this.#Ze=n,this.#et=o,this.#tt=s,this.setAttribute("aria-label",o),n&&(this.addEventListener("click",(()=>{this.toggle()})),this.toggle(!1)),this.addEventListener("click",e),this.classList.add("clickable"),this.setAttribute("role","button"),this.tabIndex=0,this.addEventListener("keydown",(e=>{"Enter"!==e.code&&"Space"!==e.code||(this.click(),i||e.stopPropagation())}))}#u(){l(a`
      <slot name="content"></slot>
    `,this.#e,{host:this})}}o.CustomElements.defineComponent("devtools-adorner",d)})),t.register("hNk6R",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync('/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n:host {\n  display: inline-flex;\n}\n\n:host(.hidden) {\n  display: none;\n}\n\nslot {\n  display: inline-flex;\n  box-sizing: border-box;\n  height: 14px;\n  line-height: 13px;\n  padding: 0 6px;\n  font-size: 8.5px;\n  color: var(--override-adorner-text-color, var(--color-text-primary));\n  background-color: var(--override-adorner-background-color, var(--color-background-elevation-1));\n  border: 1px solid var(--override-adorner-border-color, var(--color-details-hairline));\n  border-radius: 10px;\n}\n\n:host(:focus) slot {\n  border-color: var(--override-adorner-focus-border-color, var(--color-primary));\n}\n\n:host([aria-pressed="true"]) slot {\n  color: var(--override-adorner-active-text-color, var(--color-background));\n  background-color: var(--override-adorner-active-background-color, var(--color-primary));\n  border: 1px solid var(--override-adorner-active-background-color, var(--color-primary));\n}\n\n::slotted(*) {\n  height: 10px;\n}\n\n/*# sourceURL=adorner.css */\n');var o=i})),t.register("iADGB",(function(n,i){e(n.exports,"fullQualifiedSelector",(()=>s)),e(n.exports,"cssPath",(()=>r)),e(n.exports,"canGetJSPath",(()=>l)),e(n.exports,"jsPath",(()=>a)),e(n.exports,"Step",(()=>u)),e(n.exports,"xPath",(()=>c));var o=t("eQFvP");const s=function(e,t){return e.nodeType()!==Node.ELEMENT_NODE?e.localName()||e.nodeName().toLowerCase():r(e,t)},r=function(e,t){if(e.nodeType()!==Node.ELEMENT_NODE)return"";const n=[];let i=e;for(;i;){const o=d(i,Boolean(t),i===e);if(!o)break;if(n.push(o),o.optimized)break;i=i.parentNode}return n.reverse(),n.join(" > ")},l=function(e){let t=e;for(;t;){const e=t.ancestorShadowRoot();if(e&&e.shadowRootType()!==o.DOMModel.DOMNode.ShadowRootTypes.Open)return!1;t=t.ancestorShadowHost()}return!0},a=function(e,t){if(e.nodeType()!==Node.ELEMENT_NODE)return"";const n=[];let i=e;for(;i;)n.push(r(i,t)),i=i.ancestorShadowHost();n.reverse();let o="";for(let e=0;e<n.length;++e){const t=JSON.stringify(n[e]);o+=e?`.shadowRoot.querySelector(${t})`:`document.querySelector(${t})`}return o},d=function(e,t,n){if(e.nodeType()!==Node.ELEMENT_NODE)return null;const i=e.getAttribute("id");if(t){if(i)return new u(l(i),!0);const t=e.nodeName().toLowerCase();if("body"===t||"head"===t||"html"===t)return new u(e.nodeNameInCorrectCase(),!0)}const o=e.nodeNameInCorrectCase();if(i)return new u(o+l(i),!0);const s=e.parentNode;if(!s||s.nodeType()===Node.DOCUMENT_NODE)return new u(o,!0);function r(e){const t=e.getAttribute("class");return t?t.split(/\s+/g).filter(Boolean).map((function(e){return"$"+e})):[]}function l(e){return"#"+CSS.escape(e)}const a=r(e);let d=!1,c=!1,h=-1,p=-1;const m=s.children();for(let t=0;m&&(-1===h||!c)&&t<m.length;++t){const n=m[t];if(n.nodeType()!==Node.ELEMENT_NODE)continue;if(p+=1,n===e){h=p;continue}if(c)continue;if(n.nodeNameInCorrectCase()!==o)continue;d=!0;const i=new Set(a);if(!i.size){c=!0;continue}const s=r(n);for(let e=0;e<s.length;++e){const t=s[e];if(i.has(t)&&(i.delete(t),!i.size)){c=!0;break}}}let g=o;if(n&&"input"===o.toLowerCase()&&e.getAttribute("type")&&!e.getAttribute("id")&&!e.getAttribute("class")&&(g+="[type="+CSS.escape(e.getAttribute("type")||"")+"]"),c)g+=":nth-child("+(h+1)+")";else if(d)for(const e of a)g+="."+CSS.escape(e.slice(1));return new u(g,!1)},c=function(e,t){if(e.nodeType()===Node.DOCUMENT_NODE)return"/";const n=[];let i=e;for(;i;){const e=h(i,t);if(!e)break;if(n.push(e),e.optimized)break;i=i.parentNode}return n.reverse(),(n.length&&n[0].optimized?"":"/")+n.join("/")},h=function(e,t){let n;const i=p(e);if(-1===i)return null;switch(e.nodeType()){case Node.ELEMENT_NODE:if(t&&e.getAttribute("id"))return new u('//*[@id="'+e.getAttribute("id")+'"]',!0);n=e.localName();break;case Node.ATTRIBUTE_NODE:n="@"+e.nodeName();break;case Node.TEXT_NODE:case Node.CDATA_SECTION_NODE:n="text()";break;case Node.PROCESSING_INSTRUCTION_NODE:n="processing-instruction()";break;case Node.COMMENT_NODE:n="comment()";break;case Node.DOCUMENT_NODE:default:n=""}return i>0&&(n+="["+i+"]"),new u(n,e.nodeType()===Node.DOCUMENT_NODE)},p=function(e){function t(e,t){if(e===t)return!0;if(e.nodeType()===Node.ELEMENT_NODE&&t.nodeType()===Node.ELEMENT_NODE)return e.localName()===t.localName();if(e.nodeType()===t.nodeType())return!0;return(e.nodeType()===Node.CDATA_SECTION_NODE?Node.TEXT_NODE:e.nodeType())===(t.nodeType()===Node.CDATA_SECTION_NODE?Node.TEXT_NODE:t.nodeType())}const n=e.parentNode?e.parentNode.children():null;if(!n)return 0;let i;for(let o=0;o<n.length;++o)if(t(e,n[o])&&n[o]!==e){i=!0;break}if(!i)return 0;let o=1;for(let i=0;i<n.length;++i)if(t(e,n[i])){if(n[i]===e)return o;++o}return-1};class u{value;optimized;constructor(e,t){this.value=e,this.optimized=t||!1}toString(){return this.value}}})),t.register("Vxucs",(function(n,i){e(n.exports,"ElementsTreeOutline",(()=>b)),e(n.exports,"ShortcutTreeElement",(()=>w)),e(n.exports,"UpdateRecord",(()=>E)),e(n.exports,"MappedCharToEntity",(()=>S)),e(n.exports,"Renderer",(()=>x));var o=t("koSS8"),s=t("ixFnt"),r=t("eQFvP"),l=t("3T3mV"),a=t("9z2ZV"),d=t("h7UuJ"),c=t("1r4or"),h=t("4o2MM"),p=t("jlp2q"),u=t("hJQty");const m={pageDom:"Page DOM",storeAsGlobalVariable:"Store as global variable",showAllNodesDMore:"Show All Nodes ({PH1} More)",reveal:"reveal",adornerSettings:"Badge settings…"},g=s.i18n.registerUIStrings("panels/elements/ElementsTreeOutline.ts",m),f=s.i18n.getLocalizedString.bind(void 0,g),y=new WeakMap,v=new Set;class b extends(o.ObjectWrapper.eventMixin(a.TreeOutline.TreeOutline)){treeElementByNode;shadowRoot;elementInternal;includeRootDOMNode;selectEnabled;rootDOMNodeInternal;selectedDOMNodeInternal;visible;imagePreviewPopover;updateRecords;treeElementsBeingUpdated;decoratorExtensions;showHTMLCommentsSetting;multilineEditing;visibleWidthInternal;clipboardNodeData;isXMLMimeTypeInternal;suppressRevealAndSelect=!1;previousHoveredElement;treeElementBeingDragged;dragOverTreeElement;updateModifiedNodesTimeout;constructor(e,t,n){super(),this.treeElementByNode=new WeakMap;const i=document.createElement("div");this.shadowRoot=a.Utils.createShadowRootWithCoreStyles(i,{cssFile:[p.default,l.Style.default],delegatesFocus:void 0});const s=this.shadowRoot.createChild("div","elements-disclosure");this.elementInternal=this.element,this.elementInternal.classList.add("elements-tree-outline","source-code"),n&&this.elementInternal.classList.add("elements-hide-gutter"),a.ARIAUtils.setAccessibleName(this.elementInternal,f(m.pageDom)),this.elementInternal.addEventListener("focusout",this.onfocusout.bind(this),!1),this.elementInternal.addEventListener("mousedown",this.onmousedown.bind(this),!1),this.elementInternal.addEventListener("mousemove",this.onmousemove.bind(this),!1),this.elementInternal.addEventListener("mouseleave",this.onmouseleave.bind(this),!1),this.elementInternal.addEventListener("dragstart",this.ondragstart.bind(this),!1),this.elementInternal.addEventListener("dragover",this.ondragover.bind(this),!1),this.elementInternal.addEventListener("dragleave",this.ondragleave.bind(this),!1),this.elementInternal.addEventListener("drop",this.ondrop.bind(this),!1),this.elementInternal.addEventListener("dragend",this.ondragend.bind(this),!1),this.elementInternal.addEventListener("contextmenu",this.contextMenuEventFired.bind(this),!1),this.elementInternal.addEventListener("clipboard-beforecopy",this.onBeforeCopy.bind(this),!1),this.elementInternal.addEventListener("clipboard-copy",this.onCopyOrCut.bind(this,!1),!1),this.elementInternal.addEventListener("clipboard-cut",this.onCopyOrCut.bind(this,!0),!1),this.elementInternal.addEventListener("clipboard-paste",this.onPaste.bind(this),!1),this.elementInternal.addEventListener("keydown",this.onKeyDown.bind(this),!1),s.appendChild(this.elementInternal),this.element=i,this.includeRootDOMNode=!e,this.selectEnabled=t,this.rootDOMNodeInternal=null,this.selectedDOMNodeInternal=null,this.visible=!1,this.imagePreviewPopover=new(0,u.ImagePreviewPopover)(this.contentElement,(e=>{let t=e.target;for(;t&&!u.ImagePreviewPopover.getImageURL(t);)t=t.parentElementOrShadowHost();return t}),(e=>{const t=a.UIUtils.enclosingNodeOrSelfWithNodeName(e,"li");if(!t)return null;const n=a.TreeOutline.TreeElement.getTreeElementBylistItemNode(t);return n?n.node():null})),this.updateRecords=new Map,this.treeElementsBeingUpdated=new Set,this.decoratorExtensions=null,this.showHTMLCommentsSetting=o.Settings.Settings.instance().moduleSetting("showHTMLComments"),this.showHTMLCommentsSetting.addChangeListener(this.onShowHTMLCommentsChange.bind(this)),this.setUseLightSelectionColor(!0)}static forDOMModel(e){return y.get(e)||null}onShowHTMLCommentsChange(){const e=this.selectedDOMNode();e&&e.nodeType()===Node.COMMENT_NODE&&!this.showHTMLCommentsSetting.get()&&this.selectDOMNode(e.parentNode),this.update()}setWordWrap(e){this.elementInternal.classList.toggle("elements-tree-nowrap",!e)}setMultilineEditing(e){this.multilineEditing=e}visibleWidth(){return this.visibleWidthInternal||0}setVisibleWidth(e){this.visibleWidthInternal=e,this.multilineEditing&&this.multilineEditing.resize()}setClipboardData(e){if(this.clipboardNodeData){const e=this.findTreeElement(this.clipboardNodeData.node);e&&e.setInClipboard(!1),delete this.clipboardNodeData}if(e){const t=this.findTreeElement(e.node);t&&t.setInClipboard(!0),this.clipboardNodeData=e}}resetClipboardIfNeeded(e){this.clipboardNodeData&&this.clipboardNodeData.node===e&&this.setClipboardData(null)}onBeforeCopy(e){e.handled=!0}onCopyOrCut(e,t){this.setClipboardData(null);const n=t.original;if(!n||!n.target)return;if(n.target instanceof Node&&n.target.hasSelection())return;if(a.UIUtils.isEditing())return;const i=this.selectedDOMNode();i&&n.clipboardData&&(n.clipboardData.clearData(),t.handled=!0,this.performCopyOrCut(e,i))}performCopyOrCut(e,t){t&&(e&&(t.isShadowRoot()||t.ancestorUserAgentShadowRoot())||(t.copyNode(),this.setClipboardData({node:t,isCut:e})))}canPaste(e){if(e.isShadowRoot()||e.ancestorUserAgentShadowRoot())return!1;if(!this.clipboardNodeData)return!1;const t=this.clipboardNodeData.node;return(!this.clipboardNodeData.isCut||t!==e&&!t.isAncestor(e))&&e.domModel()===t.domModel()}pasteNode(e){this.canPaste(e)&&this.performPaste(e)}duplicateNode(e){this.performDuplicate(e)}onPaste(e){if(a.UIUtils.isEditing())return;const t=this.selectedDOMNode();t&&this.canPaste(t)&&(e.handled=!0,this.performPaste(t))}performPaste(e){function t(e,t){!e&&t&&this.selectDOMNode(t)}this.clipboardNodeData&&(this.clipboardNodeData.isCut?(this.clipboardNodeData.node.moveTo(e,null,t.bind(this)),this.setClipboardData(null)):this.clipboardNodeData.node.copyTo(e,null,t.bind(this)))}performDuplicate(e){if(e.isInShadowTree())return;const t=e.parentNode?e.parentNode:e;"#document"!==t.nodeName()&&e.copyTo(t,e.nextSibling)}setVisible(e){if(e!==this.visible){if(this.visible=e,!this.visible)return this.imagePreviewPopover.hide(),void(this.multilineEditing&&this.multilineEditing.cancel());this.runPendingUpdates(),this.selectedDOMNodeInternal&&this.revealAndSelectNode(this.selectedDOMNodeInternal,!1)}}get rootDOMNode(){return this.rootDOMNodeInternal}set rootDOMNode(e){this.rootDOMNodeInternal!==e&&(this.rootDOMNodeInternal=e,this.isXMLMimeTypeInternal=e&&e.isXMLNode(),this.update())}get isXMLMimeType(){return Boolean(this.isXMLMimeTypeInternal)}selectedDOMNode(){return this.selectedDOMNodeInternal}selectDOMNode(e,t){this.selectedDOMNodeInternal!==e?(this.selectedDOMNodeInternal=e,this.revealAndSelectNode(e,!t),this.selectedDOMNodeInternal===e&&this.selectedNodeChanged(Boolean(t))):this.revealAndSelectNode(e,!t)}editing(){const e=this.selectedDOMNode();if(!e)return!1;const t=this.findTreeElement(e);return t&&t.isEditing()||!1}update(){const e=this.selectedDOMNode();if(this.removeChildren(),this.rootDOMNode){if(this.includeRootDOMNode){const e=this.createElementTreeElement(this.rootDOMNode);this.appendChild(e)}else{const e=this.visibleChildren(this.rootDOMNode);for(const t of e){const e=this.createElementTreeElement(t);this.appendChild(e)}}e&&this.revealAndSelectNode(e,!0)}}selectedNodeChanged(e){this.dispatchEventToListeners(b.Events.SelectedNodeChanged,{node:this.selectedDOMNodeInternal,focus:e})}fireElementsTreeUpdated(e){this.dispatchEventToListeners(b.Events.ElementsTreeUpdated,e)}findTreeElement(e){let t=this.lookUpTreeElement(e);return t||e.nodeType()!==Node.TEXT_NODE||(t=this.lookUpTreeElement(e.parentNode)),t}lookUpTreeElement(e){if(!e)return null;const t=this.treeElementByNode.get(e);if(t)return t;const n=[];let i;for(i=e.parentNode;i&&(n.push(i),!this.treeElementByNode.has(i));i=i.parentNode);if(!i)return null;for(let t=n.length-1;t>=0;--t){const i=n[t-1]||e,o=this.treeElementByNode.get(n[t]);o&&(o.onpopulate(),i.index&&i.index>=o.expandedChildrenLimit()&&this.setExpandedChildrenLimit(o,i.index+1))}return this.treeElementByNode.get(e)||null}createTreeElementFor(e){let t=this.findTreeElement(e);return t||(e.parentNode?(t=this.createTreeElementFor(e.parentNode),t?this.showChild(t,e):null):null)}revealAndSelectNode(e,t){if(this.suppressRevealAndSelect)return;if(!this.includeRootDOMNode&&e===this.rootDOMNode&&this.rootDOMNode&&(e=this.rootDOMNode.firstChild),!e)return;const n=this.createTreeElementFor(e);n&&n.revealAndSelect(t)}treeElementFromEventInternal(e){const t=this.element.parentElement;if(!t)return null;const n=t.totalOffsetLeft()+t.offsetWidth-18,i=e.pageY,o=this.treeElementFromPoint(n,i);let s;return s=o===this.treeElementFromPoint(n,i-2)?o:this.treeElementFromPoint(n,i+2),s}onfocusout(e){r.OverlayModel.OverlayModel.hideDOMNodeHighlight()}onmousedown(e){const t=this.treeElementFromEventInternal(e);t&&!t.isEventWithinDisclosureTriangle(e)&&t.select()}setHoverEffect(e){this.previousHoveredElement!==e&&(this.previousHoveredElement instanceof h.ElementsTreeElement&&(this.previousHoveredElement.hovered=!1,delete this.previousHoveredElement),e instanceof h.ElementsTreeElement&&(e.hovered=!0,this.previousHoveredElement=e))}onmousemove(e){const t=this.treeElementFromEventInternal(e);t&&this.previousHoveredElement===t||(this.setHoverEffect(t),this.highlightTreeElement(t,!a.KeyboardShortcut.KeyboardShortcut.eventHasEitherCtrlOrMeta(e)))}highlightTreeElement(e,t){e instanceof h.ElementsTreeElement?e.node().domModel().overlayModel().highlightInOverlay({node:e.node(),selectorList:void 0},"all",t):e instanceof w&&e.domModel().overlayModel().highlightInOverlay({deferredNode:e.deferredNode(),selectorList:void 0},"all",t)}onmouseleave(e){this.setHoverEffect(null),r.OverlayModel.OverlayModel.hideDOMNodeHighlight()}ondragstart(e){const t=e.target;if(!t||t.hasSelection())return!1;if("A"===t.nodeName)return!1;const n=this.validDragSourceOrTarget(this.treeElementFromEventInternal(e));return!!n&&("BODY"!==n.node().nodeName()&&"HEAD"!==n.node().nodeName()&&(e.dataTransfer&&n.listItemElement.textContent?(e.dataTransfer.setData("text/plain",n.listItemElement.textContent.replace(/\u200b/g,"")),e.dataTransfer.effectAllowed="copyMove",this.treeElementBeingDragged=n,r.OverlayModel.OverlayModel.hideDOMNodeHighlight(),!0):void 0))}ondragover(e){if(!this.treeElementBeingDragged)return!1;const t=this.validDragSourceOrTarget(this.treeElementFromEventInternal(e));if(!t)return!1;let n=t.node();for(;n;){if(n===this.treeElementBeingDragged.nodeInternal)return!1;n=n.parentNode}return t.listItemElement.classList.add("elements-drag-over"),this.dragOverTreeElement=t,e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect="move"),!1}ondragleave(e){return this.clearDragOverTreeElementMarker(),e.preventDefault(),!1}validDragSourceOrTarget(e){if(!e)return null;if(!(e instanceof h.ElementsTreeElement))return null;const t=e,n=t.node();return n.parentNode&&n.parentNode.nodeType()===Node.ELEMENT_NODE?t:null}ondrop(e){e.preventDefault();const t=this.treeElementFromEventInternal(e);t instanceof h.ElementsTreeElement&&this.doMove(t)}doMove(e){if(!this.treeElementBeingDragged)return;let t,n;if(e.isClosingTag())t=e.node();else{const i=e.node();t=i.parentNode,n=i}if(!t||!n)return;const i=this.treeElementBeingDragged.expanded;this.treeElementBeingDragged.nodeInternal.moveTo(t,n,this.selectNodeAfterEdit.bind(this,i)),delete this.treeElementBeingDragged}ondragend(e){e.preventDefault(),this.clearDragOverTreeElementMarker(),delete this.treeElementBeingDragged}clearDragOverTreeElementMarker(){this.dragOverTreeElement&&(this.dragOverTreeElement.listItemElement.classList.remove("elements-drag-over"),delete this.dragOverTreeElement)}contextMenuEventFired(e){const t=this.treeElementFromEventInternal(e);t instanceof h.ElementsTreeElement&&this.showContextMenu(t,e)}showContextMenu(e,t){if(a.UIUtils.isEditing())return;const n=new a.ContextMenu.ContextMenu(t),i=Boolean(e.node().pseudoType()),o=e.node().nodeType()===Node.ELEMENT_NODE&&!i,s=t.target;if(!s)return;let r=s.enclosingNodeOrSelfWithClass("webkit-html-text-node");r&&r.classList.contains("bogus")&&(r=null);const l=s.enclosingNodeOrSelfWithClass("webkit-html-comment");n.saveSection().appendItem(f(m.storeAsGlobalVariable),this.saveNodeToTempVariable.bind(this,e.node())),r?e.populateTextContextMenu(n,r):o?e.populateTagContextMenu(n,t):l?e.populateNodeContextMenu(n):i&&e.populateScrollIntoView(n),n.viewSection().appendItem(f(m.adornerSettings),(()=>{c.ElementsPanel.instance().showAdornerSettingsPane()})),n.appendApplicableItems(e.node()),n.show()}async saveNodeToTempVariable(e){const t=await e.resolveToObject();await r.ConsoleModel.ConsoleModel.instance().saveToTempVariable(a.Context.Context.instance().flavor(r.RuntimeModel.ExecutionContext),t)}runPendingUpdates(){this.updateModifiedNodes()}onKeyDown(e){const t=e;if(a.UIUtils.isEditing())return;const n=this.selectedDOMNode();if(!n)return;const i=this.treeElementByNode.get(n);if(i&&a.KeyboardShortcut.KeyboardShortcut.eventHasCtrlEquivalentKey(t)&&n.parentNode){if("ArrowUp"===t.key&&n.previousSibling)return n.moveTo(n.parentNode,n.previousSibling,this.selectNodeAfterEdit.bind(this,i.expanded)),void t.consume(!0);if("ArrowDown"===t.key&&n.nextSibling)return n.moveTo(n.parentNode,n.nextSibling.nextSibling,this.selectNodeAfterEdit.bind(this,i.expanded)),void t.consume(!0)}}toggleEditAsHTML(e,t,n){const i=this.treeElementByNode.get(e);if(!i||!i.hasEditableNode())return;if(e.pseudoType())return;const o=e.parentNode,s=e.index,r=i.expanded;i.toggleEditAsHTML(function(e){n&&n();if(!e)return;if(this.runPendingUpdates(),!s)return;const t=o&&o.children(),i=t&&t[s]||o;if(!i)return;if(this.selectDOMNode(i,!0),r){const e=this.findTreeElement(i);e&&e.expand()}}.bind(this),t)}selectNodeAfterEdit(e,t,n){if(t)return null;if(this.runPendingUpdates(),!n)return null;this.selectDOMNode(n,!0);const i=this.findTreeElement(n);return e&&i&&i.expand(),i}async toggleHideElement(e){const t=e.pseudoType(),n=t?e.parentNode:e;if(!n)return;const i=e.marker("hidden-marker"),o=await n.resolveToObject("");o&&(await o.callFunction((function(e,t){const n="__web-inspector-hide-shortcut-style__",i=[];i.push(".__web-inspector-hide-shortcut__"),i.push(".__web-inspector-hide-shortcut__ *"),i.push(".__web-inspector-hidebefore-shortcut__::before"),i.push(".__web-inspector-hideafter-shortcut__::after");const o=i.join(", "),s="\n"+o+"\n{\n    visibility: hidden !important;\n}\n",r="__web-inspector-hide"+(e||"")+"-shortcut__";this.classList.toggle(r,t);let l=this;for(;l.parentNode;)l=l.parentNode;l.nodeType===Node.DOCUMENT_NODE&&(l=document.head);let a=l.querySelector("style#"+n);if(a)return;a=document.createElement("style"),a.id=n,a.textContent=s,l.appendChild(a)}),[{value:t},{value:!i}]),o.release(),e.setMarker("hidden-marker",!i||null))}isToggledToHidden(e){return Boolean(e.marker("hidden-marker"))}reset(){this.rootDOMNode=null,this.selectDOMNode(null,!1),this.imagePreviewPopover.hide(),delete this.clipboardNodeData,r.OverlayModel.OverlayModel.hideDOMNodeHighlight(),this.updateRecords.clear()}wireToDOMModel(e){y.set(e,this),e.addEventListener(r.DOMModel.Events.MarkersChanged,this.markersChanged,this),e.addEventListener(r.DOMModel.Events.NodeInserted,this.nodeInserted,this),e.addEventListener(r.DOMModel.Events.NodeRemoved,this.nodeRemoved,this),e.addEventListener(r.DOMModel.Events.AttrModified,this.attributeModified,this),e.addEventListener(r.DOMModel.Events.AttrRemoved,this.attributeRemoved,this),e.addEventListener(r.DOMModel.Events.CharacterDataModified,this.characterDataModified,this),e.addEventListener(r.DOMModel.Events.DocumentUpdated,this.documentUpdated,this),e.addEventListener(r.DOMModel.Events.ChildNodeCountUpdated,this.childNodeCountUpdated,this),e.addEventListener(r.DOMModel.Events.DistributedNodesChanged,this.distributedNodesChanged,this)}unwireFromDOMModel(e){e.removeEventListener(r.DOMModel.Events.MarkersChanged,this.markersChanged,this),e.removeEventListener(r.DOMModel.Events.NodeInserted,this.nodeInserted,this),e.removeEventListener(r.DOMModel.Events.NodeRemoved,this.nodeRemoved,this),e.removeEventListener(r.DOMModel.Events.AttrModified,this.attributeModified,this),e.removeEventListener(r.DOMModel.Events.AttrRemoved,this.attributeRemoved,this),e.removeEventListener(r.DOMModel.Events.CharacterDataModified,this.characterDataModified,this),e.removeEventListener(r.DOMModel.Events.DocumentUpdated,this.documentUpdated,this),e.removeEventListener(r.DOMModel.Events.ChildNodeCountUpdated,this.childNodeCountUpdated,this),e.removeEventListener(r.DOMModel.Events.DistributedNodesChanged,this.distributedNodesChanged,this),y.delete(e)}addUpdateRecord(e){let t=this.updateRecords.get(e);return t||(t=new E,this.updateRecords.set(e,t)),t}updateRecordForHighlight(e){return this.visible&&this.updateRecords.get(e)||null}documentUpdated(e){const t=e.data;this.reset(),t.existingDocument()&&(this.rootDOMNode=t.existingDocument())}attributeModified(e){const{node:t}=e.data;this.addUpdateRecord(t).attributeModified(e.data.name),this.updateModifiedNodesSoon()}attributeRemoved(e){const{node:t}=e.data;this.addUpdateRecord(t).attributeRemoved(e.data.name),this.updateModifiedNodesSoon()}characterDataModified(e){const t=e.data;this.addUpdateRecord(t).charDataModified(),t.parentNode&&t.parentNode.firstChild===t.parentNode.lastChild&&this.addUpdateRecord(t.parentNode).childrenModified(),this.updateModifiedNodesSoon()}nodeInserted(e){const t=e.data;this.addUpdateRecord(t.parentNode).nodeInserted(t),this.updateModifiedNodesSoon()}nodeRemoved(e){const{node:t,parent:n}=e.data;this.resetClipboardIfNeeded(t),this.addUpdateRecord(n).nodeRemoved(t),this.updateModifiedNodesSoon()}childNodeCountUpdated(e){const t=e.data;this.addUpdateRecord(t).childrenModified(),this.updateModifiedNodesSoon()}distributedNodesChanged(e){const t=e.data;this.addUpdateRecord(t).childrenModified(),this.updateModifiedNodesSoon()}updateModifiedNodesSoon(){this.updateRecords.size&&(this.updateModifiedNodesTimeout||(this.updateModifiedNodesTimeout=window.setTimeout(this.updateModifiedNodes.bind(this),50)))}updateModifiedNodes(){this.updateModifiedNodesTimeout&&(clearTimeout(this.updateModifiedNodesTimeout),delete this.updateModifiedNodesTimeout);const e=[...this.updateRecords.keys()],t=e.length>10;let n,i;t&&(n=this.element.parentNode,i=n?n.scrollTop:0,this.elementInternal.classList.add("hidden"));const o=this.rootDOMNodeInternal&&this.updateRecords.get(this.rootDOMNodeInternal);if(o&&o.hasChangedChildren())this.update();else for(const[e,t]of this.updateRecords)t.hasChangedChildren()?this.updateModifiedParentNode(e):this.updateModifiedNode(e);t&&(this.elementInternal.classList.remove("hidden"),n&&i&&(n.scrollTop=i)),this.updateRecords.clear(),this.fireElementsTreeUpdated(e)}updateModifiedNode(e){const t=this.findTreeElement(e);t&&t.updateTitle(this.updateRecordForHighlight(e))}updateModifiedParentNode(e){const t=this.findTreeElement(e);t&&(t.setExpandable(this.hasVisibleChildren(e)),t.updateTitle(this.updateRecordForHighlight(e)),v.has(t)&&this.updateChildren(t))}populateTreeElement(e){return e.childCount()||!e.isExpandable()?Promise.resolve():new Promise((t=>{e.node().getChildNodes((()=>{v.add(e),this.updateModifiedParentNode(e.node()),t()}))}))}createElementTreeElement(e,t){const n=new(0,h.ElementsTreeElement)(e,t);return n.setExpandable(!t&&this.hasVisibleChildren(e)),e.nodeType()===Node.ELEMENT_NODE&&e.parentNode&&e.parentNode.nodeType()===Node.DOCUMENT_NODE&&!e.parentNode.parentNode&&n.setCollapsible(!1),n.selectable=Boolean(this.selectEnabled),n}showChild(e,t){if(e.isClosingTag())return null;const n=this.visibleChildren(e.node()).indexOf(t);return-1===n?null:(n>=e.expandedChildrenLimit()&&this.setExpandedChildrenLimit(e,n+1),e.childAt(n))}visibleChildren(e){let t=h.ElementsTreeElement.visibleShadowRoots(e);const n=e.contentDocument();n&&t.push(n);const i=e.templateContent();i&&t.push(i);const o=e.markerPseudoElement();o&&t.push(o);const s=e.beforePseudoElement();if(s&&t.push(s),e.childNodeCount()){let n=e.children()||[];this.showHTMLCommentsSetting.get()||(n=n.filter((e=>e.nodeType()!==Node.COMMENT_NODE))),t=t.concat(n)}const r=e.afterPseudoElement();return r&&t.push(r),t}hasVisibleChildren(e){return!!e.isIframe()||(!!e.isPortal()||(!!e.contentDocument()||(!!e.templateContent()||(!!h.ElementsTreeElement.visibleShadowRoots(e).length||(!!e.hasPseudoElements()||(!!e.isInsertionPoint()||Boolean(e.childNodeCount())&&!h.ElementsTreeElement.canShowInlineText(e)))))))}createExpandAllButtonTreeElement(e){const t=a.UIUtils.createTextButton("",function(t){const n=this.visibleChildren(e.node()).length;this.setExpandedChildrenLimit(e,Math.max(n,e.expandedChildrenLimit()+h.InitialChildrenLimit)),t.consume()}.bind(this));t.value="";const n=new a.TreeOutline.TreeElement(t);return n.selectable=!1,n.button=t,n}setExpandedChildrenLimit(e,t){e.expandedChildrenLimit()!==t&&(e.setExpandedChildrenLimit(t),e.treeOutline&&!this.treeElementsBeingUpdated.has(e)&&this.updateModifiedParentNode(e.node()))}updateChildren(e){if(!e.isExpandable()){if(!e.treeOutline)return;const t=e.treeOutline.selectedTreeElement;return t&&t.hasAncestor(e)&&e.select(!0),void e.removeChildren()}console.assert(!e.isClosingTag()),this.innerUpdateChildren(e)}insertChildElement(e,t,n,i){const o=this.createElementTreeElement(t,i);return e.insertChild(o,n),o}moveChild(e,t,n){if(e.indexOfChild(t)===n)return;const i=t.selected;t.parent&&t.parent.removeChild(t),e.insertChild(t,n),i&&t.select()}innerUpdateChildren(e){if(this.treeElementsBeingUpdated.has(e))return;this.treeElementsBeingUpdated.add(e);const t=e.node(),n=this.visibleChildren(t),i=new Set(n),o=new Map;for(let t=e.childCount()-1;t>=0;--t){const n=e.childAt(t);if(!(n instanceof h.ElementsTreeElement)){e.removeChildAtIndex(t);continue}const s=n.node();i.has(s)?o.set(s,n):e.removeChildAtIndex(t)}for(let i=0;i<n.length&&i<e.expandedChildrenLimit();++i){const s=n[i],r=o.get(s)||this.findTreeElement(s);if(r&&r!==e)this.moveChild(e,r,i);else{const n=this.insertChildElement(e,s,i);this.updateRecordForHighlight(t)&&e.expanded&&h.ElementsTreeElement.animateOnDOMUpdate(n),e.childCount()>e.expandedChildrenLimit()&&this.setExpandedChildrenLimit(e,e.expandedChildrenLimit()+1)}}const s=e.childCount();if(n.length>s){const t=s;e.expandAllButtonElement||(e.expandAllButtonElement=this.createExpandAllButtonTreeElement(e)),e.insertChild(e.expandAllButtonElement,t),e.expandAllButtonElement.title=f(m.showAllNodesDMore,{PH1:n.length-s})}else e.expandAllButtonElement&&(e.expandAllButtonElement=null);if(t.isInsertionPoint())for(const n of t.distributedNodes())e.appendChild(new w(n));t.nodeType()===Node.ELEMENT_NODE&&!t.pseudoType()&&e.isExpandable()&&this.insertChildElement(e,t,e.childCount(),!0),this.treeElementsBeingUpdated.delete(e)}markersChanged(e){const t=e.data,n=this.treeElementByNode.get(t);n&&n.updateDecorations()}static treeOutlineSymbol=Symbol("treeOutline")}!function(e){let t;var n;(n=t=e.Events||(e.Events={})).SelectedNodeChanged="SelectedNodeChanged",n.ElementsTreeUpdated="ElementsTreeUpdated"}(b||(b={}));const S=new Map([[" ","nbsp"],["­","shy"],[" ","ensp"],[" ","emsp"],[" ","thinsp"],[" ","hairsp"],["​","ZeroWidthSpace"],["‌","zwnj"],["‍","zwj"],["‎","lrm"],["‏","rlm"],["‪","#x202A"],["‫","#x202B"],["‬","#x202C"],["‭","#x202D"],["‮","#x202E"],["⁠","NoBreak"],["\ufeff","#xFEFF"]]);class E{modifiedAttributes;removedAttributes;hasChangedChildrenInternal;hasRemovedChildrenInternal;charDataModifiedInternal;attributeModified(e){this.removedAttributes&&this.removedAttributes.has(e)&&this.removedAttributes.delete(e),this.modifiedAttributes||(this.modifiedAttributes=new Set),this.modifiedAttributes.add(e)}attributeRemoved(e){this.modifiedAttributes&&this.modifiedAttributes.has(e)&&this.modifiedAttributes.delete(e),this.removedAttributes||(this.removedAttributes=new Set),this.removedAttributes.add(e)}nodeInserted(e){this.hasChangedChildrenInternal=!0}nodeRemoved(e){this.hasChangedChildrenInternal=!0,this.hasRemovedChildrenInternal=!0}charDataModified(){this.charDataModifiedInternal=!0}childrenModified(){this.hasChangedChildrenInternal=!0}isAttributeModified(e){return null!==this.modifiedAttributes&&void 0!==this.modifiedAttributes&&this.modifiedAttributes.has(e)}hasRemovedAttributes(){return null!==this.removedAttributes&&void 0!==this.removedAttributes&&Boolean(this.removedAttributes.size)}isCharDataModified(){return Boolean(this.charDataModifiedInternal)}hasChangedChildren(){return Boolean(this.hasChangedChildrenInternal)}hasRemovedChildren(){return Boolean(this.hasRemovedChildrenInternal)}}let C;class x{static instance(e={forceNew:null}){const{forceNew:t}=e;return C&&!t||(C=new x),C}async render(e){let t=null;if(e instanceof r.DOMModel.DOMNode?t=e:e instanceof r.DOMModel.DeferredDOMNode&&(t=await e.resolvePromise()),!t)return null;const n=new b(!1,!0,!0);n.rootDOMNode=t;const i=n.firstChild();return i&&!i.isExpandable()&&n.element.classList.add("single-node"),n.setVisible(!0),n.element.treeElementForTest=i,n.setShowSelectionOnKeyboardFocus(!0,!0),{node:n.element,tree:n}}}class w extends a.TreeOutline.TreeElement{nodeShortcut;hoveredInternal;constructor(e){super(""),this.listItemElement.createChild("div","selection fill");const t=this.listItemElement.createChild("span","elements-tree-shortcut-title");let n=e.nodeName.toLowerCase();e.nodeType===Node.ELEMENT_NODE&&(n="<"+n+">"),t.textContent="↪ "+n;const i=(0,d.linkifyDeferredNodeReference)(e.deferredNode);a.UIUtils.createTextChild(this.listItemElement," "),i.classList.add("elements-tree-shortcut-link"),i.textContent=f(m.reveal),this.listItemElement.appendChild(i),this.nodeShortcut=e}get hovered(){return Boolean(this.hoveredInternal)}set hovered(e){this.hoveredInternal!==e&&(this.hoveredInternal=e,this.listItemElement.classList.toggle("hovered",e))}deferredNode(){return this.nodeShortcut.deferredNode}domModel(){return this.nodeShortcut.deferredNode.domModel()}onselect(e){if(!e)return!0;return this.nodeShortcut.deferredNode.highlight(),this.nodeShortcut.deferredNode.resolve(function(e){e&&this.treeOutline instanceof b&&(this.treeOutline.selectedDOMNodeInternal=e,this.treeOutline.selectedNodeChanged(!1))}.bind(this)),!0}}})),t.register("jlp2q",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync('/*\n * Copyright (c) 2014 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.editing {\n  box-shadow: var(--drop-shadow);\n  background-color: var(--color-background);\n  text-overflow: clip !important; /* stylelint-disable-line declaration-no-important */\n  padding-left: 2px;\n  margin-left: -2px;\n  padding-right: 2px;\n  margin-right: -2px;\n  margin-bottom: -1px;\n  padding-bottom: 1px;\n  opacity: 100% !important; /* stylelint-disable-line declaration-no-important */\n}\n\n.editing,\n.editing * {\n  color: var(--color-text-primary) !important; /* stylelint-disable-line declaration-no-important */\n  text-decoration: none !important; /* stylelint-disable-line declaration-no-important */\n}\n\n.editing br {\n  display: none;\n}\n\n.elements-disclosure {\n  width: 100%;\n  display: inline-block;\n  line-height: normal;\n}\n\n.elements-disclosure li {\n  /** Keep margin-left & padding-left in sync with ElementsTreeElements.updateDecorations **/\n  padding: 1px 0 0 14px;\n  margin-left: -2px;\n  word-break: normal;\n  position: relative;\n  min-height: 15px;\n  line-height: 1.36;\n  min-width: 200px;\n}\n\n.elements-disclosure li.parent {\n  display: flex;\n}\n\n.elements-disclosure li.parent:not(.always-parent) {\n  /** Keep it in sync with ElementsTreeElements.updateDecorations **/\n  margin-left: -12px;\n}\n\n.elements-disclosure li .selected-hint::before {\n  font-style: italic;\n  content: " == $0";\n  opacity: 0%;\n  position: absolute;\n  white-space: pre;\n}\n\n.elements-disclosure .elements-tree-outline:not(.hide-selection-when-blurred) li.selected .selected-hint::before {\n  position: static;\n  opacity: 60%;\n}\n\n.elements-disclosure li.parent:not(.always-parent)::before {\n  box-sizing: border-box;\n  user-select: none;\n  -webkit-mask-image: var(--image-file-treeoutlineTriangles);\n  -webkit-mask-size: 32px 24px;\n  height: 16px;\n  content: "\\A0\\A0";\n  color: transparent;\n  text-shadow: none;\n  margin-right: -3px;\n  -webkit-mask-position: 0 0;\n  background-color: var(--color-text-secondary);\n}\n\n.elements-disclosure li.parent.expanded::before {\n  -webkit-mask-position: -16px 0;\n}\n\n.elements-disclosure li .selection {\n  display: none;\n  z-index: -1;\n}\n\n.elements-disclosure li.selected .selection {\n  display: block;\n}\n\n.elements-disclosure li.elements-drag-over .selection {\n  display: block;\n  margin-top: -2px;\n  border-top: 2px solid var(--legacy-selection-bg-color);\n}\n\n.elements-disclosure .elements-tree-outline:not(.hide-selection-when-blurred) .selection {\n  background-color: var(--legacy-item-selection-inactive-bg-color);\n}\n\n.elements-disclosure li.hovered:not(.selected) .selection {\n  display: block;\n  left: 3px;\n  right: 3px;\n  background-color: var(--item-hover-color);\n  border-radius: 5px;\n}\n\n.elements-disclosure li .webkit-html-tag.close {\n  margin-left: -12px;\n}\n\n.elements-disclosure .elements-tree-outline.hide-selection-when-blurred .selected:focus-visible .highlight > * {\n  background: var(--legacy-focus-bg-color);\n  border-radius: 2px;\n  box-shadow: 0 0 0 2px var(--legacy-focus-bg-color);\n}\n\n.elements-disclosure .elements-tree-outline:not(.hide-selection-when-blurred) li.selected:focus .selection {\n  background-color: var(--legacy-item-selection-bg-color);\n}\n\n.elements-disclosure ol {\n  list-style-type: none;\n  /** Keep it in sync with ElementsTreeElements.updateDecorations **/\n  padding-inline-start: 12px;\n  margin: 0;\n}\n\n.elements-disclosure ol.children {\n  display: none;\n  min-width: 100%;\n}\n\n.elements-disclosure ol.children.expanded {\n  display: inline-block;\n}\n\n.elements-disclosure > ol {\n  position: relative;\n  margin: 0;\n  min-width: 100%;\n  min-height: 100%;\n  padding-left: 2px;\n}\n\n.elements-disclosure li.in-clipboard .highlight {\n  outline: 1px dotted var(--color-details-hairline);\n}\n\n.elements-tree-outline ol.shadow-root-depth-4 {\n  --override-shadow-root-background-color: rgb(0 0 0 / 4%);\n\n  background-color: var(--override-shadow-root-background-color);\n}\n\n.elements-tree-outline ol.shadow-root-depth-3 {\n  --override-shadow-root-background-color: rgb(0 0 0 / 3%);\n\n  background-color: var(--override-shadow-root-background-color);\n}\n\n.elements-tree-outline ol.shadow-root-depth-2 {\n  --override-shadow-root-background-color: rgb(0 0 0 / 2%);\n\n  background-color: var(--override-shadow-root-background-color);\n}\n\n.elements-tree-outline ol.shadow-root-depth-1 {\n  --override-shadow-root-background-color: rgb(0 0 0 / 1%);\n\n  background-color: var(--override-shadow-root-background-color);\n}\n\n.elements-tree-outline ol.shadow-root-deep {\n  background-color: transparent;\n}\n\n.elements-tree-editor {\n  box-shadow: var(--drop-shadow);\n  margin-right: 4px;\n}\n\nbutton,\ninput,\nselect {\n  font-family: inherit;\n  font-size: inherit;\n}\n\n.elements-gutter-decoration {\n  position: absolute;\n  top: 3px;\n  left: 2px;\n  height: 9px;\n  width: 9px;\n  border-radius: 5px;\n  border: 1px solid var(--issue-color-yellow);\n  background-color: var(--issue-color-yellow);\n}\n\n.elements-gutter-decoration.elements-has-decorated-children {\n  opacity: 50%;\n}\n\n.add-attribute {\n  margin-left: 1px;\n  margin-right: 1px;\n  white-space: nowrap;\n}\n\n.elements-tree-nowrap,\n.elements-tree-nowrap .li {\n  white-space: pre !important; /* stylelint-disable-line declaration-no-important */\n}\n\n.elements-disclosure .elements-tree-nowrap li {\n  word-wrap: normal;\n}\n/* DOM update highlight */\n@keyframes dom-update-highlight-animation {\n  from {\n    background-color: var(--color-syntax-2);\n    color: var(--color-background);\n  }\n\n  80% {\n    background-color: var(--color-syntax-8);\n  }\n\n  to {\n    background-color: inherit;\n  }\n}\n\n@keyframes dom-update-highlight-animation-dark {\n  from {\n    background-color: var(--color-syntax-2);\n    color: var(--color-background);\n  }\n\n  80% {\n    background-color: var(--color-background);\n    color: inherit;\n  }\n\n  to {\n    background-color: inherit;\n  }\n}\n\n.dom-update-highlight {\n  animation: dom-update-highlight-animation 1.4s 1 cubic-bezier(0, 0, 0.2, 1);\n  border-radius: 2px;\n}\n\n:host-context(.-theme-with-dark-background) .dom-update-highlight {\n  animation: dom-update-highlight-animation-dark 1.4s 1 cubic-bezier(0, 0, 0.2, 1);\n}\n\n.elements-disclosure.single-node li {\n  padding-left: 2px;\n}\n\n.elements-tree-shortcut-title,\n.elements-tree-shortcut-link {\n  color: var(--color-text-secondary);\n}\n\n.elements-disclosure .gutter-container {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 15px;\n  height: 15px;\n}\n\n.elements-hide-gutter .gutter-container {\n  display: none;\n}\n\n.gutter-menu-icon {\n  display: block;\n  visibility: hidden;\n  transform: rotate(-90deg) scale(0.8);\n  background-color: var(--color-background);\n  position: relative;\n  left: -7px;\n  top: -3px;\n}\n\n.elements-disclosure li.selected .gutter-container:not(.has-decorations) .gutter-menu-icon {\n  visibility: visible;\n}\n/** Guide line */\n\nli.hovered:not(.always-parent) + ol.children,\n.elements-tree-outline ol.shadow-root,\nli.selected:not(.always-parent) + ol.children {\n  background: linear-gradient(to right, var(--override-indentation-level-border-color), var(--override-indentation-level-border-color) 0.5px, transparent 0);\n  background-position-x: 5px;\n}\n\nli.selected:not(.always-parent) + ol.children {\n  --override-indentation-level-border-color: hsl(216deg 68% 80% / 100%) !important; /* stylelint-disable-line declaration-no-important */\n}\n\nli.hovered:not(.always-parent) + ol.children:not(.shadow-root) {\n  --override-indentation-level-border-color: hsl(0deg 0% 0% / 10%);\n}\n\n.elements-tree-outline ol.shadow-root {\n  --override-indentation-level-border-color: hsl(0deg 0% 80% / 100%);\n}\n\n@media (forced-colors: active) {\n  .elements-disclosure li.parent::before {\n    forced-color-adjust: none;\n    background-color: ButtonText !important; /* stylelint-disable-line declaration-no-important */\n  }\n\n  .elements-disclosure .elements-tree-outline:not(.hide-selection-when-blurred) li.selected .selected-hint::before {\n    opacity: unset;\n  }\n\n  .elements-disclosure .elements-tree-outline:not(.hide-selection-when-blurred) .selection,\n  .elements-disclosure li.hovered:not(.selected) .selection,\n  .elements-disclosure .elements-tree-outline:not(.hide-selection-when-blurred) li.selected:focus .selection {\n    forced-color-adjust: none;\n    background: canvas !important; /* stylelint-disable-line declaration-no-important */\n    border: 1px solid Highlight !important; /* stylelint-disable-line declaration-no-important */\n  }\n\n  .gutter-menu-icon {\n    forced-color-adjust: none;\n  }\n}\n\n/*# sourceURL=elementsTreeOutline.css */\n');var o=i})),t.register("bVu26",(function(n,i){e(n.exports,"GenericDecorator",(()=>d)),e(n.exports,"getRegisteredDecorators",(()=>p));var o=t("ixFnt"),s=t("1r4or");const r={domBreakpoint:"DOM Breakpoint",elementIsHidden:"Element is hidden"},l=o.i18n.registerUIStrings("panels/elements/MarkerDecorator.ts",r),a=o.i18n.getLazilyComputedLocalizedString.bind(void 0,l);class d{title;color;constructor(e){if(!e.title||!e.color)throw new Error(`Generic decorator requires a color and a title: ${e.marker}`);this.title=e.title(),this.color=e.color}decorate(e){return{title:this.title,color:this.color}}}const c={marker:"breakpoint-marker",title:a(r.domBreakpoint),color:"rgb(105, 140, 254)"},h={marker:"hidden-marker",title:a(r.elementIsHidden),color:"#555"};function p(){return[{...c,decorator:()=>new d(c)},{...h,decorator:()=>new d(h)},{decorator:s.PseudoStateMarkerDecorator.instance,marker:"pseudo-state-marker",title:void 0,color:void 0}]}})),t.register("KoYQ7",(function(n,i){e(n.exports,"MetricsSidebarPane",(()=>c));var o=t("koSS8"),s=t("lz7WY"),r=t("eQFvP"),l=t("9z2ZV"),a=t("eVQ8w"),d=t("iVex3");class c extends a.ElementsSidebarPane{originalPropertyData;previousPropertyDataCandidate;inlineStyle;highlightMode;boxElements;isEditingMetrics;constructor(){super(),this.originalPropertyData=null,this.previousPropertyDataCandidate=null,this.inlineStyle=null,this.highlightMode="",this.boxElements=[]}doUpdate(){if(this.isEditingMetrics)return Promise.resolve();const e=this.node(),t=this.cssModel();if(!e||e.nodeType()!==Node.ELEMENT_NODE||!t)return this.contentElement.removeChildren(),this.element.classList.add("collapsed"),Promise.resolve();if(!e.id)return Promise.resolve();const n=[t.getComputedStyle(e.id).then(function(t){t&&this.node()===e&&this.updateMetrics(t)}.bind(this)),t.getInlineStyles(e.id).then((t=>{t&&this.node()===e&&(this.inlineStyle=t.inlineStyle)}))];return Promise.all(n)}onCSSModelChanged(){this.update()}toggleVisibility(e){this.element.classList.toggle("invisible",!e)}getPropertyValueAsPx(e,t){const n=e.get(t);return n?Number(n.replace(/px$/,"")||0):0}getBox(e,t){const n="border"===t?"-width":"";return{left:this.getPropertyValueAsPx(e,t+"-left"+n),top:this.getPropertyValueAsPx(e,t+"-top"+n),right:this.getPropertyValueAsPx(e,t+"-right"+n),bottom:this.getPropertyValueAsPx(e,t+"-bottom"+n)}}highlightDOMNode(e,t,n){n.consume();const i=this.node();if(e&&i){if(this.highlightMode===t)return;this.highlightMode=t,i.highlight(t)}else this.highlightMode="",r.OverlayModel.OverlayModel.hideDOMNodeHighlight();for(const{element:e,name:n,backgroundColor:o}of this.boxElements){const s=!i||"all"===t||n===t;e.style.backgroundColor=s?o:"",e.classList.toggle("highlighted",s)}}updateMetrics(e){const t=document.createElement("div");t.className="metrics";const n=this;function i(e,t,n,i){const o=document.createElement("div");o.className=n;const r=("position"!==t?t+"-":"")+n+i;let l=e.get(r);return void 0===l||((""===l||"position"!==t&&"0px"===l||"position"===t&&"auto"===l)&&(l="‒"),l=l.replace(/px$/,""),l=s.NumberUtilities.toFixedIfFloating(l),o.textContent=l,o.addEventListener("dblclick",this.startEditing.bind(this,o,t,r,e),!1)),o}function r(e){let t=e.get("width");if(!t)return"";t=t.replace(/px$/,"");const i=Number(t);if(!isNaN(i)&&"border-box"===e.get("box-sizing")){const o=n.getBox(e,"border"),s=n.getBox(e,"padding");t=(i-o.left-o.right-s.left-s.right).toString()}return s.NumberUtilities.toFixedIfFloating(t)}function l(e){let t=e.get("height");if(!t)return"";t=t.replace(/px$/,"");const i=Number(t);if(!isNaN(i)&&"border-box"===e.get("box-sizing")){const o=n.getBox(e,"border"),s=n.getBox(e,"padding");t=(i-o.top-o.bottom-s.top-s.bottom).toString()}return s.NumberUtilities.toFixedIfFloating(t)}const a=new Set(["table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group"]),d=new Set(["table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group"]),c=new Set(["static"]),h=["content","padding","border","margin","position"],p=[o.Color.PageHighlight.Content,o.Color.PageHighlight.Padding,o.Color.PageHighlight.Border,o.Color.PageHighlight.Margin,o.Color.Color.fromRGBA([0,0,0,0])],u=["content","padding","border","margin","position"];let m=null;this.boxElements=[];for(let t=0;t<h.length;++t){const n=h[t],s=e.get("display"),g=e.get("position");if(!s||!g)continue;if("margin"===n&&a.has(s))continue;if("padding"===n&&d.has(s))continue;if("position"===n&&c.has(g))continue;const f=document.createElement("div");f.className=`${n} highlighted`;const y=p[t].asString(o.Color.Format.RGBA)||"";if(f.style.backgroundColor=y,f.addEventListener("mouseover",this.highlightDOMNode.bind(this,!0,"position"===n?"all":n),!1),this.boxElements.push({element:f,name:n,backgroundColor:y}),"content"===n){const t=document.createElement("span");t.textContent=r(e),t.addEventListener("dblclick",this.startEditing.bind(this,t,"width","width",e),!1);const n=document.createElement("span");n.textContent=l(e),n.addEventListener("dblclick",this.startEditing.bind(this,n,"height","height",e),!1);const i=document.createElement("span");i.textContent=" × ",f.appendChild(t),f.appendChild(i),f.appendChild(n)}else{const o="border"===n?"-width":"",s=document.createElement("div");s.className="label",s.textContent=u[t],f.appendChild(s),f.appendChild(i.call(this,e,n,"top",o)),f.appendChild(document.createElement("br")),f.appendChild(i.call(this,e,n,"left",o)),m&&f.appendChild(m),f.appendChild(i.call(this,e,n,"right",o)),f.appendChild(document.createElement("br")),f.appendChild(i.call(this,e,n,"bottom",o))}m=f}t.appendChild(m),t.addEventListener("mouseover",this.highlightDOMNode.bind(this,!1,"all"),!1),t.addEventListener("mouseleave",this.highlightDOMNode.bind(this,!1,"all"),!1),this.contentElement.removeChildren(),this.contentElement.appendChild(t),this.element.classList.remove("collapsed")}startEditing(e,t,n,i){if(l.UIUtils.isBeingEdited(e))return;const o={box:t,styleProperty:n,computedStyle:i,keyDownHandler:()=>{}},s=this.handleKeyDown.bind(this,o);o.keyDownHandler=s,e.addEventListener("keydown",s,!1),this.isEditingMetrics=!0;const r=new l.InplaceEditor.Config(this.editingCommitted.bind(this),this.editingCancelled.bind(this),o);l.InplaceEditor.InplaceEditor.startEditing(e,r);const a=e.getComponentSelection();a&&a.selectAllChildren(e)}handleKeyDown(e,t){const n=t.currentTarget;l.UIUtils.handleElementValueModifications(t,n,function(t,i){this.applyUserInput(n,i,t,e,!1)}.bind(this),void 0,(function(t,n,i){return"margin"!==e.styleProperty&&n<0&&(n=0),t+n+i}))}editingEnded(e,t){this.originalPropertyData=null,this.previousPropertyDataCandidate=null,e.removeEventListener("keydown",t.keyDownHandler,!1),delete this.isEditingMetrics}editingCancelled(e,t){if(this.inlineStyle)if(this.originalPropertyData)this.inlineStyle.allProperties()[this.originalPropertyData.index].setText(this.originalPropertyData.propertyText||"",!1);else{const e=this.inlineStyle.pastLastSourcePropertyIndex();e&&this.inlineStyle.allProperties()[e-1].setText("",!1)}this.editingEnded(e,t),this.update()}applyUserInput(e,t,n,i,s){if(!this.inlineStyle)return this.editingCancelled(e,i);if(s&&t===n)return this.editingCancelled(e,i);"position"===i.box||t&&"‒"!==t?"position"!==i.box||t&&"‒"!==t||(t="auto"):t="0px",t=t.toLowerCase(),/^\d+$/.test(t)&&(t+="px");const r=i.styleProperty,l=i.computedStyle;if("border-box"===l.get("box-sizing")&&("width"===r||"height"===r)){if(!t.match(/px$/))return void o.Console.Console.instance().error("For elements with box-sizing: border-box, only absolute content area dimensions can be applied");const e=this.getBox(l,"border"),n=this.getBox(l,"padding");let i=Number(t.replace(/px$/,""));if(isNaN(i))return;i+="width"===r?e.left+e.right+n.left+n.right:e.top+e.bottom+n.top+n.bottom,t=i+"px"}this.previousPropertyDataCandidate=null;const a=this.inlineStyle.allProperties();for(let e=0;e<a.length;++e){const n=a[e];if(n.name===i.styleProperty&&n.activeInStyle())return this.previousPropertyDataCandidate=n,void n.setValue(t,s,!0,d.bind(this))}function d(e){if(e){if(this.originalPropertyData||(this.originalPropertyData=this.previousPropertyDataCandidate),this.highlightMode){const e=this.node();if(!e)return;e.highlight(this.highlightMode)}s&&this.update()}}this.inlineStyle.appendProperty(i.styleProperty,t,d.bind(this))}editingCommitted(e,t,n,i){this.editingEnded(e,i),this.applyUserInput(e,t,n,i,!0)}wasShown(){super.wasShown(),this.registerCSSFiles([d.default])}}})),t.register("iVex3",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync("/**\n * Copyright 2017 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n.metrics {\n  padding: 8px;\n  font-size: 10px;\n  text-align: center;\n  white-space: nowrap;\n  min-height: var(--metrics-height);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n\n  /* Colors in the metrics panel need special treatment. The color of the\n  various box-model regions (margin, border, padding, content) are set in JS\n  by using the ones from the in-page overlay. They, therefore, do not depend on\n  the theme.\n  To ensure proper contrast between those colors and the 1px borders between\n  them, we use these local variables, not theme variables. */\n  --override-box-model-separator-color: #000;\n  --override-box-model-text-color: #222;\n}\n\n:host {\n  --metrics-height: 190px;\n\n  height: var(--metrics-height);\n  contain: strict;\n}\n\n:host(.invisible) {\n  visibility: hidden;\n  height: 0;\n}\n\n:host(.collapsed) {\n  visibility: collapse;\n  height: 0;\n}\n/* The font we use on Windows takes up more vertical space, so adjust\n * the height of the metrics sidebar pane accordingly.\n */\n:host-context(.platform-windows) {\n  --metrics-height: 214px;\n}\n\n.metrics .label {\n  position: absolute;\n  font-size: 10px;\n  left: 4px;\n}\n\n.metrics .position {\n  /* This border is different from the ones displayed between the box-moodel\n  regions because it is displayed against the pane background, so needs to be\n  visible in both light and dark theme. We therefore use a theme variable. */\n  border: 1px var(--color-text-secondary) dotted;\n  background-color: var(--color-background);\n  display: inline-block;\n  text-align: center;\n  padding: 3px;\n  margin: 3px;\n  position: relative;\n}\n\n.metrics .margin {\n  border: 1px dashed var(--override-box-model-separator-color);\n  background-color: var(--color-background);\n  display: inline-block;\n  text-align: center;\n  vertical-align: middle;\n  padding: 3px 6px;\n  margin: 3px;\n  position: relative;\n}\n\n.metrics .border {\n  border: 1px solid var(--override-box-model-separator-color);\n  background-color: var(--color-background);\n  display: inline-block;\n  text-align: center;\n  vertical-align: middle;\n  padding: 3px 6px;\n  margin: 3px;\n  position: relative;\n}\n\n.metrics .padding {\n  border: 1px dashed var(--override-box-model-separator-color);\n  background-color: var(--color-background);\n  display: inline-block;\n  text-align: center;\n  vertical-align: middle;\n  padding: 3px 6px;\n  margin: 3px;\n  position: relative;\n  min-width: 120px;\n}\n\n.metrics .content {\n  position: static;\n  border: 1px solid var(--override-box-model-separator-color);\n  background-color: var(--color-background);\n  display: inline-block;\n  text-align: center;\n  vertical-align: middle;\n  padding: 3px;\n  margin: 3px;\n  min-width: 80px;\n  overflow: visible;\n}\n\n.metrics .content span {\n  display: inline-block;\n}\n\n.metrics .editing {\n  position: relative;\n  z-index: 100;\n  cursor: text;\n}\n\n.metrics .left {\n  display: inline-block;\n  vertical-align: middle;\n}\n\n.metrics .right {\n  display: inline-block;\n  vertical-align: middle;\n}\n\n.metrics .top {\n  display: inline-block;\n}\n\n.metrics .bottom {\n  display: inline-block;\n}\n\n/* In dark theme, when a specific box-model region is hovered, the other regions\nlose their background colors, so we need to give them a lighter border color so\nthat region separators remain visible against the dark panel background. */\n:host-context(.-theme-with-dark-background) .margin:hover,\n:host-context(.-theme-with-dark-background) .margin:hover * {\n  border-color: var(--color-text-secondary);\n}\n\n/* With the exception of the position labels, labels are displayed on top of\nthe box-model region colors, so need to use the following color to remain\nvisible. */\n.metrics .highlighted:not(.position) > *:not(.border):not(.padding):not(.content) {\n  color: var(--override-box-model-text-color);\n}\n\n/*# sourceURL=metricsSidebarPane.css */\n");var o=i})),t.register("d6d0Q",(function(e,n){e.exports=new URL(t("27Lyk").resolve("jMvv1"),import.meta.url).toString()})),t.register("5jpv3",(function(n,i){e(n.exports,"EventListenersWidget",(()=>u)),e(n.exports,"DispatchFilterBy",(()=>m));var o=t("koSS8"),s=t("ixFnt"),r=t("eQFvP"),l=t("9z2ZV");t("lrkvC");var a=t("7Ixkn");const d={frameworkListeners:"`Framework` listeners",refresh:"Refresh",showListenersOnTheAncestors:"Show listeners on the ancestors",ancestors:"Ancestors",eventListenersCategory:"Event listeners category",all:"All",passive:"Passive",blocking:"Blocking",resolveEventListenersBoundWith:"Resolve event listeners bound with framework"},c=s.i18n.registerUIStrings("panels/elements/EventListenersWidget.ts",d),h=s.i18n.getLocalizedString.bind(void 0,c);let p;class u extends l.ThrottledWidget.ThrottledWidget{toolbarItemsInternal;showForAncestorsSetting;dispatchFilterBySetting;showFrameworkListenersSetting;eventListenersView;lastRequestedNode;constructor(){super(),this.toolbarItemsInternal=[],this.showForAncestorsSetting=o.Settings.Settings.instance().moduleSetting("showEventListenersForAncestors"),this.showForAncestorsSetting.addChangeListener(this.update.bind(this)),this.dispatchFilterBySetting=o.Settings.Settings.instance().createSetting("eventListenerDispatchFilterType",m.All),this.dispatchFilterBySetting.addChangeListener(this.update.bind(this)),this.showFrameworkListenersSetting=o.Settings.Settings.instance().createSetting("showFrameowkrListeners",!0),this.showFrameworkListenersSetting.setTitle(h(d.frameworkListeners)),this.showFrameworkListenersSetting.addChangeListener(this.showFrameworkListenersChanged.bind(this)),this.eventListenersView=new a.EventListenersView(this.update.bind(this)),this.eventListenersView.show(this.element);const e=new l.Toolbar.ToolbarButton(h(d.refresh),"largeicon-refresh");e.addEventListener(l.Toolbar.ToolbarButton.Events.Click,this.update.bind(this)),this.toolbarItemsInternal.push(e),this.toolbarItemsInternal.push(new l.Toolbar.ToolbarSettingCheckbox(this.showForAncestorsSetting,h(d.showListenersOnTheAncestors),h(d.ancestors)));const t=new l.Toolbar.ToolbarComboBox(this.onDispatchFilterTypeChanged.bind(this),h(d.eventListenersCategory));function n(e,n){const i=t.createOption(e,n);n===this.dispatchFilterBySetting.get()&&t.select(i)}n.call(this,h(d.all),m.All),n.call(this,h(d.passive),m.Passive),n.call(this,h(d.blocking),m.Blocking),t.setMaxWidth(200),this.toolbarItemsInternal.push(t),this.toolbarItemsInternal.push(new l.Toolbar.ToolbarSettingCheckbox(this.showFrameworkListenersSetting,h(d.resolveEventListenersBoundWith))),l.Context.Context.instance().addFlavorChangeListener(r.DOMModel.DOMNode,this.update,this),this.update()}static instance(e={forceNew:null}){const{forceNew:t}=e;return p&&!t||(p=new u),p}doUpdate(){this.lastRequestedNode&&(this.lastRequestedNode.domModel().runtimeModel().releaseObjectGroup(g),delete this.lastRequestedNode);const e=l.Context.Context.instance().flavor(r.DOMModel.DOMNode);if(!e)return this.eventListenersView.reset(),this.eventListenersView.addEmptyHolderIfNeeded(),Promise.resolve();this.lastRequestedNode=e;const t=!this.showForAncestorsSetting.get(),n=[];if(n.push(e.resolveToObject(g)),!t){let t=e.parentNode;for(;t;)n.push(t.resolveToObject(g)),t=t.parentNode;n.push(this.windowObjectInNodeContext(e))}return Promise.all(n).then(this.eventListenersView.addObjects.bind(this.eventListenersView)).then(this.showFrameworkListenersChanged.bind(this))}toolbarItems(){return this.toolbarItemsInternal}onDispatchFilterTypeChanged(e){const t=e.target;this.dispatchFilterBySetting.set(t.value)}showFrameworkListenersChanged(){const e=this.dispatchFilterBySetting.get(),t=e===m.All||e===m.Passive,n=e===m.All||e===m.Blocking;this.eventListenersView.showFrameworkListeners(this.showFrameworkListenersSetting.get(),t,n)}windowObjectInNodeContext(e){const t=e.domModel().runtimeModel().executionContexts();let n=t[0];if(e.frameId())for(let i=0;i<t.length;++i){const o=t[i];o.frameId===e.frameId()&&o.isDefault&&(n=o)}return n.evaluate({expression:"self",objectGroup:g,includeCommandLineAPI:!1,silent:!0,returnByValue:!1,generatePreview:!1,throwOnSideEffect:void 0,timeout:void 0,disableBreaks:void 0,replMode:void 0,allowUnsafeEvalBlockedByCSP:void 0},!1,!1).then((e=>"object"in e?e.object:null))}eventListenersArrivedForTest(){}}const m={All:"All",Blocking:"Blocking",Passive:"Passive"},g="event-listeners-panel"})),t.register("lrkvC",(function(n,i){e(n.exports,"EventListenersView",(()=>t("7Ixkn")));t("dc3ZV"),t("7Ixkn")})),t.register("dc3ZV",(function(n,i){e(n.exports,"frameworkEventListeners",(()=>r));var o=t("koSS8"),s=t("eQFvP");async function r(e){const t=e.runtimeModel().target().model(s.DOMDebuggerModel.DOMDebuggerModel);if(!t)return{eventListeners:[],internalHandlers:null};const n={internalHandlers:null,eventListeners:[]};return e.callFunction((function(){const e=[];let t=[],n=[],i=[function(e){if(!(e&&e instanceof Node))return{eventListeners:[]};const t=window.jQuery;if(!t||!t.fn)return{eventListeners:[]};const n=t,i=t._data||t.data,o=[],s=[];if("function"==typeof i){const t=i(e,"events");for(const n in t)for(const i in t[n]){const s=t[n][i];if("object"==typeof s||"function"==typeof s){const t={handler:s.handler||s,useCapture:!0,passive:!1,once:!1,type:n,remove:c.bind(e,s.selector)};o.push(t)}}const n=i(e);n&&"function"==typeof n.handle&&s.push(n.handle)}const r=n(e)[0];if(r){const e=r.$events;for(const t in e){const n=e[t];for(const e in n)if("function"==typeof n[e]){const i={handler:n[e],useCapture:!0,passive:!1,once:!1,type:t};o.push(i)}}r&&r.$handle&&s.push(r.$handle)}return{eventListeners:o,internalHandlers:s}}];try{self.devtoolsFrameworkEventListeners&&s(self.devtoolsFrameworkEventListeners)&&(i=i.concat(self.devtoolsFrameworkEventListeners))}catch(t){e.push("devtoolsFrameworkEventListeners call produced error: "+a(t))}for(let o=0;o<i.length;++o)try{const e=i[o](this);if(e.eventListeners&&s(e.eventListeners)){const n=e.eventListeners.map((e=>r(e))).filter(d);t=t.concat(n)}if(e.internalHandlers&&s(e.internalHandlers)){const t=e.internalHandlers.map((e=>l(e))).filter(d);n=n.concat(t)}}catch(t){e.push("fetcher call produced error: "+a(t))}const o={eventListeners:t,internalHandlers:n.length?n:void 0,errorString:void 0};o.internalHandlers||delete o.internalHandlers;if(e.length){let t="Framework Event Listeners API Errors:\n\t"+e.join("\n\t");t=t.substr(0,t.length-1),o.errorString=t}""!==o.errorString&&void 0!==o.errorString||delete o.errorString;return o;function s(e){if(!e||"object"!=typeof e)return!1;try{if("function"==typeof e.splice){const t=e.length;return"number"==typeof t&&t>>>0===t&&(t>0||1/t>0)}}catch(e){}return!1}function r(t){try{let n="";if(t){const e=t.type;e&&"string"==typeof e||(n+="event listener's type isn't string or empty, ");const i=t.useCapture;"boolean"!=typeof i&&(n+="event listener's useCapture isn't boolean or undefined, ");const o=t.passive;"boolean"!=typeof o&&(n+="event listener's passive isn't boolean or undefined, ");const s=t.once;"boolean"!=typeof s&&(n+="event listener's once isn't boolean or undefined, ");const r=t.handler;r&&"function"==typeof r||(n+="event listener's handler isn't a function or empty, ");const l=t.remove;if(l&&"function"!=typeof l&&(n+="event listener's remove isn't a function, "),!n)return{type:e,useCapture:i,passive:o,once:s,handler:r,remove:l}}else n+="empty event listener, ";return e.push(n.substr(0,n.length-2)),null}catch(t){return e.push(a(t)),null}}function l(t){return t&&"function"==typeof t?t:(e.push("internal handler isn't a function or empty"),null)}function a(e){try{return String(e)}catch(e){return"<error>"}}function d(e){return Boolean(e)}function c(e,t,n){if(!(this&&this instanceof Node))return;const i=window.jQuery;if(!i||!i.fn)return;i(this).off(t,e,n)}}),void 0).then(d).then((function(e){return e.getOwnProperties(!1)})).then((async function(e){if(!e.properties)throw new Error("Object properties is empty");const t=[];for(const c of e.properties)"eventListeners"===c.name&&c.value&&t.push(i(c.value).then(l)),"internalHandlers"===c.name&&c.value&&t.push((d=c.value,s.RemoteObject.RemoteArray.objectAsArray(d).map(r).then(s.RemoteObject.RemoteArray.createFromRemoteObjects.bind(null))).then(a)),"errorString"===c.name&&c.value&&(n=c.value,o.Console.Console.instance().error(String(n.value)));var n;var d;await Promise.all(t)})).then((function(){return n})).catch((e=>(console.error(e),n)));function i(n){return s.RemoteObject.RemoteArray.objectAsArray(n).map((function(n){let i,o,l,a,c=null,h=null,p=null,u=null;const m=[];function g(e){p=e?e.location:null}return m.push(n.callFunctionJSON((function(){return{type:this.type,useCapture:this.useCapture,passive:this.passive,once:this.once}}),void 0).then((function(e){void 0!==e.type&&(i=e.type);void 0!==e.useCapture&&(o=e.useCapture);void 0!==e.passive&&(l=e.passive);void 0!==e.once&&(a=e.once)}))),m.push(n.callFunction((function(){return this.handler||null})).then(d).then((function(e){return h=e,h})).then(r).then((function(e){return c=e,e.debuggerModel().functionDetailsPromise(e).then(g)}))),m.push(n.callFunction((function(){return this.remove||null})).then(d).then((function(e){if("function"!==e.type)return;u=e}))),Promise.all(m).then((function(){if(!p)throw new Error("Empty event listener's location");return new s.DOMDebuggerModel.EventListener(t,e,i,o,l,a,c,h,p,u,s.DOMDebuggerModel.EventListener.Origin.FrameworkUser)})).catch((e=>(console.error(e),null)))})).then(c)}function r(e){return s.RemoteObject.RemoteFunction.objectAsFunction(e).targetFunction()}function l(e){n.eventListeners=e}function a(e){n.internalHandlers=e}function d(e){if(e.wasThrown||!e.object)throw new Error("Exception in callFunction or empty result");return e.object}function c(e){return e.filter((function(e){return Boolean(e)}))}}})),t.register("7Ixkn",(function(n,i){e(n.exports,"EventListenersView",(()=>f)),e(n.exports,"EventListenersTreeElement",(()=>y)),e(n.exports,"ObjectEventListenerBar",(()=>v));var o=t("koSS8"),s=t("ixFnt"),r=t("eQFvP"),l=t("R6KC8"),a=t("eKcu2"),d=t("a3yig"),c=t("9z2ZV"),h=t("6E5Tv"),p=t("dc3ZV");const u={noEventListeners:"No event listeners",remove:"Remove",deleteEventListener:"Delete event listener",togglePassive:"Toggle Passive",toggleWhetherEventListenerIs:"Toggle whether event listener is passive or blocking",revealInElementsPanel:"Reveal in Elements panel",passive:"Passive"},m=s.i18n.registerUIStrings("panels/event_listeners/EventListenersView.ts",u),g=s.i18n.getLocalizedString.bind(void 0,m);class f extends c.Widget.VBox{changeCallback;enableDefaultTreeFocus;treeOutline;emptyHolder;linkifier;treeItemMap;constructor(e,t=!1){super(),this.changeCallback=e,this.enableDefaultTreeFocus=t,this.treeOutline=new c.TreeOutline.TreeOutlineInShadow,this.treeOutline.hideOverflow(),this.treeOutline.setComparator(y.comparator),this.treeOutline.element.classList.add("monospace"),this.treeOutline.setShowSelectionOnKeyboardFocus(!0),this.treeOutline.setFocusable(!0),this.element.appendChild(this.treeOutline.element),this.emptyHolder=document.createElement("div"),this.emptyHolder.classList.add("gray-info-message"),this.emptyHolder.textContent=g(u.noEventListeners),this.emptyHolder.tabIndex=-1,this.linkifier=new d.Linkifier.Linkifier,this.treeItemMap=new Map}focus(){this.enableDefaultTreeFocus&&(this.emptyHolder.parentNode?this.emptyHolder.focus():this.treeOutline.forceSelect())}async addObjects(e){this.reset(),await Promise.all(e.map((e=>e?this.addObject(e):Promise.resolve()))),this.addEmptyHolderIfNeeded(),this.eventListenersArrivedForTest()}addObject(e){let t,n=null;const i=[],o=e.runtimeModel().target().model(r.DOMDebuggerModel.DOMDebuggerModel);return o&&i.push(o.eventListeners(e).then((function(e){t=e}))),i.push((0,p.frameworkEventListeners)(e).then((function(e){n=e}))),Promise.all(i).then((async function(){if(!n)return;if(!n.internalHandlers)return;return n.internalHandlers.object().callFunctionJSON((function(){const e=[],t=new Set(this);for(const n of arguments)e.push(t.has(n));return e}),t.map((function(e){return r.RemoteObject.RemoteObject.toCallArgument(e.handler())}))).then((function(e){for(let n=0;n<t.length;++n)e[n]&&t[n].markAsFramework()}))})).then(function(){this.addObjectEventListeners(e,t),n&&this.addObjectEventListeners(e,n.eventListeners)}.bind(this))}addObjectEventListeners(e,t){if(t)for(const n of t){this.getOrCreateTreeElementForType(n.type()).addObjectEventListener(n,e)}}showFrameworkListeners(e,t,n){const i=this.treeOutline.rootElement().children();for(const o of i){let i=!0;for(const s of o.children()){const o=s,l=o.eventListener().origin();let a=!1;l!==r.DOMDebuggerModel.EventListener.Origin.FrameworkUser||e||(a=!0),l===r.DOMDebuggerModel.EventListener.Origin.Framework&&e&&(a=!0),!t&&o.eventListener().passive()&&(a=!0),n||o.eventListener().passive()||(a=!0),o.hidden=a,i=i&&a}o.hidden=i}}getOrCreateTreeElementForType(e){let t=this.treeItemMap.get(e);return t||(t=new y(e,this.linkifier,this.changeCallback),this.treeItemMap.set(e,t),t.hidden=!0,this.treeOutline.appendChild(t)),this.emptyHolder.remove(),t}addEmptyHolderIfNeeded(){let e=!0,t=null;for(const n of this.treeOutline.rootElement().children())n.hidden=!n.firstChild(),e=e&&n.hidden,t||n.hidden||(t=n);e&&!this.emptyHolder.parentNode&&this.element.appendChild(this.emptyHolder),t&&t.select(!0),this.treeOutline.setFocusable(Boolean(t))}reset(){const e=this.treeOutline.rootElement().children();for(const t of e)t.removeChildren();this.linkifier.reset()}eventListenersArrivedForTest(){}wasShown(){super.wasShown(),this.treeOutline.registerCSSFiles([h.default,a.default])}}class y extends c.TreeOutline.TreeElement{toggleOnClick;linkifier;changeCallback;constructor(e,t,n){super(e),this.toggleOnClick=!0,this.linkifier=t,this.changeCallback=n,c.ARIAUtils.setAccessibleName(this.listItemElement,`${e}, event listener`)}static comparator(e,t){return e.title===t.title?0:e.title>t.title?1:-1}addObjectEventListener(e,t){const n=new v(e,t,this.linkifier,this.changeCallback);this.appendChild(n)}}class v extends c.TreeOutline.TreeElement{eventListenerInternal;editable;changeCallback;valueTitle;constructor(e,t,n,i){super("",!0),this.eventListenerInternal=e,this.editable=!1,this.setTitle(t,n),this.changeCallback=i}async onpopulate(){const e=[],t=this.eventListenerInternal,n=t.domDebuggerModel().runtimeModel();e.push(n.createRemotePropertyFromPrimitiveValue("useCapture",t.useCapture())),e.push(n.createRemotePropertyFromPrimitiveValue("passive",t.passive())),e.push(n.createRemotePropertyFromPrimitiveValue("once",t.once())),void 0!==t.handler()&&e.push(new r.RemoteObject.RemoteObjectProperty("handler",t.handler())),l.ObjectPropertiesSection.ObjectPropertyTreeElement.populateWithProperties(this,e,[],!0,!0,null)}setTitle(e,t){const n=this.listItemElement.createChild("span","event-listener-details"),i=l.ObjectPropertiesSection.ObjectPropertiesSection.createPropertyValue(e,!1,!1);if(this.valueTitle=i.element,n.appendChild(this.valueTitle),this.eventListenerInternal.canRemove()){const e=n.createChild("span","event-listener-button");e.textContent=g(u.remove),c.Tooltip.Tooltip.install(e,g(u.deleteEventListener)),e.addEventListener("click",(e=>{this.removeListener(),e.consume()}),!1),n.appendChild(e)}if(this.eventListenerInternal.isScrollBlockingType()&&this.eventListenerInternal.canTogglePassive()){const e=n.createChild("span","event-listener-button");e.textContent=g(u.togglePassive),c.Tooltip.Tooltip.install(e,g(u.toggleWhetherEventListenerIs)),e.addEventListener("click",(e=>{this.togglePassiveListener(),e.consume()}),!1),n.appendChild(e)}const s=n.createChild("span","event-listener-tree-subtitle"),r=t.linkifyRawLocation(this.eventListenerInternal.location(),this.eventListenerInternal.sourceURL());s.appendChild(r),this.listItemElement.addEventListener("contextmenu",(t=>{const n=new c.ContextMenu.ContextMenu(t);t.target!==r&&n.appendApplicableItems(r),"node"===e.subtype&&n.defaultSection().appendItem(g(u.revealInElementsPanel),(()=>o.Revealer.reveal(e))),n.defaultSection().appendItem(g(u.deleteEventListener),this.removeListener.bind(this),!this.eventListenerInternal.canRemove()),n.defaultSection().appendCheckboxItem(g(u.passive),this.togglePassiveListener.bind(this),this.eventListenerInternal.passive(),!this.eventListenerInternal.canTogglePassive()),n.show()}))}removeListener(){this.removeListenerBar(),this.eventListenerInternal.remove()}togglePassiveListener(){this.eventListenerInternal.togglePassive().then((()=>this.changeCallback()))}removeListenerBar(){const e=this.parent;if(!e)return;e.removeChild(this),e.childCount()||e.collapse();let t=!0;for(const n of e.children())n.hidden||(t=!1);e.hidden=t}eventListener(){return this.eventListenerInternal}onenter(){return!!this.valueTitle&&(this.valueTitle.click(),!0)}ondelete(){return!!this.eventListenerInternal.canRemove()&&(this.removeListener(),!0)}}})),t.register("6E5Tv",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2015 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.tree-outline-disclosure li {\n  padding: 2px 0 0 5px;\n  overflow: hidden;\n  display: flex;\n  min-height: 17px;\n  align-items: baseline;\n}\n\n.tree-outline-disclosure > li {\n  border-top: 1px solid var(--color-background-elevation-0);\n}\n\n.tree-outline-disclosure > li:first-of-type {\n  border-top: none;\n}\n\n.tree-outline-disclosure {\n  padding-left: 0 !important; /* stylelint-disable-line declaration-no-important */\n  padding-right: 3px;\n}\n\n.tree-outline-disclosure li.parent::before {\n  top: 0 !important; /* stylelint-disable-line declaration-no-important */\n}\n\n.tree-outline-disclosure .name {\n  color: var(--color-syntax-2);\n}\n\n.tree-outline-disclosure .object-value-node,\n.tree-outline-disclosure .object-value-object {\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.event-listener-details {\n  display: flex;\n}\n\n.event-listener-tree-subtitle {\n  float: right;\n  margin-left: 5px;\n  flex-shrink: 0;\n}\n\n.event-listener-button {\n  padding: 0 3px;\n  background-color: var(--color-background-elevation-1);\n  border-radius: 3px;\n  border: 1px solid var(--color-details-hairline);\n  margin-left: 5px;\n  display: block;\n  opacity: 80%;\n  flex-shrink: 0;\n}\n\n.event-listener-button:hover {\n  background-color: var(--color-background-elevation-2);\n  opacity: 100%;\n}\n\n.tree-outline-disclosure li:hover .event-listener-button {\n  display: inline;\n}\n\n@media (forced-colors: active) {\n  .event-listener-details .event-listener-button {\n    forced-color-adjust: none;\n    opacity: 100%;\n    background: ButtonFace;\n    color: ButtonText;\n    border-color: ButtonText;\n  }\n\n  .event-listener-button:hover {\n    background-color: Highlight !important; /* stylelint-disable-line declaration-no-important */\n    color: HighlightText;\n    border-color: ButtonText;\n  }\n\n  .tree-outline.hide-selection-when-blurred .selected:focus-visible .event-listener-button,\n  .tree-outline-disclosure li:focus-visible .gray-info-message {\n    background-color: Highlight;\n    color: HighlightText;\n    border-color: HighlightText;\n  }\n}\n\n/*# sourceURL=eventListenersView.css */\n");var o=i})),t.register("iMvSZ",(function(n,i){e(n.exports,"PropertiesWidget",(()=>y));var o=t("koSS8"),s=t("e7bLS"),r=t("ixFnt"),l=t("eQFvP"),a=t("R6KC8"),d=t("9z2ZV"),c=t("gPVrm"),h=t("3OgVH");const p="properties-sidebar-pane",u={filter:"Filter",filterProperties:"Filter Properties",showAll:"Show all",showAllTooltip:"When unchecked, only properties whose values are neither null nor undefined will be shown",noMatchingProperty:"No matching property"},m=r.i18n.registerUIStrings("panels/elements/PropertiesWidget.ts",u),g=r.i18n.getLocalizedString.bind(void 0,m);let f;class y extends d.ThrottledWidget.ThrottledWidget{node;showAllPropertiesSetting;filterRegex=null;noMatchesElement;treeOutline;expandController;lastRequestedNode;constructor(){super(!0),this.showAllPropertiesSetting=o.Settings.Settings.instance().createSetting("showAllProperties",!1),this.showAllPropertiesSetting.addChangeListener(this.filterList.bind(this)),l.TargetManager.TargetManager.instance().addModelListener(l.DOMModel.DOMModel,l.DOMModel.Events.AttrModified,this.onNodeChange,this),l.TargetManager.TargetManager.instance().addModelListener(l.DOMModel.DOMModel,l.DOMModel.Events.AttrRemoved,this.onNodeChange,this),l.TargetManager.TargetManager.instance().addModelListener(l.DOMModel.DOMModel,l.DOMModel.Events.CharacterDataModified,this.onNodeChange,this),l.TargetManager.TargetManager.instance().addModelListener(l.DOMModel.DOMModel,l.DOMModel.Events.ChildNodeCountUpdated,this.onNodeChange,this),d.Context.Context.instance().addFlavorChangeListener(l.DOMModel.DOMNode,this.setNode,this),this.node=d.Context.Context.instance().flavor(l.DOMModel.DOMNode);const e=this.contentElement.createChild("div","hbox properties-widget-toolbar"),t=e.createChild("div","properties-widget-filter-box"),n=h.StylesSidebarPane.createPropertyFilterElement(g(u.filter),e,this.filterProperties.bind(this));d.ARIAUtils.setAccessibleName(n,g(u.filterProperties)),t.appendChild(n);new d.Toolbar.Toolbar("styles-pane-toolbar",e).appendToolbarItem(new d.Toolbar.ToolbarSettingCheckbox(this.showAllPropertiesSetting,g(u.showAllTooltip),g(u.showAll))),this.noMatchesElement=this.contentElement.createChild("div","gray-info-message hidden"),this.noMatchesElement.textContent=g(u.noMatchingProperty),this.treeOutline=new a.ObjectPropertiesSection.ObjectPropertiesSectionsTreeOutline({readOnly:!0}),this.treeOutline.setShowSelectionOnKeyboardFocus(!0,!1),this.expandController=new a.ObjectPropertiesSection.ObjectPropertiesSectionsTreeExpandController(this.treeOutline),this.contentElement.appendChild(this.treeOutline.element),this.treeOutline.addEventListener(d.TreeOutline.Events.ElementExpanded,(()=>{s.userMetrics.actionTaken(s.UserMetrics.Action.DOMPropertiesExpanded)})),this.update()}static instance(e={forceNew:null}){const{forceNew:t}=e;return f&&!t||(f=new y),f}filterProperties(e){this.filterRegex=e,this.filterList()}filterList(){const e=e=>{if(!this.showAllPropertiesSetting.get()){if(l.RemoteObject.RemoteObject.isNullOrUndefined(e.value))return!0;if("undefined"===e.value?.type||"object"===e.value?.type&&"null"===e.value.subtype)return!0}return null!==this.filterRegex&&(!this.filterRegex.test(e.name)&&!this.filterRegex.test(e.value?.description??""))};let t=!0;for(const n of this.treeOutline.rootElement().children()){const{property:i}=n,o=e(i);o||(t=!1),n.hidden=o}this.noMatchesElement.classList.toggle("hidden",!t)}setNode(e){this.node=e.data,this.update()}async doUpdate(){if(this.lastRequestedNode&&(this.lastRequestedNode.domModel().runtimeModel().releaseObjectGroup(p),delete this.lastRequestedNode),!this.node)return void this.treeOutline.removeChildren();this.lastRequestedNode=this.node;const e=await this.node.resolveToObject(p);if(!e)return;const t=this.treeOutline.rootElement();let{properties:n}=await l.RemoteObject.RemoteObject.loadFromObjectPerProto(e,!0);t.removeChildren(),null===n&&(n=[]),a.ObjectPropertiesSection.ObjectPropertyTreeElement.populateWithProperties(t,n,null,!0,!0,e),this.filterList()}onNodeChange(e){if(!this.node)return;const t=e.data,n=t instanceof l.DOMModel.DOMNode?t:t.node;this.node===n&&this.update()}wasShown(){super.wasShown(),this.registerCSSFiles([c.default])}}})),t.register("gPVrm",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright (c) 2017 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.properties-widget-section {\n  padding: 2px 0 2px 5px;\n  flex: none;\n}\n\n.properties-widget-toolbar {\n  border-bottom: 1px solid var(--color-details-hairline-light);\n  flex-shrink: 0;\n}\n\n.properties-widget-filter-box {\n  flex: auto;\n  display: flex;\n}\n\n.properties-widget-filter-box > input {\n  outline: none !important; /* stylelint-disable-line declaration-no-important */\n  border: none;\n  width: 100%;\n  background: var(--color-background);\n  padding-left: 4px;\n  margin: 3px;\n}\n\n.properties-widget-filter-box > input:focus,\n.properties-widget-filter-box > input:not(:placeholder-shown) {\n  box-shadow: var(--legacy-focus-ring-active-shadow);\n}\n\n.properties-widget-filter-box > input::placeholder {\n  color: var(--color-text-disabled);\n}\n\n@media (forced-colors: active) {\n  .properties-widget-filter-box > input {\n    border: 1px solid ButtonText;\n  }\n}\n\n/*# sourceURL=propertiesWidget.css */\n");var o=i})),t.register("g4JbB",(function(n,i){e(n.exports,"NodeStackTraceWidget",(()=>u)),e(n.exports,"MaxLengthForLinks",(()=>m));var o=t("ixFnt"),s=t("eQFvP"),r=t("a3yig"),l=t("9z2ZV"),a=t("43Bx0");const d={noStackTraceAvailable:"No stack trace available"},c=o.i18n.registerUIStrings("panels/elements/NodeStackTraceWidget.ts",d),h=o.i18n.getLocalizedString.bind(void 0,c);let p;class u extends l.ThrottledWidget.ThrottledWidget{noStackTraceElement;creationStackTraceElement;linkifier;constructor(){super(!0),this.noStackTraceElement=this.contentElement.createChild("div","gray-info-message"),this.noStackTraceElement.textContent=h(d.noStackTraceAvailable),this.creationStackTraceElement=this.contentElement.createChild("div","stack-trace"),this.linkifier=new r.Linkifier.Linkifier(m)}static instance(e={forceNew:null}){const{forceNew:t}=e;return p&&!t||(p=new u),p}wasShown(){super.wasShown(),l.Context.Context.instance().addFlavorChangeListener(s.DOMModel.DOMNode,this.update,this),this.registerCSSFiles([a.default]),this.update()}willHide(){l.Context.Context.instance().removeFlavorChangeListener(s.DOMModel.DOMNode,this.update,this)}async doUpdate(){const e=l.Context.Context.instance().flavor(s.DOMModel.DOMNode);if(!e)return this.noStackTraceElement.classList.remove("hidden"),void this.creationStackTraceElement.classList.add("hidden");const t=await e.creationStackTrace();if(t){this.noStackTraceElement.classList.add("hidden"),this.creationStackTraceElement.classList.remove("hidden");const n=r.JSPresentationUtils.buildStackTracePreviewContents(e.domModel().target(),this.linkifier,{stackTrace:t,tabStops:void 0});this.creationStackTraceElement.removeChildren(),this.creationStackTraceElement.appendChild(n.element)}else this.noStackTraceElement.classList.remove("hidden"),this.creationStackTraceElement.classList.add("hidden")}}const m=40})),t.register("43Bx0",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync("/*\n * Copyright 2019 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.stack-trace {\n  font-size: 11px !important; /* stylelint-disable-line declaration-no-important */\n  font-family: Menlo, monospace;\n}\n\n/*# sourceURL=nodeStackTraceWidget.css */\n");var o=i})),t.register("e1GCw",(function(n,i){e(n.exports,"ClassesPaneWidget",(()=>m)),e(n.exports,"ClassNamePrompt",(()=>v)),e(n.exports,"ButtonProvider",(()=>y));var o=t("koSS8"),s=t("ixFnt"),r=t("lz7WY"),l=t("eQFvP"),a=t("9z2ZV"),d=t("3giuV"),c=t("1r4or");const h={addNewClass:"Add new class",classesSAdded:"Classes {PH1} added",classSAdded:"Class {PH1} added",elementClasses:"Element Classes"},p=s.i18n.registerUIStrings("panels/elements/ClassesPaneWidget.ts",h),u=s.i18n.getLocalizedString.bind(void 0,p);class m extends a.Widget.Widget{input;classesContainer;prompt;mutatingNodes;pendingNodeClasses;updateNodeThrottler;previousTarget;constructor(){super(!0),this.contentElement.className="styles-element-classes-pane";const e=this.contentElement.createChild("div","title-container");this.input=e.createChild("div","new-class-input monospace"),this.setDefaultFocusedElement(this.input),this.classesContainer=this.contentElement.createChild("div","source-code"),this.classesContainer.classList.add("styles-element-classes-container"),this.prompt=new v(this.nodeClasses.bind(this)),this.prompt.setAutocompletionTimeout(0),this.prompt.renderAsBlock();const t=this.prompt.attach(this.input);this.prompt.setPlaceholder(u(h.addNewClass)),this.prompt.addEventListener(a.TextPrompt.Events.TextChanged,this.onTextChanged,this),t.addEventListener("keydown",this.onKeyDown.bind(this),!1),l.TargetManager.TargetManager.instance().addModelListener(l.DOMModel.DOMModel,l.DOMModel.Events.DOMMutated,this.onDOMMutated,this),this.mutatingNodes=new Set,this.pendingNodeClasses=new Map,this.updateNodeThrottler=new o.Throttler.Throttler(0),this.previousTarget=null,a.Context.Context.instance().addFlavorChangeListener(l.DOMModel.DOMNode,this.onSelectedNodeChanged,this)}splitTextIntoClasses(e){return e.split(/[,\s]/).map((e=>e.trim())).filter((e=>e.length))}onKeyDown(e){if("Enter"!==e.key&&!isEscKey(e))return;if("Enter"===e.key&&(e.consume(),this.prompt.acceptAutoComplete()))return;const t=e.target;let n=t.textContent;isEscKey(e)&&(r.StringUtilities.isWhitespace(n)||e.consume(!0),n=""),this.prompt.clearAutocomplete(),t.textContent="";const i=a.Context.Context.instance().flavor(l.DOMModel.DOMNode);if(!i)return;const o=this.splitTextIntoClasses(n);if(!o.length)return void this.installNodeClasses(i);for(const e of o)this.toggleClass(i,e,!0);const s=o.join(" "),d=o.length>1?u(h.classesSAdded,{PH1:s}):u(h.classSAdded,{PH1:s});a.ARIAUtils.alert(d),this.installNodeClasses(i),this.update()}onTextChanged(){const e=a.Context.Context.instance().flavor(l.DOMModel.DOMNode);e&&this.installNodeClasses(e)}onDOMMutated(e){const t=e.data;this.mutatingNodes.has(t)||(g.delete(t),this.update())}onSelectedNodeChanged(e){this.previousTarget&&this.prompt.text()&&(this.input.textContent="",this.installNodeClasses(this.previousTarget)),this.previousTarget=e.data,this.update()}wasShown(){super.wasShown(),this.update(),this.registerCSSFiles([d.default])}update(){if(!this.isShowing())return;let e=a.Context.Context.instance().flavor(l.DOMModel.DOMNode);if(e&&(e=e.enclosingElementOrSelf()),this.classesContainer.removeChildren(),this.input.disabled=!e,!e)return;const t=this.nodeClasses(e),n=[...t.keys()];n.sort(r.StringUtilities.caseInsensetiveComparator);for(const e of n){const n=a.UIUtils.CheckboxLabel.create(e,t.get(e));n.classList.add("monospace"),n.checkboxElement.addEventListener("click",this.onClick.bind(this,e),!1),this.classesContainer.appendChild(n)}}onClick(e,t){const n=a.Context.Context.instance().flavor(l.DOMModel.DOMNode);if(!n)return;const i=t.target.checked;this.toggleClass(n,e,i),this.installNodeClasses(n)}nodeClasses(e){let t=g.get(e);if(!t){const n=(e.getAttribute("class")||"").split(/\s/);t=new Map;for(let e=0;e<n.length;++e){const i=n[e].trim();i.length&&t.set(i,!0)}g.set(e,t)}return t}toggleClass(e,t,n){this.nodeClasses(e).set(t,n)}installNodeClasses(e){const t=this.nodeClasses(e),n=new Set;for(const e of t.keys())t.get(e)&&n.add(e);const i=this.splitTextIntoClasses(this.prompt.textWithCurrentSuggestion());for(const e of i)n.add(e);const o=[...n.values()].sort();this.pendingNodeClasses.set(e,o.join(" ")),this.updateNodeThrottler.schedule(this.flushPendingClasses.bind(this))}async flushPendingClasses(){const e=[];for(const n of this.pendingNodeClasses.keys()){this.mutatingNodes.add(n);const i=n.setAttributeValuePromise("class",this.pendingNodeClasses.get(n)).then(t.bind(this,n));e.push(i)}function t(e){this.mutatingNodes.delete(e)}this.pendingNodeClasses.clear(),await Promise.all(e)}}const g=new WeakMap;let f;class y{button;view;constructor(){this.button=new a.Toolbar.ToolbarToggle(u(h.elementClasses),""),this.button.setText(".cls"),this.button.element.classList.add("monospace"),this.button.addEventListener(a.Toolbar.ToolbarButton.Events.Click,this.clicked,this),this.view=new m}static instance(e={forceNew:null}){const{forceNew:t}=e;return f&&!t||(f=new y),f}clicked(){c.ElementsPanel.instance().showToolbarPane(this.view.isShowing()?null:this.view,this.button)}item(){return this.button}}class v extends a.TextPrompt.TextPrompt{nodeClasses;selectedFrameId;classNamesPromise;constructor(e){super(),this.nodeClasses=e,this.initialize(this.buildClassNameCompletions.bind(this)," "),this.disableDefaultSuggestionForEmptyInput(),this.selectedFrameId="",this.classNamesPromise=null}async getClassNames(e){const t=[],n=new Set;this.selectedFrameId=e.frameId();const i=e.domModel().cssModel(),o=i.allStyleSheets();for(const e of o){if(e.frameId!==this.selectedFrameId)continue;const o=i.getClassNames(e.id).then((e=>{for(const t of e)n.add(t)}));t.push(o)}const s=e.ownerDocument.id,r=e.domModel().classNamesPromise(s).then((e=>{for(const t of e)n.add(t)}));return t.push(r),await Promise.all(t),[...n]}async buildClassNameCompletions(e,t,n){t&&!n||(this.classNamesPromise=null);const i=a.Context.Context.instance().flavor(l.DOMModel.DOMNode);if(!i||!t&&!n&&!e.trim())return[];this.classNamesPromise&&this.selectedFrameId===i.frameId()||(this.classNamesPromise=this.getClassNames(i));let o=await this.classNamesPromise;const s=this.nodeClasses(i);return o=o.filter((e=>!s.get(e))),"."===t[0]&&(o=o.map((e=>"."+e))),o.filter((e=>e.startsWith(t))).sort().map((e=>({text:e,title:void 0,subtitle:void 0,iconType:void 0,priority:void 0,isSecondary:void 0,subtitleRenderer:void 0,selectionRange:void 0,hideGhostText:void 0,iconElement:void 0})))}}})),t.register("3giuV",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync('/**\n * Copyright 2017 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.styles-element-classes-pane {\n  background-color: var(--color-background-elevation-1);\n  border-bottom: 1px solid var(--color-details-hairline);\n  padding: 6px 2px 2px;\n}\n\n.styles-element-classes-container {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n}\n\n.styles-element-classes-pane [is="dt-checkbox"] {\n  margin-right: 15px;\n}\n\n.styles-element-classes-pane .title-container {\n  padding-bottom: 2px;\n}\n\n.styles-element-classes-pane .new-class-input {\n  padding-left: 3px;\n  padding-right: 3px;\n  overflow: hidden;\n  border: 1px solid var(--input-outline);\n  line-height: 15px;\n  margin-left: 3px;\n  width: calc(100% - 7px);\n  background-color: var(--color-background);\n  cursor: text;\n}\n\n/*# sourceURL=classesPaneWidget.css */\n');var o=i})),t.register("eYScD",(function(n,i){e(n.exports,"ElementStatePaneWidget",(()=>p)),e(n.exports,"ButtonProvider",(()=>m));var o=t("ixFnt"),s=t("eQFvP"),r=t("9z2ZV"),l=t("1r4or"),a=t("jOloH");const d={forceElementState:"Force element state",toggleElementState:"Toggle Element State"},c=o.i18n.registerUIStrings("panels/elements/ElementStatePaneWidget.ts",d),h=o.i18n.getLocalizedString.bind(void 0,c);class p extends r.Widget.Widget{inputs;inputStates;cssModel;constructor(){super(!0),this.contentElement.className="styles-element-state-pane",r.UIUtils.createTextChild(this.contentElement.createChild("div"),h(d.forceElementState));const e=document.createElement("table");e.classList.add("source-code"),r.ARIAUtils.markAsPresentation(e);const t=[];this.inputs=t,this.inputStates=new WeakMap;const n=e=>{const t=r.Context.Context.instance().flavor(s.DOMModel.DOMNode);if(!(t&&e.target instanceof HTMLInputElement))return;const n=this.inputStates.get(e.target);n&&t.domModel().cssModel().forcePseudoState(t,n,e.target.checked)},i=e=>{const i=document.createElement("td"),o=r.UIUtils.CheckboxLabel.create(":"+e),s=o.checkboxElement;return this.inputStates.set(s,e),s.addEventListener("click",n,!1),t.push(s),i.appendChild(o),i};let o=e.createChild("tr");o.appendChild(i("active")),o.appendChild(i("hover")),o=e.createChild("tr"),o.appendChild(i("focus")),o.appendChild(i("visited")),o=e.createChild("tr"),o.appendChild(i("focus-within")),o.appendChild(i("focus-visible")),o=e.createChild("tr"),o.appendChild(i("target")),this.contentElement.appendChild(e),r.Context.Context.instance().addFlavorChangeListener(s.DOMModel.DOMNode,this.update,this)}updateModel(e){this.cssModel!==e&&(this.cssModel&&this.cssModel.removeEventListener(s.CSSModel.Events.PseudoStateForced,this.update,this),this.cssModel=e,this.cssModel&&this.cssModel.addEventListener(s.CSSModel.Events.PseudoStateForced,this.update,this))}wasShown(){super.wasShown(),this.registerCSSFiles([a.default]),this.update()}update(){if(!this.isShowing())return;let e=r.Context.Context.instance().flavor(s.DOMModel.DOMNode);if(e&&(e=e.enclosingElementOrSelf()),this.updateModel(e?e.domModel().cssModel():null),e){const t=e.domModel().cssModel().pseudoState(e);for(const n of this.inputs){n.disabled=Boolean(e.pseudoType());const i=this.inputStates.get(n);n.checked=!(!t||void 0===i)&&t.indexOf(i)>=0}}else for(const e of this.inputs)e.disabled=!0,e.checked=!1}}let u;class m{button;view;constructor(){this.button=new r.Toolbar.ToolbarToggle(h(d.toggleElementState),""),this.button.setText(o.i18n.lockedString(":hov")),this.button.addEventListener(r.Toolbar.ToolbarButton.Events.Click,this.clicked,this),this.button.element.classList.add("monospace"),this.view=new p}static instance(e={forceNew:null}){const{forceNew:t}=e;return u&&!t||(u=new m),u}clicked(){l.ElementsPanel.instance().showToolbarPane(this.view.isShowing()?null:this.view,this.button)}item(){return this.button}}})),t.register("jOloH",(function(t,n){e(t.exports,"default",(()=>o));const i=new CSSStyleSheet;i.replaceSync("/**\n * Copyright 2017 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.styles-element-state-pane {\n  overflow: hidden;\n  padding-left: 2px;\n  background-color: var(--color-background-elevation-1);\n  border-bottom: 1px solid var(--color-details-hairline);\n  margin-top: 0;\n  padding-bottom: 2px;\n}\n\n.styles-element-state-pane > div {\n  margin: 8px 4px 6px;\n}\n\n.styles-element-state-pane > table {\n  width: 100%;\n  border-spacing: 0;\n}\n\n.styles-element-state-pane td {\n  padding: 0;\n}\n\n/*# sourceURL=elementStatePaneWidget.css */\n");var o=i})),t.register("l294p",(function(n,i){e(n.exports,"LayoutSidebarPane",(()=>u));var o=t("koSS8"),s=t("eQFvP"),r=t("9z2ZV");t("Px6PZ");var l=t("73QJC"),a=t("1r4or");const d=e=>{const t=e.getAttribute("class");return{id:e.id,color:"#000",name:e.localName(),domId:e.getAttribute("id"),domClasses:t?t.split(/\s+/).filter((e=>Boolean(e))):void 0,enabled:!1,reveal:()=>{a.ElementsPanel.instance().revealAndSelectNode(e,!0,!0),e.scrollIntoView()},highlight:()=>{e.highlight()},hideHighlight:()=>{s.OverlayModel.OverlayModel.hideDOMNodeHighlight()},toggle:e=>{throw new Error("Not implemented")},setColor(e){throw new Error("Not implemented")}}},c=e=>e.map((e=>{const t=d(e),n=e.id;return{...t,color:e.domModel().overlayModel().colorOfGridInPersistentOverlay(n)||"#000",enabled:e.domModel().overlayModel().isHighlightedGridInPersistentOverlay(n),toggle:t=>{t?e.domModel().overlayModel().highlightGridInPersistentOverlay(n):e.domModel().overlayModel().hideGridInPersistentOverlay(n)},setColor(t){this.color=t,e.domModel().overlayModel().setColorOfGridInPersistentOverlay(n,t)}}}));let h;const p=e=>e.map((e=>{const t=d(e),n=e.id;return{...t,color:e.domModel().overlayModel().colorOfFlexInPersistentOverlay(n)||"#000",enabled:e.domModel().overlayModel().isHighlightedFlexContainerInPersistentOverlay(n),toggle:t=>{t?e.domModel().overlayModel().highlightFlexContainerInPersistentOverlay(n):e.domModel().overlayModel().hideFlexContainerInPersistentOverlay(n)},setColor(t){this.color=t,e.domModel().overlayModel().setColorOfFlexInPersistentOverlay(n,t)}}}));class u extends r.ThrottledWidget.ThrottledWidget{layoutPane;settings;uaShadowDOMSetting;boundOnSettingChanged;domModels;constructor(){super(!0),this.layoutPane=new l.LayoutPane,this.contentElement.appendChild(this.layoutPane),this.settings=["showGridLineLabels","showGridTrackSizes","showGridAreas","extendGridLines"],this.uaShadowDOMSetting=o.Settings.Settings.instance().moduleSetting("showUAShadowDOM"),this.boundOnSettingChanged=this.onSettingChanged.bind(this),this.domModels=[]}static instance(e={forceNew:null}){const{forceNew:t}=e;return h&&!t||(h=new u),h}modelAdded(e){const t=e.overlayModel();t.addEventListener(s.OverlayModel.Events.PersistentGridOverlayStateChanged,this.update,this),t.addEventListener(s.OverlayModel.Events.PersistentFlexContainerOverlayStateChanged,this.update,this),this.domModels.push(e)}modelRemoved(e){const t=e.overlayModel();t.removeEventListener(s.OverlayModel.Events.PersistentGridOverlayStateChanged,this.update,this),t.removeEventListener(s.OverlayModel.Events.PersistentFlexContainerOverlayStateChanged,this.update,this),this.domModels=this.domModels.filter((t=>t!==e))}async fetchNodesByStyle(e){const t=this.uaShadowDOMSetting.get(),n=[];for(const i of this.domModels)try{const o=await i.getNodesByStyle(e,!0);for(const e of o){const o=i.nodeForId(e);null===o||!t&&o.ancestorUserAgentShadowRoot()||n.push(o)}}catch(e){console.warn(e)}return n}async fetchGridNodes(){return await this.fetchNodesByStyle([{name:"display",value:"grid"},{name:"display",value:"inline-grid"}])}async fetchFlexContainerNodes(){return await this.fetchNodesByStyle([{name:"display",value:"flex"},{name:"display",value:"inline-flex"}])}mapSettings(){const e=[];for(const t of this.settings){const n=o.Settings.Settings.instance().moduleSetting(t),i=n.get(),s=n.type();if(!s)throw new Error("A setting provided to LayoutSidebarPane does not have a setting type");if(s!==o.Settings.SettingType.BOOLEAN&&s!==o.Settings.SettingType.ENUM)throw new Error("A setting provided to LayoutSidebarPane does not have a supported setting type");const r={type:s,name:n.name,title:n.title()};("boolean"==typeof i||"string"==typeof i)&&e.push({...r,value:i,options:n.options().map((e=>({...e,value:e.value})))})}return e}async doUpdate(){this.layoutPane.data={gridElements:c(await this.fetchGridNodes()),flexContainerElements:p(await this.fetchFlexContainerNodes()),settings:this.mapSettings()}}onSettingChanged(e){o.Settings.Settings.instance().moduleSetting(e.data.setting).set(e.data.value)}wasShown(){for(const e of this.settings)o.Settings.Settings.instance().moduleSetting(e).addChangeListener(this.update,this);this.layoutPane.addEventListener("settingchanged",this.boundOnSettingChanged);for(const e of this.domModels)this.modelRemoved(e);this.domModels=[],s.TargetManager.TargetManager.instance().observeModels(s.DOMModel.DOMModel,this),r.Context.Context.instance().addFlavorChangeListener(s.DOMModel.DOMNode,this.update,this),this.uaShadowDOMSetting.addChangeListener(this.update,this),this.update()}willHide(){for(const e of this.settings)o.Settings.Settings.instance().moduleSetting(e).removeChangeListener(this.update,this);this.layoutPane.removeEventListener("settingchanged",this.boundOnSettingChanged),s.TargetManager.TargetManager.instance().unobserveModels(s.DOMModel.DOMModel,this),r.Context.Context.instance().removeFlavorChangeListener(s.DOMModel.DOMNode,this.update,this),this.uaShadowDOMSetting.removeChangeListener(this.update,this)}}}));
//# sourceMappingURL=elements.8926e5ab.js.map
