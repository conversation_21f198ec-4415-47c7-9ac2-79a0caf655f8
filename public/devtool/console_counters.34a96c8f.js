function e(e,t,s,o){Object.defineProperty(e,t,{get:s,set:o,enumerable:!0,configurable:!0})}var t=("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}).parcelRequire20b5;t.register("aOfum",(function(s,o){e(s.exports,"WarningErrorCounter",(()=>t("4ReuL")));t("4ReuL")})),t.register("4ReuL",(function(s,o){e(s.exports,"WarningErrorCounter",(()=>m));var n=t("koSS8"),i=t("e7bLS"),r=t("ixFnt"),a=t("eQFvP"),l=t("fm4u4"),u=t("cY3yZ"),d=t("hzgLE"),c=t("9z2ZV");const h={sErrors:"{n, plural, =1 {# error} other {# errors}}",sWarnings:"{n, plural, =1 {# warning} other {# warnings}}",openConsoleToViewS:"Open Console to view {PH1}",openIssuesToView:"{n, plural, =1 {Open Issues to view # issue:} other {Open Issues to view # issues:}}"},p=r.i18n.registerUIStrings("panels/console_counters/WarningErrorCounter.ts",h),C=r.i18n.getLocalizedString.bind(void 0,p);let g;class m{toolbarItem;consoleCounter;issueCounter;throttler;updatingForTest;constructor(){m.instanceForTest=this;const e=document.createElement("div");this.toolbarItem=new c.Toolbar.ToolbarItemWithCompactLayout(e),this.toolbarItem.setVisible(!1),this.toolbarItem.addEventListener("CompactLayoutUpdated",this.onSetCompactLayout,this),this.consoleCounter=new u.IconButton.IconButton,e.appendChild(this.consoleCounter),this.consoleCounter.data={clickHandler:n.Console.Console.instance().show.bind(n.Console.Console.instance()),groups:[{iconName:"error_icon"},{iconName:"warning_icon"}]};const t=l.IssuesManager.IssuesManager.instance();this.issueCounter=new d.IssueCounter.IssueCounter,e.appendChild(this.issueCounter),this.issueCounter.data={clickHandler:()=>{i.userMetrics.issuesPanelOpenedFrom(i.UserMetrics.IssueOpener.StatusBarIssuesCounter),c.ViewManager.ViewManager.instance().showView("issues-pane")},issuesManager:t,displayMode:"OnlyMostImportant"},this.throttler=new n.Throttler.Throttler(100),a.ConsoleModel.ConsoleModel.instance().addEventListener(a.ConsoleModel.Events.ConsoleCleared,this.update,this),a.ConsoleModel.ConsoleModel.instance().addEventListener(a.ConsoleModel.Events.MessageAdded,this.update,this),a.ConsoleModel.ConsoleModel.instance().addEventListener(a.ConsoleModel.Events.MessageUpdated,this.update,this),t.addEventListener("IssuesCountUpdated",this.update,this),this.update()}onSetCompactLayout(e){this.setCompactLayout(e.data)}setCompactLayout(e){this.consoleCounter.data={...this.consoleCounter.data,compact:e},this.issueCounter.data={...this.issueCounter.data,compact:e}}static instance(e={forceNew:null}){const{forceNew:t}=e;return g&&!t||(g=new m),g}updatedForTest(){}update(){this.updatingForTest=!0,this.throttler.schedule(this.updateThrottled.bind(this))}get titlesForTesting(){const e=this.consoleCounter.shadowRoot?.querySelector("button");return e?e.getAttribute("aria-label"):null}async updateThrottled(){const e=a.ConsoleModel.ConsoleModel.instance().errors(),t=a.ConsoleModel.ConsoleModel.instance().warnings(),s=l.IssuesManager.IssuesManager.instance(),o=s.numberOfIssues(),n=e=>0===e?void 0:`${e}`,i=C(h.sErrors,{n:e}),r=C(h.sWarnings,{n:t}),u=[n(e),n(t)];let p="";e&&t?p=`${i}, ${r}`:e?p=i:t&&(p=r);const g=C(h.openConsoleToViewS,{PH1:p}),m=this.consoleCounter.data;this.consoleCounter.data={...m,groups:m.groups.map(((e,t)=>({...e,text:u[t]}))),accessibleName:g},c.Tooltip.Tooltip.install(this.consoleCounter,g),this.consoleCounter.classList.toggle("hidden",!(e||t));const w=d.IssueCounter.getIssueCountsEnumeration(s),I=`${C(h.openIssuesToView,{n:o})} ${w}`;c.Tooltip.Tooltip.install(this.issueCounter,I),this.issueCounter.data={...this.issueCounter.data,accessibleName:I},this.issueCounter.classList.toggle("hidden",!o),this.toolbarItem.setVisible(Boolean(e||t||o)),c.InspectorView.InspectorView.instance().toolbarItemResized(),this.updatingForTest=!1,this.updatedForTest()}item(){return this.toolbarItem}static instanceForTest=null}}));
//# sourceMappingURL=console_counters.34a96c8f.js.map
