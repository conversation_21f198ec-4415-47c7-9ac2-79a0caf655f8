function e(e,t,r,s){Object.defineProperty(e,t,{get:r,set:s,enumerable:!0,configurable:!0})}var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{},r={},s={},n=t.parcelRequire20b5;null==n&&((n=function(e){if(e in r)return r[e].exports;if(e in s){var t=s[e];delete s[e];var n={id:e,exports:{}};return r[e]=n,t.call(n.exports,n,n.exports),n.exports}var i=new Error("Cannot find module '"+e+"'");throw i.code="MODULE_NOT_FOUND",i}).register=function(e,t){s[e]=t},t.parcelRequire20b5=n),n.register("9Ygmk",(function(e,t){n("27Lyk").register(JSON.parse('{"3qnR3":"weda_app.fd8f1811.js","4P7Yb":"en-US.28c3c6bb.js"}'))})),n.register("koSS8",(function(t,r){e(t.exports,"UIString",(()=>n("abeGt"))),e(t.exports,"AppProvider",(()=>n("lUzIH"))),e(t.exports,"Base64",(()=>n("8r8r9"))),e(t.exports,"CharacterIdMap",(()=>n("53zGy"))),e(t.exports,"Color",(()=>n("4Pxzi"))),e(t.exports,"ColorUtils",(()=>n("4JpmP"))),e(t.exports,"Console",(()=>n("7U4D1"))),e(t.exports,"EventTarget",(()=>n("8KVjw"))),e(t.exports,"Lazy",(()=>n("5voj1"))),e(t.exports,"Linkifier",(()=>n("cv4pb"))),e(t.exports,"ObjectWrapper",(()=>n("5gQQ4"))),e(t.exports,"ParsedURL",(()=>n("fjeUg"))),e(t.exports,"Progress",(()=>n("3oEUj"))),e(t.exports,"ResolverBase",(()=>n("jNiNc"))),e(t.exports,"ResourceType",(()=>n("k0gJh"))),e(t.exports,"Revealer",(()=>n("eP1Mk"))),e(t.exports,"Runnable",(()=>n("dgs9F"))),e(t.exports,"SegmentedRange",(()=>n("fhm4u"))),e(t.exports,"Settings",(()=>n("dMaA7"))),e(t.exports,"SimpleHistoryManager",(()=>n("aWsSW"))),e(t.exports,"StringOutputStream",(()=>n("BpZ1m"))),e(t.exports,"Throttler",(()=>n("dDY8k"))),e(t.exports,"Trie",(()=>n("8fICD"))),e(t.exports,"Worker",(()=>n("1wxTh"))),e(t.exports,"WasmDisassembly",(()=>n("3XJTj")));n("i7byn"),n("lUzIH"),n("8r8r9"),n("53zGy"),n("4Pxzi"),n("4JpmP"),n("7U4D1"),n("59M8v"),n("8KVjw"),n("iP7gF"),n("5voj1"),n("cv4pb"),n("5gQQ4"),n("fjeUg"),n("3oEUj"),n("5G4Uo"),n("jNiNc"),n("k0gJh"),n("eP1Mk"),n("dgs9F"),n("fhm4u"),n("dMaA7"),n("aWsSW"),n("BpZ1m"),n("1VFIW"),n("dDY8k"),n("8fICD"),n("3XJTj"),n("1wxTh");n("lz7WY");n("abeGt")})),n.register("i7byn",(function(e,t){})),n.register("lUzIH",(function(t,r){e(t.exports,"registerAppProvider",(()=>o)),e(t.exports,"getRegisteredAppProviders",(()=>a)),n("9X2mn");var s=n("elz4U");const i=[];function o(e){i.push(e)}function a(){return i.filter((e=>s.Runtime.isDescriptorEnabled({experiment:void 0,condition:e.condition}))).sort(((e,t)=>(e.order||0)-(t.order||0)))}})),n.register("9X2mn",(function(t,r){e(t.exports,"Runtime",(()=>n("elz4U")));n("elz4U")})),n.register("elz4U",(function(t,r){e(t.exports,"getRemoteBase",(()=>l)),e(t.exports,"Runtime",(()=>c)),e(t.exports,"experiments",(()=>p)),e(t.exports,"ExperimentsSupport",(()=>u)),e(t.exports,"Experiment",(()=>h)),e(t.exports,"ExperimentName",(()=>g)),e(t.exports,"ConditionName",(()=>m)),n("lz7WY");var s=n("5jytL");const i=new URLSearchParams(location.search);let o,a="";function l(e=self.location.toString()){const t=new URL(e),r=t.searchParams.get("remoteBase");if(!r)return null;const s=/\/serve_file\/(@[0-9a-zA-Z]+)\/?$/.exec(r);return s?{base:`${t.origin}/remote/serve_file/${s[1]}/`,version:s[1]}:null}class c{constructor(){}static instance(e={forceNew:null}){const{forceNew:t}=e;return o&&!t||(o=new c),o}static removeInstance(){o=void 0}static queryParam(e){return i.get(e)}static experimentsSetting(){try{return JSON.parse(self.localStorage&&self.localStorage.experiments?self.localStorage.experiments:"{}")}catch(e){return console.error("Failed to parse localStorage['experiments']"),{}}}static setPlatform(e){a=e}static platform(){return a}static isDescriptorEnabled(e){const t=e.experiment;if("*"===t)return!0;if(t&&t.startsWith("!")&&p.isEnabled(t.substring(1)))return!1;if(t&&!t.startsWith("!")&&!p.isEnabled(t))return!1;const r=e.condition;return!(r&&!r.startsWith("!")&&!c.queryParam(r))&&!(r&&r.startsWith("!")&&c.queryParam(r.substring(1)))}loadLegacyModule(e){return import(`../../${e}`)}}class u{#e;#t;#r;#s;#n;constructor(){this.#e=[],this.#t=new Set,this.#r=new Set,this.#s=new Set,this.#n=new Set}allConfigurableExperiments(){const e=[];for(const t of this.#e)this.#r.has(t.name)||e.push(t);return e}enabledExperiments(){return this.#e.filter((e=>e.isEnabled()))}setExperimentsSetting(e){self.localStorage&&(self.localStorage.experiments=JSON.stringify(e))}register(e,t,r,n){s.DCHECK((()=>!this.#t.has(e)),"Duplicate registration of experiment "+e),this.#t.add(e),this.#e.push(new h(this,e,t,Boolean(r),n??""))}isEnabled(e){return this.checkExperiment(e),!1!==c.experimentsSetting()[e]&&(!(!this.#r.has(e)&&!this.#s.has(e))||(!!this.#n.has(e)||Boolean(c.experimentsSetting()[e])))}setEnabled(e,t){this.checkExperiment(e);const r=c.experimentsSetting();r[e]=t,this.setExperimentsSetting(r)}enableExperimentsTransiently(e){for(const t of e)this.checkExperiment(t),this.#r.add(t)}enableExperimentsByDefault(e){for(const t of e)this.checkExperiment(t),this.#s.add(t)}setServerEnabledExperiments(e){for(const t of e)this.checkExperiment(t),this.#n.add(t)}enableForTest(e){this.checkExperiment(e),this.#r.add(e)}clearForTest(){this.#e=[],this.#t.clear(),this.#r.clear(),this.#s.clear(),this.#n.clear()}cleanUpStaleExperiments(){const e=c.experimentsSetting(),t={};for(const{name:r}of this.#e)if(e.hasOwnProperty(r)){const s=e[r];(s||this.#s.has(r))&&(t[r]=s)}this.setExperimentsSetting(t)}checkExperiment(e){s.DCHECK((()=>this.#t.has(e)),"Unknown experiment "+e)}}class h{name;title;unstable;docLink;#e;constructor(e,t,r,s,n){this.name=t,this.title=r,this.unstable=s,this.docLink=n,this.#e=e}isEnabled(){return this.#e.isEnabled(this.name)}setEnabled(e){this.#e.setEnabled(this.name,e)}}const p=new u;var g,d,m,f;(d=g||(g={})).CAPTURE_NODE_CREATION_STACKS="captureNodeCreationStacks",d.CSS_OVERVIEW="cssOverview",d.LIVE_HEAP_PROFILE="liveHeapProfile",d.DEVELOPER_RESOURCES_VIEW="developerResourcesView",d.TIMELINE_REPLAY_EVENT="timelineReplayEvent",d.CSP_VIOLATIONS_VIEW="cspViolationsView",d.WASM_DWARF_DEBUGGING="wasmDWARFDebugging",d.ALL="*",d.PROTOCOL_MONITOR="protocolMonitor",d.WEBAUTHN_PANE="webauthnPane",d.SYNC_SETTINGS="syncSettings",d.FULL_ACCESSIBILITY_TREE="fullAccessibilityTree",d.PRECISE_CHANGES="preciseChanges",d.STYLES_PANE_CSS_CHANGES="stylesPaneCSSChanges",d.HEADER_OVERRIDES="headerOverrides",d.CSS_LAYERS="cssLayers",(f=m||(m={})).CAN_DOCK="can_dock",f.NOT_SOURCES_HIDE_ADD_FOLDER="!sources.hide_add_folder"})),n.register("lz7WY",(function(t,r){e(t.exports,"DCHECK",(()=>n("5jytL").DCHECK)),e(t.exports,"assertNever",(()=>n("5Dpml").assertNever)),e(t.exports,"assertNotNullOrUndefined",(()=>n("5Dpml").assertNotNullOrUndefined)),e(t.exports,"ArrayUtilities",(()=>n("03Tsr"))),e(t.exports,"DateUtilities",(()=>n("cT3bz"))),e(t.exports,"DevToolsPath",(()=>n("8HTq8"))),e(t.exports,"KeyboardUtilities",(()=>n("ew5xm"))),e(t.exports,"MapUtilities",(()=>n("jsny4"))),e(t.exports,"NumberUtilities",(()=>n("iwS9M"))),e(t.exports,"SetUtilities",(()=>n("bk3Hl"))),e(t.exports,"StringUtilities",(()=>n("cmJc8"))),e(t.exports,"UIString",(()=>n("abeGt"))),e(t.exports,"UserVisibleError",(()=>n("bqRA0")));n("03Tsr"),n("cT3bz"),n("8HTq8"),n("ew5xm"),n("jsny4"),n("iwS9M"),n("bk3Hl"),n("cmJc8"),n("5Dpml"),n("abeGt"),n("bqRA0"),n("5jytL"),n("5Dpml")})),n.register("03Tsr",(function(t,r){e(t.exports,"removeElement",(()=>s)),e(t.exports,"sortRange",(()=>o)),e(t.exports,"binaryIndexOf",(()=>a)),e(t.exports,"lowerBound",(()=>p)),e(t.exports,"intersectOrdered",(()=>c)),e(t.exports,"mergeOrdered",(()=>u)),e(t.exports,"DEFAULT_COMPARATOR",(()=>h)),e(t.exports,"upperBound",(()=>g));const s=(e,t,r)=>{let s=e.indexOf(t);if(-1===s)return!1;if(r)return e.splice(s,1),!0;for(let r=s+1,n=e.length;r<n;++r)e[r]!==t&&(e[s++]=e[r]);return e.length=s,!0};function n(e,t,r){const s=e[t];e[t]=e[r],e[r]=s}function i(e,t,r,s,o,a){if(s<=r)return;const l=function(e,t,r,s,i){const o=e[i];n(e,s,i);let a=r;for(let i=r;i<s;++i)t(e[i],o)<0&&(n(e,a,i),++a);return n(e,s,a),a}(e,t,r,s,Math.floor(Math.random()*(s-r))+r);o<l&&i(e,t,r,l-1,o,a),l<a&&i(e,t,l+1,s,o,a)}function o(e,t,r,s,n,o){return 0===r&&s===e.length-1&&0===n&&o>=s?e.sort(t):i(e,t,r,s,n,o),e}const a=(e,t,r)=>{const s=p(e,t,r);return s<e.length&&0===r(t,e[s])?s:-1};function l(e,t,r,s){const n=[];let i=0,o=0;for(;i<e.length&&o<t.length;){const a=r(e[i],t[o]);!s&&a||n.push(a<=0?e[i]:t[o]),a<=0&&i++,a>=0&&o++}if(s){for(;i<e.length;)n.push(e[i++]);for(;o<t.length;)n.push(t[o++])}return n}const c=(e,t,r)=>l(e,t,r,!1),u=(e,t,r)=>l(e,t,r,!0),h=(e,t)=>e<t?-1:e>t?1:0;function p(e,t,r,s,n){let i=s||0,o=void 0!==n?n:e.length;for(;i<o;){const s=i+o>>1;r(t,e[s])>0?i=s+1:o=s}return o}function g(e,t,r,s,n){let i=s||0,o=void 0!==n?n:e.length;for(;i<o;){const s=i+o>>1;r(t,e[s])>=0?i=s+1:o=s}return o}})),n.register("cT3bz",(function(t,r){e(t.exports,"isValid",(()=>s)),e(t.exports,"toISO8601Compact",(()=>n));const s=e=>!isNaN(e.getTime()),n=e=>{function t(e){return(e>9?"":"0")+e}return e.getFullYear()+t(e.getMonth()+1)+t(e.getDate())+"T"+t(e.getHours())+t(e.getMinutes())+t(e.getSeconds())}})),n.register("8HTq8",(function(t,r){e(t.exports,"EmptyUrlString",(()=>s)),e(t.exports,"EmptyRawPathString",(()=>n)),e(t.exports,"EmptyEncodedPathString",(()=>i));const s="";const n="";const i=""})),n.register("ew5xm",(function(t,r){e(t.exports,"ENTER_KEY",(()=>s)),e(t.exports,"ESCAPE_KEY",(()=>n)),e(t.exports,"TAB_KEY",(()=>i)),e(t.exports,"ARROW_KEYS",(()=>o)),e(t.exports,"keyIsArrowKey",(()=>a));const s="Enter",n="Escape",i="Tab",o=new Set(["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"]);function a(e){return o.has(e)}})),n.register("jsny4",(function(t,r){e(t.exports,"inverse",(()=>s)),e(t.exports,"Multimap",(()=>n));const s=function(e){const t=new n;for(const[r,s]of e.entries())t.set(s,r);return t};class n{map=new Map;set(e,t){let r=this.map.get(e);r||(r=new Set,this.map.set(e,r)),r.add(t)}get(e){return this.map.get(e)||new Set}has(e){return this.map.has(e)}hasValue(e,t){const r=this.map.get(e);return!!r&&r.has(t)}get size(){return this.map.size}delete(e,t){const r=this.get(e);if(!r)return!1;const s=r.delete(t);return r.size||this.map.delete(e),s}deleteAll(e){this.map.delete(e)}keysArray(){return[...this.map.keys()]}valuesArray(){const e=[];for(const t of this.map.values())e.push(...t.values());return e}clear(){this.map.clear()}}})),n.register("iwS9M",(function(t,r){e(t.exports,"clamp",(()=>s)),e(t.exports,"mod",(()=>n)),e(t.exports,"bytesToString",(()=>i)),e(t.exports,"toFixedIfFloating",(()=>o)),e(t.exports,"floor",(()=>a)),e(t.exports,"greatestCommonDivisor",(()=>l)),e(t.exports,"aspectRatio",(()=>u)),e(t.exports,"withThousandsSeparator",(()=>h));const s=(e,t,r)=>{let s=e;return e<t?s=t:e>r&&(s=r),s},n=(e,t)=>(e%t+t)%t,i=e=>{if(e<1e3)return`${e.toFixed(0)} B`;const t=e/1e3;if(t<100)return`${t.toFixed(1)} kB`;if(t<1e3)return`${t.toFixed(0)} kB`;const r=t/1e3;return r<100?`${r.toFixed(1)} MB`:`${r.toFixed(0)} MB`},o=e=>{if(!e||Number.isNaN(Number(e)))return e;const t=Number(e);return t%1?t.toFixed(3):String(t)},a=(e,t=0)=>{const r=Math.pow(10,t);return Math.floor(e*r)/r},l=(e,t)=>{for(e=Math.round(e),t=Math.round(t);0!==t;){const r=t;t=e%t,e=r}return e},c=new Map([["8∶5","16∶10"]]),u=(e,t)=>{const r=l(e,t);0!==r&&(e/=r,t/=r);const s=`${e}∶${t}`;return c.get(s)||s},h=function(e){let t=String(e);const r=/(\d+)(\d{3})/;for(;t.match(r);)t=t.replace(r,"$1 $2");return t}})),n.register("bk3Hl",(function(t,r){e(t.exports,"addAll",(()=>s)),e(t.exports,"isEqual",(()=>n));const s=function(e,t){for(const r of t)e.add(r)},n=function(e,t){if(e===t)return!0;if(e.size!==t.size)return!1;for(const r of e)if(!t.has(r))return!1;return!0}})),n.register("cmJc8",(function(t,r){e(t.exports,"escapeCharacters",(()=>s)),e(t.exports,"formatAsJSLiteral",(()=>o)),e(t.exports,"sprintf",(()=>a)),e(t.exports,"toBase64",(()=>l)),e(t.exports,"findIndexesOfSubString",(()=>c)),e(t.exports,"findLineEndingIndexes",(()=>u)),e(t.exports,"isWhitespace",(()=>h)),e(t.exports,"trimURL",(()=>p)),e(t.exports,"collapseWhitespace",(()=>g)),e(t.exports,"reverse",(()=>d)),e(t.exports,"replaceControlCharacters",(()=>m)),e(t.exports,"countWtf8Bytes",(()=>f)),e(t.exports,"stripLineBreaks",(()=>S)),e(t.exports,"toTitleCase",(()=>E)),e(t.exports,"removeURLFragment",(()=>b)),e(t.exports,"regexSpecialCharacters",(()=>x)),e(t.exports,"filterRegex",(()=>w)),e(t.exports,"createSearchRegex",(()=>v)),e(t.exports,"createPlainTextSearchRegex",(()=>_)),e(t.exports,"caseInsensetiveComparator",(()=>T)),e(t.exports,"hashCode",(()=>A)),e(t.exports,"compare",(()=>I)),e(t.exports,"trimMiddle",(()=>L)),e(t.exports,"trimEndWithMaxLength",(()=>R)),e(t.exports,"escapeForRegExp",(()=>P)),e(t.exports,"naturalOrderComparator",(()=>C)),e(t.exports,"base64ToSize",(()=>N)),e(t.exports,"SINGLE_QUOTE",(()=>O)),e(t.exports,"DOUBLE_QUOTE",(()=>M)),e(t.exports,"findUnclosedCssQuote",(()=>k));const s=(e,t)=>{let r=!1;for(let s=0;s<t.length;++s)if(-1!==e.indexOf(t.charAt(s))){r=!0;break}if(!r)return String(e);let s="";for(let r=0;r<e.length;++r)-1!==t.indexOf(e.charAt(r))&&(s+="\\"),s+=e.charAt(r);return s},n=(e,t)=>e.toString(16).toUpperCase().padStart(t,"0"),i=new Map([["\b","\\b"],["\f","\\f"],["\n","\\n"],["\r","\\r"],["\t","\\t"],["\v","\\v"],["'","\\'"],["\\","\\\\"],["\x3c!--","\\x3C!--"],["<script","\\x3Cscript"],["</script","\\x3C/script"]]),o=e=>{const t=/(\\|<(?:!--|\/?script))|(\p{Control})|(\p{Surrogate})/gu,r=/(\\|'|<(?:!--|\/?script))|(\p{Control})|(\p{Surrogate})/gu,s=(e,t,r,s)=>{if(r){if(i.has(r))return i.get(r);return"\\x"+n(r.charCodeAt(0),2)}if(s){return"\\u"+n(s.charCodeAt(0),4)}return t?i.get(t)||"":e};let o="",a="";return e.includes("'")?e.includes('"')?e.includes("`")||e.includes("${")?(a="'",o=e.replaceAll(r,s)):(a="`",o=e.replaceAll(t,s)):(a='"',o=e.replaceAll(t,s)):(a="'",o=e.replaceAll(t,s)),`${a}${o}${a}`},a=(e,...t)=>{let r=0;return e.replaceAll(/%(?:(\d+)\$)?(?:\.(\d*))?([%dfs])/g,((e,s,n,i)=>{if("%"===i)return"%";if(void 0!==s&&(r=parseInt(s,10)-1,r<0))throw new RangeError(`Invalid parameter index ${r+1}`);if(r>=t.length)throw new RangeError(`Expected at least ${r+1} format parameters, but only ${t.length} where given.`);if("s"===i){const e=String(t[r++]);return void 0!==n?e.substring(0,Number(n)):e}let o=Number(t[r++]);return isNaN(o)&&(o=0),"d"===i?String(Math.floor(o)).padStart(Number(n),"0"):void 0!==n?o.toFixed(Number(n)):String(o)}))},l=e=>{function t(e){return e<26?e+65:e<52?e+71:e<62?e-4:62===e?43:63===e?47:65}const r=(new TextEncoder).encode(e.toString()),s=r.length;let n,i="";if(0===s)return i;let o=0;for(let e=0;e<s;e++)n=e%3,o|=r[e]<<(16>>>n&24),2===n&&(i+=String.fromCharCode(t(o>>>18&63),t(o>>>12&63),t(o>>>6&63),t(63&o)),o=0);return 0===n?i+=String.fromCharCode(t(o>>>18&63),t(o>>>12&63),61,61):1===n&&(i+=String.fromCharCode(t(o>>>18&63),t(o>>>12&63),t(o>>>6&63),61)),i},c=(e,t)=>{const r=[];let s=e.indexOf(t);for(;-1!==s;)r.push(s),s=e.indexOf(t,s+t.length);return r},u=e=>{const t=c(e,"\n");return t.push(e.length),t},h=e=>/^\s*$/.test(e),p=(e,t)=>{let r=e.replace(/^(https|http|file):\/\//i,"");return t&&r.toLowerCase().startsWith(t.toLowerCase())&&(r=r.substr(t.length)),r},g=e=>e.replace(/[\s\xA0]+/g," "),d=e=>e.split("").reverse().join(""),m=e=>e.replace(/[\0-\x08\x0B\f\x0E-\x1F\x80-\x9F]/g,"�"),f=e=>{let t=0;for(let r=0;r<e.length;r++){const s=e.charCodeAt(r);if(s<=127)t++;else if(s<=2047)t+=2;else if(s<55296||57343<s)t+=3;else{if(s<=56319&&r+1<e.length){const s=e.charCodeAt(r+1);if(56320<=s&&s<=57343){t+=4,r++;continue}}t+=3}}return t},S=e=>e.replace(/(\r)?\n/g,""),E=e=>e.substring(0,1).toUpperCase()+e.substring(1),b=e=>{const t=new URL(e);return t.hash="",t.toString()},y="^[]{}()\\.^$*+?|-,",x=function(){return y},w=function(e){let t="";for(let r=0;r<e.length;++r){let s=e.charAt(r);-1!==y.indexOf(s)&&(s="\\"+s),r&&(t+="[^\\0"+s+"]*"),t+=s}return new RegExp(t,"i")},v=function(e,t,r){const s=t?"g":"gi";let n;if(r)try{n=new RegExp(e,s)}catch(e){}return n||(n=_(e,s)),n},T=function(e,t){return(e=e.toUpperCase())===(t=t.toUpperCase())?0:e>t?1:-1},A=function(e){if(!e)return 0;const t=4294967291;let r=0,s=1;for(let n=0;n<e.length;n++){r=(r+s*(1506996573*e.charCodeAt(n)))%t,s=1345575271*s%t}return r=(r+s*(t-1))%t,Math.abs(0|r)},I=(e,t)=>e>t?1:e<t?-1:0,L=(e,t)=>{if(e.length<=t)return String(e);let r=t>>1,s=t-r-1;return e.codePointAt(e.length-s-1)>=65536&&(--s,++r),r>0&&e.codePointAt(r-1)>=65536&&--r,e.substr(0,r)+"…"+e.substr(e.length-s,s)},R=(e,t)=>e.length<=t?String(e):e.substr(0,t-1)+"…",P=e=>s(e,y),C=(e,t)=>{const r=/^\d+|^\D+/;let s,n,i,o;for(;;){if(!e)return t?-1:0;if(!t)return 1;if(s=e.match(r)[0],n=t.match(r)[0],i=!Number.isNaN(Number(s)),o=!Number.isNaN(Number(n)),i&&!o)return-1;if(o&&!i)return 1;if(i&&o){const e=Number(s)-Number(n);if(e)return e;if(s.length!==n.length)return Number(s)||Number(n)?n.length-s.length:s.length-n.length}else if(s!==n)return s<n?-1:1;e=e.substring(s.length),t=t.substring(n.length)}},N=function(e){if(!e)return 0;let t=3*e.length/4;return"="===e[e.length-1]&&t--,e.length>1&&"="===e[e.length-2]&&t--,t},O="'",M='"',k=function(e){let t="";for(let r=0;r<e.length;++r){const s=e[r];"\\"!==s?s!==O&&s!==M||(t===s?t="":""===t&&(t=s)):r++}return t},_=function(e,t){let r="";for(let t=0;t<e.length;++t){const s=e.charAt(t);-1!==x().indexOf(s)&&(r+="\\"),r+=s}return new RegExp(r,t||"")}})),n.register("5Dpml",(function(t,r){function s(e){if(null==e)throw new Error(`Expected given value to not be null/undefined but it was: ${e}`)}function n(e,t){throw new Error(t)}e(t.exports,"assertNotNullOrUndefined",(()=>s)),e(t.exports,"assertNever",(()=>n))})),n.register("abeGt",(function(t,r){e(t.exports,"LocalizedEmptyString",(()=>s));const s=""})),n.register("bqRA0",(function(t,r){e(t.exports,"UserVisibleError",(()=>s)),e(t.exports,"isUserVisibleError",(()=>n));class s extends Error{message;constructor(e){super(e),this.message=e}}function n(e){return"object"==typeof e&&null!==e&&e instanceof s}})),n.register("5jytL",(function(t,r){function s(e,t="DCHECK"){if(!e())throw new Error(t+":"+(new Error).stack)}e(t.exports,"DCHECK",(()=>s))})),n.register("8r8r9",(function(t,r){e(t.exports,"decode",(()=>i));const s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n=new Uint8Array(123);for(let e=0;e<s.length;++e)n[s.charCodeAt(e)]=e;function i(e){let t=3*e.length/4>>>0;61===e.charCodeAt(e.length-2)?t-=2:61===e.charCodeAt(e.length-1)&&(t-=1);const r=new Uint8Array(t);for(let t=0,s=0;t<e.length;t+=4){const i=n[e.charCodeAt(t+0)],o=n[e.charCodeAt(t+1)],a=n[e.charCodeAt(t+2)],l=n[e.charCodeAt(t+3)];r[s++]=i<<2|o>>4,r[s++]=(15&o)<<4|a>>2,r[s++]=(3&a)<<6|63&l}return r.buffer}})),n.register("53zGy",(function(t,r){e(t.exports,"CharacterIdMap",(()=>s));class s{#i;#o;#a;constructor(){this.#i=new Map,this.#o=new Map,this.#a=33}toChar(e){let t=this.#i.get(e);if(!t){if(this.#a>=65535)throw new Error("CharacterIdMap ran out of capacity!");t=String.fromCharCode(this.#a++),this.#i.set(e,t),this.#o.set(t,e)}return t}fromChar(e){const t=this.#o.get(e);return void 0===t?null:t}}})),n.register("4Pxzi",(function(t,r){e(t.exports,"Color",(()=>a)),e(t.exports,"Format",(()=>c)),e(t.exports,"Nicknames",(()=>p)),e(t.exports,"Regex",(()=>l)),e(t.exports,"PageHighlight",(()=>m)),e(t.exports,"SourceOrderHighlight",(()=>f)),e(t.exports,"IsolationModeHighlight",(()=>S)),e(t.exports,"Generator",(()=>E)),n("lz7WY");var s=n("cmJc8"),i=n("5jytL"),o=n("4JpmP");class a{#l;#c;#u;#h;#p;#g;constructor(e,t,r){this.#l=void 0,this.#c=void 0,this.#u=e,this.#h=r||null,this.#p=Boolean(this.#h),this.#g=t,void 0===this.#u[3]&&(this.#u[3]=1);for(let e=0;e<4;++e)this.#u[e]<0&&(this.#u[e]=0,this.#p=!1),this.#u[e]>1&&(this.#u[e]=1,this.#p=!1)}static parse(e){let t=e.toLowerCase().replace(/\s+/g,"").match(/^(?:#([0-9a-f]{3,4}|[0-9a-f]{6}|[0-9a-f]{8})|(\w+))$/i);if(t){if(t[1]){let r,s=t[1].toLowerCase();3===s.length?(r=c.ShortHEX,s=s.charAt(0)+s.charAt(0)+s.charAt(1)+s.charAt(1)+s.charAt(2)+s.charAt(2)):4===s.length?(r=c.ShortHEXA,s=s.charAt(0)+s.charAt(0)+s.charAt(1)+s.charAt(1)+s.charAt(2)+s.charAt(2)+s.charAt(3)+s.charAt(3)):r=6===s.length?c.HEX:c.HEXA;const n=parseInt(s.substring(0,2),16),i=parseInt(s.substring(2,4),16),o=parseInt(s.substring(4,6),16);let l=1;return 8===s.length&&(l=parseInt(s.substring(6,8),16)/255),new a([n/255,i/255,o/255,l],r,e)}if(t[2]){const r=t[2].toLowerCase(),s=p.get(r);if(void 0!==s){const t=a.fromRGBA(s);return t.#g=c.Nickname,t.#h=e,t}return null}return null}if(t=e.toLowerCase().match(/^\s*(?:(rgba?)|(hsla?)|(hwba?))\((.*)\)\s*$/),t){const r=this.splitColorFunctionParameters(t[4],!t[3]);if(!r)return null;const s=void 0!==r[3];if(t[1]){const t=[a.parseRgbNumeric(r[0]),a.parseRgbNumeric(r[1]),a.parseRgbNumeric(r[2]),s?a.parseAlphaNumeric(r[3]):1];return t.indexOf(null)>-1?null:new a(t,s?c.RGBA:c.RGB,e)}if(t[2]||t[3]){const n=[a.parseHueNumeric(r[0]),a.parseSatLightNumeric(r[1]),a.parseSatLightNumeric(r[2]),s?a.parseAlphaNumeric(r[3]):1];if(n.indexOf(null)>-1)return null;const i=[];return t[2]?(a.hsl2rgb(n,i),new a(i,s?c.HSLA:c.HSL,e)):(a.hwb2rgb(n,i),new a(i,s?c.HWBA:c.HWB,e))}}return null}static fromRGBA(e){return new a([e[0]/255,e[1]/255,e[2]/255,e[3]],c.RGBA)}static fromHSVA(e){const t=[];return a.hsva2rgba(e,t),new a(t,c.HSLA)}static splitColorFunctionParameters(e,t){const r=e.trim();let s=[];if(t&&(s=r.split(/\s*,\s*/)),!t||1===s.length)if(s=r.split(/\s+/),"/"===s[3]){if(s.splice(3,1),4!==s.length)return null}else if(s.length>2&&-1!==s[2].indexOf("/")||s.length>3&&-1!==s[3].indexOf("/")){const e=s.slice(2,4).join("");s=s.slice(0,2).concat(e.split(/\//)).concat(s.slice(4))}else if(s.length>=4)return null;return 3!==s.length&&4!==s.length||s.indexOf("")>-1?null:s}static parsePercentOrNumber(e){if(isNaN(e.replace("%","")))return null;const t=parseFloat(e);return-1!==e.indexOf("%")?e.indexOf("%")!==e.length-1?null:t/100:t}static parseRgbNumeric(e){const t=a.parsePercentOrNumber(e);return null===t?null:-1!==e.indexOf("%")?t:t/255}static parseHueNumeric(e){const t=e.replace(/(deg|g?rad|turn)$/,"");if(isNaN(t)||e.match(/\s+(deg|g?rad|turn)/))return null;const r=parseFloat(t);return-1!==e.indexOf("turn")?r%1:-1!==e.indexOf("grad")?r/400%1:-1!==e.indexOf("rad")?r/(2*Math.PI)%1:r/360%1}static parseSatLightNumeric(e){if(e.indexOf("%")!==e.length-1||isNaN(e.replace("%","")))return null;const t=parseFloat(e);return Math.min(1,t/100)}static parseAlphaNumeric(e){return a.parsePercentOrNumber(e)}static hsva2hsla(e,t){const r=e[0];let s=e[1];const n=e[2],i=(2-s)*n;0===n||0===s?s=0:s*=n/(i<1?i:2-i),t[0]=r,t[1]=s,t[2]=i/2,t[3]=e[3]}static hsl2rgb(e,t){const r=e[0];let s=e[1];const n=e[2];function i(e,t,r){return r<0?r+=1:r>1&&(r-=1),6*r<1?e+(t-e)*r*6:2*r<1?t:3*r<2?e+(t-e)*(2/3-r)*6:e}let o;s<0&&(s=0),o=n<=.5?n*(1+s):n+s-n*s;const a=2*n-o,l=r+1/3,c=r,u=r-1/3;t[0]=i(a,o,l),t[1]=i(a,o,c),t[2]=i(a,o,u),t[3]=e[3]}static hwb2rgb(e,t){const r=e[0],s=e[1],n=e[2];if(s+n>=1)t[0]=t[1]=t[2]=s/(s+n),t[3]=e[3];else{a.hsl2rgb([r,1,.5,e[3]],t);for(let e=0;e<3;++e)t[e]+=s-(s+n)*t[e]}}static hsva2rgba(e,t){a.hsva2hsla(e,b),a.hsl2rgb(b,t);for(let e=0;e<b.length;e++)b[e]=0}static desiredLuminance(e,t,r){function s(){return r?(e+.05)*t-.05:(e+.05)/t-.05}let n=s();return(n<0||n>1)&&(r=!r,n=s()),n}static approachColorValue(e,t,r,s,n){let i=e[r],o=1,a=n(e)-s,l=Math.sign(a);for(let t=100;t;t--){if(Math.abs(a)<2e-4)return e[r]=i,i;const t=Math.sign(a);if(t!==l)o/=2,l=t;else if(i<0||i>1)return null;i+=o*(2===r?-a:a),e[r]=i,a=n(e)-s}return null}static findFgColorForContrast(e,t,r){const s=e.hsva(),n=t.rgba(),i=e=>(0,o.luminance)((0,o.blendColors)(a.fromHSVA(e).rgba(),n)),l=(0,o.luminance)(t.rgba()),c=i(s)>l,u=a.desiredLuminance(l,r,c);return a.approachColorValue(s,n,2,u,i)?a.fromHSVA(s):(s[2]=1,a.approachColorValue(s,n,1,u,i)?a.fromHSVA(s):null)}static findFgColorForContrastAPCA(e,t,r){const s=e.hsva(),n=t.rgba(),i=e=>(0,o.luminanceAPCA)(a.fromHSVA(e).rgba()),l=(0,o.luminanceAPCA)(t.rgba()),c=i(s)>=l,u=(0,o.desiredLuminanceAPCA)(l,r,c);if(a.approachColorValue(s,n,2,u,i)){const e=a.fromHSVA(s);if(Math.abs((0,o.contrastRatioAPCA)(t.rgba(),e.rgba()))>=r)return e}if(s[2]=1,a.approachColorValue(s,n,1,u,i)){const e=a.fromHSVA(s);if(Math.abs((0,o.contrastRatioAPCA)(t.rgba(),e.rgba()))>=r)return e}return null}format(){return this.#g}hsla(){return this.#l||(this.#l=(0,o.rgbaToHsla)(this.#u)),this.#l}canonicalHSLA(){const e=this.hsla();return[Math.round(360*e[0]),Math.round(100*e[1]),Math.round(100*e[2]),e[3]]}hsva(){const e=this.hsla(),t=e[0];let r=e[1];const s=e[2];return r*=s<.5?s:1-s,[t,0!==r?2*r/(s+r):0,s+r,e[3]]}hwba(){return this.#c||(this.#c=(0,o.rgbaToHwba)(this.#u)),this.#c}canonicalHWBA(){const e=this.hwba();return[Math.round(360*e[0]),Math.round(100*e[1]),Math.round(100*e[2]),e[3]]}hasAlpha(){return 1!==this.#u[3]}detectHEXFormat(){let e=!0;for(let t=0;t<4;++t){if(Math.round(255*this.#u[t])%17){e=!1;break}}const t=this.hasAlpha(),r=c;return e?t?r.ShortHEXA:r.ShortHEX:t?r.HEXA:r.HEX}asString(e){if(e===this.#g&&this.#p)return this.#h;function t(e){return Math.round(255*e)}function r(e){const t=Math.round(255*e).toString(16);return 1===t.length?"0"+t:t}function n(e){return(Math.round(255*e)/17).toString(16)}switch(e||(e=this.#g),e){case c.Original:return this.#h;case c.RGB:case c.RGBA:{const e=s.sprintf("rgb(%d %d %d",t(this.#u[0]),t(this.#u[1]),t(this.#u[2]));return this.hasAlpha()?e+s.sprintf(" / %d%)",Math.round(100*this.#u[3])):e+")"}case c.HSL:case c.HSLA:{const e=this.hsla(),t=s.sprintf("hsl(%ddeg %d% %d%",Math.round(360*e[0]),Math.round(100*e[1]),Math.round(100*e[2]));return this.hasAlpha()?t+s.sprintf(" / %d%)",Math.round(100*e[3])):t+")"}case c.HWB:case c.HWBA:{const e=this.hwba(),t=s.sprintf("hwb(%ddeg %d% %d%",Math.round(360*e[0]),Math.round(100*e[1]),Math.round(100*e[2]));return this.hasAlpha()?t+s.sprintf(" / %d%)",Math.round(100*e[3])):t+")"}case c.HEXA:return s.sprintf("#%s%s%s%s",r(this.#u[0]),r(this.#u[1]),r(this.#u[2]),r(this.#u[3])).toLowerCase();case c.HEX:return this.hasAlpha()?null:s.sprintf("#%s%s%s",r(this.#u[0]),r(this.#u[1]),r(this.#u[2])).toLowerCase();case c.ShortHEXA:{const e=this.detectHEXFormat();return e!==c.ShortHEXA&&e!==c.ShortHEX?null:s.sprintf("#%s%s%s%s",n(this.#u[0]),n(this.#u[1]),n(this.#u[2]),n(this.#u[3])).toLowerCase()}case c.ShortHEX:return this.hasAlpha()||this.detectHEXFormat()!==c.ShortHEX?null:s.sprintf("#%s%s%s",n(this.#u[0]),n(this.#u[1]),n(this.#u[2])).toLowerCase();case c.Nickname:return this.nickname()}return this.#h}rgba(){return this.#u.slice()}canonicalRGBA(){const e=new Array(4);for(let t=0;t<3;++t)e[t]=Math.round(255*this.#u[t]);return e[3]=this.#u[3],e}nickname(){return g.get(String(this.canonicalRGBA()))||null}toProtocolRGBA(){const e=this.canonicalRGBA(),t={r:e[0],g:e[1],b:e[2],a:void 0};return 1!==e[3]&&(t.a=e[3]),t}invert(){const e=[];return e[0]=1-this.#u[0],e[1]=1-this.#u[1],e[2]=1-this.#u[2],e[3]=this.#u[3],new a(e,c.RGBA)}setAlpha(e){const t=this.#u.slice();return t[3]=e,new a(t,c.RGBA)}blendWith(e){const t=(0,o.blendColors)(e.#u,this.#u);return new a(t,c.RGBA)}blendWithAlpha(e){const t=this.#u.slice();return t[3]*=e,new a(t,c.RGBA)}setFormat(e){this.#g=e}equal(e){return this.#u.every(((t,r)=>t===e.#u[r]))&&this.#g===e.#g}}const l=/((?:rgb|hsl|hwb)a?\([^)]+\)|#[0-9a-fA-F]{8}|#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3,4}|\b[a-zA-Z]+\b(?!-))/g;var c,u;(u=c||(c={})).Original="original",u.Nickname="nickname",u.HEX="hex",u.ShortHEX="shorthex",u.HEXA="hexa",u.ShortHEXA="shorthexa",u.RGB="rgb",u.RGBA="rgba",u.HSL="hsl",u.HSLA="hsla",u.HWB="hwb",u.HWBA="hwba";const h=[["aliceblue",[240,248,255]],["antiquewhite",[250,235,215]],["aqua",[0,255,255]],["aquamarine",[127,255,212]],["azure",[240,255,255]],["beige",[245,245,220]],["bisque",[255,228,196]],["black",[0,0,0]],["blanchedalmond",[255,235,205]],["blue",[0,0,255]],["blueviolet",[138,43,226]],["brown",[165,42,42]],["burlywood",[222,184,135]],["cadetblue",[95,158,160]],["chartreuse",[127,255,0]],["chocolate",[210,105,30]],["coral",[255,127,80]],["cornflowerblue",[100,149,237]],["cornsilk",[255,248,220]],["crimson",[237,20,61]],["cyan",[0,255,255]],["darkblue",[0,0,139]],["darkcyan",[0,139,139]],["darkgoldenrod",[184,134,11]],["darkgray",[169,169,169]],["darkgrey",[169,169,169]],["darkgreen",[0,100,0]],["darkkhaki",[189,183,107]],["darkmagenta",[139,0,139]],["darkolivegreen",[85,107,47]],["darkorange",[255,140,0]],["darkorchid",[153,50,204]],["darkred",[139,0,0]],["darksalmon",[233,150,122]],["darkseagreen",[143,188,143]],["darkslateblue",[72,61,139]],["darkslategray",[47,79,79]],["darkslategrey",[47,79,79]],["darkturquoise",[0,206,209]],["darkviolet",[148,0,211]],["deeppink",[255,20,147]],["deepskyblue",[0,191,255]],["dimgray",[105,105,105]],["dimgrey",[105,105,105]],["dodgerblue",[30,144,255]],["firebrick",[178,34,34]],["floralwhite",[255,250,240]],["forestgreen",[34,139,34]],["fuchsia",[255,0,255]],["gainsboro",[220,220,220]],["ghostwhite",[248,248,255]],["gold",[255,215,0]],["goldenrod",[218,165,32]],["gray",[128,128,128]],["grey",[128,128,128]],["green",[0,128,0]],["greenyellow",[173,255,47]],["honeydew",[240,255,240]],["hotpink",[255,105,180]],["indianred",[205,92,92]],["indigo",[75,0,130]],["ivory",[255,255,240]],["khaki",[240,230,140]],["lavender",[230,230,250]],["lavenderblush",[255,240,245]],["lawngreen",[124,252,0]],["lemonchiffon",[255,250,205]],["lightblue",[173,216,230]],["lightcoral",[240,128,128]],["lightcyan",[224,255,255]],["lightgoldenrodyellow",[250,250,210]],["lightgreen",[144,238,144]],["lightgray",[211,211,211]],["lightgrey",[211,211,211]],["lightpink",[255,182,193]],["lightsalmon",[255,160,122]],["lightseagreen",[32,178,170]],["lightskyblue",[135,206,250]],["lightslategray",[119,136,153]],["lightslategrey",[119,136,153]],["lightsteelblue",[176,196,222]],["lightyellow",[255,255,224]],["lime",[0,255,0]],["limegreen",[50,205,50]],["linen",[250,240,230]],["magenta",[255,0,255]],["maroon",[128,0,0]],["mediumaquamarine",[102,205,170]],["mediumblue",[0,0,205]],["mediumorchid",[186,85,211]],["mediumpurple",[147,112,219]],["mediumseagreen",[60,179,113]],["mediumslateblue",[123,104,238]],["mediumspringgreen",[0,250,154]],["mediumturquoise",[72,209,204]],["mediumvioletred",[199,21,133]],["midnightblue",[25,25,112]],["mintcream",[245,255,250]],["mistyrose",[255,228,225]],["moccasin",[255,228,181]],["navajowhite",[255,222,173]],["navy",[0,0,128]],["oldlace",[253,245,230]],["olive",[128,128,0]],["olivedrab",[107,142,35]],["orange",[255,165,0]],["orangered",[255,69,0]],["orchid",[218,112,214]],["palegoldenrod",[238,232,170]],["palegreen",[152,251,152]],["paleturquoise",[175,238,238]],["palevioletred",[219,112,147]],["papayawhip",[255,239,213]],["peachpuff",[255,218,185]],["peru",[205,133,63]],["pink",[255,192,203]],["plum",[221,160,221]],["powderblue",[176,224,230]],["purple",[128,0,128]],["rebeccapurple",[102,51,153]],["red",[255,0,0]],["rosybrown",[188,143,143]],["royalblue",[65,105,225]],["saddlebrown",[139,69,19]],["salmon",[250,128,114]],["sandybrown",[244,164,96]],["seagreen",[46,139,87]],["seashell",[255,245,238]],["sienna",[160,82,45]],["silver",[192,192,192]],["skyblue",[135,206,235]],["slateblue",[106,90,205]],["slategray",[112,128,144]],["slategrey",[112,128,144]],["snow",[255,250,250]],["springgreen",[0,255,127]],["steelblue",[70,130,180]],["tan",[210,180,140]],["teal",[0,128,128]],["thistle",[216,191,216]],["tomato",[255,99,71]],["turquoise",[64,224,208]],["violet",[238,130,238]],["wheat",[245,222,179]],["white",[255,255,255]],["whitesmoke",[245,245,245]],["yellow",[255,255,0]],["yellowgreen",[154,205,50]],["transparent",[0,0,0,0]]];i.DCHECK((()=>h.every((([e])=>e.toLowerCase()===e))),"All color nicknames must be lowercase.");const p=new Map(h),g=new Map(h.map((([e,[t,r,s,n=1]])=>[String([t,r,s,n]),e]))),d=[127,32,210],m={Content:a.fromRGBA([111,168,220,.66]),ContentLight:a.fromRGBA([111,168,220,.5]),ContentOutline:a.fromRGBA([9,83,148]),Padding:a.fromRGBA([147,196,125,.55]),PaddingLight:a.fromRGBA([147,196,125,.4]),Border:a.fromRGBA([255,229,153,.66]),BorderLight:a.fromRGBA([255,229,153,.5]),Margin:a.fromRGBA([246,178,107,.66]),MarginLight:a.fromRGBA([246,178,107,.5]),EventTarget:a.fromRGBA([255,196,196,.66]),Shape:a.fromRGBA([96,82,177,.8]),ShapeMargin:a.fromRGBA([96,82,127,.6]),CssGrid:a.fromRGBA([75,0,130,1]),LayoutLine:a.fromRGBA([...d,1]),GridBorder:a.fromRGBA([...d,1]),GapBackground:a.fromRGBA([...d,.3]),GapHatch:a.fromRGBA([...d,.8]),GridAreaBorder:a.fromRGBA([26,115,232,1])},f={ParentOutline:a.fromRGBA([224,90,183,1]),ChildOutline:a.fromRGBA([0,120,212,1])},S={Resizer:a.fromRGBA([222,225,230,1]),ResizerHandle:a.fromRGBA([166,166,166,1]),Mask:a.fromRGBA([248,249,249,1])};class E{#d;#m;#f;#S;#E;constructor(e,t,r,s){this.#d=e||{min:0,max:360,count:void 0},this.#m=t||67,this.#f=r||80,this.#S=s||1,this.#E=new Map}setColorForID(e,t){this.#E.set(e,t)}colorForID(e){let t=this.#E.get(e);return t||(t=this.generateColorForID(e),this.#E.set(e,t)),t}generateColorForID(e){const t=s.hashCode(e),r=this.indexToValueInSpace(t,this.#d),n=this.indexToValueInSpace(t>>8,this.#m),i=this.indexToValueInSpace(t>>16,this.#f),o=this.indexToValueInSpace(t>>24,this.#S),a=`hsl(${r}deg ${n}% ${i}%`;return 1!==o?`${a} / ${Math.floor(100*o)}%)`:`${a})`}indexToValueInSpace(e,t){if("number"==typeof t)return t;const r=t.count||t.max-t.min;return e%=r,t.min+Math.floor(e/(r-1)*(t.max-t.min))}}const b=[0,0,0,0]})),n.register("4JpmP",(function(t,r){function s(e,t){const r=e[3];return[(1-r)*t[0]+r*e[0],(1-r)*t[1]+r*e[1],(1-r)*t[2]+r*e[2],r+t[3]*(1-r)]}function n([e,t,r]){const s=Math.max(e,t,r),n=Math.min(e,t,r),i=s-n;let o;return o=n===s?0:e===s?(1/6*(t-r)/i+1)%1:t===s?1/6*(r-e)/i+1/3:1/6*(e-t)/i+2/3,o}function i([e,t,r,s]){const i=Math.max(e,t,r),o=Math.min(e,t,r),a=i-o,l=i+o,c=.5*l;let u;return u=0===c||1===c?0:c<=.5?a/l:a/(2-l),[n([e,t,r]),u,c,s]}function o([e,t,r,s]){const i=n([e,t,r]),o=Math.max(e,t,r);return[i,Math.min(e,t,r),1-o,s]}function a([e,t,r]){return.2126*(e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4))+.7152*(t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4))+.0722*(r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4))}function l(e,t){const r=a(s(e,t)),n=a(t);return(Math.max(r,n)+.05)/(Math.min(r,n)+.05)}e(t.exports,"blendColors",(()=>s)),e(t.exports,"rgbaToHsla",(()=>i)),e(t.exports,"rgbaToHwba",(()=>o)),e(t.exports,"luminance",(()=>a)),e(t.exports,"contrastRatio",(()=>l)),e(t.exports,"luminanceAPCA",(()=>h)),e(t.exports,"contrastRatioAPCA",(()=>p)),e(t.exports,"contrastRatioByLuminanceAPCA",(()=>d)),e(t.exports,"desiredLuminanceAPCA",(()=>m)),e(t.exports,"getAPCAThreshold",(()=>S)),e(t.exports,"isLargeFont",(()=>E)),e(t.exports,"getContrastThreshold",(()=>x));const c=12.82051282051282,u=.06;function h([e,t,r]){return.2126729*Math.pow(e,2.4)+.7151522*Math.pow(t,2.4)+.072175*Math.pow(r,2.4)}function p(e,t){return d(h(s(e,t)),h(t))}function g(e){return e>.03?e:e+Math.pow(.03-e,1.45)}function d(e,t){if(e=g(e),t=g(t),Math.abs(e-t)<5e-4)return 0;let r=0;return t>=e?(r=1.25*(Math.pow(t,.55)-Math.pow(e,.58)),r=r<.001?0:r<.078?r-r*c*u:r-u):(r=1.25*(Math.pow(t,.62)-Math.pow(e,.57)),r=r>-.001?0:r>-.078?r-r*c*u:r+u),100*r}function m(e,t,r){function s(){return r?Math.pow(Math.abs(Math.pow(e,.62)-(-t-u)/1.25),1/.57):Math.pow(Math.abs(Math.pow(e,.55)-(t+u)/1.25),1/.58)}e=g(e),t/=100;let n=s();return(n<0||n>1)&&(r=!r,n=s()),n}const f=[[12,-1,-1,-1,-1,100,90,80,-1,-1],[14,-1,-1,-1,100,90,80,60,60,-1],[16,-1,-1,100,90,80,60,55,50,50],[18,-1,-1,90,80,60,55,50,40,40],[24,-1,100,80,60,55,50,40,38,35],[30,-1,90,70,55,50,40,38,35,40],[36,-1,80,60,50,40,38,35,30,25],[48,100,70,55,40,38,35,30,25,20],[60,90,60,50,38,35,30,25,20,20],[72,80,55,40,35,30,25,20,20,20],[96,70,50,35,30,25,20,20,20,20],[120,60,40,30,25,20,20,20,20,20]];function S(e,t){const r=parseFloat(e.replace("px","")),s=parseFloat(t);for(const[e,...t]of f)if(r>=e)for(const[e,r]of[900,800,700,600,500,400,300,200,100].entries())if(s>=r){const r=t[t.length-1-e];return-1===r?null:r}return null}function E(e,t){const r=72*parseFloat(e.replace("px",""))/96;return-1!==["bold","bolder","600","700","800","900"].indexOf(t)?r>=14:r>=18}f.reverse();const b={aa:3,aaa:4.5},y={aa:4.5,aaa:7};function x(e,t){return E(e,t)?b:y}})),n.register("7U4D1",(function(t,r){e(t.exports,"Console",(()=>a)),e(t.exports,"Message",(()=>h)),e(t.exports,"MessageLevel",(()=>c)),e(t.exports,"Events",(()=>l));var s=n("5gQQ4"),i=n("eP1Mk");let o;class a extends s.ObjectWrapper{#b;constructor(){super(),this.#b=[]}static instance({forceNew:e}={forceNew:!1}){return o&&!e||(o=new a),o}addMessage(e,t,r){const s=new h(e,t||c.Info,Date.now(),r||!1);this.#b.push(s),this.dispatchEventToListeners(l.MessageAdded,s)}log(e){this.addMessage(e,c.Info)}warn(e){this.addMessage(e,c.Warning)}error(e){this.addMessage(e,c.Error,!0)}messages(){return this.#b}show(){this.showPromise()}showPromise(){return(0,i.reveal)(this)}}var l,c,u;(l||(l={})).MessageAdded="messageAdded",(u=c||(c={})).Info="info",u.Warning="warning",u.Error="error";class h{text;level;timestamp;show;constructor(e,t,r,s){this.text=e,this.level=t,this.timestamp="number"==typeof r?r:Date.now(),this.show=s}}})),n.register("5gQQ4",(function(t,r){e(t.exports,"ObjectWrapper",(()=>s)),e(t.exports,"eventMixin",(()=>n));class s{listeners;addEventListener(e,t,r){this.listeners||(this.listeners=new Map);let s=this.listeners.get(e);return s||(s=new Set,this.listeners.set(e,s)),s.add({thisObject:r,listener:t}),{eventTarget:this,eventType:e,thisObject:r,listener:t}}once(e){return new Promise((t=>{const r=this.addEventListener(e,(s=>{this.removeEventListener(e,r.listener),t(s.data)}))}))}removeEventListener(e,t,r){const s=this.listeners?.get(e);if(s){for(const e of s)e.listener===t&&e.thisObject===r&&(e.disposed=!0,s.delete(e));s.size||this.listeners?.delete(e)}}hasEventListeners(e){return Boolean(this.listeners&&this.listeners.has(e))}dispatchEventToListeners(e,...[t]){const r=this.listeners?.get(e);if(!r)return;const s={data:t};for(const e of[...r])e.disposed||e.listener.call(e.thisObject,s)}}function n(e){return class extends e{#y=new s;addEventListener(e,t,r){return this.#y.addEventListener(e,t,r)}once(e){return this.#y.once(e)}removeEventListener(e,t,r){this.#y.removeEventListener(e,t,r)}hasEventListeners(e){return this.#y.hasEventListeners(e)}dispatchEventToListeners(e,...t){this.#y.dispatchEventToListeners(e,...t)}}}})),n.register("eP1Mk",(function(t,r){e(t.exports,"Revealer",(()=>l)),e(t.exports,"reveal",(()=>c)),e(t.exports,"setRevealForTest",(()=>u)),e(t.exports,"revealDestination",(()=>h)),e(t.exports,"registerRevealer",(()=>g)),e(t.exports,"RevealerDestination",(()=>m)),n("ixFnt");var s=n("ieEAB");const i={elementsPanel:"Elements panel",stylesSidebar:"styles sidebar",changesDrawer:"Changes drawer",issuesView:"Issues view",networkPanel:"Network panel",applicationPanel:"Application panel",sourcesPanel:"Sources panel"},o=s.registerUIStrings("core/common/Revealer.ts",i),a=s.getLazilyComputedLocalizedString.bind(void 0,o);class l{}let c=async function(e,t){if(!e)return Promise.reject(new Error("Can't reveal "+e));return function(r){const s=[];for(let n=0;n<r.length;++n)s.push(r[n].reveal(e,t));return Promise.race(s)}(await Promise.all(d(e).map((e=>e.loadRevealer()))))};function u(e){c=e}const h=function(e){const t=e?d(e)[0]:p[0];return t&&t.destination?.()||null},p=[];function g(e){p.push(e)}function d(e){return p.filter((function(t){if(!t.contextTypes)return!0;for(const r of t.contextTypes())if(e instanceof r)return!0;return!1}))}const m={ELEMENTS_PANEL:a(i.elementsPanel),STYLES_SIDEBAR:a(i.stylesSidebar),CHANGES_DRAWER:a(i.changesDrawer),ISSUES_VIEW:a(i.issuesView),NETWORK_PANEL:a(i.networkPanel),APPLICATION_PANEL:a(i.applicationPanel),SOURCES_PANEL:a(i.sourcesPanel)}})),n.register("ixFnt",(function(t,r){e(t.exports,"DevToolsLocale",(()=>n("lE1PY"))),e(t.exports,"i18n",(()=>n("ieEAB"))),e(t.exports,"TimeUtilities",(()=>n("kCQSR")));n("lE1PY"),n("ieEAB"),n("kCQSR")})),n.register("lE1PY",(function(t,r){e(t.exports,"DevToolsLocale",(()=>n)),e(t.exports,"localeLanguagesMatch",(()=>i));let s=null;class n{locale;lookupClosestDevToolsLocale;constructor(e){this.lookupClosestDevToolsLocale=e.lookupClosestDevToolsLocale,"browserLanguage"===e.settingLanguage?this.locale=e.navigatorLanguage||"en-US":this.locale=e.settingLanguage,this.locale=this.lookupClosestDevToolsLocale(this.locale)}static instance(e={create:!1}){if(!s&&!e.create)throw new Error("No LanguageSelector instance exists yet.");return e.create&&(s=new n(e.data)),s}forceFallbackLocale(){this.locale="en-US"}languageIsSupportedByDevTools(e){return i(e,this.lookupClosestDevToolsLocale(e))}}function i(e,t){const r=new Intl.Locale(e),s=new Intl.Locale(t);return r.language===s.language}})),n.register("ieEAB",(function(t,r){e(t.exports,"lookupClosestSupportedDevToolsLocale",(()=>l)),e(t.exports,"getAllSupportedDevToolsLocales",(()=>c)),e(t.exports,"fetchAndRegisterLocaleData",(()=>u)),e(t.exports,"getLazilyComputedLocalizedString",(()=>h)),e(t.exports,"getLocalizedString",(()=>p)),e(t.exports,"registerUIStrings",(()=>g)),e(t.exports,"getFormatLocalizedString",(()=>d)),e(t.exports,"serializeUIString",(()=>m)),e(t.exports,"deserializeUIString",(()=>f)),e(t.exports,"lockedString",(()=>S)),e(t.exports,"lockedLazyString",(()=>E)),e(t.exports,"getLocalizedLanguageRegion",(()=>b)),n("dcrwO");var s=n("gDwiM");n("lz7WY");var i=n("5jytL");n("9X2mn");n("elz4U");var o=n("lE1PY");const a=new s.I18n;new Set(["en-US","en-XL","zh"]);function l(e){return a.lookupClosestSupportedLocale(e)}function c(){return[...a.supportedLocales]}async function u(e){const t=n("1kwzI"),r=new Promise(((e,t)=>window.setTimeout((()=>t(new Error("timed out fetching locale"))),5e3))),s=await Promise.race([r,t]);a.registerLocaleData(e,s)}function h(e,t,r={}){return()=>p(e,t,r)}function p(e,t,r={}){return e.getLocalizedStringSetFor(o.DevToolsLocale.instance().locale).getLocalizedString(t,r)}function g(e,t){return a.registerFileStrings(e,t)}function d(e,t,r){const s=e.getLocalizedStringSetFor(o.DevToolsLocale.instance().locale).getMessageFormatterFor(t),n=document.createElement("span");for(const e of s.getAst())if(1===e.type){const t=r[e.value];t&&n.append(t)}else"value"in e&&n.append(String(e.value));return n}function m(e,t={}){const r={string:e,values:t};return JSON.stringify(r)}function f(e){return e?JSON.parse(e):{string:"",values:{}}}function S(e){return e}function E(e){return()=>e}function b(e,t){const r=new Intl.Locale(e);i.DCHECK((()=>void 0!==r.language)),i.DCHECK((()=>void 0!==r.baseName));const s=r.language||"en",n=r.baseName||"en-US",o=s===new Intl.Locale(t.locale).language?"en":n,a=new Intl.DisplayNames([t.locale],{type:"language"}).of(s),l=new Intl.DisplayNames([o],{type:"language"}).of(s);let c="",u="";if(r.region){c=` (${new Intl.DisplayNames([t.locale],{type:"region",style:"short"}).of(r.region)})`,u=` (${new Intl.DisplayNames([o],{type:"region",style:"short"}).of(r.region)})`}return`${a}${c} - ${l}${u}`}})),n.register("dcrwO",(function(t,r){e(t.exports,"I18n",(()=>n("gDwiM")));n("gDwiM"),n("7wJeq")})),n.register("gDwiM",(function(t,r){e(t.exports,"I18n",(()=>o));var s=n("lMCTf"),i=n("7wJeq");class o{defaultLocale;supportedLocales;localeData=new Map;constructor(e=s.LOCALES,t=s.DEFAULT_LOCALE){this.defaultLocale=t,this.supportedLocales=new Set(e)}registerLocaleData(e,t){this.localeData.set(e,t)}registerFileStrings(e,t){return new(0,i.RegisteredFileStrings)(e,t,this.localeData)}lookupClosestSupportedLocale(e){const t=Intl.getCanonicalLocales(e)[0].split("-");for(;t.length;){const e=t.join("-");if(this.supportedLocales.has(e))return e;t.pop()}return this.defaultLocale}}})),n.register("lMCTf",(function(t,r){e(t.exports,"LOCALES",(()=>s)),e(t.exports,"DEFAULT_LOCALE",(()=>n));const s=["af","am","ar","as","az","be","bg","bn","bs","ca","cs","cy","da","de","el","en-GB","en-US","en-XL","es-419","es","et","eu","fa","fi","fil","fr-CA","fr","gl","gu","he","hi","hr","hu","hy","id","is","it","ja","ka","kk","km","kn","ko","ky","lo","lt","lv","mk","ml","mn","mr","ms","my","ne","nl","no","or","pa","pl","pt-PT","pt","ro","ru","si","sk","sl","sq","sr-Latn","sr","sv","sw","ta","te","th","tr","uk","ur","uz","vi","zh","zh-HK","zh-TW","zu"],n="en-US"})),n.register("7wJeq",(function(t,r){e(t.exports,"RegisteredFileStrings",(()=>o)),n("bysJD");var s=n("9Pede");const i={};class o{filename;stringStructure;localizedMessages;localizedStringSet;constructor(e,t,r){this.filename=e,this.stringStructure=t,this.localizedMessages=r}getLocalizedStringSetFor(e){if(this.localizedStringSet)return this.localizedStringSet;const t=this.localizedMessages.get(e);if(!t)throw new Error(`No locale data registered for '${e}'`);return this.localizedStringSet=new a(this.filename,this.stringStructure,e,t),this.localizedStringSet}}class a{filename;stringStructure;localizedMessages;cachedSimpleStrings=new Map;cachedMessageFormatters=new Map;localeForFormatter;constructor(e,t,r,s){this.filename=e,this.stringStructure=t,this.localizedMessages=s,this.localeForFormatter="en-XA"===r||"en-XL"===r?"de-DE":r}getLocalizedString(e,t=i){return t===i||0===Object.keys(t).length?this.getSimpleLocalizedString(e):this.getFormattedLocalizedString(e,t)}getMessageFormatterFor(e){const t=Object.keys(this.stringStructure).find((t=>this.stringStructure[t]===e));if(!t)throw new Error(`Unable to locate '${e}' in UIStrings object`);const r=`${this.filename} | ${t}`,n=this.localizedMessages[r],i=n?n.message:e;return new s.default(i,this.localeForFormatter,void 0,{ignoreTag:!0})}getSimpleLocalizedString(e){const t=this.cachedSimpleStrings.get(e);if(t)return t;const r=this.getMessageFormatterFor(e).format();return this.cachedSimpleStrings.set(e,r),r}getFormattedLocalizedString(e,t){let r=this.cachedMessageFormatters.get(e);return r||(r=this.getMessageFormatterFor(e),this.cachedMessageFormatters.set(e,r)),r.format(t)}}})),n.register("bysJD",(function(t,r){e(t.exports,"IntlMessageFormat",(()=>n("9Pede").default));n("9Pede")})),n.register("9Pede",(function(t,r){e(t.exports,"default",(()=>Re));var s,n,i,o,a,l,c=function(){return c=Object.assign||function(e){for(var t,r=1,s=arguments.length;r<s;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},c.apply(this,arguments)};function u(e){return e.type===i.literal}function h(e){return e.type===i.argument}function p(e){return e.type===i.number}function g(e){return e.type===i.date}function d(e){return e.type===i.time}function m(e){return e.type===i.select}function f(e){return e.type===i.plural}function S(e){return e.type===i.pound}function E(e){return e.type===i.tag}function b(e){return!(!e||"object"!=typeof e||e.type!==a.number)}function y(e){return!(!e||"object"!=typeof e||e.type!==a.dateTime)}(n=s||(s={}))[n.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",n[n.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",n[n.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",n[n.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",n[n.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",n[n.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",n[n.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",n[n.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",n[n.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",n[n.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",n[n.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",n[n.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",n[n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",n[n.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",n[n.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",n[n.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",n[n.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",n[n.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",n[n.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",n[n.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",n[n.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",n[n.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",n[n.INVALID_TAG=23]="INVALID_TAG",n[n.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",n[n.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",n[n.UNCLOSED_TAG=27]="UNCLOSED_TAG",(o=i||(i={}))[o.literal=0]="literal",o[o.argument=1]="argument",o[o.number=2]="number",o[o.date=3]="date",o[o.time=4]="time",o[o.select=5]="select",o[o.plural=6]="plural",o[o.pound=7]="pound",o[o.tag=8]="tag",(l=a||(a={}))[l.number=0]="number",l[l.dateTime=1]="dateTime";var x=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,w=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;function v(e){var t={};return e.replace(w,(function(e){var r=e.length;switch(e[0]){case"G":t.era=4===r?"long":5===r?"narrow":"short";break;case"y":t.year=2===r?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw new RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw new RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":t.month=["numeric","2-digit","short","long","narrow"][r-1];break;case"w":case"W":throw new RangeError("`w/W` (week) patterns are not supported");case"d":t.day=["numeric","2-digit"][r-1];break;case"D":case"F":case"g":throw new RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":t.weekday=4===r?"short":5===r?"narrow":"short";break;case"e":if(r<4)throw new RangeError("`e..eee` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][r-4];break;case"c":if(r<4)throw new RangeError("`c..ccc` (weekday) patterns are not supported");t.weekday=["short","long","narrow","short"][r-4];break;case"a":t.hour12=!0;break;case"b":case"B":throw new RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":t.hourCycle="h12",t.hour=["numeric","2-digit"][r-1];break;case"H":t.hourCycle="h23",t.hour=["numeric","2-digit"][r-1];break;case"K":t.hourCycle="h11",t.hour=["numeric","2-digit"][r-1];break;case"k":t.hourCycle="h24",t.hour=["numeric","2-digit"][r-1];break;case"j":case"J":case"C":throw new RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":t.minute=["numeric","2-digit"][r-1];break;case"s":t.second=["numeric","2-digit"][r-1];break;case"S":case"A":throw new RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":t.timeZoneName=r<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw new RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""})),t}var T=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i;var A,I=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,L=/^(@+)?(\+|#+)?$/g,R=/(\*)(0+)|(#+)(0+)|(0+)/g,P=/^(0+)$/;function C(e){var t={};return e.replace(L,(function(e,r,s){return"string"!=typeof s?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):"+"===s?t.minimumSignificantDigits=r.length:"#"===r[0]?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+("string"==typeof s?s.length:0)),""})),t}function N(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function O(e){var t;if("E"===e[0]&&"E"===e[1]?(t={notation:"engineering"},e=e.slice(2)):"E"===e[0]&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if("+!"===r?(t.signDisplay="always",e=e.slice(2)):"+?"===r&&(t.signDisplay="exceptZero",e=e.slice(2)),!P.test(e))throw new Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}function M(e){var t=N(e);return t||{}}function k(e){for(var t={},r=0,s=e;r<s.length;r++){var n=s[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=n.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=c(c(c({},t),{notation:"scientific"}),n.options.reduce((function(e,t){return c(c({},e),M(t))}),{}));continue;case"engineering":t=c(c(c({},t),{notation:"engineering"}),n.options.reduce((function(e,t){return c(c({},e),M(t))}),{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"integer-width":if(n.options.length>1)throw new RangeError("integer-width stems only accept a single optional option");n.options[0].replace(R,(function(e,r,s,n,i,o){if(r)t.minimumIntegerDigits=s.length;else{if(n&&i)throw new Error("We currently do not support maximum integer digits");if(o)throw new Error("We currently do not support exact integer digits")}return""}));continue}if(P.test(n.stem))t.minimumIntegerDigits=n.stem.length;else if(I.test(n.stem)){if(n.options.length>1)throw new RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(I,(function(e,r,s,n,i,o){return"*"===s?t.minimumFractionDigits=r.length:n&&"#"===n[0]?t.maximumFractionDigits=n.length:i&&o?(t.minimumFractionDigits=i.length,t.maximumFractionDigits=i.length+o.length):(t.minimumFractionDigits=r.length,t.maximumFractionDigits=r.length),""})),n.options.length&&(t=c(c({},t),C(n.options[0])))}else if(L.test(n.stem))t=c(c({},t),C(n.stem));else{var i=N(n.stem);i&&(t=c(c({},t),i));var o=O(n.stem);o&&(t=c(c({},t),o))}}return t}var _=new RegExp("^"+x.source+"*"),F=new RegExp(x.source+"*$");function U(e,t){return{start:e,end:t}}var D=!!String.prototype.startsWith,V=!!String.fromCodePoint,G=!!Object.fromEntries,W=!!String.prototype.codePointAt,B=!!String.prototype.trimStart,z=!!String.prototype.trimEnd,H=!!Number.isSafeInteger?Number.isSafeInteger:function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&Math.abs(e)<=9007199254740991},j=!0;try{j="a"===(null===(A=ee("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu").exec("a"))||void 0===A?void 0:A[0])}catch(e){j=!1}var X,$,Y=D?function(e,t,r){return e.startsWith(t,r)}:function(e,t,r){return e.slice(r,r+t.length)===t},q=V?String.fromCodePoint:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var r,s="",n=e.length,i=0;n>i;){if((r=e[i++])>1114111)throw RangeError(r+" is not a valid code point");s+=r<65536?String.fromCharCode(r):String.fromCharCode(55296+((r-=65536)>>10),r%1024+56320)}return s},K=G?Object.fromEntries:function(e){for(var t={},r=0,s=e;r<s.length;r++){var n=s[r],i=n[0],o=n[1];t[i]=o}return t},J=W?function(e,t){return e.codePointAt(t)}:function(e,t){var r=e.length;if(!(t<0||t>=r)){var s,n=e.charCodeAt(t);return n<55296||n>56319||t+1===r||(s=e.charCodeAt(t+1))<56320||s>57343?n:s-56320+(n-55296<<10)+65536}},Q=B?function(e){return e.trimStart()}:function(e){return e.replace(_,"")},Z=z?function(e){return e.trimEnd()}:function(e){return e.replace(F,"")};function ee(e,t){return new RegExp(e,t)}j?($=ee("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu"),X=function(e,t){var r;return $.lastIndex=t,null!==(r=$.exec(e)[1])&&void 0!==r?r:""}):X=function(e,t){for(var r=[];;){var s=J(e,t);if(void 0===s||se(s)||ne(s))break;r.push(s),t+=s>=65536?2:1}return q.apply(void 0,r)};var te=function(){function e(e,t){void 0===t&&(t={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(e,t,r){for(var n=[];!this.isEOF();){var o=this.char();if(123===o){if((a=this.parseArgument(e,r)).err)return a;n.push(a.val)}else{if(125===o&&e>0)break;if(35!==o||"plural"!==t&&"selectordinal"!==t){if(60===o&&!this.ignoreTag&&47===this.peek()){if(r)break;return this.error(s.UNMATCHED_CLOSING_TAG,U(this.clonePosition(),this.clonePosition()))}if(60===o&&!this.ignoreTag&&re(this.peek()||0)){if((a=this.parseTag(e,t)).err)return a;n.push(a.val)}else{var a;if((a=this.parseLiteral(e,t)).err)return a;n.push(a.val)}}else{var l=this.clonePosition();this.bump(),n.push({type:i.pound,location:U(l,this.clonePosition())})}}}return{val:n,err:null}},e.prototype.parseTag=function(e,t){var r=this.clonePosition();this.bump();var n=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:i.literal,value:"<"+n+"/>",location:U(r,this.clonePosition())},err:null};if(this.bumpIf(">")){var o=this.parseMessage(e+1,t,!0);if(o.err)return o;var a=o.val,l=this.clonePosition();if(this.bumpIf("</")){if(this.isEOF()||!re(this.char()))return this.error(s.INVALID_TAG,U(l,this.clonePosition()));var c=this.clonePosition();return n!==this.parseTagName()?this.error(s.UNMATCHED_CLOSING_TAG,U(c,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">")?{val:{type:i.tag,value:n,children:a,location:U(r,this.clonePosition())},err:null}:this.error(s.INVALID_TAG,U(l,this.clonePosition())))}return this.error(s.UNCLOSED_TAG,U(r,this.clonePosition()))}return this.error(s.INVALID_TAG,U(r,this.clonePosition()))},e.prototype.parseTagName=function(){var e,t=this.offset();for(this.bump();!this.isEOF()&&(45===(e=this.char())||46===e||e>=48&&e<=57||95===e||e>=97&&e<=122||e>=65&&e<=90||183==e||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039);)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(e,t){for(var r=this.clonePosition(),s="";;){var n=this.tryParseQuote(t);if(n)s+=n;else{var o=this.tryParseUnquoted(e,t);if(o)s+=o;else{var a=this.tryParseLeftAngleBracket();if(!a)break;s+=a}}}var l=U(r,this.clonePosition());return{val:{type:i.literal,value:s,location:l},err:null}},e.prototype.tryParseLeftAngleBracket=function(){return this.isEOF()||60!==this.char()||!this.ignoreTag&&(re(e=this.peek()||0)||47===e)?null:(this.bump(),"<");var e},e.prototype.tryParseQuote=function(e){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===e||"selectordinal"===e)break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(39===r){if(39!==this.peek()){this.bump();break}t.push(39),this.bump()}else t.push(r);this.bump()}return q.apply(void 0,t)},e.prototype.tryParseUnquoted=function(e,t){if(this.isEOF())return null;var r=this.char();return 60===r||123===r||35===r&&("plural"===t||"selectordinal"===t)||125===r&&e>0?null:(this.bump(),q(r))},e.prototype.parseArgument=function(e,t){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(s.EXPECT_ARGUMENT_CLOSING_BRACE,U(r,this.clonePosition()));if(125===this.char())return this.bump(),this.error(s.EMPTY_ARGUMENT,U(r,this.clonePosition()));var n=this.parseIdentifierIfPossible().value;if(!n)return this.error(s.MALFORMED_ARGUMENT,U(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(s.EXPECT_ARGUMENT_CLOSING_BRACE,U(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:i.argument,value:n,location:U(r,this.clonePosition())},err:null};case 44:return this.bump(),this.bumpSpace(),this.isEOF()?this.error(s.EXPECT_ARGUMENT_CLOSING_BRACE,U(r,this.clonePosition())):this.parseArgumentOptions(e,t,n,r);default:return this.error(s.MALFORMED_ARGUMENT,U(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),t=this.offset(),r=X(this.message,t),s=t+r.length;return this.bumpTo(s),{value:r,location:U(e,this.clonePosition())}},e.prototype.parseArgumentOptions=function(e,t,r,n){var o,l=this.clonePosition(),u=this.parseIdentifierIfPossible().value,h=this.clonePosition();switch(u){case"":return this.error(s.EXPECT_ARGUMENT_TYPE,U(l,h));case"number":case"date":case"time":this.bumpSpace();var p=null;if(this.bumpIf(",")){this.bumpSpace();var g=this.clonePosition();if((y=this.parseSimpleArgStyleIfPossible()).err)return y;if(0===(f=Z(y.val)).length)return this.error(s.EXPECT_ARGUMENT_STYLE,U(this.clonePosition(),this.clonePosition()));p={style:f,styleLocation:U(g,this.clonePosition())}}if((x=this.tryParseArgumentClose(n)).err)return x;var d=U(n,this.clonePosition());if(p&&Y(null==p?void 0:p.style,"::",0)){var m=Q(p.style.slice(2));if("number"===u)return(y=this.parseNumberSkeletonFromString(m,p.styleLocation)).err?y:{val:{type:i.number,value:r,location:d,style:y.val},err:null};if(0===m.length)return this.error(s.EXPECT_DATE_TIME_SKELETON,d);var f={type:a.dateTime,pattern:m,location:p.styleLocation,parsedOptions:this.shouldParseSkeletons?v(m):{}};return{val:{type:"date"===u?i.date:i.time,value:r,location:d,style:f},err:null}}return{val:{type:"number"===u?i.number:"date"===u?i.date:i.time,value:r,location:d,style:null!==(o=null==p?void 0:p.style)&&void 0!==o?o:null},err:null};case"plural":case"selectordinal":case"select":var S=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(s.EXPECT_SELECT_ARGUMENT_OPTIONS,U(S,c({},S)));this.bumpSpace();var E=this.parseIdentifierIfPossible(),b=0;if("select"!==u&&"offset"===E.value){if(!this.bumpIf(":"))return this.error(s.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,U(this.clonePosition(),this.clonePosition()));var y;if(this.bumpSpace(),(y=this.tryParseDecimalInteger(s.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,s.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE)).err)return y;this.bumpSpace(),E=this.parseIdentifierIfPossible(),b=y.val}var x,w=this.tryParsePluralOrSelectOptions(e,u,t,E);if(w.err)return w;if((x=this.tryParseArgumentClose(n)).err)return x;var T=U(n,this.clonePosition());return"select"===u?{val:{type:i.select,value:r,options:K(w.val),location:T},err:null}:{val:{type:i.plural,value:r,options:K(w.val),offset:b,pluralType:"plural"===u?"cardinal":"ordinal",location:T},err:null};default:return this.error(s.INVALID_ARGUMENT_TYPE,U(l,h))}},e.prototype.tryParseArgumentClose=function(e){return this.isEOF()||125!==this.char()?this.error(s.EXPECT_ARGUMENT_CLOSING_BRACE,U(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,t=this.clonePosition();!this.isEOF();){switch(this.char()){case 39:this.bump();var r=this.clonePosition();if(!this.bumpUntil("'"))return this.error(s.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,U(r,this.clonePosition()));this.bump();break;case 123:e+=1,this.bump();break;case 125:if(!(e>0))return{val:this.message.slice(t.offset,this.offset()),err:null};e-=1;break;default:this.bump()}}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(e,t){var r=[];try{r=function(e){if(0===e.length)throw new Error("Number skeleton cannot be empty");for(var t=[],r=0,s=e.split(T).filter((function(e){return e.length>0}));r<s.length;r++){var n=s[r].split("/");if(0===n.length)throw new Error("Invalid number skeleton");for(var i=n[0],o=n.slice(1),a=0,l=o;a<l.length;a++)if(0===l[a].length)throw new Error("Invalid number skeleton");t.push({stem:i,options:o})}return t}(e)}catch(e){return this.error(s.INVALID_NUMBER_SKELETON,t)}return{val:{type:a.number,tokens:r,location:t,parsedOptions:this.shouldParseSkeletons?k(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(e,t,r,n){for(var i,o=!1,a=[],l=new Set,c=n.value,u=n.location;;){if(0===c.length){var h=this.clonePosition();if("select"===t||!this.bumpIf("="))break;var p=this.tryParseDecimalInteger(s.EXPECT_PLURAL_ARGUMENT_SELECTOR,s.INVALID_PLURAL_ARGUMENT_SELECTOR);if(p.err)return p;u=U(h,this.clonePosition()),c=this.message.slice(h.offset,this.offset())}if(l.has(c))return this.error("select"===t?s.DUPLICATE_SELECT_ARGUMENT_SELECTOR:s.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,u);"other"===c&&(o=!0),this.bumpSpace();var g=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===t?s.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:s.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,U(this.clonePosition(),this.clonePosition()));var d=this.parseMessage(e+1,t,r);if(d.err)return d;var m=this.tryParseArgumentClose(g);if(m.err)return m;a.push([c,{value:d.val,location:U(g,this.clonePosition())}]),l.add(c),this.bumpSpace(),c=(i=this.parseIdentifierIfPossible()).value,u=i.location}return 0===a.length?this.error("select"===t?s.EXPECT_SELECT_ARGUMENT_SELECTOR:s.EXPECT_PLURAL_ARGUMENT_SELECTOR,U(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!o?this.error(s.MISSING_OTHER_CLAUSE,U(this.clonePosition(),this.clonePosition())):{val:a,err:null}},e.prototype.tryParseDecimalInteger=function(e,t){var r=1,s=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var n=!1,i=0;!this.isEOF();){var o=this.char();if(!(o>=48&&o<=57))break;n=!0,i=10*i+(o-48),this.bump()}var a=U(s,this.clonePosition());return n?H(i*=r)?{val:i,err:null}:this.error(t,a):this.error(e,a)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var t=J(this.message,e);if(void 0===t)throw Error("Offset "+e+" is at invalid UTF-16 code unit boundary");return t},e.prototype.error=function(e,t){return{val:null,err:{kind:e,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var e=this.char();10===e?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},e.prototype.bumpIf=function(e){if(Y(this.message,e,this.offset())){for(var t=0;t<e.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(e){var t=this.offset(),r=this.message.indexOf(e,t);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset "+e+" must be greater than or equal to the current offset "+this.offset());for(e=Math.min(e,this.message.length);;){var t=this.offset();if(t===e)break;if(t>e)throw Error("targetOffset "+e+" is at invalid UTF-16 code unit boundary");if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&se(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),t=this.offset(),r=this.message.charCodeAt(t+(e>=65536?2:1));return null!=r?r:null},e}();function re(e){return e>=97&&e<=122||e>=65&&e<=90}function se(e){return e>=9&&e<=13||32===e||133===e||e>=8206&&e<=8207||8232===e||8233===e}function ne(e){return e>=33&&e<=35||36===e||e>=37&&e<=39||40===e||41===e||42===e||43===e||44===e||45===e||e>=46&&e<=47||e>=58&&e<=59||e>=60&&e<=62||e>=63&&e<=64||91===e||92===e||93===e||94===e||96===e||123===e||124===e||125===e||126===e||161===e||e>=162&&e<=165||166===e||167===e||169===e||171===e||172===e||174===e||176===e||177===e||182===e||187===e||191===e||215===e||247===e||e>=8208&&e<=8213||e>=8214&&e<=8215||8216===e||8217===e||8218===e||e>=8219&&e<=8220||8221===e||8222===e||8223===e||e>=8224&&e<=8231||e>=8240&&e<=8248||8249===e||8250===e||e>=8251&&e<=8254||e>=8257&&e<=8259||8260===e||8261===e||8262===e||e>=8263&&e<=8273||8274===e||8275===e||e>=8277&&e<=8286||e>=8592&&e<=8596||e>=8597&&e<=8601||e>=8602&&e<=8603||e>=8604&&e<=8607||8608===e||e>=8609&&e<=8610||8611===e||e>=8612&&e<=8613||8614===e||e>=8615&&e<=8621||8622===e||e>=8623&&e<=8653||e>=8654&&e<=8655||e>=8656&&e<=8657||8658===e||8659===e||8660===e||e>=8661&&e<=8691||e>=8692&&e<=8959||e>=8960&&e<=8967||8968===e||8969===e||8970===e||8971===e||e>=8972&&e<=8991||e>=8992&&e<=8993||e>=8994&&e<=9e3||9001===e||9002===e||e>=9003&&e<=9083||9084===e||e>=9085&&e<=9114||e>=9115&&e<=9139||e>=9140&&e<=9179||e>=9180&&e<=9185||e>=9186&&e<=9254||e>=9255&&e<=9279||e>=9280&&e<=9290||e>=9291&&e<=9311||e>=9472&&e<=9654||9655===e||e>=9656&&e<=9664||9665===e||e>=9666&&e<=9719||e>=9720&&e<=9727||e>=9728&&e<=9838||9839===e||e>=9840&&e<=10087||10088===e||10089===e||10090===e||10091===e||10092===e||10093===e||10094===e||10095===e||10096===e||10097===e||10098===e||10099===e||10100===e||10101===e||e>=10132&&e<=10175||e>=10176&&e<=10180||10181===e||10182===e||e>=10183&&e<=10213||10214===e||10215===e||10216===e||10217===e||10218===e||10219===e||10220===e||10221===e||10222===e||10223===e||e>=10224&&e<=10239||e>=10240&&e<=10495||e>=10496&&e<=10626||10627===e||10628===e||10629===e||10630===e||10631===e||10632===e||10633===e||10634===e||10635===e||10636===e||10637===e||10638===e||10639===e||10640===e||10641===e||10642===e||10643===e||10644===e||10645===e||10646===e||10647===e||10648===e||e>=10649&&e<=10711||10712===e||10713===e||10714===e||10715===e||e>=10716&&e<=10747||10748===e||10749===e||e>=10750&&e<=11007||e>=11008&&e<=11055||e>=11056&&e<=11076||e>=11077&&e<=11078||e>=11079&&e<=11084||e>=11085&&e<=11123||e>=11124&&e<=11125||e>=11126&&e<=11157||11158===e||e>=11159&&e<=11263||e>=11776&&e<=11777||11778===e||11779===e||11780===e||11781===e||e>=11782&&e<=11784||11785===e||11786===e||11787===e||11788===e||11789===e||e>=11790&&e<=11798||11799===e||e>=11800&&e<=11801||11802===e||11803===e||11804===e||11805===e||e>=11806&&e<=11807||11808===e||11809===e||11810===e||11811===e||11812===e||11813===e||11814===e||11815===e||11816===e||11817===e||e>=11818&&e<=11822||11823===e||e>=11824&&e<=11833||e>=11834&&e<=11835||e>=11836&&e<=11839||11840===e||11841===e||11842===e||e>=11843&&e<=11855||e>=11856&&e<=11857||11858===e||e>=11859&&e<=11903||e>=12289&&e<=12291||12296===e||12297===e||12298===e||12299===e||12300===e||12301===e||12302===e||12303===e||12304===e||12305===e||e>=12306&&e<=12307||12308===e||12309===e||12310===e||12311===e||12312===e||12313===e||12314===e||12315===e||12316===e||12317===e||e>=12318&&e<=12319||12320===e||12336===e||64830===e||64831===e||e>=65093&&e<=65094}function ie(e){e.forEach((function(e){if(delete e.location,m(e)||f(e))for(var t in e.options)delete e.options[t].location,ie(e.options[t].value);else p(e)&&b(e.style)||(g(e)||d(e))&&y(e.style)?delete e.style.location:E(e)&&ie(e.children)}))}function oe(e,t){var r=t&&t.cache?t.cache:me,s=t&&t.serializer?t.serializer:he;return(t&&t.strategy?t.strategy:ue)(e,{cache:r,serializer:s})}function ae(e,t,r,s){var n,i=null==(n=s)||"number"==typeof n||"boolean"==typeof n?s:r(s),o=t.get(i);return void 0===o&&(o=e.call(this,s),t.set(i,o)),o}function le(e,t,r){var s=Array.prototype.slice.call(arguments,3),n=r(s),i=t.get(n);return void 0===i&&(i=e.apply(this,s),t.set(n,i)),i}function ce(e,t,r,s,n){return r.bind(t,e,s,n)}function ue(e,t){return ce(e,this,1===e.length?ae:le,t.cache.create(),t.serializer)}var he=function(){return JSON.stringify(arguments)};function pe(){this.cache=Object.create(null)}pe.prototype.has=function(e){return e in this.cache},pe.prototype.get=function(e){return this.cache[e]},pe.prototype.set=function(e,t){this.cache[e]=t};var ge,de,me={create:function(){return new pe}},fe={variadic:function(e,t){return ce(e,this,le,t.cache.create(),t.serializer)},monadic:function(e,t){return ce(e,this,ae,t.cache.create(),t.serializer)}};(de=ge||(ge={})).MISSING_VALUE="MISSING_VALUE",de.INVALID_VALUE="INVALID_VALUE",de.MISSING_INTL_API="MISSING_INTL_API";var Se,Ee,be=class extends Error{constructor(e,t,r){super(e),this.code=t,this.originalMessage=r}toString(){return`[formatjs Error: ${this.code}] ${this.message}`}},ye=class extends be{constructor(e,t,r,s){super(`Invalid values for "${e}": "${t}". Options are "${Object.keys(r).join('", "')}"`,ge.INVALID_VALUE,s)}},xe=class extends be{constructor(e,t,r){super(`Value for "${e}" must be of type ${t}`,ge.INVALID_VALUE,r)}},we=class extends be{constructor(e,t){super(`The intl string context variable "${e}" was not provided to the string "${t}"`,ge.MISSING_VALUE,t)}};function ve(e){return"function"==typeof e}function Te(e,t,r,s,n,i,o){if(1===e.length&&u(e[0]))return[{type:Se.literal,value:e[0].value}];const a=[];for(const l of e){if(u(l)){a.push({type:Se.literal,value:l.value});continue}if(S(l)){"number"==typeof i&&a.push({type:Se.literal,value:r.getNumberFormat(t).format(i)});continue}const{value:e}=l;if(!n||!(e in n))throw new we(e,o);let c=n[e];if(h(l))c&&"string"!=typeof c&&"number"!=typeof c||(c="string"==typeof c||"number"==typeof c?String(c):""),a.push({type:"string"==typeof c?Se.literal:Se.object,value:c});else if(g(l)){const e="string"==typeof l.style?s.date[l.style]:y(l.style)?l.style.parsedOptions:void 0;a.push({type:Se.literal,value:r.getDateTimeFormat(t,e).format(c)})}else if(d(l)){const e="string"==typeof l.style?s.time[l.style]:y(l.style)?l.style.parsedOptions:void 0;a.push({type:Se.literal,value:r.getDateTimeFormat(t,e).format(c)})}else if(p(l)){const e="string"==typeof l.style?s.number[l.style]:b(l.style)?l.style.parsedOptions:void 0;e&&e.scale&&(c*=e.scale||1),a.push({type:Se.literal,value:r.getNumberFormat(t,e).format(c)})}else{if(E(l)){const{children:e,value:c}=l,u=n[c];if(!ve(u))throw new xe(c,"function",o);let h=u(Te(e,t,r,s,n,i).map((e=>e.value)));Array.isArray(h)||(h=[h]),a.push(...h.map((e=>({type:"string"==typeof e?Se.literal:Se.object,value:e}))))}if(m(l)){const e=l.options[c]||l.options.other;if(!e)throw new ye(l.value,c,Object.keys(l.options),o);a.push(...Te(e.value,t,r,s,n))}else if(f(l)){let e=l.options[`=${c}`];if(!e){if(!Intl.PluralRules)throw new be('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',ge.MISSING_INTL_API,o);const s=r.getPluralRules(t,{type:l.pluralType}).select(c-(l.offset||0));e=l.options[s]||l.options.other}if(!e)throw new ye(l.value,c,Object.keys(l.options),o);a.push(...Te(e.value,t,r,s,n,c-(l.offset||0)))}else;}}return(l=a).length<2?l:l.reduce(((e,t)=>{const r=e[e.length-1];return r&&r.type===Se.literal&&t.type===Se.literal?r.value+=t.value:e.push(t),e}),[]);var l}function Ae(e,t){return t?Object.keys(e).reduce(((r,s)=>{var n,i;return r[s]=(n=e[s],(i=t[s])?{...n||{},...i||{},...Object.keys(n).reduce(((e,t)=>(e[t]={...n[t],...i[t]||{}},e)),{})}:n),r}),{...e}):e}function Ie(e){return{create:()=>({has:t=>t in e,get:t=>e[t],set(t,r){e[t]=r}})}}(Ee=Se||(Se={}))[Ee.literal=0]="literal",Ee[Ee.object=1]="object";var Le=class{constructor(e,t=Le.defaultLocale,r,s){if(this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=e=>{const t=this.formatToParts(e);if(1===t.length)return t[0].value;const r=t.reduce(((e,t)=>(e.length&&t.type===Se.literal&&"string"==typeof e[e.length-1]?e[e.length-1]+=t.value:e.push(t.value),e)),[]);return r.length<=1?r[0]||"":r},this.formatToParts=e=>Te(this.ast,this.locales,this.formatters,this.formats,e,void 0,this.message),this.resolvedOptions=()=>({locale:Intl.NumberFormat.supportedLocalesOf(this.locales)[0]}),this.getAst=()=>this.ast,"string"==typeof e){if(this.message=e,!Le.__parse)throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");this.ast=Le.__parse(e,{ignoreTag:s?.ignoreTag})}else this.ast=e;if(!Array.isArray(this.ast))throw new TypeError("A message must be provided as a String or AST.");this.formats=Ae(Le.formats,r),this.locales=t,this.formatters=s&&s.formatters||function(e={number:{},dateTime:{},pluralRules:{}}){return{getNumberFormat:oe(((...e)=>new Intl.NumberFormat(...e)),{cache:Ie(e.number),strategy:fe.variadic}),getDateTimeFormat:oe(((...e)=>new Intl.DateTimeFormat(...e)),{cache:Ie(e.dateTime),strategy:fe.variadic}),getPluralRules:oe(((...e)=>new Intl.PluralRules(...e)),{cache:Ie(e.pluralRules),strategy:fe.variadic})}}(this.formatterCache)}static get defaultLocale(){return Le.memoizedDefaultLocale||(Le.memoizedDefaultLocale=(new Intl.NumberFormat).resolvedOptions().locale),Le.memoizedDefaultLocale}};Le.memoizedDefaultLocale=null,Le.__parse=function(e,t){void 0===t&&(t={}),t=c({shouldParseSkeletons:!0,requiresOtherClause:!0},t);var r=new te(e,t).parse();if(r.err){var n=SyntaxError(s[r.err.kind]);throw n.location=r.err.location,n.originalMessage=r.err.message,n}return(null==t?void 0:t.captureLocation)||ie(r.val),r.val},Le.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}};var Re=Le})),n.register("1kwzI",(function(e,t){e.exports=import("./"+n("27Lyk").resolve("4P7Yb")).then((()=>n("9F6kV")))})),n.register("kCQSR",(function(t,r){e(t.exports,"preciseMillisToString",(()=>l)),e(t.exports,"millisToString",(()=>c)),e(t.exports,"secondsToString",(()=>u));var s=n("ieEAB");const i={fmms:"{PH1} μs",fms:"{PH1} ms",fs:"{PH1} s",fmin:"{PH1} min",fhrs:"{PH1} hrs",fdays:"{PH1} days"},o=(0,s.registerUIStrings)("core/i18n/time-utilities.ts",i),a=s.getLocalizedString.bind(void 0,o),l=function(e,t){return t=t||0,a(i.fms,{PH1:e.toFixed(t)})},c=function(e,t){if(!isFinite(e))return"-";if(0===e)return"0";if(t&&e<.1)return a(i.fmms,{PH1:(1e3*e).toFixed(0)});if(t&&e<1e3)return a(i.fms,{PH1:e.toFixed(2)});if(e<1e3)return a(i.fms,{PH1:e.toFixed(0)});const r=e/1e3;if(r<60)return a(i.fs,{PH1:r.toFixed(2)});const s=r/60;if(s<60)return a(i.fmin,{PH1:s.toFixed(1)});const n=s/60;if(n<24)return a(i.fhrs,{PH1:n.toFixed(1)});return a(i.fdays,{PH1:(n/24).toFixed(1)})},u=function(e,t){return isFinite(e)?c(1e3*e,t):"-"}})),n.register("59M8v",(function(e,t){})),n.register("8KVjw",(function(t,r){function s(e){for(const t of e)t.eventTarget.removeEventListener(t.eventType,t.listener,t.thisObject);e.splice(0)}function n(e,t={},r=window){const s=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:t});r.dispatchEvent(s)}e(t.exports,"removeEventListeners",(()=>s)),e(t.exports,"fireEvent",(()=>n))})),n.register("iP7gF",(function(e,t){})),n.register("5voj1",(function(t,r){e(t.exports,"lazy",(()=>i));const s=Symbol("uninitialized"),n=Symbol("error");function i(e){let t=s,r=null;return()=>{if(t===n)throw r;if(t!==s)return t;try{return t=e(),t}catch(e){throw r=e,t=n,r}}}})),n.register("cv4pb",(function(t,r){e(t.exports,"Linkifier",(()=>s)),e(t.exports,"getApplicableRegisteredlinkifiers",(()=>o)),e(t.exports,"registerLinkifier",(()=>i));class s{static async linkify(e,t){if(!e)throw new Error("Can't linkify "+e);const r=o(e)[0];if(!r)throw new Error("No linkifiers registered for object "+e);return(await r.loadLinkifier()).linkify(e,t)}}const n=[];function i(e){n.push(e)}function o(e){return n.filter((function(t){if(!t.contextTypes)return!0;for(const r of t.contextTypes())if(e instanceof r)return!0;return!1}))}})),n.register("fjeUg",(function(t,r){e(t.exports,"normalizePath",(()=>i)),e(t.exports,"ParsedURL",(()=>o)),n("lz7WY");var s=n("cmJc8");function i(e){if(-1===e.indexOf("..")&&-1===e.indexOf("."))return e;const t=[],r=e.split("/");for(const e of r)"."!==e&&(".."===e?t.pop():e&&t.push(e));let s=t.join("/");return"/"===s[s.length-1]||("/"===e[0]&&s&&(s="/"+s),"/"!==e[e.length-1]&&"."!==r[r.length-1]&&".."!==r[r.length-1]||(s+="/")),s}class o{isValid;url;scheme;user;host;port;path;queryParams;fragment;folderPathComponents;lastPathComponent;blobInnerScheme;#x;#w;constructor(e){this.isValid=!1,this.url=e,this.scheme="",this.user="",this.host="",this.port="",this.path="",this.queryParams="",this.fragment="",this.folderPathComponents="",this.lastPathComponent="";const t=this.url.startsWith("blob:"),r=(t?e.substring(5):e).match(o.urlRegex());if(r)this.isValid=!0,t?(this.blobInnerScheme=r[2].toLowerCase(),this.scheme="blob"):this.scheme=r[2].toLowerCase(),this.user=r[3],this.host=r[4],this.port=r[5],this.path=r[6]||"/",this.queryParams=r[7]||"",this.fragment=r[8];else{if(this.url.startsWith("data:"))return void(this.scheme="data");if(this.url.startsWith("blob:"))return void(this.scheme="blob");if("about:blank"===this.url)return void(this.scheme="about");this.path=this.url}const s=this.path.lastIndexOf("/");-1!==s?(this.folderPathComponents=this.path.substring(0,s),this.lastPathComponent=this.path.substring(s+1)):this.lastPathComponent=this.path}static fromString(e){const t=new o(e.toString());return t.isValid?t:null}static preEncodeSpecialCharactersInPath(e){for(const t of["%",";","#","?"])e=e.replaceAll(t,encodeURIComponent(t));return e}static rawPathToEncodedPathString(e){const t=o.preEncodeSpecialCharactersInPath(e);return e.startsWith("/")?new URL(t,"file:///").pathname:new URL("/"+t,"file:///").pathname.substr(1)}static encodedFromParentPathAndName(e,t){return o.concatenate(e,"/",encodeURIComponent(t))}static urlFromParentUrlAndName(e,t){return o.concatenate(e,"/",encodeURIComponent(t))}static encodedPathToRawPathString(e){return decodeURIComponent(e)}static rawPathToUrlString(e){let t=o.preEncodeSpecialCharactersInPath(e.replace(/\\/g,"/"));return t=t.replace(/\\/g,"/"),t.startsWith("file://")||(t=t.startsWith("/")?"file://"+t:"file:///"+t),new URL(t).toString()}static relativePathToUrlString(e,t){const r=o.preEncodeSpecialCharactersInPath(e.replace(/\\/g,"/"));return new URL(r,t).toString()}static urlToRawPathString(e,t){console.assert(e.startsWith("file://"),"This must be a file URL.");const r=decodeURIComponent(e);return t?r.substr(8).replace(/\//g,"\\"):r.substr(7)}static sliceUrlToEncodedPathString(e,t){return e.substring(t)}static substr(e,t,r){return e.substr(t,r)}static substring(e,t,r){return e.substring(t,r)}static prepend(e,t){return e+t}static concatenate(e,...t){return e.concat(...t)}static trim(e){return e.trim()}static slice(e,t,r){return e.slice(t,r)}static join(e,t){return e.join(t)}static urlWithoutHash(e){const t=e.indexOf("#");return-1!==t?e.substr(0,t):e}static urlRegex(){if(o.urlRegexInstance)return o.urlRegexInstance;return o.urlRegexInstance=new RegExp("^("+/([A-Za-z][A-Za-z0-9+.-]*):\/\//.source+/(?:([A-Za-z0-9\-._~%!$&'()*+,;=:]*)@)?/.source+/((?:\[::\d?\])|(?:[^\s\/:]*))/.source+/(?::([\d]+))?/.source+")"+/(\/[^#?]*)?/.source+/(?:\?([^#]*))?/.source+/(?:#(.*))?/.source+"$"),o.urlRegexInstance}static extractPath(e){const t=this.fromString(e);return t?t.path:""}static extractOrigin(e){const t=this.fromString(e);return t?t.securityOrigin():""}static extractExtension(e){const t=(e=o.urlWithoutHash(e)).indexOf("?");-1!==t&&(e=e.substr(0,t));const r=e.lastIndexOf("/");-1!==r&&(e=e.substr(r+1));const s=e.lastIndexOf(".");if(-1!==s){const t=(e=e.substr(s+1)).indexOf("%");return-1!==t?e.substr(0,t):e}return""}static extractName(e){let t=e.lastIndexOf("/");const r=-1!==t?e.substr(t+1):e;return t=r.indexOf("?"),t<0?r:r.substr(0,t)}static completeURL(e,t){const r=t.trim();if(r.startsWith("data:")||r.startsWith("blob:")||r.startsWith("javascript:")||r.startsWith("mailto:"))return t;const s=this.fromString(r);if(s&&s.scheme){const e=s.securityOrigin(),t=s.path,n=r.substring(e.length+t.length);return e+i(t)+n}const n=this.fromString(e);if(!n)return null;if(n.isDataURL())return t;if(t.length>1&&"/"===t.charAt(0)&&"/"===t.charAt(1))return n.scheme+":"+t;const o=n.securityOrigin(),a=n.path,l=n.queryParams?"?"+n.queryParams:"";if(!t.length)return o+a+l;if("#"===t.charAt(0))return o+a+l+t;if("?"===t.charAt(0))return o+a+t;const c=t.match(/^[^#?]*/);if(!c||!t.length)throw new Error("Invalid href");let u=c[0];const h=t.substring(u.length);return"/"!==u.charAt(0)&&(u=n.folderPathComponents+"/"+u),o+i(u)+h}static splitLineAndColumn(e){const t=e.match(o.urlRegex());let r="",s=e;t&&(r=t[1],s=e.substring(t[1].length));const n=/(?::(\d+))?(?::(\d+))?$/.exec(s);let i,a;if(console.assert(Boolean(n)),!n)return{url:e,lineNumber:0,columnNumber:0};"string"==typeof n[1]&&(i=parseInt(n[1],10),i=isNaN(i)?void 0:i-1),"string"==typeof n[2]&&(a=parseInt(n[2],10),a=isNaN(a)?void 0:a-1);let l=r+s.substring(0,s.length-n[0].length);if(void 0===n[1]&&void 0===n[2]){const e=/wasm-function\[\d+\]:0x([a-z0-9]+)$/g.exec(s);e&&"string"==typeof e[1]&&(l=o.removeWasmFunctionInfoFromURL(l),a=parseInt(e[1],16),a=isNaN(a)?void 0:a)}return{url:l,lineNumber:i,columnNumber:a}}static removeWasmFunctionInfoFromURL(e){const t=e.search(/:wasm-function\[\d+\]/);return-1===t?e:e.substring(0,t)}static isRelativeURL(e){return!/^[A-Za-z][A-Za-z0-9+.-]*:/.test(e)}get displayName(){return this.#x?this.#x:this.isDataURL()?this.dataURLDisplayName():this.isBlobURL()||this.isAboutBlank()?this.url:(this.#x=this.lastPathComponent,this.#x||(this.#x=(this.host||"")+"/"),"/"===this.#x&&(this.#x=this.url),this.#x)}dataURLDisplayName(){return this.#w?this.#w:this.isDataURL()?(this.#w=s.trimEndWithMaxLength(this.url,20),this.#w):""}isAboutBlank(){return"about:blank"===this.url}isDataURL(){return"data"===this.scheme}isHttpOrHttps(){return"http"===this.scheme||"https"===this.scheme}isBlobURL(){return this.url.startsWith("blob:")}lastPathComponentWithFragment(){return this.lastPathComponent+(this.fragment?"#"+this.fragment:"")}domain(){return this.isDataURL()?"data:":this.host+(this.port?":"+this.port:"")}securityOrigin(){if(this.isDataURL())return"data:";return(this.isBlobURL()?this.blobInnerScheme:this.scheme)+"://"+this.domain()}urlWithoutScheme(){return this.scheme&&this.url.startsWith(this.scheme+"://")?this.url.substring(this.scheme.length+3):this.url}static urlRegexInstance=null}})),n.register("3oEUj",(function(t,r){e(t.exports,"Progress",(()=>s)),e(t.exports,"CompositeProgress",(()=>n)),e(t.exports,"SubProgress",(()=>i)),e(t.exports,"ProgressProxy",(()=>o));class s{setTotalWork(e){}setTitle(e){}setWorked(e,t){}incrementWorked(e){}done(){}isCanceled(){return!1}}class n{parent;#v;#T;constructor(e){this.parent=e,this.#v=[],this.#T=0,this.parent.setTotalWork(1),this.parent.setWorked(0)}childDone(){++this.#T===this.#v.length&&this.parent.done()}createSubProgress(e){const t=new i(this,e);return this.#v.push(t),t}update(){let e=0,t=0;for(let r=0;r<this.#v.length;++r){const s=this.#v[r];s.getTotalWork()&&(t+=s.getWeight()*s.getWorked()/s.getTotalWork()),e+=s.getWeight()}this.parent.setWorked(t/e)}}class i{#A;#I;#L;#R;constructor(e,t){this.#A=e,this.#I=t||1,this.#L=0,this.#R=0}isCanceled(){return this.#A.parent.isCanceled()}setTitle(e){this.#A.parent.setTitle(e)}done(){this.setWorked(this.#R),this.#A.childDone()}setTotalWork(e){this.#R=e,this.#A.update()}setWorked(e,t){this.#L=e,void 0!==t&&this.setTitle(t),this.#A.update()}incrementWorked(e){this.setWorked(this.#L+(e||1))}getWeight(){return this.#I}getWorked(){return this.#L}getTotalWork(){return this.#R}}class o{#P;#C;constructor(e,t){this.#P=e,this.#C=t}isCanceled(){return!!this.#P&&this.#P.isCanceled()}setTitle(e){this.#P&&this.#P.setTitle(e)}done(){this.#P&&this.#P.done(),this.#C&&this.#C()}setTotalWork(e){this.#P&&this.#P.setTotalWork(e)}setWorked(e,t){this.#P&&this.#P.setWorked(e,t)}incrementWorked(e){this.#P&&this.#P.incrementWorked(e)}}})),n.register("5G4Uo",(function(e,t){})),n.register("jNiNc",(function(t,r){e(t.exports,"ResolverBase",(()=>s));class s{#N=new Map;async waitFor(e){const t=this.getForId(e);return t||this.getOrCreatePromise(e)}tryGet(e,t){const r=this.getForId(e);if(!r){const r=()=>{};return this.getOrCreatePromise(e).catch(r).then((e=>{e&&t(e)})),null}return r}clear(){this.stopListening();for(const[e,{reject:t}]of this.#N.entries())t(new Error(`Object with ${e} never resolved.`));this.#N.clear()}getOrCreatePromise(e){const t=this.#N.get(e);if(t)return t.promise;let r=()=>{},s=()=>{};const n=new Promise(((e,t)=>{r=e,s=t}));return this.#N.set(e,{promise:n,resolve:r,reject:s}),this.startListening(),n}onResolve(e,t){const r=this.#N.get(e);this.#N.delete(e),0===this.#N.size&&this.stopListening(),r?.resolve(t)}}})),n.register("k0gJh",(function(t,r){e(t.exports,"ResourceType",(()=>c)),e(t.exports,"resourceTypes",(()=>p)),e(t.exports,"resourceTypeByExtension",(()=>d)),e(t.exports,"mimeTypeByExtension",(()=>m)),e(t.exports,"ResourceCategory",(()=>u)),e(t.exports,"resourceCategories",(()=>h)),n("ixFnt");var s=n("ieEAB"),i=n("fjeUg");const o={xhrAndFetch:"`XHR` and `Fetch`",scripts:"Scripts",js:"JS",stylesheets:"Stylesheets",css:"CSS",images:"Images",img:"Img",media:"Media",fonts:"Fonts",font:"Font",documents:"Documents",doc:"Doc",websockets:"WebSockets",ws:"WS",webassembly:"WebAssembly",wasm:"Wasm",manifest:"Manifest",other:"Other",document:"Document",stylesheet:"Stylesheet",image:"Image",script:"Script",texttrack:"TextTrack",fetch:"Fetch",eventsource:"EventSource",websocket:"WebSocket",webtransport:"WebTransport",signedexchange:"SignedExchange",ping:"Ping",cspviolationreport:"CSPViolationReport",preflight:"Preflight",webbundle:"WebBundle"},a=s.registerUIStrings("core/common/ResourceType.ts",o),l=s.getLazilyComputedLocalizedString.bind(void 0,a);class c{#O;#M;#k;#_;constructor(e,t,r,s){this.#O=e,this.#M=t,this.#k=r,this.#_=s}static fromMimeType(e){return e?e.startsWith("text/html")?p.Document:e.startsWith("text/css")?p.Stylesheet:e.startsWith("image/")?p.Image:e.startsWith("text/")?p.Script:e.includes("font")?p.Font:e.includes("script")?p.Script:e.includes("octet")?p.Other:e.includes("application")?p.Script:p.Other:p.Other}static fromMimeTypeOverride(e){return"application/wasm"===e?p.Wasm:"application/webbundle"===e?p.WebBundle:null}static fromURL(e){return d.get(i.ParsedURL.extractExtension(e))||null}static fromName(e){for(const t in p){const r=p[t];if(r.name()===e)return r}return null}static mimeFromURL(e){const t=i.ParsedURL.extractName(e);if(g.has(t))return g.get(t);const r=i.ParsedURL.extractExtension(e).toLowerCase();return m.get(r)}static mimeFromExtension(e){return m.get(e)}name(){return this.#O}title(){return this.#M()}category(){return this.#k}isTextType(){return this.#_}isScript(){return"script"===this.#O||"sm-script"===this.#O}hasScripts(){return this.isScript()||this.isDocument()}isStyleSheet(){return"stylesheet"===this.#O||"sm-stylesheet"===this.#O}isDocument(){return"document"===this.#O}isDocumentOrScriptOrStyleSheet(){return this.isDocument()||this.isScript()||this.isStyleSheet()}isFont(){return"font"===this.#O}isImage(){return"image"===this.#O}isFromSourceMap(){return this.#O.startsWith("sm-")}isWebbundle(){return"webbundle"===this.#O}toString(){return this.#O}canonicalMimeType(){return this.isDocument()?"text/html":this.isScript()?"text/javascript":this.isStyleSheet()?"text/css":""}}class u{title;shortTitle;constructor(e,t){this.title=e,this.shortTitle=t}}const h={XHR:new u(l(o.xhrAndFetch),s.lockedLazyString("Fetch/XHR")),Script:new u(l(o.scripts),l(o.js)),Stylesheet:new u(l(o.stylesheets),l(o.css)),Image:new u(l(o.images),l(o.img)),Media:new u(l(o.media),l(o.media)),Font:new u(l(o.fonts),l(o.font)),Document:new u(l(o.documents),l(o.doc)),WebSocket:new u(l(o.websockets),l(o.ws)),Wasm:new u(l(o.webassembly),l(o.wasm)),Manifest:new u(l(o.manifest),l(o.manifest)),Other:new u(l(o.other),l(o.other))},p={Document:new c("document",l(o.document),h.Document,!0),Stylesheet:new c("stylesheet",l(o.stylesheet),h.Stylesheet,!0),Image:new c("image",l(o.image),h.Image,!1),Media:new c("media",l(o.media),h.Media,!1),Font:new c("font",l(o.font),h.Font,!1),Script:new c("script",l(o.script),h.Script,!0),TextTrack:new c("texttrack",l(o.texttrack),h.Other,!0),XHR:new c("xhr",s.lockedLazyString("XHR"),h.XHR,!0),Fetch:new c("fetch",l(o.fetch),h.XHR,!0),EventSource:new c("eventsource",l(o.eventsource),h.XHR,!0),WebSocket:new c("websocket",l(o.websocket),h.WebSocket,!1),WebTransport:new c("webtransport",l(o.webtransport),h.WebSocket,!1),Wasm:new c("wasm",l(o.wasm),h.Wasm,!1),Manifest:new c("manifest",l(o.manifest),h.Manifest,!0),SignedExchange:new c("signed-exchange",l(o.signedexchange),h.Other,!1),Ping:new c("ping",l(o.ping),h.Other,!1),CSPViolationReport:new c("csp-violation-report",l(o.cspviolationreport),h.Other,!1),Other:new c("other",l(o.other),h.Other,!1),Preflight:new c("preflight",l(o.preflight),h.Other,!0),SourceMapScript:new c("sm-script",l(o.script),h.Script,!0),SourceMapStyleSheet:new c("sm-stylesheet",l(o.stylesheet),h.Stylesheet,!0),WebBundle:new c("webbundle",l(o.webbundle),h.Other,!1)},g=new Map([["Cakefile","text/x-coffeescript"]]),d=new Map([["js",p.Script],["mjs",p.Script],["css",p.Stylesheet],["xsl",p.Stylesheet],["avif",p.Image],["avifs",p.Image],["bmp",p.Image],["gif",p.Image],["ico",p.Image],["jpeg",p.Image],["jpg",p.Image],["jxl",p.Image],["png",p.Image],["svg",p.Image],["tif",p.Image],["tiff",p.Image],["webp",p.Media],["otf",p.Font],["ttc",p.Font],["ttf",p.Font],["woff",p.Font],["woff2",p.Font],["wasm",p.Wasm]]),m=new Map([["js","text/javascript"],["mjs","text/javascript"],["css","text/css"],["html","text/html"],["htm","text/html"],["xml","application/xml"],["xsl","application/xml"],["wasm","application/wasm"],["asp","application/x-aspx"],["aspx","application/x-aspx"],["jsp","application/x-jsp"],["c","text/x-c++src"],["cc","text/x-c++src"],["cpp","text/x-c++src"],["h","text/x-c++src"],["m","text/x-c++src"],["mm","text/x-c++src"],["coffee","text/x-coffeescript"],["dart","text/javascript"],["ts","text/typescript"],["tsx","text/typescript-jsx"],["json","application/json"],["gyp","application/json"],["gypi","application/json"],["cs","text/x-csharp"],["java","text/x-java"],["less","text/x-less"],["php","text/x-php"],["phtml","application/x-httpd-php"],["py","text/x-python"],["sh","text/x-sh"],["scss","text/x-scss"],["vtt","text/vtt"],["ls","text/x-livescript"],["md","text/markdown"],["cljs","text/x-clojure"],["cljc","text/x-clojure"],["cljx","text/x-clojure"],["styl","text/x-styl"],["jsx","text/jsx"],["avif","image/avif"],["avifs","image/avif-sequence"],["bmp","image/bmp"],["gif","image/gif"],["ico","image/ico"],["jpeg","image/jpeg"],["jpg","image/jpeg"],["jxl","image/jxl"],["png","image/png"],["svg","image/svg+xml"],["tif","image/tif"],["tiff","image/tiff"],["webp","image/webp"],["otf","font/otf"],["ttc","font/collection"],["ttf","font/ttf"],["woff","font/woff"],["woff2","font/woff2"]])})),n.register("dgs9F",(function(t,r){e(t.exports,"registerLateInitializationRunnable",(()=>n)),e(t.exports,"maybeRemoveLateInitializationRunnable",(()=>i)),e(t.exports,"lateInitializationRunnables",(()=>o)),e(t.exports,"registerEarlyInitializationRunnable",(()=>l)),e(t.exports,"earlyInitializationRunnables",(()=>c));const s=new Map;function n(e){const{id:t,loadRunnable:r}=e;if(s.has(t))throw new Error(`Duplicate late Initializable runnable id '${t}'`);s.set(t,r)}function i(e){return s.delete(e)}function o(){return[...s.values()]}const a=[];function l(e){a.push(e)}function c(){return a}})),n.register("fhm4u",(function(t,r){e(t.exports,"Segment",(()=>i)),e(t.exports,"SegmentedRange",(()=>o)),n("lz7WY");var s=n("03Tsr");class i{begin;end;data;constructor(e,t,r){if(e>t)throw new Error("Invalid segment");this.begin=e,this.end=t,this.data=r}intersects(e){return this.begin<e.end&&e.begin<this.end}}class o{#F;#U;constructor(e){this.#F=[],this.#U=e}append(e){let t=s.lowerBound(this.#F,e,((e,t)=>e.begin-t.begin)),r=t,n=null;if(t>0){const r=this.#F[t-1];n=this.tryMerge(r,e),n?(--t,e=n):this.#F[t-1].end>=e.begin&&(e.end<r.end&&this.#F.splice(t,0,new i(e.end,r.end,r.data)),r.end=e.begin)}for(;r<this.#F.length&&this.#F[r].end<=e.end;)++r;r<this.#F.length&&(n=this.tryMerge(e,this.#F[r]),n?(r++,e=n):e.intersects(this.#F[r])&&(this.#F[r].begin=e.end)),this.#F.splice(t,r-t,e)}appendRange(e){e.segments().forEach((e=>this.append(e)))}segments(){return this.#F}tryMerge(e,t){const r=this.#U&&this.#U(e,t);return r?(r.begin=e.begin,r.end=Math.max(e.end,t.end),r):null}}})),n.register("dMaA7",(function(t,r){e(t.exports,"Settings",(()=>u)),e(t.exports,"SettingsStorage",(()=>p)),e(t.exports,"Setting",(()=>d)),e(t.exports,"SettingStorageType",(()=>S)),e(t.exports,"RegExpSetting",(()=>m)),e(t.exports,"VersionController",(()=>f)),e(t.exports,"NOOP_STORAGE",(()=>h)),e(t.exports,"moduleSetting",(()=>b)),e(t.exports,"settingForTest",(()=>y)),e(t.exports,"detectColorFormat",(()=>x)),e(t.exports,"getLocalizedSettingsCategory",(()=>n("f4gCV").getLocalizedSettingsCategory)),e(t.exports,"getRegisteredSettings",(()=>n("f4gCV").getRegisteredSettings)),e(t.exports,"maybeRemoveSettingExtension",(()=>n("f4gCV").maybeRemoveSettingExtension)),e(t.exports,"registerSettingExtension",(()=>n("f4gCV").registerSettingExtension)),e(t.exports,"SettingCategory",(()=>n("f4gCV").SettingCategory)),e(t.exports,"SettingType",(()=>n("f4gCV").SettingType)),e(t.exports,"registerSettingsForTest",(()=>n("f4gCV").registerSettingsForTest)),e(t.exports,"resetSettings",(()=>n("f4gCV").resetSettings)),n("9X2mn");var s=n("elz4U"),i=n("4Pxzi"),o=n("7U4D1"),a=n("5gQQ4"),l=n("f4gCV");let c;class u{syncedStorage;globalStorage;localStorage;#D;settingNameSet;orderValuesBySettingCategory;#V;#G;moduleSettings;constructor(e,t,r){this.syncedStorage=e,this.globalStorage=t,this.localStorage=r,this.#D=new p({}),this.settingNameSet=new Set,this.orderValuesBySettingCategory=new Map,this.#V=new(0,a.ObjectWrapper),this.#G=new Map,this.moduleSettings=new Map;for(const e of(0,l.getRegisteredSettings)()){const{settingName:t,defaultValue:r,storageType:n}=e,i=e.settingType===l.SettingType.REGEX&&"string"==typeof r?this.createRegExpSetting(t,r,void 0,n):this.createSetting(t,r,n);"mac"===s.Runtime.platform()&&e.titleMac?i.setTitleFunction(e.titleMac):i.setTitleFunction(e.title),e.userActionCondition&&i.setRequiresUserAction(Boolean(s.Runtime.queryParam(e.userActionCondition))),i.setRegistration(e),this.registerModuleSetting(i)}}static hasInstance(){return void 0!==c}static instance(e={forceNew:null,syncedStorage:null,globalStorage:null,localStorage:null}){const{forceNew:t,syncedStorage:r,globalStorage:s,localStorage:n}=e;if(!c||t){if(!r||!s||!n)throw new Error(`Unable to create settings: global and local storage must be provided: ${(new Error).stack}`);c=new u(r,s,n)}return c}static removeInstance(){c=void 0}registerModuleSetting(e){const t=e.name,r=e.category(),s=e.order();if(this.settingNameSet.has(t))throw new Error(`Duplicate Setting name '${t}'`);if(r&&s){const e=this.orderValuesBySettingCategory.get(r)||new Set;if(e.has(s))throw new Error(`Duplicate order value '${s}' for settings category '${r}'`);e.add(s),this.orderValuesBySettingCategory.set(r,e)}this.settingNameSet.add(t),this.moduleSettings.set(e.name,e)}moduleSetting(e){const t=this.moduleSettings.get(e);if(!t)throw new Error("No setting registered: "+e);return t}settingForTest(e){const t=this.#G.get(e);if(!t)throw new Error("No setting registered: "+e);return t}createSetting(e,t,r){const s=this.storageFromType(r);let n=this.#G.get(e);return n||(n=new d(e,t,this.#V,s),this.#G.set(e,n)),n}createLocalSetting(e,t){return this.createSetting(e,t,S.Local)}createRegExpSetting(e,t,r,s){return this.#G.get(e)||this.#G.set(e,new m(e,t,this.#V,this.storageFromType(s),r)),this.#G.get(e)}clearAll(){this.globalStorage.removeAll(),this.localStorage.removeAll();u.instance().createSetting(f.currentVersionName,0).set(f.currentVersion)}storageFromType(e){switch(e){case S.Local:return this.localStorage;case S.Session:return this.#D;case S.Global:return this.globalStorage;case S.Synced:return this.syncedStorage}return this.globalStorage}getRegistry(){return this.#G}}const h={register:()=>{},set:()=>{},get:()=>Promise.resolve(""),remove:()=>{},clear:()=>{}};class p{object;backingStore;storagePrefix;constructor(e,t=h,r=""){this.object=e,this.backingStore=t,this.storagePrefix=r}register(e){e=this.storagePrefix+e,this.backingStore.register(e)}set(e,t){e=this.storagePrefix+e,this.object[e]=t,this.backingStore.set(e,t)}has(e){return(e=this.storagePrefix+e)in this.object}get(e){return e=this.storagePrefix+e,this.object[e]}async forceGet(e){const t=this.storagePrefix+e,r=await this.backingStore.get(t);return r&&r!==this.object[t]?this.set(e,r):r||this.remove(e),r}remove(e){e=this.storagePrefix+e,delete this.object[e],this.backingStore.remove(e)}removeAll(){this.object={},this.backingStore.clear()}dumpSizes(){o.Console.instance().log("Ten largest settings: ");const e={__proto__:null};for(const t in this.object)e[t]=this.object[t].length;const t=Object.keys(e);t.sort((function(t,r){return e[r]-e[t]}));for(let r=0;r<10&&r<t.length;++r)o.Console.instance().log("Setting: '"+t[r]+"', size: "+e[t[r]])}}function g(e){const t=e.name,r=u.instance();r.getRegistry().delete(t),r.moduleSettings.delete(t),e.storage.remove(t)}class d{name;defaultValue;eventSupport;storage;#W;#M;#B=null;#z;#H;#j=JSON;#X;#$;constructor(e,t,r,s){this.name=e,this.defaultValue=t,this.eventSupport=r,this.storage=s,s.register(e)}setSerializer(e){this.#j=e}addChangeListener(e,t){return this.eventSupport.addEventListener(this.name,e,t)}removeChangeListener(e,t){this.eventSupport.removeEventListener(this.name,e,t)}title(){return this.#M?this.#M:this.#W?this.#W():""}setTitleFunction(e){e&&(this.#W=e)}setTitle(e){this.#M=e}setRequiresUserAction(e){this.#z=e}disabled(){return this.#$||!1}setDisabled(e){this.#$=e,this.eventSupport.dispatchEventToListeners(this.name)}get(){if(this.#z&&!this.#X)return this.defaultValue;if(void 0!==this.#H)return this.#H;if(this.#H=this.defaultValue,this.storage.has(this.name))try{this.#H=this.#j.parse(this.storage.get(this.name))}catch(e){this.storage.remove(this.name)}return this.#H}async forceGet(){const e=this.name,t=this.storage.get(e),r=await this.storage.forceGet(e);if(this.#H=this.defaultValue,r)try{this.#H=this.#j.parse(r)}catch(e){this.storage.remove(this.name)}return t!==r&&this.eventSupport.dispatchEventToListeners(this.name,this.#H),this.#H}set(e){this.#X=!0,this.#H=e;try{const t=this.#j.stringify(e);try{this.storage.set(this.name,t)}catch(e){this.printSettingsSavingError(e.message,this.name,t)}}catch(e){o.Console.instance().error("Cannot stringify setting with name: "+this.name+", error: "+e.message)}this.eventSupport.dispatchEventToListeners(this.name,e)}setRegistration(e){this.#B=e}type(){return this.#B?this.#B.settingType:null}options(){return this.#B&&this.#B.options?this.#B.options.map((e=>{const{value:t,title:r,text:s,raw:n}=e;return{value:t,title:r(),text:"function"==typeof s?s():s,raw:n}})):[]}reloadRequired(){return this.#B&&this.#B.reloadRequired||null}category(){return this.#B&&this.#B.category||null}tags(){return this.#B&&this.#B.tags?this.#B.tags.map((e=>e())).join("\0"):null}order(){return this.#B&&this.#B.order||null}printSettingsSavingError(e,t,r){const s="Error saving setting with name: "+this.name+", value length: "+r.length+". Error: "+e;console.error(s),o.Console.instance().error(s),this.storage.dumpSizes()}}class m extends d{#Y;#q;constructor(e,t,r,s,n){super(e,t?[{pattern:t}]:[],r,s),this.#Y=n}get(){const e=[],t=this.getAsArray();for(let r=0;r<t.length;++r){const s=t[r];s.pattern&&!s.disabled&&e.push(s.pattern)}return e.join("|")}getAsArray(){return super.get()}set(e){this.setAsArray([{pattern:e,disabled:!1}])}setAsArray(e){this.#q=void 0,super.set(e)}asRegExp(){if(void 0!==this.#q)return this.#q;this.#q=null;try{const e=this.get();e&&(this.#q=new RegExp(e,this.#Y||""))}catch(e){}return this.#q}}class f{static get currentVersionName(){return"inspectorVersion"}static get currentVersion(){return 31}updateVersion(){const e=window.localStorage?window.localStorage[f.currentVersionName]:0,t=u.instance().createSetting(f.currentVersionName,0),r=f.currentVersion,s=t.get()||parseInt(e||"0",10);if(0===s)return void t.set(r);const n=this.methodsToRunToUpdateVersion(s,r);for(const e of n)this[e].call(this);t.set(r)}methodsToRunToUpdateVersion(e,t){const r=[];for(let s=e;s<t;++s)r.push("updateVersionFrom"+s+"To"+(s+1));return r}updateVersionFrom0To1(){this.clearBreakpointsWhenTooMany(u.instance().createLocalSetting("breakpoints",[]),5e5)}updateVersionFrom1To2(){u.instance().createSetting("previouslyViewedFiles",[]).set([])}updateVersionFrom2To3(){u.instance().createSetting("fileSystemMapping",{}).set({}),g(u.instance().createSetting("fileMappingEntries",[]))}updateVersionFrom3To4(){const e=u.instance().createSetting("showHeaSnapshotObjectsHiddenProperties",!1);b("showAdvancedHeapSnapshotProperties").set(e.get()),g(e)}updateVersionFrom4To5(){const e={FileSystemViewSidebarWidth:"fileSystemViewSplitViewState",elementsSidebarWidth:"elementsPanelSplitViewState",StylesPaneSplitRatio:"stylesPaneSplitViewState",heapSnapshotRetainersViewSize:"heapSnapshotSplitViewState","InspectorView.splitView":"InspectorView.splitViewState","InspectorView.screencastSplitView":"InspectorView.screencastSplitViewState","Inspector.drawerSplitView":"Inspector.drawerSplitViewState",layerDetailsSplitView:"layerDetailsSplitViewState",networkSidebarWidth:"networkPanelSplitViewState",sourcesSidebarWidth:"sourcesPanelSplitViewState",scriptsPanelNavigatorSidebarWidth:"sourcesPanelNavigatorSplitViewState",sourcesPanelSplitSidebarRatio:"sourcesPanelDebuggerSidebarSplitViewState","timeline-details":"timelinePanelDetailsSplitViewState","timeline-split":"timelinePanelRecorsSplitViewState","timeline-view":"timelinePanelTimelineStackSplitViewState",auditsSidebarWidth:"auditsPanelSplitViewState",layersSidebarWidth:"layersPanelSplitViewState",profilesSidebarWidth:"profilesPanelSplitViewState",resourcesSidebarWidth:"resourcesPanelSplitViewState"},t={};for(const r in e){const s=e[r],n=r+"H";let i=null;const o=u.instance().createSetting(r,t);o.get()!==t&&(i=i||{},i.vertical={},i.vertical.size=o.get(),g(o));const a=u.instance().createSetting(n,t);a.get()!==t&&(i=i||{},i.horizontal={},i.horizontal.size=a.get(),g(a)),i&&u.instance().createSetting(s,{}).set(i)}}updateVersionFrom5To6(){const e={debuggerSidebarHidden:"sourcesPanelSplitViewState",navigatorHidden:"sourcesPanelNavigatorSplitViewState","WebInspector.Drawer.showOnLoad":"Inspector.drawerSplitViewState"};for(const t in e){const r=u.instance().createSetting(t,null);if(null===r.get()){g(r);continue}const s=e[t],n="WebInspector.Drawer.showOnLoad"===t,i=r.get()!==n;g(r);const o=i?"OnlyMain":"Both",a=u.instance().createSetting(s,{}),l=a.get()||{};l.vertical=l.vertical||{},l.vertical.showMode=o,l.horizontal=l.horizontal||{},l.horizontal.showMode=o,a.set(l)}}updateVersionFrom6To7(){const e={sourcesPanelNavigatorSplitViewState:"sourcesPanelNavigatorSplitViewState",elementsPanelSplitViewState:"elementsPanelSplitViewState",stylesPaneSplitViewState:"stylesPaneSplitViewState",sourcesPanelDebuggerSidebarSplitViewState:"sourcesPanelDebuggerSidebarSplitViewState"},t={};for(const r in e){const e=u.instance().createSetting(r,t),s=e.get();s!==t&&(s.vertical&&s.vertical.size&&s.vertical.size<1&&(s.vertical.size=0),s.horizontal&&s.horizontal.size&&s.horizontal.size<1&&(s.horizontal.size=0),e.set(s))}}updateVersionFrom7To8(){}updateVersionFrom8To9(){const e=["skipStackFramesPattern","workspaceFolderExcludePattern"];for(let t=0;t<e.length;++t){const r=u.instance().createSetting(e[t],"");let s=r.get();if(!s)return;"string"==typeof s&&(s=[s]);for(let e=0;e<s.length;++e)"string"==typeof s[e]&&(s[e]={pattern:s[e]});r.set(s)}}updateVersionFrom9To10(){if(window.localStorage)for(const e in window.localStorage)e.startsWith("revision-history")&&window.localStorage.removeItem(e)}updateVersionFrom10To11(){const e=u.instance().createSetting("customDevicePresets",void 0),t=e.get();if(!Array.isArray(t))return;const r=[];for(let e=0;e<t.length;++e){const s=t[e],n={};n.title=s.title,n.type="unknown",n["user-agent"]=s.userAgent,n.capabilities=[],s.touch&&n.capabilities.push("touch"),s.mobile&&n.capabilities.push("mobile"),n.screen={},n.screen.vertical={width:s.width,height:s.height},n.screen.horizontal={width:s.height,height:s.width},n.screen["device-pixel-ratio"]=s.deviceScaleFactor,n.modes=[],n["show-by-default"]=!0,n.show="Default",r.push(n)}r.length&&u.instance().createSetting("customEmulatedDeviceList",[]).set(r),g(e)}updateVersionFrom11To12(){this.migrateSettingsFromLocalStorage()}updateVersionFrom12To13(){this.migrateSettingsFromLocalStorage(),g(u.instance().createSetting("timelineOverviewMode",""))}updateVersionFrom13To14(){const e={throughput:-1,latency:0};u.instance().createSetting("networkConditions",e).set(e)}updateVersionFrom14To15(){const e=u.instance().createLocalSetting("workspaceExcludedFolders",{}),t=e.get(),r={};for(const e in t){r[e]=[];for(const s of t[e])r[e].push(s.path)}e.set(r)}updateVersionFrom15To16(){const e=u.instance().createSetting("InspectorView.panelOrder",{}),t=e.get();for(const e of Object.keys(t))t[e]=10*(t[e]+1);e.set(t)}updateVersionFrom16To17(){const e=u.instance().createSetting("networkConditionsCustomProfiles",[]),t=e.get(),r=[];if(Array.isArray(t))for(const e of t)"string"==typeof e.title&&"object"==typeof e.value&&"number"==typeof e.value.throughput&&"number"==typeof e.value.latency&&r.push({title:e.title,value:{download:e.value.throughput,upload:e.value.throughput,latency:e.value.latency}});e.set(r)}updateVersionFrom17To18(){const e=u.instance().createLocalSetting("workspaceExcludedFolders",{}),t=e.get(),r={};for(const e in t){let s=e.replace(/\\/g,"/");s.startsWith("file://")||(s=s.startsWith("/")?"file://"+s:"file:///"+s),r[s]=t[e]}e.set(r)}updateVersionFrom18To19(){const e=u.instance().createSetting("networkLogColumnsVisibility",{status:!0,type:!0,initiator:!0,size:!0,time:!0}),t=e.get();t.name=!0,t.timeline=!0;const r={};for(const e in t)t.hasOwnProperty(e)&&(r[e.toLowerCase()]={visible:t[e]});u.instance().createSetting("networkLogColumns",{}).set(r),g(e)}updateVersionFrom19To20(){const e=u.instance().createSetting("InspectorView.panelOrder",{});u.instance().createSetting("panel-tabOrder",{}).set(e.get()),g(e)}updateVersionFrom20To21(){const e=u.instance().createSetting("networkLogColumns",{}),t=e.get();delete t.timeline,delete t.waterfall,e.set(t)}updateVersionFrom21To22(){const e=u.instance().createLocalSetting("breakpoints",[]),t=e.get();for(const e of t)e.url=e.sourceFileId,delete e.sourceFileId;e.set(t)}updateVersionFrom22To23(){}updateVersionFrom23To24(){const e=u.instance().createSetting("searchInContentScripts",!1);u.instance().createSetting("searchInAnonymousAndContentScripts",!1).set(e.get()),g(e)}updateVersionFrom24To25(){const e=u.instance().createSetting("networkLogColumns",{status:!0,type:!0,initiator:!0,size:!0,time:!0}),t=e.get();delete t.product,e.set(t)}updateVersionFrom25To26(){const e=u.instance().createSetting("messageURLFilters",{}),t=Object.keys(e.get()).map((e=>`-url:${e}`)).join(" ");if(t){const e=u.instance().createSetting("console.textFilter",""),r=e.get()?` ${e.get()}`:"";e.set(`${t}${r}`)}g(e)}updateVersionFrom26To27(){function e(e,t,r){const s=u.instance().createSetting(e,{}),n=s.get();t in n&&(n[r]=n[t],delete n[t],s.set(n))}e("panel-tabOrder","audits2","audits"),e("panel-closeableTabs","audits2","audits"),function(e,t,r){const s=u.instance().createSetting(e,"");s.get()===t&&s.set(r)}("panel-selectedTab","audits2","audits")}updateVersionFrom27To28(){const e=u.instance().createSetting("uiTheme","systemPreferred");"default"===e.get()&&e.set("systemPreferred")}updateVersionFrom28To29(){function e(e,t,r){const s=u.instance().createSetting(e,{}),n=s.get();t in n&&(n[r]=n[t],delete n[t],s.set(n))}e("panel-tabOrder","audits","lighthouse"),e("panel-closeableTabs","audits","lighthouse"),function(e,t,r){const s=u.instance().createSetting(e,"");s.get()===t&&s.set(r)}("panel-selectedTab","audits","lighthouse")}updateVersionFrom29To30(){const e=u.instance().createSetting("closeableTabs",{}),t=u.instance().createSetting("panel-closeableTabs",{}),r=u.instance().createSetting("drawer-view-closeableTabs",{}),s=t.get(),n=t.get(),i=Object.assign(n,s);e.set(i),g(t),g(r)}updateVersionFrom30To31(){g(u.instance().createSetting("recorder_recordings",[]))}migrateSettingsFromLocalStorage(){const e=new Set(["advancedSearchConfig","breakpoints","consoleHistory","domBreakpoints","eventListenerBreakpoints","fileSystemMapping","lastSelectedSourcesSidebarPaneTab","previouslyViewedFiles","savedURLs","watchExpressions","workspaceExcludedFolders","xhrBreakpoints"]);if(window.localStorage)for(const t in window.localStorage){if(e.has(t))continue;const r=window.localStorage[t];window.localStorage.removeItem(t),u.instance().globalStorage.set(t,r)}}clearBreakpointsWhenTooMany(e,t){e.get().length>t&&e.set([])}}var S,E;function b(e){return u.instance().moduleSetting(e)}function y(e){return u.instance().settingForTest(e)}function x(e){const t=i.Format;let r;const s=u.instance().moduleSetting("colorFormat").get();return r=s===t.Original?t.Original:s===t.RGB?t.RGB:s===t.HSL?t.HSL:s===t.HWB?t.HWB:s===t.HEX?e.detectHEXFormat():t.RGB,r}(E=S||(S={})).Synced="Synced",E.Global="Global",E.Local="Local",E.Session="Session"})),n.register("f4gCV",(function(t,r){e(t.exports,"registerSettingExtension",(()=>h)),e(t.exports,"getRegisteredSettings",(()=>p)),e(t.exports,"registerSettingsForTest",(()=>g)),e(t.exports,"resetSettings",(()=>d)),e(t.exports,"maybeRemoveSettingExtension",(()=>m)),e(t.exports,"SettingCategory",(()=>f)),e(t.exports,"getLocalizedSettingsCategory",(()=>y)),e(t.exports,"SettingType",(()=>E)),n("ixFnt");var s=n("ieEAB");n("9X2mn");var i=n("elz4U");const o={elements:"Elements",appearance:"Appearance",sources:"Sources",network:"Network",performance:"Performance",console:"Console",persistence:"Persistence",debugger:"Debugger",global:"Global",rendering:"Rendering",grid:"Grid",mobile:"Mobile",memory:"Memory",extension:"Extension",adorner:"Adorner",sync:"Sync"},a=s.registerUIStrings("core/common/SettingRegistration.ts",o),l=s.getLocalizedString.bind(void 0,a);let c=[];const u=new Set;function h(e){const t=e.settingName;if(u.has(t))throw new Error(`Duplicate setting name '${t}'`);u.add(t),c.push(e)}function p(){return c.filter((e=>i.Runtime.isDescriptorEnabled({experiment:e.experiment,condition:e.condition})))}function g(e,t=!1){if(0===c.length||t){c=e,u.clear();for(const t of e){const e=t.settingName;if(u.has(e))throw new Error(`Duplicate setting name '${e}'`);u.add(e)}}}function d(){c=[]}function m(e){const t=c.findIndex((t=>t.settingName===e));return!(t<0||!u.delete(e))&&(c.splice(t,1),!0)}var f,S,E,b;function y(e){switch(e){case f.ELEMENTS:return l(o.elements);case f.APPEARANCE:return l(o.appearance);case f.SOURCES:return l(o.sources);case f.NETWORK:return l(o.network);case f.PERFORMANCE:return l(o.performance);case f.CONSOLE:return l(o.console);case f.PERSISTENCE:return l(o.persistence);case f.DEBUGGER:return l(o.debugger);case f.GLOBAL:return l(o.global);case f.RENDERING:return l(o.rendering);case f.GRID:return l(o.grid);case f.MOBILE:return l(o.mobile);case f.EMULATION:return l(o.console);case f.MEMORY:return l(o.memory);case f.EXTENSIONS:return l(o.extension);case f.ADORNER:return l(o.adorner);case f.NONE:return"";case f.SYNC:return l(o.sync)}}(S=f||(f={})).NONE="",S.ELEMENTS="ELEMENTS",S.APPEARANCE="APPEARANCE",S.SOURCES="SOURCES",S.NETWORK="NETWORK",S.PERFORMANCE="PERFORMANCE",S.CONSOLE="CONSOLE",S.PERSISTENCE="PERSISTENCE",S.DEBUGGER="DEBUGGER",S.GLOBAL="GLOBAL",S.RENDERING="RENDERING",S.GRID="GRID",S.MOBILE="MOBILE",S.EMULATION="EMULATION",S.MEMORY="MEMORY",S.EXTENSIONS="EXTENSIONS",S.ADORNER="ADORNER",S.SYNC="SYNC",(b=E||(E={})).ARRAY="array",b.REGEX="regex",b.ENUM="enum",b.BOOLEAN="boolean"})),n.register("aWsSW",(function(t,r){e(t.exports,"SimpleHistoryManager",(()=>s));class s{#K;#J;#Q;#Z;constructor(e){this.#K=[],this.#J=-1,this.#Q=0,this.#Z=e}readOnlyLock(){++this.#Q}releaseReadOnlyLock(){--this.#Q}getPreviousValidIndex(){if(this.empty())return-1;let e=this.#J-1;for(;e>=0&&!this.#K[e].valid();)--e;return e<0?-1:e}getNextValidIndex(){let e=this.#J+1;for(;e<this.#K.length&&!this.#K[e].valid();)++e;return e>=this.#K.length?-1:e}readOnly(){return Boolean(this.#Q)}filterOut(e){if(this.readOnly())return;const t=[];let r=0;for(let s=0;s<this.#K.length;++s)e(this.#K[s])?s<=this.#J&&++r:t.push(this.#K[s]);this.#K=t,this.#J=Math.max(0,this.#J-r)}empty(){return!this.#K.length}active(){return this.empty()?null:this.#K[this.#J]}push(e){this.readOnly()||(this.empty()||this.#K.splice(this.#J+1),this.#K.push(e),this.#K.length>this.#Z&&this.#K.shift(),this.#J=this.#K.length-1)}canRollback(){return this.getPreviousValidIndex()>=0}canRollover(){return this.getNextValidIndex()>=0}rollback(){const e=this.getPreviousValidIndex();return-1!==e&&(this.readOnlyLock(),this.#J=e,this.#K[e].reveal(),this.releaseReadOnlyLock(),!0)}rollover(){const e=this.getNextValidIndex();return-1!==e&&(this.readOnlyLock(),this.#J=e,this.#K[e].reveal(),this.releaseReadOnlyLock(),!0)}}})),n.register("BpZ1m",(function(t,r){e(t.exports,"StringOutputStream",(()=>s));class s{#ee;constructor(){this.#ee=""}async write(e){this.#ee+=e}async close(){}data(){return this.#ee}}})),n.register("1VFIW",(function(e,t){n("8fICD")})),n.register("8fICD",(function(t,r){e(t.exports,"Trie",(()=>s));class s{#te;#re;#se;#ne;#ie;#oe;constructor(){this.#re=0,this.clear()}add(e){let t=this.#re;++this.#ie[this.#re];for(let r=0;r<e.length;++r){const s=e[r];let n=this.#se[t][s];n||(this.#oe.length?n=this.#oe.pop():(n=this.#te++,this.#ne.push(!1),this.#ie.push(0),this.#se.push(Object.create(null))),this.#se[t][s]=n),++this.#ie[n],t=n}this.#ne[t]=!0}remove(e){if(!this.has(e))return!1;let t=this.#re;--this.#ie[this.#re];for(let r=0;r<e.length;++r){const s=e[r],n=this.#se[t][s];--this.#ie[n]||(delete this.#se[t][s],this.#oe.push(n)),t=n}return this.#ne[t]=!1,!0}has(e){let t=this.#re;for(let r=0;r<e.length;++r)if(t=this.#se[t][e[r]],!t)return!1;return this.#ne[t]}words(e){e=e||"";let t=this.#re;for(let r=0;r<e.length;++r)if(t=this.#se[t][e[r]],!t)return[];const r=[];return this.dfs(t,e,r),r}dfs(e,t,r){this.#ne[e]&&r.push(t);const s=this.#se[e];for(const e in s)this.dfs(s[e],t+e,r)}longestPrefix(e,t){let r=this.#re,s=0;for(let n=0;n<e.length&&(r=this.#se[r][e[n]],r);++n)t&&!this.#ne[r]||(s=n+1);return e.substring(0,s)}clear(){this.#te=1,this.#re=0,this.#se=[Object.create(null)],this.#ne=[!1],this.#ie=[0],this.#oe=[]}}})),n.register("dDY8k",(function(t,r){e(t.exports,"Throttler",(()=>s));class s{#ae;#le;#ce;#ue;#he;#pe;#ge;#de;constructor(e){this.#ae=e,this.#le=!1,this.#ce=!1,this.#ue=null,this.#he=0,this.#pe=new Promise((e=>{this.#ge=e}))}processCompleted(){this.#he=this.getTime(),this.#le=!1,this.#ue&&this.innerSchedule(!1),this.processCompletedForTests()}processCompletedForTests(){}get process(){return this.#ue}onTimeout(){this.#de=void 0,this.#ce=!1,this.#le=!0,Promise.resolve().then(this.#ue).catch(console.error.bind(console)).then(this.processCompleted.bind(this)).then(this.#ge),this.#pe=new Promise((e=>{this.#ge=e})),this.#ue=null}schedule(e,t){this.#ue=e;const r=Boolean(this.#de)||this.#le,s=this.getTime()-this.#he>this.#ae,n=(t=Boolean(t)||!r&&s)&&!this.#ce;return this.#ce=this.#ce||t,this.innerSchedule(n),this.#pe}innerSchedule(e){if(this.#le)return;if(this.#de&&!e)return;this.#de&&this.clearTimeout(this.#de);const t=this.#ce?0:this.#ae;this.#de=this.setTimeout(this.onTimeout.bind(this),t)}clearTimeout(e){clearTimeout(e)}setTimeout(e,t){return window.setTimeout(e,t)}getTime(){return window.performance.now()}}})),n.register("3XJTj",(function(t,r){e(t.exports,"WasmDisassembly",(()=>i)),n("lz7WY");var s=n("03Tsr");class i{#me;#fe;constructor(e,t){this.#me=e,this.#fe=t}get lineNumbers(){return this.#me.length}bytecodeOffsetToLineNumber(e){return s.upperBound(this.#me,e,s.DEFAULT_COMPARATOR)-1}lineNumberToBytecodeOffset(e){return this.#me[e]}*nonBreakableLineNumbers(){let e=0,t=0;for(;e<this.lineNumbers;){if(t<this.#fe.length){if(this.lineNumberToBytecodeOffset(e)>=this.#fe[t].start){e=this.bytecodeOffsetToLineNumber(this.#fe[t++].end)+1;continue}}yield e++}}}})),n.register("1wxTh",(function(t,r){e(t.exports,"WorkerWrapper",(()=>s));class s{#Se;#Ee;constructor(e){this.#Se=new Promise((t=>{const r=new Worker(e,{type:"module"});r.onmessage=e=>{console.assert("workerReady"===e.data),r.onmessage=null,t(r)}}))}static fromURL(e){return new s(e)}postMessage(e){this.#Se.then((t=>{this.#Ee||t.postMessage(e)}))}dispose(){this.#Ee=!0,this.#Se.then((e=>e.terminate()))}terminate(){this.dispose()}set onmessage(e){this.#Se.then((t=>{t.onmessage=e}))}set onerror(e){this.#Se.then((t=>{t.onerror=e}))}}})),n("9Ygmk");
//# sourceMappingURL=weda_app.fd8f1811.js.map
