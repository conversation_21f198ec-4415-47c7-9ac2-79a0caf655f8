function e(e,n,i,t){Object.defineProperty(e,n,{get:i,set:t,enumerable:!0,configurable:!0})}var n=("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}).parcelRequire20b5;n.register("cWO9r",(function(i,t){e(i.exports,"DiffView",(()=>n("fV14z")));n("fV14z")})),n.register("fV14z",(function(i,t){e(i.exports,"buildDiffRows",(()=>u)),e(i.exports,"DiffView",(()=>m));var r=n("ixFnt"),o=n("90KXu"),d=n("dS5IF"),a=n("3T3mV"),l=n("kpUjp"),s=n("heYr7");const f={deletions:"Deletion:",additions:"Addition:",changesDiffViewer:"Changes diff viewer",SkippingDMatchingLines:"( … Skipping {PH1} matching lines … )"},c=r.i18n.registerUIStrings("ui/components/diff_view/DiffView.ts",f),h=r.i18n.getLocalizedString.bind(void 0,c);function u(e){let n=0,i=0;const t=[],r=[],d=[];for(let n=0;n<e.length;++n){const i=e[n];switch(i[0]){case o.Diff.Operation.Equal:d.push(...a(i[1],0===n,n===e.length-1)),t.push(...i[1]),r.push(...i[1]);break;case o.Diff.Operation.Insert:for(const e of i[1])d.push(s(e,"addition"));r.push(...i[1]);break;case o.Diff.Operation.Delete:if(t.push(...i[1]),e[n+1]&&e[n+1][0]===o.Diff.Operation.Insert)n++,d.push(...l(i[1].join("\n"),e[n][1].join("\n"))),r.push(...e[n][1]);else for(const e of i[1])d.push(s(e,"deletion"))}}return{originalLines:t,currentLines:r,rows:d};function a(e,t,r){const o=[];if(!t){for(let n=0;n<3&&n<e.length;n++)o.push(s(e[n],"equal"));e.length>7&&!r&&o.push(s(h(f.SkippingDMatchingLines,{PH1:e.length-6}),"spacer"))}if(!r){const r=Math.max(e.length-3-1,t?0:3);let d=e.length-3-1;t||(d-=3),d>0&&(i+=d,n+=d);for(let n=r;n<e.length;n++)o.push(s(e[n],"equal"))}return o}function l(e,n){const i=o.Diff.DiffWrapper.charDiff(e,n,!0),t=[s("","deletion")],r=[s("","addition")];for(const e of i){const n=e[1],i=e[0],d=i===o.Diff.Operation.Equal?"":"inner-diff",a=n.split("\n");for(let e=0;e<a.length;e++)e>0&&i!==o.Diff.Operation.Insert&&t.push(s("","deletion")),e>0&&i!==o.Diff.Operation.Delete&&r.push(s("","addition")),a[e]&&(i!==o.Diff.Operation.Insert&&t[t.length-1].tokens.push({text:a[e],className:d}),i!==o.Diff.Operation.Delete&&r[r.length-1].tokens.push({text:a[e],className:d}))}return t.concat(r)}function s(e,t){return"addition"===t&&n++,"deletion"===t&&i++,"equal"===t&&(i++,n++),{originalLineNumber:i,currentLineNumber:n,tokens:e?[{text:e,className:"inner-diff"}]:[],type:t}}}function g(e){const n=new Map;for(let i=0,t=0;t<e.length;t++)n.set(t+1,i),i+=e[t].length+1;return n}class p{originalHighlighter;originalMap;currentHighlighter;currentMap;constructor(e,n,i,t){this.originalHighlighter=e,this.originalMap=n,this.currentHighlighter=i,this.currentMap=t}#e(e){return d.html`
      <div class="diff-listing" aria-label=${h(f.changesDiffViewer)}>
        ${e.map((e=>this.#n(e)))}
      </div>`}#n(e){const n="equal"===e.type||"deletion"===e.type?String(e.originalLineNumber):"",i="equal"===e.type||"addition"===e.type?String(e.currentLineNumber):"";let t="",r="diff-line-marker",o=null;return"addition"===e.type?(t="+",r+=" diff-line-addition",o=d.html`<span class="diff-hidden-text">${h(f.additions)}</span>`):"deletion"===e.type&&(t="-",r+=" diff-line-deletion",o=d.html`<span class="diff-hidden-text">${h(f.deletions)}</span>`),d.html`
      <div class="diff-line-number" aria-hidden="true">${n}</div>
      <div class="diff-line-number" aria-hidden="true">${i}</div>
      <div class=${r} aria-hidden="true">${t}</div>
      <div class="diff-line-content diff-line-${e.type}" data-line-number=${i}>${o}${this.#i(e)}</div>`}#i(e){if("spacer"===e.type)return e.tokens.map((e=>d.html`${e.text}`));const[n,i]="deletion"===e.type?[this.originalHighlighter,this.originalMap.get(e.originalLineNumber)]:[this.currentHighlighter,this.currentMap.get(e.currentLineNumber)],t=[];let r=i;for(const i of e.tokens){const e=[];n.highlightRange(r,r+i.text.length,((n,i)=>{e.push(i?d.html`<span class=${i}>${n}</span>`:n)})),t.push(i.className?d.html`<span class=${i.className}>${e}</span>`:d.html`${e}`),r+=i.text.length}return t}static async render(e,n,i){const{originalLines:t,currentLines:r,rows:o}=u(e),l=new p(await a.CodeHighlighter.create(t.join("\n"),n),g(t),await a.CodeHighlighter.create(r.join("\n"),n),g(r));d.render(l.#e(o),i,{host:this})}}class m extends HTMLElement{static litTagName=d.literal`devtools-diff-view`;#t=this.attachShadow({mode:"open"});loaded;constructor(e){super(),this.#t.adoptedStyleSheets=[s.default,a.Style.default],this.loaded=e?p.render(e.diff,e.mimeType,this.#t):Promise.resolve()}set data(e){this.loaded=p.render(e.diff,e.mimeType,this.#t)}}l.CustomElements.defineComponent("devtools-diff-view",m)})),n.register("heYr7",(function(n,i){e(n.exports,"default",(()=>r));const t=new CSSStyleSheet;t.replaceSync("/*\n * Copyright 2021 The Chromium Authors. All rights reserved.\n * Use of this source code is governed by a BSD-style license that can be\n * found in the LICENSE file.\n */\n\n.diff-listing {\n  display: grid;\n  grid-template-columns: max-content max-content max-content auto;\n  font-family: var(--source-code-font-family);\n  font-size: var(--source-code-font-size);\n  white-space: pre;\n  line-height: 1.2em;\n}\n\n.diff-line-number {\n  color: var(--color-line-number);\n  padding: 0 3px 0 9px;\n  text-align: right;\n}\n\n.diff-line-marker {\n  border-right: 1px solid var(--color-details-hairline);\n  width: 20px;\n  text-align: center;\n}\n\n.diff-line-content {\n  padding: 0 4px;\n}\n\n.diff-line-marker-addition,\n.diff-line-addition {\n  --override-addition-background-color: hsl(144deg 55% 49% / 20%);\n\n  background-color: var(--override-addition-background-color);\n}\n\n.diff-line-marker-deletion,\n.diff-line-deletion {\n  --override-deletion-background-color: rgb(255 0 0 / 20%);\n\n  background-color: var(--override-deletion-background-color);\n}\n\n.diff-line-addition .inner-diff {\n  --override-addition-inner-diff-background-color: hsl(144deg 55% 49% / 30%);\n\n  background-color: var(--override-addition-inner-diff-background-color);\n}\n\n.diff-line-deletion .inner-diff {\n  --override-deletion-inner-diff-background-color: rgb(255 0 0 / 30%);\n\n  background-color: var(--override-deletion-inner-diff-background-color);\n}\n\n.diff-hidden-text {\n  display: inline-block;\n  width: 0;\n  overflow: hidden;\n}\n\n.diff-line-equal {\n  opacity: 50%;\n}\n\n.diff-line-spacer {\n  --override-spacer-background-color: rgb(0 0 255 / 10%);\n\n  text-align: center;\n  background-color: var(--override-spacer-background-color);\n}\n\n/*# sourceURL=diffView.css */\n");var r=t}));
//# sourceMappingURL=changes.5b77672a.js.map
