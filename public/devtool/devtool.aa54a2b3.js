function t(t,e,i,n){Object.defineProperty(t,e,{get:i,set:n,enumerable:!0,configurable:!0})}var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{},i={},n={},r=e.parcelRequire20b5;null==r&&((r=function(t){if(t in i)return i[t].exports;if(t in n){var e=n[t];delete n[t];var r={id:t,exports:{}};return i[t]=r,e.call(r.exports,r,r.exports),r.exports}var s=new Error("Cannot find module '"+t+"'");throw s.code="MODULE_NOT_FOUND",s}).register=function(t,e){n[t]=e},e.parcelRequire20b5=r),r.register("9PVZP",(function(t,e){r("27Lyk").register(JSON.parse('{"eBL4z":"weda_app.aa54a2b3.js","czFdi":"legacy.7ff9b6f9.js","bTjF1":"cpp.fe07f85a.js","eyPWV":"java.ec5a7103.js","lEcxu":"json.5b9e9fdf.js","f7Hx6":"markdown.1224516e.js","432R0":"php.ba30bf8d.js","iyjCs":"python.d082fa0c.js","kSe0D":"wast.7837cc6c.js","1Ivmr":"xml.34085274.js"}'))})),r.register("27Lyk",(function(e,i){var n,r;t(e.exports,"register",(()=>n),(t=>n=t)),t(e.exports,"resolve",(()=>r),(t=>r=t));var s={};n=function(t){for(var e=Object.keys(t),i=0;i<e.length;i++)s[e[i]]=t[e[i]]},r=function(t){var e=s[t];if(null==e)throw new Error("Could not resolve bundle with id "+t);return e}})),r.register("hPtJY",(function(t,e){var i,n,r=t.exports={};function s(){throw new Error("setTimeout has not been defined")}function o(){throw new Error("clearTimeout has not been defined")}function a(t){if(i===setTimeout)return setTimeout(t,0);if((i===s||!i)&&setTimeout)return i=setTimeout,setTimeout(t,0);try{return i(t,0)}catch(e){try{return i.call(null,t,0)}catch(e){return i.call(this,t,0)}}}!function(){try{i="function"==typeof setTimeout?setTimeout:s}catch(t){i=s}try{n="function"==typeof clearTimeout?clearTimeout:o}catch(t){n=o}}();var l,h=[],c=!1,u=-1;function O(){c&&l&&(c=!1,l.length?h=l.concat(h):u=-1,h.length&&d())}function d(){if(!c){var t=a(O);c=!0;for(var e=h.length;e;){for(l=h,h=[];++u<e;)l&&l[u].run();u=-1,e=h.length}l=null,c=!1,function(t){if(n===clearTimeout)return clearTimeout(t);if((n===o||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(t);try{n(t)}catch(e){try{return n.call(null,t)}catch(e){return n.call(this,t)}}}(t)}}function f(t,e){this.fun=t,this.array=e}function p(){}r.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var i=1;i<arguments.length;i++)e[i-1]=arguments[i];h.push(new f(t,e)),1!==h.length||c||a(d)},f.prototype.run=function(){this.fun.apply(null,this.array)},r.title="browser",r.browser=!0,r.env={},r.argv=[],r.version="",r.versions={},r.on=p,r.addListener=p,r.once=p,r.off=p,r.removeListener=p,r.removeAllListeners=p,r.emit=p,r.prependListener=p,r.prependOnceListener=p,r.listeners=function(t){return[]},r.binding=function(t){throw new Error("process.binding is not supported")},r.cwd=function(){return"/"},r.chdir=function(t){throw new Error("process.chdir is not supported")},r.umask=function(){return 0}})),r.register("dKQdF",(function(e,i){t(e.exports,"N",(()=>l)),t(e.exports,"g",(()=>u)),t(e.exports,"h",(()=>O)),t(e.exports,"T",(()=>p)),t(e.exports,"P",(()=>X)),t(e.exports,"p",(()=>A)),t(e.exports,"b5",(()=>B)),t(e.exports,"aJ",(()=>vt)),t(e.exports,"m",(()=>Xt)),t(e.exports,"aI",(()=>_t)),t(e.exports,"aN",(()=>jt)),t(e.exports,"n",(()=>Yt)),t(e.exports,"aG",(()=>Ut)),t(e.exports,"aC",(()=>ne)),t(e.exports,"aL",(()=>oe)),t(e.exports,"aH",(()=>Qe)),t(e.exports,"ay",(()=>Se)),t(e.exports,"az",(()=>ke)),t(e.exports,"b3",(()=>Mi)),t(e.exports,"aW",(()=>Yi)),t(e.exports,"b1",(()=>Qn)),t(e.exports,"aY",(()=>Os)),t(e.exports,"o",(()=>ws)),t(e.exports,"aX",(()=>Ps)),t(e.exports,"a_",(()=>zs)),t(e.exports,"aZ",(()=>Vs)),t(e.exports,"b0",(()=>Bs)),t(e.exports,"a$",(()=>Hs)),t(e.exports,"l",(()=>Js)),t(e.exports,"q",(()=>Ks)),t(e.exports,"j",(()=>to)),t(e.exports,"k",(()=>no)),t(e.exports,"a",(()=>io)),t(e.exports,"as",(()=>ro)),t(e.exports,"v",(()=>ao)),t(e.exports,"e",(()=>fo)),t(e.exports,"u",(()=>po)),t(e.exports,"au",(()=>go)),t(e.exports,"i",(()=>xo)),t(e.exports,"w",(()=>Po)),t(e.exports,"f",(()=>Wo)),t(e.exports,"c",(()=>Xo)),t(e.exports,"at",(()=>Co)),t(e.exports,"b",(()=>Zo)),t(e.exports,"d",(()=>_o)),t(e.exports,"s",(()=>qo)),t(e.exports,"ag",(()=>No)),t(e.exports,"ah",(()=>Bo)),t(e.exports,"t",(()=>ma)),t(e.exports,"aV",(()=>La)),t(e.exports,"aU",(()=>qa)),t(e.exports,"aT",(()=>Ya)),t(e.exports,"R",(()=>Va)),t(e.exports,"K",(()=>yl)),t(e.exports,"O",(()=>vl)),t(e.exports,"Q",(()=>Gl)),t(e.exports,"M",(()=>Il)),t(e.exports,"V",(()=>Fl)),t(e.exports,"X",(()=>ih)),t(e.exports,"Y",(()=>oh)),t(e.exports,"av",(()=>Qh)),t(e.exports,"$",(()=>Ch)),t(e.exports,"_",(()=>Ah)),t(e.exports,"Z",(()=>Vh)),t(e.exports,"a3",(()=>Uh)),t(e.exports,"a5",(()=>Jh)),t(e.exports,"a4",(()=>Kh)),t(e.exports,"a2",(()=>yc)),t(e.exports,"a1",(()=>wc)),t(e.exports,"a0",(()=>Sc)),t(e.exports,"a6",(()=>kc)),t(e.exports,"a7",(()=>$c)),t(e.exports,"ac",(()=>Xc)),t(e.exports,"ab",(()=>_c)),t(e.exports,"ae",(()=>Uc)),t(e.exports,"af",(()=>Fc)),t(e.exports,"aa",(()=>au)),t(e.exports,"a9",(()=>fu)),t(e.exports,"aj",(()=>vu)),t(e.exports,"ak",(()=>Du)),t(e.exports,"E",(()=>Nu)),t(e.exports,"C",(()=>hO)),t(e.exports,"L",(()=>cO)),t(e.exports,"ap",(()=>CO)),t(e.exports,"ar",(()=>bd)),t(e.exports,"r",(()=>Md)),t(e.exports,"aq",(()=>qd)),t(e.exports,"aw",(()=>Nd)),t(e.exports,"aB",(()=>sf)),t(e.exports,"aA",(()=>cf)),t(e.exports,"x",(()=>uf)),t(e.exports,"y",(()=>Of)),t(e.exports,"z",(()=>df)),t(e.exports,"A",(()=>ff)),t(e.exports,"B",(()=>pf)),t(e.exports,"D",(()=>mf)),t(e.exports,"F",(()=>gf)),t(e.exports,"G",(()=>Qf)),t(e.exports,"H",(()=>bf)),t(e.exports,"I",(()=>yf)),t(e.exports,"J",(()=>vf));var n=r("hPtJY");const s=1024;let o=0;class a{constructor(t,e){this.from=t,this.to=e}}class l{constructor(t={}){this.id=o++,this.perNode=!!t.perNode,this.deserialize=t.deserialize||(()=>{throw new Error("This node type doesn't define a deserialize function")})}add(t){if(this.perNode)throw new RangeError("Can't add per-node props to node types");return"function"!=typeof t&&(t=u.match(t)),e=>{let i=t(e);return void 0===i?null:[this,i]}}}l.closedBy=new l({deserialize:t=>t.split(" ")}),l.openedBy=new l({deserialize:t=>t.split(" ")}),l.group=new l({deserialize:t=>t.split(" ")}),l.contextHash=new l({perNode:!0}),l.lookAhead=new l({perNode:!0}),l.mounted=new l({perNode:!0});class h{constructor(t,e,i){this.tree=t,this.overlay=e,this.parser=i}}const c=Object.create(null);class u{constructor(t,e,i,n=0){this.name=t,this.props=e,this.id=i,this.flags=n}static define(t){let e=t.props&&t.props.length?Object.create(null):c,i=(t.top?1:0)|(t.skipped?2:0)|(t.error?4:0)|(null==t.name?8:0),n=new u(t.name||"",e,t.id,i);if(t.props)for(let i of t.props)if(Array.isArray(i)||(i=i(n)),i){if(i[0].perNode)throw new RangeError("Can't store a per-node prop on a node type");e[i[0].id]=i[1]}return n}prop(t){return this.props[t.id]}get isTop(){return(1&this.flags)>0}get isSkipped(){return(2&this.flags)>0}get isError(){return(4&this.flags)>0}get isAnonymous(){return(8&this.flags)>0}is(t){if("string"==typeof t){if(this.name==t)return!0;let e=this.prop(l.group);return!!e&&e.indexOf(t)>-1}return this.id==t}static match(t){let e=Object.create(null);for(let i in t)for(let n of i.split(" "))e[n]=t[i];return t=>{for(let i=t.prop(l.group),n=-1;n<(i?i.length:0);n++){let r=e[n<0?t.name:i[n]];if(r)return r}}}}u.none=new u("",Object.create(null),0,8);class O{constructor(t){this.types=t;for(let e=0;e<t.length;e++)if(t[e].id!=e)throw new RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...t){let e=[];for(let i of this.types){let n=null;for(let e of t){let t=e(i);t&&(n||(n=Object.assign({},i.props)),n[t[0].id]=t[1])}e.push(n?new u(i.name,n,i.id,i.flags):i)}return new O(e)}}const d=new WeakMap,f=new WeakMap;class p{constructor(t,e,i,n,r){if(this.type=t,this.children=e,this.positions=i,this.length=n,this.props=null,r&&r.length){this.props=Object.create(null);for(let[t,e]of r)this.props["number"==typeof t?t:t.id]=e}}toString(){let t=this.prop(l.mounted);if(t&&!t.overlay)return t.tree.toString();let e="";for(let t of this.children){let i=t.toString();i&&(e&&(e+=","),e+=i)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(e.length?"("+e+")":""):e}cursor(t,e=0){let i=null!=t&&d.get(this)||this.topNode,n=new k(i);return null!=t&&(n.moveTo(t,e),d.set(this,n._tree)),n}fullCursor(){return new k(this.topNode,1)}get topNode(){return new v(this,0,0,null)}resolve(t,e=0){let i=y(d.get(this)||this.topNode,t,e,!1);return d.set(this,i),i}resolveInner(t,e=0){let i=y(f.get(this)||this.topNode,t,e,!0);return f.set(this,i),i}iterate(t){let{enter:e,leave:i,from:n=0,to:r=this.length}=t;for(let t=this.cursor(),s=()=>t.node;;){let o=!1;if(t.from<=r&&t.to>=n&&(t.type.isAnonymous||!1!==e(t.type,t.from,t.to,s))){if(t.firstChild())continue;t.type.isAnonymous||(o=!0)}for(;o&&i&&i(t.type,t.from,t.to,s),o=t.type.isAnonymous,!t.nextSibling();){if(!t.parent())return;o=!0}}}prop(t){return t.perNode?this.props?this.props[t.id]:void 0:this.type.prop(t)}get propValues(){let t=[];if(this.props)for(let e in this.props)t.push([+e,this.props[e]]);return t}balance(t={}){return this.children.length<=8?this:R(u.none,this.children,this.positions,0,this.children.length,0,this.length,((t,e,i)=>new p(this.type,t,e,i,this.propValues)),t.makeTree||((t,e,i)=>new p(u.none,t,e,i)))}static build(t){return function(t){var e;let{buffer:i,nodeSet:n,maxBufferLength:r=s,reused:o=[],minRepeatType:a=n.types.length}=t,h=Array.isArray(i)?new m(i,i.length):i,c=n.types,u=0,O=0;function d(t,e,i,s,m){let{id:y,start:v,end:x,size:w}=h,S=O;for(;w<0;){if(h.next(),-1==w){let e=o[y];return i.push(e),void s.push(v-t)}if(-3==w)return void(u=y);if(-4==w)return void(O=y);throw new RangeError(`Unrecognized record size: ${w}`)}let k,$,T=c[y],P=v-t;if(x-v<=r&&($=function(t,e){let i=h.fork(),n=0,s=0,o=0,l=i.end-r,c={size:0,start:0,skip:0};t:for(let r=i.pos-t;i.pos>r;){let t=i.size;if(i.id==e&&t>=0){c.size=n,c.start=s,c.skip=o,o+=4,n+=4,i.next();continue}let h=i.pos-t;if(t<0||h<r||i.start<l)break;let u=i.id>=a?4:0,O=i.start;for(i.next();i.pos>h;){if(i.size<0){if(-3!=i.size)break t;u+=4}else i.id>=a&&(u+=4);i.next()}s=O,n+=t,o+=u}return(e<0||n==t)&&(c.size=n,c.start=s,c.skip=o),c.size>4?c:void 0}(h.pos-e,m))){let e=new Uint16Array($.size-$.skip),i=h.pos-$.size,r=e.length;for(;h.pos>i;)r=b($.start,e,r);k=new g(e,x-$.start,n),P=$.start-t}else{let t=h.pos-w;h.next();let e=[],i=[],n=y>=a?y:-1,s=0,o=x;for(;h.pos>t;)n>=0&&h.id==n&&h.size>=0?(h.end<=o-r&&(f(e,i,v,s,h.end,o,n,S),s=e.length,o=h.end),h.next()):d(v,t,e,i,n);if(n>=0&&s>0&&s<e.length&&f(e,i,v,s,v,o,n,S),e.reverse(),i.reverse(),n>-1&&s>0){let t=(W=T,(t,e,i)=>{let n,r,s=0,o=t.length-1;if(o>=0&&(n=t[o])instanceof p){if(!o&&n.type==W&&n.length==i)return n;(r=n.prop(l.lookAhead))&&(s=e[o]+n.length+r)}return Q(W,t,e,i,s)});k=R(T,e,i,0,e.length,0,x-v,t,t)}else k=Q(T,e,i,x-v,S-x)}var W;i.push(k),s.push(P)}function f(t,e,i,r,s,o,a,l){let h=[],c=[];for(;t.length>r;)h.push(t.pop()),c.push(e.pop()+i-s);t.push(Q(n.types[a],h,c,o-s,l-o)),e.push(s-i)}function Q(t,e,i,n,r=0,s){if(u){let t=[l.contextHash,u];s=s?[t].concat(s):[t]}if(r>25){let t=[l.lookAhead,r];s=s?[t].concat(s):[t]}return new p(t,e,i,n,s)}function b(t,e,i){let{id:n,start:r,end:s,size:o}=h;if(h.next(),o>=0&&n<a){let a=i;if(o>4){let n=h.pos-(o-4);for(;h.pos>n;)i=b(t,e,i)}e[--i]=a,e[--i]=s-t,e[--i]=r-t,e[--i]=n}else-3==o?u=n:-4==o&&(O=n);return i}let y=[],v=[];for(;h.pos>0;)d(t.start||0,t.bufferStart||0,y,v,-1);let x=null!==(e=t.length)&&void 0!==e?e:y.length?v[0]+y[0].length:0;return new p(c[t.topID],y.reverse(),v.reverse(),x)}(t)}}p.empty=new p(u.none,[],[],0);class m{constructor(t,e){this.buffer=t,this.index=e}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new m(this.buffer,this.index)}}class g{constructor(t,e,i){this.buffer=t,this.length=e,this.set=i}get type(){return u.none}toString(){let t=[];for(let e=0;e<this.buffer.length;)t.push(this.childString(e)),e=this.buffer[e+3];return t.join(",")}childString(t){let e=this.buffer[t],i=this.buffer[t+3],n=this.set.types[e],r=n.name;if(/\W/.test(r)&&!n.isError&&(r=JSON.stringify(r)),i==(t+=4))return r;let s=[];for(;t<i;)s.push(this.childString(t)),t=this.buffer[t+3];return r+"("+s.join(",")+")"}findChild(t,e,i,n,r){let{buffer:s}=this,o=-1;for(let a=t;a!=e&&!(Q(r,n,s[a+1],s[a+2])&&(o=a,i>0));a=s[a+3]);return o}slice(t,e,i,n){let r=this.buffer,s=new Uint16Array(e-t);for(let n=t,o=0;n<e;)s[o++]=r[n++],s[o++]=r[n++]-i,s[o++]=r[n++]-i,s[o++]=r[n++]-t;return new g(s,n-i,this.set)}}function Q(t,e,i,n){switch(t){case-2:return i<e;case-1:return n>=e&&i<e;case 0:return i<e&&n>e;case 1:return i<=e&&n>e;case 2:return n>e;case 4:return!0}}function b(t,e){let i=t.childBefore(e);for(;i;){let e=i.lastChild;if(!e||e.to!=i.to)break;e.type.isError&&e.from==e.to?(t=i,i=e.prevSibling):i=e}return t}function y(t,e,i,n){for(var r;t.from==t.to||(i<1?t.from>=e:t.from>e)||(i>-1?t.to<=e:t.to<e);){let e=!n&&t instanceof v&&t.index<0?null:t.parent;if(!e)return t;t=e}if(n)for(let n=t,s=n.parent;s;n=s,s=n.parent)n instanceof v&&n.index<0&&(null===(r=s.enter(e,i,!0))||void 0===r?void 0:r.from)!=n.from&&(t=s);for(;;){let r=t.enter(e,i,n);if(!r)return t;t=r}}class v{constructor(t,e,i,n){this.node=t,this._from=e,this.index=i,this._parent=n}get type(){return this.node.type}get name(){return this.node.type.name}get from(){return this._from}get to(){return this._from+this.node.length}nextChild(t,e,i,n,r=0){for(let s=this;;){for(let{children:o,positions:a}=s.node,h=e>0?o.length:-1;t!=h;t+=e){let h=o[t],c=a[t]+s._from;if(Q(n,i,c,c+h.length))if(h instanceof g){if(2&r)continue;let o=h.findChild(0,h.buffer.length,e,i-c,n);if(o>-1)return new S(new w(s,h,t,c),null,o)}else if(1&r||!h.type.isAnonymous||$(h)){let o;if(!(1&r)&&h.props&&(o=h.prop(l.mounted))&&!o.overlay)return new v(o.tree,c,t,s);let a=new v(h,c,t,s);return 1&r||!a.type.isAnonymous?a:a.nextChild(e<0?h.children.length-1:0,e,i,n)}}if(1&r||!s.type.isAnonymous)return null;if(t=s.index>=0?s.index+e:e<0?-1:s._parent.node.children.length,s=s._parent,!s)return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this.node.children.length-1,-1,0,4)}childAfter(t){return this.nextChild(0,1,t,2)}childBefore(t){return this.nextChild(this.node.children.length-1,-1,t,-2)}enter(t,e,i=!0,n=!0){let r;if(i&&(r=this.node.prop(l.mounted))&&r.overlay){let i=t-this.from;for(let{from:t,to:n}of r.overlay)if((e>0?t<=i:t<i)&&(e<0?n>=i:n>i))return new v(r.tree,r.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,t,e,n?0:2)}nextSignificantParent(){let t=this;for(;t.type.isAnonymous&&t._parent;)t=t._parent;return t}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}get cursor(){return new k(this)}get tree(){return this.node}toTree(){return this.node}resolve(t,e=0){return y(this,t,e,!1)}resolveInner(t,e=0){return y(this,t,e,!0)}enterUnfinishedNodesBefore(t){return b(this,t)}getChild(t,e=null,i=null){let n=x(this,t,e,i);return n.length?n[0]:null}getChildren(t,e=null,i=null){return x(this,t,e,i)}toString(){return this.node.toString()}}function x(t,e,i,n){let r=t.cursor,s=[];if(!r.firstChild())return s;if(null!=i)for(;!r.type.is(i);)if(!r.nextSibling())return s;for(;;){if(null!=n&&r.type.is(n))return s;if(r.type.is(e)&&s.push(r.node),!r.nextSibling())return null==n?s:[]}}class w{constructor(t,e,i,n){this.parent=t,this.buffer=e,this.index=i,this.start=n}}class S{constructor(t,e,i){this.context=t,this._parent=e,this.index=i,this.type=t.buffer.set.types[t.buffer.buffer[i]]}get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}child(t,e,i){let{buffer:n}=this.context,r=n.findChild(this.index+4,n.buffer[this.index+3],t,e-this.context.start,i);return r<0?null:new S(this.context,this,r)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(t){return this.child(1,t,2)}childBefore(t){return this.child(-1,t,-2)}enter(t,e,i,n=!0){if(!n)return null;let{buffer:r}=this.context,s=r.findChild(this.index+4,r.buffer[this.index+3],e>0?1:-1,t-this.context.start,e);return s<0?null:new S(this.context,this,s)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(t){return this._parent?null:this.context.parent.nextChild(this.context.index+t,t,0,4)}get nextSibling(){let{buffer:t}=this.context,e=t.buffer[this.index+3];return e<(this._parent?t.buffer[this._parent.index+3]:t.buffer.length)?new S(this.context,this._parent,e):this.externalSibling(1)}get prevSibling(){let{buffer:t}=this.context,e=this._parent?this._parent.index+4:0;return this.index==e?this.externalSibling(-1):new S(this.context,this._parent,t.findChild(e,this.index,-1,0,4))}get cursor(){return new k(this)}get tree(){return null}toTree(){let t=[],e=[],{buffer:i}=this.context,n=this.index+4,r=i.buffer[this.index+3];if(r>n){let s=i.buffer[this.index+1],o=i.buffer[this.index+2];t.push(i.slice(n,r,s,o)),e.push(0)}return new p(this.type,t,e,this.to-this.from)}resolve(t,e=0){return y(this,t,e,!1)}resolveInner(t,e=0){return y(this,t,e,!0)}enterUnfinishedNodesBefore(t){return b(this,t)}toString(){return this.context.buffer.childString(this.index)}getChild(t,e=null,i=null){let n=x(this,t,e,i);return n.length?n[0]:null}getChildren(t,e=null,i=null){return x(this,t,e,i)}}class k{constructor(t,e=0){if(this.mode=e,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,t instanceof v)this.yieldNode(t);else{this._tree=t.context.parent,this.buffer=t.context;for(let e=t._parent;e;e=e._parent)this.stack.unshift(e.index);this.bufferNode=t,this.yieldBuf(t.index)}}get name(){return this.type.name}yieldNode(t){return!!t&&(this._tree=t,this.type=t.type,this.from=t.from,this.to=t.to,!0)}yieldBuf(t,e){this.index=t;let{start:i,buffer:n}=this.buffer;return this.type=e||n.set.types[n.buffer[t]],this.from=i+n.buffer[t+1],this.to=i+n.buffer[t+2],!0}yield(t){return!!t&&(t instanceof v?(this.buffer=null,this.yieldNode(t)):(this.buffer=t.context,this.yieldBuf(t.index,t.type)))}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(t,e,i){if(!this.buffer)return this.yield(this._tree.nextChild(t<0?this._tree.node.children.length-1:0,t,e,i,this.mode));let{buffer:n}=this.buffer,r=n.findChild(this.index+4,n.buffer[this.index+3],t,e-this.buffer.start,i);return!(r<0)&&(this.stack.push(this.index),this.yieldBuf(r))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(t){return this.enterChild(1,t,2)}childBefore(t){return this.enterChild(-1,t,-2)}enter(t,e,i=!0,n=!0){return this.buffer?!!n&&this.enterChild(1,t,e):this.yield(this._tree.enter(t,e,i&&!(1&this.mode),n))}parent(){if(!this.buffer)return this.yieldNode(1&this.mode?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let t=1&this.mode?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(t)}sibling(t){if(!this.buffer)return!!this._tree._parent&&this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+t,t,0,4,this.mode));let{buffer:e}=this.buffer,i=this.stack.length-1;if(t<0){let t=i<0?0:this.stack[i]+4;if(this.index!=t)return this.yieldBuf(e.findChild(t,this.index,-1,0,4))}else{let t=e.buffer[this.index+3];if(t<(i<0?e.buffer.length:e.buffer[this.stack[i]+3]))return this.yieldBuf(t)}return i<0&&this.yield(this.buffer.parent.nextChild(this.buffer.index+t,t,0,4,this.mode))}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(t){let e,i,{buffer:n}=this;if(n){if(t>0){if(this.index<n.buffer.buffer.length)return!1}else for(let t=0;t<this.index;t++)if(n.buffer.buffer[t+3]<this.index)return!1;({index:e,parent:i}=n)}else({index:e,_parent:i}=this._tree);for(;i;({index:e,_parent:i}=i))if(e>-1)for(let n=e+t,r=t<0?-1:i.node.children.length;n!=r;n+=t){let t=i.node.children[n];if(1&this.mode||t instanceof g||!t.type.isAnonymous||$(t))return!1}return!0}move(t,e){if(e&&this.enterChild(t,0,4))return!0;for(;;){if(this.sibling(t))return!0;if(this.atLastNode(t)||!this.parent())return!1}}next(t=!0){return this.move(1,t)}prev(t=!0){return this.move(-1,t)}moveTo(t,e=0){for(;(this.from==this.to||(e<1?this.from>=t:this.from>t)||(e>-1?this.to<=t:this.to<t))&&this.parent(););for(;this.enterChild(1,t,e););return this}get node(){if(!this.buffer)return this._tree;let t=this.bufferNode,e=null,i=0;if(t&&t.context==this.buffer)t:for(let n=this.index,r=this.stack.length;r>=0;){for(let s=t;s;s=s._parent)if(s.index==n){if(n==this.index)return s;e=s,i=r+1;break t}n=this.stack[--r]}for(let t=i;t<this.stack.length;t++)e=new S(this.buffer,e,this.stack[t]);return this.bufferNode=new S(this.buffer,e,this.index)}get tree(){return this.buffer?null:this._tree.node}}function $(t){return t.children.some((t=>t instanceof g||!t.type.isAnonymous||$(t)))}const T=new WeakMap;function P(t,e){if(!t.isAnonymous||e instanceof g||e.type!=t)return 1;let i=T.get(e);if(null==i){i=1;for(let n of e.children){if(n.type!=t||!(n instanceof p)){i=1;break}i+=P(t,n)}T.set(e,i)}return i}function R(t,e,i,n,r,s,o,a,l){let h=0;for(let i=n;i<r;i++)h+=P(t,e[i]);let c=Math.ceil(1.5*h/8),u=[],O=[];return function e(i,n,r,o,a){for(let h=r;h<o;){let r=h,d=n[h],f=P(t,i[h]);for(h++;h<o;h++){let e=P(t,i[h]);if(f+e>=c)break;f+=e}if(h==r+1){if(f>c){let t=i[r];e(t.children,t.positions,0,t.children.length,n[r]+a);continue}u.push(i[r])}else{let e=n[h-1]+i[h-1].length-d;u.push(R(t,i,n,r,h,d,e,null,l))}O.push(d+a-s)}}(e,i,n,r,0),(a||l)(u,O,o)}class W{constructor(t,e,i,n,r=!1,s=!1){this.from=t,this.to=e,this.tree=i,this.offset=n,this.open=(r?1:0)|(s?2:0)}get openStart(){return(1&this.open)>0}get openEnd(){return(2&this.open)>0}static addTree(t,e=[],i=!1){let n=[new W(0,t.length,t,0,!1,i)];for(let i of e)i.to>t.length&&n.push(i);return n}static applyChanges(t,e,i=128){if(!e.length)return t;let n=[],r=1,s=t.length?t[0]:null;for(let o=0,a=0,l=0;;o++){let h=o<e.length?e[o]:null,c=h?h.fromA:1e9;if(c-a>=i)for(;s&&s.from<c;){let e=s;if(a>=e.from||c<=e.to||l){let t=Math.max(e.from,a)-l,i=Math.min(e.to,c)-l;e=t>=i?null:new W(t,i,e.tree,e.offset+l,o>0,!!h)}if(e&&n.push(e),s.to>c)break;s=r<t.length?t[r++]:null}if(!h)break;a=h.toA,l=h.toA-h.toB}return n}}class X{startParse(t,e,i){return"string"==typeof t&&(t=new C(t)),i=i?i.length?i.map((t=>new a(t.from,t.to))):[new a(0,0)]:[new a(0,t.length)],this.createParse(t,e||[],i)}parse(t,e,i){let n=this.startParse(t,e,i);for(;;){let t=n.advance();if(t)return t}}}class C{constructor(t){this.string=t}get length(){return this.string.length}chunk(t){return this.string.slice(t)}get lineChunks(){return!1}read(t,e){return this.string.slice(t,e)}}function A(t){return(e,i,n,r)=>new D(e,t,i,n,r)}class Z{constructor(t,e,i,n,r){this.parser=t,this.parse=e,this.overlay=i,this.target=n,this.ranges=r}}class _{constructor(t,e,i,n,r,s,o){this.parser=t,this.predicate=e,this.mounts=i,this.index=n,this.start=r,this.target=s,this.prev=o,this.depth=0,this.ranges=[]}}const L=new l({perNode:!0});class D{constructor(t,e,i,n,r){this.nest=e,this.input=i,this.fragments=n,this.ranges=r,this.inner=[],this.innerDone=0,this.baseTree=null,this.stoppedAt=null,this.baseParse=t}advance(){if(this.baseParse){let t=this.baseParse.advance();if(!t)return null;if(this.baseParse=null,this.baseTree=t,this.startInner(),null!=this.stoppedAt)for(let t of this.inner)t.parse.stopAt(this.stoppedAt)}if(this.innerDone==this.inner.length){let t=this.baseTree;return null!=this.stoppedAt&&(t=new p(t.type,t.children,t.positions,t.length,t.propValues.concat([[L,this.stoppedAt]]))),t}let t=this.inner[this.innerDone],e=t.parse.advance();if(e){this.innerDone++;let i=Object.assign(Object.create(null),t.target.props);i[l.mounted.id]=new h(e,t.overlay,t.parser),t.target.props=i}return null}get parsedPos(){if(this.baseParse)return 0;let t=this.input.length;for(let e=this.innerDone;e<this.inner.length;e++)this.inner[e].ranges[0].from<t&&(t=Math.min(t,this.inner[e].parse.parsedPos));return t}stopAt(t){if(this.stoppedAt=t,this.baseParse)this.baseParse.stopAt(t);else for(let e=this.innerDone;e<this.inner.length;e++)this.inner[e].parse.stopAt(t)}startInner(){let t=new q(this.fragments),e=null,i=null,n=new k(new v(this.baseTree,this.ranges[0].from,0,null),1);t:for(let r,s;null==this.stoppedAt||n.from<this.stoppedAt;){let o,l=!0;if(t.hasNode(n)){if(e){let t=e.mounts.find((t=>t.frag.from<=n.from&&t.frag.to>=n.to&&t.mount.overlay));if(t)for(let i of t.mount.overlay){let r=i.from+t.pos,s=i.to+t.pos;r>=n.from&&s<=n.to&&e.ranges.push({from:r,to:s})}}l=!1}else if(i&&(s=z(i.ranges,n.from,n.to)))l=2!=s;else if(!n.type.isAnonymous&&n.from<n.to&&(r=this.nest(n,this.input))){n.tree||M(n);let s=t.findMounts(n.from,r.parser);if("function"==typeof r.overlay)e=new _(r.parser,r.overlay,s,this.inner.length,n.from,n.tree,e);else{let t=Y(this.ranges,r.overlay||[new a(n.from,n.to)]);t.length&&this.inner.push(new Z(r.parser,r.parser.startParse(this.input,U(s,t),t),r.overlay?r.overlay.map((t=>new a(t.from-n.from,t.to-n.from))):null,n.tree,t)),r.overlay?t.length&&(i={ranges:t,depth:0,prev:i}):l=!1}}else e&&(o=e.predicate(n))&&(!0===o&&(o=new a(n.from,n.to)),o.from<o.to&&e.ranges.push(o));if(l&&n.firstChild())e&&e.depth++,i&&i.depth++;else for(;!n.nextSibling();){if(!n.parent())break t;if(e&&!--e.depth){let t=Y(this.ranges,e.ranges);t.length&&this.inner.splice(e.index,0,new Z(e.parser,e.parser.startParse(this.input,U(e.mounts,t),t),e.ranges.map((t=>new a(t.from-e.start,t.to-e.start))),e.target,t)),e=e.prev}i&&!--i.depth&&(i=i.prev)}}}}function z(t,e,i){for(let n of t){if(n.from>=i)break;if(n.to>e)return n.from<=e&&n.to>=i?2:1}return 0}function E(t,e,i,n,r,s){if(e<i){let o=t.buffer[e+1],a=t.buffer[i-2];n.push(t.slice(e,i,o,a)),r.push(o-s)}}function M(t){let{node:e}=t,i=0;do{t.parent(),i++}while(!t.tree);let n=0,r=t.tree,s=0;for(;s=r.positions[n]+t.from,!(s<=e.from&&s+r.children[n].length>=e.to);n++);let o=r.children[n],a=o.buffer;r.children[n]=function t(i,n,r,l,h){let c=i;for(;a[c+2]+s<=e.from;)c=a[c+3];let u=[],O=[];E(o,i,c,u,O,l);let d=a[c+1],f=a[c+2],m=d+s==e.from&&f+s==e.to&&a[c]==e.type.id;return u.push(m?e.toTree():t(c+4,a[c+3],o.set.types[a[c]],d,f-d)),O.push(d-l),E(o,a[c+3],n,u,O,l),new p(r,u,O,h)}(0,a.length,u.none,0,o.length);for(let n=0;n<=i;n++)t.childAfter(e.from)}class j{constructor(t,e){this.offset=e,this.done=!1,this.cursor=t.fullCursor()}moveTo(t){let{cursor:e}=this,i=t-this.offset;for(;!this.done&&e.from<i;)e.to>=t&&e.enter(i,1,!1,!1)||e.next(!1)||(this.done=!0)}hasNode(t){if(this.moveTo(t.from),!this.done&&this.cursor.from+this.offset==t.from&&this.cursor.tree)for(let e=this.cursor.tree;;){if(e==t.tree)return!0;if(!(e.children.length&&0==e.positions[0]&&e.children[0]instanceof p))break;e=e.children[0]}return!1}}class q{constructor(t){var e;if(this.fragments=t,this.curTo=0,this.fragI=0,t.length){let i=this.curFrag=t[0];this.curTo=null!==(e=i.tree.prop(L))&&void 0!==e?e:i.to,this.inner=new j(i.tree,-i.offset)}else this.curFrag=this.inner=null}hasNode(t){for(;this.curFrag&&t.from>=this.curTo;)this.nextFrag();return this.curFrag&&this.curFrag.from<=t.from&&this.curTo>=t.to&&this.inner.hasNode(t)}nextFrag(){var t;if(this.fragI++,this.fragI==this.fragments.length)this.curFrag=this.inner=null;else{let e=this.curFrag=this.fragments[this.fragI];this.curTo=null!==(t=e.tree.prop(L))&&void 0!==t?t:e.to,this.inner=new j(e.tree,-e.offset)}}findMounts(t,e){var i;let n=[];if(this.inner){this.inner.cursor.moveTo(t,1);for(let t=this.inner.cursor.node;t;t=t.parent){let r=null===(i=t.tree)||void 0===i?void 0:i.prop(l.mounted);if(r&&r.parser==e)for(let e=this.fragI;e<this.fragments.length;e++){let i=this.fragments[e];if(i.from>=t.to)break;i.tree==this.curFrag.tree&&n.push({frag:i,pos:t.from-i.offset,mount:r})}}}return n}}function Y(t,e){let i=null,n=e;for(let r=1,s=0;r<t.length;r++){let o=t[r-1].to,l=t[r].from;for(;s<n.length;s++){let t=n[s];if(t.from>=l)break;t.to<=o||(i||(n=i=e.slice()),t.from<o?(i[s]=new a(t.from,o),t.to>l&&i.splice(s+1,0,new a(l,t.to))):t.to>l?i[s--]=new a(l,t.to):i.splice(s--,1))}}return n}function V(t,e,i,n){let r=0,s=0,o=!1,l=!1,h=-1e9,c=[];for(;;){let u=r==t.length?1e9:o?t[r].to:t[r].from,O=s==e.length?1e9:l?e[s].to:e[s].from;if(o!=l){let t=Math.max(h,i),e=Math.min(u,O,n);t<e&&c.push(new a(t,e))}if(h=Math.min(u,O),1e9==h)break;u==h&&(o?(o=!1,r++):o=!0),O==h&&(l?(l=!1,s++):l=!0)}return c}function U(t,e){let i=[];for(let{pos:n,mount:r,frag:s}of t){let t=n+(r.overlay?r.overlay[0].from:0),o=t+r.tree.length,l=Math.max(s.from,t),h=Math.min(s.to,o);if(r.overlay){let o=V(e,r.overlay.map((t=>new a(t.from+n,t.to+n))),l,h);for(let e=0,n=l;;e++){let a=e==o.length,l=a?h:o[e].from;if(l>n&&i.push(new W(n,l,r.tree,-t,s.from>=n,s.to<=l)),a)break;n=o[e].to}}else i.push(new W(l,h,r.tree,-t,s.from>=t,s.to<=o))}return i}const G="undefined"==typeof Symbol?"__ͼ":Symbol.for("ͼ"),I="undefined"==typeof Symbol?"__styleSet"+Math.floor(1e8*Math.random()):Symbol("styleSet"),N="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:{};class B{constructor(t,e){this.rules=[];let{finish:i}=e||{};function n(t){return/^@/.test(t)?[t]:t.split(/,\s*/)}function r(t,e,s,o){let a=[],l=/^@(\w+)\b/.exec(t[0]),h=l&&"keyframes"==l[1];if(l&&null==e)return s.push(t[0]+";");for(let i in e){let o=e[i];if(/&/.test(i))r(i.split(/,\s*/).map((e=>t.map((t=>e.replace(/&/,t))))).reduce(((t,e)=>t.concat(e))),o,s);else if(o&&"object"==typeof o){if(!l)throw new RangeError("The value of a property ("+i+") should be a primitive value.");r(n(i),o,a,h)}else null!=o&&a.push(i.replace(/_.*/,"").replace(/[A-Z]/g,(t=>"-"+t.toLowerCase()))+": "+o+";")}(a.length||h)&&s.push((!i||l||o?t:t.map(i)).join(", ")+" {"+a.join(" ")+"}")}for(let e in t)r(n(e),t[e],this.rules)}getRules(){return this.rules.join("\n")}static newName(){let t=N[G]||1;return N[G]=t+1,"ͼ"+t.toString(36)}static mount(t,e){(t[I]||new H(t)).mount(Array.isArray(e)?e:[e])}}let F=null;class H{constructor(t){if(!t.head&&t.adoptedStyleSheets&&"undefined"!=typeof CSSStyleSheet){if(F)return t.adoptedStyleSheets=[F.sheet].concat(t.adoptedStyleSheets),t[I]=F;this.sheet=new CSSStyleSheet,t.adoptedStyleSheets=[this.sheet].concat(t.adoptedStyleSheets),F=this}else{this.styleTag=(t.ownerDocument||t).createElement("style");let e=t.head||t;e.insertBefore(this.styleTag,e.firstChild)}this.modules=[],t[I]=this}mount(t){let e=this.sheet,i=0,n=0;for(let r=0;r<t.length;r++){let s=t[r],o=this.modules.indexOf(s);if(o<n&&o>-1&&(this.modules.splice(o,1),n--,o=-1),-1==o){if(this.modules.splice(n++,0,s),e)for(let t=0;t<s.rules.length;t++)e.insertRule(s.rules[t],i++)}else{for(;n<o;)i+=this.modules[n++].rules.length;i+=s.rules.length,n++}}if(!e){let t="";for(let e=0;e<this.modules.length;e++)t+=this.modules[e].getRules()+"\n";this.styleTag.textContent=t}}}let J="lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o".split(",").map((t=>t?parseInt(t,36):1));for(let t=1;t<J.length;t++)J[t]+=J[t-1];function K(t){for(let e=1;e<J.length;e+=2)if(J[e]>t)return J[e-1]<=t;return!1}function tt(t){return t>=127462&&t<=127487}function et(t,e,i=!0,n=!0){return(i?it:nt)(t,e,n)}function it(t,e,i){if(e==t.length)return e;e&&rt(t.charCodeAt(e))&&st(t.charCodeAt(e-1))&&e--;let n=ot(t,e);for(e+=lt(n);e<t.length;){let r=ot(t,e);if(8205==n||8205==r||i&&K(r))e+=lt(r),n=r;else{if(!tt(r))break;{let i=0,n=e-2;for(;n>=0&&tt(ot(t,n));)i++,n-=2;if(i%2==0)break;e+=2}}}return e}function nt(t,e,i){for(;e>0;){let n=it(t,e-2,i);if(n<e)return n;e--}return 0}function rt(t){return t>=56320&&t<57344}function st(t){return t>=55296&&t<56320}function ot(t,e){let i=t.charCodeAt(e);if(!st(i)||e+1==t.length)return i;let n=t.charCodeAt(e+1);return rt(n)?n-56320+(i-55296<<10)+65536:i}function at(t){return t<=65535?String.fromCharCode(t):(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t)))}function lt(t){return t<65536?1:2}function ht(t,e,i=t.length){let n=0;for(let r=0;r<i;)9==t.charCodeAt(r)?(n+=e-n%e,r++):(n++,r=et(t,r));return n}class ct{constructor(){}lineAt(t){if(t<0||t>this.length)throw new RangeError(`Invalid position ${t} in document of length ${this.length}`);return this.lineInner(t,!1,1,0)}line(t){if(t<1||t>this.lines)throw new RangeError(`Invalid line number ${t} in ${this.lines}-line document`);return this.lineInner(t,!0,1,0)}replace(t,e,i){let n=[];return this.decompose(0,t,n,2),i.length&&i.decompose(0,i.length,n,3),this.decompose(e,this.length,n,1),Ot.from(n,this.length-(e-t)+i.length)}append(t){return this.replace(this.length,this.length,t)}slice(t,e=this.length){let i=[];return this.decompose(t,e,i,0),Ot.from(i,e-t)}eq(t){if(t==this)return!0;if(t.length!=this.length||t.lines!=this.lines)return!1;let e=this.scanIdentical(t,1),i=this.length-this.scanIdentical(t,-1),n=new pt(this),r=new pt(t);for(let t=e,s=e;;){if(n.next(t),r.next(t),t=0,n.lineBreak!=r.lineBreak||n.done!=r.done||n.value!=r.value)return!1;if(s+=n.value.length,n.done||s>=i)return!0}}iter(t=1){return new pt(this,t)}iterRange(t,e=this.length){return new mt(this,t,e)}iterLines(t,e){let i;if(null==t)i=this.iter();else{null==e&&(e=this.lines+1);let n=this.line(t).from;i=this.iterRange(n,Math.max(n,e==this.lines+1?this.length:e<=1?0:this.line(e-1).to))}return new gt(i)}toString(){return this.sliceString(0)}toJSON(){let t=[];return this.flatten(t),t}static of(t){if(0==t.length)throw new RangeError("A document must have at least one line");return 1!=t.length||t[0]?t.length<=32?new ut(t):Ot.from(ut.split(t,[])):ct.empty}}class ut extends ct{constructor(t,e=function(t){let e=-1;for(let i of t)e+=i.length+1;return e}(t)){super(),this.text=t,this.length=e}get lines(){return this.text.length}get children(){return null}lineInner(t,e,i,n){for(let r=0;;r++){let s=this.text[r],o=n+s.length;if((e?i:o)>=t)return new Qt(n,o,i,s);n=o+1,i++}}decompose(t,e,i,n){let r=t<=0&&e>=this.length?this:new ut(ft(this.text,t,e),Math.min(e,this.length)-Math.max(0,t));if(1&n){let t=i.pop(),e=dt(r.text,t.text.slice(),0,r.length);if(e.length<=32)i.push(new ut(e,t.length+r.length));else{let t=e.length>>1;i.push(new ut(e.slice(0,t)),new ut(e.slice(t)))}}else i.push(r)}replace(t,e,i){if(!(i instanceof ut))return super.replace(t,e,i);let n=dt(this.text,dt(i.text,ft(this.text,0,t)),e),r=this.length+i.length-(e-t);return n.length<=32?new ut(n,r):Ot.from(ut.split(n,[]),r)}sliceString(t,e=this.length,i="\n"){let n="";for(let r=0,s=0;r<=e&&s<this.text.length;s++){let o=this.text[s],a=r+o.length;r>t&&s&&(n+=i),t<a&&e>r&&(n+=o.slice(Math.max(0,t-r),e-r)),r=a+1}return n}flatten(t){for(let e of this.text)t.push(e)}scanIdentical(){return 0}static split(t,e){let i=[],n=-1;for(let r of t)i.push(r),n+=r.length+1,32==i.length&&(e.push(new ut(i,n)),i=[],n=-1);return n>-1&&e.push(new ut(i,n)),e}}class Ot extends ct{constructor(t,e){super(),this.children=t,this.length=e,this.lines=0;for(let e of t)this.lines+=e.lines}lineInner(t,e,i,n){for(let r=0;;r++){let s=this.children[r],o=n+s.length,a=i+s.lines-1;if((e?a:o)>=t)return s.lineInner(t,e,i,n);n=o+1,i=a+1}}decompose(t,e,i,n){for(let r=0,s=0;s<=e&&r<this.children.length;r++){let o=this.children[r],a=s+o.length;if(t<=a&&e>=s){let r=n&((s<=t?1:0)|(a>=e?2:0));s>=t&&a<=e&&!r?i.push(o):o.decompose(t-s,e-s,i,r)}s=a+1}}replace(t,e,i){if(i.lines<this.lines)for(let n=0,r=0;n<this.children.length;n++){let s=this.children[n],o=r+s.length;if(t>=r&&e<=o){let a=s.replace(t-r,e-r,i),l=this.lines-s.lines+a.lines;if(a.lines<l>>4&&a.lines>l>>6){let r=this.children.slice();return r[n]=a,new Ot(r,this.length-(e-t)+i.length)}return super.replace(r,o,a)}r=o+1}return super.replace(t,e,i)}sliceString(t,e=this.length,i="\n"){let n="";for(let r=0,s=0;r<this.children.length&&s<=e;r++){let o=this.children[r],a=s+o.length;s>t&&r&&(n+=i),t<a&&e>s&&(n+=o.sliceString(t-s,e-s,i)),s=a+1}return n}flatten(t){for(let e of this.children)e.flatten(t)}scanIdentical(t,e){if(!(t instanceof Ot))return 0;let i=0,[n,r,s,o]=e>0?[0,0,this.children.length,t.children.length]:[this.children.length-1,t.children.length-1,-1,-1];for(;;n+=e,r+=e){if(n==s||r==o)return i;let a=this.children[n],l=t.children[r];if(a!=l)return i+a.scanIdentical(l,e);i+=a.length+1}}static from(t,e=t.reduce(((t,e)=>t+e.length+1),-1)){let i=0;for(let e of t)i+=e.lines;if(i<32){let i=[];for(let e of t)e.flatten(i);return new ut(i,e)}let n=Math.max(32,i>>5),r=n<<1,s=n>>1,o=[],a=0,l=-1,h=[];function c(t){let e;if(t.lines>r&&t instanceof Ot)for(let e of t.children)c(e);else t.lines>s&&(a>s||!a)?(u(),o.push(t)):t instanceof ut&&a&&(e=h[h.length-1])instanceof ut&&t.lines+e.lines<=32?(a+=t.lines,l+=t.length+1,h[h.length-1]=new ut(e.text.concat(t.text),e.length+1+t.length)):(a+t.lines>n&&u(),a+=t.lines,l+=t.length+1,h.push(t))}function u(){0!=a&&(o.push(1==h.length?h[0]:Ot.from(h,l)),l=-1,a=h.length=0)}for(let e of t)c(e);return u(),1==o.length?o[0]:new Ot(o,e)}}function dt(t,e,i=0,n=1e9){for(let r=0,s=0,o=!0;s<t.length&&r<=n;s++){let a=t[s],l=r+a.length;l>=i&&(l>n&&(a=a.slice(0,n-r)),r<i&&(a=a.slice(i-r)),o?(e[e.length-1]+=a,o=!1):e.push(a)),r=l+1}return e}function ft(t,e,i){return dt(t,[""],e,i)}ct.empty=new ut([""],0);class pt{constructor(t,e=1){this.dir=e,this.done=!1,this.lineBreak=!1,this.value="",this.nodes=[t],this.offsets=[e>0?1:(t instanceof ut?t.text.length:t.children.length)<<1]}nextInner(t,e){for(this.done=this.lineBreak=!1;;){let i=this.nodes.length-1,n=this.nodes[i],r=this.offsets[i],s=r>>1,o=n instanceof ut?n.text.length:n.children.length;if(s==(e>0?o:0)){if(0==i)return this.done=!0,this.value="",this;e>0&&this.offsets[i-1]++,this.nodes.pop(),this.offsets.pop()}else if((1&r)==(e>0?0:1)){if(this.offsets[i]+=e,0==t)return this.lineBreak=!0,this.value="\n",this;t--}else if(n instanceof ut){let r=n.text[s+(e<0?-1:0)];if(this.offsets[i]+=e,r.length>Math.max(0,t))return this.value=0==t?r:e>0?r.slice(t):r.slice(0,r.length-t),this;t-=r.length}else{let r=n.children[s+(e<0?-1:0)];t>r.length?(t-=r.length,this.offsets[i]+=e):(e<0&&this.offsets[i]--,this.nodes.push(r),this.offsets.push(e>0?1:(r instanceof ut?r.text.length:r.children.length)<<1))}}}next(t=0){return t<0&&(this.nextInner(-t,-this.dir),t=this.value.length),this.nextInner(t,this.dir)}}class mt{constructor(t,e,i){this.value="",this.done=!1,this.cursor=new pt(t,e>i?-1:1),this.pos=e>i?t.length:0,this.from=Math.min(e,i),this.to=Math.max(e,i)}nextInner(t,e){if(e<0?this.pos<=this.from:this.pos>=this.to)return this.value="",this.done=!0,this;t+=Math.max(0,e<0?this.pos-this.to:this.from-this.pos);let i=e<0?this.pos-this.from:this.to-this.pos;t>i&&(t=i),i-=t;let{value:n}=this.cursor.next(t);return this.pos+=(n.length+t)*e,this.value=n.length<=i?n:e<0?n.slice(n.length-i):n.slice(0,i),this.done=!this.value,this}next(t=0){return t<0?t=Math.max(t,this.from-this.pos):t>0&&(t=Math.min(t,this.to-this.pos)),this.nextInner(t,this.cursor.dir)}get lineBreak(){return this.cursor.lineBreak&&""!=this.value}}class gt{constructor(t){this.inner=t,this.afterBreak=!0,this.value="",this.done=!1}next(t=0){let{done:e,lineBreak:i,value:n}=this.inner.next(t);return e?(this.done=!0,this.value=""):i?this.afterBreak?this.value="":(this.afterBreak=!0,this.next()):(this.value=n,this.afterBreak=!1),this}get lineBreak(){return!1}}"undefined"!=typeof Symbol&&(ct.prototype[Symbol.iterator]=function(){return this.iter()},pt.prototype[Symbol.iterator]=mt.prototype[Symbol.iterator]=gt.prototype[Symbol.iterator]=function(){return this});class Qt{constructor(t,e,i,n){this.from=t,this.to=e,this.number=i,this.text=n}get length(){return this.to-this.from}}const bt=/\r\n?|\n/;var yt,vt=((yt=vt||(vt={}))[yt.Simple=0]="Simple",yt[yt.TrackDel=1]="TrackDel",yt[yt.TrackBefore=2]="TrackBefore",yt[yt.TrackAfter=3]="TrackAfter",yt);class xt{constructor(t){this.sections=t}get length(){let t=0;for(let e=0;e<this.sections.length;e+=2)t+=this.sections[e];return t}get newLength(){let t=0;for(let e=0;e<this.sections.length;e+=2){let i=this.sections[e+1];t+=i<0?this.sections[e]:i}return t}get empty(){return 0==this.sections.length||2==this.sections.length&&this.sections[1]<0}iterGaps(t){for(let e=0,i=0,n=0;e<this.sections.length;){let r=this.sections[e++],s=this.sections[e++];s<0?(t(i,n,r),n+=r):n+=s,i+=r}}iterChangedRanges(t,e=!1){$t(this,t,e)}get invertedDesc(){let t=[];for(let e=0;e<this.sections.length;){let i=this.sections[e++],n=this.sections[e++];n<0?t.push(i,n):t.push(n,i)}return new xt(t)}composeDesc(t){return this.empty?t:t.empty?this:Pt(this,t)}mapDesc(t,e=!1){return t.empty?this:Tt(this,t,e)}mapPos(t,e=-1,i=vt.Simple){let n=0,r=0;for(let s=0;s<this.sections.length;){let o=this.sections[s++],a=this.sections[s++],l=n+o;if(a<0){if(l>t)return r+(t-n);r+=o}else{if(i!=vt.Simple&&l>=t&&(i==vt.TrackDel&&n<t&&l>t||i==vt.TrackBefore&&n<t||i==vt.TrackAfter&&l>t))return null;if(l>t||l==t&&e<0&&!o)return t==n||e<0?r:r+a;r+=a}n=l}if(t>n)throw new RangeError(`Position ${t} is out of range for changeset of length ${n}`);return r}touchesRange(t,e=t){for(let i=0,n=0;i<this.sections.length&&n<=e;){let r=n+this.sections[i++];if(this.sections[i++]>=0&&n<=e&&r>=t)return!(n<t&&r>e)||"cover";n=r}return!1}toString(){let t="";for(let e=0;e<this.sections.length;){let i=this.sections[e++],n=this.sections[e++];t+=(t?" ":"")+i+(n>=0?":"+n:"")}return t}toJSON(){return this.sections}static fromJSON(t){if(!Array.isArray(t)||t.length%2||t.some((t=>"number"!=typeof t)))throw new RangeError("Invalid JSON representation of ChangeDesc");return new xt(t)}}class wt extends xt{constructor(t,e){super(t),this.inserted=e}apply(t){if(this.length!=t.length)throw new RangeError("Applying change set to a document with the wrong length");return $t(this,((e,i,n,r,s)=>t=t.replace(n,n+(i-e),s)),!1),t}mapDesc(t,e=!1){return Tt(this,t,e,!0)}invert(t){let e=this.sections.slice(),i=[];for(let n=0,r=0;n<e.length;n+=2){let s=e[n],o=e[n+1];if(o>=0){e[n]=o,e[n+1]=s;let a=n>>1;for(;i.length<a;)i.push(ct.empty);i.push(s?t.slice(r,r+s):ct.empty)}r+=s}return new wt(e,i)}compose(t){return this.empty?t:t.empty?this:Pt(this,t,!0)}map(t,e=!1){return t.empty?this:Tt(this,t,e,!0)}iterChanges(t,e=!1){$t(this,t,e)}get desc(){return new xt(this.sections)}filter(t){let e=[],i=[],n=[],r=new Rt(this);t:for(let s=0,o=0;;){let a=s==t.length?1e9:t[s++];for(;o<a||o==a&&0==r.len;){if(r.done)break t;let t=Math.min(r.len,a-o);St(n,t,-1);let s=-1==r.ins?-1:0==r.off?r.ins:0;St(e,t,s),s>0&&kt(i,e,r.text),r.forward(t),o+=t}let l=t[s++];for(;o<l;){if(r.done)break t;let t=Math.min(r.len,l-o);St(e,t,-1),St(n,t,-1==r.ins?-1:0==r.off?r.ins:0),r.forward(t),o+=t}}return{changes:new wt(e,i),filtered:new xt(n)}}toJSON(){let t=[];for(let e=0;e<this.sections.length;e+=2){let i=this.sections[e],n=this.sections[e+1];n<0?t.push(i):0==n?t.push([i]):t.push([i].concat(this.inserted[e>>1].toJSON()))}return t}static of(t,e,i){let n=[],r=[],s=0,o=null;function a(t=!1){if(!t&&!n.length)return;s<e&&St(n,e-s,-1);let i=new wt(n,r);o=o?o.compose(i.map(o)):i,n=[],r=[],s=0}return function t(l){if(Array.isArray(l))for(let e of l)t(e);else if(l instanceof wt){if(l.length!=e)throw new RangeError(`Mismatched change set length (got ${l.length}, expected ${e})`);a(),o=o?o.compose(l.map(o)):l}else{let{from:t,to:o=t,insert:h}=l;if(t>o||t<0||o>e)throw new RangeError(`Invalid change range ${t} to ${o} (in doc of length ${e})`);let c=h?"string"==typeof h?ct.of(h.split(i||bt)):h:ct.empty,u=c.length;if(t==o&&0==u)return;t<s&&a(),t>s&&St(n,t-s,-1),St(n,o-t,u),kt(r,n,c),s=o}}(t),a(!o),o}static empty(t){return new wt(t?[t,-1]:[],[])}static fromJSON(t){if(!Array.isArray(t))throw new RangeError("Invalid JSON representation of ChangeSet");let e=[],i=[];for(let n=0;n<t.length;n++){let r=t[n];if("number"==typeof r)e.push(r,-1);else{if(!Array.isArray(r)||"number"!=typeof r[0]||r.some(((t,e)=>e&&"string"!=typeof t)))throw new RangeError("Invalid JSON representation of ChangeSet");if(1==r.length)e.push(r[0],0);else{for(;i.length<n;)i.push(ct.empty);i[n]=ct.of(r.slice(1)),e.push(r[0],i[n].length)}}}return new wt(e,i)}}function St(t,e,i,n=!1){if(0==e&&i<=0)return;let r=t.length-2;r>=0&&i<=0&&i==t[r+1]?t[r]+=e:0==e&&0==t[r]?t[r+1]+=i:n?(t[r]+=e,t[r+1]+=i):t.push(e,i)}function kt(t,e,i){if(0==i.length)return;let n=e.length-2>>1;if(n<t.length)t[t.length-1]=t[t.length-1].append(i);else{for(;t.length<n;)t.push(ct.empty);t.push(i)}}function $t(t,e,i){let n=t.inserted;for(let r=0,s=0,o=0;o<t.sections.length;){let a=t.sections[o++],l=t.sections[o++];if(l<0)r+=a,s+=a;else{let h=r,c=s,u=ct.empty;for(;h+=a,c+=l,l&&n&&(u=u.append(n[o-2>>1])),!(i||o==t.sections.length||t.sections[o+1]<0);)a=t.sections[o++],l=t.sections[o++];e(r,h,s,c,u),r=h,s=c}}}function Tt(t,e,i,n=!1){let r=[],s=n?[]:null,o=new Rt(t),a=new Rt(e);for(let t=0,e=0;;)if(-1==o.ins)t+=o.len,o.next();else if(-1==a.ins&&e<t){let i=Math.min(a.len,t-e);a.forward(i),St(r,i,-1),e+=i}else if(a.ins>=0&&(o.done||e<t||e==t&&(a.len<o.len||a.len==o.len&&!i))){for(St(r,a.ins,-1);t>e&&!o.done&&t+o.len<e+a.len;)t+=o.len,o.next();e+=a.len,a.next()}else{if(!(o.ins>=0)){if(o.done&&a.done)return s?new wt(r,s):new xt(r);throw new Error("Mismatched change set lengths")}{let i=0,n=t+o.len;for(;;)if(a.ins>=0&&e>t&&e+a.len<n)i+=a.ins,e+=a.len,a.next();else{if(!(-1==a.ins&&e<n))break;{let t=Math.min(a.len,n-e);i+=t,a.forward(t),e+=t}}St(r,i,o.ins),s&&kt(s,r,o.text),t=n,o.next()}}}function Pt(t,e,i=!1){let n=[],r=i?[]:null,s=new Rt(t),o=new Rt(e);for(let t=!1;;){if(s.done&&o.done)return r?new wt(n,r):new xt(n);if(0==s.ins)St(n,s.len,0,t),s.next();else if(0!=o.len||o.done){if(s.done||o.done)throw new Error("Mismatched change set lengths");{let e=Math.min(s.len2,o.len),i=n.length;if(-1==s.ins){let i=-1==o.ins?-1:o.off?0:o.ins;St(n,e,i,t),r&&i&&kt(r,n,o.text)}else-1==o.ins?(St(n,s.off?0:s.len,e,t),r&&kt(r,n,s.textBit(e))):(St(n,s.off?0:s.len,o.off?0:o.ins,t),r&&!o.off&&kt(r,n,o.text));t=(s.ins>e||o.ins>=0&&o.len>e)&&(t||n.length>i),s.forward2(e),o.forward(e)}}else St(n,0,o.ins,t),r&&kt(r,n,o.text),o.next()}}class Rt{constructor(t){this.set=t,this.i=0,this.next()}next(){let{sections:t}=this.set;this.i<t.length?(this.len=t[this.i++],this.ins=t[this.i++]):(this.len=0,this.ins=-2),this.off=0}get done(){return-2==this.ins}get len2(){return this.ins<0?this.len:this.ins}get text(){let{inserted:t}=this.set,e=this.i-2>>1;return e>=t.length?ct.empty:t[e]}textBit(t){let{inserted:e}=this.set,i=this.i-2>>1;return i>=e.length&&!t?ct.empty:e[i].slice(this.off,null==t?void 0:this.off+t)}forward(t){t==this.len?this.next():(this.len-=t,this.off+=t)}forward2(t){-1==this.ins?this.forward(t):t==this.ins?this.next():(this.ins-=t,this.off+=t)}}class Wt{constructor(t,e,i){this.from=t,this.to=e,this.flags=i}get anchor(){return 16&this.flags?this.to:this.from}get head(){return 16&this.flags?this.from:this.to}get empty(){return this.from==this.to}get assoc(){return 4&this.flags?-1:8&this.flags?1:0}get bidiLevel(){let t=3&this.flags;return 3==t?null:t}get goalColumn(){let t=this.flags>>5;return 33554431==t?void 0:t}map(t,e=-1){let i,n;return this.empty?i=n=t.mapPos(this.from,e):(i=t.mapPos(this.from,1),n=t.mapPos(this.to,-1)),i==this.from&&n==this.to?this:new Wt(i,n,this.flags)}extend(t,e=t){if(t<=this.anchor&&e>=this.anchor)return Xt.range(t,e);let i=Math.abs(t-this.anchor)>Math.abs(e-this.anchor)?t:e;return Xt.range(this.anchor,i)}eq(t){return this.anchor==t.anchor&&this.head==t.head}toJSON(){return{anchor:this.anchor,head:this.head}}static fromJSON(t){if(!t||"number"!=typeof t.anchor||"number"!=typeof t.head)throw new RangeError("Invalid JSON representation for SelectionRange");return Xt.range(t.anchor,t.head)}}class Xt{constructor(t,e=0){this.ranges=t,this.mainIndex=e}map(t,e=-1){return t.empty?this:Xt.create(this.ranges.map((i=>i.map(t,e))),this.mainIndex)}eq(t){if(this.ranges.length!=t.ranges.length||this.mainIndex!=t.mainIndex)return!1;for(let e=0;e<this.ranges.length;e++)if(!this.ranges[e].eq(t.ranges[e]))return!1;return!0}get main(){return this.ranges[this.mainIndex]}asSingle(){return 1==this.ranges.length?this:new Xt([this.main])}addRange(t,e=!0){return Xt.create([t].concat(this.ranges),e?0:this.mainIndex+1)}replaceRange(t,e=this.mainIndex){let i=this.ranges.slice();return i[e]=t,Xt.create(i,this.mainIndex)}toJSON(){return{ranges:this.ranges.map((t=>t.toJSON())),main:this.mainIndex}}static fromJSON(t){if(!t||!Array.isArray(t.ranges)||"number"!=typeof t.main||t.main>=t.ranges.length)throw new RangeError("Invalid JSON representation for EditorSelection");return new Xt(t.ranges.map((t=>Wt.fromJSON(t))),t.main)}static single(t,e=t){return new Xt([Xt.range(t,e)],0)}static create(t,e=0){if(0==t.length)throw new RangeError("A selection needs at least one range");for(let i=0,n=0;n<t.length;n++){let r=t[n];if(r.empty?r.from<=i:r.from<i)return Ct(t.slice(),e);i=r.to}return new Xt(t,e)}static cursor(t,e=0,i,n){return new Wt(t,t,(0==e?0:e<0?4:8)|(null==i?3:Math.min(2,i))|(null!=n?n:33554431)<<5)}static range(t,e,i){let n=(null!=i?i:33554431)<<5;return e<t?new Wt(e,t,24|n):new Wt(t,e,n|(e>t?4:0))}}function Ct(t,e=0){let i=t[e];t.sort(((t,e)=>t.from-e.from)),e=t.indexOf(i);for(let i=1;i<t.length;i++){let n=t[i],r=t[i-1];if(n.empty?n.from<=r.to:n.from<r.to){let s=r.from,o=Math.max(n.to,r.to);i<=e&&e--,t.splice(--i,2,n.anchor>n.head?Xt.range(o,s):Xt.range(s,o))}}return new Xt(t,e)}function At(t,e){for(let i of t.ranges)if(i.to>e)throw new RangeError("Selection points outside of document")}let Zt=0;class _t{constructor(t,e,i,n,r){this.combine=t,this.compareInput=e,this.compare=i,this.isStatic=n,this.extensions=r,this.id=Zt++,this.default=t([])}static define(t={}){return new _t(t.combine||(t=>t),t.compareInput||((t,e)=>t===e),t.compare||(t.combine?(t,e)=>t===e:Lt),!!t.static,t.enables)}of(t){return new Dt([],this,0,t)}compute(t,e){if(this.isStatic)throw new Error("Can't compute a static facet");return new Dt(t,this,1,e)}computeN(t,e){if(this.isStatic)throw new Error("Can't compute a static facet");return new Dt(t,this,2,e)}from(t,e){return e||(e=t=>t),this.compute([t],(i=>e(i.field(t))))}}function Lt(t,e){return t==e||t.length==e.length&&t.every(((t,i)=>t===e[i]))}class Dt{constructor(t,e,i,n){this.dependencies=t,this.facet=e,this.type=i,this.value=n,this.id=Zt++}dynamicSlot(t){var e;let i=this.value,n=this.facet.compareInput,r=this.id,s=t[r]>>1,o=2==this.type,a=!1,l=!1,h=[];for(let i of this.dependencies)"doc"==i?a=!0:"selection"==i?l=!0:0==(1&(null!==(e=t[i.id])&&void 0!==e?e:1))&&h.push(t[i.id]);return{create:t=>(t.values[s]=i(t),1),update(t,e){if(a&&e.docChanged||l&&(e.docChanged||e.selection)||h.some((e=>(1&Nt(t,e))>0))){let e=i(t);if(o?!zt(e,t.values[s],n):!n(e,t.values[s]))return t.values[s]=e,1}return 0},reconfigure(t,e){let a=i(t),l=e.config.address[r];if(null!=l){let i=Bt(e,l);if(o?zt(a,i,n):n(a,i))return t.values[s]=i,0}return t.values[s]=a,1}}}}function zt(t,e,i){if(t.length!=e.length)return!1;for(let n=0;n<t.length;n++)if(!i(t[n],e[n]))return!1;return!0}function Et(t,e,i){let n=i.map((e=>t[e.id])),r=i.map((t=>t.type)),s=n.filter((t=>!(1&t))),o=t[e.id]>>1;function a(t){let i=[];for(let e=0;e<n.length;e++){let s=Bt(t,n[e]);if(2==r[e])for(let t of s)i.push(t);else i.push(s)}return e.combine(i)}return{create(t){for(let e of n)Nt(t,e);return t.values[o]=a(t),1},update(t,i){if(!s.some((e=>1&Nt(t,e))))return 0;let n=a(t);return e.compare(n,t.values[o])?0:(t.values[o]=n,1)},reconfigure(t,r){let s=n.some((e=>1&Nt(t,e))),l=r.config.facets[e.id],h=r.facet(e);if(l&&!s&&Lt(i,l))return t.values[o]=h,0;let c=a(t);return e.compare(c,h)?(t.values[o]=h,0):(t.values[o]=c,1)}}}const Mt=_t.define({static:!0});class jt{constructor(t,e,i,n,r){this.id=t,this.createF=e,this.updateF=i,this.compareF=n,this.spec=r,this.provides=void 0}static define(t){let e=new jt(Zt++,t.create,t.update,t.compare||((t,e)=>t===e),t);return t.provide&&(e.provides=t.provide(e)),e}create(t){let e=t.facet(Mt).find((t=>t.field==this));return((null==e?void 0:e.create)||this.createF)(t)}slot(t){let e=t[this.id]>>1;return{create:t=>(t.values[e]=this.create(t),1),update:(t,i)=>{let n=t.values[e],r=this.updateF(n,i);return this.compareF(n,r)?0:(t.values[e]=r,1)},reconfigure:(t,i)=>null!=i.config.address[this.id]?(t.values[e]=i.field(this),0):(t.values[e]=this.create(t),1)}}init(t){return[this,Mt.of({field:this,create:t})]}get extension(){return this}}function qt(t){return e=>new Vt(e,t)}const Yt={lowest:qt(4),low:qt(3),default:qt(2),high:qt(1),highest:qt(0),fallback:qt(4),extend:qt(1),override:qt(0)};class Vt{constructor(t,e){this.inner=t,this.prec=e}}class Ut{of(t){return new Gt(this,t)}reconfigure(t){return Ut.reconfigure.of({compartment:this,extension:t})}get(t){return t.config.compartments.get(this)}}class Gt{constructor(t,e){this.compartment=t,this.inner=e}}class It{constructor(t,e,i,n,r,s){for(this.base=t,this.compartments=e,this.dynamicSlots=i,this.address=n,this.staticValues=r,this.facets=s,this.statusTemplate=[];this.statusTemplate.length<i.length;)this.statusTemplate.push(0)}staticFacet(t){let e=this.address[t.id];return null==e?t.default:this.staticValues[e>>1]}static resolve(t,e,i){let n=[],r=Object.create(null),s=new Map;for(let i of function(t,e,i){let n=[[],[],[],[],[]],r=new Map;return function t(s,o){let a=r.get(s);if(null!=a){if(a>=o)return;let t=n[a].indexOf(s);t>-1&&n[a].splice(t,1),s instanceof Gt&&i.delete(s.compartment)}if(r.set(s,o),Array.isArray(s))for(let e of s)t(e,o);else if(s instanceof Gt){if(i.has(s.compartment))throw new RangeError("Duplicate use of compartment in extensions");let n=e.get(s.compartment)||s.inner;i.set(s.compartment,n),t(n,o)}else if(s instanceof Vt)t(s.inner,s.prec);else if(s instanceof jt)n[o].push(s),s.provides&&t(s.provides,o);else if(s instanceof Dt)n[o].push(s),s.facet.extensions&&t(s.facet.extensions,o);else{let e=s.extension;if(!e)throw new Error(`Unrecognized extension value in extension set (${s}). This sometimes happens because multiple instances of @codemirror/state are loaded, breaking instanceof checks.`);t(e,o)}}(t,2),n.reduce(((t,e)=>t.concat(e)))}(t,e,s))i instanceof jt?n.push(i):(r[i.facet.id]||(r[i.facet.id]=[])).push(i);let o=Object.create(null),a=[],l=[];for(let t of n)o[t.id]=l.length<<1,l.push((e=>t.slot(e)));let h=null==i?void 0:i.config.facets;for(let t in r){let e=r[t],n=e[0].facet,s=h&&h[t]||[];if(e.every((t=>0==t.type)))if(o[n.id]=a.length<<1|1,Lt(s,e))a.push(i.facet(n));else{let t=n.combine(e.map((t=>t.value)));a.push(i&&n.compare(t,i.facet(n))?i.facet(n):t)}else{for(let t of e)0==t.type?(o[t.id]=a.length<<1|1,a.push(t.value)):(o[t.id]=l.length<<1,l.push((e=>t.dynamicSlot(e))));o[n.id]=l.length<<1,l.push((t=>Et(t,n,e)))}}let c=l.map((t=>t(o)));return new It(t,s,c,o,a,r)}}function Nt(t,e){if(1&e)return 2;let i=e>>1,n=t.status[i];if(4==n)throw new Error("Cyclic dependency between fields and/or facets");if(2&n)return n;t.status[i]=4;let r=t.computeSlot(t,t.config.dynamicSlots[i]);return t.status[i]=2|r}function Bt(t,e){return 1&e?t.config.staticValues[e>>1]:t.values[e>>1]}const Ft=_t.define(),Ht=_t.define({combine:t=>t.some((t=>t)),static:!0}),Jt=_t.define({combine:t=>t.length?t[0]:void 0,static:!0}),Kt=_t.define(),te=_t.define(),ee=_t.define(),ie=_t.define({combine:t=>!!t.length&&t[0]});class ne{constructor(t,e){this.type=t,this.value=e}static define(){return new re}}class re{of(t){return new ne(this,t)}}class se{constructor(t){this.map=t}of(t){return new oe(this,t)}}class oe{constructor(t,e){this.type=t,this.value=e}map(t){let e=this.type.map(this.value,t);return void 0===e?void 0:e==this.value?this:new oe(this.type,e)}is(t){return this.type==t}static define(t={}){return new se(t.map||(t=>t))}static mapEffects(t,e){if(!t.length)return t;let i=[];for(let n of t){let t=n.map(e);t&&i.push(t)}return i}}oe.reconfigure=oe.define(),oe.appendConfig=oe.define();class ae{constructor(t,e,i,n,r,s){this.startState=t,this.changes=e,this.selection=i,this.effects=n,this.annotations=r,this.scrollIntoView=s,this._doc=null,this._state=null,i&&At(i,e.newLength),r.some((t=>t.type==ae.time))||(this.annotations=r.concat(ae.time.of(Date.now())))}get newDoc(){return this._doc||(this._doc=this.changes.apply(this.startState.doc))}get newSelection(){return this.selection||this.startState.selection.map(this.changes)}get state(){return this._state||this.startState.applyTransaction(this),this._state}annotation(t){for(let e of this.annotations)if(e.type==t)return e.value}get docChanged(){return!this.changes.empty}get reconfigured(){return this.startState.config!=this.state.config}isUserEvent(t){let e=this.annotation(ae.userEvent);return!(!e||!(e==t||e.length>t.length&&e.slice(0,t.length)==t&&"."==e[t.length]))}}function le(t,e){let i=[];for(let n=0,r=0;;){let s,o;if(n<t.length&&(r==e.length||e[r]>=t[n]))s=t[n++],o=t[n++];else{if(!(r<e.length))return i;s=e[r++],o=e[r++]}!i.length||i[i.length-1]<s?i.push(s,o):i[i.length-1]<o&&(i[i.length-1]=o)}}function he(t,e,i){var n;let r,s,o;return i?(r=e.changes,s=wt.empty(e.changes.length),o=t.changes.compose(e.changes)):(r=e.changes.map(t.changes),s=t.changes.mapDesc(e.changes,!0),o=t.changes.compose(r)),{changes:o,selection:e.selection?e.selection.map(s):null===(n=t.selection)||void 0===n?void 0:n.map(r),effects:oe.mapEffects(t.effects,r).concat(oe.mapEffects(e.effects,s)),annotations:t.annotations.length?t.annotations.concat(e.annotations):e.annotations,scrollIntoView:t.scrollIntoView||e.scrollIntoView}}function ce(t,e,i){let n=e.selection,r=de(e.annotations);return e.userEvent&&(r=r.concat(ae.userEvent.of(e.userEvent))),{changes:e.changes instanceof wt?e.changes:wt.of(e.changes||[],i,t.facet(Jt)),selection:n&&(n instanceof Xt?n:Xt.single(n.anchor,n.head)),effects:de(e.effects),annotations:r,scrollIntoView:!!e.scrollIntoView}}function ue(t,e,i){let n=ce(t,e.length?e[0]:{},t.doc.length);e.length&&!1===e[0].filter&&(i=!1);for(let r=1;r<e.length;r++){!1===e[r].filter&&(i=!1);let s=!!e[r].sequential;n=he(n,ce(t,e[r],s?n.changes.newLength:t.doc.length),s)}let r=new ae(t,n.changes,n.selection,n.effects,n.annotations,n.scrollIntoView);return function(t){let e=t.startState,i=e.facet(ee),n=t;for(let r=i.length-1;r>=0;r--){let s=i[r](t);s&&Object.keys(s).length&&(n=he(t,ce(e,s,t.changes.newLength),!0))}return n==t?t:new ae(e,t.changes,t.selection,n.effects,n.annotations,n.scrollIntoView)}(i?function(t){let e=t.startState,i=!0;for(let n of e.facet(Kt)){let e=n(t);if(!1===e){i=!1;break}Array.isArray(e)&&(i=!0===i?e:le(i,e))}if(!0!==i){let n,r;if(!1===i)r=t.changes.invertedDesc,n=wt.empty(e.doc.length);else{let e=t.changes.filter(i);n=e.changes,r=e.filtered.invertedDesc}t=new ae(e,n,t.selection&&t.selection.map(r),oe.mapEffects(t.effects,r),t.annotations,t.scrollIntoView)}let n=e.facet(te);for(let i=n.length-1;i>=0;i--){let r=n[i](t);t=r instanceof ae?r:Array.isArray(r)&&1==r.length&&r[0]instanceof ae?r[0]:ue(e,de(r),!1)}return t}(r):r)}ae.time=ne.define(),ae.userEvent=ne.define(),ae.addToHistory=ne.define(),ae.remote=ne.define();const Oe=[];function de(t){return null==t?Oe:Array.isArray(t)?t:[t]}var fe,pe=((fe=pe||(pe={}))[fe.Word=0]="Word",fe[fe.Space=1]="Space",fe[fe.Other=2]="Other",fe);const me=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;let ge;try{ge=new RegExp("[\\p{Alphabetic}\\p{Number}_]","u")}catch(t){}class Qe{constructor(t,e,i,n,r,s){this.config=t,this.doc=e,this.selection=i,this.values=n,this.status=t.statusTemplate.slice(),this.computeSlot=r,s&&(s._state=this);for(let t=0;t<this.config.dynamicSlots.length;t++)Nt(this,t<<1);this.computeSlot=null}field(t,e=!0){let i=this.config.address[t.id];if(null!=i)return Nt(this,i),Bt(this,i);if(e)throw new RangeError("Field is not present in this state")}update(...t){return ue(this,t,!0)}applyTransaction(t){let e,i=this.config,{base:n,compartments:r}=i;for(let e of t.effects)e.is(Ut.reconfigure)?(i&&(r=new Map,i.compartments.forEach(((t,e)=>r.set(e,t))),i=null),r.set(e.value.compartment,e.value.extension)):e.is(oe.reconfigure)?(i=null,n=e.value):e.is(oe.appendConfig)&&(i=null,n=de(n).concat(e.value));i?e=t.startState.values.slice():(i=It.resolve(n,r,this),e=new Qe(i,this.doc,this.selection,i.dynamicSlots.map((()=>null)),((t,e)=>e.reconfigure(t,this)),null).values),new Qe(i,t.newDoc,t.newSelection,e,((e,i)=>i.update(e,t)),t)}replaceSelection(t){return"string"==typeof t&&(t=this.toText(t)),this.changeByRange((e=>({changes:{from:e.from,to:e.to,insert:t},range:Xt.cursor(e.from+t.length)})))}changeByRange(t){let e=this.selection,i=t(e.ranges[0]),n=this.changes(i.changes),r=[i.range],s=de(i.effects);for(let i=1;i<e.ranges.length;i++){let o=t(e.ranges[i]),a=this.changes(o.changes),l=a.map(n);for(let t=0;t<i;t++)r[t]=r[t].map(l);let h=n.mapDesc(a,!0);r.push(o.range.map(h)),n=n.compose(l),s=oe.mapEffects(s,l).concat(oe.mapEffects(de(o.effects),h))}return{changes:n,selection:Xt.create(r,e.mainIndex),effects:s}}changes(t=[]){return t instanceof wt?t:wt.of(t,this.doc.length,this.facet(Qe.lineSeparator))}toText(t){return ct.of(t.split(this.facet(Qe.lineSeparator)||bt))}sliceDoc(t=0,e=this.doc.length){return this.doc.sliceString(t,e,this.lineBreak)}facet(t){let e=this.config.address[t.id];return null==e?t.default:(Nt(this,e),Bt(this,e))}toJSON(t){let e={doc:this.sliceDoc(),selection:this.selection.toJSON()};if(t)for(let i in t){let n=t[i];n instanceof jt&&(e[i]=n.spec.toJSON(this.field(t[i]),this))}return e}static fromJSON(t,e={},i){if(!t||"string"!=typeof t.doc)throw new RangeError("Invalid JSON representation for EditorState");let n=[];if(i)for(let e in i){let r=i[e],s=t[e];n.push(r.init((t=>r.spec.fromJSON(s,t))))}return Qe.create({doc:t.doc,selection:Xt.fromJSON(t.selection),extensions:e.extensions?n.concat([e.extensions]):n})}static create(t={}){let e=It.resolve(t.extensions||[],new Map),i=t.doc instanceof ct?t.doc:ct.of((t.doc||"").split(e.staticFacet(Qe.lineSeparator)||bt)),n=t.selection?t.selection instanceof Xt?t.selection:Xt.single(t.selection.anchor,t.selection.head):Xt.single(0);return At(n,i.length),e.staticFacet(Ht)||(n=n.asSingle()),new Qe(e,i,n,e.dynamicSlots.map((()=>null)),((t,e)=>e.create(t)),null)}get tabSize(){return this.facet(Qe.tabSize)}get lineBreak(){return this.facet(Qe.lineSeparator)||"\n"}get readOnly(){return this.facet(ie)}phrase(t){for(let e of this.facet(Qe.phrases))if(Object.prototype.hasOwnProperty.call(e,t))return e[t];return t}languageDataAt(t,e,i=-1){let n=[];for(let r of this.facet(Ft))for(let s of r(this,e,i))Object.prototype.hasOwnProperty.call(s,t)&&n.push(s[t]);return n}charCategorizer(t){return e=this.languageDataAt("wordChars",t).join(""),t=>{if(!/\S/.test(t))return pe.Space;if(function(t){if(ge)return ge.test(t);for(let e=0;e<t.length;e++){let i=t[e];if(/\w/.test(i)||i>""&&(i.toUpperCase()!=i.toLowerCase()||me.test(i)))return!0}return!1}(t))return pe.Word;for(let i=0;i<e.length;i++)if(t.indexOf(e[i])>-1)return pe.Word;return pe.Other};var e}wordAt(t){let{text:e,from:i,length:n}=this.doc.lineAt(t),r=this.charCategorizer(t),s=t-i,o=t-i;for(;s>0;){let t=et(e,s,!1);if(r(e.slice(t,s))!=pe.Word)break;s=t}for(;o<n;){let t=et(e,o);if(r(e.slice(o,t))!=pe.Word)break;o=t}return s==o?null:Xt.range(s+i,o+i)}}function be(t,e,i={}){let n={};for(let e of t)for(let t of Object.keys(e)){let r=e[t],s=n[t];if(void 0===s)n[t]=r;else if(s===r||void 0===r);else{if(!Object.hasOwnProperty.call(i,t))throw new Error("Config merge conflict for field "+t);n[t]=i[t](s,r)}}for(let t in e)void 0===n[t]&&(n[t]=e[t]);return n}Qe.allowMultipleSelections=Ht,Qe.tabSize=_t.define({combine:t=>t.length?t[0]:4}),Qe.lineSeparator=Jt,Qe.readOnly=ie,Qe.phrases=_t.define(),Qe.languageData=Ft,Qe.changeFilter=Kt,Qe.transactionFilter=te,Qe.transactionExtender=ee,Ut.reconfigure=oe.define();class ye{eq(t){return this==t}range(t,e=t){return new ve(t,e,this)}}ye.prototype.startSide=ye.prototype.endSide=0,ye.prototype.point=!1,ye.prototype.mapMode=vt.TrackDel;class ve{constructor(t,e,i){this.from=t,this.to=e,this.value=i}}function xe(t,e){return t.from-e.from||t.value.startSide-e.value.startSide}class we{constructor(t,e,i,n){this.from=t,this.to=e,this.value=i,this.maxPoint=n}get length(){return this.to[this.to.length-1]}findIndex(t,e,i,n=0){let r=i?this.to:this.from;for(let s=n,o=r.length;;){if(s==o)return s;let n=s+o>>1,a=r[n]-t||(i?this.value[n].endSide:this.value[n].startSide)-e;if(n==s)return a>=0?s:o;a>=0?o=n:s=n+1}}between(t,e,i,n){for(let r=this.findIndex(e,-1e9,!0),s=this.findIndex(i,1e9,!1,r);r<s;r++)if(!1===n(this.from[r]+t,this.to[r]+t,this.value[r]))return!1}map(t,e){let i=[],n=[],r=[],s=-1,o=-1;for(let a=0;a<this.value.length;a++){let l,h,c=this.value[a],u=this.from[a]+t,O=this.to[a]+t;if(u==O){let t=e.mapPos(u,c.startSide,c.mapMode);if(null==t)continue;if(l=h=t,c.startSide!=c.endSide&&(h=e.mapPos(u,c.endSide),h<l))continue}else if(l=e.mapPos(u,c.startSide),h=e.mapPos(O,c.endSide),l>h||l==h&&c.startSide>0&&c.endSide<=0)continue;(h-l||c.endSide-c.startSide)<0||(s<0&&(s=l),c.point&&(o=Math.max(o,h-l)),i.push(c),n.push(l-s),r.push(h-s))}return{mapped:i.length?new we(n,r,i,o):null,pos:s}}}class Se{constructor(t,e,i=Se.empty,n){this.chunkPos=t,this.chunk=e,this.nextLayer=i,this.maxPoint=n}get length(){let t=this.chunk.length-1;return t<0?0:Math.max(this.chunkEnd(t),this.nextLayer.length)}get size(){if(this.isEmpty)return 0;let t=this.nextLayer.size;for(let e of this.chunk)t+=e.value.length;return t}chunkEnd(t){return this.chunkPos[t]+this.chunk[t].length}update(t){let{add:e=[],sort:i=!1,filterFrom:n=0,filterTo:r=this.length}=t,s=t.filter;if(0==e.length&&!s)return this;if(i&&(e=e.slice().sort(xe)),this.isEmpty)return e.length?Se.of(e):this;let o=new Te(this,null,-1).goto(0),a=0,l=[],h=new ke;for(;o.value||a<e.length;)if(a<e.length&&(o.from-e[a].from||o.startSide-e[a].value.startSide)>=0){let t=e[a++];h.addInner(t.from,t.to,t.value)||l.push(t)}else 1==o.rangeIndex&&o.chunkIndex<this.chunk.length&&(a==e.length||this.chunkEnd(o.chunkIndex)<e[a].from)&&(!s||n>this.chunkEnd(o.chunkIndex)||r<this.chunkPos[o.chunkIndex])&&h.addChunk(this.chunkPos[o.chunkIndex],this.chunk[o.chunkIndex])?o.nextChunk():((!s||n>o.to||r<o.from||s(o.from,o.to,o.value))&&(h.addInner(o.from,o.to,o.value)||l.push(new ve(o.from,o.to,o.value))),o.next());return h.finishInner(this.nextLayer.isEmpty&&!l.length?Se.empty:this.nextLayer.update({add:l,filter:s,filterFrom:n,filterTo:r}))}map(t){if(0==t.length||this.isEmpty)return this;let e=[],i=[],n=-1;for(let r=0;r<this.chunk.length;r++){let s=this.chunkPos[r],o=this.chunk[r],a=t.touchesRange(s,s+o.length);if(!1===a)n=Math.max(n,o.maxPoint),e.push(o),i.push(t.mapPos(s));else if(!0===a){let{mapped:r,pos:a}=o.map(s,t);r&&(n=Math.max(n,r.maxPoint),e.push(r),i.push(a))}}let r=this.nextLayer.map(t);return 0==e.length?r:new Se(i,e,r,n)}between(t,e,i){if(!this.isEmpty){for(let n=0;n<this.chunk.length;n++){let r=this.chunkPos[n],s=this.chunk[n];if(e>=r&&t<=r+s.length&&!1===s.between(r,t-r,e-r,i))return}this.nextLayer.between(t,e,i)}}iter(t=0){return Pe.from([this]).goto(t)}get isEmpty(){return this.nextLayer==this}static iter(t,e=0){return Pe.from(t).goto(e)}static compare(t,e,i,n,r=-1){let s=t.filter((t=>t.maxPoint>0||!t.isEmpty&&t.maxPoint>=r)),o=e.filter((t=>t.maxPoint>0||!t.isEmpty&&t.maxPoint>=r)),a=$e(s,o,i),l=new We(s,a,r),h=new We(o,a,r);i.iterGaps(((t,e,i)=>Xe(l,t,h,e,i,n))),i.empty&&0==i.length&&Xe(l,0,h,0,0,n)}static eq(t,e,i=0,n){null==n&&(n=1e9);let r=t.filter((t=>!t.isEmpty&&e.indexOf(t)<0)),s=e.filter((e=>!e.isEmpty&&t.indexOf(e)<0));if(r.length!=s.length)return!1;if(!r.length)return!0;let o=$e(r,s),a=new We(r,o,0).goto(i),l=new We(s,o,0).goto(i);for(;;){if(a.to!=l.to||!Ce(a.active,l.active)||a.point&&(!l.point||!a.point.eq(l.point)))return!1;if(a.to>n)return!0;a.next(),l.next()}}static spans(t,e,i,n,r=-1){var s;let o=new We(t,null,r,null===(s=n.filterPoint)||void 0===s?void 0:s.bind(n)).goto(e),a=e,l=o.openStart;for(;;){let t=Math.min(o.to,i);if(o.point?(n.point(a,t,o.point,o.activeForPoint(o.to),l),l=o.openEnd(t)+(o.to>t?1:0)):t>a&&(n.span(a,t,o.active,l),l=o.openEnd(t)),o.to>i)break;a=o.to,o.next()}return l}static of(t,e=!1){let i=new ke;for(let n of t instanceof ve?[t]:e?function(t){if(t.length>1)for(let e=t[0],i=1;i<t.length;i++){let n=t[i];if(xe(e,n)>0)return t.slice().sort(xe);e=n}return t}(t):t)i.add(n.from,n.to,n.value);return i.finish()}}Se.empty=new Se([],[],null,-1),Se.empty.nextLayer=Se.empty;class ke{constructor(){this.chunks=[],this.chunkPos=[],this.chunkStart=-1,this.last=null,this.lastFrom=-1e9,this.lastTo=-1e9,this.from=[],this.to=[],this.value=[],this.maxPoint=-1,this.setMaxPoint=-1,this.nextLayer=null}finishChunk(t){this.chunks.push(new we(this.from,this.to,this.value,this.maxPoint)),this.chunkPos.push(this.chunkStart),this.chunkStart=-1,this.setMaxPoint=Math.max(this.setMaxPoint,this.maxPoint),this.maxPoint=-1,t&&(this.from=[],this.to=[],this.value=[])}add(t,e,i){this.addInner(t,e,i)||(this.nextLayer||(this.nextLayer=new ke)).add(t,e,i)}addInner(t,e,i){let n=t-this.lastTo||i.startSide-this.last.endSide;if(n<=0&&(t-this.lastFrom||i.startSide-this.last.startSide)<0)throw new Error("Ranges must be added sorted by `from` position and `startSide`");return!(n<0||(250==this.from.length&&this.finishChunk(!0),this.chunkStart<0&&(this.chunkStart=t),this.from.push(t-this.chunkStart),this.to.push(e-this.chunkStart),this.last=i,this.lastFrom=t,this.lastTo=e,this.value.push(i),i.point&&(this.maxPoint=Math.max(this.maxPoint,e-t)),0))}addChunk(t,e){if((t-this.lastTo||e.value[0].startSide-this.last.endSide)<0)return!1;this.from.length&&this.finishChunk(!0),this.setMaxPoint=Math.max(this.setMaxPoint,e.maxPoint),this.chunks.push(e),this.chunkPos.push(t);let i=e.value.length-1;return this.last=e.value[i],this.lastFrom=e.from[i]+t,this.lastTo=e.to[i]+t,!0}finish(){return this.finishInner(Se.empty)}finishInner(t){if(this.from.length&&this.finishChunk(!1),0==this.chunks.length)return t;let e=new Se(this.chunkPos,this.chunks,this.nextLayer?this.nextLayer.finishInner(t):t,this.setMaxPoint);return this.from=null,e}}function $e(t,e,i){let n=new Map;for(let e of t)for(let t=0;t<e.chunk.length;t++)e.chunk[t].maxPoint<=0&&n.set(e.chunk[t],e.chunkPos[t]);let r=new Set;for(let t of e)for(let e=0;e<t.chunk.length;e++){let s=n.get(t.chunk[e]);null==s||(i?i.mapPos(s):s)!=t.chunkPos[e]||(null==i?void 0:i.touchesRange(s,s+t.chunk[e].length))||r.add(t.chunk[e])}return r}class Te{constructor(t,e,i,n=0){this.layer=t,this.skip=e,this.minPoint=i,this.rank=n}get startSide(){return this.value?this.value.startSide:0}get endSide(){return this.value?this.value.endSide:0}goto(t,e=-1e9){return this.chunkIndex=this.rangeIndex=0,this.gotoInner(t,e,!1),this}gotoInner(t,e,i){for(;this.chunkIndex<this.layer.chunk.length;){let e=this.layer.chunk[this.chunkIndex];if(!(this.skip&&this.skip.has(e)||this.layer.chunkEnd(this.chunkIndex)<t||e.maxPoint<this.minPoint))break;this.chunkIndex++,i=!1}if(this.chunkIndex<this.layer.chunk.length){let n=this.layer.chunk[this.chunkIndex].findIndex(t-this.layer.chunkPos[this.chunkIndex],e,!0);(!i||this.rangeIndex<n)&&this.setRangeIndex(n)}this.next()}forward(t,e){(this.to-t||this.endSide-e)<0&&this.gotoInner(t,e,!0)}next(){for(;;){if(this.chunkIndex==this.layer.chunk.length){this.from=this.to=1e9,this.value=null;break}{let t=this.layer.chunkPos[this.chunkIndex],e=this.layer.chunk[this.chunkIndex],i=t+e.from[this.rangeIndex];if(this.from=i,this.to=t+e.to[this.rangeIndex],this.value=e.value[this.rangeIndex],this.setRangeIndex(this.rangeIndex+1),this.minPoint<0||this.value.point&&this.to-this.from>=this.minPoint)break}}}setRangeIndex(t){if(t==this.layer.chunk[this.chunkIndex].value.length){if(this.chunkIndex++,this.skip)for(;this.chunkIndex<this.layer.chunk.length&&this.skip.has(this.layer.chunk[this.chunkIndex]);)this.chunkIndex++;this.rangeIndex=0}else this.rangeIndex=t}nextChunk(){this.chunkIndex++,this.rangeIndex=0,this.next()}compare(t){return this.from-t.from||this.startSide-t.startSide||this.to-t.to||this.endSide-t.endSide}}class Pe{constructor(t){this.heap=t}static from(t,e=null,i=-1){let n=[];for(let r=0;r<t.length;r++)for(let s=t[r];!s.isEmpty;s=s.nextLayer)s.maxPoint>=i&&n.push(new Te(s,e,i,r));return 1==n.length?n[0]:new Pe(n)}get startSide(){return this.value?this.value.startSide:0}goto(t,e=-1e9){for(let i of this.heap)i.goto(t,e);for(let t=this.heap.length>>1;t>=0;t--)Re(this.heap,t);return this.next(),this}forward(t,e){for(let i of this.heap)i.forward(t,e);for(let t=this.heap.length>>1;t>=0;t--)Re(this.heap,t);(this.to-t||this.value.endSide-e)<0&&this.next()}next(){if(0==this.heap.length)this.from=this.to=1e9,this.value=null,this.rank=-1;else{let t=this.heap[0];this.from=t.from,this.to=t.to,this.value=t.value,this.rank=t.rank,t.value&&t.next(),Re(this.heap,0)}}}function Re(t,e){for(let i=t[e];;){let n=1+(e<<1);if(n>=t.length)break;let r=t[n];if(n+1<t.length&&r.compare(t[n+1])>=0&&(r=t[n+1],n++),i.compare(r)<0)break;t[n]=i,t[e]=r,e=n}}class We{constructor(t,e,i,n=(()=>!0)){this.minPoint=i,this.filterPoint=n,this.active=[],this.activeTo=[],this.activeRank=[],this.minActive=-1,this.point=null,this.pointFrom=0,this.pointRank=0,this.to=-1e9,this.endSide=0,this.openStart=-1,this.cursor=Pe.from(t,e,i)}goto(t,e=-1e9){return this.cursor.goto(t,e),this.active.length=this.activeTo.length=this.activeRank.length=0,this.minActive=-1,this.to=t,this.endSide=e,this.openStart=-1,this.next(),this}forward(t,e){for(;this.minActive>-1&&(this.activeTo[this.minActive]-t||this.active[this.minActive].endSide-e)<0;)this.removeActive(this.minActive);this.cursor.forward(t,e)}removeActive(t){Ae(this.active,t),Ae(this.activeTo,t),Ae(this.activeRank,t),this.minActive=_e(this.active,this.activeTo)}addActive(t){let e=0,{value:i,to:n,rank:r}=this.cursor;for(;e<this.activeRank.length&&this.activeRank[e]<=r;)e++;Ze(this.active,e,i),Ze(this.activeTo,e,n),Ze(this.activeRank,e,r),t&&Ze(t,e,this.cursor.from),this.minActive=_e(this.active,this.activeTo)}next(){let t=this.to,e=this.point;this.point=null;let i=this.openStart<0?[]:null,n=0;for(;;){let r=this.minActive;if(r>-1&&(this.activeTo[r]-this.cursor.from||this.active[r].endSide-this.cursor.startSide)<0){if(this.activeTo[r]>t){this.to=this.activeTo[r],this.endSide=this.active[r].endSide;break}this.removeActive(r),i&&Ae(i,r)}else{if(!this.cursor.value){this.to=this.endSide=1e9;break}if(this.cursor.from>t){this.to=this.cursor.from,this.endSide=this.cursor.startSide;break}{let r=this.cursor.value;if(r.point)if(e&&this.cursor.to==this.to&&this.cursor.from<this.cursor.to)this.cursor.next();else{if(this.filterPoint(this.cursor.from,this.cursor.to,this.cursor.value,this.cursor.rank)){this.point=r,this.pointFrom=this.cursor.from,this.pointRank=this.cursor.rank,this.to=this.cursor.to,this.endSide=r.endSide,this.cursor.from<t&&(n=1),this.cursor.next(),this.to>t&&this.forward(this.to,this.endSide);break}this.cursor.next()}else this.addActive(i),this.cursor.next()}}}if(i){let e=0;for(;e<i.length&&i[e]<t;)e++;this.openStart=e+n}}activeForPoint(t){if(!this.active.length)return this.active;let e=[];for(let i=this.active.length-1;i>=0&&!(this.activeRank[i]<this.pointRank);i--)(this.activeTo[i]>t||this.activeTo[i]==t&&this.active[i].endSide>=this.point.endSide)&&e.push(this.active[i]);return e.reverse()}openEnd(t){let e=0;for(let i=this.activeTo.length-1;i>=0&&this.activeTo[i]>t;i--)e++;return e}}function Xe(t,e,i,n,r,s){t.goto(e),i.goto(n);let o=n+r,a=n,l=n-e;for(;;){let e=t.to+l-i.to||t.endSide-i.endSide,n=e<0?t.to+l:i.to,r=Math.min(n,o);if(t.point||i.point?t.point&&i.point&&(t.point==i.point||t.point.eq(i.point))&&Ce(t.activeForPoint(t.to+l),i.activeForPoint(i.to))||s.comparePoint(a,r,t.point,i.point):r>a&&!Ce(t.active,i.active)&&s.compareRange(a,r,t.active,i.active),n>o)break;a=n,e<=0&&t.next(),e>=0&&i.next()}}function Ce(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++)if(t[i]!=e[i]&&!t[i].eq(e[i]))return!1;return!0}function Ae(t,e){for(let i=e,n=t.length-1;i<n;i++)t[i]=t[i+1];t.pop()}function Ze(t,e,i){for(let i=t.length-1;i>=e;i--)t[i+1]=t[i];t[e]=i}function _e(t,e){let i=-1,n=1e9;for(let r=0;r<e.length;r++)(e[r]-n||t[r].endSide-t[i].endSide)<0&&(i=r,n=e[r]);return i}for(var Le={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",229:"q"},De={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"',229:"Q"},ze="undefined"!=typeof navigator&&/Chrome\/(\d+)/.exec(navigator.userAgent),Ee="undefined"!=typeof navigator&&/Apple Computer/.test(navigator.vendor),Me="undefined"!=typeof navigator&&/Gecko\/\d+/.test(navigator.userAgent),je="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),qe="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),Ye=ze&&(je||+ze[1]<57)||Me&&je,Ve=0;Ve<10;Ve++)Le[48+Ve]=Le[96+Ve]=String(Ve);for(Ve=1;Ve<=24;Ve++)Le[Ve+111]="F"+Ve;for(Ve=65;Ve<=90;Ve++)Le[Ve]=String.fromCharCode(Ve+32),De[Ve]=String.fromCharCode(Ve);for(var Ue in Le)De.hasOwnProperty(Ue)||(De[Ue]=Le[Ue]);function Ge(t){let e;return e=11==t.nodeType?t.getSelection?t:t.ownerDocument:t,e.getSelection()}function Ie(t,e){return!!e&&t.contains(1!=e.nodeType?e.parentNode:e)}function Ne(t,e){if(!e.anchorNode)return!1;try{return Ie(t,e.anchorNode)}catch(t){return!1}}function Be(t){return 3==t.nodeType?oi(t,0,t.nodeValue.length).getClientRects():1==t.nodeType?t.getClientRects():[]}function Fe(t,e,i,n){return!!i&&(Je(t,e,i,n,-1)||Je(t,e,i,n,1))}function He(t){for(var e=0;;e++)if(!(t=t.previousSibling))return e}function Je(t,e,i,n,r){for(;;){if(t==i&&e==n)return!0;if(e==(r<0?0:Ke(t))){if("DIV"==t.nodeName)return!1;let i=t.parentNode;if(!i||1!=i.nodeType)return!1;e=He(t)+(r<0?0:1),t=i}else{if(1!=t.nodeType)return!1;if(1==(t=t.childNodes[e+(r<0?-1:0)]).nodeType&&"false"==t.contentEditable)return!1;e=r<0?Ke(t):0}}}function Ke(t){return 3==t.nodeType?t.nodeValue.length:t.childNodes.length}const ti={left:0,right:0,top:0,bottom:0};function ei(t,e){let i=e?t.left:t.right;return{left:i,right:i,top:t.top,bottom:t.bottom}}class ii{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}eq(t){return this.anchorNode==t.anchorNode&&this.anchorOffset==t.anchorOffset&&this.focusNode==t.focusNode&&this.focusOffset==t.focusOffset}setRange(t){this.set(t.anchorNode,t.anchorOffset,t.focusNode,t.focusOffset)}set(t,e,i,n){this.anchorNode=t,this.anchorOffset=e,this.focusNode=i,this.focusOffset=n}}let ni,ri=null;function si(t){if(t.setActive)return t.setActive();if(ri)return t.focus(ri);let e=[];for(let i=t;i&&(e.push(i,i.scrollTop,i.scrollLeft),i!=i.ownerDocument);i=i.parentNode);if(t.focus(null==ri?{get preventScroll(){return ri={preventScroll:!0},!0}}:void 0),!ri){ri=!1;for(let t=0;t<e.length;){let i=e[t++],n=e[t++],r=e[t++];i.scrollTop!=n&&(i.scrollTop=n),i.scrollLeft!=r&&(i.scrollLeft=r)}}}function oi(t,e,i=e){let n=ni||(ni=document.createRange());return n.setEnd(t,i),n.setStart(t,e),n}function ai(t,e,i){let n={key:e,code:e,keyCode:i,which:i,cancelable:!0},r=new KeyboardEvent("keydown",n);r.synthetic=!0,t.dispatchEvent(r);let s=new KeyboardEvent("keyup",n);return s.synthetic=!0,t.dispatchEvent(s),r.defaultPrevented||s.defaultPrevented}function li(t){for(;t.attributes.length;)t.removeAttributeNode(t.attributes[0])}class hi{constructor(t,e,i=!0){this.node=t,this.offset=e,this.precise=i}static before(t,e){return new hi(t.parentNode,He(t),e)}static after(t,e){return new hi(t.parentNode,He(t)+1,e)}}const ci=[];class ui{constructor(){this.parent=null,this.dom=null,this.dirty=2}get editorView(){if(!this.parent)throw new Error("Accessing view in orphan content view");return this.parent.editorView}get overrideDOMText(){return null}get posAtStart(){return this.parent?this.parent.posBefore(this):0}get posAtEnd(){return this.posAtStart+this.length}posBefore(t){let e=this.posAtStart;for(let i of this.children){if(i==t)return e;e+=i.length+i.breakAfter}throw new RangeError("Invalid child in posBefore")}posAfter(t){return this.posBefore(t)+t.length}coordsAt(t,e){return null}sync(t){if(2&this.dirty){let e=this.dom,i=e.firstChild;for(let n of this.children){if(n.dirty){if(!n.dom&&i){let t=ui.get(i);t&&(t.parent||t.constructor!=n.constructor)||n.reuseDOM(i)}n.sync(t),n.dirty=0}if(t&&!t.written&&t.node==e&&i!=n.dom&&(t.written=!0),n.dom.parentNode==e){for(;i&&i!=n.dom;)i=Oi(i);i=n.dom.nextSibling}else e.insertBefore(n.dom,i)}for(i&&t&&t.node==e&&(t.written=!0);i;)i=Oi(i)}else if(1&this.dirty)for(let e of this.children)e.dirty&&(e.sync(t),e.dirty=0)}reuseDOM(t){}localPosFromDOM(t,e){let i;if(t==this.dom)i=this.dom.childNodes[e];else{let n=0==Ke(t)?0:0==e?-1:1;for(;;){let e=t.parentNode;if(e==this.dom)break;0==n&&e.firstChild!=e.lastChild&&(n=t==e.firstChild?-1:1),t=e}i=n<0?t:t.nextSibling}if(i==this.dom.firstChild)return 0;for(;i&&!ui.get(i);)i=i.nextSibling;if(!i)return this.length;for(let t=0,e=0;;t++){let n=this.children[t];if(n.dom==i)return e;e+=n.length+n.breakAfter}}domBoundsAround(t,e,i=0){let n=-1,r=-1,s=-1,o=-1;for(let a=0,l=i,h=i;a<this.children.length;a++){let i=this.children[a],c=l+i.length;if(l<t&&c>e)return i.domBoundsAround(t,e,l);if(c>=t&&-1==n&&(n=a,r=l),l>e&&i.dom.parentNode==this.dom){s=a,o=h;break}h=c,l=c+i.breakAfter}return{from:r,to:o<0?i+this.length:o,startDOM:(n?this.children[n-1].dom.nextSibling:null)||this.dom.firstChild,endDOM:s<this.children.length&&s>=0?this.children[s].dom:null}}markDirty(t=!1){this.dirty|=2,this.markParentsDirty(t)}markParentsDirty(t){for(let e=this.parent;e;e=e.parent){if(t&&(e.dirty|=2),1&e.dirty)return;e.dirty|=1,t=!1}}setParent(t){this.parent!=t&&(this.parent=t,this.dirty&&this.markParentsDirty(!0))}setDOM(t){this.dom&&(this.dom.cmView=null),this.dom=t,t.cmView=this}get rootView(){for(let t=this;;){let e=t.parent;if(!e)return t;t=e}}replaceChildren(t,e,i=ci){this.markDirty();for(let i=t;i<e;i++){let t=this.children[i];t.parent==this&&t.destroy()}this.children.splice(t,e-t,...i);for(let t=0;t<i.length;t++)i[t].setParent(this)}ignoreMutation(t){return!1}ignoreEvent(t){return!1}childCursor(t=this.length){return new di(this.children,t,this.children.length)}childPos(t,e=1){return this.childCursor().findPos(t,e)}toString(){let t=this.constructor.name.replace("View","");return t+(this.children.length?"("+this.children.join()+")":this.length?"["+("Text"==t?this.text:this.length)+"]":"")+(this.breakAfter?"#":"")}static get(t){return t.cmView}get isEditable(){return!0}merge(t,e,i,n,r,s){return!1}become(t){return!1}getSide(){return 0}destroy(){this.parent=null}}function Oi(t){let e=t.nextSibling;return t.parentNode.removeChild(t),e}ui.prototype.breakAfter=0;class di{constructor(t,e,i){this.children=t,this.pos=e,this.i=i,this.off=0}findPos(t,e=1){for(;;){if(t>this.pos||t==this.pos&&(e>0||0==this.i||this.children[this.i-1].breakAfter))return this.off=t-this.pos,this;let i=this.children[--this.i];this.pos-=i.length+i.breakAfter}}}function fi(t,e,i,n,r,s,o,a,l){let{children:h}=t,c=h.length?h[e]:null,u=s.length?s[s.length-1]:null,O=u?u.breakAfter:o;if(!(e==n&&c&&!o&&!O&&s.length<2&&c.merge(i,r,s.length?u:null,0==i,a,l))){if(n<h.length){let t=h[n];t&&r<t.length?(e==n&&(t=t.split(r),r=0),!O&&u&&t.merge(0,r,u,!0,0,l)?s[s.length-1]=t:(r&&t.merge(0,r,null,!1,0,l),s.push(t))):(null==t?void 0:t.breakAfter)&&(u?u.breakAfter=1:o=1),n++}for(c&&(c.breakAfter=o,i>0&&(!o&&s.length&&c.merge(i,c.length,s[0],!1,a,0)?c.breakAfter=s.shift().breakAfter:(i<c.length||c.children.length&&0==c.children[c.children.length-1].length)&&c.merge(i,c.length,null,!1,a,0),e++));e<n&&s.length;)if(h[n-1].become(s[s.length-1]))n--,s.pop(),l=s.length?0:a;else{if(!h[e].become(s[0]))break;e++,s.shift(),a=s.length?0:l}!s.length&&e&&n<h.length&&!h[e-1].breakAfter&&h[n].merge(0,0,h[e-1],!1,a,l)&&e--,(e<n||s.length)&&t.replaceChildren(e,n,s)}}function pi(t,e,i,n,r,s){let o=t.childCursor(),{i:a,off:l}=o.findPos(i,1),{i:h,off:c}=o.findPos(e,-1),u=e-i;for(let t of n)u+=t.length;t.length+=u,fi(t,h,c,a,l,n,0,r,s)}let mi="undefined"!=typeof navigator?navigator:{userAgent:"",vendor:"",platform:""},gi="undefined"!=typeof document?document:{documentElement:{style:{}}};const Qi=/Edge\/(\d+)/.exec(mi.userAgent),bi=/MSIE \d/.test(mi.userAgent),yi=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(mi.userAgent),vi=!!(bi||yi||Qi),xi=!vi&&/gecko\/(\d+)/i.test(mi.userAgent),wi=!vi&&/Chrome\/(\d+)/.exec(mi.userAgent),Si="webkitFontSmoothing"in gi.documentElement.style,ki=!vi&&/Apple Computer/.test(mi.vendor),$i=ki&&(/Mobile\/\w+/.test(mi.userAgent)||mi.maxTouchPoints>2);var Ti={mac:$i||/Mac/.test(mi.platform),windows:/Win/.test(mi.platform),linux:/Linux|X11/.test(mi.platform),ie:vi,ie_version:bi?gi.documentMode||6:yi?+yi[1]:Qi?+Qi[1]:0,gecko:xi,gecko_version:xi?+(/Firefox\/(\d+)/.exec(mi.userAgent)||[0,0])[1]:0,chrome:!!wi,chrome_version:wi?+wi[1]:0,ios:$i,android:/Android\b/.test(mi.userAgent),webkit:Si,safari:ki,webkit_version:Si?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0,tabSize:null!=gi.documentElement.style.tabSize?"tab-size":"-moz-tab-size"};class Pi extends ui{constructor(t){super(),this.text=t}get length(){return this.text.length}createDOM(t){this.setDOM(t||document.createTextNode(this.text))}sync(t){this.dom||this.createDOM(),this.dom.nodeValue!=this.text&&(t&&t.node==this.dom&&(t.written=!0),this.dom.nodeValue=this.text)}reuseDOM(t){3==t.nodeType&&this.createDOM(t)}merge(t,e,i){return(!i||i instanceof Pi&&!(this.length-(e-t)+i.length>256))&&(this.text=this.text.slice(0,t)+(i?i.text:"")+this.text.slice(e),this.markDirty(),!0)}split(t){let e=new Pi(this.text.slice(t));return this.text=this.text.slice(0,t),this.markDirty(),e}localPosFromDOM(t,e){return t==this.dom?e:e?this.text.length:0}domAtPos(t){return new hi(this.dom,t)}domBoundsAround(t,e,i){return{from:i,to:i+this.length,startDOM:this.dom,endDOM:this.dom.nextSibling}}coordsAt(t,e){return Wi(this.dom,t,e)}}class Ri extends ui{constructor(t,e=[],i=0){super(),this.mark=t,this.children=e,this.length=i;for(let t of e)t.setParent(this)}setAttrs(t){if(li(t),this.mark.class&&(t.className=this.mark.class),this.mark.attrs)for(let e in this.mark.attrs)t.setAttribute(e,this.mark.attrs[e]);return t}reuseDOM(t){t.nodeName==this.mark.tagName.toUpperCase()&&(this.setDOM(t),this.dirty|=6)}sync(t){this.dom?4&this.dirty&&this.setAttrs(this.dom):this.setDOM(this.setAttrs(document.createElement(this.mark.tagName))),super.sync(t)}merge(t,e,i,n,r,s){return!(i&&(!(i instanceof Ri&&i.mark.eq(this.mark))||t&&r<=0||e<this.length&&s<=0)||(pi(this,t,e,i?i.children:[],r-1,s-1),this.markDirty(),0))}split(t){let e=[],i=0,n=-1,r=0;for(let s of this.children){let o=i+s.length;o>t&&e.push(i<t?s.split(t-i):s),n<0&&i>=t&&(n=r),i=o,r++}let s=this.length-t;return this.length=t,n>-1&&(this.children.length=n,this.markDirty()),new Ri(this.mark,e,s)}domAtPos(t){return Zi(this.dom,this.children,t)}coordsAt(t,e){return Li(this,t,e)}}function Wi(t,e,i){let n=t.nodeValue.length;e>n&&(e=n);let r=e,s=e,o=0;0==e&&i<0||e==n&&i>=0?Ti.chrome||Ti.gecko||(e?(r--,o=1):(s++,o=-1)):i<0?r--:s++;let a=oi(t,r,s).getClientRects();if(!a.length)return ti;let l=a[(o?o<0:i>=0)?0:a.length-1];return Ti.safari&&!o&&0==l.width&&(l=Array.prototype.find.call(a,(t=>t.width))||l),o?ei(l,o<0):l||null}class Xi extends ui{constructor(t,e,i){super(),this.widget=t,this.length=e,this.side=i}static create(t,e,i){return new(t.customView||Xi)(t,e,i)}split(t){let e=Xi.create(this.widget,this.length-t,this.side);return this.length-=t,e}sync(){this.dom&&this.widget.updateDOM(this.dom)||(this.setDOM(this.widget.toDOM(this.editorView)),this.dom.contentEditable="false")}getSide(){return this.side}merge(t,e,i,n,r,s){return!(i&&(!(i instanceof Xi&&this.widget.compare(i.widget))||t>0&&r<=0||e<this.length&&s<=0)||(this.length=t+(i?i.length:0)+(this.length-e),0))}become(t){return t.length==this.length&&t instanceof Xi&&t.side==this.side&&this.widget.constructor==t.widget.constructor&&(this.widget.eq(t.widget)||this.markDirty(!0),this.widget=t.widget,!0)}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get overrideDOMText(){if(0==this.length)return ct.empty;let t=this;for(;t.parent;)t=t.parent;let e=t.editorView,i=e&&e.state.doc,n=this.posAtStart;return i?i.slice(n,n+this.length):ct.empty}domAtPos(t){return 0==t?hi.before(this.dom):hi.after(this.dom,t==this.length)}domBoundsAround(){return null}coordsAt(t,e){let i=this.dom.getClientRects(),n=null;if(!i.length)return ti;for(let e=t>0?i.length-1:0;n=i[e],!(t>0?0==e:e==i.length-1||n.top<n.bottom);e+=t>0?-1:1);return 0==t&&e>0||t==this.length&&e<=0?n:ei(n,0==t)}get isEditable(){return!1}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}}class Ci extends Xi{domAtPos(t){return new hi(this.widget.text,t)}sync(){this.setDOM(this.widget.toDOM())}localPosFromDOM(t,e){return e?3==t.nodeType?Math.min(e,this.length):this.length:0}ignoreMutation(){return!1}get overrideDOMText(){return null}coordsAt(t,e){return Wi(this.widget.text,t,e)}get isEditable(){return!0}}class Ai extends ui{constructor(t){super(),this.side=t}get length(){return 0}merge(){return!1}become(t){return t instanceof Ai&&t.side==this.side}split(){return new Ai(this.side)}sync(){if(!this.dom){let t=document.createElement("img");t.className="cm-widgetBuffer",this.setDOM(t)}}getSide(){return this.side}domAtPos(t){return hi.before(this.dom)}localPosFromDOM(){return 0}domBoundsAround(){return null}coordsAt(t){return this.dom.getBoundingClientRect()}get overrideDOMText(){return ct.empty}}function Zi(t,e,i){let n=0;for(let r=0;n<e.length;n++){let s=e[n],o=r+s.length;if(!(o==r&&s.getSide()<=0)){if(i>r&&i<o&&s.dom.parentNode==t)return s.domAtPos(i-r);if(i<=r)break;r=o}}for(;n>0;n--){let i=e[n-1].dom;if(i.parentNode==t)return hi.after(i)}return new hi(t,0)}function _i(t,e,i){let n,{children:r}=t;i>0&&e instanceof Ri&&r.length&&(n=r[r.length-1])instanceof Ri&&n.mark.eq(e.mark)?_i(n,e.children[0],i-1):(r.push(e),e.setParent(t)),t.length+=e.length}function Li(t,e,i){for(let n=0,r=0;r<t.children.length;r++){let s,o=t.children[r],a=n+o.length;if((i<=0||a==t.length||o.getSide()>0?a>=e:a>e)&&(e<a||r+1==t.children.length||(s=t.children[r+1]).length||s.getSide()>0)){let t=0;if(a==n){if(o.getSide()<=0)continue;t=i=-o.getSide()}let r=o.coordsAt(e-n,i);return t&&r?ei(r,i<0):r}n=a}let n=t.dom.lastChild;if(!n)return t.dom.getBoundingClientRect();let r=Be(n);return r[r.length-1]||null}function Di(t,e){for(let i in t)"class"==i&&e.class?e.class+=" "+t.class:"style"==i&&e.style?e.style+=";"+t.style:e[i]=t[i];return e}function zi(t,e){if(t==e)return!0;if(!t||!e)return!1;let i=Object.keys(t),n=Object.keys(e);if(i.length!=n.length)return!1;for(let r of i)if(-1==n.indexOf(r)||t[r]!==e[r])return!1;return!0}function Ei(t,e,i){if(e)for(let n in e)i&&n in i||t.removeAttribute(n);if(i)for(let n in i)e&&e[n]==i[n]||t.setAttribute(n,i[n])}Pi.prototype.children=Xi.prototype.children=Ai.prototype.children=ci;class Mi{eq(t){return!1}updateDOM(t){return!1}compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}get estimatedHeight(){return-1}ignoreEvent(t){return!0}get customView(){return null}destroy(t){}}var ji,qi=((ji=qi||(qi={}))[ji.Text=0]="Text",ji[ji.WidgetBefore=1]="WidgetBefore",ji[ji.WidgetAfter=2]="WidgetAfter",ji[ji.WidgetRange=3]="WidgetRange",ji);class Yi extends ye{constructor(t,e,i,n){super(),this.startSide=t,this.endSide=e,this.widget=i,this.spec=n}get heightRelevant(){return!1}static mark(t){return new Vi(t)}static widget(t){let e=t.side||0,i=!!t.block;return e+=i?e>0?3e8:-4e8:e>0?1e8:-1e8,new Gi(t,e,e,i,t.widget||null,!1)}static replace(t){let e=!!t.block,{start:i,end:n}=Ii(t,e);return new Gi(t,(i?e?-3e8:-1:4e8)-1,1+(n?e?2e8:1:-5e8),e,t.widget||null,!0)}static line(t){return new Ui(t)}static set(t,e=!1){return Se.of(t,e)}hasHeight(){return!!this.widget&&this.widget.estimatedHeight>-1}}Yi.none=Se.empty;class Vi extends Yi{constructor(t){let{start:e,end:i}=Ii(t);super(e?-1:4e8,i?1:-5e8,null,t),this.tagName=t.tagName||"span",this.class=t.class||"",this.attrs=t.attributes||null}eq(t){return this==t||t instanceof Vi&&this.tagName==t.tagName&&this.class==t.class&&zi(this.attrs,t.attrs)}range(t,e=t){if(t>=e)throw new RangeError("Mark decorations may not be empty");return super.range(t,e)}}Vi.prototype.point=!1;class Ui extends Yi{constructor(t){super(-2e8,-2e8,null,t)}eq(t){return t instanceof Ui&&zi(this.spec.attributes,t.spec.attributes)}range(t,e=t){if(e!=t)throw new RangeError("Line decoration ranges must be zero-length");return super.range(t,e)}}Ui.prototype.mapMode=vt.TrackBefore,Ui.prototype.point=!0;class Gi extends Yi{constructor(t,e,i,n,r,s){super(e,i,r,t),this.block=n,this.isReplace=s,this.mapMode=n?e<=0?vt.TrackBefore:vt.TrackAfter:vt.TrackDel}get type(){return this.startSide<this.endSide?qi.WidgetRange:this.startSide<=0?qi.WidgetBefore:qi.WidgetAfter}get heightRelevant(){return this.block||!!this.widget&&this.widget.estimatedHeight>=5}eq(t){var e,i;return t instanceof Gi&&((e=this.widget)==(i=t.widget)||!!(e&&i&&e.compare(i)))&&this.block==t.block&&this.startSide==t.startSide&&this.endSide==t.endSide}range(t,e=t){if(this.isReplace&&(t>e||t==e&&this.startSide>0&&this.endSide<=0))throw new RangeError("Invalid range for replacement decoration");if(!this.isReplace&&e!=t)throw new RangeError("Widget decorations can only have zero-length ranges");return super.range(t,e)}}function Ii(t,e=!1){let{inclusiveStart:i,inclusiveEnd:n}=t;return null==i&&(i=t.inclusive),null==n&&(n=t.inclusive),{start:null!=i?i:e,end:null!=n?n:e}}function Ni(t,e,i,n=0){let r=i.length-1;r>=0&&i[r]+n>=t?i[r]=Math.max(i[r],e):i.push(t,e)}Gi.prototype.point=!0;class Bi extends ui{constructor(){super(...arguments),this.children=[],this.length=0,this.prevAttrs=void 0,this.attrs=null,this.breakAfter=0}merge(t,e,i,n,r,s){if(i){if(!(i instanceof Bi))return!1;this.dom||i.transferDOM(this)}return n&&this.setDeco(i?i.attrs:null),pi(this,t,e,i?i.children:[],r,s),!0}split(t){let e=new Bi;if(e.breakAfter=this.breakAfter,0==this.length)return e;let{i:i,off:n}=this.childPos(t);n&&(e.append(this.children[i].split(n),0),this.children[i].merge(n,this.children[i].length,null,!1,0,0),i++);for(let t=i;t<this.children.length;t++)e.append(this.children[t],0);for(;i>0&&0==this.children[i-1].length;)this.children[--i].destroy();return this.children.length=i,this.markDirty(),this.length=t,e}transferDOM(t){this.dom&&(t.setDOM(this.dom),t.prevAttrs=void 0===this.prevAttrs?this.attrs:this.prevAttrs,this.prevAttrs=void 0,this.dom=null)}setDeco(t){zi(this.attrs,t)||(this.dom&&(this.prevAttrs=this.attrs,this.markDirty()),this.attrs=t)}append(t,e){_i(this,t,e)}addLineDeco(t){let e=t.spec.attributes,i=t.spec.class;e&&(this.attrs=Di(e,this.attrs||{})),i&&(this.attrs=Di({class:i},this.attrs||{}))}domAtPos(t){return Zi(this.dom,this.children,t)}reuseDOM(t){"DIV"==t.nodeName&&(this.setDOM(t),this.dirty|=6)}sync(t){var e;this.dom?4&this.dirty&&(li(this.dom),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0):(this.setDOM(document.createElement("div")),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0),void 0!==this.prevAttrs&&(Ei(this.dom,this.prevAttrs,this.attrs),this.dom.classList.add("cm-line"),this.prevAttrs=void 0),super.sync(t);let i=this.dom.lastChild;for(;i&&ui.get(i)instanceof Ri;)i=i.lastChild;if(!i||"BR"!=i.nodeName&&0==(null===(e=ui.get(i))||void 0===e?void 0:e.isEditable)&&(!Ti.ios||!this.children.some((t=>t instanceof Pi)))){let t=document.createElement("BR");t.cmIgnore=!0,this.dom.appendChild(t)}}measureTextSize(){if(0==this.children.length||this.length>20)return null;let t=0;for(let e of this.children){if(!(e instanceof Pi))return null;let i=Be(e.dom);if(1!=i.length)return null;t+=i[0].width}return{lineHeight:this.dom.getBoundingClientRect().height,charWidth:t/this.length}}coordsAt(t,e){return Li(this,t,e)}become(t){return!1}get type(){return qi.Text}static find(t,e){for(let i=0,n=0;i<t.children.length;i++){let r=t.children[i],s=n+r.length;if(s>=e){if(r instanceof Bi)return r;if(s>e)break}n=s+r.breakAfter}return null}}class Fi extends ui{constructor(t,e,i){super(),this.widget=t,this.length=e,this.type=i,this.breakAfter=0}merge(t,e,i,n,r,s){return!(i&&(!(i instanceof Fi&&this.widget.compare(i.widget))||t>0&&r<=0||e<this.length&&s<=0)||(this.length=t+(i?i.length:0)+(this.length-e),0))}domAtPos(t){return 0==t?hi.before(this.dom):hi.after(this.dom,t==this.length)}split(t){let e=this.length-t;this.length=t;let i=new Fi(this.widget,e,this.type);return i.breakAfter=this.breakAfter,i}get children(){return ci}sync(){this.dom&&this.widget.updateDOM(this.dom)||(this.setDOM(this.widget.toDOM(this.editorView)),this.dom.contentEditable="false")}get overrideDOMText(){return this.parent?this.parent.view.state.doc.slice(this.posAtStart,this.posAtEnd):ct.empty}domBoundsAround(){return null}become(t){return t instanceof Fi&&t.type==this.type&&t.widget.constructor==this.widget.constructor&&(t.widget.eq(this.widget)||this.markDirty(!0),this.widget=t.widget,this.length=t.length,this.breakAfter=t.breakAfter,!0)}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}}class Hi{constructor(t,e,i,n){this.doc=t,this.pos=e,this.end=i,this.disallowBlockEffectsBelow=n,this.content=[],this.curLine=null,this.breakAtStart=0,this.pendingBuffer=0,this.atCursorPos=!0,this.openStart=-1,this.openEnd=-1,this.text="",this.textOff=0,this.cursor=t.iter(),this.skip=e}posCovered(){if(0==this.content.length)return!this.breakAtStart&&this.doc.lineAt(this.pos).from!=this.pos;let t=this.content[this.content.length-1];return!(t.breakAfter||t instanceof Fi&&t.type==qi.WidgetBefore)}getLine(){return this.curLine||(this.content.push(this.curLine=new Bi),this.atCursorPos=!0),this.curLine}flushBuffer(t){this.pendingBuffer&&(this.curLine.append(Ji(new Ai(-1),t),t.length),this.pendingBuffer=0)}addBlockWidget(t){this.flushBuffer([]),this.curLine=null,this.content.push(t)}finish(t){t?this.pendingBuffer=0:this.flushBuffer([]),this.posCovered()||this.getLine()}buildText(t,e,i){for(;t>0;){if(this.textOff==this.text.length){let{value:e,lineBreak:i,done:n}=this.cursor.next(this.skip);if(this.skip=0,n)throw new Error("Ran out of text content when drawing inline views");if(i){this.posCovered()||this.getLine(),this.content.length?this.content[this.content.length-1].breakAfter=1:this.breakAtStart=1,this.flushBuffer([]),this.curLine=null,t--;continue}this.text=e,this.textOff=0}let n=Math.min(this.text.length-this.textOff,t,512);this.flushBuffer(e.slice(0,i)),this.getLine().append(Ji(new Pi(this.text.slice(this.textOff,this.textOff+n)),e),i),this.atCursorPos=!0,this.textOff+=n,t-=n,i=0}}span(t,e,i,n){this.buildText(e-t,i,n),this.pos=e,this.openStart<0&&(this.openStart=n)}point(t,e,i,n,r){let s=e-t;if(i instanceof Gi)if(i.block){let{type:t}=i;t!=qi.WidgetAfter||this.posCovered()||this.getLine(),this.addBlockWidget(new Fi(i.widget||new Ki("div"),s,t))}else{let o=Xi.create(i.widget||new Ki("span"),s,i.startSide),a=this.atCursorPos&&!o.isEditable&&r<=n.length&&(t<e||i.startSide>0),l=!o.isEditable&&(t<e||i.startSide<=0),h=this.getLine();2!=this.pendingBuffer||a||(this.pendingBuffer=0),this.flushBuffer(n),a&&(h.append(Ji(new Ai(1),n),r),r=n.length+Math.max(0,r-n.length)),h.append(Ji(o,n),r),this.atCursorPos=l,this.pendingBuffer=l?t<e?1:2:0}else this.doc.lineAt(this.pos).from==this.pos&&this.getLine().addLineDeco(i);s&&(this.textOff+s<=this.text.length?this.textOff+=s:(this.skip+=s-(this.text.length-this.textOff),this.text="",this.textOff=0),this.pos=e),this.openStart<0&&(this.openStart=r)}filterPoint(t,e,i,n){if(n<this.disallowBlockEffectsBelow&&i instanceof Gi){if(i.block)throw new RangeError("Block decorations may not be specified via plugins");if(e>this.doc.lineAt(this.pos).to)throw new RangeError("Decorations that replace line breaks may not be specified via plugins")}return!0}static build(t,e,i,n,r){let s=new Hi(t,e,i,r);return s.openEnd=Se.spans(n,e,i,s),s.openStart<0&&(s.openStart=s.openEnd),s.finish(s.openEnd),s}}function Ji(t,e){for(let i of e)t=new Ri(i,[t],t.length);return t}class Ki extends Mi{constructor(t){super(),this.tag=t}eq(t){return t.tag==this.tag}toDOM(){return document.createElement(this.tag)}updateDOM(t){return t.nodeName.toLowerCase()==this.tag}}const tn=[],en=_t.define(),nn=_t.define(),rn=_t.define(),sn=_t.define(),on=_t.define(),an=_t.define(),ln=oe.define({map:(t,e)=>t.map(e)}),hn=oe.define({map:(t,e)=>t.map(e)});class cn{constructor(t,e="nearest",i="nearest",n=5,r=5){this.range=t,this.y=e,this.x=i,this.yMargin=n,this.xMargin=r}map(t){return t.empty?this:new cn(this.range.map(t),this.y,this.x,this.yMargin,this.xMargin)}}const un=oe.define({map:(t,e)=>t.map(e)});function On(t,e,i){let n=t.facet(sn);n.length?n[0](e):window.onerror?window.onerror(String(e),i,void 0,void 0,e):i?console.error(i+":",e):console.error(e)}const dn=_t.define({combine:t=>!t.length||t[0]});class fn{constructor(t,e){this.field=t,this.get=e}}class pn{from(t){return new fn(this,t)}static define(){return new pn}}pn.decorations=pn.define(),pn.atomicRanges=pn.define(),pn.scrollMargins=pn.define();let mn=0;const gn=_t.define();class Qn{constructor(t,e,i){this.id=t,this.create=e,this.fields=i,this.extension=gn.of(this)}static define(t,e){let{eventHandlers:i,provide:n,decorations:r}=e||{},s=[];if(n)for(let t of Array.isArray(n)?n:[n])s.push(t);return i&&s.push(bn.from((t=>({plugin:t,handlers:i})))),r&&s.push(pn.decorations.from(r)),new Qn(mn++,t,s)}static fromClass(t,e){return Qn.define((e=>new t(e)),e)}}const bn=pn.define();class yn{constructor(t){this.spec=t,this.mustUpdate=null,this.value=null}takeField(t,e){if(this.spec)for(let{field:i,get:n}of this.spec.fields)i==t&&e.push(n(this.value))}update(t){if(this.value){if(this.mustUpdate){let t=this.mustUpdate;if(this.mustUpdate=null,this.value.update)try{this.value.update(t)}catch(e){if(On(t.state,e,"CodeMirror plugin crashed"),this.value.destroy)try{this.value.destroy()}catch(t){}this.deactivate()}}}else if(this.spec)try{this.value=this.spec.create(t)}catch(e){On(t.state,e,"CodeMirror plugin crashed"),this.deactivate()}return this}destroy(t){var e;if(null===(e=this.value)||void 0===e?void 0:e.destroy)try{this.value.destroy()}catch(e){On(t.state,e,"CodeMirror plugin crashed")}}deactivate(){this.spec=this.value=null}}const vn=_t.define(),xn=_t.define(),wn=_t.define(),Sn=_t.define();class kn{constructor(t,e,i,n){this.fromA=t,this.toA=e,this.fromB=i,this.toB=n}join(t){return new kn(Math.min(this.fromA,t.fromA),Math.max(this.toA,t.toA),Math.min(this.fromB,t.fromB),Math.max(this.toB,t.toB))}addToSet(t){let e=t.length,i=this;for(;e>0;e--){let n=t[e-1];if(!(n.fromA>i.toA)){if(n.toA<i.fromA)break;i=i.join(n),t.splice(e-1,1)}}return t.splice(e,0,i),t}static extendWithRanges(t,e){if(0==e.length)return t;let i=[];for(let n=0,r=0,s=0,o=0;;n++){let a=n==t.length?null:t[n],l=s-o,h=a?a.fromB:1e9;for(;r<e.length&&e[r]<h;){let t=e[r],n=e[r+1],s=Math.max(o,t),a=Math.min(h,n);if(s<=a&&new kn(s+l,a+l,s,a).addToSet(i),n>h)break;r+=2}if(!a)return i;new kn(a.fromA,a.toA,a.fromB,a.toB).addToSet(i),s=a.toA,o=a.toB}}}class $n{constructor(t,e,i=tn){this.view=t,this.state=e,this.transactions=i,this.flags=0,this.startState=t.state,this.changes=wt.empty(this.startState.doc.length);for(let t of i)this.changes=this.changes.compose(t.changes);let n=[];this.changes.iterChangedRanges(((t,e,i,r)=>n.push(new kn(t,e,i,r)))),this.changedRanges=n;let r=t.hasFocus;r!=t.inputState.notifiedFocused&&(t.inputState.notifiedFocused=r,this.flags|=1)}get viewportChanged(){return(4&this.flags)>0}get heightChanged(){return(2&this.flags)>0}get geometryChanged(){return this.docChanged||(10&this.flags)>0}get focusChanged(){return(1&this.flags)>0}get docChanged(){return!this.changes.empty}get selectionSet(){return this.transactions.some((t=>t.selection))}get empty(){return 0==this.flags&&0==this.transactions.length}}var Tn,Pn=((Tn=Pn||(Pn={}))[Tn.LTR=0]="LTR",Tn[Tn.RTL=1]="RTL",Tn);const Rn=Pn.LTR,Wn=Pn.RTL;function Xn(t){let e=[];for(let i=0;i<t.length;i++)e.push(1<<+t[i]);return e}const Cn=Xn("88888888888888888888888888888888888666888888787833333333337888888000000000000000000000000008888880000000000000000000000000088888888888888888888888888888888888887866668888088888663380888308888800000000000000000000000800000000000000000000000000000008"),An=Xn("4444448826627288999999999992222222222222222222222222222222222222222222222229999999999999999999994444444444644222822222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222999999949999999229989999223333333333"),Zn=Object.create(null),_n=[];for(let t of["()","[]","{}"]){let e=t.charCodeAt(0),i=t.charCodeAt(1);Zn[e]=i,Zn[i]=-e}const Ln=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;class Dn{constructor(t,e,i){this.from=t,this.to=e,this.level=i}get dir(){return this.level%2?Wn:Rn}side(t,e){return this.dir==e==t?this.to:this.from}static find(t,e,i,n){let r=-1;for(let s=0;s<t.length;s++){let o=t[s];if(o.from<=e&&o.to>=e){if(o.level==i)return s;(r<0||(0!=n?n<0?o.from<e:o.to>e:t[r].level>o.level))&&(r=s)}}if(r<0)throw new RangeError("Index out of range");return r}}const zn=[];function En(t){return[new Dn(0,t,0)]}let Mn="";function jn(t,e,i,n,r){var s;let o=n.head-t.from,a=-1;if(0==o){if(!r||!t.length)return null;e[0].level!=i&&(o=e[0].side(!1,i),a=0)}else if(o==t.length){if(r)return null;let t=e[e.length-1];t.level!=i&&(o=t.side(!0,i),a=e.length-1)}a<0&&(a=Dn.find(e,o,null!==(s=n.bidiLevel)&&void 0!==s?s:-1,n.assoc));let l=e[a];o==l.side(r,i)&&(l=e[a+=r?1:-1],o=l.side(!r,i));let h=r==(l.dir==i),c=et(t.text,o,h);if(Mn=t.text.slice(Math.min(o,c),Math.max(o,c)),c!=l.side(r,i))return Xt.cursor(c+t.from,h?-1:1,l.level);let u=a==(r?e.length-1:0)?null:e[a+(r?1:-1)];return u||l.level==i?u&&u.level<l.level?Xt.cursor(u.side(!r,i)+t.from,r?1:-1,u.level):Xt.cursor(c+t.from,r?-1:1,l.level):Xt.cursor(r?t.to:t.from,r?-1:1,i)}class qn{constructor(t,e){this.points=t,this.text="",this.lineSeparator=e.facet(Qe.lineSeparator)}append(t){this.text+=t}lineBreak(){this.text+="￿"}readRange(t,e){if(!t)return this;let i=t.parentNode;for(let n=t;;){this.findPointBefore(i,n),this.readNode(n);let t=n.nextSibling;if(t==e)break;let r=ui.get(n),s=ui.get(t);(r&&s?r.breakAfter:(r?r.breakAfter:Yn(n))||Yn(t)&&("BR"!=n.nodeName||n.cmIgnore))&&this.lineBreak(),n=t}return this.findPointBefore(i,e),this}readTextNode(t){let e=t.nodeValue;for(let i of this.points)i.node==t&&(i.pos=this.text.length+Math.min(i.offset,e.length));for(let i=0,n=this.lineSeparator?null:/\r\n?|\n/g;;){let r,s=-1,o=1;if(this.lineSeparator?(s=e.indexOf(this.lineSeparator,i),o=this.lineSeparator.length):(r=n.exec(e))&&(s=r.index,o=r[0].length),this.append(e.slice(i,s<0?e.length:s)),s<0)break;if(this.lineBreak(),o>1)for(let e of this.points)e.node==t&&e.pos>this.text.length&&(e.pos-=o-1);i=s+o}}readNode(t){if(t.cmIgnore)return;let e=ui.get(t),i=e&&e.overrideDOMText;if(null!=i){this.findPointInside(t,i.length);for(let t=i.iter();!t.next().done;)t.lineBreak?this.lineBreak():this.append(t.value)}else 3==t.nodeType?this.readTextNode(t):"BR"==t.nodeName?t.nextSibling&&this.lineBreak():1==t.nodeType&&this.readRange(t.firstChild,null)}findPointBefore(t,e){for(let i of this.points)i.node==t&&t.childNodes[i.offset]==e&&(i.pos=this.text.length)}findPointInside(t,e){for(let i of this.points)(3==t.nodeType?i.node==t:t.contains(i.node))&&(i.pos=this.text.length+Math.min(e,i.offset))}}function Yn(t){return 1==t.nodeType&&/^(DIV|P|LI|UL|OL|BLOCKQUOTE|DD|DT|H\d|SECTION|PRE)$/.test(t.nodeName)}class Vn{constructor(t,e){this.node=t,this.offset=e,this.pos=-1}}class Un extends ui{constructor(t){super(),this.view=t,this.compositionDeco=Yi.none,this.decorations=[],this.pluginDecorationLength=0,this.minWidth=0,this.minWidthFrom=0,this.minWidthTo=0,this.impreciseAnchor=null,this.impreciseHead=null,this.forceSelection=!1,this.lastUpdate=Date.now(),this.setDOM(t.contentDOM),this.children=[new Bi],this.children[0].setParent(this),this.updateDeco(),this.updateInner([new kn(0,0,0,t.state.doc.length)],0)}get root(){return this.view.root}get editorView(){return this.view}get length(){return this.view.state.doc.length}update(t){let e=t.changedRanges;this.minWidth>0&&e.length&&(e.every((({fromA:t,toA:e})=>e<this.minWidthFrom||t>this.minWidthTo))?(this.minWidthFrom=t.changes.mapPos(this.minWidthFrom,1),this.minWidthTo=t.changes.mapPos(this.minWidthTo,1)):this.minWidth=this.minWidthFrom=this.minWidthTo=0),this.view.inputState.composing<0?this.compositionDeco=Yi.none:(t.transactions.length||this.dirty)&&(this.compositionDeco=function(t,e){let i=In(t);if(!i)return Yi.none;let{from:n,to:r,node:s,text:o}=i,a=e.mapPos(n,1),l=Math.max(a,e.mapPos(r,-1)),{state:h}=t,c=3==s.nodeType?s.nodeValue:new qn([],h).readRange(s.firstChild,null).text;if(l-a<c.length)if(h.doc.sliceString(a,Math.min(h.doc.length,a+c.length),"￿")==c)l=a+c.length;else{if(h.doc.sliceString(Math.max(0,l-c.length),l,"￿")!=c)return Yi.none;a=l-c.length}else if(h.doc.sliceString(a,l,"￿")!=c)return Yi.none;return Yi.set(Yi.replace({widget:new Nn(s,o)}).range(a,l))}(this.view,t.changes)),(Ti.ie||Ti.chrome)&&!this.compositionDeco.size&&t&&t.state.doc.lines!=t.startState.doc.lines&&(this.forceSelection=!0);let i=function(t,e,i){let n=new Fn;return Se.compare(t,e,i,n),n.changes}(this.decorations,this.updateDeco(),t.changes);return e=kn.extendWithRanges(e,i),(0!=this.dirty||0!=e.length)&&(this.updateInner(e,t.startState.doc.length),t.transactions.length&&(this.lastUpdate=Date.now()),!0)}updateInner(t,e){this.view.viewState.mustMeasureContent=!0,this.updateChildren(t,e);let{observer:i}=this.view;i.ignore((()=>{this.dom.style.height=this.view.viewState.contentHeight+"px",this.dom.style.minWidth=this.minWidth?this.minWidth+"px":"";let t=Ti.chrome||Ti.ios?{node:i.selectionRange.focusNode,written:!1}:void 0;this.sync(t),this.dirty=0,t&&(t.written||i.selectionRange.focusNode!=t.node)&&(this.forceSelection=!0),this.dom.style.height=""}));let n=[];if(this.view.viewport.from||this.view.viewport.to<this.view.state.doc.length)for(let t of this.children)t instanceof Fi&&t.widget instanceof Gn&&n.push(t.dom);i.updateGaps(n)}updateChildren(t,e){let i=this.childCursor(e);for(let e=t.length-1;;e--){let n=e>=0?t[e]:null;if(!n)break;let{fromA:r,toA:s,fromB:o,toB:a}=n,{content:l,breakAtStart:h,openStart:c,openEnd:u}=Hi.build(this.view.state.doc,o,a,this.decorations,this.pluginDecorationLength),{i:O,off:d}=i.findPos(s,1),{i:f,off:p}=i.findPos(r,-1);fi(this,f,p,O,d,l,h,c,u)}}updateSelection(t=!1,e=!1){if(t&&this.view.observer.readSelectionRange(),!e&&!this.mayControlSelection()||Ti.ios&&this.view.inputState.rapidCompositionStart)return;let i=this.forceSelection;this.forceSelection=!1;let n=this.view.state.selection.main,r=this.domAtPos(n.anchor),s=n.empty?r:this.domAtPos(n.head);if(Ti.gecko&&n.empty&&1==(o=r).node.nodeType&&o.node.firstChild&&(0==o.offset||"false"==o.node.childNodes[o.offset-1].contentEditable)&&(o.offset==o.node.childNodes.length||"false"==o.node.childNodes[o.offset].contentEditable)){let t=document.createTextNode("");this.view.observer.ignore((()=>r.node.insertBefore(t,r.node.childNodes[r.offset]||null))),r=s=new hi(t,0),i=!0}var o;let a=this.view.observer.selectionRange;!i&&a.focusNode&&Fe(r.node,r.offset,a.anchorNode,a.anchorOffset)&&Fe(s.node,s.offset,a.focusNode,a.focusOffset)||(this.view.observer.ignore((()=>{Ti.android&&Ti.chrome&&this.dom.contains(a.focusNode)&&function(t,e){for(let i=t;i&&i!=e;i=i.assignedSlot||i.parentNode)if(1==i.nodeType&&"false"==i.contentEditable)return!0;return!1}(a.focusNode,this.dom)&&(this.dom.blur(),this.dom.focus({preventScroll:!0}));let t=Ge(this.root);if(n.empty){if(Ti.gecko){let t=(e=r.node,i=r.offset,1!=e.nodeType?0:(i&&"false"==e.childNodes[i-1].contentEditable?1:0)|(i<e.childNodes.length&&"false"==e.childNodes[i].contentEditable?2:0));if(t&&3!=t){let e=Bn(r.node,r.offset,1==t?1:-1);e&&(r=new hi(e,1==t?0:e.nodeValue.length))}}t.collapse(r.node,r.offset),null!=n.bidiLevel&&null!=a.cursorBidiLevel&&(a.cursorBidiLevel=n.bidiLevel)}else if(t.extend)t.collapse(r.node,r.offset),t.extend(s.node,s.offset);else{let e=document.createRange();n.anchor>n.head&&([r,s]=[s,r]),e.setEnd(s.node,s.offset),e.setStart(r.node,r.offset),t.removeAllRanges(),t.addRange(e)}var e,i})),this.view.observer.setSelectionRange(r,s)),this.impreciseAnchor=r.precise?null:new hi(a.anchorNode,a.anchorOffset),this.impreciseHead=s.precise?null:new hi(a.focusNode,a.focusOffset)}enforceCursorAssoc(){if(this.compositionDeco.size)return;let t=this.view.state.selection.main,e=Ge(this.root);if(!t.empty||!t.assoc||!e.modify)return;let i=Bi.find(this,t.head);if(!i)return;let n=i.posAtStart;if(t.head==n||t.head==n+i.length)return;let r=this.coordsAt(t.head,-1),s=this.coordsAt(t.head,1);if(!r||!s||r.bottom>s.top)return;let o=this.domAtPos(t.head+t.assoc);e.collapse(o.node,o.offset),e.modify("move",t.assoc<0?"forward":"backward","lineboundary")}mayControlSelection(){return this.view.state.facet(dn)?this.root.activeElement==this.dom:Ne(this.dom,this.view.observer.selectionRange)}nearest(t){for(let e=t;e;){let t=ui.get(e);if(t&&t.rootView==this)return t;e=e.parentNode}return null}posFromDOM(t,e){let i=this.nearest(t);if(!i)throw new RangeError("Trying to find position for a DOM position outside of the document");return i.localPosFromDOM(t,e)+i.posAtStart}domAtPos(t){let{i:e,off:i}=this.childCursor().findPos(t,-1);for(;e<this.children.length-1;){let t=this.children[e];if(i<t.length||t instanceof Bi)break;e++,i=0}return this.children[e].domAtPos(i)}coordsAt(t,e){for(let i=this.length,n=this.children.length-1;;n--){let r=this.children[n],s=i-r.breakAfter-r.length;if(t>s||t==s&&r.type!=qi.WidgetBefore&&r.type!=qi.WidgetAfter&&(!n||2==e||this.children[n-1].breakAfter||this.children[n-1].type==qi.WidgetBefore&&e>-2))return r.coordsAt(t-s,e);i=s}}measureVisibleLineHeights(){let t=[],{from:e,to:i}=this.view.viewState.viewport,n=this.view.contentDOM.clientWidth,r=n>Math.max(this.view.scrollDOM.clientWidth,this.minWidth)+1,s=-1;for(let o=0,a=0;a<this.children.length;a++){let l=this.children[a],h=o+l.length;if(h>i)break;if(o>=e){let e=l.dom.getBoundingClientRect();if(t.push(e.height),r){let t=l.dom.lastChild,i=t?Be(t):[];if(i.length){let t=i[i.length-1],r=this.view.textDirection==Pn.LTR?t.right-e.left:e.right-t.left;r>s&&(s=r,this.minWidth=n,this.minWidthFrom=o,this.minWidthTo=h)}}}o=h+l.breakAfter}return t}measureTextSize(){for(let t of this.children)if(t instanceof Bi){let e=t.measureTextSize();if(e)return e}let t,e,i=document.createElement("div");return i.className="cm-line",i.textContent="abc def ghi jkl mno pqr stu",this.view.observer.ignore((()=>{this.dom.appendChild(i);let n=Be(i.firstChild)[0];t=i.getBoundingClientRect().height,e=n?n.width/27:7,i.remove()})),{lineHeight:t,charWidth:e}}childCursor(t=this.length){let e=this.children.length;return e&&(t-=this.children[--e].length),new di(this.children,t,e)}computeBlockGapDeco(){let t=[],e=this.view.viewState;for(let i=0,n=0;;n++){let r=n==e.viewports.length?null:e.viewports[n],s=r?r.from-1:this.length;if(s>i){let n=e.lineBlockAt(s).bottom-e.lineBlockAt(i).top;t.push(Yi.replace({widget:new Gn(n),block:!0,inclusive:!0}).range(i,s))}if(!r)break;i=r.to+1}return Yi.set(t)}updateDeco(){let t=this.view.pluginField(pn.decorations);return this.pluginDecorationLength=t.length,this.decorations=[...t,...this.view.state.facet(wn),this.compositionDeco,this.computeBlockGapDeco(),this.view.viewState.lineGapDeco]}scrollIntoView(t){let e,{range:i}=t,n=this.coordsAt(i.head,i.empty?i.assoc:i.head>i.anchor?-1:1);if(!n)return;!i.empty&&(e=this.coordsAt(i.anchor,i.anchor>i.head?-1:1))&&(n={left:Math.min(n.left,e.left),top:Math.min(n.top,e.top),right:Math.max(n.right,e.right),bottom:Math.max(n.bottom,e.bottom)});let r=0,s=0,o=0,a=0;for(let t of this.view.pluginField(pn.scrollMargins))if(t){let{left:e,right:i,top:n,bottom:l}=t;null!=e&&(r=Math.max(r,e)),null!=i&&(s=Math.max(s,i)),null!=n&&(o=Math.max(o,n)),null!=l&&(a=Math.max(a,l))}let l={left:n.left-r,top:n.top-o,right:n.right+s,bottom:n.bottom+a};!function(t,e,i,n,r,s,o,a){let l=t.ownerDocument,h=l.defaultView;for(let u=t;u;)if(1==u.nodeType){let t,O=u==l.body;if(O)t={left:0,right:(c=h).innerWidth,top:0,bottom:c.innerHeight};else{if(u.scrollHeight<=u.clientHeight&&u.scrollWidth<=u.clientWidth){u=u.parentNode;continue}let e=u.getBoundingClientRect();t={left:e.left,right:e.left+u.clientWidth,top:e.top,bottom:e.top+u.clientHeight}}let d=0,f=0;if("nearest"==r)e.top<t.top?(f=-(t.top-e.top+o),i>0&&e.bottom>t.bottom+f&&(f=e.bottom-t.bottom+f+o)):e.bottom>t.bottom&&(f=e.bottom-t.bottom+o,i<0&&e.top-f<t.top&&(f=-(t.top+f-e.top+o)));else{let n=e.bottom-e.top,s=t.bottom-t.top;f=("center"==r&&n<=s?e.top+n/2-s/2:"start"==r||"center"==r&&i<0?e.top-o:e.bottom-s+o)-t.top}if("nearest"==n?e.left<t.left?(d=-(t.left-e.left+s),i>0&&e.right>t.right+d&&(d=e.right-t.right+d+s)):e.right>t.right&&(d=e.right-t.right+s,i<0&&e.left<t.left+d&&(d=-(t.left+d-e.left+s))):d=("center"==n?e.left+(e.right-e.left)/2-(t.right-t.left)/2:"start"==n==a?e.left-s:e.right-(t.right-t.left)+s)-t.left,d||f)if(O)h.scrollBy(d,f);else{if(f){let t=u.scrollTop;u.scrollTop+=f,f=u.scrollTop-t}if(d){let t=u.scrollLeft;u.scrollLeft+=d,d=u.scrollLeft-t}e={left:e.left-d,top:e.top-f,right:e.right-d,bottom:e.bottom-f}}if(O)break;u=u.assignedSlot||u.parentNode,n=r="nearest"}else{if(11!=u.nodeType)break;u=u.host}var c}(this.view.scrollDOM,l,i.head<i.anchor?-1:1,t.x,t.y,t.xMargin,t.yMargin,this.view.textDirection==Pn.LTR)}}class Gn extends Mi{constructor(t){super(),this.height=t}toDOM(){let t=document.createElement("div");return this.updateDOM(t),t}eq(t){return t.height==this.height}updateDOM(t){return t.style.height=this.height+"px",!0}get estimatedHeight(){return this.height}}function In(t){let e=t.observer.selectionRange,i=e.focusNode&&Bn(e.focusNode,e.focusOffset,0);if(!i)return null;let n=t.docView.nearest(i);if(!n)return null;if(n instanceof Bi){let t=i;for(;t.parentNode!=n.dom;)t=t.parentNode;let e=t.previousSibling;for(;e&&!ui.get(e);)e=e.previousSibling;let r=e?ui.get(e).posAtEnd:n.posAtStart;return{from:r,to:r,node:t,text:i}}{for(;;){let{parent:t}=n;if(!t)return null;if(t instanceof Bi)break;n=t}let t=n.posAtStart;return{from:t,to:t+n.length,node:n.dom,text:i}}}class Nn extends Mi{constructor(t,e){super(),this.top=t,this.text=e}eq(t){return this.top==t.top&&this.text==t.text}toDOM(){return this.top}ignoreEvent(){return!1}get customView(){return Ci}}function Bn(t,e,i){for(;;){if(3==t.nodeType)return t;if(1==t.nodeType&&e>0&&i<=0)e=Ke(t=t.childNodes[e-1]);else{if(!(1==t.nodeType&&e<t.childNodes.length&&i>=0))return null;t=t.childNodes[e],e=0}}}class Fn{constructor(){this.changes=[]}compareRange(t,e){Ni(t,e,this.changes)}comparePoint(t,e){Ni(t,e,this.changes)}}function Hn(t,e){return t.top<e.bottom-1&&t.bottom>e.top+1}function Jn(t,e){return e<t.top?{top:e,left:t.left,right:t.right,bottom:t.bottom}:t}function Kn(t,e){return e>t.bottom?{top:t.top,left:t.left,right:t.right,bottom:e}:t}function tr(t,e,i){let n,r,s,o,a,l,h,c;for(let p=t.firstChild;p;p=p.nextSibling){let t=Be(p);for(let m=0;m<t.length;m++){let g=t[m];r&&Hn(r,g)&&(g=Jn(Kn(g,r.bottom),r.top));let Q=(d=e,(f=g).left>d?f.left-d:Math.max(0,d-f.right)),b=(u=i,(O=g).top>u?O.top-u:Math.max(0,u-O.bottom));if(0==Q&&0==b)return 3==p.nodeType?er(p,e,i):tr(p,e,i);(!n||o>b||o==b&&s>Q)&&(n=p,r=g,s=Q,o=b),0==Q?i>g.bottom&&(!h||h.bottom<g.bottom)?(a=p,h=g):i<g.top&&(!c||c.top>g.top)&&(l=p,c=g):h&&Hn(h,g)?h=Kn(h,g.bottom):c&&Hn(c,g)&&(c=Jn(c,g.top))}}var u,O,d,f;if(h&&h.bottom>=i?(n=a,r=h):c&&c.top<=i&&(n=l,r=c),!n)return{node:t,offset:0};let p=Math.max(r.left,Math.min(r.right,e));return 3==n.nodeType?er(n,p,i):s||"true"!=n.contentEditable?{node:t,offset:Array.prototype.indexOf.call(t.childNodes,n)+(e>=(r.left+r.right)/2?1:0)}:tr(n,p,i)}function er(t,e,i){let n=t.nodeValue.length,r=-1,s=1e9,o=0;for(let a=0;a<n;a++){let n=oi(t,a,a+1).getClientRects();for(let l=0;l<n.length;l++){let h=n[l];if(h.top==h.bottom)continue;o||(o=e-h.left);let c=(h.top>i?h.top-i:i-h.bottom)-1;if(h.left-1<=e&&h.right+1>=e&&c<s){let i=e>=(h.left+h.right)/2,n=i;if((Ti.chrome||Ti.gecko)&&oi(t,a).getBoundingClientRect().left==h.right&&(n=!i),c<=0)return{node:t,offset:a+(n?1:0)};r=a+(n?1:0),s=c}}}return{node:t,offset:r>-1?r:o>0?t.nodeValue.length:0}}function ir(t,{x:e,y:i},n,r=-1){var s;let o,a=t.contentDOM.getBoundingClientRect(),l=a.top+t.viewState.paddingTop,{docHeight:h}=t.viewState,c=i-l;if(c<0)return 0;if(c>h)return t.state.doc.length;for(let e=t.defaultLineHeight/2,i=!1;o=t.elementAtHeight(c),o.type!=qi.Text;)for(;c=r>0?o.bottom+e:o.top-e,!(c>=0&&c<=h);){if(i)return n?null:0;i=!0,r=-r}i=l+c;let u=o.from;if(u<t.viewport.from)return 0==t.viewport.from?0:n?null:nr(t,a,o,e,i);if(u>t.viewport.to)return t.viewport.to==t.state.doc.length?t.state.doc.length:n?null:nr(t,a,o,e,i);let O=t.dom.ownerDocument,d=t.root.elementFromPoint?t.root:O,f=d.elementFromPoint(e,i);f&&!t.contentDOM.contains(f)&&(f=null),f||(e=Math.max(a.left+1,Math.min(a.right-1,e)),f=d.elementFromPoint(e,i),f&&!t.contentDOM.contains(f)&&(f=null));let p,m=-1;if(f&&0!=(null===(s=t.docView.nearest(f))||void 0===s?void 0:s.isEditable))if(O.caretPositionFromPoint){let t=O.caretPositionFromPoint(e,i);t&&({offsetNode:p,offset:m}=t)}else if(O.caretRangeFromPoint){let t=O.caretRangeFromPoint(e,i);t&&(({startContainer:p,startOffset:m}=t),Ti.safari&&function(t,e,i){let n;if(3!=t.nodeType||e!=(n=t.nodeValue.length))return!1;for(let e=t.nextSibling;e;e=e.nextSibling)if(1!=e.nodeType||"BR"!=e.nodeName)return!1;return oi(t,n-1,n).getBoundingClientRect().left>i}(p,m,e)&&(p=void 0))}if(!p||!t.docView.dom.contains(p)){let n=Bi.find(t.docView,u);if(!n)return c>o.top+o.height/2?o.to:o.from;({node:p,offset:m}=tr(n.dom,e,i))}return t.docView.posFromDOM(p,m)}function nr(t,e,i,n,r){let s=Math.round((n-e.left)*t.defaultCharacterWidth);t.lineWrapping&&i.height>1.5*t.defaultLineHeight&&(s+=Math.floor((r-i.top)/t.defaultLineHeight)*t.viewState.heightOracle.lineLength);let o=t.state.sliceDoc(i.from,i.to);return i.from+function(t,e,i,n){for(let n=0,r=0;;){if(r>=e)return n;if(n==t.length)break;r+=9==t.charCodeAt(n)?i-r%i:1,n=et(t,n)}return t.length}(o,s,t.state.tabSize)}function rr(t,e,i,n){let r=t.state.doc.lineAt(e.head),s=t.bidiSpans(r);for(let o=e,a=null;;){let e=jn(r,s,t.textDirection,o,i),l=Mn;if(!e){if(r.number==(i?t.state.doc.lines:1))return o;l="\n",r=t.state.doc.line(r.number+(i?1:-1)),s=t.bidiSpans(r),e=Xt.cursor(i?r.from:r.to)}if(a){if(!a(l))return o}else{if(!n)return e;a=n(l)}o=e}}function sr(t,e,i){let n=t.pluginField(pn.atomicRanges);for(;;){let t=!1;for(let r of n)r.between(i.from-1,i.from+1,((n,r,s)=>{i.from>n&&i.from<r&&(i=e.from>i.from?Xt.cursor(n,1):Xt.cursor(r,-1),t=!0)}));if(!t)return i}}class or{constructor(t){this.lastKeyCode=0,this.lastKeyTime=0,this.pendingIOSKey=void 0,this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastEscPress=0,this.lastContextMenu=0,this.scrollHandlers=[],this.registeredEvents=[],this.customHandlers=[],this.composing=-1,this.compositionFirstChange=null,this.compositionEndedAt=0,this.rapidCompositionStart=!1,this.mouseSelection=null;for(let e in ur){let i=ur[e];t.contentDOM.addEventListener(e,(n=>{"keydown"==e&&this.keydown(t,n)||cr(t,n)&&!this.ignoreDuringComposition(n)&&(this.mustFlushObserver(n)&&t.observer.forceFlush(),this.runCustomHandlers(e,t,n)?n.preventDefault():i(t,n))})),this.registeredEvents.push(e)}this.notifiedFocused=t.hasFocus,this.ensureHandlers(t),Ti.safari&&t.contentDOM.addEventListener("input",(()=>null))}setSelectionOrigin(t){this.lastSelectionOrigin=t,this.lastSelectionTime=Date.now()}ensureHandlers(t){let e=this.customHandlers=t.pluginField(bn);for(let i of e)for(let e in i.handlers)this.registeredEvents.indexOf(e)<0&&"scroll"!=e&&(this.registeredEvents.push(e),t.contentDOM.addEventListener(e,(i=>{cr(t,i)&&this.runCustomHandlers(e,t,i)&&i.preventDefault()})))}runCustomHandlers(t,e,i){for(let n of this.customHandlers){let r=n.handlers[t];if(r)try{if(r.call(n.plugin,i,e)||i.defaultPrevented)return!0}catch(t){On(e.state,t)}}return!1}runScrollHandlers(t,e){for(let i of this.customHandlers){let n=i.handlers.scroll;if(n)try{n.call(i.plugin,e,t)}catch(e){On(t.state,e)}}}keydown(t,e){if(this.lastKeyCode=e.keyCode,this.lastKeyTime=Date.now(),this.screenKeyEvent(t,e))return!0;if(Ti.android&&Ti.chrome&&!e.synthetic&&(13==e.keyCode||8==e.keyCode))return t.observer.delayAndroidKey(e.key,e.keyCode),!0;let i;return!(!Ti.ios||!(i=ar.find((t=>t.keyCode==e.keyCode)))||e.ctrlKey||e.altKey||e.metaKey||e.synthetic||(this.pendingIOSKey=i,setTimeout((()=>this.flushIOSKey(t)),250),0))}flushIOSKey(t){let e=this.pendingIOSKey;return!!e&&(this.pendingIOSKey=void 0,ai(t.contentDOM,e.key,e.keyCode))}ignoreDuringComposition(t){return!!/^key/.test(t.type)&&(this.composing>0||!!(Ti.safari&&Date.now()-this.compositionEndedAt<500)&&(this.compositionEndedAt=0,!0))}screenKeyEvent(t,e){let i=9==e.keyCode&&Date.now()<this.lastEscPress+2e3;return 27==e.keyCode?this.lastEscPress=Date.now():lr.indexOf(e.keyCode)<0&&(this.lastEscPress=0),i}mustFlushObserver(t){return"keydown"==t.type&&229!=t.keyCode||"compositionend"==t.type&&!Ti.ios}startMouseSelection(t){this.mouseSelection&&this.mouseSelection.destroy(),this.mouseSelection=t}update(t){this.mouseSelection&&this.mouseSelection.update(t),t.transactions.length&&(this.lastKeyCode=this.lastSelectionTime=0)}destroy(){this.mouseSelection&&this.mouseSelection.destroy()}}const ar=[{key:"Backspace",keyCode:8,inputType:"deleteContentBackward"},{key:"Enter",keyCode:13,inputType:"insertParagraph"},{key:"Delete",keyCode:46,inputType:"deleteContentForward"}],lr=[16,17,18,20,91,92,224,225];class hr{constructor(t,e,i,n){this.view=t,this.style=i,this.mustSelect=n,this.lastEvent=e;let r=t.contentDOM.ownerDocument;r.addEventListener("mousemove",this.move=this.move.bind(this)),r.addEventListener("mouseup",this.up=this.up.bind(this)),this.extend=e.shiftKey,this.multiple=t.state.facet(Qe.allowMultipleSelections)&&function(t,e){let i=t.state.facet(en);return i.length?i[0](e):Ti.mac?e.metaKey:e.ctrlKey}(t,e),this.dragMove=function(t,e){let i=t.state.facet(nn);return i.length?i[0](e):Ti.mac?!e.altKey:!e.ctrlKey}(t,e),this.dragging=!(!function(t,e){let{main:i}=t.state.selection;if(i.empty)return!1;let n=Ge(t.root);if(0==n.rangeCount)return!0;let r=n.getRangeAt(0).getClientRects();for(let t=0;t<r.length;t++){let i=r[t];if(i.left<=e.clientX&&i.right>=e.clientX&&i.top<=e.clientY&&i.bottom>=e.clientY)return!0}return!1}(t,e)||1!=Sr(e))&&null,!1===this.dragging&&(e.preventDefault(),this.select(e))}move(t){if(0==t.buttons)return this.destroy();!1===this.dragging&&this.select(this.lastEvent=t)}up(t){null==this.dragging&&this.select(this.lastEvent),this.dragging||t.preventDefault(),this.destroy()}destroy(){let t=this.view.contentDOM.ownerDocument;t.removeEventListener("mousemove",this.move),t.removeEventListener("mouseup",this.up),this.view.inputState.mouseSelection=null}select(t){let e=this.style.get(t,this.extend,this.multiple);!this.mustSelect&&e.eq(this.view.state.selection)&&e.main.assoc==this.view.state.selection.main.assoc||this.view.dispatch({selection:e,userEvent:"select.pointer",scrollIntoView:!0}),this.mustSelect=!1}update(t){t.docChanged&&this.dragging&&(this.dragging=this.dragging.map(t.changes)),this.style.update(t)&&setTimeout((()=>this.select(this.lastEvent)),20)}}function cr(t,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let i,n=e.target;n!=t.contentDOM;n=n.parentNode)if(!n||11==n.nodeType||(i=ui.get(n))&&i.ignoreEvent(e))return!1;return!0}const ur=Object.create(null),Or=Ti.ie&&Ti.ie_version<15||Ti.ios&&Ti.webkit_version<604;function dr(t,e){let i,{state:n}=t,r=1,s=n.toText(e),o=s.lines==n.selection.ranges.length;if(null!=$r&&n.selection.ranges.every((t=>t.empty))&&$r==s.toString()){let t=-1;i=n.changeByRange((i=>{let a=n.doc.lineAt(i.from);if(a.from==t)return{range:i};t=a.from;let l=n.toText((o?s.line(r++).text:e)+n.lineBreak);return{changes:{from:a.from,insert:l},range:Xt.cursor(i.from+l.length)}}))}else i=o?n.changeByRange((t=>{let e=s.line(r++);return{changes:{from:t.from,to:t.to,insert:e.text},range:Xt.cursor(t.from+e.length)}})):n.replaceSelection(s);t.dispatch(i,{userEvent:"input.paste",scrollIntoView:!0})}ur.keydown=(t,e)=>{t.inputState.setSelectionOrigin("select")};let fr=0;function pr(t,e,i,n){if(1==n)return Xt.cursor(e,i);if(2==n)return function(t,e,i=1){let n=t.charCategorizer(e),r=t.doc.lineAt(e),s=e-r.from;if(0==r.length)return Xt.cursor(e);0==s?i=1:s==r.length&&(i=-1);let o=s,a=s;i<0?o=et(r.text,s,!1):a=et(r.text,s);let l=n(r.text.slice(o,a));for(;o>0;){let t=et(r.text,o,!1);if(n(r.text.slice(t,o))!=l)break;o=t}for(;a<r.length;){let t=et(r.text,a);if(n(r.text.slice(a,t))!=l)break;a=t}return Xt.range(o+r.from,a+r.from)}(t.state,e,i);{let i=Bi.find(t.docView,e),n=t.state.doc.lineAt(i?i.posAtEnd:e),r=i?i.posAtStart:n.from,s=i?i.posAtEnd:n.to;return s<t.state.doc.length&&s==n.to&&s++,Xt.range(r,s)}}ur.touchstart=(t,e)=>{fr=Date.now(),t.inputState.setSelectionOrigin("select.pointer")},ur.touchmove=t=>{t.inputState.setSelectionOrigin("select.pointer")},ur.mousedown=(t,e)=>{if(t.observer.flush(),fr>Date.now()-2e3&&1==Sr(e))return;let i=null;for(let n of t.state.facet(rn))if(i=n(t,e),i)break;if(i||0!=e.button||(i=function(t,e){let i=br(t,e),n=Sr(e),r=t.state.selection,s=i,o=e;return{update(t){t.docChanged&&(i&&(i.pos=t.changes.mapPos(i.pos)),r=r.map(t.changes),o=null)},get(e,a,l){let h;if(o&&e.clientX==o.clientX&&e.clientY==o.clientY?h=s:(h=s=br(t,e),o=e),!h||!i)return r;let c=pr(t,h.pos,h.bias,n);if(i.pos!=h.pos&&!a){let e=pr(t,i.pos,i.bias,n),r=Math.min(e.from,c.from),s=Math.max(e.to,c.to);c=r<c.from?Xt.range(r,s):Xt.range(s,r)}return a?r.replaceRange(r.main.extend(c.from,c.to)):l?r.addRange(c):Xt.create([c])}}}(t,e)),i){let n=t.root.activeElement!=t.contentDOM;n&&t.observer.ignore((()=>si(t.contentDOM))),t.inputState.startMouseSelection(new hr(t,e,i,n))}};let mr=(t,e)=>t>=e.top&&t<=e.bottom,gr=(t,e,i)=>mr(e,i)&&t>=i.left&&t<=i.right;function Qr(t,e,i,n){let r=Bi.find(t.docView,e);if(!r)return 1;let s=e-r.posAtStart;if(0==s)return 1;if(s==r.length)return-1;let o=r.coordsAt(s,-1);if(o&&gr(i,n,o))return-1;let a=r.coordsAt(s,1);return a&&gr(i,n,a)?1:o&&mr(n,o)?-1:1}function br(t,e){let i=t.posAtCoords({x:e.clientX,y:e.clientY},!1);return{pos:i,bias:Qr(t,i,e.clientX,e.clientY)}}const yr=Ti.ie&&Ti.ie_version<=11;let vr=null,xr=0,wr=0;function Sr(t){if(!yr)return t.detail;let e=vr,i=wr;return vr=t,wr=Date.now(),xr=!e||i>Date.now()-400&&Math.abs(e.clientX-t.clientX)<2&&Math.abs(e.clientY-t.clientY)<2?(xr+1)%3:1}function kr(t,e,i,n){if(!i)return;let r=t.posAtCoords({x:e.clientX,y:e.clientY},!1);e.preventDefault();let{mouseSelection:s}=t.inputState,o=n&&s&&s.dragging&&s.dragMove?{from:s.dragging.from,to:s.dragging.to}:null,a={from:r,insert:i},l=t.state.changes(o?[o,a]:a);t.focus(),t.dispatch({changes:l,selection:{anchor:l.mapPos(r,-1),head:l.mapPos(r,1)},userEvent:o?"move.drop":"input.drop"})}ur.dragstart=(t,e)=>{let{selection:{main:i}}=t.state,{mouseSelection:n}=t.inputState;n&&(n.dragging=i),e.dataTransfer&&(e.dataTransfer.setData("Text",t.state.sliceDoc(i.from,i.to)),e.dataTransfer.effectAllowed="copyMove")},ur.drop=(t,e)=>{if(!e.dataTransfer)return;if(t.state.readOnly)return e.preventDefault();let i=e.dataTransfer.files;if(i&&i.length){e.preventDefault();let n=Array(i.length),r=0,s=()=>{++r==i.length&&kr(t,e,n.filter((t=>null!=t)).join(t.state.lineBreak),!1)};for(let t=0;t<i.length;t++){let e=new FileReader;e.onerror=s,e.onload=()=>{/[\x00-\x08\x0e-\x1f]{2}/.test(e.result)||(n[t]=e.result),s()},e.readAsText(i[t])}}else kr(t,e,e.dataTransfer.getData("Text"),!0)},ur.paste=(t,e)=>{if(t.state.readOnly)return e.preventDefault();t.observer.flush();let i=Or?null:e.clipboardData;i?(dr(t,i.getData("text/plain")),e.preventDefault()):function(t){let e=t.dom.parentNode;if(!e)return;let i=e.appendChild(document.createElement("textarea"));i.style.cssText="position: fixed; left: -10000px; top: 10px",i.focus(),setTimeout((()=>{t.focus(),i.remove(),dr(t,i.value)}),50)}(t)};let $r=null;function Tr(t,e){if(t.docView.compositionDeco.size){t.inputState.rapidCompositionStart=e;try{t.update([])}finally{t.inputState.rapidCompositionStart=!1}}}ur.copy=ur.cut=(t,e)=>{let{text:i,ranges:n,linewise:r}=function(t){let e=[],i=[],n=!1;for(let n of t.selection.ranges)n.empty||(e.push(t.sliceDoc(n.from,n.to)),i.push(n));if(!e.length){let r=-1;for(let{from:n}of t.selection.ranges){let s=t.doc.lineAt(n);s.number>r&&(e.push(s.text),i.push({from:s.from,to:Math.min(t.doc.length,s.to+1)})),r=s.number}n=!0}return{text:e.join(t.lineBreak),ranges:i,linewise:n}}(t.state);if(!i&&!r)return;$r=r?i:null;let s=Or?null:e.clipboardData;s?(e.preventDefault(),s.clearData(),s.setData("text/plain",i)):function(t,e){let i=t.dom.parentNode;if(!i)return;let n=i.appendChild(document.createElement("textarea"));n.style.cssText="position: fixed; left: -10000px; top: 10px",n.value=e,n.focus(),n.selectionEnd=e.length,n.selectionStart=0,setTimeout((()=>{n.remove(),t.focus()}),50)}(t,i),"cut"!=e.type||t.state.readOnly||t.dispatch({changes:n,scrollIntoView:!0,userEvent:"delete.cut"})},ur.focus=ur.blur=t=>{setTimeout((()=>{t.hasFocus!=t.inputState.notifiedFocused&&t.update([])}),10)},ur.beforeprint=t=>{t.viewState.printing=!0,t.requestMeasure(),setTimeout((()=>{t.viewState.printing=!1,t.requestMeasure()}),2e3)},ur.compositionstart=ur.compositionupdate=t=>{null==t.inputState.compositionFirstChange&&(t.inputState.compositionFirstChange=!0),t.inputState.composing<0&&(t.inputState.composing=0,t.docView.compositionDeco.size&&(t.observer.flush(),Tr(t,!0)))},ur.compositionend=t=>{t.inputState.composing=-1,t.inputState.compositionEndedAt=Date.now(),t.inputState.compositionFirstChange=null,setTimeout((()=>{t.inputState.composing<0&&Tr(t,!1)}),50)},ur.contextmenu=t=>{t.inputState.lastContextMenu=Date.now()},ur.beforeinput=(t,e)=>{var i;let n;if(Ti.chrome&&Ti.android&&(n=ar.find((t=>t.inputType==e.inputType)))&&(t.observer.delayAndroidKey(n.key,n.keyCode),"Backspace"==n.key||"Delete"==n.key)){let e=(null===(i=window.visualViewport)||void 0===i?void 0:i.height)||0;setTimeout((()=>{var i;((null===(i=window.visualViewport)||void 0===i?void 0:i.height)||0)>e+10&&t.hasFocus&&(t.contentDOM.blur(),t.focus())}),100)}};const Pr=["pre-wrap","normal","pre-line","break-spaces"];class Rr{constructor(){this.doc=ct.empty,this.lineWrapping=!1,this.direction=Pn.LTR,this.heightSamples={},this.lineHeight=14,this.charWidth=7,this.lineLength=30,this.heightChanged=!1}heightForGap(t,e){let i=this.doc.lineAt(e).number-this.doc.lineAt(t).number+1;return this.lineWrapping&&(i+=Math.ceil((e-t-i*this.lineLength*.5)/this.lineLength)),this.lineHeight*i}heightForLine(t){return this.lineWrapping?(1+Math.max(0,Math.ceil((t-this.lineLength)/(this.lineLength-5))))*this.lineHeight:this.lineHeight}setDoc(t){return this.doc=t,this}mustRefreshForStyle(t,e){return Pr.indexOf(t)>-1!=this.lineWrapping||this.direction!=e}mustRefreshForHeights(t){let e=!1;for(let i=0;i<t.length;i++){let n=t[i];n<0?i++:this.heightSamples[Math.floor(10*n)]||(e=!0,this.heightSamples[Math.floor(10*n)]=!0)}return e}refresh(t,e,i,n,r,s){let o=Pr.indexOf(t)>-1,a=Math.round(i)!=Math.round(this.lineHeight)||this.lineWrapping!=o||this.direction!=e;if(this.lineWrapping=o,this.direction=e,this.lineHeight=i,this.charWidth=n,this.lineLength=r,a){this.heightSamples={};for(let t=0;t<s.length;t++){let e=s[t];e<0?t++:this.heightSamples[Math.floor(10*e)]=!0}}return a}}class Wr{constructor(t,e){this.from=t,this.heights=e,this.index=0}get more(){return this.index<this.heights.length}}class Xr{constructor(t,e,i,n,r){this.from=t,this.length=e,this.top=i,this.height=n,this.type=r}get to(){return this.from+this.length}get bottom(){return this.top+this.height}join(t){let e=(Array.isArray(this.type)?this.type:[this]).concat(Array.isArray(t.type)?t.type:[t]);return new Xr(this.from,this.length+t.length,this.top,this.height+t.height,e)}moveY(t){return t?new Xr(this.from,this.length,this.top+t,this.height,Array.isArray(this.type)?this.type.map((e=>e.moveY(t))):this.type):this}}var Cr,Ar=((Cr=Ar||(Ar={}))[Cr.ByPos=0]="ByPos",Cr[Cr.ByHeight=1]="ByHeight",Cr[Cr.ByPosNoHeight=2]="ByPosNoHeight",Cr);class Zr{constructor(t,e,i=2){this.length=t,this.height=e,this.flags=i}get outdated(){return(2&this.flags)>0}set outdated(t){this.flags=(t?2:0)|-3&this.flags}setHeight(t,e){this.height!=e&&(Math.abs(this.height-e)>.001&&(t.heightChanged=!0),this.height=e)}replace(t,e,i){return Zr.of(i)}decomposeLeft(t,e){e.push(this)}decomposeRight(t,e){e.push(this)}applyChanges(t,e,i,n){let r=this;for(let s=n.length-1;s>=0;s--){let{fromA:o,toA:a,fromB:l,toB:h}=n[s],c=r.lineAt(o,Ar.ByPosNoHeight,e,0,0),u=c.to>=a?c:r.lineAt(a,Ar.ByPosNoHeight,e,0,0);for(h+=u.to-a,a=u.to;s>0&&c.from<=n[s-1].toA;)o=n[s-1].fromA,l=n[s-1].fromB,s--,o<c.from&&(c=r.lineAt(o,Ar.ByPosNoHeight,e,0,0));l+=c.from-o,o=c.from;let O=Mr.build(i,t,l,h);r=r.replace(o,a,O)}return r.updateHeight(i,0)}static empty(){return new Lr(0,0)}static of(t){if(1==t.length)return t[0];let e=0,i=t.length,n=0,r=0;for(;;)if(e==i)if(n>2*r){let r=t[e-1];r.break?t.splice(--e,1,r.left,null,r.right):t.splice(--e,1,r.left,r.right),i+=1+r.break,n-=r.size}else{if(!(r>2*n))break;{let e=t[i];e.break?t.splice(i,1,e.left,null,e.right):t.splice(i,1,e.left,e.right),i+=2+e.break,r-=e.size}}else if(n<r){let i=t[e++];i&&(n+=i.size)}else{let e=t[--i];e&&(r+=e.size)}let s=0;return null==t[e-1]?(s=1,e--):null==t[e]&&(s=1,i++),new zr(Zr.of(t.slice(0,e)),s,Zr.of(t.slice(i)))}}Zr.prototype.size=1;class _r extends Zr{constructor(t,e,i){super(t,e),this.type=i}blockAt(t,e,i,n){return new Xr(n,this.length,i,this.height,this.type)}lineAt(t,e,i,n,r){return this.blockAt(0,i,n,r)}forEachLine(t,e,i,n,r,s){s(this.blockAt(0,i,n,r))}updateHeight(t,e=0,i=!1,n){return n&&n.from<=e&&n.more&&this.setHeight(t,n.heights[n.index++]),this.outdated=!1,this}toString(){return`block(${this.length})`}}class Lr extends _r{constructor(t,e){super(t,e,qi.Text),this.collapsed=0,this.widgetHeight=0}replace(t,e,i){let n=i[0];return 1==i.length&&(n instanceof Lr||n instanceof Dr&&4&n.flags)&&Math.abs(this.length-n.length)<10?(n instanceof Dr?n=new Lr(n.length,this.height):n.height=this.height,this.outdated||(n.outdated=!1),n):Zr.of(i)}updateHeight(t,e=0,i=!1,n){return n&&n.from<=e&&n.more?this.setHeight(t,n.heights[n.index++]):(i||this.outdated)&&this.setHeight(t,Math.max(this.widgetHeight,t.heightForLine(this.length-this.collapsed))),this.outdated=!1,this}toString(){return`line(${this.length}${this.collapsed?-this.collapsed:""}${this.widgetHeight?":"+this.widgetHeight:""})`}}class Dr extends Zr{constructor(t){super(t,0)}lines(t,e){let i=t.lineAt(e).number,n=t.lineAt(e+this.length).number;return{firstLine:i,lastLine:n,lineHeight:this.height/(n-i+1)}}blockAt(t,e,i,n){let{firstLine:r,lastLine:s,lineHeight:o}=this.lines(e,n),a=Math.max(0,Math.min(s-r,Math.floor((t-i)/o))),{from:l,length:h}=e.line(r+a);return new Xr(l,h,i+o*a,o,qi.Text)}lineAt(t,e,i,n,r){if(e==Ar.ByHeight)return this.blockAt(t,i,n,r);if(e==Ar.ByPosNoHeight){let{from:e,to:n}=i.lineAt(t);return new Xr(e,n-e,0,0,qi.Text)}let{firstLine:s,lineHeight:o}=this.lines(i,r),{from:a,length:l,number:h}=i.lineAt(t);return new Xr(a,l,n+o*(h-s),o,qi.Text)}forEachLine(t,e,i,n,r,s){let{firstLine:o,lineHeight:a}=this.lines(i,r);for(let l=Math.max(t,r),h=Math.min(r+this.length,e);l<=h;){let e=i.lineAt(l);l==t&&(n+=a*(e.number-o)),s(new Xr(e.from,e.length,n,a,qi.Text)),n+=a,l=e.to+1}}replace(t,e,i){let n=this.length-e;if(n>0){let t=i[i.length-1];t instanceof Dr?i[i.length-1]=new Dr(t.length+n):i.push(null,new Dr(n-1))}if(t>0){let e=i[0];e instanceof Dr?i[0]=new Dr(t+e.length):i.unshift(new Dr(t-1),null)}return Zr.of(i)}decomposeLeft(t,e){e.push(new Dr(t-1),null)}decomposeRight(t,e){e.push(null,new Dr(this.length-t-1))}updateHeight(t,e=0,i=!1,n){let r=e+this.length;if(n&&n.from<=e+this.length&&n.more){let i=[],s=Math.max(e,n.from),o=-1,a=t.heightChanged;for(n.from>e&&i.push(new Dr(n.from-e-1).updateHeight(t,e));s<=r&&n.more;){let e=t.doc.lineAt(s).length;i.length&&i.push(null);let r=n.heights[n.index++];-1==o?o=r:Math.abs(r-o)>=.001&&(o=-2);let a=new Lr(e,r);a.outdated=!1,i.push(a),s+=e+1}s<=r&&i.push(null,new Dr(r-s).updateHeight(t,s));let l=Zr.of(i);return t.heightChanged=a||o<0||Math.abs(l.height-this.height)>=.001||Math.abs(o-this.lines(t.doc,e).lineHeight)>=.001,l}return(i||this.outdated)&&(this.setHeight(t,t.heightForGap(e,e+this.length)),this.outdated=!1),this}toString(){return`gap(${this.length})`}}class zr extends Zr{constructor(t,e,i){super(t.length+e+i.length,t.height+i.height,e|(t.outdated||i.outdated?2:0)),this.left=t,this.right=i,this.size=t.size+i.size}get break(){return 1&this.flags}blockAt(t,e,i,n){let r=i+this.left.height;return t<r?this.left.blockAt(t,e,i,n):this.right.blockAt(t,e,r,n+this.left.length+this.break)}lineAt(t,e,i,n,r){let s=n+this.left.height,o=r+this.left.length+this.break,a=e==Ar.ByHeight?t<s:t<o,l=a?this.left.lineAt(t,e,i,n,r):this.right.lineAt(t,e,i,s,o);if(this.break||(a?l.to<o:l.from>o))return l;let h=e==Ar.ByPosNoHeight?Ar.ByPosNoHeight:Ar.ByPos;return a?l.join(this.right.lineAt(o,h,i,s,o)):this.left.lineAt(o,h,i,n,r).join(l)}forEachLine(t,e,i,n,r,s){let o=n+this.left.height,a=r+this.left.length+this.break;if(this.break)t<a&&this.left.forEachLine(t,e,i,n,r,s),e>=a&&this.right.forEachLine(t,e,i,o,a,s);else{let l=this.lineAt(a,Ar.ByPos,i,n,r);t<l.from&&this.left.forEachLine(t,l.from-1,i,n,r,s),l.to>=t&&l.from<=e&&s(l),e>l.to&&this.right.forEachLine(l.to+1,e,i,o,a,s)}}replace(t,e,i){let n=this.left.length+this.break;if(e<n)return this.balanced(this.left.replace(t,e,i),this.right);if(t>this.left.length)return this.balanced(this.left,this.right.replace(t-n,e-n,i));let r=[];t>0&&this.decomposeLeft(t,r);let s=r.length;for(let t of i)r.push(t);if(t>0&&Er(r,s-1),e<this.length){let t=r.length;this.decomposeRight(e,r),Er(r,t)}return Zr.of(r)}decomposeLeft(t,e){let i=this.left.length;if(t<=i)return this.left.decomposeLeft(t,e);e.push(this.left),this.break&&(i++,t>=i&&e.push(null)),t>i&&this.right.decomposeLeft(t-i,e)}decomposeRight(t,e){let i=this.left.length,n=i+this.break;if(t>=n)return this.right.decomposeRight(t-n,e);t<i&&this.left.decomposeRight(t,e),this.break&&t<n&&e.push(null),e.push(this.right)}balanced(t,e){return t.size>2*e.size||e.size>2*t.size?Zr.of(this.break?[t,null,e]:[t,e]):(this.left=t,this.right=e,this.height=t.height+e.height,this.outdated=t.outdated||e.outdated,this.size=t.size+e.size,this.length=t.length+this.break+e.length,this)}updateHeight(t,e=0,i=!1,n){let{left:r,right:s}=this,o=e+r.length+this.break,a=null;return n&&n.from<=e+r.length&&n.more?a=r=r.updateHeight(t,e,i,n):r.updateHeight(t,e,i),n&&n.from<=o+s.length&&n.more?a=s=s.updateHeight(t,o,i,n):s.updateHeight(t,o,i),a?this.balanced(r,s):(this.height=this.left.height+this.right.height,this.outdated=!1,this)}toString(){return this.left+(this.break?" ":"-")+this.right}}function Er(t,e){let i,n;null==t[e]&&(i=t[e-1])instanceof Dr&&(n=t[e+1])instanceof Dr&&t.splice(e-1,3,new Dr(i.length+1+n.length))}class Mr{constructor(t,e){this.pos=t,this.oracle=e,this.nodes=[],this.lineStart=-1,this.lineEnd=-1,this.covering=null,this.writtenTo=t}get isCovered(){return this.covering&&this.nodes[this.nodes.length-1]==this.covering}span(t,e){if(this.lineStart>-1){let t=Math.min(e,this.lineEnd),i=this.nodes[this.nodes.length-1];i instanceof Lr?i.length+=t-this.pos:(t>this.pos||!this.isCovered)&&this.nodes.push(new Lr(t-this.pos,-1)),this.writtenTo=t,e>t&&(this.nodes.push(null),this.writtenTo++,this.lineStart=-1)}this.pos=e}point(t,e,i){if(t<e||i.heightRelevant){let n=i.widget?i.widget.estimatedHeight:0;n<0&&(n=this.oracle.lineHeight);let r=e-t;i.block?this.addBlock(new _r(r,n,i.type)):(r||n>=5)&&this.addLineDeco(n,r)}else e>t&&this.span(t,e);this.lineEnd>-1&&this.lineEnd<this.pos&&(this.lineEnd=this.oracle.doc.lineAt(this.pos).to)}enterLine(){if(this.lineStart>-1)return;let{from:t,to:e}=this.oracle.doc.lineAt(this.pos);this.lineStart=t,this.lineEnd=e,this.writtenTo<t&&((this.writtenTo<t-1||null==this.nodes[this.nodes.length-1])&&this.nodes.push(this.blankContent(this.writtenTo,t-1)),this.nodes.push(null)),this.pos>t&&this.nodes.push(new Lr(this.pos-t,-1)),this.writtenTo=this.pos}blankContent(t,e){let i=new Dr(e-t);return this.oracle.doc.lineAt(t).to==e&&(i.flags|=4),i}ensureLine(){this.enterLine();let t=this.nodes.length?this.nodes[this.nodes.length-1]:null;if(t instanceof Lr)return t;let e=new Lr(0,-1);return this.nodes.push(e),e}addBlock(t){this.enterLine(),t.type!=qi.WidgetAfter||this.isCovered||this.ensureLine(),this.nodes.push(t),this.writtenTo=this.pos=this.pos+t.length,t.type!=qi.WidgetBefore&&(this.covering=t)}addLineDeco(t,e){let i=this.ensureLine();i.length+=e,i.collapsed+=e,i.widgetHeight=Math.max(i.widgetHeight,t),this.writtenTo=this.pos=this.pos+e}finish(t){let e=0==this.nodes.length?null:this.nodes[this.nodes.length-1];!(this.lineStart>-1)||e instanceof Lr||this.isCovered?(this.writtenTo<this.pos||null==e)&&this.nodes.push(this.blankContent(this.writtenTo,this.pos)):this.nodes.push(new Lr(0,-1));let i=t;for(let t of this.nodes)t instanceof Lr&&t.updateHeight(this.oracle,i),i+=t?t.length:1;return this.nodes}static build(t,e,i,n){let r=new Mr(i,t);return Se.spans(e,i,n,r,0),r.finish(i)}}class jr{constructor(){this.changes=[]}compareRange(){}comparePoint(t,e,i,n){(t<e||i&&i.heightRelevant||n&&n.heightRelevant)&&Ni(t,e,this.changes,5)}}class qr{constructor(t,e,i){this.from=t,this.to=e,this.size=i}static same(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++){let n=t[i],r=e[i];if(n.from!=r.from||n.to!=r.to||n.size!=r.size)return!1}return!0}draw(t){return Yi.replace({widget:new Yr(this.size,t)}).range(this.from,this.to)}}class Yr extends Mi{constructor(t,e){super(),this.size=t,this.vertical=e}eq(t){return t.size==this.size&&t.vertical==this.vertical}toDOM(){let t=document.createElement("div");return this.vertical?t.style.height=this.size+"px":(t.style.width=this.size+"px",t.style.height="2px",t.style.display="inline-block"),t}get estimatedHeight(){return this.vertical?this.size:-1}}class Vr{constructor(t){this.state=t,this.pixelViewport={left:0,right:window.innerWidth,top:0,bottom:0},this.inView=!0,this.paddingTop=0,this.paddingBottom=0,this.contentDOMWidth=0,this.contentDOMHeight=0,this.editorHeight=0,this.editorWidth=0,this.heightOracle=new Rr,this.scaler=Hr,this.scrollTarget=null,this.printing=!1,this.mustMeasureContent=!0,this.visibleRanges=[],this.mustEnforceCursorAssoc=!1,this.heightMap=Zr.empty().applyChanges(t.facet(wn),ct.empty,this.heightOracle.setDoc(t.doc),[new kn(0,0,0,t.doc.length)]),this.viewport=this.getViewport(0,null),this.updateViewportLines(),this.updateForViewport(),this.lineGaps=this.ensureLineGaps([]),this.lineGapDeco=Yi.set(this.lineGaps.map((t=>t.draw(!1)))),this.computeVisibleRanges()}updateForViewport(){let t=[this.viewport],{main:e}=this.state.selection;for(let i=0;i<=1;i++){let n=i?e.head:e.anchor;if(!t.some((({from:t,to:e})=>n>=t&&n<=e))){let{from:e,to:i}=this.lineBlockAt(n);t.push(new Ur(e,i))}}this.viewports=t.sort(((t,e)=>t.from-e.from)),this.scaler=this.heightMap.height<=7e6?Hr:new Jr(this.heightOracle.doc,this.heightMap,this.viewports)}updateViewportLines(){this.viewportLines=[],this.heightMap.forEachLine(this.viewport.from,this.viewport.to,this.state.doc,0,0,(t=>{this.viewportLines.push(1==this.scaler.scale?t:Kr(t,this.scaler))}))}update(t,e=null){let i=this.state;this.state=t.state;let n=this.state.facet(wn),r=t.changedRanges,s=kn.extendWithRanges(r,function(t,e,i){let n=new jr;return Se.compare(t,e,i,n,0),n.changes}(t.startState.facet(wn),n,t?t.changes:wt.empty(this.state.doc.length))),o=this.heightMap.height;this.heightMap=this.heightMap.applyChanges(n,i.doc,this.heightOracle.setDoc(this.state.doc),s),this.heightMap.height!=o&&(t.flags|=2);let a=s.length?this.mapViewport(this.viewport,t.changes):this.viewport;(e&&(e.range.head<a.from||e.range.head>a.to)||!this.viewportIsAppropriate(a))&&(a=this.getViewport(0,e));let l=!t.changes.empty||2&t.flags||a.from!=this.viewport.from||a.to!=this.viewport.to;this.viewport=a,this.updateForViewport(),l&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(this.mapLineGaps(this.lineGaps,t.changes))),t.flags|=this.computeVisibleRanges(),e&&(this.scrollTarget=e),!this.mustEnforceCursorAssoc&&t.selectionSet&&t.view.lineWrapping&&t.state.selection.main.empty&&t.state.selection.main.assoc&&(this.mustEnforceCursorAssoc=!0)}measure(t){let e=t.contentDOM,i=window.getComputedStyle(e),n=this.heightOracle,r=i.whiteSpace,s="rtl"==i.direction?Pn.RTL:Pn.LTR,o=this.heightOracle.mustRefreshForStyle(r,s),a=o||this.mustMeasureContent||this.contentDOMHeight!=e.clientHeight,l=0,h=0;if(this.editorWidth!=t.scrollDOM.clientWidth&&(n.lineWrapping&&(a=!0),this.editorWidth=t.scrollDOM.clientWidth,l|=8),a){this.mustMeasureContent=!1,this.contentDOMHeight=e.clientHeight;let t=parseInt(i.paddingTop)||0,n=parseInt(i.paddingBottom)||0;this.paddingTop==t&&this.paddingBottom==n||(l|=8,this.paddingTop=t,this.paddingBottom=n)}let c=this.printing?{top:-1e8,bottom:1e8,left:-1e8,right:1e8}:function(t,e){let i=t.getBoundingClientRect(),n=Math.max(0,i.left),r=Math.min(innerWidth,i.right),s=Math.max(0,i.top),o=Math.min(innerHeight,i.bottom),a=t.ownerDocument.body;for(let e=t.parentNode;e&&e!=a;)if(1==e.nodeType){let t=e,i=window.getComputedStyle(t);if((t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth)&&"visible"!=i.overflow){let e=t.getBoundingClientRect();n=Math.max(n,e.left),r=Math.min(r,e.right),s=Math.max(s,e.top),o=Math.min(o,e.bottom)}e="absolute"==i.position||"fixed"==i.position?t.offsetParent:t.parentNode}else{if(11!=e.nodeType)break;e=e.host}return{left:n-i.left,right:Math.max(n,r)-i.left,top:s-(i.top+e),bottom:Math.max(s,o)-(i.top+e)}}(e,this.paddingTop),u=c.top-this.pixelViewport.top,O=c.bottom-this.pixelViewport.bottom;this.pixelViewport=c;let d=this.pixelViewport.bottom>this.pixelViewport.top&&this.pixelViewport.right>this.pixelViewport.left;if(d!=this.inView&&(this.inView=d,d&&(a=!0)),!this.inView)return 0;let f=e.clientWidth;if(this.contentDOMWidth==f&&this.editorHeight==t.scrollDOM.clientHeight||(this.contentDOMWidth=f,this.editorHeight=t.scrollDOM.clientHeight,l|=8),a){let e=t.docView.measureVisibleLineHeights();if(n.mustRefreshForHeights(e)&&(o=!0),o||n.lineWrapping&&Math.abs(f-this.contentDOMWidth)>n.charWidth){let{lineHeight:i,charWidth:a}=t.docView.measureTextSize();o=n.refresh(r,s,i,a,f/a,e),o&&(t.docView.minWidth=0,l|=8)}u>0&&O>0?h=Math.max(u,O):u<0&&O<0&&(h=Math.min(u,O)),n.heightChanged=!1,this.heightMap=this.heightMap.updateHeight(n,0,o,new Wr(this.viewport.from,e)),n.heightChanged&&(l|=2)}let p=!this.viewportIsAppropriate(this.viewport,h)||this.scrollTarget&&(this.scrollTarget.range.head<this.viewport.from||this.scrollTarget.range.head>this.viewport.to);return p&&(this.viewport=this.getViewport(h,this.scrollTarget)),this.updateForViewport(),(2&l||p)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(o?[]:this.lineGaps)),l|=this.computeVisibleRanges(),this.mustEnforceCursorAssoc&&(this.mustEnforceCursorAssoc=!1,t.docView.enforceCursorAssoc()),l}get visibleTop(){return this.scaler.fromDOM(this.pixelViewport.top)}get visibleBottom(){return this.scaler.fromDOM(this.pixelViewport.bottom)}getViewport(t,e){let i=.5-Math.max(-.5,Math.min(.5,t/1e3/2)),n=this.heightMap,r=this.state.doc,{visibleTop:s,visibleBottom:o}=this,a=new Ur(n.lineAt(s-1e3*i,Ar.ByHeight,r,0,0).from,n.lineAt(o+1e3*(1-i),Ar.ByHeight,r,0,0).to);if(e){let{head:t}=e.range;if(t<a.from||t>a.to){let i,s=Math.min(this.editorHeight,this.pixelViewport.bottom-this.pixelViewport.top),o=n.lineAt(t,Ar.ByPos,r,0,0);i="center"==e.y?(o.top+o.bottom)/2-s/2:"start"==e.y||"nearest"==e.y&&t<a.from?o.top:o.bottom-s,a=new Ur(n.lineAt(i-500,Ar.ByHeight,r,0,0).from,n.lineAt(i+s+500,Ar.ByHeight,r,0,0).to)}}return a}mapViewport(t,e){let i=e.mapPos(t.from,-1),n=e.mapPos(t.to,1);return new Ur(this.heightMap.lineAt(i,Ar.ByPos,this.state.doc,0,0).from,this.heightMap.lineAt(n,Ar.ByPos,this.state.doc,0,0).to)}viewportIsAppropriate({from:t,to:e},i=0){if(!this.inView)return!0;let{top:n}=this.heightMap.lineAt(t,Ar.ByPos,this.state.doc,0,0),{bottom:r}=this.heightMap.lineAt(e,Ar.ByPos,this.state.doc,0,0),{visibleTop:s,visibleBottom:o}=this;return(0==t||n<=s-Math.max(10,Math.min(-i,250)))&&(e==this.state.doc.length||r>=o+Math.max(10,Math.min(i,250)))&&n>s-2e3&&r<o+2e3}mapLineGaps(t,e){if(!t.length||e.empty)return t;let i=[];for(let n of t)e.touchesRange(n.from,n.to)||i.push(new qr(e.mapPos(n.from),e.mapPos(n.to),n.size));return i}ensureLineGaps(t){let e=[];if(this.heightOracle.direction!=Pn.LTR)return e;for(let i of this.viewportLines){if(i.length<4e3)continue;let n,r,s=Gr(i.from,i.to,this.state);if(s.total<4e3)continue;if(this.heightOracle.lineWrapping){let t=2e3/this.heightOracle.lineLength*this.heightOracle.lineHeight;n=Ir(s,(this.visibleTop-i.top-t)/i.height),r=Ir(s,(this.visibleBottom-i.top+t)/i.height)}else{let t=s.total*this.heightOracle.charWidth,e=2e3*this.heightOracle.charWidth;n=Ir(s,(this.pixelViewport.left-e)/t),r=Ir(s,(this.pixelViewport.right+e)/t)}let o=[];n>i.from&&o.push({from:i.from,to:n}),r<i.to&&o.push({from:r,to:i.to});let a=this.state.selection.main;a.from>=i.from&&a.from<=i.to&&Br(o,a.from-10,a.from+10),!a.empty&&a.to>=i.from&&a.to<=i.to&&Br(o,a.to-10,a.to+10);for(let{from:n,to:r}of o)r-n>1e3&&e.push(Fr(t,(t=>t.from>=i.from&&t.to<=i.to&&Math.abs(t.from-n)<1e3&&Math.abs(t.to-r)<1e3))||new qr(n,r,this.gapSize(i,n,r,s)))}return e}gapSize(t,e,i,n){let r=Nr(n,i)-Nr(n,e);return this.heightOracle.lineWrapping?t.height*r:n.total*this.heightOracle.charWidth*r}updateLineGaps(t){qr.same(t,this.lineGaps)||(this.lineGaps=t,this.lineGapDeco=Yi.set(t.map((t=>t.draw(this.heightOracle.lineWrapping)))))}computeVisibleRanges(){let t=this.state.facet(wn);this.lineGaps.length&&(t=t.concat(this.lineGapDeco));let e=[];Se.spans(t,this.viewport.from,this.viewport.to,{span(t,i){e.push({from:t,to:i})},point(){}},20);let i=e.length!=this.visibleRanges.length||this.visibleRanges.some(((t,i)=>t.from!=e[i].from||t.to!=e[i].to));return this.visibleRanges=e,i?4:0}lineBlockAt(t){return t>=this.viewport.from&&t<=this.viewport.to&&this.viewportLines.find((e=>e.from<=t&&e.to>=t))||Kr(this.heightMap.lineAt(t,Ar.ByPos,this.state.doc,0,0),this.scaler)}lineBlockAtHeight(t){return Kr(this.heightMap.lineAt(this.scaler.fromDOM(t),Ar.ByHeight,this.state.doc,0,0),this.scaler)}elementAtHeight(t){return Kr(this.heightMap.blockAt(this.scaler.fromDOM(t),this.state.doc,0,0),this.scaler)}get docHeight(){return this.scaler.toDOM(this.heightMap.height)}get contentHeight(){return this.docHeight+this.paddingTop+this.paddingBottom}}class Ur{constructor(t,e){this.from=t,this.to=e}}function Gr(t,e,i){let n=[],r=t,s=0;return Se.spans(i.facet(wn),t,e,{span(){},point(t,e){t>r&&(n.push({from:r,to:t}),s+=t-r),r=e}},20),r<e&&(n.push({from:r,to:e}),s+=e-r),{total:s,ranges:n}}function Ir({total:t,ranges:e},i){if(i<=0)return e[0].from;if(i>=1)return e[e.length-1].to;let n=Math.floor(t*i);for(let t=0;;t++){let{from:i,to:r}=e[t],s=r-i;if(n<=s)return i+n;n-=s}}function Nr(t,e){let i=0;for(let{from:n,to:r}of t.ranges){if(e<=r){i+=e-n;break}i+=r-n}return i/t.total}function Br(t,e,i){for(let n=0;n<t.length;n++){let r=t[n];if(r.from<i&&r.to>e){let s=[];r.from<e&&s.push({from:r.from,to:e}),r.to>i&&s.push({from:i,to:r.to}),t.splice(n,1,...s),n+=s.length-1}}}function Fr(t,e){for(let i of t)if(e(i))return i}const Hr={toDOM:t=>t,fromDOM:t=>t,scale:1};class Jr{constructor(t,e,i){let n=0,r=0,s=0;this.viewports=i.map((({from:i,to:r})=>{let s=e.lineAt(i,Ar.ByPos,t,0,0).top,o=e.lineAt(r,Ar.ByPos,t,0,0).bottom;return n+=o-s,{from:i,to:r,top:s,bottom:o,domTop:0,domBottom:0}})),this.scale=(7e6-n)/(e.height-n);for(let t of this.viewports)t.domTop=s+(t.top-r)*this.scale,s=t.domBottom=t.domTop+(t.bottom-t.top),r=t.bottom}toDOM(t){for(let e=0,i=0,n=0;;e++){let r=e<this.viewports.length?this.viewports[e]:null;if(!r||t<r.top)return n+(t-i)*this.scale;if(t<=r.bottom)return r.domTop+(t-r.top);i=r.bottom,n=r.domBottom}}fromDOM(t){for(let e=0,i=0,n=0;;e++){let r=e<this.viewports.length?this.viewports[e]:null;if(!r||t<r.domTop)return i+(t-n)/this.scale;if(t<=r.domBottom)return r.top+(t-r.domTop);i=r.bottom,n=r.domBottom}}}function Kr(t,e){if(1==e.scale)return t;let i=e.toDOM(t.top),n=e.toDOM(t.bottom);return new Xr(t.from,t.length,i,n-i,Array.isArray(t.type)?t.type.map((t=>Kr(t,e))):t.type)}const ts=_t.define({combine:t=>t.join(" ")}),es=_t.define({combine:t=>t.indexOf(!0)>-1}),is=B.newName(),ns=B.newName(),rs=B.newName(),ss={"&light":"."+ns,"&dark":"."+rs};function os(t,e,i){return new B(e,{finish:e=>/&/.test(e)?e.replace(/&\w*/,(e=>{if("&"==e)return t;if(!i||!i[e])throw new RangeError(`Unsupported selector: ${e}`);return i[e]})):t+" "+e})}const as=os("."+is,{"&.cm-editor":{position:"relative !important",boxSizing:"border-box","&.cm-focused":{outline:"1px dotted #212121"},display:"flex !important",flexDirection:"column"},".cm-scroller":{display:"flex !important",alignItems:"flex-start !important",fontFamily:"monospace",lineHeight:1.4,height:"100%",overflowX:"auto",position:"relative",zIndex:0},".cm-content":{margin:0,flexGrow:2,minHeight:"100%",display:"block",whiteSpace:"pre",wordWrap:"normal",boxSizing:"border-box",padding:"4px 0",outline:"none","&[contenteditable=true]":{WebkitUserModify:"read-write-plaintext-only"}},".cm-lineWrapping":{whiteSpace_fallback:"pre-wrap",whiteSpace:"break-spaces",wordBreak:"break-word",overflowWrap:"anywhere"},"&light .cm-content":{caretColor:"black"},"&dark .cm-content":{caretColor:"white"},".cm-line":{display:"block",padding:"0 2px 0 4px"},".cm-selectionLayer":{zIndex:-1,contain:"size style"},".cm-selectionBackground":{position:"absolute"},"&light .cm-selectionBackground":{background:"#d9d9d9"},"&dark .cm-selectionBackground":{background:"#222"},"&light.cm-focused .cm-selectionBackground":{background:"#d7d4f0"},"&dark.cm-focused .cm-selectionBackground":{background:"#233"},".cm-cursorLayer":{zIndex:100,contain:"size style",pointerEvents:"none"},"&.cm-focused .cm-cursorLayer":{animation:"steps(1) cm-blink 1.2s infinite"},"@keyframes cm-blink":{"0%":{},"50%":{visibility:"hidden"},"100%":{}},"@keyframes cm-blink2":{"0%":{},"50%":{visibility:"hidden"},"100%":{}},".cm-cursor, .cm-dropCursor":{position:"absolute",borderLeft:"1.2px solid black",marginLeft:"-0.6px",pointerEvents:"none"},".cm-cursor":{display:"none"},"&dark .cm-cursor":{borderLeftColor:"#444"},"&.cm-focused .cm-cursor":{display:"block"},"&light .cm-activeLine":{backgroundColor:"#f3f9ff"},"&dark .cm-activeLine":{backgroundColor:"#223039"},"&light .cm-specialChar":{color:"red"},"&dark .cm-specialChar":{color:"#f78"},".cm-tab":{display:"inline-block",overflow:"hidden",verticalAlign:"bottom"},".cm-widgetBuffer":{verticalAlign:"text-bottom",height:"1em"},".cm-placeholder":{color:"#888",display:"inline-block",verticalAlign:"top"},".cm-button":{verticalAlign:"middle",color:"inherit",fontSize:"70%",padding:".2em 1em",borderRadius:"1px"},"&light .cm-button":{backgroundImage:"linear-gradient(#eff1f5, #d9d9df)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#b4b4b4, #d0d3d6)"}},"&dark .cm-button":{backgroundImage:"linear-gradient(#393939, #111)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#111, #333)"}},".cm-textfield":{verticalAlign:"middle",color:"inherit",fontSize:"70%",border:"1px solid silver",padding:".2em .5em"},"&light .cm-textfield":{backgroundColor:"white"},"&dark .cm-textfield":{border:"1px solid #555",backgroundColor:"inherit"}},ss),ls={childList:!0,characterData:!0,subtree:!0,attributes:!0,characterDataOldValue:!0},hs=Ti.ie&&Ti.ie_version<=11;class cs{constructor(t,e,i){this.view=t,this.onChange=e,this.onScrollChanged=i,this.active=!1,this.selectionRange=new ii,this.selectionChanged=!1,this.delayedFlush=-1,this.resizeTimeout=-1,this.queue=[],this.delayedAndroidKey=null,this.scrollTargets=[],this.intersection=null,this.resize=null,this.intersecting=!1,this.gapIntersection=null,this.gaps=[],this.parentCheck=-1,this.dom=t.contentDOM,this.observer=new MutationObserver((e=>{for(let t of e)this.queue.push(t);(Ti.ie&&Ti.ie_version<=11||Ti.ios&&t.composing)&&e.some((t=>"childList"==t.type&&t.removedNodes.length||"characterData"==t.type&&t.oldValue.length>t.target.nodeValue.length))?this.flushSoon():this.flush()})),hs&&(this.onCharData=t=>{this.queue.push({target:t.target,type:"characterData",oldValue:t.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this),"function"==typeof ResizeObserver&&(this.resize=new ResizeObserver((()=>{this.view.docView.lastUpdate<Date.now()-75&&this.resizeTimeout<0&&(this.resizeTimeout=setTimeout((()=>{this.resizeTimeout=-1,this.view.requestMeasure()}),50))})),this.resize.observe(t.scrollDOM)),this.start(),this.onScroll=this.onScroll.bind(this),window.addEventListener("scroll",this.onScroll),"function"==typeof IntersectionObserver&&(this.intersection=new IntersectionObserver((t=>{this.parentCheck<0&&(this.parentCheck=setTimeout(this.listenForScroll.bind(this),1e3)),t.length>0&&t[t.length-1].intersectionRatio>0!=this.intersecting&&(this.intersecting=!this.intersecting,this.intersecting!=this.view.inView&&this.onScrollChanged(document.createEvent("Event")))}),{}),this.intersection.observe(this.dom),this.gapIntersection=new IntersectionObserver((t=>{t.length>0&&t[t.length-1].intersectionRatio>0&&this.onScrollChanged(document.createEvent("Event"))}),{})),this.listenForScroll(),this.readSelectionRange(),this.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}onScroll(t){this.intersecting&&this.flush(!1),this.onScrollChanged(t)}updateGaps(t){if(this.gapIntersection&&(t.length!=this.gaps.length||this.gaps.some(((e,i)=>e!=t[i])))){this.gapIntersection.disconnect();for(let e of t)this.gapIntersection.observe(e);this.gaps=t}}onSelectionChange(t){if(!this.readSelectionRange()||this.delayedAndroidKey)return;let{view:e}=this,i=this.selectionRange;if(e.state.facet(dn)?e.root.activeElement!=this.dom:!Ne(e.dom,i))return;let n=i.anchorNode&&e.docView.nearest(i.anchorNode);n&&n.ignoreEvent(t)||((Ti.ie&&Ti.ie_version<=11||Ti.android&&Ti.chrome)&&!e.state.selection.main.empty&&i.focusNode&&Fe(i.focusNode,i.focusOffset,i.anchorNode,i.anchorOffset)?this.flushSoon():this.flush(!1))}readSelectionRange(){let{root:t}=this.view,e=Ge(t),i=Ti.safari&&11==t.nodeType&&function(){let t=document.activeElement;for(;t&&t.shadowRoot;)t=t.shadowRoot.activeElement;return t}()==this.view.contentDOM&&function(t){let e=null;function i(t){t.preventDefault(),t.stopImmediatePropagation(),e=t.getTargetRanges()[0]}if(t.contentDOM.addEventListener("beforeinput",i,!0),document.execCommand("indent"),t.contentDOM.removeEventListener("beforeinput",i,!0),!e)return null;let n=e.startContainer,r=e.startOffset,s=e.endContainer,o=e.endOffset,a=t.docView.domAtPos(t.state.selection.main.anchor);return Fe(a.node,a.offset,s,o)&&([n,r,s,o]=[s,o,n,r]),{anchorNode:n,anchorOffset:r,focusNode:s,focusOffset:o}}(this.view)||e;return!this.selectionRange.eq(i)&&(this.selectionRange.setRange(i),this.selectionChanged=!0)}setSelectionRange(t,e){this.selectionRange.set(t.node,t.offset,e.node,e.offset),this.selectionChanged=!1}listenForScroll(){this.parentCheck=-1;let t=0,e=null;for(let i=this.dom;i;)if(1==i.nodeType)!e&&t<this.scrollTargets.length&&this.scrollTargets[t]==i?t++:e||(e=this.scrollTargets.slice(0,t)),e&&e.push(i),i=i.assignedSlot||i.parentNode;else{if(11!=i.nodeType)break;i=i.host}if(t<this.scrollTargets.length&&!e&&(e=this.scrollTargets.slice(0,t)),e){for(let t of this.scrollTargets)t.removeEventListener("scroll",this.onScroll);for(let t of this.scrollTargets=e)t.addEventListener("scroll",this.onScroll)}}ignore(t){if(!this.active)return t();try{return this.stop(),t()}finally{this.start(),this.clear()}}start(){this.active||(this.observer.observe(this.dom,ls),hs&&this.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.active=!0)}stop(){this.active&&(this.active=!1,this.observer.disconnect(),hs&&this.dom.removeEventListener("DOMCharacterDataModified",this.onCharData))}clear(){this.processRecords(),this.queue.length=0,this.selectionChanged=!1}delayAndroidKey(t,e){this.delayedAndroidKey||requestAnimationFrame((()=>{let t=this.delayedAndroidKey;this.delayedAndroidKey=null;let e=this.view.state;ai(this.view.contentDOM,t.key,t.keyCode)?this.processRecords():this.flush(),this.view.state==e&&this.view.update([])})),this.delayedAndroidKey&&"Enter"!=t||(this.delayedAndroidKey={key:t,keyCode:e})}flushSoon(){this.delayedFlush<0&&(this.delayedFlush=window.setTimeout((()=>{this.delayedFlush=-1,this.flush()}),20))}forceFlush(){this.delayedFlush>=0&&(window.clearTimeout(this.delayedFlush),this.delayedFlush=-1,this.flush())}processRecords(){let t=this.queue;for(let e of this.observer.takeRecords())t.push(e);t.length&&(this.queue=[]);let e=-1,i=-1,n=!1;for(let r of t){let t=this.readMutation(r);t&&(t.typeOver&&(n=!0),-1==e?({from:e,to:i}=t):(e=Math.min(t.from,e),i=Math.max(t.to,i)))}return{from:e,to:i,typeOver:n}}flush(t=!0){if(this.delayedFlush>=0||this.delayedAndroidKey)return;t&&this.readSelectionRange();let{from:e,to:i,typeOver:n}=this.processRecords(),r=this.selectionChanged&&Ne(this.dom,this.selectionRange);if(e<0&&!r)return;this.selectionChanged=!1;let s=this.view.state;this.onChange(e,i,n),this.view.state==s&&this.view.update([])}readMutation(t){let e=this.view.docView.nearest(t.target);if(!e||e.ignoreMutation(t))return null;if(e.markDirty("attributes"==t.type),"attributes"==t.type&&(e.dirty|=4),"childList"==t.type){let i=us(e,t.previousSibling||t.target.previousSibling,-1),n=us(e,t.nextSibling||t.target.nextSibling,1);return{from:i?e.posAfter(i):e.posAtStart,to:n?e.posBefore(n):e.posAtEnd,typeOver:!1}}return"characterData"==t.type?{from:e.posAtStart,to:e.posAtEnd,typeOver:t.target.nodeValue==t.oldValue}:null}destroy(){var t,e,i;this.stop(),null===(t=this.intersection)||void 0===t||t.disconnect(),null===(e=this.gapIntersection)||void 0===e||e.disconnect(),null===(i=this.resize)||void 0===i||i.disconnect();for(let t of this.scrollTargets)t.removeEventListener("scroll",this.onScroll);window.removeEventListener("scroll",this.onScroll),this.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange),clearTimeout(this.parentCheck),clearTimeout(this.resizeTimeout)}}function us(t,e,i){for(;e;){let n=ui.get(e);if(n&&n.parent==t)return n;let r=e.parentNode;e=r!=t.dom?r:i>0?e.nextSibling:e.previousSibling}return null}class Os{constructor(t={}){this.plugins=[],this.pluginMap=new Map,this.editorAttrs={},this.contentAttrs={},this.bidiCache=[],this.destroyed=!1,this.updateState=2,this.measureScheduled=-1,this.measureRequests=[],this.contentDOM=document.createElement("div"),this.scrollDOM=document.createElement("div"),this.scrollDOM.tabIndex=-1,this.scrollDOM.className="cm-scroller",this.scrollDOM.appendChild(this.contentDOM),this.announceDOM=document.createElement("div"),this.announceDOM.style.cssText="position: absolute; top: -10000px",this.announceDOM.setAttribute("aria-live","polite"),this.dom=document.createElement("div"),this.dom.appendChild(this.announceDOM),this.dom.appendChild(this.scrollDOM),this._dispatch=t.dispatch||(t=>this.update([t])),this.dispatch=this.dispatch.bind(this),this.root=t.root||function(t){for(;t;){if(t&&(9==t.nodeType||11==t.nodeType&&t.host))return t;t=t.assignedSlot||t.parentNode}return null}(t.parent)||document,this.viewState=new Vr(t.state||Qe.create()),this.plugins=this.state.facet(gn).map((t=>new yn(t)));for(let t of this.plugins)t.update(this);this.observer=new cs(this,((t,e,i)=>{!function(t,e,i,n){let r,s,o=t.state.selection.main;if(e>-1){let n=t.docView.domBoundsAround(e,i,0);if(!n||t.state.readOnly)return;let{from:a,to:l}=n,h=t.docView.impreciseHead||t.docView.impreciseAnchor?[]:function(t){let e=[];if(t.root.activeElement!=t.contentDOM)return e;let{anchorNode:i,anchorOffset:n,focusNode:r,focusOffset:s}=t.observer.selectionRange;return i&&(e.push(new Vn(i,n)),r==i&&s==n||e.push(new Vn(r,s))),e}(t),c=new qn(h,t.state);c.readRange(n.startDOM,n.endDOM);let u=o.from,O=null;(8===t.inputState.lastKeyCode&&t.inputState.lastKeyTime>Date.now()-100||Ti.android&&c.text.length<l-a)&&(u=o.to,O="end");let d=function(t,e,i,n){let r=Math.min(t.length,e.length),s=0;for(;s<r&&t.charCodeAt(s)==e.charCodeAt(s);)s++;if(s==r&&t.length==e.length)return null;let o=t.length,a=e.length;for(;o>0&&a>0&&t.charCodeAt(o-1)==e.charCodeAt(a-1);)o--,a--;return"end"==n&&(i-=o+Math.max(0,s-Math.min(o,a))-s),o<s&&t.length<e.length?(s-=i<=s&&i>=o?s-i:0,a=s+(a-o),o=s):a<s&&(s-=i<=s&&i>=a?s-i:0,o=s+(o-a),a=s),{from:s,toA:o,toB:a}}(t.state.doc.sliceString(a,l,"￿"),c.text,u-a,O);d&&(Ti.chrome&&13==t.inputState.lastKeyCode&&d.toB==d.from+2&&"￿￿"==c.text.slice(d.from,d.toB)&&d.toB--,r={from:a+d.from,to:a+d.toA,insert:ct.of(c.text.slice(d.from,d.toB).split("￿"))}),s=function(t,e){if(0==t.length)return null;let i=t[0].pos,n=2==t.length?t[1].pos:i;return i>-1&&n>-1?Xt.single(i+e,n+e):null}(h,a)}else if(t.hasFocus||!t.state.facet(dn)){let e=t.observer.selectionRange,{impreciseHead:i,impreciseAnchor:n}=t.docView,r=i&&i.node==e.focusNode&&i.offset==e.focusOffset||!Ie(t.contentDOM,e.focusNode)?t.state.selection.main.head:t.docView.posFromDOM(e.focusNode,e.focusOffset),a=n&&n.node==e.anchorNode&&n.offset==e.anchorOffset||!Ie(t.contentDOM,e.anchorNode)?t.state.selection.main.anchor:t.docView.posFromDOM(e.anchorNode,e.anchorOffset);r==o.head&&a==o.anchor||(s=Xt.single(a,r))}if(r||s)if(!r&&n&&!o.empty&&s&&s.main.empty?r={from:o.from,to:o.to,insert:t.state.doc.slice(o.from,o.to)}:r&&r.from>=o.from&&r.to<=o.to&&(r.from!=o.from||r.to!=o.to)&&o.to-o.from-(r.to-r.from)<=4&&(r={from:o.from,to:o.to,insert:t.state.doc.slice(o.from,r.from).append(r.insert).append(t.state.doc.slice(r.to,o.to))}),r){let e=t.state;if(Ti.ios&&t.inputState.flushIOSKey(t))return;if(Ti.android&&(r.from==o.from&&r.to==o.to&&1==r.insert.length&&2==r.insert.lines&&ai(t.contentDOM,"Enter",13)||r.from==o.from-1&&r.to==o.to&&0==r.insert.length&&ai(t.contentDOM,"Backspace",8)||r.from==o.from&&r.to==o.to+1&&0==r.insert.length&&ai(t.contentDOM,"Delete",46)))return;let i,n=r.insert.toString();if(t.state.facet(an).some((e=>e(t,r.from,r.to,n))))return;if(t.inputState.composing>=0&&t.inputState.composing++,r.from>=o.from&&r.to<=o.to&&r.to-r.from>=(o.to-o.from)/3&&(!s||s.main.empty&&s.main.from==r.from+r.insert.length)&&t.inputState.composing<0){let n=o.from<r.from?e.sliceDoc(o.from,r.from):"",s=o.to>r.to?e.sliceDoc(r.to,o.to):"";i=e.replaceSelection(t.state.toText(n+r.insert.sliceString(0,void 0,t.state.lineBreak)+s))}else{let n=e.changes(r),a=s&&!e.selection.main.eq(s.main)&&s.main.to<=n.newLength?s.main:void 0;if(e.selection.ranges.length>1&&t.inputState.composing>=0&&r.to<=o.to&&r.to>=o.to-10){let s=t.state.sliceDoc(r.from,r.to),l=In(t)||t.state.doc.lineAt(o.head),h=o.to-r.to,c=o.to-o.from;i=e.changeByRange((i=>{if(i.from==o.from&&i.to==o.to)return{changes:n,range:a||i.map(n)};let u=i.to-h,O=u-s.length;if(i.to-i.from!=c||t.state.sliceDoc(O,u)!=s||l&&i.to>=l.from&&i.from<=l.to)return{range:i};let d=e.changes({from:O,to:u,insert:r.insert}),f=i.to-o.to;return{changes:d,range:a?Xt.range(Math.max(0,a.anchor+f),Math.max(0,a.head+f)):i.map(d)}}))}else i={changes:n,selection:a&&e.selection.replaceRange(a)}}let a="input.type";t.composing&&(a+=".compose",t.inputState.compositionFirstChange&&(a+=".start",t.inputState.compositionFirstChange=!1)),t.dispatch(i,{scrollIntoView:!0,userEvent:a})}else if(s&&!s.main.eq(o)){let e=!1,i="select";t.inputState.lastSelectionTime>Date.now()-50&&("select"==t.inputState.lastSelectionOrigin&&(e=!0),i=t.inputState.lastSelectionOrigin),t.dispatch({selection:s,scrollIntoView:e,userEvent:i})}}(this,t,e,i)}),(t=>{this.inputState.runScrollHandlers(this,t),this.observer.intersecting&&this.measure()})),this.inputState=new or(this),this.docView=new Un(this),this.mountStyles(),this.updateAttrs(),this.updateState=0,window.addEventListener("resize",(()=>{-1==ps&&(ps=setTimeout(ms,50))})),this.requestMeasure(),t.parent&&t.parent.appendChild(this.dom)}get state(){return this.viewState.state}get viewport(){return this.viewState.viewport}get visibleRanges(){return this.viewState.visibleRanges}get inView(){return this.viewState.inView}get composing(){return this.inputState.composing>0}dispatch(...t){this._dispatch(1==t.length&&t[0]instanceof ae?t[0]:this.state.update(...t))}update(t){if(0!=this.updateState)throw new Error("Calls to EditorView.update are not allowed while an update is in progress");let e,i=!1,n=this.state;for(let e of t){if(e.startState!=n)throw new RangeError("Trying to update state with a transaction that doesn't start from the previous state.");n=e.state}if(this.destroyed)return void(this.viewState.state=n);if(n.facet(Qe.phrases)!=this.state.facet(Qe.phrases))return this.setState(n);e=new $n(this,n,t);let r=this.viewState.scrollTarget;try{this.updateState=2;for(let e of t){if(r&&(r=r.map(e.changes)),e.scrollIntoView){let{main:t}=e.state.selection;r=new cn(t.empty?t:Xt.cursor(t.head,t.head>t.anchor?-1:1))}for(let t of e.effects)t.is(ln)?r=new cn(t.value):t.is(hn)?r=new cn(t.value,"center"):t.is(un)&&(r=t.value)}this.viewState.update(e,r),this.bidiCache=Qs.update(this.bidiCache,e.changes),e.empty||(this.updatePlugins(e),this.inputState.update(e)),i=this.docView.update(e),this.state.facet(Sn)!=this.styleModules&&this.mountStyles(),this.updateAttrs(),this.showAnnouncements(t),this.docView.updateSelection(i,t.some((t=>t.isUserEvent("select.pointer"))))}finally{this.updateState=0}if(e.startState.facet(ts)!=e.state.facet(ts)&&(this.viewState.mustMeasureContent=!0),(i||r||this.viewState.mustEnforceCursorAssoc||this.viewState.mustMeasureContent)&&this.requestMeasure(),!e.empty)for(let t of this.state.facet(on))t(e)}setState(t){if(0!=this.updateState)throw new Error("Calls to EditorView.setState are not allowed while an update is in progress");if(this.destroyed)return void(this.viewState.state=t);this.updateState=2;let e=this.hasFocus;try{for(let t of this.plugins)t.destroy(this);this.viewState=new Vr(t),this.plugins=t.facet(gn).map((t=>new yn(t))),this.pluginMap.clear();for(let t of this.plugins)t.update(this);this.docView=new Un(this),this.inputState.ensureHandlers(this),this.mountStyles(),this.updateAttrs(),this.bidiCache=[]}finally{this.updateState=0}e&&this.focus(),this.requestMeasure()}updatePlugins(t){let e=t.startState.facet(gn),i=t.state.facet(gn);if(e!=i){let n=[];for(let r of i){let i=e.indexOf(r);if(i<0)n.push(new yn(r));else{let e=this.plugins[i];e.mustUpdate=t,n.push(e)}}for(let e of this.plugins)e.mustUpdate!=t&&e.destroy(this);this.plugins=n,this.pluginMap.clear(),this.inputState.ensureHandlers(this)}else for(let e of this.plugins)e.mustUpdate=t;for(let t=0;t<this.plugins.length;t++)this.plugins[t].update(this)}measure(t=!0){if(this.destroyed)return;this.measureScheduled>-1&&cancelAnimationFrame(this.measureScheduled),this.measureScheduled=0,t&&this.observer.flush();let e=null;try{for(let t=0;;t++){this.updateState=1;let i=this.viewport,n=this.viewState.measure(this);if(!n&&!this.measureRequests.length&&null==this.viewState.scrollTarget)break;if(t>5){console.warn(this.measureRequests.length?"Measure loop restarted more than 5 times":"Viewport failed to stabilize");break}let r=[];4&n||([this.measureRequests,r]=[r,this.measureRequests]);let s=r.map((t=>{try{return t.read(this)}catch(t){return On(this.state,t),gs}})),o=new $n(this,this.state),a=!1,l=!1;o.flags|=n,e?e.flags|=n:e=o,this.updateState=2,o.empty||(this.updatePlugins(o),this.inputState.update(o),this.updateAttrs(),a=this.docView.update(o));for(let t=0;t<r.length;t++)if(s[t]!=gs)try{let e=r[t];e.write&&e.write(s[t],this)}catch(t){On(this.state,t)}if(this.viewState.scrollTarget&&(this.docView.scrollIntoView(this.viewState.scrollTarget),this.viewState.scrollTarget=null,l=!0),a&&this.docView.updateSelection(!0),this.viewport.from==i.from&&this.viewport.to==i.to&&!l&&0==this.measureRequests.length)break}}finally{this.updateState=0,this.measureScheduled=-1}if(e&&!e.empty)for(let t of this.state.facet(on))t(e)}get themeClasses(){return is+" "+(this.state.facet(es)?rs:ns)+" "+this.state.facet(ts)}updateAttrs(){let t=bs(this,vn,{class:"cm-editor"+(this.hasFocus?" cm-focused ":" ")+this.themeClasses}),e={spellcheck:"false",autocorrect:"off",autocapitalize:"off",translate:"no",contenteditable:this.state.facet(dn)?"true":"false",class:"cm-content",style:`${Ti.tabSize}: ${this.state.tabSize}`,role:"textbox","aria-multiline":"true"};this.state.readOnly&&(e["aria-readonly"]="true"),bs(this,xn,e),this.observer.ignore((()=>{Ei(this.contentDOM,this.contentAttrs,e),Ei(this.dom,this.editorAttrs,t)})),this.editorAttrs=t,this.contentAttrs=e}showAnnouncements(t){let e=!0;for(let i of t)for(let t of i.effects)t.is(Os.announce)&&(e&&(this.announceDOM.textContent=""),e=!1,this.announceDOM.appendChild(document.createElement("div")).textContent=t.value)}mountStyles(){this.styleModules=this.state.facet(Sn),B.mount(this.root,this.styleModules.concat(as).reverse())}readMeasured(){if(2==this.updateState)throw new Error("Reading the editor layout isn't allowed during an update");0==this.updateState&&this.measureScheduled>-1&&this.measure(!1)}requestMeasure(t){if(this.measureScheduled<0&&(this.measureScheduled=requestAnimationFrame((()=>this.measure()))),t){if(null!=t.key)for(let e=0;e<this.measureRequests.length;e++)if(this.measureRequests[e].key===t.key)return void(this.measureRequests[e]=t);this.measureRequests.push(t)}}pluginField(t){let e=[];for(let i of this.plugins)i.update(this).takeField(t,e);return e}plugin(t){let e=this.pluginMap.get(t);return(void 0===e||e&&e.spec!=t)&&this.pluginMap.set(t,e=this.plugins.find((e=>e.spec==t))||null),e&&e.update(this).value}get documentTop(){return this.contentDOM.getBoundingClientRect().top+this.viewState.paddingTop}get documentPadding(){return{top:this.viewState.paddingTop,bottom:this.viewState.paddingBottom}}blockAtHeight(t,e){let i=fs(e,this);return this.elementAtHeight(t-i).moveY(i)}elementAtHeight(t){return this.readMeasured(),this.viewState.elementAtHeight(t)}visualLineAtHeight(t,e){let i=fs(e,this);return this.lineBlockAtHeight(t-i).moveY(i)}lineBlockAtHeight(t){return this.readMeasured(),this.viewState.lineBlockAtHeight(t)}viewportLines(t,e){let i=fs(e,this);for(let e of this.viewportLineBlocks)t(e.moveY(i))}get viewportLineBlocks(){return this.viewState.viewportLines}visualLineAt(t,e=0){return this.lineBlockAt(t).moveY(e+this.viewState.paddingTop)}lineBlockAt(t){return this.viewState.lineBlockAt(t)}get contentHeight(){return this.viewState.contentHeight}moveByChar(t,e,i){return sr(this,t,rr(this,t,e,i))}moveByGroup(t,e){return sr(this,t,rr(this,t,e,(e=>function(t,e,i){let n=t.state.charCategorizer(e),r=n(i);return t=>{let e=n(t);return r==pe.Space&&(r=e),r==e}}(this,t.head,e))))}moveToLineBoundary(t,e,i=!0){return function(t,e,i,n){let r=t.state.doc.lineAt(e.head),s=n&&t.lineWrapping?t.coordsAtPos(e.assoc<0&&e.head>r.from?e.head-1:e.head):null;if(s){let e=t.dom.getBoundingClientRect(),n=t.posAtCoords({x:i==(t.textDirection==Pn.LTR)?e.right-1:e.left+1,y:(s.top+s.bottom)/2});if(null!=n)return Xt.cursor(n,i?-1:1)}let o=Bi.find(t.docView,e.head),a=o?i?o.posAtEnd:o.posAtStart:i?r.to:r.from;return Xt.cursor(a,i?-1:1)}(this,t,e,i)}moveVertically(t,e,i){return sr(this,t,function(t,e,i,n){let r=e.head,s=i?1:-1;if(r==(i?t.state.doc.length:0))return Xt.cursor(r,e.assoc);let o,a=e.goalColumn,l=t.contentDOM.getBoundingClientRect(),h=t.coordsAtPos(r),c=t.documentTop;if(h)null==a&&(a=h.left-l.left),o=s<0?h.top:h.bottom;else{let e=t.viewState.lineBlockAt(r-c);null==a&&(a=Math.min(l.right-l.left,t.defaultCharacterWidth*(r-e.from))),o=(s<0?e.top:e.bottom)+c}let u=l.left+a,O=null!=n?n:t.defaultLineHeight>>1;for(let i=0;;i+=10){let n=o+(O+i)*s,h=ir(t,{x:u,y:n},!1,s);if(n<l.top||n>l.bottom||(s<0?h<r:h>r))return Xt.cursor(h,e.assoc,void 0,a)}}(this,t,e,i))}scrollPosIntoView(t){this.dispatch({effects:ln.of(Xt.cursor(t))})}domAtPos(t){return this.docView.domAtPos(t)}posAtDOM(t,e=0){return this.docView.posFromDOM(t,e)}posAtCoords(t,e=!0){return this.readMeasured(),ir(this,t,e)}coordsAtPos(t,e=1){this.readMeasured();let i=this.docView.coordsAt(t,e);if(!i||i.left==i.right)return i;let n=this.state.doc.lineAt(t),r=this.bidiSpans(n);return ei(i,r[Dn.find(r,t-n.from,-1,e)].dir==Pn.LTR==e>0)}get defaultCharacterWidth(){return this.viewState.heightOracle.charWidth}get defaultLineHeight(){return this.viewState.heightOracle.lineHeight}get textDirection(){return this.viewState.heightOracle.direction}get lineWrapping(){return this.viewState.heightOracle.lineWrapping}bidiSpans(t){if(t.length>ds)return En(t.length);let e=this.textDirection;for(let i of this.bidiCache)if(i.from==t.from&&i.dir==e)return i.order;let i=function(t,e){let i=t.length,n=e==Rn?1:2,r=e==Rn?2:1;if(!t||1==n&&!Ln.test(t))return En(i);for(let e=0,r=n,o=n;e<i;e++){let i=(s=t.charCodeAt(e))<=247?Cn[s]:1424<=s&&s<=1524?2:1536<=s&&s<=1785?An[s-1536]:1774<=s&&s<=2220?4:8192<=s&&s<=8203||8204==s?256:1;512==i?i=r:8==i&&4==o&&(i=16),zn[e]=4==i?2:i,7&i&&(o=i),r=i}var s;for(let t=0,e=n,r=n;t<i;t++){let n=zn[t];if(128==n)t<i-1&&e==zn[t+1]&&24&e?n=zn[t]=e:zn[t]=256;else if(64==n){let n=t+1;for(;n<i&&64==zn[n];)n++;let s=t&&8==e||n<i&&8==zn[n]?1==r?1:8:256;for(let e=t;e<n;e++)zn[e]=s;t=n-1}else 8==n&&1==r&&(zn[t]=1);e=n,7&n&&(r=n)}for(let e,s,o,a=0,l=0,h=0;a<i;a++)if(s=Zn[e=t.charCodeAt(a)])if(s<0){for(let t=l-3;t>=0;t-=3)if(_n[t+1]==-s){let e=_n[t+2],i=2&e?n:4&e?1&e?r:n:0;i&&(zn[a]=zn[_n[t]]=i),l=t;break}}else{if(189==_n.length)break;_n[l++]=a,_n[l++]=e,_n[l++]=h}else if(2==(o=zn[a])||1==o){let t=o==n;h=t?0:1;for(let e=l-3;e>=0;e-=3){let i=_n[e+2];if(2&i)break;if(t)_n[e+2]|=2;else{if(4&i)break;_n[e+2]|=4}}}for(let t=0;t<i;t++)if(256==zn[t]){let e=t+1;for(;e<i&&256==zn[e];)e++;let r=1==(t?zn[t-1]:n),s=r==(1==(e<i?zn[e]:n))?r?1:2:n;for(let i=t;i<e;i++)zn[i]=s;t=e-1}let o=[];if(1==n)for(let t=0;t<i;){let e=t,n=1!=zn[t++];for(;t<i&&n==(1!=zn[t]);)t++;if(n)for(let i=t;i>e;){let t=i,n=2!=zn[--i];for(;i>e&&n==(2!=zn[i-1]);)i--;o.push(new Dn(i,t,n?2:1))}else o.push(new Dn(e,t,0))}else for(let t=0;t<i;){let e=t,n=2==zn[t++];for(;t<i&&n==(2==zn[t]);)t++;o.push(new Dn(e,t,n?1:2))}return o}(t.text,this.textDirection);return this.bidiCache.push(new Qs(t.from,t.to,e,i)),i}get hasFocus(){var t;return(document.hasFocus()||Ti.safari&&(null===(t=this.inputState)||void 0===t?void 0:t.lastContextMenu)>Date.now()-3e4)&&this.root.activeElement==this.contentDOM}focus(){this.observer.ignore((()=>{si(this.contentDOM),this.docView.updateSelection()}))}destroy(){for(let t of this.plugins)t.destroy(this);this.plugins=[],this.inputState.destroy(),this.dom.remove(),this.observer.destroy(),this.measureScheduled>-1&&cancelAnimationFrame(this.measureScheduled),this.destroyed=!0}static scrollIntoView(t,e={}){return un.of(new cn("number"==typeof t?Xt.cursor(t):t,e.y,e.x,e.yMargin,e.xMargin))}static domEventHandlers(t){return Qn.define((()=>({})),{eventHandlers:t})}static theme(t,e){let i=B.newName(),n=[ts.of(i),Sn.of(os(`.${i}`,t))];return e&&e.dark&&n.push(es.of(!0)),n}static baseTheme(t){return Yt.lowest(Sn.of(os("."+is,t,ss)))}}Os.scrollTo=ln,Os.centerOn=hn,Os.styleModule=Sn,Os.inputHandler=an,Os.exceptionSink=sn,Os.updateListener=on,Os.editable=dn,Os.mouseSelectionStyle=rn,Os.dragMovesSelection=nn,Os.clickAddsSelectionRange=en,Os.decorations=wn,Os.darkTheme=es,Os.contentAttributes=xn,Os.editorAttributes=vn,Os.lineWrapping=Os.contentAttributes.of({class:"cm-lineWrapping"}),Os.announce=oe.define();const ds=4096;function fs(t,e){return(null==t?e.contentDOM.getBoundingClientRect().top:t)+e.viewState.paddingTop}let ps=-1;function ms(){ps=-1;let t=document.querySelectorAll(".cm-content");for(let e=0;e<t.length;e++){let i=ui.get(t[e]);i&&i.editorView.requestMeasure()}}const gs={};class Qs{constructor(t,e,i,n){this.from=t,this.to=e,this.dir=i,this.order=n}static update(t,e){if(e.empty)return t;let i=[],n=t.length?t[t.length-1].dir:Pn.LTR;for(let r=Math.max(0,t.length-10);r<t.length;r++){let s=t[r];s.dir!=n||e.touchesRange(s.from,s.to)||i.push(new Qs(e.mapPos(s.from,1),e.mapPos(s.to,-1),s.dir,s.order))}return i}}function bs(t,e,i){for(let n=t.state.facet(e),r=n.length-1;r>=0;r--){let e=n[r],s="function"==typeof e?e(t):e;s&&Di(s,i)}return i}const ys=Ti.mac?"mac":Ti.windows?"win":Ti.linux?"linux":"key";function vs(t,e,i){return e.altKey&&(t="Alt-"+t),e.ctrlKey&&(t="Ctrl-"+t),e.metaKey&&(t="Meta-"+t),!1!==i&&e.shiftKey&&(t="Shift-"+t),t}const xs=Os.domEventHandlers({keydown:(t,e)=>function(t,e,i,n){let r=(l=e,"Esc"==(h=!(Ye&&(l.ctrlKey||l.altKey||l.metaKey)||(Ee||qe)&&l.shiftKey&&l.key&&1==l.key.length)&&l.key||(l.shiftKey?De:Le)[l.keyCode]||l.key||"Unidentified")&&(h="Escape"),"Del"==h&&(h="Delete"),"Left"==h&&(h="ArrowLeft"),"Up"==h&&(h="ArrowUp"),"Right"==h&&(h="ArrowRight"),"Down"==h&&(h="ArrowDown"),h),s=1==r.length&&" "!=r,o="",a=!1;var l,h;ks&&ks.view==i&&ks.scope==n&&(o=ks.prefix+" ",(a=lr.indexOf(e.keyCode)<0)&&(ks=null));let c,u=t=>{if(t){for(let e of t.commands)if(e(i))return!0;t.preventDefault&&(a=!0)}return!1},O=t[n];if(O){if(u(O[o+vs(r,e,!s)]))return!0;if(s&&(e.shiftKey||e.altKey||e.metaKey)&&(c=Le[e.keyCode])&&c!=r){if(u(O[o+vs(c,e,!0)]))return!0}else if(s&&e.shiftKey&&u(O[o+vs(r,e,!0)]))return!0}return a}(function(t){let e=t.facet(ws),i=Ss.get(e);return i||Ss.set(e,i=function(t,e=ys){let i=Object.create(null),n=Object.create(null),r=(t,e)=>{let i=n[t];if(null==i)n[t]=e;else if(i!=e)throw new Error("Key binding "+t+" is used both as a regular binding and as a multi-stroke prefix")},s=(t,n,s,o)=>{let a=i[t]||(i[t]=Object.create(null)),l=n.split(/ (?!$)/).map((t=>function(t,e){const i=t.split(/-(?!$)/);let n,r,s,o,a=i[i.length-1];"Space"==a&&(a=" ");for(let t=0;t<i.length-1;++t){const a=i[t];if(/^(cmd|meta|m)$/i.test(a))o=!0;else if(/^a(lt)?$/i.test(a))n=!0;else if(/^(c|ctrl|control)$/i.test(a))r=!0;else if(/^s(hift)?$/i.test(a))s=!0;else{if(!/^mod$/i.test(a))throw new Error("Unrecognized modifier name: "+a);"mac"==e?o=!0:r=!0}}return n&&(a="Alt-"+a),r&&(a="Ctrl-"+a),o&&(a="Meta-"+a),s&&(a="Shift-"+a),a}(t,e)));for(let e=1;e<l.length;e++){let i=l.slice(0,e).join(" ");r(i,!0),a[i]||(a[i]={preventDefault:!0,commands:[e=>{let n=ks={view:e,prefix:i,scope:t};return setTimeout((()=>{ks==n&&(ks=null)}),4e3),!0}]})}let h=l.join(" ");r(h,!1);let c=a[h]||(a[h]={preventDefault:!1,commands:[]});c.commands.push(s),o&&(c.preventDefault=!0)};for(let i of t){let t=i[e]||i.key;if(t)for(let e of i.scope?i.scope.split(" "):["editor"])s(e,t,i.run,i.preventDefault),i.shift&&s(e,"Shift-"+t,i.shift,i.preventDefault)}return i}(e.reduce(((t,e)=>t.concat(e)),[]))),i}(e.state),t,e,"editor")}),ws=_t.define({enables:xs}),Ss=new WeakMap;let ks=null;const $s=!Ti.ios,Ts=_t.define({combine:t=>be(t,{cursorBlinkRate:1200,drawRangeCursor:!0},{cursorBlinkRate:(t,e)=>Math.min(t,e),drawRangeCursor:(t,e)=>t||e})});function Ps(t={}){return[Ts.of(t),Ws,Cs]}class Rs{constructor(t,e,i,n,r){this.left=t,this.top=e,this.width=i,this.height=n,this.className=r}draw(){let t=document.createElement("div");return t.className=this.className,this.adjust(t),t}adjust(t){t.style.left=this.left+"px",t.style.top=this.top+"px",this.width>=0&&(t.style.width=this.width+"px"),t.style.height=this.height+"px"}eq(t){return this.left==t.left&&this.top==t.top&&this.width==t.width&&this.height==t.height&&this.className==t.className}}const Ws=Qn.fromClass(class{constructor(t){this.view=t,this.rangePieces=[],this.cursors=[],this.measureReq={read:this.readPos.bind(this),write:this.drawSel.bind(this)},this.selectionLayer=t.scrollDOM.appendChild(document.createElement("div")),this.selectionLayer.className="cm-selectionLayer",this.selectionLayer.setAttribute("aria-hidden","true"),this.cursorLayer=t.scrollDOM.appendChild(document.createElement("div")),this.cursorLayer.className="cm-cursorLayer",this.cursorLayer.setAttribute("aria-hidden","true"),t.requestMeasure(this.measureReq),this.setBlinkRate()}setBlinkRate(){this.cursorLayer.style.animationDuration=this.view.state.facet(Ts).cursorBlinkRate+"ms"}update(t){let e=t.startState.facet(Ts)!=t.state.facet(Ts);(e||t.selectionSet||t.geometryChanged||t.viewportChanged)&&this.view.requestMeasure(this.measureReq),t.transactions.some((t=>t.scrollIntoView))&&(this.cursorLayer.style.animationName="cm-blink"==this.cursorLayer.style.animationName?"cm-blink2":"cm-blink"),e&&this.setBlinkRate()}readPos(){let{state:t}=this.view,e=t.facet(Ts),i=t.selection.ranges.map((t=>t.empty?[]:function(t,e){if(e.to<=t.viewport.from||e.from>=t.viewport.to)return[];let i=Math.max(e.from,t.viewport.from),n=Math.min(e.to,t.viewport.to),r=t.textDirection==Pn.LTR,s=t.contentDOM,o=s.getBoundingClientRect(),a=As(t),l=window.getComputedStyle(s.firstChild),h=o.left+parseInt(l.paddingLeft)+Math.min(0,parseInt(l.textIndent)),c=o.right-parseInt(l.paddingRight),u=_s(t,i),O=_s(t,n),d=u.type==qi.Text?u:null,f=O.type==qi.Text?O:null;if(t.lineWrapping&&(d&&(d=Zs(t,i,d)),f&&(f=Zs(t,n,f))),d&&f&&d.from==f.from)return m(g(e.from,e.to,d));{let i=d?g(e.from,null,d):Q(u,!1),n=f?g(null,e.to,f):Q(O,!0),r=[];return(d||u).to<(f||O).from-1?r.push(p(h,i.bottom,c,n.top)):i.bottom<n.top&&t.elementAtHeight((i.bottom+n.top)/2).type==qi.Text&&(i.bottom=n.top=(i.bottom+n.top)/2),m(i).concat(r).concat(m(n))}function p(t,e,i,n){return new Rs(t-a.left,e-a.top-.01,i-t,n-e+.01,"cm-selectionBackground")}function m({top:t,bottom:e,horizontal:i}){let n=[];for(let r=0;r<i.length;r+=2)n.push(p(i[r],t,i[r+1],e));return n}function g(e,i,n){let s=1e9,o=-1e9,a=[];function l(e,i,l,u,O){let d=t.coordsAtPos(e,e==n.to?-2:2),f=t.coordsAtPos(l,l==n.from?2:-2);s=Math.min(d.top,f.top,s),o=Math.max(d.bottom,f.bottom,o),O==Pn.LTR?a.push(r&&i?h:d.left,r&&u?c:f.right):a.push(!r&&u?h:f.left,!r&&i?c:d.right)}let u=null!=e?e:n.from,O=null!=i?i:n.to;for(let n of t.visibleRanges)if(n.to>u&&n.from<O)for(let r=Math.max(n.from,u),s=Math.min(n.to,O);;){let n=t.state.doc.lineAt(r);for(let o of t.bidiSpans(n)){let t=o.from+n.from,a=o.to+n.from;if(t>=s)break;a>r&&l(Math.max(t,r),null==e&&t<=u,Math.min(a,s),null==i&&a>=O,o.dir)}if(r=n.to+1,r>=s)break}return 0==a.length&&l(u,null==e,O,null==i,t.textDirection),{top:s,bottom:o,horizontal:a}}function Q(t,e){let i=o.top+(e?t.top:t.bottom);return{top:i,bottom:i,horizontal:[]}}}(this.view,t))).reduce(((t,e)=>t.concat(e))),n=[];for(let i of t.selection.ranges){let r=i==t.selection.main;if(i.empty?!r||$s:e.drawRangeCursor){let t=Ls(this.view,i,r);t&&n.push(t)}}return{rangePieces:i,cursors:n}}drawSel({rangePieces:t,cursors:e}){if(t.length!=this.rangePieces.length||t.some(((t,e)=>!t.eq(this.rangePieces[e])))){this.selectionLayer.textContent="";for(let e of t)this.selectionLayer.appendChild(e.draw());this.rangePieces=t}if(e.length!=this.cursors.length||e.some(((t,e)=>!t.eq(this.cursors[e])))){let t=this.cursorLayer.children;if(t.length!==e.length){this.cursorLayer.textContent="";for(const t of e)this.cursorLayer.appendChild(t.draw())}else e.forEach(((e,i)=>e.adjust(t[i])));this.cursors=e}}destroy(){this.selectionLayer.remove(),this.cursorLayer.remove()}}),Xs={".cm-line":{"& ::selection":{backgroundColor:"transparent !important"},"&::selection":{backgroundColor:"transparent !important"}}};$s&&(Xs[".cm-line"].caretColor="transparent !important");const Cs=Yt.highest(Os.theme(Xs));function As(t){let e=t.scrollDOM.getBoundingClientRect();return{left:(t.textDirection==Pn.LTR?e.left:e.right-t.scrollDOM.clientWidth)-t.scrollDOM.scrollLeft,top:e.top-t.scrollDOM.scrollTop}}function Zs(t,e,i){let n=Xt.cursor(e);return{from:Math.max(i.from,t.moveToLineBoundary(n,!1,!0).from),to:Math.min(i.to,t.moveToLineBoundary(n,!0,!0).from),type:qi.Text}}function _s(t,e){let i=t.lineBlockAt(e);if(Array.isArray(i.type))for(let t of i.type)if(t.to>e||t.to==e&&(t.to==i.to||t.type==qi.Text))return t;return i}function Ls(t,e,i){let n=t.coordsAtPos(e.head,e.assoc||1);if(!n)return null;let r=As(t);return new Rs(n.left-r.left,n.top-r.top,-1,n.bottom-n.top,i?"cm-cursor cm-cursor-primary":"cm-cursor cm-cursor-secondary")}function Ds(t,e,i,n,r){e.lastIndex=0;for(let s,o=t.iterRange(i,n),a=i;!o.next().done;a+=o.value.length)if(!o.lineBreak)for(;s=e.exec(o.value);)r(a+s.index,a+s.index+s[0].length,s)}class zs{constructor(t){let{regexp:e,decoration:i,boundary:n,maxLength:r=1e3}=t;if(!e.global)throw new RangeError("The regular expression given to MatchDecorator should have its 'g' flag set");this.regexp=e,this.getDeco="function"==typeof i?i:()=>i,this.boundary=n,this.maxLength=r}createDeco(t){let e=new ke;for(let{from:i,to:n}of function(t,e){let i=t.visibleRanges;if(1==i.length&&i[0].from==t.viewport.from&&i[0].to==t.viewport.to)return i;let n=[];for(let{from:r,to:s}of i)r=Math.max(t.state.doc.lineAt(r).from,r-e),s=Math.min(t.state.doc.lineAt(s).to,s+e),n.length&&n[n.length-1].to>=r?n[n.length-1].to=s:n.push({from:r,to:s});return n}(t,this.maxLength))Ds(t.state.doc,this.regexp,i,n,((i,n,r)=>e.add(i,n,this.getDeco(r,t,i))));return e.finish()}updateDeco(t,e){let i=1e9,n=-1;return t.docChanged&&t.changes.iterChanges(((e,r,s,o)=>{o>t.view.viewport.from&&s<t.view.viewport.to&&(i=Math.min(s,i),n=Math.max(o,n))})),t.viewportChanged||n-i>1e3?this.createDeco(t.view):n>-1?this.updateRange(t.view,e.map(t.changes),i,n):e}updateRange(t,e,i,n){for(let r of t.visibleRanges){let s=Math.max(r.from,i),o=Math.min(r.to,n);if(o>s){let i=t.state.doc.lineAt(s),n=i.to<o?t.state.doc.lineAt(o):i,a=Math.max(r.from,i.from),l=Math.min(r.to,n.to);if(this.boundary){for(;s>i.from;s--)if(this.boundary.test(i.text[s-1-i.from])){a=s;break}for(;o<n.to;o++)if(this.boundary.test(n.text[o-n.from])){l=o;break}}let h,c=[];if(i==n)for(this.regexp.lastIndex=a-i.from;(h=this.regexp.exec(i.text))&&h.index<l-i.from;){let e=h.index+i.from;c.push(this.getDeco(h,t,e).range(e,e+h[0].length))}else Ds(t.state.doc,this.regexp,a,l,((e,i,n)=>c.push(this.getDeco(n,t,e).range(e,i))));e=e.update({filterFrom:a,filterTo:l,filter:(t,e)=>t<a||e>l,add:c})}}return e}}const Es=null!=/x/.unicode?"gu":"g",Ms=new RegExp("[\0-\b\n--­؜​‎‏\u2028\u2029‭‮\ufeff￹-￼]",Es),js={0:"null",7:"bell",8:"backspace",10:"newline",11:"vertical tab",13:"carriage return",27:"escape",8203:"zero width space",8204:"zero width non-joiner",8205:"zero width joiner",8206:"left-to-right mark",8207:"right-to-left mark",8232:"line separator",8237:"left-to-right override",8238:"right-to-left override",8233:"paragraph separator",65279:"zero width no-break space",65532:"object replacement"};let qs=null;const Ys=_t.define({combine(t){let e=be(t,{render:null,specialChars:Ms,addSpecialChars:null});return(e.replaceTabs=!function(){var t;if(null==qs&&"undefined"!=typeof document&&document.body){let e=document.body.style;qs=null!=(null!==(t=e.tabSize)&&void 0!==t?t:e.MozTabSize)}return qs||!1}())&&(e.specialChars=new RegExp("\t|"+e.specialChars.source,Es)),e.addSpecialChars&&(e.specialChars=new RegExp(e.specialChars.source+"|"+e.addSpecialChars.source,Es)),e}});function Vs(t={}){return[Ys.of(t),Us||(Us=Qn.fromClass(class{constructor(t){this.view=t,this.decorations=Yi.none,this.decorationCache=Object.create(null),this.decorator=this.makeDecorator(t.state.facet(Ys)),this.decorations=this.decorator.createDeco(t)}makeDecorator(t){return new zs({regexp:t.specialChars,decoration:(e,i,n)=>{let{doc:r}=i.state,s=ot(e[0],0);if(9==s){let t=r.lineAt(n),e=i.state.tabSize,s=ht(t.text,e,n-t.from);return Yi.replace({widget:new Is((e-s%e)*this.view.defaultCharacterWidth)})}return this.decorationCache[s]||(this.decorationCache[s]=Yi.replace({widget:new Gs(t,s)}))},boundary:t.replaceTabs?void 0:/[^]/})}update(t){let e=t.state.facet(Ys);t.startState.facet(Ys)!=e?(this.decorator=this.makeDecorator(e),this.decorations=this.decorator.createDeco(t.view)):this.decorations=this.decorator.updateDeco(t,this.decorations)}},{decorations:t=>t.decorations}))]}let Us=null;class Gs extends Mi{constructor(t,e){super(),this.options=t,this.code=e}eq(t){return t.code==this.code}toDOM(t){let e=(r=this.code)>=32?"•":10==r?"␤":String.fromCharCode(9216+r),i=t.state.phrase("Control character")+" "+(js[this.code]||"0x"+this.code.toString(16)),n=this.options.render&&this.options.render(this.code,i,e);var r;if(n)return n;let s=document.createElement("span");return s.textContent=e,s.title=i,s.setAttribute("aria-label",i),s.className="cm-specialChar",s}ignoreEvent(){return!1}}class Is extends Mi{constructor(t){super(),this.width=t}eq(t){return t.width==this.width}toDOM(){let t=document.createElement("span");return t.textContent="\t",t.className="cm-tab",t.style.width=this.width+"px",t}ignoreEvent(){return!1}}const Ns=Qn.fromClass(class{constructor(){this.height=1e3,this.attrs={style:"padding-bottom: 1000px"}}update(t){let e=t.view.viewState.editorHeight-t.view.defaultLineHeight;e!=this.height&&(this.height=e,this.attrs={style:`padding-bottom: ${e}px`})}});function Bs(){return[Ns,xn.of((t=>{var e;return(null===(e=t.plugin(Ns))||void 0===e?void 0:e.attrs)||null}))]}class Fs extends Mi{constructor(t){super(),this.content=t}toDOM(){let t=document.createElement("span");return t.className="cm-placeholder",t.style.pointerEvents="none",t.appendChild("string"==typeof this.content?document.createTextNode(this.content):this.content),"string"==typeof this.content?t.setAttribute("aria-label","placeholder "+this.content):t.setAttribute("aria-hidden","true"),t}ignoreEvent(){return!1}}function Hs(t){return Qn.fromClass(class{constructor(e){this.view=e,this.placeholder=Yi.set([Yi.widget({widget:new Fs(t),side:1}).range(0)])}get decorations(){return this.view.state.doc.length?Yi.none:this.placeholder}},{decorations:t=>t.decorations})}const Js=new l;function Ks(t){return _t.define({combine:t?e=>e.concat(t):void 0})}class to{constructor(t,e,i,n=[]){this.data=t,this.topNode=i,Qe.prototype.hasOwnProperty("tree")||Object.defineProperty(Qe.prototype,"tree",{get(){return no(this)}}),this.parser=e,this.extension=[Oo.of(this),Qe.languageData.of(((t,e,i)=>t.facet(eo(t,e,i))))].concat(n)}isActiveAt(t,e,i=-1){return eo(t,e,i)==this.data}findRegions(t){let e=t.facet(Oo);if((null==e?void 0:e.data)==this.data)return[{from:0,to:t.doc.length}];if(!e||!e.allowsNesting)return[];let i=[],n=(t,e)=>{if(t.prop(Js)==this.data)return void i.push({from:e,to:e+t.length});let r=t.prop(l.mounted);if(r){if(r.tree.prop(Js)==this.data){if(r.overlay)for(let t of r.overlay)i.push({from:t.from+e,to:t.to+e});else i.push({from:e,to:e+t.length});return}if(r.overlay){let t=i.length;if(n(r.tree,r.overlay[0].from+e),i.length>t)return}}for(let i=0;i<t.children.length;i++){let r=t.children[i];r instanceof p&&n(r,t.positions[i]+e)}};return n(no(t),0),i}get allowsNesting(){return!0}}function eo(t,e,i){let n=t.facet(Oo);if(!n)return null;let r=n.data;if(n.allowsNesting)for(let n=no(t).topNode;n;n=n.enter(e,i,!0,!1))r=n.type.prop(Js)||r;return r}to.setState=oe.define();class io extends to{constructor(t,e){super(t,e,e.topNode),this.parser=e}static define(t){let e=Ks(t.languageData);return new io(e,t.parser.configure({props:[Js.add((t=>t.isTop?e:void 0))]}))}configure(t){return new io(this.data,this.parser.configure(t))}get allowsNesting(){return this.parser.wrappers.length>0}}function no(t){let e=t.field(to.state,!1);return e?e.tree:p.empty}function ro(t,e,i=50){var n;let r=null===(n=t.field(to.state,!1))||void 0===n?void 0:n.context;return r&&(r.isDone(e)||r.work(i,e))?r.tree:null}class so{constructor(t,e=t.length){this.doc=t,this.length=e,this.cursorPos=0,this.string="",this.cursor=t.iter()}syncTo(t){return this.string=this.cursor.next(t-this.cursorPos).value,this.cursorPos=t+this.string.length,this.cursorPos-this.string.length}chunk(t){return this.syncTo(t),this.string}get lineChunks(){return!0}read(t,e){let i=this.cursorPos-this.string.length;return t<i||e>=this.cursorPos?this.doc.sliceString(t,e):this.string.slice(t-i,e-i)}}let oo=null;class ao{constructor(t,e,i=[],n,r,s,o,a){this.parser=t,this.state=e,this.fragments=i,this.tree=n,this.treeLen=r,this.viewport=s,this.skipped=o,this.scheduleOn=a,this.parse=null,this.tempSkipped=[]}startParse(){return this.parser.startParse(new so(this.state.doc),this.fragments)}work(t,e){return null!=e&&e>=this.state.doc.length&&(e=void 0),this.tree!=p.empty&&this.isDone(null!=e?e:this.state.doc.length)?(this.takeTree(),!0):this.withContext((()=>{var i;let n=Date.now()+t;for(this.parse||(this.parse=this.startParse()),null!=e&&(null==this.parse.stoppedAt||this.parse.stoppedAt>e)&&e<this.state.doc.length&&this.parse.stopAt(e);;){let t=this.parse.advance();if(t){if(this.fragments=this.withoutTempSkipped(W.addTree(t,this.fragments,null!=this.parse.stoppedAt)),this.treeLen=null!==(i=this.parse.stoppedAt)&&void 0!==i?i:this.state.doc.length,this.tree=t,this.parse=null,!(this.treeLen<(null!=e?e:this.state.doc.length)))return!0;this.parse=this.startParse()}if(Date.now()>n)return!1}}))}takeTree(){let t,e;this.parse&&(t=this.parse.parsedPos)>=this.treeLen&&((null==this.parse.stoppedAt||this.parse.stoppedAt>t)&&this.parse.stopAt(t),this.withContext((()=>{for(;!(e=this.parse.advance()););})),this.treeLen=t,this.tree=e,this.fragments=this.withoutTempSkipped(W.addTree(this.tree,this.fragments,!0)),this.parse=null)}withContext(t){let e=oo;oo=this;try{return t()}finally{oo=e}}withoutTempSkipped(t){for(let e;e=this.tempSkipped.pop();)t=lo(t,e.from,e.to);return t}changes(t,e){let{fragments:i,tree:n,treeLen:r,viewport:s,skipped:o}=this;if(this.takeTree(),!t.empty){let e=[];if(t.iterChangedRanges(((t,i,n,r)=>e.push({fromA:t,toA:i,fromB:n,toB:r}))),i=W.applyChanges(i,e),n=p.empty,r=0,s={from:t.mapPos(s.from,-1),to:t.mapPos(s.to,1)},this.skipped.length){o=[];for(let e of this.skipped){let i=t.mapPos(e.from,1),n=t.mapPos(e.to,-1);i<n&&o.push({from:i,to:n})}}}return new ao(this.parser,e,i,n,r,s,o,this.scheduleOn)}updateViewport(t){if(this.viewport.from==t.from&&this.viewport.to==t.to)return!1;this.viewport=t;let e=this.skipped.length;for(let e=0;e<this.skipped.length;e++){let{from:i,to:n}=this.skipped[e];i<t.to&&n>t.from&&(this.fragments=lo(this.fragments,i,n),this.skipped.splice(e--,1))}return!(this.skipped.length>=e||(this.reset(),0))}reset(){this.parse&&(this.takeTree(),this.parse=null)}skipUntilInView(t,e){this.skipped.push({from:t,to:e})}static getSkippingParser(t){return new class extends X{createParse(e,i,n){let r=n[0].from,s=n[n.length-1].to;return{parsedPos:r,advance(){let e=oo;if(e){for(let t of n)e.tempSkipped.push(t);t&&(e.scheduleOn=e.scheduleOn?Promise.all([e.scheduleOn,t]):t)}return this.parsedPos=s,new p(u.none,[],[],s-r)},stoppedAt:null,stopAt(){}}}}}isDone(t){t=Math.min(t,this.state.doc.length);let e=this.fragments;return this.treeLen>=t&&e.length&&0==e[0].from&&e[0].to>=t}static get(){return oo}}function lo(t,e,i){return W.applyChanges(t,[{fromA:e,toA:i,fromB:e,toB:i}])}class ho{constructor(t){this.context=t,this.tree=t.tree}apply(t){if(!t.docChanged)return this;let e=this.context.changes(t.changes,t.state),i=this.context.treeLen==t.startState.doc.length?void 0:Math.max(t.changes.mapPos(this.context.treeLen),e.viewport.to);return e.work(20,i)||e.takeTree(),new ho(e)}static init(t){let e=Math.min(3e3,t.doc.length),i=new ao(t.facet(Oo).parser,t,[],p.empty,0,{from:0,to:e},[],null);return i.work(20,e)||i.takeTree(),new ho(i)}}to.state=jt.define({create:ho.init,update(t,e){for(let t of e.effects)if(t.is(to.setState))return t.value;return e.startState.facet(Oo)!=e.state.facet(Oo)?ho.init(e.state):t.apply(e)}});let co=t=>{let e=setTimeout((()=>t()),500);return()=>clearTimeout(e)};"undefined"!=typeof requestIdleCallback&&(co=t=>{let e=-1,i=setTimeout((()=>{e=requestIdleCallback(t,{timeout:400})}),100);return()=>e<0?clearTimeout(i):cancelIdleCallback(e)});const uo=Qn.fromClass(class{constructor(t){this.view=t,this.working=null,this.workScheduled=0,this.chunkEnd=-1,this.chunkBudget=-1,this.work=this.work.bind(this),this.scheduleWork()}update(t){let e=this.view.state.field(to.state).context;(e.updateViewport(t.view.viewport)||this.view.viewport.to>e.treeLen)&&this.scheduleWork(),t.docChanged&&(this.view.hasFocus&&(this.chunkBudget+=50),this.scheduleWork()),this.checkAsyncSchedule(e)}scheduleWork(){if(this.working)return;let{state:t}=this.view,e=t.field(to.state);e.tree==e.context.tree&&e.context.isDone(t.doc.length)||(this.working=co(this.work))}work(t){this.working=null;let e=Date.now();if(this.chunkEnd<e&&(this.chunkEnd<0||this.view.hasFocus)&&(this.chunkEnd=e+3e4,this.chunkBudget=3e3),this.chunkBudget<=0)return;let{state:i,viewport:{to:n}}=this.view,r=i.field(to.state);if(r.tree==r.context.tree&&r.context.isDone(n+1e5))return;let s=Math.min(this.chunkBudget,100,t?Math.max(25,t.timeRemaining()-5):1e9),o=r.context.treeLen<n&&i.doc.length>n+1e3,a=r.context.work(s,n+(o?0:1e5));this.chunkBudget-=Date.now()-e,(a||this.chunkBudget<=0)&&(r.context.takeTree(),this.view.dispatch({effects:to.setState.of(new ho(r.context))})),this.chunkBudget>0&&(!a||o)&&this.scheduleWork(),this.checkAsyncSchedule(r.context)}checkAsyncSchedule(t){t.scheduleOn&&(this.workScheduled++,t.scheduleOn.then((()=>this.scheduleWork())).catch((t=>On(this.view.state,t))).then((()=>this.workScheduled--)),t.scheduleOn=null)}destroy(){this.working&&this.working()}isWorking(){return this.working||this.workScheduled>0}},{eventHandlers:{focus(){this.scheduleWork()}}}),Oo=_t.define({combine:t=>t.length?t[0]:null,enables:[to.state,uo]});class fo{constructor(t,e=[]){this.language=t,this.support=e,this.extension=[t,e]}}class po{constructor(t,e,i,n,r,s){this.name=t,this.alias=e,this.extensions=i,this.filename=n,this.loadFunc=r,this.support=s,this.loading=null}load(){return this.loading||(this.loading=this.loadFunc().then((t=>this.support=t),(t=>{throw this.loading=null,t})))}static of(t){let{load:e,support:i}=t;if(!e){if(!i)throw new RangeError("Must pass either 'load' or 'support' to LanguageDescription.of");e=()=>Promise.resolve(i)}return new po(t.name,(t.alias||[]).concat(t.name).map((t=>t.toLowerCase())),t.extensions||[],t.filename,e,i)}static matchFilename(t,e){for(let i of t)if(i.filename&&i.filename.test(e))return i;let i=/\.([^.]+)$/.exec(e);if(i)for(let e of t)if(e.extensions.indexOf(i[1])>-1)return e;return null}static matchLanguageName(t,e,i=!0){e=e.toLowerCase();for(let i of t)if(i.alias.some((t=>t==e)))return i;if(i)for(let i of t)for(let t of i.alias){let n=e.indexOf(t);if(n>-1&&(t.length>2||!/\w/.test(e[n-1])&&!/\w/.test(e[n+t.length])))return i}return null}}const mo=_t.define(),go=_t.define({combine:t=>{if(!t.length)return"  ";if(!/^(?: +|\t+)$/.test(t[0]))throw new Error("Invalid indent unit: "+JSON.stringify(t[0]));return t[0]}});function Qo(t){let e=t.facet(go);return 9==e.charCodeAt(0)?t.tabSize*e.length:e.length}function bo(t,e){let i="",n=t.tabSize;if(9==t.facet(go).charCodeAt(0))for(;e>=n;)i+="\t",e-=n;for(let t=0;t<e;t++)i+=" ";return i}function yo(t,e){t instanceof Qe&&(t=new vo(t));for(let i of t.state.facet(mo)){let n=i(t,e);if(null!=n)return n}let i=no(t.state);return i?(n=t,r=e,So(i.resolveInner(r).enterUnfinishedNodesBefore(r),r,n)):null;var n,r}class vo{constructor(t,e={}){this.state=t,this.options=e,this.unit=Qo(t)}lineAt(t,e=1){let i=this.state.doc.lineAt(t),{simulateBreak:n}=this.options;return null!=n&&n>=i.from&&n<=i.to?(e<0?n<t:n<=t)?{text:i.text.slice(n-i.from),from:n}:{text:i.text.slice(0,n-i.from),from:i.from}:i}textAfterPos(t,e=1){if(this.options.simulateDoubleBreak&&t==this.options.simulateBreak)return"";let{text:i,from:n}=this.lineAt(t,e);return i.slice(t-n,Math.min(i.length,t+100-n))}column(t,e=1){let{text:i,from:n}=this.lineAt(t,e),r=this.countColumn(i,t-n),s=this.options.overrideIndentation?this.options.overrideIndentation(n):-1;return s>-1&&(r+=s-this.countColumn(i,i.search(/\S|$/))),r}countColumn(t,e=t.length){return ht(t,this.state.tabSize,e)}lineIndent(t,e=1){let{text:i,from:n}=this.lineAt(t,e),r=this.options.overrideIndentation;if(r){let t=r(n);if(t>-1)return t}return this.countColumn(i,i.search(/\S|$/))}get simulatedBreak(){return this.options.simulateBreak||null}}const xo=new l;function wo(t){let e=t.type.prop(xo);if(e)return e;let i,n=t.firstChild;if(n&&(i=n.type.prop(l.closedBy))){let e=t.lastChild,n=e&&i.indexOf(e.name)>-1;return t=>{return Ro(t,!0,1,void 0,n&&((i=t).pos!=i.options.simulateBreak||!i.options.simulateDoubleBreak)?e.from:void 0);var i}}return null==t.parent?ko:null}function So(t,e,i){for(;t;t=t.parent){let n=wo(t);if(n)return n(new $o(i,e,t))}return null}function ko(){return 0}class $o extends vo{constructor(t,e,i){super(t.state,t.options),this.base=t,this.pos=e,this.node=i}get textAfter(){return this.textAfterPos(this.pos)}get baseIndent(){let t=this.state.doc.lineAt(this.node.from);for(;;){let e=this.node.resolve(t.from);for(;e.parent&&e.parent.from==e.from;)e=e.parent;if(To(e,this.node))break;t=this.state.doc.lineAt(e.from)}return this.lineIndent(t.from)}continue(){let t=this.node.parent;return t?So(t,this.pos,this.base):0}}function To(t,e){for(let i=e;i;i=i.parent)if(t==i)return!0;return!1}function Po({closing:t,align:e=!0,units:i=1}){return n=>Ro(n,e,i,t)}function Ro(t,e,i,n,r){let s=t.textAfter,o=s.match(/^\s*/)[0].length,a=n&&s.slice(o,o+n.length)==n||r==t.pos+o,l=e?function(t){let e=t.node,i=e.childAfter(e.from),n=e.lastChild;if(!i)return null;let r=t.options.simulateBreak,s=t.state.doc.lineAt(i.from),o=null==r||r<=s.from?s.to:Math.min(s.to,r);for(let t=i.to;;){let r=e.childAfter(t);if(!r||r==n)return null;if(!r.type.isSkipped)return r.from<o?i:null;t=r.to}}(t):null;return l?a?t.column(l.from):t.column(l.to):t.baseIndent+(a?0:t.unit*i)}const Wo=t=>t.baseIndent;function Xo({except:t,units:e=1}={}){return i=>{let n=t&&t.test(i.textAfter);return i.baseIndent+(n?0:e*i.unit)}}function Co(){return Qe.transactionFilter.of((t=>{if(!t.docChanged||!t.isUserEvent("input.type"))return t;let e=t.startState.languageDataAt("indentOnInput",t.startState.selection.main.head);if(!e.length)return t;let i=t.newDoc,{head:n}=t.newSelection.main,r=i.lineAt(n);if(n>r.from+200)return t;let s=i.sliceString(r.from,n);if(!e.some((t=>t.test(s))))return t;let{state:o}=t,a=-1,l=[];for(let{head:t}of o.selection.ranges){let e=o.doc.lineAt(t);if(e.from==a)continue;a=e.from;let i=yo(o,e.from);if(null==i)continue;let n=/^\s*/.exec(e.text)[0],r=bo(o,i);n!=r&&l.push({from:e.from,to:e.from+n.length,insert:r})}return l.length?[t,{changes:l,sequential:!0}]:t}))}const Ao=_t.define(),Zo=new l;function _o(t){let e=t.firstChild,i=t.lastChild;return e&&e.to<i.from?{from:e.to,to:i.type.isError?t.to:i.from}:null}function Lo(t,e,i){for(let n of t.facet(Ao)){let r=n(t,e,i);if(r)return r}return function(t,e,i){let n=no(t);if(0==n.length)return null;let r=null;for(let s=n.resolveInner(i);s;s=s.parent){if(s.to<=i||s.from>i)continue;if(r&&s.from<e)break;let n=s.type.prop(Zo);if(n){let o=n(s,t);o&&o.from<=i&&o.from>=e&&o.to>i&&(r=o)}}return r}(t,e,i)}let Do=0;class zo{constructor(t,e,i){this.set=t,this.base=e,this.modified=i,this.id=Do++}static define(t){if(null==t?void 0:t.base)throw new Error("Can not derive from a modified tag");let e=new zo([],null,[]);if(e.set.push(e),t)for(let i of t.set)e.set.push(i);return e}static defineModifier(){let t=new Mo;return e=>e.modified.indexOf(t)>-1?e:Mo.get(e.base||e,e.modified.concat(t).sort(((t,e)=>t.id-e.id)))}}let Eo=0;class Mo{constructor(){this.instances=[],this.id=Eo++}static get(t,e){if(!e.length)return t;let i=e[0].instances.find((i=>{var n,r;return i.base==t&&(n=e,r=i.modified,n.length==r.length&&n.every(((t,e)=>t==r[e])))}));if(i)return i;let n=[],r=new zo(n,t,e);for(let t of e)t.instances.push(r);let s=jo(e);for(let e of t.set)for(let t of s)n.push(Mo.get(e,t));return r}}function jo(t){let e=[t];for(let i=0;i<t.length;i++)for(let n of jo(t.slice(0,i).concat(t.slice(i+1))))e.push(n);return e}function qo(t){let e=Object.create(null);for(let i in t){let n=t[i];Array.isArray(n)||(n=[n]);for(let t of i.split(" "))if(t){let i=[],r=2,s=t;for(let e=0;;){if("..."==s&&e>0&&e+3==t.length){r=1;break}let n=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(s);if(!n)throw new RangeError("Invalid path: "+t);if(i.push("*"==n[0]?null:'"'==n[0][0]?JSON.parse(n[0]):n[0]),e+=n[0].length,e==t.length)break;let o=t[e++];if(e==t.length&&"!"==o){r=0;break}if("/"!=o)throw new RangeError("Invalid path: "+t);s=t.slice(e)}let o=i.length-1,a=i[o];if(!a)throw new RangeError("Invalid path: "+t);let l=new Io(n,r,o>0?i.slice(0,o):null);e[a]=l.sort(e[a])}}return Yo.add(e)}const Yo=new l,Vo=_t.define({combine:t=>t.length?No.combinedMatch(t):null}),Uo=_t.define({combine:t=>t.length?t[0].match:null});function Go(t){return t.facet(Vo)||t.facet(Uo)}class Io{constructor(t,e,i,n){this.tags=t,this.mode=e,this.context=i,this.next=n}sort(t){return!t||t.depth<this.depth?(this.next=t,this):(t.next=this.sort(t.next),t)}get depth(){return this.context?this.context.length:0}}class No{constructor(t,e){let i;function n(t){let e=B.newName();return(i||(i=Object.create(null)))["."+e]=t,e}this.map=Object.create(null),this.all="string"==typeof e.all?e.all:e.all?n(e.all):null;for(let e of t){let t=(e.class||n(Object.assign({},e,{tag:null})))+(this.all?" "+this.all:""),i=e.tag;if(Array.isArray(i))for(let e of i)this.map[e.id]=t;else this.map[i.id]=t}this.module=i?new B(i):null,this.scope=e.scope||null,this.match=this.match.bind(this);let r=[Fo];this.module&&r.push(Os.styleModule.of(this.module)),this.extension=r.concat(Vo.of(this)),this.fallback=r.concat(Uo.of(this))}match(t,e){if(this.scope&&e!=this.scope)return null;for(let e of t.set){let i=this.map[e.id];if(void 0!==i)return e!=t&&(this.map[t.id]=i),i}return this.map[t.id]=this.all}static combinedMatch(t){if(1==t.length)return t[0].match;let e=t.some((t=>t.scope))?void 0:Object.create(null);return(i,n)=>{let r=e&&e[i.id];if(void 0!==r)return r;let s=null;for(let e of t){let t=e.match(i,n);t&&(s=s?s+" "+t:t)}return e&&(e[i.id]=s),s}}static define(t,e){return new No(t,e||{})}static get(t,e,i){let n=Go(t);return n&&n(e,i||u.none)}}function Bo(t,e,i,n=0,r=t.length){Ko(t,n,r,e,i)}const Fo=Yt.high(Qn.fromClass(class{constructor(t){this.markCache=Object.create(null),this.tree=no(t.state),this.decorations=this.buildDeco(t,Go(t.state))}update(t){let e=no(t.state),i=Go(t.state),n=i!=t.startState.facet(Vo);e.length<t.view.viewport.to&&!n&&e.type==this.tree.type?this.decorations=this.decorations.map(t.changes):(e!=this.tree||t.viewportChanged||n)&&(this.tree=e,this.decorations=this.buildDeco(t.view,i))}buildDeco(t,e){if(!e||!this.tree.length)return Yi.none;let i=new ke;for(let{from:n,to:r}of t.visibleRanges)Ko(this.tree,n,r,e,((t,e,n)=>{i.add(t,e,this.markCache[n]||(this.markCache[n]=Yi.mark({class:n})))}));return i.finish()}},{decorations:t=>t.decorations})),Ho=[""];class Jo{constructor(t,e,i){this.at=t,this.style=e,this.span=i,this.class=""}startSpan(t,e){e!=this.class&&(this.flush(t),t>this.at&&(this.at=t),this.class=e)}flush(t){t>this.at&&this.class&&this.span(this.at,t,this.class)}highlightRange(t,e,i,n,r,s){let{type:o,from:a,to:h}=t;if(a>=i||h<=e)return;Ho[r]=o.name,o.isTop&&(s=o);let c=n,u=o.prop(Yo),O=!1;for(;u;){if(!u.context||ta(u.context,Ho,r)){for(let t of u.tags){let e=this.style(t,s);e&&(c&&(c+=" "),c+=e,1==u.mode?n+=(n?" ":"")+e:0==u.mode&&(O=!0))}break}u=u.next}if(this.startSpan(t.from,c),O)return;let d=t.tree&&t.tree.prop(l.mounted);if(d&&d.overlay){let o=t.node.enter(d.overlay[0].from+a,1),l=t.firstChild();for(let u=0,O=a;;u++){let f=u<d.overlay.length?d.overlay[u]:null,p=f?f.from+a:h,m=Math.max(e,O),g=Math.min(i,p);if(m<g&&l)for(;t.from<g&&(this.highlightRange(t,m,g,n,r+1,s),this.startSpan(Math.min(i,t.to),c),!(t.to>=p)&&t.nextSibling()););if(!f||p>i)break;O=f.to+a,O>e&&(this.highlightRange(o.cursor,Math.max(e,f.from+a),Math.min(i,O),n,r,d.tree.type),this.startSpan(O,c))}l&&t.parent()}else if(t.firstChild()){do{if(!(t.to<=e)){if(t.from>=i)break;this.highlightRange(t,e,i,n,r+1,s),this.startSpan(Math.min(i,t.to),c)}}while(t.nextSibling());t.parent()}}}function Ko(t,e,i,n,r){let s=new Jo(e,n,r);s.highlightRange(t.cursor(),e,i,"",0,t.type),s.flush(i)}function ta(t,e,i){if(t.length>i-1)return!1;for(let n=i-1,r=t.length-1;r>=0;r--,n--){let i=t[r];if(i&&i!=e[n])return!1}return!0}const ea=zo.define,ia=ea(),na=ea(),ra=ea(na),sa=ea(na),oa=ea(),aa=ea(oa),la=ea(oa),ha=ea(),ca=ea(ha),ua=ea(),Oa=ea(),da=ea(),fa=ea(da),pa=ea(),ma={comment:ia,lineComment:ea(ia),blockComment:ea(ia),docComment:ea(ia),name:na,variableName:ea(na),typeName:ra,tagName:ea(ra),propertyName:sa,attributeName:ea(sa),className:ea(na),labelName:ea(na),namespace:ea(na),macroName:ea(na),literal:oa,string:aa,docString:ea(aa),character:ea(aa),attributeValue:ea(aa),number:la,integer:ea(la),float:ea(la),bool:ea(oa),regexp:ea(oa),escape:ea(oa),color:ea(oa),url:ea(oa),keyword:ua,self:ea(ua),null:ea(ua),atom:ea(ua),unit:ea(ua),modifier:ea(ua),operatorKeyword:ea(ua),controlKeyword:ea(ua),definitionKeyword:ea(ua),moduleKeyword:ea(ua),operator:Oa,derefOperator:ea(Oa),arithmeticOperator:ea(Oa),logicOperator:ea(Oa),bitwiseOperator:ea(Oa),compareOperator:ea(Oa),updateOperator:ea(Oa),definitionOperator:ea(Oa),typeOperator:ea(Oa),controlOperator:ea(Oa),punctuation:da,separator:ea(da),bracket:fa,angleBracket:ea(fa),squareBracket:ea(fa),paren:ea(fa),brace:ea(fa),content:ha,heading:ca,heading1:ea(ca),heading2:ea(ca),heading3:ea(ca),heading4:ea(ca),heading5:ea(ca),heading6:ea(ca),contentSeparator:ea(ha),list:ea(ha),quote:ea(ha),emphasis:ea(ha),strong:ea(ha),link:ea(ha),monospace:ea(ha),strikethrough:ea(ha),inserted:ea(),deleted:ea(),changed:ea(),invalid:ea(),meta:pa,documentMeta:ea(pa),annotation:ea(pa),processingInstruction:ea(pa),definition:zo.defineModifier(),constant:zo.defineModifier(),function:zo.defineModifier(),standard:zo.defineModifier(),local:zo.defineModifier(),special:zo.defineModifier()};function ga(t,e,i,n=0,r=0){null==e&&-1==(e=t.search(/[^\s\u00a0]/))&&(e=t.length);let s=r;for(let r=n;r<e;r++)9==t.charCodeAt(r)?s+=i-s%i:s++;return s}class Qa{constructor(t,e,i){this.string=t,this.tabSize=e,this.indentUnit=i,this.pos=0,this.start=0,this.lastColumnPos=0,this.lastColumnValue=0}eol(){return this.pos>=this.string.length}sol(){return 0==this.pos}peek(){return this.string.charAt(this.pos)||void 0}next(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)}eat(t){let e,i=this.string.charAt(this.pos);if(e="string"==typeof t?i==t:i&&(t instanceof RegExp?t.test(i):t(i)),e)return++this.pos,i}eatWhile(t){let e=this.pos;for(;this.eat(t););return this.pos>e}eatSpace(){let t=this.pos;for(;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>t}skipToEnd(){this.pos=this.string.length}skipTo(t){let e=this.string.indexOf(t,this.pos);if(e>-1)return this.pos=e,!0}backUp(t){this.pos-=t}column(){return this.lastColumnPos<this.start&&(this.lastColumnValue=ga(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue}indentation(){return ga(this.string,null,this.tabSize)}match(t,e,i){if("string"==typeof t){let n=t=>i?t.toLowerCase():t;return n(this.string.substr(this.pos,t.length))==n(t)?(!1!==e&&(this.pos+=t.length),!0):null}{let i=this.string.slice(this.pos).match(t);return i&&i.index>0?null:(i&&!1!==e&&(this.pos+=i[0].length),i)}}current(){return this.string.slice(this.start,this.pos)}}function ba(t){if("object"!=typeof t)return t;let e={};for(let i in t){let n=t[i];e[i]=n instanceof Array?n.slice():n}return e}class ya extends to{constructor(t){let e,i=Ks(t.languageData),n={token:(r=t).token,blankLine:r.blankLine||(()=>{}),startState:r.startState||(()=>!0),copyState:r.copyState||ba,indent:r.indent||(()=>null),languageData:r.languageData||{},tokenTable:r.tokenTable||ka};var r;super(i,new class extends X{createParse(t,i,n){return new wa(e,t,i,n)}},function(t){let e=u.define({id:$a.length,name:"Document",props:[Js.add((()=>t))]});return $a.push(e),e}(i),[mo.of(((t,e)=>this.getIndent(t,e)))]),e=this,this.streamParser=n,this.stateAfter=new l({perNode:!0}),this.tokenTable=t.tokenTable?new Wa(n.tokenTable):Xa}static define(t){return new ya(t)}getIndent(t,e){let i=no(t.state),n=i.resolve(e);for(;n&&n.type!=this.topNode;)n=n.parent;if(!n)return null;let r,s,o=va(this,i,0,n.from,e);if(o?(s=o.state,r=o.pos+1):(s=this.streamParser.startState(t.unit),r=0),e-r>1e4)return null;for(;r<e;){let i=t.state.doc.lineAt(r),n=Math.min(e,i.to);if(i.length){let e=new Qa(i.text,t.state.tabSize,t.unit);for(;e.pos<n-i.from;)Sa(this.streamParser.token,e,s)}else this.streamParser.blankLine(s,t.unit);if(n==e)break;r=i.to+1}let{text:a}=t.state.doc.lineAt(e);return this.streamParser.indent(s,/^\s*(.*)/.exec(a)[1],t)}get allowsNesting(){return!1}}function va(t,e,i,n,r){let s=i>=n&&i+e.length<=r&&e.prop(t.stateAfter);if(s)return{state:t.streamParser.copyState(s),pos:i+e.length};for(let s=e.children.length-1;s>=0;s--){let o=e.children[s],a=i+e.positions[s],l=o instanceof p&&a<r&&va(t,o,a,n,r);if(l)return l}return null}function xa(t,e,i,n,r){if(r&&i<=0&&n>=e.length)return e;r||e.type!=t.topNode||(r=!0);for(let s=e.children.length-1;s>=0;s--){let o,a=e.positions[s],l=e.children[s];if(a<n&&l instanceof p){if(!(o=xa(t,l,i-a,n-a,r)))break;return r?new p(e.type,e.children.slice(0,s).concat(o),e.positions.slice(0,s+1),a+o.length):o}}return null}class wa{constructor(t,e,i,n){this.lang=t,this.input=e,this.fragments=i,this.ranges=n,this.stoppedAt=null,this.chunks=[],this.chunkPos=[],this.chunk=[],this.chunkReused=void 0,this.rangeIndex=0,this.to=n[n.length-1].to;let r=ao.get(),s=n[0].from,{state:o,tree:a}=function(t,e,i,n){for(let n of e){let e,r=n.from+(n.openStart?25:0),s=n.to-(n.openEnd?25:0),o=r<=i&&s>i&&va(t,n.tree,0-n.offset,i,s);if(o&&(e=xa(t,n.tree,i+n.offset,o.pos+n.offset,!1)))return{state:o.state,tree:e}}return{state:t.streamParser.startState(n?Qo(n):4),tree:p.empty}}(t,i,s,null==r?void 0:r.state);this.state=o,this.parsedPos=this.chunkStart=s+a.length;for(let t=0;t<a.children.length;t++)this.chunks.push(a.children[t]),this.chunkPos.push(a.positions[t]);r&&this.parsedPos<r.viewport.from-1e5&&(this.state=this.lang.streamParser.startState(Qo(r.state)),r.skipUntilInView(this.parsedPos,r.viewport.from),this.parsedPos=r.viewport.from)}advance(){let t=ao.get(),e=null==this.stoppedAt?this.to:Math.min(this.to,this.stoppedAt),i=Math.min(e,this.chunkStart+2048);for(t&&(i=Math.min(i,t.viewport.to));this.parsedPos<i;)this.parseLine(t);return this.chunkStart<this.parsedPos&&this.finishChunk(),this.parsedPos>=e?this.finish():t&&this.parsedPos>=t.viewport.to?(t.skipUntilInView(this.parsedPos,e),this.finish()):null}stopAt(t){this.stoppedAt=t}lineAfter(t){let e=this.input.chunk(t);if(this.input.lineChunks)"\n"==e&&(e="");else{let t=e.indexOf("\n");t>-1&&(e=e.slice(0,t))}return t+e.length<=this.to?e:e.slice(0,this.to-t)}nextLine(){let t=this.parsedPos,e=this.lineAfter(t),i=t+e.length;for(let t=this.rangeIndex;;){let n=this.ranges[t].to;if(n>=i)break;if(e=e.slice(0,n-(i-e.length)),t++,t==this.ranges.length)break;let r=this.ranges[t].from,s=this.lineAfter(r);e+=s,i=r+s.length}return{line:e,end:i}}skipGapsTo(t,e,i){for(;;){let n=this.ranges[this.rangeIndex].to,r=t+e;if(i>0?n>r:n>=r)break;e+=this.ranges[++this.rangeIndex].from-n}return e}emitToken(t,e,i,n,r){if(this.ranges.length>1){e+=r=this.skipGapsTo(e,r,1);let t=this.chunk.length;i+=r=this.skipGapsTo(i,r,-1),n+=this.chunk.length-t}return this.chunk.push(t,e,i,n),r}parseLine(t){let{line:e,end:i}=this.nextLine(),n=0,{streamParser:r}=this.lang,s=new Qa(e,t?t.state.tabSize:4,t?Qo(t.state):2);if(s.eol())r.blankLine(this.state,s.indentUnit);else for(;!s.eol();){let t=Sa(r.token,s,this.state);if(t&&(n=this.emitToken(this.lang.tokenTable.resolve(t),this.parsedPos+s.start,this.parsedPos+s.pos,4,n)),s.start>1e4)break}this.parsedPos=i,this.parsedPos<this.to&&this.parsedPos++}finishChunk(){let t=p.build({buffer:this.chunk,start:this.chunkStart,length:this.parsedPos-this.chunkStart,nodeSet:Ta,topID:0,maxBufferLength:2048,reused:this.chunkReused});t=new p(t.type,t.children,t.positions,t.length,[[this.lang.stateAfter,this.lang.streamParser.copyState(this.state)]]),this.chunks.push(t),this.chunkPos.push(this.chunkStart-this.ranges[0].from),this.chunk=[],this.chunkReused=void 0,this.chunkStart=this.parsedPos}finish(){return new p(this.lang.topNode,this.chunks,this.chunkPos,this.parsedPos-this.ranges[0].from).balance()}}function Sa(t,e,i){e.start=e.pos;for(let n=0;n<10;n++){let n=t(e,i);if(e.pos>e.start)return n}throw new Error("Stream parser failed to advance stream.")}const ka=Object.create(null),$a=[u.none],Ta=new O($a),Pa=[],Ra=Object.create(null);for(let[t,e]of[["variable","variableName"],["variable-2","variableName.special"],["string-2","string.special"],["def","variableName.definition"],["tag","typeName"],["attribute","propertyName"],["type","typeName"],["builtin","variableName.standard"],["qualifier","modifier"],["error","invalid"],["header","heading"],["property","propertyName"]])Ra[t]=Aa(ka,e);class Wa{constructor(t){this.extra=t,this.table=Object.assign(Object.create(null),Ra)}resolve(t){return t?this.table[t]||(this.table[t]=Aa(this.extra,t)):0}}const Xa=new Wa(ka);function Ca(t,e){Pa.indexOf(t)>-1||(Pa.push(t),console.warn(e))}function Aa(t,e){let i=null;for(let n of e.split(".")){let e=t[n]||ma[n];e?"function"==typeof e?i?i=e(i):Ca(n,`Modifier ${n} used at start of tag`):i?Ca(n,`Tag ${n} used as modifier`):i=e:Ca(n,`Unknown highlighting tag ${n}`)}if(!i)return 0;let n=e.replace(/ /g,"_"),r=u.define({id:$a.length,name:n,props:[qo({[n]:i})]});return $a.push(r),r.id}const Za="undefined"!=typeof navigator&&!/Edge\/(\d+)/.exec(navigator.userAgent)&&/Apple Computer/.test(navigator.vendor)&&(/Mobile\/\w+/.test(navigator.userAgent)||navigator.maxTouchPoints>2);class _a{constructor(t,e,i){this.facet=e,this.createTooltipView=i,this.input=t.state.facet(e),this.tooltips=this.input.filter((t=>t)),this.tooltipViews=this.tooltips.map(i)}update(t){let e=t.state.facet(this.facet),i=e.filter((t=>t));if(e===this.input){for(let e of this.tooltipViews)e.update&&e.update(t);return!1}let n=[];for(let e=0;e<i.length;e++){let r=i[e],s=-1;if(r){for(let t=0;t<this.tooltips.length;t++){let e=this.tooltips[t];e&&e.create==r.create&&(s=t)}if(s<0)n[e]=this.createTooltipView(r);else{let i=n[e]=this.tooltipViews[s];i.update&&i.update(t)}}}for(let t of this.tooltipViews)n.indexOf(t)<0&&t.dom.remove();return this.input=e,this.tooltips=i,this.tooltipViews=n,!0}}function La(t={}){return za.of(t)}function Da(){return{top:0,left:0,bottom:innerHeight,right:innerWidth}}const za=_t.define({combine:t=>{var e,i,n;return{position:Za?"absolute":(null===(e=t.find((t=>t.position)))||void 0===e?void 0:e.position)||"fixed",parent:(null===(i=t.find((t=>t.parent)))||void 0===i?void 0:i.parent)||null,tooltipSpace:(null===(n=t.find((t=>t.tooltipSpace)))||void 0===n?void 0:n.tooltipSpace)||Da}}}),Ea=Qn.fromClass(class{constructor(t){var e;this.view=t,this.inView=!0,this.lastTransaction=0,this.measureTimeout=-1;let i=t.state.facet(za);this.position=i.position,this.parent=i.parent,this.classes=t.themeClasses,this.createContainer(),this.measureReq={read:this.readMeasure.bind(this),write:this.writeMeasure.bind(this),key:this},this.manager=new _a(t,qa,(t=>this.createTooltip(t))),this.intersectionObserver="function"==typeof IntersectionObserver?new IntersectionObserver((t=>{Date.now()>this.lastTransaction-50&&t.length>0&&t[t.length-1].intersectionRatio<1&&this.measureSoon()}),{threshold:[1]}):null,this.observeIntersection(),null===(e=t.dom.ownerDocument.defaultView)||void 0===e||e.addEventListener("resize",this.measureSoon=this.measureSoon.bind(this)),this.maybeMeasure()}createContainer(){this.parent?(this.container=document.createElement("div"),this.container.style.position="relative",this.container.className=this.view.themeClasses,this.parent.appendChild(this.container)):this.container=this.view.dom}observeIntersection(){if(this.intersectionObserver){this.intersectionObserver.disconnect();for(let t of this.manager.tooltipViews)this.intersectionObserver.observe(t.dom)}}measureSoon(){this.measureTimeout<0&&(this.measureTimeout=setTimeout((()=>{this.measureTimeout=-1,this.maybeMeasure()}),50))}update(t){t.transactions.length&&(this.lastTransaction=Date.now());let e=this.manager.update(t);e&&this.observeIntersection();let i=e||t.geometryChanged,n=t.state.facet(za);if(n.position!=this.position){this.position=n.position;for(let t of this.manager.tooltipViews)t.dom.style.position=this.position;i=!0}if(n.parent!=this.parent){this.parent&&this.container.remove(),this.parent=n.parent,this.createContainer();for(let t of this.manager.tooltipViews)this.container.appendChild(t.dom);i=!0}else this.parent&&this.view.themeClasses!=this.classes&&(this.classes=this.container.className=this.view.themeClasses);i&&this.maybeMeasure()}createTooltip(t){let e=t.create(this.view);if(e.dom.classList.add("cm-tooltip"),t.arrow&&!e.dom.querySelector(".cm-tooltip > .cm-tooltip-arrow")){let t=document.createElement("div");t.className="cm-tooltip-arrow",e.dom.appendChild(t)}return e.dom.style.position=this.position,e.dom.style.top="-10000px",this.container.appendChild(e.dom),e.mount&&e.mount(this.view),e}destroy(){var t,e;null===(t=this.view.dom.ownerDocument.defaultView)||void 0===t||t.removeEventListener("resize",this.measureSoon);for(let{dom:t}of this.manager.tooltipViews)t.remove();null===(e=this.intersectionObserver)||void 0===e||e.disconnect(),clearTimeout(this.measureTimeout)}readMeasure(){let t=this.view.dom.getBoundingClientRect();return{editor:t,parent:this.parent?this.container.getBoundingClientRect():t,pos:this.manager.tooltips.map((t=>this.view.coordsAtPos(t.pos))),size:this.manager.tooltipViews.map((({dom:t})=>t.getBoundingClientRect())),space:this.view.state.facet(za).tooltipSpace(this.view)}}writeMeasure(t){let{editor:e,space:i}=t,n=[];for(let r=0;r<this.manager.tooltips.length;r++){let s=this.manager.tooltips[r],o=this.manager.tooltipViews[r],{dom:a}=o,l=t.pos[r],h=t.size[r];if(!l||l.bottom<=Math.max(e.top,i.top)||l.top>=Math.min(e.bottom,i.bottom)||l.right<Math.max(e.left,i.left)-.1||l.left>Math.min(e.right,i.right)+.1){a.style.top="-10000px";continue}let c=s.arrow?o.dom.querySelector(".cm-tooltip-arrow"):null,u=c?7:0,O=h.right-h.left,d=h.bottom-h.top,f=o.offset||ja,p=this.view.textDirection==Pn.LTR,m=h.width>i.right-i.left?p?i.left:i.right-h.width:p?Math.min(l.left-(c?14:0)+f.x,i.right-O):Math.max(i.left,l.left-O+(c?14:0)-f.x),g=!!s.above;!s.strictSide&&(g?l.top-(h.bottom-h.top)-f.y<i.top:l.bottom+(h.bottom-h.top)+f.y>i.bottom)&&g==i.bottom-l.bottom>l.top-i.top&&(g=!g);let Q=g?l.top-d-u-f.y:l.bottom+u+f.y,b=m+O;if(!0!==o.overlap)for(let t of n)t.left<b&&t.right>m&&t.top<Q+d&&t.bottom>Q&&(Q=g?t.top-d-2-u:t.bottom+u+2);"absolute"==this.position?(a.style.top=Q-t.parent.top+"px",a.style.left=m-t.parent.left+"px"):(a.style.top=Q+"px",a.style.left=m+"px"),c&&(c.style.left=l.left+(p?f.x:-f.x)-(m+14-7)+"px"),!0!==o.overlap&&n.push({left:m,top:Q,right:b,bottom:Q+d}),a.classList.toggle("cm-tooltip-above",g),a.classList.toggle("cm-tooltip-below",!g),o.positioned&&o.positioned()}}maybeMeasure(){if(this.manager.tooltips.length&&(this.view.inView&&this.view.requestMeasure(this.measureReq),this.inView!=this.view.inView&&(this.inView=this.view.inView,!this.inView)))for(let t of this.manager.tooltipViews)t.dom.style.top="-10000px"}},{eventHandlers:{scroll(){this.maybeMeasure()}}}),Ma=Os.baseTheme({".cm-tooltip":{zIndex:100},"&light .cm-tooltip":{border:"1px solid #bbb",backgroundColor:"#f5f5f5"},"&light .cm-tooltip-section:not(:first-child)":{borderTop:"1px solid #bbb"},"&dark .cm-tooltip":{backgroundColor:"#333338",color:"white"},".cm-tooltip-arrow":{height:"7px",width:"14px",position:"absolute",zIndex:-1,overflow:"hidden","&:before, &:after":{content:"''",position:"absolute",width:0,height:0,borderLeft:"7px solid transparent",borderRight:"7px solid transparent"},".cm-tooltip-above &":{bottom:"-7px","&:before":{borderTop:"7px solid #bbb"},"&:after":{borderTop:"7px solid #f5f5f5",bottom:"1px"}},".cm-tooltip-below &":{top:"-7px","&:before":{borderBottom:"7px solid #bbb"},"&:after":{borderBottom:"7px solid #f5f5f5",top:"1px"}}},"&dark .cm-tooltip .cm-tooltip-arrow":{"&:before":{borderTopColor:"#333338",borderBottomColor:"#333338"},"&:after":{borderTopColor:"transparent",borderBottomColor:"transparent"}}}),ja={x:0,y:0},qa=_t.define({enables:[Ea,Ma]});function Ya(t){var e;null===(e=t.plugin(Ea))||void 0===e||e.maybeMeasure()}class Va{constructor(t,e,i){this.state=t,this.pos=e,this.explicit=i,this.abortListeners=[]}tokenBefore(t){let e=no(this.state).resolveInner(this.pos,-1);for(;e&&t.indexOf(e.name)<0;)e=e.parent;return e?{from:e.from,to:this.pos,text:this.state.sliceDoc(e.from,this.pos),type:e.type}:null}matchBefore(t){let e=this.state.doc.lineAt(this.pos),i=Math.max(e.from,this.pos-250),n=e.text.slice(i-e.from,this.pos-e.from),r=n.search(Ba(t,!1));return r<0?null:{from:i+r,to:this.pos,text:n.slice(r)}}get aborted(){return null==this.abortListeners}addEventListener(t,e){"abort"==t&&this.abortListeners&&this.abortListeners.push(e)}}function Ua(t){let e=Object.keys(t).join(""),i=/\w/.test(e);return i&&(e=e.replace(/\w/g,"")),`[${i?"\\w":""}${e.replace(/[^\w\s]/g,"\\$&")}]`}function Ga(t){let e=t.map((t=>"string"==typeof t?{label:t}:t)),[i,n]=e.every((t=>/^\w+$/.test(t.label)))?[/\w*$/,/\w+$/]:function(t){let e=Object.create(null),i=Object.create(null);for(let{label:n}of t){e[n[0]]=!0;for(let t=1;t<n.length;t++)i[n[t]]=!0}let n=Ua(e)+Ua(i)+"*$";return[new RegExp("^"+n),new RegExp(n)]}(e);return t=>{let r=t.matchBefore(n);return r||t.explicit?{from:r?r.from:t.pos,options:e,span:i}:null}}class Ia{constructor(t,e,i){this.completion=t,this.source=e,this.match=i}}function Na(t){return t.selection.main.head}function Ba(t,e){var i;let{source:n}=t,r=e&&"^"!=n[0],s="$"!=n[n.length-1];return r||s?new RegExp(`${r?"^":""}(?:${n})${s?"$":""}`,null!==(i=t.flags)&&void 0!==i?i:t.ignoreCase?"i":""):t}const Fa=ne.define();function Ha(t,e){let i=e.completion.apply||e.completion.label,n=e.source;"string"==typeof i?t.dispatch({changes:{from:n.from,to:n.to,insert:i},selection:{anchor:n.from+i.length},userEvent:"input.complete",annotations:Fa.of(e.completion)}):i(t,e.completion,n.from,n.to)}const Ja=new WeakMap;function Ka(t){if(!Array.isArray(t))return t;let e=Ja.get(t);return e||Ja.set(t,e=Ga(t)),e}class tl{constructor(t){this.pattern=t,this.chars=[],this.folded=[],this.any=[],this.precise=[],this.byWord=[];for(let e=0;e<t.length;){let i=ot(t,e),n=lt(i);this.chars.push(i);let r=t.slice(e,e+n),s=r.toUpperCase();this.folded.push(ot(s==r?r.toLowerCase():s,0)),e+=n}this.astral=t.length!=this.chars.length}match(t){if(0==this.pattern.length)return[0];if(t.length<this.pattern.length)return null;let{chars:e,folded:i,any:n,precise:r,byWord:s}=this;if(1==e.length){let n=ot(t,0);return n==e[0]?[0,0,lt(n)]:n==i[0]?[-200,0,lt(n)]:null}let o=t.indexOf(this.pattern);if(0==o)return[0,0,this.pattern.length];let a=e.length,l=0;if(o<0){for(let r=0,s=Math.min(t.length,200);r<s&&l<a;){let s=ot(t,r);s!=e[l]&&s!=i[l]||(n[l++]=r),r+=lt(s)}if(l<a)return null}let h=0,c=0,u=!1,O=0,d=-1,f=-1,p=/[a-z]/.test(t),m=!0;for(let n=0,l=Math.min(t.length,200),g=0;n<l&&c<a;){let l=ot(t,n);o<0&&(h<a&&l==e[h]&&(r[h++]=n),O<a&&(l==e[O]||l==i[O]?(0==O&&(d=n),f=n+1,O++):O=0));let Q,b=l<255?l>=48&&l<=57||l>=97&&l<=122?2:l>=65&&l<=90?1:0:(Q=at(l))!=Q.toLowerCase()?1:Q!=Q.toUpperCase()?2:0;(!n||1==b&&p||0==g&&0!=b)&&(e[c]==l||i[c]==l&&(u=!0)?s[c++]=n:s.length&&(m=!1)),g=b,n+=lt(l)}return c==a&&0==s[0]&&m?this.result((u?-200:0)-100,s,t):O==a&&0==d?[-200-t.length,0,f]:o>-1?[-700-t.length,o,o+this.pattern.length]:O==a?[-900-t.length,d,f]:c==a?this.result((u?-200:0)-100-700+(m?0:-1100),s,t):2==e.length?null:this.result((n[0]?-700:0)-200-1100,n,t)}result(t,e,i){let n=[t-i.length],r=1;for(let t of e){let e=t+(this.astral?lt(ot(i,t)):1);r>1&&n[r-1]==t?n[r-1]=e:(n[r++]=t,n[r++]=e)}return n}}const el=_t.define({combine:t=>be(t,{activateOnTyping:!0,override:null,maxRenderedOptions:100,defaultKeymap:!0,optionClass:()=>"",aboveCursor:!1,icons:!0,addToOptions:[]},{defaultKeymap:(t,e)=>t&&e,icons:(t,e)=>t&&e,optionClass:(t,e)=>i=>{return n=t(i),r=e(i),n?r?n+" "+r:n:r;var n,r},addToOptions:(t,e)=>t.concat(e)})});function il(t,e,i){if(t<=i)return{from:0,to:t};if(e<=t>>1){let t=Math.floor(e/i);return{from:t*i,to:(t+1)*i}}let n=Math.floor((t-e)/i);return{from:t-(n+1)*i,to:t-n*i}}class nl{constructor(t,e){this.view=t,this.stateField=e,this.info=null,this.placeInfo={read:()=>this.measureInfo(),write:t=>this.positionInfo(t),key:this};let i=t.state.field(e),{options:n,selected:r}=i.open,s=t.state.facet(el);this.optionContent=function(t){let e=t.addToOptions.slice();return t.icons&&e.push({render(t){let e=document.createElement("div");return e.classList.add("cm-completionIcon"),t.type&&e.classList.add(...t.type.split(/\s+/g).map((t=>"cm-completionIcon-"+t))),e.setAttribute("aria-hidden","true"),e},position:20}),e.push({render(t,e,i){let n=document.createElement("span");n.className="cm-completionLabel";let{label:r}=t,s=0;for(let t=1;t<i.length;){let e=i[t++],o=i[t++];e>s&&n.appendChild(document.createTextNode(r.slice(s,e)));let a=n.appendChild(document.createElement("span"));a.appendChild(document.createTextNode(r.slice(e,o))),a.className="cm-completionMatchedText",s=o}return s<r.length&&n.appendChild(document.createTextNode(r.slice(s))),n},position:50},{render(t){if(!t.detail)return null;let e=document.createElement("span");return e.className="cm-completionDetail",e.textContent=t.detail,e},position:80}),e.sort(((t,e)=>t.position-e.position)).map((t=>t.render))}(s),this.optionClass=s.optionClass,this.range=il(n.length,r,s.maxRenderedOptions),this.dom=document.createElement("div"),this.dom.className="cm-tooltip-autocomplete",this.dom.addEventListener("mousedown",(e=>{for(let i,r=e.target;r&&r!=this.dom;r=r.parentNode)if("LI"==r.nodeName&&(i=/-(\d+)$/.exec(r.id))&&+i[1]<n.length)return Ha(t,n[+i[1]]),void e.preventDefault()})),this.list=this.dom.appendChild(this.createListBox(n,i.id,this.range)),this.list.addEventListener("scroll",(()=>{this.info&&this.view.requestMeasure(this.placeInfo)}))}mount(){this.updateSel()}update(t){t.state.field(this.stateField)!=t.startState.field(this.stateField)&&this.updateSel()}positioned(){this.info&&this.view.requestMeasure(this.placeInfo)}updateSel(){let t=this.view.state.field(this.stateField),e=t.open;if((e.selected<this.range.from||e.selected>=this.range.to)&&(this.range=il(e.options.length,e.selected,this.view.state.facet(el).maxRenderedOptions),this.list.remove(),this.list=this.dom.appendChild(this.createListBox(e.options,t.id,this.range)),this.list.addEventListener("scroll",(()=>{this.info&&this.view.requestMeasure(this.placeInfo)}))),this.updateSelectedOption(e.selected)){this.info&&(this.info.remove(),this.info=null);let t=e.options[e.selected];t.completion.info&&(this.info=this.dom.appendChild(function(t,e){let i=document.createElement("div");i.className="cm-tooltip cm-completionInfo";let{info:n}=t.completion;if("string"==typeof n)i.textContent=n;else{let r=n(t.completion);r.then?r.then((t=>i.appendChild(t)),(t=>On(e.state,t,"completion info"))):i.appendChild(r)}return i}(t,this.view)),this.view.requestMeasure(this.placeInfo))}}updateSelectedOption(t){let e=null;for(let i=this.list.firstChild,n=this.range.from;i;i=i.nextSibling,n++)n==t?i.hasAttribute("aria-selected")||(i.setAttribute("aria-selected","true"),e=i):i.hasAttribute("aria-selected")&&i.removeAttribute("aria-selected");return e&&function(t,e){let i=t.getBoundingClientRect(),n=e.getBoundingClientRect();n.top<i.top?t.scrollTop-=i.top-n.top:n.bottom>i.bottom&&(t.scrollTop+=n.bottom-i.bottom)}(this.list,e),e}measureInfo(){let t=this.dom.querySelector("[aria-selected]");if(!t||!this.info)return null;let e=this.dom.getBoundingClientRect(),i=this.info.getBoundingClientRect(),n=t.getBoundingClientRect();if(n.top>Math.min(innerHeight,e.bottom)-10||n.bottom<Math.max(0,e.top)+10)return null;let r=Math.max(0,Math.min(n.top,innerHeight-i.height))-e.top,s=this.view.textDirection==Pn.RTL,o=e.left,a=innerWidth-e.right;return s&&o<Math.min(i.width,a)?s=!1:!s&&a<Math.min(i.width,o)&&(s=!0),{top:r,left:s}}positionInfo(t){this.info&&(this.info.style.top=(t?t.top:-1e6)+"px",t&&(this.info.classList.toggle("cm-completionInfo-left",t.left),this.info.classList.toggle("cm-completionInfo-right",!t.left)))}createListBox(t,e,i){const n=document.createElement("ul");n.id=e,n.setAttribute("role","listbox");for(let r=i.from;r<i.to;r++){let{completion:i,match:s}=t[r];const o=n.appendChild(document.createElement("li"));o.id=e+"-"+r,o.setAttribute("role","option");let a=this.optionClass(i);a&&(o.className=a);for(let t of this.optionContent){let e=t(i,this.view.state,s);e&&o.appendChild(e)}}return i.from&&n.classList.add("cm-completionListIncompleteTop"),i.to<t.length&&n.classList.add("cm-completionListIncompleteBottom"),n}}function rl(t){return 100*(t.boost||0)+(t.apply?10:0)+(t.info?5:0)+(t.type?1:0)}class sl{constructor(t,e,i,n,r){this.options=t,this.attrs=e,this.tooltip=i,this.timestamp=n,this.selected=r}setSelected(t,e){return t==this.selected||t>=this.options.length?this:new sl(this.options,ll(e,t),this.tooltip,this.timestamp,t)}static build(t,e,i,n,r){let s=function(t,e){let i=[],n=0;for(let r of t)if(r.hasResult())if(!1===r.result.filter)for(let t of r.result.options)i.push(new Ia(t,r,[1e9-n++]));else{let t,n=new tl(e.sliceDoc(r.from,r.to));for(let e of r.result.options)(t=n.match(e.label))&&(null!=e.boost&&(t[0]+=e.boost),i.push(new Ia(e,r,t)))}i.sort(cl);let r=[],s=null;for(let t of i.sort(cl)){if(300==r.length)break;s&&s.label==t.completion.label&&s.detail==t.completion.detail&&s.type==t.completion.type&&s.apply==t.completion.apply?rl(t.completion)>rl(s)&&(r[r.length-1]=t):r.push(t),s=t.completion}return r}(t,e);if(!s.length)return null;let o=0;if(n&&n.selected){let t=n.options[n.selected].completion;for(let e=0;e<s.length&&!o;e++)s[e].completion==t&&(o=e)}var a;return new sl(s,ll(i,o),{pos:t.reduce(((t,e)=>e.hasResult()?Math.min(t,e.from):t),1e8),create:(a=Ql,t=>new nl(t,a)),above:r.aboveCursor},n?n.timestamp:Date.now(),o)}map(t){return new sl(this.options,this.attrs,Object.assign(Object.assign({},this.tooltip),{pos:t.mapPos(this.tooltip.pos)}),this.timestamp,this.selected)}}class ol{constructor(t,e,i){this.active=t,this.id=e,this.open=i}static start(){return new ol(hl,"cm-ac-"+Math.floor(2e6*Math.random()).toString(36),null)}update(t){let{state:e}=t,i=e.facet(el),n=(i.override||e.languageDataAt("autocomplete",Na(e)).map(Ka)).map((e=>(this.active.find((t=>t.source==e))||new Ol(e,this.active.some((t=>0!=t.state))?1:0)).update(t,i)));n.length==this.active.length&&n.every(((t,e)=>t==this.active[e]))&&(n=this.active);let r=t.selection||n.some((e=>e.hasResult()&&t.changes.touchesRange(e.from,e.to)))||!function(t,e){if(t==e)return!0;for(let i=0,n=0;;){for(;i<t.length&&!t[i].hasResult;)i++;for(;n<e.length&&!e[n].hasResult;)n++;let r=i==t.length,s=n==e.length;if(r||s)return r==s;if(t[i++].result!=e[n++].result)return!1}}(n,this.active)?sl.build(n,e,this.id,this.open,i):this.open&&t.docChanged?this.open.map(t.changes):this.open;!r&&n.every((t=>1!=t.state))&&n.some((t=>t.hasResult()))&&(n=n.map((t=>t.hasResult()?new Ol(t.source,0):t)));for(let e of t.effects)e.is(gl)&&(r=r&&r.setSelected(e.value,this.id));return n==this.active&&r==this.open?this:new ol(n,this.id,r)}get tooltip(){return this.open?this.open.tooltip:null}get attrs(){return this.open?this.open.attrs:al}}const al={"aria-autocomplete":"list","aria-expanded":"false"};function ll(t,e){return{"aria-autocomplete":"list","aria-expanded":"true","aria-activedescendant":t+"-"+e,"aria-controls":t}}const hl=[];function cl(t,e){return e.match[0]-t.match[0]||t.completion.label.localeCompare(e.completion.label)}function ul(t){return t.isUserEvent("input.type")?"input":t.isUserEvent("delete.backward")?"delete":null}class Ol{constructor(t,e,i=-1){this.source=t,this.state=e,this.explicitPos=i}hasResult(){return!1}update(t,e){let i=ul(t),n=this;i?n=n.handleUserEvent(t,i,e):t.docChanged?n=n.handleChange(t):t.selection&&0!=n.state&&(n=new Ol(n.source,0));for(let e of t.effects)if(e.is(fl))n=new Ol(n.source,1,e.value?Na(t.state):-1);else if(e.is(pl))n=new Ol(n.source,0);else if(e.is(ml))for(let t of e.value)t.source==n.source&&(n=t);return n}handleUserEvent(t,e,i){return"delete"!=e&&i.activateOnTyping?new Ol(this.source,1):this.map(t.changes)}handleChange(t){return t.changes.touchesRange(Na(t.startState))?new Ol(this.source,0):this.map(t.changes)}map(t){return t.empty||this.explicitPos<0?this:new Ol(this.source,this.state,t.mapPos(this.explicitPos))}}class dl extends Ol{constructor(t,e,i,n,r,s){super(t,2,e),this.result=i,this.from=n,this.to=r,this.span=s}hasResult(){return!0}handleUserEvent(t,e,i){let n=t.changes.mapPos(this.from),r=t.changes.mapPos(this.to,1),s=Na(t.state);if((this.explicitPos<0?s<=n:s<this.from)||s>r||"delete"==e&&Na(t.startState)==this.from)return new Ol(this.source,"input"==e&&i.activateOnTyping?1:0);let o=this.explicitPos<0?-1:t.changes.mapPos(this.explicitPos);return this.span&&(n==r||this.span.test(t.state.sliceDoc(n,r)))?new dl(this.source,o,this.result,n,r,this.span):new Ol(this.source,1,o)}handleChange(t){return t.changes.touchesRange(this.from,this.to)?new Ol(this.source,0):this.map(t.changes)}map(t){return t.empty?this:new dl(this.source,this.explicitPos<0?-1:t.mapPos(this.explicitPos),this.result,t.mapPos(this.from),t.mapPos(this.to,1),this.span)}}const fl=oe.define(),pl=oe.define(),ml=oe.define({map:(t,e)=>t.map((t=>t.map(e)))}),gl=oe.define(),Ql=jt.define({create:()=>ol.start(),update:(t,e)=>t.update(e),provide:t=>[qa.from(t,(t=>t.tooltip)),Os.contentAttributes.from(t,(t=>t.attrs))]});function bl(t,e="option"){return i=>{let n=i.state.field(Ql,!1);if(!n||!n.open||Date.now()-n.open.timestamp<75)return!1;let r,s=1;"page"==e&&(r=function(t,e){let i=t.plugin(Ea);if(!i)return null;let n=i.manager.tooltips.indexOf(e);return n<0?null:i.manager.tooltipViews[n]}(i,n.open.tooltip))&&(s=Math.max(2,Math.floor(r.dom.offsetHeight/r.dom.querySelector("li").offsetHeight)-1));let o=n.open.selected+s*(t?1:-1),{length:a}=n.open.options;return o<0?o="page"==e?0:a-1:o>=a&&(o="page"==e?a-1:0),i.dispatch({effects:gl.of(o)}),!0}}const yl=t=>{let e=t.state.field(Ql,!1);return!(t.state.readOnly||!e||!e.open||Date.now()-e.open.timestamp<75||(Ha(t,e.open.options[e.open.selected]),0))},vl=t=>{let e=t.state.field(Ql,!1);return!(!e||!e.active.some((t=>0!=t.state))||(t.dispatch({effects:pl.of(null)}),0))};class xl{constructor(t,e){this.active=t,this.context=e,this.time=Date.now(),this.updates=[],this.done=void 0}}const wl=Qn.fromClass(class{constructor(t){this.view=t,this.debounceUpdate=-1,this.running=[],this.debounceAccept=-1,this.composing=0;for(let e of t.state.field(Ql).active)1==e.state&&this.startQuery(e)}update(t){let e=t.state.field(Ql);if(!t.selectionSet&&!t.docChanged&&t.startState.field(Ql)==e)return;let i=t.transactions.some((t=>(t.selection||t.docChanged)&&!ul(t)));for(let e=0;e<this.running.length;e++){let n=this.running[e];if(i||n.updates.length+t.transactions.length>50&&n.time-Date.now()>1e3){for(let t of n.context.abortListeners)try{t()}catch(t){On(this.view.state,t)}n.context.abortListeners=null,this.running.splice(e--,1)}else n.updates.push(...t.transactions)}if(this.debounceUpdate>-1&&clearTimeout(this.debounceUpdate),this.debounceUpdate=e.active.some((t=>1==t.state&&!this.running.some((e=>e.active.source==t.source))))?setTimeout((()=>this.startUpdate()),50):-1,0!=this.composing)for(let e of t.transactions)"input"==ul(e)?this.composing=2:2==this.composing&&e.selection&&(this.composing=3)}startUpdate(){this.debounceUpdate=-1;let{state:t}=this.view,e=t.field(Ql);for(let t of e.active)1!=t.state||this.running.some((e=>e.active.source==t.source))||this.startQuery(t)}startQuery(t){let{state:e}=this.view,i=Na(e),n=new Va(e,i,t.explicitPos==i),r=new xl(t,n);this.running.push(r),Promise.resolve(t.source(n)).then((t=>{r.context.aborted||(r.done=t||null,this.scheduleAccept())}),(t=>{this.view.dispatch({effects:pl.of(null)}),On(this.view.state,t)}))}scheduleAccept(){this.running.every((t=>void 0!==t.done))?this.accept():this.debounceAccept<0&&(this.debounceAccept=setTimeout((()=>this.accept()),50))}accept(){var t;this.debounceAccept>-1&&clearTimeout(this.debounceAccept),this.debounceAccept=-1;let e=[],i=this.view.state.facet(el);for(let n=0;n<this.running.length;n++){let r=this.running[n];if(void 0===r.done)continue;if(this.running.splice(n--,1),r.done){let n=new dl(r.active.source,r.active.explicitPos,r.done,r.done.from,null!==(t=r.done.to)&&void 0!==t?t:Na(r.updates.length?r.updates[0].startState:this.view.state),r.done.span&&!1!==r.done.filter?Ba(r.done.span,!0):null);for(let t of r.updates)n=n.update(t,i);if(n.hasResult()){e.push(n);continue}}let s=this.view.state.field(Ql).active.find((t=>t.source==r.active.source));if(s&&1==s.state)if(null==r.done){let t=new Ol(r.active.source,0);for(let e of r.updates)t=t.update(e,i);1!=t.state&&e.push(t)}else this.startQuery(s)}e.length&&this.view.dispatch({effects:ml.of(e)})}},{eventHandlers:{compositionstart(){this.composing=1},compositionend(){3==this.composing&&setTimeout((()=>this.view.dispatch({effects:fl.of(!1)})),20),this.composing=0}}}),Sl=Os.baseTheme({".cm-tooltip.cm-tooltip-autocomplete":{"& > ul":{fontFamily:"monospace",whiteSpace:"nowrap",overflow:"hidden auto",maxWidth_fallback:"700px",maxWidth:"min(700px, 95vw)",minWidth:"250px",maxHeight:"10em",listStyle:"none",margin:0,padding:0,"& > li":{overflowX:"hidden",textOverflow:"ellipsis",cursor:"pointer",padding:"1px 3px",lineHeight:1.2}}},"&light .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#17c",color:"white"},"&dark .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#347",color:"white"},".cm-completionListIncompleteTop:before, .cm-completionListIncompleteBottom:after":{content:'"···"',opacity:.5,display:"block",textAlign:"center"},".cm-tooltip.cm-completionInfo":{position:"absolute",padding:"3px 9px",width:"max-content",maxWidth:"300px"},".cm-completionInfo.cm-completionInfo-left":{right:"100%"},".cm-completionInfo.cm-completionInfo-right":{left:"100%"},"&light .cm-snippetField":{backgroundColor:"#00000022"},"&dark .cm-snippetField":{backgroundColor:"#ffffff22"},".cm-snippetFieldPosition":{verticalAlign:"text-top",width:0,height:"1.15em",margin:"0 -0.7px -.7em",borderLeft:"1.4px dotted #888"},".cm-completionMatchedText":{textDecoration:"underline"},".cm-completionDetail":{marginLeft:"0.5em",fontStyle:"italic"},".cm-completionIcon":{fontSize:"90%",width:".8em",display:"inline-block",textAlign:"center",paddingRight:".6em",opacity:"0.6"},".cm-completionIcon-function, .cm-completionIcon-method":{"&:after":{content:"'ƒ'"}},".cm-completionIcon-class":{"&:after":{content:"'○'"}},".cm-completionIcon-interface":{"&:after":{content:"'◌'"}},".cm-completionIcon-variable":{"&:after":{content:"'𝑥'"}},".cm-completionIcon-constant":{"&:after":{content:"'𝐶'"}},".cm-completionIcon-type":{"&:after":{content:"'𝑡'"}},".cm-completionIcon-enum":{"&:after":{content:"'∪'"}},".cm-completionIcon-property":{"&:after":{content:"'□'"}},".cm-completionIcon-keyword":{"&:after":{content:"'🔑︎'"}},".cm-completionIcon-namespace":{"&:after":{content:"'▢'"}},".cm-completionIcon-text":{"&:after":{content:"'abc'",fontSize:"50%",verticalAlign:"middle"}}});class kl{constructor(t,e,i,n){this.field=t,this.line=e,this.from=i,this.to=n}}class $l{constructor(t,e,i){this.field=t,this.from=e,this.to=i}map(t){let e=t.mapPos(this.from,-1,vt.TrackDel),i=t.mapPos(this.to,1,vt.TrackDel);return null==e||null==i?null:new $l(this.field,e,i)}}class Tl{constructor(t,e){this.lines=t,this.fieldPositions=e}instantiate(t,e){let i=[],n=[e],r=t.doc.lineAt(e),s=/^\s*/.exec(r.text)[0];for(let r of this.lines){if(i.length){let i=s,o=/^\t*/.exec(r)[0].length;for(let e=0;e<o;e++)i+=t.facet(go);n.push(e+i.length-o),r=i+r.slice(o)}i.push(r),e+=r.length+1}return{text:i,ranges:this.fieldPositions.map((t=>new $l(t.field,n[t.line]+t.from,n[t.line]+t.to)))}}static parse(t){let e,i=[],n=[],r=[];for(let s of t.split(/\r\n?|\n/)){for(;e=/[#$]\{(?:(\d+)(?::([^}]*))?|([^}]*))\}/.exec(s);){let t=e[1]?+e[1]:null,o=e[2]||e[3]||"",a=-1;for(let e=0;e<i.length;e++)(null!=t?i[e].seq==t:o&&i[e].name==o)&&(a=e);if(a<0){let e=0;for(;e<i.length&&(null==t||null!=i[e].seq&&i[e].seq<t);)e++;i.splice(e,0,{seq:t,name:o}),a=e;for(let t of r)t.field>=a&&t.field++}r.push(new kl(a,n.length,e.index,e.index+o.length)),s=s.slice(0,e.index)+o+s.slice(e.index+e[0].length)}n.push(s)}return new Tl(n,r)}}let Pl=Yi.widget({widget:new class extends Mi{toDOM(){let t=document.createElement("span");return t.className="cm-snippetFieldPosition",t}ignoreEvent(){return!1}}}),Rl=Yi.mark({class:"cm-snippetField"});class Wl{constructor(t,e){this.ranges=t,this.active=e,this.deco=Yi.set(t.map((t=>(t.from==t.to?Pl:Rl).range(t.from,t.to))))}map(t){let e=[];for(let i of this.ranges){let n=i.map(t);if(!n)return null;e.push(n)}return new Wl(e,this.active)}selectionInsideField(t){return t.ranges.every((t=>this.ranges.some((e=>e.field==this.active&&e.from<=t.from&&e.to>=t.to))))}}const Xl=oe.define({map:(t,e)=>t&&t.map(e)}),Cl=oe.define(),Al=jt.define({create:()=>null,update(t,e){for(let i of e.effects){if(i.is(Xl))return i.value;if(i.is(Cl)&&t)return new Wl(t.ranges,i.value)}return t&&e.docChanged&&(t=t.map(e.changes)),t&&e.selection&&!t.selectionInsideField(e.selection)&&(t=null),t},provide:t=>Os.decorations.from(t,(t=>t?t.deco:Yi.none))});function Zl(t,e){return Xt.create(t.filter((t=>t.field==e)).map((t=>Xt.range(t.from,t.to))))}function _l(t){let e=Tl.parse(t);return(t,i,n,r)=>{let{text:s,ranges:o}=e.instantiate(t.state,n),a={changes:{from:n,to:r,insert:ct.of(s)}};if(o.length&&(a.selection=Zl(o,0)),o.length>1){let e=new Wl(o,0),i=a.effects=[Xl.of(e)];void 0===t.state.field(Al,!1)&&i.push(oe.appendConfig.of([Al,El,jl,Sl]))}t.dispatch(t.state.update(a))}}function Ll(t){return({state:e,dispatch:i})=>{let n=e.field(Al,!1);if(!n||t<0&&0==n.active)return!1;let r=n.active+t,s=t>0&&!n.ranges.some((e=>e.field==r+t));return i(e.update({selection:Zl(n.ranges,r),effects:Xl.of(s?null:new Wl(n.ranges,r))})),!0}}const Dl=[{key:"Tab",run:Ll(1),shift:Ll(-1)},{key:"Escape",run:({state:t,dispatch:e})=>!!t.field(Al,!1)&&(e(t.update({effects:Xl.of(null)})),!0)}],zl=_t.define({combine:t=>t.length?t[0]:Dl}),El=Yt.highest(ws.compute([zl],(t=>t.facet(zl))));function Ml(t,e){return Object.assign(Object.assign({},e),{apply:_l(t)})}const jl=Os.domEventHandlers({mousedown(t,e){let i,n=e.state.field(Al,!1);if(!n||null==(i=e.posAtCoords({x:t.clientX,y:t.clientY})))return!1;let r=n.ranges.find((t=>t.from<=i&&t.to>=i));return!(!r||r.field==n.active||(e.dispatch({selection:Zl(n.ranges,r.field),effects:Xl.of(n.ranges.some((t=>t.field>r.field))?new Wl(n.ranges,r.field):null)}),0))}});function ql(t,e){return new RegExp(e(t.source),t.unicode?"u":"")}const Yl=Object.create(null);function Vl(t,e,i,n,r){for(let s=t.iterLines(),o=0;!s.next().done;){let t,{value:a}=s;for(e.lastIndex=0;t=e.exec(a);)if(!n[t[0]]&&o+t.index!=r&&(i.push({type:"text",label:t[0]}),n[t[0]]=!0,i.length>=2e3))return;o+=a.length+1}}function Ul(t,e,i,n,r){let s=t.length>=1e3,o=s&&e.get(t);if(o)return o;let a=[],l=Object.create(null);if(t.children){let s=0;for(let o of t.children){if(o.length>=1e3)for(let t of Ul(o,e,i,n-s,r-s))l[t.label]||(l[t.label]=!0,a.push(t));else Vl(o,i,a,l,r-s);s+=o.length+1}}else Vl(t,i,a,l,r);return s&&a.length<2e3&&e.set(t,a),a}const Gl=t=>{let e=t.state.languageDataAt("wordChars",t.pos).join(""),i=function(t){let e=t.replace(/[\\[.+*?(){|^$]/g,"\\$&");try{return new RegExp(`[\\p{Alphabetic}\\p{Number}_${e}]+`,"ug")}catch(t){return new RegExp(`[w${e}]`,"g")}}(e),n=t.matchBefore(ql(i,(t=>t+"$")));if(!n&&!t.explicit)return null;let r=n?n.from:t.pos;var s;return{from:r,options:Ul(t.state.doc,Yl[s=e]||(Yl[s]=new WeakMap),i,5e4,r),span:ql(i,(t=>"^"+t))}};function Il(t={}){return[Ql,el.of(t),wl,Bl,Sl]}const Nl=[{key:"Ctrl-Space",run:t=>!!t.state.field(Ql,!1)&&(t.dispatch({effects:fl.of(!0)}),!0)},{key:"Escape",run:vl},{key:"ArrowDown",run:bl(!0)},{key:"ArrowUp",run:bl(!1)},{key:"PageDown",run:bl(!0,"page")},{key:"PageUp",run:bl(!1,"page")},{key:"Enter",run:yl}],Bl=Yt.highest(ws.computeN([el],(t=>t.facet(el).defaultKeymap?[Nl]:[])));function Fl(t){var e;let i=null===(e=t.field(Ql,!1))||void 0===e?void 0:e.open;return i?i.options[i.selected].completion:null}const Hl={brackets:["(","[","{","'",'"'],before:")]}'\":;>"},Jl=oe.define({map(t,e){let i=e.mapPos(t,-1,vt.TrackAfter);return null==i?void 0:i}}),Kl=oe.define({map:(t,e)=>e.mapPos(t)}),th=new class extends ye{};th.startSide=1,th.endSide=-1;const eh=jt.define({create:()=>Se.empty,update(t,e){if(e.selection){let i=e.state.doc.lineAt(e.selection.main.head).from,n=e.startState.doc.lineAt(e.startState.selection.main.head).from;i!=e.changes.mapPos(n,-1)&&(t=Se.empty)}t=t.map(e.changes);for(let i of e.effects)i.is(Jl)?t=t.update({add:[th.range(i.value,i.value+1)]}):i.is(Kl)&&(t=t.update({filter:t=>t!=i.value}));return t}});function ih(){return[Os.inputHandler.of(sh),eh]}function nh(t){for(let e=0;e<8;e+=2)if("()[]{}<>".charCodeAt(e)==t)return"()[]{}<>".charAt(e+1);return at(t<128?t:t+1)}function rh(t,e){return t.languageDataAt("closeBrackets",e)[0]||Hl}function sh(t,e,i,n){if(t.composing)return!1;let r=t.state.selection.main;if(n.length>2||2==n.length&&1==lt(ot(n,0))||e!=r.from||i!=r.to)return!1;let s=function(t,e){let i=rh(t,t.selection.main.head),n=i.brackets||Hl.brackets;for(let r of n){let s=nh(ot(r,0));if(e==r)return s==r?uh(t,r,n.indexOf(r+r+r)>-1):hh(t,r,s,i.before||Hl.before);if(e==s&&ah(t,t.selection.main.from))return ch(t,r,s)}return null}(t.state,n);return!!s&&(t.dispatch(s),!0)}const oh=[{key:"Backspace",run:({state:t,dispatch:e})=>{let i=rh(t,t.selection.main.head).brackets||Hl.brackets,n=null,r=t.changeByRange((e=>{if(e.empty){let n=function(t,e){let i=t.sliceString(e-2,e);return lt(ot(i,0))==i.length?i:i.slice(1)}(t.doc,e.head);for(let r of i)if(r==n&&lh(t.doc,e.head)==nh(ot(r,0)))return{changes:{from:e.head-r.length,to:e.head+r.length},range:Xt.cursor(e.head-r.length),userEvent:"delete.backward"}}return{range:n=e}}));return n||e(t.update(r,{scrollIntoView:!0})),!n}}];function ah(t,e){let i=!1;return t.field(eh).between(0,t.doc.length,(t=>{t==e&&(i=!0)})),i}function lh(t,e){let i=t.sliceString(e,e+2);return i.slice(0,lt(ot(i,0)))}function hh(t,e,i,n){let r=null,s=t.changeByRange((s=>{if(!s.empty)return{changes:[{insert:e,from:s.from},{insert:i,from:s.to}],effects:Jl.of(s.to+e.length),range:Xt.range(s.anchor+e.length,s.head+e.length)};let o=lh(t.doc,s.head);return!o||/\s/.test(o)||n.indexOf(o)>-1?{changes:{insert:e+i,from:s.head},effects:Jl.of(s.head+e.length),range:Xt.cursor(s.head+e.length)}:{range:r=s}}));return r?null:t.update(s,{scrollIntoView:!0,userEvent:"input.type"})}function ch(t,e,i){let n=null,r=t.selection.ranges.map((e=>e.empty&&lh(t.doc,e.head)==i?Xt.cursor(e.head+i.length):n=e));return n?null:t.update({selection:Xt.create(r,t.selection.mainIndex),scrollIntoView:!0,effects:t.selection.ranges.map((({from:t})=>Kl.of(t)))})}function uh(t,e,i){let n=null,r=t.changeByRange((r=>{if(!r.empty)return{changes:[{insert:e,from:r.from},{insert:e,from:r.to}],effects:Jl.of(r.to+e.length),range:Xt.range(r.anchor+e.length,r.head+e.length)};let s=r.head,o=lh(t.doc,s);if(o==e){if(Oh(t,s))return{changes:{insert:e+e,from:s},effects:Jl.of(s+e.length),range:Xt.cursor(s+e.length)};if(ah(t,s)){let n=i&&t.sliceDoc(s,s+3*e.length)==e+e+e;return{range:Xt.cursor(s+e.length*(n?3:1)),effects:Kl.of(s)}}}else{if(i&&t.sliceDoc(s-2*e.length,s)==e+e&&Oh(t,s-2*e.length))return{changes:{insert:e+e+e+e,from:s},effects:Jl.of(s+e.length),range:Xt.cursor(s+e.length)};if(t.charCategorizer(s)(o)!=pe.Word){let i=t.sliceDoc(s-1,s);if(i!=e&&t.charCategorizer(s)(i)!=pe.Word)return{changes:{insert:e+e,from:s},effects:Jl.of(s+e.length),range:Xt.cursor(s+e.length)}}}return{range:n=r}}));return n?null:t.update(r,{scrollIntoView:!0,userEvent:"input.type"})}function Oh(t,e){let i=no(t).resolveInner(e+1);return i.parent&&i.from==e}const dh=Os.baseTheme({"&.cm-focused .cm-matchingBracket":{backgroundColor:"#328c8252"},"&.cm-focused .cm-nonmatchingBracket":{backgroundColor:"#bb555544"}}),fh=_t.define({combine:t=>be(t,{afterCursor:!0,brackets:"()[]{}",maxScanDistance:1e4})}),ph=Yi.mark({class:"cm-matchingBracket"}),mh=Yi.mark({class:"cm-nonmatchingBracket"}),gh=[jt.define({create:()=>Yi.none,update(t,e){if(!e.docChanged&&!e.selection)return t;let i=[],n=e.state.facet(fh);for(let t of e.state.selection.ranges){if(!t.empty)continue;let r=yh(e.state,t.head,-1,n)||t.head>0&&yh(e.state,t.head-1,1,n)||n.afterCursor&&(yh(e.state,t.head,1,n)||t.head<e.state.doc.length&&yh(e.state,t.head+1,-1,n));if(!r)continue;let s=r.matched?ph:mh;i.push(s.range(r.start.from,r.start.to)),r.end&&i.push(s.range(r.end.from,r.end.to))}return Yi.set(i,!0)},provide:t=>Os.decorations.from(t)}),dh];function Qh(t={}){return[fh.of(t),gh]}function bh(t,e,i){let n=t.prop(e<0?l.openedBy:l.closedBy);if(n)return n;if(1==t.name.length){let n=i.indexOf(t.name);if(n>-1&&n%2==(e<0?1:0))return[i[n+e]]}return null}function yh(t,e,i,n={}){let r=n.maxScanDistance||1e4,s=n.brackets||"()[]{}",o=no(t),a=o.resolveInner(e,i);for(let n=a;n;n=n.parent){let r=bh(n.type,i,s);if(r&&n.from<n.to)return vh(t,e,i,n,r,s)}return function(t,e,i,n,r,s,o){let a=i<0?t.sliceDoc(e-1,e):t.sliceDoc(e,e+1),l=o.indexOf(a);if(l<0||l%2==0!=i>0)return null;let h={from:i<0?e-1:e,to:i>0?e+1:e},c=t.doc.iterRange(e,i>0?t.doc.length:0),u=0;for(let t=0;!c.next().done&&t<=s;){let s=c.value;i<0&&(t+=s.length);let a=e+t*i;for(let t=i>0?0:s.length-1,e=i>0?s.length:-1;t!=e;t+=i){let e=o.indexOf(s[t]);if(!(e<0||n.resolve(a+t,1).type!=r))if(e%2==0==i>0)u++;else{if(1==u)return{start:h,end:{from:a+t,to:a+t+1},matched:e>>1==l>>1};u--}}i>0&&(t+=s.length)}return c.done?{start:h,matched:!1}:null}(t,e,i,o,a.type,r,s)}function vh(t,e,i,n,r,s){let o=n.parent,a={from:n.from,to:n.to},l=0,h=null==o?void 0:o.cursor;if(h&&(i<0?h.childBefore(n.from):h.childAfter(n.to)))do{if(i<0?h.to<=n.from:h.from>=n.to){if(0==l&&r.indexOf(h.type.name)>-1&&h.from<h.to)return{start:a,end:{from:h.from,to:h.to},matched:!0};if(bh(h.type,i,s))l++;else if(bh(h.type,-i,s)&&(l--,0==l))return{start:a,end:h.from==h.to?void 0:{from:h.from,to:h.to},matched:!1}}}while(i<0?h.prevSibling():h.nextSibling());return{start:a,matched:!1}}function xh(t,e){return Xt.create(t.ranges.map(e),t.mainIndex)}function wh(t,e){return t.update({selection:e,scrollIntoView:!0,userEvent:"select"})}function Sh({state:t,dispatch:e},i){let n=xh(t.selection,i);return!n.eq(t.selection)&&(e(wh(t,n)),!0)}function kh(t,e){return Xt.cursor(e?t.to:t.from)}function $h(t,e){return Sh(t,(i=>i.empty?t.moveByChar(i,e):kh(i,e)))}const Th=t=>$h(t,t.textDirection!=Pn.LTR),Ph=t=>$h(t,t.textDirection==Pn.LTR);function Rh(t,e){return Sh(t,(i=>i.empty?t.moveByGroup(i,e):kh(i,e)))}function Wh(t,e,i){let n=t.state.charCategorizer(e.from);return t.moveByChar(e,i,(r=>{let s=pe.Space,o=e.from,a=!1,l=!1,h=!1,c=e=>{if(a)return!1;o+=i?e.length:-e.length;let r,c=n(e);if(s==pe.Space&&(s=c),s!=c)return!1;if(s==pe.Word)if(e.toLowerCase()==e){if(!i&&l)return!1;h=!0}else if(h){if(i)return!1;a=!0}else{if(l&&i&&n(r=t.state.sliceDoc(o,o+1))==pe.Word&&r.toLowerCase()==r)return!1;l=!0}return!0};return c(r),c}))}function Xh(t,e){return Sh(t,(i=>i.empty?Wh(t,i,e):kh(i,e)))}const Ch=t=>Xh(t,!0),Ah=t=>Xh(t,!1);function Zh(t,e){return Sh(t,(i=>{if(!i.empty)return kh(i,e);let n=t.moveVertically(i,e);return n.head!=i.head?n:t.moveToLineBoundary(i,e)}))}const _h=t=>Zh(t,!1),Lh=t=>Zh(t,!0);function Dh(t,e){let{state:i}=t,n=xh(i.selection,(i=>i.empty?t.moveVertically(i,e,t.dom.clientHeight):kh(i,e)));if(n.eq(i.selection))return!1;let r=t.coordsAtPos(i.selection.main.head),s=t.scrollDOM.getBoundingClientRect();return t.dispatch(wh(i,n),{effects:r&&r.top>s.top&&r.bottom<s.bottom?Os.scrollIntoView(n.main.head,{y:"start",yMargin:r.top-s.top}):void 0}),!0}const zh=t=>Dh(t,!1),Eh=t=>Dh(t,!0);function Mh(t,e,i){let n=t.lineBlockAt(e.head),r=t.moveToLineBoundary(e,i);if(r.head==e.head&&r.head!=(i?n.to:n.from)&&(r=t.moveToLineBoundary(e,i,!1)),!i&&r.head==n.from&&n.length){let i=/^\s*/.exec(t.state.sliceDoc(n.from,Math.min(n.from+100,n.to)))[0].length;i&&e.head!=n.from+i&&(r=Xt.cursor(n.from+i))}return r}const jh=t=>Sh(t,(e=>Mh(t,e,!0))),qh=t=>Sh(t,(e=>Mh(t,e,!1)));function Yh(t,e,i){let n=!1,r=xh(t.selection,(e=>{let r=yh(t,e.head,-1)||yh(t,e.head,1)||e.head>0&&yh(t,e.head-1,1)||e.head<t.doc.length&&yh(t,e.head+1,-1);if(!r||!r.end)return e;n=!0;let s=r.start.from==e.head?r.end.to:r.end.from;return i?Xt.range(e.anchor,s):Xt.cursor(s)}));return!!n&&(e(wh(t,r)),!0)}const Vh=({state:t,dispatch:e})=>Yh(t,e,!1),Uh=({state:t,dispatch:e})=>Yh(t,e,!0);function Gh(t,e){let i=xh(t.state.selection,(t=>{let i=e(t);return Xt.range(t.anchor,i.head,i.goalColumn)}));return!i.eq(t.state.selection)&&(t.dispatch(wh(t.state,i)),!0)}function Ih(t,e){return Gh(t,(i=>t.moveByChar(i,e)))}const Nh=t=>Ih(t,t.textDirection!=Pn.LTR),Bh=t=>Ih(t,t.textDirection==Pn.LTR);function Fh(t,e){return Gh(t,(i=>t.moveByGroup(i,e)))}function Hh(t,e){return Gh(t,(i=>Wh(t,i,e)))}const Jh=t=>Hh(t,!0),Kh=t=>Hh(t,!1);function tc(t,e){return Gh(t,(i=>t.moveVertically(i,e)))}const ec=t=>tc(t,!1),ic=t=>tc(t,!0);function nc(t,e){return Gh(t,(i=>t.moveVertically(i,e,t.dom.clientHeight)))}const rc=t=>nc(t,!1),sc=t=>nc(t,!0),oc=t=>Gh(t,(e=>Mh(t,e,!0))),ac=t=>Gh(t,(e=>Mh(t,e,!1))),lc=({state:t,dispatch:e})=>(e(wh(t,{anchor:0})),!0),hc=({state:t,dispatch:e})=>(e(wh(t,{anchor:t.doc.length})),!0),cc=({state:t,dispatch:e})=>(e(wh(t,{anchor:t.selection.main.anchor,head:0})),!0),uc=({state:t,dispatch:e})=>(e(wh(t,{anchor:t.selection.main.anchor,head:t.doc.length})),!0);function Oc({state:t,dispatch:e},i){if(t.readOnly)return!1;let n="delete.selection",r=t.changeByRange((t=>{let{from:e,to:r}=t;if(e==r){let t=i(e);t<e?n="delete.backward":t>e&&(n="delete.forward"),e=Math.min(e,t),r=Math.max(r,t)}return e==r?{range:t}:{changes:{from:e,to:r},range:Xt.cursor(e)}}));return!r.changes.empty&&(e(t.update(r,{scrollIntoView:!0,userEvent:n})),!0)}function dc(t,e,i){if(t instanceof Os)for(let n of t.pluginField(pn.atomicRanges))n.between(e,e,((t,n)=>{t<e&&n>e&&(e=i?n:t)}));return e}const fc=(t,e)=>Oc(t,(i=>{let n,r,{state:s}=t,o=s.doc.lineAt(i);if(!e&&i>o.from&&i<o.from+200&&!/[^ \t]/.test(n=o.text.slice(0,i-o.from))){if("\t"==n[n.length-1])return i-1;let t=ht(n,s.tabSize)%Qo(s)||Qo(s);for(let e=0;e<t&&" "==n[n.length-1-e];e++)i--;r=i}else r=et(o.text,i-o.from,e,e)+o.from,r==i&&o.number!=(e?s.doc.lines:1)&&(r+=e?1:-1);return dc(t,r,e)})),pc=t=>fc(t,!1),mc=t=>fc(t,!0),gc=(t,e)=>Oc(t,(i=>{let n=i,{state:r}=t,s=r.doc.lineAt(n),o=r.charCategorizer(n);for(let t=null;;){if(n==(e?s.to:s.from)){n==i&&s.number!=(e?r.doc.lines:1)&&(n+=e?1:-1);break}let a=et(s.text,n-s.from,e)+s.from,l=s.text.slice(Math.min(n,a)-s.from,Math.max(n,a)-s.from),h=o(l);if(null!=t&&h!=t)break;" "==l&&n==i||(t=h),n=a}return dc(t,n,e)})),Qc=t=>gc(t,!1),bc=t=>Oc(t,(e=>{let i=t.lineBlockAt(e).to;return dc(t,e<i?i:Math.min(t.state.doc.length,e+1),!0)})),yc=(vc=!1,({state:t,dispatch:e})=>{if(t.readOnly)return!1;let i=t.changeByRange((e=>{let{from:i,to:n}=e,r=t.doc.lineAt(i),s=!vc&&i==n&&function(t,e){if(/\(\)|\[\]|\{\}/.test(t.sliceDoc(e-1,e+1)))return{from:e,to:e};let i,n=no(t).resolveInner(e),r=n.childBefore(e),s=n.childAfter(e);return r&&s&&r.to<=e&&s.from>=e&&(i=r.type.prop(l.closedBy))&&i.indexOf(s.name)>-1&&t.doc.lineAt(r.to).from==t.doc.lineAt(s.from).from?{from:r.to,to:s.from}:null}(t,i);vc&&(i=n=(n<=r.to?r:t.doc.lineAt(n)).to);let o=new vo(t,{simulateBreak:i,simulateDoubleBreak:!!s}),a=yo(o,i);for(null==a&&(a=/^\s*/.exec(t.doc.lineAt(i).text)[0].length);n<r.to&&/\s/.test(r.text[n-r.from]);)n++;s?({from:i,to:n}=s):i>r.from&&i<r.from+100&&!/\S/.test(r.text.slice(0,i))&&(i=r.from);let h=["",bo(t,a)];return s&&h.push(bo(t,o.lineIndent(r.from,-1))),{changes:{from:i,to:n,insert:ct.of(h)},range:Xt.cursor(i+1+h[1].length)}}));return e(t.update(i,{scrollIntoView:!0,userEvent:"input"})),!0});var vc;function xc(t,e){let i=-1;return t.changeByRange((n=>{let r=[];for(let s=n.from;s<=n.to;){let o=t.doc.lineAt(s);o.number>i&&(n.empty||n.to>o.from)&&(e(o,r,n),i=o.number),s=o.to+1}let s=t.changes(r);return{changes:r,range:Xt.range(s.mapPos(n.anchor,1),s.mapPos(n.head,1))}}))}const wc=({state:t,dispatch:e})=>!t.readOnly&&(e(t.update(xc(t,((e,i)=>{i.push({from:e.from,insert:t.facet(go)})})),{userEvent:"input.indent"})),!0),Sc=({state:t,dispatch:e})=>!t.readOnly&&(e(t.update(xc(t,((e,i)=>{let n=/^\s*/.exec(e.text)[0];if(!n)return;let r=ht(n,t.tabSize),s=0,o=bo(t,Math.max(0,r-Qo(t)));for(;s<n.length&&s<o.length&&n.charCodeAt(s)==o.charCodeAt(s);)s++;i.push({from:e.from+s,to:e.from+n.length,insert:o.slice(s)})})),{userEvent:"delete.dedent"})),!0),kc=[{key:"ArrowLeft",run:Th,shift:Nh,preventDefault:!0},{key:"Mod-ArrowLeft",mac:"Alt-ArrowLeft",run:t=>Rh(t,t.textDirection!=Pn.LTR),shift:t=>Fh(t,t.textDirection!=Pn.LTR)},{mac:"Cmd-ArrowLeft",run:qh,shift:ac},{key:"ArrowRight",run:Ph,shift:Bh,preventDefault:!0},{key:"Mod-ArrowRight",mac:"Alt-ArrowRight",run:t=>Rh(t,t.textDirection==Pn.LTR),shift:t=>Fh(t,t.textDirection==Pn.LTR)},{mac:"Cmd-ArrowRight",run:jh,shift:oc},{key:"ArrowUp",run:_h,shift:ec,preventDefault:!0},{mac:"Cmd-ArrowUp",run:lc,shift:cc},{mac:"Ctrl-ArrowUp",run:zh,shift:rc},{key:"ArrowDown",run:Lh,shift:ic,preventDefault:!0},{mac:"Cmd-ArrowDown",run:hc,shift:uc},{mac:"Ctrl-ArrowDown",run:Eh,shift:sc},{key:"PageUp",run:zh,shift:rc},{key:"PageDown",run:Eh,shift:sc},{key:"Home",run:qh,shift:ac},{key:"Mod-Home",run:lc,shift:cc},{key:"End",run:jh,shift:oc},{key:"Mod-End",run:hc,shift:uc},{key:"Enter",run:yc},{key:"Mod-a",run:({state:t,dispatch:e})=>(e(t.update({selection:{anchor:0,head:t.doc.length},userEvent:"select"})),!0)},{key:"Backspace",run:pc,shift:pc},{key:"Delete",run:mc},{key:"Mod-Backspace",mac:"Alt-Backspace",run:Qc},{key:"Mod-Delete",mac:"Alt-Delete",run:t=>gc(t,!0)},{mac:"Mod-Backspace",run:t=>Oc(t,(e=>{let i=t.lineBlockAt(e).from;return dc(t,e>i?i:Math.max(0,e-1),!1)}))},{mac:"Mod-Delete",run:bc}].concat([{key:"Ctrl-b",run:Th,shift:Nh,preventDefault:!0},{key:"Ctrl-f",run:Ph,shift:Bh},{key:"Ctrl-p",run:_h,shift:ec},{key:"Ctrl-n",run:Lh,shift:ic},{key:"Ctrl-a",run:t=>Sh(t,(e=>Xt.cursor(t.lineBlockAt(e.head).from,1))),shift:t=>Gh(t,(e=>Xt.cursor(t.lineBlockAt(e.head).from)))},{key:"Ctrl-e",run:t=>Sh(t,(e=>Xt.cursor(t.lineBlockAt(e.head).to,-1))),shift:t=>Gh(t,(e=>Xt.cursor(t.lineBlockAt(e.head).to)))},{key:"Ctrl-d",run:mc},{key:"Ctrl-h",run:pc},{key:"Ctrl-k",run:bc},{key:"Ctrl-Alt-h",run:Qc},{key:"Ctrl-o",run:({state:t,dispatch:e})=>{if(t.readOnly)return!1;let i=t.changeByRange((t=>({changes:{from:t.from,to:t.to,insert:ct.of(["",""])},range:Xt.cursor(t.from)})));return e(t.update(i,{scrollIntoView:!0,userEvent:"input"})),!0}},{key:"Ctrl-t",run:({state:t,dispatch:e})=>{if(t.readOnly)return!1;let i=t.changeByRange((e=>{if(!e.empty||0==e.from||e.from==t.doc.length)return{range:e};let i=e.from,n=t.doc.lineAt(i),r=i==n.from?i-1:et(n.text,i-n.from,!1)+n.from,s=i==n.to?i+1:et(n.text,i-n.from,!0)+n.from;return{changes:{from:r,to:s,insert:t.doc.slice(i,s).append(t.doc.slice(r,i))},range:Xt.cursor(s)}}));return!i.changes.empty&&(e(t.update(i,{scrollIntoView:!0,userEvent:"move.character"})),!0)}},{key:"Ctrl-v",run:Eh}].map((t=>({mac:t.key,run:t.run,shift:t.shift})))),$c=t=>{let e=Wc(t.state);return e.line?Pc(t):!!e.block&&Rc(t)};function Tc(t,e){return({state:i,dispatch:n})=>{if(i.readOnly)return!1;let r=t(e,i);return!!r&&(n(i.update(r)),!0)}}const Pc=Tc((function(t,e,i=e.selection.ranges){let n=[],r=-1;for(let{from:t,to:s}of i){let i=n.length,o=1e9;for(let i=t;i<=s;){let a=e.doc.lineAt(i);if(a.from>r&&(t==s||s>a.from)){r=a.from;let t=Wc(e,i).line;if(!t)continue;let s=/^\s*/.exec(a.text)[0].length,l=s==a.length,h=a.text.slice(s,s+t.length)==t?s:-1;s<a.text.length&&s<o&&(o=s),n.push({line:a,comment:h,token:t,indent:s,empty:l,single:!1})}i=a.to+1}if(o<1e9)for(let t=i;t<n.length;t++)n[t].indent<n[t].line.text.length&&(n[t].indent=o);n.length==i+1&&(n[i].single=!0)}if(2!=t&&n.some((t=>t.comment<0&&(!t.empty||t.single)))){let t=[];for(let{line:e,token:i,indent:r,empty:s,single:o}of n)!o&&s||t.push({from:e.from+r,insert:i+" "});let i=e.changes(t);return{changes:i,selection:e.selection.map(i,1)}}if(1!=t&&n.some((t=>t.comment>=0))){let t=[];for(let{line:e,comment:i,token:r}of n)if(i>=0){let n=e.from+i,s=n+r.length;" "==e.text[s-e.from]&&s++,t.push({from:n,to:s})}return{changes:t}}return null}),0),Rc=Tc(((t,e)=>function(t,e,i=e.selection.ranges){let n=i.map((t=>Wc(e,t.from).block));if(!n.every((t=>t)))return null;let r=i.map(((t,i)=>function(t,{open:e,close:i},n,r){let s,o,a=t.sliceDoc(n-50,n),l=t.sliceDoc(r,r+50),h=/\s*$/.exec(a)[0].length,c=/^\s*/.exec(l)[0].length,u=a.length-h;if(a.slice(u-e.length,u)==e&&l.slice(c,c+i.length)==i)return{open:{pos:n-h,margin:h&&1},close:{pos:r+c,margin:c&&1}};r-n<=100?s=o=t.sliceDoc(n,r):(s=t.sliceDoc(n,n+50),o=t.sliceDoc(r-50,r));let O=/^\s*/.exec(s)[0].length,d=/\s*$/.exec(o)[0].length,f=o.length-d-i.length;return s.slice(O,O+e.length)==e&&o.slice(f,f+i.length)==i?{open:{pos:n+O+e.length,margin:/\s/.test(s.charAt(O+e.length))?1:0},close:{pos:r-d-i.length,margin:/\s/.test(o.charAt(f-1))?1:0}}:null}(e,n[i],t.from,t.to)));if(2!=t&&!r.every((t=>t)))return{changes:e.changes(i.map(((t,e)=>r[e]?[]:[{from:t.from,insert:n[e].open+" "},{from:t.to,insert:" "+n[e].close}])))};if(1!=t&&r.some((t=>t))){let t=[];for(let e,i=0;i<r.length;i++)if(e=r[i]){let r=n[i],{open:s,close:o}=e;t.push({from:s.pos-r.open.length,to:s.pos+s.margin},{from:o.pos-o.margin,to:o.pos+r.close.length})}return{changes:t}}return null}(t,e,function(t){let e=[];for(let i of t.selection.ranges){let n=t.doc.lineAt(i.from),r=i.to<=n.to?n:t.doc.lineAt(i.to),s=e.length-1;s>=0&&e[s].to>n.from?e[s].to=r.to:e.push({from:n.from,to:r.to})}return e}(e))),0);function Wc(t,e=t.selection.main.head){let i=t.languageDataAt("commentTokens",e);return i.length?i[0]:{}}class Xc extends ye{compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}eq(t){return!1}destroy(t){}}Xc.prototype.elementClass="",Xc.prototype.toDOM=void 0,Xc.prototype.mapMode=vt.TrackBefore,Xc.prototype.startSide=Xc.prototype.endSide=-1,Xc.prototype.point=!0;const Cc=_t.define(),Ac={class:"",renderEmptyElements:!1,elementStyle:"",markers:()=>Se.empty,lineMarker:()=>null,lineMarkerChange:null,initialSpacer:null,updateSpacer:null,domEventHandlers:{}},Zc=_t.define();function _c(t){return[zc(),Zc.of(Object.assign(Object.assign({},Ac),t))]}const Lc=Os.baseTheme({".cm-gutters":{display:"flex",height:"100%",boxSizing:"border-box",left:0,zIndex:200},"&light .cm-gutters":{backgroundColor:"#f5f5f5",color:"#999",borderRight:"1px solid #ddd"},"&dark .cm-gutters":{backgroundColor:"#333338",color:"#ccc"},".cm-gutter":{display:"flex !important",flexDirection:"column",flexShrink:0,boxSizing:"border-box",minHeight:"100%",overflow:"hidden"},".cm-gutterElement":{boxSizing:"border-box"},".cm-lineNumbers .cm-gutterElement":{padding:"0 3px 0 5px",minWidth:"20px",textAlign:"right",whiteSpace:"nowrap"},"&light .cm-activeLineGutter":{backgroundColor:"#e2f2ff"},"&dark .cm-activeLineGutter":{backgroundColor:"#222227"}}),Dc=_t.define({combine:t=>t.some((t=>t))});function zc(t){let e=[Ec,Lc];return t&&!1===t.fixed&&e.push(Dc.of(!0)),e}const Ec=Qn.fromClass(class{constructor(t){this.view=t,this.prevViewport=t.viewport,this.dom=document.createElement("div"),this.dom.className="cm-gutters",this.dom.setAttribute("aria-hidden","true"),this.dom.style.minHeight=this.view.contentHeight+"px",this.gutters=t.state.facet(Zc).map((e=>new Yc(t,e)));for(let t of this.gutters)this.dom.appendChild(t.dom);this.fixed=!t.state.facet(Dc),this.fixed&&(this.dom.style.position="sticky"),this.syncGutters(!1),t.scrollDOM.insertBefore(this.dom,t.contentDOM)}update(t){if(this.updateGutters(t)){let e=this.prevViewport,i=t.view.viewport,n=Math.min(e.to,i.to)-Math.max(e.from,i.from);this.syncGutters(n<.8*(i.to-i.from))}t.geometryChanged&&(this.dom.style.minHeight=this.view.contentHeight+"px"),this.view.state.facet(Dc)!=!this.fixed&&(this.fixed=!this.fixed,this.dom.style.position=this.fixed?"sticky":""),this.prevViewport=t.view.viewport}syncGutters(t){let e=this.dom.nextSibling;t&&this.dom.remove();let i=Se.iter(this.view.state.facet(Cc),this.view.viewport.from),n=[],r=this.gutters.map((t=>new qc(t,this.view.viewport,-this.view.documentPadding.top)));for(let t of this.view.viewportLineBlocks){let e;if(Array.isArray(t.type)){for(let i of t.type)if(i.type==qi.Text){e=i;break}}else e=t.type==qi.Text?t:void 0;if(e){n.length&&(n=[]),jc(i,n,t.from);for(let t of r)t.line(this.view,e,n)}}for(let t of r)t.finish();t&&this.view.scrollDOM.insertBefore(this.dom,e)}updateGutters(t){let e=t.startState.facet(Zc),i=t.state.facet(Zc),n=t.docChanged||t.heightChanged||t.viewportChanged||!Se.eq(t.startState.facet(Cc),t.state.facet(Cc),t.view.viewport.from,t.view.viewport.to);if(e==i)for(let e of this.gutters)e.update(t)&&(n=!0);else{n=!0;let r=[];for(let n of i){let i=e.indexOf(n);i<0?r.push(new Yc(this.view,n)):(this.gutters[i].update(t),r.push(this.gutters[i]))}for(let t of this.gutters)t.dom.remove(),r.indexOf(t)<0&&t.destroy();for(let t of r)this.dom.appendChild(t.dom);this.gutters=r}return n}destroy(){for(let t of this.gutters)t.destroy();this.dom.remove()}},{provide:pn.scrollMargins.from((t=>0!=t.gutters.length&&t.fixed?t.view.textDirection==Pn.LTR?{left:t.dom.offsetWidth}:{right:t.dom.offsetWidth}:null))});function Mc(t){return Array.isArray(t)?t:[t]}function jc(t,e,i){for(;t.value&&t.from<=i;)t.from==i&&e.push(t.value),t.next()}class qc{constructor(t,e,i){this.gutter=t,this.height=i,this.localMarkers=[],this.i=0,this.cursor=Se.iter(t.markers,e.from)}line(t,e,i){this.localMarkers.length&&(this.localMarkers=[]),jc(this.cursor,this.localMarkers,e.from);let n=i.length?this.localMarkers.concat(i):this.localMarkers,r=this.gutter.config.lineMarker(t,e,n);r&&n.unshift(r);let s=this.gutter;if(0==n.length&&!s.config.renderEmptyElements)return;let o=e.top-this.height;if(this.i==s.elements.length){let i=new Vc(t,e.height,o,n);s.elements.push(i),s.dom.appendChild(i.dom)}else s.elements[this.i].update(t,e.height,o,n);this.height=e.bottom,this.i++}finish(){let t=this.gutter;for(;t.elements.length>this.i;){let e=t.elements.pop();t.dom.removeChild(e.dom),e.destroy()}}}class Yc{constructor(t,e){this.view=t,this.config=e,this.elements=[],this.spacer=null,this.dom=document.createElement("div"),this.dom.className="cm-gutter"+(this.config.class?" "+this.config.class:"");for(let i in e.domEventHandlers)this.dom.addEventListener(i,(n=>{let r=t.lineBlockAtHeight(n.clientY-t.documentTop);e.domEventHandlers[i](t,r,n)&&n.preventDefault()}));this.markers=Mc(e.markers(t)),e.initialSpacer&&(this.spacer=new Vc(t,0,0,[e.initialSpacer(t)]),this.dom.appendChild(this.spacer.dom),this.spacer.dom.style.cssText+="visibility: hidden; pointer-events: none")}update(t){let e=this.markers;if(this.markers=Mc(this.config.markers(t.view)),this.spacer&&this.config.updateSpacer){let e=this.config.updateSpacer(this.spacer.markers[0],t);e!=this.spacer.markers[0]&&this.spacer.update(t.view,0,0,[e])}let i=t.view.viewport;return!Se.eq(this.markers,e,i.from,i.to)||!!this.config.lineMarkerChange&&this.config.lineMarkerChange(t)}destroy(){for(let t of this.elements)t.destroy()}}class Vc{constructor(t,e,i,n){this.height=-1,this.above=0,this.markers=[],this.dom=document.createElement("div"),this.update(t,e,i,n)}update(t,e,i,n){this.height!=e&&(this.dom.style.height=(this.height=e)+"px"),this.above!=i&&(this.dom.style.marginTop=(this.above=i)?i+"px":""),function(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++)if(!t[i].compare(e[i]))return!1;return!0}(this.markers,n)||this.setMarkers(t,n)}setMarkers(t,e){let i="cm-gutterElement",n=this.dom.firstChild;for(let r=0,s=0;;){let o=s,a=r<e.length?e[r++]:null,l=!1;if(a){let t=a.elementClass;t&&(i+=" "+t);for(let t=s;t<this.markers.length;t++)if(this.markers[t].compare(a)){o=t,l=!0;break}}else o=this.markers.length;for(;s<o;){let t=this.markers[s++];if(t.toDOM){t.destroy(n);let e=n.nextSibling;n.remove(),n=e}}if(!a)break;a.toDOM&&(l?n=n.nextSibling:this.dom.insertBefore(a.toDOM(t),n)),l&&s++}this.dom.className=i,this.markers=e}destroy(){this.setMarkers(null,[])}}const Uc=_t.define(),Gc=_t.define({combine:t=>be(t,{formatNumber:String,domEventHandlers:{}},{domEventHandlers(t,e){let i=Object.assign({},t);for(let t in e){let n=i[t],r=e[t];i[t]=n?(t,e,i)=>n(t,e,i)||r(t,e,i):r}return i}})});class Ic extends Xc{constructor(t){super(),this.number=t}eq(t){return this.number==t.number}toDOM(){return document.createTextNode(this.number)}}function Nc(t,e){return t.state.facet(Gc).formatNumber(e,t.state)}const Bc=Zc.compute([Gc],(t=>({class:"cm-lineNumbers",renderEmptyElements:!1,markers:t=>t.state.facet(Uc),lineMarker:(t,e,i)=>i.some((t=>t.toDOM))?null:new Ic(Nc(t,t.state.doc.lineAt(e.from).number)),lineMarkerChange:t=>t.startState.facet(Gc)!=t.state.facet(Gc),initialSpacer:t=>new Ic(Nc(t,Hc(t.state.doc.lines))),updateSpacer(t,e){let i=Nc(e.view,Hc(e.view.state.doc.lines));return i==t.number?t:new Ic(i)},domEventHandlers:t.facet(Gc).domEventHandlers})));function Fc(t={}){return[Gc.of(t),zc(),Bc]}function Hc(t){let e=9;for(;e<t;)e=10*e+9;return e}function Jc(t,e){let i=e.mapPos(t.from,1),n=e.mapPos(t.to,-1);return i>=n?void 0:{from:i,to:n}}const Kc=oe.define({map:Jc}),tu=oe.define({map:Jc});function eu(t){let e=[];for(let{head:i}of t.state.selection.ranges)e.some((t=>t.from<=i&&t.to>=i))||e.push(t.lineBlockAt(i));return e}const iu=jt.define({create:()=>Yi.none,update(t,e){t=t.map(e.changes);for(let i of e.effects)i.is(Kc)&&!ru(t,i.value.from,i.value.to)?t=t.update({add:[uu.range(i.value.from,i.value.to)]}):i.is(tu)&&(t=t.update({filter:(t,e)=>i.value.from!=t||i.value.to!=e,filterFrom:i.value.from,filterTo:i.value.to}));if(e.selection){let i=!1,{head:n}=e.selection.main;t.between(n,n,((t,e)=>{t<n&&e>n&&(i=!0)})),i&&(t=t.update({filterFrom:n,filterTo:n,filter:(t,e)=>e<=n||t>=n}))}return t},provide:t=>Os.decorations.from(t)});function nu(t,e,i){var n;let r=null;return null===(n=t.field(iu,!1))||void 0===n||n.between(e,i,((t,e)=>{(!r||r.from>t)&&(r={from:t,to:e})})),r}function ru(t,e,i){let n=!1;return t.between(e,e,((t,r)=>{t==e&&r==i&&(n=!0)})),n}function su(t,e){return t.field(iu,!1)?e:e.concat(oe.appendConfig.of(cu()))}function ou(t,e,i=!0){let n=t.state.doc.lineAt(e.from).number,r=t.state.doc.lineAt(e.to).number;return Os.announce.of(`${t.state.phrase(i?"Folded lines":"Unfolded lines")} ${n} ${t.state.phrase("to")} ${r}.`)}const au=[{key:"Ctrl-Shift-[",mac:"Cmd-Alt-[",run:t=>{for(let e of eu(t)){let i=Lo(t.state,e.from,e.to);if(i)return t.dispatch({effects:su(t.state,[Kc.of(i),ou(t,i)])}),!0}return!1}},{key:"Ctrl-Shift-]",mac:"Cmd-Alt-]",run:t=>{if(!t.state.field(iu,!1))return!1;let e=[];for(let i of eu(t)){let n=nu(t.state,i.from,i.to);n&&e.push(tu.of(n),ou(t,n,!1))}return e.length&&t.dispatch({effects:e}),e.length>0}},{key:"Ctrl-Alt-[",run:t=>{let{state:e}=t,i=[];for(let n=0;n<e.doc.length;){let r=t.lineBlockAt(n),s=Lo(e,r.from,r.to);s&&i.push(Kc.of(s)),n=(s?t.lineBlockAt(s.to):r).to+1}return i.length&&t.dispatch({effects:su(t.state,i)}),!!i.length}},{key:"Ctrl-Alt-]",run:t=>{let e=t.state.field(iu,!1);if(!e||!e.size)return!1;let i=[];return e.between(0,t.state.doc.length,((t,e)=>{i.push(tu.of({from:t,to:e}))})),t.dispatch({effects:i}),!0}}],lu={placeholderDOM:null,placeholderText:"…"},hu=_t.define({combine:t=>be(t,lu)});function cu(t){let e=[iu,pu];return t&&e.push(hu.of(t)),e}const uu=Yi.replace({widget:new class extends Mi{toDOM(t){let{state:e}=t,i=e.facet(hu),n=e=>{let i=t.lineBlockAt(t.posAtDOM(e.target)),n=nu(t.state,i.from,i.to);n&&t.dispatch({effects:tu.of(n)}),e.preventDefault()};if(i.placeholderDOM)return i.placeholderDOM(t,n);let r=document.createElement("span");return r.textContent=i.placeholderText,r.setAttribute("aria-label",e.phrase("folded code")),r.title=e.phrase("unfold"),r.className="cm-foldPlaceholder",r.onclick=n,r}}}),Ou={openText:"⌄",closedText:"›",markerDOM:null,domEventHandlers:{}};class du extends Xc{constructor(t,e){super(),this.config=t,this.open=e}eq(t){return this.config==t.config&&this.open==t.open}toDOM(t){if(this.config.markerDOM)return this.config.markerDOM(this.open);let e=document.createElement("span");return e.textContent=this.open?this.config.openText:this.config.closedText,e.title=t.state.phrase(this.open?"Fold line":"Unfold line"),e}}function fu(t={}){let e=Object.assign(Object.assign({},Ou),t),i=new du(e,!0),n=new du(e,!1),r=Qn.fromClass(class{constructor(t){this.from=t.viewport.from,this.markers=this.buildMarkers(t)}update(t){(t.docChanged||t.viewportChanged||t.startState.facet(Oo)!=t.state.facet(Oo)||t.startState.field(iu,!1)!=t.state.field(iu,!1))&&(this.markers=this.buildMarkers(t.view))}buildMarkers(t){let e=new ke;for(let r of t.viewportLineBlocks){let s=nu(t.state,r.from,r.to)?n:Lo(t.state,r.from,r.to)?i:null;s&&e.add(r.from,r.from,s)}return e.finish()}}),{domEventHandlers:s}=e;return[r,_c({class:"cm-foldGutter",markers(t){var e;return(null===(e=t.plugin(r))||void 0===e?void 0:e.markers)||Se.empty},initialSpacer:()=>new du(e,!1),domEventHandlers:Object.assign(Object.assign({},s),{click:(t,e,i)=>{if(s.click&&s.click(t,e,i))return!0;let n=nu(t.state,e.from,e.to);if(n)return t.dispatch({effects:tu.of(n)}),!0;let r=Lo(t.state,e.from,e.to);return!!r&&(t.dispatch({effects:Kc.of(r)}),!0)}})}),cu()]}const pu=Os.baseTheme({".cm-foldPlaceholder":{backgroundColor:"#eee",border:"1px solid #ddd",color:"#888",borderRadius:".2em",margin:"0 1px",padding:"0 1px",cursor:"pointer"},".cm-foldGutter span":{padding:"0 1px",cursor:"pointer"}}),mu=ne.define(),gu=ne.define(),Qu=_t.define(),bu=_t.define({combine:t=>be(t,{minDepth:100,newGroupDelay:500},{minDepth:Math.max,newGroupDelay:Math.min})}),yu=jt.define({create:()=>Lu.empty,update(t,e){let i=e.state.facet(bu),n=e.annotation(mu);if(n){let r=e.docChanged?Xt.single(function(t){let e=0;return t.iterChangedRanges(((t,i)=>e=i)),e}(e.changes)):void 0,s=Tu.fromTransaction(e,r),o=n.side,a=0==o?t.undone:t.done;return a=s?Pu(a,a.length,i.minDepth,s):Xu(a,e.startState.selection),new Lu(0==o?n.rest:a,0==o?a:n.rest)}let r=e.annotation(gu);if("full"!=r&&"before"!=r||(t=t.isolate()),!1===e.annotation(ae.addToHistory))return e.changes.empty?t:t.addMapping(e.changes.desc);let s=Tu.fromTransaction(e),o=e.annotation(ae.time),a=e.annotation(ae.userEvent);return s?t=t.addChanges(s,o,a,i.newGroupDelay,i.minDepth):e.selection&&(t=t.addSelection(e.startState.selection,o,a,i.newGroupDelay)),"full"!=r&&"after"!=r||(t=t.isolate()),t},toJSON:t=>({done:t.done.map((t=>t.toJSON())),undone:t.undone.map((t=>t.toJSON()))}),fromJSON:t=>new Lu(t.done.map(Tu.fromJSON),t.undone.map(Tu.fromJSON))});function vu(t={}){return[yu,bu.of(t),Os.domEventHandlers({beforeinput(t,e){let i="historyUndo"==t.inputType?wu:"historyRedo"==t.inputType?Su:null;return!!i&&(t.preventDefault(),i(e))}})]}function xu(t,e){return function({state:i,dispatch:n}){if(!e&&i.readOnly)return!1;let r=i.field(yu,!1);if(!r)return!1;let s=r.pop(t,i,e);return!!s&&(n(s),!0)}}const wu=xu(0,!1),Su=xu(1,!1),ku=xu(0,!0),$u=xu(1,!0);class Tu{constructor(t,e,i,n,r){this.changes=t,this.effects=e,this.mapped=i,this.startSelection=n,this.selectionsAfter=r}setSelAfter(t){return new Tu(this.changes,this.effects,this.mapped,this.startSelection,t)}toJSON(){var t,e,i;return{changes:null===(t=this.changes)||void 0===t?void 0:t.toJSON(),mapped:null===(e=this.mapped)||void 0===e?void 0:e.toJSON(),startSelection:null===(i=this.startSelection)||void 0===i?void 0:i.toJSON(),selectionsAfter:this.selectionsAfter.map((t=>t.toJSON()))}}static fromJSON(t){return new Tu(t.changes&&wt.fromJSON(t.changes),[],t.mapped&&xt.fromJSON(t.mapped),t.startSelection&&Xt.fromJSON(t.startSelection),t.selectionsAfter.map(Xt.fromJSON))}static fromTransaction(t,e){let i=Wu;for(let e of t.startState.facet(Qu)){let n=e(t);n.length&&(i=i.concat(n))}return!i.length&&t.changes.empty?null:new Tu(t.changes.invert(t.startState.doc),i,void 0,e||t.startState.selection,Wu)}static selection(t){return new Tu(void 0,Wu,void 0,void 0,t)}}function Pu(t,e,i,n){let r=e+1>i+20?e-i-1:0,s=t.slice(r,e);return s.push(n),s}function Ru(t,e){return t.length?e.length?t.concat(e):t:e}const Wu=[];function Xu(t,e){if(t.length){let i=t[t.length-1],n=i.selectionsAfter.slice(Math.max(0,i.selectionsAfter.length-200));return n.length&&n[n.length-1].eq(e)?t:(n.push(e),Pu(t,t.length-1,1e9,i.setSelAfter(n)))}return[Tu.selection([e])]}function Cu(t){let e=t[t.length-1],i=t.slice();return i[t.length-1]=e.setSelAfter(e.selectionsAfter.slice(0,e.selectionsAfter.length-1)),i}function Au(t,e){if(!t.length)return t;let i=t.length,n=Wu;for(;i;){let r=Zu(t[i-1],e,n);if(r.changes&&!r.changes.empty||r.effects.length){let e=t.slice(0,i);return e[i-1]=r,e}e=r.mapped,i--,n=r.selectionsAfter}return n.length?[Tu.selection(n)]:Wu}function Zu(t,e,i){let n=Ru(t.selectionsAfter.length?t.selectionsAfter.map((t=>t.map(e))):Wu,i);if(!t.changes)return Tu.selection(n);let r=t.changes.map(e),s=e.mapDesc(t.changes,!0),o=t.mapped?t.mapped.composeDesc(s):s;return new Tu(r,oe.mapEffects(t.effects,e),o,t.startSelection.map(s),n)}const _u=/^(input\.type|delete)($|\.)/;class Lu{constructor(t,e,i=0,n){this.done=t,this.undone=e,this.prevTime=i,this.prevUserEvent=n}isolate(){return this.prevTime?new Lu(this.done,this.undone):this}addChanges(t,e,i,n,r){let s=this.done,o=s[s.length-1];return s=o&&o.changes&&!o.changes.empty&&t.changes&&(!i||_u.test(i))&&(!o.selectionsAfter.length&&e-this.prevTime<n&&function(t,e){let i=[],n=!1;return t.iterChangedRanges(((t,e)=>i.push(t,e))),e.iterChangedRanges(((t,e,r,s)=>{for(let t=0;t<i.length;){let e=i[t++],o=i[t++];s>=e&&r<=o&&(n=!0)}})),n}(o.changes,t.changes)||"input.type.compose"==i)?Pu(s,s.length-1,r,new Tu(t.changes.compose(o.changes),Ru(t.effects,o.effects),o.mapped,o.startSelection,Wu)):Pu(s,s.length,r,t),new Lu(s,Wu,e,i)}addSelection(t,e,i,n){let r=this.done.length?this.done[this.done.length-1].selectionsAfter:Wu;var s,o;return r.length>0&&e-this.prevTime<n&&i==this.prevUserEvent&&i&&/^select($|\.)/.test(i)&&(s=r[r.length-1],o=t,s.ranges.length==o.ranges.length&&0===s.ranges.filter(((t,e)=>t.empty!=o.ranges[e].empty)).length)?this:new Lu(Xu(this.done,t),this.undone,e,i)}addMapping(t){return new Lu(Au(this.done,t),Au(this.undone,t),this.prevTime,this.prevUserEvent)}pop(t,e,i){let n=0==t?this.done:this.undone;if(0==n.length)return null;let r=n[n.length-1];if(i&&r.selectionsAfter.length)return e.update({selection:r.selectionsAfter[r.selectionsAfter.length-1],annotations:mu.of({side:t,rest:Cu(n)}),userEvent:0==t?"select.undo":"select.redo",scrollIntoView:!0});if(r.changes){let i=1==n.length?Wu:n.slice(0,n.length-1);return r.mapped&&(i=Au(i,r.mapped)),e.update({changes:r.changes,selection:r.startSelection,effects:r.effects,annotations:mu.of({side:t,rest:i}),filter:!1,userEvent:0==t?"undo":"redo",scrollIntoView:!0})}return null}}Lu.empty=new Lu(Wu,Wu);const Du=[{key:"Mod-z",run:wu,preventDefault:!0},{key:"Mod-y",mac:"Mod-Shift-z",run:Su,preventDefault:!0},{key:"Mod-u",run:ku,preventDefault:!0},{key:"Alt-u",mac:"Mod-Shift-u",run:$u,preventDefault:!0}];class zu{constructor(t,e,i,n,r,s,o,a,l,h=0,c){this.p=t,this.stack=e,this.state=i,this.reducePos=n,this.pos=r,this.score=s,this.buffer=o,this.bufferBase=a,this.curContext=l,this.lookAhead=h,this.parent=c}toString(){return`[${this.stack.filter(((t,e)=>e%3==0)).concat(this.state)}]@${this.pos}${this.score?"!"+this.score:""}`}static start(t,e,i=0){let n=t.parser.context;return new zu(t,[],e,i,i,0,[],0,n?new Eu(n,n.start):null,0,null)}get context(){return this.curContext?this.curContext.context:null}pushState(t,e){this.stack.push(this.state,e,this.bufferBase+this.buffer.length),this.state=t}reduce(t){let e=t>>19,i=65535&t,{parser:n}=this.p,r=n.dynamicPrecedence(i);if(r&&(this.score+=r),0==e)return this.pushState(n.getGoto(this.state,i,!0),this.reducePos),i<n.minRepeatTerm&&this.storeNode(i,this.reducePos,this.reducePos,4,!0),void this.reduceContext(i,this.reducePos);let s=this.stack.length-3*(e-1)-(262144&t?6:0),o=this.stack[s-2],a=this.stack[s-1],l=this.bufferBase+this.buffer.length-a;if(i<n.minRepeatTerm||131072&t){let t=n.stateFlag(this.state,1)?this.pos:this.reducePos;this.storeNode(i,o,t,l+4,!0)}if(262144&t)this.state=this.stack[s];else{let t=this.stack[s-3];this.state=n.getGoto(t,i,!0)}for(;this.stack.length>s;)this.stack.pop();this.reduceContext(i,o)}storeNode(t,e,i,n=4,r=!1){if(0==t){let t=this,n=this.buffer.length;if(0==n&&t.parent&&(n=t.bufferBase-t.parent.bufferBase,t=t.parent),n>0&&0==t.buffer[n-4]&&t.buffer[n-1]>-1){if(e==i)return;if(t.buffer[n-2]>=e)return void(t.buffer[n-2]=i)}}if(r&&this.pos!=i){let r=this.buffer.length;if(r>0&&0!=this.buffer[r-4])for(;r>0&&this.buffer[r-2]>i;)this.buffer[r]=this.buffer[r-4],this.buffer[r+1]=this.buffer[r-3],this.buffer[r+2]=this.buffer[r-2],this.buffer[r+3]=this.buffer[r-1],r-=4,n>4&&(n-=4);this.buffer[r]=t,this.buffer[r+1]=e,this.buffer[r+2]=i,this.buffer[r+3]=n}else this.buffer.push(t,e,i,n)}shift(t,e,i){let n=this.pos;if(131072&t)this.pushState(65535&t,this.pos);else if(0==(262144&t)){let r=t,{parser:s}=this.p;(i>this.pos||e<=s.maxNode)&&(this.pos=i,s.stateFlag(r,1)||(this.reducePos=i)),this.pushState(r,n),this.shiftContext(e,n),e<=s.maxNode&&this.buffer.push(e,n,i,4)}else this.pos=i,this.shiftContext(e,n),e<=this.p.parser.maxNode&&this.buffer.push(e,n,i,4)}apply(t,e,i){65536&t?this.reduce(t):this.shift(t,e,i)}useNode(t,e){let i=this.p.reused.length-1;(i<0||this.p.reused[i]!=t)&&(this.p.reused.push(t),i++);let n=this.pos;this.reducePos=this.pos=n+t.length,this.pushState(e,n),this.buffer.push(i,n,this.reducePos,-1),this.curContext&&this.updateContext(this.curContext.tracker.reuse(this.curContext.context,t,this,this.p.stream.reset(this.pos-t.length)))}split(){let t=this,e=t.buffer.length;for(;e>0&&t.buffer[e-2]>t.reducePos;)e-=4;let i=t.buffer.slice(e),n=t.bufferBase+e;for(;t&&n==t.bufferBase;)t=t.parent;return new zu(this.p,this.stack.slice(),this.state,this.reducePos,this.pos,this.score,i,n,this.curContext,this.lookAhead,t)}recoverByDelete(t,e){let i=t<=this.p.parser.maxNode;i&&this.storeNode(t,this.pos,e,4),this.storeNode(0,this.pos,e,i?8:4),this.pos=this.reducePos=e,this.score-=190}canShift(t){for(let e=new qu(this);;){let i=this.p.parser.stateSlot(e.state,4)||this.p.parser.hasAction(e.state,t);if(0==(65536&i))return!0;if(0==i)return!1;e.reduce(i)}}recoverByInsert(t){if(this.stack.length>=300)return[];let e=this.p.parser.nextStates(this.state);if(e.length>8||this.stack.length>=120){let i=[];for(let n,r=0;r<e.length;r+=2)(n=e[r+1])!=this.state&&this.p.parser.hasAction(n,t)&&i.push(e[r],n);if(this.stack.length<120)for(let t=0;i.length<8&&t<e.length;t+=2){let n=e[t+1];i.some(((t,e)=>1&e&&t==n))||i.push(e[t],n)}e=i}let i=[];for(let t=0;t<e.length&&i.length<4;t+=2){let n=e[t+1];if(n==this.state)continue;let r=this.split();r.storeNode(0,r.pos,r.pos,4,!0),r.pushState(n,this.pos),r.shiftContext(e[t],this.pos),r.score-=200,i.push(r)}return i}forceReduce(){let t=this.p.parser.stateSlot(this.state,5);if(0==(65536&t))return!1;let{parser:e}=this.p;if(!e.validAction(this.state,t)){let i=t>>19,n=65535&t,r=this.stack.length-3*i;if(r<0||e.getGoto(this.stack[r],n,!1)<0)return!1;this.storeNode(0,this.reducePos,this.reducePos,4,!0),this.score-=100}return this.reduce(t),!0}forceAll(){for(;!this.p.parser.stateFlag(this.state,2);)if(!this.forceReduce()){this.storeNode(0,this.pos,this.pos,4,!0);break}return this}get deadEnd(){if(3!=this.stack.length)return!1;let{parser:t}=this.p;return 65535==t.data[t.stateSlot(this.state,1)]&&!t.stateSlot(this.state,4)}restart(){this.state=this.stack[0],this.stack.length=0}sameState(t){if(this.state!=t.state||this.stack.length!=t.stack.length)return!1;for(let e=0;e<this.stack.length;e+=3)if(this.stack[e]!=t.stack[e])return!1;return!0}get parser(){return this.p.parser}dialectEnabled(t){return this.p.parser.dialect.flags[t]}shiftContext(t,e){this.curContext&&this.updateContext(this.curContext.tracker.shift(this.curContext.context,t,this,this.p.stream.reset(e)))}reduceContext(t,e){this.curContext&&this.updateContext(this.curContext.tracker.reduce(this.curContext.context,t,this,this.p.stream.reset(e)))}emitContext(){let t=this.buffer.length-1;(t<0||-3!=this.buffer[t])&&this.buffer.push(this.curContext.hash,this.reducePos,this.reducePos,-3)}emitLookAhead(){let t=this.buffer.length-1;(t<0||-4!=this.buffer[t])&&this.buffer.push(this.lookAhead,this.reducePos,this.reducePos,-4)}updateContext(t){if(t!=this.curContext.context){let e=new Eu(this.curContext.tracker,t);e.hash!=this.curContext.hash&&this.emitContext(),this.curContext=e}}setLookAhead(t){t>this.lookAhead&&(this.emitLookAhead(),this.lookAhead=t)}close(){this.curContext&&this.curContext.tracker.strict&&this.emitContext(),this.lookAhead>0&&this.emitLookAhead()}}class Eu{constructor(t,e){this.tracker=t,this.context=e,this.hash=t.strict?t.hash(e):0}}var Mu,ju;(ju=Mu||(Mu={}))[ju.Insert=200]="Insert",ju[ju.Delete=190]="Delete",ju[ju.Reduce=100]="Reduce",ju[ju.MaxNext=4]="MaxNext",ju[ju.MaxInsertStackDepth=300]="MaxInsertStackDepth",ju[ju.DampenInsertStackDepth=120]="DampenInsertStackDepth";class qu{constructor(t){this.start=t,this.state=t.state,this.stack=t.stack,this.base=this.stack.length}reduce(t){let e=65535&t,i=t>>19;0==i?(this.stack==this.start.stack&&(this.stack=this.stack.slice()),this.stack.push(this.state,0,0),this.base+=3):this.base-=3*(i-1);let n=this.start.p.parser.getGoto(this.stack[this.base-3],e,!0);this.state=n}}class Yu{constructor(t,e,i){this.stack=t,this.pos=e,this.index=i,this.buffer=t.buffer,0==this.index&&this.maybeNext()}static create(t,e=t.bufferBase+t.buffer.length){return new Yu(t,e,e-t.bufferBase)}maybeNext(){let t=this.stack.parent;null!=t&&(this.index=this.stack.bufferBase-t.bufferBase,this.stack=t,this.buffer=t.buffer)}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}next(){this.index-=4,this.pos-=4,0==this.index&&this.maybeNext()}fork(){return new Yu(this.stack,this.pos,this.index)}}class Vu{constructor(){this.start=-1,this.value=-1,this.end=-1,this.extended=-1,this.lookAhead=0,this.mask=0,this.context=0}}const Uu=new Vu;class Gu{constructor(t,e){this.input=t,this.ranges=e,this.chunk="",this.chunkOff=0,this.chunk2="",this.chunk2Pos=0,this.next=-1,this.token=Uu,this.rangeIndex=0,this.pos=this.chunkPos=e[0].from,this.range=e[0],this.end=e[e.length-1].to,this.readNext()}resolveOffset(t,e){let i=this.range,n=this.rangeIndex,r=this.pos+t;for(;r<i.from;){if(!n)return null;let t=this.ranges[--n];r-=i.from-t.to,i=t}for(;e<0?r>i.to:r>=i.to;){if(n==this.ranges.length-1)return null;let t=this.ranges[++n];r+=t.from-i.to,i=t}return r}peek(t){let e,i,n=this.chunkOff+t;if(n>=0&&n<this.chunk.length)e=this.pos+t,i=this.chunk.charCodeAt(n);else{let n=this.resolveOffset(t,1);if(null==n)return-1;if(e=n,e>=this.chunk2Pos&&e<this.chunk2Pos+this.chunk2.length)i=this.chunk2.charCodeAt(e-this.chunk2Pos);else{let t=this.rangeIndex,n=this.range;for(;n.to<=e;)n=this.ranges[++t];this.chunk2=this.input.chunk(this.chunk2Pos=e),e+this.chunk2.length>n.to&&(this.chunk2=this.chunk2.slice(0,n.to-e)),i=this.chunk2.charCodeAt(0)}}return e>=this.token.lookAhead&&(this.token.lookAhead=e+1),i}acceptToken(t,e=0){let i=e?this.resolveOffset(e,-1):this.pos;if(null==i||i<this.token.start)throw new RangeError("Token end out of bounds");this.token.value=t,this.token.end=i}getChunk(){if(this.pos>=this.chunk2Pos&&this.pos<this.chunk2Pos+this.chunk2.length){let{chunk:t,chunkPos:e}=this;this.chunk=this.chunk2,this.chunkPos=this.chunk2Pos,this.chunk2=t,this.chunk2Pos=e,this.chunkOff=this.pos-this.chunkPos}else{this.chunk2=this.chunk,this.chunk2Pos=this.chunkPos;let t=this.input.chunk(this.pos),e=this.pos+t.length;this.chunk=e>this.range.to?t.slice(0,this.range.to-this.pos):t,this.chunkPos=this.pos,this.chunkOff=0}}readNext(){return this.chunkOff>=this.chunk.length&&(this.getChunk(),this.chunkOff==this.chunk.length)?this.next=-1:this.next=this.chunk.charCodeAt(this.chunkOff)}advance(t=1){for(this.chunkOff+=t;this.pos+t>=this.range.to;){if(this.rangeIndex==this.ranges.length-1)return this.setDone();t-=this.range.to-this.pos,this.range=this.ranges[++this.rangeIndex],this.pos=this.range.from}return this.pos+=t,this.pos>=this.token.lookAhead&&(this.token.lookAhead=this.pos+1),this.readNext()}setDone(){return this.pos=this.chunkPos=this.end,this.range=this.ranges[this.rangeIndex=this.ranges.length-1],this.chunk="",this.next=-1}reset(t,e){if(e?(this.token=e,e.start=t,e.lookAhead=t+1,e.value=e.extended=-1):this.token=Uu,this.pos!=t){if(this.pos=t,t==this.end)return this.setDone(),this;for(;t<this.range.from;)this.range=this.ranges[--this.rangeIndex];for(;t>=this.range.to;)this.range=this.ranges[++this.rangeIndex];t>=this.chunkPos&&t<this.chunkPos+this.chunk.length?this.chunkOff=t-this.chunkPos:(this.chunk="",this.chunkOff=0),this.readNext()}return this}read(t,e){if(t>=this.chunkPos&&e<=this.chunkPos+this.chunk.length)return this.chunk.slice(t-this.chunkPos,e-this.chunkPos);if(t>=this.chunk2Pos&&e<=this.chunk2Pos+this.chunk2.length)return this.chunk2.slice(t-this.chunk2Pos,e-this.chunk2Pos);if(t>=this.range.from&&e<=this.range.to)return this.input.read(t,e);let i="";for(let n of this.ranges){if(n.from>=e)break;n.to>t&&(i+=this.input.read(Math.max(n.from,t),Math.min(n.to,e)))}return i}}class Iu{constructor(t,e){this.data=t,this.id=e}token(t,e){!function(t,e,i,n){let r=0,s=1<<n,{parser:o}=i.p,{dialect:a}=o;t:for(;0!=(s&t[r]);){let i=t[r+1];for(let n=r+3;n<i;n+=2)if((t[n+1]&s)>0){let i=t[n];if(a.allows(i)&&(-1==e.token.value||e.token.value==i||o.overrides(i,e.token.value))){e.acceptToken(i);break}}for(let n=e.next,s=0,o=t[r+2];s<o;){let a=s+o>>1,l=i+a+(a<<1),h=t[l],c=t[l+1];if(n<h)o=a;else{if(!(n>=c)){r=t[l+2],e.advance();continue t}s=a+1}}break}}(this.data,t,e,this.id)}}Iu.prototype.contextual=Iu.prototype.fallback=Iu.prototype.extend=!1;class Nu{constructor(t,e={}){this.token=t,this.contextual=!!e.contextual,this.fallback=!!e.fallback,this.extend=!!e.extend}}function Bu(t,e=Uint16Array){if("string"!=typeof t)return t;let i=null;for(let n=0,r=0;n<t.length;){let s=0;for(;;){let e=t.charCodeAt(n++),i=!1;if(126==e){s=65535;break}e>=92&&e--,e>=34&&e--;let r=e-32;if(r>=46&&(r-=46,i=!0),s+=r,i)break;s*=46}i?i[r++]=s:i=new e(s)}return i}const Fu=void 0!==n&&/\bparse\b/.test(void 0);let Hu=null;var Ju,Ku,tO,eO;function iO(t,e,i){let n=t.fullCursor();for(n.moveTo(e);;)if(!(i<0?n.childBefore(e):n.childAfter(e)))for(;;){if((i<0?n.to<e:n.from>e)&&!n.type.isError)return i<0?Math.max(0,Math.min(n.to-1,e-25)):Math.min(t.length,Math.max(n.from+1,e+25));if(i<0?n.prevSibling():n.nextSibling())break;if(!n.parent())return i<0?0:t.length}}(tO=Ju||(Ju={}))[tO.Margin=25]="Margin";class nO{constructor(t,e){this.fragments=t,this.nodeSet=e,this.i=0,this.fragment=null,this.safeFrom=-1,this.safeTo=-1,this.trees=[],this.start=[],this.index=[],this.nextFragment()}nextFragment(){let t=this.fragment=this.i==this.fragments.length?null:this.fragments[this.i++];if(t){for(this.safeFrom=t.openStart?iO(t.tree,t.from+t.offset,1)-t.offset:t.from,this.safeTo=t.openEnd?iO(t.tree,t.to+t.offset,-1)-t.offset:t.to;this.trees.length;)this.trees.pop(),this.start.pop(),this.index.pop();this.trees.push(t.tree),this.start.push(-t.offset),this.index.push(0),this.nextStart=this.safeFrom}else this.nextStart=1e9}nodeAt(t){if(t<this.nextStart)return null;for(;this.fragment&&this.safeTo<=t;)this.nextFragment();if(!this.fragment)return null;for(;;){let e=this.trees.length-1;if(e<0)return this.nextFragment(),null;let i=this.trees[e],n=this.index[e];if(n==i.children.length){this.trees.pop(),this.start.pop(),this.index.pop();continue}let r=i.children[n],s=this.start[e]+i.positions[n];if(s>t)return this.nextStart=s,null;if(r instanceof p){if(s==t){if(s<this.safeFrom)return null;let t=s+r.length;if(t<=this.safeTo){let e=r.prop(l.lookAhead);if(!e||t+e<this.fragment.to)return r}}this.index[e]++,s+r.length>=Math.max(this.safeFrom,t)&&(this.trees.push(r),this.start.push(s),this.index.push(0))}else this.index[e]++,this.nextStart=s+r.length}}}class rO{constructor(t,e){this.stream=e,this.tokens=[],this.mainToken=null,this.actions=[],this.tokens=t.tokenizers.map((t=>new Vu))}getActions(t){let e=0,i=null,{parser:n}=t.p,{tokenizers:r}=n,s=n.stateSlot(t.state,3),o=t.curContext?t.curContext.hash:0,a=0;for(let n=0;n<r.length;n++){if(0==(1<<n&s))continue;let l=r[n],h=this.tokens[n];if((!i||l.fallback)&&((l.contextual||h.start!=t.pos||h.mask!=s||h.context!=o)&&(this.updateCachedToken(h,l,t),h.mask=s,h.context=o),h.lookAhead>h.end+25&&(a=Math.max(h.lookAhead,a)),0!=h.value)){let n=e;if(h.extended>-1&&(e=this.addActions(t,h.extended,h.end,e)),e=this.addActions(t,h.value,h.end,e),!l.extend&&(i=h,e>n))break}}for(;this.actions.length>e;)this.actions.pop();return a&&t.setLookAhead(a),i||t.pos!=this.stream.end||(i=new Vu,i.value=t.p.parser.eofTerm,i.start=i.end=t.pos,e=this.addActions(t,i.value,i.end,e)),this.mainToken=i,this.actions}getMainToken(t){if(this.mainToken)return this.mainToken;let e=new Vu,{pos:i,p:n}=t;return e.start=i,e.end=Math.min(i+1,n.stream.end),e.value=i==n.stream.end?n.parser.eofTerm:0,e}updateCachedToken(t,e,i){if(e.token(this.stream.reset(i.pos,t),i),t.value>-1){let{parser:e}=i.p;for(let n=0;n<e.specialized.length;n++)if(e.specialized[n]==t.value){let r=e.specializers[n](this.stream.read(t.start,t.end),i);if(r>=0&&i.p.parser.dialect.allows(r>>1)){0==(1&r)?t.value=r>>1:t.extended=r>>1;break}}}else t.value=0,t.end=Math.min(i.p.stream.end,i.pos+1)}putAction(t,e,i,n){for(let e=0;e<n;e+=3)if(this.actions[e]==t)return n;return this.actions[n++]=t,this.actions[n++]=e,this.actions[n++]=i,n}addActions(t,e,i,n){let{state:r}=t,{parser:s}=t.p,{data:o}=s;for(let t=0;t<2;t++)for(let a=s.stateSlot(r,t?2:1);;a+=3){if(65535==o[a]){if(1!=o[a+1]){0==n&&2==o[a+1]&&(n=this.putAction(uO(o,a+2),e,i,n));break}a=uO(o,a+2)}o[a]==e&&(n=this.putAction(uO(o,a+1),e,i,n))}return n}}(eO=Ku||(Ku={}))[eO.Distance=5]="Distance",eO[eO.MaxRemainingPerStep=3]="MaxRemainingPerStep",eO[eO.MinBufferLengthPrune=500]="MinBufferLengthPrune",eO[eO.ForceReduceLimit=10]="ForceReduceLimit",eO[eO.CutDepth=15e3]="CutDepth",eO[eO.CutTo=9e3]="CutTo";class sO{constructor(t,e,i,n){this.parser=t,this.input=e,this.ranges=n,this.recovering=0,this.nextStackID=9812,this.minStackPos=0,this.reused=[],this.stoppedAt=null,this.stream=new Gu(e,n),this.tokens=new rO(t,this.stream),this.topTerm=t.top[1];let{from:r}=n[0];this.stacks=[zu.start(this,t.top[0],r)],this.fragments=i.length&&this.stream.end-r>4*t.bufferLength?new nO(i,t.nodeSet):null}get parsedPos(){return this.minStackPos}advance(){let t,e,i=this.stacks,n=this.minStackPos,r=this.stacks=[];for(let s=0;s<i.length;s++){let o=i[s];for(;;){if(this.tokens.mainToken=null,o.pos>n)r.push(o);else{if(this.advanceStack(o,r,i))continue;{t||(t=[],e=[]),t.push(o);let i=this.tokens.getMainToken(o);e.push(i.value,i.end)}}break}}if(!r.length){let e=t&&function(t){let e=null;for(let i of t){let t=i.p.stoppedAt;(i.pos==i.p.stream.end||null!=t&&i.pos>t)&&i.p.parser.stateFlag(i.state,2)&&(!e||e.score<i.score)&&(e=i)}return e}(t);if(e)return this.stackToTree(e);if(this.parser.strict)throw Fu&&t&&console.log("Stuck with token "+(this.tokens.mainToken?this.parser.getName(this.tokens.mainToken.value):"none")),new SyntaxError("No parse at "+n);this.recovering||(this.recovering=5)}if(this.recovering&&t){let i=null!=this.stoppedAt&&t[0].pos>this.stoppedAt?t[0]:this.runRecovery(t,e,r);if(i)return this.stackToTree(i.forceAll())}if(this.recovering){let t=1==this.recovering?1:3*this.recovering;if(r.length>t)for(r.sort(((t,e)=>e.score-t.score));r.length>t;)r.pop();r.some((t=>t.reducePos>n))&&this.recovering--}else if(r.length>1)t:for(let t=0;t<r.length-1;t++){let e=r[t];for(let i=t+1;i<r.length;i++){let n=r[i];if(e.sameState(n)||e.buffer.length>500&&n.buffer.length>500){if(!((e.score-n.score||e.buffer.length-n.buffer.length)>0)){r.splice(t--,1);continue t}r.splice(i--,1)}}}this.minStackPos=r[0].pos;for(let t=1;t<r.length;t++)r[t].pos<this.minStackPos&&(this.minStackPos=r[t].pos);return null}stopAt(t){if(null!=this.stoppedAt&&this.stoppedAt<t)throw new RangeError("Can't move stoppedAt forward");this.stoppedAt=t}advanceStack(t,e,i){let n=t.pos,{parser:r}=this,s=Fu?this.stackID(t)+" -> ":"";if(null!=this.stoppedAt&&n>this.stoppedAt)return t.forceReduce()?t:null;if(this.fragments){let e=t.curContext&&t.curContext.tracker.strict,i=e?t.curContext.hash:0;for(let o=this.fragments.nodeAt(n);o;){let n=this.parser.nodeSet.types[o.type.id]==o.type?r.getGoto(t.state,o.type.id):-1;if(n>-1&&o.length&&(!e||(o.prop(l.contextHash)||0)==i))return t.useNode(o,n),Fu&&console.log(s+this.stackID(t)+` (via reuse of ${r.getName(o.type.id)})`),!0;if(!(o instanceof p)||0==o.children.length||o.positions[0]>0)break;let a=o.children[0];if(!(a instanceof p&&0==o.positions[0]))break;o=a}}let o=r.stateSlot(t.state,4);if(o>0)return t.reduce(o),Fu&&console.log(s+this.stackID(t)+` (via always-reduce ${r.getName(65535&o)})`),!0;if(t.stack.length>=15e3)for(;t.stack.length>9e3&&t.forceReduce(););let a=this.tokens.getActions(t);for(let o=0;o<a.length;){let l=a[o++],h=a[o++],c=a[o++],u=o==a.length||!i,O=u?t:t.split();if(O.apply(l,h,c),Fu&&console.log(s+this.stackID(O)+` (via ${0==(65536&l)?"shift":`reduce of ${r.getName(65535&l)}`} for ${r.getName(h)} @ ${n}${O==t?"":", split"})`),u)return!0;O.pos>n?e.push(O):i.push(O)}return!1}advanceFully(t,e){let i=t.pos;for(;;){if(!this.advanceStack(t,null,null))return!1;if(t.pos>i)return oO(t,e),!0}}runRecovery(t,e,i){let n=null,r=!1;for(let s=0;s<t.length;s++){let o=t[s],a=e[s<<1],l=e[1+(s<<1)],h=Fu?this.stackID(o)+" -> ":"";if(o.deadEnd){if(r)continue;if(r=!0,o.restart(),Fu&&console.log(h+this.stackID(o)+" (restarted)"),this.advanceFully(o,i))continue}let c=o.split(),u=h;for(let t=0;c.forceReduce()&&t<10&&(Fu&&console.log(u+this.stackID(c)+" (via force-reduce)"),!this.advanceFully(c,i));t++)Fu&&(u=this.stackID(c)+" -> ");for(let t of o.recoverByInsert(a))Fu&&console.log(h+this.stackID(t)+" (via recover-insert)"),this.advanceFully(t,i);this.stream.end>o.pos?(l==o.pos&&(l++,a=0),o.recoverByDelete(a,l),Fu&&console.log(h+this.stackID(o)+` (via recover-delete ${this.parser.getName(a)})`),oO(o,i)):(!n||n.score<o.score)&&(n=o)}return n}stackToTree(t){return t.close(),p.build({buffer:Yu.create(t),nodeSet:this.parser.nodeSet,topID:this.topTerm,maxBufferLength:this.parser.bufferLength,reused:this.reused,start:this.ranges[0].from,length:t.pos-this.ranges[0].from,minRepeatType:this.parser.minRepeatTerm})}stackID(t){let e=(Hu||(Hu=new WeakMap)).get(t);return e||Hu.set(t,e=String.fromCodePoint(this.nextStackID++)),e+t}}function oO(t,e){for(let i=0;i<e.length;i++){let n=e[i];if(n.pos==t.pos&&n.sameState(t))return void(e[i].score<t.score&&(e[i]=t))}e.push(t)}class aO{constructor(t,e,i){this.source=t,this.flags=e,this.disabled=i}allows(t){return!this.disabled||0==this.disabled[t]}}const lO=t=>t;class hO{constructor(t){this.start=t.start,this.shift=t.shift||lO,this.reduce=t.reduce||lO,this.reuse=t.reuse||lO,this.hash=t.hash||(()=>0),this.strict=!1!==t.strict}}class cO extends X{constructor(t){if(super(),this.wrappers=[],13!=t.version)throw new RangeError(`Parser version (${t.version}) doesn't match runtime version (13)`);let e=t.nodeNames.split(" ");this.minRepeatTerm=e.length;for(let i=0;i<t.repeatNodeCount;i++)e.push("");let i=Object.keys(t.topRules).map((e=>t.topRules[e][1])),n=[];for(let t=0;t<e.length;t++)n.push([]);function r(t,e,i){n[t].push([e,e.deserialize(String(i))])}if(t.nodeProps)for(let e of t.nodeProps){let t=e[0];for(let i=1;i<e.length;){let n=e[i++];if(n>=0)r(n,t,e[i++]);else{let s=e[i+-n];for(let o=-n;o>0;o--)r(e[i++],t,s);i++}}}this.nodeSet=new O(e.map(((e,r)=>u.define({name:r>=this.minRepeatTerm?void 0:e,id:r,props:n[r],top:i.indexOf(r)>-1,error:0==r,skipped:t.skippedNodes&&t.skippedNodes.indexOf(r)>-1})))),this.strict=!1,this.bufferLength=s;let o=Bu(t.tokenData);if(this.context=t.context,this.specialized=new Uint16Array(t.specialized?t.specialized.length:0),this.specializers=[],t.specialized)for(let e=0;e<t.specialized.length;e++)this.specialized[e]=t.specialized[e].term,this.specializers[e]=t.specialized[e].get;this.states=Bu(t.states,Uint32Array),this.data=Bu(t.stateData),this.goto=Bu(t.goto),this.maxTerm=t.maxTerm,this.tokenizers=t.tokenizers.map((t=>"number"==typeof t?new Iu(o,t):t)),this.topRules=t.topRules,this.dialects=t.dialects||{},this.dynamicPrecedences=t.dynamicPrecedences||null,this.tokenPrecTable=t.tokenPrec,this.termNames=t.termNames||null,this.maxNode=this.nodeSet.types.length-1,this.dialect=this.parseDialect(),this.top=this.topRules[Object.keys(this.topRules)[0]]}createParse(t,e,i){let n=new sO(this,t,e,i);for(let r of this.wrappers)n=r(n,t,e,i);return n}getGoto(t,e,i=!1){let n=this.goto;if(e>=n[0])return-1;for(let r=n[e+1];;){let e=n[r++],s=1&e,o=n[r++];if(s&&i)return o;for(let i=r+(e>>1);r<i;r++)if(n[r]==t)return o;if(s)return-1}}hasAction(t,e){let i=this.data;for(let n=0;n<2;n++)for(let r,s=this.stateSlot(t,n?2:1);;s+=3){if(65535==(r=i[s])){if(1!=i[s+1]){if(2==i[s+1])return uO(i,s+2);break}r=i[s=uO(i,s+2)]}if(r==e||0==r)return uO(i,s+1)}return 0}stateSlot(t,e){return this.states[6*t+e]}stateFlag(t,e){return(this.stateSlot(t,0)&e)>0}validAction(t,e){if(e==this.stateSlot(t,4))return!0;for(let i=this.stateSlot(t,1);;i+=3){if(65535==this.data[i]){if(1!=this.data[i+1])return!1;i=uO(this.data,i+2)}if(e==uO(this.data,i+1))return!0}}nextStates(t){let e=[];for(let i=this.stateSlot(t,1);;i+=3){if(65535==this.data[i]){if(1!=this.data[i+1])break;i=uO(this.data,i+2)}if(0==(1&this.data[i+2])){let t=this.data[i+1];e.some(((e,i)=>1&i&&e==t))||e.push(this.data[i],t)}}return e}overrides(t,e){let i=OO(this.data,this.tokenPrecTable,e);return i<0||OO(this.data,this.tokenPrecTable,t)<i}configure(t){let e=Object.assign(Object.create(cO.prototype),this);if(t.props&&(e.nodeSet=this.nodeSet.extend(...t.props)),t.top){let i=this.topRules[t.top];if(!i)throw new RangeError(`Invalid top rule name ${t.top}`);e.top=i}return t.tokenizers&&(e.tokenizers=this.tokenizers.map((e=>{let i=t.tokenizers.find((t=>t.from==e));return i?i.to:e}))),t.contextTracker&&(e.context=t.contextTracker),t.dialect&&(e.dialect=this.parseDialect(t.dialect)),null!=t.strict&&(e.strict=t.strict),t.wrap&&(e.wrappers=e.wrappers.concat(t.wrap)),null!=t.bufferLength&&(e.bufferLength=t.bufferLength),e}getName(t){return this.termNames?this.termNames[t]:String(t<=this.maxNode&&this.nodeSet.types[t].name||t)}get eofTerm(){return this.maxNode+1}get topNode(){return this.nodeSet.types[this.top[1]]}dynamicPrecedence(t){let e=this.dynamicPrecedences;return null==e?0:e[t]||0}parseDialect(t){let e=Object.keys(this.dialects),i=e.map((()=>!1));if(t)for(let n of t.split(" ")){let t=e.indexOf(n);t>=0&&(i[t]=!0)}let n=null;for(let t=0;t<e.length;t++)if(!i[t])for(let i,r=this.dialects[e[t]];65535!=(i=this.data[r++]);)(n||(n=new Uint8Array(this.maxTerm+1)))[i]=1;return new aO(t,i,n)}static deserialize(t){return new cO(t)}}function uO(t,e){return t[e]|t[e+1]<<16}function OO(t,e,i){for(let n,r=e;65535!=(n=t[r]);r++)if(n==i)return r-e;return-1}const dO=[9,10,11,12,13,32,133,160,5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8232,8233,8239,8287,12288];function fO(t){return t>=65&&t<=90||t>=97&&t<=122||t>=161}const pO=new Nu(((t,e)=>{for(let n=!1,r=0,s=0;;s++){let{next:o}=t;if(!(fO(o)||45==o||95==o||n&&(i=o,i>=48&&i<=57))){n&&t.acceptToken(40==o?94:2==r&&e.canShift(2)?2:95);break}!n&&(45!=o||s>0)&&(n=!0),r===s&&45==o&&r++,t.advance()}var i})),mO=new Nu((t=>{if(dO.includes(t.peek(-1))){let{next:e}=t;(fO(e)||95==e||35==e||46==e||91==e||58==e||45==e)&&t.acceptToken(93)}})),gO=new Nu((t=>{if(!dO.includes(t.peek(-1))){let{next:e}=t;if(37==e&&(t.advance(),t.acceptToken(1)),fO(e)){do{t.advance()}while(fO(t.next));t.acceptToken(1)}}})),QO={__proto__:null,lang:32,"nth-child":32,"nth-last-child":32,"nth-of-type":32,dir:32,url:60,"url-prefix":60,domain:60,regexp:60,selector:134},bO={__proto__:null,"@import":114,"@media":138,"@charset":142,"@namespace":146,"@keyframes":152,"@supports":164},yO={__proto__:null,not:128,only:128,from:158,to:160},vO=cO.deserialize({version:13,states:"7WOYQ[OOOOQP'#Cd'#CdOOQP'#Cc'#CcO!ZQ[O'#CfO!}QXO'#CaO#UQ[O'#ChO#aQ[O'#DPO#fQ[O'#DTOOQP'#Ec'#EcO#kQdO'#DeO$VQ[O'#DrO#kQdO'#DtO$hQ[O'#DvO$sQ[O'#DyO$xQ[O'#EPO%WQ[O'#EROOQS'#Eb'#EbOOQS'#ES'#ESQYQ[OOOOQP'#Cg'#CgOOQP,59Q,59QO!ZQ[O,59QO%_Q[O'#EVO%yQWO,58{O&RQ[O,59SO#aQ[O,59kO#fQ[O,59oO%_Q[O,59sO%_Q[O,59uO%_Q[O,59vO'bQ[O'#D`OOQS,58{,58{OOQP'#Ck'#CkOOQO'#C}'#C}OOQP,59S,59SO'iQWO,59SO'nQWO,59SOOQP'#DR'#DROOQP,59k,59kOOQO'#DV'#DVO'sQ`O,59oOOQS'#Cp'#CpO#kQdO'#CqO'{QvO'#CsO)VQtO,5:POOQO'#Cx'#CxO'iQWO'#CwO)kQWO'#CyOOQS'#Ef'#EfOOQO'#Dh'#DhO)pQ[O'#DoO*OQWO'#EiO$xQ[O'#DmO*^QWO'#DpOOQO'#Ej'#EjO%|QWO,5:^O*cQpO,5:`OOQS'#Dx'#DxO*kQWO,5:bO*pQ[O,5:bOOQO'#D{'#D{O*xQWO,5:eO*}QWO,5:kO+VQWO,5:mOOQS-E8Q-E8QOOQP1G.l1G.lO+yQXO,5:qOOQO-E8T-E8TOOQS1G.g1G.gOOQP1G.n1G.nO'iQWO1G.nO'nQWO1G.nOOQP1G/V1G/VO,WQ`O1G/ZO,qQXO1G/_O-XQXO1G/aO-oQXO1G/bO.VQXO'#CdO.zQWO'#DaOOQS,59z,59zO/PQWO,59zO/XQ[O,59zO/`QdO'#CoO/gQ[O'#DOOOQP1G/Z1G/ZO#kQdO1G/ZO/nQpO,59]OOQS,59_,59_O#kQdO,59aO/vQWO1G/kOOQS,59c,59cO/{Q!bO,59eO0TQWO'#DhO0`QWO,5:TO0eQWO,5:ZO$xQ[O,5:VO$xQ[O'#EYO0mQWO,5;TO0xQWO,5:XO%_Q[O,5:[OOQS1G/x1G/xOOQS1G/z1G/zOOQS1G/|1G/|O1ZQWO1G/|O1`QdO'#D|OOQS1G0P1G0POOQS1G0V1G0VOOQS1G0X1G0XOOQP7+$Y7+$YOOQP7+$u7+$uO#kQdO7+$uO#kQdO,59{O1nQ[O'#EXO1xQWO1G/fOOQS1G/f1G/fO1xQWO1G/fO2QQtO'#ETO2uQdO'#EeO3PQWO,59ZO3UQXO'#EhO3]QWO,59jO3bQpO7+$uOOQS1G.w1G.wOOQS1G.{1G.{OOQS7+%V7+%VO3jQWO1G/PO#kQdO1G/oOOQO1G/u1G/uOOQO1G/q1G/qO3oQWO,5:tOOQO-E8W-E8WO3}QXO1G/vOOQS7+%h7+%hO4UQYO'#CsO%|QWO'#EZO4^QdO,5:hOOQS,5:h,5:hO4lQpO<<HaO4tQtO1G/gOOQO,5:s,5:sO5XQ[O,5:sOOQO-E8V-E8VOOQS7+%Q7+%QO5cQWO7+%QOOQS-E8R-E8RO#kQdO'#EUO5kQWO,5;POOQT1G.u1G.uO5sQWO,5;SOOQP1G/U1G/UOOQP<<Ha<<HaOOQS7+$k7+$kO5{QdO7+%ZOOQO7+%b7+%bOOQS,5:u,5:uOOQS-E8X-E8XOOQS1G0S1G0SOOQPAN={AN={O6SQtO'#EWO#kQdO'#EWO6}QdO7+%ROOQO7+%R7+%ROOQO1G0_1G0_OOQS<<Hl<<HlO7_QdO,5:pOOQO-E8S-E8SOOQO<<Hu<<HuO7iQtO,5:rOOQS-E8U-E8UOOQO<<Hm<<Hm",stateData:"8j~O#TOSROS~OUWOXWO]TO^TOtUOxVO!Y_O!ZXO!gYO!iZO!k[O!n]O!t^O#RPO#WRO~O#RcO~O]hO^hOpfOtiOxjO|kO!PmO#PlO#WeO~O!RnO~P!`O`sO#QqO#RpO~O#RuO~O#RwO~OQ!QObzOf!QOh!QOn!PO#Q}O#RyO#Z{O~Ob!SO!b!UO!e!VO#R!RO!R#]P~Oh![On!PO#R!ZO~O#R!^O~Ob!SO!b!UO!e!VO#R!RO~O!W#]P~P$VOUWOXWO]TO^TOtUOxVO#RPO#WRO~OpfO!RnO~O`!hO#QqO#RpO~OQ!pOUWOXWO]TO^TOtUOxVO!Y_O!ZXO!gYO!iZO!k[O!n]O!t^O#R!oO#WRO~O!Q!qO~P&^Ob!tO~Ob!uO~Ov!vOz!wO~OP!yObgXjgX!WgX!bgX!egX#RgXagXQgXfgXhgXngXpgX#QgX#ZgXvgX!QgX!VgX~Ob!SOj!zO!b!UO!e!VO#R!RO!W#]P~Ob!}O~Ob!SO!b!UO!e!VO#R#OO~Op#SO!`#RO!R#]X!W#]X~Ob#VO~Oj!zO!W#XO~O!W#YO~Oh#ZOn!PO~O!R#[O~O!RnO!`#RO~O!RnO!W#_O~O]hO^hOtiOxjO|kO!PmO#PlO#WeO~Op!ya!R!yaa!ya~P+_Ov#aOz#bO~O]hO^hOtiOxjO#WeO~Op{i|{i!P{i!R{i#P{ia{i~P,`Op}i|}i!P}i!R}i#P}ia}i~P,`Op!Oi|!Oi!P!Oi!R!Oi#P!Oia!Oi~P,`O]WX]!UX^WXpWXtWXxWX|WX!PWX!RWX#PWX#WWX~O]#cO~O!Q#fO!W#dO~O!Q#fO~P&^Oa#XP~P#kOa#[P~P%_Oa#nOj!zO~O!W#pO~Oh#qOo#qO~O]!^Xa![X!`![X~O]#rO~Oa#sO!`#RO~Op#SO!R#]a!W#]a~O!`#ROp!aa!R!aa!W!aaa!aa~O!W#xO~O!Q#|O!q#zO!r#zO#Z#yO~O!Q!{X!W!{X~P&^O!Q$SO!W#dO~Oj!zOQ!wXa!wXb!wXf!wXh!wXn!wXp!wX#Q!wX#R!wX#Z!wX~Op$VOa#XX~P#kOa$XO~Oa#[X~P!`Oa$ZO~Oj!zOv$[O~Oa$]O~O!`#ROp!|a!R!|a!W!|a~Oa$_O~P+_OP!yO!RgX~O!Q$bO!q#zO!r#zO#Z#yO~Oj!zOv$cO~Oj!zOp$eO!V$gO!Q!Ti!W!Ti~P#kO!Q!{a!W!{a~P&^O!Q$iO!W#dO~Op$VOa#Xa~OpfOa#[a~Oa$lO~P#kOj!zOQ!zXb!zXf!zXh!zXn!zXp!zX!Q!zX!V!zX!W!zX#Q!zX#R!zX#Z!zX~Op$eO!V$oO!Q!Tq!W!Tq~P#kOa!xap!xa~P#kOj!zOQ!zab!zaf!zah!zan!zap!za!Q!za!V!za!W!za#Q!za#R!za#Z!za~Oo#Zj!Pj~",goto:",O#_PPPPP#`P#h#vP#h$U#hPP$[PPP$b$k$kP$}P$kP$k%e%wPPP&a&g#hP&mP#hP&sP#hP#h#hPPP&y']'iPP#`PP'o'o'y'oP'oP'o'oP#`P#`P#`P'|#`P(P(SPP#`P#`(V(e(s(y)T)Z)e)kPPPPPP)q)yP*e*hP+^+a+j]`Obn!s#d$QiWObfklmn!s!u#V#d$QiQObfklmn!s!u#V#d$QQdRR!ceQrTR!ghQ!gsQ!|!OR#`!hq!QXZz!t!w!z#b#c#i#r$O$V$^$e$f$jp!QXZz!t!w!z#b#c#i#r$O$V$^$e$f$jT#z#[#{q!OXZz!t!w!z#b#c#i#r$O$V$^$e$f$jp!QXZz!t!w!z#b#c#i#r$O$V$^$e$f$jQ![[R#Z!]QtTR!ihQ!gtR#`!iQvUR!jiQxVR!kjQoSQ!fgQ#W!XQ#^!`Q#_!aR$`#zQ!rnQ#g!sQ$P#dR$h$QX!pn!s#d$Qa!WY^_|!S!U#R#SR#P!SR!][R!_]R#]!_QbOU!bb!s$QQ!snR$Q#dQ#i!tU$U#i$^$jQ$^#rR$j$VQ$W#iR$k$WQgSS!eg$YR$Y#kQ$f$OR$n$fQ#e!rS$R#e$TR$T#gQ#T!TR#v#TQ#{#[R$a#{]aObn!s#d$Q[SObn!s#d$QQ!dfQ!lkQ!mlQ!nmQ#k!uR#w#VR#j!tQ|XQ!YZQ!xz[#h!t#i#r$V$^$jQ#m!wQ#o!zQ#}#bQ$O#cS$d$O$fR$m$eR#l!uQ!XYQ!a_R!{|U!TY_|Q!`^Q#Q!SQ#U!UQ#t#RR#u#S",nodeNames:"⚠ Unit VariableName Comment StyleSheet RuleSet UniversalSelector TagSelector TagName NestingSelector ClassSelector ClassName PseudoClassSelector : :: PseudoClassName PseudoClassName ) ( ArgList ValueName ParenthesizedValue ColorLiteral NumberLiteral StringLiteral BinaryExpression BinOp CallExpression Callee CallLiteral CallTag ParenthesizedContent , PseudoClassName ArgList IdSelector # IdName ] AttributeSelector [ AttributeName MatchOp ChildSelector ChildOp DescendantSelector SiblingSelector SiblingOp } { Block Declaration PropertyName Important ; ImportStatement AtKeyword import KeywordQuery FeatureQuery FeatureName BinaryQuery LogicOp UnaryQuery UnaryQueryOp ParenthesizedQuery SelectorQuery selector MediaStatement media CharsetStatement charset NamespaceStatement namespace NamespaceName KeyframesStatement keyframes KeyframeName KeyframeList from to SupportsStatement supports AtRule",maxTerm:106,nodeProps:[[l.openedBy,17,"(",48,"{"],[l.closedBy,18,")",49,"}"]],skippedNodes:[0,3],repeatNodeCount:8,tokenData:"Ay~R![OX$wX^%]^p$wpq%]qr(crs+}st,otu2Uuv$wvw2rwx2}xy3jyz3uz{3z{|4_|}8U}!O8a!O!P8x!P!Q9Z!Q![;e![!]<Y!]!^<x!^!_$w!_!`=T!`!a=`!a!b$w!b!c>O!c!}$w!}#O?[#O#P$w#P#Q?g#Q#R2U#R#T$w#T#U?r#U#c$w#c#d@q#d#o$w#o#pAQ#p#q2U#q#rA]#r#sAh#s#y$w#y#z%]#z$f$w$f$g%]$g#BY$w#BY#BZ%]#BZ$IS$w$IS$I_%]$I_$I|$w$I|$JO%]$JO$JT$w$JT$JU%]$JU$KV$w$KV$KW%]$KW&FU$w&FU&FV%]&FV~$wW$zQOy%Qz~%QW%VQoWOy%Qz~%Q~%bf#T~OX%QX^&v^p%Qpq&vqy%Qz#y%Q#y#z&v#z$f%Q$f$g&v$g#BY%Q#BY#BZ&v#BZ$IS%Q$IS$I_&v$I_$I|%Q$I|$JO&v$JO$JT%Q$JT$JU&v$JU$KV%Q$KV$KW&v$KW&FU%Q&FU&FV&v&FV~%Q~&}f#T~oWOX%QX^&v^p%Qpq&vqy%Qz#y%Q#y#z&v#z$f%Q$f$g&v$g#BY%Q#BY#BZ&v#BZ$IS%Q$IS$I_&v$I_$I|%Q$I|$JO&v$JO$JT%Q$JT$JU&v$JU$KV%Q$KV$KW&v$KW&FU%Q&FU&FV&v&FV~%Q^(fSOy%Qz#]%Q#]#^(r#^~%Q^(wSoWOy%Qz#a%Q#a#b)T#b~%Q^)YSoWOy%Qz#d%Q#d#e)f#e~%Q^)kSoWOy%Qz#c%Q#c#d)w#d~%Q^)|SoWOy%Qz#f%Q#f#g*Y#g~%Q^*_SoWOy%Qz#h%Q#h#i*k#i~%Q^*pSoWOy%Qz#T%Q#T#U*|#U~%Q^+RSoWOy%Qz#b%Q#b#c+_#c~%Q^+dSoWOy%Qz#h%Q#h#i+p#i~%Q^+wQ!VUoWOy%Qz~%Q~,QUOY+}Zr+}rs,ds#O+}#O#P,i#P~+}~,iOh~~,lPO~+}_,tWtPOy%Qz!Q%Q!Q![-^![!c%Q!c!i-^!i#T%Q#T#Z-^#Z~%Q^-cWoWOy%Qz!Q%Q!Q![-{![!c%Q!c!i-{!i#T%Q#T#Z-{#Z~%Q^.QWoWOy%Qz!Q%Q!Q![.j![!c%Q!c!i.j!i#T%Q#T#Z.j#Z~%Q^.qWfUoWOy%Qz!Q%Q!Q![/Z![!c%Q!c!i/Z!i#T%Q#T#Z/Z#Z~%Q^/bWfUoWOy%Qz!Q%Q!Q![/z![!c%Q!c!i/z!i#T%Q#T#Z/z#Z~%Q^0PWoWOy%Qz!Q%Q!Q![0i![!c%Q!c!i0i!i#T%Q#T#Z0i#Z~%Q^0pWfUoWOy%Qz!Q%Q!Q![1Y![!c%Q!c!i1Y!i#T%Q#T#Z1Y#Z~%Q^1_WoWOy%Qz!Q%Q!Q![1w![!c%Q!c!i1w!i#T%Q#T#Z1w#Z~%Q^2OQfUoWOy%Qz~%QY2XSOy%Qz!_%Q!_!`2e!`~%QY2lQzQoWOy%Qz~%QX2wQXPOy%Qz~%Q~3QUOY2}Zw2}wx,dx#O2}#O#P3d#P~2}~3gPO~2}_3oQbVOy%Qz~%Q~3zOa~_4RSUPjSOy%Qz!_%Q!_!`2e!`~%Q_4fUjS!PPOy%Qz!O%Q!O!P4x!P!Q%Q!Q![7_![~%Q^4}SoWOy%Qz!Q%Q!Q![5Z![~%Q^5bWoW#ZUOy%Qz!Q%Q!Q![5Z![!g%Q!g!h5z!h#X%Q#X#Y5z#Y~%Q^6PWoWOy%Qz{%Q{|6i|}%Q}!O6i!O!Q%Q!Q![6z![~%Q^6nSoWOy%Qz!Q%Q!Q![6z![~%Q^7RSoW#ZUOy%Qz!Q%Q!Q![6z![~%Q^7fYoW#ZUOy%Qz!O%Q!O!P5Z!P!Q%Q!Q![7_![!g%Q!g!h5z!h#X%Q#X#Y5z#Y~%Q_8ZQpVOy%Qz~%Q^8fUjSOy%Qz!O%Q!O!P4x!P!Q%Q!Q![7_![~%Q_8}S#WPOy%Qz!Q%Q!Q![5Z![~%Q~9`RjSOy%Qz{9i{~%Q~9nSoWOy9iyz9zz{:o{~9i~9}ROz9zz{:W{~9z~:ZTOz9zz{:W{!P9z!P!Q:j!Q~9z~:oOR~~:tUoWOy9iyz9zz{:o{!P9i!P!Q;W!Q~9i~;_QR~oWOy%Qz~%Q^;jY#ZUOy%Qz!O%Q!O!P5Z!P!Q%Q!Q![7_![!g%Q!g!h5z!h#X%Q#X#Y5z#Y~%QX<_S]POy%Qz![%Q![!]<k!]~%QX<rQ^PoWOy%Qz~%Q_<}Q!WVOy%Qz~%QY=YQzQOy%Qz~%QX=eS|POy%Qz!`%Q!`!a=q!a~%QX=xQ|PoWOy%Qz~%QX>RUOy%Qz!c%Q!c!}>e!}#T%Q#T#o>e#o~%QX>lY!YPoWOy%Qz}%Q}!O>e!O!Q%Q!Q![>e![!c%Q!c!}>e!}#T%Q#T#o>e#o~%QX?aQxPOy%Qz~%Q^?lQvUOy%Qz~%QX?uSOy%Qz#b%Q#b#c@R#c~%QX@WSoWOy%Qz#W%Q#W#X@d#X~%QX@kQ!`PoWOy%Qz~%QX@tSOy%Qz#f%Q#f#g@d#g~%QXAVQ!RPOy%Qz~%Q_AbQ!QVOy%Qz~%QZAmS!PPOy%Qz!_%Q!_!`2e!`~%Q",tokenizers:[mO,gO,pO,0,1,2,3],topRules:{StyleSheet:[0,4]},specialized:[{term:94,get:t=>QO[t]||-1},{term:56,get:t=>bO[t]||-1},{term:95,get:t=>yO[t]||-1}],tokenPrec:1078});let xO=null;function wO(){if(!xO&&"object"==typeof document&&document.body){let t=[];for(let e in document.body.style)/[A-Z]|^-|^(item|length)$/.test(e)||t.push(e);xO=t.sort().map((t=>({type:"property",label:t})))}return xO||[]}const SO=["active","after","before","checked","default","disabled","empty","enabled","first-child","first-letter","first-line","first-of-type","focus","hover","in-range","indeterminate","invalid","lang","last-child","last-of-type","link","not","nth-child","nth-last-child","nth-last-of-type","nth-of-type","only-of-type","only-child","optional","out-of-range","placeholder","read-only","read-write","required","root","selection","target","valid","visited"].map((t=>({type:"class",label:t}))),kO=["above","absolute","activeborder","additive","activecaption","after-white-space","ahead","alias","all","all-scroll","alphabetic","alternate","always","antialiased","appworkspace","asterisks","attr","auto","auto-flow","avoid","avoid-column","avoid-page","avoid-region","axis-pan","background","backwards","baseline","below","bidi-override","blink","block","block-axis","bold","bolder","border","border-box","both","bottom","break","break-all","break-word","bullets","button","button-bevel","buttonface","buttonhighlight","buttonshadow","buttontext","calc","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-decimal","clear","clip","close-quote","col-resize","collapse","color","color-burn","color-dodge","column","column-reverse","compact","condensed","contain","content","contents","content-box","context-menu","continuous","copy","counter","counters","cover","crop","cross","crosshair","currentcolor","cursive","cyclic","darken","dashed","decimal","decimal-leading-zero","default","default-button","dense","destination-atop","destination-in","destination-out","destination-over","difference","disc","discard","disclosure-closed","disclosure-open","document","dot-dash","dot-dot-dash","dotted","double","down","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipse","ellipsis","embed","end","ethiopic-abegede-gez","ethiopic-halehame-aa-er","ethiopic-halehame-gez","ew-resize","exclusion","expanded","extends","extra-condensed","extra-expanded","fantasy","fast","fill","fill-box","fixed","flat","flex","flex-end","flex-start","footnotes","forwards","from","geometricPrecision","graytext","grid","groove","hand","hard-light","help","hidden","hide","higher","highlight","highlighttext","horizontal","hsl","hsla","hue","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-flex","inline-grid","inline-table","inset","inside","intrinsic","invert","italic","justify","keep-all","landscape","large","larger","left","level","lighter","lighten","line-through","linear","linear-gradient","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-hexadecimal","lower-latin","lower-norwegian","lowercase","ltr","luminosity","manipulation","match","matrix","matrix3d","medium","menu","menutext","message-box","middle","min-intrinsic","mix","monospace","move","multiple","multiple_mask_images","multiply","n-resize","narrower","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","numbers","numeric","nw-resize","nwse-resize","oblique","opacity","open-quote","optimizeLegibility","optimizeSpeed","outset","outside","outside-shape","overlay","overline","padding","padding-box","painted","page","paused","perspective","pinch-zoom","plus-darker","plus-lighter","pointer","polygon","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radial-gradient","radio","read-only","read-write","read-write-plaintext-only","rectangle","region","relative","repeat","repeating-linear-gradient","repeating-radial-gradient","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","rotate","rotate3d","rotateX","rotateY","rotateZ","round","row","row-resize","row-reverse","rtl","run-in","running","s-resize","sans-serif","saturation","scale","scale3d","scaleX","scaleY","scaleZ","screen","scroll","scrollbar","scroll-position","se-resize","self-start","self-end","semi-condensed","semi-expanded","separate","serif","show","single","skew","skewX","skewY","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","soft-light","solid","source-atop","source-in","source-out","source-over","space","space-around","space-between","space-evenly","spell-out","square","start","static","status-bar","stretch","stroke","stroke-box","sub","subpixel-antialiased","svg_masks","super","sw-resize","symbolic","symbols","system-ui","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","text","text-bottom","text-top","textarea","textfield","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","to","top","transform","translate","translate3d","translateX","translateY","translateZ","transparent","ultra-condensed","ultra-expanded","underline","unidirectional-pan","unset","up","upper-latin","uppercase","url","var","vertical","vertical-text","view-box","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","wider","window","windowframe","windowtext","words","wrap","wrap-reverse","x-large","x-small","xor","xx-large","xx-small"].map((t=>({type:"keyword",label:t}))).concat(["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","grey","green","greenyellow","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"].map((t=>({type:"constant",label:t})))),$O=["a","abbr","address","article","aside","b","bdi","bdo","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","dd","del","details","dfn","dialog","div","dl","dt","em","figcaption","figure","footer","form","header","hgroup","h1","h2","h3","h4","h5","h6","hr","html","i","iframe","img","input","ins","kbd","label","legend","li","main","meter","nav","ol","output","p","pre","ruby","section","select","small","source","span","strong","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","tr","u","ul"].map((t=>({type:"type",label:t}))),TO=/^[\w-]*/,PO=t=>{let{state:e,pos:i}=t,n=no(e).resolveInner(i,-1);if("PropertyName"==n.name)return{from:n.from,options:wO(),span:TO};if("ValueName"==n.name)return{from:n.from,options:kO,span:TO};if("PseudoClassName"==n.name)return{from:n.from,options:SO,span:TO};if("TagName"==n.name){for(let{parent:t}=n;t;t=t.parent)if("Block"==t.name)return{from:n.from,options:wO(),span:TO};return{from:n.from,options:$O,span:TO}}if(!t.explicit)return null;let r=n.resolve(i),s=r.childBefore(i);return s&&":"==s.name&&"PseudoClassSelector"==r.name?{from:i,options:SO,span:TO}:s&&":"==s.name&&"Declaration"==r.name||"ArgList"==r.name?{from:i,options:kO,span:TO}:"Block"==r.name?{from:i,options:wO(),span:TO}:null},RO=io.define({parser:vO.configure({props:[xo.add({Declaration:Xo()}),Zo.add({Block:_o}),qo({"import charset namespace keyframes":ma.definitionKeyword,"media supports":ma.controlKeyword,"from to selector":ma.keyword,NamespaceName:ma.namespace,KeyframeName:ma.labelName,TagName:ma.tagName,ClassName:ma.className,PseudoClassName:ma.constant(ma.className),IdName:ma.labelName,"FeatureName PropertyName":ma.propertyName,AttributeName:ma.attributeName,NumberLiteral:ma.number,KeywordQuery:ma.keyword,UnaryQueryOp:ma.operatorKeyword,"CallTag ValueName":ma.atom,VariableName:ma.variableName,Callee:ma.operatorKeyword,Unit:ma.unit,"UniversalSelector NestingSelector":ma.definitionOperator,AtKeyword:ma.keyword,MatchOp:ma.compareOperator,"ChildOp SiblingOp, LogicOp":ma.logicOperator,BinOp:ma.arithmeticOperator,Important:ma.modifier,Comment:ma.blockComment,ParenthesizedContent:ma.special(ma.name),ColorLiteral:ma.color,StringLiteral:ma.string,":":ma.punctuation,"PseudoOp #":ma.derefOperator,"; ,":ma.separator,"( )":ma.paren,"[ ]":ma.squareBracket,"{ }":ma.brace})]}),languageData:{commentTokens:{block:{open:"/*",close:"*/"}},indentOnInput:/^\s*\}$/,wordChars:"-"}}),WO=RO.data.of({autocomplete:PO});function XO(){return new fo(RO,WO)}var CO=Object.freeze({__proto__:null,css:XO,cssCompletion:WO,cssCompletionSource:PO,cssLanguage:RO});const AO={area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0,menuitem:!0},ZO={dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},_O={dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}};function LO(t){return 9==t||10==t||13==t||32==t}let DO=null,zO=null,EO=0;function MO(t,e){let i=t.pos+e;if(EO==i&&zO==t)return DO;let n=t.peek(e);for(;LO(n);)n=t.peek(++e);let r="";for(;45==(s=n)||46==s||58==s||s>=65&&s<=90||95==s||s>=97&&s<=122||s>=161;)r+=String.fromCharCode(n),n=t.peek(++e);var s;return zO=t,EO=i,DO=r?r.toLowerCase():n==jO||n==qO?void 0:null}const jO=63,qO=33;function YO(t,e){this.name=t,this.parent=e,this.hash=e?e.hash:0;for(let e=0;e<t.length;e++)this.hash+=(this.hash<<4)+t.charCodeAt(e)+(t.charCodeAt(e)<<8)}const VO=[4,8,5,6,7],UO=new hO({start:null,shift:(t,e,i,n)=>VO.indexOf(e)>-1?new YO(MO(n,1)||"",t):t,reduce:(t,e)=>18==e&&t?t.parent:t,reuse(t,e,i,n){let r=e.type.id;return 4==r||35==r?new YO(MO(n,1)||"",t):t},hash:t=>t?t.hash:0,strict:!1}),GO=new Nu(((t,e)=>{if(60!=t.next)return void(t.next<0&&e.context&&t.acceptToken(56));t.advance();let i=47==t.next;i&&t.advance();let n=MO(t,0);if(void 0===n)return;if(!n)return t.acceptToken(i?12:4);let r=e.context?e.context.name:null;if(i){if(n==r)return t.acceptToken(9);if(r&&ZO[r])return t.acceptToken(56,-2);if(e.dialectEnabled(0))return t.acceptToken(10);for(let t=e.context;t;t=t.parent)if(t.name==n)return;t.acceptToken(11)}else{if("script"==n)return t.acceptToken(5);if("style"==n)return t.acceptToken(6);if("textarea"==n)return t.acceptToken(7);if(AO.hasOwnProperty(n))return t.acceptToken(8);r&&_O[r]&&_O[r][n]?t.acceptToken(56,-1):t.acceptToken(4)}}),{contextual:!0}),IO=new Nu((t=>{for(let e=0,i=0;;i++){if(t.next<0){i&&t.acceptToken(57);break}if(t.next=="--\x3e".charCodeAt(e)){if(e++,3==e){i>3&&t.acceptToken(57,-2);break}}else e=0;t.advance()}}));function NO(t,e,i){let n=2+t.length;return new Nu((r=>{for(let s=0,o=0,a=0;;a++){if(r.next<0){a&&r.acceptToken(e);break}if(0==s&&60==r.next||1==s&&47==r.next||s>=2&&s<n&&r.next==t.charCodeAt(s-2))s++,o++;else if(2!=s&&s!=n||!LO(r.next)){if(s==n&&62==r.next){a>o?r.acceptToken(e,-o):r.acceptToken(i,-(o-2));break}if((10==r.next||13==r.next)&&a){r.acceptToken(e,1);break}s=o=0}else o++;r.advance()}}))}const BO=NO("script",53,1),FO=NO("style",54,2),HO=NO("textarea",55,3),JO=cO.deserialize({version:13,states:",xOVOxOOO!WQ!bO'#CoO!]Q!bO'#CyO!bQ!bO'#C|O!gQ!bO'#DPO!lQ!bO'#DRO!qOXO'#CnO!|OYO'#CnO#XO[O'#CnO$eOxO'#CnOOOW'#Cn'#CnO$lO!rO'#DSO$tQ!bO'#DUO$yQ!bO'#DVOOOW'#Dj'#DjOOOW'#DX'#DXQVOxOOO%OQ#tO,59ZO%WQ#tO,59eO%`Q#tO,59hO%hQ#tO,59kO%pQ#tO,59mOOOX'#D]'#D]O%xOXO'#CwO&TOXO,59YOOOY'#D^'#D^O&]OYO'#CzO&hOYO,59YOOO['#D_'#D_O&pO[O'#C}O&{O[O,59YOOOW'#D`'#D`O'TOxO,59YO'[Q!bO'#DQOOOW,59Y,59YOOO`'#Da'#DaO'aO!rO,59nOOOW,59n,59nO'iQ!bO,59pO'nQ!bO,59qOOOW-E7V-E7VO'sQ#tO'#CqOOQO'#DY'#DYO(OQ#tO1G.uOOOX1G.u1G.uO(WQ#tO1G/POOOY1G/P1G/PO(`Q#tO1G/SOOO[1G/S1G/SO(hQ#tO1G/VOOOW1G/V1G/VO(pQ#tO1G/XOOOW1G/X1G/XOOOX-E7Z-E7ZO(xQ!bO'#CxOOOW1G.t1G.tOOOY-E7[-E7[O(}Q!bO'#C{OOO[-E7]-E7]O)SQ!bO'#DOOOOW-E7^-E7^O)XQ!bO,59lOOO`-E7_-E7_OOOW1G/Y1G/YOOOW1G/[1G/[OOOW1G/]1G/]O)^Q&jO,59]OOQO-E7W-E7WOOOX7+$a7+$aOOOY7+$k7+$kOOO[7+$n7+$nOOOW7+$q7+$qOOOW7+$s7+$sO)iQ!bO,59dO)nQ!bO,59gO)sQ!bO,59jOOOW1G/W1G/WO)xO,UO'#CtO*WO7[O'#CtOOQO1G.w1G.wOOOW1G/O1G/OOOOW1G/R1G/ROOOW1G/U1G/UOOOO'#DZ'#DZO*fO,UO,59`OOQO,59`,59`OOOO'#D['#D[O*tO7[O,59`OOOO-E7X-E7XOOQO1G.z1G.zOOOO-E7Y-E7Y",stateData:"+[~O!]OS~OSSOTPOUQOVROWTOY]OZ[O[^O^^O_^O`^Oa^Ow^Oz_O!cZO~OdaO~OdbO~OdcO~OddO~OdeO~O!VfOPkP!YkP~O!WiOQnP!YnP~O!XlORqP!YqP~OSSOTPOUQOVROWTOXqOY]OZ[O[^O^^O_^O`^Oa^Ow^O!cZO~O!YrO~P#dO!ZsO!duO~OdvO~OdwO~OfyOj|O~OfyOj!OO~OfyOj!QO~OfyOj!SO~OfyOj!UO~O!VfOPkX!YkX~OP!WO!Y!XO~O!WiOQnX!YnX~OQ!ZO!Y!XO~O!XlORqX!YqX~OR!]O!Y!XO~O!Y!XO~P#dOd!_O~O!ZsO!d!aO~Oj!bO~Oj!cO~Og!dOfeXjeX~OfyOj!fO~OfyOj!gO~OfyOj!hO~OfyOj!iO~OfyOj!jO~Od!kO~Od!lO~Od!mO~Oj!nO~Oi!qO!_!oO!a!pO~Oj!rO~Oj!sO~Oj!tO~O_!uO`!uO!_!wO!`!uO~O_!xO`!xO!a!wO!b!xO~O_!uO`!uO!_!{O!`!uO~O_!xO`!xO!a!{O!b!xO~O`_a!cwz!c~",goto:"%o!_PPPPPPPPPPPPPPPPPP!`!fP!lPP!xPP!{#O#R#X#[#_#e#h#k#q#w!`P!`!`P#}$T$k$q$w$}%T%Z%aPPPPPPPP%gX^OX`pXUOX`pezabcde{}!P!R!TR!q!dRhUR!XhXVOX`pRkVR!XkXWOX`pRnWR!XnXXOX`pQrXR!XpXYOX`pQ`ORx`Q{aQ}bQ!PcQ!RdQ!TeZ!e{}!P!R!TQ!v!oR!z!vQ!y!pR!|!yQgUR!VgQjVR!YjQmWR![mQpXR!^pQtZR!`tS_O`ToXp",nodeNames:"⚠ StartCloseTag StartCloseTag StartCloseTag StartTag StartTag StartTag StartTag StartTag StartCloseTag StartCloseTag StartCloseTag IncompleteCloseTag Document Text EntityReference CharacterReference InvalidEntity Element OpenTag TagName Attribute AttributeName Is AttributeValue UnquotedAttributeValue EndTag ScriptText CloseTag OpenTag StyleText CloseTag OpenTag TextareaText CloseTag OpenTag CloseTag SelfClosingTag Comment ProcessingInst MismatchedCloseTag CloseTag DoctypeDecl",maxTerm:66,context:UO,nodeProps:[[l.closedBy,-11,1,2,3,4,5,6,7,8,9,10,11,"EndTag",-4,19,29,32,35,"CloseTag"],[l.group,-9,12,15,16,17,18,38,39,40,41,"Entity",14,"Entity TextContent",-3,27,30,33,"TextContent Entity"],[l.openedBy,26,"StartTag StartCloseTag",-4,28,31,34,36,"OpenTag"]],skippedNodes:[0],repeatNodeCount:9,tokenData:"!#b!aR!WOX$kXY)sYZ)sZ]$k]^)s^p$kpq)sqr$krs*zsv$kvw+dwx2yx}$k}!O3f!O!P$k!P!Q7_!Q![$k![!]8u!]!^$k!^!_>b!_!`!!p!`!a8T!a!c$k!c!}8u!}#R$k#R#S8u#S#T$k#T#o8u#o$f$k$f$g&R$g%W$k%W%o8u%o%p$k%p&a8u&a&b$k&b1p8u1p4U$k4U4d8u4d4e$k4e$IS8u$IS$I`$k$I`$Ib8u$Ib$Kh$k$Kh%#t8u%#t&/x$k&/x&Et8u&Et&FV$k&FV;'S8u;'S;:j<t;:j?&r$k?&r?Ah8u?Ah?BY$k?BY?Mn8u?Mn~$k!Z$vc^PiW!``!bpOX$kXZ&RZ]$k]^&R^p$kpq&Rqr$krs&qsv$kvw)Rwx'rx!P$k!P!Q&R!Q!^$k!^!_(k!_!a&R!a$f$k$f$g&R$g~$k!R&[V^P!``!bpOr&Rrs&qsv&Rwx'rx!^&R!^!_(k!_~&Rq&xT^P!bpOv&qwx'Xx!^&q!^!_'g!_~&qP'^R^POv'Xw!^'X!_~'Xp'lQ!bpOv'gx~'ga'yU^P!``Or'rrs'Xsv'rw!^'r!^!_(]!_~'r`(bR!``Or(]sv(]w~(]!Q(rT!``!bpOr(krs'gsv(kwx(]x~(kW)WXiWOX)RZ])R^p)Rqr)Rsw)Rx!P)R!Q!^)R!a$f)R$g~)R!a*O^^P!``!bp!]^OX&RXY)sYZ)sZ]&R]^)s^p&Rpq)sqr&Rrs&qsv&Rwx'rx!^&R!^!_(k!_~&R!Z+TT!_h^P!bpOv&qwx'Xx!^&q!^!_'g!_~&q!Z+kbiWaPOX,sXZ.QZ],s]^.Q^p,sqr,srs.Qst/]tw,swx.Qx!P,s!P!Q.Q!Q!],s!]!^)R!^!a.Q!a$f,s$f$g.Q$g~,s!Z,xbiWOX,sXZ.QZ],s]^.Q^p,sqr,srs.Qst)Rtw,swx.Qx!P,s!P!Q.Q!Q!],s!]!^.i!^!a.Q!a$f,s$f$g.Q$g~,s!R.TTOp.Qqs.Qt!].Q!]!^.d!^~.Q!R.iO_!R!Z.pXiW_!ROX)RZ])R^p)Rqr)Rsw)Rx!P)R!Q!^)R!a$f)R$g~)R!Z/baiWOX0gXZ1qZ]0g]^1q^p0gqr0grs1qsw0gwx1qx!P0g!P!Q1q!Q!]0g!]!^)R!^!a1q!a$f0g$f$g1q$g~0g!Z0laiWOX0gXZ1qZ]0g]^1q^p0gqr0grs1qsw0gwx1qx!P0g!P!Q1q!Q!]0g!]!^2V!^!a1q!a$f0g$f$g1q$g~0g!R1tSOp1qq!]1q!]!^2Q!^~1q!R2VO`!R!Z2^XiW`!ROX)RZ])R^p)Rqr)Rsw)Rx!P)R!Q!^)R!a$f)R$g~)R!Z3SU!ax^P!``Or'rrs'Xsv'rw!^'r!^!_(]!_~'r!]3qe^PiW!``!bpOX$kXZ&RZ]$k]^&R^p$kpq&Rqr$krs&qsv$kvw)Rwx'rx}$k}!O5S!O!P$k!P!Q&R!Q!^$k!^!_(k!_!a&R!a$f$k$f$g&R$g~$k!]5_d^PiW!``!bpOX$kXZ&RZ]$k]^&R^p$kpq&Rqr$krs&qsv$kvw)Rwx'rx!P$k!P!Q&R!Q!^$k!^!_(k!_!`&R!`!a6m!a$f$k$f$g&R$g~$k!T6xV^P!``!bp!dQOr&Rrs&qsv&Rwx'rx!^&R!^!_(k!_~&R!X7hX^P!``!bpOr&Rrs&qsv&Rwx'rx!^&R!^!_(k!_!`&R!`!a8T!a~&R!X8`VjU^P!``!bpOr&Rrs&qsv&Rwx'rx!^&R!^!_(k!_~&R!a9U!YfSdQ^PiW!``!bpOX$kXZ&RZ]$k]^&R^p$kpq&Rqr$krs&qsv$kvw)Rwx'rx}$k}!O8u!O!P8u!P!Q&R!Q![8u![!]8u!]!^$k!^!_(k!_!a&R!a!c$k!c!}8u!}#R$k#R#S8u#S#T$k#T#o8u#o$f$k$f$g&R$g$}$k$}%O8u%O%W$k%W%o8u%o%p$k%p&a8u&a&b$k&b1p8u1p4U8u4U4d8u4d4e$k4e$IS8u$IS$I`$k$I`$Ib8u$Ib$Je$k$Je$Jg8u$Jg$Kh$k$Kh%#t8u%#t&/x$k&/x&Et8u&Et&FV$k&FV;'S8u;'S;:j<t;:j?&r$k?&r?Ah8u?Ah?BY$k?BY?Mn8u?Mn~$k!a=Pe^PiW!``!bpOX$kXZ&RZ]$k]^&R^p$kpq&Rqr$krs&qsv$kvw)Rwx'rx!P$k!P!Q&R!Q!^$k!^!_(k!_!a&R!a$f$k$f$g&R$g;=`$k;=`<%l8u<%l~$k!R>iW!``!bpOq(kqr?Rrs'gsv(kwx(]x!a(k!a!bKj!b~(k!R?YZ!``!bpOr(krs'gsv(kwx(]x}(k}!O?{!O!f(k!f!gAR!g#W(k#W#XGz#X~(k!R@SV!``!bpOr(krs'gsv(kwx(]x}(k}!O@i!O~(k!R@rT!``!bp!cPOr(krs'gsv(kwx(]x~(k!RAYV!``!bpOr(krs'gsv(kwx(]x!q(k!q!rAo!r~(k!RAvV!``!bpOr(krs'gsv(kwx(]x!e(k!e!fB]!f~(k!RBdV!``!bpOr(krs'gsv(kwx(]x!v(k!v!wBy!w~(k!RCQV!``!bpOr(krs'gsv(kwx(]x!{(k!{!|Cg!|~(k!RCnV!``!bpOr(krs'gsv(kwx(]x!r(k!r!sDT!s~(k!RD[V!``!bpOr(krs'gsv(kwx(]x!g(k!g!hDq!h~(k!RDxW!``!bpOrDqrsEbsvDqvwEvwxFfx!`Dq!`!aGb!a~DqqEgT!bpOvEbvxEvx!`Eb!`!aFX!a~EbPEyRO!`Ev!`!aFS!a~EvPFXOzPqF`Q!bpzPOv'gx~'gaFkV!``OrFfrsEvsvFfvwEvw!`Ff!`!aGQ!a~FfaGXR!``zPOr(]sv(]w~(]!RGkT!``!bpzPOr(krs'gsv(kwx(]x~(k!RHRV!``!bpOr(krs'gsv(kwx(]x#c(k#c#dHh#d~(k!RHoV!``!bpOr(krs'gsv(kwx(]x#V(k#V#WIU#W~(k!RI]V!``!bpOr(krs'gsv(kwx(]x#h(k#h#iIr#i~(k!RIyV!``!bpOr(krs'gsv(kwx(]x#m(k#m#nJ`#n~(k!RJgV!``!bpOr(krs'gsv(kwx(]x#d(k#d#eJ|#e~(k!RKTV!``!bpOr(krs'gsv(kwx(]x#X(k#X#YDq#Y~(k!RKqW!``!bpOrKjrsLZsvKjvwLowxNPx!aKj!a!b! g!b~KjqL`T!bpOvLZvxLox!aLZ!a!bM^!b~LZPLrRO!aLo!a!bL{!b~LoPMORO!`Lo!`!aMX!a~LoPM^OwPqMcT!bpOvLZvxLox!`LZ!`!aMr!a~LZqMyQ!bpwPOv'gx~'gaNUV!``OrNPrsLosvNPvwLow!aNP!a!bNk!b~NPaNpV!``OrNPrsLosvNPvwLow!`NP!`!a! V!a~NPa! ^R!``wPOr(]sv(]w~(]!R! nW!``!bpOrKjrsLZsvKjvwLowxNPx!`Kj!`!a!!W!a~Kj!R!!aT!``!bpwPOr(krs'gsv(kwx(]x~(k!V!!{VgS^P!``!bpOr&Rrs&qsv&Rwx'rx!^&R!^!_(k!_~&R",tokenizers:[BO,FO,HO,GO,IO,0,1,2,3,4,5],topRules:{Document:[0,13]},dialects:{noMatch:0},tokenPrec:464});function KO(t,e){let i=Object.create(null);for(let n of t.firstChild.getChildren("Attribute")){let t=n.getChild("AttributeName"),r=n.getChild("AttributeValue")||n.getChild("UnquotedAttributeValue");t&&(i[e.read(t.from,t.to)]=r?"AttributeValue"==r.name?e.read(r.from+1,r.to-1):e.read(r.from,r.to):"")}return i}function td(t,e,i){let n;for(let r of i)if(!r.attrs||r.attrs(n||(n=KO(t.node.parent,e))))return{parser:r.parser};return null}const ed=[9,10,11,12,13,32,133,160,5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8232,8233,8239,8287,12288],id=new hO({start:!1,shift:(t,e)=>5==e||6==e||284==e?t:285==e,strict:!1}),nd=new Nu(((t,e)=>{let{next:i}=t;(125==i||-1==i||e.context)&&e.canShift(282)&&t.acceptToken(282)}),{contextual:!0,fallback:!0}),rd=new Nu(((t,e)=>{let i,{next:n}=t;ed.indexOf(n)>-1||(47!=n||47!=(i=t.peek(1))&&42!=i)&&125!=n&&59!=n&&-1!=n&&!e.context&&e.canShift(279)&&t.acceptToken(279)}),{contextual:!0}),sd=new Nu(((t,e)=>{let{next:i}=t;if((43==i||45==i)&&(t.advance(),i==t.next)){t.advance();let i=!e.context&&e.canShift(1);t.acceptToken(i?1:2)}}),{contextual:!0}),od=new Nu((t=>{for(let e=!1,i=0;;i++){let{next:n}=t;if(n<0){i&&t.acceptToken(280);break}if(96==n){i?t.acceptToken(280):t.acceptToken(281,1);break}if(123==n&&e){1==i?t.acceptToken(3,1):t.acceptToken(280,-1);break}if(10==n&&i){t.advance(),t.acceptToken(280);break}92==n&&t.advance(),e=36==n,t.advance()}})),ad={__proto__:null,export:18,as:23,from:29,default:32,async:37,function:38,this:48,true:56,false:56,void:66,typeof:70,null:86,super:88,new:122,await:139,yield:141,delete:142,class:152,extends:154,public:197,private:197,protected:197,readonly:199,instanceof:220,in:222,const:224,import:256,keyof:307,unique:311,infer:317,is:351,abstract:371,implements:373,type:375,let:378,var:380,interface:387,enum:391,namespace:397,module:399,declare:403,global:407,for:428,of:437,while:440,with:444,do:448,if:452,else:454,switch:458,case:464,try:470,catch:472,finally:474,return:478,throw:482,break:486,continue:490,debugger:494},ld={__proto__:null,async:109,get:111,set:113,public:161,private:161,protected:161,static:163,abstract:165,override:167,readonly:173,new:355},hd={__proto__:null,"<":129},cd=cO.deserialize({version:13,states:"$1jO`QYOOO'QQ!LdO'#ChO'XOSO'#DVO)dQYO'#D]O)tQYO'#DhO){QYO'#DrO-xQYO'#DxOOQO'#E]'#E]O.]QWO'#E[O.bQWO'#E[OOQ!LS'#Ef'#EfO0aQ!LdO'#IrO2wQ!LdO'#IsO3eQWO'#EzO3jQpO'#FaOOQ!LS'#FS'#FSO3rO!bO'#FSO4QQWO'#FhO5_QWO'#FgOOQ!LS'#Is'#IsOOQ!LQ'#Ir'#IrOOQQ'#J['#J[O5dQWO'#HnO5iQ!LYO'#HoOOQQ'#If'#IfOOQQ'#Hp'#HpQ`QYOOO){QYO'#DjO5qQWO'#G[O5vQ#tO'#CmO6UQWO'#EZO6aQWO'#EgO6fQ#tO'#FRO7QQWO'#G[O7VQWO'#G`O7bQWO'#G`O7pQWO'#GcO7pQWO'#GdO7pQWO'#GfO5qQWO'#GiO8aQWO'#GlO9oQWO'#CdO:PQWO'#GyO:XQWO'#HPO:XQWO'#HRO`QYO'#HTO:XQWO'#HVO:XQWO'#HYO:^QWO'#H`O:cQ!LZO'#HdO){QYO'#HfO:nQ!LZO'#HhO:yQ!LZO'#HjO5iQ!LYO'#HlO){QYO'#DWOOOS'#Hr'#HrO;UOSO,59qOOQ!LS,59q,59qO=gQbO'#ChO=qQYO'#HsO>UQWO'#ItO@TQbO'#ItO'dQYO'#ItO@[QWO,59wO@rQ&jO'#DbOAkQWO'#E]OAxQWO'#JPOBTQWO'#JOOBTQWO'#JOOB]QWO,5:yOBbQWO'#I}OBiQWO'#DyO5vQ#tO'#EZOBwQWO'#EZOCSQ`O'#FROOQ!LS,5:S,5:SOC[QYO,5:SOEYQ!LdO,5:^OEvQWO,5:dOFaQ!LYO'#I|O7VQWO'#I{OFhQWO'#I{OFpQWO,5:xOFuQWO'#I{OGTQYO,5:vOITQWO'#EWOJ_QWO,5:vOKnQWO'#DlOKuQYO'#DqOLPQ&jO,5;PO){QYO,5;POOQQ'#Er'#ErOOQQ'#Et'#EtO){QYO,5;RO){QYO,5;RO){QYO,5;RO){QYO,5;RO){QYO,5;RO){QYO,5;RO){QYO,5;RO){QYO,5;RO){QYO,5;RO){QYO,5;RO){QYO,5;ROOQQ'#Ex'#ExOLXQYO,5;cOOQ!LS,5;h,5;hOOQ!LS,5;i,5;iONXQWO,5;iOOQ!LS,5;j,5;jO){QYO'#H}ON^Q!LYO,5<TONxQWO,5;RO){QYO,5;fO! bQpO'#JTO! PQpO'#JTO! iQpO'#JTO! zQpO,5;qOOOO,5;{,5;{O!!YQYO'#FcOOOO'#H|'#H|O3rO!bO,5;nO!!aQpO'#FeOOQ!LS,5;n,5;nO!!}Q,UO'#CrOOQ!LS'#Cu'#CuO!#bQWO'#CuO!#gOSO'#CyO!$TQ#tO,5<QO!$[QWO,5<SO!%hQWO'#FrO!%uQWO'#FsO!%zQWO'#FwO!&|Q&jO'#F{O!'oQ,UO'#IoOOQ!LS'#Io'#IoO!'yQWO'#InO!(XQWO'#ImOOQ!LS'#Cs'#CsOOQ!LS'#C|'#C|O!(aQWO'#DOOJdQWO'#FjOJdQWO'#FlO!(fQWO'#FnO!(kQWO'#FoO!(pQWO'#FuOJdQWO'#FzO!(uQWO'#E^O!)^QWO,5<RO`QYO,5>YOOQQ'#Ii'#IiOOQQ,5>Z,5>ZOOQQ-E;n-E;nO!+YQ!LdO,5:UOOQ!LQ'#Cp'#CpO!+yQ#tO,5<vOOQO'#Cf'#CfO!,[QWO'#CqO!,dQ!LYO'#IjO5_QWO'#IjO:^QWO,59XO!,rQpO,59XO!,zQ#tO,59XO5vQ#tO,59XO!-VQWO,5:vO!-_QWO'#GxO!-mQWO'#J`O){QYO,5;kO!-uQ&jO,5;mO!-zQWO,5=cO!.PQWO,5=cO!.UQWO,5=cO5iQ!LYO,5=cO5qQWO,5<vO!.dQWO'#E_O!.xQ&jO'#E`OOQ!LQ'#I}'#I}O!/ZQ!LYO'#J]O5iQ!LYO,5<zO7pQWO,5=QOOQO'#Cr'#CrO!/fQpO,5<}O!/nQ#tO,5=OO!/yQWO,5=QO!0OQ`O,5=TO:^QWO'#GnO5qQWO'#GpO!0WQWO'#GpO5vQ#tO'#GsO!0]QWO'#GsOOQQ,5=W,5=WO!0bQWO'#GtO!0jQWO'#CmO!0oQWO,59OO!0yQWO,59OO!2{QYO,59OOOQQ,59O,59OO!3YQ!LYO,59OO){QYO,59OO!3eQYO'#G{OOQQ'#G|'#G|OOQQ'#G}'#G}O`QYO,5=eO!3uQWO,5=eO){QYO'#DxO`QYO,5=kO`QYO,5=mO!3zQWO,5=oO`QYO,5=qO!4PQWO,5=tO!4UQYO,5=zOOQQ,5>O,5>OO){QYO,5>OO5iQ!LYO,5>QOOQQ,5>S,5>SO!8VQWO,5>SOOQQ,5>U,5>UO!8VQWO,5>UOOQQ,5>W,5>WO!8[Q`O,59rOOOS-E;p-E;pOOQ!LS1G/]1G/]O!8aQbO,5>_O'dQYO,5>_OOQO,5>d,5>dO!8kQYO'#HsOOQO-E;q-E;qO!8xQWO,5?`O!9QQbO,5?`O!9XQWO,5?jOOQ!LS1G/c1G/cO!9aQpO'#DTOOQO'#Iv'#IvO){QYO'#IvO!:OQpO'#IvO!:mQpO'#DcO!;OQ&jO'#DcO!=ZQYO'#DcO!=bQWO'#IuO!=jQWO,59|O!=oQWO'#EaO!=}QWO'#JQO!>VQWO,5:zO!>mQ&jO'#DcO){QYO,5?kO!>wQWO'#HxOOQO-E;v-E;vO!9XQWO,5?jOOQ!LQ1G0e1G0eO!@TQ&jO'#D|OOQ!LS,5:e,5:eO){QYO,5:eOITQWO,5:eO!@[QWO,5:eO:^QWO,5:uO!,rQpO,5:uO!,zQ#tO,5:uO5vQ#tO,5:uOOQ!LS1G/n1G/nOOQ!LS1G0O1G0OOOQ!LQ'#EV'#EVO){QYO,5?hO!@gQ!LYO,5?hO!@xQ!LYO,5?hO!APQWO,5?gO!AXQWO'#HzO!APQWO,5?gOOQ!LQ1G0d1G0dO7VQWO,5?gOOQ!LS1G0b1G0bO!AsQ!LdO1G0bO!BdQ!LbO,5:rOOQ!LS'#Fq'#FqO!CQQ!LdO'#IoOGTQYO1G0bO!EPQ#tO'#IwO!EZQWO,5:WO!E`QbO'#IxO){QYO'#IxO!EjQWO,5:]OOQ!LS'#DT'#DTOOQ!LS1G0k1G0kO!EoQWO1G0kO!HQQ!LdO1G0mO!HXQ!LdO1G0mO!JlQ!LdO1G0mO!JsQ!LdO1G0mO!LzQ!LdO1G0mO!M_Q!LdO1G0mO#!OQ!LdO1G0mO#!VQ!LdO1G0mO#$jQ!LdO1G0mO#$qQ!LdO1G0mO#&fQ!LdO1G0mO#)`Q7^O'#ChO#+ZQ7^O1G0}O#-UQ7^O'#IsOOQ!LS1G1T1G1TO#-iQ!LdO,5>iOOQ!LQ-E;{-E;{O#.YQ!LdO1G0mOOQ!LS1G0m1G0mO#0[Q!LdO1G1QO#0{QpO,5;sO#1QQpO,5;tO#1VQpO'#F[O#1kQWO'#FZOOQO'#JU'#JUOOQO'#H{'#H{O#1pQpO1G1]OOQ!LS1G1]1G1]OOOO1G1f1G1fO#2OQ7^O'#IrO#2YQWO,5;}OLXQYO,5;}OOOO-E;z-E;zOOQ!LS1G1Y1G1YOOQ!LS,5<P,5<PO#2_QpO,5<POOQ!LS,59a,59aOITQWO'#C{OOOS'#Hq'#HqO#2dOSO,59eOOQ!LS,59e,59eO){QYO1G1lO!(kQWO'#IPO#2oQWO,5<eOOQ!LS,5<b,5<bOOQO'#GV'#GVOJdQWO,5<pOOQO'#GX'#GXOJdQWO,5<rOJdQWO,5<tOOQO1G1n1G1nO#2zQ`O'#CpO#3_Q`O,5<^O#3fQWO'#JXO5qQWO'#JXO#3tQWO,5<`OJdQWO,5<_O#3yQ`O'#FqO#4WQ`O'#JYO#4bQWO'#JYOITQWO'#JYO#4gQWO,5<cOOQ!LQ'#Dg'#DgO#4lQWO'#FtO#4wQpO'#F|O!&wQ&jO'#F|O!&wQ&jO'#GOO#5YQWO'#GPO!(pQWO'#GSOOQO'#IR'#IRO#5_Q&jO,5<gOOQ!LS,5<g,5<gO#5fQ&jO'#F|O#5tQ&jO'#F}O#5|Q&jO'#F}OOQ!LS,5<u,5<uOJdQWO,5?YOJdQWO,5?YO#6RQWO'#ISO#6^QWO,5?XOOQ!LS'#Ch'#ChO#7QQ#tO,59jOOQ!LS,59j,59jO#7sQ#tO,5<UO#8fQ#tO,5<WO#8pQWO,5<YOOQ!LS,5<Z,5<ZO#8uQWO,5<aO#8zQ#tO,5<fOGTQYO1G1mO#9[QWO1G1mOOQQ1G3t1G3tOOQ!LS1G/p1G/pONXQWO1G/pOOQQ1G2b1G2bOITQWO1G2bO){QYO1G2bOITQWO1G2bO#9aQWO1G2bO#9oQWO,59]O#:xQWO'#EWOOQ!LQ,5?U,5?UO#;SQ!LYO,5?UOOQQ1G.s1G.sO:^QWO1G.sO!,rQpO1G.sO!,zQ#tO1G.sO#;bQWO1G0bO#;gQWO'#ChO#;rQWO'#JaO#;zQWO,5=dO#<PQWO'#JaO#<UQWO'#JaO#<^QWO'#I[O#<lQWO,5?zO#<tQbO1G1VOOQ!LS1G1X1G1XO5qQWO1G2}O#<{QWO1G2}O#=QQWO1G2}O#=VQWO1G2}OOQQ1G2}1G2}O#=[Q#tO1G2bO7VQWO'#JOO7VQWO'#EaO7VQWO'#IUO#=mQ!LYO,5?wOOQQ1G2f1G2fO!/yQWO1G2lOITQWO1G2iO#=xQWO1G2iOOQQ1G2j1G2jOITQWO1G2jO#=}QWO1G2jO#>VQ&jO'#GhOOQQ1G2l1G2lO!&wQ&jO'#IWO!0OQ`O1G2oOOQQ1G2o1G2oOOQQ,5=Y,5=YO#>_Q#tO,5=[O5qQWO,5=[O#5YQWO,5=_O5_QWO,5=_O!,rQpO,5=_O!,zQ#tO,5=_O5vQ#tO,5=_O#>pQWO'#J_O#>{QWO,5=`OOQQ1G.j1G.jO#?QQ!LYO1G.jO#?]QWO1G.jO#?bQWO1G.jO5iQ!LYO1G.jO#?jQbO,5?|O#?tQWO,5?|O#@PQYO,5=gO#@WQWO,5=gO7VQWO,5?|OOQQ1G3P1G3PO`QYO1G3POOQQ1G3V1G3VOOQQ1G3X1G3XO:XQWO1G3ZO#@]QYO1G3]O#DWQYO'#H[OOQQ1G3`1G3`O:^QWO1G3fO#DeQWO1G3fO5iQ!LYO1G3jOOQQ1G3l1G3lOOQ!LQ'#Fx'#FxO5iQ!LYO1G3nO5iQ!LYO1G3pOOOS1G/^1G/^O#DmQ`O,5<TO#DuQbO1G3yOOQO1G4O1G4OO){QYO,5>_O#EPQWO1G4zO#EXQWO1G5UO#EaQWO,5?bOLXQYO,5:{O7VQWO,5:{O:^QWO,59}OLXQYO,59}O!,rQpO,59}O#EfQ7^O,59}OOQO,5:{,5:{O#EpQ&jO'#HtO#FWQWO,5?aOOQ!LS1G/h1G/hO#F`Q&jO'#HyO#FtQWO,5?lOOQ!LQ1G0f1G0fO!;OQ&jO,59}O#F|QbO1G5VO7VQWO,5>dOOQ!LQ'#ES'#ESO#GWQ!LrO'#ETO!?{Q&jO'#D}OOQO'#Hw'#HwO#GrQ&jO,5:hOOQ!LS,5:h,5:hO#GyQ&jO'#D}O#H[Q&jO'#D}O#HcQ&jO'#EYO#HfQ&jO'#ETO#HsQ&jO'#ETO!?{Q&jO'#ETO#IWQWO1G0PO#I]Q`O1G0POOQ!LS1G0P1G0PO){QYO1G0POITQWO1G0POOQ!LS1G0a1G0aO:^QWO1G0aO!,rQpO1G0aO!,zQ#tO1G0aO#IdQ!LdO1G5SO){QYO1G5SO#ItQ!LYO1G5SO#JVQWO1G5RO7VQWO,5>fOOQO,5>f,5>fO#J_QWO,5>fOOQO-E;x-E;xO#JVQWO1G5RO#JmQ!LdO,59jO#LlQ!LdO,5<UO#NnQ!LdO,5<WO$!pQ!LdO,5<fOOQ!LS7+%|7+%|O$$xQ!LdO7+%|O$%iQWO'#HuO$%sQWO,5?cOOQ!LS1G/r1G/rO$%{QYO'#HvO$&YQWO,5?dO$&bQbO,5?dOOQ!LS1G/w1G/wOOQ!LS7+&V7+&VO$&lQ7^O,5:^O){QYO7+&iO$&vQ7^O,5:UOOQO1G1_1G1_OOQO1G1`1G1`O$'TQMhO,5;vOLXQYO,5;uOOQO-E;y-E;yOOQ!LS7+&w7+&wOOOO7+'Q7+'QOOOO1G1i1G1iO$'`QWO1G1iOOQ!LS1G1k1G1kO$'eQ`O,59gOOOS-E;o-E;oOOQ!LS1G/P1G/PO$'lQ!LdO7+'WOOQ!LS,5>k,5>kO$(]QWO,5>kOOQ!LS1G2P1G2PP$(bQWO'#IPPOQ!LS-E;}-E;}O$)RQ#tO1G2[O$)tQ#tO1G2^O$*OQ#tO1G2`OOQ!LS1G1x1G1xO$*VQWO'#IOO$*eQWO,5?sO$*eQWO,5?sO$*mQWO,5?sO$*xQWO,5?sOOQO1G1z1G1zO$+WQ#tO1G1yO$+hQWO'#IQO$+xQWO,5?tOITQWO,5?tO$,QQ`O,5?tOOQ!LS1G1}1G1}O5iQ!LYO,5<hO5iQ!LYO,5<iO$,[QWO,5<iO#5TQWO,5<iO!,rQpO,5<hO$,aQWO,5<jO5iQ!LYO,5<kO$,[QWO,5<nOOQO-E<P-E<POOQ!LS1G2R1G2RO!&wQ&jO,5<hO$,iQWO,5<iO!&wQ&jO,5<jO!&wQ&jO,5<iO$,tQ#tO1G4tO$-OQ#tO1G4tOOQO,5>n,5>nOOQO-E<Q-E<QO!-uQ&jO,59lO){QYO,59lO$-]QWO1G1tOJdQWO1G1{O$-bQ!LdO7+'XOOQ!LS7+'X7+'XOGTQYO7+'XOOQ!LS7+%[7+%[O$.RQ`O'#JZO#IWQWO7+'|O$.]QWO7+'|O$.eQ`O7+'|OOQQ7+'|7+'|OITQWO7+'|O){QYO7+'|OITQWO7+'|OOQO1G.w1G.wO$.oQ!LbO'#ChO$/PQ!LbO,5<lO$/nQWO,5<lOOQ!LQ1G4p1G4pOOQQ7+$_7+$_O:^QWO7+$_O!,rQpO7+$_OGTQYO7+%|O$/sQWO'#IZO$0UQWO,5?{OOQO1G3O1G3OO5qQWO,5?{O$0UQWO,5?{O$0^QWO,5?{OOQO,5>v,5>vOOQO-E<Y-E<YOOQ!LS7+&q7+&qO$0cQWO7+(iO5iQ!LYO7+(iO5qQWO7+(iO$0hQWO7+(iO$0mQWO7+'|OOQ!LQ,5>p,5>pOOQ!LQ-E<S-E<SOOQQ7+(W7+(WO$0{Q!LbO7+(TOITQWO7+(TO$1VQ`O7+(UOOQQ7+(U7+(UOITQWO7+(UO$1^QWO'#J^O$1iQWO,5=SOOQO,5>r,5>rOOQO-E<U-E<UOOQQ7+(Z7+(ZO$2cQ&jO'#GqOOQQ1G2v1G2vOITQWO1G2vO){QYO1G2vOITQWO1G2vO$2jQWO1G2vO$2xQ#tO1G2vO5iQ!LYO1G2yO#5YQWO1G2yO5_QWO1G2yO!,rQpO1G2yO!,zQ#tO1G2yO$3ZQWO'#IYO$3fQWO,5?yO$3nQ&jO,5?yOOQ!LQ1G2z1G2zOOQQ7+$U7+$UO$3vQWO7+$UO5iQ!LYO7+$UO$3{QWO7+$UO){QYO1G5hO){QYO1G5iO$4QQYO1G3RO$4XQWO1G3RO$4^QYO1G3RO$4eQ!LYO1G5hOOQQ7+(k7+(kO5iQ!LYO7+(uO`QYO7+(wOOQQ'#Jd'#JdOOQQ'#I]'#I]O$4oQYO,5=vOOQQ,5=v,5=vO){QYO'#H]O$4|QWO'#H_OOQQ7+)Q7+)QO$5RQYO7+)QO7VQWO7+)QOOQQ7+)U7+)UOOQQ7+)Y7+)YOOQQ7+)[7+)[OOQO1G4|1G4|O$9PQ7^O1G0gO$9ZQWO1G0gOOQO1G/i1G/iO$9fQ7^O1G/iO:^QWO1G/iOLXQYO'#DcOOQO,5>`,5>`OOQO-E;r-E;rOOQO,5>e,5>eOOQO-E;w-E;wO!,rQpO1G/iO:^QWO,5:iOOQO,5:o,5:oO){QYO,5:oO$9pQ!LYO,5:oO$9{Q!LYO,5:oO!,rQpO,5:iOOQO-E;u-E;uOOQ!LS1G0S1G0SO!?{Q&jO,5:iO$:ZQ&jO,5:iO$:lQ!LrO,5:oO$;WQ&jO,5:iO!?{Q&jO,5:oOOQO,5:t,5:tO$;_Q&jO,5:oO$;lQ!LYO,5:oOOQ!LS7+%k7+%kO#IWQWO7+%kO#I]Q`O7+%kOOQ!LS7+%{7+%{O:^QWO7+%{O!,rQpO7+%{O$<QQ!LdO7+*nO){QYO7+*nOOQO1G4Q1G4QO7VQWO1G4QO$<bQWO7+*mO$<jQ!LdO1G2[O$>lQ!LdO1G2^O$@nQ!LdO1G1yO$BvQ#tO,5>aOOQO-E;s-E;sO$CQQbO,5>bO){QYO,5>bOOQO-E;t-E;tO$C[QWO1G5OO$CdQ7^O1G0bO$EkQ7^O1G0mO$ErQ7^O1G0mO$GsQ7^O1G0mO$GzQ7^O1G0mO$IoQ7^O1G0mO$JSQ7^O1G0mO$LaQ7^O1G0mO$LhQ7^O1G0mO$NiQ7^O1G0mO$NpQ7^O1G0mO%!eQ7^O1G0mO%!xQ!LdO<<JTO%#iQ7^O1G0mO%%XQ7^O'#IoO%'UQ7^O1G1QOLXQYO'#F^OOQO'#JV'#JVOOQO1G1b1G1bO%'cQWO1G1aO%'hQ7^O,5>iOOOO7+'T7+'TOOOS1G/R1G/ROOQ!LS1G4V1G4VOJdQWO7+'zO%'rQWO,5>jO5qQWO,5>jOOQO-E;|-E;|O%(QQWO1G5_O%(QQWO1G5_O%(YQWO1G5_O%(eQ`O,5>lO%(oQWO,5>lOITQWO,5>lOOQO-E<O-E<OO%(tQ`O1G5`O%)OQWO1G5`OOQO1G2S1G2SOOQO1G2T1G2TO5iQ!LYO1G2TO$,[QWO1G2TO5iQ!LYO1G2SO%)WQWO1G2UOITQWO1G2UOOQO1G2V1G2VO5iQ!LYO1G2YO!,rQpO1G2SO#5TQWO1G2TO%)]QWO1G2UO%)eQWO1G2TOJdQWO7+*`OOQ!LS1G/W1G/WO%)pQWO1G/WOOQ!LS7+'`7+'`O%)uQ#tO7+'gO%*VQ!LdO<<JsOOQ!LS<<Js<<JsOITQWO'#ITO%*vQWO,5?uOOQQ<<Kh<<KhOITQWO<<KhO#IWQWO<<KhO%+OQWO<<KhO%+WQ`O<<KhOITQWO1G2WOOQQ<<Gy<<GyO:^QWO<<GyO%+bQ!LdO<<IhOOQ!LS<<Ih<<IhOOQO,5>u,5>uO%,RQWO,5>uO%,WQWO,5>uOOQO-E<X-E<XO%,`QWO1G5gO%,`QWO1G5gO5qQWO1G5gO%,hQWO<<LTOOQQ<<LT<<LTO%,mQWO<<LTO5iQ!LYO<<LTO){QYO<<KhOITQWO<<KhOOQQ<<Ko<<KoO$0{Q!LbO<<KoOOQQ<<Kp<<KpO$1VQ`O<<KpO%,rQ&jO'#IVO%,}QWO,5?xOLXQYO,5?xOOQQ1G2n1G2nO#GWQ!LrO'#ETO!?{Q&jO'#GrOOQO'#IX'#IXO%-VQ&jO,5=]OOQQ,5=],5=]O%-^Q&jO'#ETO%-iQ&jO'#ETO%.QQ&jO'#ETO%.[Q&jO'#GrO%.mQWO7+(bO%.rQWO7+(bO%.zQ`O7+(bOOQQ7+(b7+(bOITQWO7+(bO){QYO7+(bOITQWO7+(bO%/UQWO7+(bOOQQ7+(e7+(eO5iQ!LYO7+(eO#5YQWO7+(eO5_QWO7+(eO!,rQpO7+(eO%/dQWO,5>tOOQO-E<W-E<WOOQO'#Gu'#GuO%/oQWO1G5eO5iQ!LYO<<GpOOQQ<<Gp<<GpO%/wQWO<<GpO%/|QWO7++SO%0RQWO7++TOOQQ7+(m7+(mO%0WQWO7+(mO%0]QYO7+(mO%0dQWO7+(mO){QYO7++SO){QYO7++TOOQQ<<La<<LaOOQQ<<Lc<<LcOOQQ-E<Z-E<ZOOQQ1G3b1G3bO%0iQWO,5=wOOQQ,5=y,5=yO:^QWO<<LlO%0nQWO<<LlOLXQYO7+&ROOQO7+%T7+%TO%0sQ7^O1G5VO:^QWO7+%TOOQO1G0T1G0TO%0}Q!LdO1G0ZOOQO1G0Z1G0ZO){QYO1G0ZO%1XQ!LYO1G0ZO:^QWO1G0TO!,rQpO1G0TO!?{Q&jO1G0TO%1dQ!LYO1G0ZO%1rQ&jO1G0TO%2TQ!LYO1G0ZO%2iQ!LrO1G0ZO%2sQ&jO1G0TO!?{Q&jO1G0ZOOQ!LS<<IV<<IVOOQ!LS<<Ig<<IgO:^QWO<<IgO%2zQ!LdO<<NYOOQO7+)l7+)lO%3[Q!LdO7+'gO%5dQbO1G3|O%5nQ7^O7+%|O%5{Q7^O,59jO%7xQ7^O,5<UO%9uQ7^O,5<WO%;rQ7^O,5<fO%=bQ7^O7+'WO%=oQ7^O7+'XO%=|QWO,5;xOOQO7+&{7+&{O%>RQ#tO<<KfOOQO1G4U1G4UO%>cQWO1G4UO%>nQWO1G4UO%>|QWO7+*yO%>|QWO7+*yOITQWO1G4WO%?UQ`O1G4WO%?`QWO7+*zOOQO7+'o7+'oO5iQ!LYO7+'oOOQO7+'n7+'nO$,[QWO7+'pO%?hQ`O7+'pOOQO7+'t7+'tO5iQ!LYO7+'nO$,[QWO7+'oO%?oQWO7+'pOITQWO7+'pO#5TQWO7+'oO%?tQ#tO<<MzOOQ!LS7+$r7+$rO%@OQ`O,5>oOOQO-E<R-E<RO#IWQWOANASOOQQANASANASOITQWOANASO%@YQ!LbO7+'rOOQQAN=eAN=eO5qQWO1G4aOOQO1G4a1G4aO%@gQWO1G4aO%@lQWO7++RO%@lQWO7++RO5iQ!LYOANAoO%@tQWOANAoOOQQANAoANAoO%@yQWOANASO%ARQ`OANASOOQQANAZANAZOOQQANA[ANA[O%A]QWO,5>qOOQO-E<T-E<TO%AhQ7^O1G5dO#5YQWO,5=^O5_QWO,5=^O!,rQpO,5=^OOQO-E<V-E<VOOQQ1G2w1G2wO$:lQ!LrO,5:oO!?{Q&jO,5=^O%ArQ&jO,5=^O%BTQ&jO,5:oOOQQ<<K|<<K|OITQWO<<K|O%.mQWO<<K|O%B_QWO<<K|O%BgQ`O<<K|O){QYO<<K|OITQWO<<K|OOQQ<<LP<<LPO5iQ!LYO<<LPO#5YQWO<<LPO5_QWO<<LPO%BqQ&jO1G4`O%ByQWO7++POOQQAN=[AN=[O5iQ!LYOAN=[OOQQ<<Nn<<NnOOQQ<<No<<NoOOQQ<<LX<<LXO%CRQWO<<LXO%CWQYO<<LXO%C_QWO<<NnO%CdQWO<<NoOOQQ1G3c1G3cOOQQANBWANBWO:^QWOANBWO%CiQ7^O<<ImOOQO<<Ho<<HoOOQO7+%u7+%uO%0}Q!LdO7+%uO){QYO7+%uOOQO7+%o7+%oO:^QWO7+%oO!,rQpO7+%oO%CsQ!LYO7+%uO!?{Q&jO7+%oO%DOQ!LYO7+%uO%D^Q&jO7+%oO%DoQ!LYO7+%uOOQ!LSAN?RAN?RO%ETQ!LdO<<KfO%G]Q7^O<<JTO%GjQ7^O1G1yO%IYQ7^O1G2[O%KVQ7^O1G2^O%MSQ7^O<<JsO%MaQ7^O<<IhOOQO1G1d1G1dOOQO7+)p7+)pO%MnQWO7+)pO%MyQWO<<NeO%NRQ`O7+)rOOQO<<KZ<<KZO5iQ!LYO<<K[O$,[QWO<<K[OOQO<<KY<<KYO5iQ!LYO<<KZO%N]Q`O<<K[O$,[QWO<<KZOOQQG26nG26nO#IWQWOG26nOOQO7+){7+){O5qQWO7+){O%NdQWO<<NmOOQQG27ZG27ZO5iQ!LYOG27ZOITQWOG26nOLXQYO1G4]O%NlQWO7++OO5iQ!LYO1G2xO#5YQWO1G2xO5_QWO1G2xO!,rQpO1G2xO!?{Q&jO1G2xO%2iQ!LrO1G0ZO%NtQ&jO1G2xO%.mQWOANAhOOQQANAhANAhOITQWOANAhO& VQWOANAhO& _Q`OANAhOOQQANAkANAkO5iQ!LYOANAkO#5YQWOANAkOOQO'#Gv'#GvOOQO7+)z7+)zOOQQG22vG22vOOQQANAsANAsO& iQWOANAsOOQQANDYANDYOOQQANDZANDZO& nQYOG27rOOQO<<Ia<<IaO%0}Q!LdO<<IaOOQO<<IZ<<IZO:^QWO<<IZO){QYO<<IaO!,rQpO<<IZO&%lQ!LYO<<IaO!?{Q&jO<<IZO&%wQ!LYO<<IaO&&VQ7^O7+'gOOQO<<M[<<M[OOQOAN@vAN@vO5iQ!LYOAN@vOOQOAN@uAN@uO$,[QWOAN@vO5iQ!LYOAN@uOOQQLD,YLD,YOOQO<<Mg<<MgOOQQLD,uLD,uO#IWQWOLD,YO&'uQ7^O7+)wOOQO7+(d7+(dO5iQ!LYO7+(dO#5YQWO7+(dO5_QWO7+(dO!,rQpO7+(dO!?{Q&jO7+(dOOQQG27SG27SO%.mQWOG27SOITQWOG27SOOQQG27VG27VO5iQ!LYOG27VOOQQG27_G27_O:^QWOLD-^OOQOAN>{AN>{OOQOAN>uAN>uO%0}Q!LdOAN>{O:^QWOAN>uO){QYOAN>{O!,rQpOAN>uO&(PQ!LYOAN>{O&([Q7^O<<KfOOQOG26bG26bO5iQ!LYOG26bOOQOG26aG26aOOQQ!$( t!$( tOOQO<<LO<<LOO5iQ!LYO<<LOO#5YQWO<<LOO5_QWO<<LOO!,rQpO<<LOOOQQLD,nLD,nO%.mQWOLD,nOOQQLD,qLD,qOOQQ!$(!x!$(!xOOQOG24gG24gOOQOG24aG24aO%0}Q!LdOG24gO:^QWOG24aO){QYOG24gOOQOLD+|LD+|OOQOANAjANAjO5iQ!LYOANAjO#5YQWOANAjO5_QWOANAjOOQQ!$(!Y!$(!YOOQOLD*RLD*ROOQOLD){LD){O%0}Q!LdOLD*ROOQOG27UG27UO5iQ!LYOG27UO#5YQWOG27UOOQO!$'Mm!$'MmOOQOLD,pLD,pO5iQ!LYOLD,pOOQO!$(![!$(![OLXQYO'#DrO&)zQbO'#IrOLXQYO'#DjO&*RQ!LdO'#ChO&*lQbO'#ChO&*|QYO,5:vOLXQYO,5;ROLXQYO,5;ROLXQYO,5;ROLXQYO,5;ROLXQYO,5;ROLXQYO,5;ROLXQYO,5;ROLXQYO,5;ROLXQYO,5;ROLXQYO,5;ROLXQYO,5;ROLXQYO'#H}O&,|QWO,5<TO&.`QWO,5;ROLXQYO,5;fO!(aQWO'#DOO!(aQWO'#DOOITQWO'#FjO&-UQWO'#FjOITQWO'#FlO&-UQWO'#FlOITQWO'#FzO&-UQWO'#FzOLXQYO,5?kO&*|QYO1G0bO&.gQ7^O'#ChOLXQYO1G1lOITQWO,5<pO&-UQWO,5<pOITQWO,5<rO&-UQWO,5<rOITQWO,5<_O&-UQWO,5<_O&*|QYO1G1mOLXQYO7+&iOITQWO1G1{O&-UQWO1G1{O&*|QYO7+'XO&*|QYO7+%|OITQWO7+'zO&-UQWO7+'zO&.qQWO'#E[O&.vQWO'#E[O&/OQWO'#EzO&/TQWO'#EgO&/YQWO'#JPO&/eQWO'#I}O&/pQWO,5:vO&/uQ#tO,5<QO&/|QWO'#FsO&0RQWO'#FsO&0WQWO,5<RO&0`QWO,5:vO&0hQ7^O1G0}O&0oQWO,5<aO&0tQWO,5<aO&0yQWO1G1mO&1OQWO1G0bO&1TQ#tO1G2`O&1[Q#tO1G2`O4QQWO'#FhO5_QWO'#FgOBwQWO'#EZOLXQYO,5;cO!(pQWO'#FuO!(pQWO'#FuOJdQWO,5<tOJdQWO,5<t",stateData:"&2X~O'WOS'XOSTOSUOS~OPTOQTOXyO]cO_hObnOcmOhcOjTOkcOlcOqTOsTOxRO{cO|cO}cO!TSO!_kO!dUO!gTO!hTO!iTO!jTO!kTO!nlO#dsO#tpO#x^O%PqO%RtO%TrO%UrO%XuO%ZvO%^wO%_wO%axO%nzO%t{O%v|O%x}O%z!OO%}!PO&T!QO&X!RO&Z!SO&]!TO&_!UO&a!VO'ZPO'dQO'mYO'zaO~OP[XZ[X_[Xj[Xu[Xv[Xx[X!R[X!a[X!b[X!d[X!j[X!{[X#WdX#[[X#][X#^[X#_[X#`[X#a[X#b[X#c[X#e[X#g[X#i[X#j[X#o[X'U[X'd[X'n[X'u[X'v[X~O!]$lX~P$zOR!WO'S!XO'T!ZO~OPTOQTO]cOb!kOc!jOhcOjTOkcOlcOqTOsTOxRO{cO|cO}cO!T!bO!_kO!dUO!gTO!hTO!iTO!jTO!kTO!n!iO#t!lO#x^O'Z![O'dQO'mYO'zaO~O!Q!`O!R!]O!O'hP!O'rP~P'dO!S!mO~P`OPTOQTO]cOb!kOc!jOhcOjTOkcOlcOqTOsTOxRO{cO|cO}cO!T!bO!_kO!dUO!gTO!hTO!iTO!jTO!kTO!n!iO#t!lO#x^O'Z9YO'dQO'mYO'zaO~OPTOQTO]cOb!kOc!jOhcOjTOkcOlcOqTOsTOxRO{cO|cO}cO!T!bO!_kO!dUO!gTO!hTO!iTO!jTO!kTO!n!iO#t!lO#x^O'dQO'mYO'zaO~O!Q!rO#U!uO#V!rO'Z9ZO!c'oP~P+{O#W!vO~O!]!wO#W!vO~OP#^OZ#dOj#ROu!{Ov!{Ox!|O!R#bO!a#TO!b!yO!d!zO!j#^O#[#PO#]#QO#^#QO#_#QO#`#SO#a#TO#b#TO#c#TO#e#UO#g#WO#i#YO#j#ZO'dQO'n#[O'u!}O'v#OO~O_'fX'U'fX!c'fX!O'fX!T'fX%Q'fX!]'fX~P.jO!{#eO#o#eOP'gXZ'gX_'gXj'gXu'gXv'gXx'gX!R'gX!a'gX!b'gX!d'gX!j'gX#['gX#]'gX#^'gX#_'gX#`'gX#a'gX#b'gX#e'gX#g'gX#i'gX#j'gX'd'gX'n'gX'u'gX'v'gX~O#c'gX'U'gX!O'gX!c'gXn'gX!T'gX%Q'gX!]'gX~P0zO!{#eO~O#z#fO$R#jO~O!T#kO#x^O$U#lO$W#nO~O]#qOh$OOj#rOk#qOl#qOq$POs$QOx#xO!T#yO!_$VO!d#vO#V$WO#t$TO$_$RO$a$SO$d$UO'Z#pO'd#sO'_'aP~O!d$XO~O!]$ZO~O_$[O'U$[O~O'Z$`O~O!d$XO'Z$`O'[$bO'`$cO~Oc$iO!d$XO'Z$`O~O#c#TO~O]$rOu$nO!T$kO!d$mO%R$qO'Z$`O'[$bO^(SP~O!n$sO~Ox$tO!T$uO'Z$`O~Ox$tO!T$uO%Z$yO'Z$`O~O'Z$zO~O#dsO%RtO%TrO%UrO%XuO%ZvO%^wO%_wO~Ob%TOc%SO!n%QO%P%RO%c%PO~P7uOb%WOcmO!T%VO!nlO#dsO%PqO%TrO%UrO%XuO%ZvO%^wO%_wO%axO~O`%ZO!{%^O%R%XO'[$bO~P8tO!d%_O!g%cO~O!d%dO~O!TSO~O_$[O'R%lO'U$[O~O_$[O'R%oO'U$[O~O_$[O'R%qO'U$[O~OR!WO'S!XO'T%uO~OP[XZ[Xj[Xu[Xv[Xx[X!R[X!RdX!a[X!b[X!d[X!j[X!{[X!{dX#WdX#[[X#][X#^[X#_[X#`[X#a[X#b[X#c[X#e[X#g[X#i[X#j[X#o[X'd[X'n[X'u[X'v[X~O!O[X!OdX~P;aO!Q%wO!O&gX!O&lX!R&gX!R&lX~P'dO!R%yO!O'hX~OP#^OZ#dOj#ROu!{Ov!{Ox!|O!R%yO!a#TO!b!yO!d!zO!j#^O#[#PO#]#QO#^#QO#_#QO#`#SO#a#TO#b#TO#c#TO#e#UO#g#WO#i#YO#j#ZO'dQO'n#[O'u!}O'v#OO~O!O'hX~P>^O!O&OO~Ox&RO!W&]O!X&UO!Y&UO'[$bO~O]&SOk&SO!Q&VO'e&PO!S'iP!S'tP~P@aO!O'qX!R'qX!]'qX!c'qX'n'qX~O!{'qX#W#PX!S'qX~PAYO!{&^O!O'sX!R'sX~O!R&_O!O'rX~O!O&bO~O!{#eO~PAYOS&fO!T&cO!o&eO'Z$`O~Oc&kO!d$XO'Z$`O~Ou$nO!d$mO~O!S&lO~P`Ou!{Ov!{Ox!|O!b!yO!d!zO'dQOP!faZ!faj!fa!R!fa!a!fa!j!fa#[!fa#]!fa#^!fa#_!fa#`!fa#a!fa#b!fa#c!fa#e!fa#g!fa#i!fa#j!fa'n!fa'u!fa'v!fa~O_!fa'U!fa!O!fa!c!fan!fa!T!fa%Q!fa!]!fa~PCcO!c&mO~O!]!wO!{&oO'n&nO!R'pX_'pX'U'pX~O!c'pX~PE{O!R&sO!c'oX~O!c&uO~Ox$tO!T$uO#V&vO'Z$`O~OPTOQTO]cOb!kOc!jOhcOjTOkcOlcOqTOsTOxRO{cO|cO}cO!TSO!_kO!dUO!gTO!hTO!iTO!jTO!kTO!n!iO#t!lO#x^O'Z9YO'dQO'mYO'zaO~O]#qOh$OOj#rOk#qOl#qOq$POs9lOx#xO!T#yO!_:oO!d#vO#V9rO#t$TO$_9nO$a9pO$d$UO'Z&zO'd#sO~O#W&|O~O]#qOh$OOj#rOk#qOl#qOq$POs$QOx#xO!T#yO!_$VO!d#vO#V$WO#t$TO$_$RO$a$SO$d$UO'Z&zO'd#sO~O'_'kP~PJdO!Q'QO!c'lP~P){O'e'SO'mYO~OP9VOQ9VO]cOb:mOc!jOhcOj9VOkcOlcOq9VOs9VOxRO{cO|cO}cO!T!bO!_9XO!dUO!g9VO!h9VO!i9VO!j9VO!k9VO!n!iO#t!lO#x^O'Z'bO'dQO'mYO'z:kO~O!d!zO~O!R#bO_$]a'U$]a!c$]a!O$]a!T$]a%Q$]a!]$]a~O#d'iO~PITO!]'kO!T'wX#w'wX#z'wX$R'wX~Ou'lO~P! POu'lO!T'wX#w'wX#z'wX$R'wX~O!T'nO#w'rO#z'mO$R'sO~O!Q'vO~PLXO#z#fO$R'yO~Ou$eXx$eX!b$eX'n$eX'u$eX'v$eX~OSfX!RfX!{fX'_fX'_$eX~P!!iOk'{O~OR'|O'S'}O'T(PO~Ou(ROx(SO'n#[O'u(UO'v(WO~O'_(QO~P!#rO'_(ZO~O]#qOh$OOj#rOk#qOl#qOq$POs9lOx#xO!T#yO!_:oO!d#vO#V9rO#t$TO$_9nO$a9pO$d$UO'd#sO~O!Q(_O'Z([O!c'{P~P!$aO#W(aO~O!Q(eO'Z(bO!O'|P~P!$aO_(nOj(sOx(kO!W(qO!X(jO!Y(jO!d(hO!x(rO$w(mO'[$bO'e(gO~O!S(pO~P!&XO!b!yOu'cXx'cX'n'cX'u'cX'v'cX!R'cX!{'cX~O'_'cX#m'cX~P!'TOS(vO!{(uO!R'bX'_'bX~O!R(wO'_'aX~O'Z(yO~O!d)OO~O'Z&zO~O!d(hO~Ox$tO!Q!rO!T$uO#U!uO#V!rO'Z$`O!c'oP~O!]!wO#W)SO~OP#^OZ#dOj#ROu!{Ov!{Ox!|O!a#TO!b!yO!d!zO!j#^O#[#PO#]#QO#^#QO#_#QO#`#SO#a#TO#b#TO#c#TO#e#UO#g#WO#i#YO#j#ZO'dQO'n#[O'u!}O'v#OO~O_!^a!R!^a'U!^a!O!^a!c!^an!^a!T!^a%Q!^a!]!^a~P!)fOS)[O!T&cO!o)ZO%Q)YO'`$cO~O'Z$zO'_'aP~O!])_O!T'^X_'^X'U'^X~O!d$XO'`$cO~O!d$XO'Z$`O'`$cO~O!]!wO#W&|O~O])jO%R)kO'Z)gO!S(TP~O!R)lO^(SX~O'e'SO~OZ)pO~O^)qO~O!T$kO'Z$`O'[$bO^(SP~Ox$tO!Q)vO!R&_O!T$uO'Z$`O!O'rP~O]&YOk&YO!Q)wO'e'SO!S'tP~O!R)xO_(PX'U(PX~O!{)|O'`$cO~OS*PO!T#yO'`$cO~O!T*RO~Ou*TO!TSO~O!n*YO~Oc*_O~O'Z(yO!S(RP~Oc$iO~O%RtO'Z$zO~P8tOZ*eO^*dO~OPTOQTO]cObnOcmOhcOjTOkcOlcOqTOsTOxRO{cO|cO}cO!_kO!dUO!gTO!hTO!iTO!jTO!kTO!nlO#x^O%PqO'dQO'mYO'zaO~O!T!bO#t!lO'Z9YO~P!1RO^*dO_$[O'U$[O~O_*iO#d*kO%T*kO%U*kO~P){O!d%_O~O%t*pO~O!T*rO~O&U*uO&V*tOP&SaQ&SaX&Sa]&Sa_&Sab&Sac&Sah&Saj&Sak&Sal&Saq&Sas&Sax&Sa{&Sa|&Sa}&Sa!T&Sa!_&Sa!d&Sa!g&Sa!h&Sa!i&Sa!j&Sa!k&Sa!n&Sa#d&Sa#t&Sa#x&Sa%P&Sa%R&Sa%T&Sa%U&Sa%X&Sa%Z&Sa%^&Sa%_&Sa%a&Sa%n&Sa%t&Sa%v&Sa%x&Sa%z&Sa%}&Sa&T&Sa&X&Sa&Z&Sa&]&Sa&_&Sa&a&Sa'Q&Sa'Z&Sa'd&Sa'm&Sa'z&Sa!S&Sa%{&Sa`&Sa&Q&Sa~O'Z*xO~On*{O~O!O&ga!R&ga~P!)fO!Q+PO!O&gX!R&gX~P){O!R%yO!O'ha~O!O'ha~P>^O!R&_O!O'ra~O!RwX!R!ZX!SwX!S!ZX!]wX!]!ZX!d!ZX!{wX'`!ZX~O!]+UO!{+TO!R#TX!R'jX!S#TX!S'jX!]'jX!d'jX'`'jX~O!]+WO!d$XO'`$cO!R!VX!S!VX~O]&QOk&QOx&RO'e(gO~OP9VOQ9VO]cOb:mOc!jOhcOj9VOkcOlcOq9VOs9VOxRO{cO|cO}cO!T!bO!_9XO!dUO!g9VO!h9VO!i9VO!j9VO!k9VO!n!iO#t!lO#x^O'dQO'mYO'z:kO~O'Z9vO~P!;^O!R+[O!S'iX~O!S+^O~O!]+UO!{+TO!R#TX!S#TX~O!R+_O!S'tX~O!S+aO~O]&QOk&QOx&RO'[$bO'e(gO~O!X+bO!Y+bO~P!>[Ox$tO!Q+dO!T$uO'Z$`O!O&lX!R&lX~O_+hO!W+kO!X+gO!Y+gO!r+oO!s+mO!t+nO!u+lO!x+pO'[$bO'e(gO'm+eO~O!S+jO~P!?]OS+uO!T&cO!o+tO~O!{+{O!R'pa!c'pa_'pa'U'pa~O!]!wO~P!@gO!R&sO!c'oa~Ox$tO!Q,OO!T$uO#U,QO#V,OO'Z$`O!R&nX!c&nX~O_#Oi!R#Oi'U#Oi!O#Oi!c#Oin#Oi!T#Oi%Q#Oi!]#Oi~P!)fO#W!za!R!za!c!za!{!za!T!za_!za'U!za!O!za~P!#rO#W'cXP'cXZ'cX_'cXj'cXv'cX!a'cX!d'cX!j'cX#['cX#]'cX#^'cX#_'cX#`'cX#a'cX#b'cX#c'cX#e'cX#g'cX#i'cX#j'cX'U'cX'd'cX!c'cX!O'cX!T'cXn'cX%Q'cX!]'cX~P!'TO!R,ZO'_'kX~P!#rO'_,]O~O!R,^O!c'lX~P!)fO!c,aO~O!O,bO~OP#^Ou!{Ov!{Ox!|O!b!yO!d!zO!j#^O'dQOZ#Zi_#Zij#Zi!R#Zi!a#Zi#]#Zi#^#Zi#_#Zi#`#Zi#a#Zi#b#Zi#c#Zi#e#Zi#g#Zi#i#Zi#j#Zi'U#Zi'n#Zi'u#Zi'v#Zi!O#Zi!c#Zin#Zi!T#Zi%Q#Zi!]#Zi~O#[#Zi~P!EtO#[#PO~P!EtOP#^Ou!{Ov!{Ox!|O!b!yO!d!zO!j#^O#[#PO#]#QO#^#QO#_#QO'dQOZ#Zi_#Zi!R#Zi!a#Zi#`#Zi#a#Zi#b#Zi#c#Zi#e#Zi#g#Zi#i#Zi#j#Zi'U#Zi'n#Zi'u#Zi'v#Zi!O#Zi!c#Zin#Zi!T#Zi%Q#Zi!]#Zi~Oj#Zi~P!H`Oj#RO~P!H`OP#^Oj#ROu!{Ov!{Ox!|O!b!yO!d!zO!j#^O#[#PO#]#QO#^#QO#_#QO#`#SO'dQO_#Zi!R#Zi#e#Zi#g#Zi#i#Zi#j#Zi'U#Zi'n#Zi'u#Zi'v#Zi!O#Zi!c#Zin#Zi!T#Zi%Q#Zi!]#Zi~OZ#Zi!a#Zi#a#Zi#b#Zi#c#Zi~P!JzOZ#dO!a#TO#a#TO#b#TO#c#TO~P!JzOP#^OZ#dOj#ROu!{Ov!{Ox!|O!a#TO!b!yO!d!zO!j#^O#[#PO#]#QO#^#QO#_#QO#`#SO#a#TO#b#TO#c#TO#e#UO'dQO_#Zi!R#Zi#g#Zi#i#Zi#j#Zi'U#Zi'n#Zi'v#Zi!O#Zi!c#Zin#Zi!T#Zi%Q#Zi!]#Zi~O'u#Zi~P!MrO'u!}O~P!MrOP#^OZ#dOj#ROu!{Ov!{Ox!|O!a#TO!b!yO!d!zO!j#^O#[#PO#]#QO#^#QO#_#QO#`#SO#a#TO#b#TO#c#TO#e#UO#g#WO'dQO'u!}O_#Zi!R#Zi#i#Zi#j#Zi'U#Zi'n#Zi!O#Zi!c#Zin#Zi!T#Zi%Q#Zi!]#Zi~O'v#Zi~P#!^O'v#OO~P#!^OP#^OZ#dOj#ROu!{Ov!{Ox!|O!a#TO!b!yO!d!zO!j#^O#[#PO#]#QO#^#QO#_#QO#`#SO#a#TO#b#TO#c#TO#e#UO#g#WO#i#YO'dQO'u!}O'v#OO~O_#Zi!R#Zi#j#Zi'U#Zi'n#Zi!O#Zi!c#Zin#Zi!T#Zi%Q#Zi!]#Zi~P#$xOP[XZ[Xj[Xu[Xv[Xx[X!a[X!b[X!d[X!j[X!{[X#WdX#[[X#][X#^[X#_[X#`[X#a[X#b[X#c[X#e[X#g[X#i[X#j[X#o[X'd[X'n[X'u[X'v[X!R[X!S[X~O#m[X~P#']OP#^OZ9jOj9_Ou!{Ov!{Ox!|O!a9aO!b!yO!d!zO!j#^O#[9]O#]9^O#^9^O#_9^O#`9`O#a9aO#b9aO#c9aO#e9bO#g9dO#i9fO#j9gO'dQO'n#[O'u!}O'v#OO~O#m,dO~P#)gOP'gXZ'gXj'gXu'gXv'gXx'gX!a'gX!b'gX!d'gX!j'gX#['gX#]'gX#^'gX#_'gX#`'gX#a'gX#b'gX#e'gX#g'gX#i'gX#j'gX'd'gX'n'gX'u'gX'v'gX!R'gX~O!{9kO#o9kO#c'gX#m'gX!S'gX~P#+bO_&qa!R&qa'U&qa!c&qan&qa!O&qa!T&qa%Q&qa!]&qa~P!)fOP#ZiZ#Zi_#Zij#Ziv#Zi!R#Zi!a#Zi!b#Zi!d#Zi!j#Zi#[#Zi#]#Zi#^#Zi#_#Zi#`#Zi#a#Zi#b#Zi#c#Zi#e#Zi#g#Zi#i#Zi#j#Zi'U#Zi'd#Zi!O#Zi!c#Zin#Zi!T#Zi%Q#Zi!]#Zi~P!#rO_#ni!R#ni'U#ni!O#ni!c#nin#ni!T#ni%Q#ni!]#ni~P!)fO#z,fO~O#z,gO~O!]'kO!{,hO!T$OX#w$OX#z$OX$R$OX~O!Q,iO~O!T'nO#w,kO#z'mO$R,lO~O!R9hO!S'fX~P#)gO!S,mO~O$R,oO~OR'|O'S'}O'T,rO~O],uOk,uO!O,vO~O!RdX!]dX!cdX!c$eX'ndX~P!!iO!c,|O~P!#rO!R,}O!]!wO'n&nO!c'{X~O!c-SO~O!O$eX!R$eX!]$lX~P!!iO!R-UO!O'|X~P!#rO!]-WO~O!O-YO~O!Q(_O'Z$`O!c'{P~Oj-^O!]!wO!d$XO'`$cO'n&nO~O!])_O~O!S-dO~P!&XO!X-eO!Y-eO'[$bO'e(gO~Ox-gO'e(gO~O!x-hO~O'Z$zO!R&vX'_&vX~O!R(wO'_'aa~Ou-mOv-mOx-nO'nra'ura'vra!Rra!{ra~O'_ra#mra~P#6fOu(ROx(SO'n$^a'u$^a'v$^a!R$^a!{$^a~O'_$^a#m$^a~P#7[Ou(ROx(SO'n$`a'u$`a'v$`a!R$`a!{$`a~O'_$`a#m$`a~P#7}O]-oO~O#W-pO~O'_$na!R$na#m$na!{$na~P!#rO#W-sO~OS-|O!T&cO!o-{O%Q-zO~O'_-}O~O]#qOj#rOk#qOl#qOq$POs9lOx#xO!T#yO!_:oO!d#vO#V9rO#t$TO$_9nO$a9pO$d$UO'd#sO~Oh.PO'Z.OO~P#9tO!])_O!T'^a_'^a'U'^a~O#W.VO~OZ[X!RdX!SdX~O!R.WO!S(TX~O!S.YO~OZ.ZO~O].]O'Z)gO~O!T$kO'Z$`O^'OX!R'OX~O!R)lO^(Sa~O!c.`O~P!)fO].bO~OZ.cO~O^.dO~OS-|O!T&cO!o-{O%Q-zO'`$cO~O!R)xO_(Pa'U(Pa~O!{.jO~OS.mO!T#yO~O'e'SO!S(QP~OS.wO!T.sO!o.vO%Q.uO'`$cO~OZ/RO!R/PO!S(RX~O!S/SO~O^/UO_$[O'U$[O~O]/VO~O]/WO'Z(yO~O#c/XO%r/YO~P0zO!{#eO#c/XO%r/YO~O_/ZO~P){O_/]O~O%{/aOP%yiQ%yiX%yi]%yi_%yib%yic%yih%yij%yik%yil%yiq%yis%yix%yi{%yi|%yi}%yi!T%yi!_%yi!d%yi!g%yi!h%yi!i%yi!j%yi!k%yi!n%yi#d%yi#t%yi#x%yi%P%yi%R%yi%T%yi%U%yi%X%yi%Z%yi%^%yi%_%yi%a%yi%n%yi%t%yi%v%yi%x%yi%z%yi%}%yi&T%yi&X%yi&Z%yi&]%yi&_%yi&a%yi'Q%yi'Z%yi'd%yi'm%yi'z%yi!S%yi`%yi&Q%yi~O`/gO!S/eO&Q/fO~P`O!TSO!d/jO~O!R#bOn$]a~O!O&gi!R&gi~P!)fO!R%yO!O'hi~O!R&_O!O'ri~O!O/nO~O!R!Va!S!Va~P#)gO]&QOk&QO!Q/tO'e(gO!R&hX!S&hX~P@aO!R+[O!S'ia~O]&YOk&YO!Q)wO'e'SO!R&mX!S&mX~O!R+_O!S'ta~O!O'si!R'si~P!)fO_$[O!]!wO!d$XO!j0OO!{/|O'U$[O'`$cO'n&nO~O!S0RO~P!?]O!X0SO!Y0SO'[$bO'e(gO'm+eO~O!W0TO~P#GyO!TSO!W0TO!u0VO!x0WO~P#GyO!W0TO!s0YO!t0YO!u0VO!x0WO~P#GyO!T&cO~O!T&cO~P!#rO!R'pi!c'pi_'pi'U'pi~P!)fO!{0cO!R'pi!c'pi_'pi'U'pi~O!R&sO!c'oi~Ox$tO!T$uO#V0eO'Z$`O~O#WraPraZra_rajra!ara!bra!dra!jra#[ra#]ra#^ra#_ra#`ra#ara#bra#cra#era#gra#ira#jra'Ura'dra!cra!Ora!Tranra%Qra!]ra~P#6fO#W$^aP$^aZ$^a_$^aj$^av$^a!a$^a!b$^a!d$^a!j$^a#[$^a#]$^a#^$^a#_$^a#`$^a#a$^a#b$^a#c$^a#e$^a#g$^a#i$^a#j$^a'U$^a'd$^a!c$^a!O$^a!T$^an$^a%Q$^a!]$^a~P#7[O#W$`aP$`aZ$`a_$`aj$`av$`a!a$`a!b$`a!d$`a!j$`a#[$`a#]$`a#^$`a#_$`a#`$`a#a$`a#b$`a#c$`a#e$`a#g$`a#i$`a#j$`a'U$`a'd$`a!c$`a!O$`a!T$`an$`a%Q$`a!]$`a~P#7}O#W$naP$naZ$na_$naj$nav$na!R$na!a$na!b$na!d$na!j$na#[$na#]$na#^$na#_$na#`$na#a$na#b$na#c$na#e$na#g$na#i$na#j$na'U$na'd$na!c$na!O$na!T$na!{$nan$na%Q$na!]$na~P!#rO_#Oq!R#Oq'U#Oq!O#Oq!c#Oqn#Oq!T#Oq%Q#Oq!]#Oq~P!)fO!R&iX'_&iX~PJdO!R,ZO'_'ka~O!Q0mO!R&jX!c&jX~P){O!R,^O!c'la~O!R,^O!c'la~P!)fO#m!fa!S!fa~PCcO#m!^a!R!^a!S!^a~P#)gO!T1QO#x^O$P1RO~O!S1VO~On1WO~P!#rO_$Yq!R$Yq'U$Yq!O$Yq!c$Yqn$Yq!T$Yq%Q$Yq!]$Yq~P!)fO!O1XO~O],uOk,uO~Ou(ROx(SO'v(WO'n$xi'u$xi!R$xi!{$xi~O'_$xi#m$xi~P$(jOu(ROx(SO'n$zi'u$zi'v$zi!R$zi!{$zi~O'_$zi#m$zi~P$)]O#m1YO~P!#rO!Q1[O'Z$`O!R&rX!c&rX~O!R,}O!c'{a~O!R,}O!]!wO!c'{a~O!R,}O!]!wO'n&nO!c'{a~O'_$gi!R$gi#m$gi!{$gi~P!#rO!Q1cO'Z(bO!O&tX!R&tX~P!$aO!R-UO!O'|a~O!R-UO!O'|a~P!#rO!]!wO~O!]!wO#c1mO~Oj1qO!]!wO'n&nO~O!R'bi'_'bi~P!#rO!{1tO!R'bi'_'bi~P!#rO!c1wO~O_$Zq!R$Zq'U$Zq!O$Zq!c$Zqn$Zq!T$Zq%Q$Zq!]$Zq~P!)fO!R1{O!T'}X~P!#rO!T&cO%Q2OO~O!T&cO%Q2OO~P!#rO!T$eX$u[X_$eX'U$eX~P!!iO$u2SOugXxgX!TgX'ngX'ugX'vgX_gX'UgX~O$u2SO~O]2YO%R2ZO'Z)gO!R&}X!S&}X~O!R.WO!S(Ta~OZ2_O~O^2`O~O]2cO~OS2eO!T&cO!o2dO%Q2OO~O_$[O'U$[O~P!#rO!T#yO~P!#rO!R2jO!{2lO!S(QX~O!S2mO~Ox(kO!W2vO!X2oO!Y2oO!r2uO!s2tO!t2tO!x2sO'[$bO'e(gO'm+eO~O!S2rO~P$1nOS2}O!T.sO!o2|O%Q2{O~OS2}O!T.sO!o2|O%Q2{O'`$cO~O'Z(yO!R&|X!S&|X~O!R/PO!S(Ra~O]3XO'e3WO~O]3YO~O^3[O~O!c3_O~P){O_3aO~O_3aO~P){O#c3cO%r3dO~PE{O`/gO!S3hO&Q/fO~P`O!]3jO~O&V3kOP&SqQ&SqX&Sq]&Sq_&Sqb&Sqc&Sqh&Sqj&Sqk&Sql&Sqq&Sqs&Sqx&Sq{&Sq|&Sq}&Sq!T&Sq!_&Sq!d&Sq!g&Sq!h&Sq!i&Sq!j&Sq!k&Sq!n&Sq#d&Sq#t&Sq#x&Sq%P&Sq%R&Sq%T&Sq%U&Sq%X&Sq%Z&Sq%^&Sq%_&Sq%a&Sq%n&Sq%t&Sq%v&Sq%x&Sq%z&Sq%}&Sq&T&Sq&X&Sq&Z&Sq&]&Sq&_&Sq&a&Sq'Q&Sq'Z&Sq'd&Sq'm&Sq'z&Sq!S&Sq%{&Sq`&Sq&Q&Sq~O!R#Ti!S#Ti~P#)gO!{3mO!R#Ti!S#Ti~O!R!Vi!S!Vi~P#)gO_$[O!{3tO'U$[O~O_$[O!]!wO!{3tO'U$[O~O!X3xO!Y3xO'[$bO'e(gO'm+eO~O_$[O!]!wO!d$XO!j3yO!{3tO'U$[O'`$cO'n&nO~O!W3zO~P$:ZO!W3zO!u3}O!x4OO~P$:ZO_$[O!]!wO!j3yO!{3tO'U$[O'n&nO~O!R'pq!c'pq_'pq'U'pq~P!)fO!R&sO!c'oq~O#W$xiP$xiZ$xi_$xij$xiv$xi!a$xi!b$xi!d$xi!j$xi#[$xi#]$xi#^$xi#_$xi#`$xi#a$xi#b$xi#c$xi#e$xi#g$xi#i$xi#j$xi'U$xi'd$xi!c$xi!O$xi!T$xin$xi%Q$xi!]$xi~P$(jO#W$ziP$ziZ$zi_$zij$ziv$zi!a$zi!b$zi!d$zi!j$zi#[$zi#]$zi#^$zi#_$zi#`$zi#a$zi#b$zi#c$zi#e$zi#g$zi#i$zi#j$zi'U$zi'd$zi!c$zi!O$zi!T$zin$zi%Q$zi!]$zi~P$)]O#W$giP$giZ$gi_$gij$giv$gi!R$gi!a$gi!b$gi!d$gi!j$gi#[$gi#]$gi#^$gi#_$gi#`$gi#a$gi#b$gi#c$gi#e$gi#g$gi#i$gi#j$gi'U$gi'd$gi!c$gi!O$gi!T$gi!{$gin$gi%Q$gi!]$gi~P!#rO!R&ia'_&ia~P!#rO!R&ja!c&ja~P!)fO!R,^O!c'li~O#m#Oi!R#Oi!S#Oi~P#)gOP#^Ou!{Ov!{Ox!|O!b!yO!d!zO!j#^O'dQOZ#Zij#Zi!a#Zi#]#Zi#^#Zi#_#Zi#`#Zi#a#Zi#b#Zi#c#Zi#e#Zi#g#Zi#i#Zi#j#Zi#m#Zi'n#Zi'u#Zi'v#Zi!R#Zi!S#Zi~O#[#Zi~P$CqO#[9]O~P$CqOP#^Ou!{Ov!{Ox!|O!b!yO!d!zO!j#^O#[9]O#]9^O#^9^O#_9^O'dQOZ#Zi!a#Zi#`#Zi#a#Zi#b#Zi#c#Zi#e#Zi#g#Zi#i#Zi#j#Zi#m#Zi'n#Zi'u#Zi'v#Zi!R#Zi!S#Zi~Oj#Zi~P$EyOj9_O~P$EyOP#^Oj9_Ou!{Ov!{Ox!|O!b!yO!d!zO!j#^O#[9]O#]9^O#^9^O#_9^O#`9`O'dQO#e#Zi#g#Zi#i#Zi#j#Zi#m#Zi'n#Zi'u#Zi'v#Zi!R#Zi!S#Zi~OZ#Zi!a#Zi#a#Zi#b#Zi#c#Zi~P$HROZ9jO!a9aO#a9aO#b9aO#c9aO~P$HROP#^OZ9jOj9_Ou!{Ov!{Ox!|O!a9aO!b!yO!d!zO!j#^O#[9]O#]9^O#^9^O#_9^O#`9`O#a9aO#b9aO#c9aO#e9bO'dQO#g#Zi#i#Zi#j#Zi#m#Zi'n#Zi'v#Zi!R#Zi!S#Zi~O'u#Zi~P$JgO'u!}O~P$JgOP#^OZ9jOj9_Ou!{Ov!{Ox!|O!a9aO!b!yO!d!zO!j#^O#[9]O#]9^O#^9^O#_9^O#`9`O#a9aO#b9aO#c9aO#e9bO#g9dO'dQO'u!}O#i#Zi#j#Zi#m#Zi'n#Zi!R#Zi!S#Zi~O'v#Zi~P$LoO'v#OO~P$LoOP#^OZ9jOj9_Ou!{Ov!{Ox!|O!a9aO!b!yO!d!zO!j#^O#[9]O#]9^O#^9^O#_9^O#`9`O#a9aO#b9aO#c9aO#e9bO#g9dO#i9fO'dQO'u!}O'v#OO~O#j#Zi#m#Zi'n#Zi!R#Zi!S#Zi~P$NwO_#ky!R#ky'U#ky!O#ky!c#kyn#ky!T#ky%Q#ky!]#ky~P!)fOP#ZiZ#Zij#Ziv#Zi!a#Zi!b#Zi!d#Zi!j#Zi#[#Zi#]#Zi#^#Zi#_#Zi#`#Zi#a#Zi#b#Zi#c#Zi#e#Zi#g#Zi#i#Zi#j#Zi#m#Zi'd#Zi!R#Zi!S#Zi~P!#rO!b!yOP'cXZ'cXj'cXu'cXv'cXx'cX!a'cX!d'cX!j'cX#['cX#]'cX#^'cX#_'cX#`'cX#a'cX#b'cX#c'cX#e'cX#g'cX#i'cX#j'cX#m'cX'd'cX'n'cX'u'cX'v'cX!R'cX!S'cX~O#m#ni!R#ni!S#ni~P#)gO!S4`O~O!R&qa!S&qa~P#)gO!]!wO'n&nO!R&ra!c&ra~O!R,}O!c'{i~O!R,}O!]!wO!c'{i~O!O&ta!R&ta~P!#rO!]4gO~O!R-UO!O'|i~P!#rO!R-UO!O'|i~O!O4mO~O!]!wO#c4sO~Oj4tO!]!wO'n&nO~O!O4vO~O'_$iq!R$iq#m$iq!{$iq~P!#rO_$Zy!R$Zy'U$Zy!O$Zy!c$Zyn$Zy!T$Zy%Q$Zy!]$Zy~P!)fO!R1{O!T'}a~O!T&cO%Q4{O~O!T&cO%Q4{O~P!#rO_#Oy!R#Oy'U#Oy!O#Oy!c#Oyn#Oy!T#Oy%Q#Oy!]#Oy~P!)fOZ5OO~O]5QO'Z)gO~O!R.WO!S(Ti~O]5TO~O^5UO~O'e'SO!R&yX!S&yX~O!R2jO!S(Qa~O!S5cO~P$1nOx-gO'e(gO'm+eO~O!W5fO!X5eO!Y5eO!x0WO'[$bO'e(gO'm+eO~O!s5gO!t5gO~P%-iO!X5eO!Y5eO'[$bO'e(gO'm+eO~O!T.sO~O!T.sO%Q5iO~O!T.sO%Q5iO~P!#rOS5nO!T.sO!o5mO%Q5iO~OZ5sO!R&|a!S&|a~O!R/PO!S(Ri~O]5vO~O!c5wO~O!c5xO~O!c5yO~O!c5yO~P){O_5{O~O!]6OO~O!c6QO~O!R'si!S'si~P#)gO_$[O'U$[O~P!)fO_$[O!{6VO'U$[O~O_$[O!]!wO!{6VO'U$[O~O!X6[O!Y6[O'[$bO'e(gO'm+eO~O_$[O!]!wO!j6]O!{6VO'U$[O'n&nO~O!d$XO'`$cO~P%2TO!W6^O~P%1rO!R'py!c'py_'py'U'py~P!)fO#W$iqP$iqZ$iq_$iqj$iqv$iq!R$iq!a$iq!b$iq!d$iq!j$iq#[$iq#]$iq#^$iq#_$iq#`$iq#a$iq#b$iq#c$iq#e$iq#g$iq#i$iq#j$iq'U$iq'd$iq!c$iq!O$iq!T$iq!{$iqn$iq%Q$iq!]$iq~P!#rO!R&ji!c&ji~P!)fO#m#Oq!R#Oq!S#Oq~P#)gOu-mOv-mOx-nOPraZrajra!ara!bra!dra!jra#[ra#]ra#^ra#_ra#`ra#ara#bra#cra#era#gra#ira#jra#mra'dra'nra'ura'vra!Rra!Sra~Ou(ROx(SOP$^aZ$^aj$^av$^a!a$^a!b$^a!d$^a!j$^a#[$^a#]$^a#^$^a#_$^a#`$^a#a$^a#b$^a#c$^a#e$^a#g$^a#i$^a#j$^a#m$^a'd$^a'n$^a'u$^a'v$^a!R$^a!S$^a~Ou(ROx(SOP$`aZ$`aj$`av$`a!a$`a!b$`a!d$`a!j$`a#[$`a#]$`a#^$`a#_$`a#`$`a#a$`a#b$`a#c$`a#e$`a#g$`a#i$`a#j$`a#m$`a'd$`a'n$`a'u$`a'v$`a!R$`a!S$`a~OP$naZ$naj$nav$na!a$na!b$na!d$na!j$na#[$na#]$na#^$na#_$na#`$na#a$na#b$na#c$na#e$na#g$na#i$na#j$na#m$na'd$na!R$na!S$na~P!#rO#m$Yq!R$Yq!S$Yq~P#)gO#m$Zq!R$Zq!S$Zq~P#)gO!S6hO~O'_$|y!R$|y#m$|y!{$|y~P!#rO!]!wO!R&ri!c&ri~O!]!wO'n&nO!R&ri!c&ri~O!R,}O!c'{q~O!O&ti!R&ti~P!#rO!R-UO!O'|q~O!O6oO~P!#rO!O6oO~O!R'by'_'by~P!#rO!R&wa!T&wa~P!#rO!T$tq_$tq'U$tq~P!#rOZ6wO~O!R.WO!S(Tq~O]6zO~O!T&cO%Q6{O~O!T&cO%Q6{O~P!#rO!{6|O!R&ya!S&ya~O!R2jO!S(Qi~P#)gO!X7SO!Y7SO'[$bO'e(gO'm+eO~O!W7UO!x4OO~P%ArO!T.sO%Q7XO~O!T.sO%Q7XO~P!#rO]7`O'e7_O~O!R/PO!S(Rq~O!c7bO~O!c7bO~P){O!c7dO~O!c7eO~O!R#Ty!S#Ty~P#)gO_$[O!{7kO'U$[O~O_$[O!]!wO!{7kO'U$[O~O!X7nO!Y7nO'[$bO'e(gO'm+eO~O_$[O!]!wO!j7oO!{7kO'U$[O'n&nO~O#W$|yP$|yZ$|y_$|yj$|yv$|y!R$|y!a$|y!b$|y!d$|y!j$|y#[$|y#]$|y#^$|y#_$|y#`$|y#a$|y#b$|y#c$|y#e$|y#g$|y#i$|y#j$|y'U$|y'd$|y!c$|y!O$|y!T$|y!{$|yn$|y%Q$|y!]$|y~P!#rO#m#ky!R#ky!S#ky~P#)gOP$giZ$gij$giv$gi!a$gi!b$gi!d$gi!j$gi#[$gi#]$gi#^$gi#_$gi#`$gi#a$gi#b$gi#c$gi#e$gi#g$gi#i$gi#j$gi#m$gi'd$gi!R$gi!S$gi~P!#rOu(ROx(SO'v(WOP$xiZ$xij$xiv$xi!a$xi!b$xi!d$xi!j$xi#[$xi#]$xi#^$xi#_$xi#`$xi#a$xi#b$xi#c$xi#e$xi#g$xi#i$xi#j$xi#m$xi'd$xi'n$xi'u$xi!R$xi!S$xi~Ou(ROx(SOP$ziZ$zij$ziv$zi!a$zi!b$zi!d$zi!j$zi#[$zi#]$zi#^$zi#_$zi#`$zi#a$zi#b$zi#c$zi#e$zi#g$zi#i$zi#j$zi#m$zi'd$zi'n$zi'u$zi'v$zi!R$zi!S$zi~O#m$Zy!R$Zy!S$Zy~P#)gO#m#Oy!R#Oy!S#Oy~P#)gO!]!wO!R&rq!c&rq~O!R,}O!c'{y~O!O&tq!R&tq~P!#rO!O7uO~P!#rO!R.WO!S(Ty~O!R2jO!S(Qq~O!X8RO!Y8RO'[$bO'e(gO'm+eO~O!T.sO%Q8UO~O!T.sO%Q8UO~P!#rO!c8XO~O&V8YOP&S!ZQ&S!ZX&S!Z]&S!Z_&S!Zb&S!Zc&S!Zh&S!Zj&S!Zk&S!Zl&S!Zq&S!Zs&S!Zx&S!Z{&S!Z|&S!Z}&S!Z!T&S!Z!_&S!Z!d&S!Z!g&S!Z!h&S!Z!i&S!Z!j&S!Z!k&S!Z!n&S!Z#d&S!Z#t&S!Z#x&S!Z%P&S!Z%R&S!Z%T&S!Z%U&S!Z%X&S!Z%Z&S!Z%^&S!Z%_&S!Z%a&S!Z%n&S!Z%t&S!Z%v&S!Z%x&S!Z%z&S!Z%}&S!Z&T&S!Z&X&S!Z&Z&S!Z&]&S!Z&_&S!Z&a&S!Z'Q&S!Z'Z&S!Z'd&S!Z'm&S!Z'z&S!Z!S&S!Z%{&S!Z`&S!Z&Q&S!Z~O_$[O!{8_O'U$[O~O_$[O!]!wO!{8_O'U$[O~OP$iqZ$iqj$iqv$iq!a$iq!b$iq!d$iq!j$iq#[$iq#]$iq#^$iq#_$iq#`$iq#a$iq#b$iq#c$iq#e$iq#g$iq#i$iq#j$iq#m$iq'd$iq!R$iq!S$iq~P!#rO!R&yq!S&yq~P#)gO_$[O!{8tO'U$[O~OP$|yZ$|yj$|yv$|y!a$|y!b$|y!d$|y!j$|y#[$|y#]$|y#^$|y#_$|y#`$|y#a$|y#b$|y#c$|y#e$|y#g$|y#i$|y#j$|y#m$|y'd$|y!R$|y!S$|y~P!#rOn'fX~P.jOn[X!O[X!c[X%r[X!T[X%Q[X!][X~P$zO!]dX!c[X!cdX'ndX~P;aOP9VOQ9VO]cOb:mOc!jOhcOj9VOkcOlcOq9VOs9VOxRO{cO|cO}cO!TSO!_9XO!dUO!g9VO!h9VO!i9VO!j9VO!k9VO!n!iO#t!lO#x^O'Z'bO'dQO'mYO'z:kO~O!R9hO!S$]a~O]#qOh$OOj#rOk#qOl#qOq$POs9mOx#xO!T#yO!_:pO!d#vO#V9sO#t$TO$_9oO$a9qO$d$UO'Z&zO'd#sO~O#d'iO~P&-UO!S[X!SdX~P;aO#W9[O~O!]!wO#W9[O~O!{9kO~O#c9aO~O!{9tO!R'sX!S'sX~O!{9kO!R'qX!S'qX~O#W9uO~O'_9wO~P!#rO#W9|O~O#W9}O~O!]!wO#W:OO~O!]!wO#W9uO~O#m:PO~P#)gO#W:QO~O#W:RO~O#W:SO~O#W:TO~O#m:UO~P!#rO#m:VO~P!#rO#x~!b!r!t!u#U#V'z$_$a$d$u%P%Q%R%X%Z%^%_%a%c~UT#x'z#]}'W'X#z'W'Z'e~",goto:"#Ed(XPPPPPPPP(YP(jP*^PPPP-uPP.[3n5b5uP5uPPP5uP7c5uP5uP7gPP7lP8Q<cPPPP<gPPPP<g?XPPP?_AjP<gPDTPPPPE{<gPPPPPGt<gPPJuKrPPPPKvM`PMhNiPKr<g<g!#p!&k!+^!+^!.mPPP!.t!1j<gPPPPPPPPPP!4aP!5rPP<g!7PP<gP<g<g<g<gP<g!9dPP!<]P!?Q!?Y!?^!?^P!<YP!?b!?bP!BVP!BZ<g<g!Ba!ET5uP5uP5u5uP!FW5u5u!HO5u!JQ5u!Kr5u5u!L`!NY!NY!N^!NY!NfP!NYP5u# b5u#!l5u5u-uPPP##yPP#$c#$cP#$cP#$x#$cPP#%OP#$uP#$u#%bMd#$u#&P#&V#&Y(Y#&](YP#&d#&d#&dP(YP(YP(YP(YPP(YP#&j#&mP#&m(YPPP(YP(YP(YP(YP(YP(Y(Y#&q#&{#'R#'X#'g#'m#'s#'}#(T#(d#(j#(x#)O#)U#)d#)y#+]#+k#+q#+w#+}#,T#,_#,e#,k#,u#-X#-_PPPPPPPP#-ePP#.X#2VPP#3m#3t#3|PP#8Y#:m#@i#@l#@o#@z#@}PP#AQ#AU#As#Bj#Bn#CSPP#CW#C^#CbP#Ce#Ci#Cl#D[#Dr#Dw#Dz#D}#ET#EW#E[#E`mhOSj}!n$Z%b%e%f%h*m*r/a/dQ$hmQ$opQ%YyS&U!b+[Q&j!jS(j#y(oQ)e$iQ)r$qQ*^%SQ+b&]S+g&c+iQ+y&kQ-e(qQ/O*_Y0S+k+l+m+n+oS2o.s2qU3x0T0V0YU5e2t2u2vS6[3z3}S7S5f5gQ7n6^R8R7U$p[ORSTUjk}!S!W!]!`!n!v!z!|#P#Q#R#S#T#U#V#W#X#Y#Z#b#e$Z$m%Z%^%b%d%e%f%h%l%w%y&R&^&e&o&|'Q(Q)S)Z*i*m*r+P+t+{,^,d-n-s-{.V.v/X/Y/Z/]/a/d/f/|0c0m2d2|3a3c3d3t5m5{6V7k8_8t!j'd#]#k&V'v+T+W,i/t1Q2l3m6|9V9X9[9]9^9_9`9a9b9c9d9e9f9g9h9k9t9u9w:O:P:S:T:nQ(z$QQ)j$kQ*`%VQ*g%_Q,T9lQ.Q)_Q.])kQ/W*eQ2Y.WQ3U/PQ4X9mR5Q2ZpeOSjy}!n$Z%X%b%e%f%h*m*r/a/dR*b%Z&WVOSTjkn}!S!W!k!n!v!z!|#P#Q#R#S#T#U#V#W#X#Y#Z#]#b#e#k$Z$m%Z%^%_%b%d%e%f%h%l%y&R&^&e&o&|'Q'v(Q)S)Z*i*m*r+P+T+W+t+{,^,d,i-n-s-{.V.v/X/Y/Z/]/a/d/f/t/|0c0m1Q2d2l2|3a3c3d3m3t5m5{6V6|7k8_8t9V9X9[9]9^9_9`9a9b9c9d9e9f9g9h9k9t9u9w:O:P:S:T:m:n[!cRU!]!`%w&VQ$alQ$gmS$lp$qv$vrs!r!u$X$t&_&s&v)v)w)x*k+U+d,O,Q/j0eQ%OwQ&g!iQ&i!jS(^#v(hS)d$h$iQ)h$kQ)u$sQ*X%QQ*]%SS+x&j&kQ-R(_Q.U)eQ.[)kQ.^)lQ.a)pQ.y*YS.}*^*_Q0a+yQ1Z,}Q2X.WQ2].ZQ2b.cQ3T/OQ4d1[Q5P2ZQ5S2_Q6v5OR7x6w!Y$em!j$g$h$i&T&i&j&k(i)d)e+X+f+x+y-_.U/y0P0U0a1p3w3|6Y7l8`Q)]$aQ)}${Q*Q$|Q*[%SQ.e)uQ.x*XU.|*]*^*_Q3O.yS3S.}/OQ5`2nQ5r3TS7Q5a5dS8P7R7TQ8j8QR8y8kW#|a$c(w:kS${t%XQ$|uQ$}vR){$y$V#{a!w!y#d#v#x$R$S$W&f'|(V(X(Y(a(e(u(v)Y)[)_)|*P+u,Z-U-W-p-z-|.j.m.u.w1Y1c1m1t1{2O2S2e2{2}4g4s4{5i5n6{7X8U9j9n9o9p9q9r9s9x9y9z9{9|9}:Q:R:U:V:k:q:rT'}#s(OV({$Q9l9mU&Y!b$u+_Q'T!{Q)o$nQ.n*RQ1u-mR5[2j&^cORSTUjk}!S!W!]!`!n!v!z!|#P#Q#R#S#T#U#V#W#X#Y#Z#]#b#e#k$Z$m%Z%^%_%b%d%e%f%h%l%w%y&R&V&^&e&o&|'Q'v(Q)S)Z*i*m*r+P+T+W+t+{,^,d,i-n-s-{.V.v/X/Y/Z/]/a/d/f/t/|0c0m1Q2d2l2|3a3c3d3m3t5m5{6V6|7k8_8t9V9X9[9]9^9_9`9a9b9c9d9e9f9g9h9k9t9u9w:O:P:S:T:n$]#aZ!_!o$_%v%|&x'P'V'W'X'Y'Z'[']'^'_'`'a'c'f'j't)n*}+Y+c+z,Y,`,c,e,s-q/o/r0b0l0p0q0r0s0t0u0v0w0x0y0z0{0|1P1U1y2V3o3r4S4V4W4]4^5^6R6U6b6f6g7h7{8]8r8}9W:dT!XQ!Y&_cORSTUjk}!S!W!]!`!n!v!z!|#P#Q#R#S#T#U#V#W#X#Y#Z#]#b#e#k$Z$m%Z%^%_%b%d%e%f%h%l%w%y&R&V&^&e&o&|'Q'v(Q)S)Z*i*m*r+P+T+W+t+{,^,d,i-n-s-{.V.v/X/Y/Z/]/a/d/f/t/|0c0m1Q2d2l2|3a3c3d3m3t5m5{6V6|7k8_8t9V9X9[9]9^9_9`9a9b9c9d9e9f9g9h9k9t9u9w:O:P:S:T:nQ&W!bR/u+[Y&Q!b&U&]+[+bS(i#y(oS+f&c+iS-_(j(qQ-`(kQ-f(rQ.p*TU0P+g+k+lU0U+m+n+oS0Z+p2sQ1p-eQ1r-gQ1s-hS2n.s2qU3w0S0T0VQ3{0WQ3|0YS5a2o2vS5d2t2uU6Y3x3z3}Q6_4OS7R5e5fQ7T5gS7l6[6^S8Q7S7UQ8`7nR8k8RlhOSj}!n$Z%b%e%f%h*m*r/a/dQ%j!QS&w!v9[Q)b$fQ*V%OQ*W%PQ+v&hS,X&|9uS-r)S:OQ.S)cQ.r*UQ/h*tQ/i*uQ/q+VQ0X+mQ0_+wS1z-s:SQ2T.TS2W.V:TQ3n/sQ3q/zQ4Q0`Q4}2UQ6P3kQ6S3pQ6W3vQ6`4RQ7f6QQ7i6XQ8[7jQ8o8YQ8q8^R8|8s$W#`Z!_!o%v%|&x'P'V'W'X'Y'Z'[']'^'_'`'a'c'f'j't)n*}+Y+c+z,Y,`,c,s-q/o/r0b0l0p0q0r0s0t0u0v0w0x0y0z0{0|1P1U1y2V3o3r4S4V4W4]4^5^6R6U6b6f6g7h7{8]8r8}9W:dU(t#z&{1OT)W$_,e$W#_Z!_!o%v%|&x'P'V'W'X'Y'Z'[']'^'_'`'a'c'f'j't)n*}+Y+c+z,Y,`,c,s-q/o/r0b0l0p0q0r0s0t0u0v0w0x0y0z0{0|1P1U1y2V3o3r4S4V4W4]4^5^6R6U6b6f6g7h7{8]8r8}9W:dQ'e#`S)V$_,eR-t)W&^cORSTUjk}!S!W!]!`!n!v!z!|#P#Q#R#S#T#U#V#W#X#Y#Z#]#b#e#k$Z$m%Z%^%_%b%d%e%f%h%l%w%y&R&V&^&e&o&|'Q'v(Q)S)Z*i*m*r+P+T+W+t+{,^,d,i-n-s-{.V.v/X/Y/Z/]/a/d/f/t/|0c0m1Q2d2l2|3a3c3d3m3t5m5{6V6|7k8_8t9V9X9[9]9^9_9`9a9b9c9d9e9f9g9h9k9t9u9w:O:P:S:T:nQ%e{Q%f|Q%h!OQ%i!PR/`*pQ&d!iQ)X$aQ+s&gS-y)])uS0[+q+rW1}-v-w-x.eS4P0]0^U4z2P2Q2RU6t4y5W5XQ7w6uR8f7zT+h&c+iS+f&c+iU0P+g+k+lU0U+m+n+oS0Z+p2sS2n.s2qU3w0S0T0VQ3{0WQ3|0YS5a2o2vS5d2t2uU6Y3x3z3}Q6_4OS7R5e5fQ7T5gS7l6[6^S8Q7S7UQ8`7nR8k8RS+h&c+iT2p.s2qS&q!q/^Q-Q(^Q-](iS0O+f2nQ1`-RS1j-^-fU3y0U0Z5dQ4c1ZS4q1q1sU6]3{3|7TQ6j4dQ6s4tR7o6_Q!xXS&p!q/^Q)T$YQ)`$dQ)f$jQ+|&qQ-P(^Q-[(iQ-a(lQ.R)aQ.z*ZS/}+f2nS1_-Q-RS1i-]-fQ1l-`Q1o-bQ3Q.{W3u0O0U0Z5dQ4b1ZQ4f1`S4k1j1sQ4r1rQ5p3RW6Z3y3{3|7TS6i4c4dQ6n4mQ6q4qQ7O5_Q7]5qS7m6]6_Q7q6jQ7s6oQ7v6sQ7}7PQ8W7^Q8a7oQ8d7uQ8h8OQ8w8iQ9P8xQ9T9QQ:^:XQ:g:bR:h:c$rWORSTUjk}!S!W!]!`!n!v!z!|#P#Q#R#S#T#U#V#W#X#Y#Z#b#e$Z$m%Z%^%_%b%d%e%f%h%l%w%y&R&^&e&o&|'Q(Q)S)Z*i*m*r+P+t+{,^,d-n-s-{.V.v/X/Y/Z/]/a/d/f/|0c0m2d2|3a3c3d3t5m5{6V7k8_8tS!xn!k!j:W#]#k&V'v+T+W,i/t1Q2l3m6|9V9X9[9]9^9_9`9a9b9c9d9e9f9g9h9k9t9u9w:O:P:S:T:nR:^:m$rXORSTUjk}!S!W!]!`!n!v!z!|#P#Q#R#S#T#U#V#W#X#Y#Z#b#e$Z$m%Z%^%_%b%d%e%f%h%l%w%y&R&^&e&o&|'Q(Q)S)Z*i*m*r+P+t+{,^,d-n-s-{.V.v/X/Y/Z/]/a/d/f/|0c0m2d2|3a3c3d3t5m5{6V7k8_8tQ$Yb!Y$dm!j$g$h$i&T&i&j&k(i)d)e+X+f+x+y-_.U/y0P0U0a1p3w3|6Y7l8`S$jn!kQ)a$eQ*Z%SW.{*[*]*^*_U3R.|.}/OQ5_2nS5q3S3TU7P5`5a5dQ7^5rU8O7Q7R7TS8i8P8QS8x8j8kQ9Q8y!j:X#]#k&V'v+T+W,i/t1Q2l3m6|9V9X9[9]9^9_9`9a9b9c9d9e9f9g9h9k9t9u9w:O:P:S:T:nQ:b:lR:c:m$f]OSTjk}!S!W!n!v!z!|#P#Q#R#S#T#U#V#W#X#Y#Z#b#e$Z$m%Z%^%b%d%e%f%h%l%y&R&^&e&o&|'Q(Q)S)Z*i*m*r+P+t+{,^,d-n-s-{.V.v/X/Y/Z/]/a/d/f/|0c0m2d2|3a3c3d3t5m5{6V7k8_8tY!hRU!]!`%wv$vrs!r!u$X$t&_&s&v)v)w)x*k+U+d,O,Q/j0eQ*h%_!h:Y#]#k'v+T+W,i/t1Q2l3m6|9V9X9[9]9^9_9`9a9b9c9d9e9f9g9h9k9t9u9w:O:P:S:T:nR:]&VS&Z!b$uR/w+_$p[ORSTUjk}!S!W!]!`!n!v!z!|#P#Q#R#S#T#U#V#W#X#Y#Z#b#e$Z$m%Z%^%b%d%e%f%h%l%w%y&R&^&e&o&|'Q(Q)S)Z*i*m*r+P+t+{,^,d-n-s-{.V.v/X/Y/Z/]/a/d/f/|0c0m2d2|3a3c3d3t5m5{6V7k8_8t!j'd#]#k&V'v+T+W,i/t1Q2l3m6|9V9X9[9]9^9_9`9a9b9c9d9e9f9g9h9k9t9u9w:O:P:S:T:nR*g%_$roORSTUjk}!S!W!]!`!n!v!z!|#P#Q#R#S#T#U#V#W#X#Y#Z#b#e$Z$m%Z%^%_%b%d%e%f%h%l%w%y&R&^&e&o&|'Q(Q)S)Z*i*m*r+P+t+{,^,d-n-s-{.V.v/X/Y/Z/]/a/d/f/|0c0m2d2|3a3c3d3t5m5{6V7k8_8tQ'T!{!k:Z#]#k&V'v+T+W,i/t1Q2l3m6|9V9X9[9]9^9_9`9a9b9c9d9e9f9g9h9k9t9u9w:O:P:S:T:n!h#VZ!_$_%v%|&x'P'^'_'`'a'f'j)n*}+c+z,Y,`,s-q0b0l0|1y2V3r4S4V6U7h8]8r8}9W!R9c'c't+Y,e/o/r0p0x0y0z0{1P1U3o4W4]4^5^6R6b6f6g7{:d!d#XZ!_$_%v%|&x'P'`'a'f'j)n*}+c+z,Y,`,s-q0b0l0|1y2V3r4S4V6U7h8]8r8}9W}9e'c't+Y,e/o/r0p0z0{1P1U3o4W4]4^5^6R6b6f6g7{:d!`#]Z!_$_%v%|&x'P'f'j)n*}+c+z,Y,`,s-q0b0l0|1y2V3r4S4V6U7h8]8r8}9Wl(Y#t&})R,{-T-i-j0j1x4a4u:_:i:jx:n'c't+Y,e/o/r0p1P1U3o4W4]4^5^6R6b6f6g7{:d!`:q&y'h(](c+r,W,p-X-u-x.i.k0^0i1a1e2R2g2i2y4U4h4n4w4|5X5l6a6l6r7ZZ:r0}4[6c7p8b&^cORSTUjk}!S!W!]!`!n!v!z!|#P#Q#R#S#T#U#V#W#X#Y#Z#]#b#e#k$Z$m%Z%^%_%b%d%e%f%h%l%w%y&R&V&^&e&o&|'Q'v(Q)S)Z*i*m*r+P+T+W+t+{,^,d,i-n-s-{.V.v/X/Y/Z/]/a/d/f/t/|0c0m1Q2d2l2|3a3c3d3m3t5m5{6V6|7k8_8t9V9X9[9]9^9_9`9a9b9c9d9e9f9g9h9k9t9u9w:O:P:S:T:nS#l`#mR1R,h&e_ORSTU`jk}!S!W!]!`!n!v!z!|#P#Q#R#S#T#U#V#W#X#Y#Z#]#b#e#k#m$Z$m%Z%^%_%b%d%e%f%h%l%w%y&R&V&^&e&o&|'Q'v(Q)S)Z*i*m*r+P+T+W+t+{,^,d,h,i-n-s-{.V.v/X/Y/Z/]/a/d/f/t/|0c0m1Q2d2l2|3a3c3d3m3t5m5{6V6|7k8_8t9V9X9[9]9^9_9`9a9b9c9d9e9f9g9h9k9t9u9w:O:P:S:T:nS#g^#nT'm#i'qT#h^#nT'o#i'q&e`ORSTU`jk}!S!W!]!`!n!v!z!|#P#Q#R#S#T#U#V#W#X#Y#Z#]#b#e#k#m$Z$m%Z%^%_%b%d%e%f%h%l%w%y&R&V&^&e&o&|'Q'v(Q)S)Z*i*m*r+P+T+W+t+{,^,d,h,i-n-s-{.V.v/X/Y/Z/]/a/d/f/t/|0c0m1Q2d2l2|3a3c3d3m3t5m5{6V6|7k8_8t9V9X9[9]9^9_9`9a9b9c9d9e9f9g9h9k9t9u9w:O:P:S:T:nT#l`#mQ#o`R'x#m$rbORSTUjk}!S!W!]!`!n!v!z!|#P#Q#R#S#T#U#V#W#X#Y#Z#b#e$Z$m%Z%^%_%b%d%e%f%h%l%w%y&R&^&e&o&|'Q(Q)S)Z*i*m*r+P+t+{,^,d-n-s-{.V.v/X/Y/Z/]/a/d/f/|0c0m2d2|3a3c3d3t5m5{6V7k8_8t!k:l#]#k&V'v+T+W,i/t1Q2l3m6|9V9X9[9]9^9_9`9a9b9c9d9e9f9g9h9k9t9u9w:O:P:S:T:n#RdOSUj}!S!W!n!|#k$Z%Z%^%_%b%d%e%f%h%l&R&e'v)Z*i*m*r+t,i-n-{.v/X/Y/Z/]/a/d/f1Q2d2|3a3c3d5m5{t#za!y$R$S$W(V(X(Y(a(u(v,Z-p1Y1t:k:q:r!|&{!w#d#v#x&f'|(e)Y)[)_)|*P+u-U-W-z-|.j.m.u.w1c1m1{2O2S2e2{2}4g4s4{5i5n6{7X8U9n9p9r9x9z9|:Q:UQ)P$UQ,t(Rc1O9j9o9q9s9y9{9}:R:Vt#wa!y$R$S$W(V(X(Y(a(u(v,Z-p1Y1t:k:q:rS(l#y(oQ)Q$VQ-b(m!|:`!w#d#v#x&f'|(e)Y)[)_)|*P+u-U-W-z-|.j.m.u.w1c1m1{2O2S2e2{2}4g4s4{5i5n6{7X8U9n9p9r9x9z9|:Q:Ub:a9j9o9q9s9y9{9}:R:VQ:e:oR:f:pt#za!y$R$S$W(V(X(Y(a(u(v,Z-p1Y1t:k:q:r!|&{!w#d#v#x&f'|(e)Y)[)_)|*P+u-U-W-z-|.j.m.u.w1c1m1{2O2S2e2{2}4g4s4{5i5n6{7X8U9n9p9r9x9z9|:Q:Uc1O9j9o9q9s9y9{9}:R:VlfOSj}!n$Z%b%e%f%h*m*r/a/dQ(d#xQ*y%oQ*z%qR1b-U$U#{a!w!y#d#v#x$R$S$W&f'|(V(X(Y(a(e(u(v)Y)[)_)|*P+u,Z-U-W-p-z-|.j.m.u.w1Y1c1m1t1{2O2S2e2{2}4g4s4{5i5n6{7X8U9j9n9o9p9q9r9s9x9y9z9{9|9}:Q:R:U:V:k:q:rQ*O$|Q.l*QQ2h.kR5Z2iT(n#y(oS(n#y(oT2p.s2qQ)`$dQ-a(lQ.R)aQ.z*ZQ3Q.{Q5p3RQ7O5_Q7]5qQ7}7PQ8W7^Q8h8OQ8w8iQ9P8xR9T9Ql(V#t&})R,{-T-i-j0j1x4a4u:_:i:j!`9x&y'h(](c+r,W,p-X-u-x.i.k0^0i1a1e2R2g2i2y4U4h4n4w4|5X5l6a6l6r7ZZ9y0}4[6c7p8bn(X#t&})R,y,{-T-i-j0j1x4a4u:_:i:j!b9z&y'h(](c+r,W,p-X-u-x.i.k0^0g0i1a1e2R2g2i2y4U4h4n4w4|5X5l6a6l6r7Z]9{0}4[6c6d7p8bpeOSjy}!n$Z%X%b%e%f%h*m*r/a/dQ%UxR*i%_peOSjy}!n$Z%X%b%e%f%h*m*r/a/dR%UxQ*S$}R.h){qeOSjy}!n$Z%X%b%e%f%h*m*r/a/dQ.t*XS2z.x.yW5h2w2x2y3OU7W5j5k5lU8S7V7Y7ZQ8l8TR8z8mQ%]yR*c%XR3X/RR7`5sS$lp$qR.^)lQ%bzR*m%cR*s%iT/b*r/dQjOQ!nST$^j!nQ(O#sR,q(OQ!YQR%t!YQ!^RU%z!^%{+QQ%{!_R+Q%|Q+]&WR/v+]Q,[&}R0k,[Q,_'PS0n,_0oR0o,`Q+i&cR0Q+iS!eR$tU&`!e&a+RQ&a!fR+R%}Q+`&ZR/x+`Q&t!sQ+}&rU,R&t+}0fR0f,SQ'q#iR,j'qQ#m`R'w#mQ#cZU'g#c*|9iQ*|9WR9i'tQ-O(^W1]-O1^4e6kU1^-P-Q-RS4e1_1`R6k4f#q(T#t&y&}'h(](c(|(})R+r,U,V,W,p,y,z,{-T-X-i-j-u-x.i.k0^0g0h0i0j0}1a1e1x2R2g2i2y4U4Y4Z4[4a4h4n4u4w4|5X5l6a6c6d6e6l6r7Z7p8b:_:i:jQ-V(cU1d-V1f4iQ1f-XR4i1eQ(o#yR-c(oQ(x#}R-l(xQ1|-uR4x1|Q)y$wR.g)yQ2k.nS5]2k6}R6}5^Q*U%OR.q*UQ2q.sR5b2qQ/Q*`S3V/Q5tR5t3XQ.X)hW2[.X2^5R6xQ2^.[Q5R2]R6x5SQ)m$lR._)mQ/d*rR3g/dWiOSj!nQ%g}Q)U$ZQ*l%bQ*n%eQ*o%fQ*q%hQ/_*mS/b*r/dR3f/aQ$]gQ%k!RQ%n!TQ%p!UQ%r!VQ)t$rQ)z$xQ*b%]Q*w%mS/T*c*fQ/k*vQ/l*yQ/m*zS/{+f2nQ1g-ZQ1h-[Q1n-aQ2a.bQ2f.iQ3P.zQ3Z/VQ3e/`Y3s/}0O0U0Z5dQ4j1iQ4l1kQ4o1oQ5V2cQ5Y2gQ5o3QQ5u3Y[6T3r3u3y3{3|7TQ6m4kQ6p4pQ6y5TQ7[5pQ7a5vW7g6U6Z6]6_Q7r6nQ7t6qQ7y6zQ7|7OQ8V7]U8Z7h7m7oQ8c7sQ8e7vQ8g7}Q8n8WS8p8]8aQ8u8dQ8v8hQ8{8rQ9O8wQ9R8}Q9S9PR9U9TQ$fmQ&h!jU)c$g$h$iQ+V&TU+w&i&j&kQ-Z(iS.T)d)eQ/s+XQ/z+fS0`+x+yQ1k-_Q2U.UQ3p/yS3v0P0UQ4R0aQ4p1pS6X3w3|Q7j6YQ8^7lR8s8`S#ua:kR)^$cU#}a$c:kR-k(wQ#taS&y!w)_Q&}!yQ'h#dQ(]#vQ(c#xQ(|$RQ(}$SQ)R$WQ+r&fQ,U9nQ,V9pQ,W9rQ,p'|Q,y(VQ,z(XQ,{(YQ-T(aQ-X(eQ-i(uQ-j(vd-u)Y-z.u2O2{4{5i6{7X8UQ-x)[Q.i)|Q.k*PQ0^+uQ0g9xQ0h9zQ0i9|Q0j,ZQ0}9jQ1a-UQ1e-WQ1x-pQ2R-|Q2g.jQ2i.mQ2y.wQ4U:QQ4Y9oQ4Z9qQ4[9sQ4a1YQ4h1cQ4n1mQ4u1tQ4w1{Q4|2SQ5X2eQ5l2}Q6a:UQ6c9}Q6d9yQ6e9{Q6l4gQ6r4sQ7Z5nQ7p:RQ8b:VQ:_:kQ:i:qR:j:rlgOSj}!n$Z%b%e%f%h*m*r/a/dS!pU%dQ%m!SQ%s!WQ'U!|Q'u#kS*f%Z%^Q*j%_Q*v%lQ+S&RQ+q&eQ,n'vQ-w)ZQ/[*iQ0]+tQ1T,iQ1v-nQ2Q-{Q2x.vQ3]/XQ3^/YQ3`/ZQ3b/]Q3i/fQ4_1QQ5W2dQ5k2|Q5z3aQ5|3cQ5}3dQ7Y5mR7c5{!vZOSUj}!S!n!|$Z%Z%^%_%b%d%e%f%h%l&R&e)Z*i*m*r+t-n-{.v/X/Y/Z/]/a/d/f2d2|3a3c3d5m5{Q!_RQ!oTQ$_kS%v!]%yQ%|!`Q&x!vQ'P!zQ'V#PQ'W#QQ'X#RQ'Y#SQ'Z#TQ'[#UQ']#VQ'^#WQ'_#XQ'`#YQ'a#ZQ'c#]Q'f#bQ'j#eW't#k'v,i1QQ)n$mS*}%w+PS+Y&V/tQ+c&^Q+z&oQ,Y&|Q,`'QQ,c9VQ,e9XQ,s(QQ-q)SQ/o+TQ/r+WQ0b+{Q0l,^Q0p9[Q0q9]Q0r9^Q0s9_Q0t9`Q0u9aQ0v9bQ0w9cQ0x9dQ0y9eQ0z9fQ0{9gQ0|,dQ1P9kQ1U9hQ1y-sQ2V.VQ3o9tQ3r/|Q4S0cQ4V0mQ4W9uQ4]9wQ4^:OQ5^2lQ6R3mQ6U3tQ6b:PQ6f:SQ6g:TQ7h6VQ7{6|Q8]7kQ8r8_Q8}8tQ9W!WR:d:nR!aRR&X!bS&T!b+[S+X&U&]R/y+bR'O!yR'R!zT!tU$XS!sU$XU$wrs*kS&r!r!uQ,P&sQ,S&vQ.f)xS0d,O,QR4T0e`!dR!]!`$t%w&_)v+dh!qUrs!r!u$X&s&v)x,O,Q0eQ/^*kQ/p+UQ3l/jT:[&V)wT!gR$tS!fR$tS%x!]&_S%}!`)vS+O%w+dT+Z&V)wT&[!b$uQ#i^R'z#nT'p#i'qR1S,hT(`#v(hR(f#xQ-v)YQ2P-zQ2w.uQ4y2OQ5j2{Q6u4{Q7V5iQ7z6{Q8T7XR8m8UlhOSj}!n$Z%b%e%f%h*m*r/a/dQ%[yR*b%XV$xrs*kR.o*RR*a%VQ$ppR)s$qR)i$kT%`z%cT%az%cT/c*r/d",nodeNames:"⚠ ArithOp ArithOp InterpolationStart extends LineComment BlockComment Script ExportDeclaration export Star as VariableName String from ; default FunctionDeclaration async function VariableDefinition TypeParamList TypeDefinition ThisType this LiteralType ArithOp Number BooleanLiteral TemplateType InterpolationEnd Interpolation VoidType void TypeofType typeof MemberExpression . ?. PropertyName [ TemplateString Interpolation null super RegExp ] ArrayExpression Spread , } { ObjectExpression Property async get set PropertyDefinition Block : NewExpression new TypeArgList CompareOp < ) ( ArgList UnaryExpression await yield delete LogicOp BitOp ParenthesizedExpression ClassExpression class extends ClassBody MethodDeclaration Privacy static abstract override PrivatePropertyDefinition PropertyDeclaration readonly Optional TypeAnnotation Equals StaticBlock FunctionExpression ArrowFunction ParamList ParamList ArrayPattern ObjectPattern PatternProperty Privacy readonly Arrow MemberExpression PrivatePropertyName BinaryExpression ArithOp ArithOp ArithOp ArithOp BitOp CompareOp instanceof in const CompareOp BitOp BitOp BitOp LogicOp LogicOp ConditionalExpression LogicOp LogicOp AssignmentExpression UpdateOp PostfixExpression CallExpression TaggedTemplateExpression DynamicImport import ImportMeta JSXElement JSXSelfCloseEndTag JSXStartTag JSXSelfClosingTag JSXIdentifier JSXNamespacedName JSXMemberExpression JSXSpreadAttribute JSXAttribute JSXAttributeValue JSXEscape JSXEndTag JSXOpenTag JSXFragmentTag JSXText JSXEscape JSXStartCloseTag JSXCloseTag PrefixCast ArrowFunction TypeParamList SequenceExpression KeyofType keyof UniqueType unique ImportType InferredType infer TypeName ParenthesizedType FunctionSignature ParamList NewSignature IndexedType TupleType Label ArrayType ReadonlyType ObjectType MethodType PropertyType IndexSignature CallSignature TypePredicate is NewSignature new UnionType LogicOp IntersectionType LogicOp ConditionalType ParameterizedType ClassDeclaration abstract implements type VariableDeclaration let var TypeAliasDeclaration InterfaceDeclaration interface EnumDeclaration enum EnumBody NamespaceDeclaration namespace module AmbientDeclaration declare GlobalDeclaration global ClassDeclaration ClassBody MethodDeclaration AmbientFunctionDeclaration ExportGroup VariableName VariableName ImportDeclaration ImportGroup ForStatement for ForSpec ForInSpec ForOfSpec of WhileStatement while WithStatement with DoStatement do IfStatement if else SwitchStatement switch SwitchBody CaseLabel case DefaultLabel TryStatement try catch finally ReturnStatement return ThrowStatement throw BreakStatement break ContinueStatement continue DebuggerStatement debugger LabeledStatement ExpressionStatement",maxTerm:330,context:id,nodeProps:[[l.closedBy,3,"InterpolationEnd",40,"]",51,"}",66,")",132,"JSXSelfCloseEndTag JSXEndTag",146,"JSXEndTag"],[l.group,-26,8,15,17,58,184,188,191,192,194,197,200,211,213,219,221,223,225,228,234,238,240,242,244,246,248,249,"Statement",-30,12,13,24,27,28,41,43,44,45,47,52,60,68,74,75,91,92,101,103,119,122,124,125,126,127,129,130,148,149,151,"Expression",-22,23,25,29,32,34,152,154,156,157,159,160,161,163,164,165,167,168,169,178,180,182,183,"Type",-3,79,85,90,"ClassItem"],[l.openedBy,30,"InterpolationStart",46,"[",50,"{",65,"(",131,"JSXStartTag",141,"JSXStartTag JSXStartCloseTag"]],skippedNodes:[0,5,6],repeatNodeCount:28,tokenData:"!C}~R!`OX%TXY%cYZ'RZ[%c[]%T]^'R^p%Tpq%cqr'crs(kst0htu2`uv4pvw5ewx6cxy<yyz=Zz{=k{|>k|}?O}!O>k!O!P?`!P!QCl!Q!R!0[!R![!1q![!]!7s!]!^!8V!^!_!8g!_!`!9d!`!a!:[!a!b!<R!b!c%T!c!}2`!}#O!=d#O#P%T#P#Q!=t#Q#R!>U#R#S2`#S#T!>i#T#o2`#o#p!>y#p#q!?O#q#r!?f#r#s!?x#s$f%T$f$g%c$g#BY2`#BY#BZ!@Y#BZ$IS2`$IS$I_!@Y$I_$I|2`$I|$I}!Bq$I}$JO!Bq$JO$JT2`$JT$JU!@Y$JU$KV2`$KV$KW!@Y$KW&FU2`&FU&FV!@Y&FV?HT2`?HT?HU!@Y?HU~2`W%YR$UWO!^%T!_#o%T#p~%T,T%jg$UW'W+{OX%TXY%cYZ%TZ[%c[p%Tpq%cq!^%T!_#o%T#p$f%T$f$g%c$g#BY%T#BY#BZ%c#BZ$IS%T$IS$I_%c$I_$JT%T$JT$JU%c$JU$KV%T$KV$KW%c$KW&FU%T&FU&FV%c&FV?HT%T?HT?HU%c?HU~%T,T'YR$UW'X+{O!^%T!_#o%T#p~%T$T'jS$UW!j#{O!^%T!_!`'v!`#o%T#p~%T$O'}S#e#v$UWO!^%T!_!`(Z!`#o%T#p~%T$O(bR#e#v$UWO!^%T!_#o%T#p~%T'u(rZ$UW]!ROY(kYZ)eZr(krs*rs!^(k!^!_+U!_#O(k#O#P-b#P#o(k#o#p+U#p~(k&r)jV$UWOr)ers*Ps!^)e!^!_*a!_#o)e#o#p*a#p~)e&r*WR$P&j$UWO!^%T!_#o%T#p~%T&j*dROr*ars*ms~*a&j*rO$P&j'u*{R$P&j$UW]!RO!^%T!_#o%T#p~%T'm+ZV]!ROY+UYZ*aZr+Urs+ps#O+U#O#P+w#P~+U'm+wO$P&j]!R'm+zROr+Urs,Ts~+U'm,[U$P&j]!ROY,nZr,nrs-Vs#O,n#O#P-[#P~,n!R,sU]!ROY,nZr,nrs-Vs#O,n#O#P-[#P~,n!R-[O]!R!R-_PO~,n'u-gV$UWOr(krs-|s!^(k!^!_+U!_#o(k#o#p+U#p~(k'u.VZ$P&j$UW]!ROY.xYZ%TZr.xrs/rs!^.x!^!_,n!_#O.x#O#P0S#P#o.x#o#p,n#p~.x!Z/PZ$UW]!ROY.xYZ%TZr.xrs/rs!^.x!^!_,n!_#O.x#O#P0S#P#o.x#o#p,n#p~.x!Z/yR$UW]!RO!^%T!_#o%T#p~%T!Z0XT$UWO!^.x!^!_,n!_#o.x#o#p,n#p~.xy0mZ$UWOt%Ttu1`u!^%T!_!c%T!c!}1`!}#R%T#R#S1`#S#T%T#T#o1`#p$g%T$g~1`y1g]$UW'mqOt%Ttu1`u!Q%T!Q![1`![!^%T!_!c%T!c!}1`!}#R%T#R#S1`#S#T%T#T#o1`#p$g%T$g~1`&i2k_$UW#zS'Z%k'epOt%Ttu2`u}%T}!O3j!O!Q%T!Q![2`![!^%T!_!c%T!c!}2`!}#R%T#R#S2`#S#T%T#T#o2`#p$g%T$g~2`[3q_$UW#zSOt%Ttu3ju}%T}!O3j!O!Q%T!Q![3j![!^%T!_!c%T!c!}3j!}#R%T#R#S3j#S#T%T#T#o3j#p$g%T$g~3j$O4wS#^#v$UWO!^%T!_!`5T!`#o%T#p~%T$O5[R$UW#o#vO!^%T!_#o%T#p~%T%r5lU'v%j$UWOv%Tvw6Ow!^%T!_!`5T!`#o%T#p~%T$O6VS$UW#i#vO!^%T!_!`5T!`#o%T#p~%T'u6jZ$UW]!ROY6cYZ7]Zw6cwx*rx!^6c!^!_8T!_#O6c#O#P:T#P#o6c#o#p8T#p~6c&r7bV$UWOw7]wx*Px!^7]!^!_7w!_#o7]#o#p7w#p~7]&j7zROw7wwx*mx~7w'm8YV]!ROY8TYZ7wZw8Twx+px#O8T#O#P8o#P~8T'm8rROw8Twx8{x~8T'm9SU$P&j]!ROY9fZw9fwx-Vx#O9f#O#P9}#P~9f!R9kU]!ROY9fZw9fwx-Vx#O9f#O#P9}#P~9f!R:QPO~9f'u:YV$UWOw6cwx:ox!^6c!^!_8T!_#o6c#o#p8T#p~6c'u:xZ$P&j$UW]!ROY;kYZ%TZw;kwx/rx!^;k!^!_9f!_#O;k#O#P<e#P#o;k#o#p9f#p~;k!Z;rZ$UW]!ROY;kYZ%TZw;kwx/rx!^;k!^!_9f!_#O;k#O#P<e#P#o;k#o#p9f#p~;k!Z<jT$UWO!^;k!^!_9f!_#o;k#o#p9f#p~;k%V=QR!d$}$UWO!^%T!_#o%T#p~%TZ=bR!cR$UWO!^%T!_#o%T#p~%T%R=tU'[!R#_#v$UWOz%Tz{>W{!^%T!_!`5T!`#o%T#p~%T$O>_S#[#v$UWO!^%T!_!`5T!`#o%T#p~%T$u>rSj$m$UWO!^%T!_!`5T!`#o%T#p~%T&i?VR!R&a$UWO!^%T!_#o%T#p~%T&i?gVu%n$UWO!O%T!O!P?|!P!Q%T!Q![@r![!^%T!_#o%T#p~%Ty@RT$UWO!O%T!O!P@b!P!^%T!_#o%T#p~%Ty@iR!Qq$UWO!^%T!_#o%T#p~%Ty@yZ$UWkqO!Q%T!Q![@r![!^%T!_!g%T!g!hAl!h#R%T#R#S@r#S#X%T#X#YAl#Y#o%T#p~%TyAqZ$UWO{%T{|Bd|}%T}!OBd!O!Q%T!Q![CO![!^%T!_#R%T#R#SCO#S#o%T#p~%TyBiV$UWO!Q%T!Q![CO![!^%T!_#R%T#R#SCO#S#o%T#p~%TyCVV$UWkqO!Q%T!Q![CO![!^%T!_#R%T#R#SCO#S#o%T#p~%T,TCs`$UW#]#vOYDuYZ%TZzDuz{Jl{!PDu!P!Q!-e!Q!^Du!^!_Fx!_!`!.^!`!a!/]!a!}Du!}#OHq#O#PJQ#P#oDu#o#pFx#p~DuXD|[$UW}POYDuYZ%TZ!PDu!P!QEr!Q!^Du!^!_Fx!_!}Du!}#OHq#O#PJQ#P#oDu#o#pFx#p~DuXEy_$UW}PO!^%T!_#Z%T#Z#[Er#[#]%T#]#^Er#^#a%T#a#bEr#b#g%T#g#hEr#h#i%T#i#jEr#j#m%T#m#nEr#n#o%T#p~%TPF}V}POYFxZ!PFx!P!QGd!Q!}Fx!}#OG{#O#PHh#P~FxPGiU}P#Z#[Gd#]#^Gd#a#bGd#g#hGd#i#jGd#m#nGdPHOTOYG{Z#OG{#O#PH_#P#QFx#Q~G{PHbQOYG{Z~G{PHkQOYFxZ~FxXHvY$UWOYHqYZ%TZ!^Hq!^!_G{!_#OHq#O#PIf#P#QDu#Q#oHq#o#pG{#p~HqXIkV$UWOYHqYZ%TZ!^Hq!^!_G{!_#oHq#o#pG{#p~HqXJVV$UWOYDuYZ%TZ!^Du!^!_Fx!_#oDu#o#pFx#p~Du,TJs^$UW}POYJlYZKoZzJlz{NQ{!PJl!P!Q!,R!Q!^Jl!^!_!!]!_!}Jl!}#O!'|#O#P!+a#P#oJl#o#p!!]#p~Jl,TKtV$UWOzKoz{LZ{!^Ko!^!_M]!_#oKo#o#pM]#p~Ko,TL`X$UWOzKoz{LZ{!PKo!P!QL{!Q!^Ko!^!_M]!_#oKo#o#pM]#p~Ko,TMSR$UWU+{O!^%T!_#o%T#p~%T+{M`ROzM]z{Mi{~M]+{MlTOzM]z{Mi{!PM]!P!QM{!Q~M]+{NQOU+{,TNX^$UW}POYJlYZKoZzJlz{NQ{!PJl!P!Q! T!Q!^Jl!^!_!!]!_!}Jl!}#O!'|#O#P!+a#P#oJl#o#p!!]#p~Jl,T! ^_$UWU+{}PO!^%T!_#Z%T#Z#[Er#[#]%T#]#^Er#^#a%T#a#bEr#b#g%T#g#hEr#h#i%T#i#jEr#j#m%T#m#nEr#n#o%T#p~%T+{!!bY}POY!!]YZM]Zz!!]z{!#Q{!P!!]!P!Q!&x!Q!}!!]!}#O!$`#O#P!&f#P~!!]+{!#VY}POY!!]YZM]Zz!!]z{!#Q{!P!!]!P!Q!#u!Q!}!!]!}#O!$`#O#P!&f#P~!!]+{!#|UU+{}P#Z#[Gd#]#^Gd#a#bGd#g#hGd#i#jGd#m#nGd+{!$cWOY!$`YZM]Zz!$`z{!${{#O!$`#O#P!&S#P#Q!!]#Q~!$`+{!%OYOY!$`YZM]Zz!$`z{!${{!P!$`!P!Q!%n!Q#O!$`#O#P!&S#P#Q!!]#Q~!$`+{!%sTU+{OYG{Z#OG{#O#PH_#P#QFx#Q~G{+{!&VTOY!$`YZM]Zz!$`z{!${{~!$`+{!&iTOY!!]YZM]Zz!!]z{!#Q{~!!]+{!&}_}POzM]z{Mi{#ZM]#Z#[!&x#[#]M]#]#^!&x#^#aM]#a#b!&x#b#gM]#g#h!&x#h#iM]#i#j!&x#j#mM]#m#n!&x#n~M],T!(R[$UWOY!'|YZKoZz!'|z{!(w{!^!'|!^!_!$`!_#O!'|#O#P!*o#P#QJl#Q#o!'|#o#p!$`#p~!'|,T!(|^$UWOY!'|YZKoZz!'|z{!(w{!P!'|!P!Q!)x!Q!^!'|!^!_!$`!_#O!'|#O#P!*o#P#QJl#Q#o!'|#o#p!$`#p~!'|,T!*PY$UWU+{OYHqYZ%TZ!^Hq!^!_G{!_#OHq#O#PIf#P#QDu#Q#oHq#o#pG{#p~Hq,T!*tX$UWOY!'|YZKoZz!'|z{!(w{!^!'|!^!_!$`!_#o!'|#o#p!$`#p~!'|,T!+fX$UWOYJlYZKoZzJlz{NQ{!^Jl!^!_!!]!_#oJl#o#p!!]#p~Jl,T!,Yc$UW}POzKoz{LZ{!^Ko!^!_M]!_#ZKo#Z#[!,R#[#]Ko#]#^!,R#^#aKo#a#b!,R#b#gKo#g#h!,R#h#iKo#i#j!,R#j#mKo#m#n!,R#n#oKo#o#pM]#p~Ko,T!-lV$UWT+{OY!-eYZ%TZ!^!-e!^!_!.R!_#o!-e#o#p!.R#p~!-e+{!.WQT+{OY!.RZ~!.R$P!.g[$UW#o#v}POYDuYZ%TZ!PDu!P!QEr!Q!^Du!^!_Fx!_!}Du!}#OHq#O#PJQ#P#oDu#o#pFx#p~Du]!/f[#wS$UW}POYDuYZ%TZ!PDu!P!QEr!Q!^Du!^!_Fx!_!}Du!}#OHq#O#PJQ#P#oDu#o#pFx#p~Duy!0cd$UWkqO!O%T!O!P@r!P!Q%T!Q![!1q![!^%T!_!g%T!g!hAl!h#R%T#R#S!1q#S#U%T#U#V!3X#V#X%T#X#YAl#Y#b%T#b#c!2w#c#d!4m#d#l%T#l#m!5{#m#o%T#p~%Ty!1x_$UWkqO!O%T!O!P@r!P!Q%T!Q![!1q![!^%T!_!g%T!g!hAl!h#R%T#R#S!1q#S#X%T#X#YAl#Y#b%T#b#c!2w#c#o%T#p~%Ty!3OR$UWkqO!^%T!_#o%T#p~%Ty!3^W$UWO!Q%T!Q!R!3v!R!S!3v!S!^%T!_#R%T#R#S!3v#S#o%T#p~%Ty!3}Y$UWkqO!Q%T!Q!R!3v!R!S!3v!S!^%T!_#R%T#R#S!3v#S#b%T#b#c!2w#c#o%T#p~%Ty!4rV$UWO!Q%T!Q!Y!5X!Y!^%T!_#R%T#R#S!5X#S#o%T#p~%Ty!5`X$UWkqO!Q%T!Q!Y!5X!Y!^%T!_#R%T#R#S!5X#S#b%T#b#c!2w#c#o%T#p~%Ty!6QZ$UWO!Q%T!Q![!6s![!^%T!_!c%T!c!i!6s!i#R%T#R#S!6s#S#T%T#T#Z!6s#Z#o%T#p~%Ty!6z]$UWkqO!Q%T!Q![!6s![!^%T!_!c%T!c!i!6s!i#R%T#R#S!6s#S#T%T#T#Z!6s#Z#b%T#b#c!2w#c#o%T#p~%T%w!7|R!]V$UW#m%hO!^%T!_#o%T#p~%T!P!8^R_w$UWO!^%T!_#o%T#p~%T+c!8rR'`d!a%Y#x&s'zP!P!Q!8{!^!_!9Q!_!`!9_W!9QO$WW#v!9VP#`#v!_!`!9Y#v!9_O#o#v#v!9dO#a#v%w!9kT!{%o$UWO!^%T!_!`'v!`!a!9z!a#o%T#p~%T$P!:RR#W#w$UWO!^%T!_#o%T#p~%T%w!:gT'_!s#a#v$RS$UWO!^%T!_!`!:v!`!a!;W!a#o%T#p~%T$O!:}R#a#v$UWO!^%T!_#o%T#p~%T$O!;_T#`#v$UWO!^%T!_!`5T!`!a!;n!a#o%T#p~%T$O!;uS#`#v$UWO!^%T!_!`5T!`#o%T#p~%T%w!<YV'n%o$UWO!O%T!O!P!<o!P!^%T!_!a%T!a!b!=P!b#o%T#p~%T$`!<vRv$W$UWO!^%T!_#o%T#p~%T$O!=WS$UW#j#vO!^%T!_!`5T!`#o%T#p~%T&e!=kRx&]$UWO!^%T!_#o%T#p~%TZ!={R!OR$UWO!^%T!_#o%T#p~%T$O!>]S#g#v$UWO!^%T!_!`5T!`#o%T#p~%T$P!>pR$UW'd#wO!^%T!_#o%T#p~%T~!?OO!T~%r!?VT'u%j$UWO!^%T!_!`5T!`#o%T#p#q!=P#q~%T$u!?oR!S$knQ$UWO!^%T!_#o%T#p~%TX!@PR!kP$UWO!^%T!_#o%T#p~%T,T!@gr$UW'W+{#zS'Z%k'epOX%TXY%cYZ%TZ[%c[p%Tpq%cqt%Ttu2`u}%T}!O3j!O!Q%T!Q![2`![!^%T!_!c%T!c!}2`!}#R%T#R#S2`#S#T%T#T#o2`#p$f%T$f$g%c$g#BY2`#BY#BZ!@Y#BZ$IS2`$IS$I_!@Y$I_$JT2`$JT$JU!@Y$JU$KV2`$KV$KW!@Y$KW&FU2`&FU&FV!@Y&FV?HT2`?HT?HU!@Y?HU~2`,T!CO_$UW'X+{#zS'Z%k'epOt%Ttu2`u}%T}!O3j!O!Q%T!Q![2`![!^%T!_!c%T!c!}2`!}#R%T#R#S2`#S#T%T#T#o2`#p$g%T$g~2`",tokenizers:[rd,sd,od,0,1,2,3,4,5,6,7,8,nd],topRules:{Script:[0,7]},dialects:{jsx:11335,ts:11337},dynamicPrecedences:{149:1,176:1},specialized:[{term:287,get:(t,e)=>{return i=e,("extends"==t&&i.dialectEnabled(1)?4:-1)<<1;var i}},{term:287,get:t=>ad[t]||-1},{term:297,get:t=>ld[t]||-1},{term:63,get:t=>hd[t]||-1}],tokenPrec:11358}),ud=[Ml("function ${name}(${params}) {\n\t${}\n}",{label:"function",detail:"definition",type:"keyword"}),Ml("for (let ${index} = 0; ${index} < ${bound}; ${index}++) {\n\t${}\n}",{label:"for",detail:"loop",type:"keyword"}),Ml("for (let ${name} of ${collection}) {\n\t${}\n}",{label:"for",detail:"of loop",type:"keyword"}),Ml("try {\n\t${}\n} catch (${error}) {\n\t${}\n}",{label:"try",detail:"block",type:"keyword"}),Ml("class ${name} {\n\tconstructor(${params}) {\n\t\t${}\n\t}\n}",{label:"class",detail:"definition",type:"keyword"}),Ml('import {${names}} from "${module}"\n${}',{label:"import",detail:"named",type:"keyword"}),Ml('import ${name} from "${module}"\n${}',{label:"import",detail:"default",type:"keyword"})],Od=io.define({parser:cd.configure({props:[xo.add({IfStatement:Xo({except:/^\s*({|else\b)/}),TryStatement:Xo({except:/^\s*({|catch\b|finally\b)/}),LabeledStatement:Wo,SwitchBody:t=>{let e=t.textAfter,i=/^\s*\}/.test(e),n=/^\s*(case|default)\b/.test(e);return t.baseIndent+(i?0:n?1:2)*t.unit},Block:Po({closing:"}"}),ArrowFunction:t=>t.baseIndent+t.unit,"TemplateString BlockComment":()=>-1,"Statement Property":Xo({except:/^{/}),JSXElement(t){let e=/^\s*<\//.test(t.textAfter);return t.lineIndent(t.node.from)+(e?0:t.unit)},JSXEscape(t){let e=/\s*\}/.test(t.textAfter);return t.lineIndent(t.node.from)+(e?0:t.unit)},"JSXOpenTag JSXSelfClosingTag":t=>t.column(t.node.from)+t.unit}),Zo.add({"Block ClassBody SwitchBody EnumBody ObjectExpression ArrayExpression":_o,BlockComment:t=>({from:t.from+2,to:t.to-2})}),qo({"get set async static":ma.modifier,"for while do if else switch try catch finally return throw break continue default case":ma.controlKeyword,"in of await yield void typeof delete instanceof":ma.operatorKeyword,"let var const function class extends":ma.definitionKeyword,"import export from":ma.moduleKeyword,"with debugger as new":ma.keyword,TemplateString:ma.special(ma.string),Super:ma.atom,BooleanLiteral:ma.bool,this:ma.self,null:ma.null,Star:ma.modifier,VariableName:ma.variableName,"CallExpression/VariableName TaggedTemplateExpression/VariableName":ma.function(ma.variableName),VariableDefinition:ma.definition(ma.variableName),Label:ma.labelName,PropertyName:ma.propertyName,PrivatePropertyName:ma.special(ma.propertyName),"CallExpression/MemberExpression/PropertyName":ma.function(ma.propertyName),"FunctionDeclaration/VariableDefinition":ma.function(ma.definition(ma.variableName)),"ClassDeclaration/VariableDefinition":ma.definition(ma.className),PropertyDefinition:ma.definition(ma.propertyName),PrivatePropertyDefinition:ma.definition(ma.special(ma.propertyName)),UpdateOp:ma.updateOperator,LineComment:ma.lineComment,BlockComment:ma.blockComment,Number:ma.number,String:ma.string,ArithOp:ma.arithmeticOperator,LogicOp:ma.logicOperator,BitOp:ma.bitwiseOperator,CompareOp:ma.compareOperator,RegExp:ma.regexp,Equals:ma.definitionOperator,"Arrow : Spread":ma.punctuation,"( )":ma.paren,"[ ]":ma.squareBracket,"{ }":ma.brace,"InterpolationStart InterpolationEnd":ma.special(ma.brace),".":ma.derefOperator,", ;":ma.separator,TypeName:ma.typeName,TypeDefinition:ma.definition(ma.typeName),"type enum interface implements namespace module declare":ma.definitionKeyword,"abstract global Privacy readonly override":ma.modifier,"is keyof unique infer":ma.operatorKeyword,JSXAttributeValue:ma.attributeValue,JSXText:ma.content,"JSXStartTag JSXStartCloseTag JSXSelfCloseEndTag JSXEndTag":ma.angleBracket,"JSXIdentifier JSXNameSpacedName":ma.tagName,"JSXAttribute/JSXIdentifier JSXAttribute/JSXNameSpacedName":ma.attributeName})]}),languageData:{closeBrackets:{brackets:["(","[","{","'",'"',"`"]},commentTokens:{line:"//",block:{open:"/*",close:"*/"}},indentOnInput:/^\s*(?:case |default:|\{|\}|<\/)$/,wordChars:"$"}}),dd=Od.configure({dialect:"ts"}),fd=Od.configure({dialect:"jsx"}),pd=Od.configure({dialect:"jsx ts"});function md(t={}){let e=t.jsx?t.typescript?pd:fd:t.typescript?dd:Od;return new fo(e,Od.data.of({autocomplete:(i=["LineComment","BlockComment","String"],n=Ga(ud),t=>{for(let e=no(t.state).resolveInner(t.pos,-1);e;e=e.parent)if(i.indexOf(e.name)>-1)return null;return n(t)})}));var i,n}function gd(t,e,i,n){return i.line(t+n.line).from+e+(1==t?n.col-1:-1)}function Qd(t,e,i){let n=gd(t.line,t.column,e,i),r={from:n,to:null!=t.endLine&&1!=t.endColumn?gd(t.endLine,t.endColumn,e,i):n,message:t.message,source:t.ruleId?"jshint:"+t.ruleId:"jshint",severity:1==t.severity?"warning":"error"};if(t.fix){let{range:e,text:s}=t.fix,o=e[0]+i.pos-n,a=e[1]+i.pos-n;r.actions=[{name:"fix",apply(t,e){t.dispatch({changes:{from:e+o,to:e+a,insert:s},scrollIntoView:!0})}}]}return r}var bd=Object.freeze({__proto__:null,esLint:function(t,e){return e||(e={parserOptions:{ecmaVersion:2019,sourceType:"module"},env:{browser:!0,node:!0,es6:!0,es2015:!0,es2017:!0,es2020:!0},rules:{}},t.getRules().forEach(((t,i)=>{t.meta.docs.recommended&&(e.rules[i]=2)}))),i=>{let{state:n}=i,r=[];for(let{from:i,to:s}of Od.findRegions(n)){let o=n.doc.lineAt(i),a={line:o.number-1,col:i-o.from,pos:i};for(let o of t.verify(n.sliceDoc(i,s),e))r.push(Qd(o,n.doc,a))}return r}},javascript:md,javascriptLanguage:Od,jsxLanguage:fd,snippets:ud,tsxLanguage:pd,typescriptLanguage:dd});const yd=["_blank","_self","_top","_parent"],vd=["ascii","utf-8","utf-16","latin1","latin1"],xd=["get","post","put","delete"],wd=["application/x-www-form-urlencoded","multipart/form-data","text/plain"],Sd=["true","false"],kd={},$d={a:{attrs:{href:null,ping:null,type:null,media:null,target:yd,hreflang:null}},abbr:kd,acronym:kd,address:kd,applet:kd,area:{attrs:{alt:null,coords:null,href:null,target:null,ping:null,media:null,hreflang:null,type:null,shape:["default","rect","circle","poly"]}},article:kd,aside:kd,audio:{attrs:{src:null,mediagroup:null,crossorigin:["anonymous","use-credentials"],preload:["none","metadata","auto"],autoplay:["autoplay"],loop:["loop"],controls:["controls"]}},b:kd,base:{attrs:{href:null,target:yd}},basefont:kd,bdi:kd,bdo:kd,big:kd,blockquote:{attrs:{cite:null}},body:kd,br:kd,button:{attrs:{form:null,formaction:null,name:null,value:null,autofocus:["autofocus"],disabled:["autofocus"],formenctype:wd,formmethod:xd,formnovalidate:["novalidate"],formtarget:yd,type:["submit","reset","button"]}},canvas:{attrs:{width:null,height:null}},caption:kd,center:kd,cite:kd,code:kd,col:{attrs:{span:null}},colgroup:{attrs:{span:null}},command:{attrs:{type:["command","checkbox","radio"],label:null,icon:null,radiogroup:null,command:null,title:null,disabled:["disabled"],checked:["checked"]}},data:{attrs:{value:null}},datagrid:{attrs:{disabled:["disabled"],multiple:["multiple"]}},datalist:{attrs:{data:null}},dd:kd,del:{attrs:{cite:null,datetime:null}},details:{attrs:{open:["open"]}},dfn:kd,dir:kd,div:kd,dl:kd,dt:kd,em:kd,embed:{attrs:{src:null,type:null,width:null,height:null}},eventsource:{attrs:{src:null}},fieldset:{attrs:{disabled:["disabled"],form:null,name:null}},figcaption:kd,figure:kd,font:kd,footer:kd,form:{attrs:{action:null,name:null,"accept-charset":vd,autocomplete:["on","off"],enctype:wd,method:xd,novalidate:["novalidate"],target:yd}},frame:kd,frameset:kd,h1:kd,h2:kd,h3:kd,h4:kd,h5:kd,h6:kd,head:{children:["title","base","link","style","meta","script","noscript","command"]},header:kd,hgroup:kd,hr:kd,html:{attrs:{manifest:null}},i:kd,iframe:{attrs:{src:null,srcdoc:null,name:null,width:null,height:null,sandbox:["allow-top-navigation","allow-same-origin","allow-forms","allow-scripts"],seamless:["seamless"]}},img:{attrs:{alt:null,src:null,ismap:null,usemap:null,width:null,height:null,crossorigin:["anonymous","use-credentials"]}},input:{attrs:{alt:null,dirname:null,form:null,formaction:null,height:null,list:null,max:null,maxlength:null,min:null,name:null,pattern:null,placeholder:null,size:null,src:null,step:null,value:null,width:null,accept:["audio/*","video/*","image/*"],autocomplete:["on","off"],autofocus:["autofocus"],checked:["checked"],disabled:["disabled"],formenctype:wd,formmethod:xd,formnovalidate:["novalidate"],formtarget:yd,multiple:["multiple"],readonly:["readonly"],required:["required"],type:["hidden","text","search","tel","url","email","password","datetime","date","month","week","time","datetime-local","number","range","color","checkbox","radio","file","submit","image","reset","button"]}},ins:{attrs:{cite:null,datetime:null}},kbd:kd,keygen:{attrs:{challenge:null,form:null,name:null,autofocus:["autofocus"],disabled:["disabled"],keytype:["RSA"]}},label:{attrs:{for:null,form:null}},legend:kd,li:{attrs:{value:null}},link:{attrs:{href:null,type:null,hreflang:null,media:null,sizes:["all","16x16","16x16 32x32","16x16 32x32 64x64"]}},map:{attrs:{name:null}},mark:kd,menu:{attrs:{label:null,type:["list","context","toolbar"]}},meta:{attrs:{content:null,charset:vd,name:["viewport","application-name","author","description","generator","keywords"],"http-equiv":["content-language","content-type","default-style","refresh"]}},meter:{attrs:{value:null,min:null,low:null,high:null,max:null,optimum:null}},nav:kd,noframes:kd,noscript:kd,object:{attrs:{data:null,type:null,name:null,usemap:null,form:null,width:null,height:null,typemustmatch:["typemustmatch"]}},ol:{attrs:{reversed:["reversed"],start:null,type:["1","a","A","i","I"]},children:["li","script","template","ul","ol"]},optgroup:{attrs:{disabled:["disabled"],label:null}},option:{attrs:{disabled:["disabled"],label:null,selected:["selected"],value:null}},output:{attrs:{for:null,form:null,name:null}},p:kd,param:{attrs:{name:null,value:null}},pre:kd,progress:{attrs:{value:null,max:null}},q:{attrs:{cite:null}},rp:kd,rt:kd,ruby:kd,s:kd,samp:kd,script:{attrs:{type:["text/javascript"],src:null,async:["async"],defer:["defer"],charset:vd}},section:kd,select:{attrs:{form:null,name:null,size:null,autofocus:["autofocus"],disabled:["disabled"],multiple:["multiple"]}},small:kd,source:{attrs:{src:null,type:null,media:null}},span:kd,strike:kd,strong:kd,style:{attrs:{type:["text/css"],media:null,scoped:null}},sub:kd,summary:kd,sup:kd,table:kd,tbody:kd,td:{attrs:{colspan:null,rowspan:null,headers:null}},textarea:{attrs:{dirname:null,form:null,maxlength:null,name:null,placeholder:null,rows:null,cols:null,autofocus:["autofocus"],disabled:["disabled"],readonly:["readonly"],required:["required"],wrap:["soft","hard"]}},tfoot:kd,th:{attrs:{colspan:null,rowspan:null,headers:null,scope:["row","col","rowgroup","colgroup"]}},thead:kd,time:{attrs:{datetime:null}},title:kd,tr:kd,track:{attrs:{src:null,label:null,default:null,kind:["subtitles","captions","descriptions","chapters","metadata"],srclang:null}},tt:kd,u:kd,ul:{children:["li","script","template","ul","ol"]},var:kd,video:{attrs:{src:null,poster:null,width:null,height:null,crossorigin:["anonymous","use-credentials"],preload:["auto","metadata","none"],autoplay:["autoplay"],mediagroup:["movie"],muted:["muted"],controls:["controls"]}},wbr:kd},Td={accesskey:null,class:null,contenteditable:Sd,contextmenu:null,dir:["ltr","rtl","auto"],draggable:["true","false","auto"],dropzone:["copy","move","link","string:","file:"],hidden:["hidden"],id:null,inert:["inert"],itemid:null,itemprop:null,itemref:null,itemscope:["itemscope"],itemtype:null,lang:["ar","bn","de","en-GB","en-US","es","fr","hi","id","ja","pa","pt","ru","tr","zh"],spellcheck:Sd,autocorrect:Sd,autocapitalize:Sd,style:null,tabindex:null,title:null,translate:["yes","no"],onclick:null,rel:["stylesheet","alternate","author","bookmark","help","license","next","nofollow","noreferrer","prefetch","prev","search","tag"],role:"alert application article banner button cell checkbox complementary contentinfo dialog document feed figure form grid gridcell heading img list listbox listitem main navigation region row rowgroup search switch tab table tabpanel textbox timer".split(" "),"aria-activedescendant":null,"aria-atomic":Sd,"aria-autocomplete":["inline","list","both","none"],"aria-busy":Sd,"aria-checked":["true","false","mixed","undefined"],"aria-controls":null,"aria-describedby":null,"aria-disabled":Sd,"aria-dropeffect":null,"aria-expanded":["true","false","undefined"],"aria-flowto":null,"aria-grabbed":["true","false","undefined"],"aria-haspopup":Sd,"aria-hidden":Sd,"aria-invalid":["true","false","grammar","spelling"],"aria-label":null,"aria-labelledby":null,"aria-level":null,"aria-live":["off","polite","assertive"],"aria-multiline":Sd,"aria-multiselectable":Sd,"aria-owns":null,"aria-posinset":null,"aria-pressed":["true","false","mixed","undefined"],"aria-readonly":Sd,"aria-relevant":null,"aria-required":Sd,"aria-selected":["true","false","undefined"],"aria-setsize":null,"aria-sort":["ascending","descending","none","other"],"aria-valuemax":null,"aria-valuemin":null,"aria-valuenow":null,"aria-valuetext":null},Pd=Object.keys($d),Rd=Object.keys(Td);function Wd(t,e,i=t.length){if(!e)return"";let n=e.firstChild,r=n&&n.getChild("TagName");return r?t.sliceString(r.from,Math.min(r.to,i)):""}function Xd(t,e=!1){for(let i=t.parent;i;i=i.parent)if("Element"==i.name){if(!e)return i;e=!1}return null}function Cd(t,e){let i=$d[Wd(t,Xd(e,!0))];return(null==i?void 0:i.children)||Pd}function Ad(t,e){let i=[];for(let n=e;n=Xd(n);){let r=Wd(t,n);if(r&&"CloseTag"==n.lastChild.name)break;r&&i.indexOf(r)<0&&("EndTag"==e.name||e.from>=n.firstChild.to)&&i.push(r)}return i}const Zd=/^[:\-\.\w\u00b7-\uffff]+$/;function _d(t,e,i,n){let r=/\s*>/.test(t.sliceDoc(n,n+5))?"":">";return{from:i,to:n,options:Cd(t.doc,e).map((t=>({label:t,type:"type"}))).concat(Ad(t.doc,e).map(((t,e)=>({label:"/"+t,apply:"/"+t+r,type:"type",boost:99-e})))),span:/^\/?[:\-\.\w\u00b7-\uffff]*$/}}function Ld(t,e,i,n){let r=/\s*>/.test(t.sliceDoc(n,n+5))?"":">";return{from:i,to:n,options:Ad(t.doc,e).map(((t,e)=>({label:t,apply:t+r,type:"type",boost:99-e}))),span:Zd}}function Dd(t){let{state:e,pos:i}=t,n=no(e).resolveInner(i),r=n.resolve(i,-1);for(let t,e=i;n==r&&(t=r.childBefore(e));){let i=t.lastChild;if(!i||!i.type.isError||i.from<i.to)break;n=r=t,e=i.from}return"TagName"==r.name?r.parent&&/CloseTag$/.test(r.parent.name)?Ld(e,r,r.from,i):_d(e,r,r.from,i):"StartTag"==r.name?_d(e,r,i,i):"StartCloseTag"==r.name||"IncompleteCloseTag"==r.name?Ld(e,r,i,i):t.explicit&&("OpenTag"==r.name||"SelfClosingTag"==r.name)||"AttributeName"==r.name?function(t,e,i,n){let r=Xd(e),s=r?$d[Wd(t.doc,r)]:null;return{from:i,to:n,options:(s&&s.attrs?Object.keys(s.attrs).concat(Rd):Rd).map((t=>({label:t,type:"property"}))),span:Zd}}(e,r,"AttributeName"==r.name?r.from:i,i):"Is"==r.name||"AttributeValue"==r.name||"UnquotedAttributeValue"==r.name?function(t,e,i,n){var r;let s,o=null===(r=e.parent)||void 0===r?void 0:r.getChild("AttributeName"),a=[];if(o){let r=t.sliceDoc(o.from,o.to),l=Td[r];if(!l){let i=Xd(e),n=i?$d[Wd(t.doc,i)]:null;l=(null==n?void 0:n.attrs)&&n.attrs[r]}if(l){let e=t.sliceDoc(i,n).toLowerCase(),r='"',o='"';/^['"]/.test(e)?(s='"'==e[0]?/^[^"]*$/:/^[^']*$/,r="",o=t.sliceDoc(n,n+1)==e[0]?"":e[0],e=e.slice(1),i++):s=/^[^\s<>='"]*$/;for(let t of l)a.push({label:t,apply:r+t+o,type:"constant"})}}return{from:i,to:n,options:a,span:s}}(e,r,"Is"==r.name?i:r.from,i):!t.explicit||"Element"!=n.name&&"Text"!=n.name&&"Document"!=n.name?null:function(t,e,i){let n=[],r=0;for(let i of Cd(t.doc,e))n.push({label:"<"+i,type:"type"});for(let i of Ad(t.doc,e))n.push({label:"</"+i+">",type:"type",boost:99-r++});return{from:i,to:i,options:n,span:/^<\/?[:\-\.\w\u00b7-\uffff]*$/}}(e,r,i)}const zd=io.define({parser:JO.configure({props:[xo.add({Element(t){let e=/^(\s*)(<\/)?/.exec(t.textAfter);return t.node.to<=t.pos+e[0].length?t.continue():t.lineIndent(t.node.from)+(e[2]?0:t.unit)},"OpenTag CloseTag SelfClosingTag":t=>t.column(t.node.from)+t.unit,Document(t){if(t.pos+/\s*/.exec(t.textAfter)[0].length<t.node.to)return t.continue();let e,i=null;for(let e=t.node;;){let t=e.lastChild;if(!t||"Element"!=t.name||t.to!=e.to)break;i=e=t}return i&&(!(e=i.lastChild)||"CloseTag"!=e.name&&"SelfClosingTag"!=e.name)?t.lineIndent(i.from)+t.unit:null}}),Zo.add({Element(t){let e=t.firstChild,i=t.lastChild;return e&&"OpenTag"==e.name?{from:e.to,to:"CloseTag"==i.name?i.from:t.to}:null}}),qo({"Text RawText":ma.content,"StartTag StartCloseTag SelfCloserEndTag EndTag SelfCloseEndTag":ma.angleBracket,TagName:ma.tagName,"MismatchedCloseTag/TagName":[ma.tagName,ma.invalid],AttributeName:ma.attributeName,"AttributeValue UnquotedAttributeValue":ma.attributeValue,Is:ma.definitionOperator,"EntityReference CharacterReference":ma.character,Comment:ma.blockComment,ProcessingInst:ma.processingInstruction,DoctypeDecl:ma.documentMeta})],wrap:function(t){let e=[],i=[],n=[];for(let r of t){let t="script"==r.tag?e:"style"==r.tag?i:"textarea"==r.tag?n:null;if(!t)throw new RangeError("Only script, style, and textarea tags can host nested parsers");t.push(r)}return A(((t,r)=>{let s=t.type.id;return 27==s?td(t,r,e):30==s?td(t,r,i):33==s?td(t,r,n):null}))}([{tag:"script",attrs:t=>!t.type||/^(?:text|application)\/(?:x-)?(?:java|ecma)script$|^module$|^$/i.test(t.type),parser:Od.parser},{tag:"style",attrs:t=>(!t.lang||"css"==t.lang)&&(!t.type||/^(text\/)?(x-)?(stylesheet|css)$/i.test(t.type)),parser:RO.parser}])}),languageData:{commentTokens:{block:{open:"\x3c!--",close:"--\x3e"}},indentOnInput:/^\s*<\/\w+\W$/,wordChars:"-._"}}),Ed=zd.data.of({autocomplete:Dd});function Md(t={}){let e=zd;return!1===t.matchClosingTags&&(e=e.configure({dialect:"noMatch"})),new fo(e,[Ed,!1!==t.autoCloseTags?jd:[],md().support,XO().support])}const jd=Os.inputHandler.of(((t,e,i,n)=>{if(t.composing||t.state.readOnly||e!=i||">"!=n&&"/"!=n||!zd.isActiveAt(t.state,e,-1))return!1;let{state:r}=t,s=r.changeByRange((t=>{var e,i,s;let o,{head:a}=t,l=no(r).resolveInner(a,-1);if("TagName"!=l.name&&"StartTag"!=l.name||(l=l.parent),">"==n&&"OpenTag"==l.name){if("CloseTag"!=(null===(i=null===(e=l.parent)||void 0===e?void 0:e.lastChild)||void 0===i?void 0:i.name)&&(o=Wd(r.doc,l.parent,a)))return{range:Xt.cursor(a+1),changes:{from:a,insert:`></${o}>`}}}else if("/"==n&&"OpenTag"==l.name){let t=l.parent,e=null==t?void 0:t.parent;if(t.from==a-1&&"CloseTag"!=(null===(s=e.lastChild)||void 0===s?void 0:s.name)&&(o=Wd(r.doc,e,a))){let t=`/${o}>`;return{range:Xt.cursor(a+t.length),changes:{from:a,insert:t}}}}return{range:t}}));return!s.changes.empty&&(t.dispatch(s,{userEvent:"input.type",scrollIntoView:!0}),!0)}));var qd=Object.freeze({__proto__:null,autoCloseTags:jd,html:Md,htmlCompletion:Ed,htmlCompletionSource:Dd,htmlLanguage:zd});const Yd=_t.define({combine(t){let e,i;for(let n of t)e=e||n.topContainer,i=i||n.bottomContainer;return{topContainer:e,bottomContainer:i}}}),Vd=Qn.fromClass(class{constructor(t){this.input=t.state.facet(Nd),this.specs=this.input.filter((t=>t)),this.panels=this.specs.map((e=>e(t)));let e=t.state.facet(Yd);this.top=new Ud(t,!0,e.topContainer),this.bottom=new Ud(t,!1,e.bottomContainer),this.top.sync(this.panels.filter((t=>t.top))),this.bottom.sync(this.panels.filter((t=>!t.top)));for(let t of this.panels)t.dom.classList.add("cm-panel"),t.mount&&t.mount()}update(t){let e=t.state.facet(Yd);this.top.container!=e.topContainer&&(this.top.sync([]),this.top=new Ud(t.view,!0,e.topContainer)),this.bottom.container!=e.bottomContainer&&(this.bottom.sync([]),this.bottom=new Ud(t.view,!1,e.bottomContainer)),this.top.syncClasses(),this.bottom.syncClasses();let i=t.state.facet(Nd);if(i!=this.input){let e=i.filter((t=>t)),n=[],r=[],s=[],o=[];for(let i of e){let e,a=this.specs.indexOf(i);a<0?(e=i(t.view),o.push(e)):(e=this.panels[a],e.update&&e.update(t)),n.push(e),(e.top?r:s).push(e)}this.specs=e,this.panels=n,this.top.sync(r),this.bottom.sync(s);for(let t of o)t.dom.classList.add("cm-panel"),t.mount&&t.mount()}else for(let e of this.panels)e.update&&e.update(t)}destroy(){this.top.sync([]),this.bottom.sync([])}},{provide:pn.scrollMargins.from((t=>({top:t.top.scrollMargin(),bottom:t.bottom.scrollMargin()})))});class Ud{constructor(t,e,i){this.view=t,this.top=e,this.container=i,this.dom=void 0,this.classes="",this.panels=[],this.syncClasses()}sync(t){for(let e of this.panels)e.destroy&&t.indexOf(e)<0&&e.destroy();this.panels=t,this.syncDOM()}syncDOM(){if(0==this.panels.length)return void(this.dom&&(this.dom.remove(),this.dom=void 0));if(!this.dom){this.dom=document.createElement("div"),this.dom.className=this.top?"cm-panels cm-panels-top":"cm-panels cm-panels-bottom",this.dom.style[this.top?"top":"bottom"]="0";let t=this.container||this.view.dom;t.insertBefore(this.dom,this.top?t.firstChild:null)}let t=this.dom.firstChild;for(let e of this.panels)if(e.dom.parentNode==this.dom){for(;t!=e.dom;)t=Gd(t);t=t.nextSibling}else this.dom.insertBefore(e.dom,t);for(;t;)t=Gd(t)}scrollMargin(){return!this.dom||this.container?0:Math.max(0,this.top?this.dom.getBoundingClientRect().bottom-Math.max(0,this.view.scrollDOM.getBoundingClientRect().top):Math.min(innerHeight,this.view.scrollDOM.getBoundingClientRect().bottom)-this.dom.getBoundingClientRect().top)}syncClasses(){if(this.container&&this.classes!=this.view.themeClasses){for(let t of this.classes.split(" "))t&&this.container.classList.remove(t);for(let t of(this.classes=this.view.themeClasses).split(" "))t&&this.container.classList.add(t)}}}function Gd(t){let e=t.nextSibling;return t.remove(),e}const Id=Os.baseTheme({".cm-panels":{boxSizing:"border-box",position:"sticky",left:0,right:0},"&light .cm-panels":{backgroundColor:"#f5f5f5",color:"black"},"&light .cm-panels-top":{borderBottom:"1px solid #ddd"},"&light .cm-panels-bottom":{borderTop:"1px solid #ddd"},"&dark .cm-panels":{backgroundColor:"#333338",color:"white"}}),Nd=_t.define({enables:[Vd,Id]}),Bd="function"==typeof String.prototype.normalize?t=>t.normalize("NFKD"):t=>t;class Fd{constructor(t,e,i=0,n=t.length,r){this.value={from:0,to:0},this.done=!1,this.matches=[],this.buffer="",this.bufferPos=0,this.iter=t.iterRange(i,n),this.bufferStart=i,this.normalize=r?t=>r(Bd(t)):Bd,this.query=this.normalize(e)}peek(){if(this.bufferPos==this.buffer.length){if(this.bufferStart+=this.buffer.length,this.iter.next(),this.iter.done)return-1;this.bufferPos=0,this.buffer=this.iter.value}return ot(this.buffer,this.bufferPos)}next(){for(;this.matches.length;)this.matches.pop();return this.nextOverlapping()}nextOverlapping(){for(;;){let t=this.peek();if(t<0)return this.done=!0,this;let e=at(t),i=this.bufferStart+this.bufferPos;this.bufferPos+=lt(t);let n=this.normalize(e);for(let t=0,r=i;;t++){let s=n.charCodeAt(t),o=this.match(s,r);if(o)return this.value=o,this;if(t==n.length-1)break;r==i&&t<e.length&&e.charCodeAt(t)==s&&r++}}}match(t,e){let i=null;for(let n=0;n<this.matches.length;n+=2){let r=this.matches[n],s=!1;this.query.charCodeAt(r)==t&&(r==this.query.length-1?i={from:this.matches[n+1],to:e+1}:(this.matches[n]++,s=!0)),s||(this.matches.splice(n,2),n-=2)}return this.query.charCodeAt(0)==t&&(1==this.query.length?i={from:e,to:e+1}:this.matches.push(1,e)),i}}"undefined"!=typeof Symbol&&(Fd.prototype[Symbol.iterator]=function(){return this});const Hd={from:-1,to:-1,match:/.*/.exec("")},Jd="gm"+(null==/x/.unicode?"":"u"),Kd=new WeakMap;class tf{constructor(t,e){this.from=t,this.text=e}get to(){return this.from+this.text.length}static get(t,e,i){let n=Kd.get(t);if(!n||n.from>=i||n.to<=e){let n=new tf(e,t.sliceString(e,i));return Kd.set(t,n),n}if(n.from==e&&n.to==i)return n;let{text:r,from:s}=n;return s>e&&(r=t.sliceString(e,s)+r,s=e),n.to<i&&(r+=t.sliceString(n.to,i)),Kd.set(t,new tf(s,r)),new tf(e,r.slice(e-s,i-s))}}class ef{constructor(t,e,i,n,r){this.text=t,this.to=r,this.done=!1,this.value=Hd,this.matchPos=n,this.re=new RegExp(e,Jd+((null==i?void 0:i.ignoreCase)?"i":"")),this.flat=tf.get(t,n,this.chunkEnd(n+5e3))}chunkEnd(t){return t>=this.to?this.to:this.text.lineAt(t).to}next(){for(;;){let t=this.re.lastIndex=this.matchPos-this.flat.from,e=this.re.exec(this.flat.text);if(e&&!e[0]&&e.index==t&&(this.re.lastIndex=t+1,e=this.re.exec(this.flat.text)),e&&this.flat.to<this.to&&e.index+e[0].length>this.flat.text.length-10&&(e=null),e){let t=this.flat.from+e.index,i=t+e[0].length;return this.value={from:t,to:i,match:e},this.matchPos=i+(t==i?1:0),this}if(this.flat.to==this.to)return this.done=!0,this;this.flat=tf.get(this.text,this.flat.from,this.chunkEnd(this.flat.from+2*this.flat.text.length))}}}"undefined"!=typeof Symbol&&(class{constructor(t,e,i,n=0,r=t.length){if(this.to=r,this.curLine="",this.done=!1,this.value=Hd,/\\[sWDnr]|\n|\r|\[\^/.test(e))return new ef(t,e,i,n,r);this.re=new RegExp(e,Jd+((null==i?void 0:i.ignoreCase)?"i":"")),this.iter=t.iter();let s=t.lineAt(n);this.curLineStart=s.from,this.matchPos=n,this.getLine(this.curLineStart)}getLine(t){this.iter.next(t),this.iter.lineBreak?this.curLine="":(this.curLine=this.iter.value,this.curLineStart+this.curLine.length>this.to&&(this.curLine=this.curLine.slice(0,this.to-this.curLineStart)),this.iter.next())}nextLine(){this.curLineStart=this.curLineStart+this.curLine.length+1,this.curLineStart>this.to?this.curLine="":this.getLine(0)}next(){for(let t=this.matchPos-this.curLineStart;;){this.re.lastIndex=t;let e=this.matchPos<=this.to&&this.re.exec(this.curLine);if(e){let i=this.curLineStart+e.index,n=i+e[0].length;if(this.matchPos=n+(i==n?1:0),i==this.curLine.length&&this.nextLine(),i<n||i>this.value.to)return this.value={from:i,to:n,match:e},this;t=this.matchPos-this.curLineStart}else{if(!(this.curLineStart+this.curLine.length<this.to))return this.done=!0,this;this.nextLine(),t=0}}}}.prototype[Symbol.iterator]=ef.prototype[Symbol.iterator]=function(){return this});const nf={highlightWordAroundCursor:!1,minSelectionLength:1,maxMatches:100},rf=_t.define({combine:t=>be(t,nf,{highlightWordAroundCursor:(t,e)=>t||e,minSelectionLength:Math.min,maxMatches:Math.min})});function sf(t){let e=[hf,lf];return t&&e.push(rf.of(t)),e}const of=Yi.mark({class:"cm-selectionMatch"}),af=Yi.mark({class:"cm-selectionMatch cm-selectionMatch-main"}),lf=Qn.fromClass(class{constructor(t){this.decorations=this.getDeco(t)}update(t){(t.selectionSet||t.docChanged||t.viewportChanged)&&(this.decorations=this.getDeco(t.view))}getDeco(t){let e=t.state.facet(rf),{state:i}=t,n=i.selection;if(n.ranges.length>1)return Yi.none;let r,s=n.main,o=null;if(s.empty){if(!e.highlightWordAroundCursor)return Yi.none;let t=i.wordAt(s.head);if(!t)return Yi.none;o=i.charCategorizer(s.head),r=i.sliceDoc(t.from,t.to)}else{let t=s.to-s.from;if(t<e.minSelectionLength||t>200)return Yi.none;if(r=i.sliceDoc(s.from,s.to).trim(),!r)return Yi.none}let a=[];for(let n of t.visibleRanges){let t=new Fd(i.doc,r,n.from,n.to);for(;!t.next().done;){let{from:n,to:r}=t.value;if((!o||(0==n||o(i.sliceDoc(n-1,n))!=pe.Word)&&(r==i.doc.length||o(i.sliceDoc(r,r+1))!=pe.Word))&&(o&&n<=s.from&&r>=s.to?a.push(af.range(n,r)):(n>=s.to||r<=s.from)&&a.push(of.range(n,r)),a.length>e.maxMatches))return Yi.none}}return Yi.set(a)}},{decorations:t=>t.decorations}),hf=Os.baseTheme({".cm-selectionMatch":{backgroundColor:"#99ff7780"},".cm-searchMatch .cm-selectionMatch":{backgroundColor:"transparent"}}),cf=({state:t,dispatch:e})=>{let{ranges:i}=t.selection;if(i.some((t=>t.from===t.to)))return(({state:t,dispatch:e})=>{let{selection:i}=t,n=Xt.create(i.ranges.map((e=>t.wordAt(e.head)||Xt.cursor(e.head))),i.mainIndex);return!n.eq(i)&&(e(t.update({selection:n})),!0)})({state:t,dispatch:e});let n=t.sliceDoc(i[0].from,i[0].to);if(t.selection.ranges.some((e=>t.sliceDoc(e.from,e.to)!=n)))return!1;let r=function(t,e){let{main:i,ranges:n}=t.selection,r=t.wordAt(i.head),s=r&&r.from==i.from&&r.to==i.to;for(let i=!1,r=new Fd(t.doc,e,n[n.length-1].to);;){if(r.next(),!r.done){if(i&&n.some((t=>t.from==r.value.from)))continue;if(s){let e=t.wordAt(r.value.from);if(!e||e.from!=r.value.from||e.to!=r.value.to)continue}return r.value}if(i)return null;r=new Fd(t.doc,e,0,Math.max(0,n[n.length-1].from-1)),i=!0}}(t,n);return!!r&&(e(t.update({selection:t.selection.addRange(Xt.range(r.from,r.to),!1),effects:Os.scrollIntoView(r.to)})),!0)};async function uf(){return ya.define((await r("ebNrr").then((function(t){return t.c}))).clojure)}async function Of(){return ya.define((await r("ebNrr").then((function(t){return t.a}))).coffeeScript)}function df(){return r("kF9Z2")}function ff(){return r("9CD9J")}function pf(){return r("xdy0P")}function mf(){return r("3ItKJ")}function gf(){return r("kvZvW")}function Qf(){return r("5huHv")}async function bf(){return ya.define((await r("ebNrr").then((function(t){return t.s}))).shell)}function yf(){return r("aPntQ")}function vf(){return r("2Cwuy")}})),r.register("ebNrr",(function(t,e){t.exports=import("./"+r("27Lyk").resolve("czFdi")).then((()=>r("bz5oi")))})),r.register("kF9Z2",(function(t,e){t.exports=Promise.all([import("./"+r("27Lyk").resolve("eBL4z")),import("./"+r("27Lyk").resolve("bTjF1"))]).then((()=>r("iOup8")))})),r.register("9CD9J",(function(t,e){t.exports=Promise.all([import("./"+r("27Lyk").resolve("eBL4z")),import("./"+r("27Lyk").resolve("eyPWV"))]).then((()=>r("jen28")))})),r.register("xdy0P",(function(t,e){t.exports=Promise.all([import("./"+r("27Lyk").resolve("eBL4z")),import("./"+r("27Lyk").resolve("lEcxu"))]).then((()=>r("7W805")))})),r.register("3ItKJ",(function(t,e){t.exports=Promise.all([import("./"+r("27Lyk").resolve("eBL4z")),import("./"+r("27Lyk").resolve("f7Hx6"))]).then((()=>r("JtUjz")))})),r.register("kvZvW",(function(t,e){t.exports=Promise.all([import("./"+r("27Lyk").resolve("eBL4z")),import("./"+r("27Lyk").resolve("432R0"))]).then((()=>r("2SaWH")))})),r.register("5huHv",(function(t,e){t.exports=Promise.all([import("./"+r("27Lyk").resolve("eBL4z")),import("./"+r("27Lyk").resolve("iyjCs"))]).then((()=>r("foxXU")))})),r.register("aPntQ",(function(t,e){t.exports=Promise.all([import("./"+r("27Lyk").resolve("eBL4z")),import("./"+r("27Lyk").resolve("kSe0D"))]).then((()=>r("9r8WZ")))})),r.register("2Cwuy",(function(t,e){t.exports=Promise.all([import("./"+r("27Lyk").resolve("eBL4z")),import("./"+r("27Lyk").resolve("1Ivmr"))]).then((()=>r("5avh7")))})),r("9PVZP");
//# sourceMappingURL=weda_app.aa54a2b3.js.map
