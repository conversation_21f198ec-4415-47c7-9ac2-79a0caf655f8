// 双向转换完整测试

const { EnhancedStyleTransformer } = require('./enhancedTailwindTransformer');

console.log('=== Tailwind CSS 双向转换完整测试 ===\n');

const bidirectionalTests = [
  {
    name: '标准线性渐变',
    tailwindClasses: ['bg-gradient-to-br', 'from-blue-900', 'to-indigo-800'],
    expectedCSS: {
      background: 'linear-gradient(135deg, #1e3a8a, #3730a3)'
    }
  },
  {
    name: '多色线性渐变',
    tailwindClasses: ['bg-gradient-to-r', 'from-red-500', 'via-yellow-500', 'to-green-500'],
    expectedCSS: {
      background: 'linear-gradient(90deg, #ef4444, #eab308, #22c55e)'
    }
  },
  {
    name: '文本渐变',
    tailwindClasses: ['bg-gradient-to-r', 'from-purple-600', 'to-pink-600', 'bg-clip-text', 'text-transparent'],
    expectedCSS: {
      background: 'linear-gradient(90deg, #9333ea, #db2777)',
      backgroundClip: 'text',
      color: 'transparent'
    }
  },
  {
    name: '复杂 Grid 布局',
    tailwindClasses: ['grid-cols-[200px_minmax(900px,_1fr)_100px]'],
    expectedCSS: {
      gridTemplateColumns: '200px minmax(900px, 1fr) 100px'
    }
  },
  {
    name: '标准 Grid 布局',
    tailwindClasses: ['grid-cols-4'],
    expectedCSS: {
      gridTemplateColumns: 'repeat(4, minmax(0, 1fr))'
    }
  }
];

console.log('🔄 正向转换测试 (Tailwind → CSS):\n');

bidirectionalTests.forEach((test, index) => {
  console.log(`${index + 1}. ${test.name}`);
  console.log(`   输入: ${test.tailwindClasses.join(' ')}`);
  
  try {
    const result = EnhancedStyleTransformer.toInlineStyle(test.tailwindClasses);
    console.log(`   输出: ${JSON.stringify(result.style, null, 2)}`);
    
    // 验证关键属性
    let isCorrect = true;
    for (const [key, expectedValue] of Object.entries(test.expectedCSS)) {
      if (result.style[key] !== expectedValue) {
        console.log(`   ❌ 错误: ${key} 期望 "${expectedValue}", 实际 "${result.style[key]}"`);
        isCorrect = false;
      }
    }
    
    if (isCorrect) {
      console.log(`   ✅ 正向转换成功`);
    }
  } catch (error) {
    console.log(`   ❌ 错误: ${error.message}`);
  }
  
  console.log('');
});

console.log('\n🔄 反向转换测试 (CSS → Tailwind):\n');

const reverseTests = [
  {
    name: '标准线性渐变',
    cssStyle: {
      background: 'linear-gradient(135deg, #1e3a8a, #3730a3)'
    },
    expectedTailwind: 'bg-gradient-to-br from-blue-900 to-indigo-800'
  },
  {
    name: '简单线性渐变',
    cssStyle: {
      background: 'linear-gradient(90deg, #ef4444, #22c55e)'
    },
    expectedTailwind: 'bg-gradient-to-r from-red-500 to-green-500'
  },
  {
    name: '文本渐变',
    cssStyle: {
      background: 'linear-gradient(90deg, #9333ea, #db2777)',
      backgroundClip: 'text',
      color: 'transparent'
    },
    expectedTailwind: 'bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent'
  },
  {
    name: '复杂 Grid',
    cssStyle: {
      gridTemplateColumns: '200px minmax(900px, 1fr) 100px'
    },
    expectedTailwind: 'grid-cols-[200px_minmax(900px,_1fr)_100px]'
  },
  {
    name: '标准 Grid',
    cssStyle: {
      gridTemplateColumns: 'repeat(3, minmax(0, 1fr))'
    },
    expectedTailwind: 'grid-cols-3'
  },
  {
    name: '自定义渐变',
    cssStyle: {
      background: 'linear-gradient(45deg, #f3ec78, #af4261)'
    },
    expectedTailwind: 'bg-gradient-to-[45deg] from-[#f3ec78] to-[#af4261]'
  }
];

reverseTests.forEach((test, index) => {
  console.log(`${index + 1}. ${test.name}`);
  console.log(`   输入: ${JSON.stringify(test.cssStyle)}`);
  
  try {
    const result = EnhancedStyleTransformer.toStyle(test.cssStyle);
    console.log(`   输出: ${result}`);
    
    // 简单的包含检查（因为顺序可能不同）
    const outputWords = result.split(' ');
    const expectedWords = test.expectedTailwind.split(' ');
    
    let hasAllExpected = true;
    for (const word of expectedWords) {
      if (!outputWords.includes(word)) {
        console.log(`   ⚠️  缺少: ${word}`);
        hasAllExpected = false;
      }
    }
    
    if (hasAllExpected) {
      console.log(`   ✅ 反向转换成功`);
    } else {
      console.log(`   ⚠️  部分匹配`);
    }
  } catch (error) {
    console.log(`   ❌ 错误: ${error.message}`);
  }
  
  console.log('');
});

console.log('=== 双向转换完整性测试 ===\n');

// 测试双向转换的一致性
const roundTripTests = [
  ['bg-gradient-to-r', 'from-blue-500', 'to-purple-500'],
  ['grid-cols-3'],
  ['grid-cols-[200px_1fr_100px]']
];

roundTripTests.forEach((originalClasses, index) => {
  console.log(`${index + 1}. 往返测试: ${originalClasses.join(' ')}`);
  
  try {
    // 第一步: Tailwind → CSS
    const cssResult = EnhancedStyleTransformer.toInlineStyle(originalClasses);
    console.log(`   CSS: ${JSON.stringify(cssResult.style)}`);
    
    // 第二步: CSS → Tailwind
    const tailwindResult = EnhancedStyleTransformer.toStyle(cssResult.style);
    console.log(`   回转: ${tailwindResult}`);
    
    console.log(`   ✅ 往返转换完成\n`);
  } catch (error) {
    console.log(`   ❌ 错误: ${error.message}\n`);
  }
});

console.log('双向转换测试完成！🎉');
