// 调试缺失的渐变

console.log('=== 调试缺失的渐变 ===\n');

// 从HTML结果中提取的问题渐变
const missingGradients = [
  {
    index: 9,
    classes: [
      'bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))]',
      'from-purple-400',
      'via-pink-500', 
      'to-red-500'
    ],
    expected: 'radial-gradient(ellipse_at_center, #c084fc, #ec4899, #ef4444)',
    htmlResult: 'width: 100%; height: 100px; border-radius: 8px;' // 没有背景
  },
  {
    index: 12,
    classes: [
      'bg-[radial-gradient(ellipse_at_left,_var(--tw-gradient-stops))]',
      'from-green-300',
      'via-blue-500',
      'to-purple-600'
    ],
    expected: 'radial-gradient(ellipse_at_left, #86efac, #3b82f6, #9333ea)',
    htmlResult: 'width: 100%; height: 100px; border-radius: 8px;' // 没有背景
  },
  {
    index: 13,
    classes: [
      'bg-[conic-gradient(from_90deg_at_50%_50%,_var(--tw-gradient-stops))]',
      'from-red-500',
      'via-purple-500',
      'to-blue-500'
    ],
    expected: 'conic-gradient(from_90deg_at_50%_50%, #ef4444, #a855f7, #3b82f6)',
    htmlResult: 'width: 100%; height: 100px; border-radius: 8px;' // 没有背景
  },
  {
    index: 15,
    classes: [
      'bg-[conic-gradient(from_270deg_at_50%_50%,_var(--tw-gradient-stops))]',
      'from-green-400',
      'to-blue-500'
    ],
    expected: 'conic-gradient(from_270deg_at_50%_50%, #4ade80, #3b82f6)',
    htmlResult: 'width: 100%; height: 100px; border-radius: 8px;' // 没有背景
  },
  {
    index: 16,
    classes: [
      'bg-[conic-gradient(from_0deg_at_50%_50%,_var(--tw-gradient-stops))]',
      'from-pink-500',
      'via-red-500',
      'to-yellow-500'
    ],
    expected: 'conic-gradient(from_0deg_at_50%_50%, #ec4899, #ef4444, #f59e0b)',
    htmlResult: 'width: 100%; height: 100px; border-radius: 8px;' // 没有背景
  },
  {
    index: 17,
    classes: ['bg-[linear-gradient(45deg,_#f3ec78,_#af4261)]'],
    expected: 'linear-gradient(45deg, #f3ec78, #af4261)',
    htmlResult: 'width: 100%; height: 100px; border-radius: 8px;' // 没有背景
  },
  {
    index: 18,
    classes: ['bg-[linear-gradient(90deg,_rgba(131,58,180,1)_0%,_rgba(253,29,29,1)_50%,_rgba(252,176,69,1)_100%)]'],
    expected: 'linear-gradient(90deg, rgba(131,58,180,1) 0%, rgba(253,29,29,1) 50%, rgba(252,176,69,1) 100%)',
    htmlResult: 'width: 100%; height: 100px; border-radius: 8px;' // 没有背景
  },
  {
    index: 19,
    classes: ['bg-[radial-gradient(circle_at_10%_20%,_rgb(255,200,124)_0%,_rgb(252,251,121)_90%)]'],
    expected: 'radial-gradient(circle_at_10%_20%, rgb(255,200,124) 0%, rgb(252,251,121) 90%)',
    htmlResult: 'width: 100%; height: 100px; border-radius: 8px;' // 没有背景
  },
  {
    index: 20,
    classes: ['bg-[conic-gradient(at_125%_50%,_#b78cf7,_#ff7c94,_#ffcf0d,_#ff7c94,_#b78cf7)]'],
    expected: 'conic-gradient(at_125%_50%, #b78cf7, #ff7c94, #ffcf0d, #ff7c94, #b78cf7)',
    htmlResult: 'width: 100%; height: 100px; border-radius: 8px;' // 没有背景
  }
];

console.log('🔍 缺失渐变分析:\n');

missingGradients.forEach((gradient, index) => {
  console.log(`${index + 1}. 第${gradient.index}个渐变`);
  console.log(`   类名: ${gradient.classes.join(' ')}`);
  console.log(`   期望: ${gradient.expected}`);
  console.log(`   实际: ${gradient.htmlResult}`);
  console.log(`   问题: 没有生成背景样式\n`);
});

console.log('=== 问题分类 ===\n');

console.log('📊 缺失渐变类型统计:');
console.log('   • 包含 var(--tw-gradient-stops) 的复杂渐变: 5个');
console.log('   • 纯自定义渐变 (无 var): 4个');
console.log('   • 总计缺失: 9个');
console.log('');

console.log('🔧 可能的原因:');
console.log('   1. parseComplexGradientWithColors() 没有正确识别某些格式');
console.log('   2. 自定义渐变的处理逻辑有问题');
console.log('   3. 类名解析的正则表达式不够完整');
console.log('   4. 处理顺序导致某些渐变被跳过');
console.log('');

console.log('🎯 需要检查的具体问题:');
console.log('   • 为什么第9个和第12个 radial-gradient 没有被处理？');
console.log('   • 为什么第13、15、16个 conic-gradient 没有被处理？');
console.log('   • 为什么第17-20个自定义渐变没有被处理？');
console.log('');

console.log('💡 调试建议:');
console.log('   1. 检查 parseComplexGradientWithColors() 的匹配逻辑');
console.log('   2. 验证自定义渐变的处理路径');
console.log('   3. 添加更多调试日志来跟踪处理流程');
console.log('   4. 确保所有渐变类型都有对应的处理逻辑');

console.log('\n🚨 需要立即修复的问题: 9个渐变完全没有背景样式！');
