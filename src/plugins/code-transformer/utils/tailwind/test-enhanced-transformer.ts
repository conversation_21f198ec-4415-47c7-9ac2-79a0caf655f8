// 由于运行环境限制，我们先创建一个简化的测试
// import { EnhancedStyleTransformer } from './enhancedTailwindTransformer';

// 模拟测试数据
const testCases = {
  grid: {
    complexStyle: { gridTemplateColumns: '200px minmax(900px, 1fr) 100px' },
    complexClasses: ['grid-cols-[200px_minmax(900px,_1fr)_100px]'],
    standardStyle: { gridTemplateColumns: 'repeat(3, minmax(0, 1fr))' },
    standardClasses: ['grid-cols-3'],
  },
  gradient: {
    complexStyle: {
      background:
        'linear-gradient(45deg, rgba(255, 0, 0, 0.8) 0%, rgba(0, 255, 0, 0.6) 50%, rgba(0, 0, 255, 0.9) 100%)',
    },
    textGradientStyle: {
      background: 'linear-gradient(90deg, #ff0000, #00ff00)',
      backgroundClip: 'text',
      color: 'transparent',
    },
    complexClasses: [
      'bg-gradient-to-tr',
      'from-[rgba(255,0,0,0.8)]',
      'via-[rgba(0,255,0,0.6)]',
      'to-[rgba(0,0,255,0.9)]',
    ],
    textGradientClasses: [
      'bg-gradient-to-r',
      'from-[#ff0000]',
      'to-[#00ff00]',
      'bg-clip-text',
      'text-transparent',
    ],
  },
};

// 测试 Grid 转换
console.log('=== Grid 转换测试 ===');

// 测试 1: 复杂 Grid 列定义 (style -> tailwind)
const complexGridStyle = {
  gridTemplateColumns: '200px minmax(900px, 1fr) 100px',
};
const gridTailwind = EnhancedStyleTransformer.toStyle(complexGridStyle);
console.log('复杂 Grid 列定义:', gridTailwind);
// 期望输出: grid-cols-[200px_minmax(900px,_1fr)_100px]

// 测试 2: 复杂 Grid 类名转回样式 (tailwind -> style)
const complexGridClasses = ['grid-cols-[200px_minmax(900px,_1fr)_100px]'];
const gridStyleResult = EnhancedStyleTransformer.toInlineStyle(complexGridClasses);
console.log('复杂 Grid 类名转样式:', gridStyleResult);
// 期望输出: { gridTemplateColumns: '200px minmax(900px, 1fr) 100px' }

// 测试 3: 标准 Grid 转换
const standardGridStyle = {
  gridTemplateColumns: 'repeat(3, minmax(0, 1fr))',
};
const standardGridTailwind = EnhancedStyleTransformer.toStyle(standardGridStyle);
console.log('标准 Grid 转换:', standardGridTailwind);
// 期望输出: grid-cols-3

const standardGridClasses = ['grid-cols-3'];
const standardGridStyleResult = EnhancedStyleTransformer.toInlineStyle(standardGridClasses);
console.log('标准 Grid 类名转样式:', standardGridStyleResult);
// 期望输出: { gridTemplateColumns: 'repeat(3, minmax(0, 1fr))' }

console.log('\n=== 渐变转换测试 ===');

// 测试 4: 复杂渐变 (style -> tailwind)
const complexGradientStyle = {
  background:
    'linear-gradient(45deg, rgba(255, 0, 0, 0.8) 0%, rgba(0, 255, 0, 0.6) 50%, rgba(0, 0, 255, 0.9) 100%)',
};
const gradientTailwind = EnhancedStyleTransformer.toStyle(complexGradientStyle);
console.log('复杂渐变转换:', gradientTailwind);

// 测试 5: 文本渐变 (style -> tailwind)
const textGradientStyle = {
  background: 'linear-gradient(90deg, #ff0000, #00ff00)',
  backgroundClip: 'text',
  color: 'transparent',
};
const textGradientTailwind = EnhancedStyleTransformer.toStyle(textGradientStyle);
console.log('文本渐变转换:', textGradientTailwind);

// 测试 6: 复杂渐变类名转回样式 (tailwind -> style)
const complexGradientClasses = [
  'bg-gradient-to-tr',
  'from-[rgba(255,0,0,0.8)]',
  'via-[rgba(0,255,0,0.6)]',
  'to-[rgba(0,0,255,0.9)]',
];
const gradientStyleResult = EnhancedStyleTransformer.toInlineStyle(complexGradientClasses);
console.log('复杂渐变类名转样式:', gradientStyleResult);

// 测试 7: 文本渐变类名转回样式
const textGradientClasses = [
  'bg-gradient-to-r',
  'from-[#ff0000]',
  'to-[#00ff00]',
  'bg-clip-text',
  'text-transparent',
];
const textGradientStyleResult = EnhancedStyleTransformer.toInlineStyle(textGradientClasses);
console.log('文本渐变类名转样式:', textGradientStyleResult);

console.log('\n=== 边界情况测试 ===');

// 测试 8: 带角度的自定义渐变
const customAngleGradientStyle = {
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
};
const customAngleGradientTailwind = EnhancedStyleTransformer.toStyle(customAngleGradientStyle);
console.log('自定义角度渐变:', customAngleGradientTailwind);

// 测试 9: 复杂 Grid 行定义
const complexGridRowStyle = {
  gridTemplateRows: '100px auto 1fr 50px',
};
const gridRowTailwind = EnhancedStyleTransformer.toStyle(complexGridRowStyle);
console.log('复杂 Grid 行定义:', gridRowTailwind);

const complexGridRowClasses = ['grid-rows-[100px_auto_1fr_50px]'];
const gridRowStyleResult = EnhancedStyleTransformer.toInlineStyle(complexGridRowClasses);
console.log('复杂 Grid 行类名转样式:', gridRowStyleResult);

// 测试 10: 多色渐变（超过3种颜色）
const multiColorGradientClasses = [
  'bg-gradient-to-r',
  'from-[#ff0000]',
  'via-[#00ff00]',
  'via-[#0000ff]',
  'to-[#ffff00]',
];
const multiColorGradientResult = EnhancedStyleTransformer.toInlineStyle(multiColorGradientClasses);
console.log('多色渐变类名转样式:', multiColorGradientResult);
