import { parse } from '@babel/parser';
import traverse from '@babel/traverse';
import generate from '@babel/generator';
import * as t from '@babel/types';
import { IPublicTypeNodeSchema, IPublicTypeRootSchema } from '@alilc/lowcode-types';
import { ParseError, TransformError } from '../types';
import { classToStyle } from './classToStyle';
import { isJSExpression } from '@alilc/lowcode-utils';
import { validateJsxContent } from '../jsxValidator';

const logger: any = function (msg) {};

export class CodeToSchemaTransformer {
  private currentLoopArgs: string[] = [];
  private allLoopArgs: string[] = [];
  private currentSlotParams: string[] = [];
  private componentState: Record<string, any> = {};
  // 新增：用于收集用到的组件名
  private usedComponentNames: Set<string> = new Set();

  async transform(code: string): Promise<{
    methods: Array<{ name: string; desc: string; code: string }>;
    schema: IPublicTypeNodeSchema;
    usedComponentNames: string[];
  }> {
    // 每次transform前清空组件名集合
    this.usedComponentNames.clear();
    let classDepth = 0; // 新增：用于判断class嵌套层级
    let inRootClass = false; // 新增：标记当前是否在根 class
    try {
      // 1. 解析 JSX 代码为 AST
      const ast = parse(code, {
        sourceType: 'module',
        plugins: ['jsx', 'typescript'],
      });

      console.log('ast: ', ast);

      // 2. 遍历 AST 构建 schema 和解析方法
      let rootSchema: IPublicTypeNodeSchema | null = null;
      let isFindClassRenderRoot = false;
      const methods: Array<{ name: string; desc: string; code: string }> = [];

      traverse(ast, {
        ClassDeclaration: {
          enter(path) {
            classDepth++;
            if (classDepth === 1) {
              inRootClass = true; // 进入根 class
              // 只检查根部 class
              const hasConstructor = path.node.body.body.some(
                (member) => t.isClassMethod(member) && member.kind === 'constructor',
              );
              if (hasConstructor) {
                throw new Error('根部 React 组件 class 不允许使用 constructor');
              }
            }
          },
          exit(path) {
            if (classDepth === 1) {
              inRootClass = false; // 离开根 class
            }
            classDepth--;
          },
        },
        ClassProperty: (path) => {
          // 检查是否是类的直接属性（而不是嵌套在其他方法内部的属性）
          if (!inRootClass) {
            return; // 只收集根 class 的属性
          }
          if (!t.isClassBody(path.parent)) {
            return; // 跳过嵌套属性
          }

          if (t.isIdentifier(path.node.key)) {
            const propertyName = path.node.key.name;

            // 处理 state
            if (propertyName === 'state') {
              this.processClassProperty(path.node);
              return;
            }

            // 处理箭头函数属性
            if (t.isArrowFunctionExpression(path.node.value)) {
              // 检查是否已经收集过同名方法，避免重复收集
              const existingMethod = methods.find((m) => m.name === propertyName);
              if (existingMethod) {
                console.log(`跳过重复属性方法: ${propertyName}`);
                return;
              }

              // 获取属性的注释
              const comments = path.node.leadingComments || [];
              const desc = comments
                .map((comment) => comment.value.trim())
                .map((comment) => comment.replace(/^\*+/, '').trim()) // 移除开头的星号
                .join('\n');

              // 获取箭头函数的参数
              const params = path.node.value.params
                .map((param) => {
                  if (t.isIdentifier(param)) {
                    return param.name;
                  } else if (t.isObjectPattern(param)) {
                    // 处理解构参数
                    return `{${param.properties
                      .map((prop) => {
                        if (t.isObjectProperty(prop) && t.isIdentifier(prop.key)) {
                          return prop.key.name;
                        }
                        return '';
                      })
                      .filter(Boolean)
                      .join(', ')}}`;
                  } else if (t.isAssignmentPattern(param)) {
                    // 处理带默认值的参数
                    const paramName = t.isIdentifier(param.left) ? param.left.name : '';
                    const defaultValue = generate(param.right).code;
                    return paramName ? `${paramName} = ${defaultValue}` : '';
                  }
                  return '';
                })
                .filter(Boolean);

              // 获取函数体代码
              let bodyCode = '';
              if (t.isBlockStatement(path.node.value.body)) {
                bodyCode = generate(path.node.value.body).code;
                // 移除多余的大括号
                bodyCode = bodyCode.replace(/^{|}$/g, '').trim();
              } else {
                // 如果是表达式体，需要添加 return
                bodyCode = `return ${generate(path.node.value.body).code};`;
              }

              // 构建完整的函数代码
              const isAsync = path.node.value.async;
              const functionCode = isAsync
                ? `async function ${propertyName}(${params.join(', ')}) {\n${bodyCode}\n}`
                : `function ${propertyName}(${params.join(', ')}) {\n${bodyCode}\n}`;

              // 添加到方法列表
              methods.push({
                name: propertyName,
                desc,
                code: functionCode.trim(),
              });
            }
          }
        },
        CallExpression: (path) => {
          if (t.isIdentifier(path.node.callee) && path.node.callee.name === 'useState') {
            const parent = path.findParent((p) => t.isVariableDeclarator(p));
            if (parent && t.isVariableDeclarator(parent.node)) {
              if (t.isArrayPattern(parent.node.id)) {
                const stateName = parent.node.id.elements[0]?.name;
                if (stateName) {
                  const { code: initValue } = generate(parent.node.init);

                  // 尝试解析原始值
                  try {
                    // 首先尝试直接解析JSON
                    const parsedValue = JSON.parse(initValue.trim());
                    this.componentState[stateName] = parsedValue;
                  } catch {
                    // 如果直接解析失败，尝试将字符串转换为可执行的JavaScript表达式
                    try {
                      // 对于形如 [{ ... }] 或 { ... } 的字符串，尝试将其转换为有效的JavaScript表达式
                      // 注意：这里使用eval是安全的，因为我们只处理来自代码的静态值
                      const jsExpression = initValue.trim();
                      // 使用Function构造函数而不是直接eval，更安全
                      const evalFunc = new Function('return ' + jsExpression);
                      const evalValue = evalFunc();

                      // 确保结果是一个有效的值
                      if (evalValue !== undefined) {
                        this.componentState[stateName] = evalValue;
                      } else {
                        // 如果解析失败，保持原始字符串值
                        this.componentState[stateName] = initValue.trim();
                      }
                    } catch {
                      // 如果所有解析方法都失败，保持原始字符串值
                      this.componentState[stateName] = initValue.trim();
                    }
                  }
                }
              }
            }
          }
        },
        ClassMethod: (path) => {
          // 检查是否是类的直接方法（而不是嵌套在其他方法内部的函数）
          if (!inRootClass) {
            return; // 只收集根 class 的方法
          }
          if (!t.isClassBody(path.parent)) {
            return; // 跳过嵌套函数
          }

          // 获取方法名
          const methodName = path.node.key.name;

          // 处理 render 方法
          if (methodName === 'render') {
            const returnStatement = path.node.body.body.find(
              (node) => node.type === 'ReturnStatement',
            );
            if (returnStatement && returnStatement.argument) {
              rootSchema = this.transformJSXElement(returnStatement.argument);
              console.log('rootSchema Name 1: ', rootSchema.componentName);
              isFindClassRenderRoot = true;
            }
            return; // 直接返回，不处理 render 方法
          }

          // 检查是否已经收集过同名方法，避免重复收集
          const existingMethod = methods.find((m) => m.name === methodName);
          if (existingMethod) {
            console.log(`跳过重复方法: ${methodName}`);
            return;
          }

          // 获取方法的注释
          const comments = path.node.leadingComments || [];
          const desc = comments
            .map((comment) => comment.value.trim())
            .map((comment) => comment.replace(/^\*+/, '').trim()) // 移除开头的星号
            .join('\n');

          // 获取方法的参数
          const params = path.node.params
            .map((param) => {
              if (t.isIdentifier(param)) {
                return param.name;
              } else if (t.isObjectPattern(param)) {
                // 处理解构参数
                return `{${param.properties
                  .map((prop) => {
                    if (t.isObjectProperty(prop) && t.isIdentifier(prop.key)) {
                      return prop.key.name;
                    }
                    return '';
                  })
                  .filter(Boolean)
                  .join(', ')}}`;
              } else if (t.isAssignmentPattern(param)) {
                // 处理带默认值的参数
                const paramName = t.isIdentifier(param.left) ? param.left.name : '';
                const defaultValue = generate(param.right).code;
                return paramName ? `${paramName} = ${defaultValue}` : '';
              }
              return '';
            })
            .filter(Boolean);

          // 获取方法代码并转换为独立函数
          const methodCode = generate(path.node.body).code;
          const isAsync = path.node.async;
          // 移除多余的大括号
          const cleanCode = methodCode.replace(/^{|}$/g, '').trim();
          const functionCode = isAsync
            ? `async function ${methodName}(${params.join(', ')}) {\n${cleanCode}\n}`
            : `function ${methodName}(${params.join(', ')}) {\n${cleanCode}\n}`;

          // 添加到方法列表
          methods.push({
            name: methodName,
            desc,
            code: functionCode.trim(),
          });

          // 检查是否是根组件
          const returnStatement = path.node.body.body.find(
            (node) => node.type === 'ReturnStatement',
          );
          if (returnStatement && returnStatement.argument) {
            rootSchema = this.transformJSXElement(returnStatement.argument);
            console.log('rootSchema Name 2: ', rootSchema.componentName);
          }
        },
        FunctionDeclaration: (path) => {
          // 检查是否是顶级函数声明（而不是嵌套在类方法或其他函数内部）
          const isTopLevel =
            t.isProgram(path.parent) ||
            (t.isExportDefaultDeclaration(path.parent) && t.isProgram(path.parent.parent)) ||
            (t.isExportNamedDeclaration(path.parent) && t.isProgram(path.parent.parent));

          if (!isTopLevel) {
            return; // 跳过嵌套函数声明
          }

          // 获取函数名
          const functionName = path.node.id?.name;
          if (!functionName) return;

          // 获取函数源代码，保留内部注释
          const { code: functionCode } = generate(path.node, {
            retainLines: false,
            retainFunctionParens: true,
            comments: true, // 保留注释
            compact: false,
            concise: false,
            jsescOption: {
              minimal: true,
            },
          });

          // 获取函数的注释
          const comments = path.node.leadingComments || [];
          const desc = comments.map((comment) => comment.value.trim()).join('\n');

          // 添加到方法列表
          methods.push({
            name: functionName,
            desc,
            code: functionCode.trim(),
          });

          // 检查是否是根组件
          const returnStatement = path.node.body.body.find(
            (node) => node.type === 'ReturnStatement',
          );
          if (returnStatement && returnStatement.argument) {
            rootSchema = this.transformJSXElement(returnStatement.argument);
            console.log('rootSchema Name 2: ', rootSchema.componentName);
          }
        },
        ArrowFunctionExpression: (path) => {
          // 只处理顶级的箭头函数组件（通常是 export default 或变量声明中的）
          const isTopLevelComponent =
            t.isVariableDeclarator(path.parent) &&
            t.isVariableDeclaration(path.parent.parent) &&
            (t.isProgram(path.parent.parent.parent) ||
              t.isExportDefaultDeclaration(path.parent.parent.parent) ||
              t.isExportNamedDeclaration(path.parent.parent.parent));

          if (!isTopLevelComponent) {
            return; // 跳过嵌套的箭头函数
          }

          if (t.isBlockStatement(path.node.body)) {
            const returnStatement = path.node.body.body.find(
              (node) => node.type === 'ReturnStatement',
            );
            if (returnStatement && returnStatement.argument) {
              const nodeSchema = this.transformJSXElement(returnStatement.argument);
              if (nodeSchema?.componentName) {
                rootSchema = nodeSchema;
                console.log(
                  'rootSchema Name 3: ',
                  rootSchema.componentName,
                  returnStatement.argument,
                );
                isFindClassRenderRoot = true;
              }
            }
          } else if (t.isJSXElement(path.node.body)) {
            if (!isFindClassRenderRoot) {
              rootSchema = this.transformJSXElement(path.node.body);
              console.log('rootSchema Name: 4', rootSchema.componentName);
            }
          }
        },
      });

      // 在保存文件前添加规范检查
      const validateErrors = validateJsxContent(code);
      if (validateErrors.length > 0) {
        throw new Error(`JSX规范检查失败:\n${validateErrors.join('\n')}`);
      }

      if (!rootSchema) {
        throw new Error('未找到根组件');
      }

      return {
        methods,
        schema: {
          ...rootSchema,
          ...(Object.keys(this.componentState).length > 0 ? { state: this.componentState } : {}),
        } as IPublicTypeNodeSchema & { state?: Record<string, any> },
        usedComponentNames: this.getUsedComponentNames(),
      };
    } catch (error) {
      if (error instanceof ParseError) {
        throw new TransformError('JSX 解析错误', error);
      }
      throw error;
    }
  }

  private checkDestructure(code) {
    // 使用正则表达式匹配解构模式
    const destructuringFromThisRegex = /(?:const|let)\s*{([^}]*)}\s*=\s*this\s*;/;
    const matches = code.match(destructuringFromThisRegex);
    if (matches) {
      console.log('【transformArrowFunctionToNormalFunction】检测到从this解构的模式');
      console.log(
        '解构的变量列表:',
        matches[1]
          .trim()
          .split(',')
          .map((s) => s.trim()),
      );
      // TODO: 这里可以添加处理解构的逻辑
      return false;
    }
    return true;
  }

  private transformJSFunctionAddThis(sourceCode: string, functionParams: string[] = []) {
    let code = sourceCode;
    // 检查函数体中是否包含循环变量或作用域变量
    const hasLoopVars = this.currentLoopArgs.some((arg) => code.includes(arg));
    const hasSlotParams = this.currentSlotParams.some((param) => code.includes(param));
    console.log(`【transformArrowFunctionToNormalFunction】是否包含循环变量:`, hasLoopVars);
    console.log(`【transformArrowFunctionToNormalFunction】是否包含作用域变量:`, hasSlotParams);

    if (hasLoopVars || hasSlotParams) {
      // 如果包含循环变量或作用域变量，需要确保正确添加this前缀
      // 处理循环变量
      this.currentLoopArgs.forEach((arg) => {
        // 检查是否是函数参数，如果是函数参数则跳过
        if (functionParams.includes(arg)) {
          console.log(
            `【transformArrowFunctionToNormalFunction】${arg} 是函数参数，跳过添加this前缀`,
          );
          return;
        }

        // 修改正则表达式，确保不会匹配到对象属性名
        // 使用负向前瞻，确保后面不是冒号
        const pattern = new RegExp(`\\b${arg}\\b(?!\\s*:)`, 'g');

        if (pattern.test(code)) {
          // 检查是否已经添加了this前缀
          if (code.includes(`this.${arg}`)) {
            console.log(
              `【transformArrowFunctionToNormalFunction】循环变量 ${arg} 已经有this前缀，跳过`,
            );
            return;
          }
          if (!this.checkDestructure(code)) {
            return;
          }

          console.log(`【transformArrowFunctionToNormalFunction】为循环变量 ${arg} 添加this前缀`);
          code = code.replace(pattern, `this.${arg}`);
        }
      });

      // 处理作用域变量
      this.currentSlotParams.forEach((param) => {
        // 检查是否是函数参数，如果是函数参数则跳过
        if (functionParams.includes(param)) {
          console.log(
            `【transformArrowFunctionToNormalFunction】${param} 是函数参数，跳过添加this前缀`,
          );
          return;
        }

        // 修改正则表达式，确保不会匹配到对象属性名
        // 使用负向前瞻，确保后面不是冒号
        const pattern = new RegExp(`\\b${param}\\b(?!\\s*:)`, 'g');
        if (pattern.test(code)) {
          // 检查是否已经添加了this前缀
          if (code.includes(`this.${param}`)) {
            console.log(
              `【transformArrowFunctionToNormalFunction】作用域变量 ${param} 已经有this前缀，跳过`,
            );
            return;
          }
          if (!this.checkDestructure(code)) {
            return;
          }
          console.log(
            `【transformArrowFunctionToNormalFunction】为作用域变量 ${param} 添加this前缀`,
          );
          code = code.replace(pattern, `this.${param}`);
        }
      });

      // 特殊处理对象字面量中的简写属性
      // 匹配形如 { item: record, index } 中的 index
      const objectPattern = /{[^}]*}/g;
      const matches = code.match(objectPattern);
      if (matches) {
        console.log(`【transformArrowFunctionToNormalFunction】找到对象字面量:`, matches);

        matches.forEach((match) => {
          console.log(`【transformArrowFunctionToNormalFunction】处理对象字面量:`, match);

          // 分割对象字面量的属性
          const props = match
            .slice(1, -1)
            .split(',')
            .map((p) => p.trim())
            .filter((p) => p);
          console.log(`【transformArrowFunctionToNormalFunction】分割的属性:`, props);

          // 处理每个属性
          const processedProps = props.map((prop) => {
            // 如果属性包含冒号，说明不是简写形式
            if (prop.includes(':')) {
              return prop;
            }

            // 检查是否是 this.变量名 的形式
            const thisPattern = /^this\.(\w+)$/;
            const thisMatch = prop.match(thisPattern);
            if (thisMatch) {
              const varName = thisMatch[1];
              console.log(`【transformArrowFunctionToNormalFunction】发现this.变量形式:`, varName);
              return `${varName}: this.${varName}`;
            }

            return prop;
          });

          // 重新组合对象字面量
          const newObjectLiteral = `{ ${processedProps.join(', ')} }`;
          console.log(
            `【transformArrowFunctionToNormalFunction】处理后的对象字面量:`,
            newObjectLiteral,
          );

          code = code.replace(match, newObjectLiteral);
        });
      }

      // 修复可能出现的语法错误
      code = code
        .replace(/,\s*}/g, ' }') // 移除对象末尾的逗号
        .replace(/{\s*,/g, '{ ') // 移除对象开头的逗号
        .replace(/,\s*,/g, ','); // 移除重复的逗号

      console.log(`【transformArrowFunctionToNormalFunction】最终处理结果:`, code);
    }
    return code;
  }

  private transformJSXAttributeValue(
    value: t.JSXAttribute['value'],
    componentName?: string,
    attrName?: string,
  ): any {
    console.log(
      `【transformJSXAttributeValue】开始处理属性值 - 组件:${componentName}, 属性:${attrName}`,
    );

    console.log('value: ', value);

    if (value === null) {
      console.log(`【transformJSXAttributeValue】属性值为null，返回true`);
      return true;
    }

    if (t.isStringLiteral(value)) {
      console.log(`【transformJSXAttributeValue】处理字符串字面量: "${value.value}"`);
      return value.value;
    }

    // 处理特殊的条件属性
    if (t.isJSXExpressionContainer(value) && t.isLogicalExpression(value.expression)) {
      console.log(`【transformJSXAttributeValue】处理逻辑表达式: ${value.expression.operator}`);

      // 检查逻辑表达式的右侧是否为JSX元素
      if (t.isJSXElement(value.expression.right)) {
        console.log(`【transformJSXAttributeValue】逻辑表达式中包含JSX元素，转换为JSSlot`);

        // 获取条件表达式
        const leftCode = generate(value.expression.left).code;
        let conditionValue = leftCode;

        // 处理条件表达式中的变量引用
        this.currentSlotParams.forEach((param) => {
          const paramPattern = new RegExp(`\\b${param}\\b(?!\\s*[:=])`, 'g');
          if (paramPattern.test(conditionValue)) {
            console.log(
              `【transformJSXAttributeValue】为条件表达式中的JSSlot参数 ${param} 添加this前缀`,
            );
            conditionValue = conditionValue.replace(paramPattern, `this.${param}`);
          }
        });

        this.allLoopArgs.forEach((arg) => {
          const argPattern = new RegExp(`\\b${arg}\\b(?!\\s*[:=])`, 'g');
          if (argPattern.test(conditionValue) && !conditionValue.includes(`this.${arg}`)) {
            console.log(
              `【transformJSXAttributeValue】为条件表达式中的循环变量 ${arg} 添加this前缀`,
            );
            conditionValue = conditionValue.replace(argPattern, `this.${arg}`);
          }
        });

        // 转换JSX元素为schema
        const jsxSchema = this.transformJSXElement(value.expression.right);

        // 为JSX元素添加条件
        jsxSchema.condition = {
          type: 'JSExpression',
          value: conditionValue,
        };

        return {
          type: 'JSSlot',
          value: [jsxSchema],
        };
      }

      return {
        type: 'JSExpression',
        value: this.transformLogicalExpression(value.expression),
      };
    }

    if (t.isJSXElement(value)) {
      console.log(`【transformJSXAttributeValue】处理JSX元素`);
      return {
        type: 'JSSlot',
        value: [this.transformJSXElement(value)],
      };
    }

    if (t.isJSXExpressionContainer(value)) {
      const expression = value.expression;
      console.log(
        `【transformJSXAttributeValue】处理JSX表达式容器，表达式类型: ${expression.type}`,
      );

      // 处理字面量值，直接返回原始值
      if (t.isNumericLiteral(expression)) {
        console.log(`【transformJSXAttributeValue】处理数字字面量: ${expression.value}`);
        return expression.value;
      }
      if (t.isBooleanLiteral(expression)) {
        console.log(`【transformJSXAttributeValue】处理布尔字面量: ${expression.value}`);
        return expression.value;
      }
      if (t.isStringLiteral(expression)) {
        console.log(`【transformJSXAttributeValue】处理字符串字面量: "${expression.value}"`);
        return expression.value;
      }
      if (t.isNullLiteral(expression)) {
        console.log(`【transformJSXAttributeValue】处理null字面量`);
        return null;
      }

      // 处理立即执行的箭头函数表达式
      if (t.isCallExpression(expression) && t.isArrowFunctionExpression(expression.callee)) {
        console.log(`【transformJSXAttributeValue】处理立即执行的箭头函数表达式`);
        const arrowFunction = expression.callee;
        // 保持箭头函数的形式，直接返回原始代码
        const { code } = generate(expression);
        return {
          type: 'JSExpression',
          value: code,
        };
      }

      // 处理 function(...).bind(this) 表达式
      if (
        t.isCallExpression(expression) &&
        t.isMemberExpression(expression.callee) &&
        t.isIdentifier(expression.callee.property) &&
        expression.callee.property.name === 'bind' &&
        t.isFunctionExpression(expression.callee.object)
      ) {
        console.log(`【transformJSXAttributeValue】处理 function(...).bind(this) 表达式`);
        const functionExpr = expression.callee.object;

        // 检查函数是否返回 JSX 元素
        const returnStatement = functionExpr.body.body.find((stmt) => t.isReturnStatement(stmt));
        if (
          returnStatement &&
          t.isReturnStatement(returnStatement) &&
          returnStatement.argument &&
          t.isJSXElement(returnStatement.argument)
        ) {
          console.log(
            `【transformJSXAttributeValue】function(...).bind(this) 返回JSX元素，转换为JSSlot`,
          );

          // 提取函数参数名称
          const params = functionExpr.params.map((param) => {
            if (t.isIdentifier(param)) {
              return param.name;
            }
            return '...'; // 对于复杂参数模式，简化处理
          });

          console.log(`【transformJSXAttributeValue】bind函数参数:`, params);

          // 保存当前的参数列表
          const prevSlotParams = [...this.currentSlotParams];
          // 更新当前的JSSlot参数
          this.currentSlotParams = params;

          console.log(`【DEBUG】设置JSSlot参数:`, this.currentSlotParams);

          try {
            // 处理JSX元素，此时会使用currentSlotParams
            const jsxElement = this.transformJSXElement(returnStatement.argument);

            return {
              type: 'JSSlot',
              params: params?.length ? params : [''], // 添加params字段，包含函数参数名称
              value: [jsxElement],
            };
          } finally {
            // 恢复之前的参数列表
            this.currentSlotParams = prevSlotParams;
            console.log(`【DEBUG】恢复JSSlot参数:`, this.currentSlotParams);
          }
        }
      }

      // 特殊处理Text组件的children属性，但首先检查是否为JSXElement
      if (componentName === 'Text' && attrName === 'children') {
        console.log(`【transformJSXAttributeValue】特殊处理Text组件的children属性:`, expression);

        // 如果children是JSXElement，应该返回JSSlot而不是JSExpression
        if (t.isJSXElement(expression)) {
          console.log(`【transformJSXAttributeValue】Text组件children是JSX元素，转换为JSSlot`);
          return {
            type: 'JSSlot',
            value: [this.transformJSXElement(expression)],
          };
        }

        // 获取表达式的原始代码
        const { code } = generate(expression);
        console.log(`【transformJSXAttributeValue】Text组件children表达式原始代码: "${code}"`);
        console.log(`【transformJSXAttributeValue】当前循环变量:`, this.currentLoopArgs);
        console.log(`【transformJSXAttributeValue】当前JSSlot参数:`, this.currentSlotParams);

        // 添加调试代码，检查是否包含record
        if (code.includes('record')) {
          console.log(
            `【DEBUG】发现record引用，检查是否在JSSlot参数中:`,
            this.currentSlotParams.includes('record'),
            '当前JSSlot参数:',
            this.currentSlotParams,
          );
        }

        // 处理循环变量引用，确保正确添加this前缀
        let expressionValue = code;

        // 先检查是否已经有重复的this前缀，如this.this.item，将其修正为this.item
        const thisRepeatPattern1 = /this\.this\./g;
        if (thisRepeatPattern1.test(expressionValue)) {
          console.log(`【transformJSXAttributeValue】修复重复的this前缀`);
          expressionValue = expressionValue.replace(thisRepeatPattern1, 'this.');
        }

        // 检查当前循环变量和所有循环变量
        console.log(`【transformJSXAttributeValue】当前循环变量:`, this.currentLoopArgs);
        console.log(`【transformJSXAttributeValue】所有循环变量:`, this.allLoopArgs);

        // 替换形如 this.item 的引用为 item（临时替换，后面会再添加this前缀）
        this.allLoopArgs.forEach((arg) => {
          const thisPattern = new RegExp(`this\\.${arg}\\b`, 'g');
          if (thisPattern.test(expressionValue)) {
            console.log(`【transformJSXAttributeValue】替换 this.${arg} 为 ${arg}`);
            expressionValue = expressionValue.replace(thisPattern, arg);
          }
        });

        // 处理JSSlot参数，移除已有的this前缀（如果有）
        this.currentSlotParams.forEach((param) => {
          const thisPattern = new RegExp(`this\\.${param}\\b`, 'g');
          if (thisPattern.test(expressionValue)) {
            console.log(`【transformJSXAttributeValue】替换 this.${param} 为 ${param}`);
            expressionValue = expressionValue.replace(thisPattern, param);
          }
        });

        // 现在为所有循环变量添加this前缀
        this.allLoopArgs.forEach((arg) => {
          // 先处理带属性的循环变量，使用全局替换
          const argWithPropPattern = new RegExp(
            `\\b${arg}((?:\\?\\.|\\.)[\\w$]+(?:(?:\\?\\.|\\.)[\\w$]+)*)`,
            'g',
          );
          console.log('argWithPropPattern: ', argWithPropPattern, expressionValue);

          // 直接进行全局替换，但要避免重复添加this前缀
          expressionValue = expressionValue.replace(argWithPropPattern, (match, p1) => {
            // 检查这个匹配是否已经有this前缀
            if (match.startsWith(`this.${arg}`)) {
              return match; // 已经有this前缀，保持不变
            }
            console.log(
              `【transformJSXAttributeValue】为带属性的循环变量 ${arg} 添加this前缀: ${match} -> this.${arg}${p1}`,
            );
            return `this.${arg}${p1}`;
          });

          // 再处理独立的循环变量
          const argPattern = new RegExp(`\\b${arg}\\b(?!\\s*=)(?!\\.|\\\")`, 'g');
          expressionValue = expressionValue.replace(argPattern, (match, offset, str) => {
            // 检查前面是否已经有this前缀
            const beforeMatch = str.substring(Math.max(0, offset - 5), offset);
            if (beforeMatch.endsWith('this.')) {
              return match; // 已经有this前缀，保持不变
            }
            console.log(
              `【transformJSXAttributeValue】为独立循环变量 ${arg} 添加this前缀: ${match} -> this.${arg}`,
            );
            return `this.${arg}`;
          });
        });

        // 为所有JSSlot参数添加this前缀
        this.currentSlotParams.forEach((param) => {
          // 匹配独立的JSSlot参数（不是作为对象属性的一部分）
          const paramPattern = new RegExp(`\\b${param}\\b(?!\\s*=)(?!\\.|\\\")`, 'g');
          if (paramPattern.test(expressionValue)) {
            console.log(`【transformJSXAttributeValue】为JSSlot参数 ${param} 添加this前缀`);
            expressionValue = expressionValue.replace(paramPattern, `this.${param}`);
          }

          // 特别处理形如 record?.property 或 record.property 的情况
          const paramWithPropPattern = new RegExp(
            `\\b${param}((?:\\?\\.|\\.)[\\w$]+(?:(?:\\?\\.|\\.)[\\w$]+)*)`,
            'g',
          );
          if (paramWithPropPattern.test(expressionValue)) {
            console.log(`【transformJSXAttributeValue】为带属性的JSSlot参数 ${param} 添加this前缀`);

            // 检查是否已经添加了this前缀，避免重复添加
            if (
              expressionValue.includes(`this.${param}?.`) ||
              expressionValue.includes(`this.${param}.`)
            ) {
              console.log(
                `【transformJSXAttributeValue】带属性的JSSlot参数 ${param} 已经有this前缀，跳过`,
              );
            } else {
              expressionValue = expressionValue.replace(paramWithPropPattern, `this.${param}$1`);
            }
          }
        });

        // 处理感叹号前缀，确保不会重复添加
        expressionValue = this.fixExclamationMarks(expressionValue);

        // 最后检查是否有重复的this前缀，如this.this.item，将其修正为this.item
        const thisRepeatPattern2 = /this\.this\./g;
        if (thisRepeatPattern2.test(expressionValue)) {
          console.log(`【transformJSXAttributeValue】修复重复的this前缀`);
          expressionValue = expressionValue.replace(thisRepeatPattern2, 'this.');
        }

        console.log(`【transformJSXAttributeValue】处理后的表达式值: "${expressionValue}"`);

        return {
          type: 'JSExpression',
          value: expressionValue,
        };
      }

      // 处理箭头函数
      if (t.isArrowFunctionExpression(expression)) {
        console.log(`【transformJSXAttributeValue】处理箭头函数`);

        // 检查箭头函数是否返回JSX元素
        if (t.isJSXElement(expression.body)) {
          console.log(`【transformJSXAttributeValue】箭头函数直接返回JSX元素，转换为JSSlot`);

          // 提取函数参数名称
          const params = expression.params.map((param) => {
            if (t.isIdentifier(param)) {
              return param.name;
            }
            return '...'; // 对于复杂参数模式，简化处理
          });

          console.log(`【transformJSXAttributeValue】箭头函数参数:`, params);

          // 保存当前的参数列表
          const prevSlotParams = [...this.currentSlotParams];
          // 更新当前的JSSlot参数
          this.currentSlotParams = params;

          console.log(`【DEBUG】设置JSSlot参数:`, this.currentSlotParams);

          try {
            // 处理JSX元素，此时会使用currentSlotParams
            const jsxElement = this.transformJSXElement(expression.body);

            return {
              type: 'JSSlot',
              params: params?.length ? params : [''], // 添加params字段，包含函数参数名称
              value: [jsxElement],
            };
          } finally {
            // 恢复之前的参数列表
            this.currentSlotParams = prevSlotParams;
            console.log(`【DEBUG】恢复JSSlot参数:`, this.currentSlotParams);
          }
        }

        // 检查函数中是否包含循环变量引用
        const functionValue = this.transformArrowFunctionToNormalFunction(expression);
        console.log(`【transformJSXAttributeValue】转换后的函数值:`, functionValue);

        // 检查当前循环变量
        const hasLoopVars = this.currentLoopArgs.some((arg) => functionValue.includes(arg));
        console.log(`【transformJSXAttributeValue】是否包含循环变量:`, hasLoopVars);

        if (hasLoopVars) {
          // 如果包含循环变量，需要确保正确添加this前缀
          let processedValue = functionValue;
          console.log('processedValue: ', processedValue);
          // this.currentLoopArgs.forEach((arg) => {
          //   // 替换未加this前缀的循环变量引用
          //   const pattern = new RegExp(`\\b${arg}\\b(?!\\s*[:=])`, 'g');
          //   if (pattern.test(processedValue)) {
          //     if (processedValue.includes(`this.${arg}`)) {
          //       console.log(`【transformJSXAttributeValue】循环变量 ${arg} 已经有this前缀，跳过`);
          //       return;
          //     }
          //     processedValue = processedValue.replace(pattern, `this.${arg}`);
          //   }
          // });
          processedValue = this.transformJSFunctionAddThis(processedValue);
          console.log(`【transformJSXAttributeValue】处理后的函数值:`, processedValue);
          return {
            type: 'JSFunction',
            value: processedValue,
          };
        }

        return {
          type: 'JSFunction',
          value: functionValue,
        };
      }

      if (t.isJSXElement(expression)) {
        console.log(`【transformJSXAttributeValue】处理JSX元素表达式`);
        return {
          type: 'JSSlot',
          value: [this.transformJSXElement(expression)],
        };
      }

      if (t.isArrayExpression(expression)) {
        // 添加日志
        console.log(`【transformJSXAttributeValue】处理数组表达式:`, expression);

        // 使用新的方法处理数组表达式
        return this.transformArrayExpression(expression);
      }

      if (t.isObjectExpression(expression)) {
        console.log(`【transformJSXAttributeValue】处理对象表达式`);
        const obj: any = {};
        expression.properties.forEach((prop) => {
          if (t.isObjectProperty(prop)) {
            const key = t.isIdentifier(prop.key)
              ? prop.key.name
              : t.isStringLiteral(prop.key)
              ? prop.key.value
              : '';
            if (key) {
              console.log(
                `【transformJSXAttributeValue】处理对象属性: ${key}, 值类型: ${prop.value.type}`,
              );
              // 处理对象属性中的字面量值
              if (t.isNumericLiteral(prop.value)) {
                obj[key] = prop.value.value;
              } else if (t.isBooleanLiteral(prop.value)) {
                obj[key] = prop.value.value;
              } else if (t.isStringLiteral(prop.value)) {
                obj[key] = prop.value.value;
              } else if (t.isNullLiteral(prop.value)) {
                obj[key] = null;
              } else if (t.isJSXExpressionContainer(prop.value)) {
                const exprContainer = prop.value as t.JSXExpressionContainer;
                console.log(this.transformJSExpression(exprContainer.expression));
                obj[key] = {
                  type: 'JSExpression',
                  value: this.transformJSExpression(exprContainer.expression),
                };
              } else if (t.isMemberExpression(prop.value)) {
                obj[key] = {
                  type: 'JSExpression',
                  value: this.transformJSExpression(prop.value),
                };

                console.log(this.transformJSExpression(prop.value));
              } else if (t.isObjectExpression(prop.value)) {
                obj[key] = this.transformJSXAttributeValue({
                  type: 'JSXExpressionContainer',
                  expression: prop.value,
                });
              } else if (t.isArrayExpression(prop.value)) {
                // 直接处理数组，不转换为字符串形式的JSExpression
                console.log(`【transformJSXAttributeValue】对象属性中的数组:`, prop.value);
                obj[key] = this.transformArrayExpression(prop.value);
              } else if (
                t.isArrowFunctionExpression(prop.value) ||
                t.isFunctionExpression(prop.value)
              ) {
                console.log(`【transformJSXAttributeValue】对象属性中的函数:`, prop.value);

                // 提取函数参数名称
                const params = prop.value.params.map((param) => {
                  if (t.isIdentifier(param)) {
                    return param.name;
                  }
                  return '...'; // 对于复杂参数模式，简化处理
                });

                // 保存当前的参数列表
                const prevSlotParams = [...this.currentSlotParams];
                // 更新当前的JSSlot参数
                this.currentSlotParams = params;

                try {
                  // 对于FunctionExpression，直接处理为JSFunction
                  if (t.isFunctionExpression(prop.value)) {
                    console.log(`【transformJSXAttributeValue】普通函数表达式，转换为JSFunction`);
                    obj[key] = {
                      type: 'JSFunction',
                      value: this.transformJSExpression(prop.value),
                    };
                  }
                  // 检查箭头函数是否返回JSX元素数组
                  else if (t.isArrayExpression(prop.value.body)) {
                    const hasJSXElement = prop.value.body.elements.some(
                      (element) => element && t.isJSXElement(element),
                    );
                    if (hasJSXElement) {
                      console.log(
                        `【transformJSXAttributeValue】箭头函数返回JSX元素数组，转换为JSSlot`,
                      );
                      // 将数组中的JSX元素转换为JSSlot
                      const jsxElements = prop.value.body.elements
                        .filter((element) => element && t.isJSXElement(element))
                        .map((element) => this.transformJSXElement(element as t.JSXElement));

                      obj[key] = {
                        type: 'JSSlot',
                        params: params?.length ? params : [''], // 添加params字段
                        value: jsxElements,
                      };
                    } else {
                      // 普通箭头函数，使用JSExpression
                      obj[key] = {
                        type: 'JSExpression',
                        value: this.transformJSExpression(prop.value),
                      };
                    }
                  } else if (t.isBlockStatement(prop.value.body)) {
                    // 检查函数体中的return语句是否返回JSX元素数组
                    const returnStatement = prop.value.body.body.find((stmt) =>
                      t.isReturnStatement(stmt),
                    );
                    if (
                      returnStatement &&
                      t.isReturnStatement(returnStatement) &&
                      returnStatement.argument &&
                      t.isArrayExpression(returnStatement.argument)
                    ) {
                      const hasJSXElement = returnStatement.argument.elements.some(
                        (element) => element && t.isJSXElement(element),
                      );

                      if (hasJSXElement) {
                        console.log(
                          `【transformJSXAttributeValue】箭头函数块中返回JSX元素数组，转换为JSSlot`,
                        );
                        // 将数组中的JSX元素转换为JSSlot
                        const jsxElements = returnStatement.argument.elements
                          .filter((element) => element && t.isJSXElement(element))
                          .map((element) => this.transformJSXElement(element as t.JSXElement));

                        obj[key] = {
                          type: 'JSSlot',
                          params: params?.length ? params : [''], // 添加params字段
                          value: jsxElements,
                        };
                      } else {
                        // 普通箭头函数，使用JSExpression
                        obj[key] = {
                          type: 'JSExpression',
                          value: this.transformJSExpression(prop.value),
                        };
                      }
                    } else if (
                      returnStatement &&
                      t.isReturnStatement(returnStatement) &&
                      returnStatement.argument &&
                      t.isJSXElement(returnStatement.argument)
                    ) {
                      // 处理返回单个JSX元素的情况
                      console.log(
                        `【transformJSXAttributeValue】箭头函数块中返回单个JSX元素，转换为JSSlot`,
                      );
                      obj[key] = {
                        type: 'JSSlot',
                        params: params?.length ? params : [''], // 添加params字段
                        value: [this.transformJSXElement(returnStatement.argument as t.JSXElement)],
                      };
                    } else {
                      // 普通箭头函数，使用JSExpression
                      obj[key] = {
                        type: 'JSExpression',
                        value: this.transformJSExpression(prop.value),
                      };
                    }
                  } else if (t.isJSXElement(prop.value.body)) {
                    // 处理直接返回JSX元素的箭头函数
                    console.log(
                      `【transformJSXAttributeValue】箭头函数直接返回JSX元素，转换为JSSlot`,
                    );
                    obj[key] = {
                      type: 'JSSlot',
                      params: params?.length ? params : [''], // 添加params字段
                      value: [this.transformJSXElement(prop.value.body)],
                    };
                  } else {
                    // 普通箭头函数，使用JSExpression
                    obj[key] = {
                      type: 'JSExpression',
                      value: this.transformJSExpression(prop.value),
                    };
                  }
                } finally {
                  // 恢复之前的参数列表
                  this.currentSlotParams = prevSlotParams;
                }
              } else if (
                t.isCallExpression(prop.value) &&
                t.isMemberExpression(prop.value.callee) &&
                t.isIdentifier(prop.value.callee.property) &&
                prop.value.callee.property.name === 'bind' &&
                t.isFunctionExpression(prop.value.callee.object)
              ) {
                console.log(
                  `【transformJSXAttributeValue】对象属性中的 function(...).bind(this):`,
                  prop.value,
                );

                const functionExpr = prop.value.callee.object;

                // 检查函数是否返回 JSX 元素
                const returnStatement = functionExpr.body.body.find((stmt) =>
                  t.isReturnStatement(stmt),
                );
                if (
                  returnStatement &&
                  t.isReturnStatement(returnStatement) &&
                  returnStatement.argument &&
                  t.isJSXElement(returnStatement.argument)
                ) {
                  console.log(
                    `【transformJSXAttributeValue】对象属性中的 function(...).bind(this) 返回JSX元素，转换为JSSlot`,
                  );

                  // 提取函数参数名称
                  const params = functionExpr.params.map((param) => {
                    if (t.isIdentifier(param)) {
                      return param.name;
                    }
                    return '...'; // 对于复杂参数模式，简化处理
                  });

                  // 保存当前的参数列表
                  const prevSlotParams = [...this.currentSlotParams];
                  // 更新当前的JSSlot参数
                  this.currentSlotParams = params;

                  try {
                    // 处理JSX元素，此时会使用currentSlotParams
                    const jsxElement = this.transformJSXElement(returnStatement.argument);

                    obj[key] = {
                      type: 'JSSlot',
                      params: params?.length ? params : [''], // 添加params字段，包含函数参数名称
                      value: [jsxElement],
                    };
                  } finally {
                    // 恢复之前的参数列表
                    this.currentSlotParams = prevSlotParams;
                  }
                } else {
                  // 如果不是返回JSX的函数，则作为普通JSExpression处理
                  obj[key] = {
                    type: 'JSExpression',
                    value: this.transformJSExpression(prop.value),
                  };
                }
              } else {
                let transformedValue;
                if (
                  t.isStringLiteral(prop.value) ||
                  t.isJSXElement(prop.value) ||
                  (typeof t.isJSXFragment === 'function' && t.isJSXFragment(prop.value)) ||
                  t.isJSXExpressionContainer(prop.value) ||
                  prop.value === null
                ) {
                  // 只有这几种类型才安全传递
                  transformedValue = this.transformJSXAttributeValue(
                    prop.value as t.JSXAttribute['value'],
                  );
                } else if (t.isExpression(prop.value)) {
                  transformedValue = {
                    type: 'JSExpression',
                    value: this.transformJSExpression(prop.value as t.Expression),
                  };
                } else {
                  // 其它类型兜底
                  transformedValue = {
                    type: 'JSExpression',
                    value: '/* 处理错误 */',
                  };
                }
                obj[key] = transformedValue;
              }
            }
          }
        });
        console.log(`【transformJSXAttributeValue】生成的对象:`, obj);
        return obj;
      }

      // 恢复被删除的代码
      if (t.isMemberExpression(expression)) {
        console.log(`【transformJSXAttributeValue】处理成员表达式`);
        const result = {
          type: 'JSExpression',
          value: this.transformJSExpression(expression),
        };
        console.log(`【transformJSXAttributeValue】成员表达式结果:`, result.value);
        return result;
      }

      // 对于其他类型的表达式，仍然需要转换为JSExpression
      console.log(`【transformJSXAttributeValue】处理其他类型表达式: ${expression.type}`);
      const result = {
        type: 'JSExpression',
        value: this.transformJSExpression(expression),
      };
      console.log(`【transformJSXAttributeValue】其他表达式结果:`, result.value);
      return result;
    }

    console.log(`【transformJSXAttributeValue】未处理的属性值类型: ${value?.type || 'undefined'}`);
    return undefined;
  }

  // 添加一个新方法专门用于处理数组表达式，返回实际的数组而不是字符串
  private transformArrayExpression(expr: t.ArrayExpression): any[] {
    console.log('transformArrayExpression 处理数组:', expr);

    return expr.elements.map((element) => {
      if (!element) return null;

      if (t.isStringLiteral(element)) {
        console.log('数组中的字符串:', element.value);
        return element.value;
      }

      if (t.isNumericLiteral(element)) {
        console.log('数组中的数字:', element.value);
        return element.value;
      }

      if (t.isBooleanLiteral(element)) {
        console.log('数组中的布尔值:', element.value);
        return element.value;
      }

      if (t.isNullLiteral(element)) {
        console.log('数组中的null');
        return null;
      }

      if (t.isJSXElement(element)) {
        console.log('数组中的JSX元素');
        return {
          type: 'JSSlot',
          value: [this.transformJSXElement(element)],
        };
      }

      if (t.isObjectExpression(element)) {
        console.log('数组中的对象');
        return this.transformJSXAttributeValue({
          type: 'JSXExpressionContainer',
          expression: element,
        });
      }

      if (t.isMemberExpression(element)) {
        console.log('数组中的成员表达式', this.transformJSExpression(element));
        return {
          type: 'JSExpression',
          value: this.transformJSExpression(element),
        };
      }

      // 对于其他类型，使用JSExpression
      console.log('数组中的其他类型表达式', this.transformJSExpression(element));
      return {
        type: 'JSExpression',
        value: this.transformJSExpression(element),
      };
    });
  }

  private transformJSExpression(
    expr:
      | t.Expression
      | t.JSXEmptyExpression
      | t.SpreadElement
      | t.OptionalCallExpression
      | t.FunctionExpression
      | t.PrivateName,
  ): string {
    console.log(`【transformJSExpression】开始处理表达式，类型: ${expr.type}`);
    console.log('【transformJSExpression】表达式完整信息:', expr);

    // 处理 PrivateName 类型
    if (t.isPrivateName && t.isPrivateName(expr)) {
      console.log(`【transformJSExpression】处理私有名称: #${expr.id.name}`);
      return `#${expr.id.name}`;
    }

    if (t.isJSXEmptyExpression(expr)) {
      console.log(`【transformJSExpression】处理空表达式`);
      return '';
    }

    // 处理展开运算符
    if (expr.type === 'SpreadElement') {
      console.log(`【transformJSExpression】处理展开运算符`);
      // 使用类型断言确保TypeScript知道这是SpreadElement类型
      const spreadExpr = expr as unknown as { argument: t.Expression };
      const argument = this.transformJSExpression(spreadExpr.argument);
      return `...${argument}`;
    }

    if (t.isIdentifier(expr)) {
      // 检查是否是循环参数或JSSlot参数
      const isLoopArg = this.allLoopArgs.includes(expr.name);
      const isSlotParam = this.currentSlotParams.includes(expr.name);
      console.log(
        `【transformJSExpression】处理标识符: ${expr.name}, 是否是循环变量: ${isLoopArg}, 是否是JSSlot参数: ${isSlotParam}`,
      );

      // 如果是循环参数或JSSlot参数，需要添加 this 前缀
      if (isLoopArg || isSlotParam) {
        console.log(`【transformJSExpression】为变量 ${expr.name} 添加this前缀`);
        return `this.${expr.name}`;
      }
      // 检查是否是对象属性访问中的循环参数
      if (expr.name === 'this') {
        return 'this';
      }

      // 添加调试代码，检查是否是record
      if (expr.name === 'record') {
        console.log(
          `【DEBUG】发现record标识符，但不在JSSlot参数中:`,
          this.currentSlotParams.includes('record'),
          '当前JSSlot参数:',
          this.currentSlotParams,
        );
      }

      return expr.name;
    }

    if (t.isStringLiteral(expr)) {
      console.log(`【transformJSExpression】处理字符串字面量: "${expr.value}"`);
      return JSON.stringify(expr.value);
    }

    if (t.isNumericLiteral(expr)) {
      console.log(`【transformJSExpression】处理数字字面量: ${expr.value}`);
      return String(expr.value);
    }

    if (t.isBooleanLiteral(expr)) {
      console.log(`【transformJSExpression】处理布尔字面量: ${expr.value}`);
      return expr.value ? 'true' : 'false';
    }

    if (t.isNullLiteral(expr)) {
      console.log(`【transformJSExpression】处理null字面量`);
      return 'null';
    }

    if (t.isThisExpression(expr)) {
      console.log(`【transformJSExpression】处理this表达式`);
      return 'this';
    }

    // 添加对箭头函数的处理
    if (t.isArrowFunctionExpression(expr)) {
      console.log(`【transformJSExpression】处理箭头函数`);
      return this.transformArrowFunctionToNormalFunction(expr);
    }

    // 添加对普通函数表达式的处理
    if (t.isFunctionExpression(expr)) {
      console.log(`【transformJSExpression】处理普通函数表达式`);
      return this.transformFunctionExpressionToString(expr);
    }

    if (t.isArrayExpression(expr)) {
      // 添加日志
      console.log(`【transformJSExpression】处理数组表达式:`, expr);

      // 注意：这个方法返回的是字符串表示，用于JSExpression的value
      // 如果需要实际的数组对象，应该使用transformArrayExpression方法
      const elements = expr.elements.map((element) => {
        if (!element) return 'null';

        // 安全处理不同类型的元素
        try {
          if (t.isExpression(element)) {
            return this.transformJSExpression(element);
          }
          return 'null';
        } catch (error) {
          console.error(`【transformJSExpression】处理数组元素时出错:`, error);
          return 'null';
        }
      });
      // 这里可能是问题所在，我们返回了字符串形式的数组
      console.log(`【transformJSExpression】数组元素字符串:`, elements);
      return `[${elements.join(', ')}]`;
    }

    if (t.isObjectExpression(expr)) {
      console.log(`【transformJSExpression】处理对象表达式`);
      const properties = expr.properties.map((prop) => {
        if (t.isObjectProperty(prop)) {
          let key = t.isIdentifier(prop.key) ? prop.key.name : `"${(prop.key as any).value}"`;

          // 处理简写属性
          if (prop.shorthand) {
            // 如果是简写属性，我们需要把它展开
            // 例如 { index } 需要变成 { index: this.index }
            if (t.isExpression(prop.value)) {
              const value = this.transformJSExpression(prop.value);
              return `${key}: ${value}`;
            }
            return `${key}: ${key}`;
          }

          if (t.isExpression(prop.value)) {
            let value = this.transformJSExpression(prop.value);
            return `${key}: ${value}`;
          }
          return `${key}: undefined`;
        }
        // 其它类型（如 ArrayPattern、RestElement、SpreadElement 等）直接跳过
        return '';
      });
      return `{ ${properties.filter(Boolean).join(', ')} }`;
    }

    if (t.isMemberExpression(expr) || t.isOptionalMemberExpression(expr)) {
      const object = expr.object;
      const property = expr.property;
      const optional = expr.optional;
      const computed = expr.computed; // 是否是计算属性（使用中括号）

      console.log(
        `【transformJSExpression】处理成员表达式，对象类型: ${object.type}, 属性类型: ${property.type}, 是否计算属性: ${computed}`,
      );

      // 处理this表达式
      if (t.isThisExpression(object) && t.isIdentifier(property)) {
        console.log(`【transformJSExpression】处理this成员表达式: this.${property.name}`);
        return `this${optional ? '?.' : '.'}${property.name}`;
      }

      // 处理嵌套成员表达式
      if (t.isMemberExpression(object)) {
        const objectStr = this.transformJSExpression(object);

        // 处理计算属性（使用中括号访问）
        if (computed) {
          const propExpr = t.isExpression(property)
            ? this.transformJSExpression(property)
            : property.toString();
          console.log(`【transformJSExpression】处理计算成员表达式: ${objectStr}[${propExpr}]`);

          // 检查属性表达式中是否包含循环变量
          let propExprStr = propExpr;
          this.allLoopArgs.forEach((arg) => {
            // 如果属性表达式中直接使用了循环变量（没有this前缀），添加this前缀
            const argPattern = new RegExp(`\\b${arg}\\b(?!\\s*[:=])(?!\\.|\\\")`, 'g');
            if (argPattern.test(propExprStr) && !propExprStr.includes(`this.${arg}`)) {
              console.log(`【transformJSExpression】在索引表达式中为循环变量 ${arg} 添加this前缀`);
              propExprStr = propExprStr.replace(argPattern, `this.${arg}`);
            }

            // 特别处理形如 item?.property 或 item.property 的情况
            const argWithPropPattern = new RegExp(
              `\\b${arg}((?:\\?\\.|\\.)[\\w$]+(?:(?:\\?\\.|\\.)[\\w$]+)*)`,
              'g',
            );
            if (argWithPropPattern.test(propExprStr) && !propExprStr.includes(`this.${arg}`)) {
              console.log(
                `【transformJSExpression】在索引表达式中为带属性的循环变量 ${arg} 添加this前缀`,
              );
              propExprStr = propExprStr.replace(argWithPropPattern, `this.${arg}$1`);
            }
          });

          // 检查属性表达式中是否包含JSSlot参数
          this.currentSlotParams.forEach((param) => {
            // 如果属性表达式中直接使用了JSSlot参数（没有this前缀），添加this前缀
            const paramPattern = new RegExp(`\\b${param}\\b(?!\\s*[:=])(?!\\.|\\\")`, 'g');
            if (paramPattern.test(propExprStr) && !propExprStr.includes(`this.${param}`)) {
              console.log(
                `【transformJSExpression】在索引表达式中为JSSlot参数 ${param} 添加this前缀`,
              );
              propExprStr = propExprStr.replace(paramPattern, `this.${param}`);
            }

            // 特别处理形如 record?.property 或 record.property 的情况
            const paramWithPropPattern = new RegExp(
              `\\b${param}((?:\\?\\.|\\.)[\\w$]+(?:(?:\\?\\.|\\.)[\\w$]+)*)`,
              'g',
            );
            if (paramWithPropPattern.test(propExprStr) && !propExprStr.includes(`this.${param}`)) {
              console.log(
                `【transformJSExpression】在索引表达式中为带属性的JSSlot参数 ${param} 添加this前缀`,
              );
              propExprStr = propExprStr.replace(paramWithPropPattern, `this.${param}$1`);
            }
          });

          return `${objectStr}${optional ? '?.' : ''}[${propExprStr}]`;
        }

        // 处理普通属性（使用点访问）
        let propStr = '';
        if (t.isIdentifier(property)) {
          propStr = property.name;
        } else if (t.isExpression(property)) {
          propStr = this.transformJSExpression(property);
        } else {
          propStr = property.toString();
        }

        console.log(`【transformJSExpression】处理嵌套成员表达式: ${objectStr}.${propStr}`);
        return `${objectStr}${optional ? '?.' : '.'}${propStr}`;
      }

      // 处理标识符
      if (t.isIdentifier(object)) {
        // 检查对象是否是循环参数或JSSlot参数
        const isObjectLoopArg = this.allLoopArgs.includes(object.name);
        const isObjectSlotParam = this.currentSlotParams.includes(object.name);
        console.log(
          `【transformJSExpression】处理标识符成员表达式: ${object.name}, 是否是循环变量: ${isObjectLoopArg}, 是否是JSSlot参数: ${isObjectSlotParam}, 当前循环变量:`,
          this.currentLoopArgs,
          '所有循环变量:',
          this.allLoopArgs,
          '当前JSSlot参数:',
          this.currentSlotParams,
        );

        // 处理计算属性（使用中括号访问）
        if (computed) {
          // 如果对象是循环变量或JSSlot参数，添加this前缀
          const objStr = isObjectLoopArg || isObjectSlotParam ? `this.${object.name}` : object.name;

          // 处理属性表达式
          const propExpr = t.isExpression(property)
            ? this.transformJSExpression(property)
            : property.toString();
          console.log(`【transformJSExpression】处理计算成员表达式: ${objStr}[${propExpr}]`);

          // 检查属性表达式中是否包含循环变量
          let propExprStr = propExpr;
          this.allLoopArgs.forEach((arg) => {
            // 如果属性表达式中直接使用了循环变量（没有this前缀），添加this前缀
            const argPattern = new RegExp(`\\b${arg}\\b(?!\\s*[:=])(?!\\.|\\\")`, 'g');
            if (argPattern.test(propExprStr) && !propExprStr.includes(`this.${arg}`)) {
              console.log(`【transformJSExpression】在索引表达式中为循环变量 ${arg} 添加this前缀`);
              propExprStr = propExprStr.replace(argPattern, `this.${arg}`);
            }

            // 特别处理形如 item?.property 或 item.property 的情况
            const argWithPropPattern = new RegExp(
              `\\b${arg}((?:\\?\\.|\\.)[\\w$]+(?:(?:\\?\\.|\\.)[\\w$]+)*)`,
              'g',
            );
            if (argWithPropPattern.test(propExprStr) && !propExprStr.includes(`this.${arg}`)) {
              console.log(
                `【transformJSExpression】在索引表达式中为带属性的循环变量 ${arg} 添加this前缀`,
              );
              propExprStr = propExprStr.replace(argWithPropPattern, `this.${arg}$1`);
            }
          });

          // 检查属性表达式中是否包含JSSlot参数
          this.currentSlotParams.forEach((param) => {
            // 如果属性表达式中直接使用了JSSlot参数（没有this前缀），添加this前缀
            const paramPattern = new RegExp(`\\b${param}\\b(?!\\s*[:=])(?!\\.|\\\")`, 'g');
            if (paramPattern.test(propExprStr) && !propExprStr.includes(`this.${param}`)) {
              console.log(
                `【transformJSExpression】在索引表达式中为JSSlot参数 ${param} 添加this前缀`,
              );
              propExprStr = propExprStr.replace(paramPattern, `this.${param}`);
            }

            // 特别处理形如 record?.property 或 record.property 的情况
            const paramWithPropPattern = new RegExp(
              `\\b${param}((?:\\?\\.|\\.)[\\w$]+(?:(?:\\?\\.|\\.)[\\w$]+)*)`,
              'g',
            );
            if (paramWithPropPattern.test(propExprStr) && !propExprStr.includes(`this.${param}`)) {
              console.log(
                `【transformJSExpression】在索引表达式中为带属性的JSSlot参数 ${param} 添加this前缀`,
              );
              propExprStr = propExprStr.replace(paramWithPropPattern, `this.${param}$1`);
            }
          });

          return `${objStr}${optional ? '?.' : ''}[${propExprStr}]`;
        }

        // 处理普通属性（使用点访问）
        let propStr = '';
        if (t.isIdentifier(property)) {
          propStr = property.name;
        } else if (t.isExpression(property)) {
          propStr = this.transformJSExpression(property);
        } else {
          propStr = property.toString();
        }

        // 如果是循环参数或JSSlot参数，添加this前缀
        if (isObjectLoopArg || isObjectSlotParam) {
          console.log(
            `【transformJSExpression】为循环变量或JSSlot参数成员表达式添加this前缀: this.${object.name}.${propStr}`,
          );
          return `this.${object.name}${optional ? '?.' : '.'}${propStr}`;
        }

        // 如果是state或其他常见对象属性，保持原样
        if (object.name === 'state' || object.name === 'props' || object.name === 'context') {
          console.log(`【transformJSExpression】保持常见对象属性原样: ${object.name}.${propStr}`);
          return `${object.name}${optional ? '?.' : '.'}${propStr}`;
        }

        console.log(`【transformJSExpression】普通成员表达式: ${object.name}.${propStr}`);
        return `${object.name}${optional ? '?.' : '.'}${propStr}`;
      }

      // 处理其他类型的对象
      const objectStr = this.transformJSExpression(object);

      // 处理计算属性（使用中括号访问）
      if (computed) {
        const propExpr = this.transformJSExpression(property);
        console.log(
          `【transformJSExpression】处理其他类型计算成员表达式: ${objectStr}[${propExpr}]`,
        );

        // 检查属性表达式中是否包含循环变量
        let propExprStr = propExpr;
        this.allLoopArgs.forEach((arg) => {
          // 如果属性表达式中直接使用了循环变量（没有this前缀），添加this前缀
          const argPattern = new RegExp(`\\b${arg}\\b(?!\\s*[:=])`, 'g');
          if (argPattern.test(propExprStr) && !propExprStr.includes(`this.${arg}`)) {
            console.log(`【transformJSExpression】在索引表达式中为循环变量 ${arg} 添加this前缀`);
            propExprStr = propExprStr.replace(argPattern, `this.${arg}`);
          }

          // 特别处理形如 item?.property 或 item.property 的情况
          const argWithPropPattern = new RegExp(`\\b${arg}(\\??\\.\\w+)`, 'g');
          if (argWithPropPattern.test(propExprStr) && !propExprStr.includes(`this.${arg}`)) {
            console.log(
              `【transformJSExpression】在索引表达式中为带属性的循环变量 ${arg} 添加this前缀`,
            );
            propExprStr = propExprStr.replace(argWithPropPattern, `this.${arg}$1`);
          }
        });

        // 检查属性表达式中是否包含JSSlot参数
        this.currentSlotParams.forEach((param) => {
          // 如果属性表达式中直接使用了JSSlot参数（没有this前缀），添加this前缀
          const paramPattern = new RegExp(`\\b${param}\\b(?!\\s*[:=])`, 'g');
          if (paramPattern.test(propExprStr) && !propExprStr.includes(`this.${param}`)) {
            console.log(
              `【transformJSExpression】在索引表达式中为JSSlot参数 ${param} 添加this前缀`,
            );
            propExprStr = propExprStr.replace(paramPattern, `this.${param}`);
          }

          // 特别处理形如 record?.property 或 record.property 的情况
          const paramWithPropPattern = new RegExp(
            `\\b${param}((?:\\?\\.|\\.)[\\w$]+(?:(?:\\?\\.|\\.)[\\w$]+)*)`,
            'g',
          );
          if (paramWithPropPattern.test(propExprStr) && !propExprStr.includes(`this.${param}`)) {
            console.log(
              `【transformJSExpression】在索引表达式中为带属性的JSSlot参数 ${param} 添加this前缀`,
            );
            propExprStr = propExprStr.replace(paramWithPropPattern, `this.${param}$1`);
          }
        });

        return `${objectStr}${optional ? '?.' : ''}[${propExprStr}]`;
      }

      // 处理普通属性（使用点访问）
      let propStr = '';
      if (t.isIdentifier(property)) {
        propStr = property.name;
      } else if (t.isExpression(property)) {
        propStr = this.transformJSExpression(property);
      } else {
        propStr = property.toString();
      }

      console.log(`【transformJSExpression】其他类型成员表达式: ${objectStr}.${propStr}`);
      return `${objectStr}${optional ? '?.' : '.'}${propStr}`;
    }

    if (t.isBinaryExpression(expr)) {
      console.log('【transformJSExpression】处理二元表达式:', {
        operator: expr.operator,
        left: expr.left.type,
        right: expr.right.type,
        fullExpr: expr,
      });
      const left = this.transformJSExpression(expr.left);
      const right = this.transformJSExpression(expr.right);

      // 组合二元表达式
      let result = `${left} ${expr.operator} ${right}`;

      // 检查是否包含JSSlot参数
      this.currentSlotParams.forEach((param) => {
        // 匹配独立的JSSlot参数（不是作为对象属性的一部分）
        const paramPattern = new RegExp(`\\b${param}\\b(?!\\s*[:=])`, 'g');
        if (paramPattern.test(result) && !result.includes(`this.${param}`)) {
          console.log(`【transformJSExpression】为二元表达式中的JSSlot参数 ${param} 添加this前缀`);
          result = result.replace(paramPattern, `this.${param}`);
        }

        // 特别处理形如 record?.property 或 record.property 的情况
        const paramWithPropPattern = new RegExp(`\\b${param}(\\??\\.\\w+)`, 'g');
        if (paramWithPropPattern.test(result) && !result.includes(`this.${param}`)) {
          console.log(
            `【transformJSExpression】为二元表达式中带属性的JSSlot参数 ${param} 添加this前缀`,
          );

          // 检查是否已经添加了this前缀，避免重复添加
          if (result.includes(`this.${param}?.`) || result.includes(`this.${param}.`)) {
            console.log(
              `【transformJSExpression】二元表达式中带属性的JSSlot参数 ${param} 已经有this前缀，跳过`,
            );
          } else {
            result = result.replace(paramWithPropPattern, `this.${param}$1`);
          }
        }
      });

      // 处理感叹号前缀，确保不会重复添加
      result = this.fixExclamationMarks(result);

      console.log(`【transformJSExpression】二元表达式结果: ${result}`);
      return result;
    }

    if (t.isConditionalExpression(expr)) {
      console.log(`【transformJSExpression】开始处理条件表达式`);
      console.log(`【transformJSExpression】条件表达式test类型: ${expr.test.type}`);
      console.log(`【transformJSExpression】条件表达式consequent类型: ${expr.consequent.type}`);
      console.log(`【transformJSExpression】条件表达式alternate类型: ${expr.alternate.type}`);

      const test = this.transformJSExpression(expr.test);
      console.log(`【transformJSExpression】条件表达式test结果: ${test}`);

      const consequent = this.transformJSExpression(expr.consequent);
      console.log(`【transformJSExpression】条件表达式consequent结果: ${consequent}`);

      const alternate = this.transformJSExpression(expr.alternate);
      console.log(`【transformJSExpression】条件表达式alternate结果: ${alternate}`);

      // 组合条件表达式
      let result = `${test} ? ${consequent} : ${alternate}`;

      // 检查是否包含JSSlot参数
      this.currentSlotParams.forEach((param) => {
        // 匹配独立的JSSlot参数（不是作为对象属性的一部分）
        const paramPattern = new RegExp(`\\b${param}\\b(?!\\s*[:=])`, 'g');
        if (paramPattern.test(result) && !result.includes(`this.${param}`)) {
          console.log(`【transformJSExpression】为条件表达式中的JSSlot参数 ${param} 添加this前缀`);
          result = result.replace(paramPattern, `this.${param}`);
        }

        // 特别处理形如 record?.property 或 record.property 的情况
        const paramWithPropPattern = new RegExp(`\\b${param}(\\??\\.\\w+)`, 'g');
        if (paramWithPropPattern.test(result) && !result.includes(`this.${param}`)) {
          console.log(
            `【transformJSExpression】为条件表达式中带属性的JSSlot参数 ${param} 添加this前缀`,
          );

          // 检查是否已经添加了this前缀，避免重复添加
          if (result.includes(`this.${param}?.`) || result.includes(`this.${param}.`)) {
            console.log(
              `【transformJSExpression】条件表达式中带属性的JSSlot参数 ${param} 已经有this前缀，跳过`,
            );
          } else {
            result = result.replace(paramWithPropPattern, `this.${param}$1`);
          }
        }
      });

      // 处理感叹号前缀，确保不会重复添加
      result = this.fixExclamationMarks(result);

      console.log(`【transformJSExpression】条件表达式最终结果: ${result}`);
      return result;
    }

    if (t.isLogicalExpression(expr)) {
      console.log(`【transformJSExpression】处理逻辑表达式: ${expr.operator}`);
      return this.transformLogicalExpression(expr);
    }

    if (t.isCallExpression(expr)) {
      console.log(`【transformJSExpression】处理函数调用表达式`);
      return this.transformCallExpression(expr);
    }

    if (t.isJSXElement(expr)) {
      console.log(`【transformJSExpression】处理JSX元素表达式`);
      // 对于在表达式中的JSX元素，我们需要将其转换为JSSlot形式
      const jsxSchema = this.transformJSXElement(expr);
      // 返回一个特殊标记，表示这里应该是JSSlot
      return `__JSX_ELEMENT_${JSON.stringify(jsxSchema)}__`;
    }

    if (t.isUnaryExpression(expr)) {
      console.log(`【transformJSExpression】处理一元表达式: ${expr.operator}`);
      const argument = this.transformJSExpression(expr.argument);
      // 对于需要空格的操作符（如 typeof, void, delete 等），在操作符和参数之间加空格
      const needSpace = ['typeof', 'void', 'delete'].includes(expr.operator);
      return needSpace ? `${expr.operator} ${argument}` : `${expr.operator}${argument}`;
    }

    if (t.isOptionalCallExpression(expr)) {
      console.log(`【transformJSExpression】处理可选调用表达式`);
      const callee = this.transformJSExpression(expr.callee as t.Expression);
      const args = expr.arguments
        .map((arg) => {
          if (t.isArrowFunctionExpression(arg)) {
            return this.transformArrowFunctionToNormalFunction(arg);
          }
          return this.transformJSExpression(arg as t.Expression);
        })
        .join(', ');
      return `${callee}(${args})`;
    }

    if (t.isTemplateLiteral(expr)) {
      // 处理模板字符串
      const quasis = expr.quasis.map((q) => q.value.cooked);
      const expressions = expr.expressions.map((e) => {
        if (t.isExpression(e)) {
          return this.transformJSExpression(e);
        }
        return ''; // 对于TSType，返回空字符串
      });
      // 拼接成模板字符串
      let result = '';
      for (let i = 0; i < quasis.length; i++) {
        result += quasis[i];
        if (i < expressions.length) {
          result += '${' + expressions[i] + '}';
        }
      }
      return '`' + result + '`';
    }

    if (t.isNewExpression(expr)) {
      // 处理 new 表达式
      const callee = t.isExpression(expr.callee)
        ? this.transformJSExpression(expr.callee)
        : expr.callee.name; // 处理 V8IntrinsicIdentifier
      const args = expr.arguments
        .map((arg) => {
          if (t.isExpression(arg)) {
            return this.transformJSExpression(arg);
          }
          return ''; // 对于ArgumentPlaceholder，返回空字符串
        })
        .filter(Boolean)
        .join(', ');
      return `new ${callee}(${args})`;
    }

    console.log(`【transformJSExpression】未处理的表达式类型: ${expr.type}`);
    return '';
  }

  private transformLogicalExpression(expr: t.LogicalExpression): string {
    const left = this.transformJSExpression(expr.left);
    const right = this.transformJSExpression(expr.right);

    // 组合逻辑表达式
    let result = `${left} ${expr.operator} ${right}`;

    // 检查是否包含JSSlot参数
    this.currentSlotParams.forEach((param) => {
      // 匹配独立的JSSlot参数（不是作为对象属性的一部分）
      const paramPattern = new RegExp(`\\b${param}\\b(?!\\s*[:=])`, 'g');
      if (paramPattern.test(result) && !result.includes(`this.${param}`)) {
        console.log(`【transformLogicalExpression】为JSSlot参数 ${param} 添加this前缀`);
        result = result.replace(paramPattern, `this.${param}`);
      }

      // 特别处理形如 record?.property 或 record.property 的情况
      const paramWithPropPattern = new RegExp(`\\b${param}(\\??\\.\\w+)`, 'g');
      if (paramWithPropPattern.test(result) && !result.includes(`this.${param}`)) {
        console.log(`【transformLogicalExpression】为带属性的JSSlot参数 ${param} 添加this前缀`);

        // 检查是否已经添加了this前缀，避免重复添加
        if (result.includes(`this.${param}?.`) || result.includes(`this.${param}.`)) {
          console.log(
            `【transformLogicalExpression】带属性的JSSlot参数 ${param} 已经有this前缀，跳过`,
          );
        } else {
          result = result.replace(paramWithPropPattern, `this.${param}$1`);
        }
      }
    });

    // 处理感叹号前缀，确保不会重复添加
    result = this.fixExclamationMarks(result);

    return result;
  }

  private transformCallExpression(expr: t.CallExpression): string {
    if (t.isMemberExpression(expr.callee) || t.isOptionalMemberExpression(expr.callee)) {
      const object = expr.callee.object;
      const property = t.isIdentifier(expr.callee.property) ? expr.callee.property.name : '';

      // 特别处理this表达式，确保this被保留
      if (t.isThisExpression(object)) {
        const args = expr.arguments
          .map((arg) => {
            if (t.isArrowFunctionExpression(arg)) {
              return this.transformArrowFunctionToNormalFunction(arg);
            }
            if (t.isExpression(arg)) {
              return this.transformJSExpression(arg);
            }
            return ''; // 对于ArgumentPlaceholder，返回空字符串
          })
          .filter(Boolean)
          .join(', ');
        return `this.${property}(${args})`;
      }

      const objectStr = this.transformJSExpression(object);
      const args = expr.arguments
        .map((arg) => {
          if (t.isArrowFunctionExpression(arg)) {
            return this.transformArrowFunctionToNormalFunction(arg);
          }
          if (t.isExpression(arg)) {
            return this.transformJSExpression(arg);
          }
          return ''; // 对于ArgumentPlaceholder，返回空字符串
        })
        .filter(Boolean)
        .join(', ');
      return `${objectStr}.${property}(${args})`;
    }
    if (t.isIdentifier(expr.callee)) {
      const args = expr.arguments
        .map((arg) => {
          if (t.isArrowFunctionExpression(arg)) {
            return this.transformArrowFunctionToNormalFunction(arg);
          }
          if (t.isExpression(arg)) {
            return this.transformJSExpression(arg);
          }
          return ''; // 对于ArgumentPlaceholder，返回空字符串
        })
        .filter(Boolean)
        .join(', ');
      return `${expr.callee.name}(${args})`;
    }
    return '';
  }

  private transformBlockStatement(block: t.BlockStatement): string {
    const statements = block.body
      .map((statement) => {
        if (t.isReturnStatement(statement)) {
          return `return ${
            statement.argument ? this.transformJSExpression(statement.argument) : ''
          }`;
        }
        if (t.isExpressionStatement(statement)) {
          // 确保表达式中的this引用被保留
          const exprStr = this.transformJSExpression(statement.expression);
          return exprStr;
        }
        if (t.isVariableDeclaration(statement)) {
          // 处理变量声明语句
          const declarations = statement.declarations
            .map((declarator) => {
              const id = t.isIdentifier(declarator.id)
                ? declarator.id.name
                : generate(declarator.id).code;
              const init = declarator.init
                ? this.transformJSExpression(declarator.init)
                : 'undefined';
              return `${id} = ${init}`;
            })
            .join(', ');
          return `${statement.kind} ${declarations}`;
        }
        if (t.isIfStatement(statement)) {
          // 处理if语句
          const test = this.transformJSExpression(statement.test);
          const consequent = t.isBlockStatement(statement.consequent)
            ? this.transformBlockStatement(statement.consequent)
            : t.isExpression(statement.consequent)
            ? this.transformJSExpression(statement.consequent)
            : generate(statement.consequent).code;

          let result = `if (${test}) ${consequent}`;

          // 处理else部分
          if (statement.alternate) {
            const alternate = t.isBlockStatement(statement.alternate)
              ? this.transformBlockStatement(statement.alternate)
              : t.isIfStatement(statement.alternate)
              ? generate(statement.alternate).code // 处理else if
              : t.isExpression(statement.alternate)
              ? this.transformJSExpression(statement.alternate)
              : generate(statement.alternate).code;
            result += ` else ${alternate}`;
          }

          return result;
        }
        if (t.isTryStatement(statement)) {
          // 处理try语句
          const tryBlock = this.transformBlockStatement(statement.block);
          let result = `try ${tryBlock}`;

          // 处理catch部分
          if (statement.handler) {
            const param = statement.handler.param
              ? t.isIdentifier(statement.handler.param)
                ? statement.handler.param.name
                : generate(statement.handler.param).code
              : '';
            const catchBlock = this.transformBlockStatement(statement.handler.body);
            result += ` catch${param ? ` (${param})` : ''} ${catchBlock}`;
          }

          // 处理finally部分
          if (statement.finalizer) {
            const finallyBlock = this.transformBlockStatement(statement.finalizer);
            result += ` finally ${finallyBlock}`;
          }

          return result;
        }
        if (t.isForStatement(statement)) {
          // 处理for循环
          const init = statement.init
            ? t.isVariableDeclaration(statement.init)
              ? generate(statement.init).code
              : this.transformJSExpression(statement.init)
            : '';
          const test = statement.test ? this.transformJSExpression(statement.test) : '';
          const update = statement.update ? this.transformJSExpression(statement.update) : '';
          const forBody = t.isBlockStatement(statement.body)
            ? this.transformBlockStatement(statement.body)
            : t.isExpression(statement.body)
            ? this.transformJSExpression(statement.body)
            : generate(statement.body).code;

          return `for (${init}; ${test}; ${update}) ${forBody}`;
        }
        if (t.isWhileStatement(statement)) {
          // 处理while循环
          const test = this.transformJSExpression(statement.test);
          const whileBody = t.isBlockStatement(statement.body)
            ? this.transformBlockStatement(statement.body)
            : t.isExpression(statement.body)
            ? this.transformJSExpression(statement.body)
            : generate(statement.body).code;

          return `while (${test}) ${whileBody}`;
        }
        if (t.isForInStatement(statement) || t.isForOfStatement(statement)) {
          // 处理for...in和for...of循环
          const left = t.isVariableDeclaration(statement.left)
            ? generate(statement.left).code
            : t.isExpression(statement.left)
            ? this.transformJSExpression(statement.left)
            : generate(statement.left).code; // 对于其他LVal类型，使用generate
          const right = this.transformJSExpression(statement.right);
          const forBody = t.isBlockStatement(statement.body)
            ? this.transformBlockStatement(statement.body)
            : t.isExpression(statement.body)
            ? this.transformJSExpression(statement.body)
            : generate(statement.body).code; // 对于其他Statement类型，使用generate
          const forType = t.isForInStatement(statement) ? 'in' : 'of';

          return `for (${left} ${forType} ${right}) ${forBody}`;
        }

        // 对于其他不支持的语句类型，尝试使用generate生成代码
        try {
          console.log('使用generate处理语句类型:', statement.type);
          const { code } = generate(statement);
          return code;
        } catch (error) {
          console.error('使用generate处理语句失败:', error);
          console.log('不支持的语句类型:', statement.type, statement);
          return `/* 不支持的语句类型: ${statement.type} */`;
        }
      })
      .filter(Boolean);
    return `{ ${statements.join('; ')} }`;
  }

  private transformJSXChild(
    child: t.JSXElement | t.JSXText | t.JSXExpressionContainer | t.JSXFragment | t.JSXSpreadChild,
    parentComponentName?: string,
  ): any {
    console.log(
      `【transformJSXChild】开始处理JSX子节点，类型: ${child.type}, 父组件: ${
        parentComponentName || '无'
      }`,
    );

    // 特殊处理Text组件的混合文本模式（文本+表达式）
    if (parentComponentName === 'Text' && t.isJSXText(child)) {
      // 检查下一个兄弟节点是否为JSXExpressionContainer
      const nextSibling = this.getNextSibling(child);
      if (nextSibling && t.isJSXExpressionContainer(nextSibling)) {
        console.log(`【transformJSXChild】检测到Text组件中的混合文本模式`);

        // 获取当前文本内容和表达式内容
        const textContent = child.value.trim();
        const exprContent = generate(nextSibling.expression).code;

        // 将文本和表达式组合成一个模板字符串表达式
        const combinedExpr = `\`${textContent}\${${exprContent}}\``;
        console.log(`【transformJSXChild】组合后的表达式: ${combinedExpr}`);

        // 标记这个节点已被处理，避免重复处理
        this.markNodeAsProcessed(nextSibling);

        return {
          type: 'JSExpression',
          value: combinedExpr,
        };
      }
    }

    if (t.isJSXText(child)) {
      const text = child.value.trim();
      console.log(`【transformJSXChild】处理JSX文本: "${text}"`);
      return text ? text : null;
    }

    if (t.isJSXElement(child)) {
      console.log(`【transformJSXChild】处理JSX元素`);
      return this.transformJSXElement(child);
    }

    if (t.isJSXExpressionContainer(child)) {
      const expression = child.expression;
      console.log(`【transformJSXChild】处理JSX表达式容器，表达式类型: ${expression.type}`);

      // 检查是否是空表达式（注释），如果是则直接返回null，避免生成空的Text组件
      if (t.isJSXEmptyExpression(expression)) {
        console.log(`【transformJSXChild】检测到JSX注释，跳过处理`);
        return null;
      }

      // 特殊处理Text组件的子节点，确保它是JSExpression类型
      if (parentComponentName === 'Text') {
        console.log(`【transformJSXChild】特殊处理Text组件的子节点:`, expression);

        // 获取表达式的原始代码
        const { code } = generate(expression);
        console.log(`【transformJSXChild】Text组件子节点表达式原始代码: "${code}"`);
        console.log(`【transformJSXChild】当前循环变量:`, this.currentLoopArgs);
        console.log(`【transformJSXChild】当前JSSlot参数:`, this.currentSlotParams);

        // 处理循环变量引用，确保正确添加this前缀
        let expressionValue = code;

        // 先检查是否已经有重复的this前缀，如this.this.item，将其修正为this.item
        const thisRepeatPattern1 = /this\.this\./g;
        if (thisRepeatPattern1.test(expressionValue)) {
          console.log(`【transformJSXChild】修复重复的this前缀`);
          expressionValue = expressionValue.replace(thisRepeatPattern1, 'this.');
        }

        // 检查当前循环变量和所有循环变量
        console.log(`【transformJSXChild】当前循环变量:`, this.currentLoopArgs);
        console.log(`【transformJSXChild】所有循环变量:`, this.allLoopArgs);

        // 替换形如 this.item 的引用为 item（临时替换，后面会再添加this前缀）
        this.allLoopArgs.forEach((arg) => {
          const thisPattern = new RegExp(`this\\.${arg}\\b`, 'g');
          if (thisPattern.test(expressionValue)) {
            console.log(`【transformJSXChild】替换 this.${arg} 为 ${arg}`);
            expressionValue = expressionValue.replace(thisPattern, arg);
          }
        });

        // 处理JSSlot参数，移除已有的this前缀（如果有）
        this.currentSlotParams.forEach((param) => {
          const thisPattern = new RegExp(`this\\.${param}\\b`, 'g');
          if (thisPattern.test(expressionValue)) {
            console.log(`【transformJSXChild】替换 this.${param} 为 ${param}`);
            expressionValue = expressionValue.replace(thisPattern, param);
          }
        });

        // 现在为所有循环变量添加this前缀
        this.allLoopArgs.forEach((arg) => {
          // 特别处理形如 item?.property 或 item.property 的情况
          const argWithPropPattern = new RegExp(
            `\\b${arg}((?:\\??\\.|\\.)[\\w$]+(?:\\??\\.|\\.)?[\\w$]*)`,
            'g',
          );
          console.log('argWithPropPattern: ', argWithPropPattern, expressionValue);
          if (argWithPropPattern.test(expressionValue)) {
            console.log(`【transformJSXChild】为带属性的循环变量 ${arg} 添加this前缀`);

            // 直接进行全局替换，但要避免重复添加this前缀
            const argWithPropPattern = new RegExp(
              `\\b${arg}((?:\\??\\.|\\.)[\\w$]+(?:\\??\\.|\\.)?[\\w$]*)`,
              'g',
            );
            expressionValue = expressionValue.replace(argWithPropPattern, (match, p1) => {
              // 检查这个匹配是否已经有this前缀
              if (match.startsWith(`this.${arg}`)) {
                return match; // 已经有this前缀，保持不变
              }
              console.log(
                `【transformJSXChild】为带属性的循环变量 ${arg} 添加this前缀: ${match} -> this.${arg}${p1}`,
              );
              return `this.${arg}${p1}`;
            });
          }

          // 处理独立的循环变量，包括比较操作符的情况
          const argPattern = new RegExp(
            `\\b${arg}\\b(?![\\w$])(?!\\s*\\?\\s*\\.)(?!\\s*\\.)(?!\\s*\\[)(?!\\s*\\(\\s*\\{)`,
            'g',
          );

          // 直接进行全局替换，处理独立的循环变量
          expressionValue = expressionValue.replace(argPattern, (match, offset, str) => {
            // 检查前面是否已经有this前缀
            const beforeMatch = str.substring(Math.max(0, offset - 5), offset);
            if (beforeMatch.endsWith('this.')) {
              return match; // 已经有this前缀，保持不变
            }
            console.log(
              `【transformJSXChild】为独立循环变量 ${arg} 添加this前缀: ${match} -> this.${arg}`,
            );
            return `this.${arg}`;
          });
        });

        // 为所有JSSlot参数添加this前缀
        this.currentSlotParams.forEach((param) => {
          // 匹配独立的JSSlot参数（不是作为对象属性的一部分）
          const paramPattern = new RegExp(`\\b${param}\\b(?!\\s*=)(?!\\.|\\\")`, 'g');
          if (paramPattern.test(expressionValue)) {
            console.log(`【transformJSXChild】为JSSlot参数 ${param} 添加this前缀`);
            expressionValue = expressionValue.replace(paramPattern, `this.${param}`);
          }

          // 特别处理形如 record?.property 或 record.property 的情况
          const paramWithPropPattern = new RegExp(
            `\\b${param}((?:\\?\.|\.)[\\w$]+(?:(?:\\?\.|\.)[\\w$]+)*)`,
            'g',
          );
          if (paramWithPropPattern.test(expressionValue)) {
            console.log(`【transformJSXChild】为带属性的JSSlot参数 ${param} 添加this前缀`);

            // 检查是否已经添加了this前缀，避免重复添加
            if (
              expressionValue.includes(`this.${param}?.`) ||
              expressionValue.includes(`this.${param}.`)
            ) {
              console.log(`【transformJSXChild】带属性的JSSlot参数 ${param} 已经有this前缀，跳过`);
            } else {
              expressionValue = expressionValue.replace(paramWithPropPattern, `this.${param}$1`);
            }
          }
        });

        // 处理感叹号前缀，确保不会重复添加
        expressionValue = this.fixExclamationMarks(expressionValue);

        // 最后检查是否有重复的this前缀，如this.this.item，将其修正为this.item
        const thisRepeatPattern2 = /this\.this\./g;
        if (thisRepeatPattern2.test(expressionValue)) {
          console.log(`【transformJSXChild】修复重复的this前缀`);
          expressionValue = expressionValue.replace(thisRepeatPattern2, 'this.');
        }

        console.log(`【transformJSXChild】处理后的表达式值: "${expressionValue}"`);

        const result = {
          type: 'JSExpression',
          value: expressionValue,
        };
        if (result.value.startsWith('this.state.form?.images.map(function(item, index)')) {
          debugger;
        }
        console.log(`【transformJSXChild】Text组件子节点最终结果:`, result.value);
        return result;
      }

      if (t.isLogicalExpression(expression)) {
        // 处理逻辑表达式作为条件渲染
        let result;
        if (t.isJSXElement(expression.right)) {
          result = this.transformJSXElement(expression.right);
        } else if (
          (t.isCallExpression(expression.right) || t.isOptionalCallExpression(expression.right)) &&
          (t.isMemberExpression(expression.right.callee) ||
            t.isOptionalMemberExpression(expression.right.callee)) &&
          t.isIdentifier(expression.right.callee.property) &&
          expression.right.callee.property.name === 'map'
        ) {
          // 这是一个循环渲染
          const arrayExpr = expression.right.callee.object;
          const mapCallback = expression.right.arguments[0];

          if (t.isArrowFunctionExpression(mapCallback)) {
            const params = mapCallback.params;
            const body = mapCallback.body;

            // 保存之前的循环参数
            const prevLoopArgs = [...this.currentLoopArgs];

            // 获取循环变量名
            const itemParam = params[0];
            const indexParam = params[1];
            const itemName = t.isIdentifier(itemParam) ? itemParam.name : 'item';
            const indexName = t.isIdentifier(indexParam) ? indexParam.name : 'index';

            console.log(
              `【transformJSXChild】逻辑表达式中的循环变量: itemName=${itemName}, indexName=${indexName}`,
            );

            // 更新当前循环参数
            this.currentLoopArgs = [itemName, indexName];

            // 将新的循环变量添加到所有循环变量列表中
            if (!this.allLoopArgs.includes(itemName)) {
              this.allLoopArgs.push(itemName);
            }
            if (!this.allLoopArgs.includes(indexName)) {
              this.allLoopArgs.push(indexName);
            }

            try {
              if (t.isJSXElement(body)) {
                result = this.transformJSXElement(body);
                result.loop = {
                  type: 'JSExpression',
                  value: this.transformJSExpression(arrayExpr),
                };
                result.loopArgs = [itemName, indexName];
              } else {
                result = {
                  type: 'JSExpression',
                  value: this.transformJSExpression(expression.right),
                };
              }
            } finally {
              // 恢复之前的循环参数
              this.currentLoopArgs = prevLoopArgs;
            }
          } else {
            result = {
              type: 'JSExpression',
              value: this.transformJSExpression(expression.right),
            };
          }
        } else {
          result = {
            type: 'JSExpression',
            value: this.transformJSExpression(expression.right),
          };
        }

        // 将左侧的表达式作为条件，右侧的 JSX 作为节点内容
        const { code } = generate(expression.left);
        let conditionValue = code;

        // 先处理JSSlot参数
        this.currentSlotParams.forEach((param) => {
          // 匹配独立的JSSlot参数（不是作为对象属性的一部分）
          const paramPattern = new RegExp(`\\b${param}\\b(?!\\s*[:=])`, 'g');
          if (paramPattern.test(conditionValue)) {
            console.log(`【transformJSXChild】为条件表达式中的JSSlot参数 ${param} 添加this前缀`);
            conditionValue = conditionValue.replace(paramPattern, `this.${param}`);
          }

          // 特别处理形如 item?.property 或 item.property 的情况
          const paramWithPropPattern = new RegExp(`\\b${param}(\\??\\.\\w+)`, 'g');
          if (paramWithPropPattern.test(conditionValue)) {
            console.log(
              `【transformJSXChild】为条件表达式中带属性的JSSlot参数 ${param} 添加this前缀`,
            );

            // 直接进行全局替换，但要避免重复添加this前缀
            const paramWithPropPattern = new RegExp(`\\b${param}(\\??\\.\\w+)`, 'g');
            conditionValue = conditionValue.replace(paramWithPropPattern, (match, p1) => {
              // 检查这个匹配是否已经有this前缀
              if (match.startsWith(`this.${param}`)) {
                return match; // 已经有this前缀，保持不变
              }
              console.log(
                `【transformJSXChild】为条件表达式中带属性的JSSlot参数 ${param} 添加this前缀: ${match} -> this.${param}${p1}`,
              );
              return `this.${param}${p1}`;
            });
          }
        });

        // 处理感叹号前缀，确保不会重复添加
        conditionValue = this.fixExclamationMarks(conditionValue);

        // 处理循环变量引用，确保正确添加this前缀
        // 先检查是否已经有重复的this前缀，如this.this.item，将其修正为this.item
        const thisRepeatPattern1 = /this\.this\./g;
        if (thisRepeatPattern1.test(conditionValue)) {
          console.log(`【transformJSXChild】修复重复的this前缀`);
          conditionValue = conditionValue.replace(thisRepeatPattern1, 'this.');
        }

        // 检查当前循环变量和所有循环变量
        console.log(`【transformJSXChild】当前循环变量:`, this.currentLoopArgs);
        console.log(`【transformJSXChild】所有循环变量:`, this.allLoopArgs);

        // 替换形如 this.item 的引用为 item（临时替换，后面会再添加this前缀）
        this.allLoopArgs.forEach((arg) => {
          const thisPattern = new RegExp(`this\\.${arg}\\b`, 'g');
          if (thisPattern.test(conditionValue)) {
            console.log(`【transformJSXChild】替换 this.${arg} 为 ${arg}`);
            conditionValue = conditionValue.replace(thisPattern, arg);
          }
        });

        // 处理JSSlot参数，移除已有的this前缀（如果有）
        this.currentSlotParams.forEach((param) => {
          const thisPattern = new RegExp(`this\\.${param}\\b`, 'g');
          if (thisPattern.test(conditionValue)) {
            console.log(`【transformJSXChild】替换 this.${param} 为 ${param}`);
            conditionValue = conditionValue.replace(thisPattern, param);
          }
        });
        console.log('conditionValue: ', conditionValue);
        console.log('this.allLoopArgs: ', this.allLoopArgs);
        // 现在为所有循环变量添加this前缀
        this.allLoopArgs.forEach((arg) => {
          console.log('conditionValue: ', conditionValue, arg);
          // 先处理带属性的循环变量
          const argWithPropPattern = new RegExp(`\\b${arg}(\\??\\.|\\.)([\\w$]+)`, 'g');
          if (argWithPropPattern.test(conditionValue)) {
            console.log('conditionValue: ', conditionValue, arg);
            console.log(`【transformJSXChild】为带属性的循环变量 ${arg} 添加this前缀`);

            // 直接进行全局替换，但要避免重复添加this前缀
            conditionValue = conditionValue.replace(
              new RegExp(`\\b${arg}(?:\\??\\.|\\.)(\\w+)`, 'g'),
              (match, p1) => {
                // 检查这个匹配是否已经有this前缀
                if (match.startsWith(`this.${arg}`)) {
                  return match; // 已经有this前缀，保持不变
                }
                console.log(
                  `【transformJSXChild】为带属性的循环变量 ${arg} 添加this前缀: ${match} -> this.${arg}?.${p1}`,
                );
                return `this.${arg}?.${p1}`;
              },
            );
          }

          // 再处理独立的循环变量
          const argPattern = new RegExp(`\\b${arg}\\b(?!\\s*=[^=])(?!\\.|\\")`, 'g');
          console.log(
            `【DEBUG】处理循环变量 ${arg}，正则: ${argPattern}，当前conditionValue: "${conditionValue}"`,
          );
          console.log(`【DEBUG】正则测试结果: ${argPattern.test(conditionValue)}`);
          // 重置lastIndex
          argPattern.lastIndex = 0;
          // 直接进行全局替换，处理独立的循环变量
          const originalConditionValue = conditionValue;
          conditionValue = conditionValue.replace(argPattern, (match, offset, str) => {
            console.log(`【DEBUG】匹配到: "${match}", offset: ${offset}`);
            // 检查前面是否已经有this前缀
            const beforeMatch = str.substring(Math.max(0, offset - 5), offset);
            console.log(`【DEBUG】前面的字符: "${beforeMatch}"`);
            if (beforeMatch.endsWith('this.')) {
              console.log(`【DEBUG】已有this前缀，跳过`);
              return match; // 已经有this前缀，保持不变
            }
            console.log(
              `【transformJSXChild】为独立循环变量 ${arg} 添加this前缀: ${match} -> this.${arg}`,
            );
            return `this.${arg}`;
          });
          console.log(
            `【DEBUG】处理${arg}后，conditionValue从 "${originalConditionValue}" 变为 "${conditionValue}"`,
          );
        });

        // 为所有JSSlot参数添加this前缀
        this.currentSlotParams.forEach((param) => {
          // 匹配独立的JSSlot参数（不是作为对象属性的一部分）
          const paramPattern = new RegExp(`\\b${param}\\b(?!\\s*=[^=])(?!\\.|\\\")`, 'g');
          conditionValue = conditionValue.replace(paramPattern, (match, offset, str) => {
            // 检查前面是否已经有this前缀
            const beforeMatch = str.substring(Math.max(0, offset - 5), offset);
            if (beforeMatch.endsWith('this.')) {
              return match; // 已经有this前缀，保持不变
            }
            console.log(
              `【transformJSXChild】为JSSlot参数 ${param} 添加this前缀: ${match} -> this.${param}`,
            );
            return `this.${param}`;
          });

          // 特别处理形如 record?.property 或 record.property 的情况
          const paramWithPropPattern = new RegExp(`\\b${param}(\\??\\.\\w+)`, 'g');
          if (paramWithPropPattern.test(conditionValue)) {
            console.log(`【transformJSXChild】为带属性的JSSlot参数 ${param} 添加this前缀`);

            // 直接进行全局替换，但要避免重复添加this前缀
            conditionValue = conditionValue.replace(paramWithPropPattern, (match, p1) => {
              // 检查这个匹配是否已经有this前缀
              if (match.startsWith(`this.${param}`)) {
                return match; // 已经有this前缀，保持不变
              }
              console.log(
                `【transformJSXChild】为带属性的JSSlot参数 ${param} 添加this前缀: ${match} -> this.${param}${p1}`,
              );
              return `this.${param}${p1}`;
            });
          }
        });

        // 处理感叹号前缀，确保不会重复添加
        conditionValue = this.fixExclamationMarks(conditionValue);
        console.log('this.currentSlotParams: ', this.currentSlotParams);
        // 处理作用域变量，确保正确添加this前缀
        this.currentSlotParams.forEach((param) => {
          console.log(`【transformJSXChild】处理作用域变量: ${param}`);
          // 匹配独立的作用域变量（包括比较操作符的情况）
          const paramPattern = new RegExp(
            `\\b${param}\\b(?![\\w$])(?!\\s*\\?\\s*\\.)(?!\\s*\\.)(?!\\s*\\[)(?!\\s*\\(\\s*\\{)`,
            'g',
          );
          // 直接进行全局替换，处理独立的作用域变量
          conditionValue = conditionValue.replace(paramPattern, (match, offset, str) => {
            // 检查前面是否已经有this前缀
            const beforeMatch = str.substring(Math.max(0, offset - 5), offset);
            if (beforeMatch.endsWith('this.')) {
              return match; // 已经有this前缀，保持不变
            }
            console.log(
              `【transformJSXChild】为独立作用域变量 ${param} 添加this前缀: ${match} -> this.${param}`,
            );
            return `this.${param}`;
          });

          // 特别处理形如 record?.property 或 record.property 的情况
          const paramWithPropPattern = new RegExp(`\\b${param}(\\??\\.\\w+)`, 'g');
          if (paramWithPropPattern.test(conditionValue)) {
            console.log(`【transformJSXChild】为带属性的作用域变量 ${param} 添加this前缀`);

            // 直接进行全局替换，但要避免重复添加this前缀
            conditionValue = conditionValue.replace(paramWithPropPattern, (match, p1) => {
              // 检查这个匹配是否已经有this前缀
              if (match.startsWith(`this.${param}`)) {
                return match; // 已经有this前缀，保持不变
              }
              console.log(
                `【transformJSXChild】为带属性的作用域变量 ${param} 添加this前缀: ${match} -> this.${param}${p1}`,
              );
              return `this.${param}${p1}`;
            });
          }
        });

        // 最后检查是否有重复的this前缀，如this.this.item，将其修正为this.item
        const thisRepeatPattern2 = /this\.this\./g;
        if (thisRepeatPattern2.test(conditionValue)) {
          console.log(`【transformJSXChild】修复重复的this前缀`);
          conditionValue = conditionValue.replace(thisRepeatPattern2, 'this.');
        }

        console.log('【transformJSXChild】处理后的条件表达式:', conditionValue);
        result.condition = {
          type: 'JSExpression',
          value: conditionValue,
        };
        console.log('【transformJSXChild】条件表达式最终结果:', result.condition);

        return result;
      }

      if (t.isConditionalExpression(expression)) {
        console.log(`【transformJSXChild】处理条件表达式，转换为两个独立的条件渲染`);

        // 获取条件表达式
        const test = this.transformJSExpression(expression.test);

        // 创建条件渲染节点的辅助函数
        const createConditionalNode = (expr: t.Expression, conditionValue: string) => {
          if (t.isStringLiteral(expr)) {
            // 对于字符串字面量，包装成Text组件
            return {
              componentName: 'Text',
              props: {
                children: expr.value,
              },
              children: [],
              condition: {
                type: 'JSExpression',
                value: conditionValue,
              },
            };
          } else {
            // 对于其他表达式，包装成Text组件并使用JSExpression
            return {
              componentName: 'Text',
              props: {
                children: {
                  type: 'JSExpression',
                  value: this.transformJSExpression(expr),
                },
              },
              children: [],
              condition: {
                type: 'JSExpression',
                value: conditionValue,
              },
            };
          }
        };

        // 创建两个独立的条件渲染节点
        const result = {
          componentName: 'View', // 直接使用View而不是Fragment
          children: [
            createConditionalNode(expression.consequent, test),
            createConditionalNode(expression.alternate, `!(${test})`),
          ],
        };

        console.log(`【transformJSXChild】条件表达式处理结果:`, result);
        return result;
      }

      // 处理数组遍历表达式
      if (
        (t.isCallExpression(expression) || t.isOptionalCallExpression(expression)) &&
        (t.isMemberExpression(expression.callee) ||
          t.isOptionalMemberExpression(expression.callee)) &&
        t.isIdentifier(expression.callee.property) &&
        expression.callee.property.name === 'map'
      ) {
        console.log(`【transformJSXChild】处理map循环表达式`);
        const arrayExpr = expression.callee.object;
        const mapCallback = expression.arguments[0];

        let actualArrayExpr = arrayExpr;
        if (
          t.isCallExpression(arrayExpr) &&
          t.isIdentifier(arrayExpr.callee) &&
          arrayExpr.callee.name === '__$$evalArray'
        ) {
          const evalArrayArg = arrayExpr.arguments[0];
          if (t.isArrowFunctionExpression(evalArrayArg)) {
            if (t.isExpression(evalArrayArg.body)) {
              actualArrayExpr = evalArrayArg.body;
            } else {
              // 如果是BlockStatement，我们需要找到return语句
              const returnStmt = evalArrayArg.body.body.find((stmt) => t.isReturnStatement(stmt));
              if (returnStmt && t.isReturnStatement(returnStmt) && returnStmt.argument) {
                actualArrayExpr = returnStmt.argument;
              }
            }
          }
        }

        if (t.isArrowFunctionExpression(mapCallback)) {
          const itemParam = mapCallback.params[0];
          const indexParam = mapCallback.params[1];

          // 保存之前的循环参数
          const prevLoopArgs = [...this.currentLoopArgs];

          // 获取循环变量名
          const itemName = t.isIdentifier(itemParam) ? itemParam.name : 'item';
          const indexName = t.isIdentifier(indexParam) ? indexParam.name : 'index';

          console.log(
            `【transformJSXChild】循环变量: itemName=${itemName}, indexName=${indexName}`,
          );

          // 更新当前循环参数
          this.currentLoopArgs = [itemName, indexName];

          // 将新的循环变量添加到所有循环变量列表中
          if (!this.allLoopArgs.includes(itemName)) {
            this.allLoopArgs.push(itemName);
          }
          if (!this.allLoopArgs.includes(indexName)) {
            this.allLoopArgs.push(indexName);
          }

          // 在处理完当前循环后恢复之前的循环参数
          const cleanup = () => {
            console.log(`【transformJSXChild】恢复之前的循环变量:`, prevLoopArgs);
            this.currentLoopArgs = prevLoopArgs;
            // 注意：我们不从 allLoopArgs 中移除变量，因为它们可能在后续的嵌套表达式中被引用
          };

          // 获取遍历项的 JSX 结构
          const jsxBody = t.isBlockStatement(mapCallback.body)
            ? mapCallback.body.body.find((node) => t.isReturnStatement(node))?.argument
            : mapCallback.body;

          // 创建一个基础的容器节点
          const containerSchema = {
            componentName: 'View',
            props: {},
            children: [],
            loop: {
              type: 'JSExpression',
              value: '',
            },
            loopArgs: [itemName, indexName],
          };

          // 获取循环数组表达式的原始代码
          try {
            const { code } = generate(actualArrayExpr);
            console.log(`【transformJSXChild】循环数组表达式原始代码: "${code}"`);

            // 处理循环数组表达式中的循环变量引用
            let arrayExprValue = code;

            // 检查当前循环变量和父级循环变量
            const allLoopArgs = [...this.allLoopArgs];
            console.log(
              `【transformJSXChild】处理循环数组表达式中的循环变量引用，所有循环变量:`,
              allLoopArgs,
            );

            // 先检查是否已经有重复的this前缀
            const doubleThisPattern = /this\.this\./g;
            if (doubleThisPattern.test(arrayExprValue)) {
              console.log(`【transformJSXChild】修复循环数组表达式中重复的this前缀`);
              arrayExprValue = arrayExprValue.replace(doubleThisPattern, 'this.');
            }

            // 处理 JSSlot 参数
            this.currentSlotParams.forEach((param) => {
              // 匹配独立的 JSSlot 参数（不是作为对象属性的一部分）
              const paramPattern = new RegExp(`\\b${param}\\b(?!\\s*[:=])`, 'g');
              if (paramPattern.test(arrayExprValue) && !arrayExprValue.includes(`this.${param}`)) {
                console.log(
                  `【transformJSXChild】为循环数组表达式中的 JSSlot 参数 ${param} 添加this前缀`,
                );
                arrayExprValue = arrayExprValue.replace(paramPattern, `this.${param}`);
              }

              // 特别处理形如 record?.property 或 record.property 的情况
              const paramWithPropPattern = new RegExp(`\\b${param}(\\??\\.\\w+)`, 'g');
              if (
                paramWithPropPattern.test(arrayExprValue) &&
                !arrayExprValue.includes(`this.${param}`)
              ) {
                console.log(
                  `【transformJSXChild】为循环数组表达式中带属性的 JSSlot 参数 ${param} 添加this前缀`,
                );
                arrayExprValue = arrayExprValue.replace(paramWithPropPattern, `this.${param}$1`);
              }
            });

            // 处理父级循环变量引用
            if (!arrayExprValue.startsWith('this.')) {
              allLoopArgs.forEach((arg) => {
                // 匹配独立的循环变量（不是作为对象属性的一部分）
                const argPattern = new RegExp(`\\b${arg}\\b(?!\\s*[:=])`, 'g');
                if (argPattern.test(arrayExprValue) && !arrayExprValue.includes(`this.${arg}`)) {
                  console.log(
                    `【transformJSXChild】为循环数组表达式中的循环变量 ${arg} 添加this前缀`,
                  );
                  arrayExprValue = arrayExprValue.replace(argPattern, `this.${arg}`);
                }

                // 特别处理形如 item1?.property 或 item1.property 的情况
                const argWithPropPattern = new RegExp(`\\b${arg}(\\??\\.\\w+)`, 'g');
                if (
                  argWithPropPattern.test(arrayExprValue) &&
                  !arrayExprValue.includes(`this.${arg}`)
                ) {
                  console.log(
                    `【transformJSXChild】为循环数组表达式中带属性的循环变量 ${arg} 添加this前缀`,
                  );
                  arrayExprValue = arrayExprValue.replace(argWithPropPattern, `this.${arg}$1`);
                }
              });
            }

            // 处理感叹号前缀
            arrayExprValue = this.fixExclamationMarks(arrayExprValue);

            containerSchema.loop.value = arrayExprValue;
            console.log('containerSchema: ', containerSchema);
            console.log(`【transformJSXChild】最终循环数组表达式: "${arrayExprValue}"`);
          } catch (error) {
            console.error(`【transformJSXChild】处理循环数组表达式时出错:`, error);
            containerSchema.loop.value = 'null';
          }

          try {
            // 处理遍历项的子节点
            if (jsxBody && t.isJSXElement(jsxBody)) {
              console.log(`【transformJSXChild】处理map循环中的JSX元素`);
              containerSchema.children = [this.transformJSXElement(jsxBody)];
            } else if (
              jsxBody &&
              (t.isCallExpression(jsxBody) || t.isOptionalCallExpression(jsxBody)) &&
              t.isArrowFunctionExpression(jsxBody.callee) &&
              t.isJSXElement(jsxBody.callee.body)
            ) {
              console.log(`【transformJSXChild】处理map循环中的函数调用JSX元素`);
              containerSchema.children = [this.transformJSXElement(jsxBody.callee?.body)];
            } else {
              console.log(`【transformJSXChild】未能识别的map循环体类型:`, jsxBody?.type);
            }

            console.log(`【transformJSXChild】生成的循环容器:`, containerSchema);
            return {
              ...containerSchema?.children[0],
              loop: containerSchema.loop,
              loopArgs: containerSchema.loopArgs,
            };
          } finally {
            cleanup();
          }
        }
      }

      // 处理条件表达式
      if (t.isLogicalExpression(expression) || t.isUnaryExpression(expression)) {
        // 获取表达式的原始代码
        const { code } = generate(expression);
        console.log(`【transformJSXChild】原始条件表达式: "${code}"`);
        console.log(`【transformJSXChild】当前循环变量列表:`, this.currentLoopArgs);

        // 处理循环变量引用
        let expressionValue = code;

        // 先检查是否已经有this前缀
        const hasThisPrefix = expressionValue.includes('this.');
        console.log(`【transformJSXChild】表达式是否已包含this前缀: ${hasThisPrefix}`);

        // 处理循环变量
        this.currentLoopArgs.forEach((arg) => {
          console.log(`【transformJSXChild】处理循环变量: ${arg}`);

          // 直接处理形如 item1 或 item1?.property 或 item1.property 的所有情况
          // 修改正则表达式，不再期望arg后面有额外的数字
          const varPattern = new RegExp(`\\b(${arg})(?:\\??\\.[\\w\\.]+|\\b)`, 'g');
          console.log(`【transformJSXChild】正则表达式模式: ${varPattern}`);
          console.log(
            `【transformJSXChild】测试条件表达式: "${expressionValue}" 是否匹配正则: ${varPattern.test(
              expressionValue,
            )}`,
          );

          // 重置正则表达式的lastIndex，因为test方法会改变它
          varPattern.lastIndex = 0;

          if (varPattern.test(expressionValue)) {
            console.log(`【transformJSXChild】找到循环变量 ${arg} 的匹配`);

            // 重置正则表达式的lastIndex
            varPattern.lastIndex = 0;

            // 先获取所有匹配项
            const matches = [];
            let match;
            const tempRegex = new RegExp(`\\b(${arg})(?:\\??\\.[\\w\\.]+|\\b)`, 'g');
            console.log(`【transformJSXChild】临时正则表达式: ${tempRegex}`);

            // 手动测试一下匹配
            const testMatch = expressionValue.match(tempRegex);
            console.log(`【transformJSXChild】手动测试匹配结果:`, testMatch);

            // 重置正则表达式
            tempRegex.lastIndex = 0;

            while ((match = tempRegex.exec(expressionValue)) !== null) {
              matches.push({
                full: match[0],
                varName: match[1],
              });
              console.log(`【transformJSXChild】匹配到: ${match[0]}, 变量名: ${match[1]}`);
            }

            // 从最长的匹配开始处理，避免部分替换问题
            matches.sort((a, b) => b.full.length - a.full.length);

            for (const item of matches) {
              // 检查是否已经有this前缀
              if (expressionValue.includes(`this.${item.varName}`)) {
                console.log(`【transformJSXChild】变量 ${item.varName} 已经有this前缀，跳过`);
                continue;
              }

              // 替换变量名为带this前缀的形式
              console.log(`【transformJSXChild】为变量 ${item.varName} 添加this前缀`);
              const replaceRegex = new RegExp(`\\b${item.varName}\\b`, 'g');
              console.log(`【transformJSXChild】替换正则表达式: ${replaceRegex}`);

              // 测试替换正则是否匹配
              console.log(
                `【transformJSXChild】测试替换正则是否匹配: ${replaceRegex.test(expressionValue)}`,
              );
              replaceRegex.lastIndex = 0;

              const beforeReplace = expressionValue;
              expressionValue = expressionValue.replace(replaceRegex, `this.${item.varName}`);

              // 检查替换是否成功
              if (beforeReplace === expressionValue) {
                console.log(`【transformJSXChild】警告: 替换未生效!`);
              } else {
                console.log(
                  `【transformJSXChild】替换成功: "${beforeReplace}" -> "${expressionValue}"`,
                );
              }
            }
          }
        });

        // 处理感叹号前缀
        expressionValue = this.fixExclamationMarks(expressionValue);

        // 最后检查是否有重复的this前缀，如this.this.item，将其修正为this.item
        const thisRepeatPattern2 = /this\.this\./g;
        if (thisRepeatPattern2.test(expressionValue)) {
          console.log(`【transformJSXChild】修复重复的this前缀`);
          expressionValue = expressionValue.replace(thisRepeatPattern2, 'this.');
        }

        console.log(`【transformJSXChild】处理后的条件表达式: "${expressionValue}"`);
        console.log(`【transformJSXChild】条件表达式最终结果:`, {
          type: 'JSExpression',
          value: expressionValue,
        });

        if (expressionValue.startsWith('this.state.form?.images.map(function(item, index)')) {
          debugger;
        }
        return {
          type: 'JSExpression',
          value: expressionValue,
        };
      }

      // 处理对象表达式中的条件
      if (t.isObjectExpression(expression)) {
        console.log(`【transformJSXChild】处理对象表达式`);
        const obj: any = {};
        expression.properties.forEach((prop) => {
          if (t.isObjectProperty(prop)) {
            const key = t.isIdentifier(prop.key)
              ? prop.key.name
              : t.isStringLiteral(prop.key)
              ? prop.key.value
              : '';
            if (key) {
              console.log(`【transformJSXChild】处理对象属性: ${key}, 值类型: ${prop.value.type}`);
              // 处理对象属性中的字面量值
              if (t.isNumericLiteral(prop.value)) {
                obj[key] = prop.value.value;
              } else if (t.isBooleanLiteral(prop.value)) {
                obj[key] = prop.value.value;
              } else if (t.isStringLiteral(prop.value)) {
                obj[key] = prop.value.value;
              } else if (t.isNullLiteral(prop.value)) {
                obj[key] = null;
              } else if (t.isJSXExpressionContainer(prop.value)) {
                const exprContainer = prop.value as t.JSXExpressionContainer;
                obj[key] = {
                  type: 'JSExpression',
                  value: this.transformJSExpression(exprContainer.expression),
                };
                if (
                  obj[key].value.startsWith('this.state.form?.images.map(function(item, index)')
                ) {
                  debugger;
                }
              } else if (t.isMemberExpression(prop.value)) {
                obj[key] = {
                  type: 'JSExpression',
                  value: this.transformJSExpression(prop.value),
                };
              } else if (t.isObjectExpression(prop.value)) {
                // 特殊处理 _unsafe_classNameJson 中的条件
                if (key === '_unsafe_classNameJson') {
                  console.log('【transformJSXChild】开始处理 _unsafe_classNameJson:', prop.value);
                  const classNameJson = prop.value.properties.map((p) => {
                    if (t.isObjectProperty(p)) {
                      const propKey = t.isIdentifier(p.key) ? p.key.name : '';
                      console.log('【transformJSXChild】处理 _unsafe_classNameJson 属性:', propKey);
                      if (propKey === 'condition') {
                        const { code } = generate(p.value);
                        let conditionValue = code;
                        console.log('【transformJSXChild】原始条件表达式:', conditionValue);
                        console.log('【transformJSXChild】条件表达式的 AST:', p.value);
                        console.log('【transformJSXChild】当前循环变量列表:', this.currentLoopArgs);

                        // 处理循环变量
                        this.currentLoopArgs.forEach((arg) => {
                          console.log('【transformJSXChild】处理循环变量:', arg);
                          // 处理父级循环变量
                          const parentArgPattern = new RegExp(
                            `\\b${arg}\\d+\\b(?!\\s*=)(?!\\.|\\\")`,
                            'g',
                          );
                          if (parentArgPattern.test(conditionValue)) {
                            console.log('【transformJSXChild】找到父级循环变量:', arg);
                            conditionValue = conditionValue.replace(parentArgPattern, (match) => {
                              if (conditionValue.includes(`this.${match}`)) {
                                console.log(
                                  '【transformJSXChild】父级循环变量已有this前缀:',
                                  match,
                                );
                                return match;
                              }
                              console.log(
                                '【transformJSXChild】为父级循环变量添加this前缀:',
                                match,
                              );
                              return `this.${match}`;
                            });
                          }

                          // 处理父级循环变量的属性访问
                          const parentArgWithPropPattern = new RegExp(
                            `\\b${arg}\\d+(\\??\\.\\w+)`,
                            'g',
                          );
                          if (parentArgWithPropPattern.test(conditionValue)) {
                            console.log('【transformJSXChild】找到带属性的父级循环变量:', arg);
                            conditionValue = conditionValue.replace(
                              parentArgWithPropPattern,
                              (match, prop) => {
                                if (conditionValue.includes(`this.${match}`)) {
                                  console.log(
                                    '【transformJSXChild】带属性的父级循环变量已有this前缀:',
                                    match,
                                  );
                                  return match;
                                }
                                console.log(
                                  '【transformJSXChild】为带属性的父级循环变量添加this前缀:',
                                  match,
                                );
                                return `this.${match}${prop}`;
                              },
                            );
                          }
                        });

                        // 处理JSSlot参数
                        console.log(
                          '【transformJSXChild】当前JSSlot参数列表:',
                          this.currentSlotParams,
                        );
                        this.currentSlotParams.forEach((param) => {
                          console.log('【transformJSXChild】处理JSSlot参数:', param);

                          // 处理独立的JSSlot参数
                          const slotParamPattern = new RegExp(
                            `\\b${param}\\b(?!\\s*=)(?!\\.|\\\")`,
                            'g',
                          );
                          if (slotParamPattern.test(conditionValue)) {
                            console.log('【transformJSXChild】找到JSSlot参数:', param);
                            conditionValue = conditionValue.replace(slotParamPattern, (match) => {
                              if (conditionValue.includes(`this.${match}`)) {
                                console.log('【transformJSXChild】JSSlot参数已有this前缀:', match);
                                return match;
                              }
                              console.log('【transformJSXChild】为JSSlot参数添加this前缀:', match);
                              return `this.${match}`;
                            });
                          }

                          // 处理JSSlot参数的属性访问
                          const slotParamWithPropPattern = new RegExp(
                            `\\b${param}(\\??\\.\\w+)`,
                            'g',
                          );
                          if (slotParamWithPropPattern.test(conditionValue)) {
                            console.log('【transformJSXChild】找到带属性的JSSlot参数:', param);
                            conditionValue = conditionValue.replace(
                              slotParamWithPropPattern,
                              (match, prop) => {
                                if (conditionValue.includes(`this.${match}`)) {
                                  console.log(
                                    '【transformJSXChild】带属性的JSSlot参数已有this前缀:',
                                    match,
                                  );
                                  return match;
                                }
                                console.log(
                                  '【transformJSXChild】为带属性的JSSlot参数添加this前缀:',
                                  match,
                                );
                                return `this.${match}`;
                              },
                            );
                          }
                        });

                        console.log('【transformJSXChild】处理后的条件表达式:', conditionValue);
                        console.log('【transformJSXChild】条件表达式最终结果:', {
                          type: 'JSExpression',
                          value: conditionValue,
                        });

                        return {
                          ...(t.isObjectProperty(p)
                            ? { [propKey]: { type: 'JSExpression', value: conditionValue } }
                            : {}),
                          ...(t.isObjectProperty(p) &&
                          t.isIdentifier(p.key) &&
                          p.key.name === 'selector'
                            ? { selector: t.isStringLiteral(p.value) ? p.value.value : '' }
                            : {}),
                        };
                      }
                    }
                    return {};
                  });
                  console.log(
                    '【transformJSXChild】_unsafe_classNameJson 最终结果:',
                    classNameJson,
                  );
                  obj[key] = classNameJson;
                } else {
                  obj[key] = this.transformJSXAttributeValue({
                    type: 'JSXExpressionContainer',
                    expression: prop.value,
                  });
                }
              } else if (t.isArrayExpression(prop.value)) {
                obj[key] = this.transformArrayExpression(prop.value);
              } else {
                try {
                  let transformedValue;
                  if (
                    t.isStringLiteral(prop.value) ||
                    t.isJSXElement(prop.value) ||
                    (typeof t.isJSXFragment === 'function' && t.isJSXFragment(prop.value)) ||
                    t.isJSXExpressionContainer(prop.value) ||
                    prop.value === null
                  ) {
                    // 只有这几种类型才安全传递给 transformJSXAttributeValue
                    transformedValue = this.transformJSXAttributeValue(
                      prop.value as t.JSXAttribute['value'],
                    );
                  } else if (t.isExpression(prop.value)) {
                    transformedValue = {
                      type: 'JSExpression',
                      value: this.transformJSExpression(prop.value as t.Expression),
                    };
                  } else {
                    // 其它类型兜底
                    transformedValue = {
                      type: 'JSExpression',
                      value: '/* 处理错误 */',
                    };
                  }
                  obj[key] = transformedValue;
                } catch (error) {
                  console.error(`【transformJSXChild】处理对象属性 ${key} 时出错:`, error);
                  obj[key] = {
                    type: 'JSExpression',
                    value: '/* 处理错误 */',
                  };
                }
              }
            }
          }
        });
        return obj;
      }

      // 对于其他简单表达式（如 {item}），转换为JSExpression
      console.log(`【transformJSXChild】处理简单表达式: ${expression.type}`);
      const { code } = generate(expression);
      console.log(`【transformJSXChild】简单表达式原始代码: "${code}"`);

      // 处理循环变量引用，确保正确添加this前缀
      let expressionValue = code;

      // 检查是否是循环变量或JSSlot参数
      if (t.isIdentifier(expression)) {
        const isLoopArg = this.allLoopArgs.includes(expression.name);
        const isSlotParam = this.currentSlotParams.includes(expression.name);
        console.log(
          `【transformJSXChild】标识符: ${expression.name}, 是否是循环变量: ${isLoopArg}, 是否是JSSlot参数: ${isSlotParam}`,
        );

        if (isLoopArg || isSlotParam) {
          console.log(`【transformJSXChild】为变量 ${expression.name} 添加this前缀`);
          expressionValue = `this.${expression.name}`;
        }
      } else {
        // 对于非Identifier表达式，使用transformJSExpression处理
        expressionValue = this.transformJSExpression(expression);
      }

      console.log(`【transformJSXChild】简单表达式处理结果: "${expressionValue}"`);
      // 将JSExpression包装成Text组件，确保所有节点都可以在画布中选择
      return {
        componentName: 'Text',
        props: {
          children: {
            type: 'JSExpression',
            value: expressionValue,
          },
        },
        children: [],
      };

      return null;
    }

    if (t.isJSXFragment(child)) {
      console.log(`【transformJSXChild】处理JSX Fragment`);
      return {
        componentName: 'View', // 直接使用View而不是Fragment
        children: child.children
          .filter((child) => !t.isJSXText(child) || child.value.trim())
          .map((child) => this.transformJSXChild(child, 'View')), // 这里也改为View
      };
    }

    return null;
  }

  private transformJSXElement(element: t.JSXElement): IPublicTypeNodeSchema {
    const schema: IPublicTypeNodeSchema = {
      componentName: '',
      props: {},
      children: [],
    };

    if (!element.openingElement) {
      return schema;
    }

    // 处理组件名，支持 Form.Item 这种复合组件名
    if (t.isJSXIdentifier(element.openingElement?.name)) {
      schema.componentName = element.openingElement.name.name;
      // 新增：收集组件名
      this.usedComponentNames.add(schema.componentName);
    } else if (t.isJSXMemberExpression(element.openingElement?.name)) {
      // 处理复合组件名，如 Form.Item
      const parts: string[] = [];
      let current: t.JSXMemberExpression | t.JSXIdentifier = element.openingElement.name;

      while (t.isJSXMemberExpression(current)) {
        if (t.isJSXIdentifier(current.property)) {
          parts.unshift(current.property.name);
        }
        current = current.object;
      }

      if (t.isJSXIdentifier(current)) {
        parts.unshift(current.name);
      }

      schema.componentName = parts.join('.');
      // 新增：收集复合组件名
      this.usedComponentNames.add(schema.componentName);
    }

    // 处理属性
    element.openingElement?.attributes.forEach((attr) => {
      if (t.isJSXAttribute(attr)) {
        const propName = attr.name.name as string;
        const propValue = this.transformJSXAttributeValue(
          attr.value,
          schema.componentName,
          propName,
        );
        console.log('propValue: ', attr.value, propValue);
        if (propValue !== undefined) {
          schema.props[propName] = propValue;
        }
      }
    });

    // 转换组件名
    schema.componentName = this.transformComponentName(schema) || schema.componentName;

    // 检查Tailwind样式互转
    const nodeClassName = schema.props.className as string;
    console.log('nodeClassName: ', nodeClassName);
    const needTransformStyle = !!nodeClassName;
    if (needTransformStyle) {
      // 进行Tailwind classNameToStyle 的转换
      const styleAboutProps = classToStyle(nodeClassName, {
        // globalStyles: this.globalStyles,
        // fileStyles: this.fileStyles,
        // variables: this.variables,
      });
      if (styleAboutProps.style) {
        schema.props = { ...schema.props, ...styleAboutProps };
      }
      console.log('styleAboutProps: ', nodeClassName, styleAboutProps);
      if (schema.props.className) {
        // @ts-ignore
        window.unconvertedClasses = { ...(window.unconvertedClasses || {}), [nodeClassName]: true };
      }
    }

    // 处理代码ref到SchemaRef
    if (schema.props.ref && isJSExpression(schema.props.ref)) {
      schema.props.ref = schema.props.ref.value.match(/\"([^\"]+)\"/)?.[1] || '';
    }

    // 处理子节点
    const children = element.children
      .filter((child) => !t.isJSXText(child) || child.value.trim())
      .map((child, index, array) => {
        // 如果当前节点已被处理（作为混合文本的一部分），则跳过
        if (this.isNodeProcessed(child)) {
          return null;
        }
        return this.transformJSXChild(child, schema.componentName);
      })
      .filter(Boolean); // 过滤掉null值

    // 检查是否需要合并混合文本内容（适用于p、span等可能包含文本的标签）
    const shouldMergeMixedContent = [
      'p',
      'span',
      'h1',
      'h2',
      'h3',
      'h4',
      'h5',
      'h6',
      'div',
    ].includes(schema.componentName.toLowerCase());

    if (shouldMergeMixedContent && children.length > 1) {
      // 检查是否包含混合内容（文本 + 表达式）
      const hasMixedContent =
        children.some((child) => typeof child === 'string') &&
        children.some(
          (child) =>
            child &&
            typeof child === 'object' &&
            child.componentName === 'Text' &&
            child.props.children &&
            child.props.children.type === 'JSExpression',
        );

      if (hasMixedContent) {
        console.log(
          `【transformJSXElement】检测到${schema.componentName}中的混合文本内容，合并为模板字符串`,
        );

        // 将所有children合并为一个模板字符串表达式
        const templateParts = children.map((child) => {
          if (typeof child === 'string') {
            return child;
          } else if (
            child &&
            typeof child === 'object' &&
            child.componentName === 'Text' &&
            child.props.children &&
            child.props.children.type === 'JSExpression'
          ) {
            return `\${${child.props.children.value}}`;
          }
          return '';
        });

        const mergedTemplate = `\`${templateParts.join('')}\``;
        console.log(`【transformJSXElement】合并后的模板字符串: ${mergedTemplate}`);

        schema.props.children = {
          type: 'JSExpression',
          value: mergedTemplate,
        };
        schema.children = [];
        return schema;
      }
    }

    // 对于 Text 组件，我们需要特殊处理
    if (schema.componentName === 'Text') {
      if (children.length === 1) {
        // 检查是否是包装后的JSExpression（由transformJSXChild处理后的结果）
        if (
          children[0] &&
          typeof children[0] === 'object' &&
          children[0].componentName === 'Text' &&
          children[0].props.children &&
          children[0].props.children.type === 'JSExpression'
        ) {
          schema.props.children = children[0].props.children;
        } else {
          // 保持原有逻辑
          schema.props.children = children[0];
        }
        // 清空 schema.children 以避免重复
        schema.children = [];
      } else if (children.length > 1) {
        // 如果还有多个子节点，说明可能有其他复杂的JSX结构，保持原有的children数组
        schema.children = children;
      }
    } else {
      // 对于其他组件，确保所有文本都被包装成Text组件
      if (children.length === 1) {
        if (typeof children[0] === 'string') {
          // 将单个字符串包装成Text组件，而不是直接设置为props.children
          console.log(
            `【transformJSXElement】将${schema.componentName}中的单个字符串包装成Text组件: "${children[0]}"`,
          );
          schema.children = [
            {
              componentName: 'Text',
              props: {
                children: children[0],
              },
              children: [],
            },
          ];
        } else {
          // 修复bug：不要将Text组件的children提取到父组件，而是保持Text组件作为子组件
          schema.children = children;
        }
      } else {
        // 处理多个子节点时，将字符串包装成Text组件
        console.log(
          `【transformJSXElement】处理${schema.componentName}中的多个子节点，包装字符串为Text组件`,
        );

        // 所有字符串都包装成Text组件
        schema.children = children.map((child) => {
          if (typeof child === 'string') {
            console.log(`【transformJSXElement】包装字符串为Text组件: "${child}"`);
            return {
              componentName: 'Text',
              props: {
                children: child,
              },
              children: [],
            };
          }
          return child;
        });
      }
    }

    return schema;
  }

  transformComponentName(schema: IPublicTypeNodeSchema): string {
    if (schema.componentName === 'div') {
      return 'View';
    }
    if (schema.componentName === 'span') {
      return 'Text';
    }
    if (schema.componentName === 'img') {
      return 'Image';
    }
    if (schema.componentName === 'i') {
      return 'Icon';
    }
    if (schema.componentName === 'a') {
      return 'View';
    }
    if (schema.componentName === 'Fragment') {
      return 'View';
    }
  }

  private transformArrowFunctionToNormalFunction(expr: t.ArrowFunctionExpression): string {
    console.log('将箭头函数转换为普通函数:', expr);

    const isAsync = expr.async ? 'async ' : '';
    try {
      // 处理函数参数
      const params = expr.params
        .map((param) => {
          try {
            return generate(param).code;
          } catch (e) {
            console.error('生成参数代码失败:', e);
            if (t.isIdentifier(param)) {
              return param.name;
            }
            return '...'; // 对于复杂参数模式，简化处理
          }
        })
        .join(', ');

      // 处理函数体
      let bodyCode = '';

      if (t.isBlockStatement(expr.body)) {
        // 如果是代码块，直接生成整个代码块
        try {
          bodyCode = generate(expr.body).code;
          console.log('生成的函数体代码块:', bodyCode);
        } catch (e) {
          console.error('生成函数体代码块失败:', e);
          // 失败时回退到原来的处理方式
          bodyCode = this.processBlockStatement(expr.body);
        }
      } else {
        // 如果是表达式，生成return语句
        try {
          const exprCode = generate(expr.body).code;
          console.log('生成的表达式代码:', exprCode);
          bodyCode = `{ return ${exprCode}; }`;
        } catch (e) {
          console.error('生成表达式代码失败:', e);
          // 失败时回退到原来的处理方式
          bodyCode = `{ return ${this.transformJSExpression(expr.body)}; }`;
        }
      }

      // 提取函数参数名列表
      const paramNames = expr.params
        .map((param) => {
          if (t.isIdentifier(param)) {
            return param.name;
          }
          return null;
        })
        .filter(Boolean) as string[];

      const validatedCode = this.transformJSFunctionAddThis(bodyCode, paramNames);

      // 组合成完整的函数
      return `${isAsync}function(${params}) ${validatedCode}`;
    } catch (error) {
      console.error('使用generate生成函数代码失败:', error);

      // 失败时回退到原来的处理方式
      // 处理函数参数
      const params = expr.params
        .map((param) => {
          if (t.isIdentifier(param)) {
            return param.name;
          }
          return '...'; // 对于复杂参数模式，简化处理
        })
        .join(', ');

      // 处理函数体
      let bodyCode = '';
      if (t.isBlockStatement(expr.body)) {
        bodyCode = this.processBlockStatement(expr.body);
      } else {
        bodyCode = `{ return ${this.transformJSExpression(expr.body)}; }`;
      }

      // 检查函数体中是否包含循环变量或作用域变量
      const hasLoopVars = this.currentLoopArgs.some((arg) => bodyCode.includes(arg));
      const hasSlotParams = this.currentSlotParams.some((param) => bodyCode.includes(param));

      if (hasLoopVars || hasSlotParams) {
        // 如果包含循环变量或作用域变量，需要确保正确添加this前缀
        // 处理循环变量
        this.currentLoopArgs.forEach((arg) => {
          // 修改正则表达式，确保不会匹配到对象属性名
          // 使用负向前瞻，确保后面不是冒号
          const pattern = new RegExp(`\\b${arg}\\b(?!\\s*:)`, 'g');
          if (pattern.test(bodyCode)) {
            // 检查是否已经添加了this前缀
            if (bodyCode.includes(`this.${arg}`)) {
              console.log(
                `【transformArrowFunctionToNormalFunction】循环变量 ${arg} 已经有this前缀，跳过`,
              );
              return;
            }
            console.log(`【transformArrowFunctionToNormalFunction】为循环变量 ${arg} 添加this前缀`);
            bodyCode = bodyCode.replace(pattern, `this.${arg}`);
          }
        });

        // 处理作用域变量
        this.currentSlotParams.forEach((param) => {
          // 修改正则表达式，确保不会匹配到对象属性名
          // 使用负向前瞻，确保后面不是冒号
          const pattern = new RegExp(`\\b${param}\\b(?!\\s*:)`, 'g');
          if (pattern.test(bodyCode)) {
            // 检查是否已经添加了this前缀
            if (bodyCode.includes(`this.${param}`)) {
              console.log(
                `【transformArrowFunctionToNormalFunction】作用域变量 ${param} 已经有this前缀，跳过`,
              );
              return;
            }
            console.log(
              `【transformArrowFunctionToNormalFunction】为作用域变量 ${param} 添加this前缀`,
            );
            bodyCode = bodyCode.replace(pattern, `this.${param}`);
          }
        });
      }

      return `${isAsync}function(${params}) ${bodyCode}`;
    }
  }

  // 辅助方法：处理代码块
  private processBlockStatement(block: t.BlockStatement): string {
    try {
      // 尝试直接生成整个代码块
      return generate(block).code;
    } catch (e) {
      console.error('直接生成代码块失败，回退到逐语句处理:', e);

      // 逐语句处理
      return block.body
        .map((statement) => {
          try {
            // 尝试直接生成语句代码
            return generate(statement).code;
          } catch (statementError) {
            console.error('生成语句代码失败:', statementError);

            // 特殊处理各种语句类型
            if (t.isReturnStatement(statement)) {
              return `return ${
                statement.argument ? this.transformJSExpression(statement.argument) : ''
              }`;
            }
            if (t.isExpressionStatement(statement)) {
              return this.transformJSExpression(statement.expression);
            }
            if (t.isVariableDeclaration(statement)) {
              const declarations = statement.declarations
                .map((declarator) => {
                  const id = t.isIdentifier(declarator.id)
                    ? declarator.id.name
                    : generate(declarator.id).code;
                  const init = declarator.init
                    ? this.transformJSExpression(declarator.init)
                    : 'undefined';
                  return `${id} = ${init}`;
                })
                .join(', ');
              return `${statement.kind} ${declarations}`;
            }

            // 对于其他不支持的语句类型，返回注释
            return `/* 不支持的语句类型: ${statement.type} */`;
          }
        })
        .join(';\n  ');
    }
  }

  // 添加一个辅助方法来修复感叹号问题
  private fixExclamationMarks(expr: string): string {
    // 修复多个感叹号的情况，如 !!!! 改为 !!
    return expr.replace(/!{3,}/g, '!!');
  }

  private transformStateValue(value: t.Expression): any {
    // 处理字面量，直接返回原始值
    if (t.isStringLiteral(value)) {
      return value.value;
    }
    if (t.isNumericLiteral(value)) {
      return value.value;
    }
    if (t.isBooleanLiteral(value)) {
      return value.value;
    }
    if (t.isNullLiteral(value)) {
      return null;
    }
    if (t.isIdentifier(value) && value.name === 'undefined') {
      return undefined;
    }

    // 处理数组
    if (t.isArrayExpression(value)) {
      // 先转换所有元素
      const transformedElements = value.elements.map((element) => {
        if (!element) return null;
        // 只处理Expression类型，其他类型转换为JSExpression
        if (t.isExpression(element)) {
          return this.transformStateValue(element);
        }
        // 对于其他类型（如SpreadElement），使用generate生成代码
        try {
          return {
            type: 'JSExpression',
            value: generate(element).code,
          };
        } catch (error) {
          return {
            type: 'JSExpression',
            value: '/* 不支持的数组元素类型 */',
          };
        }
      });

      // 检查是否所有元素都是简单值（不包含JSExpression）
      const isSimpleArray = transformedElements.every((element) => {
        // null、undefined、基本类型都是简单的
        if (element === null || element === undefined || typeof element !== 'object') {
          return true;
        }
        // 如果是对象但不是JSExpression，也是简单的（比如纯字面量对象、数组）
        return !(element.type === 'JSExpression');
      });

      if (isSimpleArray) {
        return transformedElements;
      }
    }

    // 处理对象
    if (t.isObjectExpression(value)) {
      const obj: any = {};
      let isSimpleObject = true;

      for (const prop of value.properties) {
        if (!t.isObjectProperty(prop)) {
          isSimpleObject = false;
          break;
        }

        const key = t.isIdentifier(prop.key)
          ? prop.key.name
          : t.isStringLiteral(prop.key)
          ? prop.key.value
          : '';

        if (!key) {
          isSimpleObject = false;
          break;
        }

        // 递归处理属性值
        const propValue = this.transformStateValue(prop.value as t.Expression);
        // 如果属性值是JSExpression，说明包含复杂表达式，整个对象就不是简单对象
        if (propValue && typeof propValue === 'object' && propValue.type === 'JSExpression') {
          isSimpleObject = false;
          break;
        }
        obj[key] = propValue;
      }

      if (isSimpleObject) {
        return obj;
      }
    }

    // 对于其他所有情况，转换为 JSExpression
    return {
      type: 'JSExpression',
      value: this.transformJSExpression(value),
    };
  }

  // 修改处理 state 的代码
  private processClassProperty(node: t.ClassProperty) {
    if (t.isIdentifier(node.key) && node.key.name === 'state' && node.value) {
      if (t.isObjectExpression(node.value)) {
        const stateObj: Record<string, any> = {};
        node.value.properties.forEach((prop) => {
          if (t.isObjectProperty(prop)) {
            const key = t.isIdentifier(prop.key)
              ? prop.key.name
              : t.isStringLiteral(prop.key)
              ? prop.key.value
              : '';

            if (key) {
              stateObj[key] = this.transformStateValue(prop.value as t.Expression);
            }
          }
        });
        this.componentState = stateObj;
      }
    }
  }

  // 获取节点的下一个兄弟节点
  private getNextSibling(node: any): any {
    if (!node || !node.parentPath) return null;
    const siblings = node.parentPath.node.children;
    const currentIndex = siblings.indexOf(node);
    return currentIndex < siblings.length - 1 ? siblings[currentIndex + 1] : null;
  }

  // 标记节点为已处理
  private markNodeAsProcessed(node: any): void {
    if (!node) return;
    node._processed = true;
  }

  // 检查节点是否已被处理
  private isNodeProcessed(node: any): boolean {
    return node && node._processed === true;
  }

  // 新增：获取所有用到的组件名
  getUsedComponentNames(): string[] {
    return Array.from(this.usedComponentNames);
  }

  // 添加处理普通函数表达式的方法
  private transformFunctionExpressionToString(expr: t.FunctionExpression): string {
    console.log('将普通函数表达式转换为字符串:', expr);

    const isAsync = expr.async ? 'async ' : '';
    try {
      // 处理函数参数
      const params = expr.params
        .map((param) => {
          try {
            return generate(param).code;
          } catch (e) {
            console.error('生成参数代码失败:', e);
            if (t.isIdentifier(param)) {
              return param.name;
            }
            return '...'; // 对于复杂参数模式，简化处理
          }
        })
        .join(', ');

      // 提取函数参数名列表
      const paramNames = expr.params
        .map((param) => {
          if (t.isIdentifier(param)) {
            return param.name;
          }
          return null;
        })
        .filter(Boolean) as string[];

      // 处理函数体
      let bodyCode = '';
      try {
        bodyCode = generate(expr.body).code;
        console.log('生成的函数体代码块:', bodyCode);
      } catch (e) {
        console.error('生成函数体代码块失败:', e);
        // 失败时回退到原来的处理方式
        bodyCode = this.processBlockStatement(expr.body);
      }

      const validatedCode = this.transformJSFunctionAddThis(bodyCode, paramNames);

      // 组合成完整的函数
      return `${isAsync}function(${params}) ${validatedCode}`;
    } catch (error) {
      console.error('使用generate生成函数代码失败:', error);

      // 失败时回退到原来的处理方式
      const params = expr.params
        .map((param) => {
          if (t.isIdentifier(param)) {
            return param.name;
          }
          return '...'; // 对于复杂参数模式，简化处理
        })
        .join(', ');

      // 提取函数参数名列表
      const paramNames = expr.params
        .map((param) => {
          if (t.isIdentifier(param)) {
            return param.name;
          }
          return null;
        })
        .filter(Boolean) as string[];

      let bodyCode = this.processBlockStatement(expr.body);

      // 检查函数体中是否包含循环变量或作用域变量
      const hasLoopVars = this.currentLoopArgs.some((arg) => bodyCode.includes(arg));
      const hasSlotParams = this.currentSlotParams.some((param) => bodyCode.includes(param));

      if (hasLoopVars || hasSlotParams) {
        bodyCode = this.transformJSFunctionAddThis(bodyCode, paramNames);
      }

      return `${isAsync}function(${params}) ${bodyCode}`;
    }
  }
}

export const codeToSchema = new CodeToSchemaTransformer();
