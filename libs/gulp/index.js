var gulp = require('gulp');
var ncp = require('ncp').ncp;
var mkdirp = require('mkdirp');

gulp.task('default', function (cb) {
  ncp('../../dist', '../../../../mengti/nest-server/public/static/app');
  // ncp('../dist/index.html', '../../server/view/worktop_index.html');
  cb();
});

gulp.task('copy-runner', function (cb) {
  ncp('../../dist', '../../../../mengti/nest-server/public/static/runner');
  // ncp('../dist/index.html', '../../server/view/worktop_index.html');
  cb();
});

gulp.task('copy-local-runner', function (cb) {
  ncp('../../dist', '../../public/runner');
  cb();
});
