# 使用 LowcodeRender 实现组件使用引导

想法：

## AutoList

- 如在`AutoList`组件设置区顶部，增加`设置引导`按钮，点击右侧弹出 LowcodeRender
- 自定义组件内，通过 props 传入 resourceTreeModal 和 lowcodeEngine.project 以及 pageStore
- 让用户选择数据源字段
- 选择接口中的分页字段
- 生成 JSFunction 传给 fetchData

## 生成动作

- 选择内置行为
  - 跳转页面
    - 选择页面
    - 传递数据
      - ArraySetter
    - JSFunction
      - value: 'function(){}'
      - form: { pageName, params: [] }
  - 设置数据
  - 打开弹窗
  - 触发动画
