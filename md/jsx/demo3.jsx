import { Input, Form, Select, Input, DatePicker, Button, Upload, Form, Typography, Icon } from '@appthen/antd';
import { View, AtIcon, Text, Page } from '@appthen/react';
import { ProModal, ProPopconfirm, ProTable } from '@appthen/antd-pro';
import './unit.css';
    
/*
 * 数据与接口请求定义
 */
class DataSource {

  @dataSource({
    uri: this.constants.api,
    contentType: "JSON",
    method: "POST",
    headers: {
      router: "service/component/auth/addComponent",
      __enable: ["router"]
    }
  })
  add?: string = "";
  form?: any = {
    status: "1"
  };

  @dataSource({
    uri: this.constants.api,
    contentType: "JSON",
    method: "POST",
    isCors: true,
    headers: {
      router: "service/component/auth/getComponentList",
      __enable: ["router"]
    },
    dataHandler: res => {
      let data = res.data?.data?.data;
      let arr = [];
      for (let i = 0; i < data?.length; i += 2) {
        arr.push(data.slice(i, i + 2));
      }
      return arr;
    }
  })
  data?: string = "";
  /* 编辑id */
  _id?: string = "";

  @dataSource({
    uri: this.constants.api,
    contentType: "JSON",
    method: "POST",
    isCors: true,
    headers: {
      router: "service/component/auth/updateComponent",
      __enable: ["router"]
    }
  })
  edit?: string = "";

  @dataSource({
    uri: this.constants.api,
    contentType: "JSON",
    method: "POST",
    isCors: true,
    headers: {
      router: "service/component/auth/deleteComponent",
      __enable: ["router"]
    }
  })
  delete?: string = "";
}

class Document extends React.Component {
  state = { form: { status: '1' } };

  /*
   * 提交表单
   */
  async submitForm(event) {
    // 点击按钮时的回调
    this.$('form_hcej')
      ?.validateFields()
      .then(async (values) => {
        console.log('onClick', this.state.form, values);
        const add = await this.dataSourceMap[
          this.state._id ? 'edit' : 'add'
        ]?.load({
          ...this.state.form,
          ...values,
          _id: this.state._id,
        });
        if (add.code == 100) {
          this.setState({
            form: {},
          });
          this.$('pro_modal_p4zu46qx2dr')?.hide();
          this.$('pro_table_5obcg8p0ugu')?.reload();
          this.utils.messages()?.success(add.msg);
        }
      });
  }

  /*
   * 验证表单
   */
  verificationForm(values) {
    // 提交表单且数据验证成功后回调事件
    console.log('onFinish', this.state.form);
  }

  /*
   * 通用修改数据
   */
  setStateValue(e, { field, valueField, indexs }, cb) {
    const state = { ...this.state };
    let value = e;
    if (valueField) {
      value = valueField.split('.').reduce((obj, key) => obj && obj[key], e);
    }
    const _field =
      indexs?.length > 0
        ? field
            .replace(/\.\[\]/g, (match) => `[${indexs.shift()}].`)
            .replace('.[item]', '')
        : field;
    this.utils.setValue(state, _field, value);
    this.setState(state, cb);
  }

  /*
   * 打开弹窗
   */
  popup(event, { item } = {}) {
    this.setState(
      {
        form: {},
        _id: '',
      },
      () => {
        // 点击按钮时的回调
        if (item) {
          this.setState(
            {
              _id: item?._id,
              form: {
                ...item,
              },
            },
            () => {
              console.log(9999, this.state.form);
              this.$('pro_modal_p4zu46qx2dr')?.show();
            }
          );
        } else {
          this.$('pro_modal_p4zu46qx2dr')?.show();
        }
      }
    );
  }

  /*
   * 删除项目
   */
  async deleteItem({ _id }) {
    // 点击确认的回调
    console.log('onConfirm');

    const deleteres = await this.dataSourceMap['delete']?.load({
      _id,
    });

    if (deleteres.code == 100) {
      this.$('modal')?.close();
      this.utils.messages()?.success(deleteres?.msg);
      this.$('pro_table_5obcg8p0ugu')?.reload();
    }
  }

  /*
   * 上传前
   */

  render() {
    return (
      <Page>
        <ProModal
          title="Component"
          width={760}
          operations={[
            { action: 'cancel', type: 'normal', content: 'Cancel' },
            { action: 'submit', type: 'primary', content: 'Confirm' },
          ]}
          mask={true}
          centered={false}
          closable={true}
          confirmLoading={false}
          destroyOnClose={true}
          forceRender={false}
          keyboard={true}
          maskClosable={true}
          onOk={() => this.submitForm({})}
        >
          <View>
            <Form
              labelCol={{ span: 7 }}
              wrapperCol={{ span: 14 }}
              onValuesChange={(changedValues, allValues) =>
                this.setStateValue(
                  { ...this.state.form, ...changedValues },
                  {
                    field: 'form',
                  }
                )
              }
              onFinish={() => {
                const self = this;
                try {
                  return function onFinish(values) {
                    console.log('onFinish', values);
                  }.apply(self, arguments);
                } catch (e) {
                  console.warn(
                    'call function which parsed by lowcode failed: ',
                    e
                  );
                  return e.message;
                }
              }}
              onFinishFailed={() => {
                const self = this;
                try {
                  return function onFinishFailed({
                    values,
                    errorFields,
                    outOfDate,
                  }) {
                    console.log(
                      'onFinishFailed',
                      values,
                      errorFields,
                      outOfDate
                    );
                  }.apply(self, arguments);
                } catch (e) {
                  console.warn(
                    'call function which parsed by lowcode failed: ',
                    e
                  );
                  return e.message;
                }
              }}
              name="basic"
              colon={true}
              hideRequiredMark={false}
              preserve={true}
              scrollToFirstError={true}
              validateMessages={{ required: "'${name}' 不能为空" }}
              values={this.state.form}
            >
              <Form.Item
                label="Red Number"
                labelAlign="right"
                colon={true}
                required={true}
                noStyle={false}
                valuePropName="value"
                name="red_number"
                requiredobj={{ required: true, message: 'please enter' }}
                typeobj={{ message: '' }}
                lenobj={{ max: 0, min: 0, message: '' }}
                patternobj={{ pattern: '', message: '' }}
              >
                <Input
                  placeholder="please enter"
                  bordered={true}
                  disabled={false}
                />
              </Form.Item>
              <Form.Item
                label="Name"
                labelAlign="right"
                colon={true}
                required={true}
                noStyle={false}
                valuePropName="value"
                name="name"
                requiredobj={{ required: true, message: 'please enter' }}
                typeobj={{ message: '' }}
                lenobj={{ max: 0, min: 0, message: '' }}
                patternobj={{ pattern: '', message: '' }}
              >
                <Input
                  placeholder="please enter"
                  bordered={true}
                  disabled={false}
                />
              </Form.Item>
              <Form.Item
                label="Serial Number"
                labelAlign="right"
                colon={true}
                required={true}
                noStyle={false}
                valuePropName="value"
                name="serial_number"
                requiredobj={{ required: true, message: 'please enter' }}
                typeobj={{ message: '' }}
                lenobj={{ max: 0, min: 0, message: '' }}
                patternobj={{ pattern: '', message: '' }}
              >
                <Input
                  placeholder="please enter"
                  bordered={true}
                  disabled={false}
                />
              </Form.Item>
              <Form.Item
                label="Type"
                labelAlign="right"
                colon={true}
                required={false}
                noStyle={false}
                valuePropName="value"
                name="type"
                requiredobj={{ required: false, message: 'please enter' }}
                typeobj={{ message: '' }}
                lenobj={{ max: 0, min: 0, message: '' }}
                patternobj={{ pattern: '', message: '' }}
              >
                <Input
                  placeholder="please enter"
                  bordered={true}
                  disabled={false}
                />
              </Form.Item>
              <Form.Item
                label="Status"
                labelAlign="right"
                colon={true}
                required={false}
                noStyle={false}
                valuePropName="value"
                name="use_status"
                requiredobj={{ required: false, message: 'please enter' }}
                typeobj={{ message: '' }}
                lenobj={{ max: 0, min: 0, message: '' }}
                patternobj={{ pattern: '', message: '' }}
              >
                <Select
                  options={[
                    {
                      label: 'Stored before Test',
                      value: 'Stored before Test',
                      _unsafe_MixedSetter_value_select: 'StringSetter',
                    },
                    {
                      label: 'In Test',
                      value: 'In Test',
                      _unsafe_MixedSetter_value_select: 'StringSetter',
                    },
                    {
                      label: 'Stored after Test',
                      value: 'Stored after Test',
                      _unsafe_MixedSetter_value_select: 'StringSetter',
                    },
                    {
                      label: 'Destroyed',
                      value: 'Destroyed',
                      disabled: false,
                      _unsafe_MixedSetter_value_select: 'StringSetter',
                    },
                  ]}
                  allowClear={false}
                  autoFocus={false}
                  defaultActiveFirstOption={true}
                  disabled={false}
                  labelInValue={false}
                  showSearch={false}
                  loading={false}
                  bordered={true}
                  optionFilterProp="value"
                  tokenSeparators={[]}
                  maxTagCount={0}
                  maxTagTextLength={0}
                  className="w-full"
                />
              </Form.Item>
              <Form.Item
                label="Comments"
                labelAlign="right"
                colon={true}
                required={false}
                noStyle={false}
                valuePropName="value"
                name="comments"
                requiredobj={{ required: false, message: 'please enter' }}
                typeobj={{ message: '' }}
                lenobj={{ max: 0, min: 0, message: '' }}
                patternobj={{ pattern: '', message: '' }}
              >
                <Input.TextArea
                  autoSize={{ minRows: 3, maxRows: 3 }}
                  placeholder="please enter"
                  bordered={true}
                  disabled={false}
                  showCount={false}
                />
              </Form.Item>
              <Form.Item
                label="Install Date"
                labelAlign="right"
                colon={true}
                required={false}
                noStyle={false}
                valuePropName="value"
                name="xxx"
                requiredobj={{ required: false, message: 'please enter' }}
                typeobj={{ message: '' }}
                lenobj={{ max: 0, min: 0, message: '' }}
                patternobj={{ pattern: '', message: '' }}
              >
                <View>
                  <DatePicker
                    value={this.state.form?.install_date}
                    picker="date"
                    format="YYYY-MM-DD HH:mm:00"
                    allowClear={true}
                    bordered={true}
                    showToday={true}
                    autoFocus={false}
                    disabled={false}
                    inputReadOnly={false}
                    showTime={true}
                    onChange={(date, dateString) => {
                      setTimeout(() => {
                        this.setStateValue(date.valueOf(), {
                          field: 'form.install_date',
                        });
                      }, 200);
                    }}
                  />
                </View>
              </Form.Item>
              <Form.Item
                label="Logo"
                labelAlign="right"
                colon={true}
                required={false}
                noStyle={false}
                valuePropName="value"
                name="logo"
                requiredobj={{ required: false, message: 'please enter' }}
                typeobj={{ message: '' }}
                lenobj={{ max: 0, min: 0, message: '' }}
                patternobj={{ pattern: '', message: '' }}
              >
                <View>
                  <Upload
                    multiple={false}
                    maxCount={1}
                    directory={false}
                    disabled={false}
                    openFileDialogOnClick={true}
                    showUploadList={{
                      showPreviewIcon: false,
                    }}
                    listType="picture-card"
                    name="files"
                    method="post"
                    withCredentials={false}
                    _unsafe_MixedSetter_action_select="VariableSetter"
                    accept="jpg,jpeg,png"
                    action={this.constants.api}
                    headers={{
                      Authorization:
                        'Bearer ' + window.localStorage.getItem('accessToken'),
                      router: 'service/common/auth/uploadFile',
                    }}
                    _unsafe_extendAttr={[
                      {
                        key: 'showUploadList',
                        value: {
                          showPreviewIcon: false,
                        },
                      },
                    ]}
                    onChange={(info) => {
                      const { status } = info.file;
                      if (status === 'done') {
                        const response = info.file.response;
                        console.log('response: ', response);
                        if (
                          response &&
                          response.code === 200 &&
                          response.data?.fileUrl
                        ) {
                          this.setState({
                            form: {
                              ...this.state.form,
                              logo: response.data?.fileUrl,
                            },
                          });
                        } else {
                          this.utils.message.error('Upload failed.');
                        }
                      } else if (status === 'error') {
                        this.utils.message.error('Upload failed.');
                      }
                    }}
                    _unsafe_MixedSetter_fileList_select="ExpressionSetter"
                    defaultFileList={
                      !!this.state.form?.logo
                        ? [
                            {
                              uid: '-1',
                              name: 'image.png',
                              status: 'done',
                              url: this.state.form?.logo?.replace(
                                'http:',
                                'https:'
                              ),
                            },
                          ]
                        : undefined
                    }
                    fileList={undefined}
                    _unsafe_MixedSetter_defaultFileList_select="ExpressionSetter"
                    onRemove={(file) => {
                      this.setState({
                        form: {
                          ...this.state.form,
                          logo: '',
                        },
                      });
                    }}
                    className=""
                  >
                    <Button
                      children={
                        <View>
                          <AtIcon
                            color="#999999"
                            size={30}
                            svg="&#60;svg t=&#34;1712487996399&#34; class=&#34;icon&#34; viewBox=&#34;0 0 1024 1024&#34; version=&#34;1.1&#34; xmlns=&#34;http://www.w3.org/2000/svg&#34; p-id=&#34;20022&#34; width=&#34;100%&#34; height=&#34;100%&#34;&#62;&#60;path d=&#34;M927.07 469.31H554.66V96.94h-85.34v372.37H96.93v85.34h372.39v372.41h85.34V554.65h372.41z&#34; fill=&#34;currentColor&#34; p-id=&#34;20023&#34;&#62;&#60;/path&#62;&#60;/svg&#62;"
                          />
                        </View>
                      }
                      htmlType="button"
                      type="link"
                      size="large"
                      shape="default"
                      block={false}
                      danger={false}
                      ghost={true}
                      disabled={false}
                      _unsafe_MixedSetter_children_select="SlotSetter"
                      onClick={(event) => {
                        console.log(1111, event);
                      }}
                      className=""
                    />
                  </Upload>
                </View>
              </Form.Item>
              {!!false && (
                <Form.Item
                  label="Sensors"
                  labelAlign="right"
                  colon={true}
                  required={true}
                  noStyle={false}
                  valuePropName="value"
                  name="h_b_m_sensors"
                  requiredobj={{ required: true, message: '必填' }}
                  typeobj={{ message: '' }}
                  lenobj={{ max: 0, min: 0, message: '' }}
                  patternobj={{ pattern: '', message: '' }}
                >
                  <Select
                    options={[
                      { label: 'A', value: 'A' },
                      { label: 'B', value: 'B' },
                      { label: 'C', value: 'C' },
                    ]}
                    allowClear={false}
                    autoFocus={false}
                    defaultActiveFirstOption={true}
                    disabled={false}
                    labelInValue={false}
                    showSearch={false}
                    loading={false}
                    bordered={true}
                    optionFilterProp="value"
                    tokenSeparators={[]}
                    maxTagCount={0}
                    maxTagTextLength={0}
                    placeholder="please enter"
                    className="w-full"
                  />
                </Form.Item>
              )}
            </Form>
          </View>
        </ProModal>
        <View className="bg-[#efefef]">
          <View className="flex flex-row items-center pt-[10px] pb-[10px] pl-[34px] pr-[20px]">
            <View className="flex-1">
              <Typography.Text
                children="Component"
                code={false}
                delete={false}
                disabled={false}
                mark={false}
                keyboard={false}
                underline={false}
                strong={true}
                className="text-base text-[#333333]"
              />
            </View>
          </View>
          <View className="pb-[20px] pl-[20px] pr-[20px]">
            <ProTable
              cardBordered={true}
              columns={[
                {
                  title: 'Search',
                  dataIndex: 'keyword',
                  valueType: 'text',
                  align: 'left',
                  fixed: '',
                  hideInTable: true,
                },
                {
                  title: 'Red Number',
                  dataIndex: 'red_number',
                  valueType: 'text',
                  align: 'left',
                  fixed: '',
                  hideInSearch: true,
                  sorter: true,
                },
                {
                  title: 'Project',
                  align: 'left',
                  fixed: '',
                  render: (text, record, index) => (
                    <View>
                      <View
                        onClick={(e) => {
                          if (record?.projectInfo?._id) {
                            this.history.push(
                              '/tenet-project-detail?project_id=' +
                                record?.projectInfo?._id
                            );
                          }
                        }}
                        className=""
                      >
                        <Text
                          children={record?.projectInfo?.name || '-'}
                          className="text-sm text-[#0056e4]"
                        />
                      </View>
                    </View>
                  ),
                  hideInSearch: true,
                },
                {
                  title: 'Type',
                  dataIndex: 'type',
                  align: 'left',
                  fixed: '',
                  hideInSearch: true,
                  sorter: true,
                },
                {
                  title: 'Status',
                  dataIndex: 'use_status',
                  align: 'left',
                  fixed: '',
                  sorter: true,
                },
                {
                  title: 'Name',
                  dataIndex: 'name',
                  valueType: 'text',
                  align: 'left',
                  fixed: '',
                  hideInSearch: true,
                  sorter: true,
                },
                {
                  title: 'Serial Number',
                  dataIndex: 'serial_number',
                  align: 'left',
                  fixed: '',
                  hideInSearch: true,
                  sorter: true,
                },
                {
                  title: 'Install Date',
                  dataIndex: 'install_date',
                  valueType: 'dateTime',
                  align: 'left',
                  fixed: '',
                  hideInSearch: true,
                  sorter: true,
                },
                {
                  title: 'Options',
                  dataIndex: 'options',
                  valueType: 'option',
                  align: 'left',
                  fixed: 'right',
                  render: (text, record, index) => [
                    <Button
                      type="link"
                      children="edit"
                      htmlType="button"
                      size="middle"
                      shape="default"
                      block={false}
                      danger={false}
                      ghost={false}
                      disabled={false}
                      icon=""
                      onClick={(event) =>
                        this.popup(event, {
                          item: record,
                        })
                      }
                    />,
                    <ProPopconfirm
                      title="Are you sure to delete it?"
                      okType="primary"
                      okText="confirm"
                      cancelText="cancel"
                      onConfirm={() => this.deleteItem({ _id: record?._id })}
                    >
                      <Button
                        children="delete"
                        htmlType="button"
                        type="link"
                        size="middle"
                        shape="default"
                        block={false}
                        danger={true}
                        ghost={false}
                        disabled={false}
                        icon=""
                      />
                    </ProPopconfirm>,
                  ],
                },
              ]}
              rowKey="id"
              pagination={{
                defaultPageSize: 10,
                showSizeChanger: false,
                showQuickJumper: false,
                simple: false,
                size: 'default',
              }}
              search={true}
              toolBarRender={(currentPageData) => (
                <Button
                  type="primary"
                  children="Add"
                  htmlType="button"
                  size="middle"
                  shape="default"
                  icon={
                    <Icon
                      type="PlusOutlined"
                      size={16}
                      rotate={0}
                      spin={false}
                    />
                  }
                  block={false}
                  danger={false}
                  ghost={false}
                  disabled={false}
                  onClick={(event) =>
                    this.popup(event, {
                      item: undefined,
                    })
                  }
                />
              )}
              intl="enUSIntl"
              manualRequest={false}
              showHeader={true}
              size="default"
              tableLayout=""
              scroll={{ scrollToFirstRowOnChange: true }}
              rowSelection={false}
              toolBarRenderOpen={true}
              dateFormatter="string"
              request={async (params, sort, filter) => {
                const { current, pageSize, ...where } = params;

                const data = await this.dataSourceMap['data']?.load({
                  page: current,
                  pageSize,
                  ...where,
                  sort_field: Object.keys(sort || {})[0],
                  sort_order: Object.values(sort || {})[0]
                    ? Object.values(sort || {})[0] === 'ascend'
                      ? 'asc'
                      : 'desc'
                    : undefined,
                });
                return {
                  data: data?.rows || [],
                  success: true,
                  total: data?.total,
                };
              }}
              dataSource={this.state.data?.rows}
            />
          </View>
        </View>
      </Page>
    );
  }
}
