import { Input, Button, InputNumber, Empty, Icon } from '@appthen/antd';
import { View, Text, Image, AtIcon, Component } from '@appthen/react';
import { ProModal, ProDrawer } from '@appthen/antd-pro';
import { TodoListCard, DAvatar, TodoListComponent } from '@/components';
import './ProjectTodoList.css';
    
/*
 * 数据与接口请求定义
 */
class DataSource {

  @dataSource({
    uri: this.constants.api,
    contentType: "JSON",
    method: "POST",
    isCors: true,
    dataHandler: res => {
      let data = res.data?.data?.data;
      let arr = [];
      for (let i = 0; i < data?.length; i += 2) {
        arr.push(data.slice(i, i + 2));
      }
      return arr;
    },
    headers: {
      router: "service/to_do_list/auth/getToDoList"
    },
    params: {
      project_id: this.props.project_id,
      page: 1,
      pageSize: 100
    },
    isInit: true
  })
  todo?: Partial<To_do_list表>[] = [];

  @dataSource({
    uri: this.constants.api,
    contentType: "JSON",
    method: "POST",
    isCors: true,
    headers: {
      router: "service/to_do_list/auth/addLogs"
    }
  })
  addremark?: string = "";

  @dataSource({
    uri: this.constants.api,
    params: {
      project_id: this.props?.project_id || '65fb3a6914320b5e0b4743ae'
    },
    contentType: "JSON",
    method: "POST",
    isCors: true,
    headers: {
      router: "service/to_do_list/auth/getLogsList"
    },
    dataHandler: res => {
      let data = res.data?.data?.data;
      let arr = [];
      for (let i = 0; i < data?.length; i += 2) {
        arr.push(data.slice(i, i + 2));
      }
      return arr;
    },
    isInit: true
  })
  getallremark?: string = "";
  current_todo_id?: string = "";
  inputValue?: string = "";
  project?: Partial<Project表> = {};

  @dataSource({
    uri: this.constants.api,
    contentType: "JSON",
    method: "POST",
    isCors: true,
    headers: {
      router: "service/project/auth/getProjectInfo"
    },
    dataHandler: res => {
      let data = res.data?.data?.data;
      let arr = [];
      for (let i = 0; i < data?.length; i += 2) {
        arr.push(data.slice(i, i + 2));
      }
      return arr;
    },
    params: {
      _id: "65fb3a6914320b5e0b4743ae"
    }
  })
  getProject?: any = {};

  @dataSource({
    uri: this.constants.api,
    contentType: "JSON",
    method: "POST",
    isCors: true,
    headers: {
      router: "service/to_do_list/auth/finishToDo"
    }
  })
  finishToDo?: string = "";
  isAck?: boolean = false;

  @dataSource({
    uri: this.constants.api,
    contentType: "JSON",
    method: "POST",
    isCors: true,
    headers: {
      router: "service/to_do_list/auth/acknowledgeToDo",
      __enable: ["router"]
    },
    params: {
      parent_id: this.state.current_todo_id,
      __enable: ["parent_id"]
    }
  })
  acknowledgeToDo?: any = {};
  subTab?: string = "comments";
  allLogs?: any = [];
  currentTestBench?: any = {};
  current_operating?: number = 0;
  todoMap?: any = {};
  loading?: boolean = false;
}

class Document extends React.Component {
  state = {
    todo: [],
    addremark: '',
    getallremark: '',
    current_todo_id: '',
    inputValue: '',
    project: {},
    getProject: {},
    finishToDo: '',
    isAck: false,
    acknowledgeToDo: {},
    subTab: 'comments',
    allLogs: [],
    currentTestBench: {},
    current_operating: 0,
    todoMap: {},
    loading: false,
  };

  /*
   * 应用加载后执行
   */
  async componentDidMount() {
    const project_id = this.props.project_id || '662db49a207dead1a3e3c8bb';
    const project =
      this.props.project ||
      (await this.dataSourceMap['getProject']?.load({
        _id: project_id,
      }));
    const project_model_tree =
      typeof project.project_model_tree === 'string'
        ? JSON.parse(project.project_model_tree)
        : project.project_model_tree;
    let currentTestBench;
    if (project_model_tree) {
      const testBenchCell = project_model_tree?.cells?.find(
        (_) => _.shape === 'test-bench'
      );
      currentTestBench = testBenchCell?.data?.item;
    }
    this.setState(
      {
        project,
        currentTestBench,
      },
      async () => {
        // const todos = await this.dataSourceMap["todo"]?.load({
        //   project_id
        // });
        // console.log(123,remark,todos)
      }
    );
  }

  /*
   * 通用修改数据
   */
  setStateValue(e, { field, valueField, indexs }, cb) {
    const state = { ...this.state };
    let value = e;
    if (valueField) {
      value = valueField.split('.').reduce((obj, key) => obj && obj[key], e);
    }
    const _field =
      indexs?.length > 0
        ? field
            .replace(/\.\[\]/g, (match) => `[${indexs.shift()}].`)
            .replace('.[item]', '')
        : field;
    this.utils.setValue(state, _field, value);
    this.setState(state, cb);
  }

  /*
   * 选择todo
   */
  selectTodo(e, { item }) {
    const needChangeTab = this.state.subTab === 'logs';
    this.setState(
      {
        subTab: 'comments',
      },
      () => {
        setTimeout(
          () => {
            var contentElement = document.getElementById(item._id);
            contentElement.scrollIntoView();
            // var rightContentElement = document.getElementById('right-content');
            // var containerRect = rightContentElement.getBoundingClientRect();
            // var contentRect = contentElement.getBoundingClientRect();
            // var scrollToOffset = contentRect.top - containerRect.top + rightContentElement.scrollTop;
            // rightContentElement.scrollTo({
            //   top: scrollToOffset - 30,
            //   behavior: 'smooth'
            // });
            this.setStateValue(item._id, {
              field: 'current_todo_id',
            });
          },
          needChangeTab ? 1000 : 0
        );
      }
    );
  }

  /*
   * 确认添加信息
   */
  async confirmAdditionInformation() {
    // 点击确定回调
    if (!this.state.inputValue) return this.utils.message?.info('please enter');
    let content = this.state.inputValue;
    const todo = this.state.todo.find(
      (_) => _._id === this.state.current_todo_id
    );
    if (!todo) return this.utils.message.error('todo not exist');
    if (this.state.isAck) {
      const acknowledgeToDo = await this.dataSourceMap['acknowledgeToDo']?.load(
        {
          current_operating:
            todo.type === 'hours' && this.state.current_operating
              ? this.state.current_operating * 100
              : this.state.current_operating,
        }
      );
      console.log('acknowledgeToDo: ', acknowledgeToDo);
      if (acknowledgeToDo.code === 100) {
        const { type, operating_value } = acknowledgeToDo.data || {};
        content =
          content +
          '|||Current Running ' +
          (type === 'hours' ? 'Hour' : 'Cycle') +
          ' ' +
          parseInt(operating_value);
      } else {
        return this.utils.message.error(
          acknowledgeToDo.msg || 'Acknowledge Fail'
        );
      }
    }
    const addremark = await this.dataSourceMap['addremark']?.load({
      _id: this.state.current_todo_id,
      content,
    });
    if (addremark.code == 100) {
      this.setState({
        inputValue: '',
      });
      this.utils.message.success(addremark.msg);
      this.$('pro_modal_ce5nhf3q6jc')?.hide();
      this.refresh();
    } else {
      this.setState({
        inputValue: '',
      });
      this.utils.message?.success(addremark.msg || 'error');
    }
  }

  /*
   * 获得TODO
   */
  async getTodo(e) {
    const todos = this.state.todo;
    // 默认选择第一个
    // if (todos?.[0]?._id) {
    //   this.setStateValue(todos?.[0]?._id, {
    //     field: 'current_todo_id'
    //   })
    // }
    const remark = await this.dataSourceMap['getallremark']?.load({
      project_id: this.state.project?._id,
    });
    const allLogs = [];
    if (remark?.length > 0) {
      remark.forEach((item) => {
        if (item.logs)
          allLogs.push(
            ...(item.logs || []).map((_) => {
              return {
                ..._,
                todo_id: item._id,
              };
            })
          );
      });
    }
    this.setState({
      allLogs: allLogs.sort((a, b) => a.create_time - b.create_time),
    });
  }

  /*
   * refresh
   */
  async refresh() {
    const remark = await this.dataSourceMap['getallremark']?.load({
      project_id: this.state.project?._id,
    });
    const allLogs = [];
    if (remark?.length > 0) {
      remark.forEach((item) => {
        allLogs.push(...(item.logs || []));
      });
    }
    this.setState(
      {
        allLogs: allLogs.sort((a, b) => a.create_time - b.create_time),
      },
      () => {
        this.state.todoListRef?.refresh();
      }
    );
  }

  /*
   * 切换SubTab
   */
  toggleSubtab(e, { tab }) {
    this.setState(
      {
        subTab: tab,
      },
      () => {
        if (tab === 'logs') {
          this.state.todoListRef?.switchTab('todo');
        }
      }
    );
  }

  /*
   * Ack
   */
  async ack(event, { item1, index }) {
    // const todo = this.state.todo.find(_ => _._id === this.state.current_todo_id);
    // if (!todo) return this.utils.message.error('todo not exist');
    // const iotData = await this.utils.reloadGlobalData("iot", {
    //   identifier: this.state.currentTestBench?.identifier
    // });
    // let current_operating;
    // if (todo.type !== 'date') {
    //   const _current_operating = iotData[todo.type === 'cycles' ? 'cycle_is' : 'operating hour-is'];
    //   current_operating = todo.type === 'hours' ? _current_operating / 100 : _current_operating
    // }
    this.setState(
      {
        isAck: true,
      },
      () => {
        this.$('pro_modal_ce5nhf3q6jc')?.show();
      }
    );
  }

  /*
   * 获取Operating
   */
  async getOperating() {
    const todo = this.state.todo.find(
      (_) => _._id === this.state.current_todo_id
    );
    if (!todo) return this.utils.message.error('todo not exist');
    const iotData = await this.utils.reloadGlobalData('iot', {
      identifier: this.state.currentTestBench?.identifier,
    });
    let current_operating;
    if (todo.type !== 'date') {
      const _current_operating =
        iotData[todo.type === 'cycles' ? 'cycle_is' : 'operating hour-is'];
      current_operating =
        todo.type === 'hours' ? _current_operating / 100 : _current_operating;
    }
    this.setState({
      current_operating,
    });
  }

  render() {
    return (
      <React.Fragment className="bg-[#efefef] flex flex-col">
        <ProModal
          title={this.state.isAck ? 'Acknowledge' : 'Add Comment'}
          width={760}
          operations={[
            { action: 'cancel', type: 'normal', content: 'cancel' },
            { action: 'submit', type: 'primary', content: 'submit' },
          ]}
          mask={true}
          centered={false}
          closable={true}
          confirmLoading={false}
          destroyOnClose={false}
          forceRender={false}
          keyboard={true}
          maskClosable={true}
          onOk={() => this.confirmAdditionInformation()}
          afterClose={(e) => {
            this.setState({
              isAck: false,
            });
          }}
        >
          <View className="pt-[10px]">
            <Input.TextArea
              autoSize={{ minRows: 3, maxRows: 3 }}
              placeholder="....."
              bordered={true}
              disabled={false}
              showCount={false}
              value={this.state.inputValue}
              onChange={() =>
                this.setStateValue.apply(
                  this,
                  Array.prototype.slice.call(arguments).concat([
                    {
                      field: 'inputValue',
                      valueField: 'target.value',
                    },
                  ])
                )
              }
            />
          </View>
          {!!this.state.isAck && (
            <View
              _unsafe_MixedSetter____condition____select="VariableSetter"
              className="flex flex-row mt-[15px] items-center"
            >
              <View className="flex flex-row">
                <Text
                  children="*"
                  className="text-sm text-[#e41111] mr-[1px]"
                />
                <Text
                  children="Current Operating"
                  className="text-sm text-[#333]"
                />
              </View>
              <View className="flex-1 pl-[15px]">
                <InputNumber
                  placeholder="please enter"
                  autoFocus={false}
                  disabled={false}
                  controls={true}
                  bordered={true}
                  size="large"
                  value={this.state.current_operating}
                  onChange={(value) =>
                    this.setStateValue(value, {
                      field: 'current_operating',
                    })
                  }
                  addonAfter={
                    <View>
                      <Button
                        type="link"
                        children="Read From Iot"
                        htmlType="button"
                        size="middle"
                        shape="default"
                        block={false}
                        danger={false}
                        ghost={false}
                        disabled={false}
                        onClick={(event) => this.getOperating()}
                      />
                    </View>
                  }
                  _unsafe_MixedSetter_addonBefore_select="SlotSetter"
                  _unsafe_MixedSetter_addonAfter_select="SlotSetter"
                  className="w-[300px]"
                />
              </View>
            </View>
          )}
        </ProModal>
        <View className="flex flex-col pl-[20px] pr-[20px] h-[calc(100vh - 63px)] pb-[20px]">
          <View className="pl-[20px] pr-[20px] flex flex-row pt-[15px]">
            <View className="bg-[#fcfcfc] border border-[#ededed] border-solid rounded-[6px] pt-[12px] pb-[12px] pr-[16px] flex-1">
              <View className="flex flex-row items-center pl-[18px] pr-[18px] pt-[10px] pb-[10px]">
                <View className="w-[6px] h-[6px] bg-[#064a82] rounded-[3px]" />
                <View className="ml-[8px]">
                  <Text
                    children="Name："
                    className="text-sm text-[#555555] font-normal"
                  />
                </View>
                <View className="flex-1 flex flex-row justify-end">
                  <Text
                    children={this.state.project?.name || 'name'}
                    className="text-base text-[#333] font-bold"
                  />
                </View>
              </View>
              <View className="flex flex-row items-center pl-[18px] pr-[18px] pt-[10px] pb-[10px]">
                <View className="w-[6px] h-[6px] bg-[#064a82] rounded-[3px]" />
                <View className="ml-[8px]">
                  <Text
                    children="Approve Status："
                    className="text-sm text-[#555555] font-normal"
                  />
                </View>
                <View className="flex-1 flex flex-row justify-end">
                  <Text
                    children={
                      this.constants.auditStatusEnum[
                        this.state.project?.audit_status || '0'
                      ]
                    }
                    className="text-base text-[#333] font-bold"
                  />
                </View>
              </View>
              <View className="flex flex-row items-center pl-[18px] pr-[18px] pt-[10px] pb-[10px]">
                <View className="w-[6px] h-[6px] bg-[#064a82] rounded-[3px]" />
                <View className="ml-[8px]">
                  <Text
                    children="Work Status："
                    className="text-sm text-[#555555] font-normal"
                  />
                </View>
                <View className="flex-1 flex flex-row justify-end">
                  <Text
                    children={
                      this.constants.workStatusEnum[
                        this.state.project?.work_status || 'not_started'
                      ]
                    }
                    className="text-base text-[#333] font-bold"
                  />
                </View>
              </View>
            </View>
          </View>
          <View className="flex flex-row flex-1 overflow-hidden">
            <View className="bg-[#ffffff] w-[40%] pb-[20px] flex flex-col">
              <View className="flex flex-row pl-[20px] items-center pt-[15px]">
                <View className="pb-[8px] border-b-[3px] border-b-[#1a437d] border-b-solid">
                  <Text
                    children="Todos"
                    className="text-base text-[#064a82] font-bold"
                  />
                </View>
                <View className="flex flex-row flex-1 justify-end pr-[9px]">
                  {!!false && (
                    <View className="pt-[15px] pb-[15px] pl-[15px] pr-[15px]">
                      <Image
                        src="https://cdn.appthen.com/FpXDvWDf3ooUhFYyjcu1EBdY8Jzt"
                        remote={false}
                        fit={false}
                        className="w-[24px] h-[24px]"
                      />
                    </View>
                  )}
                  {!!false && (
                    <View className="pl-[15px] pt-[15px] pb-[15px] pr-[15px]">
                      <Image
                        src="https://cdn.appthen.com/FkEsrsBFy5mWqjbhnOaZe9kH-eVX"
                        remote={false}
                        fit={false}
                        className="w-[24px] h-[24px]"
                      />
                    </View>
                  )}
                </View>
                <View>
                  <Button
                    type="link"
                    children="manage"
                    htmlType="button"
                    size="small"
                    shape="default"
                    block={false}
                    danger={false}
                    ghost={false}
                    disabled={false}
                    onClick={(event) => {
                      this.$('pro_drawer_dbi83n0scec')?.show();
                    }}
                  />
                </View>
              </View>
              {!!!!this.state.project?._id && (
                <View
                  _unsafe_MixedSetter____condition____select="VariableSetter"
                  className="overflow-scroll-y pl-[10px] pr-[10px] pt-[7px] flex flex-col flex-1"
                  _unsafe_classNameJson={[{ selector: 'overflow-scroll-y' }]}
                >
                  <TodoListCard
                    onSelect={(item, index) =>
                      this.selectTodo(null, {
                        item,
                        index,
                      })
                    }
                    projectId={this.props?.project_id}
                    current_todo_id={this.state.current_todo_id}
                    onData={(data) => {
                      const todoMap = {};
                      data.forEach((_) => {
                        todoMap[_._id] = _;
                      });
                      this.setState(
                        {
                          todo: data,
                          loading: false,
                          todoMap,
                        },
                        () => {
                          this.getTodo();
                        }
                      );
                    }}
                    getRef={(node) => {
                      this.setState({
                        todoListRef: node,
                      });
                    }}
                    onLoading={(e) => {
                      this.setState({
                        loading: true,
                      });
                    }}
                  />
                </View>
              )}
            </View>
            <View
              _unsafe_extendAttr={[{ key: 'id', value: 'right-content' }]}
              id="right-content"
              className="bg-[#ffffff] flex flex-col ml-[20px] flex-1 pb-[20px]"
            >
              <View className="flex flex-row pt-[18px] pl-[12px] pb-[10px]">
                <View
                  inlineStyle={[
                    {
                      enable: this.state.subTab !== 'comments',
                      name: '动态样式1',
                      style: {
                        borderBottomWidth: '2px',
                        borderBottomColor: '#ffffff',
                        borderBottomStyle: 'solid',
                      },
                    },
                  ]}
                  onClick={(e) =>
                    this.toggleSubtab(e, {
                      tab: 'comments',
                    })
                  }
                  className="pb-[8px] border-b-[3px] border-b-[#1a437d] border-b-solid flex flex-col"
                >
                  <Text
                    children="Comments"
                    className="text-base text-[#064a82] font-bold"
                  />
                </View>
                <View
                  inlineStyle={[
                    {
                      enable: this.state.subTab !== 'logs',
                      name: '动态样式1',
                      style: {
                        borderBottomWidth: '2px',
                        borderBottomColor: '#ffffff',
                        borderBottomStyle: 'solid',
                      },
                    },
                  ]}
                  onClick={(e) =>
                    this.toggleSubtab(e, {
                      tab: 'logs',
                    })
                  }
                  className="pb-[8px] border-b-[3px] border-b-[#1a437d] border-b-solid flex flex-col ml-[15px]"
                >
                  <Text
                    children="Logs"
                    className="text-base text-[#064a82] font-bold"
                  />
                </View>
                <View __hidePh={true} className="flex-1" />
                <View />
              </View>
              <View
                className="overflow-scroll-y rounded-lg shadow-[inset 3px 4px 7px 0 rgba(0,0,0,0.09)] flex-1"
                _unsafe_classNameJson={[{ selector: 'overflow-scroll-y' }]}
              >
                <View>
                  {!!(this.state.subTab === 'logs' && !this.state.loading) && (
                    <View _unsafe_MixedSetter____condition____select="VariableSetter">
                      {this.state.allLogs.map((item, index) => (
                        <View
                          inlineStyle={[
                            {
                              enable: index % 2 === 0,
                              name: '动态样式1',
                              style: { backgroundColor: '#f1f1f1' },
                            },
                          ]}
                          className="flex flex-row pt-[10px] pb-[6px]"
                        >
                          <View className="pl-[10px] pr-[10px]">
                            <Text
                              children={this.utils
                                .dayjs(item?.create_time)
                                .format('YYYY-MM-DD HH:mm:ss')}
                              className="text-sm text-[#333]"
                            />
                          </View>
                          <View className="flex flex-row">
                            <View className="ml-[5px]">
                              <Text
                                children={
                                  this.state.todoMap[item?.todo_id]?.name || '-'
                                }
                                className="text-sm text-[#333] font-medium"
                              />
                            </View>
                            <View className="ml-[10px]">
                              <Text
                                children={
                                  '| ' +
                                  item?.content
                                    ?.replace('|||', ' | ')
                                    ?.replace('Current', '')
                                }
                                className="text-sm text-[#333] font-medium"
                              />
                            </View>
                          </View>
                          <View className="flex-1 flex flex-row">
                            <View className="flex flex-row ml-[10px]">
                              <View>
                                <Text
                                  children={
                                    ' | ' +
                                      this.state.todoMap[item?.todo_id]
                                        ?.componentInfo?.name || '--'
                                  }
                                  className="text-sm text-[#333] font-medium"
                                />
                              </View>
                            </View>
                            <View className="flex flex-row">
                              <View>
                                <Text
                                  children={
                                    ' | ' +
                                    (this.state.todoMap[
                                      item?.todo_id
                                    ]?.consumableMaterialList
                                      ?.map((_) => _.name)
                                      ?.join(',') || '--')
                                  }
                                  className="text-sm text-[#333] ml-[10px] font-medium"
                                />
                              </View>
                            </View>
                          </View>
                        </View>
                      ))}
                    </View>
                  )}
                </View>
                {!!(this.state.subTab === 'comments') && (
                  <View
                    _unsafe_MixedSetter____condition____select="VariableSetter"
                    className="pt-[20px]"
                  >
                    <View _unsafe_extendAttr={[]} className="pb-[20px]">
                      {this.state.todo.map((item1, index) => (
                        <View
                          _unsafe_extendAttr={[
                            { key: 'id', value: item1?._id },
                          ]}
                          id={item1?._id}
                          className="pr-[20px] flex flex-col"
                        >
                          <View
                            inlineStyle={[
                              {
                                enable:
                                  item1?._id === this.state.current_todo_id,
                                name: '动态样式1',
                                style: { backgroundColor: '#f5f5eb' },
                              },
                              {
                                enable:
                                  this.state.current_todo_id &&
                                  item1?._id !== this.state.current_todo_id,
                                name: '隐藏未选项',
                                style: { display: 'none' },
                              },
                            ]}
                            className="bg-[#ffffff] rounded-[6px] border border-[#ededed] border-solid pl-[28px] pr-[28px] pt-[20px] ml-[20px]"
                          >
                            <View className="flex flex-col mb-[10px]">
                              <Text
                                children={index + 1 + '. ' + item1?.name}
                                className="text-sm text-[#333333]"
                              />
                            </View>
                            <View className="flex flex-col mb-[10px] mt-[10px]">
                              <Text
                                children="Comments"
                                className="text-xs text-[#7d7a7a]"
                              />
                            </View>
                            {!!(item1?.logs?.length === 0) && (
                              <View
                                _unsafe_MixedSetter____condition____select="VariableSetter"
                                className="flex flex-col mb-[10px] mt-[10px] justify-center items-center h-[50px]"
                              >
                                <Text
                                  children="No Comments"
                                  className="text-xs text-[#7d7a7a]"
                                />
                              </View>
                            )}
                            {(item1?.logs).map((item, index) => (
                              <View _unsafe_MixedSetter____condition____select="VariableSetter">
                                <View className="flex flex-row">
                                  <View className="mr-[15px] mt-[6px]">
                                    <DAvatar
                                      text={item1?.requestedByInfo?.username}
                                    />
                                  </View>
                                  <View
                                    style={{
                                      paddingTop: '10px',
                                      paddingBottom: '10px',
                                      paddingLeft: '10px',
                                      paddingRight: '10px',
                                      display: 'flex',
                                      flexDirection: 'column',
                                    }}
                                    className={`${
                                      item1?._id == this.state.current_todo_id
                                        ? 'todo_chat'
                                        : ''
                                    } ${
                                      item1?._id !== this.state.current_todo_id
                                        ? 'todo1_chat'
                                        : ''
                                    }`}
                                    _unsafe_classNameJson={[
                                      {
                                        selector: 'todo_chat',
                                        condition:
                                          item1?._id ==
                                          this.state.current_todo_id,
                                      },
                                      {
                                        selector: 'todo1_chat',
                                        condition:
                                          item1?._id !==
                                          this.state.current_todo_id,
                                      },
                                    ]}
                                  >
                                    <Text
                                      children={item?.content.split('|||')[0]}
                                      className="text-sm text-[rgba(0,0,0,0.88)]"
                                    />
                                  </View>
                                </View>
                                {!!!!item?.content?.split('|||')?.[1] && (
                                  <View
                                    _unsafe_MixedSetter____condition____select="VariableSetter"
                                    className="flex flex-row items-center mt-[5px] ml-[47px]"
                                  >
                                    <View>
                                      <AtIcon
                                        color="#2448e7"
                                        size={14}
                                        svg="&#60;svg xmlns=&#34;http://www.w3.org/2000/svg&#34; width=&#34;100%&#34; height=&#34;100%&#34; viewBox=&#34;0 0 24 24&#34;&#62;&#60;path fill=&#34;currentColor&#34; d=&#34;M11 14v2q0 .425.288.713T12 17q.425 0 .713-.288T13 16v-2h2q.425 0 .713-.288T16 13q0-.425-.288-.713T15 12h-2v-2q0-.425-.288-.713T12 9q-.425 0-.713.288T11 10v2H9q-.425 0-.713.288T8 13q0 .425.288.713T9 14h2Zm1 8q-1.875 0-3.513-.713t-2.85-1.924q-1.212-1.213-1.924-2.85T3 13q0-1.875.713-3.513t1.924-2.85q1.213-1.212 2.85-1.924T12 4q1.875 0 3.513.713t2.85 1.925q1.212 1.212 1.925 2.85T21 13q0 1.875-.713 3.513t-1.924 2.85q-1.213 1.212-2.85 1.925T12 22Zm0-9ZM2.05 7.3q-.275-.275-.275-.7t.275-.7L4.9 3.05q.275-.275.7-.275t.7.275q.275.275.275.7t-.275.7L3.45 7.3q-.275.275-.7.275t-.7-.275Zm19.9 0q-.275.275-.7.275t-.7-.275L17.7 4.45q-.275-.275-.275-.7t.275-.7q.275-.275.7-.275t.7.275l2.85 2.85q.275.275.275.7t-.275.7ZM12 20q2.925 0 4.963-2.038T19 13q0-2.925-2.038-4.963T12 6Q9.075 6 7.037 8.038T5 13q0 2.925 2.038 4.963T12 20Z&#34;/&#62;&#60;/svg&#62;"
                                      />
                                    </View>
                                    <Text
                                      children={item?.content.split('|||')[1]}
                                      className="text-xs text-[#0a49b1] ml-[4px]"
                                    />
                                  </View>
                                )}
                                <View className="flex flex-col pl-[100px] mt-[10px] mb-[10px]">
                                  <Text
                                    children={this.utils
                                      .moment(item?.create_time)
                                      .format('YYYY-MM-DD HH:mm:ss')}
                                    className="text-xs text-[#c1c1c1] font-normal"
                                  />
                                </View>
                              </View>
                            ))}
                            {!!(item1?._id == this.state.current_todo_id) && (
                              <View
                                _unsafe_MixedSetter____condition____select="VariableSetter"
                                className="flex flex-row justify-end pb-[15px]"
                              >
                                {!!(item1?.finish_status === 'progress') && (
                                  <View _unsafe_MixedSetter____condition____select="VariableSetter">
                                    <Button
                                      type="dashed"
                                      children="+ Comments"
                                      htmlType="submit"
                                      size="middle"
                                      shape="default"
                                      block={false}
                                      danger={false}
                                      ghost={false}
                                      loading={false}
                                      disabled={false}
                                      onClick={(event) => {
                                        this.$('pro_modal_ce5nhf3q6jc')?.show();
                                      }}
                                      _unsafe_MixedSetter____condition____select="VariableSetter"
                                      className="text-[#f5a623]"
                                    />
                                  </View>
                                )}
                                <View>
                                  {!!(item1?.finish_status === 'progress') && (
                                    <Button
                                      type="primary"
                                      children="Acknowledge"
                                      htmlType="submit"
                                      size="middle"
                                      shape="default"
                                      block={false}
                                      danger={false}
                                      ghost={false}
                                      loading={false}
                                      disabled={false}
                                      onClick={(event) =>
                                        this.ack(event, {
                                          item1: item1,
                                          index: index,
                                        })
                                      }
                                      _unsafe_MixedSetter____condition____select="VariableSetter"
                                      className="inline ml-[15px]"
                                    />
                                  )}
                                </View>
                                <View className="ml-[15px]">
                                  {!!(item1?.finish_status === 'progress') && (
                                    <Button
                                      type="primary"
                                      children="Complete"
                                      htmlType="button"
                                      size="middle"
                                      shape="default"
                                      block={false}
                                      danger={false}
                                      ghost={false}
                                      disabled={false}
                                      onClick={async (event) => {
                                        this.utils.Modal.confirm({
                                          title:
                                            'Please confirm it is indeed completed? Warning: This task is completed in its entirety, no further tasks will be prompted for the next cycle!',
                                          onOk: async () => {
                                            const project_id =
                                              this.props.project_id ||
                                              '65fb3a6914320b5e0b4743ae';
                                            const finishToDo =
                                              await this.dataSourceMap[
                                                'finishToDo'
                                              ]?.load({
                                                parent_id: item1._id,
                                              });
                                            if (finishToDo.code === 100) {
                                              this.refresh();
                                              this.utils.message.success(
                                                finishToDo.msg ||
                                                  'complate success'
                                              );
                                            } else {
                                              this.utils.message.success(
                                                finishToDo.msg ||
                                                  'complate fail'
                                              );
                                            }
                                          },
                                        });
                                      }}
                                      _unsafe_MixedSetter____condition____select="VariableSetter"
                                    />
                                  )}
                                </View>
                              </View>
                            )}
                          </View>
                          {!!(
                            !this.state.current_todo_id &&
                            index !== this.state.todo?.length - 1
                          ) && (
                            <View
                              _unsafe_MixedSetter____condition____select="VariableSetter"
                              className="pl-[36px] ml-[28px]"
                            >
                              <View className="w-[2px] bg-[#d8d8d8] h-[30px]" />
                            </View>
                          )}
                        </View>
                      ))}
                    </View>
                    {!!!this.state.getallremark?.length && (
                      <View
                        _unsafe_MixedSetter____condition____select="VariableSetter"
                        className="flex-1"
                      >
                        <Empty description="..." />
                      </View>
                    )}
                  </View>
                )}
              </View>
            </View>
          </View>
        </View>
        <ProDrawer
          title="Todo Management"
          placement="right"
          destroyOnClose={true}
          width="1200"
          operations={[]}
          mask={true}
          maskClosable={true}
          autoFocus={true}
          keyboard={true}
          closable={true}
          forceRender={false}
          closeIcon={<Icon type="CloseOutlined" size={16} />}
          afterVisibleChange={(visible) => {
            if (!visible) {
              this.refresh();
            }
          }}
        >
          <TodoListComponent
            project_id={this.props?.project_id}
            project={this.props?.project}
            iot={this.state.currentTestBench}
          />
        </ProDrawer>
      </React.Fragment>
    );
  }
}
