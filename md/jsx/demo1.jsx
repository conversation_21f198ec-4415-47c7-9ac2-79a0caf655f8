import { Text, View, AtIcon, Modal, Image, Page } from '@appthen/react';
import { Descriptions, DatePicker, Button } from '@appthen/antd';
import { TrainInfoDetail, ScreenHeaderBar, TrendAreaChart, FaultWarningSortingDiagram, LineScreen, ListOfTrains, AlarmList } from '@/components';
import './dashboardTwo.css';
    
/*
 * 数据与接口请求定义
 */
class DataSource {
  /* normal|warning|danger */
  tag?: string = "normal";
  /* 当前车辆 */
  currentTrain: any = {};
  showTrainDetail?: boolean = false;

  @dataSource({
    uri: this.utils.getGlobalData('BaseURL') + '/train/list' + `?pageNum=1&pageSize=10`,
    contentType: "JSON",
    method: "GET",
    debugState: {
      data: ""
    },
    params: {
      __enable: []
    },
    query: {
      pageNum: 1,
      pageSize: 10
    },
    isCors: true,
    dataHandler: res => {
      let data = res.data?.data?.data;
      let arr = [];
      for (let i = 0; i < data?.length; i += 2) {
        arr.push(data.slice(i, i + 2));
      }
      return arr;
    },
    isInit: false
  })
  trains?: any = [];
  statusMap?: any = {
    trains: {
      total: 0,
      normal: 0,
      warning: 0,
      error: 0
    },
    carriages: {
      total: 0,
      normal: 0,
      warning: 0,
      error: 0
    },
    doors: {
      total: 0,
      normal: 0,
      warning: 0,
      error: 0
    }
  };
  currentAlert: any = {
    createBy: "",
    createTime: "2025-01-03 07:22:21",
    updateBy: "",
    updateTime: null,
    remark: null,
    id: 16691,
    trainId: "TRAIN_001",
    carriageId: null,
    doorId: "4",
    troubleCode: "DT2002",
    troubleLevel: 2,
    description: "关门过程中检测到障碍物",
    occurTime: "2025-01-03T07:22:21.000+00:00",
    resolveTime: null,
    resolved: false,
    additionalInfo: null,
    motorCurrent: 0,
    doorPosition: 0,
    motorSpeed: 0,
    operationCount: null,
    delFlag: "0"
  };
  startTime: string = "";
  endTime: string = "";
}

class Document extends React.Component {
  state = {
    tag: 'normal',
    showTrainDetail: false,
    trains: [],
    statusMap: {
      trains: { total: 0, normal: 0, warning: 0, error: 0 },
      carriages: { total: 0, normal: 0, warning: 0, error: 0 },
      doors: { total: 0, normal: 0, warning: 0, error: 0 },
    },
    currentAlert: {
      createBy: '',
      createTime: '2025-01-03 07:22:21',
      updateBy: '',
      id: 16691,
      trainId: 'TRAIN_001',
      doorId: '4',
      troubleCode: 'DT2002',
      troubleLevel: 2,
      description: '关门过程中检测到障碍物',
      occurTime: '2025-01-03T07:22:21.000+00:00',
      resolved: false,
      motorCurrent: 0,
      doorPosition: 0,
      motorSpeed: 0,
      delFlag: '0',
    },
  };

  /*
   * 渲染故障图表
   */
  renderFaultChart() {
    const echarts = this.utils.echarts;
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
      },
      grid: {
        right: '20%',
        left: '10%',
        bottom: '10%',
      },
      legend: {
        data: ['故障'],
        right: 30,
        top: 10,
        icon: 'circle',
        itemWidth: 10,
        itemHeight: 10,
        textStyle: {
          fontSize: 12,
          color: '#fff',
        },
      },
      xAxis: {
        type: 'category',
        axisLine: { show: false },
        axisTick: { show: false },
        data: ['906', '907', '909', '908'],
        textStyle: {
          fontSize: 12,
          color: '#fff',
        },
      },
      yAxis: {
        min: 0,
        max: 10,
        interval: 2,
        textStyle: {
          fontSize: 12,
          color: '#fff',
        },
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: { show: false },
      },
      series: [
        {
          name: '故障',
          type: 'bar',
          data: [4, 2, 6, 4],
          itemStyle: {
            color: '#EB3D3D',
          },
          barWidth: '10%',
        },
      ],
    };

    return option;
  }

  /*
   * 渲染预警图表
   */
  renderAlertChart() {
    const echarts = this.utils.echarts;
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '2%',
        right: '4%',
        bottom: '15%',
        top: '26%',
        containLabel: true,
      },
      legend: {
        data: ['故障', '预警'],
        right: 20,
        top: 20,
        textStyle: {
          color: '#fff',
        },
        itemWidth: 12,
        itemHeight: 10,
        // itemGap: 35
      },
      xAxis: {
        type: 'category',
        data: ['601', '602', '603', '604', '605', '606', '607', '608'],
        axisLine: {
          lineStyle: {
            color: 'white',
          },
        },
        axisLabel: {
          // interval: 0,
          // rotate: 40,
          textStyle: {
            fontFamily: 'Microsoft YaHei',
          },
        },
      },

      yAxis: {
        type: 'value',
        max: '20',
        axisLine: {
          show: false,
          lineStyle: {
            color: 'white',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255,255,255,0.3)',
          },
        },
        axisLabel: {},
      },
      dataZoom: [
        {
          show: true,
          height: 12,
          xAxisIndex: [0],
          bottom: '8%',
          start: 10,
          end: 90,
          handleIcon:
            'path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z',
          handleSize: '110%',
          handleStyle: {
            color: '#d3dee5',
          },
          textStyle: {
            color: '#fff',
          },
          borderColor: '#90979c',
        },
        {
          type: 'inside',
          show: true,
          height: 15,
          start: 1,
          end: 35,
        },
      ],
      series: [
        {
          name: '故障',
          type: 'bar',
          barWidth: '15%',
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: '#fccb05',
                },
                {
                  offset: 1,
                  color: '#f5804d',
                },
              ]),
              barBorderRadius: 12,
            },
          },
          data: [1, 10, 2, 3, 4, 5, 6, 7],
        },
        {
          name: '预警',
          type: 'bar',
          barWidth: '15%',
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: '#F56811',
                },
                {
                  offset: 1,
                  color: '#FF0000',
                },
              ]),
              barBorderRadius: 11,
            },
          },
          data: [20, 10, 12, 3, 4, 15, 6, 17],
        },
      ],
    };

    return option;
  }

  /*
   * 动态计算
   */
  dynamicComputation() {
    // 设计图的宽度
    var designWidth = 1920;
    // 希望在设计图宽度上1rem等于多少像素
    var remBase = 100;

    // 获取当前视口的宽度
    var viewportWidth = document.documentElement.clientWidth;

    // 计算html的font-size
    var rem = (viewportWidth / designWidth) * remBase;

    // 设置html的font-size
    document.documentElement.style.fontSize = rem + 'px';
  }

  /*
   * 页面加载后执行
   */
  async componentDidMount() {
    // 在页面加载时执行一次
    this.dynamicComputation();

    // 也可以在窗口大小变化时重新计算
    window.onresize = () => {
      this.dynamicComputation();
    };

    this.eid = this.utils.event.$on('TroubleChange', () => {
      setTimeout(() => {
        this.updateTrainStatus();
      }, 500);
    });

    const trains = await this.utils.reloadGlobalData('trains');
    // 初始化 WS
    this.utils.wsController();
    this.updateTrainStatus();
  }

  /*
   * 点击日志
   */

  /*
   * 点击车辆
   */
  clickVehicle(train) {
    this.setState(
      {
        currentTrain: train,
      },
      () => {
        setTimeout(() => {
          this.setState({
            showTrainDetail: true,
          });
        }, 400);
      }
    );
  }

  /*
   * 清楚选择
   */
  clearChoice() {
    this.setState(
      {
        showTrainDetail: false,
      },
      () => {
        setTimeout(() => {
          this.setState({
            currentTrain: null,
          });
        }, 400);
      }
    );
  }

  /*
   * 查看日志详情
   */
  async viewingLogDetails(item) {
    const troubleRules = await this.utils.reloadGlobalData('troubleRules');
    const troubleRule = troubleRules?.find((_) => {
      return item.troubleCode == _.value;
    });
    if (troubleRule) {
      item.rule = troubleRule;
      this.setState(
        {
          currentAlert: item,
        },
        () => {
          this.$('modal')?.open();
        }
      );
    }
  }

  /*
   * 监听鼠标移动
   */
  handleMouseMove(e) {
    const container = document.getElementById('map-container');
    if (!container) return;
    if (!this.enableMove) return;

    const { left, top, width, height } = container.getBoundingClientRect();
    const centerX = left + width / 2;
    const centerY = top + height / 2;

    // 计算鼠标位置相对于中心点的偏移
    const mouseX = e.clientX - centerX;
    const mouseY = e.clientY - centerY;

    // 计算移动距离（反向移动）
    const moveX = -(mouseX / centerX) * 50; // 15是移动系数，可以调整
    const moveY = -(mouseY / centerY) * 50;

    container.style.transform = `translate(${moveX}px, ${moveY}px) scale(1.8)`;
  }

  /*
   * 监听鼠标移入
   */
  listenToTheMouseMoveIn(e) {
    const container = document.getElementById('map-container');
    if (!container) return;
    this.enableMove = false;
    container.style.transition = `all 0.8s ease-in-out`;
    container.style.transform = `translate(0px, 0px) scale(1.8)`;
    if (this.timer2) {
      clearTimeout(this.timer2);
      this.timer2 = null;
    }
    this.timer1 = setTimeout(() => {
      container.style.transition = ``;
      this.enableMove = true;
    }, 800);
  }

  /*
   * 监听鼠标移出
   */
  monitorMouseOut(e) {
    const container = document.getElementById('map-container');
    if (!container) return;

    container.style.transition = `all 0.8s ease-in-out`;
    container.style.transform = `translate(0px, 0px) scale(1)`;
    if (this.timer1) {
      clearTimeout(this.timer1);
      this.timer1 = null;
    }
    this.timer2 = setTimeout(() => {
      container.style.transition = ``;
    }, 800);
  }

  /*
   * 更新列车状态
   */
  async updateTrainStatus() {
    const trainStatus = await this.utils.reloadGlobalData('trainStatus');
    // console.log('trainStatus: ', trainStatus);
    const statusMap = this.calculateDoorStatistics(trainStatus);
    // console.log('statusMap: ', statusMap);
    this.setState({
      statusMap,
    });
    trainStatus.stats = statusMap;
    this.utils.setGlobalData('trainStatus', trainStatus);
    this.utils.event.$emit('GLOBAL_DATA', 'trains');
  }

  /*
   * 汇总统计
   */
  calculateDoorStatistics(doorStatusData) {
    const statusMap = {
      SERIOUS: 'ERROR',
      MAJOR: 'WARNING',
      MINOR: 'NORMAL',
      NORMAL: 'NORMAL',
    };
    const stats = {
      // 车辆统计
      trains: {
        total: 0,
        normal: 0,
        warning: 0,
        error: 0,
      },
      // 车厢统计
      carriages: {
        total: 0,
        normal: 0,
        warning: 0,
        error: 0,
      },
      // 车门统计
      doors: {
        total: 0,
        normal: 0,
        warning: 0,
        error: 0,
      },
      doorStatus: {},
    };

    // 用于跟踪车辆和车厢的状态
    const trainStatus = new Map(); // 记录每个车辆的最高级别状态
    const carriageStatus = new Map(); // 记录每个车厢的最高级别状态
    const doorStatus = new Map(); // 记录每个车厢的最高级别状态

    // 遍历所有车门数据
    Object.entries(doorStatusData).forEach(([trainId, doors]) => {
      // 初始化该车辆的状态跟踪
      if (!trainStatus.has(trainId)) {
        trainStatus.set(trainId, 'NORMAL');
        stats.trains.total++;
      }

      doors.forEach((door) => {
        const carriageKey = `${trainId}_${door.car_id}`;
        const doorKey = `${trainId}|${door.car_id}|${door.door_id}`;
        // 初始化该车厢的状态跟踪
        if (!carriageStatus.has(carriageKey)) {
          carriageStatus.set(carriageKey, 'NORMAL');
          stats.carriages.total++;
        }

        // 更新车门计数
        stats.doors.total++;
        door.status = statusMap[door.highest_trouble_level];
        if (door.highest_trouble_level) {
          stats.doors[
            door.highest_trouble_level === 'SERIOUS' ? 'error' : 'warning'
          ]++;
          stats.doors[door.highest_trouble_level]++;
        }
        stats.doorStatus[doorKey] = door.status;

        // 更新车厢状态（取最高级别）
        const currentCarriageStatus = carriageStatus.get(carriageKey);
        if (
          this.getPriorityLevel(door.highest_trouble_level) >
          this.getPriorityLevel(currentCarriageStatus)
        ) {
          carriageStatus.set(carriageKey, door.highest_trouble_level);
        }

        // 更新车辆状态（取最高级别）
        const currentTrainStatus = trainStatus.get(trainId);
        if (
          this.getPriorityLevel(door.highest_trouble_level) >
          this.getPriorityLevel(currentTrainStatus)
        ) {
          trainStatus.set(trainId, door.highest_trouble_level);
        }
      });
    });
    // 统计车辆状态
    trainStatus.forEach((status) => {
      stats.trains[statusMap[status].toLowerCase()]++;
    });

    // 统计车厢状态
    carriageStatus.forEach((status) => {
      stats.carriages[statusMap[status].toLowerCase()]++;
    });
    console.log('stats: ', stats);
    return stats;
  }

  /*
   * 获取车门优先级
   */
  getPriorityLevel(status) {
    switch (status?.toUpperCase()) {
      case 'SERIOUS':
        return 3;
      case 'MAJOR':
        return 2;
      case 'MINOR':
        return 1;
      case 'NORMAL':
        return 1;
      case 'ERROR':
        return 3;
      case 'WARNING':
        return 2;
      case 'NORMAL':
        return 1;
      default:
        return 0;
    }
  }

  /*
   * 计算日记表格
   */
  calculationJournalForm() {
    return [
      {
        key: '1',
        label: '门信息',
        children: this.state.currentAlert?.doorId,
      },
      {
        key: '2',
        label: '故障类型',
        children: this.state.currentAlert?.troubleCode,
      },
      {
        key: '3',
        label: '故障描述',
        children: this.state.currentAlert?.description,
      },
      {
        key: '4',
        label: '故障状态',
        children: this.state.currentAlert?.resolved ? '故障中' : '已恢复',
      },
      {
        key: '5',
        label: '发生时间',
        children: this.state.currentAlert?.createTime,
      },
      {
        key: '6',
        label: '恢复时间',
        children: this.state.currentAlert?.resolveTime || '-',
      },
    ];
  }

  /*
   * 页面退出前执行
   */
  componentWillUnmount() {
    clearTimeout(window.transStatusTimer);
    this.utils.event.$off(this.eid);
  }

  /*
   * 点击日志查看车辆
   */
  clickLogToViewTheVehicle(logItem) {
    console.log('clickLogToViewTheVehicle: ', logItem);
    this.setState(
      {
        currentAlert: logItem,
      },
      () => {
        const train = this.utils
          .getGlobalData('trains')
          ?.find((_) => _.trainCode === logItem.trainId);
        if (train) {
          this.clickVehicle(train);
        }
      }
    );
  }

  render() {
    return (
      <Page
        backgroundImage=""
        className="h-screen bg-[#000000] bg-no-repeat bg-[url(https://cdn.appthen.com/FqCxZoOoNzrXxNreEJHp-FXla_Fw)] bg-cover bg-[position:center center]"
      >
        <Modal
          animate="pop"
          renderView={(props) => (
            <View className="flex flex-col w-[800px] h-[600px] bg-[url(https://cdn.appthen.com/Fmj3y3WsoFGW0uJgu3qLzARaX1yd)] bg-[length:100% 100%] bg-[position:center center]">
              <View className="flex flex-row h-[40px] pr-[3px] mt-[17px] ml-[33px]">
                <View className="bg-[url(https://cdn.appthen.com/FuLe38gAsB5_f0HNmjc1z_eFMh9T)] bg-[length:100% 100%] bg-[position:center center] flex flex-row items-center w-[400px]">
                  <View className="flex flex-row pl-[12px] flex-1">
                    <View className="flex flex-row items-center ml-[6px]">
                      <Text
                        children="详情"
                        className="text-base text-[#ffffff]"
                      />
                    </View>
                  </View>
                </View>
                <View __hidePh={true} className="flex-1" />
                <View
                  onClick={(e) => {
                    this.$('modal')?.close();
                  }}
                  className="pt-[7px] pr-[16px] pl-[10px]"
                >
                  <AtIcon
                    color="#acacac"
                    size={29}
                    svg="&#60;svg xmlns=&#34;http://www.w3.org/2000/svg&#34; width=&#34;100%&#34; height=&#34;100%&#34; viewBox=&#34;0 0 32 32&#34;&#62;&#60;path fill=&#34;currentColor&#34; d=&#34;M17.414 16L24 9.414L22.586 8L16 14.586L9.414 8L8 9.414L14.586 16L8 22.586L9.414 24L16 17.414L22.586 24L24 22.586z&#34;/&#62;&#60;/svg&#62;"
                  />
                </View>
              </View>
              <View className="pl-[32px] pr-[30px] pt-[25px]">
                <View
                  __hidePh={true}
                  className="bg-[rgba(255,255,255,0.08)] rounded pt-[15px] pr-[15px] pb-[15px] pl-[15px]"
                >
                  <Descriptions
                    title=""
                    items={this.calculationJournalForm()}
                    bordered={false}
                    column={2}
                    size="default"
                    layout="horizontal"
                    colon={false}
                    _unsafe_MixedSetter_items_select="ExpressionSetter"
                    className="text-[#cacacb]"
                  />
                </View>
                <View
                  __hidePh={true}
                  className="bg-[rgba(255,255,255,0.08)] rounded pt-[15px] pr-[15px] pb-[15px] pl-[15px] mt-[20px]"
                >
                  <View>
                    <Text
                      children="处理措施"
                      className="text-sm text-[rgba(165,165,165,0.65)]"
                    />
                  </View>
                  <View className="mt-[15px]">
                    <Text
                      children={
                        this.state.currentAlert?.rule?.emergencyMeasures || ''
                      }
                      className="text-sm text-[#ffffff]"
                    />
                  </View>
                  <View className="mt-[15px]">
                    <Text
                      children={
                        this.state.currentAlert?.rule?.maintenanceMeasures || ''
                      }
                      className="text-sm text-[#ffffff]"
                    />
                  </View>
                </View>
              </View>
            </View>
          )}
          visible={false}
          maskClosable={true}
          className=""
        />
        {!!!!this.state.showTrainDetail && (
          <View
            animationIn="fadeIn"
            inDelay={200}
            _unsafe_MixedSetter____condition____select="VariableSetter"
            className="fixed top-[0px] left-[0px] bottom-[0px] right-[0px] z-[200]"
          >
            {!!!!this.state.currentTrain && (
              <TrainInfoDetail
                onClose={(e) => {
                  this.clearChoice();
                }}
                onClickLog={(e) => {
                  this.viewingLogDetails(e);
                }}
                currentTrain={this.state.currentTrain}
                _unsafe_MixedSetter____condition____select="VariableSetter"
              />
            )}
          </View>
        )}
        <View className="h-screen pb-[16px] bg-[rgba(0,0,0,0.38)] flex flex-col pl-[16px] pt-[15px]">
          {!!false && <ScreenHeaderBar />}
          <View className="flex flex-row relative z-[110]">
            <View className="pr-[10px] pt-[3px]">
              <View className="flex flex-row">
                <View className="w-[120px] h-[30px] bg-[#17316c] rounded-[5px] flex flex-row pl-[10px] items-center pr-[5px] mr-[10px] border border-[#2854b7] border-solid">
                  <View className="flex-1">
                    <Text children="6号线" className="text-xs text-[#197dcb]" />
                  </View>
                  <View>
                    <AtIcon
                      color="#197dcb"
                      size={18}
                      svg="&#60;svg t=&#34;1712409164945&#34; class=&#34;icon&#34; viewBox=&#34;0 0 1024 1024&#34; version=&#34;1.1&#34; xmlns=&#34;http://www.w3.org/2000/svg&#34; p-id=&#34;4235&#34; width=&#34;100%&#34; height=&#34;100%&#34;&#62;&#60;path d=&#34;M830.24 340.688l11.328 11.312a16 16 0 0 1 0 22.624L530.448 685.76a16 16 0 0 1-22.64 0L196.688 374.624a16 16 0 0 1 0-22.624l11.312-11.312a16 16 0 0 1 22.624 0l288.496 288.496 288.512-288.496a16 16 0 0 1 22.624 0z&#34; fill=&#34;currentColor&#34; p-id=&#34;4236&#34;&#62;&#60;/path&#62;&#60;/svg&#62;"
                    />
                  </View>
                </View>
                {!!false && (
                  <View
                    onClick={(e) => {
                      this.$('modal')?.open();
                    }}
                    className="w-[120px] h-[30px] bg-[#17316c] rounded-[5px] flex flex-row pl-[10px] items-center pr-[5px] mr-[10px] border border-[#2854b7] border-solid"
                  >
                    <View className="flex-1">
                      <Text
                        children="列车号"
                        className="text-xs text-[#197dcb]"
                      />
                    </View>
                    <View>
                      <AtIcon
                        color="#197dcb"
                        size={18}
                        svg="&#60;svg t=&#34;1712409164945&#34; class=&#34;icon&#34; viewBox=&#34;0 0 1024 1024&#34; version=&#34;1.1&#34; xmlns=&#34;http://www.w3.org/2000/svg&#34; p-id=&#34;4235&#34; width=&#34;100%&#34; height=&#34;100%&#34;&#62;&#60;path d=&#34;M830.24 340.688l11.328 11.312a16 16 0 0 1 0 22.624L530.448 685.76a16 16 0 0 1-22.64 0L196.688 374.624a16 16 0 0 1 0-22.624l11.312-11.312a16 16 0 0 1 22.624 0l288.496 288.496 288.512-288.496a16 16 0 0 1 22.624 0z&#34; fill=&#34;currentColor&#34; p-id=&#34;4236&#34;&#62;&#60;/path&#62;&#60;/svg&#62;"
                      />
                    </View>
                  </View>
                )}
                <DatePicker.RangePicker
                  allowClear={true}
                  bordered={true}
                  autoFocus={false}
                  disabled={false}
                  showTime={true}
                  inputReadOnly={false}
                  onChange={(dates, dateStrings) => {
                    this.setState({
                      startTime: dateStrings[0],
                      endTime: dateStrings[1],
                    });
                  }}
                  placeholder="日期时间"
                />
                {!!false && (
                  <Button
                    type="default"
                    children="查询"
                    htmlType="button"
                    size="middle"
                    shape="default"
                    block={false}
                    danger={false}
                    ghost={false}
                    disabled={false}
                    className="ml-[20px]"
                  />
                )}
              </View>
            </View>
            <View __hidePh={true} className="flex-1" />
            <View className="flex flex-row" />
          </View>
          <View className="flex flex-row flex-1 pt-[15px] overflow-hidden pr-[11px]">
            <View
              inlineStyle={[
                {
                  enable: !!this.state.currentTrain,
                  name: '隐藏',
                  style: { transform: 'translateX(-110%)' },
                },
              ]}
              className="flex flex-col w-[449px] overflow-hidden bg-[url(https://cdn.appthen.com/FhzpsDCeXiLeGktu60rNEa9rleqy)] bg-no-repeat bg-[length:100% 100%] bg-[position:center center] mr-[5px] pl-[5px] relative z-[300]"
            >
              <View className="pb-[0px]">
                <View className="left-[0px] z-10 bottom-[0px] top-[83px] flex-col flex w-[469px]">
                  <View className="flex-1 flex flex-col">
                    <View className="mb-[24px] pt-[25px] pl-[0px]">
                      <View className="bg-[url(https://cdn.appthen.com/Fs46mGuFlCPE4PZL7Y0t-EgfaDdb)] bg-cover bg-[position:center center] w-[420px] h-[42px] flex flex-row items-center pl-[43px] ml-[25px]">
                        <Text
                          children="车门状态指标"
                          className="text-base text-[#ffffff]"
                        />
                      </View>
                    </View>
                    <View className="flex flex-row pr-[26px]">
                      <View className="flex flex-col items-center flex-1">
                        <View className="mb-[20px]">
                          <Text
                            children="监控车辆数"
                            className="text-sm text-[#b5c2e4]"
                          />
                        </View>
                        <View className="bg-[url(https://cdn.appthen.com/FsvwMI5ZrJaq_Vv3DNVy1xevq6yN)] w-[72px] h-[72px] bg-cover bg-[position:center center] flex flex-col justify-center items-center">
                          <Text
                            children={
                              this.state.statusMap?.trains?.total || '0'
                            }
                            className="text-2xl"
                          />
                        </View>
                        <View className="bg-[url(https://cdn.appthen.com/FodJ-12Es9i7OaGnsj4OwWJdwMz4)] w-[101px] h-[67px] bg-cover bg-[position:center center] mt-[-20px]" />
                      </View>
                      <View className="flex flex-col items-center flex-1">
                        <View className="mb-[20px]">
                          <Text
                            children="轻微故障车门"
                            className="text-sm text-[#b5c2e4]"
                          />
                        </View>
                        <View className="bg-[url(https://cdn.appthen.com/FsvwMI5ZrJaq_Vv3DNVy1xevq6yN)] w-[72px] h-[72px] bg-cover bg-[position:center center] flex flex-col justify-center items-center">
                          <Text
                            children={
                              this.state.statusMap?.doors?.warning || '0'
                            }
                            className="text-2xl text-[#ffffff]"
                          />
                        </View>
                        <View className="bg-[url(https://cdn.appthen.com/FodJ-12Es9i7OaGnsj4OwWJdwMz4)] w-[101px] h-[67px] bg-cover bg-[position:center center] mt-[-20px]" />
                      </View>
                      <View className="flex flex-col items-center flex-1">
                        <View className="mb-[20px]">
                          <Text
                            children="严重故障车门"
                            className="text-sm text-[#b5c2e4]"
                          />
                        </View>
                        <View className="bg-[url(https://cdn.appthen.com/FsvwMI5ZrJaq_Vv3DNVy1xevq6yN)] w-[72px] h-[72px] bg-cover bg-[position:center center] flex flex-col justify-center items-center">
                          <Text
                            children={this.state.statusMap?.doors?.error || '0'}
                            className="text-2xl text-[#ffffff]"
                          />
                        </View>
                        <View className="bg-[url(https://cdn.appthen.com/FodJ-12Es9i7OaGnsj4OwWJdwMz4)] w-[101px] h-[67px] bg-cover bg-[position:center center] mt-[-20px]" />
                      </View>
                    </View>
                  </View>
                </View>
                <View className="flex flex-col mt-[11px] pl-[25px] pr-[26px]">
                  <View className="bg-[url(https://cdn.appthen.com/Fs46mGuFlCPE4PZL7Y0t-EgfaDdb)] bg-cover bg-[position:center center] w-[420px] h-[42px] flex flex-row items-center mb-[10px] pl-[43px]">
                    <Text
                      children="告警数量变化趋势"
                      className="text-base text-[#ffffff]"
                    />
                  </View>
                  <View className="pb-[14px] bg-[rgba(2,17,56,0.25)] border-[0.5px] border-[rgba(11,80,146,0.5)] border-solid rounded-[2px] flex flex-col h-[220px]">
                    <TrendAreaChart />
                  </View>
                </View>
                <View className="flex flex-col mt-[14px] pl-[25px] pr-[26px]">
                  <View className="bg-[url(https://cdn.appthen.com/Fs46mGuFlCPE4PZL7Y0t-EgfaDdb)] bg-cover bg-[position:center center] w-[420px] h-[42px] flex flex-row items-center mb-[10px] pl-[43px]">
                    <Text
                      children="车辆故障排序"
                      className="text-base text-[#ffffff]"
                    />
                  </View>
                  <View className="bg-[rgba(2,17,56,0.25)] border-[0.5px] border-[rgba(11,80,146,0.5)] border-solid rounded-[2px] h-[220px] pb-[15px] flex flex-col">
                    <FaultWarningSortingDiagram />
                  </View>
                </View>
              </View>
            </View>
            <View className="flex-1 relative overflow-hidden">
              <View className="flex flex-col justify-center items-center pt-[39px] absolute top-[0] left-[0] right-[0px] z-[100]" />
              <View
                inlineStyle={[
                  {
                    enable: !!this.state.currentTrain,
                    name: 'hide',
                    style: { transform: 'translateY(-115%)' },
                  },
                ]}
                className="pt-[-5px] pr-[80px] pb-[39px] pl-[80px] flex flex-col justify-center items-center"
              >
                <View className="absolute top-[43px] left-[13px] z-[100] flex flex-row">
                  <View className="flex-1 flex flex-col justify-center items-center">
                    <View
                      __hidePh={true}
                      className="w-[48px] h-[46px] bg-[url(https://cdn.appthen.com/FpqJL6oomFuAvQw_g5X-jAbtx3pe)] bg-cover bg-[position:center center]"
                    />
                    <View className="h-[24px] w-[80px] bg-[url(https://cdn.appthen.com/Fp1wTmw-tCXIWEqBaFpU08xgtD3z)] bg-[length:100% 100%] bg-[position:center center] flex flex-col justify-center items-center">
                      <Text
                        children={this.state.statusMap?.doors?.warning || '0'}
                        className="text-sm text-[#ffffff]"
                      />
                    </View>
                  </View>
                  <View className="flex-1 flex flex-col justify-center items-center ml-[15px]">
                    <View
                      __hidePh={true}
                      className="w-[48px] h-[46px] bg-[url(https://cdn.appthen.com/FtnyStJIZlXBlgdvfGqch35yLerH)] bg-cover bg-[position:center center]"
                    />
                    <View className="h-[24px] w-[80px] bg-[url(https://cdn.appthen.com/FoPGNNgdlErIufXXvDnvFIZURTdy)] flex flex-col justify-center items-center bg-cover bg-[position:center center]">
                      <Text
                        children={this.state.statusMap?.doors?.error || '0'}
                        className="text-sm text-[#ffffff]"
                      />
                    </View>
                  </View>
                </View>
                <LineScreen
                  clickVehicle={(train) => {
                    const statusItem = train;
                    this.clickLogToViewTheVehicle(statusItem.trouble);
                  }}
                />
              </View>
              <View
                inlineStyle={[
                  {
                    enable: !!this.state.currentTrain,
                    name: 'hide',
                    style: { transform: 'translateY(110%)' },
                  },
                ]}
                className="absolute left-0 bottom-0 right-[0px] z-[300] pl-[10px] pr-[17px]"
              >
                <View className="flex flex-col mt-[11px] pr-[10px] bg-[url(https://cdn.appthen.com/FmSlUwQ5bmKbIAO6RWSrnsFloJaO)] bg-[length:100% 100%] bg-[position:center center] h-[300px] pt-[19px] pl-[20px]">
                  <View className="flex flex-row pr-[9px] items-start">
                    <View className="bg-[url(https://cdn.appthen.com/FtX4M6RI1LjJi_un-0YssFgvMY4H)] w-[350px] h-[42px] flex flex-row items-center pl-[13px] mb-[5px] ml-[8px] bg-[length:100% 100%] bg-[position:center center]">
                      <AtIcon
                        color="#bdb7b7"
                        size={23}
                        svg="&#60;svg xmlns=&#34;http://www.w3.org/2000/svg&#34; width=&#34;100%&#34; height=&#34;100%&#34; viewBox=&#34;0 0 32 32&#34;&#62;&#60;path fill=&#34;currentColor&#34; d=&#34;M21 3H11a5.006 5.006 0 0 0-5 5v12a4.99 4.99 0 0 0 3.582 4.77L7.769 29h2.176l1.714-4h8.682l1.714 4h2.176l-1.813-4.23A4.99 4.99 0 0 0 26 20V8a5.006 5.006 0 0 0-5-5M11 5h10a2.995 2.995 0 0 1 2.816 2H8.184A2.995 2.995 0 0 1 11 5m13 14h-3v2h2.816A2.995 2.995 0 0 1 21 23H11a2.995 2.995 0 0 1-2.816-2H11v-2H8v-2h16Zm0-4H8V9h16Z&#34;/&#62;&#60;/svg&#62;"
                        className="mr-[5px]"
                      />
                      <Text
                        children="列车状态总览"
                        className="text-base text-[#ffffff]"
                      />
                    </View>
                    <View __hidePh={true} className="flex-1" />
                    <View className="flex flex-row relative">
                      <View
                        __hidePh={true}
                        className="flex flex-row h-[32px] rounded-[2px] border border-[#0c4ccc] border-solid overflow-hidden"
                      >
                        <View
                          __hidePh={true}
                          className="flex flex-row w-[32px] h-[32px] justify-center items-center"
                        >
                          <AtIcon
                            color="#d4d4d4"
                            size={23}
                            svg="&#60;svg xmlns=&#34;http://www.w3.org/2000/svg&#34; width=&#34;100%&#34; height=&#34;100%&#34; viewBox=&#34;0 0 32 32&#34;&#62;&#60;path fill=&#34;currentColor&#34; d=&#34;M21 3H11a5.006 5.006 0 0 0-5 5v12a4.99 4.99 0 0 0 3.582 4.77L7.769 29h2.176l1.714-4h8.682l1.714 4h2.176l-1.813-4.23A4.99 4.99 0 0 0 26 20V8a5.006 5.006 0 0 0-5-5M11 5h10a2.995 2.995 0 0 1 2.816 2H8.184A2.995 2.995 0 0 1 11 5m13 14h-3v2h2.816A2.995 2.995 0 0 1 21 23H11a2.995 2.995 0 0 1-2.816-2H11v-2H8v-2h16Zm0-4H8V9h16Z&#34;/&#62;&#60;/svg&#62;"
                            className=""
                          />
                        </View>
                        <View className="min-w-[40px] bg-[rgba(6,24,68,0.8)] flex flex-col justify-center items-center">
                          <Text
                            children={
                              this.state.statusMap?.trains?.normal || '0'
                            }
                            className="text-sm text-[#e3e1e1] font-medium"
                          />
                        </View>
                      </View>
                      <View
                        __hidePh={true}
                        className="flex flex-row h-[32px] rounded-[2px] border border-[#a60808] border-solid overflow-hidden ml-[10px] bg-[rgba(117,3,20,0.8)]"
                      >
                        <View
                          __hidePh={true}
                          className="flex flex-row w-[32px] h-[32px] justify-center items-center"
                        >
                          <AtIcon
                            color="#d4d4d4"
                            size={23}
                            svg="&#60;svg xmlns=&#34;http://www.w3.org/2000/svg&#34; width=&#34;100%&#34; height=&#34;100%&#34; viewBox=&#34;0 0 32 32&#34;&#62;&#60;path fill=&#34;currentColor&#34; d=&#34;M21 3H11a5.006 5.006 0 0 0-5 5v12a4.99 4.99 0 0 0 3.582 4.77L7.769 29h2.176l1.714-4h8.682l1.714 4h2.176l-1.813-4.23A4.99 4.99 0 0 0 26 20V8a5.006 5.006 0 0 0-5-5M11 5h10a2.995 2.995 0 0 1 2.816 2H8.184A2.995 2.995 0 0 1 11 5m13 14h-3v2h2.816A2.995 2.995 0 0 1 21 23H11a2.995 2.995 0 0 1-2.816-2H11v-2H8v-2h16Zm0-4H8V9h16Z&#34;/&#62;&#60;/svg&#62;"
                            className=""
                          />
                        </View>
                        <View className="min-w-[40px] flex flex-col justify-center items-center">
                          <Text
                            children={
                              this.state.statusMap?.trains?.error || '0'
                            }
                            className="text-sm text-[#e3e1e1] font-medium"
                          />
                        </View>
                      </View>
                      <View
                        __hidePh={true}
                        className="flex flex-row h-[32px] rounded-[2px] border border-[#e47e34] border-solid overflow-hidden ml-[10px] bg-[rgba(228,126,52,0.19)]"
                      >
                        <View
                          __hidePh={true}
                          className="flex flex-row w-[32px] h-[32px] justify-center items-center"
                        >
                          <AtIcon
                            color="#d4d4d4"
                            size={23}
                            svg="&#60;svg xmlns=&#34;http://www.w3.org/2000/svg&#34; width=&#34;100%&#34; height=&#34;100%&#34; viewBox=&#34;0 0 32 32&#34;&#62;&#60;path fill=&#34;currentColor&#34; d=&#34;M21 3H11a5.006 5.006 0 0 0-5 5v12a4.99 4.99 0 0 0 3.582 4.77L7.769 29h2.176l1.714-4h8.682l1.714 4h2.176l-1.813-4.23A4.99 4.99 0 0 0 26 20V8a5.006 5.006 0 0 0-5-5M11 5h10a2.995 2.995 0 0 1 2.816 2H8.184A2.995 2.995 0 0 1 11 5m13 14h-3v2h2.816A2.995 2.995 0 0 1 21 23H11a2.995 2.995 0 0 1-2.816-2H11v-2H8v-2h16Zm0-4H8V9h16Z&#34;/&#62;&#60;/svg&#62;"
                            className=""
                          />
                        </View>
                        <View className="min-w-[40px] flex flex-col justify-center items-center">
                          <Text
                            children={
                              this.state.statusMap?.trains?.warning || '0'
                            }
                            className="text-sm text-[#e3e1e1] font-medium"
                          />
                        </View>
                      </View>
                      <View className="absolute left-[8px] bottom-[-19px]">
                        <Image
                          src="https://cdn.appthen.com/FqujKTmKGjtG1JJR4wMA0OP2uT7I?imageView2/0/w/1000"
                          remote={false}
                          fit={false}
                          className="w-[14px] h-[10px] left-[8px]"
                        />
                      </View>
                    </View>
                  </View>
                  <ListOfTrains
                    onClick={(train) => {
                      console.log('click train: ', train);
                      this.clickVehicle(train);
                    }}
                  />
                </View>
              </View>
            </View>
            <View
              inlineStyle={[
                {
                  enable: !!this.state.currentTrain,
                  name: 'hide',
                  style: { transform: 'translateX(110%)' },
                },
              ]}
              className="flex flex-col w-[494px] overflow-hidden bg-[url(https://cdn.appthen.com/FhzpsDCeXiLeGktu60rNEa9rleqy)] bg-no-repeat bg-[length:100% 100%] bg-[position:center center] mr-[5px] pl-[32px] pt-[24px] relative z-[300]"
            >
              <View className="flex flex-col pr-[19px] overflow-hidden pb-[6px] h-full">
                <View className="flex-1 flex flex-col overflow-hidden mb-[10px]">
                  <View className="bg-[url(https://cdn.appthen.com/Fs46mGuFlCPE4PZL7Y0t-EgfaDdb)] bg-cover bg-[position:center center] w-[420px] h-[42px] flex flex-row items-center mb-[15px] pl-[43px]">
                    <Text
                      children="车门重要故障"
                      className="text-base text-[#ffffff]"
                    />
                  </View>
                  <AlarmList
                    alertLevel="2"
                    onClickAlert={(item) => {
                      this.viewingLogDetails(item);
                    }}
                    onClickLogRoot={(item) => {
                      this.clickLogToViewTheVehicle(item);
                    }}
                    startTime={
                      this.state.startTime
                        ? new Date(this.state.startTime).valueOf()
                        : undefined
                    }
                    endTime={
                      this.state.endTime
                        ? new Date(this.state.endTime).valueOf()
                        : undefined
                    }
                  />
                </View>
                <View className="flex-1 flex flex-col overflow-hidden">
                  <View className="bg-[url(https://cdn.appthen.com/Fs46mGuFlCPE4PZL7Y0t-EgfaDdb)] bg-cover bg-[position:center center] w-[420px] h-[42px] flex flex-row items-center mb-[15px] pl-[43px]">
                    <Text
                      children="车门严重故障"
                      className="text-base text-[#ffffff]"
                    />
                  </View>
                  <AlarmList
                    alertLevel="1"
                    onClickAlert={(item) => {
                      this.viewingLogDetails(item);
                    }}
                    onClickLogRoot={(item) => {
                      this.clickLogToViewTheVehicle(item);
                    }}
                    startTime={
                      this.state.startTime
                        ? new Date(this.state.startTime).valueOf()
                        : undefined
                    }
                    endTime={
                      this.state.endTime
                        ? new Date(this.state.endTime).valueOf()
                        : undefined
                    }
                  />
                </View>
              </View>
            </View>
          </View>
        </View>
        {!!false && (
          <View className="bg-[url(https://cdn.appthen.com/Ft6WAHdaWXoyg75lrRckO8EaQ06c)] bg-cover w-full h-[31px] bg-[position:center center] bg-no-repeat fixed bottom-[0px] left-[0px] right-[0px] z-[100]" />
        )}
      </Page>
    );
  }
}
