# 快速双向绑定

## 流程

- 以`Input`组件举例，我需要实现让用户选择要绑定的字段，拿到`form?.name`或者`name`、`articles?.title`
- setPropValue(`value`, { type: "JSExpression", value: "this.state.form?.name" })

## 配置

```ts
{
  componentName: 'ComponentHeaderSetter',
  props: {
    fastBind: {
      inputField: 'value', // 默认
      outField: 'onChange', // 默认
      outValue: 'value', // value | e.detail.value
    },
  },
},
```

```ts
{
  "name": "onFieldChange",
  "desc": "接收组件数据",
  "type": "action",
  "fkId": "6436c0ff76dc53dab0b46c6a",
  "fileName": "HomePage",
  "projectId": "6436c0ff76dc53dab0b46c66",
  "params": [],
  "fields": [],
  "code": {
      "type": "JSFunction",
      "value": "function onFieldChange(e, {\n  field,\n  valueField\n}) {\n  const state = { ...this.state };\n  let value = e;\n  if (valueField) {\n    value = valueField.split('.').reduce((obj, key) => obj && obj[key], e);\n  }\n  this.utils.setValue(state, field, value);\n  this.setState(state);\n}"
  },
  "source": "function onFieldChange(e, {\n  field,\n  valueField\n}) {\n  const state = { ...this.state };\n  let value = e;\n  if (valueField) {\n    value = valueField.split('.').reduce((obj, key) => obj && obj[key], e);\n  }\n  this.utils.setValue(state, field, value);\n  this.setState(state);\n}",
  "_id": "HomePage##action##onFieldChange"
}
```

## 输出

```ts
{
  "methods": {
    "componentDidMount": {
      "type": "JSFunction",
      "value": "function() {\n  console.log('page init', this.state)\n}"
    },
    "unidirectionalBinding": {
      "type": "JSFunction",
      "value": "function unidirectionalBinding(value, {\n  field\n}) {\n  this.setState({\n    [field]: value\n  })\n}"
    }
  },
}
```

```JSON
{
  "componentName": "Input",
  "props": {
    "type": "text",
    "confirmType": "done",
    "value": {
      "type": "JSExpression",
      "value": "this.state.inputText",
      "mock": ""
    },
    "placeholder": "请输入",
    "password": false,
    "style": {},
    "__events": {
      "eventDataList": [
        {
          "type": "componentEvent",
          "name": "onChange",
          "relatedEventName": "unidirectionalBinding",
          "paramStr": "{\n \tfield: \"inputText\"\n}"
        }
      ],
      "eventList": []
    },
    "onChange": {
      "type": "JSFunction",
      "value": "function(){return this.unidirectionalBinding.apply(this,Array.prototype.slice.call(arguments).concat([{\n \tfield: \"inputText\"\n}])) }"
    }
  },
}
```
