// https://umijs.org/config/
import { defineConfig } from '@umijs/max';

const { PREVIEW_ENV, DEV_CROSS_UI } = process.env;

export default defineConfig({
  define: {
    'process.env.IsDev': true,
    'process.env.ServiceApiDomain': 'http://127.0.0.1:8360',
    'process.env.ApiDomain': 'http://127.0.0.1:1626',
    'process.env.SocketDomain': 'ws://127.0.0.1:1626',
    'process.env.PREVIEW_ENV': PREVIEW_ENV,
    'process.env.DEV_CROSS_UI': DEV_CROSS_UI,
  },
  publicPath: '/',
  favicons: ['/favicon.ico'],
});
