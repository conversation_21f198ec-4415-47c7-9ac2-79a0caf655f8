// https://umijs.org/config/
import { defineConfig } from '@umijs/max';

const { RUNNER, PREVIEW_ENV, DEV_CROSS_UI } = process.env;

export default defineConfig({
  define: {
    'process.env.IsDev': RUNNER === 'local',
    'process.env.SocketDomain':
      RUNNER === 'local' ? 'ws://127.0.0.1:1626' : 'wss://editor.appthen.com',
    'process.env.ApiDomain':
      RUNNER === 'local' ? 'http://127.0.0.1:1626' : 'https://editor.appthen.com',
    'process.env.PREVIEW_ENV': PREVIEW_ENV,
    'process.env.DEV_CROSS_UI': DEV_CROSS_UI,
  },
  styles: [
    // 'https://uipaas-assets.com/prod/npm/@alilc/lowcode-engine/1.1.4/dist/css/engine-core.css',
    'https://cdn.appthen.com/editor/engine/1.3.17/css/engine-core.css',
    'https://cdn.appthen.com/editor/npm/@alilc/lowcode-engine-ext/1.0.6-beta.8/dist/css/engine-ext.css',
    'https://cdn.appthen.com/editor/npm/antd/5.25.4/reset.min.css',
    'https://at.alicdn.com/t/c/font_3763523_zfvqa3rgc6.css',
  ],
  headScripts: [
    {
      src: 'https://cdn.appthen.com/editor/npm/react/18.2.0/react.production.min.js',
    },
    {
      src: 'https://cdn.appthen.com/editor/npm/react/18.2.0/react-dom.production.min.js',
    },
    {
      src: 'https://cdn.appthen.com/editor/npm/prop-types/15.7.2/prop-types.js',
    },
    {
      src: 'https://cdn.appthen.com/editor/npm/react15-polyfill/0.0.1/dist/index.js',
    },
    {
      src: 'https://cdn.appthen.com/editor/npm/lodash/4.6.1/lodash.min.js',
    },
    {
      src: 'https://cdn.appthen.com/editor/npm/moment.js/2.29.1/moment-with-locales.min.js',
    },
    {
      src: 'https://cdn.appthen.com/editor/npm/antd/5.9.0/dayjs.min.js',
    },
    // {
    //   src: 'https://cdn.appthen.com/editor/npm/antd/4.23.0/antd.min.js',
    //   crossorigin: true,
    // },
    {
      src: 'https://cdn.appthen.com/editor/npm/antd/5.9.0/antd.min.js',
      crossorigin: true,
    },
    {
      src: 'https://cdn.appthen.com/editor/npm/alifd__next/1.26.4/next-with-locales.min.js',
      // src: 'https://unpkg.com/@alifd/next@1.26.26/dist/next-with-locales.min.js',
      crossorigin: true,
    },
    {
      // src: 'https://cdn.appthen.com/editor/js/engine-core.js',
      src: 'https://cdn.appthen.com/editor/engine/1.3.17/js/engine-core.js',
      // src: 'http://127.0.0.1:5555/js/engine-core.js',
      // src: 'https://uipaas-assets.com/prod/npm/@alilc/lowcode-engine/1.1.6-beta.3/dist/js/engine-core.js',
      crossorigin: true,
    },
    {
      src: 'https://cdn.appthen.com/editor/js/engine-ext.js',
      // src: 'https://uipaas-assets.com/prod/npm/@alilc/lowcode-engine-ext/1.0.6-beta.21/dist/js/engine-ext.js',
      crossorigin: true,
    },
  ],
  publicPath: RUNNER === 'local' ? '/runner/' : '/static/runner/',
  favicons: ['/favicon.ico'],
});
