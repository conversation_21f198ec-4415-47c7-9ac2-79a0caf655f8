// https://umijs.org/config/
import { defineConfig } from '@umijs/max';
import { resolve } from 'path';
import routes from './routes';

const { REACT_APP_ENV } = process.env;
export default defineConfig({
  title: 'Appthen Studio',
  hash: true,
  antd: false,
  request: {},
  initialState: {},
  model: {},
  favicons: ['/static/app/favicon.ico'],
  // layout: null,
  // https://umijs.org/zh-CN/plugins/plugin-locale
  locale: {
    // default zh-CN
    default: 'zh-CN',
    antd: true,
    // default true, when it is true, will use `navigator.language` overwrite default
    baseNavigator: true,
  },
  alias: {
    // antd: 'antd/dist/antd.min.js',
    // '@': resolve(__dirname, '../src'),
    // '@': resolve(__dirname, '../src'),
    // '@@': resolve(__dirname, '../src/.umi'),
    // '@tarojs': resolve(__dirname, '../src/components/@tarojs'),
  },
  targets: { chrome: 80, firefox: 72, safari: 13, edge: 80 },
  history: {
    type: 'hash',
  },
  // @ts-ignore
  chainWebpack(memo, { env, webpack }) {
    // 设置 alias
    memo.resolve.alias.set('@tarojs', resolve(__dirname, '../src/components/@tarojs'));
    memo.resolve.alias.set('vscode', resolve(__dirname, '../src/plugins/vscode-adapter'));

    // 配置 Module Federation
    memo.plugin('module-federation').use(webpack.container.ModuleFederationPlugin, [
      {
        name: 'host',
        shared: {
          i18next: { singleton: true, eager: true },
          'react-i18next': { singleton: true, eager: true },
        },
      },
    ]);

    // memo.module
    //   .rule('worker')
    //   .test(/\.worker\.js$/)
    //   .use('worker-loader')
    //   .loader('worker-loader')
    //   .options({
    //     inline: 'no-fallback',
    //   })
    //   .end();

    // memo.plugin('monaco-editor-webpack-plugin').use(MonacoWebpackPlugin, [
    //   {
    //     // available options are documented at https://github.com/Microsoft/monaco-editor-webpack-plugin#options
    //     languages: ['json', 'typescript'],
    //     monacoEditorPath: join(__dirname, '../node_modules/monaco-editor'),
    //   },
    // ]);
  },
  // umi routes: https://umijs.org/docs/routing
  routes,
  access: {},
  // Theme for antd: https://ant.design/docs/react/customize-theme-cn
  // theme: 'default',
  // theme: {
  //   // 如果不想要 configProvide 动态设置主题需要把这个设置为 default
  //   // 只有设置为 variable， 才能使用 configProvide 动态设置主色调
  //   // https://ant.design/docs/react/customize-theme-variable-cn
  //   'root-entry-name': 'dark',
  //   'text-color': 'rgba(255,255,255, 0.85)',
  //   'text-color-dark': 'rgba(255,255,255, 0.85)',
  //   'icon-color': 'rgba(255,255,255, 0.85)',
  //   'primary-color': defaultSettings.primaryColor,
  //   'body-background': '#454545',
  //   'component-background': '#353535',
  //   'popover-background': '#454545',
  //   'background-color-light': '#454545',
  //   'border-color-base': '#555555',
  //   'border-radius-base': '10px',
  // },
  ignoreMomentLocale: true,
  // proxy: proxy[REACT_APP_ENV || 'dev'],
  manifest: {
    basePath: '/',
  },
  // vite: {}, // 启用 Vite 模式
  // esmi: {
  //   cdnOrigin: 'https://esm.sh',
  // },
  mfsu: true,
  // Fast Refresh 热更新
  fastRefresh: true,
  presets: [],
  sassLoader: {},
  // 禁用默认的错误覆盖层
  devtool: process.env.NODE_ENV === 'development' ? 'eval' : false,
  // 添加错误处理配置
  deadCode: false, // 禁用死代码检测，避免误报
  externals: {
    react: 'var window.React',
    antd: 'var window.antd',
    // 'mobx-react': 'mobxReact',
    'react-dom': 'var window.ReactDOM',
    'prop-types': 'var window.PropTypes',
    '@alifd/next': 'var window.Next',
    '@alilc/lowcode-engine': 'var window.AliLowCodeEngine',
    '@alilc/lowcode-editor-core': 'var window.AliLowCodeEngine.common.editorCabin',
    '@alilc/lowcode-editor-skeleton': 'var window.AliLowCodeEngine.common.skeletonCabin',
    '@alilc/lowcode-designer': 'var window.AliLowCodeEngine.common.designerCabin',
    '@alilc/lowcode-engine-ext': 'var window.AliLowCodeEngineExt',
    '@ali/lowcode-engine': 'var window.AliLowCodeEngine',
    moment: 'var window.dayjs',
    lodash: 'var window._',
    reactflow: 'var window.AppthenReactFlow',
    eruda: 'var window.eruda',
  },
  styles: [
    // 'https://uipaas-assets.com/prod/npm/@alilc/lowcode-engine/1.1.4/dist/css/engine-core.css',
    'https://cdn.appthen.com/editor/engine/********/css/engine-core.css',
    'https://cdn.appthen.com/editor/npm/@alilc/lowcode-engine-ext/1.0.6-beta.8/dist/css/engine-ext.css',
    'https://cdn.appthen.com/editor/npm/antd/5.25.4/reset.min.css',
    'https://cdn.appthen.com/editor/assets/icon/iconfont.css',
  ],

  headScripts: [
    {
      // src: 'https://cdn.bootcdn.net/ajax/libs/react/16.9.0/umd/react.development.js',
      // src: 'https://cdn.bootcdn.net/ajax/libs/react/16.9.0/umd/react.production.js',
      src: 'https://cdn.appthen.com/editor/npm/react/16.14.0/umd/react.production.min.js',
    },
    {
      // src: 'https://cdn.bootcdn.net/ajax/libs/react-dom/16.9.0/umd/react-dom.development.js',
      // src: 'https://cdn.bootcdn.net/ajax/libs/react-dom/16.9.0/umd/react-dom.production.min.js',
      src: 'https://cdn.appthen.com/editor/npm/react-dom/16.14.0/umd/react-dom.production.min.js',
    },
    {
      src: 'https://cdn.appthen.com/editor/npm/prop-types/15.7.2/prop-types.js',
    },
    {
      // src: 'https://cdn.bootcdn.net/ajax/libs/react/16.9.0/umd/react.profiling.min.js',
      src: 'https://cdn.appthen.com/editor/npm/react15-polyfill/0.0.1/dist/index.js',
    },
    {
      src: 'https://cdn.appthen.com/editor/npm/lodash/4.6.1/lodash.min.js',
    },
    {
      src: 'https://cdn.appthen.com/editor/npm/moment.js/2.29.1/moment-with-locales.min.js',
    },
    {
      src: 'https://cdn.appthen.com/editor/npm/antd/5.9.0/dayjs.min.js',
    },
    // {
    //   src: 'https://cdn.appthen.com/editor/npm/antd/4.23.0/antd.min.js',
    //   crossorigin: true,
    // },
    {
      src: 'https://cdn.appthen.com/editor/npm/antd/5.23.3/antd.min.js',
      crossorigin: true,
    },
    {
      src: 'https://cdn.appthen.com/editor/npm/alifd__next/1.26.4/next-with-locales.min.js',
      // src: 'https://unpkg.com/@alifd/next@1.26.26/dist/next-with-locales.min.js',
      crossorigin: true,
    },
    {
      // src: 'https://cdn.appthen.com/editor/js/engine-core.js',
      src: 'https://cdn.appthen.com/editor/engine/********/js/engine-core.js',
      // src: 'http://127.0.0.1:5555/js/engine-core.js',
      // src: 'https://uipaas-assets.com/prod/npm/@alilc/lowcode-engine/1.1.6-beta.3/dist/js/engine-core.js',
      crossorigin: true,
    },
    {
      src: 'https://cdn.appthen.com/editor/js/engine-ext.js',
      // src: 'https://uipaas-assets.com/prod/npm/@alilc/lowcode-engine-ext/1.0.6-beta.21/dist/js/engine-ext.js',
      crossorigin: true,
    },
    {
      src: 'https://cdn.appthen.com/editor/npm/eruda/1.0.2/eruda.js',
      // src: 'https://uipaas-assets.com/prod/npm/@alilc/lowcode-engine-ext/1.0.6-beta.21/dist/js/engine-ext.js',
    },
    // {
    //   src: 'https://cdn.appthen.com/material/118159/@appthen/react-log-viewer/0.0.4/index.umd.js',
    // },
    {
      src: 'https://cdn.appthen.com/editor/npm/@tailwindcss/browser@4.1.10/index.global.js',
    },
  ],

  define: {
    'process.env.VERSION': 187,
    'process.env.IsDev': false,
    'process.env.ServiceApiDomain': 'https://www.appthen.com',
    'process.env.SocketDomain': 'wss://editor.appthen.com',
    'process.env.ApiDomain': 'https://editor.appthen.com',
  },
  publicPath: '/static/app/',
  jsMinifier: 'terser',
  jsMinifierOptions: {
    compress: {
      ecma: 2020,
      passes: 2,
    },
    format: {
      ecma: 2020,
    },
  },
  // tailwindcss: {},
});
