"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const enhancedTailwindTransformer_1 = require("./enhancedTailwindTransformer");
console.log('=== 标准 Tailwind 渐变测试 ===');
// 测试标准 Tailwind 渐变格式: bg-gradient-to-br from-blue-900 to-indigo-800
const standardGradientClasses = ['bg-gradient-to-br', 'from-blue-900', 'to-indigo-800'];
console.log('输入类名:', standardGradientClasses.join(' '));
const result = enhancedTailwindTransformer_1.EnhancedStyleTransformer.toInlineStyle(standardGradientClasses);
console.log('转换结果:', result);
// 测试反向转换
if (result.style && result.style.background) {
    const reverseResult = enhancedTailwindTransformer_1.EnhancedStyleTransformer.toStyle({ background: result.style.background });
    console.log('反向转换:', reverseResult);
}
console.log('\n=== 复杂渐变测试 ===');
// 测试复杂渐变
const complexGradientClasses = [
    'bg-gradient-to-tr',
    'from-blue-500',
    'via-purple-500',
    'to-pink-500',
];
console.log('输入类名:', complexGradientClasses.join(' '));
const complexResult = enhancedTailwindTransformer_1.EnhancedStyleTransformer.toInlineStyle(complexGradientClasses);
console.log('转换结果:', complexResult);
console.log('\n=== 文本渐变测试 ===');
// 测试文本渐变
const textGradientClasses = [
    'bg-gradient-to-r',
    'from-blue-600',
    'to-purple-600',
    'bg-clip-text',
    'text-transparent',
];
console.log('输入类名:', textGradientClasses.join(' '));
const textResult = enhancedTailwindTransformer_1.EnhancedStyleTransformer.toInlineStyle(textGradientClasses);
console.log('转换结果:', textResult);
console.log('\n=== Grid 测试 ===');
// 测试复杂 Grid
const gridClasses = ['grid-cols-[200px_minmax(900px,_1fr)_100px]'];
console.log('输入类名:', gridClasses.join(' '));
const gridResult = enhancedTailwindTransformer_1.EnhancedStyleTransformer.toInlineStyle(gridClasses);
console.log('转换结果:', gridResult);
// 测试反向转换
if (gridResult.style && gridResult.style.gridTemplateColumns) {
    const gridReverseResult = enhancedTailwindTransformer_1.EnhancedStyleTransformer.toStyle({
        gridTemplateColumns: gridResult.style.gridTemplateColumns,
    });
    console.log('反向转换:', gridReverseResult);
}
// 测试 Grid 转换
console.log('=== Grid 转换测试 ===');
// 测试 1: 复杂 Grid 列定义 (style -> tailwind)
const complexGridStyle = {
    gridTemplateColumns: '200px minmax(900px, 1fr) 100px',
};
const gridTailwind = enhancedTailwindTransformer_1.EnhancedStyleTransformer.toStyle(complexGridStyle);
console.log('复杂 Grid 列定义:', gridTailwind);
// 期望输出: grid-cols-[200px_minmax(900px,_1fr)_100px]
// 测试 2: 复杂 Grid 类名转回样式 (tailwind -> style)
const complexGridClasses = ['grid-cols-[200px_minmax(900px,_1fr)_100px]'];
const gridStyleResult = enhancedTailwindTransformer_1.EnhancedStyleTransformer.toInlineStyle(complexGridClasses);
console.log('复杂 Grid 类名转样式:', gridStyleResult);
// 期望输出: { gridTemplateColumns: '200px minmax(900px, 1fr) 100px' }
// 测试 3: 标准 Grid 转换
const standardGridStyle = {
    gridTemplateColumns: 'repeat(3, minmax(0, 1fr))',
};
const standardGridTailwind = enhancedTailwindTransformer_1.EnhancedStyleTransformer.toStyle(standardGridStyle);
console.log('标准 Grid 转换:', standardGridTailwind);
// 期望输出: grid-cols-3
const standardGridClasses = ['grid-cols-3'];
const standardGridStyleResult = enhancedTailwindTransformer_1.EnhancedStyleTransformer.toInlineStyle(standardGridClasses);
console.log('标准 Grid 类名转样式:', standardGridStyleResult);
// 期望输出: { gridTemplateColumns: 'repeat(3, minmax(0, 1fr))' }
console.log('\n=== 渐变转换测试 ===');
// 测试 4: 复杂渐变 (style -> tailwind)
const complexGradientStyle = {
    background: 'linear-gradient(45deg, rgba(255, 0, 0, 0.8) 0%, rgba(0, 255, 0, 0.6) 50%, rgba(0, 0, 255, 0.9) 100%)',
};
const gradientTailwind = enhancedTailwindTransformer_1.EnhancedStyleTransformer.toStyle(complexGradientStyle);
console.log('复杂渐变转换:', gradientTailwind);
// 测试 5: 文本渐变 (style -> tailwind)
const textGradientStyle = {
    background: 'linear-gradient(90deg, #ff0000, #00ff00)',
    backgroundClip: 'text',
    color: 'transparent',
};
const textGradientTailwind = enhancedTailwindTransformer_1.EnhancedStyleTransformer.toStyle(textGradientStyle);
console.log('文本渐变转换:', textGradientTailwind);
// 测试 6: 复杂渐变类名转回样式 (tailwind -> style)
const complexGradientClasses = [
    'bg-gradient-to-tr',
    'from-[rgba(255,0,0,0.8)]',
    'via-[rgba(0,255,0,0.6)]',
    'to-[rgba(0,0,255,0.9)]',
];
const gradientStyleResult = enhancedTailwindTransformer_1.EnhancedStyleTransformer.toInlineStyle(complexGradientClasses);
console.log('复杂渐变类名转样式:', gradientStyleResult);
// 测试 7: 文本渐变类名转回样式
const textGradientClasses = [
    'bg-gradient-to-r',
    'from-[#ff0000]',
    'to-[#00ff00]',
    'bg-clip-text',
    'text-transparent',
];
const textGradientStyleResult = enhancedTailwindTransformer_1.EnhancedStyleTransformer.toInlineStyle(textGradientClasses);
console.log('文本渐变类名转样式:', textGradientStyleResult);
console.log('\n=== 边界情况测试 ===');
// 测试 8: 带角度的自定义渐变
const customAngleGradientStyle = {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
};
const customAngleGradientTailwind = enhancedTailwindTransformer_1.EnhancedStyleTransformer.toStyle(customAngleGradientStyle);
console.log('自定义角度渐变:', customAngleGradientTailwind);
// 测试 9: 复杂 Grid 行定义
const complexGridRowStyle = {
    gridTemplateRows: '100px auto 1fr 50px',
};
const gridRowTailwind = enhancedTailwindTransformer_1.EnhancedStyleTransformer.toStyle(complexGridRowStyle);
console.log('复杂 Grid 行定义:', gridRowTailwind);
const complexGridRowClasses = ['grid-rows-[100px_auto_1fr_50px]'];
const gridRowStyleResult = enhancedTailwindTransformer_1.EnhancedStyleTransformer.toInlineStyle(complexGridRowClasses);
console.log('复杂 Grid 行类名转样式:', gridRowStyleResult);
// 测试 10: 多色渐变（超过3种颜色）
const multiColorGradientClasses = [
    'bg-gradient-to-r',
    'from-[#ff0000]',
    'via-[#00ff00]',
    'via-[#0000ff]',
    'to-[#ffff00]',
];
const multiColorGradientResult = enhancedTailwindTransformer_1.EnhancedStyleTransformer.toInlineStyle(multiColorGradientClasses);
console.log('多色渐变类名转样式:', multiColorGradientResult);
