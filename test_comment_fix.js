// 测试修复注释生成空Text组件的问题
const { CodeToSchemaTransformer } = require('./src/plugins/code-transformer/codeToSchema/index.ts');

// 测试用例：包含注释的JSX代码
const testCode = `
export default function TestComponent() {
  return (
    <div>
      {/* 这是一个注释 */}
      <div>正常内容</div>
      {/* 另一个注释 */}
    </div>
  );
}
`;

async function testCommentFix() {
  const transformer = new CodeToSchemaTransformer();
  
  try {
    const result = await transformer.transform(testCode);
    console.log('转换结果:', JSON.stringify(result.schema, null, 2));
    
    // 检查是否还有空的Text组件
    const hasEmptyTextComponents = JSON.stringify(result.schema).includes('"componentName":"Text","props":{"children":""}');
    
    if (hasEmptyTextComponents) {
      console.log('❌ 测试失败：仍然存在空的Text组件');
    } else {
      console.log('✅ 测试成功：没有发现空的Text组件');
    }
    
  } catch (error) {
    console.error('测试出错:', error);
  }
}

testCommentFix();
