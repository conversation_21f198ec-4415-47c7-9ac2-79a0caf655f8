/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  corePlugins: {
    // 保留基础功能
    backgroundColor: true,
    textColor: true,
    width: true,
    height: true,
    padding: true,
    margin: true,
    borderWidth: true,
    borderColor: true,
    borderRadius: true,
    fontSize: true,
    fontWeight: true,
    display: true,
    position: true,
    inset: true,
    zIndex: true,
    opacity: true,

    // 保留需要运行时支持的功能
    animation: true,
    transitionProperty: true,
    transform: true,

    // 交互状态
    hover: true,
    focus: true,
    active: true,
    disabled: true,
    group: true, // group-hover 等
    peer: true, // peer-hover 等

    // 响应式设计
    screens: true,

    // 暗色模式
    darkMode: true,

    // 特殊功能
    space: true, // space-between
    divide: true, // divider
    aspectRatio: true, // 宽高比
  },
  darkMode: 'class', // 使用 class 策略而不是媒体查询
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: 'rgb(var(--tw-color-primary) / <alpha-value>)',
          light: 'rgb(var(--tw-color-primary-light) / <alpha-value>)',
          dark: 'rgb(var(--tw-color-primary-dark) / <alpha-value>)',
        },
        secondary: {
          DEFAULT: 'rgb(var(--tw-color-secondary) / <alpha-value>)',
          light: 'rgb(var(--tw-color-secondary-light) / <alpha-value>)',
          dark: 'rgb(var(--tw-color-secondary-dark) / <alpha-value>)',
        },
      },
      animation: {
        'spin-slow': 'spin 3s linear infinite',
      },
      transitionProperty: {
        'max-height': 'max-height',
      },
    },
  },
  plugins: [],
};
