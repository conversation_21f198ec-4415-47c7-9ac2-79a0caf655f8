import './styles.css';

// 添加主题切换功能
const toggleTheme = () => {
  const html = document.documentElement;
  const currentTheme = html.getAttribute('data-theme');
  html.setAttribute('data-theme', currentTheme === 'dark' ? 'light' : 'dark');
};

// 添加颜色切换功能
const changeButtonColor = () => {
  const root = document.documentElement;
  const randomColor = '#' + Math.floor(Math.random() * 16777215).toString(16);
  const darkerColor = randomColor.replace(/[0-9a-f]/g, (c) =>
    Math.max(0, parseInt(c, 16) - 2).toString(16),
  );
  root.style.setProperty('--button-bg', randomColor);
  root.style.setProperty('--button-hover', darkerColor);
};

// 添加示例内容
document.querySelector('#app').innerHTML = `
  <div class="space-y-8 p-[20px]">
    <h1 class="text-[24px] font-[bold]">Tailwind 特殊功能示例</h1>
    
    <!-- CSS 变量控制的按钮示例 -->
    <section>
      <h2 class="text-[18px] mb-[10px]">CSS 变量控制的按钮</h2>
      <div class="space-y-4">
        <button class="custom-button">
          悬浮查看效果
        </button>
        
        <button class="custom-button" onclick="changeButtonColor()">
          点击随机改变颜色
        </button>

        <div class="text-[14px] text-[#666]">
          当前按钮颜色：<span id="currentColor"></span>
        </div>
      </div>
    </section>

    <!-- 主题色示例 -->
    <section>
      <h2 class="text-[18px] mb-[10px]">主题色示例</h2>
      <div class="space-y-4">
        <!-- 主色系 -->
        <div class="space-y-2">
          <div class="h-[40px] bg-primary"></div>
          <div class="h-[40px] bg-primary-light"></div>
          <div class="h-[40px] bg-primary-dark"></div>
        </div>
        
        <!-- 次要色系 -->
        <div class="space-y-2">
          <div class="h-[40px] bg-secondary"></div>
          <div class="h-[40px] bg-secondary-light"></div>
          <div class="h-[40px] bg-secondary-dark"></div>
        </div>

        <button onclick="document.documentElement.setAttribute('data-theme', document.documentElement.getAttribute('data-theme') === 'dark' ? 'light' : 'dark')" 
                class="bg-primary text-[#fff] p-[8px] rounded-[4px] hover:bg-primary-dark">
          切换主题
        </button>
      </div>
    </section>

    <!-- 1. Hover/Focus/Active 状态 -->
    <section>
      <h2 class="text-[18px] mb-[10px]">1. 状态示例</h2>
      <button class="bg-[#3b82f6] text-[#fff] p-[8px] rounded-[4px] hover:bg-[#2563eb] focus:ring-2 active:bg-[#1d4ed8]">
        Hover/Focus/Active 按钮
      </button>
    </section>

    <!-- 2. 暗色模式 -->
    <section>
      <h2 class="text-[18px] mb-[10px]">2. 暗色模式</h2>
      <div class="bg-[#fff] dark:bg-[#1f2937] p-[16px] rounded-[8px]">
        <p class="text-[#000] dark:text-[#fff]">暗色模式文本</p>
      </div>
      <button onclick="document.documentElement.classList.toggle('dark')" class="mt-[8px] bg-[#4b5563] text-[#fff] p-[8px] rounded-[4px]">
        切换暗色模式
      </button>
    </section>

    <!-- 3. 响应式设计 -->
    <section>
      <h2 class="text-[18px] mb-[10px]">3. 响应式设计</h2>
      <div class="bg-[#e5e7eb] p-[16px] rounded-[8px]">
        <p class="text-[14px] sm:text-[16px] md:text-[18px] lg:text-[20px]">
          响应式文本 - 改变窗口大小查看效果
        </p>
      </div>
    </section>

    <!-- 4. 动画效果 -->
    <section>
      <h2 class="text-[18px] mb-[10px]">4. 动画效果</h2>
      <div class="flex gap-[16px]">
        <div class="w-[40px] h-[40px] bg-[#3b82f6] animate-spin"></div>
        <div class="w-[40px] h-[40px] bg-[#3b82f6] animate-spin-slow"></div>
      </div>
    </section>

    <!-- 5. Group Hover -->
    <section>
      <h2 class="text-[18px] mb-[10px]">5. Group Hover</h2>
      <div class="group bg-[#f3f4f6] p-[16px] rounded-[8px] cursor-pointer">
        <h3 class="text-[16px] group-hover:text-[#3b82f6]">主标题</h3>
        <p class="text-[14px] text-[#6b7280] group-hover:text-[#2563eb]">
          悬浮时整体变色
        </p>
      </div>
    </section>

    <!-- 6. Peer -->
    <section>
      <h2 class="text-[18px] mb-[10px]">6. Peer 元素</h2>
      <div class="space-y-2">
        <input type="text" 
               class="peer p-[8px] border-[1px] border-[#d1d5db] rounded-[4px] w-[200px]"
               placeholder="输入内容..." />
        <p class="text-[#ef4444] hidden peer-invalid:block">
          输入有误
        </p>
      </div>
    </section>

    <!-- 7. Space Between -->
    <section>
      <h2 class="text-[18px] mb-[10px]">7. Space Between</h2>
      <div class="flex space-x-4">
        <div class="w-[40px] h-[40px] bg-[#3b82f6]"></div>
        <div class="w-[40px] h-[40px] bg-[#3b82f6]"></div>
        <div class="w-[40px] h-[40px] bg-[#3b82f6]"></div>
      </div>
    </section>

    <!-- 8. Divide -->
    <section>
      <h2 class="text-[18px] mb-[10px]">8. Divide 分割线</h2>
      <div class="divide-y divide-[#e5e7eb]">
        <div class="p-[16px]">项目 1</div>
        <div class="p-[16px]">项目 2</div>
        <div class="p-[16px]">项目 3</div>
      </div>
    </section>

    <!-- 9. Aspect Ratio -->
    <section>
      <h2 class="text-[18px] mb-[10px]">9. 宽高比</h2>
      <div class="w-[200px] aspect-video bg-[#3b82f6]">
        16:9 视频容器
      </div>
    </section>

    <!-- 10. Transform & Transition -->
    <section>
      <h2 class="text-[18px] mb-[10px]">10. 变换和过渡</h2>
      <div class="flex gap-[16px]">
        <div class="w-[100px] h-[100px] bg-[#3b82f6] transition-transform hover:scale-110">
          Hover 缩放
        </div>
        <div class="w-[100px] h-[100px] bg-[#3b82f6] transition-transform hover:rotate-45">
          Hover 旋转
        </div>
      </div>
    </section>
  </div>
`;

// 显示当前颜色
const updateCurrentColor = () => {
  const color = getComputedStyle(document.documentElement).getPropertyValue('--button-bg').trim();
  document.getElementById('currentColor').textContent = color;
};

// 监听颜色变化
const observer = new MutationObserver(updateCurrentColor);
observer.observe(document.documentElement, {
  attributes: true,
  attributeFilter: ['style'],
});

// 初始显示颜色
updateCurrentColor();
