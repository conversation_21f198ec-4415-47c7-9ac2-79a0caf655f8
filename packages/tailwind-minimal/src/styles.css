@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --tw-color-primary: 59 130 246; /* blue-500 */
  --tw-color-primary-light: 96 165 250; /* blue-400 */
  --tw-color-primary-dark: 37 99 235; /* blue-600 */

  --tw-color-secondary: 107 114 128; /* gray-500 */
  --tw-color-secondary-light: 156 163 175; /* gray-400 */
  --tw-color-secondary-dark: 75 85 99; /* gray-600 */

  --button-bg: #3b82f6;
  --button-hover: #1d4ed8;
  --button-text: #ffffff;
}

[data-theme='dark'] {
  --tw-color-primary: 37 99 235; /* blue-600 */
  --tw-color-primary-light: 59 130 246; /* blue-500 */
  --tw-color-primary-dark: 29 78 216; /* blue-700 */

  --tw-color-secondary: 75 85 99; /* gray-600 */
  --tw-color-secondary-light: 107 114 128; /* gray-500 */
  --tw-color-secondary-dark: 55 65 81; /* gray-700 */

  --button-bg: #1d4ed8;
  --button-hover: #1e40af;
  --button-text: #ffffff;
}

/* 自定义按钮样式 */
.custom-button {
  background-color: var(--button-bg);
  color: var(--button-text);
  padding: 8px 16px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.custom-button:hover {
  background-color: var(--button-hover);
}
