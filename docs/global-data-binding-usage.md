# 响应式 GlobalData 绑定功能

## 功能概述

新增的 `bindGlobalData` 功能为 React 类组件提供了响应式的全局数据绑定能力，实现精确的组件更新控制。

### 核心特性

1. **自动订阅**: 组件调用 `this.globalData.get(key)` 时自动订阅该数据
2. **精确更新**: 只有订阅了特定 key 的组件会在数据变化时重新渲染
3. **内存安全**: 组件卸载时自动清理所有订阅，防止内存泄漏
4. **性能优化**: 支持批量操作和条件订阅

## API 接口

### 绑定和解绑

```javascript
// 绑定组件到全局数据源
appHelper.utils.bindGlobalData(componentInstance);

// 解绑组件
appHelper.utils.unbindGlobalData(componentInstance);
```

### 响应式 globalData 对象

```javascript
// 获取数据（自动订阅）
const value = this.globalData.get('userInfo');

// 设置数据（通知所有订阅者）
this.globalData.set('userInfo', { name: '张三' });

// 加载数据源（请求接口并自动订阅）
await this.globalData.load('userInfo', { userId: 123 });

// 批量加载多个数据源（并行请求）
await this.globalData.loadAll([
  { key: 'userInfo', params: { userId: 123 } },
  { key: 'userSettings' },
  { key: 'permissions', params: { roleId: 'admin' } },
]);

// 批量获取（一次性订阅多个key，不请求接口）
const data = this.globalData.getAll(['userInfo', 'settings', 'permissions']);

// 取消订阅特定key
this.globalData.unsubscribe('notifications');
```

### 全局 API

```javascript
// 加载特定数据源
await appHelper.utils.loadGlobalData('userInfo', { userId: 123 });

// 批量加载多个数据源
await appHelper.utils.loadGlobalDataBatch([
  { key: 'userInfo', params: { userId: 123 } },
  { key: 'userSettings' },
]);

// 重新加载数据源（兼容原有API）
await appHelper.utils.reloadGlobalData('userInfo', { userId: 123 });
```

## 使用方式

### 1. 低代码引擎层面自动绑定

在引擎的组件渲染层自动注入绑定逻辑：

```javascript
export class LowCodeComponentWrapper extends Component {
  componentDidMount() {
    // 引擎层面自动注入
    if (this.props.appHelper?.utils?.bindGlobalData) {
      this.props.appHelper.utils.bindGlobalData(this);
    }
  }

  componentWillUnmount() {
    // 引擎层面自动注入
    if (this.props.appHelper?.utils?.unbindGlobalData) {
      this.props.appHelper.utils.unbindGlobalData(this);
    }
  }

  render() {
    // 直接使用 this.globalData
    const userInfo = this.globalData?.get('userInfo');
    const theme = this.globalData?.get('theme');

    return (
      <div style={{ backgroundColor: theme?.primaryColor }}>
        <h1>欢迎 {userInfo?.name}</h1>
        <button onClick={this.handleUpdateTheme}>更新主题</button>
      </div>
    );
  }

  handleUpdateTheme = () => {
    // 设置数据，所有订阅了 'theme' 的组件都会自动更新
    this.globalData?.set('theme', {
      primaryColor: '#ff6600',
    });
  };
}
```

### 2. 导出代码注解方式

对于导出到本地的代码，使用装饰器注解：

```javascript
// 使用装饰器
export const UserProfileComponent = withGlobalData(
  class extends Component {
    render() {
      const userInfo = this.globalData?.get('userInfo');
      const settings = this.globalData?.get('userSettings');

      return (
        <div className="user-profile">
          <img src={userInfo?.avatar} alt="头像" />
          <h2>{userInfo?.name}</h2>
          <p>主题: {settings?.theme}</p>
          <button onClick={this.updateUserName}>更新用户名</button>
        </div>
      );
    }

    updateUserName = () => {
      const newName = prompt('请输入新的用户名:');
      if (newName) {
        const currentUserInfo = this.globalData?.get('userInfo');
        this.globalData?.set('userInfo', {
          ...currentUserInfo,
          name: newName,
        });
      }
    };
  },
);
```

### 3. 手动绑定方式

```javascript
export class ManualBindingComponent extends Component {
  componentDidMount() {
    if (window.appHelper?.utils?.bindGlobalData) {
      window.appHelper.utils.bindGlobalData(this);
    }
  }

  componentWillUnmount() {
    if (window.appHelper?.utils?.unbindGlobalData) {
      window.appHelper.utils.unbindGlobalData(this);
    }
  }

  render() {
    const count = this.globalData?.get('counter') || 0;

    return (
      <div>
        <h3>计数器: {count}</h3>
        <button onClick={this.increment}>+1</button>
        <button onClick={this.reset}>重置</button>
      </div>
    );
  }

  increment = () => {
    const current = this.globalData?.get('counter') || 0;
    this.globalData?.set('counter', current + 1);
  };

  reset = () => {
    this.globalData?.set('counter', 0);
  };
}
```

## 高级用法

### 条件订阅

```javascript
export class ConditionalSubscriptionComponent extends Component {
  state = {
    isSubscribedToNotifications: false,
  };

  render() {
    const userInfo = this.globalData?.get('userInfo');

    // 只有在需要时才订阅通知数据
    const notifications = this.state.isSubscribedToNotifications
      ? this.globalData?.get('notifications')
      : null;

    return (
      <div>
        <h3>用户: {userInfo?.name}</h3>
        <label>
          <input
            type="checkbox"
            checked={this.state.isSubscribedToNotifications}
            onChange={this.handleNotificationToggle}
          />
          订阅通知更新
        </label>
        {notifications && (
          <div>
            <h4>通知 ({notifications?.length})</h4>
            {notifications?.map((notif) => (
              <div key={notif.id}>{notif.message}</div>
            ))}
          </div>
        )}
      </div>
    );
  }

  handleNotificationToggle = (e) => {
    const isSubscribed = e.target.checked;
    this.setState({ isSubscribedToNotifications: isSubscribed });

    if (!isSubscribed) {
      // 手动取消订阅，减少不必要的更新
      this.globalData?.unsubscribe('notifications');
    }
  };
}
```

### 批量数据操作

```javascript
export class BatchOperationComponent extends Component {
  componentDidMount() {
    window.appHelper?.utils?.bindGlobalData(this);

    // 一次性订阅多个key
    const data = this.globalData?.getAll(['userInfo', 'settings', 'permissions']);
    console.log('初始数据:', data);
  }

  render() {
    const userInfo = this.globalData?.get('userInfo');
    const settings = this.globalData?.get('settings');
    const permissions = this.globalData?.get('permissions');

    return (
      <div>
        <h3>用户信息</h3>
        <p>姓名: {userInfo?.name}</p>
        <p>主题: {settings?.theme}</p>
        <p>权限: {permissions?.join(', ')}</p>
      </div>
    );
  }
}
```

### 数据源加载示例

```javascript
export class DataSourceLoadingComponent extends Component {
  state = {
    loading: false,
    error: null,
  };

  componentDidMount() {
    window.appHelper?.utils?.bindGlobalData(this);
    this.loadInitialData();
  }

  // 加载初始数据
  loadInitialData = async () => {
    this.setState({ loading: true, error: null });

    try {
      // 并行加载多个数据源
      await this.globalData?.loadAll([
        { key: 'userProfile', params: { userId: this.props.userId } },
        { key: 'userSettings' },
        { key: 'notifications', params: { limit: 10 } },
      ]);
    } catch (error) {
      this.setState({ error: error.message });
    } finally {
      this.setState({ loading: false });
    }
  };

  // 刷新特定数据
  refreshUserProfile = async () => {
    try {
      await this.globalData?.load('userProfile', {
        userId: this.props.userId,
        fresh: true,
      });
    } catch (error) {
      console.error('刷新用户信息失败:', error);
    }
  };

  // 加载更多通知
  loadMoreNotifications = async () => {
    const currentNotifications = this.globalData?.get('notifications') || [];

    try {
      await this.globalData?.load('notifications', {
        offset: currentNotifications.length,
        limit: 10,
      });
    } catch (error) {
      console.error('加载更多通知失败:', error);
    }
  };

  render() {
    const { loading, error } = this.state;
    const userProfile = this.globalData?.get('userProfile');
    const settings = this.globalData?.get('userSettings');
    const notifications = this.globalData?.get('notifications') || [];

    if (loading && !userProfile) {
      return <div>加载中...</div>;
    }

    if (error) {
      return (
        <div>
          <p>加载失败: {error}</p>
          <button onClick={this.loadInitialData}>重新加载</button>
        </div>
      );
    }

    return (
      <div>
        <div>
          <h3>用户资料</h3>
          <p>姓名: {userProfile?.name}</p>
          <p>邮箱: {userProfile?.email}</p>
          <button onClick={this.refreshUserProfile}>刷新资料</button>
        </div>

        <div>
          <h3>设置</h3>
          <p>主题: {settings?.theme}</p>
          <p>语言: {settings?.language}</p>
        </div>

        <div>
          <h3>通知 ({notifications.length})</h3>
          {notifications.map((notif) => (
            <div key={notif.id}>{notif.message}</div>
          ))}
          <button onClick={this.loadMoreNotifications}>加载更多</button>
        </div>
      </div>
    );
  }
}
```

## 调试和监控

### 自动日志监控

系统会自动在关键操作时输出日志，帮助你监控内存使用情况：

```javascript
// 组件绑定时
[GlobalData] 绑定新组件: component_1703123456789_abc123def
[GlobalData] 组件绑定完成: {componentId: "...", 当前组件总数: 1, 活跃组件数: 1}

// 订阅数据时
[GlobalData] 组件 component_1703123456789_abc123def 订阅了数据: userInfo

// 组件清理时
[GlobalData] 开始清理组件: component_1703123456789_abc123def
[GlobalData] 清理组件 component_1703123456789_abc123def 的订阅: ["userInfo", "settings"]
[GlobalData] 数据 settings 已无订阅者，从订阅列表中移除
[GlobalData] 组件实例 component_1703123456789_abc123def 已从内存中移除
[GlobalData] 组件清理完成: {componentId: "...", 剩余组件总数: 0, 活跃组件数: 0, ...}
```

### 手动状态检查

```javascript
// 随时检查当前内存状态
appHelper.utils.printGlobalDataStats();

// 输出示例:
[GlobalData] 当前内存状态: {
  总组件数: 3,
  活跃组件数: 3,
  非活跃组件数: 0,
  总订阅数: 5,
  详细订阅情况: {
    "userInfo": ["component_123", "component_456"],
    "settings": ["component_123"],
    "notifications": ["component_789"]
  },
  时间戳: "2024/1/1 下午2:30:45"
}
```

### 内存泄漏警告

系统会自动检测并警告潜在的内存问题：

```javascript
// 发现非活跃组件时
⚠️ [GlobalData] 发现 2 个非活跃组件，可能存在内存泄漏风险

// 重复解绑时
⚠️ [GlobalData] 试图解绑未绑定的组件，可能重复调用了 unbindGlobalData

// 组件未正确使用 globalData 时
⚠️ [GlobalData] 有组件实例但无订阅，可能存在未正确使用 globalData 的组件
```

### 订阅状态监控组件

```javascript
export class GlobalDataDebugPanel extends Component {
  state = { stats: null };

  componentDidMount() {
    window.appHelper?.utils?.bindGlobalData(this);
    this.timer = setInterval(this.updateStats, 1000);
  }

  componentWillUnmount() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  }

  updateStats = () => {
    const dataSourceEngine = window.appHelper?.utils?.dataSourceEngine;
    if (dataSourceEngine?.getSubscriptionStats) {
      this.setState({
        stats: dataSourceEngine.getSubscriptionStats(),
      });
    }
  };

  render() {
    const { stats } = this.state;

    return (
      <div
        style={{
          position: 'fixed',
          top: 10,
          right: 10,
          background: '#f0f0f0',
          padding: '10px',
          borderRadius: '5px',
          fontSize: '12px',
        }}
      >
        <h4>GlobalData 状态监控</h4>
        {stats && (
          <div>
            <div>总组件数: {stats.totalComponents}</div>
            <div>活跃组件数: {stats.activeComponents}</div>
            <div>订阅数: {stats.totalSubscriptions}</div>
          </div>
        )}
        <button onClick={this.forceReload}>强制重新加载数据源</button>
      </div>
    );
  }

  forceReload = () => {
    window.appHelper?.utils?.reloadGlobalData?.();
  };
}
```

## 实现原理

1. **组件实例管理**: 使用 Map 维护组件实例和其生命周期状态
2. **订阅机制**:
   - `subscriptions`: key -> 组件 ID 列表的映射
   - `reverseSubscriptions`: 组件 ID -> key 列表的映射（用于快速清理）
3. **响应式代理**: `ReactiveGlobalData` 类提供响应式接口
4. **自动清理**: 在组件卸载时自动清理所有相关订阅

## 性能优势

1. **精确更新**: 只有真正需要的组件才会重新渲染
2. **内存安全**: 自动清理避免内存泄漏
3. **批量操作**: 支持一次性订阅多个数据
4. **条件订阅**: 可以根据组件状态动态订阅/取消订阅

## 注意事项

1. 确保在组件卸载时正确清理订阅（装饰器会自动处理）
2. 避免在 render 方法中进行不必要的数据获取
3. 对于频繁变化的数据，考虑使用条件订阅优化性能
4. 使用调试面板监控订阅状态，及时发现性能问题

## 与其他方案对比

| 特性     | bindGlobalData | Redux | MobX | Context |
| -------- | -------------- | ----- | ---- | ------- |
| 学习成本 | 低             | 中    | 中   | 低      |
| 精确更新 | ✅             | ❌    | ✅   | ❌      |
| 自动清理 | ✅             | ❌    | ✅   | ❌      |
| 类型安全 | ✅             | ✅    | ✅   | ✅      |
| 调试支持 | ✅             | ✅    | ✅   | ❌      |
| 包大小   | 最小           | 大    | 中   | 无      |
