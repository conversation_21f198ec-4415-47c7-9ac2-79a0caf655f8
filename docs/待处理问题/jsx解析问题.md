# JSX 解析问题

## PropValue Object 含有 Function

```jsx
import { ECharts } from '@appthen/echarts';
import { View, Component } from '@appthen/react';
import './EnvironmentalChangeCurve.css';

/*
 * 数据与接口请求定义
 */
class IState {
  temperatureData: number[];
  humidityData: number[];
}

class Document extends React.Component {
  state = {
    temperatureData: [
      22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19,
    ],
    humidityData: [
      45, 46, 47, 48, 49, 50, 52, 55, 58, 60, 62, 60, 58, 55, 52, 50, 49, 48, 47, 46, 45, 44, 43, 42,
    ],
  };

  render() {
    return (
      <React.Fragment fill={true}>
        <View className="flex-1 flex flex-col">
          <ECharts
            advanced={false}
            title={{ text: '', show: true }}
            tooltip={{
              trigger: 'axis',
              formatter: function (params) {
                return `时间: ${params[0].name}<br/>
                        温度: ${params[0].value}°C<br/>
                        湿度: ${params[1].value}%`;
              },
            }}
            legend={{
              show: true,
              data: ['温度', '湿度'],
              top: 30,
              textStyle: {
                color: '#F0F0F0',
              },
            }}
            xAxis={{
              type: 'category',
              name: '时间(小时)',
              show: true,
              data: Array.from(
                {
                  length: 24,
                },
                function (_, i) {
                  return `${i}:00`;
                },
              ),
              axisLabel: {
                interval: 0,
                rotate: 45,
                color: '#F0F0F0',
              },
              axisLine: {
                lineStyle: {
                  color: '#F0F0F0',
                },
              },
            }}
            yAxis={[
              {
                type: 'value',
                name: '温度(°C)',
                show: true,
                min: 15,
                max: 35,
                axisLabel: {
                  color: '#F0F0F0',
                },
                axisLine: {
                  lineStyle: {
                    color: '#F0F0F0',
                  },
                },
              },
              {
                type: 'value',
                name: '湿度(%)',
                show: true,
                min: 40,
                max: 65,
                axisLabel: {
                  color: '#F0F0F0',
                },
                axisLine: {
                  lineStyle: {
                    color: '#F0F0F0',
                  },
                },
              },
            ]}
            series={[
              {
                name: '温度',
                type: 'line',
                data: this.state.temperatureData,
                smooth: true,
                symbol: 'circle',
                symbolSize: 8,
                lineStyle: {
                  width: 3,
                  color: '#7EC0EE',
                },
                itemStyle: {
                  color: '#7EC0EE',
                },
              },
              {
                name: '湿度',
                type: 'line',
                yAxisIndex: 1,
                data: this.state.humidityData,
                smooth: true,
                symbol: 'circle',
                symbolSize: 8,
                lineStyle: {
                  width: 3,
                  color: '#36A2EB',
                },
                itemStyle: {
                  color: '#36A2EB',
                },
              },
            ]}
            backgroundColor="#1E1E2D"
            textStyle={{
              color: '#F0F0F0',
            }}
            className="flex-1"
          />
        </View>
      </React.Fragment>
    );
  }
}
```

问题 JSX 片段：

```
data: Array.from(
  {
    length: 24,
  },
  function (_, i) {
    return `${i}:00`;
  }
),
```

解析结果：

```
Array.from({ length: 24 }, )
```

问题预估: 缺少对这种情况的处理，默认转为空字符串了
