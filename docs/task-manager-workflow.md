# TaskManager 工作流程模拟运行文档

本文档通过模拟运行的方式，详细说明 TaskManager 作为 AI 代码助手的工作流程，帮助理解系统的执行机制。

## 1. 基础概念

在深入了解执行流程之前，我们先了解几个核心概念：

### TaskContext

```typescript
interface TaskContext {
  taskId: string; // 任务唯一标识
  taskType: string; // 任务类型（如：'code_analysis', 'file_modification', 'code_generation'等）
  startTime: number; // 开始时间
  status: 'pending' | 'running' | 'completed' | 'failed'; // 任务状态
  progress: number; // 进度
  retries: number; // 重试次数
  input: any; // 输入数据
  output?: any; // 输出数据
  error?: Error; // 错误信息
  cached?: boolean; // 是否已缓存
  parentTaskId?: string; // 父任务ID，用于任务链追踪
}
```

### TaskProcessor

```typescript
interface TaskProcessor {
  analyze: (userInput: string) => Promise<TaskContext[]>; // 分析用户输入，生成任务列表
  prepare: (context: TaskContext) => Promise<void>; // 准备阶段
  execute: (context: TaskContext) => Promise<void>; // 执行阶段
  cleanup: (context: TaskContext) => Promise<void>; // 清理阶段
}
```

### ServiceComponents

```typescript
interface ServiceComponents {
  // 用户界面服务 - 处理用户输入和界面更新
  UIService: {
    handleUserInput: (input: string) => Promise<void>; // 处理用户输入
    updateUI: (changes: UIChange[]) => Promise<void>; // 更新界面
    showProgress: (progress: number) => void; // 显示进度
    showError: (error: Error) => void; // 显示错误
  };

  // 意图理解服务 - 分析用户意图
  IntentService: {
    analyzeIntent: (input: string) => Promise<Intent>; // 分析用户意图
    extractContext: (input: string) => Promise<Context>; // 提取上下文信息
  };

  // 任务规划服务 - 生成任务计划
  PlanningService: {
    generatePlan: (intent: Intent) => Promise<TaskContext[]>; // 生成任务计划
    optimizePlan: (tasks: TaskContext[]) => Promise<TaskContext[]>; // 优化任务计划
  };

  // 任务执行服务 - 执行具体任务
  ExecutionService: {
    executeTask: (task: TaskContext) => Promise<void>; // 执行任务
    monitorProgress: (taskId: string) => Promise<number>; // 监控进度
  };
}
```

## 2. 模拟运行示例

让我们通过一个用户请求的完整生命周期来理解整个执行流程。

### 2.1 用户界面交互

```typescript
// 1. 用户在界面输入请求
const userInput = '帮我重构这个函数，使其更具可读性和可维护性';

// 2. UI服务处理输入
await uiService.handleUserInput(userInput);

// 3. 显示处理中状态
await uiService.updateUI([
  {
    type: 'status',
    value: 'processing',
  },
]);
```

### 2.2 意图理解阶段

```typescript
// 1. 分析用户意图
const intent = await intentService.analyzeIntent(userInput);
// 输出示例：
// {
//   type: 'code_refactor',
//   target: 'current_function',
//   goals: ['readability', 'maintainability']
// }

// 2. 提取上下文信息
const context = await intentService.extractContext(userInput);
// 输出示例：
// {
//   currentFile: 'src/components/Button.tsx',
//   selection: { start: 100, end: 200 },
//   language: 'typescript'
// }
```

### 2.3 任务规划阶段

```typescript
// 1. 生成初始任务计划
const tasks = await planningService.generatePlan(intent);
// 生成的任务列表：
// 1. 代码分析任务 - 分析当前函数的结构和上下文
// 2. 重构建议生成任务 - 基于分析结果生成重构建议
// 3. 代码修改任务 - 执行实际的代码修改
// 4. UI更新任务 - 在界面上展示修改结果

// 2. 优化任务计划
const optimizedTasks = await planningService.optimizePlan(tasks);
```

### 2.4 任务执行流程

#### 准备阶段 (prepare)

```typescript
try {
  // 收集当前文件上下文
  const fileContext = await this.config.context.getCurrentFileContext();
  // 获取相关文件和依赖
  const projectContext = await this.config.context.getProjectContext();
  // 分析代码结构
  const codeAnalysis = await this.config.tools.analyzer.analyzeCode(fileContext.content);

  context.input = {
    fileContext,
    projectContext,
    codeAnalysis,
  };
  context.progress = 0.3;

  // 更新UI进度
  await uiService.showProgress(context.progress);
} catch (error) {
  this.handleTaskError(context, error as Error);
}
```

#### 执行阶段 (execute)

```typescript
try {
  const { fileContext, codeAnalysis } = context.input;

  // 根据任务类型执行不同的操作
  switch (context.taskType) {
    case 'code_analysis':
      context.output = await this.analyzeCode(fileContext, codeAnalysis);
      break;
    case 'file_modification':
      context.output = await this.modifyFile(fileContext, context.input.changes);
      // 实时更新UI预览
      await uiService.updateUI([
        {
          type: 'preview',
          content: context.output.preview,
        },
      ]);
      break;
    case 'code_generation':
      context.output = await this.generateCode(context.input.requirements);
      break;
  }

  context.progress = 0.8;
  await uiService.showProgress(context.progress);
} catch (error) {
  this.handleTaskError(context, error as Error);
}
```

#### 清理阶段 (cleanup)

```typescript
try {
  // 更新文件系统
  if (context.output?.fileChanges) {
    await this.applyFileChanges(context.output.fileChanges);
  }

  // 缓存任务结果
  if (context.output) {
    this.cacheTaskResult(context.taskId, context.output);
  }

  context.progress = 1.0;

  // 更新UI状态
  await uiService.updateUI([
    {
      type: 'status',
      value: 'completed',
    },
  ]);

  // 显示完成提示
  await uiService.showSuccess('重构完成');
} catch (error) {
  this.handleTaskError(context, error as Error);
}
```

### 2.5 错误处理流程

```typescript
private async handleTaskError(task: TaskContext, error: Error): Promise<void> {
  task.error = error;

  // 记录错误信息
  await this.logger.error(`Task ${task.taskId} failed:`, error);

  if (task.retries < this.maxRetries) {
    // 重试逻辑
    task.retries++;
    task.status = 'pending';
    this.moveTaskToPending(task.taskId);

    // 更新UI状态
    await uiService.updateUI([{
      type: 'status',
      value: 'retrying'
    }]);
  } else {
    // 失败处理
    task.status = 'failed';
    this.moveTaskToFailed(task.taskId);

    // 通知用户
    await uiService.showError(error);
  }
}
```

## 3. 任务队列管理

```typescript
private async processQueue(): Promise<void> {
  if (this.queue.running.length >= this.maxConcurrent) {
    return;
  }

  // 优先处理高优先级任务
  const pendingTasks = this.queue.pending
    .sort((a, b) => b.priority - a.priority);

  while (pendingTasks.length > 0 &&
         this.queue.running.length < this.maxConcurrent) {
    const task = pendingTasks.shift();
    if (task) {
      this.queue.running.push(task);
      this.executeTask(task).catch(console.error);

      // 更新UI任务状态
      await uiService.updateUI([{
        type: 'task_status',
        taskId: task.taskId,
        status: 'running'
      }]);
    }
  }
}
```

## 4. 上下文管理

```typescript
class ContextManager {
  // 项目级上下文缓存
  private projectContext: Map<string, any>;
  // 文件级上下文缓存
  private fileContext: Map<string, any>;
  // UI上下文缓存
  private uiContext: Map<string, any>;

  async getProjectContext(): Promise<any> {
    // 获取项目结构、依赖关系等信息
  }

  async getFileContext(filePath: string): Promise<any> {
    // 获取特定文件的上下文信息
  }

  async getUIContext(): Promise<any> {
    // 获取当前UI状态信息
  }

  async updateContext(changes: any): Promise<void> {
    // 更新上下文信息
    // 同步更新UI
    await uiService.updateUI([
      {
        type: 'context_update',
        changes: changes,
      },
    ]);
  }
}
```

## 总结

TaskManager 通过以下机制实现了类似 Cursor 的 AI 代码助手功能：

1. 智能任务分析

   - 理解用户输入
   - 将复杂需求拆分为子任务
   - 建立任务依赖关系

2. 上下文感知

   - 维护项目级上下文
   - 追踪文件变更
   - 理解代码依赖
   - 同步 UI 状态

3. 任务执行管理

   - 异步任务处理
   - 优先级队列
   - 错误恢复机制
   - 实时 UI 更新

4. 反馈机制
   - 实时进度更新
   - 错误通知
   - 结果展示
   - 交互式预览

这种设计使得系统能够：

- 理解并执行复杂的代码相关任务
- 维护连贯的对话上下文
- 提供智能的代码建议和修改
- 处理各种异常情况
- 提供流畅的用户体验
- 实时反馈执行状态
