# 数据模型系统升级文档

## 1. 概述

本次升级主要针对数据模型系统（`ITypeEntity`）进行了功能扩展，特别是在数据库建模（`type: 'db'`）方面增加了更多特性支持。这些升级旨在提供更强大的数据模型定义能力，同时保持与多数据库（PostgreSQL、MySQL、MongoDB）的兼容性。

## 2. 主要升级内容

### 2.1 数据库表配置

```typescript
// 数据库表配置
db?: {
  // 表名
  name?: string;
  // 数据库名
  database?: string;
  // 表注释
  comment?: string;
  // 表引擎（MySQL）
  engine?: string;
  // 字符集
  charset?: string;
  // 排序规则
  collation?: string;
  // 是否同步到数据库
  synchronize?: boolean;
  // 是否自动生成主键
  autoIncrement?: boolean;
  // 主键类型
  primaryKeyType?: 'uuid' | 'increment' | 'rowid';
  // 主键名称
  primaryKeyName?: string;
  // 是否使用软删除
  softDelete?: boolean;
  // 软删除字段名
  softDeleteColumn?: string;
  // 是否使用时间戳
  timestamps?: boolean;
  // 创建时间字段名
  createdAtColumn?: string;
  // 更新时间字段名
  updatedAtColumn?: string;
  // 是否使用版本控制
  versioning?: boolean;
  // 版本字段名
  versionColumn?: string;
};
```

### 2.2 字段类型扩展

```typescript
// 在 ITypeEntityField 中添加
db?: {
  // 数据库字段类型
  type?: 'varchar' | 'char' | 'text' | 'int' | 'bigint' | 'float' | 'double' | 'decimal' | 'boolean' | 'date' | 'datetime' | 'timestamp' | 'json' | 'jsonb' | 'array' | 'simple-array' | 'uuid';
  // 字段长度
  length?: number;
  // 字段精度（用于decimal类型）
  precision?: number;
  // 字段小数位数（用于decimal类型）
  scale?: number;
  // 是否可为空
  nullable?: boolean;
  // 默认值
  default?: any;
  // 字段注释
  comment?: string;
  // 是否唯一
  unique?: boolean;
  // 是否自增
  isGenerated?: boolean;
  // 生成策略
  generationStrategy?: 'increment' | 'uuid' | 'rowid';
  // 是否为主键
  isPrimary?: boolean;
  // 是否为外键
  isForeign?: boolean;
  // 外键引用
  foreignKey?: {
    table: string;
    column: string;
    onDelete?: 'CASCADE' | 'SET NULL' | 'RESTRICT' | 'NO ACTION';
    onUpdate?: 'CASCADE' | 'SET NULL' | 'RESTRICT' | 'NO ACTION';
  };
  // 是否使用软删除
  isSoftDelete?: boolean;
  // 是否使用时间戳
  isTimestamp?: boolean;
  // 是否使用版本控制
  isVersion?: boolean;
};
```

### 2.3 数据库迁移支持

```typescript
migrations?: {
  version: number;    // 迁移版本号
  up: string;        // 升级脚本
  down: string;      // 回滚脚本
}[];
```

- 支持数据库版本控制
- 提供升级和回滚机制
- 便于数据库结构的演进管理

### 2.4 数据访问控制

```typescript
access?: {
  enabled?: boolean;  // 是否启用访问控制

  // 数据操作拦截器
  interceptors?: {
    select?: { ... };  // 查询拦截
    insert?: { ... };  // 插入拦截
    update?: { ... };  // 更新拦截
    delete?: { ... };  // 删除拦截
  };

  // 数据验证规则
  validations?: {
    fields?: { ... };  // 字段级验证
    entity?: { ... };  // 实体级验证
  };

  // 数据转换器
  transformers?: {
    beforeWrite?: { ... };  // 写入前转换
    afterRead?: { ... };    // 读取后转换
  };
};
```

### 2.5 字段关系定义

```typescript
relation?: {
  type: 'one-to-one' | 'one-to-many' | 'many-to-many';
  target: string;      // 关联的目标表
  foreignKey: string;  // 外键字段
};
```

### 2.6 数据库约束

```typescript
constraints?: {
  check?: string;      // 检查约束
  default?: any;       // 默认值
  references?: {       // 外键引用
    table: string;
    column: string;
  };
};
```

### 2.7 索引支持

```typescript
indexes?: {
  type: 'btree' | 'hash' | 'gist' | 'gin';
  unique?: boolean;
  name?: string;
}[];
```

## 3. 使用示例

### 3.1 基础数据模型定义

```typescript
const userModel: ITypeEntity = {
  name: 'User',
  type: 'db',
  desc: '用户信息',
  db: {
    name: 'sys_user',
    database: 'postgres',
    comment: '系统用户表',
    synchronize: true,
    timestamps: true,
    softDelete: true,
  },
  fields: [
    {
      name: 'id',
      type: 'string',
      required: true,
      db: {
        type: 'uuid',
        isPrimary: true,
        isGenerated: true,
        generationStrategy: 'uuid',
      },
    },
    {
      name: 'username',
      type: 'string',
      required: true,
      db: {
        type: 'varchar',
        length: 50,
        nullable: true,
        comment: '用户名',
        unique: true,
      },
    },
    {
      name: 'password',
      type: 'string',
      required: true,
      db: {
        type: 'varchar',
        length: 100,
        nullable: true,
        comment: '密码',
      },
    },
  ],
};
```

### 3.2 关系定义示例

```typescript
const postModel: ITypeEntity = {
  name: 'Post',
  type: 'vo',
  fields: [
    {
      name: 'author',
      type: 'object',
      relation: {
        type: 'many-to-one',
        target: 'User',
        foreignKey: 'author_id',
      },
    },
  ],
};
```

### 3.3 访问控制示例

```typescript
const orderModel: ITypeEntity = {
  name: 'Order',
  type: 'db',
  access: {
    enabled: true,
    interceptors: {
      select: {
        enabled: true,
        condition: "user.id === currentUser.id || currentUser.role === 'admin'",
        message: '无权访问该订单',
      },
    },
  },
};
```

## 4. 技术实现

### 4.1 与 TypeORM 集成

- 利用 TypeORM 的装饰器机制实现数据库表配置
- 使用 TypeORM 的字段类型系统
- 通过 TypeORM 的实体监听器实现数据转换和验证
- 利用 TypeORM 的迁移系统实现数据库版本控制

### 4.2 多数据库支持

- 通过 TypeORM 的数据库适配器支持多种数据库
- 使用数据库无关的配置方式
- 支持数据库特定的功能（如 PostgreSQL 的 GIN 索引）

## 5. 后续优化方向

### 5.1 功能增强

- [ ] 支持更多数据库特定的字段类型
- [ ] 增加数据库表分区支持
- [ ] 提供更丰富的索引类型
- [ ] 支持数据库视图定义
- [ ] 增加存储过程和函数支持

### 5.2 性能优化

- [ ] 优化数据访问控制性能
- [ ] 改进数据验证机制
- [ ] 优化数据转换处理

### 5.3 开发体验

- [ ] 提供更友好的配置界面
- [ ] 增加更多的使用示例
- [ ] 完善错误提示信息

## 6. 注意事项

1. 数据库迁移脚本需要谨慎编写，确保可回滚
2. 访问控制条件需要考虑性能影响
3. 数据验证规则要避免过于复杂
4. 索引定义要考虑实际查询场景

## 7. 参考资源

- TypeORM 文档
- 各数据库官方文档
- 数据模型设计最佳实践

## 8. 更新日志

### v1.0.0 (2024-03-xx)

- 初始版本发布
- 支持基本的数据模型定义
- 实现数据访问控制
- 支持数据库迁移
- 添加关系定义支持
- 实现数据库约束
- 支持索引定义
