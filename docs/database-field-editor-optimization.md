# 数据库字段编辑器优化方案

## 目录

- [概述](#概述)
- [智能默认值建议系统](#智能默认值建议系统)
- [字段类型扩展](#字段类型扩展)
- [数据库类型完善](#数据库类型完善)
- [字段验证规则](#字段验证规则)
- [关系字段配置](#关系字段配置)
- [索引配置增强](#索引配置增强)
- [字段模板预设](#字段模板预设)
- [批量操作功能](#批量操作功能)
- [代码生成预览](#代码生成预览)
- [导入导出功能](#导入导出功能)
- [实施建议](#实施建议)

## 概述

本文档详细说明了数据库字段编辑器的优化方案，主要目的是提升用户体验，增强功能完整性，并提供更智能的配置建议。

## 智能默认值建议系统

### 设计原则

1. **类型优先**：首先根据字段类型提供基础默认值选项
2. **名称匹配**：通过字段名模式进一步细化建议
3. **数据库适配**：考虑不同数据库的语法差异
4. **智能排序**：将最相关的建议排在前面

### 核心实现

```javascript
/**
 * 智能默认值建议系统
 * @param {string} fieldName - 字段名称
 * @param {string} fieldType - 字段类型
 * @param {string} dbType - 数据库类型 (mysql/postgresql/sqlite)
 * @returns {Array} 建议的默认值数组
 */
function getDefaultValueSuggestions(fieldName, fieldType, dbType = 'mysql') {
  // 基础类型建议映射
  const typeBasedSuggestions = {
    string: {
      common: ["''", 'NULL'],
      mysql: ["''", 'NULL'],
      postgresql: ["''", 'NULL'],
      sqlite: ["''", 'NULL'],
    },
    number: {
      common: ['0', '1', 'NULL'],
      mysql: ['0', '1', 'NULL'],
      postgresql: ['0', '1', 'NULL'],
      sqlite: ['0', '1', 'NULL'],
    },
    boolean: {
      common: ['false', 'true'],
      mysql: ['0', '1', 'false', 'true'],
      postgresql: ['false', 'true'],
      sqlite: ['0', '1', 'false', 'true'],
    },
    Date: {
      common: ['CURRENT_TIMESTAMP', 'NULL'],
      mysql: ['CURRENT_TIMESTAMP', 'CURRENT_DATE', 'CURRENT_TIME', 'NULL'],
      postgresql: ['NOW()', 'CURRENT_TIMESTAMP', 'CURRENT_DATE', 'NULL'],
      sqlite: ["datetime('now')", "date('now')", "time('now')", 'NULL'],
    },
    object: {
      common: ['NULL', '{}'],
      mysql: ['NULL', "'{}'"],
      postgresql: ['NULL', "'{}'::jsonb"],
      sqlite: ['NULL', "'{}'"],
    },
    json: {
      common: ['{}', '[]', 'NULL'],
      mysql: ["'{}'", "'[]'", 'NULL'],
      postgresql: ["'{}'::jsonb", "'[]'::jsonb", 'NULL'],
      sqlite: ["'{}'", "'[]'", 'NULL'],
    },
    uuid: {
      common: ['UUID()', 'NULL'],
      mysql: ['UUID()', 'NULL'],
      postgresql: ['gen_random_uuid()', 'uuid_generate_v4()', 'NULL'],
      sqlite: ['NULL'], // SQLite 不原生支持 UUID 函数
    },
  };

  // 字段名模式匹配建议
  const nameBasedSuggestions = {
    // 时间相关字段
    timeFields: {
      patterns: [
        /^create.*time$/i,
        /^created.*at$/i,
        /^add.*time$/i,
        /^update.*time$/i,
        /^updated.*at$/i,
        /^modify.*time$/i,
        /^delete.*time$/i,
        /^deleted.*at$/i,
        /^birth.*date$/i,
        /^start.*time$/i,
        /^end.*time$/i,
      ],
      suggestions: {
        createTime: {
          Date: {
            mysql: ['CURRENT_TIMESTAMP'],
            postgresql: ['NOW()', 'CURRENT_TIMESTAMP'],
            sqlite: ["datetime('now')"],
          },
          string: ['new Date().toISOString()', "''"],
          number: ['Date.now()', '0'],
        },
        updateTime: {
          Date: {
            mysql: ['CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP', 'CURRENT_TIMESTAMP'],
            postgresql: ['NOW()', 'CURRENT_TIMESTAMP'],
            sqlite: ["datetime('now')"],
          },
          string: ['new Date().toISOString()', "''"],
          number: ['Date.now()', '0'],
        },
      },
    },

    // 状态相关字段
    statusFields: {
      patterns: [
        /^status$/i,
        /^state$/i,
        /.*status$/i,
        /.*state$/i,
        /^is[A-Z]/i,
        /^has[A-Z]/i,
        /^can[A-Z]/i,
        /enabled$/i,
        /disabled$/i,
        /active$/i,
        /inactive$/i,
        /deleted$/i,
        /visible$/i,
        /published$/i,
        /approved$/i,
      ],
      suggestions: {
        status: {
          string: ["'active'", "'inactive'", "'pending'", "'draft'", "'published'"],
          number: ['1', '0', '2', '99'],
          boolean: ['true', 'false'],
          enum: ['ACTIVE', 'INACTIVE', 'PENDING'],
        },
        isDeleted: {
          boolean: ['false', 'true'],
          number: ['0', '1'],
          string: ["'0'", "'1'", "'false'", "'true'"],
        },
        isActive: {
          boolean: ['true', 'false'],
          number: ['1', '0'],
          string: ["'1'", "'0'", "'active'", "'inactive'"],
        },
        isEnabled: {
          boolean: ['true', 'false'],
          number: ['1', '0'],
          string: ["'enabled'", "'disabled'"],
        },
        isPublished: {
          boolean: ['false', 'true'],
          number: ['0', '1'],
          string: ["'draft'", "'published'"],
        },
      },
    },

    // ID相关字段
    idFields: {
      patterns: [
        /^id$/i,
        /.*[Ii]d$/i,
        /^.*[Ii]d$/i,
        /^uuid$/i,
        /.*[Uu]uid$/i,
        /^key$/i,
        /.*[Kk]ey$/i,
      ],
      suggestions: {
        id: {
          number: ['AUTO_INCREMENT', '0'],
          string: ['UUID()', "''"],
          uuid: ['UUID()', 'gen_random_uuid()'],
        },
        uuid: {
          string: {
            mysql: ['UUID()', "''"],
            postgresql: ['gen_random_uuid()', 'uuid_generate_v4()', "''"],
            sqlite: ["''"],
          },
          uuid: {
            mysql: ['UUID()'],
            postgresql: ['gen_random_uuid()', 'uuid_generate_v4()'],
            sqlite: ['NULL'],
          },
        },
      },
    },

    // 数值相关字段
    numericFields: {
      patterns: [
        /sort$/i,
        /order$/i,
        /count$/i,
        /amount$/i,
        /price$/i,
        /weight$/i,
        /score$/i,
        /rating$/i,
        /level$/i,
        /rank$/i,
        /quantity$/i,
        /total$/i,
        /sum$/i,
        /avg$/i,
        /max$/i,
        /min$/i,
      ],
      suggestions: {
        sort: {
          number: ['0', '1', '100', '999'],
          string: ["'0'", "'1'"],
        },
        order: {
          number: ['0', '1', '999', '9999'],
          string: ["'0'", "'1'"],
        },
        count: {
          number: ['0', '1'],
          string: ["'0'"],
        },
        amount: {
          number: ['0.00', '0'],
          string: ["'0.00'", "'0'"],
        },
        price: {
          number: ['0.00', '0', '1.00'],
          string: ["'0.00'", "'0'"],
        },
        weight: {
          number: ['0.0', '0', '1.0'],
          string: ["'0.0'"],
        },
        score: {
          number: ['0', '100', '60'],
          string: ["'0'", "'100'"],
        },
        rating: {
          number: ['0', '5', '1'],
          string: ["'0'", "'5'"],
        },
      },
    },

    // 文本相关字段
    textFields: {
      patterns: [
        /name$/i,
        /title$/i,
        /desc/i,
        /content$/i,
        /remark$/i,
        /note$/i,
        /comment$/i,
        /text$/i,
        /label$/i,
        /tag$/i,
        /category$/i,
        /type$/i,
      ],
      suggestions: {
        name: {
          string: ["''", "'未命名'", "'无名称'", 'NULL'],
          object: ['NULL', "'{}'"],
        },
        title: {
          string: ["''", "'无标题'", "'未设置标题'", 'NULL'],
          object: ['NULL'],
        },
        description: {
          string: ["''", "'暂无描述'", 'NULL'],
          object: ['NULL'],
          json: ["'{}'", 'NULL'],
        },
        content: {
          string: ["''", 'NULL'],
          object: ['NULL', "'{}'"],
          json: ["'{}'", "'[]'", 'NULL'],
        },
        remark: {
          string: ["''", "'备注'", 'NULL'],
          object: ['NULL'],
        },
        note: {
          string: ["''", "'注释'", 'NULL'],
          object: ['NULL'],
        },
        comment: {
          string: ["''", "'评论'", 'NULL'],
          object: ['NULL'],
        },
        category: {
          string: ["'default'", "'未分类'", "''", 'NULL'],
          number: ['1', '0'],
        },
        type: {
          string: ["'default'", "'normal'", "''", 'NULL'],
          number: ['1', '0'],
        },
      },
    },
  };

  // 开始匹配逻辑
  let suggestions = [];

  // 1. 获取基础类型建议
  const baseTypeSuggestions = typeBasedSuggestions[fieldType];
  if (baseTypeSuggestions) {
    const dbSpecific = baseTypeSuggestions[dbType] || baseTypeSuggestions.common;
    suggestions = [...dbSpecific];
  }

  // 2. 字段名模式匹配
  const fieldNameLower = fieldName.toLowerCase();

  // 遍历各个字段分类
  Object.values(nameBasedSuggestions).forEach((category) => {
    // 检查字段名是否匹配任何模式
    const matchesPattern = category.patterns.some((pattern) => pattern.test(fieldName));

    if (matchesPattern && category.suggestions) {
      // 查找最具体的匹配
      Object.keys(category.suggestions).forEach((specificField) => {
        if (
          fieldNameLower.includes(specificField.toLowerCase()) ||
          specificField.toLowerCase().includes(fieldNameLower)
        ) {
          const fieldSuggestions = category.suggestions[specificField];
          const typeSuggestions = fieldSuggestions[fieldType];

          if (typeSuggestions) {
            let specificSuggestions = [];

            if (typeof typeSuggestions === 'object' && !Array.isArray(typeSuggestions)) {
              // 数据库特定建议
              specificSuggestions = typeSuggestions[dbType] || typeSuggestions.common || [];
            } else {
              // 通用建议
              specificSuggestions = typeSuggestions;
            }

            // 将特定建议放在前面
            suggestions = [...specificSuggestions, ...suggestions];
          }
        }
      });
    }
  });

  // 3. 去重并限制数量
  const uniqueSuggestions = [...new Set(suggestions)];

  return uniqueSuggestions.slice(0, 8); // 最多返回8个建议
}
```

### 使用示例

```javascript
// 示例 1: 创建时间字段
getDefaultValueSuggestions('createTime', 'Date', 'mysql');
// 返回: ['CURRENT_TIMESTAMP', 'CURRENT_DATE', 'CURRENT_TIME', 'NULL']

// 示例 2: 状态字段
getDefaultValueSuggestions('isActive', 'boolean', 'postgresql');
// 返回: ['true', 'false']

// 示例 3: 名称字段
getDefaultValueSuggestions('userName', 'string', 'mysql');
// 返回: ["''", "'未命名'", "'无名称'", 'NULL']

// 示例 4: 金额字段
getDefaultValueSuggestions('totalAmount', 'number', 'mysql');
// 返回: ['0.00', '0', '0', '1', 'NULL']
```

### 字段名匹配规则

```javascript
/**
 * 获取字段名所属分类
 * @param {string} fieldName - 字段名称
 * @returns {string} 字段分类
 */
function getFieldNameCategory(fieldName) {
  const categories = {
    time: {
      patterns: [
        /time$/i,
        /at$/i,
        /date$/i,
        /created/i,
        /updated/i,
        /modified/i,
        /^create/,
        /^update/,
        /^modify/,
        /birth/i,
        /start/i,
        /end/i,
        /expire/i,
      ],
      weight: 10, // 权重，用于排序
    },
    status: {
      patterns: [
        /status$/i,
        /state$/i,
        /^is[A-Z]/i,
        /enabled$/i,
        /disabled$/i,
        /active$/i,
        /deleted$/i,
        /visible$/i,
        /published$/i,
        /approved$/i,
        /verified$/i,
        /confirmed$/i,
      ],
      weight: 9,
    },
    id: {
      patterns: [
        /^id$/i,
        /Id$/i,
        /^.*id$/i,
        /^uuid$/i,
        /^.*uuid$/i,
        /^key$/i,
        /^.*key$/i,
        /^.*Key$/i,
      ],
      weight: 8,
    },
    numeric: {
      patterns: [
        /count$/i,
        /num$/i,
        /amount$/i,
        /price$/i,
        /sort$/i,
        /order$/i,
        /weight$/i,
        /score$/i,
        /rating$/i,
        /level$/i,
        /rank$/i,
        /quantity$/i,
        /total$/i,
        /sum$/i,
        /avg$/i,
        /max$/i,
        /min$/i,
      ],
      weight: 7,
    },
    text: {
      patterns: [
        /name$/i,
        /title$/i,
        /desc/i,
        /content$/i,
        /remark$/i,
        /note$/i,
        /comment$/i,
        /text$/i,
        /label$/i,
        /tag$/i,
        /category$/i,
        /type$/i,
      ],
      weight: 6,
    },
  };

  const matches = [];

  for (const [category, config] of Object.entries(categories)) {
    const matchCount = config.patterns.filter((regex) => regex.test(fieldName)).length;

    if (matchCount > 0) {
      matches.push({
        category,
        weight: config.weight + matchCount,
        matchCount,
      });
    }
  }

  // 按权重排序，返回最匹配的分类
  matches.sort((a, b) => b.weight - a.weight);

  return matches.length > 0 ? matches[0].category : 'general';
}
```

## 字段类型扩展

### 当前类型

```javascript
const currentTypes = ['string', 'number', 'boolean', 'Date', 'object'];
```

### 扩展类型

```javascript
const extendedTypes = [
  // 基础类型
  'string',
  'number',
  'boolean',
  'Date',
  'object',

  // 扩展基础类型
  'bigint', // 大整数
  'decimal', // 精确小数
  'float', // 浮点数
  'double', // 双精度浮点数

  // 特殊类型
  'uuid', // UUID类型
  'email', // 邮箱类型
  'url', // URL类型
  'phone', // 电话号码
  'json', // JSON对象
  'text', // 长文本
  'richtext', // 富文本
  'markdown', // Markdown文本

  // 数组类型
  'Array<string>', // 字符串数组
  'Array<number>', // 数字数组
  'Array<object>', // 对象数组
  'Array<Date>', // 日期数组

  // 枚举类型
  'enum', // 枚举
  'set', // 集合

  // 地理类型
  'geometry', // 几何类型
  'point', // 点坐标
  'polygon', // 多边形

  // 二进制类型
  'blob', // 二进制大对象
  'buffer', // 缓冲区
  'file', // 文件类型
  'image', // 图片类型

  // 时间类型细分
  'datetime', // 日期时间
  'date', // 仅日期
  'time', // 仅时间
  'timestamp', // 时间戳
  'year', // 年份

  // 特殊数值类型
  'currency', // 货币
  'percentage', // 百分比
  'rating', // 评分
];
```

### 类型分组配置

```javascript
const typeGroups = {
  basic: {
    label: '基础类型',
    types: ['string', 'number', 'boolean', 'Date', 'object'],
  },
  numeric: {
    label: '数值类型',
    types: ['number', 'bigint', 'decimal', 'float', 'double', 'currency', 'percentage', 'rating'],
  },
  text: {
    label: '文本类型',
    types: ['string', 'text', 'richtext', 'markdown', 'email', 'url', 'phone'],
  },
  time: {
    label: '时间类型',
    types: ['Date', 'datetime', 'date', 'time', 'timestamp', 'year'],
  },
  array: {
    label: '数组类型',
    types: ['Array<string>', 'Array<number>', 'Array<object>', 'Array<Date>'],
  },
  special: {
    label: '特殊类型',
    types: ['uuid', 'json', 'enum', 'set', 'geometry', 'point', 'blob', 'file', 'image'],
  },
};
```

## 数据库类型完善

### 按数据库分类的类型映射

```javascript
const dbTypeMapping = {
  mysql: {
    string: {
      primary: ['varchar', 'char', 'text'],
      extended: ['tinytext', 'mediumtext', 'longtext', 'binary', 'varbinary'],
    },
    number: {
      primary: ['int', 'bigint', 'decimal'],
      extended: ['tinyint', 'smallint', 'mediumint', 'float', 'double', 'bit'],
    },
    date: {
      primary: ['datetime', 'timestamp', 'date'],
      extended: ['time', 'year'],
    },
    boolean: {
      primary: ['boolean', 'tinyint(1)'],
      extended: ['bit(1)'],
    },
    json: {
      primary: ['json'],
      extended: ['longtext'], // 作为JSON存储的备选
    },
    binary: {
      primary: ['blob'],
      extended: ['tinyblob', 'mediumblob', 'longblob'],
    },
    other: {
      primary: ['enum', 'set'],
      extended: ['geometry', 'point', 'linestring', 'polygon'],
    },
  },

  postgresql: {
    string: {
      primary: ['varchar', 'char', 'text'],
      extended: ['citext', 'ltree', 'cidr', 'inet', 'macaddr'],
    },
    number: {
      primary: ['integer', 'bigint', 'decimal'],
      extended: ['smallint', 'real', 'double precision', 'numeric', 'serial', 'bigserial'],
    },
    date: {
      primary: ['timestamp', 'timestamptz', 'date'],
      extended: ['time', 'timetz', 'interval'],
    },
    boolean: {
      primary: ['boolean'],
      extended: [],
    },
    json: {
      primary: ['json', 'jsonb'],
      extended: [],
    },
    binary: {
      primary: ['bytea'],
      extended: [],
    },
    array: {
      primary: ['integer[]', 'text[]', 'varchar[]'],
      extended: ['json[]', 'timestamp[]'],
    },
    other: {
      primary: ['uuid', 'xml'],
      extended: ['money', 'point', 'line', 'lseg', 'box', 'path', 'polygon', 'circle'],
    },
  },

  sqlite: {
    string: {
      primary: ['TEXT'],
      extended: ['VARCHAR', 'CHAR'],
    },
    number: {
      primary: ['INTEGER', 'REAL'],
      extended: ['NUMERIC'],
    },
    date: {
      primary: ['TEXT'], // SQLite 将日期存储为文本
      extended: ['INTEGER', 'REAL'], // 或时间戳
    },
    boolean: {
      primary: ['INTEGER'], // 0/1
      extended: ['TEXT'], // 'true'/'false'
    },
    json: {
      primary: ['TEXT'],
      extended: ['JSON'], // SQLite 3.38+
    },
    binary: {
      primary: ['BLOB'],
      extended: [],
    },
  },
};
```

### 类型转换建议

```javascript
/**
 * 根据字段类型推荐数据库类型
 * @param {string} fieldType - 字段类型
 * @param {string} dbType - 数据库类型
 * @returns {Object} 推荐的数据库类型配置
 */
function getRecommendedDbType(fieldType, dbType = 'mysql') {
  const recommendations = {
    mysql: {
      string: {
        default: 'varchar(255)',
        short: 'varchar(50)', // < 50字符
        medium: 'varchar(500)', // 50-500字符
        long: 'text', // > 500字符
        fixed: 'char(10)', // 固定长度
      },
      number: {
        default: 'int',
        small: 'tinyint', // 0-255
        medium: 'int', // -2^31 到 2^31-1
        large: 'bigint', // -2^63 到 2^63-1
        decimal: 'decimal(10,2)', // 精确小数
        float: 'float', // 单精度浮点
        double: 'double', // 双精度浮点
      },
      boolean: {
        default: 'tinyint(1)',
        alternative: 'boolean',
      },
      Date: {
        default: 'datetime',
        dateOnly: 'date',
        timeOnly: 'time',
        timestamp: 'timestamp',
        year: 'year',
      },
      json: {
        default: 'json',
        alternative: 'longtext',
      },
      uuid: {
        default: 'varchar(36)',
        binary: 'binary(16)',
      },
      email: {
        default: 'varchar(255)',
      },
      url: {
        default: 'varchar(2048)',
      },
      phone: {
        default: 'varchar(20)',
      },
      text: {
        default: 'text',
        medium: 'mediumtext',
        long: 'longtext',
      },
      file: {
        default: 'varchar(500)', // 存储文件路径
        blob: 'longblob', // 存储文件内容
      },
    },

    postgresql: {
      string: {
        default: 'varchar(255)',
        unlimited: 'text',
        fixed: 'char(10)',
      },
      number: {
        default: 'integer',
        small: 'smallint',
        large: 'bigint',
        decimal: 'decimal(10,2)',
        float: 'real',
        double: 'double precision',
        serial: 'serial',
        bigserial: 'bigserial',
      },
      boolean: {
        default: 'boolean',
      },
      Date: {
        default: 'timestamp',
        withTimezone: 'timestamptz',
        dateOnly: 'date',
        timeOnly: 'time',
        interval: 'interval',
      },
      json: {
        default: 'jsonb',
        alternative: 'json',
      },
      uuid: {
        default: 'uuid',
      },
      array: {
        stringArray: 'text[]',
        numberArray: 'integer[]',
        jsonArray: 'jsonb[]',
      },
    },

    sqlite: {
      string: {
        default: 'TEXT',
      },
      number: {
        default: 'INTEGER',
        decimal: 'REAL',
      },
      boolean: {
        default: 'INTEGER', // 0/1
      },
      Date: {
        default: 'TEXT', // ISO 8601 format
        timestamp: 'INTEGER',
      },
      json: {
        default: 'TEXT',
      },
    },
  };

  return recommendations[dbType]?.[fieldType] || { default: 'TEXT' };
}
```

## 字段验证规则

### 验证规则配置

```javascript
const validationRules = {
  // 基础验证
  basic: {
    required: {
      type: 'boolean',
      default: false,
      description: '是否必填',
    },
    nullable: {
      type: 'boolean',
      default: true,
      description: '是否允许为空',
    },
  },

  // 字符串验证
  string: {
    minLength: {
      type: 'number',
      default: 0,
      description: '最小长度',
    },
    maxLength: {
      type: 'number',
      default: 255,
      description: '最大长度',
    },
    pattern: {
      type: 'string',
      default: '',
      description: '正则表达式模式',
      examples: [
        '^[a-zA-Z0-9]+$', // 字母数字
        '^[\\u4e00-\\u9fa5]+$', // 中文
        '^1[3-9]\\d{9}$', // 手机号
        '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$', // 邮箱
      ],
    },
    enum: {
      type: 'array',
      default: [],
      description: '枚举值列表',
      examples: [
        ['active', 'inactive', 'pending'],
        ['draft', 'published', 'archived'],
        ['low', 'medium', 'high'],
      ],
    },
  },

  // 数值验证
  number: {
    min: {
      type: 'number',
      default: null,
      description: '最小值',
    },
    max: {
      type: 'number',
      default: null,
      description: '最大值',
    },
    step: {
      type: 'number',
      default: 1,
      description: '步长',
    },
    precision: {
      type: 'number',
      default: 10,
      description: '总位数',
    },
    scale: {
      type: 'number',
      default: 2,
      description: '小数位数',
    },
    positive: {
      type: 'boolean',
      default: false,
      description: '是否必须为正数',
    },
    integer: {
      type: 'boolean',
      default: false,
      description: '是否必须为整数',
    },
  },

  // 日期验证
  date: {
    minDate: {
      type: 'string',
      default: '',
      description: '最小日期',
      format: 'YYYY-MM-DD',
    },
    maxDate: {
      type: 'string',
      default: '',
      description: '最大日期',
      format: 'YYYY-MM-DD',
    },
    format: {
      type: 'string',
      default: 'YYYY-MM-DD HH:mm:ss',
      description: '日期格式',
      examples: ['YYYY-MM-DD', 'YYYY-MM-DD HH:mm:ss', 'MM/DD/YYYY', 'DD/MM/YYYY'],
    },
  },

  // 数组验证
  array: {
    minItems: {
      type: 'number',
      default: 0,
      description: '最少项目数',
    },
    maxItems: {
      type: 'number',
      default: null,
      description: '最多项目数',
    },
    uniqueItems: {
      type: 'boolean',
      default: false,
      description: '项目是否必须唯一',
    },
  },

  // 对象验证
  object: {
    schema: {
      type: 'object',
      default: {},
      description: 'JSON Schema定义',
    },
    additionalProperties: {
      type: 'boolean',
      default: true,
      description: '是否允许额外属性',
    },
  },

  // 文件验证
  file: {
    maxSize: {
      type: 'string',
      default: '10MB',
      description: '最大文件大小',
    },
    allowedTypes: {
      type: 'array',
      default: [],
      description: '允许的文件类型',
      examples: [
        ['image/jpeg', 'image/png', 'image/gif'],
        ['application/pdf', 'application/msword'],
        ['text/plain', 'text/csv'],
      ],
    },
    allowedExtensions: {
      type: 'array',
      default: [],
      description: '允许的文件扩展名',
      examples: [
        ['.jpg', '.jpeg', '.png', '.gif'],
        ['.pdf', '.doc', '.docx'],
        ['.txt', '.csv', '.json'],
      ],
    },
  },
};
```

### 验证规则模板

```javascript
const validationTemplates = {
  // 常用字段验证模板
  email: {
    type: 'string',
    pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
    maxLength: 255,
    errorMessage: '请输入有效的邮箱地址',
  },

  phone: {
    type: 'string',
    pattern: '^1[3-9]\\d{9}$',
    maxLength: 11,
    errorMessage: '请输入有效的手机号码',
  },

  password: {
    type: 'string',
    minLength: 8,
    maxLength: 128,
    pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$',
    errorMessage: '密码至少8位，包含大小写字母和数字',
  },

  url: {
    type: 'string',
    pattern:
      '^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$',
    maxLength: 2048,
    errorMessage: '请输入有效的URL地址',
  },

  idCard: {
    type: 'string',
    pattern:
      '^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$',
    length: 18,
    errorMessage: '请输入有效的身份证号码',
  },

  bankCard: {
    type: 'string',
    pattern: '^[1-9]\\d{12,19}$',
    minLength: 13,
    maxLength: 20,
    errorMessage: '请输入有效的银行卡号',
  },

  ipAddress: {
    type: 'string',
    pattern:
      '^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$',
    errorMessage: '请输入有效的IP地址',
  },

  username: {
    type: 'string',
    pattern: '^[a-zA-Z0-9_]{3,20}$',
    minLength: 3,
    maxLength: 20,
    errorMessage: '用户名只能包含字母、数字和下划线，长度3-20位',
  },

  amount: {
    type: 'number',
    min: 0,
    precision: 10,
    scale: 2,
    positive: true,
    errorMessage: '请输入有效的金额',
  },

  percentage: {
    type: 'number',
    min: 0,
    max: 100,
    scale: 2,
    errorMessage: '百分比必须在0-100之间',
  },

  age: {
    type: 'number',
    min: 0,
    max: 150,
    integer: true,
    errorMessage: '年龄必须在0-150之间的整数',
  },
};
```

## 关系字段配置

### 关系类型定义

```javascript
const relationTypes = {
  none: {
    label: '无关系',
    description: '独立字段，不与其他实体关联',
  },
  oneToOne: {
    label: '一对一',
    description: '当前实体的一个记录对应目标实体的一个记录',
    decorators: ['@OneToOne', '@JoinColumn'],
    example: 'User -> Profile',
  },
  oneToMany: {
    label: '一对多',
    description: '当前实体的一个记录对应目标实体的多个记录',
    decorators: ['@OneToMany'],
    example: 'User -> Orders',
  },
  manyToOne: {
    label: '多对一',
    description: '当前实体的多个记录对应目标实体的一个记录',
    decorators: ['@ManyToOne', '@JoinColumn'],
    example: 'Orders -> User',
  },
  manyToMany: {
    label: '多对多',
    description: '当前实体的多个记录对应目标实体的多个记录',
    decorators: ['@ManyToMany', '@JoinTable'],
    example: 'Users -> Roles',
  },
};
```

### 关系配置选项

```javascript
const relationConfig = {
  // 基础配置
  basic: {
    type: {
      type: 'select',
      options: Object.keys(relationTypes),
      default: 'none',
      description: '关系类型',
    },
    targetEntity: {
      type: 'select',
      options: [], // 动态加载可用实体
      default: '',
      description: '目标实体',
      required: true,
      condition: 'type !== "none"',
    },
    targetField: {
      type: 'select',
      options: [], // 根据目标实体动态加载字段
      default: 'id',
      description: '目标字段',
    },
  },

  // 外键配置
  foreignKey: {
    name: {
      type: 'string',
      default: '',
      description: '外键字段名',
      placeholder: '默认为 {targetEntity}Id',
    },
    referencedColumnName: {
      type: 'string',
      default: 'id',
      description: '引用的目标字段名',
    },
    onDelete: {
      type: 'select',
      options: ['RESTRICT', 'CASCADE', 'SET NULL', 'SET DEFAULT', 'NO ACTION'],
      default: 'RESTRICT',
      description: '删除时的操作',
    },
    onUpdate: {
      type: 'select',
      options: ['RESTRICT', 'CASCADE', 'SET NULL', 'SET DEFAULT', 'NO ACTION'],
      default: 'CASCADE',
      description: '更新时的操作',
    },
  },

  // 中间表配置（多对多）
  joinTable: {
    name: {
      type: 'string',
      default: '',
      description: '中间表名',
      placeholder: '默认为 {entity1}_{entity2}',
    },
    joinColumn: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          description: '当前实体的连接字段名',
        },
        referencedColumnName: {
          type: 'string',
          default: 'id',
          description: '当前实体的引用字段名',
        },
      },
    },
    inverseJoinColumn: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          description: '目标实体的连接字段名',
        },
        referencedColumnName: {
          type: 'string',
          default: 'id',
          description: '目标实体的引用字段名',
        },
      },
    },
  },

  // 加载配置
  loading: {
    eager: {
      type: 'boolean',
      default: false,
      description: '是否立即加载关联数据',
    },
    lazy: {
      type: 'boolean',
      default: true,
      description: '是否懒加载关联数据',
    },
    cascade: {
      type: 'multiselect',
      options: ['insert', 'update', 'remove', 'soft-remove', 'recover'],
      default: [],
      description: '级联操作类型',
    },
  },
};
```

### 关系配置示例

```javascript
const relationExamples = {
  userProfile: {
    name: 'profile',
    type: 'oneToOne',
    targetEntity: 'Profile',
    targetField: 'id',
    joinColumn: {
      name: 'profileId',
      referencedColumnName: 'id',
    },
    cascade: ['insert', 'update'],
    eager: false,
  },

  userOrders: {
    name: 'orders',
    type: 'oneToMany',
    targetEntity: 'Order',
    mappedBy: 'user',
    cascade: ['insert', 'update', 'remove'],
    eager: false,
  },

  orderUser: {
    name: 'user',
    type: 'manyToOne',
    targetEntity: 'User',
    joinColumn: {
      name: 'userId',
      referencedColumnName: 'id',
    },
    onDelete: 'CASCADE',
    eager: true,
  },

  userRoles: {
    name: 'roles',
    type: 'manyToMany',
    targetEntity: 'Role',
    joinTable: {
      name: 'user_roles',
      joinColumn: {
        name: 'userId',
        referencedColumnName: 'id',
      },
      inverseJoinColumn: {
        name: 'roleId',
        referencedColumnName: 'id',
      },
    },
    cascade: ['insert'],
    eager: false,
  },
};
```

## 索引配置增强

### 索引类型扩展

```javascript
const indexTypes = {
  // 基础索引类型
  index: {
    label: '普通索引',
    description: '提高查询性能的基础索引',
    mysql: 'INDEX',
    postgresql: 'INDEX',
    sqlite: 'INDEX',
  },
  unique: {
    label: '唯一索引',
    description: '确保字段值唯一性的索引',
    mysql: 'UNIQUE INDEX',
    postgresql: 'UNIQUE INDEX',
    sqlite: 'UNIQUE INDEX',
  },
  primary: {
    label: '主键索引',
    description: '主键约束，自动创建唯一索引',
    mysql: 'PRIMARY KEY',
    postgresql: 'PRIMARY KEY',
    sqlite: 'PRIMARY KEY',
  },
  fulltext: {
    label: '全文索引',
    description: '用于全文搜索的索引',
    mysql: 'FULLTEXT INDEX',
    postgresql: 'GIN INDEX', // 使用 GIN 索引配合 tsvector
    sqlite: 'FTS INDEX', // SQLite FTS
  },
  spatial: {
    label: '空间索引',
    description: '用于地理空间数据的索引',
    mysql: 'SPATIAL INDEX',
    postgresql: 'GIST INDEX',
    sqlite: 'RTREE INDEX',
  },
  composite: {
    label: '复合索引',
    description: '多个字段组合的索引',
    mysql: 'INDEX',
    postgresql: 'INDEX',
    sqlite: 'INDEX',
  },
  partial: {
    label: '部分索引',
    description: '带条件的索引',
    mysql: null, // MySQL 不支持
    postgresql: 'INDEX WHERE',
    sqlite: 'INDEX WHERE',
  },
  expression: {
    label: '表达式索引',
    description: '基于表达式的索引',
    mysql: null, // MySQL 8.0+ 支持函数索引
    postgresql: 'INDEX ON expression',
    sqlite: null,
  },
};
```

### 索引高级配置

```javascript
const indexAdvancedConfig = {
  // 索引算法
  algorithm: {
    mysql: {
      options: ['BTREE', 'HASH'],
      default: 'BTREE',
      description: 'MySQL索引算法',
    },
    postgresql: {
      options: ['btree', 'hash', 'gist', 'spgist', 'gin', 'brin'],
      default: 'btree',
      description: 'PostgreSQL索引算法',
    },
    sqlite: {
      options: ['BTREE'], // SQLite 主要使用 B-tree
      default: 'BTREE',
      description: 'SQLite索引算法',
    },
  },

  // 锁定选项
  lockOption: {
    mysql: {
      options: ['DEFAULT', 'NONE', 'SHARED', 'EXCLUSIVE'],
      default: 'DEFAULT',
      description: 'MySQL索引锁定选项',
    },
    postgresql: {
      options: ['CONCURRENTLY'],
      default: '',
      description: 'PostgreSQL并发创建索引',
    },
  },

  // 存储参数
  storage: {
    mysql: {
      keyBlockSize: {
        type: 'number',
        default: null,
        description: '键块大小',
      },
      parser: {
        type: 'string',
        default: '',
        description: '全文索引解析器',
      },
    },
    postgresql: {
      fillfactor: {
        type: 'number',
        min: 10,
        max: 100,
        default: 90,
        description: '填充因子',
      },
      fastupdate: {
        type: 'boolean',
        default: true,
        description: 'GIN索引快速更新',
      },
    },
  },

  // 条件索引
  condition: {
    where: {
      type: 'string',
      default: '',
      description: 'WHERE条件表达式',
      examples: [
        'status = "active"',
        'deleted_at IS NULL',
        'price > 0',
        'created_at > "2023-01-01"',
      ],
    },
  },

  // 索引属性
  properties: {
    visible: {
      type: 'boolean',
      default: true,
      description: '索引可见性（MySQL 8.0+）',
    },
    comment: {
      type: 'string',
      default: '',
      description: '索引注释',
    },
    concurrent: {
      type: 'boolean',
      default: false,
      description: '并发创建索引（PostgreSQL）',
    },
  },
};
```

### 索引性能分析

```javascript
const indexPerformanceGuidelines = {
  // 索引建议
  recommendations: {
    highSelectivity: {
      description: '高选择性字段适合建索引',
      criteria: '字段唯一值比例 > 80%',
      examples: ['id', 'email', 'phone', 'uuid'],
    },
    frequentWhere: {
      description: '经常用于WHERE条件的字段',
      criteria: '查询频率高的过滤字段',
      examples: ['status', 'type', 'category_id', 'user_id'],
    },
    orderBy: {
      description: '经常用于排序的字段',
      criteria: 'ORDER BY 子句中的字段',
      examples: ['created_at', 'updated_at', 'sort_order'],
    },
    joinColumns: {
      description: '连接查询中的字段',
      criteria: 'JOIN 条件中的字段',
      examples: ['user_id', 'category_id', 'parent_id'],
    },
  },

  // 索引反模式
  antiPatterns: {
    lowSelectivity: {
      description: '低选择性字段不适合建索引',
      criteria: '字段唯一值比例 < 20%',
      examples: ['gender', 'boolean类型字段'],
    },
    tooManyIndexes: {
      description: '过多索引影响写入性能',
      criteria: '单表索引数量 > 5个',
      recommendation: '合并相关索引为复合索引',
    },
    wideIndexes: {
      description: '过宽的复合索引',
      criteria: '复合索引字段数 > 3个',
      recommendation: '重新评估索引必要性',
    },
    redundantIndexes: {
      description: '冗余索引',
      criteria: '存在包含关系的索引',
      example: '(a) 和 (a,b) 索引冗余',
    },
  },

  // 索引维护
  maintenance: {
    monitoring: {
      description: '索引使用情况监控',
      metrics: ['查询频率', '索引命中率', '维护成本'],
      tools: ['EXPLAIN', 'performance_schema', 'pg_stat_user_indexes'],
    },
    optimization: {
      description: '索引优化建议',
      actions: ['删除未使用索引', '合并相似索引', '调整索引顺序'],
      frequency: '定期分析和优化',
    },
  },
};
```

## 字段模板预设

### 常用字段模板

```javascript
const fieldTemplates = {
  // 基础字段模板
  basic: [
    {
      name: '主键ID',
      icon: 'key',
      template: {
        name: 'id',
        desc: '主键ID，自增',
        type: 'number',
        required: true,
        isArray: false,
        db: {
          type: 'bigint',
          isPrimary: true,
          isGenerated: true,
          nullable: false,
          comment: '主键ID',
        },
      },
    },
    {
      name: 'UUID主键',
      icon: 'key',
      template: {
        name: 'id',
        desc: 'UUID主键',
        type: 'uuid',
        required: true,
        isArray: false,
        db: {
          type: 'varchar',
          length: 36,
          isPrimary: true,
          nullable: false,
          default: 'UUID()',
          comment: 'UUID主键',
        },
      },
    },
  ],

  // 时间字段模板
  timestamp: [
    {
      name: '创建时间',
      icon: 'clock',
      template: {
        name: 'createTime',
        desc: '记录创建时间',
        type: 'Date',
        required: true,
        isArray: false,
        db: {
          type: 'datetime',
          nullable: false,
          default: 'CURRENT_TIMESTAMP',
          comment: '创建时间',
        },
      },
    },
    {
      name: '更新时间',
      icon: 'clock',
      template: {
        name: 'updateTime',
        desc: '记录更新时间',
        type: 'Date',
        required: true,
        isArray: false,
        db: {
          type: 'datetime',
          nullable: false,
          default: 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
          comment: '更新时间',
        },
      },
    },
    {
      name: '删除时间',
      icon: 'clock',
      template: {
        name: 'deleteTime',
        desc: '软删除时间',
        type: 'Date',
        required: false,
        isArray: false,
        db: {
          type: 'datetime',
          nullable: true,
          default: null,
          comment: '删除时间',
        },
      },
    },
  ],

  // 状态字段模板
  status: [
    {
      name: '状态字段',
      icon: 'toggle',
      template: {
        name: 'status',
        desc: '记录状态：1-正常，0-禁用',
        type: 'number',
        required: true,
        isArray: false,
        db: {
          type: 'tinyint',
          length: 1,
          nullable: false,
          default: '1',
          comment: '状态：1-正常，0-禁用',
        },
        validation: {
          enum: [0, 1],
        },
      },
    },
    {
      name: '软删除标记',
      icon: 'delete',
      template: {
        name: 'isDeleted',
        desc: '是否删除：0-未删除，1-已删除',
        type: 'boolean',
        required: true,
        isArray: false,
        db: {
          type: 'tinyint',
          length: 1,
          nullable: false,
          default: '0',
          comment: '是否删除：0-未删除，1-已删除',
        },
      },
    },
    {
      name: '是否启用',
      icon: 'power',
      template: {
        name: 'isEnabled',
        desc: '是否启用：1-启用，0-禁用',
        type: 'boolean',
        required: true,
        isArray: false,
        db: {
          type: 'tinyint',
          length: 1,
          nullable: false,
          default: '1',
          comment: '是否启用：1-启用，0-禁用',
        },
      },
    },
  ],

  // 文本字段模板
  text: [
    {
      name: '名称字段',
      icon: 'text',
      template: {
        name: 'name',
        desc: '名称',
        type: 'string',
        required: true,
        isArray: false,
        db: {
          type: 'varchar',
          length: 255,
          nullable: false,
          default: "''",
          comment: '名称',
        },
        validation: {
          maxLength: 255,
          minLength: 1,
        },
      },
    },
    {
      name: '标题字段',
      icon: 'heading',
      template: {
        name: 'title',
        desc: '标题',
        type: 'string',
        required: true,
        isArray: false,
        db: {
          type: 'varchar',
          length: 500,
          nullable: false,
          default: "''",
          comment: '标题',
        },
      },
    },
    {
      name: '描述字段',
      icon: 'description',
      template: {
        name: 'description',
        desc: '描述信息',
        type: 'string',
        required: false,
        isArray: false,
        db: {
          type: 'text',
          nullable: true,
          comment: '描述信息',
        },
      },
    },
    {
      name: '备注字段',
      icon: 'note',
      template: {
        name: 'remark',
        desc: '备注',
        type: 'string',
        required: false,
        isArray: false,
        db: {
          type: 'text',
          nullable: true,
          comment: '备注',
        },
      },
    },
  ],

  // 用户相关字段模板
  user: [
    {
      name: '创建人',
      icon: 'user',
      template: {
        name: 'createBy',
        desc: '创建人ID',
        type: 'string',
        required: true,
        isArray: false,
        db: {
          type: 'varchar',
          length: 36,
          nullable: false,
          comment: '创建人ID',
        },
        relation: {
          type: 'manyToOne',
          targetEntity: 'User',
          joinColumn: {
            name: 'createBy',
            referencedColumnName: 'id',
          },
        },
      },
    },
    {
      name: '更新人',
      icon: 'user',
      template: {
        name: 'updateBy',
        desc: '更新人ID',
        type: 'string',
        required: false,
        isArray: false,
        db: {
          type: 'varchar',
          length: 36,
          nullable: true,
          comment: '更新人ID',
        },
        relation: {
          type: 'manyToOne',
          targetEntity: 'User',
          joinColumn: {
            name: 'updateBy',
            referencedColumnName: 'id',
          },
        },
      },
    },
  ],

  // 排序字段模板
  sort: [
    {
      name: '排序字段',
      icon: 'sort',
      template: {
        name: 'sort',
        desc: '排序序号',
        type: 'number',
        required: true,
        isArray: false,
        db: {
          type: 'int',
          nullable: false,
          default: '0',
          comment: '排序序号',
        },
        validation: {
          min: 0,
        },
      },
    },
    {
      name: '显示顺序',
      icon: 'order',
      template: {
        name: 'displayOrder',
        desc: '显示顺序',
        type: 'number',
        required: true,
        isArray: false,
        db: {
          type: 'int',
          nullable: false,
          default: '999',
          comment: '显示顺序',
        },
      },
    },
  ],

  // 业务字段模板
  business: [
    {
      name: '金额字段',
      icon: 'money',
      template: {
        name: 'amount',
        desc: '金额',
        type: 'number',
        required: true,
        isArray: false,
        db: {
          type: 'decimal',
          precision: 10,
          scale: 2,
          nullable: false,
          default: '0.00',
          comment: '金额',
        },
        validation: {
          min: 0,
          precision: 10,
          scale: 2,
        },
      },
    },
    {
      name: '价格字段',
      icon: 'price',
      template: {
        name: 'price',
        desc: '价格',
        type: 'number',
        required: true,
        isArray: false,
        db: {
          type: 'decimal',
          precision: 8,
          scale: 2,
          nullable: false,
          default: '0.00',
          comment: '价格',
        },
      },
    },
    {
      name: '数量字段',
      icon: 'number',
      template: {
        name: 'quantity',
        desc: '数量',
        type: 'number',
        required: true,
        isArray: false,
        db: {
          type: 'int',
          nullable: false,
          default: '0',
          comment: '数量',
        },
        validation: {
          min: 0,
        },
      },
    },
  ],
};
```

### 模板应用函数

```javascript
/**
 * 应用字段模板
 * @param {Object} template - 字段模板
 * @param {Object} customization - 自定义配置
 * @returns {Object} 应用模板后的字段配置
 */
function applyFieldTemplate(template, customization = {}) {
  const field = JSON.parse(JSON.stringify(template)); // 深拷贝

  // 应用自定义配置
  if (customization.name) {
    field.name = customization.name;
  }

  if (customization.desc) {
    field.desc = customization.desc;
  }

  if (customization.db) {
    field.db = { ...field.db, ...customization.db };
  }

  if (customization.validation) {
    field.validation = { ...field.validation, ...customization.validation };
  }

  return field;
}

/**
 * 批量应用模板
 * @param {Array} templates - 模板数组
 * @param {string} prefix - 字段名前缀
 * @returns {Array} 字段配置数组
 */
function batchApplyTemplates(templates, prefix = '') {
  return templates.map((template) => {
    const field = JSON.parse(JSON.stringify(template));
    if (prefix) {
      field.name = prefix + field.name.charAt(0).toUpperCase() + field.name.slice(1);
    }
    return field;
  });
}
```

## 批量操作功能

### 批量操作类型

```javascript
const batchOperations = {
  // 字段属性批量设置
  fieldProperties: [
    {
      name: '批量设置必填',
      icon: 'required',
      description: '将选中字段设置为必填',
      action: 'setRequired',
      value: true,
    },
    {
      name: '批量取消必填',
      icon: 'optional',
      description: '将选中字段设置为非必填',
      action: 'setRequired',
      value: false,
    },
    {
      name: '批量设置可空',
      icon: 'nullable',
      description: '将选中字段设置为允许为空',
      action: 'setNullable',
      value: true,
    },
    {
      name: '批量设置不可空',
      icon: 'not-null',
      description: '将选中字段设置为不允许为空',
      action: 'setNullable',
      value: false,
    },
  ],

  // 数据库类型批量设置
  dbTypes: [
    {
      name: '批量设置VARCHAR类型',
      description: '将选中的字符串字段设置为VARCHAR类型',
      action: 'setDbType',
      value: 'varchar',
      condition: 'fieldType === "string"',
    },
    {
      name: '批量设置TEXT类型',
      description: '将选中的字符串字段设置为TEXT类型',
      action: 'setDbType',
      value: 'text',
      condition: 'fieldType === "string"',
    },
    {
      name: '批量设置INT类型',
      description: '将选中的数字字段设置为INT类型',
      action: 'setDbType',
      value: 'int',
      condition: 'fieldType === "number"',
    },
  ],

  // 默认值批量设置
  defaultValues: [
    {
      name: '批量设置空字符串',
      description: '将选中的字符串字段默认值设置为空字符串',
      action: 'setDefault',
      value: "''",
      condition: 'fieldType === "string"',
    },
    {
      name: '批量设置0',
      description: '将选中的数字字段默认值设置为0',
      action: 'setDefault',
      value: '0',
      condition: 'fieldType === "number"',
    },
    {
      name: '批量设置当前时间',
      description: '将选中的日期字段默认值设置为当前时间',
      action: 'setDefault',
      value: 'CURRENT_TIMESTAMP',
      condition: 'fieldType === "Date"',
    },
    {
      name: '批量清除默认值',
      description: '清除选中字段的默认值',
      action: 'setDefault',
      value: null,
    },
  ],

  // 字段名称批量操作
  naming: [
    {
      name: '添加前缀',
      description: '为选中字段名称添加前缀',
      action: 'addPrefix',
      inputType: 'text',
      placeholder: '请输入前缀',
    },
    {
      name: '添加后缀',
      description: '为选中字段名称添加后缀',
      action: 'addSuffix',
      inputType: 'text',
      placeholder: '请输入后缀',
    },
    {
      name: '移除前缀',
      description: '移除选中字段名称的指定前缀',
      action: 'removePrefix',
      inputType: 'text',
      placeholder: '请输入要移除的前缀',
    },
    {
      name: '移除后缀',
      description: '移除选中字段名称的指定后缀',
      action: 'removeSuffix',
      inputType: 'text',
      placeholder: '请输入要移除的后缀',
    },
    {
      name: '转换命名风格',
      description: '转换选中字段的命名风格',
      action: 'convertNaming',
      inputType: 'select',
      options: [
        { label: '驼峰命名 (camelCase)', value: 'camelCase' },
        { label: '下划线命名 (snake_case)', value: 'snake_case' },
        { label: '帕斯卡命名 (PascalCase)', value: 'PascalCase' },
        { label: '短横线命名 (kebab-case)', value: 'kebab-case' },
      ],
    },
  ],

  // 验证规则批量设置
  validation: [
    {
      name: '批量设置最大长度',
      description: '为选中的字符串字段设置最大长度',
      action: 'setMaxLength',
      inputType: 'number',
      placeholder: '请输入最大长度',
      condition: 'fieldType === "string"',
    },
    {
      name: '批量设置最小长度',
      description: '为选中的字符串字段设置最小长度',
      action: 'setMinLength',
      inputType: 'number',
      placeholder: '请输入最小长度',
      condition: 'fieldType === "string"',
    },
    {
      name: '批量设置数值范围',
      description: '为选中的数字字段设置数值范围',
      action: 'setNumberRange',
      inputType: 'range',
      fields: ['min', 'max'],
      condition: 'fieldType === "number"',
    },
  ],
};
```

### 批量操作实现

```javascript
/**
 * 批量操作处理器
 */
class BatchOperationHandler {
  /**
   * 执行批量操作
   * @param {Array} selectedFields - 选中的字段数组
   * @param {Object} operation - 操作配置
   * @param {any} value - 操作值
   * @returns {Array} 更新后的字段数组
   */
  static execute(selectedFields, operation, value) {
    const updatedFields = [...selectedFields];

    updatedFields.forEach((field) => {
      // 检查条件
      if (operation.condition && !this.evaluateCondition(field, operation.condition)) {
        return;
      }

      // 执行操作
      switch (operation.action) {
        case 'setRequired':
          field.required = value;
          break;

        case 'setNullable':
          if (!field.db) field.db = {};
          field.db.nullable = value;
          break;

        case 'setDbType':
          if (!field.db) field.db = {};
          field.db.type = value;
          break;

        case 'setDefault':
          field.default = value;
          if (field.db) {
            field.db.default = value;
          }
          break;

        case 'addPrefix':
          field.name = value + field.name;
          break;

        case 'addSuffix':
          field.name = field.name + value;
          break;

        case 'removePrefix':
          if (field.name.startsWith(value)) {
            field.name = field.name.substring(value.length);
          }
          break;

        case 'removeSuffix':
          if (field.name.endsWith(value)) {
            field.name = field.name.substring(0, field.name.length - value.length);
          }
          break;

        case 'convertNaming':
          field.name = this.convertNaming(field.name, value);
          break;

        case 'setMaxLength':
          if (!field.validation) field.validation = {};
          field.validation.maxLength = value;
          if (field.db && field.db.type === 'varchar') {
            field.db.length = Math.min(value, field.db.length || 255);
          }
          break;

        case 'setMinLength':
          if (!field.validation) field.validation = {};
          field.validation.minLength = value;
          break;

        case 'setNumberRange':
          if (!field.validation) field.validation = {};
          if (value.min !== undefined) field.validation.min = value.min;
          if (value.max !== undefined) field.validation.max = value.max;
          break;
      }
    });

    return updatedFields;
  }

  /**
   * 评估条件表达式
   * @param {Object} field - 字段对象
   * @param {string} condition - 条件表达式
   * @returns {boolean} 条件结果
   */
  static evaluateCondition(field, condition) {
    // 简单的条件评估器
    try {
      // 替换变量
      const expr = condition
        .replace(/fieldType/g, `"${field.type}"`)
        .replace(/fieldName/g, `"${field.name}"`)
        .replace(/dbType/g, `"${field.db?.type || ''}"`);

      // 使用 Function 构造器安全评估
      return new Function('return ' + expr)();
    } catch (error) {
      console.warn('条件评估失败:', condition, error);
      return false;
    }
  }

  /**
   * 转换命名风格
   * @param {string} name - 原始名称
   * @param {string} style - 目标风格
   * @returns {string} 转换后的名称
   */
  static convertNaming(name, style) {
    switch (style) {
      case 'camelCase':
        return name
          .replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
          .replace(/^[A-Z]/, (letter) => letter.toLowerCase());

      case 'snake_case':
        return name
          .replace(/([A-Z])/g, '_$1')
          .replace(/^_/, '')
          .toLowerCase();

      case 'PascalCase':
        return name
          .replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
          .replace(/^[a-z]/, (letter) => letter.toUpperCase());

      case 'kebab-case':
        return name
          .replace(/([A-Z])/g, '-$1')
          .replace(/^-/, '')
          .replace(/_/g, '-')
          .toLowerCase();

      default:
        return name;
    }
  }
}
```

## 代码生成预览

### 代码生成器配置

```javascript
const codeGenerators = {
  typeorm: {
    name: 'TypeORM Entity',
    description: '生成TypeORM实体类代码',
    fileExtension: '.ts',
    template: 'typeorm-entity',
  },
  sequelize: {
    name: 'Sequelize Model',
    description: '生成Sequelize模型代码',
    fileExtension: '.js',
    template: 'sequelize-model',
  },
  prisma: {
    name: 'Prisma Schema',
    description: '生成Prisma数据模型',
    fileExtension: '.prisma',
    template: 'prisma-schema',
  },
  sql: {
    name: 'SQL DDL',
    description: '生成SQL建表语句',
    fileExtension: '.sql',
    template: 'sql-ddl',
  },
  mongoose: {
    name: 'Mongoose Schema',
    description: '生成Mongoose模式定义',
    fileExtension: '.js',
    template: 'mongoose-schema',
  },
};
```

### 实时预览功能

```javascript
/**
 * 实时代码生成预览
 */
class CodePreviewGenerator {
  /**
   * 生成TypeORM实体类代码
   * @param {Object} typeEntity - 类型实体对象
   * @returns {string} TypeORM代码
   */
  static generateTypeORM(typeEntity) {
    let code = '';

    // 导入声明
    code += `import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm';\n\n`;

    // 实体类注释
    if (typeEntity.desc) {
      code += `/**\n * ${typeEntity.desc}\n */\n`;
    }

    // 索引装饰器
    if (typeEntity.db?.indexes?.length > 0) {
      typeEntity.db.indexes.forEach((index) => {
        const columns = index.columns.map((col) => `'${col}'`).join(', ');
        const options = [];
        if (index.unique) options.push('unique: true');
        if (index.name) options.push(`name: '${index.name}'`);
        if (index.where) options.push(`where: '${index.where}'`);

        const optionsStr = options.length > 0 ? `, { ${options.join(', ')} }` : '';
        code += `@Index([${columns}]${optionsStr})\n`;
      });
    }

    // 实体装饰器
    code += `@Entity('${typeEntity.db?.name || typeEntity.name.toLowerCase()}')\n`;
    code += `export class ${typeEntity.name} {\n`;

    // 字段定义
    typeEntity.fields.forEach((field) => {
      // 字段注释
      if (field.desc) {
        code += `  /**\n   * ${field.desc}\n   */\n`;
      }

      // 主键装饰器
      if (field.db?.isPrimary) {
        if (field.db?.isGenerated) {
          code += `  @PrimaryGeneratedColumn('increment')\n`;
        } else {
          code += `  @PrimaryGeneratedColumn()\n`;
        }
      } else {
        // 普通字段装饰器
        const columnOptions = [];
        if (field.db?.type) columnOptions.push(`type: '${field.db.type}'`);
        if (field.db?.length) columnOptions.push(`length: ${field.db.length}`);
        if (field.db?.precision) columnOptions.push(`precision: ${field.db.precision}`);
        if (field.db?.scale) columnOptions.push(`scale: ${field.db.scale}`);
        if (field.db?.nullable !== undefined) columnOptions.push(`nullable: ${field.db.nullable}`);
        if (field.db?.default !== undefined) {
          const defaultValue =
            typeof field.db.default === 'string' ? `'${field.db.default}'` : field.db.default;
          columnOptions.push(`default: ${defaultValue}`);
        }
        if (field.db?.comment) columnOptions.push(`comment: '${field.db.comment}'`);

        const optionsStr = columnOptions.length > 0 ? `{ ${columnOptions.join(', ')} }` : '';
        code += `  @Column(${optionsStr})\n`;
      }

      // 字段声明
      const optional = !field.required ? '?' : '';
      const arrayType = field.isArray ? '[]' : '';
      code += `  ${field.name}${optional}: ${field.type}${arrayType};\n\n`;
    });

    code += `}\n`;

    return code;
  }

  /**
   * 生成SQL DDL语句
   * @param {Object} typeEntity - 类型实体对象
   * @param {string} dbType - 数据库类型
   * @returns {string} SQL代码
   */
  static generateSQL(typeEntity, dbType = 'mysql') {
    const tableName = typeEntity.db?.name || typeEntity.name.toLowerCase();
    let sql = `-- ${typeEntity.desc || typeEntity.name}\n`;
    sql += `CREATE TABLE \`${tableName}\` (\n`;

    const columns = [];
    const indexes = [];

    // 字段定义
    typeEntity.fields.forEach((field) => {
      let columnDef = `  \`${field.name}\``;

      // 数据类型
      if (field.db?.type) {
        let type = field.db.type.toUpperCase();
        if (field.db.length) {
          type += `(${field.db.length})`;
        } else if (field.db.precision && field.db.scale) {
          type += `(${field.db.precision},${field.db.scale})`;
        }
        columnDef += ` ${type}`;
      }

      // 约束
      if (!field.db?.nullable) {
        columnDef += ' NOT NULL';
      }

      // 默认值
      if (field.db?.default !== undefined) {
        columnDef += ` DEFAULT ${field.db.default}`;
      }

      // 自增
      if (field.db?.isGenerated) {
        columnDef += ' AUTO_INCREMENT';
      }

      // 注释
      if (field.db?.comment) {
        columnDef += ` COMMENT '${field.db.comment}'`;
      }

      columns.push(columnDef);

      // 主键
      if (field.db?.isPrimary) {
        indexes.push(`  PRIMARY KEY (\`${field.name}\`)`);
      }
    });

    // 类级别索引
    if (typeEntity.db?.indexes?.length > 0) {
      typeEntity.db.indexes.forEach((index) => {
        const columns = index.columns.map((col) => `\`${col}\``).join(', ');
        const indexType = index.unique ? 'UNIQUE INDEX' : 'INDEX';
        const indexName = index.name || `idx_${index.columns.join('_')}`;
        indexes.push(`  ${indexType} \`${indexName}\` (${columns})`);
      });
    }

    sql += columns.join(',\n');
    if (indexes.length > 0) {
      sql += ',\n' + indexes.join(',\n');
    }
    sql += '\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;\n';

    return sql;
  }
}
```

## 导入导出功能

### 支持的格式

```javascript
const importExportFormats = {
  json: {
    name: 'JSON格式',
    extension: '.json',
    mimeType: 'application/json',
    description: '标准JSON格式，包含完整的字段配置信息',
  },
  excel: {
    name: 'Excel文件',
    extension: '.xlsx',
    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    description: 'Excel表格格式，便于批量编辑字段信息',
  },
  csv: {
    name: 'CSV文件',
    extension: '.csv',
    mimeType: 'text/csv',
    description: 'CSV格式，支持基础字段信息导入导出',
  },
  yaml: {
    name: 'YAML格式',
    extension: '.yaml',
    mimeType: 'text/yaml',
    description: 'YAML格式，结构清晰易读',
  },
  sql: {
    name: 'SQL语句',
    extension: '.sql',
    mimeType: 'text/plain',
    description: 'SQL建表语句，可直接执行',
  },
};
```

## 实施建议

### 优先级排序

#### 高优先级（立即实施）

1. **智能默认值建议系统** - 提升用户体验，减少配置时间
2. **字段类型扩展** - 支持更多数据类型，提高适用性
3. **验证规则配置** - 增强数据质量保证
4. **字段模板预设** - 快速创建常用字段

#### 中优先级（短期实施）

1. **批量操作功能** - 提高批量配置效率
2. **索引配置增强** - 完善数据库性能优化
3. **关系字段配置** - 支持复杂数据模型
4. **代码生成预览** - 实时查看生成结果

#### 低优先级（长期规划）

1. **导入导出功能** - 方便数据迁移和备份
2. **历史记录功能** - 操作记录和版本管理
3. **性能监控** - 索引使用情况分析

### 实施步骤

#### 第一阶段：核心功能增强

1. 实现智能默认值建议系统
2. 扩展字段类型选项
3. 完善数据库类型映射
4. 添加基础验证规则

#### 第二阶段：用户体验优化

1. 实现字段模板预设
2. 添加批量操作功能
3. 增强索引配置界面
4. 实现实时代码预览

#### 第三阶段：高级功能

1. 完善关系字段配置
2. 实现导入导出功能
3. 添加性能分析工具
4. 集成历史记录功能

### 技术考虑

#### 兼容性

- 保持与现有数据结构的兼容性
- 提供数据迁移工具
- 支持渐进式升级

#### 性能

- 优化大量字段的渲染性能
- 实现虚拟滚动
- 添加搜索和过滤功能

#### 可扩展性

- 插件化架构设计
- 支持自定义字段类型
- 提供 API 接口

### 测试策略

#### 单元测试

- 默认值建议算法测试
- 字段验证规则测试
- 批量操作功能测试

#### 集成测试

- 代码生成功能测试
- 导入导出功能测试
- 数据库兼容性测试

#### 用户体验测试

- 界面易用性测试
- 性能压力测试
- 错误处理测试

---

## 总结

本优化方案从用户体验、功能完整性和技术可行性三个维度对数据库字段编辑器进行了全面的分析和设计。通过智能化的建议系统、丰富的配置选项和便捷的批量操作，将大大提升开发者的工作效率和使用体验。

建议按照优先级逐步实施，确保每个阶段都能为用户带来实际价值，同时保持系统的稳定性和可维护性。
