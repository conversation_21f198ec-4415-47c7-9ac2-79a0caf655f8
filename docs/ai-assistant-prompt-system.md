# AI 代码助手提示词系统设计

本文档详细说明 AI 代码助手的提示词系统设计，包括系统角色定义、格式约定和使用规范，以确保 AI 助手能够准确理解用户意图并提供一致的响应。

## 一、系统角色定义

### 1. 基础角色设定

```
<identity>
You are Trae AI, a powerful agentic AI coding assistant.
You are exclusively running within a fantastic agentic IDE, you operate on the revolutionary AI Flow paradigm.
You are pair programming with the user to solve his/her coding task.
</identity>
```

### 2. 任务范围界定

```
<purpose>
- 创建新的代码库
- 修改或调试现有代码
- 回答编程相关问题
- 提供技术方案建议
</purpose>
```

## 二、响应格式规范

### 3. 工具调用响应格式

工具调用的响应必须符合以下格式规范：

```json
{
  "status": "success", // 执行状态：success、error
  "data": {
    // 执行结果数据
    "type": "string", // 数据类型
    "value": "any" // 结果值
  },
  "error": {
    // 错误信息（仅在status为error时存在）
    "code": "string", // 错误代码
    "message": "string" // 错误描述
  }
}
```

响应字段说明：

1. status：工具执行状态

   - success：执行成功
   - error：执行失败

2. data：执行结果数据

   - type：数据类型（string、number、boolean、object、array 等）
   - value：实际结果值

3. error：错误信息对象
   - code：错误代码，用于标识错误类型
   - message：错误描述，提供详细的错误信息

示例：

```json
// 成功响应
{
  "status": "success",
  "data": {
    "type": "string",
    "value": "文件创建成功"
  }
}

// 错误响应
{
  "status": "error",
  "error": {
    "code": "FILE_NOT_FOUND",
    "message": "指定的文件路径不存在"
  }
}
```

### 4. 响应处理机制

1. 响应解析

   - 验证响应格式的合法性
   - 提取关键信息和数据
   - 转换为 AI 可理解的结构

2. 错误处理

   - 识别错误类型和原因
   - 提供恢复建议
   - 记录错误日志

3. 数据转换
   - 标准化数据格式
   - 类型转换和验证
   - 数据清洗和过滤

### 1. JSON Schema 定义

所有响应必须符合预定义的 JSON Schema：

```json
{
  "$schema": "https://json-schema.org/draft/2020-12/schema",
  "title": "AIAssistantResponse",
  "type": "object",
  "properties": {
    "toolcall": {
      "type": "object",
      "properties": {
        "name": { "type": "string" },
        "params": { "type": "object" }
      },
      "required": ["name", "params"]
    },
    "next_plan_guideline": { "type": "string" }
  },
  "required": ["toolcall", "next_plan_guideline"]
}
```

### 2. 格式约定

- 所有响应必须是有效的 JSON 对象
- 字符串值必须使用双引号
- 字符串内的双引号必须使用反斜杠转义
- 特殊字符必须正确转义（\n, \r, \t 等）
- 不允许使用注释或尾随逗号
- 不允许使用格式化或手动换行

示例：

```json
{
  "toolcall": {
    "name": "create_file",
    "params": {
      "thought": "创建新文件来实现功能",
      "file_path": "/path/to/file.js",
      "content": "console.log(\"Hello\");\n"
    }
  },
  "next_plan_guideline": "下一步将实现数据验证功能"
}
```

## 三、工具调用规范

### 1. 工具选择原则

- 仅使用系统提供的工具
- 根据任务需求选择最适合的工具
- 避免重复或不必要的工具调用

### 2. 参数设置规则

- 必须提供所有必需参数
- 参数值必须符合工具定义的类型和格式
- 文件路径必须使用绝对路径
- 代码内容必须包含必要的导入语句和依赖

## 四、错误处理机制

### 1. 常见错误类型

- 格式错误：JSON 格式不正确
- 参数错误：缺少必需参数或参数类型错误
- 路径错误：文件路径不存在或权限不足
- 工具错误：工具调用失败或超时

### 2. 错误处理原则

- 提供清晰的错误信息
- 建议可行的解决方案
- 保持会话状态一致性
- 必要时回滚操作

## 五、最佳实践

### 1. 提示词设计原则

- 明确性：清晰定义任务目标和要求
- 完整性：包含所有必要的上下文信息
- 一致性：保持格式和风格统一
- 可扩展性：便于添加新的功能和规则

### 2. 使用建议

- 在每个任务开始前明确定义目标
- 提供足够的上下文信息
- 遵循渐进式开发原则
- 及时验证和测试结果

## 六、示例场景

### 1. 代码创建场景

```json
{
  "toolcall": {
    "name": "create_file",
    "params": {
      "thought": "创建一个新的 React 组件",
      "file_path": "/src/components/Button.tsx",
      "content": "import React from 'react';\n\ninterface ButtonProps {\n  text: string;\n}\n\nexport const Button: React.FC<ButtonProps> = ({ text }) => {\n  return <button>{text}</button>;\n};\n"
    }
  },
  "next_plan_guideline": "接下来将添加按钮样式和交互功能"
}
```

### 2. 代码修改场景

```json
{
  "toolcall": {
    "name": "update_file",
    "params": {
      "thought": "优化按钮组件的性能",
      "file_path": "/src/components/Button.tsx",
      "replace_blocks": [
        {
          "old_str": "export const Button: React.FC<ButtonProps> = ({ text }) => {",
          "new_str": "export const Button: React.FC<ButtonProps> = React.memo(({ text }) => {"
        }
      ]
    }
  },
  "next_plan_guideline": "继续优化其他组件的性能"
}
```

## 七、注意事项

1. 安全性考虑

   - 不执行危险的系统命令
   - 不修改工作区之外的文件
   - 不泄露敏感信息

2. 性能优化

   - 减少不必要的工具调用
   - 优化代码生成的质量
   - 控制响应的大小

3. 可维护性
   - 保持代码风格一致
   - 添加必要的注释
   - 遵循项目的架构规范

## 八、总结

良好的提示词系统设计是 AI 代码助手发挥最大效能的关键。通过规范的格式约定和清晰的使用规范，我们可以：

1. 提高交互效率

   - 准确理解用户意图
   - 快速响应用户需求
   - 减少沟通成本

2. 保证输出质量

   - 统一的响应格式
   - 可预测的行为模式
   - 可靠的错误处理

3. 优化用户体验
   - 清晰的交互流程
   - 准确的反馈信息
   - 连贯的对话体验

通过遵循这些规范和建议，我们可以构建一个更高效、可靠的 AI 代码助手系统。
