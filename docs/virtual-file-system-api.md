# 虚拟文件系统 API 文档

## 简介

虚拟文件系统(VFS)提供了一套完整的文件操作接口，支持基础文件操作、目录管理、版本控制和依赖管理等功能。本文档详细说明各个接口的使用方法。

## 编辑器桥接器 (EditorVFSAdapter)

### 简介

EditorVFSAdapter 是一个桥接器，用于连接编辑器与虚拟文件系统，提供了项目导入导出、文档管理、类型实体管理等功能。

### 初始化

```typescript
import { EditorVFSAdapter } from './adapters/EditorAdapter';

// 获取桥接器实例
const adapter = EditorVFSAdapter.getInstance(projectId);
```

### 核心功能

#### 项目管理

##### importProject(project: LcProject): Promise<void>

导入项目，包括项目配置、资源、文档和类型实体等。

```typescript
await adapter.importProject({
  _id: 'project-id',
  title: '项目名称',
  description: '项目描述',
  documents: [],
  // ... 其他项目信息
});
```

##### updateProjectConfig(config: Partial<LcProject>): Promise<void>

更新项目配置。

```typescript
await adapter.updateProjectConfig({
  title: '新项目名称',
  description: '新项目描述',
});
```

##### updateProjectAssets(assets: { packages?: any; assets?: any }): Promise<void>

更新项目资源。

```typescript
await adapter.updateProjectAssets({
  packages: {
    /* 包配置 */
  },
  assets: {
    /* 资源配置 */
  },
});
```

#### 文档管理

##### importExistingDocuments(documents: Partial<LcDocument>[]): Promise<void>

批量导入已有文档。

```typescript
await adapter.importExistingDocuments([
  {
    type: 'Page',
    fileName: 'index',
    folder: '/pages',
  },
]);
```

##### loadDocumentSource(fileName: string): Promise<string>

按需加载文档源代码。

```typescript
const source = await adapter.loadDocumentSource('index');
```

##### putDocument(fileName: string, content: string, options?: PutDocumentOptions): Promise<void>

保存文档内容到虚拟文件系统。当使用可视化方式编辑 LcDocument 类型的页面或组件时，调用此方法将编辑后的内容同步写入虚拟文件系统。

**参数**

- fileName: 文件名
- content: 文档内容
- options:
  - isNew?: boolean - 是否为新建文档，默认为 false

```typescript
// 新建文档
await adapter.putDocument('newPage', documentContent, {
  isNew: true,
});

// 更新文档
await adapter.putDocument('index', documentContent);
```

##### deleteDocument(fileName: string, options?: DeleteDocumentOptions): Promise<void>

从虚拟文件系统中删除指定文档。

**参数**

- fileName: 文件名
- options:
  - author?: string - 作者信息
  - message?: string - 删除原因

```typescript
await adapter.deleteDocument('unused-page', {
  author: 'developer',
  message: 'Remove unused page',
});
```

#### 类型实体管理

##### loadTypeEntitySource(typeId: string): Promise<string>

按需加载类型实体的源代码。

```typescript
const source = await adapter.loadTypeEntitySource('type-entity-id');
```

#### 项目结构管理

##### getProjectStructure(path?: string, options?: ProjectStructureOptions): Promise<string[] | FileSystemNode[]>

获取项目文件系统结构。

```typescript
const structure = await adapter.getProjectStructure('/', {
  recursive: true,
  tree: false,
  includeTypeEntity: true,
});
```

##### listDirectory(path: string, options?: ListDirectoryOptions): Promise<string[] | FileSystemNode[]>

获取指定目录下的文件列表。

```typescript
const files = await adapter.listDirectory('/pages', {
  recursive: false,
  includeTypeEntity: true,
});
```

### 使用建议

1. 在项目初始化时，使用 `importProject` 导入完整的项目信息
2. 使用 `loadDocumentSource` 和 `loadTypeEntitySource` 实现按需加载，提高性能
3. 使用 `getProjectStructure` 获取项目结构时，根据需求合理设置选项
4. 项目配置更新时，使用 `updateProjectConfig` 和 `updateProjectAssets` 保持同步

## 基础文件操作

### readFile(path: string): Promise<any>

读取指定路径的文件内容。

**参数**

- path: 文件路径，必须以 '/' 开头

**返回值**

- Promise<any>: 文件内容

**示例**

```typescript
const content = await vfs.readFile('/project.json');
```

### writeFile(path: string, content: any, options?: WriteOptions): Promise<void>

写入文件内容，支持版本控制。

**参数**

- path: 文件路径
- content: 文件内容
- options: 写入选项
  - author?: string - 作者信息
  - message?: string - 提交信息

**示例**

```typescript
await vfs.writeFile('/pages/index.json', content, {
  author: 'developer',
  message: 'Update index page',
});
```

### deleteFile(path: string): Promise<void>

删除指定文件。

**参数**

- path: 文件路径

**示例**

```typescript
await vfs.deleteFile('/pages/unused.json');
```

## 目录操作

### mkdir(path: string): Promise<void>

创建目录。

**参数**

- path: 目录路径

**示例**

```typescript
await vfs.mkdir('/pages/components');
```

### readdir(path: string, options?: ReaddirOptions): Promise<string[] | FileSystemNode[]>

读取目录内容。

**参数**

- path: 目录路径
- options:
  - recursive?: boolean - 是否递归读取
  - includeStats?: boolean - 是否包含文件状态信息
  - includeTypeEntity?: boolean - 是否包含类型实体信息

**返回值**

- 文件/目录名称列表或详细节点信息

**示例**

```typescript
// 获取目录列表
const files = await vfs.readdir('/pages');

// 获取详细信息
const nodes = await vfs.readdir('/pages', {
  recursive: true,
  includeStats: true,
});
```

### rmdir(path: string, recursive?: boolean): Promise<void>

删除目录。

**参数**

- path: 目录路径
- recursive: 是否递归删除，如果目录非空必须设置为 true

**示例**

```typescript
await vfs.rmdir('/pages/unused', true);
```

## 版本控制

### getFileHistory(path: string): Promise<FileChange[]>

获取文件的变更历史。

**参数**

- path: 文件路径

**返回值**

- FileChange[]: 变更记录列表

**示例**

```typescript
const history = await vfs.getFileHistory('/pages/index.json');
```

### getFileVersions(path: string): Promise<FileVersion[]>

获取文件的所有版本。

**参数**

- path: 文件路径

**返回值**

- FileVersion[]: 版本列表

**示例**

```typescript
const versions = await vfs.getFileVersions('/pages/index.json');
```

### revertToVersion(path: string, version: number): Promise<void>

回退到指定版本。

**参数**

- path: 文件路径
- version: 版本号

**示例**

```typescript
await vfs.revertToVersion('/pages/index.json', 1);
```

## 依赖管理

### registerDependency(path: string, type: DependencyType): Promise<void>

注册文件依赖关系。

**参数**

- path: 文件路径
- type: 依赖类型 ('component' | 'code' | 'data' | 'style')

**示例**

```typescript
await vfs.registerDependency('/pages/index.json', 'component');
```

### getDependencies(path: string): Promise<Dependency[]>

获取文件的所有依赖。

**参数**

- path: 文件路径

**返回值**

- Dependency[]: 依赖列表

**示例**

```typescript
const deps = await vfs.getDependencies('/pages/index.json');
```

### getLoadingOrder(path: string): Promise<string[]>

获取依赖加载顺序。

**参数**

- path: 文件路径

**返回值**

- string[]: 按加载顺序排列的文件路径列表

**示例**

```typescript
const order = await vfs.getLoadingOrder('/pages/index.json');
```

## 文件监听

### watch(path: string, callback: WatchCallback): () => void

监听文件变化。

**参数**

- path: 文件路径
- callback: 变化回调函数

**返回值**

- 取消监听的函数

**示例**

```typescript
const unwatch = vfs.watch('/pages/index.json', (path) => {
  console.log(`File ${path} changed`);
});

// 取消监听
unwatch();
```

## 工具方法

### move(srcPath: string, destPath: string): Promise<void>

移动文件或目录。

**参数**

- srcPath: 源路径
- destPath: 目标路径

**示例**

```typescript
await vfs.move('/pages/old.json', '/pages/new.json');
```

### rename(oldPath: string, newPath: string): Promise<void>

重命名文件或目录。

**参数**

- oldPath: 原路径
- newPath: 新路径

**示例**

```typescript
await vfs.rename('/pages/old.json', '/pages/new.json');
```

## 注意事项

1. 所有路径必须以 '/' 开头
2. 文件操作都是异步的，需要使用 async/await 处理
3. 写入文件时建议提供 author 和 message 信息，便于版本管理
4. 删除非空目录时必须设置 recursive 为 true
5. 监听文件变化时记得在适当时机取消监听，避免内存泄漏

## 类型定义

```typescript
interface WriteOptions {
  author?: string;
  message?: string;
}

interface FileSystemStats {
  type: 'file' | 'directory';
  size: number;
  createTime: number;
  modifyTime: number;
  isMetadataOnly?: boolean;
}

interface FileChange {
  id?: number;
  type: 'create' | 'update' | 'delete';
  path: string;
  content?: any;
  timestamp: number;
  author?: string;
}

interface FileVersion {
  id?: number;
  path: string;
  version: number;
  content: any;
  timestamp: number;
  author?: string;
  message?: string;
}

type DependencyType = 'component' | 'code' | 'data' | 'style';

interface Dependency {
  path: string;
  type: DependencyType;
  dependencies: string[];
}

type WatchCallback = (path: string) => void;
```
